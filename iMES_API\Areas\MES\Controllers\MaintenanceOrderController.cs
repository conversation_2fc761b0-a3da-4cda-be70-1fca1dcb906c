using AutoMapper;
using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class MaintenanceOrderController : ControllerBaseAPI
    {
        private readonly IMapper _mapper;
        private readonly IMaintenanceOrderRepository _maintenanceOrderRepository;
        private readonly ILogger<MaintenanceOrderController> _logger;

        public MaintenanceOrderController(
            IMapper mapper, 
            IMaintenanceOrderRepository maintenanceOrderRepository, 
            ILogger<MaintenanceOrderController> logger)
        {
            _mapper = mapper;
            _maintenanceOrderRepository = maintenanceOrderRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get list of maintenance orders with optional filters
        /// </summary>
        /// <param name="searchModel">Search parameters</param>
        /// <returns>List of maintenance orders</returns>
        [HttpPost("MaintenanceOrderList")]
        public async Task<IActionResult> MaintenanceOrderList([FromBody] MaintenanceOrderSearchModel searchModel)
        {
            try
            {
                var (orders, totalCount) = await _maintenanceOrderRepository.GetMaintenanceOrderListAsync(searchModel);

                if (orders == null || orders.Count == 0)
                {
                    return Ok(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.OK,
                        IsSuccess = true,
                        Message = "Không tìm thấy dữ liệu",
                        Data = new 
                        {
                            Items = new List<MaintenanceOrderViewModel>(),
                            TotalCount = 0,
                            PageNumber = searchModel.PageNumber,
                            PageSize = searchModel.PageSize,
                            TotalPages = 0
                        }
                    });
                }

                var totalPages = (int)Math.Ceiling(totalCount / (double)searchModel.PageSize);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = new 
                    {
                        Items = orders,
                        TotalCount = totalCount,
                        PageNumber = searchModel.PageNumber,
                        PageSize = searchModel.PageSize,
                        TotalPages = totalPages
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaintenanceOrderList: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get maintenance order by ID
        /// </summary>
        /// <param name="id">Order ID</param>
        /// <param name="companyCode">Company Code</param>
        /// <returns>Maintenance order details</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetMaintenanceOrderById(string id, [FromQuery] string companyCode = null)
        {
            try
            {
                var record = await _maintenanceOrderRepository.GetMaintenanceOrderByIdAsync(id, companyCode);

                if (record == null)
                {
                    _logger.LogWarning($"GetMaintenanceOrderById returned no record for id: {id}");
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = "Không tìm thấy bản ghi"
                    });
                }

                _logger.LogDebug($"GetMaintenanceOrderById returned record for id: {id}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaintenanceOrderById: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Create a new maintenance order
        /// </summary>
        [HttpPost("Create")]
        public async Task<IActionResult> CreateMaintenanceOrder([FromBody] CreateMaintenanceOrderModel model)
        {
            try
            {
                var (success, message, orderNumber) = await _maintenanceOrderRepository.CreateMaintenanceOrderAsync(model);

                if (!success)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = message ?? "Failed to create maintenance order"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = message,
                    Data = new { OrderNumber = orderNumber }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in CreateMaintenanceOrder: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Close a maintenance order
        /// </summary>
        [HttpPost("Close/{orderNumber}")]
        public async Task<IActionResult> CloseMaintenanceOrder(string orderNumber)
        {
            try
            {
                var (success, message) = await _maintenanceOrderRepository.CloseMaintenanceOrderAsync(orderNumber);

                if (!success)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = message ?? "Failed to close maintenance order"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in CloseMaintenanceOrder: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get workcenters for a company code
        /// </summary>
        [HttpGet("workcenters/{companyCode}")]
        public async Task<IActionResult> GetWorkCenters(string companyCode)
        {
            try
            {
                var workcenters = await _maintenanceOrderRepository.GetWorkCentersAsync(companyCode);
                
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = workcenters
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetWorkCenters: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get equipment suggestions for autocomplete
        /// </summary>
        /// <param name="companyCode">Company Code</param>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of equipment suggestions</returns>
        [HttpGet("equipment/{companyCode}")]
        public async Task<IActionResult> GetEquipment(string companyCode, [FromQuery] string searchTerm = "")
        {
            try
            {
                var equipment = await _maintenanceOrderRepository.GetEquipmentAsync(companyCode, searchTerm);
                
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = equipment
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetEquipment: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Clear the work center cache for a specific company code or all companies
        /// </summary>
        /// <param name="companyCode">Optional company code. If not provided, clears cache for all companies</param>
        /// <returns>Success status</returns>
        [HttpPost("workcenters/clear-cache")]
        public async Task<IActionResult> ClearWorkCenterCache([FromQuery] string companyCode = null)
        {
            try
            {
                var success = await _maintenanceOrderRepository.ClearWorkCenterCacheAsync(companyCode);
                
                if (success)
                {
                    string message = string.IsNullOrEmpty(companyCode) 
                        ? "Successfully cleared all work center cache entries" 
                        : $"Successfully cleared work center cache for company {companyCode}";
                    
                    return Ok(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.OK,
                        IsSuccess = true,
                        Message = message
                    });
                }
                else
                {
                    return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                    {
                        Code = (int)HttpStatusCode.InternalServerError,
                        IsSuccess = false,
                        Message = "Failed to clear work center cache"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in ClearWorkCenterCache: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("test")]
        [AllowAnonymous]
        public IActionResult Test()
        {
            return Ok("MaintenanceOrderController is reachable");
        }
    }
} 