import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../Storage/storageSecureStorage.dart';
import '../Storage/storageSharedPreferences.dart';
import '../element/TableImExportWH.dart';
import '../element/TableInfo.dart';
import '../model/detailReservationImported.dart';
import '../model/getReservation.dart';
import '../model/listWarehouseTranfer.dart';
import '../repository/function/detailReservationImportedFunction.dart';
import '../repository/function/exportWarehouseFunction.dart';
import 'LostConnect.dart';

class DetailReservation extends StatefulWidget {
  final String token;
  final DataListWarehouseTranfer dataListWarehouseTranfer;
  final String dateTimeOld;
  const DetailReservation({Key? key, required this.token, required this.dataListWarehouseTranfer, required this.dateTimeOld}) : super(key: key);

  @override
  _DetailReservationState createState() => _DetailReservationState();
}

class _DetailReservationState extends State<DetailReservation> {
  bool _isLoading = false;
  bool _isNotWifi = false;
  // DataRawMeterial? _dataRawMaterial;
  // DataBatch? _getBatch;
  DataDetailReservationImported? _dataDetailReservationImported;
  DataReservation? _dataReservation;
  // DataGetQuantityImported? _dataGetQuantityImported;
  // List<PoDetailResponses>? _poDetailResponses = [];
  // final List _lsDataQuantityDetail= ["1","2","3"];
  late bool _timeOut;
  bool _disableButton = false;

  @override
  void initState() {
    super.initState();
    _getDataRawMaterial();
  }

  Future<void> _getDataRawMaterial() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _isNotWifi = false;
        });

        final dataDetailReservationImportedReservation = await Future.wait([
          DetailReservationImportedFuncion.getWarehouseTranferNoReservation(widget.dataListWarehouseTranfer.reservationId.toString(), widget.token),
          ExportWareHouseFunction.fetchReservation(widget.dataListWarehouseTranfer.reservationId.toString(), widget.token),
        ]);
        if (dataDetailReservationImportedReservation.isNotEmpty) {
          if (dataDetailReservationImportedReservation[0] != null && dataDetailReservationImportedReservation[1] != null) {
            if (!mounted) return;
            setState(() {
              _isLoading = false;
              _dataDetailReservationImported = dataDetailReservationImportedReservation[0] as DataDetailReservationImported;
              _dataReservation = dataDetailReservationImportedReservation[1] as DataReservation?;
            });
          } else {
            if (!mounted) return;
            setState(() {
              _isLoading = false;
            });
          }
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _timeOut = false;
      });
    }
  }

  Future<void> _removeCurrentUser(BuildContext context) async {
    setState(() {
      _disableButton = true;
    });
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => WillPopScope(
        onWillPop: () async => false,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
    await Future.wait([
      StorageSharedPreferences.removeShared("id"),
      StorageSharedPreferences.removeShared("datetimeNow"),
      SecureStorage.removeSecure("user", null)
    ]);
    if (!mounted) return;
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Đăng xuất thành công',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1)));
    Future.delayed(const Duration(seconds: 0), () {
      Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
    });
  }

  //  Future<void> _getDataReservation() async {
  //
  // }
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context);
          return false;
        },
        child: _timeOut == true
            ? Scaffold(
                backgroundColor: Colors.grey.shade200,
                body: Center(
                    child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Icon(Icons.access_time_filled_rounded, size: 45.sp),
                    SizedBox(height: 10.h),
                    Text(
                      "Hết phiên đăng nhập!",
                      style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10.h),
                    Text(
                      "Bạn đã hết phiên đăng nhập! Vui lòng đăng nhập trở lại",
                      style: TextStyle(
                        fontSize: 13.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    ElevatedButton.icon(
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 5.h, horizontal: 30.w)),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r), side: const BorderSide(color: Colors.white))),
                        side: MaterialStateProperty.all(
                          const BorderSide(
                            color: Color(0xff000000),
                          ),
                        ),
                        backgroundColor: MaterialStateProperty.all(const Color(0xff000000)),
                      ),
                      onPressed: _disableButton == true
                          ? null
                          : () async {
                              await _removeCurrentUser(context);
                            },
                      icon: Icon(Icons.refresh_rounded, size: 17.sp, color: Colors.white),
                      label: Text(
                        "Đăng xuất",
                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 15.sp),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                )))
            : Scaffold(
                backgroundColor: Colors.grey.shade200,
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Chi tiết reservation',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: _isNotWifi == true
                    ? LostConnect(checkConnect: () => _getDataRawMaterial())
                    : _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : _dataDetailReservationImported == null
                            ? Center(child: Text("Không tìm thấy thông tin pallet NVL đã nhập kho!", style: TextStyle(fontSize: 15.sp)))
                            : SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: <Widget>[
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          Text(
                                            "I. LỆNH CẤP VẬT TƯ",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          TableInfo(
                                            textCL1: "Số reservation:",
                                            textCL2: widget.dataListWarehouseTranfer.reservationNumber ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Item:",
                                            textCL2: widget.dataListWarehouseTranfer.item ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Mã NVL:",
                                            textCL2: _dataReservation!.materialCode ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Nhà máy xuất:",
                                            textCL2: _dataReservation!.plant ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Kho xuất:",
                                            textCL2: _dataReservation!.storageLocation ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          // TableInfoNoTop(
                                          //   textCL1: "Số lô:",
                                          //   textCL2: _dataReservation!.batch ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          TableInfoNoTop(
                                            textCL1: "Số lượng yêu cầu:",
                                            textCL2: _dataReservation!.reqQuantityRnd == null ? "" : (_dataReservation!.reqQuantityRnd.toString()),
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "ĐVT:",
                                            textCL2: _dataReservation!.unit ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Nhà máy nhập:",
                                            textCL2: _dataReservation!.riPlant ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Kho nhập:",
                                            textCL2: _dataReservation!.riStorageLocation ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          // TableInfoNoTop(
                                          //   textCL1: "SO/ SO Line:",
                                          //   textCL2: _dataReservation!.sosoLine ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1: "WBS:",
                                          //   textCL2: _dataReservation!.wbs ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1: "LSX:",
                                          //   textCL2: _dataReservation!.lsx ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          Text(
                                            "II. THÔNG TIN GIAO DỊCH CHUYỂN KHO",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          const HeaderTableImExportWH(
                                              textCL1: "Kho xuất", textCL2: "Kho nhập", colorCL2: 0xff303F9F, colorCL1: 0xff303F9F),
                                          TableImExportWHText(
                                            textCL1: "Sloc",
                                            textCL2: _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.slocExport ?? "",
                                            textCL3: "Sloc",
                                            textCL4: _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.slocImport ?? "",
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          TableImExportWHText(
                                            textCL1: "Warehouse No",
                                            textCL2:
                                                _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.warehouseNoExport ?? "",
                                            textCL3: "Warehouse No",
                                            textCL4:
                                                _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.warehouseNoImport ?? "",
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          TableImExportWHText(
                                            textCL1: "Storage Bin",
                                            textCL2:
                                                _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.storageBinExport ?? "",
                                            textCL3: "Storage Bin",
                                            textCL4:
                                                _dataDetailReservationImported == null ? "" : _dataDetailReservationImported!.storageBinImport ?? "",
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "III. THÔNG TIN NVL CHUYỂN KHO",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          TableInfo(
                                            textCL1: "Mã NVL:",
                                            textCL2: _dataDetailReservationImported!.productCode == null
                                                ? ""
                                                : _dataDetailReservationImported!.productCode.toString(),
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Tên NVL:",
                                            textCL2: _dataDetailReservationImported!.productName == null
                                                ? ""
                                                : _dataDetailReservationImported!.productName.toString(),
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Số lô:",
                                            textCL2: _dataDetailReservationImported!.batchNumber ?? "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Số lượng chuyển:",
                                            textCL2: _dataDetailReservationImported!.sumQuantityImported == null
                                                ? ""
                                                : _dataDetailReservationImported!.sumQuantityImported!.toStringAsFixed(2),
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "IV. SỐ LƯỢNG CHUYỂN KHO CHI TIẾT",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.only(left: 3.w),
                                            child: SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Table(
                                                    border: TableBorder.all(width: 0.5.w),
                                                    columnWidths: <int, TableColumnWidth>{
                                                      0: FixedColumnWidth(100.w),
                                                      1: FixedColumnWidth(100.w),
                                                      2: FixedColumnWidth(100.w),
                                                      3: FixedColumnWidth(70.w),
                                                      4: FixedColumnWidth(70.w),
                                                      5: FixedColumnWidth(70.w),
                                                    },
                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                    children: const <TableRow>[
                                                      TableRow(
                                                        decoration: BoxDecoration(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                        children: <Widget>[
                                                          _TitleTableIV(text: "SO/SOLine"),
                                                          _TitleTableIV(text: "WBS"),
                                                          _TitleTableIV(text: "LSX Đại trà"),
                                                          _TitleTableIV(text: "Số lượng chuyển"),
                                                          _TitleTableIV(text: "SL tồn"),
                                                          _TitleTableIV(text: "ĐVT"),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    children: List.generate(
                                                      (_dataDetailReservationImported!.quantityImporteds ?? []).length,
                                                      (index) => Table(
                                                        border: TableBorder(
                                                          left: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          verticalInside: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        ),
                                                        columnWidths: <int, TableColumnWidth>{
                                                          0: FixedColumnWidth(100.w),
                                                          1: FixedColumnWidth(100.w),
                                                          2: FixedColumnWidth(100.w),
                                                          3: FixedColumnWidth(70.w),
                                                          4: FixedColumnWidth(70.w),
                                                          5: FixedColumnWidth(70.w),
                                                        },
                                                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                        children: <TableRow>[
                                                          TableRow(
                                                            children: <Widget>[
                                                              Container(
                                                                  margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  child: (_dataDetailReservationImported!.quantityImporteds![index].so != null &&
                                                                              _dataDetailReservationImported!.quantityImporteds![index].so != "") &&
                                                                          (_dataDetailReservationImported!.quantityImporteds![index].soLine != null &&
                                                                              _dataDetailReservationImported!.quantityImporteds![index].soLine != "")
                                                                      ? Column(
                                                                          children: <Widget>[
                                                                            Text(
                                                                              _dataDetailReservationImported!.quantityImporteds![index].so ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                            Text(
                                                                              _dataDetailReservationImported!.quantityImporteds![index].soLine ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                          ],
                                                                        )
                                                                      : Center(child: Text("Tồn trơn", style: TextStyle(fontSize: 12.sp)))),
                                                              _ColumnTableIV(
                                                                  text: _dataDetailReservationImported!.quantityImporteds![index].wbs ?? ""),
                                                              _ColumnTableIV(
                                                                  text: _dataDetailReservationImported!.quantityImporteds![index].lsxdt ?? ""),
                                                              _ColumnTableIV(
                                                                  text: _dataDetailReservationImported!.quantityImporteds![index].quantityImported!
                                                                      .toStringAsFixed(2)),
                                                              _ColumnTableIV(
                                                                  text: _dataDetailReservationImported!.quantityImporteds![index].quantityStock!
                                                                      .toStringAsFixed(2)),
                                                              _ColumnTableIV(
                                                                  text: _dataDetailReservationImported!.quantityImporteds![index].unit == null
                                                                      ? ""
                                                                      : _dataDetailReservationImported!.quantityImporteds![index].unit.toString()),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                  ],
                                ),
                              ),
              ));
  }
}

class _ColumnTableIV extends StatelessWidget {
  final String text;
  const _ColumnTableIV({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        child: Text(text, style: TextStyle(fontSize: 12.sp), textAlign: TextAlign.center));
  }
}

class _TitleTableIV extends StatelessWidget {
  final String text;
  const _TitleTableIV({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}
