﻿using ISD.API.Core;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class SlocController : ControllerBaseAPI
    {
        #region Tìm kiếm danh sách Sloc
        /// <summary>API Search "Tìm kiếm danh sách Sloc"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/Sloc/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "plant": "test",
        ///                 "slocList": "test",
        ///                 "warehouseNo": "123",
        ///                 "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": [
        ///              {
        ///                "stt": 1,
        ///                "slocId": "74c32f98-c523-4373-8f59-f8682a58001a",
        ///                "plant": "test",
        ///                "slocList": "test",
        ///                "warehouseNo": "123",
        ///                "createTime": "2022-07-15T12:32:43.55",
        ///                "lastEditTime": "2022-07-15T13:06:13.387",
        ///                "actived": true
        ///              }
        ///            ],
        ///            "additionalData": {
        ///              "draw": 1,
        ///              "recordsTotal": 1,
        ///              "recordsFiltered": 1
        ///            }
        ///          }
        /// </remarks>
        [HttpPost("Search")]
        public ActionResult GetSloc([FromBody] SlocSearchViewModel searchVM)
        {
            var querySearch = _unitOfWork.SlocRepository.GetSloc(searchVM.WarehouseNo, searchVM.Sloc, searchVM.Plant, searchVM.ProductionWarehouse);

            //var datatblModel = new DatatableViewModel()
            //{
            //    draw = searchVM.Paging.draw,
            //    start = searchVM.Paging.start,
            //    length = searchVM.Paging.length,
            //};

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = NewCustomSearchRepository.CustomSearchFunc<SlocResultViewModel>(searchVM.Paging, out filteredResultsCount, out totalResultsCount, querySearch, "stt");

            if (res != null && res.Count() > 0)
            {
                int i = searchVM.Paging.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<SlocResultViewModel>>
            {
                Data = res,
                Draw = searchVM.Paging.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        /// <summary>
        /// Update slocList
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("UpdateSloc")]
        public async Task<IActionResult> UpdateSloc([FromBody] UpdateSlocRequest request)
        {
            //Tìm kiếm slocList
            var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == request.Id);

            //Kiểm tra tồn tại
            if (sloc == null)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Sloc") });

            //Cập nhật productionWarehouse
            if (request.ProductionWarehouse == true)
            {
                sloc.ProductionWarehouse = request.ProductionWarehouse;
                    
            }
            else
            {
                sloc.ProductionWarehouse = null;
            }
            _context.SaveChanges();
            var MessagePW = LanguageResource.MessageWS;

            //Kiểm tra defaultStorageBin có bị trùng
            if (!string.IsNullOrEmpty(request.DefaultStorageBin))
            {
                var exist = await _context.SlocModel.FirstOrDefaultAsync(x => x.DefaultStorageBin == request.DefaultStorageBin);
                if (exist != null && exist.Sloc != sloc.Sloc)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 400,
                        IsSuccess = false,
                        Data = MessagePW,
                        Message = LanguageResource.CheckSloc
                    });
                }
                //Cập nhật default StorageBin
                sloc.DefaultStorageBin = request.DefaultStorageBin;
                _context.SaveChanges();
            }
            return Ok(new ApiSuccessResponse<bool>
            {
                Data = true,
                Message = string.Format(CommonResource.Msg_Update_Success, "Sloc")
            });
        }

        /// <summary>
        /// Update slocList
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("GetSloc")]
        public async Task<IActionResult> GetSloc([FromQuery] GetSlocRequest request)
        {
            //Tìm kiếm slocList
            var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == request.Sloc &&
                                                                         x.Plant == request.Plant);

            //Kiểm tra tồn tại
            if (sloc == null)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Sloc") });

            var response = new SlocDetailResponse
            {
                Sloc = sloc.Sloc,
                Plant = sloc.Plant,
                DefaultStorageBin = sloc.DefaultStorageBin,
                WarehouseNo = sloc.WarehouseNo
            };

            _context.SaveChanges();

            return Ok(new ApiResponse
            {
                Data = response,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Get sloc")
            });
        }

        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll([FromQuery] string plant )
        {
            var slocList = await _context.SlocModel.Where(x => (string.IsNullOrEmpty(plant) || x.Plant == plant) && x.Actived == true).OrderBy(s => s.Plant).ThenBy(s => s.Sloc).ToListAsync();

            var response = slocList.Select(x => new SlocDetailResponse
            {
                Id = x.Id,
                Sloc = x.Sloc,
                SlocName = x.SlocName,
                Plant = x.Plant,
                DefaultStorageBin = x.DefaultStorageBin,
                WarehouseNo = x.WarehouseNo,
                // ProductionWarehouse return "true" or "null"
                ProductionWarehouse = x.ProductionWarehouse == true ? "true" : "null"
            }).ToList();

            return Ok(new ApiResponse
            {
                Data = response,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Get sloc")
            });
        }
    }
}