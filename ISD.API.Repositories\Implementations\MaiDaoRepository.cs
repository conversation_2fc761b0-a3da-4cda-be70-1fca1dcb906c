using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ISD.API.EntityModels;
using ISD.API.EntityModels.Models;
using ISD.API.Repositories.Interfaces;
using ISD.API.ViewModels.MaiDao;
using ISD.API.EntityModels.Data;

namespace ISD.API.Repositories.Implementations
{
    public class MaiDaoRepository : IMaiDaoRepository
    {
        private readonly EntityDataContext _context;

        public MaiDaoRepository(EntityDataContext context)
        {
            _context = context;
        }

        public async Task<MaiDaoListResponse> GetMaiDaoListAsync(MaiDaoSearchModel model)
        {
            try
            {
                var query = _context.MaiDaoModel.AsQueryable();
                
                // Log initial query and filter parameters
                var totalRecordsInDb = await _context.MaiDaoModel.CountAsync();
                Console.WriteLine($"[MaiDaoRepository] Total records in database: {totalRecordsInDb}");
                Console.WriteLine($"[MaiDaoRepository] Filter parameters - CompanyCode: {model.CompanyCode}, EquipmentCode: {model.EquipmentCode}, OperationType: {model.OperationType}, Status: {model.Status}, FromDate: {model.FromDate}, ToDate: {model.ToDate}");

                // Apply filters
                if (!string.IsNullOrEmpty(model.CompanyCode))
                {
                    query = query.Where(m => m.CompanyCode == model.CompanyCode);
                    var countAfterCompany = await query.CountAsync();
                    Console.WriteLine($"[MaiDaoRepository] After CompanyCode filter: {countAfterCompany} records");
                }

                if (!string.IsNullOrEmpty(model.EquipmentCode))
                {
                    query = query.Where(m => m.EquipmentCode.Contains(model.EquipmentCode));
                    var countAfterEquipment = await query.CountAsync();
                    Console.WriteLine($"[MaiDaoRepository] After EquipmentCode filter: {countAfterEquipment} records");
                }

                if (!string.IsNullOrEmpty(model.MaterialCode))
                {
                    query = query.Where(m => m.MaterialCode.Contains(model.MaterialCode));
                    var countAfterMaterial = await query.CountAsync();
                    Console.WriteLine($"[MaiDaoRepository] After MaterialCode filter: {countAfterMaterial} records");
                }

                if (!string.IsNullOrEmpty(model.OperationType))
                {
                    query = query.Where(m => m.OperationType == model.OperationType);
                    var countAfterOperationType = await query.CountAsync();
                    Console.WriteLine($"[MaiDaoRepository] After OperationType filter: {countAfterOperationType} records");
                }

                if (!string.IsNullOrEmpty(model.Status))
                {
                    query = query.Where(m => m.Status == model.Status);
                    var countAfterStatus = await query.CountAsync();
                    Console.WriteLine($"[MaiDaoRepository] After Status filter: {countAfterStatus} records");
                }

                // For date filtering, we'll get all records first then filter in memory
                // This is necessary because the Date field is stored as string in dd/MM/yyyy format
                List<MaiDaoModel> allRecords = null;
                
                if (model.FromDate.HasValue || model.ToDate.HasValue)
                {
                    // Get all records that match other criteria first
                    allRecords = await query.ToListAsync();
                    
                    if (model.FromDate.HasValue)
                    {
                        allRecords = allRecords.Where(m => 
                        {
                            if (DateTime.TryParseExact(m.Date, "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out var parsedDate))
                            {
                                return parsedDate >= model.FromDate.Value.Date;
                            }
                            return false;
                        }).ToList();
                    }

                    if (model.ToDate.HasValue)
                    {
                        allRecords = allRecords.Where(m => 
                        {
                            if (DateTime.TryParseExact(m.Date, "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out var parsedDate))
                            {
                                return parsedDate <= model.ToDate.Value.Date;
                            }
                            return false;
                        }).ToList();
                    }
                    
                    Console.WriteLine($"[MaiDaoRepository] After date filtering: {allRecords.Count} records");
                }

                // Get the final dataset
                List<MaiDaoModel> finalRecords;
                if (allRecords != null)
                {
                    finalRecords = allRecords;
                }
                else
                {
                    finalRecords = await query.ToListAsync();
                }

                // Get total count
                var totalCount = finalRecords.Count;

                // Apply pagination and ordering
                var items = finalRecords
                    .OrderByDescending(m => m.CreatedDate)
                    .Skip((model.PageNumber - 1) * model.PageSize)
                    .Take(model.PageSize)
                    .Select(m => new MaiDaoRecordVM
                    {
                        MaiDaoId = m.MaiDaoId,
                        Date = m.Date,
                        EquipmentCode = m.EquipmentCode,
                        EquipmentName = m.EquipmentName,
                        MaterialCode = m.MaterialCode,
                        MaterialName = m.MaterialName,
                        MaterialBatch = m.MaterialBatch,
                        OperationType = m.OperationType,
                        EmployeeCodes = m.EmployeeCodes,
                        EmployeeNames = m.EmployeeNames,
                        RequestingEmployeeCode = m.RequestingEmployeeCode,
                        RequestingEmployeeName = m.RequestingEmployeeName,
                        Note = m.Note,
                        Status = m.Status,
                        CreatedDate = m.CreatedDate,
                        CreateBy = m.CreateBy,
                        UpdatedDate = m.UpdatedDate,
                        UpdateBy = m.UpdateBy,
                        CompanyCode = m.CompanyCode
                    })
                    .ToList();

                // Fetch equipment names for items that don't have them
                var equipmentCodesWithoutNames = items
                    .Where(i => string.IsNullOrEmpty(i.EquipmentName) && !string.IsNullOrEmpty(i.EquipmentCode))
                    .Select(i => i.EquipmentCode)
                    .Distinct()
                    .ToList();

                if (equipmentCodesWithoutNames.Any())
                {
                    // Transform equipment codes to match EquipmentMdModel.EQUNR format
                    var paddedEquipmentCodes = equipmentCodesWithoutNames.Select(code => code.PadLeft(18, '0')).ToList();
                    
                    // Use GroupBy to handle duplicate keys and take the first occurrence
                    var equipmentData = await _context.EquipmentMdModel
                        .Where(e => paddedEquipmentCodes.Contains(e.EQUNR))
                        .Select(e => new { Key = e.EQUNR.TrimStart('0'), Value = e.EQKTX })
                        .ToListAsync();
                    
                    var equipmentNames = equipmentData
                        .GroupBy(e => e.Key)
                        .ToDictionary(g => g.Key, g => g.First().Value);

                    // Update items with equipment names
                    foreach (var item in items.Where(i => string.IsNullOrEmpty(i.EquipmentName)))
                    {
                        if (equipmentNames.ContainsKey(item.EquipmentCode))
                        {
                            item.EquipmentName = equipmentNames[item.EquipmentCode];
                        }
                    }
                }

                // Fetch employee names for items that don't have them
                foreach (var item in items.Where(i => string.IsNullOrEmpty(i.EmployeeNames) && !string.IsNullOrEmpty(i.EmployeeCodes)))
                {
                    var employeeCodes = item.EmployeeCodes.Split(',');
                    var employees = await _context.SalesEmployeeModel
                        .Where(e => employeeCodes.Contains(e.SalesEmployeeCode))
                        .Select(e => new { EmployeeCode = e.SalesEmployeeCode, EmployeeName = e.SalesEmployeeName })
                        .ToListAsync();

                    item.EmployeeNames = string.Join(", ", employees.Select(e => e.EmployeeName));
                }

                // Fetch requesting employee names for items that don't have them
                foreach (var item in items.Where(i => string.IsNullOrEmpty(i.RequestingEmployeeName) && !string.IsNullOrEmpty(i.RequestingEmployeeCode)))
                {
                    var employee = await _context.SalesEmployeeModel
                        .Where(e => e.SalesEmployeeCode == item.RequestingEmployeeCode)
                        .Select(e => e.SalesEmployeeName)
                        .FirstOrDefaultAsync();

                    if (!string.IsNullOrEmpty(employee))
                    {
                        item.RequestingEmployeeName = employee;
                    }
                }

                return new MaiDaoListResponse
                {
                    Data = items,
                    TotalCount = totalCount,
                    Status = true,
                    Message = "Success"
                };
            }
            catch (Exception ex)
            {
                return new MaiDaoListResponse
                {
                    Status = false,
                    Message = $"Error: {ex.Message}"
                };
            }
        }

        public async Task<MaiDaoResponse> GetMaiDaoByIdAsync(Guid maiDaoId)
        {
            try
            {
                var maiDao = await _context.MaiDaoModel
                    .Where(m => m.MaiDaoId == maiDaoId)
                    .FirstOrDefaultAsync();

                if (maiDao == null)
                {
                    return new MaiDaoResponse
                    {
                        Status = false,
                        Message = "MaiDao record not found"
                    };
                }

                var result = new MaiDaoRecordVM
                {
                    MaiDaoId = maiDao.MaiDaoId,
                    Date = maiDao.Date,
                    EquipmentCode = maiDao.EquipmentCode,
                    EquipmentName = maiDao.EquipmentName,
                    MaterialCode = maiDao.MaterialCode,
                    MaterialName = maiDao.MaterialName,
                    MaterialBatch = maiDao.MaterialBatch,
                    OperationType = maiDao.OperationType,
                    EmployeeCodes = maiDao.EmployeeCodes,
                    EmployeeNames = maiDao.EmployeeNames,
                    RequestingEmployeeCode = maiDao.RequestingEmployeeCode,
                    RequestingEmployeeName = maiDao.RequestingEmployeeName,
                    Note = maiDao.Note,
                    Status = maiDao.Status,
                    CreatedDate = maiDao.CreatedDate,
                    CreateBy = maiDao.CreateBy,
                    UpdatedDate = maiDao.UpdatedDate,
                    UpdateBy = maiDao.UpdateBy,
                    CompanyCode = maiDao.CompanyCode
                };

                // Fetch equipment name if not stored
                if (string.IsNullOrEmpty(result.EquipmentName) && !string.IsNullOrEmpty(maiDao.EquipmentCode))
                {
                    var paddedEquipmentCode = maiDao.EquipmentCode.PadLeft(18, '0');
                    var equipment = await _context.EquipmentMdModel
                        .Where(e => e.EQUNR == paddedEquipmentCode)
                        .FirstOrDefaultAsync();

                    if (equipment != null)
                    {
                        result.EquipmentName = equipment.EQKTX;
                    }
                }

                // Fetch employee names if not stored
                if (string.IsNullOrEmpty(result.EmployeeNames) && !string.IsNullOrEmpty(maiDao.EmployeeCodes))
                {
                    var employeeCodes = maiDao.EmployeeCodes.Split(',');
                    var employees = await _context.SalesEmployeeModel
                        .Where(e => employeeCodes.Contains(e.SalesEmployeeCode))
                        .Select(e => new { EmployeeCode = e.SalesEmployeeCode, EmployeeName = e.SalesEmployeeName })
                        .ToListAsync();

                    result.EmployeeNames = string.Join(", ", employees.Select(e => e.EmployeeName));
                }

                // Fetch requesting employee name if not stored
                if (string.IsNullOrEmpty(result.RequestingEmployeeName) && !string.IsNullOrEmpty(maiDao.RequestingEmployeeCode))
                {
                    var employee = await _context.SalesEmployeeModel
                        .Where(e => e.SalesEmployeeCode == maiDao.RequestingEmployeeCode)
                        .Select(e => e.SalesEmployeeName)
                        .FirstOrDefaultAsync();

                    if (!string.IsNullOrEmpty(employee))
                    {
                        result.RequestingEmployeeName = employee;
                    }
                }

                return new MaiDaoResponse
                {
                    Data = result,
                    Status = true,
                    Message = "Success"
                };
            }
            catch (Exception ex)
            {
                return new MaiDaoResponse
                {
                    Status = false,
                    Message = $"Error: {ex.Message}"
                };
            }
        }

        public async Task<MaiDaoResponse> CreateMaiDaoAsync(MaiDaoRecordVM model, Guid userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newMaiDao = new MaiDaoModel
                {
                    MaiDaoId = Guid.NewGuid(),
                    Date = model.Date,
                    EquipmentCode = model.EquipmentCode,
                    EquipmentName = model.EquipmentName,
                    MaterialCode = model.MaterialCode,
                    MaterialName = model.MaterialName,
                    MaterialBatch = model.MaterialBatch,
                    OperationType = model.OperationType,
                    EmployeeCodes = model.EmployeeCodes,
                    EmployeeNames = model.EmployeeNames,
                    RequestingEmployeeCode = model.RequestingEmployeeCode,
                    RequestingEmployeeName = model.RequestingEmployeeName,
                    Note = model.Note,
                    Status = model.Status ?? "Created", // Default status
                    CreatedDate = DateTime.Now,
                    CreateBy = userId,
                    CompanyCode = model.CompanyCode
                };

                // Add initial history record
                var initialHistory = new MaiDaoHistoryModel
                {
                    HistoryId = Guid.NewGuid(),
                    MaiDaoId = newMaiDao.MaiDaoId,
                    Action = "CREATE",
                    NewStatus = newMaiDao.Status,
                    ChangedBy = userId.ToString(),
                    ChangedDate = DateTime.Now
                };

                await _context.MaiDaoModel.AddAsync(newMaiDao);
                await _context.MaiDaoHistoryModel.AddAsync(initialHistory);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return new MaiDaoResponse
                {
                    Data = new MaiDaoRecordVM
                    {
                        MaiDaoId = newMaiDao.MaiDaoId,
                        Date = newMaiDao.Date,
                        EquipmentCode = newMaiDao.EquipmentCode,
                        EquipmentName = newMaiDao.EquipmentName,
                        MaterialCode = newMaiDao.MaterialCode,
                        MaterialName = newMaiDao.MaterialName,
                        MaterialBatch = newMaiDao.MaterialBatch,
                        OperationType = newMaiDao.OperationType,
                        EmployeeCodes = newMaiDao.EmployeeCodes,
                        EmployeeNames = newMaiDao.EmployeeNames,
                        RequestingEmployeeCode = newMaiDao.RequestingEmployeeCode,
                        RequestingEmployeeName = newMaiDao.RequestingEmployeeName,
                        Note = newMaiDao.Note,
                        Status = newMaiDao.Status,
                        CreatedDate = newMaiDao.CreatedDate,
                        CreateBy = newMaiDao.CreateBy,
                        CompanyCode = newMaiDao.CompanyCode
                    },
                    Status = true,
                    Message = "MaiDao record created successfully"
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return new MaiDaoResponse
                {
                    Status = false,
                    Message = $"Error creating MaiDao record: {ex.Message}"
                };
            }
        }

        public async Task<MaiDaoResponse> UpdateMaiDaoAsync(Guid maiDaoId, MaiDaoRecordVM model, Guid userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var maiDao = await _context.MaiDaoModel
                    .Include(m => m.MaiDaoHistoryModel)
                    .Where(m => m.MaiDaoId == maiDaoId)
                    .FirstOrDefaultAsync();

                if (maiDao == null)
                {
                    return new MaiDaoResponse
                    {
                        Status = false,
                        Message = "MaiDao record not found"
                    };
                }

                // Track status change if it's different
                var oldStatus = maiDao.Status;
                var hasStatusChanged = !string.IsNullOrEmpty(model.Status) && model.Status != oldStatus;

                maiDao.Date = model.Date;
                maiDao.EquipmentCode = model.EquipmentCode;
                maiDao.EquipmentName = model.EquipmentName;
                maiDao.MaterialCode = model.MaterialCode;
                maiDao.MaterialName = model.MaterialName;
                maiDao.MaterialBatch = model.MaterialBatch;
                maiDao.OperationType = model.OperationType;
                maiDao.EmployeeCodes = model.EmployeeCodes;
                maiDao.EmployeeNames = model.EmployeeNames;
                maiDao.RequestingEmployeeCode = model.RequestingEmployeeCode;
                maiDao.RequestingEmployeeName = model.RequestingEmployeeName;
                maiDao.Note = model.Note;
                maiDao.Status = model.Status;
                maiDao.UpdatedDate = DateTime.Now;
                maiDao.UpdateBy = userId;

                // Add status change history if status changed
                if (hasStatusChanged)
                {
                    var statusHistory = new MaiDaoHistoryModel
                    {
                        HistoryId = Guid.NewGuid(),
                        MaiDaoId = maiDao.MaiDaoId,
                        Action = "STATUS_CHANGE",
                        OldStatus = oldStatus,
                        NewStatus = model.Status,
                        ChangedBy = userId.ToString(),
                        ChangedDate = DateTime.Now
                    };

                    await _context.MaiDaoHistoryModel.AddAsync(statusHistory);
                }
                else
                {
                    // Add general update history if no status change
                    var updateHistory = new MaiDaoHistoryModel
                    {
                        HistoryId = Guid.NewGuid(),
                        MaiDaoId = maiDao.MaiDaoId,
                        Action = "UPDATE",
                        NewStatus = maiDao.Status,
                        ChangedBy = userId.ToString(),
                        ChangedDate = DateTime.Now
                    };

                    await _context.MaiDaoHistoryModel.AddAsync(updateHistory);
                }

                _context.MaiDaoModel.Update(maiDao);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return new MaiDaoResponse
                {
                    Data = new MaiDaoRecordVM
                    {
                        MaiDaoId = maiDao.MaiDaoId,
                        Date = maiDao.Date,
                        EquipmentCode = maiDao.EquipmentCode,
                        EquipmentName = maiDao.EquipmentName,
                        MaterialCode = maiDao.MaterialCode,
                        MaterialName = maiDao.MaterialName,
                        MaterialBatch = maiDao.MaterialBatch,
                        OperationType = maiDao.OperationType,
                        EmployeeCodes = maiDao.EmployeeCodes,
                        EmployeeNames = maiDao.EmployeeNames,
                        RequestingEmployeeCode = maiDao.RequestingEmployeeCode,
                        RequestingEmployeeName = maiDao.RequestingEmployeeName,
                        Note = maiDao.Note,
                        Status = maiDao.Status,
                        CreatedDate = maiDao.CreatedDate,
                        CreateBy = maiDao.CreateBy,
                        UpdatedDate = maiDao.UpdatedDate,
                        UpdateBy = maiDao.UpdateBy,
                        CompanyCode = maiDao.CompanyCode
                    },
                    Status = true,
                    Message = "MaiDao record updated successfully"
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return new MaiDaoResponse
                {
                    Status = false,
                    Message = $"Error updating MaiDao record: {ex.Message}"
                };
            }
        }

        public async Task<MaiDaoRecordVM> CheckExistingRecordAsync(string equipmentCode, string companyCode)
        {
            try
            {
                // Look for incomplete records (not completed or cancelled) for the given equipment
                var existingRecord = await _context.MaiDaoModel
                    .Where(m => m.EquipmentCode == equipmentCode 
                               && m.CompanyCode == companyCode
                               && (m.Status == "Created" || m.Status == "Confirmed" || string.IsNullOrEmpty(m.Status)))
                    .OrderByDescending(m => m.CreatedDate)
                    .FirstOrDefaultAsync();

                if (existingRecord == null)
                {
                    return null;
                }

                var result = new MaiDaoRecordVM
                {
                    MaiDaoId = existingRecord.MaiDaoId,
                    Date = existingRecord.Date,
                    EquipmentCode = existingRecord.EquipmentCode,
                    EquipmentName = existingRecord.EquipmentName,
                    MaterialCode = existingRecord.MaterialCode,
                    MaterialName = existingRecord.MaterialName,
                    MaterialBatch = existingRecord.MaterialBatch,
                    OperationType = existingRecord.OperationType,
                    EmployeeCodes = existingRecord.EmployeeCodes,
                    EmployeeNames = existingRecord.EmployeeNames,
                    RequestingEmployeeCode = existingRecord.RequestingEmployeeCode,
                    RequestingEmployeeName = existingRecord.RequestingEmployeeName,
                    Note = existingRecord.Note,
                    Status = existingRecord.Status,
                    CreatedDate = existingRecord.CreatedDate,
                    CreateBy = existingRecord.CreateBy,
                    UpdatedDate = existingRecord.UpdatedDate,
                    UpdateBy = existingRecord.UpdateBy,
                    CompanyCode = existingRecord.CompanyCode
                };

                // Fetch equipment name if not stored
                if (string.IsNullOrEmpty(result.EquipmentName))
                {
                    var paddedEquipmentCode = equipmentCode.PadLeft(18, '0');
                    var equipment = await _context.EquipmentMdModel
                        .Where(e => e.EQUNR == paddedEquipmentCode)
                        .FirstOrDefaultAsync();

                    if (equipment != null)
                    {
                        result.EquipmentName = equipment.EQKTX;
                    }
                }

                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<List<EquipmentItem>> GetEquipmentAsync(string companyCode, string searchTerm)
        {
            try
            {
                var query = _context.EquipmentMdModel.AsQueryable();

                searchTerm = searchTerm.Trim();

                // Apply company code filter 
                if (!string.IsNullOrEmpty(companyCode))
                {
                    query = query.Where(e => e.IWERK == companyCode);
                }

                // Apply search term filter
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    // Create padded search term for EQUNR field
                    //var paddedSearchTerm = searchTerm.PadLeft(18, '0');
                    
                    query = query.Where(e => 
                        e.EQUNR.Contains(searchTerm)
                        || e.EQKTX.Contains(searchTerm)
                    );
                }

                // Get and return the equipment items
                var equipmentData = await query
                    .Select(e => new { 
                        EQUNR = e.EQUNR, 
                        EQKTX = e.EQKTX 
                    })
                    .Distinct()
                    .ToListAsync();

                return equipmentData
                    .Select(e => new EquipmentItem
                    {
                        EquipmentCode = e.EQUNR.TrimStart('0'), // Trim leading zeros for display
                        EquipmentName = e.EQKTX
                    })
                    .Distinct()
                    .OrderBy(e => e.EquipmentCode)
                    .Take(20)
                    .ToList();
            }
            catch (Exception)
            {
                return new List<EquipmentItem>();
            }
        }

        public async Task<List<MaterialItem>> GetMaterialsAsync(string companyCode, string searchTerm)
        {
            try
            {
                var query = _context.ProductLatestModel.AsQueryable();

                //// Apply company code filter
                //if (!string.IsNullOrEmpty(companyCode))
                //{
                //    query = query.Where(m => m.IWERK == companyCode);
                //}

                // Apply search term filter (case-insensitive)
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var searchTermLower = searchTerm.ToLower();
                    query = query.Where(m => 
                        (m.ProductCode != null && m.ProductCode.ToLower().Contains(searchTermLower)) || 
                        (m.ProductName != null && m.ProductName.ToLower().Contains(searchTermLower))
                    );
                }

                // Get and return the material items with ordering
                return await query
                    .Where(m => m.ProductCode != null) // Ensure ProductCode is not null
                    .OrderBy(m => m.ProductCode)
                    .Select(m => new MaterialItem
                    {
                        MaterialCode = m.ProductCode,
                        MaterialName = m.ProductName ?? ""
                    })
                    .Take(20)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<MaterialItem>();
            }
        }

        public async Task<List<EmployeeItem>> GetEmployeesAsync(string companyCode)
        {
            try
            {
                var query = _context.SalesEmployeeModel.AsQueryable();

                // Apply company code filter if needed
                if (!string.IsNullOrEmpty(companyCode))
                {
                    // Assuming there's a company field in SalesEmployeeModel
                    // If not, you might need to adjust this filter or remove it
                    // query = query.Where(e => e.CompanyCode == companyCode);
                }

                return await query
                    .Where(e => !string.IsNullOrEmpty(e.SalesEmployeeCode) && !string.IsNullOrEmpty(e.SalesEmployeeName))
                    .OrderBy(e => e.SalesEmployeeCode)
                    .Select(e => new EmployeeItem
                    {
                        EmployeeCode = e.SalesEmployeeCode,
                        EmployeeName = e.SalesEmployeeName
                    })
                    .Take(100)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<EmployeeItem>();
            }
        }

        public async Task<List<MaiDaoHistoryModel>> GetMaiDaoHistoryAsync(Guid maiDaoId)
        {
            try
            {
                return await _context.MaiDaoHistoryModel
                    .Where(h => h.MaiDaoId == maiDaoId)
                    .OrderByDescending(h => h.ChangedDate)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<MaiDaoHistoryModel>();
            }
        }
    }
} 