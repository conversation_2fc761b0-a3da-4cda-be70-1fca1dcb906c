﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("QualityControlDetailModel", Schema = "MES")]
    [Index("QualityControlId", Name = "IX_QualityControlDetailModel_QualityControlId")]
    public partial class QualityControlDetailModel
    {
        [Key]
        public Guid QualityControlDetailId { get; set; }
        public Guid? QualityControlId { get; set; }
        [StringLength(50)]
        public string TestMethod { get; set; }
        [StringLength(50)]
        public string SamplingLevel { get; set; }
        [StringLength(1000)]
        public string AcceptableLevel { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? InspectionQuantity { get; set; }
        [StringLength(50)]
        public string Result { get; set; }
        [StringLength(50)]
        public string LimitCritical { get; set; }
        [StringLength(50)]
        public string LimitHigh { get; set; }
        [StringLength(50)]
        public string LimitLow { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? TongSoSanPhamLoi { get; set; }
        [StringLength(50)]
        public string QuanDocCode { get; set; }
        [StringLength(50)]
        public string ToTruongCode { get; set; }
        [StringLength(50)]
        public string QAQCCode { get; set; }
        [StringLength(50)]
        public string KCSCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? QualityDate { get; set; }
        public Guid? QualityChecker { get; set; }
        [StringLength(100)]
        public string QualityType { get; set; }
        public int? LoiNangChapNhan { get; set; }
        public int? LoiNheChapNhan { get; set; }
        [StringLength(50)]
        public string StepCode { get; set; }
        public int? CheckingTimes { get; set; }
        [StringLength(100)]
        public string Barcode { get; set; }
    }
}