﻿using ISD.API.ViewModels;
using ISD.API.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.AspNet.Identity;

namespace ISD.Middlewares
{
    public class JwtMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly JwtSettings _appSettings;

        public JwtMiddleware(RequestDelegate next, IOptions<JwtSettings> appSettings)
        {
            _next = next;
            _appSettings = appSettings.Value;
        }

        public async Task Invoke(HttpContext context)
        {
            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
            var tokenType = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").First();

            if (token != null && tokenType != "Basic")
            {
                try 
                {
                    attachUserToContext(context, token);
                }
                catch (Exception)
                {
                    // Optionally log the error
                    // Don't throw the exception to allow the request to continue
                    // The user will simply be unauthenticated
                }
            }

            await _next(context);
        }

        private void attachUserToContext(HttpContext context, string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_appSettings.IssuerSigningKey);
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                ClockSkew = TimeSpan.Zero,
            }, out SecurityToken validatedToken);

            var jwtToken = (JwtSecurityToken)validatedToken;
            var userId = Guid.Parse(jwtToken.Claims.First(x => x.Type == "Id").Value);
            var identity = new ClaimsIdentity(jwtToken.Claims, DefaultAuthenticationTypes.ExternalBearer);
            var user = new ClaimsPrincipal(identity);
            context.User = user;
            context.Items["CurrentUser"] = new AppUserPrincipal(context.User as ClaimsPrincipal);
            context.Items["Permission"] = JsonConvert.DeserializeObject<PermissionMobileViewModel>(jwtToken.Claims.First(x => x.Type == "Permission").Value);
            // Attach the user role to the context
            var userRole = jwtToken.Claims.First(x => x.Type == "Role").Value;
            context.Items["Role"] = userRole;
        }
    }
}
