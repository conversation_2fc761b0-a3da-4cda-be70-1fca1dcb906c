﻿using Azure.Core;
using ISD.API.Constant.Config;
using ISD.API.EntityModels;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver;
using System.Collections.Generic;
using System.IO;
using System;
using System.Linq;
using System.Threading;
using ISD.API.EntityModels.Models;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    public class LogController : ControllerBase
    {
        #region Tìm kiếm danh sách Log
        /// <summary>API Search "Tìm kiếm danh sách Log"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/StorageBin/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "plant": "test",
        ///                 "sloc": "test",
        ///                 "warehouseNo": "123",
        ///                 "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": [
        ///              {
        ///                "stt": 1,
        ///                "storageBinId": "74c32f98-c523-4373-8f59-f8682a58001a",
        ///                "plant": "test",
        ///                "sloc": "test",
        ///                "warehouseNo": "123",
        ///                "createTime": "2022-07-15T12:32:43.55",
        ///                "lastEditTime": "2022-07-15T13:06:13.387",
        ///                "actived": true
        ///              }
        ///            ],
        ///            "additionalData": {
        ///              "draw": 1,
        ///              "recordsTotal": 1,
        ///              "recordsFiltered": 1
        ///            }
        ///          }
        /// </remarks>
        [HttpGet("Search")]
        public ActionResult GetLog([FromQuery] LogSearchViewModel vm)
        {
            //Get connection string
            var settings = MongoClientSettings.FromConnectionString(MongoDbConfig.ConnectionString);
            settings.ServerApi = new ServerApi(ServerApiVersion.V1);
            var client = new MongoClient(settings);

            //Get database 
            var database = client.GetDatabase(MongoDbConfig.DatabaseName);

            //Get collection
            var log = database?.GetCollection<ExternalLogModel>(MongoDbConfig.CollectionName);

            var data = log.AsQueryable().Take(vm.Length);

            if (vm.CreatedTime.HasValue)
                data.Where(x => x.CreatedTime.Date == vm.CreatedTime.Value.Date);

            if (!string.IsNullOrEmpty(vm.Method))
                data.Where(x => x.Method == vm.Method);

            if (!string.IsNullOrEmpty(vm.CreatedBy))
                data.Where(x => x.CreatedBy == vm.CreatedBy);

            var querySearch = data.Select(e => new LogResultViewModel
                                               {
                                                   Response = e.Response,
                                                   Request = e.Request,
                                                   CreatedTime = e.CreatedTime,
                                                   CreatedBy = e.CreatedBy,
                                                   Method = e.Method,
                                                   Url = e.Url,
                                                   Id = e.Id
                                               }).ToList();
            
            if (querySearch.Any())
            {
                int i = 0;
                foreach (var item in querySearch)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = querySearch                
            });
        }
        #endregion  
    }
}
