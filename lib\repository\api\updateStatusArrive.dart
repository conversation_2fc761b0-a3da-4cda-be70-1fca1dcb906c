import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';

class UpdateStatusArriveApi {
  static Future<http.Response> updateStatusArrive(String rawMaterialCardId, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "update-status-arrive");
    Map<String, String> dataPut = {
      'rawMaterialCardId': rawMaterialCardId,
    };
    debugPrint(url.toString());
    final response = await http.put(
      url,
      headers: UrlApi.headersToken(token),
      body: jsonEncode(dataPut),
    );
    debugPrint(response.body);
    debugPrint(dataPut.toString());
    return response;
  }
}
