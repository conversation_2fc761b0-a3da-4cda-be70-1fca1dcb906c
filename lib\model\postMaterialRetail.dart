class PostMaterialRetail {
  String? taskId;
  List<MaterialRetailDetails>? materialRetailDetails;

  PostMaterialRetail({this.taskId, this.materialRetailDetails});

  PostMaterialRetail.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'];
    if (json['materialRetailDetails'] != null) {
      materialRetailDetails = <MaterialRetailDetails>[];
      json['materialRetailDetails'].forEach((v) {
        materialRetailDetails!.add(MaterialRetailDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['taskId'] = taskId;
    if (materialRetailDetails != null) {
      data['materialRetailDetails'] =
          materialRetailDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MaterialRetailDetails {
  String? slocId;
  String? so;
  String? soLine;
  String? wbs;
  String? stepCode;
  String? batchNumber;
  String? rawMaterialCardId;
  double? quantityUsed;
  String? unitQuantityUsed;
  double? quantity;
  String? unit;

  MaterialRetailDetails(
      {this.slocId,
        this.so,
        this.soLine,
        this.wbs,
        this.stepCode,
        this.batchNumber,
        this.rawMaterialCardId,
        this.quantityUsed,
        this.unitQuantityUsed,
        this.quantity,
        this.unit});

  MaterialRetailDetails.fromJson(Map<String, dynamic> json) {
    slocId = json['slocId'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    stepCode = json['stepCode'];
    batchNumber = json['batchNumber'];
    rawMaterialCardId = json['rawMaterialCardId'];
    quantityUsed = json['quantityUsed'];
    unitQuantityUsed = json['unitQuantityUsed'];
    quantity = json['quantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['slocId'] = slocId;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] =wbs;
    data['stepCode'] = stepCode;
    data['batchNumber'] = batchNumber;
    data['rawMaterialCardId'] = rawMaterialCardId;
    data['quantityUsed'] = quantityUsed;
    data['unitQuantityUsed'] = unitQuantityUsed;
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}
class MessagePostMaterialRetail {
  int? code;
  bool? isSuccess;
  String? message;
  bool? data;


  MessagePostMaterialRetail(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  MessagePostMaterialRetail.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = this.data;
    return data;
  }
}