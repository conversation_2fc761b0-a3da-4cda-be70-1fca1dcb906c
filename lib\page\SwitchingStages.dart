import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/switchingStagesApi.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/RowDetail.dart';
import '../repository/function/switchingStagesFunction.dart';

class SwitchingStage extends StatefulWidget {
  final DataSwitchingStages? dataSwitchingStages;
  final String token;
  final SwitchingStages? switchingStages;
  final String dateTimeOld;
  final String toBardCode;

  const SwitchingStage(
      {Key? key,
      required this.dataSwitchingStages,
      required this.token,
      required this.switchingStages,
      required this.dateTimeOld,
      required this.toBardCode})
      : super(key: key);

  @override
  _SwitchingStageState createState() => _SwitchingStageState();
}

class _SwitchingStageState extends State<SwitchingStage> {
  AdditionalData? _selectedDropdown;

  bool _error = false;

  List<AdditionalData> _getAdditionalData = [];
  String _getCreateTime = " ";
  @override
  void initState() {
    super.initState();
    _setDefaultValue();
  }

  void _setDefaultValue() {
    if (widget.dataSwitchingStages != null) {
      _getCreateTime = SwitchingStagesFunction.getCreateTime(widget.dataSwitchingStages!);
    }
    _getAdditionalData = SwitchingStagesFunction.getAdditionalData(widget.switchingStages);
  }

  void setError() {
    setState(() {
      if (_selectedDropdown == null || (_selectedDropdown != null && _selectedDropdown!.arbpLSUB == " ")) {
        _error = true;
      } else {
        _error = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, false);
          return false;
        },
        child: Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context, false);
                },
              ),
              title: Text(
                'Chuyển Công Đoạn',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  Container(
                      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
                      decoration: const BoxDecoration(color: Colors.white),
                      child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                        LabeledDetailRow(title: 'LSX ĐT:', text: widget.dataSwitchingStages?.productionOrder ?? ""),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(title: 'Đợt SX:', text: widget.dataSwitchingStages?.summary ?? ""),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(title: 'LSX SAP:', text: widget.dataSwitchingStages?.productionOrderSAP ?? ""),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(
                            title: 'Sản phẩm:',
                            text:
                                '(${widget.dataSwitchingStages?.qty ?? ""} ${widget.dataSwitchingStages?.unit ?? ""}) ${widget.dataSwitchingStages?.productCode ?? ""} | ${widget.dataSwitchingStages?.productName ?? ""}'),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(
                            title: 'Chi tiết:',
                            text:
                                '(${(widget.dataSwitchingStages?.productAttributesQtyD ?? 0.0).round().toString()}/${(widget.dataSwitchingStages?.productAttributesQty ?? 0.0).round().toString()} ${widget.dataSwitchingStages?.productAttributesUnit ?? ""}) ${widget.dataSwitchingStages?.productAttributes ?? ""} | ${widget.dataSwitchingStages?.productAttributesName ?? ""} (${widget.dataSwitchingStages?.poT12 ?? ""})'),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(
                            title: 'CĐ hoàn tất:',
                            text: '${widget.dataSwitchingStages?.fromStepCode ?? ""} | ${widget.dataSwitchingStages?.fromStepName ?? ""}'),
                        SizedBox(
                          height: 15.h,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'CĐ kế tiếp:',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Column(children: <Widget>[
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade200),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<AdditionalData>(
                                        isExpanded: true,
                                        isDense: true,
                                        itemHeight: null,
                                        value: _selectedDropdown ?? SwitchingStagesFunction.defaultValueAdditionalData,
                                        iconSize: 15.sp,
                                        style: const TextStyle(color: Colors.black),
                                        onChanged: (AdditionalData? value) async {
                                          setState(() {
                                            _selectedDropdown = value!;
                                          });
                                        },
                                        items: _getAdditionalData.map((AdditionalData option) {
                                          return DropdownMenuItem<AdditionalData>(
                                            value: option,
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                              child: Text(
                                                option.display.toString(),
                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                        selectedItemBuilder: (BuildContext context) {
                                          return _getAdditionalData.map<Widget>((AdditionalData option) {
                                            return Text(option.display.toString(),
                                                style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                          }).toList();
                                        },
                                      ),
                                    ),
                                  ),
                                  //     Container(
                                  //   // height: 40,
                                  //   decoration: BoxDecoration(
                                  //       color: Colors.white,
                                  //       border: Border.all(
                                  //           color:  _error == true
                                  //           ? const Color(0xFFD32F2F)
                                  //           : Colors.grey.shade200)),
                                  //   child: DropdownButtonHideUnderline(
                                  //     child: DropdownButton<AdditionalData>(
                                  //       icon: const Icon(Icons.arrow_drop_down),
                                  //       isExpanded: true,
                                  //       iconSize: 22.sp,
                                  //       value: _selectedDropdown ?? SwitchingStagesFunction.defaultValueAdditionalData,
                                  //       style: const TextStyle(color: Colors.black),
                                  //       onChanged: (AdditionalData? value) async {
                                  //         setState(() {
                                  //           _selectedDropdown = value!;
                                  //         });
                                  //       },
                                  //       items: _getAdditionalData.map((AdditionalData option) {
                                  //         return DropdownMenuItem<AdditionalData>(
                                  //             value: option,
                                  //             child: Padding(
                                  //                 padding: EdgeInsets.only(left: 15.w),
                                  //                 child: Center( child: Text(
                                  //                   option.display.toString(),
                                  //                   style: TextStyle(
                                  //                       color: Colors.black,
                                  //                       fontSize: 12.sp),
                                  //                 ))));
                                  //       }).toList(),
                                  //     ),
                                  //   ),
                                  // ),
                                  SizedBox(height: _error == true ? 10.h : 0),
                                  Visibility(
                                      visible: _error,
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: <Widget>[
                                          Flexible(
                                            flex: 1,
                                            child: Icon(Icons.error_outline, size: 12.sp, color: Colors.red[700]),
                                          ),
                                          SizedBox(width: 5.w),
                                          Flexible(
                                              flex: 8,
                                              child: Text("Bạn chưa chọn CĐ kế tiếp", style: TextStyle(fontSize: 12.sp, color: Colors.red[700])))
                                        ],
                                      )),
                                ]),
                              ),
                            )
                          ],
                        ),
                        SizedBox(height: (widget.dataSwitchingStages?.listDetail ?? []).isNotEmpty ? 25.h : 0),
                        Visibility(
                          visible: (widget.dataSwitchingStages?.listDetail ?? []).isNotEmpty ? true : false,
                          child: Text(
                            'Dữ liểu chuyển:',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(height: (widget.dataSwitchingStages?.listDetail ?? []).isNotEmpty ? 5.h : 0),
                        Visibility(
                          visible: (widget.dataSwitchingStages?.listDetail ?? []).isNotEmpty ? true : false,
                          child: ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: (widget.dataSwitchingStages?.listDetail ?? []).length,
                              itemBuilder: (context, index) {
                                final item = (widget.dataSwitchingStages?.listDetail ?? [])[index];
                                return Table(
                                  border: TableBorder.all(color: Colors.grey.shade400, width: 0.5),
                                  columnWidths: const <int, TableColumnWidth>{
                                    0: FractionColumnWidth(0.1),
                                    1: FractionColumnWidth(0.5),
                                    2: FractionColumnWidth(0.4),
                                  },
                                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                  children: <TableRow>[
                                    TableRow(
                                      children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                          child: Center(
                                            child: Text(
                                              (index + 1).toString(),
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                          child: Text(
                                            item.ktext.toString(),
                                            style: TextStyle(fontSize: 12.sp),
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                          child: Text(
                                            ((item.quantity ?? 0.0) < 1.0
                                                    ? (item.quantity ?? 0.0).toStringAsFixed(3)
                                                    : (item.quantity ?? 0.0).round().toString()) +
                                                ' Đ',
                                            style: TextStyle(fontSize: 12.sp),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                );
                              }),
                        ),
                        SizedBox(
                          height: 25.h,
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            "NV Tạo: ${widget.dataSwitchingStages?.createByFullName ?? " "} - $_getCreateTime",
                            style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                          ),
                        ),
                        const Divider(),
                        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
                          Container(
                            decoration: const BoxDecoration(),
                            child: ElevatedButton(
                              style: ButtonStyle(
                                shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xffeceff1)))),
                                side: MaterialStateProperty.all(
                                  const BorderSide(
                                    color: Color(0xffeceff1),
                                  ),
                                ),
                                backgroundColor: MaterialStateProperty.all(const Color(0xffeceff1)),
                              ),
                              onPressed: () {
                                Navigator.pop(context, false);
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(vertical: 10.h),
                                child: Text(
                                  'Đóng',
                                  style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                ),
                              ),
                            ),
                          ),
                          Row(children: <Widget>[
                            Container(
                              decoration: const BoxDecoration(),
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xff0052cc)))),
                                  side: MaterialStateProperty.all(
                                    const BorderSide(
                                      color: Color(0xff0052cc),
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                ),
                                onPressed: () {
                                  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                  if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                    Platform.isAndroid
                                        ? showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                        : showCupertinoDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                  } else {
                                    setError();
                                    if (_error == false) {
                                      SwitchingStagesFunction.postSwitchingSages(context, widget.toBardCode, _selectedDropdown!.arbpLSUB.toString(),
                                          widget.dataSwitchingStages!, widget.token);
                                    }
                                  }
                                },
                                child: Container(
                                  margin: EdgeInsets.symmetric(vertical: 10.h),
                                  child: Text(
                                    'Chuyển',
                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                  ),
                                ),
                              ),
                            ),
                          ])
                        ])
                      ]))
                ],
              ),
            )));
  }
}
