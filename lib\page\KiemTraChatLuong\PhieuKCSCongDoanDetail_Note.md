# QC Production Step Detail (Phiếu KCS Công Đ<PERSON>ạn) - Technical Documentation

## 1. System Overview

### ✨ What it does
The QC Production Step Detail system manages quality control processes for specific production steps. It handles the complete workflow from production recording to QC inspection, ensuring quality checks are performed at the right stages of manufacturing.

### 🔧 Key Components
- **Production Recording**: Workers scan hang tags to record production output
- **QC Data Creation**: Automatic QualityControlModel creation/update during production
- **QC Inspection**: QC workers scan hang tags and select production steps for inspection
- **Step-specific QC**: Detailed quality control checks for individual production steps

---

## 2. Data Flow Architecture

### 🏗️ Core Entities

#### QualityControlModel (Master QC Record)
```csharp
Key Fields:
- QualityControlId: Guid (Primary Key)
- ProductAttribute: string (e.g., "0101.9.3.2" - Product detail/variant)
- StepCode: string (e.g., "CAT", "TKV", "EPV" - Production step code)
- CustomerReference: Guid (TaskId of ThucThiLenhSanXuatModel)
- LSXSAP: string (Production Order SAP)
- LSXDT: string (Production Order Internal)
- SaleOrgCode: string (Plant/Company Code)
- WorkShopCode: string (Workshop Code)
- Status: bool (false = Pending QC, true = QC Complete)
- ConfirmDate: DateTime (Production confirmation time)
```

#### QualityControlDetailModel (Specific QC Checks)
```csharp
Key Fields:
- QualityControlDetailId: Guid
- QualityControlId: Guid (FK to QualityControlModel)
- QualityChecker: Guid (QC Worker AccountId)
- QualityDate: DateTime (QC Inspection time)
- TestMethod: string (Inspection method)
- Result: string (Pass/Fail result)
- StepCode: string (Specific production step)
```

#### ThucThiLenhSanXuatModel (Production Execution Task)
```csharp
Key Fields:
- TaskId: Guid (Links to QualityControlModel.CustomerReference)
- ParentTaskId: Guid (Links to main production order)
- Barcode: string (Hang tag barcode)
- ProductAttributes: string (Product variant)
- ProductCode: string
- ProductName: string
```

---

## 3. Production Recording Flow

### 📍 Entry Point
**Location**: `iMES_API/Areas/MES/Controllers/ProductionRecordingController.cs` (Lines: 1056-1069)

### 🔄 Execution Flow

#### 3.1. Production Worker Scans Hang Tag
```csharp
// When worker scans hang tag for first time
TaskId = _unitOfWork.ProductionManagementRepository.CreateNewExecutionTask(
    handTag.CustomerReference.Value, 
    Barcode, 
    CurrentUserId: CurrentUser.AccountId
);
```

#### 3.2. QualityControlModel Creation/Update
```csharp
var confirmWorkCenterViewModel = new ConfirmWorkCenterViewModel {
    TaskId = productionOrderViewModel.TaskId,
    DepartmentId = productionOrderViewModel.DepartmentId,
    StepCode = productionOrderViewModel.StepCode,
    WorkCenterConfirmTime = currentDate
};

_unitOfWork.QualityControlRepository.CreateQualityControl2(confirmWorkCenterViewModel);
```

### ⚙️ CreateQualityControl2 Logic
**Location**: `ISD.API.Repositories/MES/QualityControlRepository.cs` (Lines: 1308-1431)

#### 3.2.1. Data Retrieval
```csharp
// Get execution task info
var ttlxs = new ProductionManagementRepository(_context)
    .GetExecutionTaskByTaskId(confirmWorkCenterViewModel.TaskId);

// Get environmental conditions from SAP
var Environmental = _context.SOTextHeader100Model
    .Where(x => x.SO == ttlxs.Property1 && x.TEXT_ID == "H022")
    .Select(x => x.LONGTEXT)
    .FirstOrDefault();
```

#### 3.2.2. Department & Plant Resolution
```csharp
// Get WorkShop from Department hierarchy
string WorkShopCode = null;
var DepartmentId = confirmWorkCenterViewModel.DepartmentId;
if (DepartmentId != null) {
    var WorkShopId = _context.DepartmentModel
        .Where(x => x.DepartmentId == DepartmentId)
        .FirstOrDefault().WorkShopId;
    
    if (WorkShopId != null) {
        WorkShopCode = _context.WorkShopModel
            .Where(x => x.WorkShopId == WorkShopId)
            .FirstOrDefault().WorkShopCode;
    }
}

// Get Plant/Company Code
var SaleOrgCode = (from p in _context.TaskModel
                   join c in _context.CompanyModel on p.CompanyId equals c.CompanyId
                   where p.TaskId == ttlxs.ParentTaskId
                   select c.CompanyCode).FirstOrDefault();
```

#### 3.2.3. QualityControlModel Creation/Update
```csharp
var data = new QualityControlModel() {
    QualityControlId = Guid.NewGuid(),
    SaleOrgCode = SaleOrgCode,
    WorkShopCode = WorkShopCode,
    StepCode = confirmWorkCenterViewModel.StepCode,  // Key field
    ConfirmDate = confirmWorkCenterViewModel.WorkCenterConfirmTime,
    CreateBy = confirmWorkCenterViewModel.ConfirmBy,
    CreateTime = DateTime.Now,
    VBELN = ttlxs.Property1,                        // SO Number
    Environmental = Environmental,
    LSXSAP = ttlxs.ProductionOrder_SAP,
    LSXDT = ttlxs.ProductionOrder,
    ProductAttribute = ttlxs.ProductAttributes,     // Key field
    ProductCode = ttlxs.ProductCode,
    ProductName = ttlxs.ProductName,
    CustomerReference = ttlxs.TaskId,               // Key field
    Status = false                                  // Pending QC
};

// Check if QC record already exists (composite key)
var check = _context.QualityControlModel.Where(x => 
    x.ProductAttribute == data.ProductAttribute &&
    x.StepCode == data.StepCode &&
    x.CustomerReference == data.CustomerReference
).FirstOrDefault();

if (check == null) {
    _context.Entry(data).State = EntityState.Added;
} else {
    // Update confirmation time if already exists
    check.ConfirmDate = confirmWorkCenterViewModel.WorkCenterConfirmTime;
}
```

### 🔑 Key Principles
- **Composite Key**: ProductAttribute + StepCode + CustomerReference uniquely identifies a QC record
- **Idempotent**: Multiple production scans update ConfirmDate, don't create duplicates
- **Automatic Creation**: QC records created during production, not during QC inspection
- **Initial Status**: All QC records start with Status = false (Pending QC)

---

## 4. QC Inspection Flow

### 📍 Entry Point (Mobile App)
**Location**: `TTF_MES_Mobile/lib/page/KiemTraChatLuong/PhieuKCSCongDoanDetail.dart`

### 🔄 Execution Flow

#### 4.1. QC Worker Scans Hang Tag
**API Call**: `GetQualityControl2Async(HangTagId)`
**Location**: `iMES_API/Areas/MES/Controllers/QualityControlController.cs` (Line: 453)

```dart
// Mobile app calls API to get production info
final data = await QualityControlFunction.getQualityControlByHangTagId(
    hangTagId, 
    widget.user.token
);
```

#### 4.1.1. Data Validation
```csharp
// API validates that ThucThiLenhSanXuatModel exists
// If QC scans before production recording, this will fail
var executionTask = _context.ThucThiLenhSanXuatModel
    .Where(x => x.HangTagId == HangTagId)
    .FirstOrDefault();

if (executionTask == null) {
    // Error: QC scanning before production recording
    throw new InvalidOperationException("Production not recorded yet");
}
```

#### 4.2. Production Step Selection
**Mobile Function**: `_onCongDoanNhoChanged()` (Lines: 1029-1138)
**API Call**: `GetCongDoanInfo(hangTagId, StepCode, QualityControlDetailId)`
**Location**: `iMES_API/Areas/MES/Controllers/QualityControlController.cs` (Line: 2006)

```dart
Future<void> _onCongDoanNhoChanged(DropdownItemList? value) async {
    setState(() {
        _selectedCongDoanNho = value;
        _isLoadingCongDoan = true;
    });

    var hangTagId = _hangTagId;
    var congDoan = value!.catalogCode.toString();
    
    // Get step-specific QC information
    final data = await QualityControlFunction.fetchCongDoanInfo(
        hangTagId, 
        congDoan, 
        widget.user.token.toString()
    );
}
```

#### 4.2.1. Step Information Processing
```dart
// Process returned data from GetCongDoanInfo
if (data != null) {
    // Load existing QC data if available
    _qualityControl?.workShopName = _congDoanInfoVm!.workShopName;
    _qualityControl!.qualityControlDetail = _congDoanInfoVm!.qualityControlDetail;
    _qualityControl!.qualityControlId = _congDoanInfoVm!.qualityControl?.qualityControlId;
    _qualityControl!.confirmDate = _congDoanInfoVm!.qualityControl?.confirmDate;

    if (_congDoanInfoVm!.qualityControlDetail != null) {
        _isNewMode = false;  // Load existing QC data
        showToast(
            message: "Đã load thông phiếu kiểm tra gần nhất, để tạo phiếu mới bấm vào nút [+]"
        );
    } else {
        _isNewMode = true;   // Create new QC record
    }

    // Load master data for QC forms
    _lsCaNhanGayLoiMasterData = _congDoanInfoVm!.caNhanGayLoiList!;
    _lsHangMucKiemTraMasterData = _congDoanInfoVm!.hangMucKiemTraMasterData!;
    
    // Load existing QC information if available
    _lsThongTinKiemTra = _congDoanInfoVm?.qualityControlInformation ?? [];
    _lsError = _congDoanInfoVm?.error ?? [];
}
```

### 📋 **Important Update: QualityControlDetailId Priority Lookup**

**Enhancement (10/05/2025)**: The `GetCongDoanInfo` method now **prioritizes QualityControlDetailId** when provided:

```csharp
// Priority 1: Direct lookup from saved QualityControlDetailModel
if (QualityControlDetailId != null)
{
    qualityControlModel = (from qcd in _context.QualityControlDetailModel
                         join qc in _context.QualityControlModel on qcd.QualityControlId equals qc.QualityControlId
                         where qcd.QualityControlDetailId == QualityControlDetailId
                         select qc).FirstOrDefault();
}

// Priority 2: HangTag-based lookup (when no QualityControlDetailId)
if (qualityControlModel == null && hangTagId != null)
{
    // ... existing HangTag-based lookup logic
}
```

**Key Benefits:**
- ✅ **Performance**: Direct lookup instead of complex HangTag joins when QC already saved
- ✅ **Accuracy**: Gets exact QualityControlModel from saved QualityControlDetailModel
- ✅ **Reliability**: Bypasses potential HangTag lookup issues for completed QC inspections
- ✅ **Data Integrity**: Uses the actual saved QC state rather than reconstructing from HangTag

**When This Applies:**
- **QC Detail Editing**: When user edits an existing QC inspection
- **QC Review**: When reviewing previously completed QC records
- **QC History**: When accessing historical QC data
- **Error Corrections**: When updating existing QC details

### 🎯 Update 21/04/2025: Flexible QC Policy
```dart
// New policy: Allow QC even without step confirmation
if (data == null) {
    // Previously would show error and return
    // showAlert(context: context, title: 'Thông báo', 
    //     content: 'Chưa xác nhận công đoạn $congDoan, vui lòng chọn công đoạn khác');
    
    // Now: Allow free QC without step confirmation requirement
    setState(() {
        // Initialize basic QC mode for new inspection
        _isNewMode = true;
        _congDoanInfoVm = null;
        
        // Use available master data from initial load
        _lsCaNhanGayLoiMasterData = _qualityControlModel?.caNhanGayLoiList ?? [];
        _lsCaNhanGayLoiAllMasterData = _qualityControlModel?.caNhanGayLoiList ?? [];
        _lsHangMucKiemTraMasterData = [];
        
        // Initialize empty data for new QC
        _lsThongTinKiemTra = [];
        _lsError = [];
        
        _isLoadingCongDoan = false;
    });
    
    showToast(message: "Chưa có dữ liệu sản xuất, có thể tạo phiếu QC mới");
    return; // Exit early but allow QC to continue
}
```

### 🔧 Fix for ProductAttribute Accuracy Issue (10/05/2025)

#### ❌ **Previous Problem (09/05/2025 & 21/04/2025):**
```csharp
// Problematic intermediate fallback - could return wrong ProductAttribute
if (qualityControlModel == null)
{
    // This gets ANY QualityControlModel, potentially wrong ProductAttribute
    qualityControlModel = (from ht in _context.HangTagModel
                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                           where ht.HangTagId == HangTagId
                           // && qc.ProductAttribute == productAttribute  // REMOVED!
                           select qc).FirstOrDefault();
}
```

#### ✅ **Phase 1 Solution:**
```csharp
// Step 1: Only exact ProductAttribute match in primary lookup
var qualityControlModel = (from ht in _context.HangTagModel
                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                           where ht.HangTagId == HangTagId
                           && qc.ProductAttribute == productAttribute  // EXACT match required
                           select qc).FirstOrDefault();

// Step 2: Phase 1 fallback with ProductAttribute priority
if (qualityControlModel == null)
{
    // Primary fallback: ProductAttribute + LSXSAP exact match
    qualityControlModel = (from qc in _context.QualityControlModel
                         join task in _context.TaskModel on qc.LSXSAP equals task.Summary
                         where qc.ProductAttribute == productAttribute  // EXACT match
                         && task.TaskId == customerReference
                         select qc).FirstOrDefault();
    
    // Secondary fallback: LSXSAP only (still better than random)
    if (qualityControlModel == null)
    {
        qualityControlModel = (from qc in _context.QualityControlModel
                             join task in _context.TaskModel on qc.LSXSAP equals task.Summary
                             where task.TaskId == customerReference
                             select qc).FirstOrDefault();
    }
}
```

**Key Benefits:**
- ✅ Always tries exact ProductAttribute match first
- ✅ Phase 1 fallback prioritizes ProductAttribute accuracy
- ✅ Eliminates random ProductAttribute selection
- ✅ Maintains backwards compatibility

### 🔧 Fix for RangeError in NhaMayLoi Controllers
```dart
// Problem: _lsControllerNhaMayLoi RangeError when QC scans before production
// Solution: Handle null _congDoanInfoVm and _qualityControl safely

// In addDefaultError function:
if (_congDoanInfoVm != null) {
    // Normal case with production data
    _lsControllerNhaMayLoi.add(TextEditingController(text: _qualityControl?.storeName ?? ""));
    _lsControllerPhanXuongLoi.add(TextEditingController(text: _congDoanInfoVm!.workShopName ?? ""));
    _lsControllerToChuyenLoi.add(TextEditingController(text: _congDoanInfoVm!.departmentName ?? ""));
} else {
    // QC without production confirmation - use available data or empty
    _lsControllerNhaMayLoi.add(TextEditingController(text: _qualityControl?.storeName ?? ""));
    _lsControllerPhanXuongLoi.add(TextEditingController(text: _qualityControl?.workShopName ?? ""));
    _lsControllerToChuyenLoi.add(TextEditingController(text: ""));
}

// In setupError function for existing errors:
_lsControllerNhaMayLoi[i].text = _lsError[i].nhaMayLoi == null 
    ? (_qualityControl?.storeName ?? "") 
    : _lsError[i].nhaMayLoi.toString();
_lsControllerPhanXuongLoi[i].text = _lsError[i].phanXuongLoi == null 
    ? (_congDoanInfoVm?.workShopName ?? _qualityControl?.workShopName ?? "") 
    : _lsError[i].phanXuongLoi.toString();
```

---

## 5. Data Processing Patterns

### 🔍 Key Lookup Strategies

#### 5.1. QC Record Identification
```csharp
// Composite key lookup for existing QC records
var existingQC = _context.QualityControlModel.Where(x => 
    x.ProductAttribute == productAttribute &&    // Product variant (e.g., "0101.9.3.2")
    x.StepCode == stepCode &&                   // Production step (e.g., "CAT", "TKV")
    x.CustomerReference == taskId               // Execution task ID
).FirstOrDefault();
```

#### 5.2. Hierarchical Data Resolution
```csharp
// Department → WorkShop → Plant hierarchy
DepartmentId → WorkShopId → WorkShopCode
TaskId → ParentTaskId → CompanyId → SaleOrgCode

// Production chain linking
HangTagId → ThucThiLenhSanXuatModel.TaskId → QualityControlModel.CustomerReference
```

#### 5.3. Master Data Integration
```csharp
// Environmental conditions from SAP
SOTextHeader100Model.LONGTEXT where TEXT_ID = "H022"

// Product information
ProductModel join on CompanyId + ERPProductCode

// Routing information  
RoutingInventorModel for step sequences and quantities
```

---

## 6. Error Scenarios & Handling

### ⚠️ Common Issues

#### 6.1. QC Before Production Recording
```csharp
// Problem: QC worker scans hang tag before production worker
// Result: ThucThiLenhSanXuatModel doesn't exist
// Solution: Production worker must scan first to create execution task

if (executionTask == null) {
    return BadRequest("Chưa ghi nhận sản lượng. Vui lòng liên hệ nhân viên sản xuất quét thẻ treo trước.");
}
```

#### 6.2. Missing Step Confirmation
```dart
// Problem: QC selects step that hasn't been confirmed in production
// Old behavior: Show error and block QC
// New behavior (21/04/2025): Allow QC to proceed

if (data == null) {
    // Update: Allow flexible QC without strict step confirmation
    setState(() => _isLoadingCongDoan = false);
    // Continue with QC process instead of blocking
}
```

#### 6.3. Duplicate QC Creation
```csharp
// Problem: Multiple production scans creating duplicate QC records
// Solution: Composite key check and update instead of insert

var check = _context.QualityControlModel.Where(x => 
    x.ProductAttribute == data.ProductAttribute &&
    x.StepCode == data.StepCode &&
    x.CustomerReference == data.CustomerReference
).FirstOrDefault();

if (check == null) {
    _context.Entry(data).State = EntityState.Added;
} else {
    check.ConfirmDate = confirmWorkCenterViewModel.WorkCenterConfirmTime;  // Update only
}
```

---

## 7. Mobile App State Management

### 📱 Key State Variables
```dart
// Core QC data
QualityControlViewModel? _qualityControl;
CongDoanInfoViewModel? _congDoanInfoVm;
bool _isNewMode = true;  // true = new QC, false = load existing

// Form state
DropdownItemList? _selectedCongDoanNho;
bool _isLoadingCongDoan = false;
bool _errorCongDoanNho = false;

// Master data
List<CaNhanGayLoiViewModel> _lsCaNhanGayLoiMasterData = [];
List<HangMucKiemTraViewModel> _lsHangMucKiemTraMasterData = [];

// QC details
List<QualityControlInformationMappingViewModel> _lsThongTinKiemTra = [];
List<QualityControlErrorViewModel> _lsError = [];
```

### 🔄 State Transition Flow
```dart
1. Initial State: _isNewMode = true, no QC data loaded
2. Step Selection: User selects production step → API call
3. Data Loading: _isLoadingCongDoan = true
4. Data Processing: 
   - If existing QC found → _isNewMode = false, load data
   - If no QC found → _isNewMode = true, prepare new form
5. Form Ready: _isLoadingCongDoan = false, UI updated
```

---

## 8. API Integration Points

### 🔗 Key API Endpoints

#### 8.1. Production Recording
```csharp
POST /api/ProductionRecording/ConfirmWorkCenter
→ Triggers CreateQualityControl2()
→ Creates/Updates QualityControlModel
```

#### 8.2. QC Data Retrieval
```csharp
GET /api/QualityControl/GetQualityControl2Async?HangTagId={id}
→ Returns production information for QC
→ Validates ThucThiLenhSanXuatModel exists
```

#### 8.3. Step-Specific QC Info
```csharp
GET /api/QualityControl/GetCongDoanInfo?hangTagId={id}&StepCode={step}
→ Returns step-specific QC data and master data
→ Loads existing QC details if available
```

### 📋 Data Transfer Objects
```csharp
// Request models
ConfirmWorkCenterViewModel {
    TaskId, DepartmentId, StepCode, WorkCenterConfirmTime
}

// Response models  
QualityControlViewModel {
    QualityControlId, ProductAttribute, StepCode, Status, ConfirmDate
}

CongDoanInfoViewModel {
    QualityControl, QualityControlDetail, QualityControlInformation,
    Error, CaNhanGayLoiList, HangMucKiemTraMasterData
}
```

---

## 9. Performance Considerations

### ⚡ Optimization Strategies

#### 9.1. Database Query Optimization
```csharp
// Use composite indexes on QualityControlModel
CREATE INDEX IX_QualityControl_Composite 
ON QualityControlModel (ProductAttribute, StepCode, CustomerReference)

// Efficient lookup with AsNoTracking for read-only queries
var data = _context.QualityControlModel.AsNoTracking()
    .Where(x => x.QualityControlId == id)
    .FirstOrDefault();
```

#### 9.2. Mobile App Performance
```dart
// Minimize API calls with smart caching
if (_congDoanInfoVm?.qualityControl?.qualityControlId == existingId) {
    // Use cached data instead of API call
    return;
}

// Show loading states to improve perceived performance
setState(() => _isLoadingCongDoan = true);
```

### 💡 Best Practices
- **Lazy Loading**: Load step details only when step is selected
- **State Caching**: Cache master data to avoid repeated API calls  
- **Composite Keys**: Use efficient multi-column lookups
- **Error Recovery**: Graceful fallbacks for missing data scenarios

---

## 10. Sequence Diagram

### 🔄 Complete Production to QC Flow

```
Production Worker          API Server              Database           QC Worker            Mobile App
      |                        |                       |                   |                    |
      | 1. Scan HangTag       |                       |                   |                    |
      |---------------------->|                       |                   |                    |
      |                       | 2. CreateNewExecutionTask               |                    |
      |                       |---------------------->|                   |                    |
      |                       |                       | 3. ThucThiLenhSanXuatModel           |
      |                       |                       |<------|            |                    |
      |                       | 4. CreateQualityControl2                  |                    |
      |                       |---------------------->|                   |                    |
      |                       |                       | 5. QualityControlModel               |
      |                       |                       |<------|            |                    |
      |                       |                       |                   |                    |
      |                       |                       |                   | 6. Scan HangTag   |
      |                       |                       |                   |<------------------|
      |                       | 7. GetQualityControl2Async               |                   |
      |                       |<------|               |                   |                   |
      |                       |                       |                   | 8. Select Step    |
      |                       |                       |                   |<------------------|
      |                       | 9. GetCongDoanInfo   |                   |                   |
      |                       |<------|               |                   |                   |
      |                       |                       | 10. Load QC Data  |                   |
      |                       |                       |<------|            |                   |
```

---

## 11. Phase 1 Testing Plan

### 🧪 **Comprehensive Test Cases for Phase 1 Implementation**

#### **Test Category A: ProductAttribute Accuracy Tests**

##### **A1. Exact ProductAttribute Match - Production First**
```
Setup:
- Production worker scans HangTag → Creates QualityControlModel with exact ProductAttribute
- QC worker scans same HangTag

Expected Result:
- GetQualityControl2Async returns QualityControlModel with exact matching ProductAttribute
- No fallback logic triggered
- Correct product-specific data loaded

Test Data:
- HangTagId: "HT001"
- ProductAttribute: "0101.9.3.2"
- StepCode: "CAT"
```

##### **A2. Multiple ProductAttributes - Verify Exact Match**
```
Setup:
- Multiple QualityControlModels exist for same CustomerReference but different ProductAttributes
- ProductAttribute A: "0101.9.3.2" 
- ProductAttribute B: "0101.9.3.3"
- QC scans HangTag with ProductAttribute A

Expected Result:
- Returns QualityControlModel with ProductAttribute A only
- Does NOT return ProductAttribute B
- Exact match prioritized over random selection

Test Data:
- HangTagId: "HT002" (ProductAttribute: "0101.9.3.2")
- Existing QC records for both "0101.9.3.2" and "0101.9.3.3"
```

##### **A3. No Intermediate Fallback Triggered**
```
Setup:
- No ThucThiLenhSanXuatModel exists (QC-first scenario)
- QualityControlModel exists with different ProductAttribute

Expected Result:
- Primary lookup fails (correct behavior)
- Phase 1 fallback triggered (not intermediate fallback)
- Exact ProductAttribute match attempted in fallback
- No wrong ProductAttribute returned

Test Data:
- HangTagId: "HT003" 
- ProductAttribute: "0101.9.3.2"
- Existing QC record has ProductAttribute: "0101.9.3.3"
```

#### **Test Category B: Phase 1 Fallback Lookup Tests**

##### **B1. QC-First Scenario - LSXSAP Match Success**
```
Setup:
- QC worker scans before production worker
- No ThucThiLenhSanXuatModel exists
- QualityControlModel exists with matching LSXSAP + ProductAttribute

Expected Result:
- Primary lookup fails
- Phase 1 fallback succeeds with exact ProductAttribute + LSXSAP match
- Correct QualityControlModel returned
- ttlsx lookup uses LSXSAP-based strategy

Test Data:
- HangTagId: "HT004"
- ProductAttribute: "0101.9.3.2"
- LSXSAP: "PO123456"
- CustomerReference: null (QC-first record)
```

##### **B2. LSXSAP Fallback - ProductAttribute Priority**
```
Setup:
- Primary lookup fails
- Multiple QualityControlModels exist with same LSXSAP
- Only one matches ProductAttribute

Expected Result:
- Phase 1 primary fallback returns exact ProductAttribute + LSXSAP match
- Secondary fallback NOT triggered
- Correct ProductAttribute maintained

Test Data:
- HangTagId: "HT005"
- ProductAttribute: "0101.9.3.2"
- LSXSAP: "PO123456"
- Multiple QC records: {"0101.9.3.2", "0101.9.3.3"} with same LSXSAP
```

##### **B3. LSXSAP Secondary Fallback**
```
Setup:
- Primary lookup fails
- Phase 1 primary fallback fails (no ProductAttribute match)
- Phase 1 secondary fallback available (LSXSAP only)

Expected Result:
- Secondary fallback triggered
- Returns QualityControlModel with matching LSXSAP
- ProductAttribute may differ (acceptable in this edge case)

Test Data:
- HangTagId: "HT006"
- ProductAttribute: "0101.9.3.2" (not found)
- LSXSAP: "PO123456" (exists with different ProductAttribute)
```

#### **Test Category C: GetCongDoanInfo Step-Specific Tests**

##### **C1. Step Selection - Exact Match**
```
Setup:
- Production recorded for specific StepCode
- QC selects same StepCode

Expected Result:
- Returns QualityControlModel with exact StepCode + ProductAttribute match
- Step-specific QC data loaded correctly
- Master data populated for selected step

Test Data:
- HangTagId: "HT007"
- ProductAttribute: "0101.9.3.2"
- StepCode: "CAT"
```

##### **C2. Step Selection - Phase 1 Fallback with StepCode**
```
Setup:
- QC scans first, selects StepCode
- No production confirmation yet
- QualityControlModel exists with matching LSXSAP + ProductAttribute + StepCode

Expected Result:
- Phase 1 fallback succeeds with triple match
- Step-specific data loaded from existing QC record
- Existing QC inspection data displayed

Test Data:
- HangTagId: "HT008"
- ProductAttribute: "0101.9.3.2"
- StepCode: "TKV"
- LSXSAP: "PO123456"
```

##### **C3. Step Selection - No Step Confirmation Yet**
```
Setup:
- QC selects StepCode that hasn't been confirmed in production
- No existing QC record for this step

Expected Result:
- Returns NotFound with "Chưa confirm công đoạn" message
- OR triggers Phase 2 QC record creation (if implemented)
- Graceful error handling

Test Data:
- HangTagId: "HT009"
- ProductAttribute: "0101.9.3.2"
- StepCode: "EPV" (not confirmed yet)
```

#### **Test Category D: Data Integrity Tests**

##### **D1. ThucThiLenhSanXuatModel Lookup Enhancement**
```
Setup:
- QualityControlModel exists (Phase 1 created)
- CustomerReference is null
- LSXSAP-based ThucThiLenhSanXuatModel lookup needed

Expected Result:
- ttlsx lookup uses LSXSAP + Barcode strategy
- Correct ThucThiLenhSanXuatModel returned
- Production data integrated correctly

Test Data:
- QualityControlId with CustomerReference: null
- LSXSAP: "PO123456"
- Barcode: "HT010"
```

##### **D2. Composite Key Validation**
```
Setup:
- Multiple scenarios with same partial keys
- Test uniqueness constraints

Expected Result:
- ProductAttribute + StepCode + CustomerReference uniqueness maintained
- ProductAttribute + LSXSAP + StepCode alternative key works
- No duplicate record creation

Test Data:
- Multiple combinations of {ProductAttribute, StepCode, CustomerReference, LSXSAP}
```

##### **D3. Null Data Handling**
```
Setup:
- Missing TaskModel records
- Missing HangTagModel records
- Null LSXSAP values

Expected Result:
- Graceful error handling
- Appropriate error messages
- No null pointer exceptions

Test Data:
- Invalid HangTagIds
- Missing parent tasks
- Corrupted data scenarios
```

#### **Test Category E: End-to-End Workflow Tests**

##### **E1. Complete Production-First Flow**
```
Workflow:
1. Production worker scans HangTag
2. QualityControlModel created with exact ProductAttribute
3. QC worker scans same HangTag
4. QC selects production step
5. QC performs inspection
6. QC saves results

Expected Result:
- All steps succeed with exact ProductAttribute
- No fallback logic triggered
- Complete QC workflow functional
```

##### **E2. Complete QC-First Flow**
```
Workflow:
1. QC worker scans HangTag (before production)
2. Phase 1 fallback triggered
3. QC selects production step
4. QC performs inspection with limited data
5. Production worker scans later
6. QC data updated with production info

Expected Result:
- QC can proceed without production
- Phase 1 fallback provides necessary data
- Later production scan enhances data
```

##### **E3. Mixed Scenario Flow**
```
Workflow:
1. Production confirms StepA
2. QC scans and inspects StepA
3. QC tries to inspect StepB (not confirmed)
4. Production confirms StepB
5. QC inspects StepB

Expected Result:
- StepA inspection works normally
- StepB inspection fails initially
- StepB inspection succeeds after production confirmation
```

#### **Test Category F: Performance & Edge Cases**

##### **F1. High Volume Concurrent Access**
```
Setup:
- Multiple QC workers scanning simultaneously
- Same HangTag, different steps
- Database concurrency handling

Expected Result:
- No race conditions
- Consistent ProductAttribute returned
- Performance within acceptable limits
```

##### **F2. Data Migration Compatibility**
```
Setup:
- Existing QualityControlModel records (pre-Phase 1)
- New Phase 1 enhanced lookups
- Mixed old/new data scenarios

Expected Result:
- Backward compatibility maintained
- Old records accessible via new logic
- No data corruption
```

##### **F3. Network/Database Failure Recovery**
```
Setup:
- Temporary database connectivity issues
- Partial data loading scenarios
- Timeout handling

Expected Result:
- Graceful error handling
- Retry mechanisms work
- User-friendly error messages
```

### 📋 **Test Execution Checklist**

#### **Priority 1 (Critical)**
- [ ] A1: Exact ProductAttribute Match - Production First
- [ ] A2: Multiple ProductAttributes - Verify Exact Match
- [ ] B1: QC-First Scenario - LSXSAP Match Success
- [ ] C1: Step Selection - Exact Match
- [ ] E1: Complete Production-First Flow

#### **Priority 2 (High)**
- [ ] A3: No Intermediate Fallback Triggered
- [ ] B2: LSXSAP Fallback - ProductAttribute Priority
- [ ] C2: Step Selection - Phase 1 Fallback with StepCode
- [ ] D1: ThucThiLenhSanXuatModel Lookup Enhancement
- [ ] E2: Complete QC-First Flow

#### **Priority 3 (Medium)**
- [ ] B3: LSXSAP Secondary Fallback
- [ ] C3: Step Selection - No Step Confirmation Yet
- [ ] D2: Composite Key Validation
- [ ] D3: Null Data Handling
- [ ] E3: Mixed Scenario Flow

#### **Priority 4 (Low)**
- [ ] F1: High Volume Concurrent Access
- [ ] F2: Data Migration Compatibility
- [ ] F3: Network/Database Failure Recovery

### 🔧 **Test Data Extraction Scripts**

#### **A. ProductAttribute Accuracy Test Data (FAST QUERIES)**

```sql
-- FAST A1: Simple HangTag overview (runs in seconds)
SELECT TOP 50
    ht.HangTagId,
    ht.ProductAttribute,
    ht.CustomerReference,
    ht.CreatedTime,
    -- Quick existence checks (no joins)
    CASE WHEN EXISTS(SELECT 1 FROM MES.ThucThiLenhSanXuatModel ttl WHERE ttl.ParentTaskId = ht.CustomerReference) 
         THEN 'Has Production' ELSE 'No Production' END AS ProductionStatus,
    CASE WHEN EXISTS(SELECT 1 FROM MES.QualityControlModel qc 
                     JOIN MES.ThucThiLenhSanXuatModel ttl ON qc.CustomerReference = ttl.TaskId 
                     WHERE ttl.ParentTaskId = ht.CustomerReference AND qc.ProductAttribute = ht.ProductAttribute) 
         THEN 'Has QC' ELSE 'No QC' END AS QCStatus
FROM MES.HangTagModel ht WITH (NOLOCK)
WHERE ht.ProductAttribute IS NOT NULL
    AND ht.CustomerReference IS NOT NULL
    AND ht.CreatedTime >= DATEADD(day, -30, GETDATE()) -- Recent records only
ORDER BY ht.CreatedTime DESC;

-- FAST A2: Find multiple ProductAttributes (simple and fast)
SELECT TOP 20
    ht.CustomerReference,
    COUNT(DISTINCT ht.ProductAttribute) AS ProductAttributeCount,
    MIN(ht.ProductAttribute) AS FirstProductAttribute,
    MAX(ht.ProductAttribute) AS SecondProductAttribute,
    MIN(CAST(ht.HangTagId AS VARCHAR(50))) AS FirstHangTagId
FROM MES.HangTagModel ht WITH (NOLOCK)
WHERE ht.CustomerReference IS NOT NULL 
    AND ht.ProductAttribute IS NOT NULL
    AND ht.CreatedTime >= DATEADD(day, -60, GETDATE())
GROUP BY ht.CustomerReference
HAVING COUNT(DISTINCT ht.ProductAttribute) > 1
ORDER BY ProductAttributeCount DESC;
```

#### **B. Phase 1 Fallback Test Data (FAST QUERIES)**

```sql
-- FAST B1: QC-First scenarios (very quick)
SELECT TOP 20
    qc.QualityControlId,
    qc.ProductAttribute,
    qc.StepCode,
    qc.LSXSAP,
    qc.CustomerReference,
    qc.CreateTime,
    qc.Status
FROM MES.QualityControlModel qc WITH (NOLOCK)
WHERE qc.CustomerReference IS NULL
    AND qc.LSXSAP IS NOT NULL
    AND qc.CreateTime >= DATEADD(day, -30, GETDATE())
ORDER BY qc.CreateTime DESC;

-- FAST B2: Multiple QC records with same LSXSAP (quick grouping)
SELECT TOP 10
    qc.LSXSAP,
    COUNT(*) AS QCRecordCount,
    COUNT(DISTINCT qc.ProductAttribute) AS UniqueProductAttributes,
    MIN(qc.ProductAttribute) AS FirstProductAttribute,
    MIN(CAST(qc.QualityControlId AS VARCHAR(50))) AS FirstQualityControlId
FROM MES.QualityControlModel qc WITH (NOLOCK)
WHERE qc.LSXSAP IS NOT NULL
    AND qc.CreateTime >= DATEADD(day, -30, GETDATE())
GROUP BY qc.LSXSAP
HAVING COUNT(*) > 1
ORDER BY QCRecordCount DESC;
```

### ⚡ **SUPER FAST Test Data Queries (Run These First!)**

```sql
-- 🚀 INSTANT: Get any recent HangTag for testing (< 1 second)
SELECT TOP 5
    HangTagId,
    ProductAttribute,
    CustomerReference,
    CreatedTime
FROM MES.HangTagModel WITH (NOLOCK)
WHERE ProductAttribute IS NOT NULL 
    AND CustomerReference IS NOT NULL
    AND CreatedTime >= DATEADD(day, -7, GETDATE())
ORDER BY CreatedTime DESC;

-- 🚀 INSTANT: Get any QC record for testing (< 1 second)  
SELECT TOP 5
    QualityControlId,
    ProductAttribute,
    StepCode,
    LSXSAP,
    CustomerReference,
    CreateTime
FROM MES.QualityControlModel WITH (NOLOCK)
WHERE ProductAttribute IS NOT NULL
    AND CreateTime >= DATEADD(day, -7, GETDATE())
ORDER BY CreateTime DESC;

-- 🚀 INSTANT: Get any production record for testing (< 1 second)
SELECT TOP 5
    TaskId,
    ParentTaskId,
    ProductAttributes,
    WorkCenterConfirmTime,
    CreateTime
FROM MES.ThucThiLenhSanXuatModel WITH (NOLOCK)
WHERE ProductAttributes IS NOT NULL
    AND CreateTime >= DATEADD(day, -7, GETDATE())
ORDER BY CreateTime DESC;

-- 🚀 INSTANT: Find QC-first records (< 1 second)
SELECT TOP 3
    QualityControlId,
    ProductAttribute,
    StepCode,
    LSXSAP
FROM MES.QualityControlModel WITH (NOLOCK)
WHERE CustomerReference IS NULL
    AND LSXSAP IS NOT NULL
ORDER BY CreateTime DESC;
```

#### **C. Step-Specific Test Data**

```sql
-- C1 & C2: Find step-specific QC data with production confirmation
SELECT 
    qc.QualityControlId,
    qc.ProductAttribute,
    qc.StepCode,
    qc.LSXSAP,
    qc.CustomerReference,
    qc.ConfirmDate,
    qc.Status,
    -- Production info
    ttl.TaskId AS ExecutionTaskId,
    ttl.ProductAttributes AS TTLProductAttributes,
    ttl.WorkCenterConfirmTime,
    ttl.IsWorkCenterCompleted,
    -- HangTag info
    ht.HangTagId,
    ht.ProductAttribute AS HangTagProductAttribute
FROM MES.QualityControlModel qc
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON qc.CustomerReference = ttl.TaskId
INNER JOIN MES.HangTagModel ht ON ttl.ParentTaskId = ht.CustomerReference
WHERE qc.StepCode IS NOT NULL
    AND qc.ProductAttribute IS NOT NULL
ORDER BY qc.StepCode, qc.ProductAttribute, qc.CreateTime DESC;

-- C3: Find steps without confirmation (for testing step selection scenarios)
SELECT DISTINCT
    cat.CatalogCode AS AvailableStepCode,
    cat.CatalogText_vi AS StepName,
    -- Count existing QC records for this step
    (SELECT COUNT(*) 
     FROM MES.QualityControlModel qc2 
     WHERE qc2.StepCode = cat.CatalogCode) AS ExistingQCCount
FROM Task.CatalogModel cat
WHERE cat.CatalogTypeCode = 'PRODUCTION_PROCESS' -- Adjust based on your step catalog type
ORDER BY cat.CatalogCode;
```

#### **D. Data Integrity Test Data**

```sql
-- D1: ThucThiLenhSanXuatModel lookup scenarios
SELECT 
    ttl.TaskId,
    ttl.ParentTaskId,
    ttl.ProductAttributes,
    ttl.Barcode,
    ttl.WorkCenterConfirmTime,
    ttl.IsWorkCenterCompleted,
    -- Parent task info
    t.Summary AS ParentTaskSummary,
    t.ProductCode,
    t.ProductName,
    -- HangTag relationship
    ht.HangTagId,
    ht.ProductAttribute,
    -- QC relationship
    qc.QualityControlId,
    qc.StepCode,
    qc.CustomerReference AS QCCustomerReference
FROM MES.ThucThiLenhSanXuatModel ttl
INNER JOIN Task.TaskModel t ON ttl.ParentTaskId = t.TaskId
LEFT JOIN MES.HangTagModel ht ON t.TaskId = ht.CustomerReference
LEFT JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
WHERE ttl.ProductAttributes IS NOT NULL
ORDER BY ttl.CreateTime DESC;

-- D2: Composite key validation scenarios
SELECT 
    qc.ProductAttribute,
    qc.StepCode,
    qc.CustomerReference,
    qc.LSXSAP,
    COUNT(*) AS DuplicateCount,
    STRING_AGG(CAST(qc.QualityControlId AS VARCHAR(50)), ', ') AS QualityControlIds
FROM MES.QualityControlModel qc
WHERE qc.ProductAttribute IS NOT NULL 
    AND qc.StepCode IS NOT NULL
GROUP BY qc.ProductAttribute, qc.StepCode, qc.CustomerReference, qc.LSXSAP
HAVING COUNT(*) > 1
ORDER BY DuplicateCount DESC;
```

#### **E. End-to-End Workflow Test Data**

```sql
-- E1: Complete production-first workflow data
WITH ProductionFirstData AS (
    SELECT 
        ht.HangTagId,
        ht.ProductAttribute,
        ht.CustomerReference,
        ttl.TaskId AS ExecutionTaskId,
        ttl.WorkCenterConfirmTime,
        qc.QualityControlId,
        qc.StepCode,
        qc.ConfirmDate,
        qc.Status,
        -- Determine workflow sequence
        CASE 
            WHEN ttl.WorkCenterConfirmTime < qc.CreateTime THEN 'Production First'
            WHEN ttl.WorkCenterConfirmTime > qc.CreateTime THEN 'QC First'
            ELSE 'Simultaneous'
        END AS WorkflowSequence
    FROM MES.HangTagModel ht
    INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
    INNER JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
    WHERE ht.ProductAttribute = qc.ProductAttribute
)
SELECT * FROM ProductionFirstData
WHERE WorkflowSequence = 'Production First'
ORDER BY HangTagId;

-- E2: QC-first workflow data
SELECT 
    qc.QualityControlId,
    qc.ProductAttribute,
    qc.StepCode,
    qc.LSXSAP,
    qc.CreateTime AS QCCreateTime,
    qc.CustomerReference,
    -- Try to find later production data
    ttl.TaskId AS LaterExecutionTaskId,
    ttl.WorkCenterConfirmTime AS LaterProductionTime,
    CASE 
        WHEN ttl.TaskId IS NOT NULL THEN 'Production Added Later'
        ELSE 'Still QC-Only'
    END AS WorkflowStatus
FROM MES.QualityControlModel qc
LEFT JOIN MES.ThucThiLenhSanXuatModel ttl ON qc.CustomerReference = ttl.TaskId
WHERE qc.CustomerReference IS NULL OR ttl.WorkCenterConfirmTime > qc.CreateTime
ORDER BY qc.CreateTime DESC;
```

#### **F. Performance & Edge Cases Test Data**

```sql
-- F1: High volume concurrent scenarios
SELECT 
    ht.HangTagId,
    ht.ProductAttribute,
    COUNT(qc.QualityControlId) AS QCRecordCount,
    COUNT(DISTINCT qc.StepCode) AS UniqueStepCount,
    MIN(qc.CreateTime) AS FirstQCTime,
    MAX(qc.CreateTime) AS LastQCTime,
    DATEDIFF(SECOND, MIN(qc.CreateTime), MAX(qc.CreateTime)) AS TimeSpanSeconds
FROM MES.HangTagModel ht
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
INNER JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
GROUP BY ht.HangTagId, ht.ProductAttribute
HAVING COUNT(qc.QualityControlId) > 3 -- High activity HangTags
ORDER BY QCRecordCount DESC;

-- F2: Data migration compatibility check
SELECT 
    'Pre-Phase1' AS DataType,
    COUNT(*) AS RecordCount,
    COUNT(CASE WHEN CustomerReference IS NOT NULL THEN 1 END) AS WithCustomerReference,
    COUNT(CASE WHEN CustomerReference IS NULL THEN 1 END) AS WithoutCustomerReference,
    COUNT(CASE WHEN LSXSAP IS NOT NULL THEN 1 END) AS WithLSXSAP
FROM MES.QualityControlModel
WHERE CreateTime < '2025-04-21' -- Before Phase 1 implementation

UNION ALL

SELECT 
    'Post-Phase1' AS DataType,
    COUNT(*) AS RecordCount,
    COUNT(CASE WHEN CustomerReference IS NOT NULL THEN 1 END) AS WithCustomerReference,
    COUNT(CASE WHEN CustomerReference IS NULL THEN 1 END) AS WithoutCustomerReference,
    COUNT(CASE WHEN LSXSAP IS NOT NULL THEN 1 END) AS WithLSXSAP
FROM MES.QualityControlModel
WHERE CreateTime >= '2025-04-21'; -- After Phase 1 implementation
```

### 🔍 **Specific Test Case Data Queries**

#### **Find HangTag for Test Case A1 (Production First)**
```sql
-- Get a HangTag that has completed production workflow
SELECT TOP 5
    ht.HangTagId,
    ht.ProductAttribute,
    ht.CustomerReference,
    ttl.TaskId,
    ttl.WorkCenterConfirmTime,
    qc.QualityControlId,
    qc.StepCode
FROM MES.HangTagModel ht
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
INNER JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
WHERE ht.ProductAttribute IS NOT NULL
    AND ttl.IsWorkCenterCompleted = 1
    AND qc.ProductAttribute = ht.ProductAttribute
ORDER BY ttl.WorkCenterConfirmTime DESC;
```

#### **Find HangTag for Test Case B1 (QC First)**
```sql
-- Get a HangTag for QC-first testing
SELECT TOP 5
    ht.HangTagId,
    ht.ProductAttribute,
    ht.CustomerReference,
    t.Summary AS LSXSAP,
    qc.QualityControlId,
    qc.StepCode,
    qc.CustomerReference AS QCCustomerRef
FROM MES.HangTagModel ht
INNER JOIN Task.TaskModel t ON ht.CustomerReference = t.TaskId
LEFT JOIN MES.QualityControlModel qc ON t.Summary = qc.LSXSAP 
    AND ht.ProductAttribute = qc.ProductAttribute
WHERE ht.ProductAttribute IS NOT NULL
    AND t.Summary IS NOT NULL
ORDER BY ht.CreatedTime DESC;
```

#### **Find Test Data for Different StepCodes**
```sql
-- Get available step codes for testing
SELECT DISTINCT 
    qc.StepCode,
    COUNT(*) AS RecordCount,
    STRING_AGG(DISTINCT qc.ProductAttribute, ', ') AS ProductAttributes
FROM MES.QualityControlModel qc
WHERE qc.StepCode IS NOT NULL
GROUP BY qc.StepCode
ORDER BY RecordCount DESC;
```

### 🔍 **Analysis of Your Current Data**

Based on your query results showing:
- **HangTagId**: `8a2375e3-2598-447d-9703-78fc3d1051f7`
- **ProductAttribute**: `0101` 
- **Multiple QC Status**: "No QC Record" and "QC Record Exists"

This indicates **multiple `ThucThiLenhSanXuatModel` records** for the same HangTag. Here are queries to analyze this specific case:

#### **Analyze Your Specific HangTag**
```sql
-- Deep dive into the specific HangTag from your results
DECLARE @HangTagId UNIQUEIDENTIFIER = '8a2375e3-2598-447d-9703-78fc3d1051f7';

-- 1. Check all ThucThiLenhSanXuatModel records for this HangTag
SELECT 
    ttl.TaskId,
    ttl.ParentTaskId,
    ttl.ProductAttributes,
    ttl.WorkCenterConfirmTime,
    ttl.IsWorkCenterCompleted,
    ttl.ConfirmWorkCenter,
    ttl.CreateTime,
    -- Check corresponding QC records
    qc.QualityControlId,
    qc.StepCode,
    qc.ProductAttribute AS QCProductAttribute,
    qc.Status AS QCStatus,
    qc.CreateTime AS QCCreateTime
FROM MES.HangTagModel ht
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
LEFT JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
WHERE ht.HangTagId = @HangTagId
ORDER BY ttl.CreateTime, qc.CreateTime;

-- 2. Check if there are QC records with NULL CustomerReference for this ProductAttribute
SELECT 
    qc.QualityControlId,
    qc.ProductAttribute,
    qc.StepCode,
    qc.LSXSAP,
    qc.CustomerReference,
    qc.CreateTime,
    qc.Status,
    -- Try to match with TaskModel
    t.TaskId AS TaskModelMatch,
    t.Summary AS TaskSummary
FROM MES.QualityControlModel qc
LEFT JOIN Task.TaskModel t ON qc.LSXSAP = t.Summary
WHERE qc.ProductAttribute = '0101' 
    AND (qc.CustomerReference IS NULL OR qc.LSXSAP IS NOT NULL)
ORDER BY qc.CreateTime DESC;
```

#### **Get Clean Test Data for Each Scenario**
```sql
-- TEST CASE A1: Get single HangTag with Production-First scenario
SELECT TOP 1
    ht.HangTagId,
    ht.ProductAttribute,
    ht.CustomerReference,
    ttl.TaskId,
    ttl.WorkCenterConfirmTime,
    qc.QualityControlId,
    qc.StepCode,
    qc.CreateTime AS QCCreateTime,
    'Use this for A1 test' AS TestNote
FROM MES.HangTagModel ht
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
INNER JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
WHERE ht.ProductAttribute IS NOT NULL
    AND qc.ProductAttribute = ht.ProductAttribute
    AND ttl.WorkCenterConfirmTime < qc.CreateTime  -- Production first
ORDER BY ttl.WorkCenterConfirmTime DESC;

-- TEST CASE A2: Get HangTag with multiple ProductAttributes
SELECT 
    CustomerReference,
    STRING_AGG(CAST(HangTagId AS VARCHAR(50)), ', ') AS HangTagIds,
    STRING_AGG(ProductAttribute, ', ') AS ProductAttributes,
    COUNT(DISTINCT ProductAttribute) AS ProductAttributeCount,
    'Use these for A2 test' AS TestNote
FROM MES.HangTagModel 
WHERE CustomerReference IS NOT NULL 
    AND ProductAttribute IS NOT NULL
GROUP BY CustomerReference
HAVING COUNT(DISTINCT ProductAttribute) > 1
ORDER BY ProductAttributeCount DESC;

-- TEST CASE B1: Get QC-First scenario
SELECT TOP 1
    qc.QualityControlId,
    qc.ProductAttribute,
    qc.StepCode,
    qc.LSXSAP,
    qc.CustomerReference,
    t.TaskId AS TaskModelId,
    ht.HangTagId,
    'Use this for B1 QC-First test' AS TestNote
FROM MES.QualityControlModel qc
INNER JOIN Task.TaskModel t ON qc.LSXSAP = t.Summary
INNER JOIN MES.HangTagModel ht ON t.TaskId = ht.CustomerReference 
    AND ht.ProductAttribute = qc.ProductAttribute
WHERE qc.CustomerReference IS NULL  -- QC-First scenario
    AND qc.LSXSAP IS NOT NULL
ORDER BY qc.CreateTime DESC;

-- TEST CASE C1: Get Step-specific scenario
SELECT TOP 1
    ht.HangTagId,
    ht.ProductAttribute,
    qc.StepCode,
    qc.QualityControlId,
    ttl.TaskId,
    'Use this for C1 step-specific test' AS TestNote
FROM MES.HangTagModel ht
INNER JOIN MES.ThucThiLenhSanXuatModel ttl ON ht.CustomerReference = ttl.ParentTaskId
INNER JOIN MES.QualityControlModel qc ON ttl.TaskId = qc.CustomerReference
WHERE ht.ProductAttribute = qc.ProductAttribute
    AND qc.StepCode IS NOT NULL
    AND qc.StepCode != ''
ORDER BY qc.CreateTime DESC;
```

### 📋 **Quick Test Data Selection Guide**

Based on your database structure, here's what to look for:

1. **For Test A1 (Production First)**: Use HangTags where `WorkflowStatus = 'Production First'`
2. **For Test A2 (Multiple ProductAttributes)**: Use CustomerReferences with `ProductAttributeCount > 1`
3. **For Test B1 (QC First)**: Use QualityControlModels where `CustomerReference IS NULL`
4. **For Test C1 (Step Selection)**: Use any HangTag with valid StepCode

### ⚠️ **Data Quality Issues to Watch**

From your results, I notice:
- Multiple `ThucThiLenhSanXuatModel` records per HangTag (causing duplicates)
- Inconsistent QC record linking
- Potential data integrity issues

Use the refined queries above to get cleaner, more reliable test data.

### 🎯 **Recommended Next Steps**

1. Run the **refined HangTagSummary query** to get clean overview
2. Use the **specific test case queries** to get targeted test data
3. **Analyze your specific HangTag** using the deep dive queries
4. Choose **one HangTag per test case** to avoid confusion

### 📊 **Success Criteria**

1. **ProductAttribute Accuracy**: 100% exact match when expected
2. **Fallback Reliability**: Phase 1 fallback works in QC-first scenarios
3. **No Regression**: Existing production-first flow unaffected
4. **Performance**: Response time < 500ms for normal cases
5. **Error Handling**: Clear error messages, no exceptions
6. **Data Integrity**: No duplicate or corrupted records

---

## 12. Future Enhancements

### 🚀 Potential Improvements

#### 11.1. Real-time Synchronization
```dart
// WebSocket integration for real-time QC status updates
Stream<QCStatusUpdate> watchQCStatus(String hangTagId) {
    return WebSocketService.listen('/qc-updates/$hangTagId');
}
```

#### 11.2. Offline Capability
```dart
// Cache QC data for offline operation
await QCOfflineStorage.cacheQCData(qualityControlData);
// Sync when network available
await QCSync.uploadPendingQC();
```

#### 11.3. Analytics Integration
```csharp
// Track QC performance metrics
QCAnalytics.TrackInspectionTime(stepCode, inspectionDuration);
QCAnalytics.TrackDefectRate(productCode, defectCount);
```

## 12. Proposed Enhanced Solution: QC-First Record Creation

### 🚀 **Better Approach: Create QualityControlModel on QC Scan**

Instead of allowing QC with null data, **create actual QualityControlModel records** when QC scans first:

#### ✅ **Phase 1 Implementation Status: COMPLETED**

**Phase 1 (Fallback Lookup)** has been implemented in the following files:
- `QualityControlController.cs` - Updated GetQualityControl2Async and GetCongDoanInfo with fallback logic
- `QualityControlRepository.cs` - Added CreateQualityControlForQCFirst method *(planned for Phase 2)*

#### 🔄 **When to Call CreateQualityControlForQCFirst**

Currently **NOT CALLED** in Phase 1. Here's when it should be triggered:

**Scenario**: QC worker scans HangTag before production worker
**Trigger Point**: In `GetQualityControl2Async` after all fallback strategies fail
**Current Behavior**: Returns `NotFound` error
**Desired Behavior**: Create QualityControlModel and allow QC to proceed

#### ✅ **ProductAttribute Fix: COMPLETED (10/05/2025)**

**Fixed ProductAttribute Accuracy Issue:**
- Removed problematic intermediate fallback in `GetQualityControl2Async` that could return wrong ProductAttribute
- Now uses exact ProductAttribute matching in primary lookup, with Phase 1 fallback ensuring correct ProductAttribute
- Phase 1 fallback prioritizes exact ProductAttribute + LSXSAP match over random selection

#### 12.0. **Phase 1: Fallback Lookup Logic**

```csharp
// GetQualityControl2Async & GetCongDoanInfo Enhanced Logic
if (qualityControlModel == null) {
    // Phase 1 Fallback: Try lookup using LSXSAP instead of CustomerReference
    var parentTask = _context.TaskModel.FirstOrDefault(x => x.TaskId == customerReference);
    if (parentTask != null) {
        // Primary fallback: Match ProductAttribute + LSXSAP
        qualityControlModel = (from qc in _context.QualityControlModel
                             join task in _context.TaskModel on qc.LSXSAP equals task.Summary
                             where qc.ProductAttribute == productAttribute 
                             && task.TaskId == customerReference
                             select qc).FirstOrDefault();
        
        // Secondary fallback: Match only LSXSAP
        if (qualityControlModel == null) {
            qualityControlModel = (from qc in _context.QualityControlModel
                                 join task in _context.TaskModel on qc.LSXSAP equals task.Summary
                                 where task.TaskId == customerReference
                                 select qc).FirstOrDefault();
        }
    }
}

// Enhanced ThucThiLenhSanXuatModel lookup
if (ttlsx == null && !string.IsNullOrEmpty(qualityControlModel.LSXSAP)) {
    ttlsx = (from ttl in _context.ThucThiLenhSanXuatModel
             join task in _context.TaskModel on ttl.ParentTaskId equals task.TaskId
             where task.Summary == qualityControlModel.LSXSAP
             && ttl.Barcode == HangTagId
             select ttl).FirstOrDefault();
}
```

**Key Benefits:**
- ✅ QC can scan before production without errors
- ✅ Uses LSXSAP (Summary) as alternative lookup key
- ✅ Maintains backward compatibility with existing production-first flow
- ✅ Progressive fallback: ProductAttribute + LSXSAP → LSXSAP only

#### 12.1. Modified CreateQualityControl Logic
```csharp
// New: CreateQualityControlForQCFirst method
public void CreateQualityControlForQCFirst(string hangTagId, string stepCode, Guid qualityChecker)
{
    // Get HangTag and related production order info
    var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == hangTagId);
    if (hangTag == null) return;

    // Get production order summary (LSXSAP) from parent task
    var parentTask = _context.TaskModel.FirstOrDefault(x => x.TaskId == hangTag.CustomerReference);
    if (parentTask == null) return;

    var data = new QualityControlModel()
    {
        QualityControlId = Guid.NewGuid(),
        // Use production order info available from HangTag
        LSXSAP = parentTask.Summary,  // Use Summary as LSXSAP
        ProductAttribute = hangTag.ProductAttribute,
        StepCode = stepCode,
        CustomerReference = null,     // Will be updated when production scans
        
        // Basic QC info
        QualityChecker = qualityChecker,
        QualityDate = DateTime.Now,
        CreateTime = DateTime.Now,
        Status = false,
        
        // Extract other info from parent task
        ProductCode = parentTask.ProductCode,
        ProductName = parentTask.ProductName,
        SaleOrgCode = GetSaleOrgFromTask(parentTask.TaskId),
    };

    // Check if already exists (prevent duplicates)
    var existing = _context.QualityControlModel.FirstOrDefault(x => 
        x.LSXSAP == data.LSXSAP &&
        x.ProductAttribute == data.ProductAttribute &&
        x.StepCode == data.StepCode &&
        x.CustomerReference == null  // Only match QC-first records
    );

    if (existing == null)
    {
        _context.Entry(data).State = EntityState.Added;
        _context.SaveChanges();
    }
}
```

#### 12.2. Enhanced Lookup Strategy
```csharp
// Modified GetCongDoanInfo with fallback lookup
public IActionResult GetCongDoanInfo(Guid? hangTagId, string StepCode)
{
    QualityControlModel qualityControlModel = null;

    // Strategy 1: Normal lookup (production scanned first)
    qualityControlModel = (from ht in _context.HangTagModel
                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                           where ht.HangTagId == hangTagId && qc.StepCode == StepCode
                           select qc).FirstOrDefault();

    // Strategy 2: Fallback lookup (QC scanned first, CustomerReference is null)
    if (qualityControlModel == null)
    {
        var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == hangTagId);
        if (hangTag != null)
        {
            var parentTask = _context.TaskModel.FirstOrDefault(x => x.TaskId == hangTag.CustomerReference);
            if (parentTask != null)
            {
                qualityControlModel = _context.QualityControlModel.FirstOrDefault(x =>
                    x.LSXSAP == parentTask.Summary &&
                    x.ProductAttribute == hangTag.ProductAttribute &&
                    x.StepCode == StepCode &&
                    x.CustomerReference == null  // QC-first record
                );
            }
        }
    }

    // Strategy 3: Create new if not found (QC-first scenario)
    if (qualityControlModel == null)
    {
        CreateQualityControlForQCFirst(hangTagId.ToString(), StepCode, CurrentUser.AccountId);
        // Retry lookup after creation
        // ... (repeat Strategy 2 logic)
    }

    // Continue with existing logic...
}
```

#### 12.3. Production Integration
```csharp
// Enhanced CreateQualityControl2 to handle existing QC records
public void CreateQualityControl2(ConfirmWorkCenterViewModel confirmWorkCenterViewModel)
{
    var ttlxs = new ProductionManagementRepository(_context).GetExecutionTaskByTaskId(confirmWorkCenterViewModel.TaskId);
    
    // Check if QC-first record exists
    var existingQCFirst = _context.QualityControlModel.FirstOrDefault(x =>
        x.LSXSAP == ttlxs.ProductionOrder_SAP &&
        x.ProductAttribute == ttlxs.ProductAttributes &&
        x.StepCode == confirmWorkCenterViewModel.StepCode &&
        x.CustomerReference == null  // QC-first record
    );

    if (existingQCFirst != null)
    {
        // Update existing QC-first record with production data
        existingQCFirst.CustomerReference = ttlxs.TaskId;
        existingQCFirst.ConfirmDate = confirmWorkCenterViewModel.WorkCenterConfirmTime;
        existingQCFirst.VBELN = ttlxs.Property1;
        existingQCFirst.Environmental = GetEnvironmental(ttlxs.Property1);
        // ... update other production-specific fields
    }
    else
    {
        // Create new record (existing logic)
        var data = new QualityControlModel() { /* existing logic */ };
        // ... existing creation logic
    }
    
    _context.SaveChanges();
}
```

### 🔄 **Data Flow with Enhanced Solution**

#### **Current Phase 1 Flow**
```
1. QC scans HangTagId → GetQualityControl2Async
2. Primary lookup fails (no ThucThiLenhSanXuatModel)
3. Phase 1 fallback succeeds/fails (LSXSAP-based lookup)
4. If all fail → Returns NotFound("Sản xuất chưa quét MES")
```

#### **Phase 2 Enhanced Flow (with CreateQualityControlForQCFirst)**
```
1. QC scans HangTagId → GetQualityControl2Async
2. Primary lookup fails (no ThucThiLenhSanXuatModel)
3. Phase 1 fallback fails (no existing QC record)
4. **NEW**: CreateQualityControlForQCFirst triggered
   - LSXSAP = parentTask.Summary
   - ProductAttribute = hangTag.ProductAttribute  
   - StepCode = null (set when step selected)
   - CustomerReference = null
5. QC can proceed with inspection
6. Later: Production scans → Updates CustomerReference + production fields
```

#### **Integration Points for CreateQualityControlForQCFirst**

**1. In GetQualityControl2Async (Line ~590)**
```csharp
// Current code (Phase 1)
if (qualityControlModel == null)
{
    return NotFound(new
    {
        Code = HttpStatusCode.NotFound,
        Success = false,
        Data = "Sản xuất chưa quét MES",
    });
}

// Phase 2 Enhancement
if (qualityControlModel == null)
{
    // Try to create QC record for QC-first scenario
    var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == HangTagId);
    if (hangTag != null && hangTag.CustomerReference != null)
    {
        _unitOfWork.QualityControlRepository.CreateQualityControlForQCFirst(
            HangTagId.ToString(), 
            null, // StepCode will be set when step is selected
            CurrentUser.AccountId
        );
        
        // Retry lookup after creation
        qualityControlModel = // ... retry Phase 1 fallback logic
    }
    
    if (qualityControlModel == null)
    {
        return NotFound(new
        {
            Code = HttpStatusCode.NotFound,
            Success = false,
            Data = "Sản xuất chưa quét MES",
        });
    }
}
```

**2. In GetCongDoanInfo (Line ~2055)**
```csharp
// Current code (Phase 1)
if (qualityControlModel == null)
{
    return NotFound(new
    {
        Code = HttpStatusCode.NotFound,
        Success = false,
        Data = "Chưa confirm công đoạn " + StepCode,
    });
}

// Phase 2 Enhancement
if (qualityControlModel == null)
{
    // Create QC record for specific step
    var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == hangTagId);
    if (hangTag != null)
    {
        _unitOfWork.QualityControlRepository.CreateQualityControlForQCFirst(
            hangTagId.ToString(), 
            StepCode, 
            CurrentUser.AccountId
        );
        
        // Retry lookup after creation
        qualityControlModel = // ... retry Phase 1 fallback logic with StepCode
    }
}
```

#### Scenario A: QC Scans First (Phase 2)
```
1. QC scans HangTagId → GetQualityControl2Async
2. Primary lookup fails (no ThucThiLenhSanXuatModel)
3. Phase 1 fallback fails (no existing QC record)
4. CreateQualityControlForQCFirst creates new record
5. QC selects step → GetCongDoanInfo → Updates StepCode
6. QC performs inspection
7. Later: Production scans → Updates CustomerReference + production fields
```

#### Scenario B: Production Scans First (Current Flow - Works in Phase 1)
```
1. Production scans → CreateQualityControl2 → Normal QualityControlModel
2. QC scans → Primary lookup succeeds → Normal flow
```

### 📋 **Summary: When CreateQualityControlForQCFirst is Called**

| **Scenario** | **Current Phase 1** | **Phase 2 (with CreateQualityControlForQCFirst)** |
|--------------|--------------------|----------------------------------------------------|
| **QC scans before production** | ❌ Returns NotFound error | ✅ Creates QualityControlModel, allows QC |
| **QC selects unconfirmed step** | ❌ Returns "Chưa confirm công đoạn" | ✅ Creates QC record for that step |
| **Production scans first** | ✅ Works normally | ✅ Works normally |
| **Existing QC records** | ✅ Phase 1 fallback finds them | ✅ Phase 1 fallback finds them |

### 🎯 **Call Points Summary**

1. **GetQualityControl2Async**: After Phase 1 fallback fails, before returning NotFound
2. **GetCongDoanInfo**: After step-specific lookup fails, before returning "Chưa confirm công đoạn"

### ⚡ **Quick Implementation Status**

- **Phase 1**: ✅ Implemented (fallback lookup only)
- **CreateQualityControlForQCFirst method**: 📝 Written but not called
- **Phase 2 integration**: ❌ Not implemented yet

**To enable QC-first workflow**: Integrate the CreateQualityControlForQCFirst calls at the specified integration points above.

### 💡 **Benefits of This Approach**

1. **Complete QC Records**: Real QualityControlModel entries, not null data handling
2. **Backwards Compatible**: Doesn't break existing production-first flow  
3. **Data Integrity**: Proper relationships maintained when production updates
4. **Flexible Workflow**: Supports both QC-first and production-first scenarios
5. **Efficient Lookups**: Uses composite keys for fast retrieval

### 🔧 **Implementation Priority**

1. **Phase 1**: Implement fallback lookup strategy (Strategies 1-2)
2. **Phase 2**: Add QC-first record creation (Strategy 3)  
3. **Phase 3**: Enhance production integration to update existing records

This approach provides a **complete solution** rather than a workaround, making the system truly flexible for different workflow scenarios.

---
