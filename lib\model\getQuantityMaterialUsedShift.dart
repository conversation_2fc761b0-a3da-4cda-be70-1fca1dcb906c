class GetQuantityMaterialUsedShift {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataQuantityMaterialUsedShift>? data;

  GetQuantityMaterialUsedShift({this.code,
    this.isSuccess,
    this.message,
    this.data});

  GetQuantityMaterialUsedShift.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataQuantityMaterialUsedShift>[];
      json['data'].forEach((v) {
        data!.add(DataQuantityMaterialUsedShift.fromJson(v));
      });
    }
  }
    Map<String, dynamic> toJson() {
      final Map<String, dynamic> data = <String, dynamic>{};
      data['code'] = code;
      data['isSuccess'] = isSuccess;
      data['message'] = message;
      if (this.data != null) {
        data['data'] = this.data!.map((v) => v.toJson()).toList();
      }
      return data;
    }
}
class DataQuantityMaterialUsedShift {
  String? slocId;
  String? slocDisplay;
  double? quantity;
  String? unit;

  DataQuantityMaterialUsedShift(
      {this.slocId, this.slocDisplay, this.quantity, this.unit});

  DataQuantityMaterialUsedShift.fromJson(Map<String, dynamic> json) {
    slocId = json['slocId'];
    slocDisplay = json['slocDisplay'];
    quantity = json['quantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['slocId'] = slocId;
    data['slocDisplay'] = slocDisplay;
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}