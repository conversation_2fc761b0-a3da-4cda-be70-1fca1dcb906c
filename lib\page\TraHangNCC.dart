import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';

import '../model/RequestTraHangNCC.dart';
import '../model/userModel.dart';
import '../element/ButtonInventoryMNG.dart';
import '../repository/function/traHangNCCFunction.dart';
import '../screenArguments/screenArgumentMaterialUnused.dart';
import '../screenArguments/screenArgumentStatisticsMaterials.dart';
import '../screenArguments/screenArgumentsCreateNewTraHangNCC.dart';
import 'ListQCNVL.dart';

class TraHangNCC extends StatefulWidget {
  const TraHangNCC({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
  }) : super(
          key: key,
        );

  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;

  @override
  _TraHangNCCState createState() => _TraHangNCCState();
}

class _TraHangNCCState extends State<TraHangNCC> {
  bool _isLoadingScroll = false;
  late bool _timeOut;
  bool _isLoading = false;
  bool _isNotWifi = false;
  String _error = "";

  List<DataRequestReturnVendorModel> _listRequestReturnVendorModel = [];

  @override
  void initState() {
    super.initState();
    _getListData();
    // _controllerListView.addListener(_goScroll);
  }

  Future<void> _getListData() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          // _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false);
        });
        final dataResponse = await Future.wait([
          TraHangNCCFunction.getListData(widget.token.toString()),
        ]);
        if (!mounted) return;
        setState(() {
          _isLoading = false;
        });
        if (dataResponse.isNotEmpty) {
          if ((dataResponse[0] as List<DataRequestReturnVendorModel>?) != null) {
            setState(() {
              _listRequestReturnVendorModel = dataResponse[0] as List<DataRequestReturnVendorModel>;
              // _pageNumber += 1;
              // _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false);
              // _lsDataLstQCNVLByFilter = dataResponse[0] as List<DataLstQCNVLByFilter>? ?? [];
              // _lsDataLstQCNVLByFilter.sort((a, b) {
              //   return b.barcodeCreateDate!.compareTo(a.barcodeCreateDate!);
              // });
            });
          }
          // _lsDataGetStatusGoodArrive = dataResponse[1] as List<DataGetStatusGoodsArrive>;
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _timeOut = false;
        _error = error.toString();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: _listRequestReturnVendorModel.isNotEmpty ? Colors.grey.shade300 : Colors.white,
        appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            // 'Trả hàng NCC',
            'Danh sách yêu cầu trả hàng NCC',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          ),
          actions: [
            IconButton(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              hoverColor: Colors.transparent,
              icon: Icon(
                Icons.add,
                size: 19.sp,
                color: Colors.white,
              ),
              onPressed: _isLoading == true
                  ? null
                  : () async {
                      final check = await Navigator.pushNamed(
                        context,
                        "/CreateNewTraHangNCC",
                        arguments: ScreenArgumentsCreateNewTraHangNCC(token: widget.token, dateTimeOld: widget.dateTimeOld),
                      );

                      _getListData();

                      if (check == null) return;

                      // if (!mounted) return;
                      // if (check == null) return;
                      // if ((check as QRCodePageListQCModel).isScan == true) {
                      //   final checkSave = await Navigator.pushNamed(context, '/BottomNavigatorBar',
                      //       arguments: ScreenArgumentNavigatorBar("", widget.dateTimeOld, check.qrCode.toString(), "qr", widget.userModel));
                      //   if (!mounted) return;
                      //   if (checkSave == null) return;
                      //   if (checkSave == true) {
                      //     _refresh();
                      //   }
                      // }
                    },
            ),
            // // Toggle search drawer
            // Builder(
            //   builder: (BuildContext context) {
            //     return IconButton(
            //       highlightColor: Colors.transparent,
            //       splashColor: Colors.transparent,
            //       hoverColor: Colors.transparent,
            //       icon: Icon(Icons.search_outlined, size: 19.sp, color: Colors.white),
            //       onPressed: () {
            //         Scaffold.of(context).openEndDrawer();
            //       },
            //     );
            //   },
            // ),
          ],
        ),
        body: _isLoading == true
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        ListView.separated(
                          physics: _isLoadingScroll == true ? const NeverScrollableScrollPhysics() : const ScrollPhysics(),
                          // key: ObjectKey(_listTraHangNCC[0]),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemCount: _listRequestReturnVendorModel.length,
                          // controller: _controllerListView,
                          separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                          itemBuilder: (context, index) {
                            final item = _listRequestReturnVendorModel[index];
                            return GestureDetector(
                                onTap: () {
                                  Navigator.pushNamed(
                                    context,
                                    '/CreateNewTraHangNCC',
                                    arguments: ScreenArgumentsCreateNewTraHangNCC(
                                        token: widget.token, dateTimeOld: widget.dateTimeOld, id: _listRequestReturnVendorModel[index].id),
                                  ).then((value) {
                                    if (value == true) {
                                      _getListData();
                                    }
                                  }).onError((error, stackTrace) {
                                    debugPrint('Lỗi:$error');
                                  });
                                },
                                child: Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      RichTextWidget(
                                        title: "Thời gian tạo: ",
                                        content: item.createTime == null ? "" : DateFormat("HH:mm dd/MM/yyyy").format(item.createTime!),
                                      ),
                                      const Divider(),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            flex: 6,
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                RichTextWidget(
                                                  title: "Mã phiếu: ",
                                                  content: item.requestReturnVendorCode,
                                                ),
                                                RichTextWidget(
                                                  title: "Loại: ",
                                                  content: item.returnType == 1 ? "Trả hàng theo PO mua hàng" : "Trả hàng theo PO trả hàng",
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                                              decoration: BoxDecoration(
                                                color: item.warehouseStaffId.toString() == "null" ? Colors.amber : Colors.green,
                                                borderRadius: BorderRadius.circular(3.r),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  item.warehouseStaffId.toString() == "null" ? "Kho chưa xác nhận" : "Kho đã xác nhận",
                                                  style: TextStyle(fontSize: 12.sp, color: Colors.white),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Divider(),

                                      item.returnType == 1
                                          ? RichTextWidget(
                                              title: "NCC: ",
                                              content: item.vendorCode + " | " + item.vendorName,
                                            )
                                          : Container(),

                                      RichTextWidget(
                                        title: "PO: ",
                                        content: item.listPo.toString(),
                                      ),
                                      // SizedBox(height: 10.h),
                                      // RichTextWidget(
                                      //   title: "Thời gian QC: ",
                                      //   content: item.qcCreateTime == null ? "" : DateFormat("HH:mm dd/MM/yyyy").format(item.qcCreateTime!),
                                      // ),
                                      // SizedBox(height: 10.h),
                                      // RichTextWidget(
                                      //   title: "Trạng thái về nhà máy: ",
                                      //   content:
                                      //       _lsDataLstQCNVLByFilter[index].isGoodsArrive == null || _lsDataLstQCNVLByFilter[index].isGoodsArrive == false
                                      //           ? "Vừa in Barcode"
                                      //           : "Đã về nhà máy",
                                      // ),
                                      // SizedBox(height: 10.h),
                                    ],
                                  ),
                                ));
                          },
                        ),
                        // Visibility(
                        //   visible: _isLoadingScroll,
                        //   child: Align(
                        //     alignment: Alignment.center,
                        //     child: Container(
                        //       decoration: BoxDecoration(
                        //         boxShadow: [
                        //           BoxShadow(
                        //             color: Colors.grey.withOpacity(0.5),
                        //             spreadRadius: 3,
                        //             blurRadius: 20,
                        //             offset: const Offset(0, 1),
                        //           ),
                        //         ],
                        //       ),
                        //       child: CircleAvatar(
                        //           backgroundColor: Colors.white,
                        //           child: Padding(
                        //             padding: REdgeInsets.all(6),
                        //             child: const CircularProgressIndicator(),
                        //           )),
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                    // const SizedBox(height: 10),
                    // if (permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Ghi nhận NVL chưa sử dụng hết") != null)
                    //   ButtonInventoryMNG(
                    //     txt: "Tạo yêu cầu trả hàng NCC",
                    //     icon: const Icon(Icons.edit_note_rounded, size: 20, color: Colors.white),
                    //     route: () => Navigator.pushNamed(context, "/CreateNewTraHangNCC",
                    //         arguments: ScreenArgumentsCreateNewTraHangNCC(token: token, dateTimeOld: dateTimeOld)),
                    //   ),
                    SizedBox(height: 200.0),
                    // ButtonInventoryMNG(
                    //   txt: "Xác nhận trả hàng NCC",
                    //   icon: const Icon(Icons.note_alt_outlined, size: 20, color: Colors.white),
                    //   route: () => Navigator.pushNamed(context, "/StatisticsMaterials",
                    //       arguments: ScreenArgumentStatisticsMaterial(widget.token, widget.dateTimeOld)),
                    // ),
                  ],
                ),
              ));
  }
}
