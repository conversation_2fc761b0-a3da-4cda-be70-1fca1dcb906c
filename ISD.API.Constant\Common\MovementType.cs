﻿namespace ISD.API.Constant.Common
{
    public class MovementType
    {
        public const string Receive = "101"; //Nhập kho tích hợp SAP
        public const string DeliverOneStep = "311"; //Chuyển xuất kho 1 bước
        public const string DeliverTwoStep = "313"; //Chuyển xuất kho
        public const string ReceiveTwoStep = "315"; //Chuyển nhập kho
        public const string MaterialUsedShift = "200"; //Thống kế NVL sử dụng trong ca
        public const string MaterialUnused = "201"; //NVL trả lại
        public const string Deliver = "261"; //Xuất vào lsx
        public const string Allocate = "262"; //Phân bổ
    }

    public enum MaterialType
    {
        MainMaterial = 1, //Vật liệu chính

        ChemicalMaterial, //Vật liệu hóa chất

        OtherMaterial     //Vật liệu khác
    }
}
