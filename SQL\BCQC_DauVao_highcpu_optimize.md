Of course. I can help analyze this execution plan and provide an optimized script.

This execution plan is for a very complex query that joins many tables. The optimizer even timed out trying to find a better plan (`StatementOptmEarlyAbortReason="TimeOut"`), which is a clear sign that the query needs to be simplified or supported with better indexing.

Here is a summary of the key performance issues identified from the plan:

1.  **Missing Indexes**: The plan has identified two missing indexes with a high-impact score (15.3 and 15.2). Creating them is the highest priority and will likely yield significant performance gains.
2.  **Inefficient Query Structure**: The query appears to scan a large number of records from `QualityControlModel` and then performs lookups, instead of starting from the more selective filter (the `PurchaseOrderCode`).
3.  **`SELECT DISTINCT`**: The query uses `SELECT DISTINCT` across a large number of columns, which forces the database to perform a costly sort operation on the entire result set to remove duplicates. This is often a symptom of incorrect joins that produce more rows than expected.
4.  **Data Type Conversion**: A warning in the plan (`PlanAffectingConvert`) shows that the `POItem` column is being converted to an integer. This prevents the efficient use of indexes on that column and can lead to inaccurate estimates by the query optimizer.

Below are my recommendations to optimize the query.

### Recommendation 1: Create Missing Indexes

The most critical step is to create the indexes suggested by the execution plan. These will help the database retrieve data much more efficiently by avoiding costly table scans and lookups.

Here are the T-SQL commands to create these indexes:


```bash
-- It seems you've provided SQL scripts. I'll execute them directly against the database.
CREATE INDEX IX_MESP2_RawMaterial_PurchaseOrderDetail_Mapping_PurchaseOrderDetailId
ON MESP2.RawMaterial_PurchaseOrderDetail_Mapping (PurchaseOrderDetailId)
INCLUDE (RawMaterialCardId, Quantity);

CREATE INDEX IX_MES_QualityControl_QCInformation_Mapping_QualityControlId
ON MES.QualityControl_QCInformation_Mapping (QualityControlId)
INCLUDE (SoSanPhamLoi);

```

Of course. I have analyzed the execution plan and identified several areas for optimization. The query is quite complex, leading to the query optimizer timing out and potentially choosing a suboptimal plan.

Here is a summary of the main issues:

1.  **Missing Indexes**: The plan suggests two high-impact missing indexes. Creating them is the top priority and should provide a significant performance boost.
2.  **Inefficient `SELECT DISTINCT`**: The use of `SELECT DISTINCT` on a large result set with many columns forces a very expensive sort operation to remove duplicate rows. These duplicates are likely caused by one-to-many joins (e.g., a single QC ticket having multiple errors).
3.  **Potential for Duplicate Rows**: Joins to `QualityControl_Error_Mapping` and `WarehouseTransactionModel` can produce multiple rows for a single quality control event, which `DISTINCT` was hiding at a high cost.
4.  **Data Type Mismatch**: There's a warning about converting the `POItem` column from a string to an integer. This prevents the database from using an index on that column efficiently.

Here are the optimized script and recommendations.

### Recommendation 1: Add Missing Indexes

The following T-SQL commands will create the missing indexes. Please execute these in your SQL Server environment (like SQL Server Management Studio). This is the most important step for improving performance.

```sql
-- Index to support joining Purchase Order Details with Raw Material mapping
CREATE INDEX IX_MESP2_RawMaterial_PurchaseOrderDetail_Mapping_PurchaseOrderDetailId
ON MESP2.RawMaterial_PurchaseOrderDetail_Mapping (PurchaseOrderDetailId)
INCLUDE (RawMaterialCardId, Quantity);

-- Index to support joining QC information
CREATE INDEX IX_MES_QualityControl_QCInformation_Mapping_QualityControlId
ON MES.QualityControl_QCInformation_Mapping (QualityControlId)
INCLUDE (SoSanPhamLoi);
```

### Recommendation 2: Optimized Query

The original query can be rewritten to be more efficient and readable by using Common Table Expressions (CTEs) and by handling the duplicate rows explicitly, which removes the need for the expensive `DISTINCT` operation.

Here is the optimized query:

```sql
-- Declare parameters for testing
DECLARE @PO NVARCHAR(100) = '4100078736';
DECLARE @FromDate DATETIME = '2025-07-28 00:00:00.000';
DECLARE @ToDate DATETIME = '2025-08-03 23:59:59.000';

-- Use Common Table Expressions (CTEs) to break down the query and filter data early.
-- 1. Start from the most selective part of the query: Purchase Orders.
WITH PO_Details AS (
    SELECT
        pod.PurchaseOrderDetailId,
        pod.PurchaseOrderCode,
        pod.POItem,
        pod.Plant,
        pod.POQuantity,
        pod.ProductCode,
        CASE
            WHEN pod.SDDocument IS NULL OR pod.SDDocument = '' THEN pod.WBSElement
            ELSE pod.SDDocument + ' / ' + pod.Item
        END AS SOWbs
    FROM MESP2.PurchaseOrderDetailModel pod
    WHERE (@PO IS NULL OR pod.PurchaseOrderCode = @PO)
),

-- 2. Link to Raw Material Cards.
RawMaterialLink AS (
    SELECT
        p.PurchaseOrderCode,
        p.POItem,
        p.Plant,
        p.POQuantity,
        p.SOWbs,
        rmm.RawMaterialCardId,
        rmm.Quantity AS SoLuongGiaoHang
    FROM PO_Details p
    INNER JOIN MESP2.RawMaterial_PurchaseOrderDetail_Mapping rmm ON p.PurchaseOrderDetailId = rmm.PurchaseOrderDetailId
),

-- 3. Aggregate multiple error records for each QC ticket to avoid duplicates from the error mapping table.
QCErrorsAggregated AS (
    SELECT
        qce.QualityControlId,
        -- Using MIN() to pick one value when multiple errors exist.
        -- If all values need to be displayed, consider STRING_AGG (for SQL Server 2017+) or another aggregation method.
        MIN(cat.CatalogText_vi) AS LoaiLoi,
        MIN(qce.CatalogCode) AS MaLoi,
        MIN(qce.PhuongAnXuLy) AS PhuongAnXuLy,
        SUM(CASE WHEN qce.LevelError = 'NGHIEMTRONG' THEN qce.QuantityError ELSE 0 END) AS LoiNghiemTrong,
        SUM(CASE WHEN qce.LevelError = 'NANG' THEN qce.QuantityError ELSE 0 END) AS LoiNang,
        SUM(CASE WHEN qce.LevelError = 'NHE' THEN qce.QuantityError ELSE 0 END) AS LoiNhe
    FROM MES.QualityControl_Error_Mapping qce
    LEFT JOIN tMasterData.CatalogModel cat ON qce.CatalogCode = cat.CatalogCode
    GROUP BY qce.QualityControlId
),

-- 4. Get the latest warehouse transaction for each raw material card to avoid duplicates.
LatestWarehouseTransaction AS (
    SELECT
        ReferenceDocumentId,
        DocumentDate,
        ROW_NUMBER() OVER(PARTITION BY ReferenceDocumentId ORDER BY DocumentDate DESC, WarhouseTransactionId DESC) as rn
    FROM MESP2.WarehouseTransactionModel
)

-- Final SELECT joining all the pre-filtered and aggregated data.
SELECT
    qc.QualityControlId,
    qc.QualityControlCode,
    qc.QualityDate AS NgayKiemTra,
    rml.Plant AS NhaMay,
    receiveInfo.GoodsReceivedNote AS SoPhieuNhapKho,
    CASE
        WHEN LEFT(rml.PurchaseOrderCode, 2) = '49' THEN N'BTP gia công'
        ELSE 'NVL'
    END AS LoaiHangHoa,
    lwt.DocumentDate AS NgayGiaoHang,
    rml.PurchaseOrderCode AS PO,
    CAST(rml.POItem AS INT) AS POLine, -- WARNING: This cast can impact performance. Change column type if possible.
    rml.SOWbs,
    p.ProductCode AS MaHangHoa,
    p.ProductName AS TenHangHoa,
    v.ShortName AS NhaCungCap,
    rmc.Specifications AS QuyCach,
    rml.POQuantity AS SoLuongDatHang,
    rml.SoLuongGiaoHang,
    '' AS SoLuongDaNhan, -- Placeholder from original query
    qcd.InspectionQuantity AS SoLuongKiemTra,
    qce.LoaiLoi,
    qce.MaLoi,
    qci.SoSanPhamLoi,
    qce.LoiNghiemTrong,
    qce.LoiNang,
    qce.LoiNhe,
    rmc.Unit AS DonViTinh,
    -- Handle potential division by zero
    CASE
        WHEN qcd.InspectionQuantity > 0 THEN CONVERT(NVARCHAR(20), CAST(ROUND((qci.SoSanPhamLoi * 1.0 / qcd.InspectionQuantity) * 100, 2) AS DECIMAL(18, 2))) + '%'
        ELSE '0%'
    END AS TyLePhanTramLoi,
    acc.EmployeeCode + ' | ' + COALESCE(acc.FullName, acc.Username) AS QcKiemTra,
    acc.EmployeeCode AS QcKiemTraCode,
    COALESCE(acc.FullName, acc.Username) AS QcKiemTraName,
    qc.Result AS KetQua,
    '' AS ViTriLoiNhaMay, -- Placeholders from original query
    '' AS ViTriLoiPhanXuong,
    '' AS ViTriLoiToChuyen,
    '' AS CaNhanLoiQuanDoc,
    '' AS CaNhanLoiToTruong,
    '' AS CaNhanLoiQAQC,
    '' AS CaNhanLoiKCS,
    '' AS CaNhanLoiCongNhan,
    qce.PhuongAnXuLy,
    qc.CreateTime
FROM RawMaterialLink rml
INNER JOIN MES.QualityControlModel qc ON rml.RawMaterialCardId = qc.CustomerReference
LEFT JOIN MES.QualityControlDetailModel qcd ON qc.QualityControlId = qcd.QualityControlId
LEFT JOIN MES.QualityControl_QCInformation_Mapping qci ON qc.QualityControlId = qci.QualityControlId
LEFT JOIN QCErrorsAggregated qce ON qc.QualityControlId = qce.QualityControlId
LEFT JOIN MESP2.ReceiveInformationModel receiveInfo ON rml.RawMaterialCardId = receiveInfo.RawMaterialCardId
LEFT JOIN MESP2.RawMaterialCardModel rmc ON rml.RawMaterialCardId = rmc.RawMaterialCardId
LEFT JOIN LatestWarehouseTransaction lwt ON rml.RawMaterialCardId = lwt.ReferenceDocumentId AND lwt.rn = 1
LEFT JOIN tMasterData.VendorModel v ON rmc.VendorCode = v.SupplierNumber
LEFT JOIN tSale.ProductLatestModel p ON qc.ProductCode = p.ProductCode
LEFT JOIN pms.AccountModel acc ON qc.QualityChecker = acc.AccountId
WHERE
    qc.QualityType = 'DAUVAO'
    AND qc.Result IS NOT NULL
    AND (@FromDate IS NULL OR qc.QualityDate >= @FromDate)
    AND (@ToDate IS NULL OR qc.QualityDate <= @ToDate)
ORDER BY
    qc.QualityDate DESC, qc.CreateTime DESC;

```

### Key Changes in the Optimized Script:

*   **CTEs for Clarity**: The query is broken into logical steps using CTEs, which makes it easier to understand and allows the optimizer to work with smaller, pre-filtered sets of data.
*   **Removed `DISTINCT`**: The expensive `DISTINCT` operation has been removed.
*   **Aggregation for Errors**: The `QCErrorsAggregated` CTE handles the one-to-many join with the errors table by aggregating the results using `GROUP BY`. This is much more efficient than fetching all rows and then removing duplicates.
*   **Handling Transaction Duplicates**: The `LatestWarehouseTransaction` CTE uses `ROW_NUMBER()` to select only the most recent warehouse transaction for each raw material card, preventing another source of duplicate rows.
*   **Improved Filtering**: The query structure now starts with filtering on the purchase order (`@PO`), which should be more selective and reduce the amount of data processed in later joins.

### Recommendation 3: Database Schema Change

For a long-term fix, consider changing the data type of the `PurchaseOrderDetailModel.POItem` column to a numeric type (e.g., `INT` or `BIGINT`) if it only stores numbers. This will eliminate the need for the `CAST` operation in the query and allow for more efficient index usage.