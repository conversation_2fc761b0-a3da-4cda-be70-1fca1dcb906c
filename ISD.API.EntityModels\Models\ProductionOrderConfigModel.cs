﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductionOrderConfigModel", Schema = "MES")]
    public partial class ProductionOrderConfigModel
    {
        [Key]
        public int ProductionOrderConfigModelId { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string Plant { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string OrderType { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string ConfigCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDate { get; set; }
        public Guid? CreatedBy { get; set; }
    }
}