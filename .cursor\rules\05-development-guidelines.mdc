---
description: 
globs: 
alwaysApply: false
---
# Development Guidelines

## Flutter Version
- Flutter version information in [NOTE-flutter-version.md](mdc:TTF_MES_Mobile/NOTE-flutter-version.md)
- Release notes in [NOTE-flutter-RELEASE.txt](mdc:TTF_MES_Mobile/NOTE-flutter-RELEASE.txt)

## Dependencies
Main dependencies:
- flutter_screenutil - For responsive UI
- http - For API communication
- flutter_secure_storage & shared_preferences - For data persistence
- qr_code_scanner - For QR code scanning
- onesignal_flutter - For push notifications
- intl - For internationalization

## Code Style
- Follows standard Flutter/Dart conventions
- Static analysis configured in [analysis_options.yaml](mdc:TTF_MES_Mobile/analysis_options.yaml)
- Screen reference guide: [NOTE-screen-ref.md](mdc:TTF_MES_Mobile/NOTE-screen-ref.md)

## Testing
Tests are stored in the [test/](mdc:TTF_MES_Mobile/test) directory

## Assets
- Images and other resources in [assets/](mdc:TTF_MES_Mobile/assets) directory
- App icons and splash screen configured in pubspec.yaml

