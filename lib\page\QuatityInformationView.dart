// import 'dart:io';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:ttf/model/qualityControlApi.dart';
// import '../element/lsQualitiyControlInformation.dart';
// import '../model/QuantityInformationSelectedInfor.dart';
// import '../model/mulitListImageFile.dart';
//
//
// class QuantityInformationView extends StatelessWidget {
//   final ImagePicker pickerImage;
//   final List<QualityControlInformation> lsQualityControlInformation;
//   final QualityControl? qualityControl;
//   final List<QualityControlInformationIdList?> lsSelectedInfo;
//   final List<QualityControlInformationIdList>? lsQualityControlInformationIdList;
//   final List<int> lsGetIndexInfo;
//   final List<TextEditingController> lsTextEditingController;
//   final List<List<File>> lsGetFile;
//   final ValueChanged<MultiListImageFile> pickFileImageInformation;
//   final ValueChanged<MultiListImageDeleteFile> deleteListQualityInformation;
//   final List<TextEditingController> lsControllerInformation;
//   // final List<FocusNode> focusInformation;
//   final VoidCallback addNewCard;
//   final List<bool> lsErrorInfor;
//   final ValueChanged<int> checkErrorInformationCheck;
//   final ValueChanged<QuantityInformationSelected> checkErrorSelectedInfo;
//   final List<bool> checkVisiButtonInformation;
//   final ValueChanged<int> deleteItemList;
//   final VoidCallback resetState;
//   const QuantityInformationView({Key? key,
//     required this.pickerImage,
//     required this.lsQualityControlInformation,
//     required this.qualityControl,
//     required this.lsSelectedInfo,
//     required this.lsQualityControlInformationIdList,
//     required this.lsGetIndexInfo,
//     required this.lsTextEditingController,
//     required this.lsGetFile,
//     required this.pickFileImageInformation,
//     required this.deleteListQualityInformation,
//     required this.lsControllerInformation,
//     // required this.focusInformation,
//     required this.addNewCard,
//     required this.lsErrorInfor,
//     required this.checkErrorInformationCheck,
//     required this.checkErrorSelectedInfo,
//     required this.checkVisiButtonInformation,
//     required this.deleteItemList,
//     required this.resetState
//   }) : super(key: key);
//
//
//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//         child: SafeArea(
//           minimum: EdgeInsets.symmetric(horizontal: 5.w),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: <Widget>[
//               SizedBox(height: 15.h),
//               Container(
//                 decoration: BoxDecoration(
//                   border: Border.all(
//                       width: 0.5, color: const Color(0xff0052cc)),
//                 ),
//                 child: Column(
//                   children: <Widget>[
//                     const _TitleQuantityInformation(),
//                     SizedBox(height: 10.h),
//                     Column(
//                       children: List.generate(lsQualityControlInformation.length, (index) =>
//                         LsQualityControlInformationView(
//                                 qualityControl:qualityControl,
//                                   fileViewModel:lsQualityControlInformation[index].checkedFileViewModel ?? [],
//                                   index:index,
//                                   lsSelectedInfo:lsSelectedInfo,
//                                   lsQualityControlInformationIdList:lsQualityControlInformationIdList,
//                                   lsGetIndexInfo:lsGetIndexInfo,
//                                   lsTextEditingController:lsTextEditingController,
//                                   lsGetFile:lsGetFile,
//                                   pickImage: (MultiListImageFile value)  {
//                                     pickFileImageInformation(value);
//                                   },
//                                   deleteImage: (value) => deleteListQualityInformation(value),
//                                   code: lsQualityControlInformation[index].qualityControlInformationCode.toString(),
//                                   lsControllerInformation: lsControllerInformation,
//                                   // focusInformation: focusInformation,
//                                   pickerImage: pickerImage,
//                                   errorInfor:lsErrorInfor[index],
//                           checkErrorInformationCheck: checkErrorInformationCheck,
//                           checkErrorSelectedInfo: checkErrorSelectedInfo,
//                            checkVisiButtonInformation: checkVisiButtonInformation,
//                           deleteItemList: deleteItemList, resetState: resetState,
//                               ),
//                       )),
//                     Padding(
//                       padding:
//                       EdgeInsets.symmetric(horizontal: 15.w),
//                       child: IntrinsicHeight(
//                         child: Row(
//                           crossAxisAlignment:
//                           CrossAxisAlignment.stretch,
//                           children: <Widget>[
//                             Expanded(
//                               flex: 6,
//                               child: Container(
//                                 decoration: BoxDecoration(
//                                   border: Border.all(
//                                       width: 0.5,
//                                       color: Colors.grey.shade300),
//                                 ),
//                                 child: const Text(""),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 padding: EdgeInsets.symmetric(
//                                     vertical: 5.h),
//                                 height: 40.h,
//                                 decoration: BoxDecoration(
//                                   border: Border.all(
//                                       width: 0.5,
//                                       color: Colors.grey.shade300),
//                                 ),
//                                 child: Container(
//                                   padding: EdgeInsets.symmetric(
//                                       horizontal: 20.w),
//                                   child: ElevatedButton.icon(
//                                     icon: Icon(
//                                       Icons.add,
//                                       color: Colors.white,
//                                       size: 15.sp,
//                                     ),
//                                     style: ButtonStyle(
//                                       side:
//                                       MaterialStateProperty.all(
//                                         const BorderSide(
//                                           color: Colors.green,
//                                         ),
//                                       ),
//                                       backgroundColor:
//                                       MaterialStateProperty.all(
//                                           Colors.green),
//                                     ),
//                                     onPressed: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null:() {
//                                       addNewCard();
//                                     },
//                                     label: Text(
//                                       'Thêm',
//                                       style: TextStyle(
//                                         color: Colors.white,
//                                         fontWeight: FontWeight.bold,
//                                         fontSize: 11.sp,
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     SizedBox(height: 15.h),
//                   ],
//                 ),
//               ),
//               // SizedBox(height: 15.h),
//               // GestureDetector(
//               //   onTap: () {
//               //     _checkError();
//               //     debugPrint(_lsError.isEmpty.toString());
//               //     if(_lsError.where((element) => element == true).isEmpty){
//               //       FocusScope.of(context).unfocus();
//               //       Navigator.pushNamed(context, '/ErrorQuatityView',
//               //           arguments: ScreenArgumentErrorQuality(
//               //               widget.token,
//               //               widget.qualityControl,
//               //               widget.lsError,
//               //               widget.lsErrorList,
//               //               widget.getSendQualityControlDetail,
//               //               widget.selectedStaff,
//               //               widget.selectedType,
//               //               widget.pO,
//               //               widget.quantityCheck,
//               //               widget.selectedResult,
//               //               QualityControlInfoFunction.getLsSendQualityControlInformation(
//               //                   _lsQualityControlInformation,
//               //                    widget.qualityControl,
//               //                   _lsSelectedInfo,
//               //                   _lsTextEditingController,
//               //                   _lsGetFile
//               //               ),
//               //               widget.lsFileTabCheck,
//               //               widget.formatDatePost,
//               //               widget.dateTimeOld
//               //           ));
//               //     }
//               //   },
//               //   child: Container(
//               //     padding: EdgeInsets.symmetric(vertical: 15.h),
//               //     width: double.infinity,
//               //     decoration: BoxDecoration(
//               //       borderRadius: BorderRadius.all(
//               //         Radius.circular(5.r),
//               //       ),
//               //       color: const Color(0xff0052cc),
//               //     ),
//               //     child: Center(
//               //       child: Text(
//               //         'Tiếp tục',
//               //         style: TextStyle(
//               //             color: Colors.white,
//               //             fontSize: 13.sp,
//               //             fontWeight: FontWeight.bold),
//               //       ),
//               //     ),
//               //   ),
//               // ),
//               SizedBox(height: 15.h),
//             ],
//           ),
//         ),
//       );
//   }
// }
// // class _ErrorView extends StatelessWidget {
// //   const _ErrorView({Key? key}) : super(key: key);
// //
// //   @override
// //   Widget build(BuildContext context) {
// //     return Center(
// //         child:Text('Phiếu kiểm tra xảy ra lỗi! Vui lòng thử lại sau',style: TextStyle(fontSize: 15.sp,color: Colors.black)
// //         ));
// //   }
// // }
// class _TitleQuantityInformation extends StatelessWidget {
//   const _TitleQuantityInformation({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: double.infinity,
//       padding: EdgeInsets.symmetric(
//           vertical: 15.h, horizontal: 15.h),
//       decoration: const BoxDecoration(
//         color: Color(0xff0052cc),
//       ),
//       child: Text(
//         "Thông tin kiểm tra",
//         style: TextStyle(
//             fontWeight: FontWeight.bold,
//             fontSize: 13.sp,
//             color: Colors.white),
//       ),
//     );
//   }
// }
