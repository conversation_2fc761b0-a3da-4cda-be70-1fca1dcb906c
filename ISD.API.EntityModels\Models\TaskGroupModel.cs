﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskGroupModel", Schema = "Task")]
    public partial class TaskGroupModel
    {
        [Key]
        public Guid GroupId { get; set; }
        [StringLength(1000)]
        public string GroupName { get; set; }
        public Guid? CreatedAccountId { get; set; }
    }
}