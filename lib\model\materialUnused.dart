class MaterialUnusedModel {
  String? rawMaterialCardId;
  String? slocId;
  String? productCode;
  String? batchNumber;
  double? quantity;
  String? unit;
  String? stepCode;

  MaterialUnusedModel(
      {this.rawMaterialCardId,
        this.slocId,
        this.productCode,
        this.batchNumber,
        this.quantity,
        this.unit,
        this.stepCode
      });

  MaterialUnusedModel.fromJson(Map<String, dynamic> json) {
    rawMaterialCardId = json['rawMaterialCardId'];
    slocId = json['slocId'];
    productCode = json['productCode'];
    batchNumber = json['batchNumber'];
    quantity = json['quantity'];
    unit = json['unit'];
    stepCode = json['stepCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['rawMaterialCardId'] = rawMaterialCardId;
    data['slocId'] = slocId;
    data['productCode'] = productCode;
    data['batchNumber'] = batchNumber;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['stepCode'] = stepCode;
    return data;
  }
}

class MaterialUnusedMessage {
  int? code;
  bool? isSuccess;
  String? message;
  bool? data;


  MaterialUnusedMessage(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  MaterialUnusedMessage.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = this.data;
    return data;
  }
}