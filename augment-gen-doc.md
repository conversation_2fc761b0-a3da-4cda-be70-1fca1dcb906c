# TTF_MES_Mobile System Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [System Architecture](#system-architecture)
   - [High-Level Architecture](#high-level-architecture)
   - [Backend Architecture](#backend-architecture)
   - [Mobile Architecture](#mobile-architecture)
   - [Data Flow](#data-flow)
   - [Technology Stack](#technology-stack)
3. [Key Functional Modules](#key-functional-modules)
   - [Authentication & User Management](#1-authentication--user-management-module)
   - [Production Management](#2-production-management-module)
   - [Quality Control](#3-quality-control-module)
   - [Inventory Management](#4-inventory-management-module)
   - [SAP Integration](#5-sap-integration-module)
   - [Reporting & Analytics](#6-reporting--analytics-module)
   - [Notification & Alerts](#7-notification--alerts-module)
4. [Quality Control Module Details](#quality-control-module)
   - [QC Features and Files](#qc-features-and-files)
   - [QC Supporting Components](#qc-supporting-components)
5. [Integration Points](#integration-points)
6. [Environment Configuration](#environment-configuration)
7. [FAQs](#faqs)
8. [Additional Notes](#additional-notes)

## Introduction

This document contains comprehensive information about the TTF_MES_Mobile codebase. It serves as a reference for key aspects of the system architecture, features, and implementation details.

## System Architecture

### High-Level Architecture

TTF_MES_Mobile is a Manufacturing Execution System (MES) designed to manage and track manufacturing processes, quality control, inventory, and production workflows. The system follows a client-server architecture with:

1. **.NET Core Backend API** (`iMES_API`) - Handles business logic, data persistence, and integration with other systems
2. **Flutter Mobile Frontend** (`TTF_MES_Mobile`) - Provides a mobile interface for factory workers and supervisors
3. **SAP Integration** - Connects with enterprise SAP systems for data exchange

### System Components

#### Backend Components (iMES_API)

- **Core API Project** (`iMES_API`): Entry point for the application
- **Controllers**: Organized by functional areas with API versioning (v1, v2, v3)
- **Repositories**: Implement data access logic and business rules
- **Entity Models**: Define database entities and relationships
- **View Models**: Define DTOs for API responses
- **Database**: SQL Server with Entity Framework Core ORM
- **Authentication**: JWT-based authentication and role-based authorization

#### Mobile Components (TTF_MES_Mobile)

- **UI Screens**: Organized by functional modules (Quality Control, Production, etc.)
- **API Communication**: HTTP requests to backend with environment-specific endpoints
- **State Management**: Combination of StatefulWidget and local state
- **Navigation**: Route-based navigation with screen arguments
- **Utilities**: Barcode scanning, image capture, secure storage

#### Integration Points

- **SAP Integration**: SOAP/Web Services for bidirectional data exchange
- **Mobile-Backend Integration**: RESTful API with JWT authentication
- **Database Integration**: Entity Framework Core with SQL Server

### Backend Architecture

The backend follows a layered architecture:

1. **Presentation Layer (Controllers)**
   - **Areas**: Functional grouping of controllers (MES, Permission, SAPIntegration)
   - **API Versioning**: Different versions (v1, v2, v3) for different client types
   - **Controllers**: Handle HTTP requests, input validation, and response formatting
   - **Filters**: Global filters for cross-cutting concerns (e.g., AuditLogFilter)

2. **Business Logic Layer (Repositories)**
   - **Repository Pattern**: Encapsulates data access logic
   - **Unit of Work Pattern**: Manages transactions and consistency
   - **Service Classes**: Implement complex business logic
   - **Validation**: Business rule validation

3. **Data Access Layer**
   - **Entity Framework Core**: ORM for database operations
   - **Entity Models**: Define database schema and relationships
   - **DbContext**: EntityDataContext for database operations
   - **Migrations**: Database schema versioning

4. **Cross-Cutting Concerns**
   - **Authentication**: JWT token generation and validation
   - **Authorization**: Role-based access control
   - **Logging**: Request/response logging
   - **Exception Handling**: Global exception middleware
   - **Configuration**: Environment-specific settings

### Mobile Architecture

The mobile app follows a feature-based architecture:

1. **UI Layer**
   - **Pages**: Screen implementations organized by feature
   - **Widgets**: Reusable UI components
   - **Themes**: Consistent styling across the app
   - **Navigation**: Route-based navigation with arguments

2. **State Management**
   - **Local State**: StatefulWidget for screen-level state
   - **Form State**: Form validation and submission
   - **Navigation State**: Route management

3. **Data Layer**
   - **Models**: Data structures for API responses
   - **API Clients**: HTTP communication with backend
   - **Local Storage**: Secure storage for sensitive data
   - **Shared Preferences**: User settings and preferences

4. **Utility Layer**
   - **Helpers**: Common utility functions
   - **Constants**: App-wide constants
   - **Extensions**: Dart extensions for added functionality
   - **Platform Services**: Camera, barcode scanning, notifications

### Data Flow

1. **User Authentication Flow**
   - Mobile app sends credentials to `/api/v1/Permission/Auth/Authenticate`
   - Backend validates credentials and returns JWT token
   - Mobile app stores token in secure storage
   - Token is included in subsequent API requests

2. **Production Recording Flow**
   - User scans product barcode using mobile app
   - App sends barcode to API to retrieve product details
   - User records production data
   - Data is sent to API and stored in database
   - API may trigger integration with SAP

3. **Quality Control Flow**
   - User selects QC type and scans product
   - App retrieves QC requirements from API
   - User performs inspection and records results
   - Results (including photos) are sent to API
   - API updates QC status and may trigger notifications

### Technology Stack

1. **Backend**
   - .NET Core
   - Entity Framework Core
   - SQL Server
   - JWT Authentication
   - Swagger for API documentation

2. **Mobile**
   - Flutter
   - Dart
   - HTTP for API communication
   - Secure Storage for sensitive data
   - Camera integration for QR scanning and photos

3. **DevOps**
   - Jenkins for CI/CD
   - Git for version control
   - Multiple deployment environments (DEV, QAS, PRD)

## Quality Control Module Details

The Quality Control (QC) module is a comprehensive system for managing quality inspections throughout the manufacturing process. It's primarily located in the `lib/page/KiemTraChatLuong/` directory in the mobile app.

#### QC Features and Files

1. **Input Quality Control (Đầu Vào)**
   - **Purpose**: Inspect incoming materials and components
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoDauVao.dart` - List view of input QC reports
     - `lib/page/KiemTraChatLuong/BaoCaoDauVaoDetail.dart` - Detailed input QC report form
     - `lib/element/FillterListQCNVL.dart` - Filter for input QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlDauVao`
     - `QualityControlController.PostQualityControlDauVao`

2. **Process Quality Control (Công Đoạn)**
   - **Purpose**: Quality checks during manufacturing process
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/PhieuKCSCongDoan.dart` - Process QC report list
     - `lib/page/KiemTraChatLuong/PhieuKCSCongDoanDetail.dart` - Process QC report details
     - `lib/page/KiemTraChatLuong/element/FillterListKCS.dart` - Filter for process QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlCongDoan`
     - `QualityControlController.PostQualityControlCongDoan`

3. **QA/QC Acceptance (Nghiệm Thu)**
   - **Purpose**: Final acceptance quality checks
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQAQCNghiemThu.dart` - QA/QC acceptance report list
     - `lib/page/KiemTraChatLuong/BaoCaoQAQCNghiemThuDetail.dart` - QA/QC acceptance report details
     - `lib/page/KiemTraChatLuong/element/FilterListQCNghiemThu.dart` - Filter for acceptance QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlQCNghiemThu`
     - `QualityControlController.PostQualityControlQCNghiemThu`

4. **Sample Quality Control (QC Mẫu)**
   - **Purpose**: Quality checks on product samples
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQCMau.dart` - Sample QC report list
     - `lib/page/KiemTraChatLuong/BaoCaoQCMauDetail.dart` - Sample QC report details
     - `lib/page/KiemTraChatLuong/element/FilterListQCMau.dart` - Filter for sample QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlQCMau`
     - `QualityControlController.PostQualityControlQCMau`

5. **Processing Quality Control (Gia Công)**
   - **Purpose**: Quality checks for outsourced processing
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQCGiaCong.dart` - Processing QC report list
     - `lib/page/KiemTraChatLuong/BaoCaoQCGiaCongDetail.dart` - Processing QC report details
     - `lib/page/KiemTraChatLuong/element/FilterListQCGiaCong.dart` - Filter for processing QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlQCGiaCong`
     - `QualityControlController.PostQualityControlQCGiaCong`

6. **Product Quality Control (Sản Phẩm)**
   - **Purpose**: Final product quality inspection
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQCSanPham.dart` - Product QC report list
     - `lib/page/KiemTraChatLuong/BaoCaoQCSanPhamDetail.dart` - Product QC report details
     - `lib/page/KiemTraChatLuong/element/FilterListQCSanPham.dart` - Filter for product QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlSanPham`
     - `QualityControlController.PostQualityControlSanPham`

7. **Field Quality Control (Hiện Trường)**
   - **Purpose**: On-site quality inspections
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQCHienTruong.dart` - Field QC report list
     - `lib/page/KiemTraChatLuong/BaoCaoQCHienTruongDetail.dart` - Field QC report details
     - `lib/page/KiemTraChatLuong/element/FilterListQCHienTruong.dart` - Filter for field QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControlQCHienTruong`
     - `QualityControlController.PostQualityControlQCHienTruong`

8. **5S Quality Control**
   - **Purpose**: Workplace organization and cleanliness audits (Sort, Set in order, Shine, Standardize, Sustain)
   - **Mobile Files**:
     - `lib/page/KiemTraChatLuong/BaoCaoQC5S.dart` - 5S QC report list
     - `lib/page/KiemTraChatLuong/BaoCaoQC5SDetail.dart` - 5S QC report details
     - `lib/page/KiemTraChatLuong/element/FilterListQC5S.dart` - Filter for 5S QC list
   - **API Endpoints**:
     - `QualityControlController.GetQualityControl5S`
     - `QualityControlController.PostQualityControl5S`

#### QC Supporting Components

1. **QC List and Navigation**
   - **Purpose**: Main entry points and navigation for QC features
   - **Files**:
     - `lib/page/KiemTraChatLuong/KiemTraChatLuong.dart` - Main QC menu
     - `lib/page/KiemTraChatLuong/ListQC.dart` - List of all QC tasks
     - `lib/page/KiemTraChatLuong/ListQCNVL.dart` - List of material QC tasks
     - `lib/page/KiemTraChatLuong/QRCodePageListQC.dart` - QR code scanning for QC

2. **QC Models and API**
   - **Purpose**: Data models and API communication for QC features
   - **Files**:
     - `lib/model/qualityControlApi.dart` - QC data models
     - `lib/repository/api/qualityControlApi.dart` - API endpoints for QC
     - `lib/repository/function/qualityControlFunction.dart` - Business logic for QC
     - `lib/model/GetDefectLevel.dart` - Defect level models
     - `lib/model/multiSelectedErrorQuality.dart` - Error tracking models

3. **QC UI Components**
   - **Purpose**: Reusable UI components for QC screens
   - **Files**:
     - `lib/element/QualityTitle.dart` - Title component for QC screens
     - `lib/element/QualityErrorValidate.dart` - Error validation component
     - `lib/element/DropdownQualityView.dart` - Dropdown component for QC
     - `lib/element/ListChoseFileQuality.dart` - File selection component
     - `lib/element/listImagePicker.dart` - Image picker for QC documentation

4. **Backend Components**
   - **Purpose**: Server-side implementation of QC features
   - **Files**:
     - `iMES_API/Areas/MES/Controllers/QualityControlController.cs` - Main QC API controller
     - `ISD.API.Repositories/MES/QualityControlRepository.cs` - QC data repository
     - `ISD.API.EntityModels/Models/QualityControlModel.cs` - QC data model
     - `ISD.API.ViewModels/MES/QualityControlViewModel.cs` - QC view models

## Key Functional Modules

### 1. Authentication & User Management Module

**Purpose**: Manages user access, authentication, and authorization throughout the system.

**Key Components**:
- **Login System**: Username/password authentication with JWT token generation
- **User Management**: User creation, role assignment, and profile management
- **Permission System**: Role-based access control for features and data
- **Session Management**: Token refresh, session timeout, and secure logout

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/Permission/Controllers/AuthController.cs` - Authentication endpoints
  - `ISD.API.EntityModels/Models/AccountModel.cs` - User account data model
  - `ISD.API.Core/Extensions/ControllerBaseAPI.cs` - Base controller with user context
- **Mobile**:
  - `lib/page/Login.dart` - Login screen
  - `lib/repository/api/loginApi.dart` - Authentication API calls
  - `lib/Storage/storageSecureStorage.dart` - Secure token storage

### 2. Production Management Module

**Purpose**: Tracks and manages the production process from work orders to completion.

**Key Components**:
- **Work Order Management**: Creation, tracking, and completion of work orders
- **Production Recording**: Recording production quantities and times
- **Process Step Management**: Tracking progress through manufacturing steps
- **Production Reporting**: Performance metrics and production statistics

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/MES/Controllers/WorkOrderController.cs` - Work order endpoints
  - `iMES_API/Areas/MES/Controllers/ProductionManagementController.cs` - Production management
  - `ISD.API.EntityModels/Models/WorkOrderModel.cs` - Work order data model
- **Mobile**:
  - `lib/page/GhiNhanSanLuong/` - Production recording screens
  - `lib/page/ChuyenCongDoan/` - Process step management screens

### 3. Quality Control Module

**Purpose**: Manages quality inspections throughout the manufacturing process.

*(Detailed in the Quality Control section below)*

### 4. Inventory Management Module

**Purpose**: Tracks materials, components, and finished products throughout the facility.

**Key Components**:
- **Warehouse Management**: Storage location tracking and management
- **Material Tracking**: Raw material and component inventory
- **Stock Transfers**: Movement of materials between locations
- **Inventory Reporting**: Stock levels and inventory valuation

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/MES/Controllers/SlocController.cs` - Storage location endpoints
  - `iMES_API/Areas/MES/Controllers/InventoryController.cs` - Inventory management
  - `ISD.API.EntityModels/Models/InventoryModel.cs` - Inventory data model
- **Mobile**:
  - `lib/page/QuanLyKho/` - Warehouse management screens
  - `lib/page/ChuyenKho/` - Stock transfer screens

### 5. SAP Integration Module

**Purpose**: Facilitates data exchange between the MES system and SAP ERP.

**Key Components**:
- **Purchase Order Integration**: Synchronization of purchase orders from SAP
- **Inventory Integration**: Stock level updates to/from SAP
- **Production Data Exchange**: Work order and production data synchronization
- **Master Data Synchronization**: Product, customer, and vendor data sync

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/SAPIntegration/Controllers/` - SAP integration endpoints
  - `ISD.API.Repositories/APISAP/SAPAPIRepository.cs` - SAP API communication
  - `SAP/Models/` - SAP data models
- **Integration**:
  - `iMES_API/Services/SAPService.cs` - SAP service implementation

### 6. Reporting & Analytics Module

**Purpose**: Provides insights and reports on production, quality, and inventory data.

**Key Components**:
- **Production Reports**: Output, efficiency, and utilization reports
- **Quality Reports**: Defect rates, quality metrics, and trend analysis
- **Inventory Reports**: Stock levels, movements, and valuation
- **Dashboard**: Real-time KPIs and performance indicators

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/MES/Controllers/ReportController.cs` - Reporting endpoints
  - `ISD.API.Repositories/MES/ReportRepository.cs` - Report data access
- **Mobile**:
  - `lib/page/BaoCao/` - Reporting screens
  - `lib/page/Dashboard/` - Dashboard screens

### 7. Notification & Alerts Module

**Purpose**: Delivers timely notifications and alerts to users.

**Key Components**:
- **Push Notifications**: Real-time alerts to mobile devices
- **In-App Notifications**: System messages within the application
- **Alert Configuration**: User-specific notification preferences
- **Event Triggers**: Business events that generate notifications

**Key Files**:
- **Backend**:
  - `iMES_API/Areas/MES/Controllers/NotificationController.cs` - Notification endpoints
  - `ISD.API.EntityModels/Models/NotificationModel.cs` - Notification data model
- **Mobile**:
  - `lib/page/NotificationPage.dart` - Notifications screen
  - `lib/service/notificationService.dart` - Notification handling

## Integration Points

1. **SAP Integration**
   - Dedicated controllers in `Areas/SAPIntegration/`
   - SOAP/Web Services communication
   - Bidirectional data flow for purchase orders, inventory, and production data

2. **Mobile-Backend Integration**
   - RESTful API endpoints
   - JWT authentication
   - File uploads for photos and documents

## Environment Configuration

- **Backend Environments**: Development, QA, and Production
- **Mobile Environments**: Development, QA, and Production API endpoints

## FAQs

1. **What is the overall architecture and purpose of the system?**
   - Manufacturing Execution System (MES) with .NET Core API backend and Flutter mobile frontend
   - Manages manufacturing processes, tracks production, handles quality control, and integrates with SAP

2. **How does authentication and authorization work?**
   - JWT authentication
   - Role-based permission system
   - Mobile app stores tokens securely using SecureStorage

3. **What are the key database entities and their relationships?**
   - SQL Server with Entity Framework Core
   - Key entities include Account/User management, Products, Production processes, Work Orders, and Quality Control
   - Base entity classes provide common fields like Id, CreateTime, LastEditTime

4. **How does the mobile app interact with the API?**
   - HTTP requests to different API endpoints
   - Environment-specific URLs (DEV, QAS, PRD)
   - Route system for navigation between screens
   - QR code scanning for data entry

5. **How does the system integrate with external systems like SAP?**
   - Dedicated SAPIntegration area in the API
   - SOAP/Web Services communication
   - Integration for Purchase Orders, Inventory, and production data

## Additional Notes

*This section will be updated with additional memories as they are created.*
