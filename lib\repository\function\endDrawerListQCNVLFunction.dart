import 'dart:convert';

import 'package:flutter/material.dart';

import '../../model/drawerFilterListQCNVL.dart';
import '../../model/getLstQCNVLByFilter.dart';
import '../../model/getStatusGoodsArrive.dart';
import '../api/getStatusGoodsArriveApi.dart';
import 'listQcFunction.dart';

class EndDrawerListQCNVLFunction{
  static Future<DrawerFilterListQCNVL> sendBackData(
      BuildContext context,
      String token,
      GetLstQCNVLByFilter getLstQCNVLByFilter,
      int pageNumber
      ) async {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) =>
          WillPopScope(
            onWillPop: () async => false,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
    );
    final lsDataWarehouseTranfe = await ListQCFunction.postLstQCNVLByFilter(getLstQCNVLByFilter, token);
    Navigator.pop(context);
    pageNumber+=1;
    getLstQCNVLByFilter.pageNumber = pageNumber;
    final drawerFilterListQCNVL = DrawerFilterListQCNVL(
       getLstQCNVLByFilter: getLstQCNVLByFilter,
      lsDataLstQCNVLByFilter: lsDataWarehouseTranfe
    );
    return drawerFilterListQCNVL;

  }
  static Future<List<DataGetStatusGoodsArrive>> fetchStatusGoodsArrive(String token
      ) async {

    final response = await GetStatusGoodsArriveApi.getStatusGoodsArriveApi(token);
    if(response.statusCode == 200){
      final responseStatusGoodsArriveApi = jsonDecode(response.body);
        final dataStatusGoodsArriveApi = GetStatusGoodsArrive.fromJson(responseStatusGoodsArriveApi);
        if (dataStatusGoodsArriveApi.code == 200 && dataStatusGoodsArriveApi.isSuccess == true) {
          return dataStatusGoodsArriveApi.data ?? [];
        } else {
          return [];
        }
    }
    return [];

  }
}