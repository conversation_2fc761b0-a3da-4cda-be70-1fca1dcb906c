# Implementation Guide for Adding New Features to TTF_MES_Mobile

This guide provides a comprehensive overview of how to implement new features in the TTF_MES_Mobile application, adhering to the established coding patterns and UI components.

## Table of Contents

- [Implementation Guide for Adding New Features to TTF\_MES\_Mobile](#implementation-guide-for-adding-new-features-to-ttf_mes_mobile)
  - [Table of Contents](#table-of-contents)
  - [Implementation Overview](#implementation-overview)
  - [Frontend Implementation](#frontend-implementation)
    - [Creating the List Screen](#creating-the-list-screen)
    - [Implementing Search and Filter](#implementing-search-and-filter)
    - [Creating the Detail Screen](#creating-the-detail-screen)
    - [Creating Service Functions](#creating-service-functions)
    - [Creating Data Models](#creating-data-models)
    - [Adding Routes](#adding-routes)
    - [Adding to Main Page](#adding-to-main-page)
    - [Using Dummy Data](#using-dummy-data)
  - [Backend Implementation](#backend-implementation)
    - [Designing Database Tables](#designing-database-tables)
    - [Creating Models](#creating-models)
    - [Implementing Controllers](#implementing-controllers)
    - [Implementing Repositories](#implementing-repositories)
    - [Creating ViewModels](#creating-viewmodels)
  - [UI Component Reference](#ui-component-reference)
    - [Date Input](#date-input)
    - [Time Input](#time-input)
    - [Text Input](#text-input)
    - [Numeric Input](#numeric-input)
    - [Multiple Line Input](#multiple-line-input)
    - [Select Input](#select-input)
    - [Autocomplete with API Suggestions](#autocomplete-with-api-suggestions)
    - [Suggestions from Master Data](#suggestions-from-master-data)
    - [Employee Multiple Input](#employee-multiple-input)
  - [Integration Testing](#integration-testing)

## Implementation Overview

When adding a new feature to the TTF_MES_Mobile application, you need to work on both frontend and backend components:

1. **Frontend (Flutter)**: Create list and detail screens, implement filtering, create models for data handling, define routes, and add the feature to the main page.
2. **Backend (ASP.NET Core)**: Design database tables, create models, controllers, repositories, and view models.

## Frontend Implementation

### Creating the List Screen

Create a new file for the list screen in a dedicated feature folder, following this structure:

```
lib/page/FeatureName/FeatureNameList.dart
```

Key components to include:
- State variables for loading, error handling, and network connectivity
- Data fetching logic
- UI for displaying the list of records
- Filtering functionality
- Navigation to the detail screen

Example structure based on existing features:

```dart
class _FeatureNameListState extends State<FeatureNameList> {
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  List<FeatureNameRecord>? _records;
  FeatureNameSearchModel? _searchModel;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    // Initialize search model and load data
  }

  Future<void> _loadData() async {
    // Fetch data from API
  }

  // UI building methods
}
```

### Implementing Search and Filter

Create a filter component in `lib/page/FeatureName/element/FilterListFeatureName.dart` for search functionality:

```dart
class FilterListFeatureName extends StatefulWidget {
  final FeatureNameSearchModel? searchModel;
  final Function(FeatureNameSearchModel) onFilterSelected;
  final String token;
  final DataUser user;

  const FilterListFeatureName({
    Key? key,
    this.searchModel,
    required this.onFilterSelected,
    required this.token,
    required this.user,
  }) : super(key: key);

  @override
  _FilterListFeatureNameState createState() => _FilterListFeatureNameState();
}
```

Include filter fields relevant to your feature (dates, status, etc.) and implement the filter application logic.

### Creating the Detail Screen

Create a detail screen in `lib/page/FeatureName/FeatureNameDetail.dart`:

```dart
class FeatureNameDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;

  const FeatureNameDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
  }) : super(key: key);

  @override
  _FeatureNameDetailState createState() => _FeatureNameDetailState();

  bool get isViewMode => viewMode ?? id.isNotEmpty;
}
```

Include form fields, data loading logic, and validation. Use the `FormLayout` widget for consistent UI styling:

```dart
FormLayout(
  title: "Feature Information",
  children: [
    // Form fields
  ],
)
```

### Creating Service Functions

Create service functions in `lib/repository/function/featureNameFunction.dart`:

```dart
class FeatureNameFunction {
  static Future<FeatureNameListResponse?> fetchFeatureNameList(
    String token,
    FeatureNameSearchModel model,
  ) async {
    // API call implementation
  }

  static Future<FeatureNameRecord?> fetchFeatureNameDetail(
    String token,
    String id,
  ) async {
    // API call implementation
  }

  static Future<bool> saveFeatureName(
    String token,
    FeatureNameRecord record,
  ) async {
    // API call implementation
  }
}
```

### Creating Data Models

Create models in `lib/model/featureNameModel.dart` for API data handling:

```dart
class FeatureNameRecord {
  String? id;
  String? date;
  // Other fields

  FeatureNameRecord({
    this.id,
    this.date,
    // Other fields
  });

  factory FeatureNameRecord.fromJson(Map<String, dynamic> json) {
    return FeatureNameRecord(
      id: json['id'],
      date: json['date'],
      // Other fields
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date,
      // Other fields
    };
  }
}
```

Also create a search model in `lib/model/FeatureNameSearchModel.dart` for filtering:

```dart
class FeatureNameSearchModel {
  String? companyCode;
  String? fromDate;
  String? toDate;
  String? status;
  int pageNumber;
  int pageSize;

  FeatureNameSearchModel({
    this.companyCode,
    this.fromDate,
    this.toDate,
    this.status,
    this.pageNumber = 1,
    this.pageSize = 10,
  });

  Map<String, dynamic> toJson() {
    // Convert to JSON
  }
}
```

### Adding Routes

Define routes in `lib/route/route.dart`:

```dart
// Add to RouteGenerator.generateRoute switch statement
case '/FeatureName':
  return MaterialPageRoute(
    settings: const RouteSettings(name: '/FeatureName'),
    builder: (_) {
      CommonScreenUserArgument argument = args as CommonScreenUserArgument;
      return FeatureNameList(
        dateTimeOld: argument.dateTimeOld.toString(),
        user: argument.user,
      );
    },
  );

case '/FeatureNameDetail':
  return MaterialPageRoute(
    settings: const RouteSettings(name: '/FeatureNameDetail'),
    builder: (_) {
      final Map<String, dynamic> argument = args as Map<String, dynamic>;
      return FeatureNameDetail(
        id: argument['id'] as String,
        dateTimeOld: argument['dateTimeOld'] as String,
        user: argument['user'] as DataUser,
        viewMode: argument['viewMode'] as bool?,
      );
    },
  );
```

### Adding to Main Page

Add the feature to the `Mainpage.dart` file's `buttonList`:

```dart
// In the buttonList getter in _MainPageView class
if (checkViewFeatureNamePermission)
  {
    'txt': 'Feature Name',
    'route': '/FeatureName',
    'icon': Icons.feature_icon,
    'arguments': CommonScreenUserArgument(
      null, // id
      dateTimeOld, // dateTimeOld
      null, // qrCode
      null, // fromPage
      getSaveUser, // user
    ),
  },
```

Also add the permission check variable:

```dart
// In _MainPageState class
late bool _checkViewFeatureNamePermission;

// In _timeOutSession method
_checkViewFeatureNamePermission =
    widget.getSaveUser.permission!.mobileScreenModel!
        .firstWhereOrNull((element) => element.screenCode == "FeatureName") != null;
```

### Using Dummy Data

Create dummy data methods for UI testing:

```dart
List<FeatureNameRecord> _getMockRecords() {
  return [
    FeatureNameRecord(
      id: '1',
      date: '2024-07-01',
      // Other mock fields
    ),
    // More mock records
  ];
}
```

## Backend Implementation

### Designing Database Tables

Design your SQL database tables and create a script:

```sql
CREATE TABLE FeatureName (
    Id NVARCHAR(50) PRIMARY KEY,
    Date DATE NOT NULL,
    -- Other fields
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME,
    ModifiedBy NVARCHAR(50),
    ModifiedDate DATETIME
);
```

### Creating Models

Create C# model classes:

```csharp
public class FeatureName
{
    public string Id { get; set; }
    public DateTime Date { get; set; }
    // Other properties
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
}
```

### Implementing Controllers

Create API controllers:

```csharp
[Route("api/[controller]")]
[ApiController]
public class FeatureNameController : ControllerBase
{
    private readonly IFeatureNameRepository _repository;

    public FeatureNameController(IFeatureNameRepository repository)
    {
        _repository = repository;
    }

    [HttpGet]
    public async Task<ActionResult<FeatureNameListResponse>> GetList([FromQuery] FeatureNameSearchModel model)
    {
        // Implementation
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<FeatureNameRecord>> GetDetail(string id)
    {
        // Implementation
    }

    [HttpPost]
    public async Task<ActionResult<bool>> Create([FromBody] FeatureNameRecord model)
    {
        // Implementation
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<bool>> Update(string id, [FromBody] FeatureNameRecord model)
    {
        // Implementation
    }
}
```

### Implementing Repositories

Create repositories for data access:

```csharp
public interface IFeatureNameRepository
{
    Task<FeatureNameListResponse> GetListAsync(FeatureNameSearchModel model);
    Task<FeatureNameRecord> GetDetailAsync(string id);
    Task<bool> CreateAsync(FeatureNameRecord model);
    Task<bool> UpdateAsync(string id, FeatureNameRecord model);
}

public class FeatureNameRepository : IFeatureNameRepository
{
    private readonly AppDbContext _context;

    public FeatureNameRepository(AppDbContext context)
    {
        _context = context;
    }

    // Implementation methods
}
```

### Creating ViewModels

Create view models for API responses:

```csharp
public class FeatureNameRecord
{
    public string Id { get; set; }
    public DateTime Date { get; set; }
    // Other properties
}

public class FeatureNameListResponse
{
    public List<FeatureNameRecord> Data { get; set; }
    public int TotalCount { get; set; }
}

public class FeatureNameSearchModel
{
    public string CompanyCode { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Status { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}
```

## UI Component Reference

### Date Input

Use this pattern for date inputs:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Text(
          'Ngày',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: TextField(
          controller: _dateController,
          enabled: !widget.isViewMode,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            suffixIcon: Icon(Icons.calendar_today, size: 15.sp),
          ),
          readOnly: true,
          onTap: widget.isViewMode
              ? null
              : () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    setState(() {
                      _dateController.text = DateFormat('dd/MM/yyyy').format(date);
                    });
                  }
                },
        ),
      ),
    ],
  ),
),
```

### Time Input

Use this pattern for time inputs:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Text(
          'Thời gian',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: TextField(
          controller: _timeController,
          enabled: !widget.isViewMode,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            suffixIcon: Icon(Icons.access_time, size: 15.sp),
          ),
          readOnly: true,
          onTap: widget.isViewMode
              ? null
              : () async {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.now(),
                  );
                  if (time != null) {
                    final now = DateTime.now();
                    final dt = DateTime(now.year, now.month, now.day, time.hour, time.minute);
                    setState(() {
                      _timeController.text = DateFormat('hh:mm a').format(dt);
                    });
                  }
                },
        ),
      ),
    ],
  ),
),
```

When saving, convert the displayed time format to the API format:

```dart
if (_timeController.text.isNotEmpty) {
  final time = DateFormat('hh:mm a').parse(_timeController.text);
  serverTime = DateFormat('HH:mm').format(time);
}
```

### Text Input

Use this pattern for text inputs:

```dart
FormInputField(
  label: 'Label Text',
  controller: _textController,
  enabled: !widget.isViewMode,
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Please enter some text';
    }
    return null;
  },
),
```

Or use the standard pattern:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Text(
          'Label Text',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: TextFormField(
          controller: _textController,
          enabled: !widget.isViewMode,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter some text';
            }
            return null;
          },
        ),
      ),
    ],
  ),
),
```

### Numeric Input

Use this pattern for numeric inputs:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Text(
          'Numeric Value',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: TextFormField(
          controller: _numericController,
          enabled: !widget.isViewMode,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')), // Allow decimals with up to 2 places
          ],
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a value';
            }
            return null;
          },
        ),
      ),
    ],
  ),
),
```

### Multiple Line Input

Use this pattern for multi-line text inputs:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.start,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Padding(
          padding: EdgeInsets.only(top: 6.h),
          child: Text(
            'Ghi chú',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: TextFormField(
          controller: _noteController,
          enabled: !widget.isViewMode,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
          ),
          maxLines: 4,
          minLines: 4,
        ),
      ),
    ],
  ),
),
```

### Select Input

Use this pattern for dropdown/select inputs:

```dart
Padding(
  padding: EdgeInsets.symmetric(horizontal: 15.w),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: <Widget>[
      Expanded(
        flex: 3,
        child: Text(
          'Status',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      SizedBox(width: 10.w),
      Expanded(
        flex: 7,
        child: DropdownButtonFormField<String>(
          value: _status,
          isExpanded: true,
          isDense: true,
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(3.r),
              borderSide: BorderSide(width: 0.5, color: Colors.grey.shade400),
            ),
          ),
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
          items: _statusOptions.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value, style: TextStyle(fontSize: 12.sp)),
            );
          }).toList(),
          onChanged: widget.isViewMode ? null : (String? newValue) {
            setState(() {
              _status = newValue;
            });
          },
        ),
      ),
    ],
  ),
),
```

### Autocomplete with API Suggestions

Use this pattern for autocomplete fields with API-fetched suggestions:

```dart
AutoCompleteField(
  label: 'LSX SAP',
  enabled: !widget.isViewMode,
  controller: _lsxSAPController,
  suggestions: _lsxSAPSuggestions,
  isLoading: _isLoadingLSXSAP,
  onChanged: (value) {
    // Call API to get suggestions if needed
    if (!widget.isViewMode && value.length >= 3) {
      _fetchLSXSAPSuggestions(value);
    }
  },
  onSuggestionSelected: widget.isViewMode ? _noOpSuggestion : (suggestion) {
    setState(() {
      _lsxSAPController.text = suggestion;
      // Load additional data based on selection if needed
    });
  },
),
```

With the API fetching function:

```dart
Future<void> _fetchLSXSAPSuggestions(String query) async {
  if (query.length < 3) return;
  
  setState(() {
    _isLoadingLSXSAP = true;
  });
  
  try {
    final suggestions = await YourApiFunction.fetchSuggestions(
      widget.user.token!,
      query,
    );
    
    if (!mounted) return;
    
    setState(() {
      _lsxSAPSuggestions = suggestions;
      _isLoadingLSXSAP = false;
    });
  } catch (e) {
    debugPrint('Error fetching suggestions: $e');
    if (!mounted) return;
    setState(() {
      _isLoadingLSXSAP = false;
    });
  }
}
```

### Suggestions from Master Data

Use this pattern for autocomplete fields with master data suggestions:

```dart
AutoCompleteField(
  label: 'Nguyên nhân',
  enabled: !widget.isViewMode,
  suggestions: DowntimeMasterData.reasonSuggestions,
  controller: _reasonController,
  onChanged: widget.isViewMode ? _noOp : (value) {
    // Handle text changes if needed
  },
  onSuggestionSelected: widget.isViewMode ? _noOpSuggestion : (suggestion) {
    setState(() {
      _reasonController.text = suggestion;
    });
  },
),
```

Create a master data class for static suggestions:

```dart
class FeatureNameMasterData {
  static const List<String> categorySuggestions = [
    'Category 1',
    'Category 2',
    'Category 3',
    // More suggestions
  ];
  
  // Other suggestion lists
}
```

### Employee Multiple Input

Use the `EmployeeTypeAhead` component for employee selection:

```dart
Column(
  children: [
    Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Cá nhân chịu trách nhiệm',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            icon: Icon(Icons.add, size: 18.sp),
            onPressed: widget.isViewMode ? null : _addEmployeeField,
            constraints: const BoxConstraints(),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    ),
    SizedBox(height: 5.h),
    ..._buildEmployeeFields(),
  ],
),
```

With the supporting methods:

```dart
void _addEmployeeField() {
  setState(() {
    _employeeControllers.add(TextEditingController());
    _selectedEmployees.add(null);
  });
}

void _removeEmployeeField(int index) {
  setState(() {
    _employeeControllers[index].dispose();
    _employeeControllers.removeAt(index);
    _selectedEmployees.removeAt(index);
  });
}

List<Widget> _buildEmployeeFields() {
  final fields = <Widget>[];
  
  for (int i = 0; i < _employeeControllers.length; i++) {
    fields.add(
      Padding(
        padding: EdgeInsets.only(bottom: 5.h),
        child: Row(
          children: [
            Expanded(
              child: EmployeeTypeAhead(
                controller: _employeeControllers[i],
                enabled: !widget.isViewMode,
                onSuggestionSelected: widget.isViewMode ? _noOpEmployeeRecord : (employee) {
                  setState(() {
                    _selectedEmployees[i] = employee;
                    _employeeControllers[i].text = '${employee.employeeId} | ${employee.employeeName}';
                  });
                },
                onFetchSuggestions: _fetchEmployeeSuggestions,
              ),
            ),
            SizedBox(width: 5.w),
            IconButton(
              icon: Icon(Icons.remove_circle_outline, color: Colors.red, size: 18.sp),
              onPressed: widget.isViewMode ? null : () => _removeEmployeeField(i),
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }
  
  return fields;
}

Future<List<EmployeeRecord>> _fetchEmployeeSuggestions(String pattern) async {
  if (pattern.length < 2) return [];
  
  try {
    // Cache suggestions if already loaded
    if (_employeeSuggestions.isNotEmpty) {
      return _employeeSuggestions
          .where((e) => 
              (e.employeeId?.toLowerCase().contains(pattern.toLowerCase()) ?? false) ||
              (e.employeeName?.toLowerCase().contains(pattern.toLowerCase()) ?? false))
          .toList();
    }
    
    // Fetch from API if not cached
    final suggestions = await YourApiFunction.fetchEmployees(
      widget.user.token!,
      pattern,
    );
    
    if (!mounted) return [];
    
    setState(() {
      _employeeSuggestions = suggestions;
    });
    
    return suggestions;
  } catch (e) {
    debugPrint('Error fetching employee suggestions: $e');
    return [];
  }
}
```

## Integration Testing

After implementing both frontend and backend:

1. **Test List Screen**: Verify data loading, filtering, and navigation
2. **Test Detail Screen**: Test form inputs, validations, and saving
3. **Test API Integration**: Ensure API calls work correctly
4. **Test Edge Cases**: Handle error conditions, network issues, etc.

Use the dummy data approach initially to validate UI, then switch to real API integration when both frontend and backend are ready. 