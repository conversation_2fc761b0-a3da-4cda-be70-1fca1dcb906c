import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/utils/numberHelper.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountAndroid.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountiOS.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/RowDetail.dart';
import '../element/TableImExportWH.dart';
import '../element/TableInfo.dart';
import '../element/timeOut.dart';
import '../model/GetBarcodeReceive.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getListSOWBSByBatchTranferWarehouse.dart';
import '../model/getReservation.dart';
import '../model/getStorageBin.dart';
import '../model/listWarehouseTranfer.dart';
import '../model/postGetListSOWBSByBatch.dart';
import '../model/slocAddresse.dart';
import '../model/sumAmountExport.dart';
import '../model/warehouseTranfer.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/importWareHouseFunction.dart';
import 'LostConnect.dart';

class ExportWarehouse2 extends StatefulWidget {
  final DataListWarehouseTranfer dataListWarehouseTranfer;
  final String token;
  final String plant;
  final List<DataSlocAddress> lsDataSlocAddress;
  final String dateTimeOld;
  const ExportWarehouse2(
      {Key? key,
      required this.dataListWarehouseTranfer,
      required this.token,
      required this.plant,
      required this.lsDataSlocAddress,
      required this.dateTimeOld})
      : super(key: key);
  @override
  _ExportWarehouse2State createState() => _ExportWarehouse2State();
}

class _ExportWarehouse2State extends State<ExportWarehouse2> {
  final _storageBinController = TextEditingController();
  final _storageBinControllerImport = TextEditingController();
  final _focusStorageBinController = FocusNode();
  final _focusStorageBinControllerImport = FocusNode();
  List<TextEditingController> _lsControllerSoLuongChuyen = [];
  List<TextEditingController> _lsControllerSTTLine = [];
  List<FocusNode> _lsFocusNode = [];
  List<DataGetListSOWBSByBatchTranferWareHouse> _lsDataGetListSOWBSByBatchTranferWareHouse = [];
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isLoadingRawMaterial = false;
  List<DataGetBarcodeReceive> _dataGetBarcodeReceive = [];

  DataSlocAddress? _selectedSloc;
  DataSlocAddress? _selectedSlocInput;

  DataReservation? _dataReservation;

  List<DataReservation>? _dataReservationList;

  String? _defaultStorageBinIDExport;
  String? _defaultStorageBinIDImport;
  List<SumAmountExport> _sumAmountEntered = [];
  bool _errorSelectedSlocImport = false;
  bool _disableDropdown = false;
  bool _disableDropdownInput = false;
  // bool _errorStorageBinExistenceExport = false;
  // bool _errorStorageBinExistenceImport = false;
  bool _errorQuantity = false;
  bool? _disableButton;
  String _message = "";
  String? _materialID;
  List<DataSlocAddress?> _getLsDataSlocAddress = [];
  late bool _timeOut;
  List<DataGetStorageBin> _lsDataGetStorageBn = [];
  List<DataGetStorageBin> _lsDataGetStorageBnImport = [];
  bool _errorStorageBin = false;
  bool _errorStorageBinImport = false;
  Timer? _timer;
  Timer? _timerImport;
  bool _isLoadingStorageBin = false;
  bool _isLoadingStorageBinImport = false;

  @override
  void initState() {
    super.initState();

    _init();
  }

  void _setFilter(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _storageBinController.text = dataGetStorageBin.value ?? " ";
      _defaultStorageBinIDExport = dataGetStorageBin.key ?? " ";
      _lsDataGetStorageBn = [];
    });
  }

  void _setFilterImport(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _storageBinControllerImport.text = dataGetStorageBin.value ?? " ";
      _defaultStorageBinIDImport = dataGetStorageBin.key ?? " ";
      _lsDataGetStorageBnImport = [];
    });
  }

  Future<void> _onChangeNVL(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBin != true) {
          _errorStorageBin = true;
        }
      } else {
        if (_errorStorageBin != false) {
          _errorStorageBin = false;
        }
      }
    });
    if (!mounted) return;
    _timer?.cancel();
    if (_errorStorageBin == false) {
      _getAutocompleteStorageBin(value);
    }
  }

  Future<void> _onChangeNVLImport(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBinImport != true) {
          _errorStorageBinImport = true;
        }
      } else {
        if (_errorStorageBinImport != false) {
          _errorStorageBinImport = false;
        }
      }
    });
    if (!mounted) return;
    _timerImport?.cancel();
    if (_errorStorageBinImport == false) {
      _getAutocompleteStorageBinImport(value);
    }
  }

  Future<void> _getAutocompleteStorageBin(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = false;
              if (data != null) {
                _lsDataGetStorageBn = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _getAutocompleteStorageBinImport(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timerImport = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBinImport = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBinImport = false;
              if (data != null) {
                _lsDataGetStorageBnImport = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _getDataRawMaterial(String materialID, BuildContext context) async {
    try {
      setState(() {
        _isLoadingRawMaterial = true;
      });

      if (kDebugMode) {
        print(materialID);
      }

      final dataBarcodeReceived = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);

      if (!mounted) return;

      if (dataBarcodeReceived != null) {
        // var isMaterialOnHeader = dataBarcodeReceived.productCode == _dataReservation!.materialCode;

        bool isMaterialOnHeader = false;

        isMaterialOnHeader = _dataReservationList!.any((item) => item.materialCode == dataBarcodeReceived.productCode);

        // 1. Material không tồn tại trên reservatoin
        if (!isMaterialOnHeader) {
          setState(() {
            _errorQuantity = false;
            _isLoadingRawMaterial = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Mã NVL không đúng vui lòng quét lại!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1),
            ),
          );
        } else {
          // 2. Materrial có trên header
          // 2.1. Kiểm tra batch đã load chưa (khi quét lại)
          if (_dataGetBarcodeReceive.firstWhereOrNull(
                  (element) => element.productCode == dataBarcodeReceived.productCode && element.batchNumber == dataBarcodeReceived.batchNumber) !=
              null) {
            setState(() {
              _isLoadingRawMaterial = false;
            });
          } else {
            // 2.2. Quét mới thì sẽ request lên server lấy thông tin batch
            PostGetListSOWBSByBatch postGetListSOWBSByBatch = PostGetListSOWBSByBatch(
              productCode: dataBarcodeReceived.productCode,
              plant: widget.plant,
              sloc: _selectedSloc!.sloc,
              batchNumber: dataBarcodeReceived.batchNumber,
            );
            final getListSOWBS = await ExportWareHouseFunction.fetchListSOWBSBYBatchTranfer(postGetListSOWBSByBatch, widget.token);
            if (!mounted) return;

            debugPrint(jsonEncode(getListSOWBS));

            // 3.1 Lấy thông tin SO/WBS by Batch
            if (getListSOWBS != null) {
              for (int i = 0; i < getListSOWBS.length; i++) {
                if (getListSOWBS.isNotEmpty) {
                  getListSOWBS[i].productCode = dataBarcodeReceived.productCode;
                  getListSOWBS[i].batchNumber = dataBarcodeReceived.batchNumber;
                  getListSOWBS[i].rawMaterialCardId = materialID;
                }

                // getListSOWBS[i].wbs = "PRJ2-0526/1/1/001"
                // var foundHeader = _dataReservationList.firstWhereOrNull((element) => getListSOWBS[i].wbs.contains(element.note));
              }

              setState(() {
                _errorQuantity = false;
                // _lsControllers = [];
                // _lsFocusNode = [];
                _materialID = materialID;
                _isLoadingRawMaterial = false;
                // if(_dataGetBarcodeReceive.firstWhereOrNull((element) => element.batchNumber == dataBarcodeReceive.batchNumber) == null){
                _dataGetBarcodeReceive.add(dataBarcodeReceived);

                // _sumAmountEntered.add(
                //   SumAmountExport(
                //     code: dataBarcodeReceived.batchNumber.toString(),
                //     sum: 0.0,
                //     productCode: dataBarcodeReceived.productCode,
                //   ),
                // );

                // Sử dụng để chạy những batch mới thêm vào getListSOWBS
                var startIndex = _lsDataGetListSOWBSByBatchTranferWareHouse.length - 1;
                _lsDataGetListSOWBSByBatchTranferWareHouse.addAll(getListSOWBS);
                _lsDataGetListSOWBSByBatchTranferWareHouse.removeWhere(
                  (element) =>
                      (element.so == null || element.so == "") &&
                      (element.soLine == null || element.soLine == "") &&
                      (element.wbs == null || element.wbs == "") &&
                      (element.quantity == null || element.quantity == 0.0),
                );

                if (_lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty) {
                  // Chạy những LINE BATCH WBS vừa mới thêm vào
                  for (int i = startIndex + 1; i < _lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
                    // Nhiều line 1 wbs
                    List<int> foundIndices = [];
                    double totalReqQuantity = 0;

                    var itemDetail = _lsDataGetListSOWBSByBatchTranferWareHouse[i];

                    // Step 1
                    var quantityItemIndex = _dataReservationList?.indexWhere((element) =>
                        element.materialCode == itemDetail.productCode &&
                        element.note!.contains(itemDetail.wbs.toString()) &&
                        element.reqQuantity == itemDetail.quantity);

                    if (quantityItemIndex != -1) {
                      foundIndices.add(quantityItemIndex! + 1); // +1 to the index when adding to the list
                      totalReqQuantity += itemDetail.quantity!;

                      _lsControllerSoLuongChuyen.add(TextEditingController(text: totalReqQuantity.toString()));
                      _lsControllerSTTLine.add(TextEditingController(text: foundIndices.join(',')));
                      _lsFocusNode.add(FocusNode());
                      continue;
                    }

                    // Step 2
                    // Chạy từng LINE HEADER để lấy ra số lượng cần chuyển và STT line tương ứng với những line mới thêm và
                    for (int j = 0; j < _dataReservationList!.length; j++) {
                      var headerItem = _dataReservationList![j];
                      var wbsItem = itemDetail.wbs;
                      var headerBatch = headerItem.batch;

                      // Nếu wbs trên server có chứa wbs trên header thì lấy ra STT line và số lượng cần chuyển
                      if (wbsItem != null) {
                        var condition1 = headerItem.materialCode == itemDetail.productCode && headerItem.note!.contains(itemDetail.wbs.toString());
                        var condition2 = true;

                        if (headerBatch != null && headerBatch != "") {
                          condition2 = (headerBatch == itemDetail.batchNumber);
                        }

                        if (condition1 && condition2) {
                          double? slYeuCau = headerItem.reqQuantity;
                          double? slTon = itemDetail.quantity;

                          // // TODO: kiểm tra line này đã phân bổ trước đó chưa, nếu có rồi thì thê
                          // var sttLineDaPhanBo = _lsControllerSTTLine.firstWhereOrNull((element) => element.text == (j + 1).toString());

                          // if (sttLineDaPhanBo != null) {
                          //   int? soLuongChuyenIdex = int.tryParse(sttLineDaPhanBo.text);

                          //   if (soLuongChuyenIdex != null) {
                          //     double? quantity = double.tryParse(_lsControllerSoLuongChuyen[soLuongChuyenIdex].text);

                          //     if (quantity != null) {
                          //       totalReqQuantity += quantity;
                          //     }
                          //   }
                          // }

                          var slDaPhanBo = _getSLDaPhanBo(headerItem.materialCode, wbsItem);

                          slYeuCau = roundToDecimals((slYeuCau! - slDaPhanBo), 3);
                          // 0.1 < 0.2
                          if (slTon! <= slYeuCau!) {
                            slYeuCau = slTon;
                          }

                          // slYeuCau = slYeuCau - slDaPhanBo;
                          // if(slYeuCau >slDaPhanBo)
                          if (slYeuCau > 0) {
                            foundIndices.add(j + 1); // +1 to the index when adding to the list
                            totalReqQuantity += slYeuCau;
                          }
                        }
                      }
                    }

                    // var isPointer = pointerUnits.contains(_lsDataGetListSOWBSByBatchTranferWareHouse[i].unit);

                    // if (isPointer) {
                    //   totalReqQuantity = double.parse(totalReqQuantity.toStringAsFixed(3));
                    // } else {
                    //   totalReqQuantity = double.parse(totalReqQuantity.toStringAsFixed(0));
                    // }

                    if (foundIndices.isNotEmpty) {
                      _lsControllerSoLuongChuyen.add(TextEditingController(text: totalReqQuantity.toString()));
                      _lsControllerSTTLine.add(TextEditingController(text: foundIndices.join(',')));
                    } else {
                      _lsControllerSoLuongChuyen.add(TextEditingController());
                      _lsControllerSTTLine.add(TextEditingController());
                    }

                    // 1 line 1 wbs
                    // var foundHeaderIndex = _dataReservationList?.indexWhere((element) => element.note!.contains(getListSOWBS[i].wbs.toString()));
                    // if (foundHeaderIndex != -1) {
                    //   var foundHeader = _dataReservationList![foundHeaderIndex!];
                    //   _lsControllerSoLuongChuyen.add(TextEditingController(text: foundHeader.reqQuantity.toString()));
                    //   _lsControllerSTTLine.add(TextEditingController(text: (foundHeaderIndex + 1).toString()));
                    // } else {
                    //   _lsControllerSoLuongChuyen.add(TextEditingController());
                    //   _lsControllerSTTLine.add(TextEditingController());
                    // }

                    _lsFocusNode.add(FocusNode());
                  }

                  var batchNumber = dataBarcodeReceived.batchNumber.toString();
                  var productCode = dataBarcodeReceived.productCode;
                  List<double> getListDouble = ExportWareHouseFunction.getListDouble2(
                    batchNumber,
                    _lsDataGetListSOWBSByBatchTranferWareHouse,
                    _lsControllerSoLuongChuyen,
                    productCode,
                  );

                  double sum = ImportWareHouseFunction.amountListNumber(getListDouble);
                  SumAmountExport newSumAmountExport = SumAmountExport(
                    code: batchNumber,
                    sum: sum,
                    productCode: productCode,
                  );
                  _sumAmountEntered.add(newSumAmountExport);
                } else {
                  _sumAmountEntered.add(
                    SumAmountExport(
                      code: dataBarcodeReceived.batchNumber.toString(),
                      sum: 0.0,
                      productCode: dataBarcodeReceived.productCode,
                    ),
                  );
                }
              });
              // print("getListSOWBS");
              // print(json.encode(getListSOWBS));
              // print("_dataGetBarcodeReceive");
              // print(json.encode(_dataGetBarcodeReceive));
              // print("_lsDataGetListSOWBSByBatchTranferWareHouse");
              // print(json.encode(_lsDataGetListSOWBSByBatchTranferWareHouse));
            } else {
              // 3.2 Batch không có WBS
              setState(() {
                // _lsDataGetListSOWBSByBatchTranferWareHouse = [];
                // _materialID = null;
                // _selectedSloc = null;
                // if(_dataGetBarcodeReceive.firstWhereOrNull((element) => element.batchNumber == dataBarcodeReceive.batchNumber) == null){
                _dataGetBarcodeReceive.add(dataBarcodeReceived);
                _sumAmountEntered.add(SumAmountExport(code: dataBarcodeReceived.batchNumber.toString(), sum: 0.0));
                // }
                _isLoadingRawMaterial = false;
                // _lsControllers = [];
                // _lsFocusNode = [];
              });
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  backgroundColor: Colors.black,
                  content: Text(
                    'Không tìm thấy thông tin danh sách SO/WBS',
                    style: TextStyle(fontSize: 15.sp, color: Colors.white),
                  ),
                  duration: const Duration(seconds: 1)));
            }
          }
        }
      } else {
        setState(() {
          // _lsDataGetListSOWBSByBatchTranferWareHouse = [];
          // _materialID = null;
          // _selectedSloc = null;
          // _dataGetBarcodeReceive = null;
          _isLoadingRawMaterial = false;
          // _lsControllers = [];
          // _lsFocusNode =  [];
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin NVL chuyển kho!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingRawMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoadingRawMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi vui lòng thử lại sau!',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setSelectedSlocOnInit() {
    // CLEAR
    // _selectedSlocInput = null;
    // _storageBinControllerImport.text = "";
    // _defaultStorageBinIDImport = null;

    // SELECT
    // _selectedSlocInput = selectedSlocItem;
    // _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
    // _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
    // if (_selectedSlocInput == null) {
    //   _errorSelectedSlocImport = true;
    // } else {
    //   _errorSelectedSlocImport = false;
    // }

    // Kho xuất
    if (_dataReservation!.storageLocation!.isNotEmpty) {
      var khoXuatData = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == _dataReservation!.storageLocation);

      if (khoXuatData != null) {
        _selectedSloc = khoXuatData;
        _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
        _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
        _lsDataGetStorageBn = [];
        _errorStorageBin = false;
      }
    }

    // Kho nhập
    if (_dataReservation!.riStorageLocation!.isNotEmpty) {
      var khoNhapData = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == _dataReservation!.riStorageLocation);

      if (khoNhapData != null) {
        _selectedSlocInput = khoNhapData;
        _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
        _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
        _lsDataGetStorageBnImport = [];
        _errorStorageBinImport = false;
      }
    }
  }

  Future<void> _init() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _isNotWifi = false;
        });

        var reservationId = widget.dataListWarehouseTranfer.reservationId.toString();
        final dataList = await ExportWareHouseFunction.fetchReservationItems(reservationId, widget.token);
        _dataReservationList = dataList;

        debugPrint("jsonEncode(_dataReservationList)");
        debugPrint(jsonEncode(_dataReservationList));
        // var data = dataList[0];
        var data = dataList?.where((element) => element.reservationId == reservationId).firstOrNull;
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          if (data != null) {
            _dataReservation = data;
          }
          _getLsDataSlocAddress = widget.lsDataSlocAddress.map((e) => DataSlocAddress.clone(e)).toList();
        });
        // print("_dataReservation");
        // print(json.encode(_dataReservation));
        // print("_getLsDataSlocAddress");
        // print(json.encode(_getLsDataSlocAddress));
        _setSelectedSlocOnInit();
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _timeOut = false;
      });
    }
  }

  void _setAddressQRCode(DataSlocAddress? data, BuildContext context, String isFrom) {
    try {
      if (isFrom == 'export') {
        if (_dataReservation!.storageLocation != null && _dataReservation!.storageLocation != "") {
          var slocNotValid = _dataReservation!.storageLocation != data!.sloc;

          if (kDebugMode) {
            slocNotValid = false;
          }

          if (slocNotValid) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho xuất không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBin =
                  data.defaultStorageBin;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                  data.defaultStorageBinId;
              _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
              if (_selectedSloc != null) {
                _disableDropdown = true;
                _materialID = null;
                _dataGetBarcodeReceive = [];
                _lsDataGetListSOWBSByBatchTranferWareHouse = [];
                _lsControllerSoLuongChuyen = [];
                _lsControllerSTTLine = [];
                _lsFocusNode = [];
                _sumAmountEntered = [];
              }
              _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
              _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
              _lsDataGetStorageBn = [];
              _errorStorageBin = false;
            });
          }
        } else {
          if (!mounted) return;
          setState(() {
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data!.sloc)]!.defaultStorageBin =
                data!.defaultStorageBin;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                data.defaultStorageBinId;
            _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
            if (_selectedSloc != null) {
              _disableDropdown = true;
              _materialID = null;
              _dataGetBarcodeReceive = [];
              _lsDataGetListSOWBSByBatchTranferWareHouse = [];
              _lsControllerSoLuongChuyen = [];
              _lsControllerSTTLine = [];
              _lsFocusNode = [];
              _sumAmountEntered = [];
            }
            _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
            _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
            _lsDataGetStorageBn = [];
            _errorStorageBin = false;
          });
        }
      } else {
        if (_dataReservation!.riStorageLocation != null && _dataReservation!.riStorageLocation != "") {
          if (_dataReservation!.riStorageLocation != data!.sloc) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho nhập không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBin =
                  data.defaultStorageBin;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                  data.defaultStorageBinId;
              _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
              if (_selectedSlocInput != null) {
                _errorSelectedSlocImport = false;
              }
              if (_selectedSlocInput != null) {
                _disableDropdownInput = true;
              }
              _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
              _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
              _lsDataGetStorageBnImport = [];
              _errorStorageBinImport = false;
            });
          }
        } else {
          if (!mounted) return;
          setState(() {
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data!.sloc)]!.defaultStorageBin =
                data!.defaultStorageBin;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                data.defaultStorageBinId;
            _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
            if (_selectedSlocInput != null) {
              _errorSelectedSlocImport = false;
            }
            if (_selectedSlocInput != null) {
              _disableDropdownInput = true;
            }
            _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
            _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
            _lsDataGetStorageBnImport = [];
            _errorStorageBinImport = false;
          });
        }
      }
    } catch (error) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      debugPrint("error: $error");
    }
  }

  void _setSloc(DataSlocAddress? value, BuildContext context) {
    try {
      if (_dataReservation!.storageLocation != null && _dataReservation!.storageLocation != "") {
        if (value!.slocDisplay == "--Bỏ chọn--") {
          _selectedSloc = null;
          _storageBinController.text = "";
          _defaultStorageBinIDExport = null;
          _materialID = null;
          _dataGetBarcodeReceive = [];
          _lsDataGetListSOWBSByBatchTranferWareHouse = [];
          _lsControllerSoLuongChuyen = [];
          _lsControllerSTTLine = [];
          _lsFocusNode = [];
          _sumAmountEntered = [];
        } else {
          var slocNotValid = _dataReservation!.storageLocation != value.sloc;

          if (kDebugMode) {
            slocNotValid = false;
          }

          if (slocNotValid) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho xuất không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _selectedSloc = value;
              _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
              _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
              _materialID = null;
              _dataGetBarcodeReceive = [];
              _lsDataGetListSOWBSByBatchTranferWareHouse = [];
              _lsControllerSoLuongChuyen = [];
              _lsControllerSTTLine = [];
              _lsFocusNode = [];
              _sumAmountEntered = [];
            });
          }
        }
      } else {
        if (!mounted) return;
        setState(() {
          if (value!.slocDisplay == "--Bỏ chọn--") {
            _selectedSloc = null;
            _storageBinController.text = "";
            _defaultStorageBinIDExport = null;
            _materialID = null;
            _dataGetBarcodeReceive = [];
            _lsDataGetListSOWBSByBatchTranferWareHouse = [];
            _lsControllerSoLuongChuyen = [];
            _lsControllerSTTLine = [];
            _lsFocusNode = [];
            _sumAmountEntered = [];
          } else {
            _selectedSloc = value;
            _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
            _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
            _materialID = null;
            _dataGetBarcodeReceive = [];
            _lsDataGetListSOWBSByBatchTranferWareHouse = [];
            _lsControllerSoLuongChuyen = [];
            _lsControllerSTTLine = [];
            _lsFocusNode = [];
            _sumAmountEntered = [];
          }
        });
      }
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _selectedSloc = null;
        _storageBinController.text = "";
        _defaultStorageBinIDExport = null;
        _materialID = null;
        _dataGetBarcodeReceive = [];
        _lsDataGetListSOWBSByBatchTranferWareHouse = [];
        _lsControllerSoLuongChuyen = [];
        _lsControllerSTTLine = [];
        _lsFocusNode = [];
        _sumAmountEntered = [];
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setSlocInput(DataSlocAddress? selectedSlocItem, BuildContext context) {
    if (_dataReservation!.riStorageLocation != null && _dataReservation!.riStorageLocation != "") {
      if (selectedSlocItem!.slocDisplay == "--Bỏ chọn--") {
        _selectedSlocInput = null;
        _storageBinControllerImport.text = "";
        _defaultStorageBinIDImport = null;
      } else {
        if (_dataReservation!.riStorageLocation != selectedSlocItem.sloc) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Sloc kho nhập không đúng, vui lòng chọn lại!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1)));
        } else {
          setState(() {
            _selectedSlocInput = selectedSlocItem;
            _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
            _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
            if (_selectedSlocInput == null) {
              _errorSelectedSlocImport = true;
            } else {
              _errorSelectedSlocImport = false;
            }
          });
        }
      }
    } else {
      setState(() {
        if (selectedSlocItem!.slocDisplay == "--Bỏ chọn--") {
          _selectedSlocInput = null;
          _storageBinControllerImport.text = "";
          _defaultStorageBinIDImport = null;
        } else {
          _selectedSlocInput = selectedSlocItem;
          _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
          _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
          if (_selectedSlocInput == null) {
            _errorSelectedSlocImport = true;
          } else {
            _errorSelectedSlocImport = false;
          }
        }
      });
    }
  }

  void _checkError() {
    setState(() {
      if (_selectedSlocInput == null) {
        _errorSelectedSlocImport = true;
      } else {
        _errorSelectedSlocImport = false;
      }
      // if (_storageBinController.text.isEmpty) {
      //   if( _errorStorageBinExistenceExport != false){
      //     _errorStorageBinExistenceExport = false;
      //   }
      // }else{
      //   if(_getLsDataSlocAddress.firstWhereOrNull((element) => element != null && element.defaultStorageBin == _storageBinController.text) != null){
      //     _errorStorageBinExistenceExport = false;
      //   }else{
      //     _errorStorageBinExistenceExport = true;
      //   }
      // }
      if (_lsControllerSoLuongChuyen.where((element) => element.text.isNotEmpty).isNotEmpty) {
        if (_lsControllerSoLuongChuyen.where((element) => element.text.isNotEmpty && double.parse(element.text) <= 0.0).isNotEmpty) {
          _errorQuantity = true;
          _message = "Số lượng chuyển không được nhỏ hơn 0.0";
        } else {
          _errorQuantity = false;
          _message = "";
        }
      } else {
        _errorQuantity = true;
        _message = "Vui lòng nhập số lượng chuyển";
      }

      // if (_storageBinControllerImport.text.isEmpty) {
      //   if( _errorStorageBinExistenceImport != false){
      //     _errorStorageBinExistenceImport = false;
      //   }
      // }else{
      //   if(_getLsDataSlocAddress.firstWhereOrNull((element) => element != null && element.defaultStorageBin == _storageBinControllerImport.text) != null){
      //     _errorStorageBinExistenceImport = false;
      //   }else{
      //     _errorStorageBinExistenceImport = true;
      //   }
      // }
      // debugPrint(ImportWareHouseFunction.checkIsFormatDouble(_lsControllers).toString());
      // if(ImportWareHouseFunction.checkIsFormatDouble(_lsControllers) == false){
      //   _errorFormatView = true;
      // }else{
      //   _errorFormatView = false;
      // }
    });
  }

  Future<void> _sendPost(BuildContext context) async {
    List<WarehouseTranferDetails>? lsWareHouseTranferDetails = ExportWareHouseFunction.getListWarehouseTranferDetails2(
      _lsControllerSoLuongChuyen,
      _lsDataGetListSOWBSByBatchTranferWareHouse,
      _lsControllerSTTLine,
    );
    WarehouseTranfer warehouseTranfer = WarehouseTranfer(
      reservationId: widget.dataListWarehouseTranfer.reservationId,
      slocExportId: _selectedSloc!.slocId,
      storageBinExportId: _storageBinController.text.isEmpty ? null : _defaultStorageBinIDExport,
      slocImportId: _selectedSlocInput!.slocId,
      storageBinImportId: _storageBinControllerImport.text.isEmpty ? null : _defaultStorageBinIDImport,
      rawMaterialCardId: _materialID,
      // batchNumber: _dataGetBarcodeReceive!.batchNumber,
      warehouseTranferDetails: lsWareHouseTranferDetails,
    );
    debugPrint("jsonEncode(warehouseTranfer)");
    debugPrint(jsonEncode(warehouseTranfer));

    var dummyWarehouseTransfer =
        '{"reservationId":"8b5393f6-0d42-42f7-93b4-d61ca9c9db68","slocExportId":"fa52b581-e12b-4988-ac81-7f08e8c378c0","storageBinExportId":null,"slocImportId":"915c1d28-d204-453b-b418-a2497c79925c","storageBinImportId":null,"rawMaterialCardId":"891d2e56-68f3-466d-89ed-2eeb016e84f3","warehouseTranferDetails":[{"productCode":"291010265","so":null,"soLine":null,"wbs":"PRJ2-0526/1/1/001","quantity":3.0,"unit":"CON","batchNumber":"HW280819TT","rawMaterialCardId":"f9311920-2abe-44e8-bef2-2912e3a2c824"},{"productCode":"291010265","so":null,"soLine":null,"wbs":"PRJ2-0778/1/1/002","quantity":2.0,"unit":"CON","batchNumber":"HW280819TT","rawMaterialCardId":"f9311920-2abe-44e8-bef2-2912e3a2c824"},{"productCode":"300000278","so":null,"soLine":null,"wbs":null,"quantity":4.0,"unit":"CON","batchNumber":"DC220720TT","rawMaterialCardId":"badee50e-225d-4371-9f74-e1f13582c73e"},{"productCode":"300000278","so":null,"soLine":null,"wbs":"PRJ1-1431/1/1","quantity":1.0,"unit":"CON","batchNumber":"TT11011910","rawMaterialCardId":"891d2e56-68f3-466d-89ed-2eeb016e84f3"}]}';

    WarehouseTranfer warehouseTranferDummy = WarehouseTranfer.fromJson(jsonDecode(dummyWarehouseTransfer));

    await ExportWareHouseFunction.sendWarehouseFuncion2(warehouseTranfer, widget.token, context, widget.plant);
  }

  double _getSLDaPhanBo(String? productCode, String wbs) {
    if (productCode == null) {
      return 0.0;
    }

    // double total = _lsDataGetListSOWBSByBatchTranferWareHouse
    //     .where((item) => item.productCode == productCode && item.wbs == wbs)
    //     .map((item) => item.quantity ?? 0.0)
    //     .fold(0.0, (prev, element) => prev + element);

    // double total = _sumAmountEntered.where((item) => item.productCode == productCode).map((item) => item.sum).fold(0.0, (prev, sum) => prev + sum);

    // Step 1: Find the indexes where productCode and wbs match
    List<int> matchedIndexes = [];
    for (int i = 0; i < _lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      if (_lsDataGetListSOWBSByBatchTranferWareHouse[i].productCode == productCode && _lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs == wbs) {
        matchedIndexes.add(i);
      }
    }

    // Step 2: Retrieve the entries from _lsControllerSoLuongChuyen using the indexes
    List<String> matchedTexts = [];

    for (int index in matchedIndexes) {
      if (index < _lsControllerSoLuongChuyen.length && _lsControllerSoLuongChuyen[index] != null) {
        matchedTexts.add(_lsControllerSoLuongChuyen[index].text);
      }
    }

    // Step 3: Sum the .text values
    double total = matchedTexts.fold(0.0, (prev, text) => prev + double.parse(text));

    return total;
  }

  Future<void> _submitData(BuildContext context) async {
    if (_isLoadingRawMaterial == true) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 2)));
    } else {
      _checkError();
      if (_errorSelectedSlocImport == false && _errorQuantity == false
          // &&
          // _errorFormatView == false
          ) {
        FocusScope.of(context).unfocus();

        // double numberSum = _sumAmountEntered.map((e) => e.sum).toList().reduce((a, b) => a + b);

        var isSLChuyenVuotYeuCau = false;

        Set<String?> uniqueMaterialCodes = _dataReservationList!.map((reservation) => reservation.materialCode).toSet();

        for (String? materialCode in uniqueMaterialCodes) {
          bool isPointer = false;

          var productItem = _dataReservationList?.firstWhereOrNull((element) => element.materialCode == materialCode);

          if (productItem != null) {
            isPointer = pointerUnits.contains(productItem.unit);
          }

          double reservationSum = _dataReservationList!
              .where((reservation) => reservation.materialCode == materialCode)
              .map((e) => e.reqQuantityRnd ?? 0)
              .fold(0, (a, b) => a + b); // fold used instead of reduce to handle empty lists.

          double numberSum = _sumAmountEntered.where((e) => e.productCode == materialCode).map((e) => e.sum).fold(0, (a, b) => a + b);

          if (isPointer) {
            reservationSum = double.parse(reservationSum.toStringAsFixed(3));
          } else {
            reservationSum = double.parse(reservationSum.toStringAsFixed(0));
          }

          if (numberSum != reservationSum) {
            isSLChuyenVuotYeuCau = true;
            break; // Exit the loop early as we've found a mismatch.
          }
        }

        if (isSLChuyenVuotYeuCau) {
          bool? check = false;

          if (Platform.isAndroid) {
            check = await showDialog<bool>(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => const DialogExceedTheAmountAndroid(
                      message: 'Số lượng chuyển khác số lượng yêu cầu, bạn có muốn tiếp tục?',
                    ));
          } else {
            check = await showCupertinoDialog<bool>(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => const DialogExceedTheAmountIos(
                      title: 'SL chuyển khác SL yêu cầu',
                      message: 'Số lượng chuyển khác số lượng yêu cầu, bạn có muốn tiếp tục?',
                    ));
          }

          if (!mounted) return;
          if (check == null) return;
          if (check == true) {
            _sendPost(context);
          }

          return;
        }

        _sendPost(context);
      }
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  String getSLYeuCauByProductCode(String? productCode) {
    var ret = "";

    if (productCode == null) {
      return "";
    }

    bool isPointer = false;

    var productItem = _dataReservationList?.firstWhereOrNull((element) => element.materialCode == productCode);

    if (productItem != null) {
      isPointer = pointerUnits.contains(productItem.unit);
    }

    var data = _dataReservationList?.where((element) => element.materialCode == productCode).map((e) => e.reqQuantity).fold(0.0, (a, b) => a + b!);

    if (data != null) {
      if (isPointer) {
        return data.toStringAsFixed(3);
      }

      return data.toStringAsFixed(0);
    }

    return "";
  }

  List<String> filterListNo(String? productCode, String? wbs) {
    if (productCode == null || wbs == null) {
      return [];
    }

    List<String> indexList = [];

    // for (int i = 0; i < _dataReservationList!.length; i++) {
    //   if (_dataReservationList![i].materialCode == productCode) {
    //     indexList.add((i + 1).toString());
    //   }
    // }

    for (int i = 0; i < _dataReservationList!.length; i++) {
      debugPrint("i " + i.toString());

      debugPrint("_dataReservationList![i].materialCode");
      debugPrint(_dataReservationList![i].materialCode);

      debugPrint("_dataReservationList![i].note");
      debugPrint(_dataReservationList![i].note);

      if (_dataReservationList![i].materialCode == productCode && _dataReservationList![i].note!.contains(wbs!)) {
        indexList.add((i + 1).toString());
      }
    }

    return indexList;
  }

  void setControllerSTTLine(int index, String stt) {
    setState(() {
      _lsControllerSTTLine[index].text = stt;
    });
  }

  void _autoSetControllerSTTLineOneLine(int index) {
    int count = 0;

    var productCode = _lsDataGetListSOWBSByBatchTranferWareHouse[index].productCode;

    for (var element in _dataReservationList!) {
      if (element.materialCode == productCode) {
        count++;
      }
    }

    if (count == 1) {
      var indexReservation = _dataReservationList!.indexWhere((element) => element.materialCode == productCode);

      var stt = (indexReservation + 1).toString();

      setState(() {
        _lsControllerSTTLine[index].text = stt;
      });
    }
  }

  @override
  void dispose() {
    for (var i in _lsControllerSoLuongChuyen) {
      i.dispose();
    }
    for (var i in _lsControllerSTTLine) {
      i.dispose();
    }
    _storageBinController.dispose();
    _storageBinControllerImport.dispose();
    _focusStorageBinController.dispose();
    _focusStorageBinControllerImport.dispose();
    for (var i in _lsFocusNode) {
      i.dispose();
    }
    // for (var i in _lsFocusNode) {
    //   i.dispose();
    // }
    if (_timer != null) {
      _timer!.cancel();
    }
    if (_timerImport != null) {
      _timerImport!.cancel();
    }
    // _keyboardSubscription.cancel();
    super.dispose();
  }

  // 1. Config table lệnh cấp
  Map<int, TableColumnWidth> columnWidthConfigLenhCap() {
    return {
      0: FixedColumnWidth(40.w),
      1: FixedColumnWidth(80.w),
      2: FixedColumnWidth(70.w),
      3: FixedColumnWidth(70.w),
      4: FixedColumnWidth(200.w),
      5: FixedColumnWidth(100.w),
      6: FixedColumnWidth(50.w),
    };
  }

  var headerTitleLenhCap = <Widget>[
    const _TitleTableItem(text: "STT (No.)"),
    const _TitleTableItem(text: "Mã NVL"),
    const _TitleTableItem(text: "SL yêu cầu"),
    const _TitleTableItem(text: "ĐVT"),
    const _TitleTableItem(text: "Ghi chú"),
    const _TitleTableItem(text: "WBS"),
    const _TitleTableItem(text: "Item"),
  ];

  // 2. Config table thông tin NVL chuyển kho (batch)
  Map<int, TableColumnWidth> columnWidthConfigThongTinNVL() {
    return {
      0: FixedColumnWidth(100.w),
      1: FixedColumnWidth(70.w),
    };
  }

  var headerTitleThongTinNVL = <Widget>[
    const _TitleTableItem(text: "Số lô"),
    const _TitleTableItem(text: "SL chuyển"),
  ];

  // 3. Config table chi tiết
  Map<int, TableColumnWidth> columnWidthConfigChiTiet() {
    return {
      0: FixedColumnWidth(75.w),
      1: FixedColumnWidth(90.w),
      2: FixedColumnWidth(100.w),
      3: FixedColumnWidth(100.w),
      4: FixedColumnWidth(100.w),
      5: FixedColumnWidth(70.w),
      6: FixedColumnWidth(70.w),
      7: FixedColumnWidth(70.w),
      8: FixedColumnWidth(70.w),
    };
  }

  var headerTitleChiTiet = <Widget>[
    const _TitleTableIV(text: "Mã NVL"),
    const _TitleTableIV(text: "Số lô"),
    const _TitleTableIV(text: "SO/SOLine"),
    const _TitleTableIV(text: "WBS"),
    const _TitleTableIV(text: "LSX Đại trà"),
    const _TitleTableIV(text: "SL chuyển"),
    const _TitleTableIV(text: "STT lệnh cấp"),
    const _TitleTableIV(text: "SL tồn"),
    const _TitleTableIV(text: "ĐVT"),
  ];

  double getSumForProductCode(String? productCode, String? batchNumber) {
    final sumAmount = _sumAmountEntered.firstWhere((element) => element.productCode == productCode && element.code == batchNumber,
        orElse: () => SumAmountExport(code: "", sum: 0.0, productCode: null));
    return sumAmount.sum;
  }

  List<DataGetBarcodeReceive> getUniqueProductCodes() {
    Set<String?> uniqueCodes = {}; // This set will track the unique product codes.
    List<DataGetBarcodeReceive> uniqueEntries = [];

    for (DataGetBarcodeReceive item in _dataGetBarcodeReceive) {
      if (!uniqueCodes.contains(item.productCode)) {
        uniqueCodes.add(item.productCode);
        uniqueEntries.add(item);
      }
    }

    return uniqueEntries;
  }

  List<Widget> generateWidgetsForData() {
    List<DataGetBarcodeReceive> uniqueProductCodes = getUniqueProductCodes();

    return uniqueProductCodes.expand((dataItem) {
      List<Widget> widgetsForCurrentProduct = [
        // General details for current productCode
        TableInfo(
          textCL1: "Mã NVL:",
          textCL2: dataItem.productCode ?? " ",
          colorCL1: 0xff303F9F,
          colorCL2: 0xffffffff,
        ),
        TableInfoNoTop(
          textCL1: "Tên NVL:",
          textCL2: dataItem.productName ?? " ",
          colorCL1: 0xff303F9F,
          colorCL2: 0xffffffff,
        ),
        TableInfoNoTop(
          textCL1: "Số lượng yêu cầu:",
          textCL2: getSLYeuCauByProductCode(dataItem.productCode ?? " "),
          colorCL1: 0xff303F9F,
          colorCL2: 0xffffffff,
        ),
        SizedBox(height: 2.h),
        // Header table
        Table(
          border: TableBorder.all(width: 0.5.w),
          columnWidths: columnWidthConfigThongTinNVL(),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: <TableRow>[
            TableRow(
              decoration: const BoxDecoration(
                color: Color(0xff303F9F),
              ),
              children: headerTitleThongTinNVL,
            ),
          ],
        )
      ];

      // Add all batchNumbers for current productCode
      List<Widget> batchWidgets = _dataGetBarcodeReceive.where((item) => item.productCode == dataItem.productCode).map((filteredDataItem) {
        return Table(
          border: TableBorder(
            left: BorderSide(color: Colors.black, width: 0.5.w),
            right: BorderSide(color: Colors.black, width: 0.5.w),
            bottom: BorderSide(color: Colors.black, width: 0.5.w),
            verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
          ),
          columnWidths: columnWidthConfigThongTinNVL(),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: <TableRow>[
            TableRow(
              children: <Widget>[
                _ColumnTableItem(text: filteredDataItem.batchNumber ?? ""),
                _ColumnTableItem(text: getSumForProductCode(filteredDataItem.productCode, filteredDataItem.batchNumber).toStringAsFixed(2)),
              ],
            ),
          ],
        );
      }).toList();

      widgetsForCurrentProduct.addAll(batchWidgets);

      double totalSumForCurrentProduct =
          _sumAmountEntered.where((item) => item.productCode == dataItem.productCode).map((item) => item.sum).fold(0.0, (prev, curr) => prev + curr);

      widgetsForCurrentProduct.add(
        Table(
          border: TableBorder(
            left: BorderSide(color: Colors.black, width: 0.5.w),
            right: BorderSide(color: Colors.black, width: 0.5.w),
            bottom: BorderSide(color: Colors.black, width: 0.5.w),
            verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
          ),
          columnWidths: columnWidthConfigThongTinNVL(),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: <TableRow>[
            TableRow(
              children: <Widget>[
                const _ColumnTableItem(text: "Tổng", fontWeight: FontWeight.bold),
                _ColumnTableItem(text: totalSumForCurrentProduct.toStringAsFixed(2), fontWeight: FontWeight.bold),
              ],
            ),
          ],
        ),
      );

      // // Add all batchNumbers for current productCode
      // widgetsForCurrentProduct.addAll(
      //   _dataGetBarcodeReceive.where((item) => item.productCode == dataItem.productCode).map((filteredDataItem) {
      //     return Table(
      //       border: TableBorder(
      //         left: BorderSide(color: Colors.black, width: 0.5.w),
      //         right: BorderSide(color: Colors.black, width: 0.5.w),
      //         bottom: BorderSide(color: Colors.black, width: 0.5.w),
      //         verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
      //       ),
      //       columnWidths: columnWidthConfigThongTinNVL(),
      //       defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      //       children: <TableRow>[
      //         TableRow(
      //           children: <Widget>[
      //             _ColumnTableItem(text: filteredDataItem.batchNumber ?? ""),
      //             _ColumnTableItem(text: getSumForProductCode(filteredDataItem.productCode, filteredDataItem.batchNumber).toStringAsFixed(2)),
      //           ],
      //         ),
      //       ],
      //     );
      //   }).toList(),
      // );

      widgetsForCurrentProduct.add(SizedBox(height: 15.h));

      return widgetsForCurrentProduct;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> dummyBarcode = [
      // 1697199
      {'reservationNumber': '1697199', 'materialID': 'b01a5f4e-0abd-4927-8cfc-7c8b5cf2eb2c', 'batch': 'DPL24823DH'},
      {'reservationNumber': '1697199', 'materialID': '99dddeac-6b1e-4696-bf26-7049358a3d91', 'batch': 'DPL08823DH'},
      {'reservationNumber': '1697199', 'materialID': '12ff287e-80a1-4c51-ac72-31e62ab925ff', 'batch': 'DPL17823DH'},
      // 1707138
      {'reservationNumber': '1707138', 'materialID': 'a6f5dbab-f677-46e4-ade8-18b67bef2f3e', 'batch': 'DHW03723DH'},
      {'reservationNumber': '1707138', 'materialID': 'c1fa6a71-b795-4f83-8928-2545d5dac8f3', 'batch': 'DHW26723DH'},
      {'reservationNumber': '1707138', 'materialID': 'fa3d2831-7334-4133-a079-9cba445c6938', 'batch': 'DHW04823DH'},
      // 1707806
      {'reservationNumber': '1707806', 'materialID': '66615c4e-b93b-4e9d-94b2-91c0e89d92ba', 'batch': 'DHW23823DH'},
      {'reservationNumber': '1707806', 'materialID': '1f37808c-bfe8-4b09-a23d-7cb741edc44e', 'batch': 'DHW23823DH'},
      {'reservationNumber': '1707806', 'materialID': '0146b465-feed-476b-8727-d35b331c3bb0', 'batch': 'DHW04823DH'},
      //
      {'reservationNumber': '1708832', 'materialID': '3a8ffc41-934f-42d1-982b-35be11b9fd1a', 'batch': 'DPL22823DH'},
      {'reservationNumber': '1708832', 'materialID': 'eb8b2d32-243c-47e1-bf1f-af818a152d8e', 'batch': 'DPL24823DH'},

      {'reservationNumber': '2234707', 'materialID': '81b00b31-c1ce-41f7-9373-e10a2846cd73', 'batch': 'DBP15624DH'},

// <T1>81b00b31-c1ce-41f7-9373-e10a2846cd73</T1><T2>294010953</T2>"<T3>DBP15624DH</T3>"
    ];

    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, false);
          return false;
        },
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: _timeOut == true
                ? WillPopScope(
                    onWillPop: () => Future.value(false),
                    child: Scaffold(
                        backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
                : Scaffold(
                    backgroundColor: Colors.grey.shade200,
                    appBar: AppBar(
                      titleSpacing: 0,
                      automaticallyImplyLeading: false,
                      backgroundColor: const Color(0xff0052cc),
                      elevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                        onPressed: () {
                          Navigator.pop(context, false);
                        },
                      ),
                      title: Text(
                        "Chuyển Kho NVL (nhiều line)",
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                      ),
                    ),
                    body: _isNotWifi
                        ? LostConnect(checkConnect: () => _init())
                        : _isLoading == true
                            ? const Center(child: CircularProgressIndicator())
                            : SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: <Widget>[
                                    const _TitleExportWareHouse(),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          const _HeaderExportWareHouse(title: "I. LỆNH CẤP VẬT TƯ"),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          TableInfoExport(
                                            textCL1: "Số reservation:",
                                            textCL2: _dataReservation!.reservationCode ?? "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),

                                          // TableInfoNoTopExport(
                                          //   textCL1: "Mã NVL:",
                                          //   textCL2: _dataReservation != null ? _dataReservation!.materialCode ?? " " : " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),

                                          // Nhà máy xuất, nhà máy nhập
                                          _PlantExportImport(dataReservation: _dataReservation),
                                          // Kho xuất, kho nhập
                                          _WareHouseExportImport(dataReservation: _dataReservation),

                                          // TableInfoNoTop(
                                          //   textCL1:"Nhà máy xuất:",
                                          //   textCL2: _dataReservation!.plant ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Kho xuất:",
                                          //   textCL2: _dataReservation!.storageLocation ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Số lô:",
                                          //   textCL2:  _dataReservation!.batch ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTopExport(
                                          //   textCL1: "Số lượng yêu cầu:",
                                          //   textCL2: _dataReservation!.reqQuantityRnd == null ? "" : (_dataReservation!.reqQuantityRnd.toString()),
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTopExport(
                                          //   textCL1: "ĐVT:",
                                          //   textCL2: _dataReservation!.unit ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Nhà máy nhập:",
                                          //   textCL2:  _dataReservation!.riPlant ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Kho nhập:",
                                          //   textCL2:  _dataReservation!.riStorageLocation ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"SO/ SO Line:",
                                          //   textCL2:  _dataReservation!.sosoLine ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"WBS:",
                                          //   textCL2:  _dataReservation!.wbs ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"LSX:",
                                          //   textCL2:  _dataReservation!.lsx ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          SizedBox(height: 5.h),
                                          SingleChildScrollView(
                                            scrollDirection: Axis.horizontal,
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Table(
                                                  border: TableBorder.all(width: 0.5.w),
                                                  columnWidths: columnWidthConfigLenhCap(),
                                                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                  children: <TableRow>[
                                                    TableRow(
                                                      decoration: const BoxDecoration(color: Color(0xff303F9F)),
                                                      children: headerTitleLenhCap,
                                                    ),
                                                  ],
                                                ),
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: List.generate(
                                                    _dataReservationList!.length,
                                                    (index) => Table(
                                                      border: TableBorder(
                                                        left: BorderSide(color: Colors.black, width: 0.5.w),
                                                        right: BorderSide(color: Colors.black, width: 0.5.w),
                                                        bottom: BorderSide(color: Colors.black, width: 0.5.w),
                                                        verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
                                                      ),
                                                      columnWidths: columnWidthConfigLenhCap(),
                                                      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                      children: <TableRow>[
                                                        TableRow(
                                                          children: <Widget>[
                                                            _ColumnTableItem(text: (index + 1).toString()),
                                                            _ColumnTableItem(text: _dataReservationList![index].materialCode ?? ""),
                                                            _ColumnTableItem(text: _dataReservationList![index].reqQuantity.toString()),
                                                            _ColumnTableItem(text: _dataReservationList![index].unit ?? ""),
                                                            _ColumnTableItem(
                                                              text: _dataReservationList![index].note ?? "",
                                                              fontSize: 10,
                                                              textAlign: TextAlign.left,
                                                            ),
                                                            _ColumnTableItem(text: _dataReservationList![index].wbs ?? ""),
                                                            _ColumnTableItem(text: _dataReservationList![index].item ?? ""),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          const _HeaderExportWareHouse(title: "THÔNG TIN GIAO DỊCH CHUYỂN KHO"),
                                          // Text(
                                          //   "II. THÔNG TIN GIAO DỊCH CHUYỂN KHO",
                                          //   style: TextStyle(
                                          //     fontSize: 14.sp,
                                          //     fontWeight: FontWeight.bold,
                                          //   ),
                                          // ),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Flexible(
                                                flex: 5,
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton.icon(
                                                    style: ButtonStyle(
                                                      // padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 0.0, horizontal: 12.0)),
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                      side: MaterialStateProperty.all(
                                                        const BorderSide(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                    ),
                                                    onPressed: () async {
                                                      FocusScope.of(context).unfocus();
                                                      final data = await Navigator.pushNamed(context, '/QRCodePageGetSlocExport');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      _setAddressQRCode(data as DataSlocAddress, context, 'export');
                                                    },
                                                    icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                    label: Text(
                                                      "Quét kho xuất",
                                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Flexible(
                                                flex: 5,
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton.icon(
                                                    style: ButtonStyle(
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                      side: MaterialStateProperty.all(
                                                        const BorderSide(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                    ),
                                                    onPressed: () async {
                                                      FocusScope.of(context).unfocus();
                                                      final data = await Navigator.pushNamed(context, '/QRCodePageGetSlocExport');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      _setAddressQRCode(data as DataSlocAddress, context, 'import');
                                                    },
                                                    icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                    label: Text(
                                                      "Quét kho nhập",
                                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                          const LabeledDetailRow(title: "Loại giao dịch:", text: "Chuyển kho 1 bước"),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          ContainerError.widgetError(_errorSelectedSlocImport, 'Kho nhập chưa được nhập'),
                                          SizedBox(height: _errorSelectedSlocImport == true ? 10.h : 0),
                                          const HeaderTableImExportWH(
                                              textCL1: "Kho xuất", textCL2: "Kho nhập", colorCL2: 0xff303F9F, colorCL1: 0xff303F9F),
                                          TableImExportWHDD(
                                            textCL1: "Sloc",
                                            selectedSloc: _selectedSloc,
                                            onChange: (value) => _setSloc(value, context),
                                            disableDropdown: _disableDropdown,
                                            disableDropdownInput: _disableDropdownInput,
                                            lsDataSlocAddress: _getLsDataSlocAddress,
                                            textCL3: "Sloc",
                                            selectedSlocInput: _selectedSlocInput,
                                            onChangeInput: (value) => _setSlocInput(value, context),
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          TableImExportWHText(
                                            textCL1: "Warehouse No",
                                            textCL2: _selectedSloc != null ? _selectedSloc!.warehouseNo ?? "" : "",
                                            textCL3: "Warehouse No",
                                            textCL4: _selectedSlocInput != null ? _selectedSlocInput!.warehouseNo ?? "" : "",
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 3,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xffEEEEEE),
                                                        border: Border(
                                                          left: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child: Text(
                                                      "Storage Bin",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 2,
                                                    child: Container(
                                                      height: double.infinity,
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                      decoration: BoxDecoration(
                                                          color: const Color(0xFFFFFFFF),
                                                          border: Border(
                                                            right: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                            bottom: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                          )),
                                                      child: Container(
                                                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                        decoration: BoxDecoration(
                                                            border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                            borderRadius: BorderRadius.circular(3.r)),
                                                        child: TextFormField(
                                                          maxLines: null,
                                                          textAlign: TextAlign.center,
                                                          controller: _storageBinController,
                                                          focusNode: _focusStorageBinController,
                                                          style: TextStyle(fontSize: 12.sp),
                                                          decoration: InputDecoration(
                                                            focusedBorder: InputBorder.none,
                                                            enabledBorder: InputBorder.none,
                                                            border: InputBorder.none,
                                                            contentPadding: EdgeInsets.zero,
                                                            errorBorder: InputBorder.none,
                                                            disabledBorder: InputBorder.none,
                                                            filled: true,
                                                            isDense: true,
                                                            fillColor: Colors.white,
                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                          ),
                                                          onChanged: _onChangeNVL,
                                                        ),
                                                      ),
                                                    )),
                                                Expanded(
                                                  flex: 3,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xffEEEEEE),
                                                        border: Border(
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child: Text(
                                                      "Storage Bin",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xFFFFFFFF),
                                                        border: Border(
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child: Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        textAlign: TextAlign.center,
                                                        controller: _storageBinControllerImport,
                                                        focusNode: _focusStorageBinControllerImport,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          focusedBorder: InputBorder.none,
                                                          enabledBorder: InputBorder.none,
                                                          border: InputBorder.none,
                                                          contentPadding: EdgeInsets.zero,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          filled: true,
                                                          isDense: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                        ),
                                                        onChanged: _onChangeNVLImport,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: 5.h),
                                          Row(
                                            children: [
                                              Expanded(
                                                flex: 5,
                                                child: _ListSuggestions(
                                                    lsDataGetStorageBn: _lsDataGetStorageBn,
                                                    setFilter: (DataGetStorageBin value) {
                                                      _setFilter(value);
                                                    },
                                                    isLoadingStorageBin: _isLoadingStorageBin),
                                              ),
                                              Expanded(
                                                flex: 5,
                                                child: _ListSuggestions(
                                                    lsDataGetStorageBn: _lsDataGetStorageBnImport,
                                                    setFilter: (DataGetStorageBin value) {
                                                      _setFilterImport(value);
                                                    },
                                                    isLoadingStorageBin: _isLoadingStorageBinImport),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "III. THÔNG TIN NVL CHUYỂN KHO",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Container(
                                            decoration: const BoxDecoration(),
                                            child: ElevatedButton.icon(
                                              style: ButtonStyle(
                                                shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                side: MaterialStateProperty.all(
                                                  BorderSide(
                                                    color: _selectedSloc != null ? const Color(0xff303F9F) : const Color(0xffe0e0e0),
                                                  ),
                                                ),
                                                backgroundColor: MaterialStateProperty.all(
                                                    _selectedSloc != null ? const Color(0xff303F9F) : const Color(0xffe0e0e0)),
                                              ),
                                              onPressed: _selectedSloc == null || _isLoadingRawMaterial == true
                                                  ? null
                                                  : () async {
                                                      if (!isTokenLive(widget.dateTimeOld)) {
                                                        Platform.isAndroid
                                                            ? showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                            : showCupertinoDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                        return;
                                                      }
                                                      FocusScope.of(context).unfocus();
                                                      final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      if ((data as GetBackDataQRCodePage).isScan == true) {
                                                        if (!mounted) return;
                                                        await _getDataRawMaterial(data.materialID.toString(), context);
                                                      }
                                                    },
                                              icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                              label: Text(
                                                _isLoadingRawMaterial == true ? "..." : "Quét mã",
                                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 5.h),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: generateWidgetsForData(),
                                          )
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "IV. SỐ LƯỢNG CHUYỂN KHO CHI TIẾT",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          kDebugMode
                                              ? Wrap(
                                                  alignment: WrapAlignment.start,
                                                  runAlignment: WrapAlignment.start,
                                                  spacing: 2.w,
                                                  children: [
                                                    ElevatedButton(
                                                      style: ElevatedButton.styleFrom(
                                                        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
                                                        minimumSize: const Size(0, 0), // Add this
                                                      ),
                                                      onPressed: () async {
                                                        // Gán kho xuất thành kho nhập để lấy được tồn kho sau khi đã chuyển kho (dùng để test)
                                                        setState(() {
                                                          _lsControllerSoLuongChuyen.clear();
                                                          _lsControllerSTTLine.clear();
                                                          _sumAmountEntered.clear();
                                                          _dataGetBarcodeReceive.clear();
                                                          _lsDataGetListSOWBSByBatchTranferWareHouse.clear();
                                                        });
                                                      },
                                                      child: const Text('clear', style: TextStyle(fontSize: 10)),
                                                    ),
                                                    ElevatedButton(
                                                      style: ElevatedButton.styleFrom(
                                                        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
                                                        minimumSize: const Size(0, 0), // Add this
                                                      ),
                                                      onPressed: () async {
                                                        // Gán kho xuất thành kho nhập để lấy được tồn kho sau khi đã chuyển kho (dùng để test)
                                                        setState(() {
                                                          if (_dataReservation!.storageLocation!.isNotEmpty) {
                                                            var khoXuatData = _getLsDataSlocAddress
                                                                .firstWhereOrNull((element) => element!.sloc == _dataReservation!.riStorageLocation);

                                                            if (khoXuatData != null) {
                                                              _selectedSloc = khoXuatData;
                                                              _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
                                                              _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
                                                              _lsDataGetStorageBn = [];
                                                              _errorStorageBin = false;
                                                            }
                                                          }
                                                        });
                                                      },
                                                      child: const Text('set Sloc', style: TextStyle(fontSize: 10)),
                                                    ),
                                                    // specify the width of the SizedBox
                                                    ...dummyBarcode
                                                        .where((item) => item['reservationNumber'] == _dataReservation!.reservationCode)
                                                        .map((item) {
                                                      return ElevatedButton(
                                                        style: ElevatedButton.styleFrom(
                                                          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
                                                          minimumSize: const Size(0, 0), // Add this
                                                        ),
                                                        onPressed: () async {
                                                          await _getDataRawMaterial(item['materialID'].toString(), context);
                                                        },
                                                        child: Text(item['batch'].toString(), style: const TextStyle(fontSize: 10)),
                                                      );
                                                    }).toList()
                                                  ],
                                                )
                                              : Container(),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.only(left: 3.w),
                                            child: SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Table(
                                                    border: TableBorder.all(width: 0.5.w),
                                                    columnWidths: columnWidthConfigChiTiet(),
                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                    children: <TableRow>[
                                                      TableRow(
                                                        decoration: const BoxDecoration(color: Color(0xff303F9F)),
                                                        children: headerTitleChiTiet,
                                                      ),
                                                    ],
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    children: List.generate(
                                                      (_lsDataGetListSOWBSByBatchTranferWareHouse).length,
                                                      (index) => Table(
                                                        border: TableBorder(
                                                          left: BorderSide(color: Colors.black, width: 0.5.w),
                                                          right: BorderSide(color: Colors.black, width: 0.5.w),
                                                          bottom: BorderSide(color: Colors.black, width: 0.5.w),
                                                          verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
                                                        ),
                                                        columnWidths: columnWidthConfigChiTiet(),
                                                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                        children: <TableRow>[
                                                          TableRow(
                                                            children: <Widget>[
                                                              // Mã NVL
                                                              _ColumnTableIV(
                                                                  text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].productCode ?? ""),
                                                              // Số lô
                                                              _ColumnTableIV(
                                                                  text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].batchNumber ?? ""),
                                                              // SO/SOLine
                                                              Container(
                                                                  margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  child: (_lsDataGetListSOWBSByBatchTranferWareHouse[index].so != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].so != "") &&
                                                                          (_lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine != "")
                                                                      ? Column(
                                                                          children: <Widget>[
                                                                            Text(
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].so ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                            Text(
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                          ],
                                                                        )
                                                                      : _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs != ""
                                                                          ? Center(child: Text("", style: TextStyle(fontSize: 12.sp)))
                                                                          : Center(child: Text("Tồn trơn", style: TextStyle(fontSize: 12.sp)))),
                                                              // WBS
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs ?? ""),
                                                              // LSX Đại trà
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].lsxdt ?? ""),
                                                              // SL chuyển
                                                              Padding(
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                child: Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  decoration: BoxDecoration(
                                                                      border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                      borderRadius: BorderRadius.circular(3.r)),
                                                                  child: TextFormField(
                                                                    enabled: _lsDataGetListSOWBSByBatchTranferWareHouse[index].quantity != 0.0,
                                                                    maxLines: null,
                                                                    textAlign: TextAlign.center,
                                                                    controller: _lsControllerSoLuongChuyen[index],
                                                                    focusNode: _lsFocusNode[index],
                                                                    style: TextStyle(fontSize: 12.sp),
                                                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                    inputFormatters: <TextInputFormatter>[
                                                                      FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d{0,3}')),
                                                                      CommaTextInputFormatter()
                                                                    ],
                                                                    decoration: InputDecoration(
                                                                      border: InputBorder.none,
                                                                      isDense: true,
                                                                      contentPadding: EdgeInsets.zero,
                                                                      errorBorder: InputBorder.none,
                                                                      disabledBorder: InputBorder.none,
                                                                      filled: true,
                                                                      fillColor: Colors.white,
                                                                      hintStyle: TextStyle(fontSize: 12.sp),
                                                                    ),
                                                                    onChanged: (value) {
                                                                      String productCode =
                                                                          _lsDataGetListSOWBSByBatchTranferWareHouse[index].productCode ?? "";

                                                                      String batchNumber =
                                                                          _lsDataGetListSOWBSByBatchTranferWareHouse[index].batchNumber.toString();

                                                                      List<double> getListDouble = ExportWareHouseFunction.getListDouble2(
                                                                        batchNumber,
                                                                        _lsDataGetListSOWBSByBatchTranferWareHouse,
                                                                        _lsControllerSoLuongChuyen,
                                                                        productCode,
                                                                      );

                                                                      double sum = ImportWareHouseFunction.amountListNumber(getListDouble);

                                                                      int getIndex = _sumAmountEntered.indexWhere((element) =>
                                                                          element.productCode == productCode && element.code == batchNumber);

                                                                      SumAmountExport newSumAmountExport = SumAmountExport(
                                                                          code: _sumAmountEntered[getIndex].code, sum: sum, productCode: productCode);

                                                                      setState(() {
                                                                        _sumAmountEntered.removeAt(getIndex);
                                                                        _sumAmountEntered.insert(getIndex, newSumAmountExport);

                                                                        // _autoSetControllerSTTLineOneLine(index);

                                                                        if (_lsControllerSoLuongChuyen
                                                                            .where((element) => element.text.isNotEmpty)
                                                                            .isNotEmpty) {
                                                                          if (_lsControllerSoLuongChuyen
                                                                              .where((element) =>
                                                                                  element.text.isNotEmpty && double.parse(element.text) <= 0.0)
                                                                              .isNotEmpty) {
                                                                            if (_errorQuantity != true) {
                                                                              _errorQuantity = true;
                                                                            }
                                                                            _message = "Số lượng chuyển không được nhỏ hơn 0.0";
                                                                          } else {
                                                                            if (_errorQuantity != false) {
                                                                              _errorQuantity = false;
                                                                            }
                                                                            _message = "";
                                                                          }
                                                                        } else {
                                                                          if (_errorQuantity != true) {
                                                                            _errorQuantity = true;
                                                                          }
                                                                          _message = "Vui lòng nhập số lượng chuyển!";
                                                                        }
                                                                      });
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                              // STT Lệnh cấp
                                                              Padding(
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                child: Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  decoration: BoxDecoration(
                                                                    border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                    borderRadius: BorderRadius.circular(3.r),
                                                                  ),
                                                                  child: TypeAheadField(
                                                                    suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                                                      constraints: BoxConstraints(
                                                                        minWidth: 150.w,
                                                                      ),
                                                                    ),
                                                                    textFieldConfiguration: TextFieldConfiguration(
                                                                      enabled: _lsDataGetListSOWBSByBatchTranferWareHouse[index].quantity != 0.0,
                                                                      decoration: InputDecoration(
                                                                        labelStyle: TextStyle(fontSize: 11.sp),
                                                                        contentPadding: EdgeInsets.zero,
                                                                        isDense: true,
                                                                        border: InputBorder.none,
                                                                        focusedBorder: InputBorder.none,
                                                                        enabledBorder: InputBorder.none,
                                                                        fillColor: Colors.white,
                                                                      ),
                                                                      controller: _lsControllerSTTLine[index],
                                                                      // focusNode: _lsFocusNode[index],
                                                                      textAlign: TextAlign.center,
                                                                      style: TextStyle(fontSize: 12.sp),
                                                                      onChanged: (value) {
                                                                        // ... (The rest of your onChanged logic here)
                                                                      },
                                                                    ),
                                                                    suggestionsCallback: (pattern) {
                                                                      // Return your list of suggestions here
                                                                      return filterListNo(
                                                                        _lsDataGetListSOWBSByBatchTranferWareHouse[index].productCode,
                                                                        _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs,
                                                                      );
                                                                    },
                                                                    itemBuilder: (context, suggestion) {
                                                                      return ListTile(
                                                                        dense: true,
                                                                        titleAlignment: ListTileTitleAlignment.center,
                                                                        // minVerticalPadding: 0,
                                                                        contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                        title: Text(
                                                                          suggestion,
                                                                          textAlign: TextAlign.center, // Center the text horizontally
                                                                          style: TextStyle(fontSize: 10.sp),
                                                                        ),
                                                                      );
                                                                    },
                                                                    onSuggestionSelected: (suggestion) {
                                                                      // Handle the suggestion selection here
                                                                      // print(suggestion);
                                                                      setControllerSTTLine(index, suggestion);
                                                                    },
                                                                    noItemsFoundBuilder: (value) {
                                                                      return Padding(
                                                                          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 2.w),
                                                                          child: Text(" ", style: TextStyle(fontSize: 11.sp)));
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                              // SL tồn
                                                              _ColumnTableIV(
                                                                  text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].quantity == null
                                                                      ? ""
                                                                      : _lsDataGetListSOWBSByBatchTranferWareHouse[index]
                                                                          .quantity!
                                                                          .toStringAsFixed(3)),
                                                              // ĐVT
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].unit ?? ""),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: _errorQuantity == true ? 10.h : 0),
                                          ContainerError.widgetError(_errorQuantity, _message)
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                                        width: double.infinity,
                                        decoration: const BoxDecoration(),
                                        child: ElevatedButton(
                                          style: ButtonStyle(
                                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                            side: MaterialStateProperty.all(
                                              BorderSide(
                                                  color: _lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty
                                                      ? const Color(0xff303F9F)
                                                      : const Color(0xffe0e0e0)),
                                            ),
                                            backgroundColor: MaterialStateProperty.all(_lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty
                                                ? const Color(0xff303F9F)
                                                : const Color(0xffe0e0e0)),
                                          ),
                                          onPressed: _lsDataGetListSOWBSByBatchTranferWareHouse.isEmpty
                                              ? null
                                              : () {
                                                  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                  if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                    Platform.isAndroid
                                                        ? showDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                        : showCupertinoDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                  } else {
                                                    _submitData(context);
                                                  }
                                                },
                                          child: Container(
                                            margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                            child: Text(
                                              "Chuyển kho",
                                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 250.h,
                                    ),
                                  ],
                                ),
                              ),
                  )));
  }
}

class _ColumnTableIV extends StatelessWidget {
  final String text;
  const _ColumnTableIV({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        child: Text(text, style: TextStyle(fontSize: 12.sp), textAlign: TextAlign.center));
  }
}

class _ColumnTableItem extends StatelessWidget {
  final String text;
  final int? fontSize;
  final TextAlign? textAlign;
  final FontWeight? fontWeight; // Added this line

  const _ColumnTableItem({
    Key? key,
    required this.text,
    this.fontSize,
    this.textAlign, // Default is center
    this.fontWeight, // Default is center
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize != null ? fontSize!.sp : 12.sp,
          fontWeight: fontWeight, // Added this line
        ),
        textAlign: textAlign ?? TextAlign.center, // Use the provided textAlign
      ),
    );
  }
}

class _TitleTableItem extends StatelessWidget {
  final String text;
  final int? fontSize;
  final TextAlign? textAlign;

  const _TitleTableItem({
    Key? key,
    required this.text,
    this.fontSize,
    this.textAlign, // Default is center
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
      child: Text(
        text,
        style: TextStyle(fontSize: fontSize != null ? fontSize!.sp : 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: textAlign ?? TextAlign.center, // Use the provided textAlign
      ),
    );
  }
}

class _TitleTableIV extends StatelessWidget {
  final String text;
  const _TitleTableIV({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _TitleExportWareHouse extends StatelessWidget {
  const _TitleExportWareHouse({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          "CHUYỂN KHO NVL CÓ LỆNH CẤP",
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class _ListSuggestions extends StatelessWidget {
  final ValueChanged<DataGetStorageBin> setFilter;
  final List<DataGetStorageBin> lsDataGetStorageBn;
  final bool isLoadingStorageBin;
  const _ListSuggestions({Key? key, required this.setFilter, required this.lsDataGetStorageBn, required this.isLoadingStorageBin}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: lsDataGetStorageBn.isNotEmpty || isLoadingStorageBin == true,
      child: SizedBox(
        height: 75.h,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 7,
                offset: const Offset(0, 1), // changes position of shadow
              ),
            ],
          ),
          child: isLoadingStorageBin == true
              ? const Center(child: CircularProgressIndicator())
              : Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Colors.white),
                  child: SingleChildScrollView(
                    child: Column(
                      children: List.generate(lsDataGetStorageBn.length, (index) {
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 5.h),
                          child: InkWell(
                            onTap: () {
                              setFilter(lsDataGetStorageBn[index]);
                            },
                            child: Text(
                              lsDataGetStorageBn[index].value.toString(),
                              style: TextStyle(fontSize: 13.sp, color: Colors.black),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}

class _HeaderExportWareHouse extends StatelessWidget {
  final String title;
  const _HeaderExportWareHouse({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

class _PlantExportImport extends StatelessWidget {
  final DataReservation? dataReservation;
  const _PlantExportImport({Key? key, required this.dataReservation}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Nhà máy xuất:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation == null ? "" : dataReservation!.plant ?? "",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Nhà máy nhập:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation == null ? "" : dataReservation!.riPlant ?? " ",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _WareHouseExportImport extends StatelessWidget {
  final DataReservation? dataReservation;
  const _WareHouseExportImport({Key? key, required this.dataReservation}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Kho xuất:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation!.storageLocation ?? "",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Kho nhập:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation!.riStorageLocation ?? " ",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
