import 'dart:convert';
import 'package:ttf/model/HangMucKiemTraVm.dart';
import 'package:ttf/model/qualityControlApi.dart';

class CongDoanInfoVm {
  String? workShopCode;
  String? workShopName;
  QualityControl? qualityControl;
  List<SalesEmployeeWorkshopVm>? salesEmployeeWorkshopList;
  List<DropdownItemList>? caNhanGayLoiList;
  String? quanDocCode;
  String? quanDocName;
  String? toTruongCode;
  String? toTruongName;
  String? qaqcCode;
  String? qaqcName;
  String? kcsCode;
  String? kcsName;
  String? caNhanGayLoiCode;
  String? caNhanGayLoiName;
  List<SalesEmployeeWorkshopVm>? quanDocList;
  List<SalesEmployeeWorkshopVm>? toTruongList;
  List<SalesEmployeeWorkshopVm>? qaqcList;
  List<SalesEmployeeWorkshopVm>? kcsList;

  QualityControlDetail? qualityControlDetail;
  List<Error>? error;
  List<QualityControlInformation>? qualityControlInformation;
  List<HangMucKiemTraVm>? hangMucKiemTraMasterData;

  String? departmentCode;
  String? departmentName;

  String? saleOrgCode;
  String? storeName;

  CongDoanInfoVm({
    this.workShopCode,
    this.workShopName,
    this.qualityControl,
    this.salesEmployeeWorkshopList,
    this.caNhanGayLoiList,
    this.quanDocCode,
    this.quanDocName,
    this.toTruongCode,
    this.toTruongName,
    this.qaqcCode,
    this.qaqcName,
    this.kcsCode,
    this.kcsName,
    this.caNhanGayLoiCode,
    this.caNhanGayLoiName,
    this.quanDocList,
    this.toTruongList,
    this.qaqcList,
    this.kcsList,
    this.qualityControlDetail,
    this.error,
    this.qualityControlInformation,
    this.departmentCode,
    this.departmentName,
    this.saleOrgCode,
    this.storeName,
    this.hangMucKiemTraMasterData,
  });

  CongDoanInfoVm.fromJson(Map<String, dynamic> json) {
    workShopCode = json['workShopCode'];
    workShopName = json['workShopName'];
    qualityControl = json['qualityControl'] != null ? QualityControl.fromJson(json['qualityControl']) : null;
    if (json['salesEmployeeWorkshopList'] != null) {
      salesEmployeeWorkshopList = (json['salesEmployeeWorkshopList'] as List).map((i) => SalesEmployeeWorkshopVm.fromJson(i)).toList();
    }
    if (json['caNhanGayLoiList'] != null) {
      caNhanGayLoiList = (json['caNhanGayLoiList'] as List).map((i) => DropdownItemList.fromJson(i)).toList();
    }
    quanDocCode = json['quanDocCode'];
    quanDocName = json['quanDocName'];
    toTruongCode = json['toTruongCode'];
    toTruongName = json['toTruongName'];
    qaqcCode = json['qaqcCode'];
    qaqcName = json['qaqcName'];
    kcsCode = json['kcsCode'];
    kcsName = json['kcsName'];
    caNhanGayLoiCode = json['caNhanGayLoiCode'];
    caNhanGayLoiName = json['caNhanGayLoiName'];
    if (json['quanDocList'] != null) {
      quanDocList = <SalesEmployeeWorkshopVm>[];
      json['quanDocList'].forEach((v) {
        quanDocList!.add(SalesEmployeeWorkshopVm.fromJson(v));
      });
    }

    if (json['toTruongList'] != null) {
      toTruongList = <SalesEmployeeWorkshopVm>[];
      json['toTruongList'].forEach((v) {
        toTruongList!.add(SalesEmployeeWorkshopVm.fromJson(v));
      });
    }

    if (json['qaqcList'] != null) {
      qaqcList = <SalesEmployeeWorkshopVm>[];
      json['qaqcList'].forEach((v) {
        qaqcList!.add(SalesEmployeeWorkshopVm.fromJson(v));
      });
    }

    if (json['kcsList'] != null) {
      kcsList = <SalesEmployeeWorkshopVm>[];
      json['kcsList'].forEach((v) {
        kcsList!.add(SalesEmployeeWorkshopVm.fromJson(v));
      });
    }

    if (json['error'] != null) {
      error = <Error>[];
      json['error'].forEach((v) {
        error!.add(Error.fromJson(v));
      });
    }

    if (json['qualityControlInformation'] != null) {
      qualityControlInformation = <QualityControlInformation>[];
      json['qualityControlInformation'].forEach((v) {
        qualityControlInformation!.add(QualityControlInformation.fromJson(v));
      });
    }

    if (json['hangMucKiemTraMasterData'] != null) {
      hangMucKiemTraMasterData = <HangMucKiemTraVm>[];
      json['hangMucKiemTraMasterData'].forEach((v) {
        hangMucKiemTraMasterData!.add(HangMucKiemTraVm.fromJson(v));
      });
    }

    qualityControlDetail = json['qualityControlDetail'] != null ? QualityControlDetail.fromJson(json['qualityControlDetail']) : null;

    departmentCode = json['departmentCode'];
    departmentName = json['departmentName'];

    saleOrgCode = json['saleOrgCode'];
    storeName = json['storeName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['workShopCode'] = workShopCode;
    data['workShopName'] = workShopName;
    data['qualityControl'] = qualityControl?.toJson();
    data['salesEmployeeWorkshopList'] = salesEmployeeWorkshopList?.map((item) => item.toJson()).toList();
    data['caNhanGayLoiList'] = caNhanGayLoiList?.map((item) => item.toJson()).toList();
    data['quanDocCode'] = quanDocCode;
    data['quanDocName'] = quanDocName;
    data['toTruongCode'] = toTruongCode;
    data['toTruongName'] = toTruongName;
    data['qaqcCode'] = qaqcCode;
    data['qaqcName'] = qaqcName;
    data['kcsCode'] = kcsCode;
    data['kcsName'] = kcsName;
    data['caNhanGayLoiCode'] = caNhanGayLoiCode;
    data['caNhanGayLoiName'] = caNhanGayLoiName;
    if (quanDocList != null) {
      data['quanDocList'] = quanDocList!.map((item) => item.toJson()).toList();
    }

    if (toTruongList != null) {
      data['toTruongList'] = toTruongList!.map((item) => item.toJson()).toList();
    }

    if (qaqcList != null) {
      data['qaqcList'] = qaqcList!.map((item) => item.toJson()).toList();
    }

    if (kcsList != null) {
      data['kcsList'] = kcsList!.map((item) => item.toJson()).toList();
    }

    if (error != null) {
      data['error'] = error!.map((item) => item.toJson()).toList();
    }

    if (qualityControlInformation != null) {
      data['qualityControlInformation'] = qualityControlInformation!.map((item) => item.toJson()).toList();
    }

    if (hangMucKiemTraMasterData != null) {
      data['hangMucKiemTraMasterData'] = hangMucKiemTraMasterData!.map((item) => item.toJson()).toList();
    }

    if (qualityControlDetail != null) {
      data['qualityControlDetail'] = qualityControlDetail!.toJson();
    }

    data['departmentCode'] = departmentCode;
    data['departmentName'] = departmentName;

    data['saleOrgCode'] = saleOrgCode;
    data['storeName'] = storeName;

    return data;
  }
}

class SalesEmployeeWorkshopVm {
  String? salesEmployeeCode;
  String? salesEmployeeName;
  String? departmentCode;
  String? departmentName;
  String? workShopCode;
  String? workShopName;
  int? levelCode;

  SalesEmployeeWorkshopVm({
    this.salesEmployeeCode,
    this.salesEmployeeName,
    this.departmentCode,
    this.departmentName,
    this.workShopCode,
    this.workShopName,
    this.levelCode,
  });

  factory SalesEmployeeWorkshopVm.fromJson(Map<String, dynamic> json) {
    return SalesEmployeeWorkshopVm(
      salesEmployeeCode: json['salesEmployeeCode'],
      salesEmployeeName: json['salesEmployeeName'],
      departmentCode: json['departmentCode'],
      departmentName: json['departmentName'],
      workShopCode: json['workShopCode'],
      workShopName: json['workShopName'],
      levelCode: json['levelCode'] != null ? int.parse(json['levelCode']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'salesEmployeeCode': salesEmployeeCode,
      'salesEmployeeName': salesEmployeeName,
      'departmentCode': departmentCode,
      'departmentName': departmentName,
      'workShopCode': workShopCode,
      'workShopName': workShopName,
      'levelCode': levelCode?.toString(),
    };
  }
}
