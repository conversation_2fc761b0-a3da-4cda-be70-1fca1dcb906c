﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class NotificationController : ControllerBaseAPI
    {
        public IConfiguration _configuration { get; }
        public NotificationController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpGet("GetAllById")]
        public async Task<ActionResult> GetAllById(string accountId, int pageIndex = 0, int pageSize = 20)
        {
            var ret = new List<PushNotificationModel>();

            var idGuid = Guid.Parse(accountId);
            var accountModel = _context.AccountModel.FirstOrDefault(a => a.AccountId == idGuid);

            if (accountModel != null)
            {
                var userName = accountModel.UserName;
                //ret = _context.PushNotificationModel.Where(n => n.ExternalId == userName)
                //                                    .OrderByDescending(n => n.CreateTime)
                //                                    .ToList();

                ret = _context.PushNotificationModel.Where(n => n.ExternalId == userName)
                                                    .OrderByDescending(n => n.CreateTime)
                                                    .Skip(pageIndex * pageSize)
                                                    .Take(pageSize)
                                                    .ToList();
            };

            //return ret;
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = ret,
            });
        }

        [HttpGet("GetByEmployeeCode")]
        public async Task<ActionResult> GetByEmployeeCode(string employeeCode)
        {
            var ret = new List<PushNotificationModel>();

            var accountModel = _context.AccountModel.FirstOrDefault(a => a.EmployeeCode == employeeCode);

            if (accountModel != null)
            {
                var userName = accountModel.UserName;
                ret = _context.PushNotificationModel.Where(n => n.ExternalId == userName).OrderByDescending(n => n.CreateTime).ToList();
            };

            //return ret;
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = ret,
            });
        }

        public class EmployeeViewModel
        {
            public Guid AccountId { get; set; }
            public string UserName { get; set; }
            public string FullName { get; set; }
            public string EmployeeCode { get; set; }
            public string SaleOrgCode { get; set; }
            public string StoreName { get; set; }
        }

        [HttpGet("GetAllEmployeeByPlant")]
        public async Task<ActionResult> GetAllEmployeeByPlant(string plant)
        {
            var ret = new List<EmployeeViewModel>();

            var query = from am in _context.AccountModel
                        from sm in am.Store
                        where
                            am.EmployeeCode != null // if EmployeeCode is mean that is not employee, could be admin, vendor
                            && !string.IsNullOrEmpty(am.FullName) && sm.SaleOrgCode == plant
                        select new EmployeeViewModel
                        {
                            AccountId = am.AccountId,
                            UserName = am.UserName,
                            FullName = am.FullName,
                            EmployeeCode = am.EmployeeCode,
                            SaleOrgCode = sm.SaleOrgCode,
                            StoreName = sm.StoreName
                        };


            ret = query.ToList();

            //return ret;
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = ret,
            });
        }


        public class ForwardInputViewModel
        {
            public Guid ForwardById { get; set; }
            public Guid NotificationId { get; set; }
            public List<EmployeeViewModel> ForwardList { get; set; }
        }

        [HttpPost("ForwardNotification")]
        public async Task<ActionResult> PostForwardNotification([FromBody] ForwardInputViewModel input)
        {

            var notification = _context.PushNotificationModel.FirstOrDefault(n => n.Id == input.NotificationId);

            foreach (var item in input.ForwardList)
            {

                var accountModel = _context.AccountModel.FirstOrDefault(n => n.EmployeeCode == item.EmployeeCode);

                if (accountModel != null)
                {
                    var newNotification = new PushNotificationModel
                    {
                        Id = Guid.NewGuid(),
                        Title = notification.Title,
                        Message = notification.Message,
                        NotificationType = "Forward",
                        TargetUser = "",
                        ExternalId = accountModel.UserName, // tam.nc tien.lx
                        SentAt = null,
                        IsForwarded = false,
                        Status = 0,
                        CreatedBy = input.ForwardById.ToString(),
                        CreateTime = DateTime.Now,
                        UpdatedAt = null,
                        ForwardBy = input.ForwardById.ToString(),
                        NotificationIdForward = input.NotificationId
                    };

                    notification.IsForwarded = true;
                    notification.UpdatedAt = DateTime.Now;

                    List<string> usernameList = input.ForwardList.Select(e => e.UserName).ToList();
                    string usernameString = string.Join(", ", usernameList);

                    notification.ForwardedUsers = usernameString;

                    _context.PushNotificationModel.Add(newNotification);

                    _context.SaveChanges();
                }
            }

            //return ret;
            //return Ok(new ApiResponse
            //{
            //    Code = 200,
            //    IsSuccess = true,
            //    Data = notification,
            //});
            return Ok("Notification forwarded successfully");
        }


    }
}
