import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:ttf/route/route.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:ttf/service/navigatorService.dart';
import 'package:ttf/utils/appConfig.dart';
import 'Storage/storageSecureStorage.dart';
import 'Storage/storageSharedPreferences.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

Future<void> main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await StorageSharedPreferences.init();
  if (Platform.isIOS) {
    if (StorageSharedPreferences.getBool('first_run') ?? true) {
      await SecureStorage.removeAll(null);
      StorageSharedPreferences.setBool('first_run', false);
    }
  }
  await AppConfig.initialize();
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    _initPushNotification();
  }

  _initPushNotification() {
    // FirebaseMessaging.instance.requestPermission(alert: true, badge: true, sound: true);

    // FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    //   print('Message received in foreground: ${message.data}');
    //   if (message.notification != null) {
    //     print('Message notification: ${message.notification}');
    //   }
    // });

    // FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    //   print('Message opened from a tap: ${message.data}');
    // });

    // FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
    //   if (message != null) {
    //     print('Message opened from terminated state: ${message.data}');
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(360, 690),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            title: 'TTF MES 3',
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            theme: ThemeData(
              dividerTheme: const DividerThemeData(
                color: Colors.grey,
                thickness: 0.5,
              ),
              colorScheme: ColorScheme.light().copyWith(
                primary: Colors.blue,
                // onPrimary: Colors.red,
                primaryContainer: Colors.blue,
                secondary: Colors.black,
                onSurface: Colors.black,
                onBackground: Colors.black,
                surfaceTint: Colors.white,
                // outlineVariant: Colors.grey // dividerColor
              ),
              pageTransitionsTheme: const PageTransitionsTheme(builders: {
                TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
              }),
              iconTheme: const IconThemeData(
                color: Colors.white, // set the color
              ),
              buttonTheme: const ButtonThemeData(
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 15),
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ButtonStyle(
                  // backgroundColor: MaterialStateProperty.all(Colors.red),
                  // foregroundColor: MaterialStateProperty.all(Colors.white),
                  padding: MaterialStateProperty.all(
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                  ),
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                ),
              ),
              primarySwatch: Colors.blue,
            ),
            initialRoute: '/',
            onGenerateRoute: RouteGenerator.generateRoute,
            debugShowCheckedModeBanner: false,
            // useInheritedMediaQuery: true,
            supportedLocales: const [
              Locale('en', ''),
              Locale('vi', ''),
            ],
            navigatorKey: NavigationService.instance.navigationKey,
          );
        });
  }
}
