*&---------------------------------------------------------------------*
*& Include          ZPP_IN_BCDMTP_TOP
*&---------------------------------------------------------------------*
*-----------------------------------------------------------------------
* Tables Definition
*-----------------------------------------------------------------------
report zpp_pg_bcdmtp message-id zmc_ps.
tables: mast, mara, stpo, vbap.

*-----------------------------------------------------------------------
* Types Definition
*-----------------------------------------------------------------------
types:
  begin of gty_main,
    matnr  type mast-matnr,
    stlan  type mast-stlan,
    stlal  type mast-stlal,
    stlnr  type mast-stlnr,
    werks  type mast-werks,
    bum    type mara-meins,
    datuv  type stko-datuv,
    andat  type stko-andat,
    annam  type stko-annam,
    aedat  type stko-aedat,
    aenam  type stko-aenam,
    bmein  type stko-bmein,
    bmeng  type stko-bmeng,
    stlst  type stko-stlst,
    idnrk  type stpo-idnrk,
    posnr  type stpo-posnr,
    meins  type stpo-meins,
    menge  type stpo-menge,
    fmeng  type stpo-fmeng,
    ausch  type stpo-ausch,
    stkkz  type stpo-stkkz,
    potx1  type stpo-potx1,
    potx2  type stpo-potx2,
    z_duan type mara-z_duan,
    vbeln  type vbap-vbeln,
    datec  type stpo-aedat,
    vgknt  type stpo-vgknt,
    stlkn  type stpo-stlkn,
    lkenz  type stas-lkenz,     "KhanDV update 04.05.2021
    dvdat  type stas-dvdat,     "end
    stktx  type stko-stktx,
  end of gty_main,
  "Price control indicator
  begin of gty_mbew,
    werks type werks_d,
    matnr type mbew-matnr,
    vprsv type mbew-vprsv,
    stprs type mbew-stprs,
    verpr type mbew-verpr,
    meins type mara-meins,
  end of gty_mbew.

types:
  begin of gty_stko,
    stlty    type stlty,
    stlnr    type stnum,
    objectid type cdobjectv,
  end of gty_stko,
  gty_t_stko type standard table of gty_stko.

types:
  gty_cdhdr   type cdhdr,
  gty_t_cdhdr type standard table of gty_cdhdr.


*-----------------------------------------------------------------------
* Work Area Definition
*-----------------------------------------------------------------------
data:
  gs_layout   type  lvc_s_layo.

*-----------------------------------------------------------------------
* Internal tables Definition
*-----------------------------------------------------------------------
data:
  gt_main     type table of gty_main,
  gs_main     type gty_main,
  gt_makt     type table of makt,
  gt_mbew     type table of gty_mbew,
  gt_alv      type table of zpp_st_bcdmtp,
  gt_stko     type gty_t_stko,
  gt_stko2    type table of stko,
  gt_stko4    type table of stko,
  gs_stko2    type  stko,
  gs_stko4    type  stko,
  gt_stko3    type table of stko,
  gs_stko3    type  stko,
  gt_cdhdr    type gty_t_cdhdr,
  gt_fieldcat type lvc_t_fcat.  "ALV field catalog table

* Range table
data:
  gt_r_matnr  type range of matnr.

************************************************************************
* Selection screen
************************************************************************
selection-screen begin of block b01 with frame.
selection-screen begin of line.
selection-screen position 5.
parameters p_ttda type c radiobutton group gr1 modif id gr1 user-command rad default 'X'.
selection-screen comment 7(30) text-c01 for field p_ttda.
parameters p_xk type c radiobutton group gr1 modif id gr1.
selection-screen comment 40(20) text-c02 for field p_xk.
selection-screen end of line.

selection-screen skip.
select-options:
  s_duan for mara-z_duan matchcode object zsh_zduan,
  s_vbeln for vbap-vbeln modif id e01.

select-options:
  s_stlan    for mast-stlan  obligatory default '1'.
select-options:
  s_werks    for mast-werks obligatory default '1000'.   "Plan
select-options:
  s_matnr    for mast-matnr matchcode object mat1,
  s_mtart    for mara-mtart,
  s_matkl    for mara-matkl,
  s_stlal    for mast-stlal,
  s_idnrk    for stpo-idnrk.
selection-screen skip.
select-options:
  s_andat for mast-andat,
  s_datuv for stpo-datuv.
*PARAMETERS:
*  CB_MTBOM   TYPE CHAR1 NO-DISPLAY .   "Check Multi-level BOM

selection-screen end of block b01.