﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MemberOfExternalProfileTargetGroupModel", Schema = "Marketing")]
    public partial class MemberOfExternalProfileTargetGroupModel
    {
        [Key]
        public Guid ExternalProfileTargetGroupId { get; set; }
        public Guid? TargetGroupId { get; set; }
        [StringLength(500)]
        public string FullName { get; set; }
        [StringLength(500)]
        public string Email { get; set; }
        [StringLength(500)]
        public string Phone { get; set; }

        [ForeignKey("TargetGroupId")]
        [InverseProperty("MemberOfExternalProfileTargetGroupModel")]
        public virtual TargetGroupModel TargetGroup { get; set; }
    }
}