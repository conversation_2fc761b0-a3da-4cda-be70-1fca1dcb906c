# Code Guidelines

Welcome to the project! To ensure consistency, maintainability, and scalability, please adhere to the following code guidelines when developing new features or modifying existing ones.

## Table of Contents

1. [Project Structure](#project-structure)
2. [Naming Conventions](#naming-conventions)
3. [File Organization](#file-organization)
4. [Coding Standards](#coding-standards)
    - [Dar<PERSON> & Flutter](#dart--flutter)
    - [SAP ABAP](#sap-abap)
5. [State Management](#state-management)
6. [Error Handling](#error-handling)
    - [Dar<PERSON> & Flutter](#dart--flutter-error-handling)
    - [SAP ABAP](#sap-abap-error-handling)
7. [UI Components](#ui-components)
8. [Network Connectivity](#network-connectivity)
9. [Extensions](#extensions)
10. [Version Control](#version-control)
11. [Adding a New Feature](#adding-a-new-feature)

---

## Project Structure

Organize your project into clearly defined directories to separate concerns and enhance readability.

```
lib/
├── controllers/
│   ├── DowntimeController.dart
│   └── MaintenanceOrderController.dart
├── models/
│   ├── DowntimeModel.dart
│   ├── MaintenanceOrderModel.dart
│   └── UserModel.dart
├── pages/
│   ├── Downtime/
│   │   ├── DowntimeList.dart
│   │   ├── DowntimeDetail.dart
│   │   └── element/
│   │       └── FilterListDowntime.dart
│   ├── MaintenanceOrder/
│   │   ├── MaintenanceOrderList.dart
│   │   ├── MaintenanceOrderDetail.dart
│   │   └── element/
│   │       └── FilterListMaintenanceOrder.dart
│   └── common/
│       └── LostConnect.dart
├── repositories/
│   ├── DowntimeRepository.dart
│   └── MaintenanceOrderRepository.dart
├── widgets/
│   └── AutoCompleteField.dart
├── utils/
│   └── Extensions.dart
└── main.dart

```

### Description

- **controllers/**: Contains controller classes handling business logic.
- **models/**: Defines the data structures used across the application.
- **pages/**: Contains UI pages segmented by feature (e.g., Downtime, MaintenanceOrder).
  - **element/**: UI components specific to a feature's page.
- **repositories/**: Manages data operations such as API calls and database interactions.
- **widgets/**: Reusable UI components used across multiple pages.
- **utils/**: Utility classes and extension methods.
- **main.dart**: The entry point of the application.

---

## Naming Conventions

Consistency in naming enhances code readability and maintainability.

### Files

- **PascalCase** for class and file names.
  - Example: `DowntimeList.dart`, `MaintenanceOrderController.dart`
- Use prefix/suffix to indicate the file type.
  - Controllers: `*Controller.dart`
  - Repositories: `*Repository.dart`
  - Models: `*Model.dart`
  - Pages: `*List.dart`, `*Detail.dart`
  - Filters: `FilterList*.dart`

### Classes

- **PascalCase** for class names.
  - Example: `DowntimeList`, `MaintenanceOrderController`

### Methods and Variables

- **camelCase** for method and variable names.
  - Example: `initializeData()`, `_isLoading`

### Constants

- **ALL_CAPS_WITH_UNDERSCORES** for constant values.
  - Example: `API_BASE_URL`

### Extensions

- Use meaningful names indicating the extended type.
  - Example: `TapExtension` for extending Widget.

---

## File Organization

Organize code within files logically to separate concerns and promote single responsibility.

### Pages

Each page should handle a specific feature and contain corresponding UI and logic.

- **List Pages**: Display lists of items.
  - Example: `DowntimeList.dart`, `MaintenanceOrderList.dart`
- **Detail Pages**: Display detailed information of a selected item.
  - Example: `DowntimeDetail.dart`, `MaintenanceOrderDetail.dart`

### Controllers

Handle the business logic and interact with repositories.

- **Naming**: `[Feature]Controller.dart`
  - Example: `DowntimeController.dart`

### Repositories

Manage data operations such as fetching from APIs or databases.

- **Naming**: `[Feature]Repository.dart`
  - Example: `DowntimeRepository.dart`

### Filters

Encapsulate filter UI and logic for list views.

- **Naming**: `FilterList[Feature].dart`
  - Example: `FilterListDowntime.dart`, `FilterListMaintenanceOrder.dart`

### Widgets

Reusable UI components that can be shared across different parts of the app.

- **Example**: `AutoCompleteField.dart`

### Extensions

Extend existing classes with additional functionality.

- **Example**: `Extensions.dart` containing `TapExtension`

---

## Coding Standards

Maintain high-quality code by following these standards.

### Dart & Flutter

- **Best Practices**
  - Follow [Effective Dart](https://dart.dev/guides/language/effective-dart) guidelines.
  - Use `const` constructors and widgets where possible.
  - Leverage Flutter's widget tree efficiently to optimize performance.

- **Formatting**
  - Use `flutter format` or your IDE's Dart formatter to maintain consistent code style.
  - Indent using 2 spaces.
  - Limit lines to 80 characters for readability.

- **Documentation**
  - Document public classes and methods using DartDoc comments.
    - Example:

      ```dart
      /// Fetches the downtime list from the repository.
      Future<void> _initializeData() async { ... }
      ```
  
  - Provide clear and concise comments where necessary.

- **Naming**
  - Use descriptive names that convey the purpose.
  - Avoid abbreviations unless widely recognized.

- **Error Handling**
  - Use try-catch blocks to handle potential exceptions.
  - Provide meaningful error messages for debugging.
    - Example:

      ```dart
      try {
        // Code that might throw an exception
      } on SocketException {
        // Handle network error
      } catch (e) {
        // Handle other errors
      }
      ```

### SAP ABAP

- **Naming Conventions**
  - **Function Modules and Methods**: Use prefixes that indicate functionality and follow SAP naming standards.
    - Example: `ZFM_MES_M2_LIST`, `ZFM_MES_M2_CREATE`, `ZFM_MES_M2_TECO`
  - **Variables**: Use meaningful names with prefixes indicating their type.
    - Example: `lt_orders` for internal tables, `ls_order` for line structures, `lv_offset` for variables.

- **File Naming**
  - Use transaction codes or descriptive names for function modules.
    - Example: `ZFM_MES_M2_LIST.abap`

- **Modularity**
  - Divide code into manageable sections using clear sections and comments.
  - Encapsulate functionality within function modules to enhance reusability.

- **Documentation**
  - Use block comments to describe the purpose and interface of function modules.
    - Example:
      ```abap
      FUNCTION ZFM_MES_M2_LIST.
      *"----------------------------------------------------------------------
      *"*"Local Interface:
      *"  IMPORTING
      *"     VALUE(IV_COMPANY_CODE) TYPE  WERKS_D
      *"     ...
      *"----------------------------------------------------------------------
      ```

- **Error Handling**
  - Use TRY...CATCH blocks to handle exceptions.
  - Provide meaningful error messages and ensure proper transaction handling (COMMIT/ROLLBACK).
  - Example:
    ```abap
    TRY.
        " Code that might throw an exception
      CATCH cx_root INTO DATA(lx_root).
        ev_success = ''.
        ev_message = lx_root->get_text( ).
    ENDTRY.
    ```

- **Performance Considerations**
  - Optimize SELECT statements by selecting only necessary fields.
  - Use proper indexing and avoid unnecessary data retrieval.
  - Implement pagination effectively to handle large data sets.

- **Consistency**
  - Follow a consistent coding style for indentation, spacing, and alignment.
  - Use uppercase for ABAP keywords and lowercase for variable names.

- **Security**
  - Validate all inputs to prevent injection attacks.
  - Handle sensitive data appropriately.

---

## State Management

Manage state effectively to ensure a responsive and maintainable application.

### Approach

- Utilize Flutter's `StatefulWidget` for local state management.
- For complex state management, consider using providers, BLoC, or other state management solutions as the project scales.

### Example

In `DowntimeList.dart`:

```dart
class DowntimeList extends StatefulWidget {
  // ...
}

class _DowntimeListState extends State<DowntimeList> {
  bool _isLoading = true;
  // Other state variables

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  // Methods to manage state
}
```

---

## Error Handling

Ensure robust error handling to enhance user experience and simplify debugging.

### Dart & Flutter Error Handling

#### Network Errors

- Detect network connectivity using packages like `connectivity_plus`.
- Provide user-friendly messages when offline.
- Example in `DowntimeList.dart`:

  ```dart
  if (_isNotWifi) {
    return LostConnect(checkConnect: () => _loadData());
  }
  ```

#### General Errors

- Catch exceptions and update UI accordingly.
- Log errors for debugging purposes.
- Example:

  ```dart
  catch (error) {
    debugPrint("Error in _initializeData: $error");
    setState(() {
      _isError = true;
    });
  }
  ```

### SAP ABAP Error Handling

#### Network Errors

- Not typically applicable as ABAP runs on the SAP server side, but handle exceptions related to external system calls.

#### General Errors

- Use TRY...CATCH blocks to handle exceptions.
- Provide meaningful error messages and ensure proper transaction handling.
- Example:

  ```abap
  TRY.
      " Code that might throw an exception
    CATCH cx_root INTO DATA(lx_root).
      ev_success = ''.
      ev_message = lx_root->get_text( ).
  ENDTRY.
  ```

- Ensure that transactions are properly managed using `BAPI_TRANSACTION_COMMIT` and `BAPI_TRANSACTION_ROLLBACK` based on success or failure.

---

## UI Components

Design consistent and reusable UI components to maintain a cohesive look and feel.

### Pages

- **List Pages**: Display items in a `ListView.builder` with `Card` widgets.
- **Detail Pages**: Show detailed information with appropriate widgets like `Text`, `Icons`, etc.

### Widgets

- **Reusable Components**: Create widgets like `AutoCompleteField.dart` for repeated use.
- **Extensions**: Utilize extension methods for common widget behaviors.
  - Example: `TapExtension` to add tap functionality.

    ```dart
    extension TapExtension on Widget {
      Widget onTap(VoidCallback onTap) {
        return InkWell(
          onTap: onTap,
          child: this,
        );
      }
    }
    ```

### Styling

- Use `flutter_screenutil` for responsive sizing.
- Maintain a consistent color scheme and typography.
- Example:

  ```dart
  Text(
    'Downtime',
    style: TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14.sp,
      color: Colors.white,
    ),
  )
  ```

---

## Network Connectivity

Handle network connectivity gracefully to ensure a seamless user experience.

### Checking Connectivity

- Use the `connectivity_plus` package to monitor network status.
- Implement checks before performing network operations.

### Handling Connectivity Changes

- Show appropriate UI when offline, such as a `LostConnect` widget.
- Allow users to retry actions once connectivity is restored.

### Example

In `DowntimeList.dart`:

```dart
Future<void> _checkConnectNetwork() async {
  _result = await Connectivity().checkConnectivity();
}

Widget _buildBody() {
  if (_isNotWifi) {
    return LostConnect(checkConnect: () => _loadData());
  }
  // Other UI states
}
```

---

## Extensions

Leverage Dart extensions to add functionality to existing classes without modifying their source code.

### Example: Tap Extension

Add tap functionality to any widget.

```dart
extension TapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }
}
```

### Usage

```dart
Card(
  // Card properties
).onTap(() {
  // Handle tap
});
```

---

## Version Control

Maintain a clean and organized codebase using version control best practices.

### Branching Strategy

- **Main Branches**:
  - `main`: Production-ready code.
  - `develop`: Integration branch for features.
- **Feature Branches**:
  - `feature/[feature-name]`: Develop new features.

### Commit Messages

- Use clear and descriptive commit messages.
- Follow the [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) specification.

### Example Commit Message

```
feat: add downtime list page with filtering

- Implement DowntimeList.dart with fetch and display logic
- Add FilterListDowntime.dart for filtering options
- Update DowntimeController and DowntimeRepository
```

---

## Adding a New Feature

When adding a new feature, follow the structure and guidelines outlined above to ensure consistency.

### Example: Adding a New Feature `Inventory`

1. **Create Files and Directories**

   ```
   lib/
   ├── controllers/
   │   └── InventoryController.dart
   ├── models/
   │   └── InventoryModel.dart
   ├── pages/
   │   ├── Inventory/
   │   │   ├── InventoryList.dart
   │   │   ├── InventoryDetail.dart
   │   │   └── element/
   │   │       └── FilterListInventory.dart
   ├── repositories/
   │   └── InventoryRepository.dart
   └── widgets/
       └── AutoCompleteField.dart
   ```

2. **Implement Controller**

   ```dart
   // lib/controllers/InventoryController.dart
   class InventoryController {
     final InventoryRepository _repository = InventoryRepository();

     Future<List<InventoryModel>> fetchInventoryList() async {
       return await _repository.getInventoryList();
     }

     // Additional business logic
   }
   ```

3. **Implement Repository**

   ```dart
   // lib/repositories/InventoryRepository.dart
   class InventoryRepository {
     Future<List<InventoryModel>> getInventoryList() async {
       // Implement API call or database query
     }

     // Additional data operations
   }
   ```

4. **Create Models**

   ```dart
   // lib/models/InventoryModel.dart
   class InventoryModel {
     final String id;
     final String name;
     // Additional fields

     InventoryModel({required this.id, required this.name});

     factory InventoryModel.fromJson(Map<String, dynamic> json) {
       return InventoryModel(
         id: json['id'],
         name: json['name'],
         // Initialize additional fields
       );
     }

     // Additional methods if necessary
   }
   ```

5. **Develop UI Pages**
   - **List Page**

     ```dart
     // lib/pages/Inventory/InventoryList.dart
     class InventoryList extends StatefulWidget {
       // Implementation similar to DowntimeList.dart
     }

     class _InventoryListState extends State<InventoryList> {
       // State variables and methods
     }
     ```

   - **Detail Page**

     ```dart
     // lib/pages/Inventory/InventoryDetail.dart
     class InventoryDetail extends StatelessWidget {
       // Implementation for detailed view
     }
     ```

   - **Filter List**

     ```dart
     // lib/pages/Inventory/element/FilterListInventory.dart
     class FilterListInventory extends StatelessWidget {
       // Implementation for filtering inventory list
     }
     ```

6. **Update Navigation**

   Ensure that the new pages are accessible via your app's navigation system.

   ```dart
   // Example in main.dart or appropriate routing file
   routes: {
     '/InventoryList': (context) => InventoryList(),
     '/InventoryDetail': (context) => InventoryDetail(),
     // Other routes
   },
   ```

---

## SAP ABAP Coding Guidelines

To maintain consistency and quality in SAP ABAP development, follow the guidelines outlined below.

### Naming Conventions

Consistency in naming enhances readability and maintainability.

- **Function Modules and Methods**
  - Use prefixes that indicate functionality.
    - Custom function modules should begin with `Z` or `Y`.
      - Example: `ZFM_MES_M2_LIST`, `ZFM_MES_M2_CREATE`, `ZFM_MES_M2_TECO`
  - Use underscores to separate meaningful parts.
    - Example: `ZFM_MES_M2_TECO` (Technical Complete)

- **Variables**
  - Use prefixes to indicate data types.
    - `lt_` for internal tables (e.g., `lt_orders`)
    - `ls_` for line structures (e.g., `ls_order`)
    - `lv_` for scalar variables (e.g., `lv_offset`)

- **Constants**
  - Use uppercase letters with underscores.
    - Example: `CONSTANT_NAME`

- **Structures and Tables**
  - Use meaningful names that reflect their purpose.
    - Example: `zst_mes_m2`, `ZTBT_MES_M2`

### File Organization

Organize ABAP code to promote readability and reusability.

- **Function Modules**
  - Group related function modules logically.
  - Use descriptive names that reflect their purpose.

- **Includes and Headers**
  - Use includes for shared code segments.
  - Maintain clear separation between different sections of code.

- **Comments**
  - Use block comments to describe the purpose and interface.
    - Example:
      ```abap
      FUNCTION ZFM_MES_M2_LIST.
      *"----------------------------------------------------------------------
      *"*"Local Interface:
      *"  IMPORTING
      *"     VALUE(IV_COMPANY_CODE) TYPE  WERKS_D
      *"     VALUE(IV_ORDER_NUMBER) TYPE  AUFNR OPTIONAL
      *"     ...
      *"----------------------------------------------------------------------
      ```

### Coding Practices

- **Modularity**
  - Divide code into meaningful sections using clear sections and comments.
  - Encapsulate functionality within function modules to enhance reusability.

- **Performance Optimization**
  - Optimize SELECT statements by selecting only necessary fields.
  - Use proper indexing and avoid unnecessary data retrieval.
  - Implement pagination effectively to handle large data sets.

- **Error Handling**
  - Use TRY...CATCH blocks to handle exceptions.
  - Provide meaningful error messages and ensure proper transaction handling (COMMIT/ROLLBACK).
  - Example:

    ```abap
    TRY.
        " Code that might throw an exception
      CATCH cx_root INTO DATA(lx_root).
        ev_success = ''.
        ev_message = lx_root->get_text( ).
    ENDTRY.
    ```

- **Consistency**
  - Follow a consistent coding style for indentation, spacing, and alignment.
  - Use uppercase for ABAP keywords and lowercase for variable names.

- **Security**
  - Validate all inputs to prevent injection attacks.
  - Handle sensitive data appropriately.

### Documentation

- **Function Modules**
  - Clearly document the purpose, parameters, and return values.
  - Use SAP documentation standards.
    - Example:
      ```abap
      FUNCTION ZFM_MES_M2_TECO.
      *"----------------------------------------------------------------------
      *"*"Local Interface:
      *"  IMPORTING
      *"     VALUE(IV_ORDER_NUMBER) TYPE  AUFNR
      *"  EXPORTING
      *"     VALUE(EV_SUCCESS) TYPE  BOOLE_D
      *"     VALUE(EV_MESSAGE) TYPE  CHAR200
      *"----------------------------------------------------------------------
      ```

- **Inline Comments**
  - Use inline comments to explain complex logic.
  - Avoid obvious comments; focus on explaining the "why" not the "what."

### Example Function Module Documentation

#### `ZFM_MES_M2_LIST.abap`

```abap:SAP/ZFM_MES_M2_LIST.abap
FUNCTION ZFM_MES_M2_LIST.
*"----------------------------------------------------------------------
*"*"Local Interface:
*"  IMPORTING
*"     VALUE(IV_COMPANY_CODE) TYPE  WERKS_D
*"     VALUE(IV_ORDER_NUMBER) TYPE  AUFNR OPTIONAL
*"     VALUE(IV_STATUS) TYPE  CHAR20 OPTIONAL
*"     VALUE(IV_FROM_DATE) TYPE  DATS OPTIONAL
*"     VALUE(IV_TO_DATE) TYPE  DATS OPTIONAL
*"     VALUE(IV_PAGE_SIZE) TYPE  INT4 DEFAULT 20
*"     VALUE(IV_PAGE_NUMBER) TYPE  INT4 DEFAULT 1
*"  EXPORTING
*"     VALUE(EV_TOTAL_COUNT) TYPE  INT4
*"     VALUE(EV_SUCCESS) TYPE  CHAR1
*"     VALUE(EV_MESSAGE) TYPE  CHAR100
*"  TABLES
*"      ET_ORDERS TYPE  ZTBT_MES_M2
*"----------------------------------------------------------------------
  DATA: lt_orders TYPE STANDARD TABLE OF zst_mes_m2 WITH KEY aufnr,
        ls_order  TYPE zst_mes_m2,
        lv_offset TYPE int4,
        lt_jest TYPE TABLE OF jest,
        lt_status_objnr TYPE TABLE OF jest,
        lr_stat TYPE RANGE OF jest-stat.
  
  TRY.
      " Calculate offset for pagination
      lv_offset = ( iv_page_number - 1 ) * iv_page_size.

      " Convert iv_status to uppercase to ensure case-insensitive matching
      iv_status = to_upper( iv_status ).

      " Define status mapping
      CASE iv_status.
        WHEN 'CREATED'.
          lr_stat = VALUE #(
            ( sign = 'I' option = 'EQ' low = 'I0002' )
            ( sign = 'I' option = 'EQ' low = 'I0009' )
            ( sign = 'I' option = 'EQ' low = 'I0016' )
            ( sign = 'E' option = 'EQ' low = 'I0028' )
            ( sign = 'E' option = 'EQ' low = 'I0045' )
            ( sign = 'E' option = 'EQ' low = 'I0046' )
          ).
        WHEN 'IN_PROCESS'.
          lr_stat = VALUE #(
            ( sign = 'I' option = 'EQ' low = 'I0028' )
            ( sign = 'E' option = 'EQ' low = 'I0045' )
            ( sign = 'E' option = 'EQ' low = 'I0046' )
          ).
        WHEN 'COMPLETED'.
          lr_stat = VALUE #(
            ( sign = 'I' option = 'EQ' low = 'I0045' )
            ( sign = 'I' option = 'EQ' low = 'I0046' )
          ).
        WHEN OTHERS.
          " Optional: Handle unexpected status values
          WRITE: / 'LOG: Unexpected iv_status value:', iv_status.
          lr_stat = VALUE #( ). " Empty range
      ENDCASE.
  
      " Debug: Log contents of lr_stat
      LOOP AT lr_stat INTO DATA(ls_stat).
        WRITE: / 'LOG: Status Range - SIGN:', ls_stat-sign,
               'OPTION:', ls_stat-option, 'LOW:', ls_stat-low.
      ENDLOOP.
  
      " Get matching object numbers first
      SELECT DISTINCT objnr
        FROM jest
        WHERE stat IN @lr_stat
          AND inact <> 'X'
        INTO TABLE @lt_status_objnr.
  
      " Debug: Log number of objnr retrieved
      WRITE: / 'LOG: Number of objnr retrieved:', LINES( lt_status_objnr ).
  
      " Get total count first for pagination
      SELECT COUNT(*)
        FROM aufk AS a
        WHERE a~auart = 'M2'
          AND a~werks = @iv_company_code
          AND ( @iv_order_number IS NULL 
                OR @iv_order_number = '' 
                OR a~aufnr = @iv_order_number )
          AND ( @iv_from_date = 00000000 
                OR a~erdat >= @iv_from_date )
          AND ( @iv_to_date = 00000000 
                OR a~erdat <= @iv_to_date )
          AND ( @iv_status = '' 
                OR EXISTS ( SELECT 1 
                           FROM jest AS j 
                           WHERE j~objnr = a~objnr 
                             AND j~stat IN @lr_stat
                             AND j~inact <> 'X' ) )
        INTO @ev_total_count.
  
      " Main query with joins to get order details
      SELECT DISTINCT
          a~aufnr,
          a~objnr,
          a~auart,
          a~ktext,
          a~erdat,
          a~ernam,
          a~werks,
          a~stort,
          a~kostl,
          a~vaplz,
          h~equnr,
          ek~eqktx,
          il~tplnr
        FROM aufk AS a
        LEFT JOIN afko AS k ON k~aufnr = a~aufnr
        LEFT JOIN afvc AS v ON v~aufpl = k~aufpl
        LEFT JOIN afih AS h ON h~aufnr = a~aufnr
        LEFT JOIN equz AS z ON z~equnr = h~equnr
                           AND z~datbi >= @sy-datum
        LEFT JOIN eqkt AS ek ON ek~equnr = z~equnr
                            AND ek~spras = @sy-langu
        LEFT JOIN iloa AS il ON il~iloan = z~iloan
                            AND il~stort = a~stort
        WHERE a~auart = 'M2'
          AND a~werks = @iv_company_code
          AND ( @iv_order_number IS NULL 
                OR @iv_order_number = '' 
                OR a~aufnr = @iv_order_number )
          AND ( @iv_from_date = 00000000 
                OR a~erdat >= @iv_from_date )
          AND ( @iv_to_date = 00000000 
                OR a~erdat <= @iv_to_date )
          AND ( @iv_status = '' 
                OR ( @iv_status = 'COMPLETED' AND EXISTS ( SELECT 1 
                                                           FROM jest AS j 
                                                           WHERE j~objnr = a~objnr 
                                                             AND j~stat IN ('I0045', 'I0046')
                                                             AND j~inact <> 'X' ) )
                OR ( @iv_status = 'IN_PROCESS' AND EXISTS ( SELECT 1 
                                                            FROM jest AS j 
                                                            WHERE j~objnr = a~objnr 
                                                              AND j~stat = 'I0028'
                                                              AND j~inact <> 'X' )
                     AND NOT EXISTS ( SELECT 1 
                                      FROM jest AS j 
                                      WHERE j~objnr = a~objnr 
                                        AND j~stat IN ('I0045', 'I0046')
                                        AND j~inact <> 'X' ) )
                OR ( @iv_status = 'CREATED' AND EXISTS ( SELECT 1 
                                                           FROM jest AS j 
                                                           WHERE j~objnr = a~objnr 
                                                             AND j~stat IN ('I0002', 'I0009', 'I0016')
                                                             AND j~inact <> 'X' )
                     AND NOT EXISTS ( SELECT 1 
                                      FROM jest AS j 
                                      WHERE j~objnr = a~objnr 
                                        AND j~stat IN ('I0028', 'I0045', 'I0046')
                                        AND j~inact <> 'X' ) ) )
        ORDER BY a~erdat DESCENDING, a~aufnr DESCENDING
        INTO CORRESPONDING FIELDS OF TABLE @lt_orders
        UP TO @iv_page_size ROWS
        OFFSET @lv_offset.
  
      " Get order statuses using objnr
      IF lt_orders IS NOT INITIAL.
        SELECT *
          FROM jest
          FOR ALL ENTRIES IN @lt_orders
          WHERE objnr = @lt_orders-objnr
            AND inact <> 'X'
          INTO TABLE @lt_jest.
      ENDIF.
  
      " Log the number of jest entries retrieved
      WRITE: / 'LOG: Number of jest entries retrieved:', LINES( lt_jest ).
  
      " Map statuses for each order
      LOOP AT lt_orders ASSIGNING FIELD-SYMBOL(<ls_order>).
        DATA: lt_order_statuses TYPE STANDARD TABLE OF jest.
        lt_order_statuses = VALUE #( FOR ls IN lt_jest 
                                     WHERE ( objnr = <ls_order>-objnr ) 
                                     ( ls ) ).
  
        " Log statuses for the current order
        LOOP AT lt_order_statuses INTO DATA(ls_jest).
          WRITE: / 'LOG: Order:', <ls_order>-aufnr, 'Stat:', ls_jest-stat.
        ENDLOOP.
  
        " Check statuses in priority order
        IF line_exists( lt_order_statuses[ stat = 'I0045' ] ) OR
           line_exists( lt_order_statuses[ stat = 'I0046' ] ).
          <ls_order>-status = 'COMPLETED'.
        ELSEIF line_exists( lt_order_statuses[ stat = 'I0028' ] ).
          <ls_order>-status = 'IN_PROCESS'.
        ELSEIF line_exists( lt_order_statuses[ stat = 'I0002' ] ) OR
               line_exists( lt_order_statuses[ stat = 'I0009' ] ) OR
               line_exists( lt_order_statuses[ stat = 'I0016' ] ).
          <ls_order>-status = 'CREATED'.
        ELSE.
          <ls_order>-status = 'UNKNOWN'.
        ENDIF.
      ENDLOOP.
  
      " Move results to export parameter
      et_orders[] = lt_orders[].
  
      ev_success = 'X'.
      ev_message = 'Success'.
  
    CATCH cx_root INTO DATA(lx_root).
      ev_success = ''.
      ev_message = lx_root->get_text( ).
    ENDTRY.
  
ENDFUNCTION.
```

---

## Conclusion

Adhering to these guidelines will facilitate a consistent and efficient development process. Ensure that all team members are familiar with and follow these standards to maintain the quality and scalability of the project.

If you have any questions or suggestions regarding these guidelines, please reach out to the project maintainer.

Happy coding!