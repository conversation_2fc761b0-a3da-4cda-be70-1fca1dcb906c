class TyLeTieuHaoModel {
  final List<TyLeTieuHaoRecord>? data;
  final bool status;
  final String message;

  TyLeTieuHaoModel({
    this.data,
    this.status = true,
    this.message = 'Success',
  });

  factory TyLeTieuHaoModel.fromJson(Map<String, dynamic> json) {
    return TyLeTieuHaoModel(
      data: json['data'] != null ? (json['data'] as List).map((item) => TyLeTieuHaoRecord.fromJson(item)).toList() : null,
      status: json['isSuccess'] ?? true,
      message: json['message'] ?? 'Success',
    );
  }
}

class TyLeTieuHaoRecord {
  final String? id;
  final String? date;
  final String? lsxSAP;
  final String? dotSanXuat;
  final String? maSanPham;
  final String? tenSanPham;
  final String? maNVL;
  final String? tenNVL;
  final String? dvt;
  final double? slThoSuDung;
  final double? slSPLamDuoc;
  final double? slM2DinhMuc;
  final double? tyLeTheoDM;
  final String? personInChargeCodeMany;
  final String? createdDate;
  final String? createBy;
  final String? updatedDate;
  final String? updateBy;
  final String? companyCode;
  final String? status;
  final String? note;
  final double? soLuongKH;

  TyLeTieuHaoRecord({
    this.id,
    this.date,
    this.lsxSAP,
    this.dotSanXuat,
    this.maSanPham,
    this.tenSanPham,
    this.maNVL,
    this.tenNVL,
    this.dvt,
    this.slThoSuDung,
    this.slSPLamDuoc,
    this.slM2DinhMuc,
    this.tyLeTheoDM,
    this.personInChargeCodeMany,
    this.createdDate,
    this.createBy,
    this.updatedDate,
    this.updateBy,
    this.companyCode,
    this.status,
    this.note,
    this.soLuongKH,
  });

  factory TyLeTieuHaoRecord.fromJson(Map<String, dynamic> json) {
    return TyLeTieuHaoRecord(
      id: json['tyLeTieuHaoId'],
      date: json['date'],
      lsxSAP: json['lsxsap'],
      dotSanXuat: json['dotSanXuat'],
      maSanPham: json['maSanPham'],
      tenSanPham: json['tenSanPham'],
      maNVL: json['maNVL'],
      tenNVL: json['tenNVL'],
      dvt: json['dvt'],
      slThoSuDung: json['slThoSuDung']?.toDouble(),
      slSPLamDuoc: json['slspLamDuoc']?.toDouble(),
      slM2DinhMuc: json['slM2DinhMuc']?.toDouble(),
      tyLeTheoDM: json['tyLeTheoDM']?.toDouble(),
      personInChargeCodeMany: json['personInChargeCodeMany'],
      createdDate: json['createdDate'],
      createBy: json['createBy'],
      updatedDate: json['updatedDate'],
      updateBy: json['updateBy'],
      companyCode: json['companyCode'],
      status: json['status'],
      note: json['note'],
      soLuongKH: json['soLuongKH']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tyLeTieuHaoId': id,
      'date': date,
      'lsxSAP': lsxSAP,
      'dotSanXuat': dotSanXuat,
      'maSanPham': maSanPham,
      'tenSanPham': tenSanPham,
      'maNVL': maNVL,
      'tenNVL': tenNVL,
      'dvt': dvt,
      'slThoSuDung': slThoSuDung,
      'slSPLamDuoc': slSPLamDuoc,
      'slM2DinhMuc': slM2DinhMuc,
      'tyLeTheoDM': tyLeTheoDM,
      'personInChargeCodeMany': personInChargeCodeMany,
      'companyCode': companyCode,
      'status': status,
      'note': note,
      'soLuongKH': soLuongKH,
    };
  }
}

class LSXSAPSuggestion {
  final String lsxSAP;
  final String dotSanXuat;
  final String maSanPham;
  final String tenSanPham;
  final double soLuongKH;

  LSXSAPSuggestion({
    required this.lsxSAP,
    required this.dotSanXuat,
    required this.maSanPham,
    required this.tenSanPham,
    required this.soLuongKH,
  });

  factory LSXSAPSuggestion.fromJson(Map<String, dynamic> json) {
    return LSXSAPSuggestion(
      lsxSAP: json['lsxsap'] ?? '', // lsxSAP not working, lsxsap is working
      dotSanXuat: json['dotSanXuat'] ?? '',
      maSanPham: json['maSanPham'] ?? '',
      tenSanPham: json['tenSanPham'] ?? '',
      soLuongKH: json['soLuongKH']?.toDouble() ?? 0,
    );
  }
}

class MaterialSuggestion {
  final String bomItem;
  final String maNVL;
  final String tenNVL;
  final String dvt;
  final double dinhMucSanPham;

  MaterialSuggestion({
    required this.bomItem,
    required this.maNVL,
    required this.tenNVL,
    required this.dvt,
    required this.dinhMucSanPham,
  });

  factory MaterialSuggestion.fromJson(Map<String, dynamic> json) {
    return MaterialSuggestion(
      bomItem: json['bomItem'] ?? '',
      maNVL: json['maNVL'] ?? '',
      tenNVL: json['tenNVL'] ?? '',
      dvt: json['dvt'] ?? '',
      dinhMucSanPham: json['dinhMucSanPham']?.toDouble() ?? 0,
    );
  }
}

class MaterialSuggestionItem {
  final String? maNVL;
  final String? tenNVL;
  final String? dvt;

  MaterialSuggestionItem({
    this.maNVL,
    this.tenNVL,
    this.dvt,
  });

  factory MaterialSuggestionItem.fromJson(Map<String, dynamic> json) {
    return MaterialSuggestionItem(
      maNVL: json['maNVL'] as String?,
      tenNVL: json['tenNVL'] as String?,
      dvt: json['dvt'] as String?,
    );
  }
}
