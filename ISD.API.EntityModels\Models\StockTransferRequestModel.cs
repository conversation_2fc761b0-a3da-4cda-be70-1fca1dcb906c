﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StockTransferRequestModel", Schema = "Warehouse")]
    public partial class StockTransferRequestModel
    {
        public StockTransferRequestModel()
        {
            StockTransferRequestDetailModel = new HashSet<StockTransferRequestDetailModel>();
        }

        [Key]
        public Guid Id { get; set; }
        public int StockTransferRequestCode { get; set; }
        public Guid? FromStock { get; set; }
        public Guid? ToStock { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? StoreId { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
        public bool? IsDelete { get; set; }
        public bool? Actived { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(50)]
        public string SalesEmployeeCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocumentDate { get; set; }
        public Guid? DeletedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DeletedTime { get; set; }
        [StringLength(500)]
        public string SenderName { get; set; }
        [StringLength(20)]
        public string SenderPhone { get; set; }
        [StringLength(500)]
        public string RecipientName { get; set; }
        [StringLength(20)]
        public string RecipientPhone { get; set; }
        [StringLength(4000)]
        public string RecipientAddress { get; set; }
        [StringLength(500)]
        public string RecipientCompany { get; set; }
        [StringLength(4000)]
        public string SenderAddress { get; set; }
        [StringLength(4000)]
        public string DeletedReason { get; set; }

        [ForeignKey("CompanyId")]
        [InverseProperty("StockTransferRequestModel")]
        public virtual CompanyModel Company { get; set; }
        [ForeignKey("CreateBy")]
        [InverseProperty("StockTransferRequestModelCreateByNavigation")]
        public virtual AccountModel CreateByNavigation { get; set; }
        [ForeignKey("DeletedBy")]
        [InverseProperty("StockTransferRequestModelDeletedByNavigation")]
        public virtual AccountModel DeletedByNavigation { get; set; }
        [ForeignKey("FromStock")]
        [InverseProperty("StockTransferRequestModelFromStockNavigation")]
        public virtual StockModel FromStockNavigation { get; set; }
        [ForeignKey("LastEditBy")]
        [InverseProperty("StockTransferRequestModelLastEditByNavigation")]
        public virtual AccountModel LastEditByNavigation { get; set; }
        [ForeignKey("StoreId")]
        [InverseProperty("StockTransferRequestModel")]
        public virtual StoreModel Store { get; set; }
        [ForeignKey("ToStock")]
        [InverseProperty("StockTransferRequestModelToStockNavigation")]
        public virtual StockModel ToStockNavigation { get; set; }
        [InverseProperty("StockTransferRequest")]
        public virtual ICollection<StockTransferRequestDetailModel> StockTransferRequestDetailModel { get; set; }
    }
}