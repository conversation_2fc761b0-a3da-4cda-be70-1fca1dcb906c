import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../element/ButtonInventoryMNG.dart';
import '../element/FormLayout.dart';
import '../element/LoadingScreen.dart';
import '../element/QualityFormFields.dart';
import '../element/timeOut.dart';
import '../model/slocAddresse.dart';
import '../model/userModel.dart';
import '../model/getStorageBin.dart';
import '../model/FilterLsQC.dart';
import '../screenArguments/screenArgumentQRPage.dart';
import '../utils/ui_helpers.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../repository/function/loginFunction.dart';
import '../page/KiemTraChatLuong/element/LoadingOverlay.dart';
import '../model/poInfo.dart';
import '../page/ContinuousQRScannerPage.dart';

class ImportProduct extends StatefulWidget {
  const ImportProduct({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
    required this.user,
  }) : super(key: key);

  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  final DataUser user;

  @override
  State<ImportProduct> createState() => _ImportProductState();
}

class _ImportProductState extends State<ImportProduct> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;
  bool _disableButton = false;
  late bool _timeOut;
  String _error = "";

  // Track quantities - these are now handled by the POSummary class
  // int _totalQuantity = 0;
  // int _scannedQuantity = 0;
  // String _currentPO = "";

  // Plant dropdown state
  SalesOrgCodes? _selectedSalesOrgCodes;
  List<SalesOrgCodes>? _salesOrgCodes;

  // Controllers
  final TextEditingController _warehouseController = TextEditingController();
  final TextEditingController _sapOrderController = TextEditingController();
  final TextEditingController _productCodeController = TextEditingController();
  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _serialController = TextEditingController();
  final TextEditingController _productGroupController = TextEditingController();
  final TextEditingController _batchController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();

  // Storage bin and sloc data
  String? _storageBinID;
  DataSlocAddress? _selectedSloc;
  bool _errorStorageBin = false;
  bool _errorSelectedSloc = false;
  Timer? _timer;
  List<DataSlocAddress> _getLsDataSlocAddress = [];
  List<DataGetStorageBin> _lsDataGetStorageBn = [];
  bool _isLoadingStorageBin = false;
  bool _disableDropdownSloc = false;

  // Product group options
  final List<Map<String, String>> _productGroups = [
    {"key": "NĐ", "value": "Nội địa"},
    {"key": "VS", "value": "Ván sàn"},
    {"key": "CL", "value": "Chỉ len tường"},
    {"key": "NE", "value": "Nẹp"},
    {"key": "DL", "value": "Đố lót"},
    {"key": "HM", "value": "Hàng mẫu"},
    {"key": "CT", "value": "Công trình"},
    {"key": "SG", "value": "Sàn gỗ Trường Thành"},
    {"key": "XK", "value": "Xuất khẩu"},
  ];

  String? _selectedProductGroup;

  // Company codes
  final Map<String, String> _companyCodes = {
    "H": "1000", // TTFH
    "C": "1100", // TTFC
    "S": "1200", // TTFS
    "R": "1300", // TTFR
    "T": "3000", // TTFT
    "1": "2100", // TTDL1
    "2": "2000", // TTDL2
  };

  // Scanned products list
  List<ScannedProduct> _scannedProducts = [];

  // Add this as a class member
  Map<String, int> _poQuantities = {};

  // Cache for PO information
  Map<String, POInfo> _poInfoCache = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Check for session timeout
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        setState(() {
          _timeOut = true;
          _isLoading = false;
        });
        return;
      } else {
        setState(() {
          _timeOut = false;
        });
      }

      // Instead of loading from storage, create the plant list directly from user data
      // This is similar to how FilterListDowntime.dart handles it
      setState(() {
        // Create a default list with the current user's company
        _salesOrgCodes = [
          SalesOrgCodes(
            saleOrgCode: widget.plant,
            storeName: widget.user.companyName ?? _getPlantName(widget.plant),
          )
        ];

        // If user has access to other companies, add them
        if (widget.user.companyCode != null) {
          // Set the selected plant to the current user's plant
          _selectedSalesOrgCodes = _salesOrgCodes!.first;
        }
      });

      // Try to load company list as a fallback, but don't depend on it
      try {
        // final companyList = await LoginFunction.getCompanyListFromStorage(widget.accountId);
        final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);

        if (companyList != null && companyList.isNotEmpty && mounted) {
          setState(() {
            _salesOrgCodes = companyList
                .map((company) => SalesOrgCodes(
                      saleOrgCode: company.companyCode,
                      storeName: company.companyName,
                    ))
                .toList();

            if (_salesOrgCodes!.isNotEmpty) {
              _selectedSalesOrgCodes = _salesOrgCodes!.firstWhere(
                (element) => element.saleOrgCode == widget.plant,
                orElse: () => _salesOrgCodes!.first,
              );
            }
          });
        }
      } catch (e) {
        debugPrint("Error loading company list from storage: $e");
        // Continue with the default plant list we created above
      }

      // Load sloc addresses
      final data = await ImportWareHouseFunction.fetchSlocAddress(widget.plant, widget.token);

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        if (data != null) {
          _getLsDataSlocAddress = data;
        }

        // Initialize batch number after loading initial data
        _batchController.text = _generateBatchNumber();
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  // Helper method to get plant name from code
  String _getPlantName(String plantCode) {
    switch (plantCode) {
      case "1000":
        return "TTFH";
      case "1100":
        return "TTFC";
      case "1200":
        return "TTFS";
      case "1300":
        return "TTFR";
      case "3000":
        return "TTFT";
      case "2100":
        return "TTDL1";
      case "2000":
        return "TTDL2";
      default:
        return "Plant $plantCode";
    }
  }

  String _generateBatchNumber() {
    final now = DateTime.now();

    // X is always "N"
    const prefix = "N";

    // AA is product group code
    final productGroup = _selectedProductGroup ?? "XX";

    // DD is day
    final day = now.day.toString().padLeft(2, '0');

    // M is month (1-9, O for 10, N for 11, D for 12)
    String month;
    if (now.month <= 9) {
      month = now.month.toString();
    } else if (now.month == 10) {
      month = 'O';
    } else if (now.month == 11) {
      month = 'N';
    } else {
      // month == 12
      month = 'D';
    }

    // YY is year
    final year = now.year.toString().substring(2);

    // B is product status (always D for now)
    const status = "D";

    // C is company code
    String companyCode = "H"; // Default to H (1000)

    // If a plant is selected, try to determine the company code
    if (_selectedSalesOrgCodes != null && _selectedSalesOrgCodes!.saleOrgCode != null) {
      // Find the key in _companyCodes that matches the selected plant code
      for (var entry in _companyCodes.entries) {
        if (_companyCodes[entry.key] == _selectedSalesOrgCodes!.saleOrgCode) {
          companyCode = entry.key;
          break;
        }
      }
    }

    return "$prefix$productGroup$day$month$year$status$companyCode";
  }

  void _setAddressQRCode(DataSlocAddress? data, BuildContext context) {
    try {
      if (!mounted) return;
      setState(() {
        _selectedSloc = data;
        _warehouseController.text = data?.sloc ?? "";
        if (_selectedSloc != null) {
          _errorSelectedSloc = false;
          if (_selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo != "") {
            if (_locationController.text.isEmpty) {
              _errorStorageBin = true;
            } else {
              _errorStorageBin = false;
            }
          } else {
            _errorStorageBin = false;
          }

          // Update location for all existing scanned products when address QR is scanned
          String newLocation = data?.defaultStorageBin ?? _locationController.text;
          if (newLocation.isNotEmpty) {
            _locationController.text = newLocation;
            for (int i = 0; i < _scannedProducts.length; i++) {
              _scannedProducts[i] = ScannedProduct(
                sapOrder: _scannedProducts[i].sapOrder,
                productCode: _scannedProducts[i].productCode,
                productName: _scannedProducts[i].productName,
                batch: _scannedProducts[i].batch,
                location: newLocation, // Update to new location from QR scan
                so: _scannedProducts[i].so,
                soItem: _scannedProducts[i].soItem,
                wbs: _scannedProducts[i].wbs,
                unit: _scannedProducts[i].unit,
                importQuantity: _scannedProducts[i].importQuantity,
                totalQuantity: _scannedProducts[i].totalQuantity,
                importedQuantity: _scannedProducts[i].importedQuantity,
              );
            }
          }
        } else {
          _errorSelectedSloc = true;
        }
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _selectedSloc = null;
      });
    }
  }

  Future<void> _onStorageBinChanged(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBin != true) {
          _errorStorageBin = true;
        }
      } else {
        if (_errorStorageBin != false) {
          _errorStorageBin = false;
        }
      }

      // Update location for all existing scanned products when user types in storage bin
      if (value.isNotEmpty) {
        for (int i = 0; i < _scannedProducts.length; i++) {
          _scannedProducts[i] = ScannedProduct(
            sapOrder: _scannedProducts[i].sapOrder,
            productCode: _scannedProducts[i].productCode,
            productName: _scannedProducts[i].productName,
            batch: _scannedProducts[i].batch,
            location: value, // Update to new location as user types
            so: _scannedProducts[i].so,
            soItem: _scannedProducts[i].soItem,
            wbs: _scannedProducts[i].wbs,
            unit: _scannedProducts[i].unit,
            importQuantity: _scannedProducts[i].importQuantity,
            totalQuantity: _scannedProducts[i].totalQuantity,
            importedQuantity: _scannedProducts[i].importedQuantity,
          );
        }
      }
    });
    if (!mounted) return;
    _timer?.cancel();
    if (_errorStorageBin == false) {
      _getAutocompleteStorageBin(value);
    }
  }

  Future<void> _getAutocompleteStorageBin(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = false;
              if (data != null) {
                _lsDataGetStorageBn = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  void _setFilter(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _locationController.text = dataGetStorageBin.value ?? " ";
      _storageBinID = dataGetStorageBin.key ?? "";
      if (_selectedSloc != null && _selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo != "") {
        if (_locationController.text.isEmpty) {
          _errorStorageBin = true;
        } else {
          _errorStorageBin = false;
        }
      } else {
        _errorStorageBin = false;
      }
      _lsDataGetStorageBn = [];

      // Update location for all existing scanned products when storage bin changes
      String newLocation = dataGetStorageBin.value ?? "";
      for (int i = 0; i < _scannedProducts.length; i++) {
        _scannedProducts[i] = ScannedProduct(
          sapOrder: _scannedProducts[i].sapOrder,
          productCode: _scannedProducts[i].productCode,
          productName: _scannedProducts[i].productName,
          batch: _scannedProducts[i].batch,
          location: newLocation, // Update to new location
          so: _scannedProducts[i].so,
          soItem: _scannedProducts[i].soItem,
          wbs: _scannedProducts[i].wbs,
          unit: _scannedProducts[i].unit,
          importQuantity: _scannedProducts[i].importQuantity,
          totalQuantity: _scannedProducts[i].totalQuantity,
          importedQuantity: _scannedProducts[i].importedQuantity,
        );
      }
    });
  }

  @override
  void dispose() {
    _warehouseController.dispose();
    _sapOrderController.dispose();
    _productCodeController.dispose();
    _productNameController.dispose();
    _serialController.dispose();
    _productGroupController.dispose();
    _batchController.dispose();
    _locationController.dispose();
    if (_timer != null) {
      _timer!.cancel();
    }
    super.dispose();
  }

  // Method to fetch PO information including total quantity
  Future<POInfo?> _fetchPOInformation(String serial) async {
    try {
      setState(() {
        _isSaving = true; // Show loading indicator while fetching PO info
      });

      // Extract the Production Order by removing the last 5 characters from the serial
      String productionOrder = serial.length > 5 ? serial.substring(0, serial.length - 5) : serial;

      debugPrint('Extracting Production Order from $serial: $productionOrder');

      // First check if we already have this production order in cache
      if (_poInfoCache.containsKey(productionOrder)) {
        debugPrint('Cache HIT for Production Order: $productionOrder');

        // Make sure we update the _poQuantities map with the cached data
        POInfo cachedInfo = _poInfoCache[productionOrder]!;
        _poQuantities[cachedInfo.po] = cachedInfo.quantity;
        debugPrint('Using cached quantity for ${cachedInfo.po}: ${cachedInfo.quantity}');

        setState(() {
          _isSaving = false;
        });
        return _poInfoCache[productionOrder];
      }

      // If not in cache, fetch from API
      debugPrint('Cache MISS for Production Order: $productionOrder - Fetching from API');
      final poInfo = await ImportWareHouseFunction.fetchPOInfoBySerial(serial, widget.token);

      if (!mounted) return null;

      setState(() {
        _isSaving = false;
        if (poInfo != null) {
          // Store the quantity for this PO
          _poQuantities[poInfo.po] = poInfo.quantity;
          debugPrint('Storing quantity for ${poInfo.po}: ${poInfo.quantity}');

          // Cache the PO info by production order for future use
          _poInfoCache[productionOrder] = poInfo;
          debugPrint('Added to cache: $productionOrder -> ${poInfo.po} (${poInfo.productName})');
        }
      });

      return poInfo;
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return null;
      setState(() {
        _isSaving = false;
        _error = error.toString();
      });

      // Show error toast
      showToast(
        context: context,
        message: "Lỗi khi tải thông tin LSX: ${error.toString()}",
        duration: 2,
      );

      return null;
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  Future<void> _addScannedProduct(String serial) async {
    // Fetch the PO information for this serial
    POInfo? poInfo = await _fetchPOInformation(serial);

    // If we couldn't get the PO info, don't add the product
    if (poInfo == null) {
      showToast(
        context: context,
        message: "Không thể tải thông tin LSX cho sản phẩm này",
        duration: 2,
      );
      return;
    }

    // Check if this PO already exists in the scanned products list
    bool isDuplicate = _scannedProducts.any((product) => product.sapOrder == poInfo.po);

    if (isDuplicate) {
      // Show toast message for duplicate PO
      showToast(
        context: context,
        message: "LSX ${poInfo.po} đã được thêm vào danh sách",
        duration: 2,
      );
      return;
    }

    // Check if there's remaining quantity to import
    if (poInfo.remainingQuantity <= 0) {
      showToast(
        context: context,
        message: "LSX ${poInfo.po} đã nhập đủ số lượng",
        duration: 2,
      );
      return;
    }

    // Add the product with the real data from the API
    setState(() {
      _scannedProducts.add(
        ScannedProduct(
          sapOrder: poInfo.po,
          productCode: poInfo.productCode,
          productName: poInfo.productName,
          batch: _batchController.text,
          location: _locationController.text,
          so: poInfo.so ?? "",
          soItem: poInfo.soItem ?? "10",
          wbs: poInfo.wbs ?? "",
          unit: "CAI",
          importQuantity: 1, // Default quantity to import
          totalQuantity: poInfo.quantity,
          importedQuantity: poInfo.importedQuantity,
        ),
      );
    });

    // Show success toast
    if (mounted) {
      showToast(
        context: context,
        message: "Đã thêm LSX ${poInfo.po} vào danh sách",
        duration: 1,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey[100], body: TimeOutView(setButton: _setButton, disableButton: _disableButton)))
        : Scaffold(
            backgroundColor: Colors.grey[100],
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(
                  Icons.arrow_back_ios_new_rounded,
                  size: 14.sp,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'Nhập kho thành phẩm',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: SafeArea(
              child: LoadingOverlay(
                isLoading: _isSaving,
                child: KeyboardDismissOnTap(
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Padding(
                            padding: EdgeInsets.only(bottom: 20.h),
                            child: _buildBody(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
  }

  Widget _buildBody() {
    if (_isNotWifi) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.wifi_off, size: 48.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              'Không có kết nối mạng',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _isNotWifi = false;
                });
                _loadInitialData();
              },
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SECTION I: THÔNG TIN NHẬP KHO THÀNH PHẨM
        Container(
          margin: EdgeInsets.only(bottom: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // Section header
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 15.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5.r),
                    topRight: Radius.circular(5.r),
                  ),
                ),
                child: Text(
                  "I. THÔNG TIN NHẬP KHO THÀNH PHẨM",
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Date field (non-editable)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 2.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Ngày:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 10,
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 3.w),
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(3.r),
                          color: Colors.grey.shade100,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                DateFormat('dd/MM/yyyy').format(DateTime.now()),
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.grey),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 3.h),

              // Plant field
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 2.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Plant:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 10,
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 3.w),
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(3.r),
                          color: Colors.white,
                        ),
                        child: _salesOrgCodes == null || _salesOrgCodes!.isEmpty
                            ? Center(child: Text('Loading...', style: TextStyle(fontSize: 11.sp)))
                            : _salesOrgCodes!.length == 1
                                ? Padding(
                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                    child: Text(
                                      _salesOrgCodes!.first.storeName ?? "",
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  )
                                : DropdownButtonHideUnderline(
                                    child: DropdownButton<SalesOrgCodes>(
                                      isExpanded: true,
                                      isDense: true,
                                      itemHeight: null,
                                      value: _selectedSalesOrgCodes,
                                      iconSize: 15.sp,
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                      onChanged: (SalesOrgCodes? value) async {
                                        if (value == null) return;
                                        setState(() {
                                          _isLoading = true;
                                          _selectedSalesOrgCodes = value;
                                          // Clear existing values since they're for a different plant
                                          _selectedSloc = null;
                                          _warehouseController.clear();
                                          _locationController.clear();
                                          _getLsDataSlocAddress.clear();
                                          _storageBinID = null;
                                          _lsDataGetStorageBn = [];
                                        });

                                        // Reload sloc addresses for new plant
                                        try {
                                          final data = await ImportWareHouseFunction.fetchSlocAddress(value.saleOrgCode ?? "", widget.token);
                                          if (!mounted) return;
                                          setState(() {
                                            _isLoading = false;
                                            if (data != null) {
                                              _getLsDataSlocAddress = data;
                                            }
                                            // Update batch number when plant changes (which affects company code)
                                            _batchController.text = _generateBatchNumber();
                                          });
                                        } catch (error) {
                                          if (!mounted) return;
                                          setState(() {
                                            _isLoading = false;
                                            _error = error.toString();
                                          });
                                        }
                                      },
                                      items: _salesOrgCodes?.map((SalesOrgCodes code) {
                                        return DropdownMenuItem<SalesOrgCodes>(
                                          value: code,
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(vertical: 3.h),
                                            child: Text(
                                              "${code.storeName}",
                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      selectedItemBuilder: (BuildContext context) {
                                        return _salesOrgCodes?.map<Widget>((SalesOrgCodes code) {
                                              return Text(
                                                code.storeName.toString(),
                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                overflow: TextOverflow.ellipsis,
                                              );
                                            }).toList() ??
                                            [];
                                      },
                                    ),
                                  ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 3.h),

              // Product Group Dropdown
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 2.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Nhóm hàng:',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 10,
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 3.w),
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(3.r),
                          color: Colors.white,
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            isDense: true,
                            itemHeight: null,
                            value: _selectedProductGroup,
                            iconSize: 15.sp,
                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                            hint: Text(
                              'Chọn nhóm hàng',
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: Colors.grey,
                              ),
                            ),
                            items: _productGroups.map((Map<String, String> group) {
                              return DropdownMenuItem<String>(
                                value: group["key"],
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: 3.h),
                                  child: Text(
                                    "${group["key"]} - ${group["value"]}",
                                    style: TextStyle(
                                      fontSize: 11.sp,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setState(() {
                                _selectedProductGroup = newValue;
                                // Update batch number when product group changes
                                _batchController.text = _generateBatchNumber();
                              });
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
            ],
          ),
        ),
        // SECTION II: CHỌN VỊ TRÍ KHO NHẬP
        Container(
          margin: EdgeInsets.only(bottom: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // Warehouse section header
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w, bottom: 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5.r),
                    topRight: Radius.circular(5.r),
                  ),
                ),
                child: Text(
                  "II. CHỌN VỊ TRÍ KHO NHẬP",
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Scan QR button for warehouse
              Container(
                decoration: const BoxDecoration(color: Colors.white),
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                child: ElevatedButton.icon(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                    side: MaterialStateProperty.all(
                      const BorderSide(color: Color(0xff303F9F)),
                    ),
                    backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                  ),
                  onPressed: () async {
                    FocusScope.of(context).unfocus();
                    await Future<void>.delayed(const Duration(milliseconds: 500));
                    final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
                    if (data == null) return;
                    if (!mounted) return;
                    _setAddressQRCode(data as DataSlocAddress?, context);
                  },
                  icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                  label: Text(
                    'Quét mã',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                  ),
                ),
              ),

              // Plant field
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: const BoxDecoration(color: Colors.white),
                child: IntrinsicHeight(
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        flex: 4,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xff303F9F),
                            border: Border(
                              top: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              left: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Text(
                            "Plant:",
                            style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            border: Border(
                              top: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Text(
                            _selectedSalesOrgCodes?.storeName ??
                                (_salesOrgCodes != null && _salesOrgCodes!.isNotEmpty ? _salesOrgCodes!.first.storeName ?? "" : ""),
                            style: TextStyle(fontSize: 11.sp, color: Colors.black),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Sloc field
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: const BoxDecoration(color: Colors.white),
                child: IntrinsicHeight(
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        flex: 4,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xff303F9F),
                            border: Border(
                              left: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: RichText(
                            text: TextSpan(
                              text: 'Sloc',
                              style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
                              children: <TextSpan>[
                                TextSpan(text: ': ', style: TextStyle(fontSize: 12.sp, color: Colors.white)),
                                TextSpan(text: '*', style: TextStyle(fontSize: 12.sp, color: Colors.red)),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            border: Border(
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<DataSlocAddress>(
                                    isExpanded: true,
                                    isDense: true,
                                    itemHeight: null,
                                    value: _selectedSloc,
                                    iconSize: 15.sp,
                                    style: const TextStyle(color: Colors.white),
                                    onChanged: _disableDropdownSloc
                                        ? null
                                        : (DataSlocAddress? value) {
                                            setState(() {
                                              _selectedSloc = value;
                                              if (value != null) {
                                                _warehouseController.text = value.sloc ?? "";
                                                _locationController.text = value.defaultStorageBin ?? "";
                                                _storageBinID = value.defaultStorageBinId;

                                                // Update location for all existing scanned products
                                                for (int i = 0; i < _scannedProducts.length; i++) {
                                                  _scannedProducts[i] = ScannedProduct(
                                                    sapOrder: _scannedProducts[i].sapOrder,
                                                    productCode: _scannedProducts[i].productCode,
                                                    productName: _scannedProducts[i].productName,
                                                    batch: _scannedProducts[i].batch,
                                                    location: value.defaultStorageBin ?? "", // Update to new location
                                                    so: _scannedProducts[i].so,
                                                    soItem: _scannedProducts[i].soItem,
                                                    wbs: _scannedProducts[i].wbs,
                                                    unit: _scannedProducts[i].unit,
                                                    importQuantity: _scannedProducts[i].importQuantity,
                                                    totalQuantity: _scannedProducts[i].totalQuantity,
                                                    importedQuantity: _scannedProducts[i].importedQuantity,
                                                  );
                                                }
                                              }
                                            });
                                          },
                                    items: _getLsDataSlocAddress.map((DataSlocAddress sloc) {
                                      return DropdownMenuItem<DataSlocAddress>(
                                        value: sloc,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 0.w),
                                          child: Text(
                                            sloc.slocDisplay ?? " ",
                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    selectedItemBuilder: (BuildContext context) {
                                      return _getLsDataSlocAddress.map<Widget>((DataSlocAddress sloc) {
                                        return Text(
                                          sloc.slocDisplay.toString(),
                                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                          overflow: TextOverflow.ellipsis,
                                        );
                                      }).toList();
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: _errorSelectedSloc ? 5.h : 0),
                              if (_errorSelectedSloc)
                                Text(
                                  "Vui lòng chọn Sloc",
                                  style: TextStyle(color: Colors.red, fontSize: 10.sp),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Warehouse No field
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: const BoxDecoration(color: Colors.white),
                child: IntrinsicHeight(
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        flex: 4,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xff303F9F),
                            border: Border(
                              left: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Text(
                            "Warehouse No:",
                            style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            border: Border(
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Text(
                            _selectedSloc == null ? "" : _selectedSloc!.warehouseNo ?? "",
                            style: TextStyle(fontSize: 11.sp, color: Colors.black),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Storage Bin field
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: const BoxDecoration(color: Colors.white),
                child: IntrinsicHeight(
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        flex: 4,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xff303F9F),
                            border: Border(
                              left: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Text(
                            "Storage Bin:",
                            style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Container(
                          height: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            border: Border(
                              right: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                              bottom: BorderSide(
                                color: Colors.black,
                                width: 0.5.w,
                              ),
                            ),
                          ),
                          child: Column(
                            children: <Widget>[
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: Stack(
                                  children: [
                                    TextFormField(
                                      maxLines: null,
                                      enabled: _selectedSloc != null,
                                      textAlign: TextAlign.center,
                                      controller: _locationController,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        focusedBorder: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        filled: true,
                                        isDense: true,
                                        fillColor: Colors.white,
                                        hintStyle: TextStyle(fontSize: 12.sp),
                                      ),
                                      onChanged: (value) {
                                        _onStorageBinChanged(value);
                                      },
                                    ),
                                    Visibility(
                                      visible: _selectedSloc != null,
                                      child: Positioned(
                                        top: 0,
                                        right: 5,
                                        child: InkWell(
                                          onTap: () async {
                                            final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
                                            if (data == null) return;
                                            if (!mounted) return;

                                            var storageBin = data as DataSlocAddress;
                                            if (storageBin.defaultStorageBin != null) {
                                              setState(() {
                                                _locationController.text = storageBin.defaultStorageBin!;
                                                _storageBinID = storageBin.defaultStorageBinId!;
                                              });
                                            }
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(4.0),
                                            child: Icon(Icons.camera_alt_outlined, size: 20.sp, color: Colors.black),
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(height: _lsDataGetStorageBn.isNotEmpty || _isLoadingStorageBin ? 5.h : 0),
                              if (_isLoadingStorageBin)
                                const Center(child: CircularProgressIndicator())
                              else if (_lsDataGetStorageBn.isNotEmpty)
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.5),
                                        spreadRadius: 2,
                                        blurRadius: 7,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: _lsDataGetStorageBn.map((bin) {
                                      return InkWell(
                                        onTap: () => _setFilter(bin),
                                        child: Container(
                                          width: double.infinity,
                                          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                                          child: Text(
                                            bin.value ?? "",
                                            style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              SizedBox(height: _errorStorageBin ? 5.h : 0),
                              if (_errorStorageBin)
                                Text(
                                  "Vui lòng nhập storage Bin",
                                  style: TextStyle(color: Colors.red, fontSize: 10.sp),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10.h),
            ],
          ),
        ),

        // SECTION III: CHI TIẾT NHẬP KHO
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "III. CHI TIẾT NHẬP KHO",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),

              // Display quantity information
              if (_scannedProducts.isNotEmpty)
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8.h),
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(5.r),
                    // border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Thông tin đã quét:",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          // decoration: TextDecoration.underline,
                        ),
                      ),
                      SizedBox(height: 5.h),
                      ..._getScannedPOSummary()
                          .map((poSummary) => Padding(
                                padding: EdgeInsets.only(bottom: 3.h),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "LSX: ${poSummary.poNumber}",
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        // fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          "Sẽ nhập: ${_scannedProducts.firstWhere((p) => p.sapOrder == poSummary.poNumber).importQuantity}",
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            // fontWeight: FontWeight.bold,
                                            color: poSummary.remainingCount <= 0 ? Colors.green[700] : Colors.black,
                                          ),
                                        ),
                                        Text(
                                          " / ${poSummary.totalCount}",
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ))
                          .toList(),
                    ],
                  ),
                ),

              // Scan QR button for products
              Container(
                padding: EdgeInsets.only(bottom: 0.h, top: 5.h),
                decoration: const BoxDecoration(color: Colors.white),
                width: double.infinity,
                alignment: Alignment.center,
                child: ElevatedButton.icon(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                    side: MaterialStateProperty.all(
                      const BorderSide(color: Color(0xff303F9F)),
                    ),
                    backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                  ),
                  onPressed: () async {
                    // Validate required fields before opening scanner
                    if (_selectedSalesOrgCodes == null) {
                      showToast(
                        context: context,
                        message: "Vui lòng chọn Plant trước khi quét",
                        duration: 2,
                      );
                      return;
                    }

                    if (_selectedProductGroup == null) {
                      showToast(
                        context: context,
                        message: "Vui lòng chọn nhóm hàng trước khi quét",
                        duration: 2,
                      );
                      return;
                    }

                    if (_selectedSloc == null) {
                      setState(() {
                        _errorSelectedSloc = true;
                      });
                      showToast(
                        context: context,
                        message: "Vui lòng chọn Sloc trước khi quét",
                        duration: 2,
                      );
                      return;
                    }

                    // if (_selectedSloc!.warehouseNo != null &&
                    //     _selectedSloc!.warehouseNo!.isNotEmpty &&
                    //     (_locationController.text.isEmpty || _storageBinID == null)) {
                    //   setState(() {
                    //     _errorStorageBin = true;
                    //   });
                    //   showToast(
                    //     context: context,
                    //     message: "Vui lòng nhập Storage Bin trước khi quét",
                    //     duration: 2,
                    //   );
                    //   return;
                    // }

                    // Navigate to the continuous scanner page
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ContinuousQRScannerPage(
                          token: widget.token,
                          permission: widget.permission!,
                          plant: widget.plant,
                          dateTimeOld: widget.dateTimeOld,
                          batch: _batchController.text,
                          location: _locationController.text,
                          scannedProducts: _scannedProducts,
                          poInfoCache: _poInfoCache,
                          onProductScanned: (ScannedProduct product) {
                            setState(() {
                              _scannedProducts.add(product);
                            });
                          },
                        ),
                      ),
                    );

                    // Update the cache with any new entries from the scanner page
                    if (result != null && result is Map<String, dynamic>) {
                      setState(() {
                        // Update the PO info cache
                        if (result['poInfoCache'] != null && result['poInfoCache'] is Map<String, POInfo>) {
                          int oldSize = _poInfoCache.length;
                          _poInfoCache.addAll(result['poInfoCache'] as Map<String, POInfo>);
                          int newSize = _poInfoCache.length;
                          debugPrint('Updated cache from scanner page: $oldSize -> $newSize entries');
                        }

                        // Update the PO quantities map
                        if (result['poQuantities'] != null && result['poQuantities'] is Map<String, int>) {
                          _poQuantities.addAll(result['poQuantities'] as Map<String, int>);
                          debugPrint('Updated PO quantities from scanner page: ${_poQuantities.length} entries');
                        }
                      });
                    }
                  },
                  icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                  label: Text(
                    'Quét TP',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                  ),
                ),
              ),

              // DEBUG SECTION - Mock QR Scan Buttons
              kDebugMode
                  ? Container(
                      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.h),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(5.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(bottom: 8.h),
                            child: Text(
                              "DEBUG: Simulate QR Scan",
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ),
                          Text(
                            "PO: 3500010156 | Mã TP: 550010609 | Tên TP: Ghế Harrison Chair-Alcala Wheat",
                            style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 5.h),
                          Wrap(
                            spacing: 8.w,
                            runSpacing: 8.h,
                            children: [
                              _buildDebugButton("350001015618616", tooltip: "PO: 3500010156 + 18616"),
                              _buildDebugButton("3500010156121E0", tooltip: "PO: 3500010156 + 121E0"),
                              _buildDebugButton("350001015684722", tooltip: "PO: 3500010156 + 84722"),
                              _buildDebugButton("3500010156E49F3", tooltip: "PO: 3500010156 + E49F3"),
                              _buildDebugButton("3500010156FDE25", tooltip: "PO: 3500010156 + FDE25"),
                              _buildDebugButton("350001015620925", tooltip: "PO: 3500010156 + 20925"),
                              _buildDebugButton("350001015635606", tooltip: "PO: 3500010156 + 35606"),
                            ],
                          ),
                          SizedBox(height: 10.h),
                          Text(
                            "PO: 3500010604 | Mã TP: 550018455 | Tên TP: Sofa 2 chỗ_1512-S_CMO_WR_BARKER ST",
                            style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 5.h),
                          Wrap(
                            spacing: 8.w,
                            runSpacing: 8.h,
                            children: [
                              _buildDebugButton("350001060434437", background: Colors.blue, tooltip: "PO: 3500010604 + 34437"),
                              _buildDebugButton("3500010604D0A64", background: Colors.blue, tooltip: "PO: 3500010604 + D0A64"),
                              _buildDebugButton("35000106043B519", background: Colors.blue, tooltip: "PO: 3500010604 + 3B519"),
                            ],
                          ),
                          Wrap(
                            spacing: 8.w,
                            runSpacing: 8.h,
                            children: [
                              _buildDebugButton("380000000015857", background: Colors.green, tooltip: "PO: 3800000000 + 15857"),
                              _buildDebugButton("38000000006DE01", background: Colors.green, tooltip: "PO: 3800000000 + 6DE01"),
                            ],
                          ),
                        ],
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        ),
        Container(
          padding: EdgeInsets.only(bottom: 5.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Container(
            padding: EdgeInsets.only(left: 10.w),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 30.w, bottom: 15.h),
                    child: Table(
                      border: TableBorder.all(width: 0.5.w),
                      columnWidths: <int, TableColumnWidth>{
                        0: FixedColumnWidth(90.w), // LSX SAP
                        1: FixedColumnWidth(75.w), // Mã TP
                        2: FixedColumnWidth(200.w), // Tên TP
                        3: FixedColumnWidth(120.w), // Số lượng
                        4: FixedColumnWidth(85.w), // Batch
                        5: FixedColumnWidth(100.w), // Vị trí
                        6: FixedColumnWidth(80.w), // Chức năng
                      },
                      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                      children: [
                        TableRow(
                          decoration: const BoxDecoration(
                            color: Color(0xff303F9F),
                          ),
                          children: <Widget>[
                            _buildTableHeader("LSX SAP"),
                            _buildTableHeader("Mã TP"),
                            _buildTableHeader("Tên TP"),
                            _buildTableHeader("Số lượng"),
                            _buildTableHeader("Batch"),
                            _buildTableHeader("Vị trí"),
                            _buildTableHeader("Chức năng"),
                          ],
                        ),
                        ..._scannedProducts
                            .asMap()
                            .map((index, product) => MapEntry(
                                index,
                                TableRow(
                                  children: <Widget>[
                                    _buildTableCell(product.sapOrder),
                                    _buildTableCell(product.productCode),
                                    _buildTableCell(product.productName, textAlign: TextAlign.left),
                                    _buildQuantityCell(index, product),
                                    _buildTableCell(product.batch),
                                    _buildTableCell(product.location),
                                    _buildDeleteButton(index),
                                  ],
                                )))
                            .values
                            .toList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // Add submit button
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
          child: GestureDetector(
            onTap: _scannedProducts.isEmpty ? null : _validateAndSubmit,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 15.h),
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(5.r),
                ),
                color: _scannedProducts.isEmpty ? Colors.grey.shade400 : const Color(0xff0052cc),
              ),
              child: Center(
                child: Text(
                  'Lưu',
                  style: TextStyle(color: Colors.white, fontSize: 14.sp, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 250.h),
      ],
    );
  }

  Widget _buildTableHeader(String text) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }

  // Text, and Align
  Widget _buildTableCell(String text, {TextAlign? textAlign}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp),
        textAlign: textAlign ?? TextAlign.center,
      ),
    );
  }

  Widget _buildQuantityCell(int index, ScannedProduct product) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Column(
        children: [
          Container(
            width: 80.w,
            child: TextFormField(
              initialValue: product.importQuantity.toString(),
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.r),
                ),
                isDense: true,
              ),
              onChanged: (value) {
                int? newQuantity = int.tryParse(value);
                if (newQuantity != null && newQuantity > 0 && newQuantity <= product.remainingQuantity) {
                  setState(() {
                    _scannedProducts[index].importQuantity = newQuantity;
                  });
                }
              },
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            "Max: ${product.remainingQuantity}",
            style: TextStyle(fontSize: 10.sp, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  // Add this method to build debug buttons
  Widget _buildDebugButton(String serial, {Color? background, String? tooltip}) {
    return Tooltip(
      message: tooltip ?? serial,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(background ?? Colors.amber[700]),
          padding: MaterialStateProperty.all(EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h)),
          textStyle: MaterialStateProperty.all(TextStyle(fontSize: 10.sp)),
        ),
        onPressed: () async {
          await _addScannedProduct(serial);
        },
        child: Text(serial),
      ),
    );
  }

  List<POSummary> _getScannedPOSummary() {
    // Create a summary for each scanned product (now 1:1 with PO)
    return _scannedProducts.map((product) {
      debugPrint(
          'PO Summary - ${product.sapOrder}: imported=${product.importQuantity}, total=${product.totalQuantity}, remaining=${product.remainingQuantity}');

      return POSummary(
        poNumber: product.sapOrder,
        totalCount: product.totalQuantity,
        importedCount: product.importedQuantity + product.importQuantity, // Current imported + what we're adding
        remainingCount: product.remainingQuantity - product.importQuantity, // What will remain after this import
      );
    }).toList();
  }

  Widget _buildDeleteButton(int index) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: IntrinsicHeight(
        child: Container(
          child: Align(
            alignment: Alignment.center,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                setState(() {
                  _scannedProducts.removeAt(index);
                });
              },
            ),
          ),
        ),
      ),
    );
  }

  // Validate and save the data
  Future<void> _validateAndSubmit() async {
    // Validate required fields
    if (_selectedSalesOrgCodes == null) {
      showToast(
        context: context,
        message: "Vui lòng chọn Plant",
        duration: 2,
      );
      return;
    }

    if (_selectedSloc == null) {
      setState(() {
        _errorSelectedSloc = true;
      });
      showToast(
        context: context,
        message: "Vui lòng chọn Sloc",
        duration: 2,
      );
      return;
    }

    // if (_selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo!.isNotEmpty && (_locationController.text.isEmpty || _storageBinID == null)) {
    //   setState(() {
    //     _errorStorageBin = true;
    //   });
    //   showToast(
    //     context: context,
    //     message: "Vui lòng nhập Storage Bin",
    //     duration: 2,
    //   );
    //   return;
    // }

    if (_scannedProducts.isEmpty) {
      showToast(
        context: context,
        message: "Vui lòng quét ít nhất một sản phẩm",
        duration: 2,
      );
      return;
    }

    // Create the data object for saving
    final importData = FinishedProductImport(
      plant: _selectedSalesOrgCodes!.saleOrgCode ?? "",
      sloc: _warehouseController.text,
      slocId: _selectedSloc?.slocId,
      storageBin: _locationController.text,
      storageBinId: _storageBinID,
      batch: _batchController.text,
      products: _scannedProducts,
    );

    try {
      // setState(() {
      //   _isSaving = true;
      // });

      // Call the API to import finished products
      await ImportWareHouseFunction.importFinishedProduct(importData.toJson(), widget.token, context);

      // if (!mounted) return;

      // setState(() {
      //   _isSaving = false;
      // });

      // Show success message
      // showToast(
      //   context: context,
      //   message: "Nhập kho thành công",
      //   duration: 2,
      // );

      // Clear scanned products after successful save
      // setState(() {
      //   _scannedProducts = [];
      // });
    } catch (error) {
      if (!mounted) return;

      // Show error message
      showToast(
        context: context,
        message: "Lỗi: ${error.toString()}",
        duration: 3,
      );

      // setState(() {
      //   _isSaving = false;
      // });
    }
  }
}

class ScannedProduct {
  final String sapOrder;
  final String productCode;
  final String productName;
  final String batch;
  final String location;
  final String so;
  final String soItem;
  final String wbs;
  final String unit;
  int importQuantity;
  final int totalQuantity;
  final int importedQuantity;

  ScannedProduct({
    required this.sapOrder,
    required this.productCode,
    required this.productName,
    required this.batch,
    required this.location,
    required this.so,
    required this.soItem,
    required this.wbs,
    required this.unit,
    required this.importQuantity,
    required this.totalQuantity,
    required this.importedQuantity,
  });

  int get remainingQuantity => totalQuantity - importedQuantity;
  bool get canImportMore => remainingQuantity > 0;
}

class POSummary {
  final String poNumber;
  final int totalCount;
  final int importedCount;
  final int remainingCount;

  POSummary({
    required this.poNumber,
    required this.totalCount,
    required this.importedCount,
    required this.remainingCount,
  });
}

class FinishedProductImport {
  final String plant;
  final String sloc;
  final String? slocId;
  final String? storageBin;
  final String? storageBinId;
  final String batch;
  final List<ScannedProduct> products;

  FinishedProductImport({
    required this.plant,
    required this.sloc,
    required this.slocId,
    required this.storageBin,
    required this.storageBinId,
    required this.batch,
    required this.products,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['slocId'] = slocId;
    data['storageBin'] = storageBin;
    data['storageBinId'] = storageBinId;
    data['batch'] = batch;
    data['products'] = products
        .map((product) => {
              'sapOrder': product.sapOrder,
              'productCode': product.productCode,
              'productName': product.productName,
              'importQuantity': product.importQuantity,
              'batch': product.batch,
              'location': product.location,
              'so': product.so,
              'soItem': product.soItem,
              'wbs': product.wbs,
              'unit': product.unit,
            })
        .toList();
    return data;
  }
}
