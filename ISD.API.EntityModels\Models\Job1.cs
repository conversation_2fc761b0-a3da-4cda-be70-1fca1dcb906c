﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Job", Schema = "TTF_MES")]
    public partial class Job1
    {
        public Job1()
        {
            JobParameter1 = new HashSet<JobParameter1>();
            State1 = new HashSet<State1>();
        }

        [Key]
        public long Id { get; set; }
        public long? StateId { get; set; }
        [StringLength(20)]
        public string StateName { get; set; }
        [Required]
        public string InvocationData { get; set; }
        [Required]
        public string Arguments { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedAt { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpireAt { get; set; }

        [InverseProperty("Job")]
        public virtual ICollection<JobParameter1> JobParameter1 { get; set; }
        [InverseProperty("Job")]
        public virtual ICollection<State1> State1 { get; set; }
    }
}