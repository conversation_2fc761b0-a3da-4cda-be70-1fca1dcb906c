﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Profile_Opportunity_MaterialModel", Schema = "Customer")]
    public partial class Profile_Opportunity_MaterialModel
    {
        [Key]
        public Guid OpportunityMaterialId { get; set; }
        public Guid? ProfileId { get; set; }
        [StringLength(500)]
        public string MaterialCode { get; set; }
        /// <summary>
        /// 1: Nội thất bàn giao
        /// </summary>
        public int? MaterialType { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }

        [ForeignKey("ProfileId")]
        [InverseProperty("Profile_Opportunity_MaterialModel")]
        public virtual ProfileModel Profile { get; set; }
    }
}