﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductModel", Schema = "tSale")]
    public partial class ProductModel
    {
        public ProductModel()
        {
            AccessoryProductModel = new HashSet<AccessoryProductModel>();
            ColorProductModel = new HashSet<ColorProductModel>();
            DeliveryDetailModel = new HashSet<DeliveryDetailModel>();
            ImageProductModel = new HashSet<ImageProductModel>();
            PriceProductModel = new HashSet<PriceProductModel>();
            ProductWarrantyModel = new HashSet<ProductWarrantyModel>();
            PropertiesProductModel = new HashSet<PropertiesProductModel>();
            SpecificationsProductModel = new HashSet<SpecificationsProductModel>();
            StockReceivingDetailModel = new HashSet<StockReceivingDetailModel>();
            StockTransferRequestDetailModel = new HashSet<StockTransferRequestDetailModel>();
            TemplateAndGiftMemberAddressModel = new HashSet<TemplateAndGiftMemberAddressModel>();
            TransferDetailModel = new HashSet<TransferDetailModel>();
            WarehouseProductModel = new HashSet<WarehouseProductModel>();
            PeriodicallyChecking = new HashSet<PeriodicallyCheckingModel>();
            PlateFee = new HashSet<PlateFeeModel>();
            Promotion = new HashSet<CustomerPromotionModel>();
            PromotionNavigation = new HashSet<PromotionModel>();
        }

        [Key]
        public Guid ProductId { get; set; }
        [Required]
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(50)]
        public string ERPProductCode { get; set; }
        [Required]
        [StringLength(4000)]
        public string ProductName { get; set; }
        public Guid? BrandId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? CylinderCapacity { get; set; }
        public Guid? CategoryId { get; set; }
        public Guid ConfigurationId { get; set; }
        [StringLength(50)]
        public string GuaranteePeriod { get; set; }
        [StringLength(100)]
        public string ImageUrl { get; set; }
        public bool? isHot { get; set; }
        public int? OrderIndex { get; set; }
        public bool Actived { get; set; }
        public Guid? ParentCategoryId { get; set; }
        public Guid? CompanyId { get; set; }
        public bool? isInventory { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        public Guid? WarrantyId { get; set; }
        [StringLength(50)]
        public string MEINS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VOLUM { get; set; }
        [StringLength(50)]
        public string VOLEH { get; set; }
        [StringLength(50)]
        public string GROES { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? BRGEW { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? NTGEW { get; set; }
        [StringLength(50)]
        public string GEWEI { get; set; }
        [StringLength(100)]
        public string SLCARTON { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(50)]
        public string MTART { get; set; }
        [StringLength(255)]
        public string Z_MAUSAC { get; set; }

        [InverseProperty("Product")]
        public virtual ICollection<AccessoryProductModel> AccessoryProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<ColorProductModel> ColorProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<DeliveryDetailModel> DeliveryDetailModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<ImageProductModel> ImageProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<PriceProductModel> PriceProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<ProductWarrantyModel> ProductWarrantyModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<PropertiesProductModel> PropertiesProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<SpecificationsProductModel> SpecificationsProductModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<StockReceivingDetailModel> StockReceivingDetailModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<StockTransferRequestDetailModel> StockTransferRequestDetailModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<TemplateAndGiftMemberAddressModel> TemplateAndGiftMemberAddressModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<TransferDetailModel> TransferDetailModel { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<WarehouseProductModel> WarehouseProductModel { get; set; }

        [ForeignKey("ProductId")]
        [InverseProperty("Product")]
        public virtual ICollection<PeriodicallyCheckingModel> PeriodicallyChecking { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("Product")]
        public virtual ICollection<PlateFeeModel> PlateFee { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("Product")]
        public virtual ICollection<CustomerPromotionModel> Promotion { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("Product")]
        public virtual ICollection<PromotionModel> PromotionNavigation { get; set; }
    }
}