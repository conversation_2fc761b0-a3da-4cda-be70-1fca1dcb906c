﻿using ISD.API.Constant.Common;
using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using SAPGetStock;
using SAPGetThongTinTheTreo;
using System;
using System.Linq;
using System.Threading.Tasks;
using MaterialType = ISD.API.Constant.MESP2.MaterialType;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class BatchController : ControllerBaseAPI
    {
        #region Get số lô tự động
        /// <summary>Get số lô tự động</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v1/MES/Batch/get-auto-batch
        ///     Params: 
        ///             + version : 1
        ///             
        ///             //Loại NVL là thành phẩm (TP) và NVL không QL Kiện (NVLKK)
        ///             {
        ///                 "materialType": "TP" || "NVLKK",           Loại NVL  
        ///                 "importExportType": "N",                   NVL Nội địa/Nhập khẩu
        ///                 "materialGroup": "XK",                     Nhóm hàng
        ///                 "materialStatus": "D"                      Tình trạng hàng hóa
        ///             }
        ///             
        ///             //Loại NVL là hóa chất (HC)
        ///             {
        ///                 "materialType": "HC",                      Loại NVL  
        ///                 "importExportType": "I",                   NVL Nội địa/Nhập khẩu
        ///                 "materialGroup": "XK",                     Nhóm hàng
        ///                 "materialStatus": "D",                     Tình trạng hàng hóa
        ///                 "nsxhsd": "1"                              Số chạy 1->9 dùng để phân biệt NSX và HSD
        ///             }
        ///             
        ///             //Loại NVL là Da (Da)
        ///             {
        ///                 "materialType": "DA",                      Loại NVL  
        ///                 "importExportType": "I",                   NVL Nội địa/Nhập khẩu
        ///                 "skinColor": "BY",                         Màu da 
        ///                 "skinType": "L",                           Chủng loại Da
        ///                 "materialStatus": "D"                      Tình trạng hàng hóa
        ///             }
        ///             
        /// 
        ///             //Loại NVL là gỗ/Ván (có quản lý kiện)
        ///             {
        ///                 "materialType": "KIEN",                    Loại NVL  
        ///                 "caseCode": "D"                            Số mã kiện
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                  "code": 200,
        ///                  "isSuccess": true,
        ///                  "data": {
        ///                      "batchNumber": "NXK22821DH"
        ///                  },
        ///                  "additionalData": null
        ///              }
        /// </remarks>
        [HttpPost("get-auto-batch")]
        public IActionResult GetAutoBatch([FromBody] GetAutoBatchRequest request)
        {
            var response = new GetAutoBatchResponse();

            var plant = CurrentUser?.CompanyCode;

            string plantCode = null;

            //Convert plant number to string theo quy tắc đặt số lô
             
            if (plant == "1000")
            {
                plantCode = "H";
            }

            if (plant == "1200")
            {
                plantCode = "S";
            }

            if (plant == "1100")
            {
                plantCode = "C";
            }

            var dateNow = DateTime.Now;
            var monthNow = dateNow.ToString("MM");
            var dayNow = dateNow.ToString("dd");
            var yearNow = dateNow.ToString("yy");
            var monthHd = monthNow.Substring(1, 1);

            //Nếu tháng hiện tại là 10,11,12. Giá trị của tháng sẽ là chữ cái đầu tiên theo tiếng Anh. October => O
            if (dateNow.Month == 10 || dateNow.Month == 11 || dateNow.Month == 12)
            {
                monthNow = dateNow.ToString("MMMM");
                monthHd = monthNow.Substring(0, 1);
            }

            //Loại NVL là Thành phẩm hoặc NVL  không QL kiện
            if (request.MaterialType == MaterialType.FinishedProduct || request.MaterialType == MaterialType.RawMaterialUnmanaged)
            {
                //Số lô
                response.BatchNumber = $"{request.ImportExportType}{request.MaterialGroup}{dayNow}{monthHd}{yearNow}{request.MaterialStatus}{plantCode}";
            }

            //Loại NVL là hóa chất
            if (request.MaterialType == MaterialType.Chemistry)
            {
                //Số lô
                response.BatchNumber = $"{request.ImportExportType}{request.MaterialGroup}{dayNow}{monthHd}{yearNow}{request.MaterialStatus}{request.NSXHSD}";
            }
            //Loại NVL là Da
            if (request.MaterialType ==  MaterialType.Skin)
            {
                //Số lô
                response.BatchNumber = $"{request.ImportExportType}{request.SkinColor}{dayNow}{monthHd}{yearNow}{request.SkinType}{request.MaterialStatus}";
            }
            //Loại NVL là Gỗ/Ván có quản lý kiện
            if (request.MaterialType == MaterialType.WoodBoardManagement)
            {
                //Số lô
                response.BatchNumber = $"{request.ImportExportType}{yearNow}{monthHd}{request.CaseCode}";
            }


            return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
        }
        #endregion

        #region GET số lượng tồn kho theo nvl và sloc
        /// <summary>
        /// GET số lượng tồn kho theo nvl và sloc
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("get-inventory-material-sloc")]
        public async Task<IActionResult> GetInventoryByMaterialSloc([FromBody] GetInventoryByMaterialSlocRequest request)
        {
            //Call SAP get số lượng tồn kho theo material code và sloc
            var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
            {
                LGORT = request.Sloc,
                MATNR = request.ProductCode,
                WERKS = CurrentUser?.CompanyCode
            });

            var querySelect = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.LABST > 0).Select(x => new GetInventoryByMaterialSlocResponse
            {
                //Số lượng tồn
                Quantity = x.LABST,
                //SO/SOLine or WBS
                SOWBS = x.SSNUM.Replace(" ", ""),
                //Số lô
                Batch = x.CHARG
            }).ToList();

            //var data = querySelect.GroupBy(e => e.Batch, (k, v) => new { Batch = k, Data = v.ToList() }).ToList();
            
            return Ok(new ApiResponse { Code = 200, Data = querySelect, Message = string.Format(CommonResource.Msg_Succes, "GET số lượng tồn")});
        }
        #endregion

        /// <summary>
        /// GET thông tin thẻ treo
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("get-info-pallet")]
        public async Task<IActionResult> GetInfoPallet([FromBody] ZMES_PRODU_ORDER_EXT_API request)
        {
            var responseSAP = await _unitOfWork.SAPAPIRepository.GetThongTinTheTreo(request);
            var data = responseSAP.ZMES_PRODU_ORDER_EXT_APIResponse.IT_PROD_ORDER_EXT.FirstOrDefault();

            var response = new GetThongTinTheTreoResponse
            {
                SoPOKhachHang = data?.BSTKD_E,
                TTMoiTruong = ""
            };

            return Ok(new ApiResponse { Code = 200, Data = response, Message = string.Format(CommonResource.Msg_Succes, "GET THÔNG TIN THẺ TREO") });
        }


        /// <summary>
        /// Hủy chứng từ nhập kho MES
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("cancel-document-mes")]
        public async Task<IActionResult> CancelDocumentMESAsync([FromBody] CancelDocumentMESRequest request)
        {
            //Tìm chứng từ đã nhập kho
            var receiveNoteSAP = await _context.WarehouseTransactionModel.FirstOrDefaultAsync(x => x.GoodsReceivedNote == request.GoodsReceivedNote && x.MovementType == MovementType.Receive);

            if (receiveNoteSAP is null)
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Phiếu nhập kho") });

            //Thẻ treo
            var pallet = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == receiveNoteSAP.ReferenceDocumentId);

            //Hủy chứng từ nhập kho của thẻ treo
            pallet.IsReceive = false;

            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, Data = true, IsSuccess = true, Message = string.Format(CommonResource.Msg_Succes, "Hủy chứng từ nhập kho MES") });
        }
    }
}
