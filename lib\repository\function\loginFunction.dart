import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:ttf/model/qualityControlApi.dart';
import '../../Storage/storageSecureStorage.dart';
import '../../Storage/storageSharedPreferences.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../model/saveAccountLogin.dart';
import '../../model/userModel.dart';
import '../../utils/user_helper.dart';
import '../api/userLogin.dart';

class LoginFunction {
  static bool checkIsSend = false;
  static GetListCompanyByUserName defaultValue = GetListCompanyByUserName(companyCode: ' ', companyName: '-- Công ty --');
  static const Duration loginTimeout = Duration(seconds: 120);

  static Future<ConnectivityResult> checkConnectNetwork() async {
    final result = await Connectivity().checkConnectivity();
    return result;
  }

  static Future<void> setCurrentUser(
    String user,
    String company,
    String formattedDate,
    String saleOrg,
    GetListCompanyByUserName? selectedCompany,
    DropdownItemList? selectedEnvironment,
    String keyUser,
    String keyAccount,
    String keyPassword,
    String keyDateTimeNow,
    String keyCompany,
    String keySaleOrg,
    String keySelectedCompany,
    String keySelectedEnvironment,
    String userName,
    String password,
  ) async {
    await Future.wait([
      SecureStorage.setString(
        user,
        keyUser,
        userName.isNotEmpty ? userName : null,
      ),
      SecureStorage.setString(
          jsonEncode(SaveAccountLogin(
            userName: userName,
            password: password,
            company: company,
            saleOrg: saleOrg,
            selectedCompany: selectedCompany,
            selectedEnvironment: selectedEnvironment,
          )),
          "saveAccountLogin",
          userName.isNotEmpty ? userName : null),
      // SecureStorage.setString(
      //     userName, keyAccount, userName.isNotEmpty ? userName : null),
      // SecureStorage.setString(
      //     password, keyPassword, userName.isNotEmpty ? userName : null),
      // SecureStorage.setString(
      //     company, keyCompany, userName.isNotEmpty ? userName : null),
      // SecureStorage.setString(
      //     saleOrg, keySaleOrg, userName.isNotEmpty ? userName : null),
      // SecureStorage.setString(jsonEncode(selectedCompany!.toJson()),
      //     keySelectedCompany, userName.isNotEmpty ? userName : null),
      StorageSharedPreferences.setString(keyDateTimeNow, formattedDate),
    ]);
  }

  static Future<List<GetListCompanyByUserName>?> fetchCompany(BuildContext context, String userName) async {
    checkIsSend = false;
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => WillPopScope(
        onWillPop: () async => false,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
    final value = await UserLogin.getListCompanyUsername(userName);
    Navigator.pop(context);
    checkIsSend = true;
    if (value.statusCode == 200) {
      final responseData = jsonDecode(value.body);
      if (responseData != false) {
        final companyData = List<GetListCompanyByUserName>.from(responseData.map((i) => GetListCompanyByUserName.fromJson(i)));
        return companyData;
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Tên tài khoản không tồn tại! Vui lòng kiểm tra lại tên tài khoản',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
        return null;
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! vui lòng đăng nhập lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 2)));
      return null;
    }
  }

  static Future<GetSaleOrgByCompanyId?> fetchSaleOrg(String companyCode, String userName) async {
    final value = await UserLogin.getSaleOrgByCompanyId(companyCode, userName);
    if (value.body != "") {
      final responseData = jsonDecode(value.body);
      if (value.statusCode == 200) {
        final saleOrgData = GetSaleOrgByCompanyId.fromJson(responseData[0]);
        return saleOrgData;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  // static Future<List<String>> fetchAndSaveRoutingEnabledCompanyCodes() async {
  //   try {
  //     final response = await UserLogin.getRoutingEnabledCompanyCodes().timeout(
  //       loginTimeout,
  //       onTimeout: () {
  //         throw TimeoutException('Fetch routing-enabled company codes timeout');
  //       },
  //     );

  //     if (response.statusCode == 200) {
  //       final responseData = jsonDecode(response.body);

  //       if (responseData != null && responseData is List) {
  //         // Extract company codes from the response
  //         final List<String> companyCodes = List<String>.from(responseData.map((code) => code.toString()));

  //         // Save the list to SecureStorage
  //         await UserHelper.saveRoutingEnabledCompanyCodes(companyCodes);

  //         debugPrint("Saved routing-enabled company codes: $companyCodes");
  //         return companyCodes;
  //       }
  //     }

  //     // If the API call fails or returns unexpected data, return default values
  //     debugPrint("Failed to fetch routing-enabled company codes, using defaults");
  //     return const ['1000', '1100', '1010', '1200'];
  //   } catch (e) {
  //     debugPrint("Error fetching routing-enabled company codes: $e");
  //     return const ['1000', '1100', '1010', '1200'];
  //   }
  // }

  static Future<UserModel?> login(
    BuildContext context,
    String userName,
    String password,
    String? companyCode,
    List<GetListCompanyByUserName> companyData,
    GetListCompanyByUserName? selectedCompany,
    DropdownItemList? selectedEnvironment,
    String keyUser,
    String keyAccount,
    String keyPassword,
    String keyDateTimeNow,
    String keyCompany,
    String keySaleOrg,
    String keySelectedCompany,
    String keySelectedEnvironment,
  ) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      final dataSaleOrg = await fetchSaleOrg(companyCode.toString(), userName).timeout(
        loginTimeout,
        onTimeout: () {
          throw TimeoutException('Login timeout');
        },
      );

      if (dataSaleOrg == null) {
        Navigator.pop(context);
        checkIsSend = true;
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            "Không tìm thấy thông tin SaleOrg! vui lòng đăng nhập lại sau",
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1),
        ));
        return null;
      } else {
        final value = await UserLogin.performLogin(userName, password, dataSaleOrg.saleOrgCode!, companyCode.toString()).timeout(
          loginTimeout,
          onTimeout: () {
            throw TimeoutException('Login timeout');
          },
        );

        final decodedBody = jsonDecode(value.body);

        if (value.statusCode == 200) {
          if (decodedBody['code'] == 200 && decodedBody['isSuccess'] == true) {
            // Login successful, fetch routing-enabled company codes
            // await fetchAndSaveRoutingEnabledCompanyCodes();

            UserModel dataUser = UserModel.fromJson(decodedBody);

            String formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
            if (Platform.isIOS) {
              // iOS
              await SecureStorage.removeAll(null);
            } else {}

            await LoginFunction.setCurrentUser(
                jsonEncode(dataUser.toJson()),
                jsonEncode(companyData),
                formattedDate,
                jsonEncode(dataSaleOrg.toJson()),
                selectedCompany,
                selectedEnvironment,
                keyUser,
                keyAccount,
                keyPassword,
                keyDateTimeNow,
                keyCompany,
                keySaleOrg,
                keySelectedCompany,
                keySelectedEnvironment,
                userName,
                password);

            OneSignal.login(userName); // username
            if (dataUser.data!.employeeCode != null && dataUser.data!.employeeCode!.isNotEmpty) {
              OneSignal.User.addAlias("employee_code", dataUser.data!.employeeCode);
            }

            Navigator.pop(context);
            checkIsSend = true;
            return dataUser;
          } else {
            // Error login
            Navigator.pop(context);
            checkIsSend = true;
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                decodedBody["message"],
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1),
            ));
            return null;
          }
        } else if (value.statusCode == 400) {
          Navigator.pop(context);
          checkIsSend = true;
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: decodedBody["message"]));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     "Đăng nhập thất bại!",
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          //   duration: const Duration(seconds: 1),
          // ));
          return null;
        } else {
          Navigator.pop(context);
          checkIsSend = true;
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: decodedBody["message"]));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     "Xảy ra lỗi! vui lòng đăng nhập lại sau",
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          //   duration: const Duration(seconds: 1),
          // ));
          return null;
        }
      }
    } on TimeoutException catch (_) {
      await SecureStorage.removeSecure("user", null);
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => const DialogError(message: 'Máy chủ đang bận! (Timeout)'),
      );
      return null;
    } on SocketException catch (_) {
      // if(!mounted) return;
      // setState(() {
      //   _checkCatchWifi = true;
      //   _checkCatchError = false;
      // });
      await SecureStorage.removeSecure("user", null);
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      return null;
    } catch (error) {
      //
      // if(!mounted) return;
      // setState(() {
      //   _checkCatchError = true;
      //   _checkCatchWifi = false;
      // });
      await SecureStorage.removeSecure("user", null);
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Máy chủ không phản hồi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      return null;
    }
  }

  // static  Future<void> _userLogin(BuildContext context) async {
  //
  //   try{
  //     // setState(() {
  //     //   _checkIsSendLogin = false;
  //     //   // _checkCatchWifi = false;
  //     //   // _checkCatchError = false;
  //     // });
  //     showDialog<String>(
  //       barrierDismissible: false,
  //       context: context,
  //       builder: (BuildContext context) =>
  //           WillPopScope(
  //             onWillPop: () async => false,
  //             child: const Center(
  //               child: CircularProgressIndicator(),
  //             ),
  //           ),
  //     );
  //
  //     final user = await LoginFunction.login(context,
  //         _saleOrgData,
  //         _controllerUserName.text,
  //         _controllerPassWord.text,
  //         _companyCode,
  //         _companyData,
  //         _selectedCompany,
  //         _keyUser,
  //         _keyAccount,
  //         _keyPassword,
  //         _keyDateTimeNow,
  //         _keyCompany,
  //         _keySaleOrg,
  //         _keySelectedCompany
  //     );
  //     Navigator.pop(context);
  //     if(user != null){
  //       // if (!mounted) return;
  //       // setState(() {
  //       //   // if( _checkCatchWifi == false &&_checkCatchError == false) {
  //       //     _saveLocal = true;
  //       //   // }
  //       //   _checkIsSendLogin = true;
  //       // });
  //       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //           backgroundColor: Colors.black,
  //           content: Text(
  //             'Chào mừng ${user.data!.userName.toString()}',
  //             style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //           ),
  //           duration: const Duration(seconds: 2)));
  //       Future.delayed(const Duration(seconds: 0), () {
  //         Navigator.pushReplacementNamed(context, "/mainPage", arguments: ScreenArgumentMainPage(user.data!));
  //       });
  //     }
  //   } on SocketException catch (_) {
  //     // if(!mounted) return;
  //     // setState(() {
  //     //   _checkCatchWifi = true;
  //     //   _checkCatchError = false;
  //     // });
  //     await SecureStorage.removeSecure(_keyUser, null);
  //     if(_checkIsSendLogin == false){
  //       Navigator.pop(context);
  //     }
  //     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //         backgroundColor: Colors.black,
  //         content: Text(
  //           'Không có kết nối mạng',
  //           style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //         ),
  //         duration: const Duration(seconds: 1)));
  //   } catch (error) {
  //     // if(!mounted) return;
  //     // setState(() {
  //     //   _checkCatchError = true;
  //     //   _checkCatchWifi = false;
  //     // });
  //     await SecureStorage.removeSecure(_keyUser, null);
  //     if(_checkIsSendLogin == false){
  //       Navigator.pop(context);
  //     }
  //     debugPrint(error.toString());
  //     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //         backgroundColor: Colors.black,
  //         content: Text(
  //           'Xảy ra lỗi! Vui lòng thử lại sau',
  //           style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //         ),
  //         duration: const Duration(seconds: 1)));
  //   }
  // }
  static Future<List<GetListCompanyByUserName>?> getCompany(BuildContext context, String username) async {
    try {
      final value = await fetchCompany(context, username);

      if (value != null) {
        return value;
      } else {
        return null;
      }
    } on SocketException catch (_) {
      print("--- TIEN LOG SOCKET EXCEPTION ---");
      print(_.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      return null;
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      return null;
    }
  }

  static Future<List<GetListCompanyByUserName>?> getCompanyListFromStorage(String userName) async {
    try {
      final dataStorage = await SecureStorage.getString("saveAccountLogin", userName);
      if (dataStorage != null) {
        final SaveAccountLogin saveAccountLogin = SaveAccountLogin.fromJson(jsonDecode(dataStorage));
        if (saveAccountLogin.company != null) {
          return List<GetListCompanyByUserName>.from(jsonDecode(saveAccountLogin.company!).map((i) => GetListCompanyByUserName.fromJson(i)));
        }
      }
      return null;
    } catch (error) {
      debugPrint("Error getting company list from storage: $error");
      return null;
    }
  }
}
