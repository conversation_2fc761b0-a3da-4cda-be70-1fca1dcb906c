# Detailed Screen Documentation for TTF MES Mobile Application

This document provides detailed information about the implementation of key screens in the TTF MES Mobile application, focusing on the main features.

## Table of Contents
1. [<PERSON><PERSON><PERSON> tra chất lượng (Quality Control)](#1-kiểm-tra-chất-lượng-quality-control)
2. [Downtime](#2-downtime)
3. [Maintenance Order](#3-maintenance-order)

---

## 1. <PERSON><PERSON><PERSON> tra chất lượng (Quality Control)

### BaoCaoDauVao (Input Quality Report)

**Purpose**: Displays a list of input quality control reports for raw materials.

**Key Features**:
- Displays a list of quality control records for incoming materials
- Supports filtering by various criteria
- QR code scanning for quick access to specific records
- Pull-to-refresh functionality
- Pagination with infinite scrolling
- Status indicators for quality control status (checked/unchecked)
- Status indicators for quality control results (pass/fail)

**Implementation Details**:
- Uses `ScrollController` for detecting scroll position and loading more data
- Implements pagination with `pageNumber` and `pageSize` parameters
- Handles network connectivity issues with dedicated error screens
- Uses `FilterListQCNVL` as an end drawer for filtering options
- Implements QR code scanning via navigation to a dedicated QR scanning screen

**Data Flow**:
1. Initial data is loaded from API using `ListQCFunction.postLstQCNVLByFilter2`
2. Status data is loaded from `EndDrawerListQCNVLFunction.fetchStatusGoodsArrive`
3. When a filter is applied, the data is refreshed with new filter parameters
4. When scrolling reaches the bottom, more data is loaded with incremented page number

**UI Components**:
- AppBar with back button, title, QR code scanner button, and filter button
- List of cards showing quality control records
- Each card displays:
  - Barcode creation time
  - PO number
  - Vendor information
  - QC status (checked/unchecked)
  - Material code and name
  - SO/WBS information
  - QC result (pass/fail) if checked
  - Actual delivery date
  - QC time
  - Factory arrival status
  - Factory arrival date
  - QC employee name (if checked)

**Navigation**:
- Tapping on a record navigates to `BaoCaoDauVaoDetail` screen with the record ID
- QR code button navigates to `QRCodePageListQC` for scanning
- Filter button opens the end drawer with filtering options

## 2. Downtime

### DowntimeList

**Purpose**: Displays a list of downtime records and allows creation of new records.

**Key Features**:
- Lists all downtime records with detailed information
- Supports filtering by various criteria
- Pull-to-refresh functionality
- Status indicators for approval status
- Floating action button for creating new downtime records

**Implementation Details**:
- Uses `RefreshIndicator` for pull-to-refresh functionality
- Implements filtering via an end drawer
- Handles network connectivity issues with dedicated error screens
- Uses a search model for filtering and pagination
- Calculates time spans between start and end times

**Data Flow**:
1. Initial data is loaded from API using `DowntimeFunction.fetchDowntimeList`
2. Default date range is set to "ThisWeek"
3. When a filter is applied, the data is refreshed with new filter parameters
4. When creating or updating a record, the list is refreshed

**UI Components**:
- AppBar with back button, title, and filter button
- List of cards showing downtime records
- Each card displays:
  - Department name/code
  - Step code and name
  - Status indicator (Approved, Rejected, Created, Pending)
  - Time range with calculated duration
  - Reason for downtime
  - Responsible team
  - Company code
  - Responsibility percentage (if available)
  - Notes (if available)
  - Creation timestamp
- Floating action button for creating new records

**Navigation**:
- Tapping on a record navigates to `DowntimeDetail` screen with the record ID
- Floating action button navigates to `DowntimeDetail` with an empty ID for creating a new record
- Filter button opens the end drawer with filtering options

**Status Handling**:
- Different status values are displayed with appropriate colors:
  - Approved: Green
  - Rejected: Red
  - Created: Blue
  - Pending: Orange

## 3. Maintenance Order

### MaintenanceOrderList

**Purpose**: Displays a list of maintenance orders and allows creation of new orders.

**Key Features**:
- Lists all maintenance orders with detailed information
- Supports filtering by various criteria
- Pull-to-refresh functionality
- Infinite scrolling with pagination
- Status indicators for order status
- Floating action button for creating new maintenance orders

**Implementation Details**:
- Uses `ScrollController` for detecting scroll position and loading more data
- Implements pagination with `pageNumber` and `pageSize` parameters
- Uses `RefreshIndicator` for pull-to-refresh functionality
- Implements filtering via an end drawer
- Handles network connectivity issues with dedicated error screens
- Uses a search model for filtering and pagination

**Data Flow**:
1. Initial data is loaded from API using `MaintenanceOrderFunction.fetchMaintenanceOrderList`
2. Default date range is set to "ThisMonth"
3. When scrolling reaches the bottom, more data is loaded with incremented page number
4. When a filter is applied, the data is refreshed with new filter parameters
5. When creating or updating a record, the list is refreshed

**UI Components**:
- AppBar with back button, title, and filter button
- List of cards showing maintenance orders
- Each card displays:
  - Order number
  - Plant information
  - Status indicator with appropriate color
  - Description
  - Equipment number and name
  - Functional location (if available)
  - Work center
  - Creation timestamp
  - Closing timestamp (if available)
- Floating action button for creating new orders
- Loading indicator at the bottom when more data is being loaded

**Navigation**:
- Tapping on a record navigates to `MaintenanceOrderDetail` screen with the record ID and view mode set to true
- Floating action button navigates to `MaintenanceOrderDetail` with an empty ID for creating a new order
- Filter button opens the end drawer with filtering options

**Pagination**:
- Initial page size is set to 20 records
- When scrolling to the bottom, the next page is loaded
- Loading indicator is shown at the bottom while loading more data
- When fewer than 20 records are returned, it's assumed there's no more data

## Common Patterns Across Screens

### Error Handling
All screens implement consistent error handling:
1. Network connectivity issues show a dedicated "Lost Connection" screen
2. API errors show appropriate error messages
3. Loading states show progress indicators

### Filtering
Most list screens implement filtering via an end drawer:
1. Filter options are specific to each screen
2. Default filters are applied on initial load
3. Date ranges are commonly used (ThisWeek, ThisMonth, etc.)
4. Filters are applied immediately when selected

### Pagination
Two pagination approaches are used:
1. **Scroll-based pagination**: Used in most lists, loads more data when scrolling to the bottom
2. **Page-based pagination**: Used in some screens, shows a fixed number of records per page

### Navigation
Consistent navigation patterns:
1. Back button in AppBar returns to previous screen
2. Tapping on list items navigates to detail screens
3. Floating action button creates new records
4. QR code scanning is used for quick access to specific records

### Status Indicators
Status values are consistently displayed with color-coded indicators:
1. Success states (Approved, Pass): Green
2. Error states (Rejected, Fail): Red
3. Pending states (Created, Pending): Blue or Orange
4. Neutral states: Gray

### Refresh Mechanisms
Two refresh mechanisms are implemented:
1. Pull-to-refresh: Implemented with `RefreshIndicator`
2. Manual refresh: Triggered after creating or updating records

### Data Loading
Data loading follows a consistent pattern:
1. Show loading indicator
2. Fetch data from API
3. Handle errors if any
4. Update UI with fetched data
5. Implement pagination if needed