﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RolesModel", Schema = "pms")]
    public partial class RolesModel
    {
        public RolesModel()
        {
            MobileScreenPermissionModel = new HashSet<MobileScreenPermissionModel>();
            PagePermissionModel = new HashSet<PagePermissionModel>();
            RoleInChargeModel = new HashSet<RoleInChargeModel>();
            Account = new HashSet<AccountModel>();
        }

        [Key]
        public Guid RolesId { get; set; }
        [StringLength(50)]
        public string RolesCode { get; set; }
        [Required]
        [StringLength(50)]
        public string RolesName { get; set; }
        public bool? isCheckLoginByTime { get; set; }
        public bool? isEmployeeGroup { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WorkingTimeFrom { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WorkingTimeTo { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        public bool? isSendSMSPermission { get; set; }

        [InverseProperty("Roles")]
        public virtual ICollection<MobileScreenPermissionModel> MobileScreenPermissionModel { get; set; }
        [InverseProperty("Roles")]
        public virtual ICollection<PagePermissionModel> PagePermissionModel { get; set; }
        [InverseProperty("Roles")]
        public virtual ICollection<RoleInChargeModel> RoleInChargeModel { get; set; }

        [ForeignKey("RolesId")]
        [InverseProperty("Roles")]
        public virtual ICollection<AccountModel> Account { get; set; }
    }
}