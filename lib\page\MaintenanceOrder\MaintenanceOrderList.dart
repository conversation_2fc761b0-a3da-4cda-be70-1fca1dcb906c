import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ttf/model/maintenanceOrderModel.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import 'package:ttf/repository/function/maintenanceOrderFunction.dart';
import 'package:ttf/utils/status_utils.dart';
import '../LostConnect.dart';
import '../../model/userModel.dart';
import 'element/FilterListMaintenanceOrder.dart';

// Define page size constant
const int kPageSize = 20;

class MaintenanceOrderList extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;

  const MaintenanceOrderList({
    Key? key,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _MaintenanceOrderListState createState() => _MaintenanceOrderListState();
}

class _MaintenanceOrderListState extends State<MaintenanceOrderList> {
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isLoadingMore = false;
  List<MaintenanceOrder> _records = [];
  ConnectivityResult _result = ConnectivityResult.none;
  MaintenanceOrderSearchModel? _searchModel;
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _initializeData();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      if (_searchModel != null) {
        _searchModel = MaintenanceOrderSearchModel(
          companyCode: _searchModel!.companyCode,
          orderNumber: _searchModel!.orderNumber,
          status: _searchModel!.status,
          fromDate: _searchModel!.fromDate,
          toDate: _searchModel!.toDate,
          pageNumber: _currentPage + 1,
          pageSize: kPageSize, // Updated from 8
        );

        final data = await MaintenanceOrderFunction.fetchMaintenanceOrderList(
          widget.user.token!,
          _searchModel!,
        );

        if (data != null && data.data.isNotEmpty) {
          setState(() {
            _records.addAll(data.data);
            _currentPage++;
            _hasMoreData = data.data.length >= kPageSize; // If we got less than 8 items, there's no more data
          });
        } else {
          setState(() {
            _hasMoreData = false;
          });
        }
      }
    } catch (error) {
      debugPrint("Error in _loadMoreData: $error");
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
        _currentPage = 1;
        _hasMoreData = true;
        _records = [];
      });

      // Initialize default search model if not exists
      if (_searchModel == null) {
        final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.user.token!);
        if (dataDropdown != null) {
          final commonDates = dataDropdown.data?.commonDates;
          if (commonDates != null) {
            final thisMonth = commonDates.firstWhere(
              (element) => element.catalogCode == "ThisMonth",
              orElse: () => commonDates.first,
            );

            final commonDateModel = await ListQCFunction.getCommonDateModel(
              thisMonth.catalogCode!,
              widget.user.token!,
            );

            if (commonDateModel != null) {
              _searchModel = MaintenanceOrderSearchModel(
                companyCode: widget.user.companyCode ?? '',
                pageNumber: 1,
                pageSize: kPageSize, // Load 8 items initially
                fromDate: commonDateModel.fromDate != null ? DateTime.parse(commonDateModel.fromDate!) : null,
                toDate: commonDateModel.toDate != null ? DateTime.parse(commonDateModel.toDate!) : null,
              );
            }
          }
        }
      } else {
        _searchModel = MaintenanceOrderSearchModel(
          companyCode: _searchModel!.companyCode,
          orderNumber: _searchModel!.orderNumber,
          status: _searchModel!.status,
          fromDate: _searchModel!.fromDate,
          toDate: _searchModel!.toDate,
          pageNumber: 1,
          pageSize: kPageSize, // Load 8 items initially
        );
      }

      // Fetch the actual data
      if (_searchModel != null) {
        final data = await MaintenanceOrderFunction.fetchMaintenanceOrderList(
          widget.user.token!,
          _searchModel!,
        );

        if (!mounted) return;

        if (data != null) {
          setState(() {
            _records = data.data;
            _hasMoreData = data.data.length >= kPageSize; // If we got less than 8 items, there's no more data
          });
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDateTime(String? dateTime) {
    if (dateTime == null) return '';
    try {
      final dt = DateTime.parse(dateTime);
      return DateFormat('dd/MM/yyyy HH:mm').format(dt);
    } catch (e) {
      return dateTime;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          Builder(
            builder: (BuildContext context) {
              return IconButton(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                hoverColor: Colors.transparent,
                icon: Icon(
                  Icons.search_outlined,
                  size: 19.sp,
                  color: Colors.white,
                ),
                onPressed: _isLoading == true
                    ? null
                    : () async {
                        await _checkConnectNetwork();
                        if (!mounted) return;
                        if (_result != ConnectivityResult.none) {
                          Scaffold.of(context).openEndDrawer();
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            backgroundColor: Colors.black,
                            content: Text(
                              'Tính năng cần có kết nối internet để sử dụng',
                              style: TextStyle(fontSize: 15.sp, color: Colors.white),
                            ),
                          ));
                        }
                      },
              );
            },
          ),
        ],
        title: Text(
          'Maintenance Orders (M2)',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      endDrawer: _isLoading == true
          ? null
          : FilterListMaintenanceOrder(
              searchModel: _searchModel,
              token: widget.user.token!,
              user: widget.user,
              onFilterSelected: (MaintenanceOrderSearchModel filter) {
                setState(() {
                  _searchModel = filter;
                });
                _initializeData();
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.pushNamed(
            context,
            '/MaintenanceOrderDetail',
            arguments: {
              'id': '',
              'dateTimeOld': widget.dateTimeOld,
              'user': widget.user,
              'viewMode': false,
            },
          );
          if (result == true) {
            _initializeData();
          }
        },
        backgroundColor: const Color(0xff4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: _buildBody(),
    );
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _initializeData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    if (_records.isEmpty) {
      return RefreshIndicator(
        onRefresh: _initializeData,
        child: ListView(
          children: [
            SizedBox(height: MediaQuery.of(context).size.height / 3),
            Center(
              child: Text(
                'Không có dữ liệu',
                style: TextStyle(fontSize: 15.sp),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _initializeData,
      child: ListView.builder(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: _records.length + (_hasMoreData ? 1 : 0), // Add 1 for loading indicator
        itemBuilder: (context, index) {
          if (index == _records.length) {
            // Show loading indicator at the bottom
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final record = _records[index];
          return Card(
            elevation: 2,
            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Số phiếu: ${record.orderNumber}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              RichText(
                                text: TextSpan(
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.black,
                                  ),
                                  children: [
                                    const TextSpan(text: 'Nhà máy: '),
                                    TextSpan(
                                      text: record.plant ?? '',
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (record.status != null)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color: StatusUtils.getMaintenanceStatusColor(record.status!),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              StatusUtils.getMaintenanceStatusText(record.status!),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.sp,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.description_outlined, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            '${record.description ?? ''}',
                            style: TextStyle(fontSize: 12.sp),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Divider(height: 16.h),
                    Row(
                      children: [
                        Icon(Icons.build_outlined, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Text(
                          'Thiết bị:',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(left: 0.w),
                            child: Text(
                              '${record.equipmentNumber ?? ''} | ${record.equipmentName ?? ''}',
                              style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (record.functionalLocation != null) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(Icons.location_on_outlined, size: 16.sp, color: Colors.grey[600]),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: Text(
                              'Location: ${record.functionalLocation}',
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ),
                        ],
                      ),
                    ],
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.work_outline, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            'Work Center: ${record.mainWorkCenter ?? ''}',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tạo lúc: ${_formatDateTime(record.creationDate)}',
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: Colors.grey[500],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                        if (record.closeDate != null)
                          Text(
                            'Đóng lúc: ${_formatDateTime(record.closeDate)}',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Colors.grey[500],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ).onTap(() async {
            final result = await Navigator.pushNamed(
              context,
              '/MaintenanceOrderDetail',
              arguments: {
                'id': record.orderNumber ?? '',
                'dateTimeOld': widget.dateTimeOld,
                'user': widget.user,
                'viewMode': true,
                'maintenanceOrder': record,
              },
            );
            if (result == true) {
              _initializeData();
            }
          });
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}

extension TapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }
}
