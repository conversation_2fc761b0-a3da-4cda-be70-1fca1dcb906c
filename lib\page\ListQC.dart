import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/userModel.dart';
import '../element/errorViewPost.dart';
import '../element/timeOut.dart';
import '../model/drawerFilterQC.dart';
import '../model/getListQCByFilter.dart';
import '../model/postFilterQC.dart';
import '../element/FilterLsQC.dart';
import '../model/qrCodePageListQCModel.dart';
import '../repository/function/listQcFunction.dart';
import '../screenArguments/ScreenArgumentNavigatorBar.dart';
import 'LostConnect.dart';

class ListQC extends StatefulWidget {
  // final String token;
  // final String accountID;
  final String dateTimeOld;
  final DataUser user;
  const ListQC(
      {Key? key,
      // required this.token, required this.accountID,
      required this.dateTimeOld,
      required this.user})
      : super(key: key);

  @override
  _ListQCState createState() => _ListQCState();
}

class _ListQCState extends State<ListQC> {
  bool? _isLoading;
  ConnectivityResult _result = ConnectivityResult.none;
  DateTime? _stringToDateTimeConfirm;
  String? _dateFormatStringConfirm;
  DateTime? _stringToDateTimeQualityDate;
  String? _dateFormatStringQualityDate;
  bool _isNotWifi = false;
  FilterQCModel? _filterLSQC;
  CommonDateModel? _commonDateModel;
  List<DataListQC>? _defaultDataFilter = [];
  List<SalesOrgCodes>? _salesOrgCodes;
  List<WorkCenters>? _workCenters;
  List<WorkShops>? _workShops;
  List<CommonDates>? _commonDates;
  List<ResultsDataQC>? _results;
  String _notFoundFilter = " ";
  FilterQCVm? _postFilterQC;
  String _error = "";
  late bool _timeOut;
  bool? _disableButton;

  @override
  void initState() {
    super.initState();
    _fetchApi();
  }

  Future<void> _getDropDownListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _timeOut = false;
      });
      final dataDropdown = await ListQCFunction.getDropDownListQCApi(widget.user.token.toString());
      if (dataDropdown == null) {
        if (!mounted) return;
        setState(() {
          _isLoading = true;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _filterLSQC = dataDropdown;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
        _timeOut = false;
      });
    }
  }

  Future<void> _getCommonDate() async {
    try {
      final dataCommonDateModel =
          await ListQCFunction.getCommonDateModel(_filterLSQC!.additionalData!.selectedConfirmCommonDate.toString(), widget.user.token.toString());
      if (!mounted) return;
      setState(() {
        _commonDateModel = dataCommonDateModel;
        _salesOrgCodes = _filterLSQC!.data!.salesOrgCodes;
        _salesOrgCodes!.insert(0, (ListQCFunction.defaultSalesOrgCodes));
        _workCenters = _filterLSQC!.data!.workCenters;
        _workCenters!.insert(0, (ListQCFunction.defaultWorkCenters));
        _results = _filterLSQC!.data!.results;
        _results!.insert(0, (ListQCFunction.defaultResultsDataQC));
        _workShops = _filterLSQC!.data!.workShops;
        _workShops!.insert(0, (ListQCFunction.defaultWorkShops));
        _commonDates = _filterLSQC!.data!.commonDates;
        _postFilterQC = FilterQCVm(
          _filterLSQC!.additionalData!.selectedSalesOrgCode,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          _filterLSQC!.additionalData!.selectedConfirmCommonDate,
          _commonDateModel!.fromDate,
          _commonDateModel!.toDate,
          null,
          null,
          null,
          null,
        );
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _postListQCApi() async {
    try {
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        debugPrint(_error);
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _rePostListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
      });
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _fetchApi() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
    } else {
      await _getDropDownListQCApi();
      if (_filterLSQC != null) {
        await _getCommonDate();
        if (_commonDateModel != null) {
          _postListQCApi();
        }
      }
    }
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
        : _isNotWifi == true
            ? Scaffold(
                backgroundColor: Colors.grey.shade200,
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Danh Sách Kiểm Tra BTP',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: LostConnect(checkConnect: () => _fetchApi()))
            : Scaffold(
                endDrawerEnableOpenDragGesture: false,
                backgroundColor: _defaultDataFilter != null && _defaultDataFilter!.isNotEmpty ? Colors.grey.shade200 : Colors.white,
                // key: _key,
                endDrawer: _isLoading == true
                    ? null
                    : FilterLsQC(
                        filterLSQC: _filterLSQC!,
                        lsStatus: ListQCFunction.lsStatus,
                        results: _results,
                        commonDates: _commonDates,
                        workCenters: _workCenters,
                        commonDateModel: _commonDateModel ?? CommonDateModel(),
                        salesOrgCodes: _salesOrgCodes,
                        workShops: _workShops,
                        token: widget.user.token.toString(),
                        onFilterSelected: (List<DrawerFilterQC> filter) {
                          setState(() {
                            _defaultDataFilter = filter[0].getDataFilter;
                            _notFoundFilter = filter[0].notFoundFilter ?? " ";
                            _postFilterQC = filter[0].postFilterQC;
                          });
                        },
                      ),
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  actions: [
                    IconButton(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: Icon(
                        Icons.camera_alt,
                        size: 19.sp,
                        color: Colors.white,
                      ),
                      onPressed: _isLoading == true
                          ? null
                          : () async {
                              final check = await Navigator.pushNamed(context, "/QRCodePageListQC");
                              if (!mounted) return;
                              if (check == null) return;
                              if ((check as QRCodePageListQCModel).isScan == true) {
                                final checkSave = await Navigator.pushNamed(context, '/BottomNavigatorBar',
                                    arguments: ScreenArgumentNavigatorBar("", widget.dateTimeOld, check.qrCode.toString(), "qr", widget.user));
                                if (checkSave == null) return;
                                if (checkSave == true) {
                                  _rePostListQCApi();
                                }
                              }
                            },
                    ),
                    Builder(
                      builder: (BuildContext context) {
                        return IconButton(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          icon: Icon(
                            Icons.search_outlined,
                            size: 19.sp,
                            color: Colors.white,
                          ),
                          onPressed: _isLoading == true
                              ? null
                              : () async {
                                  await _checkConnectNetwork();
                                  if (!mounted) return;
                                  if (_result != ConnectivityResult.none) {
                                    Scaffold.of(context).openEndDrawer();
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                      backgroundColor: Colors.black,
                                      content: Text(
                                        'Tính năng cần có kết nối internet để sử dụng',
                                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                      ),
                                    ));
                                  }
                                },
                        );
                      },
                    ),
                  ],
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Danh Sách Kiểm Tra BTP',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: _error != ""
                    ? ErrorViewPost(error: _error)
                    : _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : _defaultDataFilter != null && _defaultDataFilter!.isNotEmpty
                            ? ListView.builder(
                                key: ObjectKey(_defaultDataFilter![0]),
                                padding: EdgeInsets.zero,
                                shrinkWrap: true,
                                scrollDirection: Axis.vertical,
                                itemCount: _defaultDataFilter!.length,
                                itemBuilder: (context, index) {
                                  final item = _defaultDataFilter![index];
                                  if (item.confirmDate != null) {
                                    _stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(item.confirmDate.toString());
                                    _dateFormatStringConfirm = DateFormat('dd/MM/yyyy ').format(_stringToDateTimeConfirm!);
                                  }
                                  if (item.qualityDate != null) {
                                    _stringToDateTimeQualityDate = DateFormat("yyyy-MM-dd").parse(item.qualityDate.toString());
                                    _dateFormatStringQualityDate = DateFormat('dd-MM-yyyy').format(_stringToDateTimeQualityDate!);
                                  } else {
                                    _dateFormatStringQualityDate = item.qualityDate;
                                  }
                                  return GestureDetector(
                                      onTap: () async {
                                        debugPrint(item.qualityControlId);
                                        Navigator.pushNamed(context, '/BottomNavigatorBar',
                                            arguments: ScreenArgumentNavigatorBar(
                                              item.qualityControlId.toString(),
                                              widget.dateTimeOld,
                                              "",
                                              "normal",
                                              widget.user,
                                            )).then((value) {
                                          // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                                          // final check = arguments.refresh;
                                          // if(GlobalValue.refreshListQC == true){
                                          if (value == true) {
                                            _rePostListQCApi();
                                          }
                                        }).onError((error, stackTrace) {
                                          debugPrint('Lỗi:$error');
                                        });
                                        // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                                        // if (arguments.refresh == true) {
                                        // print(GlobalValue.refresh);
                                        // if (GlobalValue.refresh == true) {
                                        //   _rePostListQCApi();
                                        // } else {
                                        //   return;
                                        // }

                                        // }else{
                                        //   return;
                                        // }
                                        // if (data == null) return;
                                        // if (data == true) {
                                        //   getListQCApi();
                                        // }
                                      },
                                      child: _ListQCView(
                                          item: item,
                                          dateFormatStringConfirm: _dateFormatStringConfirm,
                                          dateFormatStringQualityDate: _dateFormatStringQualityDate));
                                })
                            : _NotFoundFilterView(
                                notFoundFilter: _notFoundFilter,
                              ));
  }
}

class _NotFoundFilterView extends StatelessWidget {
  final String notFoundFilter;
  const _NotFoundFilterView({Key? key, required this.notFoundFilter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            notFoundFilter,
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}

class _ListQCView extends StatelessWidget {
  final DataListQC item;
  final String? dateFormatStringConfirm;
  final String? dateFormatStringQualityDate;
  const _ListQCView({Key? key, required this.item, required this.dateFormatStringConfirm, required this.dateFormatStringQualityDate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Nhà máy: ${item.saleOrgCode.toString()}",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "LSX SAP: ${item.lsxsap.toString()}",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    decoration: BoxDecoration(
                      color: item.status == false ? Colors.deepOrange : const Color(0xff0052cc),
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "DSX: ${item.dsx.toString()}",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "LSX DT: ${item.lsxdt.toString()} ",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    decoration: BoxDecoration(
                      color: item.result != null
                          ? item.result == 'Fail'
                              ? Colors.red.shade800
                              : Colors.green
                          : Colors.yellow.shade800,
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.result != null ? item.result.toString() : "Chưa có kết quả",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          SizedBox(height: 10.h),
          Text(
            item.productName.toString(),
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            "Công đoạn lớn: ${item.workCenterCode.toString()}",
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
          const Divider(),
          // SizedBox(height: 10.h),
          Row(
            children: <Widget>[
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày Confirm",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      // dateFormatStringConfirm,
                      dateFormatStringConfirm.toString(),
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Text("|"),
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày kiểm tra",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      dateFormatStringQualityDate ?? " ",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Expanded(
              //   flex: 2,
              //   child: Icon(Icons.arrow_forward_ios_rounded, size: 18.sp, color: Colors.grey.shade600),
              // ),
            ],
          ),
        ],
      ),
    );
  }
}
