﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PurchaseOrderDetailModel", Schema = "MESP2")]
    public partial class PurchaseOrderDetailModel
    {
        public PurchaseOrderDetailModel()
        {
            RawMaterial_PurchaseOrderDetail_Mapping = new HashSet<RawMaterial_PurchaseOrderDetail_Mapping>();
        }

        [Key]
        public Guid PurchaseOrderDetailId { get; set; }
        public Guid? PurchaseOrderId { get; set; }
        [StringLength(50)]
        public string PurchaseOrderCode { get; set; }
        [StringLength(50)]
        public string POItem { get; set; }
        public int? POItemInt { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(50)]
        public string Material { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(50)]
        public string DeletionInd { get; set; }
        [StringLength(500)]
        public string ShortText { get; set; }
        [StringLength(50)]
        public string StorageLocation { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? POQuantity { get; set; }
        [StringLength(50)]
        public string OrderUnit { get; set; }
        [StringLength(50)]
        public string DelivCompl { get; set; }
        [StringLength(50)]
        public string AcctAssgmtCat { get; set; }
        [StringLength(50)]
        public string ItemCategory { get; set; }
        [StringLength(50)]
        public string GoodsReceipt { get; set; }
        [StringLength(50)]
        public string SDDocument { get; set; }
        [StringLength(50)]
        public string Item { get; set; }
        [StringLength(50)]
        public string WBSElement { get; set; }
        [StringLength(50)]
        public string CumulativeQuantity { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? CumulativeQuantityInt { get; set; }
        [StringLength(2000)]
        public string BasicDataText { get; set; }
        [StringLength(200)]
        public string Sso { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }

        [ForeignKey("PurchaseOrderId")]
        [InverseProperty("PurchaseOrderDetailModel")]
        public virtual PurchaseOrderMasterModel PurchaseOrder { get; set; }
        [InverseProperty("PurchaseOrderDetail")]
        public virtual ICollection<RawMaterial_PurchaseOrderDetail_Mapping> RawMaterial_PurchaseOrderDetail_Mapping { get; set; }
    }
}