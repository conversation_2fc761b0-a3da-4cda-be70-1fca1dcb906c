﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("QualityControl_FileAttachment_Mapping", Schema = "MES")]
    public partial class QualityControl_FileAttachment_Mapping
    {
        [Key]
        public Guid FileAttachmentId { get; set; }
        [Key]
        public Guid QualityControlId { get; set; }
        [StringLength(200)]
        public string Note { get; set; }

        [ForeignKey("FileAttachmentId")]
        [InverseProperty("QualityControl_FileAttachment_Mapping")]
        public virtual FileAttachmentModel FileAttachment { get; set; }
        [ForeignKey("QualityControlId")]
        [InverseProperty("QualityControl_FileAttachment_Mapping")]
        public virtual QualityControlModel QualityControl { get; set; }
    }
}