﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("FunctionModel", Schema = "pms")]
    public partial class FunctionModel
    {
        public FunctionModel()
        {
            MobileScreenPermissionModel = new HashSet<MobileScreenPermissionModel>();
            PagePermissionModel = new HashSet<PagePermissionModel>();
            MobileScreen = new HashSet<MobileScreenModel>();
            Page = new HashSet<PageModel>();
        }

        [Key]
        [StringLength(50)]
        public string FunctionId { get; set; }
        [StringLength(50)]
        public string FunctionName { get; set; }
        public int? OrderIndex { get; set; }

        [InverseProperty("Function")]
        public virtual ICollection<MobileScreenPermissionModel> MobileScreenPermissionModel { get; set; }
        [InverseProperty("Function")]
        public virtual ICollection<PagePermissionModel> PagePermissionModel { get; set; }

        [ForeignKey("FunctionId")]
        [InverseProperty("Function")]
        public virtual ICollection<MobileScreenModel> MobileScreen { get; set; }
        [ForeignKey("FunctionId")]
        [InverseProperty("Function")]
        public virtual ICollection<PageModel> Page { get; set; }
    }
}