class GetInventoryByListSOWBS {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetInventoryByListSOWBS? data;


  GetInventoryByListSOWBS(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetInventoryByListSOWBS.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetInventoryByListSOWBS.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetInventoryByListSOWBS {
  double? quantity;
  String? unit;
  DataGetInventoryByListSOWBS({this.quantity, this.unit});

  DataGetInventoryByListSOWBS.fromJson(Map<String, dynamic> json) {
    quantity = json['quantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}