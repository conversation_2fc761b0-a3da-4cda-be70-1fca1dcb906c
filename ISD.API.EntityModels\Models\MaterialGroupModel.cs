﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MaterialGroupModel", Schema = "MES")]
    public partial class MaterialGroupModel
    {
        [Key]
        public Guid MaterialGroupId { get; set; }
        [StringLength(10)]
        public string MANDT { get; set; }
        [StringLength(50)]
        public string MATKL { get; set; }
        [StringLength(1000)]
        public string WGBEZ { get; set; }
        public string WGBEZ60 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}