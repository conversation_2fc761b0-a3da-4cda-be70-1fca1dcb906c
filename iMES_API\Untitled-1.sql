
CREATE PROCEDURE Task.usp_SearchTask
	--<PERSON><PERSON><PERSON><PERSON> công ty
	@ServiceTechnicalTeamCode NVARCHAR(50) = NULL,
	--<PERSON><PERSON> yêu cầu
	@TaskCode INT,
	--Ti<PERSON><PERSON> đề
	@Summary NVARCHAR(4000),
	--<PERSON><PERSON><PERSON>
	@WorkFlowId UNIQUEIDENTIFIER,
	--Tr<PERSON><PERSON> thái
	@TaskStatusCode NVARCHAR(50),
	--<PERSON><PERSON><PERSON><PERSON> trạng thái
	@TaskProcessCode NVARCHAR(100),
	--NV theo dõi
	@Reporter NVARCHAR(50),
	--NV được phân công
	@Assignee NVARCHAR(50),
	--<PERSON><PERSON><PERSON><PERSON> hà<PERSON>
	@ProfileId UNIQUEIDENTIFIER,
	--<PERSON><PERSON><PERSON> hệ
	@CompanyId UNIQUEIDENTIFIER,
	--<PERSON><PERSON><PERSON><PERSON> tạo
	@CreateBy NVARCHAR(50),
	--<PERSON><PERSON><PERSON> <PERSON><PERSON>
	@PriorityCode NVARCHAR(50),
	--<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
	@CreatedFromDate DATETIME,
	@CreatedToDate DATETIME,
	--<PERSON><PERSON><PERSON> tiế<PERSON>h<PERSON>
	@ReceiveFromDate DATETIME,
	@ReceiveToDate DATETIME,
	--<PERSON><PERSON><PERSON> b<PERSON>t đầu
	@StartFromDate DATETIME,
	@StartToDate DATETIME,
	--Ngày đến hạn
	@EstimateEndFromDate DATETIME,
	@EstimateEndToDate DATETIME,
	--Ngày kết thúc
	@EndFromDate DATETIME,
	@EndToDate DATETIME,
	--Kanban
	@KanbanId UNIQUEIDENTIFIER,
	--Loại task
	@Type NVARCHAR(50),
	--Lỗi
	@CommonMistakeCode NVARCHAR(50),
	--Phương thức xử lý
	@ErrorCode NVARCHAR(50),
	--Hình thức bảo hành
	@ErrorTypeCode NVARCHAR(50),
	--Đơn vị thi công
	@ConstructionUnit UNIQUEIDENTIFIER,
	@WorkFlowIdList dbo.WorkFlowIdList READONLY,
	@AccountId UNIQUEIDENTIFIER,
	--TaskProcessCode
	@TaskProcessCode_Todo BIT= NULL ,
	@TaskProcessCode_Processing BIT= NULL,
	@TaskProcessCode_Incomplete BIT= NULL,
	@TaskProcessCode_CompletedOnTime BIT= NULL,
	@TaskProcessCode_CompletedExpire BIT= NULL,
	@TaskProcessCode_Expired BIT= NULL,
	@DomainImageWorkFlow NVARCHAR(100)= NULL,
	--Nhóm vật tư (ProductCategoryCode)
	@ProductCategoryCode NVARCHAR(50),
	--Lỗi thường gặp (UsualErrorCode)
	@UsualErrorCodeList dbo.StringList READONLY,
	--Mã màu (ProductColorCode)
	@ProductColorCodeList dbo.StringList READONLY,
	--Nhóm KH
	@ProfileGroupCode NVARCHAR(50),
	--NV kinh doanh
	@SalesSupervisorCode NVARCHAR(50),
	--Phòng ban
	@DepartmentCode NVARCHAR(50),
	--Phân loại chuyến thăm (THKH)
	@VisitTypeCode NVARCHAR(50),
	--Công ty
	@CurrentCompanyCode NVARCHAR(50),
	--Trạng thái hoạt động
	@Actived BIT,
	@IsReport BIT,
	--Record paging
	@PageSize INT = NULL,
    @PageNumber INT = NULL,
    @FilteredResultsCount INT OUTPUT,
	--NV kết thúc (người nhập hoàn thành)
	@CompletedEmployee NVARCHAR(50) = NULL,
	--Tỉnh/thành
	@ProvinceId UNIQUEIDENTIFIER = NULL,
	--Quận/huyện
	@DistrictId UNIQUEIDENTIFIER = NULL,
	--Phường/xã
	@WardId UNIQUEIDENTIFIER = NULL,
	--Mã KH
	@ProfileCode NVARCHAR(50) = NULL,
	--Tên KH
	@ProfileName NVARCHAR(500) = NULL,
	--Lệnh sản xuất đại trà
	@LSXDT NVARCHAR(50) = NULL,
	--Đợt sản xuất
	@DSX NVARCHAR(50) = NULL,
	--Lệnh sản xuất SAP
	@LSXSAP NVARCHAR(50) = NULL,
	@isDeleted BIT = NULL,
	--Mã sản phẩm
	@ProductCode NVARCHAR(50) = NULL,
	--Xem theo số lượng > 0
	@isViewQtyHasValue BIT = NULL,
	--Số lượng: từ đến
	@FromQty INT = NULL,
	@ToQty INT = NULL
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @TaskFilterCode NVARCHAR(20)
	SET @TaskFilterCode = (SELECT TaskFilterCode FROM pms.AccountModel WHERE AccountId = @AccountId)

	DECLARE @CurrentEmployeeCode NVARCHAR(20)
	SET @CurrentEmployeeCode = (SELECT EmployeeCode FROM pms.AccountModel WHERE AccountId = @AccountId)

	IF (@CreatedToDate IS NOT NULL AND CONVERT(VARCHAR(8), @CreatedToDate, 108) = '00:00:00')
	BEGIN
		SET @CreatedToDate = DATEADD(day, DATEDIFF(day, 0, @CreatedToDate), '23:59:59')
	END
	IF (@ReceiveToDate IS NOT NULL AND CONVERT(VARCHAR(8), @ReceiveToDate, 108) = '00:00:00')
	BEGIN
		SET @ReceiveToDate = DATEADD(day, DATEDIFF(day, 0, @ReceiveToDate), '23:59:59')
	END
	IF (@StartToDate IS NOT NULL AND CONVERT(VARCHAR(8), @StartToDate, 108) = '00:00:00')
	BEGIN
		SET @StartToDate = DATEADD(day, DATEDIFF(day, 0, @StartToDate), '23:59:59')
	END
	IF (@EstimateEndToDate IS NOT NULL AND CONVERT(VARCHAR(8), @EstimateEndToDate, 108) = '00:00:00')
	BEGIN
		SET @EstimateEndToDate = DATEADD(day, DATEDIFF(day, 0, @EstimateEndToDate), '23:59:59')
	END
	IF (@EndToDate IS NOT NULL AND CONVERT(VARCHAR(8), @EndToDate, 108) = '00:00:00')
	BEGIN
		SET @EndToDate = DATEADD(day, DATEDIFF(day, 0, @EndToDate), '23:59:59')
	END

	DECLARE @NumberOfRows_WorkFlowId INT = (SELECT COUNT(WorkFlowId) 
											FROM @WorkFlowIdList 
											WHERE WorkFlowId IS NOT NULL AND WorkFlowId <> '00000000-0000-0000-0000-000000000000')
	DECLARE @NumberOfRows_UsualErrorCode INT = (SELECT COUNT(Code) 
												FROM @UsualErrorCodeList 
												WHERE Code IS NOT NULL AND Code <> '')
	DECLARE @NumberOfRows_ColorCode INT = (SELECT COUNT(Code) 
										   FROM @ProductColorCodeList 
										   WHERE Code IS NOT NULL AND Code <> '')

	DECLARE @querySelect NVARCHAR(MAX)
	DECLARE @querySelect2 NVARCHAR(MAX)
	DECLARE @querySelectReport NVARCHAR(MAX)
	DECLARE @queryFrom NVARCHAR(MAX)
	DECLARE @queryFromReport NVARCHAR(MAX)
	DECLARE @queryWhere NVARCHAR(MAX)
	DECLARE @queryOffset NVARCHAR(MAX) = ''
	DECLARE @queryOffset2 NVARCHAR(MAX) = ''
	
	DECLARE @beginQuerySelect NVARCHAR(MAX)
	DECLARE @endQuerySelect NVARCHAR(MAX)

	SET @beginQuerySelect = '
								SELECT	t.*
										,pri.CatalogText_vi AS [PriorityText_vi]
										,CAST(p.ProfileCode AS NVARCHAR(50)) AS ProfileCode
										,p.ProfileForeignCode
										,p.ProfileName
										,p.ProfileShortName
										,ISNULL(p.Address, '''') + ISNULL('', '' + ward.Appellation + '' '' + ward.WardName, '''') + ISNULL('', '' + d.Appellation + '' '' + d.DistrictName, '''') + ISNULL('', '' + pr.ProvinceName, '''') AS ProfileAddress
										,ISNULL(pr.ProvinceName, '''') AS ProvinceName
										,ISNULL(d.Appellation + '' '' + d.DistrictName, '''') AS DistrictName
										,ISNULL(ward.Appellation + '' '' + ward.WardName, '''') AS WardName
										,SaleOffice.CatalogText_vi AS SaleOfficeName
										,p.Phone
										,p.Email
										--Checkin
										,checkin.CheckInTime
										,checkin.CheckOutTime	
								FROM
								(
							'
	SET @querySelect = 'SELECT  ROW_NUMBER() OVER (ORDER BY CASE WHEN t.StartDate IS NULL THEN 0
															ELSE 1 END, t.StartDate DESC) AS RowNum
							, t.[TaskId]
							,CONVERT(INT, t.[TaskCode]) AS TaskCode
							,CASE
								WHEN LEN(t.[Summary]) > 70
								THEN SUBSTRING(t.[Summary], 1, 70) + ''...''
								ELSE t.[Summary]
							END AS Summary
							,t.[ProfileId]
							,CASE
								WHEN LEN(t.[Description]) > 70
								THEN SUBSTRING(t.[Description], 1, 70) + ''...''
								ELSE t.[Description] 
							END AS [Description]
							,t.[Description] AS DetailSummary
							,t.[WorkFlowId]
							,w.[WorkFlowCode]
							,w.[WorkFlowName]
							,t.[TaskStatusId]
							,ts.[TaskStatusCode]
							,ts.[TaskStatusName]
							,t.[isDeleted]
							,ts.OrderIndex AS TaskStatusOrderIndex
							,t.[PriorityCode]
							,ts.[ProcessCode]
							,CASE
								--Quá hạn
								WHEN ts.ProcessCode <> ''completed'' 
								AND (
									t.EstimateEndDate IS NOT NULL
									AND t.EstimateEndDate < GETDATE()
									AND t.EndDate IS NULL
								)
								THEN ''#dd4b39''
								--Hoàn thành đúng hạn
								WHEN ts.ProcessCode = ''completed'' 
								AND (
									t.EndDate <= t.EstimateEndDate
									OR t.EstimateEndDate IS NULL 
									OR t.EndDate IS NULL
								)
								THEN ''#398439''
								--Hoàn thành quá hạn
								WHEN ts.ProcessCode = ''completed'' 
								AND (
									t.EstimateEndDate IS NOT NULL 
									AND t.EndDate IS NOT NULL 
									AND t.EstimateEndDate < t.EndDate
								)
								THEN ''#f39c12''
								--Chờ xử lý
								WHEN ts.ProcessCode = ''todo''
								THEN ''#fff''
								--Đang thực hiện
								WHEN (ts.TaskStatusCode = ''PC'' AND ts.ProcessCode = ''processing'')
								THEN ''#F39C12''
								 WHEN (ts.TaskStatusCode <> ''PC'' AND ts.ProcessCode = ''processing'')
								THEN ''#0052CC''
								ELSE ''#fff''
							END AS TaskStatusBackgroundColor
							,CASE
								WHEN ts.ProcessCode <> ''completed'' 
								AND (
									t.EstimateEndDate IS NOT NULL
									AND t.EstimateEndDate < GETDATE()
									AND t.EndDate IS NULL
								)
								THEN ''#fff''
								WHEN ts.ProcessCode = ''completed'' 
								AND (
									t.EndDate <= t.EstimateEndDate
									OR t.EstimateEndDate IS NULL 
									OR t.EndDate IS NULL
								)
								THEN ''#fff''
								WHEN ts.ProcessCode = ''completed'' 
								AND (
									t.EstimateEndDate IS NOT NULL 
									AND t.EndDate IS NOT NULL 
									AND t.EstimateEndDate < t.EndDate
								)
								THEN ''#fff''
								WHEN ts.ProcessCode = ''todo''
								THEN ''#000''
								WHEN ts.ProcessCode = ''processing''
								THEN ''#fff''
								ELSE ''#000''
							END AS TaskStatusColor
							,CONVERT(INT, ts.OrderIndex) AS ProcessCodeIndex
							,t.[ReceiveDate]
							,t.[StartDate]
							,t.[EstimateEndDate] 
							,t.[EndDate] 
							,t.[Date1]
							,t.SubtaskCode
							,t.Actived
							--Sản phẩm
							,product.ERPProductCode AS ProductCode
							,product.ProductName
							,t.Qty
							,t.Number2
							'
	

	SET @queryFrom = ' FROM Task.TaskModel t WITH(NOLOCK)
	--WorkFlow
	JOIN Task.WorkFlowModel w ON w.WorkFlowId = t.WorkFlowId
	--Task Status
	JOIN Task.TaskStatusModel ts ON ts.TaskStatusId = t.TaskStatusId
	--Reporter
	LEFT JOIN tMasterData.SalesEmployeeModel s ON t.Reporter = s.SalesEmployeeCode
	--CreateByName
	INNER JOIN pms.AccountModel ac ON t.CreateBy = ac.AccountId
	LEFT JOIN tMasterData.SalesEmployeeModel salesemp ON salesemp.SalesEmployeeCode = ac.EmployeeCode
	--Product
	LEFT JOIN tSale.ProductModel product ON t.ProductId = product.ProductId
	
	'
	IF (@Type = 'LSXC')
	BEGIN
			SET @querySelect2 = N'
									--Lệnh sản xuất đại trà
									,t.Property5 AS LSXDT
									--Lệnh sản xuất đại trà  - SAP
									,t.Property3 AS LSXDTSAP
									--Đợt sản xuất
									,parent.Summary AS DSX
									--Lệnh sản xuất SAP
									,t.Summary AS LSXSAP
									-- ProductSO Code
									,proSO.ProductCode as ProductSOCode
									-- ProductSO Name
									,proSO.ProductName as ProductSOName
							'
			SET @queryFrom = @queryFrom + '
							--Task parent
							LEFT JOIN Task.TaskModel parent ON t.ParentTaskId = parent.TaskId
							-- ProductSOLine
							LEFT JOIN MES.SaleOrderItem100Model so ON so.VBELN = t.Property1 AND so.POSNR = t.Property2
							-- ProductSoLineName
							-- LEFT JOIN tSale.ProductModel proSO ON so.UPMAT = proSO.ERPProductCode
              LEFT JOIN tSale.ProductLatestModel proSO ON so.UPMAT = proSO.ProductCode
			'
	END
	ELSE IF(@Type = 'LSXD')
	BEGIN
		SET @querySelect2 = N'
									--Lệnh sản xuất đại trà
									,t.Property5 AS LSXDT
									--Lệnh sản xuất đại trà - SAP
									,t.Property3 AS LSXDTSAP
									--Đợt sản xuất
									,t.Summary AS DSX
							'
	END


	--TaskContact
	/*IF (@CompanyId IS NOT NULL)
	BEGIN
		SET @queryFrom =  @queryFrom + ' INNER JOIN (
											SELECT tc.TaskId
											FROM Task.TaskContactModel tc 
											WHERE tc.ContactId = ''' + CAST(@CompanyId AS NVARCHAR(50)) +
									''') taskContact ON taskContact.TaskId = t.TaskId '

	END */

	--TaskAssignee
	IF (@Assignee IS NOT NULL AND @Assignee <> '' AND (@Reporter IS NULL OR @Reporter = ''))
	BEGIN
		SET @queryFrom =  @queryFrom + ' INNER JOIN (
											SELECT a.TaskId
											FROM Task.TaskAssignModel a
											JOIN tMasterData.SalesEmployeeModel s ON s.SalesEmployeeCode = a.SalesEmployeeCode
											--Phòng ban
											WHERE a.SalesEmployeeCode = ''' + @Assignee +
									''') assignee ON assignee.TaskId = t.TaskId '
	END 

	--WorkFlowIdList
	IF (@NumberOfRows_WorkFlowId > 0)
	BEGIN
		SELECT WorkFlowId
		INTO #WorkFlowIdList
		FROM @WorkFlowIdList

		SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT WorkFlowId
											FROM #WorkFlowIdList
										) list ON t.WorkFlowId = list.WorkFlowId '
	END 

	--ProductCategoryCode
	IF (@ProductCategoryCode IS NOT NULL AND @ProductCategoryCode <> '')
	BEGIN
		SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT tp.TaskId
											FROM Task.TaskProductModel tp 
											WHERE tp.ProductCategoryCode = ''' + @ProductCategoryCode + '''
										) category ON t.TaskId = category.TaskId '
	END

	--UsualErrorCodeList
	IF (@NumberOfRows_UsualErrorCode > 0)
	BEGIN
		SELECT Code
		INTO #UsualErrorCodeList
		FROM @UsualErrorCodeList

		SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT DISTINCT tp.TaskId
											FROM Task.TaskProductModel tp
											INNER JOIN [Task].[TaskProductUsualErrorModel] tpe ON tp.TaskProductId = tpe.TaskProductId
											INNER JOIN #UsualErrorCodeList errList ON tpe.UsualErrorCode = errList.Code
										) error ON t.TaskId = error.TaskId '

	END 

	--ProductColorCodeList
	IF (@NumberOfRows_ColorCode > 0)
	BEGIN
		SELECT LTRIM(RTRIM(Code)) AS Code
		INTO #ProductColorCodeList
		FROM @ProductColorCodeList

		SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT tp.TaskId
											FROM #ProductColorCodeList colorList
											INNER JOIN Task.TaskProductModel tp ON LTRIM(RTRIM(colorList.Code)) = LTRIM(RTRIM(tp.ProductColorCode))
										) color ON t.TaskId = color.TaskId '
	END 

	--Kanban
	IF ((@KanbanId IS NOT NULL AND @KanbanId <> '00000000-0000-0000-0000-000000000000') AND (@Type = 'MyWork' OR @Type = 'MyFollow' OR @Type = 'MyCalendar'))
	BEGIN
		SET @querySelect2 = @querySelect2 + ', CASE
													WHEN ''' + ISNULL(CAST(@KanbanId AS NVARCHAR(50)), '') + ''' = ''''
													THEN NULL
													ELSE CAST(k.OrderIndex AS NVARCHAR(20))
											   END AS [state] '

		SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT m.TaskStatusId, d.OrderIndex
											FROM tMasterData.Kanban_TaskStatus_Mapping m
											JOIN tMasterData.KanbanDetailModel d ON d.KanbanDetailId = m.KanbanDetailId
											JOIN tMasterData.KanbanModel master ON d.KanbanId = master.KanbanId
											WHERE master.Actived = 1 
											AND d.KanbanId = ''' + CAST(@KanbanId AS NVARCHAR(50)) + '''
										) k ON k.TaskStatusId = t.TaskStatusId '
	END
	ELSE IF((@KanbanId IS NULL OR @KanbanId = '00000000-0000-0000-0000-000000000000') AND (@Type = 'MyWork' OR @Type = 'MyFollow' OR @Type = 'M_MyWork' OR @Type = 'M_MyFollow' OR @Type = 'MyCalendar'))
	BEGIN
			SET @querySelect2 = @querySelect2 + ',CAST(k.OrderIndex AS NVARCHAR(20)) AS [state] '

			SET @queryFrom = @queryFrom + ' INNER JOIN (
											SELECT m.TaskStatusId, d.OrderIndex
											FROM tMasterData.Kanban_TaskStatus_Mapping m
											JOIN tMasterData.KanbanDetailModel d ON d.KanbanDetailId = m.KanbanDetailId
											JOIN tMasterData.KanbanModel master ON d.KanbanId = master.KanbanId
											WHERE master.Actived = 1 
											AND master.KanbanCode = ''' + @Type + '''
										) k ON k.TaskStatusId = t.TaskStatusId '
	END

	SET @queryWhere = ' WHERE  
		(
			--Việc cần làm
			 ((' + ISNULL(CAST(@TaskProcessCode_Todo AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode = ''todo''))
			--Đang thực hiện
			OR ((' + ISNULL(CAST(@TaskProcessCode_Processing AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode = ''processing''))
			--Chưa hoàn thành
			OR ((' + ISNULL(CAST(@TaskProcessCode_Incomplete AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode <> ''completed''))
			--Hoàn thành đúng hạn
			OR ((' + ISNULL(CAST(@TaskProcessCode_CompletedOnTime AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode = ''completed'' 
				AND (
					t.EndDate <= t.EstimateEndDate
					OR t.EstimateEndDate IS NULL 
					OR t.EndDate IS NULL
				)
			))
			--Hoàn thành quá hạn
			OR ((' + ISNULL(CAST(@TaskProcessCode_CompletedExpire AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode = ''completed'' 
				AND (
					t.EstimateEndDate IS NOT NULL 
					AND t.EndDate IS NOT NULL 
					AND t.EstimateEndDate < t.EndDate
				)
			))
			--Quá hạn
			OR ((' + ISNULL(CAST(@TaskProcessCode_Expired AS NVARCHAR(1)), 0) + ' = 1 AND ts.ProcessCode <> ''completed'' 
				AND (
					t.EstimateEndDate IS NOT NULL
					AND t.EstimateEndDate < GETDATE()
					AND t.EndDate IS NULL
				)
			))
		)
		
		--Task riêng tư
		AND ((t.isPrivate IS NULL OR t.isPrivate = 0)  
			OR (t.isPrivate = 1 
					AND (t.CreateBy = ''' + CAST(@AccountId AS NVARCHAR(50)) + '''
							OR t.Reporter = ''' + @CurrentEmployeeCode + '''
							OR EXISTS(
								SELECT a.TaskId
								FROM Task.TaskAssignModel a
								WHERE a.SalesEmployeeCode = ''' + @CurrentEmployeeCode + ''' AND a.TaskId = t.TaskId
							)
						)
				)
			)

		--Task chưa bị xóa
		--AND (t.isDeleted IS NULL OR t.isDeleted = 0)
		--Không có LSX ĐT thì không hiển thị lên luôn
		AND (t.Property3 IS NOT NULL AND t.Property3 <> '''')
		 '

	--ServiceTechnicalTeamCode
	IF (@ServiceTechnicalTeamCode IS NOT NULL AND @ServiceTechnicalTeamCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ServiceTechnicalTeamCode = ''' + @ServiceTechnicalTeamCode + ''') '
	END

	--TaskCode
	IF (@TaskCode IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CAST(t.TaskCode AS NVARCHAR(50)) LIKE ''%'' + CAST('''+ CAST(@TaskCode AS NVARCHAR(20)) + ''' AS NVARCHAR(50)) + ''%''
												OR CAST(t.SubtaskCode AS NVARCHAR(50)) LIKE ''%'' + CAST('''+ CAST(@TaskCode AS NVARCHAR(20)) + ''' AS NVARCHAR(50)) + ''%''
												) '
	END

	--Summary
	IF (@Summary IS NOT NULL AND @Summary <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Summary LIKE N''%'' + N''' + @Summary + ''' + ''%'') '
	END
	--WorkFlowId
	IF (@WorkFlowId IS NOT NULL)
	BEGIN
	
		SET @queryWhere = @queryWhere + ' AND (t.WorkFlowId = ''' + CAST(@WorkFlowId AS NVARCHAR(50)) + ''') '
	END
		--CompanyId
	IF (@CompanyId IS NOT NULL)
	BEGIN
	
		SET @queryWhere = @queryWhere + ' AND (t.CompanyId = ''' + CAST(@CompanyId AS NVARCHAR(50)) + ''') '
	END
	-- Delete
	IF(@isDeleted IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.isDeleted = ''' + CAST(@isDeleted AS NVARCHAR(10)) + ''') '
	END
	--TaskStatusCode
	IF (@TaskStatusCode IS NOT NULL AND @TaskStatusCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (ts.TaskStatusCode = ''' + @TaskStatusCode + ''') '
	END

	--PRINT(@queryWhere)

	--Reporter
	IF ((@Reporter IS NOT NULL AND @Reporter <> '') AND (@Assignee IS NULL OR @Assignee = ''))
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Reporter = ''' + @Reporter + ''' OR EXISTS (
																							SELECT a.TaskId
																							FROM Task.TaskReporterModel a
																							JOIN tMasterData.SalesEmployeeModel s ON s.SalesEmployeeCode = a.SalesEmployeeCode
																							WHERE a.SalesEmployeeCode = ''' + @Reporter + ''' AND a.TaskId = t.TaskId
																						)) '
	END

	

	-- Assignee and Reporter
	IF ((@Reporter IS NOT NULL AND @Reporter <> '') AND (@Assignee IS NOT NULL AND @Assignee <> ''))
	BEGIN
		SET @queryFrom =  @queryFrom + ' LEFT JOIN (
											SELECT a.TaskId
											FROM Task.TaskAssignModel a
											JOIN tMasterData.SalesEmployeeModel s ON s.SalesEmployeeCode = a.SalesEmployeeCode
											--Phòng ban
											WHERE a.SalesEmployeeCode = ''' + @Assignee +
									''') assignee ON assignee.TaskId = t.TaskId '

		SET @queryWhere = @queryWhere + ' AND (t.Reporter = ''' + @Reporter + ''' OR EXISTS (
																							SELECT a.TaskId
																							FROM Task.TaskReporterModel a
																							JOIN tMasterData.SalesEmployeeModel s ON s.SalesEmployeeCode = a.SalesEmployeeCode
																							WHERE a.SalesEmployeeCode = ''' + @Reporter + ''' AND a.TaskId = t.TaskId
																						)) '
	END

	--ProfileId
	IF (@ProfileId IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ProfileId = ''' + CAST(@ProfileId AS NVARCHAR(50)) + ''') '
	END

	--CreateBy
	IF (@CreateBy IS NOT NULL AND @CreateBy <> '')
	BEGIN
		IF(@Assignee IS NOT NULL AND @Reporter IS NOT NULL)
		BEGIN
			SET @queryWhere = @queryWhere + ' OR (''' + @CreateBy + ''' = (SELECT acc.EmployeeCode FROM pms.AccountModel acc WHERE acc.AccountId = t.CreateBy)) '
		END
		ELSE
		BEGIN
			SET @queryWhere = @queryWhere + ' AND (''' + @CreateBy + ''' = (SELECT acc.EmployeeCode FROM pms.AccountModel acc WHERE acc.AccountId = t.CreateBy)) '
		END
	END

	--PriorityCode
	IF (@PriorityCode IS NOT NULL AND @PriorityCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.PriorityCode = ''' + @PriorityCode + ''') '
	END

	--CreatedFromDate
	IF (@CreatedFromDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CONVERT(DATETIME,''' + CAST(@CreatedFromDate AS NVARCHAR(50)) + ''',120) <= t.CreateTime) '
	END

	--CreatedToDate
	IF (@CreatedToDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.CreateTime <= CONVERT(DATETIME,''' + CAST(@CreatedToDate AS NVARCHAR(50)) + ''',120)) '
	END

	--ReceiveFromDate
	IF (@ReceiveFromDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CONVERT(DATETIME,''' + CAST(@ReceiveFromDate AS NVARCHAR(50)) + ''',120) <= t.ReceiveDate) '
	END

	--ReceiveToDate
	IF (@ReceiveToDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ReceiveDate <= CONVERT(DATETIME,''' + CAST(@ReceiveToDate AS NVARCHAR(50)) + ''',120)) '
	END

	--StartFromDate
	IF (@StartFromDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CONVERT(DATETIME,''' + CAST(@StartFromDate AS NVARCHAR(50)) + ''',120) <= t.StartDate) '
		
	END

	--StartToDate
	IF (@StartToDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.StartDate <= CONVERT(DATETIME,''' + CAST(@StartToDate AS NVARCHAR(50)) + ''',120)) '
	END

	--EstimateEndFromDate
	IF (@EstimateEndFromDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CONVERT(DATETIME,''' + CAST(@EstimateEndFromDate AS NVARCHAR(50)) + ''',120) <= t.EstimateEndDate) '
	END

	--EstimateEndToDate
	IF (@EstimateEndToDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.EstimateEndDate <= CONVERT(DATETIME,''' + CAST(@EstimateEndToDate AS NVARCHAR(50)) + ''',120)) '
	END

	--EndFromDate
	IF (@EndFromDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (CONVERT(DATETIME,''' + CAST(@EndFromDate AS NVARCHAR(50)) + ''',120) <= t.EndDate) '
	END

	--EndToDate
	IF (@EndToDate IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.EndDate <= CONVERT(DATETIME,''' + CAST(@EndToDate AS NVARCHAR(50)) + ''',120)) '
	END

	--Type
	DECLARE @FisrtType NVARCHAR(50) = @Type
	IF (@Type = 'MyWork' OR @Type = 'MyFollow' OR @Type = 'MyCalendar')
	BEGIN
		SET @Type = 'ALL'
	END
	--IF (@Type IS NOT NULL AND @Type <> '')
	--BEGIN
	--	SET @queryWhere = @queryWhere + ' AND ((''' + @Type + ''' = ''ALL'') OR ''' + @Type + ''' = w.WorkflowCategoryCode) '
	--END

	--CommonMistakeCode
	IF (@CommonMistakeCode IS NOT NULL AND @CommonMistakeCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.CommonMistakeCode = ''' + @CommonMistakeCode + ''') '
	END

	--ErrorTypeCode
	IF (@ErrorTypeCode IS NOT NULL AND @ErrorTypeCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ErrorTypeCode = ''' + @ErrorTypeCode + ''') '
	END

	--ErrorCode
	IF (@ErrorCode IS NOT NULL AND @ErrorCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ErrorCode = ''' + @ErrorCode + ''') '
	END

	--ConstructionUnit
	IF (@ConstructionUnit IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.ConstructionUnit = ''' + CAST(@ConstructionUnit AS NVARCHAR(50)) + ''') '
	END

	--TaskFilterCode
	IF(@AccountId IS NOT NULL AND @TaskFilterCode IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' 
		AND ((
			(''' + ISNULL(@TaskFilterCode, '') + ''' = ''TatCa'') OR 
			(
				--CaNhan
				--Được phân công OR Đang theo dõi
				(''' + ISNULL(@TaskFilterCode, '') + ''' = ''CaNhan'' AND 
					(
						t.Reporter = ''' + ISNULL(@CurrentEmployeeCode, '') + ''' 
						OR ''' + ISNULL(@CurrentEmployeeCode, '') + ''' IN (SELECT ta.SalesEmployeeCode
																			FROM Task.TaskAssignModel ta 
																			WHERE t.TaskId = ta.TaskId)
					)
				)
				OR
				--CongTy
				(''' + ISNULL(@TaskFilterCode, '') + ''' = ''CongTy'' AND
					''' + ISNULL(@CurrentEmployeeCode, '') + ''' IN (SELECT accountCompany.EmployeeCode
																	 FROM pms.AccountModel accountCompany
																	 JOIN pms.AccountInStoreModel m ON m.AccountId = accountCompany.AccountId
																	 JOIN tMasterData.StoreModel s ON s.StoreId = m.StoreId
																	 JOIN tMasterData.CompanyModel comp ON comp.CompanyId = s.CompanyId
																	 WHERE t.CompanyId = comp.CompanyId)
				)
				OR
				--ChiNhanh
				(''' + ISNULL(@TaskFilterCode, '') + ''' = ''ChiNhanh'' AND
					''' + ISNULL(@CurrentEmployeeCode, '') + ''' IN (SELECT accountStore.EmployeeCode
																	 FROM pms.AccountModel accountStore
																	 JOIN pms.AccountInStoreModel m ON m.AccountId = accountStore.AccountId
																	 JOIN tMasterData.StoreModel s ON s.StoreId = m.StoreId
																	 WHERE t.StoreId = s.StoreId)
				)
			)
		)) '
	END

	--Nhóm KH
	IF (@ProfileGroupCode IS NOT NULL AND @ProfileGroupCode <> '')
	BEGIN 
		SET @queryFrom = @queryFrom + ' INNER JOIN (SELECT DISTINCT ProfileId 
												    FROM Customer.ProfileGroupModel 
													WHERE ProfileGroupCode = ''' + @ProfileGroupCode + ''') pg ON pg.ProfileId = t.ProfileId '
	END

	--NV kinh doanh
	IF (@SalesSupervisorCode IS NOT NULL AND @SalesSupervisorCode <> '')
	BEGIN 
		SET @queryFrom = @queryFrom + ' INNER JOIN (SELECT DISTINCT ProfileId 
												    FROM Customer.PersonInChargeModel 
													WHERE SalesEmployeeCode = ''' + @SalesSupervisorCode + ''') pc ON pc.ProfileId = t.ProfileId '
	END

	--Phòng ban (của NV được phân công)
	IF (@DepartmentCode IS NOT NULL AND @DepartmentCode <> '')
	BEGIN 
		SET @queryFrom = @queryFrom + ' INNER JOIN (SELECT DISTINCT ta.TaskId 
												    FROM Task.TaskAssignModel ta
													INNER JOIN tMasterData.SalesEmployeeModel s ON s.SalesEmployeeCode = ta.SalesEmployeeCode
													INNER JOIN pms.AccountModel ac ON s.SalesEmployeeCode = ac.EmployeeCode
													INNER JOIN pms.AccountInRoleModel m ON ac.AccountId = m.AccountId
													INNER JOIN pms.RolesModel r ON m.RolesId = r.RolesId
													WHERE r.isEmployeeGroup = 1 
													AND r.RolesCode = ''' + @DepartmentCode + ''') roles ON roles.TaskId = t.TaskId '
	END

	--Phân loại chuyến thăm
	IF (@VisitTypeCode IS NOT NULL AND @VisitTypeCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.VisitTypeCode = ''' + @VisitTypeCode + ''') '
	END

	--Trạng thái hoạt động
	IF(@Actived IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Actived = ''' + CAST(@Actived AS NVARCHAR(10)) + ''') '
	END
	
	--ELSE 
	--BEGIN
	--	SET @queryWhere = @queryWhere + ' AND (t.Actived = 1) '
	--END 

	--NV kết thúc
	IF(@CompletedEmployee IS NOT NULL AND @CompletedEmployee <> '')
	BEGIN
		DECLARE @CompletedEmployeeId UNIQUEIDENTIFIER = (SELECT AccountId FROM pms.AccountModel WHERE EmployeeCode = @CompletedEmployee)
		IF(@CompletedEmployeeId IS NOT NULL)
		BEGIN 
			SET @queryFrom = @queryFrom + ' INNER JOIN (
												SELECT c.*
												FROM utilities.ChangeDataLogModel c
												INNER JOIN (
													SELECT  c.PrimaryKey, MAX(c.LastEditTime) AS MaxDate
													FROM utilities.ChangeDataLogModel c
													WHERE  c.LastEditBy = ''' + CAST(@CompletedEmployeeId AS nvarchar(50)) + '''
														AND c.TableName = ''TaskModel''
														AND c.FieldName = ''TaskStatusId''
													GROUP BY c.PrimaryKey
												) MaxDates_CTE 
													ON MaxDates_CTE.PrimaryKey =  c.PrimaryKey
													AND MaxDates_CTE.MaxDate = c.LastEditTime
												INNER JOIN Task.TaskStatusModel ts on CONVERT(uniqueidentifier, CAST(c.NewData AS nvarchar(max))) like ts.TaskStatusId
												WHERE c.LastEditBy = ''' + CAST(@CompletedEmployeeId AS nvarchar(50)) + '''
														AND c.TableName = ''TaskModel''
														AND c.FieldName = ''TaskStatusId''
														AND ts.ProcessCode = ''completed''
										) c ON t.TaskId = c.PrimaryKey '
		END
	END

	--Tỉnh/thành
	IF(@ProvinceId IS NOT NULL)
	BEGIN

	SET @queryFrom = @queryFrom + ' INNER JOIN (
												SELECT pr.ProvinceId, p.ProfileId, p.[Address]
												FROM  Customer.ProfileModel p
												LEFT JOIN tMasterData.ProvinceModel pr ON p.ProvinceId = pr.ProvinceId
												WHERE pr.ProvinceId = ''' + CAST(@ProvinceId AS nvarchar(50)) + '''
												UNION
												SELECT pr.ProvinceId, p.ProfileId, p.[Address]
												FROM  Customer.AddressBookModel p
												LEFT JOIN tMasterData.ProvinceModel pr ON p.ProvinceId = pr.ProvinceId
												WHERE pr.ProvinceId = ''' + CAST(@ProvinceId AS nvarchar(50)) + '''
									) pr ON pr.ProfileId = t.ProfileId AND t.ProfileAddress like N'''' + pr.[Address] + ''%'' '
	END

	--Quận/huyện
	IF(@DistrictId IS NOT NULL)
	BEGIN

	SET @queryFrom = @queryFrom + ' INNER JOIN (
												SELECT d.DistrictId, p.ProfileId, p.[Address]
												FROM  Customer.ProfileModel p
												LEFT JOIN tMasterData.DistrictModel d ON p.DistrictId = d.DistrictId
												WHERE d.DistrictId = ''' + CAST(@DistrictId AS nvarchar(50)) + '''
												UNION
												SELECT d.DistrictId, p.ProfileId, p.[Address]
												FROM  Customer.AddressBookModel p
												LEFT JOIN tMasterData.DistrictModel d ON p.DistrictId = d.DistrictId
												WHERE d.DistrictId = ''' + CAST(@DistrictId AS nvarchar(50)) + '''
									) d ON d.ProfileId = t.ProfileId AND t.ProfileAddress like N'''' + d.[Address] + ''%'' '
	END

	--Phường/xã
	IF(@WardId IS NOT NULL)
	BEGIN

	SET @queryFrom = @queryFrom + ' INNER JOIN (
												SELECT w.WardId, p.ProfileId, p.[Address]
												FROM  Customer.ProfileModel p
												LEFT JOIN tMasterData.WardModel w ON p.WardId = w.WardId
												WHERE w.WardId = ''' + CAST(@WardId AS nvarchar(50)) + '''
												UNION
												SELECT w.WardId, p.ProfileId, p.[Address]
												FROM  Customer.AddressBookModel p
												LEFT JOIN tMasterData.WardModel w ON p.WardId = w.WardId
												WHERE w.WardId = ''' + CAST(@WardId AS nvarchar(50)) + '''
									) pw ON pw.ProfileId = t.ProfileId AND t.ProfileAddress like N'''' + pw.[Address] + ''%'' '
	END

	--Mã KH
	IF(@ProfileCode IS NOT NULL AND @ProfileCode <> '')
	BEGIN
		SET @queryFrom = @queryFrom + ' INNER JOIN (
													SELECT p.ProfileId
													FROM  Customer.ProfileModel p
													WHERE p.ProfileCode LIKE N''%' + CAST(@ProfileCode AS nvarchar(50)) + '%''
										) proCode ON proCode.ProfileId = t.ProfileId '
	END

	--Tên KH
	IF(@ProfileName IS NOT NULL AND @ProfileName <> '')
	BEGIN
		SET @queryFrom = @queryFrom + ' INNER JOIN (
													SELECT p.ProfileId
													FROM  Customer.ProfileModel p
													WHERE p.ProfileName LIKE N''%' + CAST(@ProfileName AS nvarchar(500)) + '%''
										) proName ON proName.ProfileId = t.ProfileId '
	END

	--LSX ĐT
	IF(@LSXDT IS NOT NULL AND @LSXDT <> '')
	BEGIN 
		SET @queryWhere = @queryWhere + ' AND t.Property3 LIKE N''%' + CAST(@LSXDT AS nvarchar(50)) + '%'' '
	END

	--Đợt SX
	IF(@DSX IS NOT NULL AND @DSX <> '')
	BEGIN 
		IF(@Type = 'LSXC')
		BEGIN
			SET @queryWhere = @queryWhere + ' AND parent.Summary LIKE N''%' + CAST(@DSX AS nvarchar(50)) + '%'' '
		
		END
		ELSE IF(@Type = 'LSXD')
		BEGIN
			SET @queryWhere = @queryWhere + ' AND t.Summary LIKE N''%' + CAST(@DSX AS nvarchar(50)) + '%'' '
		END
	END

	--LSX SAP
	IF(@LSXSAP IS NOT NULL AND @LSXSAP <> '')
	BEGIN 
		IF(@Type = 'LSXC')
		BEGIN
			SET @queryWhere = @queryWhere + ' AND t.Summary LIKE N''%' + CAST(@LSXSAP AS nvarchar(50)) + '%'' '
		END
	END

	--Xem theo số lượng > 0
	IF(@isViewQtyHasValue IS NOT NULL AND @isViewQtyHasValue = 1)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Number2 IS NOT NULL AND t.Number2 > 0) ' 
	END
	IF(@FromQty IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Number2 IS NOT NULL AND t.Number2 >= ' + CAST(@FromQty AS nvarchar(50)) + ') ' 
	END
	IF(@ToQty IS NOT NULL)
	BEGIN
		SET @queryWhere = @queryWhere + ' AND (t.Number2 IS NOT NULL AND t.Number2 <= ' + CAST(@ToQty AS nvarchar(50)) + ') ' 
	END

	IF (@PageSize IS NOT NULL AND @PageNumber IS NOT NULL AND @FisrtType <> 'MyCalendar' AND (@IsReport IS NULL OR @IsReport = 0))
	BEGIN
		DECLARE @Offset NVARCHAR(10) = @PageSize * (@PageNumber - 1)
		SET @queryOffset = ' ORDER BY t.TaskCode DESC
							OFFSET CONVERT(INT,' + @Offset + ') ROWS
							FETCH NEXT CONVERT(INT,' + CAST(@PageSize AS NVARCHAR(10)) + ') ROWS ONLY' -- OPTION (RECOMPILE)
		
		DECLARE @StartRow INT = @PageSize * (@PageNumber - 1) + 1
		DECLARE @EndRow INT = @PageSize * @PageNumber
		SET @queryOffset2 = ' WHERE RowNum BETWEEN  ' + + CAST(@StartRow AS NVARCHAR(10)) + ' AND ' + CAST(@EndRow AS NVARCHAR(10)) + 
							' ORDER BY CASE WHEN t.StartDate IS NULL THEN 0
											ELSE 1 END, t.StartDate DESC '

	END

	--MÃ SP
	IF(@ProductCode IS NOT NULL AND @ProductCode <> '')
	BEGIN
		SET @queryWhere = @queryWhere + ' AND product.ERPProductCode LIKE N''%' + CAST(@ProductCode AS nvarchar(50)) + '%'' '
	END 

	--end query select
	SET @endQuerySelect =	'
								) t
								--Priority
								LEFT JOIN (
											SELECT CatalogCode, CatalogText_vi 
											FROM tMasterData.CatalogModel WITH(NOLOCK)
											WHERE CatalogTypeCode = ''Priority'' AND Actived = 1) pri ON t.PriorityCode = pri.CatalogCode
								--Profile
								LEFT JOIN Customer.ProfileModel p ON t.ProfileId = p.ProfileId
								LEFT JOIN tMasterData.ProvinceModel pr ON p.ProvinceId = pr.ProvinceId
								LEFT JOIN tMasterData.DistrictModel d ON p.DistrictId = d.DistrictId
								LEFT JOIN tMasterData.WardModel ward ON p.WardId = ward.WardId
								--SaleOffice
								LEFT JOIN (SELECT CatalogCode, CatalogText_vi
										   FROM tMasterData.CatalogModel WITH(NOLOCK)
										   WHERE CatalogTypeCode = ''SaleOffice'' AND Actived = 1) SaleOffice ON p.SaleOfficeCode = SaleOffice.CatalogCode
								--CheckIn
								LEFT JOIN Task.CheckInOutModel checkin ON t.TaskId = checkin.TaskId 
							'

	
	PRINT(@beginQuerySelect)
	PRINT(@querySelect)
	PRINT(@querySelect2)
	PRINT(@queryFrom)
	PRINT(@queryWhere)
	--PRINT(@queryOffset)
	PRINT(@endQuerySelect)
	PRINT(@queryOffset2)

    --EXEC (@beginQuerySelect + @querySelect + @querySelect2 + @queryFrom + @queryWhere + @queryOffset + @endQuerySelect)
	--Paging using ROW_NUMBER
	EXEC (@beginQuerySelect + @querySelect + @querySelect2 + @queryFrom + @queryWhere + @endQuerySelect  + @queryOffset2)

	DECLARE @CountRows NVARCHAR(100) = 'SELECT COUNT(*) '
	CREATE TABLE #FilteredResultsCount (FilteredResultsCount INT)

	INSERT INTO #FilteredResultsCount
	EXEC (@CountRows + @queryFrom + @queryWhere)

	SELECT @FilteredResultsCount = FilteredResultsCount FROM #FilteredResultsCount
END

/*

declare @p31 dbo.WorkFlowIdList
insert into @p31 values('6A7B21C8-4839-4791-9F98-5585520FB37F')

declare @p41 dbo.StringList
insert into @p41 values(NULL)

declare @p42 dbo.StringList
insert into @p42 values(NULL)

declare @p52 int
set @p52=NULL
exec sp_executesql N'EXEC [Task].[usp_SearchTask] @ServiceTechnicalTeamCode, @TaskCode, @Summary, @WorkFlowId, @TaskStatusCode, 
@TaskProcessCode, @Reporter, @Assignee, @ProfileId, @CompanyId, @CreateBy, @PriorityCode, @CreatedFromDate, @CreatedToDate, 
@ReceiveFromDate, @ReceiveToDate, @StartFromDate, @StartToDate, @EstimateEndFromDate, @EstimateEndToDate, @EndFromDate, @EndToDate, 
@KanbanId, @Type, @CommonMistakeCode, @ErrorCode, @ErrorTypeCode, @ConstructionUnit, @WorkFlowIdList, @AccountId, 
@TaskProcessCode_Todo, @TaskProcessCode_Processing, @TaskProcessCode_Incomplete, @TaskProcessCode_CompletedOnTime, 
@TaskProcessCode_CompletedExpire, @TaskProcessCode_Expired, @DomainImageWorkFlow, @ProductCategoryCode, @UsualErrorCodeList, 
@ProductColorCodeList, @ProfileGroupCode, @SalesSupervisorCode, @DepartmentCode, @VisitTypeCode, @CurrentCompanyCode, @Actived, 
@IsReport, @PageSize, @PageNumber, @FilteredResultsCount OUT, @CompletedEmployee, @ProvinceId, @DistrictId, @WardId, @ProfileCode, 
@ProfileName, @LSXDT, @DSX, @LSXSAP',N'@ServiceTechnicalTeamCode nvarchar(4000),@TaskCode int,@Summary nvarchar(4000),@WorkFlowId 
uniqueidentifier,@TaskStatusCode nvarchar(4000),@TaskProcessCode nvarchar(4000),@Reporter nvarchar(4000),@Assignee 
nvarchar(4000),@ProfileId uniqueidentifier,@CompanyId uniqueidentifier,@CreateBy nvarchar(4000),@PriorityCode 
nvarchar(4000),@CreatedFromDate datetime,@CreatedToDate datetime,@ReceiveFromDate datetime,@ReceiveToDate datetime,@StartFromDate 
datetime,@StartToDate datetime,@EstimateEndFromDate datetime,@EstimateEndToDate datetime,@EndFromDate datetime,@EndToDate 
datetime,@KanbanId uniqueidentifier,@Type nvarchar(4),@CommonMistakeCode nvarchar(4000),@ErrorCode nvarchar(4000),@ErrorTypeCode 
nvarchar(4000),@ConstructionUnit nvarchar(4000),@WorkFlowIdList [dbo].[WorkFlowIdList] READONLY,@AccountId 
uniqueidentifier,@TaskProcessCode_Todo bit,@TaskProcessCode_Processing bit,@TaskProcessCode_Incomplete 
bit,@TaskProcessCode_CompletedOnTime bit,@TaskProcessCode_CompletedExpire bit,@TaskProcessCode_Expired bit,@DomainImageWorkFlow 
nvarchar(4000),@ProductCategoryCode nvarchar(4000),@UsualErrorCodeList [dbo].[StringList] READONLY,@ProductColorCodeList [dbo].[StringList] READONLY,@ProfileGroupCode nvarchar(4000),@SalesSupervisorCode nvarchar(4000),@DepartmentCode nvarchar(4000),@VisitTypeCode nvarchar(4000),@CurrentCompanyCode nvarchar(4000),@Actived bit,@IsReport nvarchar(4000),@PageSize int,@PageNumber int,@FilteredResultsCount int output,@CompletedEmployee nvarchar(4000),@ProvinceId uniqueidentifier,@DistrictId uniqueidentifier,@WardId uniqueidentifier,@ProfileCode nvarchar(4000),@ProfileName nvarchar(4000),@LSXDT nvarchar(4000),@DSX nvarchar(4000),@LSXSAP nvarchar(4000)',

@ServiceTechnicalTeamCode=NULL,
@TaskCode=NULL,
@Summary=NULL,
@WorkFlowId=NULL,
@TaskStatusCode=NULL,
@TaskProcessCode=NULL,
@Reporter=NULL,
@Assignee=NULL,@ProfileId=NULL,@CompanyId=1000,@CreateBy=NULL,@PriorityCode=NULL,@CreatedFromDate=NULL,@CreatedToDate=NULL,@ReceiveFromDate=NULL,@ReceiveToDate=NULL,@StartFromDate=NULL,@StartToDate=NULL,@EstimateEndFromDate=NULL,@EstimateEndToDate=NULL,@EndFromDate=NULL,@EndToDate=NULL,@KanbanId='AF16F647-EE2B-4E83-8C84-FE15B8DC59BA',@Type=N'LSXC',@CommonMistakeCode=NULL,@ErrorCode=NULL,@ErrorTypeCode=NULL,@ConstructionUnit=NULL,@WorkFlowIdList=@p31,@AccountId='D3D0CB44-0E76-40D0-8D90-D960DFBDD53A',@TaskProcessCode_Todo=1,@TaskProcessCode_Processing=1,@TaskProcessCode_Incomplete=1,@TaskProcessCode_CompletedOnTime=1,@TaskProcessCode_CompletedExpire=1,@TaskProcessCode_Expired=1,@DomainImageWorkFlow=NULL,@ProductCategoryCode=NULL,@UsualErrorCodeList=@p41,@ProductColorCodeList=@p42,@ProfileGroupCode=NULL,@SalesSupervisorCode=NULL,@DepartmentCode=NULL,@VisitTypeCode=NULL,@CurrentCompanyCode=NULL,@Actived=NULL,@IsReport=N'False',@PageSize=10,@PageNumber=1,@FilteredResultsCount=@p52 output,@CompletedEmployee=NULL,@ProvinceId=NULL,@DistrictId=NULL,@WardId=NULL,@ProfileCode=NULL,@ProfileName=NULL,@LSXDT=NULL,@DSX=NULL,@LSXSAP=NULL
select @p52
*/
GO