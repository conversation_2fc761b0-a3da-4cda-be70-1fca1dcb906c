import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class LabeledDetailRow extends StatelessWidget {
  final String title;
  final String text;
  final TextStyle? titleStyle;
  final TextStyle? textStyle;
  final TextStyle? fontStyle; // New font style parameter

  const LabeledDetailRow({
    Key? key,
    required this.title,
    required this.text,
    this.titleStyle,
    this.textStyle,
    this.fontStyle, // New font style parameter
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Expanded(
          flex: 3,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              title,
              style: titleStyle ??
                  TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
          flex: 7,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              text,
              style: textStyle ??
                  TextStyle(
                    fontSize: 12.sp,
                  ),
            ),
          ),
        ),
      ],
    );
  }
}
