﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DepartmentRoutingMapping", Schema = "MES")]
    public partial class DepartmentRoutingMapping
    {
        [Key]
        [StringLength(50)]
        public string DepartmentCode { get; set; }
        [Key]
        [StringLength(50)]
        public string RoutingCode { get; set; }
        [StringLength(50)]
        public string PlantCode { get; set; }
        public bool? Actived { get; set; }
    }
}