import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/utils/user_helper.dart';
import '../repository/function/mainPageFunction.dart';

class TimeOutView extends StatelessWidget {
  final bool disableButton;
  final VoidCallback setButton;
  const TimeOutView({Key? key, required this.disableButton, required this.setButton}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(Icons.access_time_filled_rounded, size: 45.sp),
        SizedBox(height: 10.h),
        Text(
          "Hết phiên đăng nhập!",
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10.h),
        Text(
          "Bạn đã hết phiên đăng nhập! <PERSON>ui lòng đăng nhập trở lại",
          style: TextStyle(
            fontSize: 13.sp,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 20.h,
        ),
        ElevatedButton.icon(
          style: ButtonStyle(
            padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 5.h, horizontal: 30.w)),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r), side: const BorderSide(color: Colors.white))),
            side: MaterialStateProperty.all(
              const BorderSide(
                color: Color(0xff000000),
              ),
            ),
            backgroundColor: MaterialStateProperty.all(const Color(0xff000000)),
          ),
          onPressed: disableButton == true
              ? null
              : () async {
                  showDialog<String>(
                    barrierDismissible: false,
                    context: context,
                    builder: (BuildContext context) => WillPopScope(
                      onWillPop: () async => false,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                  // Clear UserHelper cache
                  UserHelper().clearCache();
                  bool check = await MainPageFunction.removeCurrentUser("id", "datetimeNow", "user");
                  Navigator.pop(context);
                  if (check == true) {
                    setButton();
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thành công',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 1)));
                    Future.delayed(const Duration(seconds: 0), () {
                      Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
                    });
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thất bại! Ứng dụng xảy ra lỗi!',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 2)));
                  }
                },
          icon: Icon(Icons.refresh_rounded, color: Colors.white, size: 17.sp),
          label: Text(
            "Đăng xuất",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 15.sp),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    ));
  }
}
