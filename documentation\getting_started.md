# Getting Started

This guide will help you set up your development environment, clone the repository, and run the TTF MES Mobile application.

## Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

1. **Flutter SDK**: Version 2.19.2 or higher
   - Follow the [official Flutter installation guide](https://flutter.dev/docs/get-started/install)
   - Run `flutter doctor` to verify your installation

2. **Dart SDK**: Version that comes with Flutter SDK
   - Usually installed with Flutter SDK

3. **Android Studio** (for Android development):
   - Version 4.0 or higher
   - Android SDK tools
   - Android Emulator or physical device

4. **Xcode** (for iOS development, macOS only):
   - Version 12.0 or higher
   - iOS Simulator or physical iOS device

5. **Git**: For version control
   - Follow the [official Git installation guide](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git)

6. **VS Code** (optional but recommended):
   - With Flutter and Dart plugins
   - Cursor editor is also supported

### Setting Up Flutter

1. Add Flutter to your PATH:
   ```bash
   export PATH="$PATH:[PATH_TO_FLUTTER_DIRECTORY]/flutter/bin"
   ```

2. Verify the Flutter installation:
   ```bash
   flutter doctor
   ```

3. Accept Android licenses (if developing for Android):
   ```bash
   flutter doctor --android-licenses
   ```

## Installation Guide

### Cloning the Repository

1. Clone the TTF MES Mobile repository:
   ```bash
   git clone [repository-url]
   cd TTF_MES_Mobile
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

### Configuration

1. Check the Flutter version required:
   - See the file `NOTE-flutter-version.md` for the recommended Flutter version
   - If needed, switch to the specified Flutter version using:
     ```bash
     flutter version [version]
     ```

2. Configure environment-specific settings:
   - The application uses different environments (development, QA, production)
   - Environment-specific configurations are managed in `lib/utils/appConfig.dart`

## Development Environment

### IDE Setup

#### VS Code

1. Install the following extensions:
   - Flutter
   - Dart
   - Flutter Widget Snippets

2. Configure VS Code settings:
   - Enable formatting on save
   - Set Dart line length to project standard

3. Open the project:
   ```bash
   code .
   ```

#### Android Studio

1. Install Flutter and Dart plugins:
   - Go to Preferences > Plugins
   - Search for and install Flutter plugin (Dart will be installed with it)

2. Open the project:
   - File > Open > Select the project directory

### Emulator Setup

#### Android Emulator

1. Open Android Studio
2. Go to AVD Manager
3. Create a new virtual device (Pixel 4 or similar recommended)
4. Start the emulator

#### iOS Simulator (macOS only)

1. Open a terminal and run:
   ```bash
   open -a Simulator
   ```

## Building and Running the App

### Running in Development Mode

1. Ensure your emulator/device is connected:
   ```bash
   flutter devices
   ```

2. Run the application:
   ```bash
   flutter run
   ```

3. For specific device:
   ```bash
   flutter run -d [device-id]
   ```

### Build Variants

#### Debug Build

```bash
flutter build apk --debug
```

#### Release Build

```bash
flutter build apk --release
```

For iOS (macOS only):
```bash
flutter build ios --release
```

### Useful Flutter Commands

- **Clean the project**:
  ```bash
  flutter clean
  ```

- **Analyze code**:
  ```bash
  flutter analyze
  ```

- **Run tests**:
  ```bash
  flutter test
  ```

- **Update dependencies**:
  ```bash
  flutter pub upgrade
  ```

## Troubleshooting

### Common Issues

1. **Missing dependencies**:
   - Run `flutter pub get` to fetch all dependencies

2. **Gradle build failures** (Android):
   - Check your Gradle version compatibility
   - Try running `flutter clean` and then `flutter run`

3. **iOS build issues** (macOS only):
   - Ensure Xcode is properly configured
   - Check team settings in Xcode project

4. **Flutter version mismatches**:
   - Make sure you're using the correct Flutter version as specified in `NOTE-flutter-version.md`
   - Use `flutter version [version]` to switch versions

### Getting Help

If you encounter issues not covered in this guide, you can:

1. Check the project documentation in the `docs/` and `documentation/` directories
2. Refer to the Flutter documentation at [flutter.dev](https://flutter.dev/docs)
3. Contact the development team for project-specific questions

## Next Steps

After setting up your development environment and running the application, you can:

1. Explore the codebase to understand the project structure
2. Refer to the [Technical Architecture](./technical_architecture.md) documentation
3. Read about [Core Features](./core_features.md) to understand the application's functionality
4. Check [Code Guidelines](./code_guidelines.md) before contributing to the project 