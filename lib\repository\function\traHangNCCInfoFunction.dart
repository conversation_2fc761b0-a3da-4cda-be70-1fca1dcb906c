import '../../model/DataTraHangNCC.dart';

class TraHangNCCInfoFunction {
  // static DataTraHangNCC defaultTraHangNCCInfo = DataTraHangNCC(
  //   stt: 1,
  //   maNVL: "",
  //   tenNVL: "",
  //   soLuongTra: 0,
  //   soLuongPO: 0,
  //   dvt: "",
  // );
  static DataTraHangNCC defaultTraHangNCCInfo() {
    return DataTraHangNCC(
      stt: 1,
      maNVL: "",
      tenNVL: "",
      soLuongTra: "",
      soLuongPO: "0",
      dvt: "",
    );
  }

  // static List<LsSendTraHangNCCInfo> getLsSendTraHangNCCInfo(List<TraHangNCCInfo>? lsTraHangNCCInfo, QualityControl? qualityControl,
  //     List<TraHangNCCInfoIdList?> lsSelectedInfo, List<TextEditingController> lsTextEditingController, List<List<File>> lsGetFile) {
  //   List<LsSendTraHangNCCInfo> lsSendTraHangNCCInfo = [];
  //   LsSendTraHangNCCInfo? getInformation;
  //   for (int i = 0; i < lsTraHangNCCInfo!.length; i++) {
  //     if (qualityControl!.qualityControlInformation!.isNotEmpty) {
  //       getInformation = LsSendTraHangNCCInfo(
  //           lsTraHangNCCInfo[i].qualityControlQCInformationId == " " ? "null" : lsTraHangNCCInfo[i].qualityControlQCInformationId.toString(),
  //           lsSelectedInfo[i] != null
  //               ? lsSelectedInfo[i]!.id != " "
  //                   ? lsSelectedInfo[i]!.id.toString()
  //                   : "null"
  //               : lsTraHangNCCInfo[i].qualityControlInformationId.toString(),
  //           lsTextEditingController[i].text.isNotEmpty ? lsTextEditingController[i].text : lsTraHangNCCInfo[i].notes ?? "null",
  //           lsGetFile[i]);
  //       lsSendTraHangNCCInfo.add(getInformation);
  //     } else {
  //       getInformation = LsSendTraHangNCCInfo(
  //           "null",
  //           lsSelectedInfo[i] != null
  //               ? lsSelectedInfo[i]!.id != " "
  //                   ? lsSelectedInfo[i]!.id.toString()
  //                   : "null"
  //               : "null",
  //           lsTextEditingController[i].text.isEmpty ? "null" : lsTextEditingController[i].text,
  //           lsGetFile[i]);
  //       lsSendTraHangNCCInfo.add(getInformation);
  //     }
  //   }

  //   return lsSendTraHangNCCInfo;
  // }
}
