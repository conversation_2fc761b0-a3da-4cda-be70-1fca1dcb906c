class ResultListQCNVL {
  bool? key;
  String? value;

  ResultListQCNVL({this.key,
    this.value});
}
class DeliveryStatus{
  bool? key;
  String? value;

  DeliveryStatus({this.key,
    this.value});
}
class QCStatus{
  bool? key;
  String? value;

  QCStatus({this.key,
    this.value});
}

class QCFactoryReturnDate {
  int? code;
  bool? isSuccess;
  String? message;
  List<Data>? data;


  QCFactoryReturnDate(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  QCFactoryReturnDate.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? key;
  String? value;

  Data({this.key, this.value});

  Data.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}
