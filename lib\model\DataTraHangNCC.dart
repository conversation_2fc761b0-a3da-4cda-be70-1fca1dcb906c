class DataTraHangNCC {
  int? id;
  int? stt;
  String? maNVL;
  String? tenNVL;
  String? poNumber;
  String? soLuongTra;
  String? soLuongThucTe;
  String? soLuongPO;
  String? dvt;

  DataTraHangNCC({this.stt, this.maNVL, this.tenNVL, this.poNumber, this.soLuongTra, this.soLuongThucTe, this.soLuongPO, this.dvt});

  DataTraHangNCC.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stt = json['stt'];
    maNVL = json['maNVL'];
    tenNVL = json['tenNVL'];
    poNumber = json['poNumber'];
    soLuongTra = json['soLuongTra'].toString();
    soLuongThucTe = json['soLuongThucTe'].toString();
    soLuongPO = json['soLuongPO'].toString();
    dvt = json['dvt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['stt'] = stt;
    data['maNVL'] = maNVL;
    data['tenNVL'] = tenNVL;
    data['poNumber'] = poNumber;
    data['soLuongTra'] = soLuongTra;
    data['soLuongThucTe'] = soLuongThucTe;
    data['soLuongPO'] = soLuongPO;
    data['dvt'] = dvt;
    return data;
  }
}
