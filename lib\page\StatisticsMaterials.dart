import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../Storage/storageSharedPreferences.dart';
import '../Widget/dialogWidget/DialogStatisticsMaterials.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/TableInfo.dart';
import '../element/timeOut.dart';
import '../model/GetBarcodeReceive.dart';
import '../model/getBackDataDialogStatistics.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getInventoryByListSOWBS.dart';
import '../model/getInventoryBySOWBS.dart';
import '../model/getStep.dart';
import '../model/materialUsedShift.dart';
import '../model/postGetListSOWBSByBatch.dart';
import '../model/slocAddresse.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../repository/function/materialUsedShiftFunction.dart';
import 'LostConnect.dart';

class StatisticsMaterials extends StatefulWidget {
  final String plant;
  final String token;
  final String dateTimeOld;

  const StatisticsMaterials({Key? key, required this.token, required this.plant, required this.dateTimeOld}) : super(key: key);

  @override
  _StatisticsMaterialsState createState() => _StatisticsMaterialsState();
}

class _StatisticsMaterialsState extends State<StatisticsMaterials> {
  final _controllerNumberUse = TextEditingController();
  String? _rawMaterialID;
  DataSlocAddress? _selectedSloc;
  DataGetBarcodeReceive? _dataRawMaterial;
  List<DataSlocAddress> _listDataSlocAddress = [];
  List<DataSlocAddress> _getLsDataSlocAddress = [];
  List<DataGetStep> _lsStepCode = [];
  DataGetStep? _selectStepCode;
  // DataBatch? _getBatch;

  List<SowbSs> _lsSowbs = [];

  List<TextEditingController> _soLuongSXControllers = [];

  bool _isLoading = false;
  bool _isLoadingMaterial = false;
  bool _notWifi = false;
  DataGetInventoryByListSOWBS? _dataGetInventoryByListSOWBS;
  bool _isLoadingSAP = false;
  late bool _timeOut;
  bool _disableButton = false;

  double _totalSoLuongSX = 0;

  @override
  void initState() {
    super.initState();
    _getSlocAddress();
  }

  // void _calculateTotalSoLuongSX() {
  //   double total = 0;
  //   for (var sowbs in _lsSowbs) {
  //     total += sowbs.soLuongSX ?? 0;
  //   }
  //   setState(() {
  //     _totalSoLuongSX = total;
  //   });
  // }

  void _updateTotalSoLuongSX() {
    double total = _lsSowbs.fold(0, (sum, item) => sum + (item.soLuongSX ?? 0));
    _controllerNumberUse.text = total.toStringAsFixed(3);
  }

  Future<void> _getTonKhoSAP(GetBackDataDialogStatistic? check, BuildContext context) async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoadingSAP = true;
      });
      final data = await MaterialUsedShiftFunction.getInventoryByListSOWBS(check!.getInventoryBySOWBS!, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoadingSAP = false;
      });
      if (data != null) {
        setState(() {
          _lsSowbs = check.getInventoryBySOWBS!.sowbSs ?? [];
          _dataGetInventoryByListSOWBS = data;
        });
        _soLuongSXControllers = List.generate(
          _lsSowbs.length,
          (index) => TextEditingController(text: _lsSowbs[index].soLuongSX?.toString() ?? '0'),
        );
        _updateTotalSoLuongSX();
      } else {
        if (!mounted) return;
        setState(() {
          _lsSowbs = [];
          _dataGetInventoryByListSOWBS = null;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin tồn kho SAP!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingSAP = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingSAP = false;
      });
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            error.toString(),
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  Future<void> _getDataRawMaterial(String materialID, BuildContext context) async {
    try {
      setState(() {
        _isLoadingMaterial = true;
        _controllerNumberUse.text = "";
      });
      final getRawMaterial = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);
      if (!mounted) return;
      if (getRawMaterial != null) {
        // final getBatch = await  ExportWareHouseFunction.fetchBatch(materialID, widget.token);
        if (!mounted) return;
        setState(() {
          _rawMaterialID = materialID;
          _isLoadingMaterial = false;
          // _getBatch = getBatch;
          _dataRawMaterial = getRawMaterial;
        });
        if (_selectedSloc != null && _dataRawMaterial != null && _isLoadingMaterial == false) {
          PostGetListSOWBSByBatch postGetListSOWBSByBatch = PostGetListSOWBSByBatch(
            productCode: _dataRawMaterial!.productCode ?? "",
            plant: widget.plant,
            sloc: _selectedSloc!.sloc,
            batchNumber: _dataRawMaterial!.batchNumber,
          );
          final check = await showDialog<GetBackDataDialogStatistic>(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DialogStatisticsMaterials(postGetListSOWBSByBatch: postGetListSOWBSByBatch, token: widget.token));
          if (!mounted) return;
          debugPrint("Check:");
          if (check == null) return;
          debugPrint("Check != null");
          if (check.isSave == true) {
            if (check.getInventoryBySOWBS != null) {
              _getTonKhoSAP(check, context);
            }
          } else {
            if (!mounted) return;
            setState(() {
              _selectedSloc = null;
              _lsSowbs = [];
              _dataGetInventoryByListSOWBS = null;
            });
          }
          // else{
          //   setState(() {
          //     _selectedSloc = null;
          //     _lsSowbs =  [];
          //     _dataGetInventoryByListSOWBS = null;
          //     _getBatch = null;
          //     _dataRawMaterial = null;
          //   });
          // }
        }
      } else {
        if (!mounted) return;
        setState(() {
          _dataRawMaterial = null;
          _isLoadingMaterial = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin pallet NVL!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi vui lòng thử lại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setStage(DataGetStep? value, BuildContext context) {
    setState(() {
      _selectStepCode = value!;
    });
  }

  Future<void> _setSloc(DataSlocAddress? value, BuildContext context) async {
    setState(() {
      _selectedSloc = value;
      _controllerNumberUse.text = "";
    });
    if (_dataRawMaterial != null) {
      if (_isLoadingMaterial == false) {
        PostGetListSOWBSByBatch postGetListSOWBSByBatch = PostGetListSOWBSByBatch(
            productCode: _dataRawMaterial!.productCode ?? "", plant: widget.plant, sloc: value!.sloc, batchNumber: _dataRawMaterial!.batchNumber);
        final check = await showDialog<GetBackDataDialogStatistic>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) => DialogStatisticsMaterials(postGetListSOWBSByBatch: postGetListSOWBSByBatch, token: widget.token));
        if (!mounted) return;
        if (check == null) return;
        if (check.isSave == true) {
          if (check.getInventoryBySOWBS != null) {
            await _getTonKhoSAP(check, context);
          }
        } else {
          if (!mounted) return;
          setState(() {
            _selectedSloc = null;
            _lsSowbs = [];
            _dataGetInventoryByListSOWBS = null;
          });
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    }
  }

  Future<void> _getSlocAddress() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _isLoading = true;
          _notWifi = false;
          _timeOut = false;
        });
        final data = await Future.wait(
            [ImportWareHouseFunction.fetchSlocAddress(widget.plant, widget.token), MaterialUsedShiftFunction.fetchStepCode(widget.token)]);
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          if (data.isNotEmpty) {
            if (data[0] != null) {
              _listDataSlocAddress = (data[0] as List<DataSlocAddress>?)!;
              _getLsDataSlocAddress = _listDataSlocAddress.map((e) => DataSlocAddress.clone(e)).toList();
              _getLsDataSlocAddress.sort((a, b) {
                return a.sloc!.toLowerCase().compareTo(b.sloc!.toLowerCase());
              });
            }
            if (data[1] != null) {
              _lsStepCode = (data[1] as List<DataGetStep>?)!;
            }
          }
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _notWifi = true;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _notWifi = false;
        _timeOut = false;
      });
    }
  }

  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }
  // void checkError(){
  //   setState(() {
  //   if(_selectedSloc == null){
  //     if(_errorSelectedSloc != true) {
  //         _errorSelectedSloc = true;
  //     }
  //   }else{
  //     if(_errorSelectedSloc != false) {
  //         _errorSelectedSloc = false;
  //     }
  //   }
  //   if(_selectedDataSOWBSByBatch == null){
  //     if(_errorSelectedSOWBS != true) {
  //       _errorSelectedSOWBS = true;
  //     }
  //   }else{
  //     if(_errorSelectedSOWBS != false) {
  //       _errorSelectedSOWBS = false;
  //     }
  //   }
  //   if(_controllerNumberUse.text.isEmpty){
  //     if(_errorQuantity != true){
  //       _errorQuantity = true;
  //     }
  //   }else{
  //     if(_errorQuantity != false){
  //       _errorQuantity = false;
  //     }
  //   }
  //   });
  // }
  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  String _getDisplayText(SowbSs item) {
    if (item.so != null && item.so != "" && item.soLine != null && item.soLine != "") {
      return "${item.so}/${item.soLine}";
    } else if (item.wbs != null && item.wbs != "") {
      return item.wbs!;
    } else {
      return "Tồn trơn";
    }
  }

  @override
  void dispose() {
    _controllerNumberUse.dispose();
    // dispose _soLuongSXControllers
    for (var controller in _soLuongSXControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> dummyTheTreo = [
      {'LSX': 'DHW05D23DH 291013279', 'RawMaterialCardManualId': 'a9fb4939-158e-4cad-93d7-3ab4a8bb58b9'},
      {'LSX': 'DHW29D23DS 280010822', 'RawMaterialCardManualId': '8de35a06-c12a-472f-a549-c42695725b36'},
      {'LSX': '100 Test', 'RawMaterialCardManualId': 'dc93ece0-4c54-4643-9cc5-a23607a94fb3'},
      {'LSX': '270012076 DHC08624D1', 'RawMaterialCardManualId': '287714ae-b368-4b8c-9b10-e1a7b6324423'},
      {'LSX': 'error', 'RawMaterialCardManualId': 'e1548c7e-d28f-48e2-beb2-d0555b9096bd'},
      {'LSX': 'Tồn trơn', 'RawMaterialCardManualId': '5c9794f6-e2db-4aa2-a879-4d1f104d2f6d'},
      {'LSX': 'PRD', 'RawMaterialCardManualId': '3aaa7ab0-50ed-40ee-ab32-41e1abda6d50'},
      {'LSX': 'PRD 2', 'RawMaterialCardManualId': '9f387648-1386-4399-9bb2-7abf48d82268'},
    ];

    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton)))
        : Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'Thống kê NVL',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: _notWifi == true
                ? LostConnect(checkConnect: () => _getSlocAddress())
                : _isLoading == true
                    ? const Center(child: CircularProgressIndicator())
                    : _getLsDataSlocAddress.isEmpty || _lsStepCode.isEmpty
                        ? Center(
                            child: Text(
                                _getLsDataSlocAddress.isEmpty
                                    ? "Lấy thông tin danh sách sloc thất bại!"
                                    : "Lấy thông tin danh sách Stepcode thất bại!",
                                style: TextStyle(
                                  fontSize: 15.sp,
                                ),
                                textAlign: TextAlign.center))
                        : GestureDetector(
                            onTap: () {
                              // Dismiss the keyboard when tapping outside of the text field
                              FocusScope.of(context).unfocus();
                            },
                            child: SingleChildScrollView(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                kDebugMode
                                    ? RenderDebugButtons(dummyTheTreo, (item) async {
                                        // await _getDataRawMaterial(data.materialID.toString(), context);
                                        await _getDataRawMaterial(item["RawMaterialCardManualId"], context);
                                      }, 'LSX')
                                    : Container(),
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                  decoration: const BoxDecoration(color: Colors.white),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Align(
                                        alignment: Alignment.centerRight,
                                        child: Container(
                                          decoration: const BoxDecoration(),
                                          child: ElevatedButton.icon(
                                            style: ButtonStyle(
                                              shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                              side: MaterialStateProperty.all(
                                                const BorderSide(
                                                  color: Color(0xff303F9F),
                                                ),
                                              ),
                                              backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                            ),
                                            onPressed: () async {
                                              String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                              DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                              DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                              if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                Platform.isAndroid
                                                    ? showDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                    : showCupertinoDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                              } else {
                                                if (_isLoadingMaterial == true) {
                                                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                      backgroundColor: Colors.black,
                                                      content: Text(
                                                        'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
                                                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                      ),
                                                      duration: const Duration(seconds: 2)));
                                                } else {
                                                  final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                  if (!mounted) return;
                                                  if (data == null) return;
                                                  if ((data as GetBackDataQRCodePage).isScan == true) {
                                                    if (!mounted) return;
                                                    await _getDataRawMaterial(data.materialID.toString(), context);
                                                  }
                                                }
                                              }
                                            },
                                            icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                            label: Text(
                                              'Quét mã',
                                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.h),
                                      TableInfo(
                                        textCL1: "Plant:",
                                        textCL2: widget.plant,
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      IntrinsicHeight(
                                        child: Row(
                                          children: <Widget>[
                                            Expanded(
                                              flex: 4,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xff303F9F),
                                                  border: Border(
                                                    left: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Text(
                                                  "Sloc:",
                                                  style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                ),
                                              ),
                                            ),
                                            Expanded(
                                              flex: 6,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFFFFFFF),
                                                  border: Border(
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                        borderRadius: BorderRadius.circular(3.r),
                                                      ),
                                                      child: DropdownButtonHideUnderline(
                                                        child: DropdownButton<DataSlocAddress>(
                                                          isExpanded: true,
                                                          isDense: true,
                                                          itemHeight: null,
                                                          value: _selectedSloc,
                                                          iconSize: 15.sp,
                                                          style: const TextStyle(color: Colors.white),
                                                          onChanged: (DataSlocAddress? value) {
                                                            _setSloc(value, context);
                                                          },
                                                          items: _getLsDataSlocAddress.map((DataSlocAddress wareHouse) {
                                                            return DropdownMenuItem<DataSlocAddress>(
                                                                value: wareHouse,
                                                                child: Padding(
                                                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                  child: Text(
                                                                    wareHouse.slocDisplay ?? " ",
                                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                  ),
                                                                ));
                                                          }).toList(),
                                                          selectedItemBuilder: (BuildContext context) {
                                                            return _getLsDataSlocAddress.map<Widget>((DataSlocAddress wareHouse) {
                                                              return Text(wareHouse.slocDisplay.toString(),
                                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                  overflow: TextOverflow.ellipsis);
                                                            }).toList();
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    // SizedBox(height: _errorSelectedSloc ? 5.h : 0),
                                                    // ContainerError.widgetError(_errorSelectedSloc, 'Vui lòng chọn sloc'),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      TableInfoNoTop(
                                        textCL1: "Mã NVL:",
                                        textCL2: _isLoadingMaterial == true
                                            ? "...Loading"
                                            : _dataRawMaterial == null
                                                ? ""
                                                : _dataRawMaterial!.productCode ?? " ",
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      TableInfoNoTop(
                                        textCL1: "Tên NVL:",
                                        textCL2: _isLoadingMaterial == true
                                            ? "...Loading"
                                            : _dataRawMaterial == null
                                                ? ""
                                                : _dataRawMaterial!.productName ?? " ",
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      TableInfoNoTop(
                                        textCL1: "Số lô:",
                                        textCL2: _isLoadingMaterial == true
                                            ? "...Loading"
                                            : _dataRawMaterial == null
                                                ? " "
                                                : _dataRawMaterial!.batchNumber == "false"
                                                    ? ""
                                                    : _dataRawMaterial!.batchNumber ?? " ",
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      TableInfoNoTop(
                                        textCL1: "Ngày sản xuất:",
                                        textCL2: _isLoadingMaterial == true
                                            ? "...Loading"
                                            : _dataRawMaterial == null
                                                ? ""
                                                : _dataRawMaterial!.manuDate ?? " ",
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      IntrinsicHeight(
                                        child: Row(
                                          children: <Widget>[
                                            // Expanded(
                                            //   flex: 6,
                                            //   child: Container(
                                            //       height: double.infinity,
                                            //       padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                            //       decoration: BoxDecoration(
                                            //         color: const Color(0xFFFFFFFF),
                                            //         border: Border(
                                            //           right: BorderSide(
                                            //             color: Colors.black,
                                            //             width: 0.5.w,
                                            //           ),
                                            //           bottom: BorderSide(
                                            //             color: Colors.black,
                                            //             width: 0.5.w,
                                            //           ),
                                            //         ),
                                            //       ),
                                            //       child: Align(
                                            //         alignment: Alignment.centerLeft,
                                            //         child: _isLoadingSAP == true
                                            //             ? Text("...Loading", style: TextStyle(fontSize: 12.sp))
                                            //             : Column(
                                            //                 crossAxisAlignment: CrossAxisAlignment.start,
                                            //                 mainAxisAlignment: MainAxisAlignment.start,
                                            //                 children: List.generate(
                                            //                   _lsSowbs.length,
                                            //                   (index) => Text(
                                            //                     _getDisplayText(_lsSowbs[index]),
                                            //                     style: TextStyle(
                                            //                       fontSize: 12.sp,
                                            //                     ),
                                            //                   ),
                                            //                 ),
                                            //               ),
                                            //       )),
                                            // ),
                                            Expanded(
                                              flex: 6,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFFFFFFF),
                                                  border: Border(
                                                    right: BorderSide(color: Colors.black, width: 0.5.w),
                                                    bottom: BorderSide(color: Colors.black, width: 0.5.w),
                                                  ),
                                                ),
                                                child: Align(
                                                  alignment: Alignment.centerLeft,
                                                  child: _isLoadingSAP == true
                                                      ? Text("...Loading", style: TextStyle(fontSize: 12.sp))
                                                      : SingleChildScrollView(
                                                          child: Table(
                                                            border: TableBorder.all(color: Colors.black, width: 0.5.w),
                                                            columnWidths: const <int, TableColumnWidth>{
                                                              0: FlexColumnWidth(5),
                                                              1: FlexColumnWidth(2),
                                                              2: FlexColumnWidth(2),
                                                            },
                                                            children: [
                                                              // Table Header
                                                              TableRow(
                                                                decoration: const BoxDecoration(color: Color(0xff303F9F)),
                                                                children: [
                                                                  Padding(
                                                                    padding: EdgeInsets.all(3.w),
                                                                    child: Text(
                                                                      "SO/WBS",
                                                                      style: TextStyle(
                                                                          fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                                      textAlign: TextAlign.center,
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: EdgeInsets.all(3.w),
                                                                    child: Text(
                                                                      "Tồn kho",
                                                                      style: TextStyle(
                                                                          fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                                      textAlign: TextAlign.center,
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: EdgeInsets.all(3.w),
                                                                    child: Text(
                                                                      "Số lượng SX",
                                                                      style: TextStyle(
                                                                          fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                                      textAlign: TextAlign.center,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              // Table Data
                                                              ..._lsSowbs.asMap().entries.map((entry) {
                                                                final index = entry.key;
                                                                final item = entry.value;
                                                                return TableRow(
                                                                  children: [
                                                                    TableCell(
                                                                      child: Padding(
                                                                        padding: EdgeInsets.all(3.w),
                                                                        child: Text(
                                                                          _getDisplayText(item),
                                                                          style: TextStyle(fontSize: 11.sp),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    TableCell(
                                                                      child: SizedBox(
                                                                        height: 23, // Set a fixed height for the cell
                                                                        child: Center(
                                                                          // Center widget for both horizontal and vertical centering
                                                                          child: Text(
                                                                            item.soLuong?.toString() ?? "",
                                                                            style: TextStyle(fontSize: 11.sp),
                                                                            textAlign: TextAlign.center,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    // TableCell(
                                                                    //   child: Padding(
                                                                    //     padding: EdgeInsets.all(3.w),
                                                                    //     child: Text(
                                                                    //       item.soLuongSX?.toString() ?? "",
                                                                    //       style: TextStyle(fontSize: 11.sp),
                                                                    //     ),
                                                                    //   ),
                                                                    // ),
                                                                    TableCell(
                                                                      child: Container(
                                                                        margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                                                                        decoration: BoxDecoration(
                                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                                        ),
                                                                        child: TextFormField(
                                                                          // initialValue: item.soLuongSX?.toString() ?? "",
                                                                          // controller: TextEditingController(text: item.soLuongSX?.toString() ?? ""),
                                                                          controller: _soLuongSXControllers[index],
                                                                          style: TextStyle(fontSize: 11.sp),
                                                                          keyboardType: TextInputType.number,
                                                                          textAlign: TextAlign.center,
                                                                          decoration: InputDecoration(
                                                                            border: InputBorder.none,
                                                                            focusedBorder: InputBorder.none,
                                                                            enabledBorder: InputBorder.none,
                                                                            errorBorder: InputBorder.none,
                                                                            disabledBorder: InputBorder.none,
                                                                            isDense: true,
                                                                            contentPadding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 8.w),
                                                                          ),
                                                                          onChanged: (value) {
                                                                            setState(() {
                                                                              double inputValue = double.tryParse(value) ?? 0;
                                                                              double maxValue = item.soLuong ?? 0;
                                                                              if (inputValue > maxValue) {
                                                                                item.soLuongSX = maxValue;
                                                                                _soLuongSXControllers[index].text = maxValue.toString();
                                                                              } else {
                                                                                item.soLuongSX = inputValue;
                                                                              }
                                                                            });
                                                                            _updateTotalSoLuongSX();
                                                                          },
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                );
                                                              }).toList(),
                                                            ],
                                                          ),
                                                        ),
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      IntrinsicHeight(
                                        child: Row(
                                          children: <Widget>[
                                            Expanded(
                                              flex: 4,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xff303F9F),
                                                  border: Border(
                                                    left: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Text(
                                                  "Công đoạn sử dụng:",
                                                  style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                ),
                                              ),
                                            ),
                                            Expanded(
                                              flex: 6,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFFFFFFF),
                                                  border: Border(
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                        borderRadius: BorderRadius.circular(3.r),
                                                      ),
                                                      child: DropdownButtonHideUnderline(
                                                        child: DropdownButton<DataGetStep>(
                                                          isExpanded: true,
                                                          isDense: true,
                                                          itemHeight: null,
                                                          value: _selectStepCode,
                                                          iconSize: 15.sp,
                                                          style: const TextStyle(color: Colors.white),
                                                          onChanged: (DataGetStep? value) {
                                                            _setStage(value, context);
                                                          },
                                                          items: _lsStepCode.map((DataGetStep stepCode) {
                                                            return DropdownMenuItem<DataGetStep>(
                                                                value: stepCode,
                                                                child: Padding(
                                                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                  child: Text(
                                                                    stepCode.value ?? " ",
                                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                  ),
                                                                ));
                                                          }).toList(),
                                                          selectedItemBuilder: (BuildContext context) {
                                                            return _lsStepCode.map<Widget>((DataGetStep stepCode) {
                                                              return Text(stepCode.value.toString(),
                                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                  overflow: TextOverflow.ellipsis);
                                                            }).toList();
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    // SizedBox(height: _errorSelectedSloc ? 5.h : 0),
                                                    // ContainerError.widgetError(_errorSelectedSloc, 'Vui lòng chọn sloc'),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      TableInfoNoTop(
                                        textCL1: "Tồn SAP:",
                                        textCL2: _isLoadingSAP == true
                                            ? "...Loading"
                                            : _dataGetInventoryByListSOWBS == null
                                                ? ""
                                                : _dataGetInventoryByListSOWBS!.quantity == null
                                                    ? ""
                                                    : "${_dataGetInventoryByListSOWBS!.quantity!.toStringAsFixed(3)} ${_dataGetInventoryByListSOWBS!.unit ?? ""}",
                                        colorCL1: 0xff303F9F,
                                        colorCL2: 0xFFFFFFFF,
                                      ),
                                      IntrinsicHeight(
                                        child: Row(
                                          children: <Widget>[
                                            Expanded(
                                              flex: 4,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xff303F9F),
                                                  border: Border(
                                                    left: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Text(
                                                  "Số lượng sử dụng:",
                                                  style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                ),
                                              ),
                                            ),
                                            Expanded(
                                              flex: 6,
                                              child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFFFFFFF),
                                                  border: Border(
                                                    right: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                    bottom: BorderSide(
                                                      color: Colors.black,
                                                      width: 0.5.w,
                                                    ),
                                                  ),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Expanded(
                                                          flex: 5,
                                                          child: Container(
                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                            decoration: BoxDecoration(
                                                                border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                borderRadius: BorderRadius.circular(3.r)),
                                                            child: TextFormField(
                                                              enabled: _dataGetInventoryByListSOWBS != null,
                                                              readOnly: true,
                                                              maxLines: null,
                                                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                              inputFormatters: <TextInputFormatter>[
                                                                FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d*')),
                                                                CommaTextInputFormatter()
                                                              ],
                                                              textAlign: TextAlign.left,
                                                              controller: _controllerNumberUse,
                                                              style: TextStyle(fontSize: 12.sp),
                                                              decoration: InputDecoration(
                                                                border: InputBorder.none,
                                                                focusedBorder: InputBorder.none,
                                                                enabledBorder: InputBorder.none,
                                                                errorBorder: InputBorder.none,
                                                                disabledBorder: InputBorder.none,
                                                                filled: true,
                                                                isDense: true,
                                                                fillColor: Colors.white,
                                                                hintStyle: TextStyle(fontSize: 12.sp),
                                                                contentPadding: EdgeInsets.zero,
                                                              ),
                                                              // onChanged: (value){
                                                              //   if(_controllerNumberUse.text.isEmpty) {
                                                              //     if (_errorQuantity != true) {
                                                              //       setState(() {
                                                              //         _errorQuantity = true;
                                                              //       });
                                                              //     }
                                                              //   }else{
                                                              //     if (_errorQuantity != false) {
                                                              //       setState(() {
                                                              //         _errorQuantity = false;
                                                              //       });
                                                              //     }
                                                              //   }
                                                              // },
                                                            ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 1,
                                                          child: Center(
                                                            child: Text(
                                                              _isLoadingMaterial == true
                                                                  ? "..."
                                                                  : _dataRawMaterial == null
                                                                      ? ""
                                                                      : _dataGetInventoryByListSOWBS == null
                                                                          ? ""
                                                                          : _dataGetInventoryByListSOWBS!.unit ?? "",
                                                              style: TextStyle(
                                                                fontSize: 12.sp,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    // SizedBox(height: _errorQuantity == true ? 5.h : 0),
                                                    // ContainerError.widgetError(_errorQuantity, 'Vui lòng nhập số lượng sử dụng'),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // SizedBox(height: _dataRawMaterial == null ?0:10.h),
                                      // Visibility(
                                      //   visible: _dataRawMaterial != null,
                                      //   child: Align(
                                      //     alignment: Alignment.centerRight,
                                      //     child: Text(
                                      //         _dataRawMaterial == null ? "":"Được tạo bởi ${_dataRawMaterial!.createBy ?? " "} vào lúc ${_dataRawMaterial!.createTime ?? " "}",
                                      //         style: TextStyle(
                                      //             fontSize: 12.sp, color: Colors.grey),
                                      //         textAlign: TextAlign.end)),
                                      // )
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10.h),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                                  width: double.infinity,
                                  decoration: const BoxDecoration(),
                                  child: ElevatedButton(
                                    style: ButtonStyle(
                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                      side: MaterialStateProperty.all(
                                        const BorderSide(
                                          color: Color(0xff303F9F),
                                        ),
                                      ),
                                      backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                    ),
                                    onPressed: () async {
                                      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                        Platform.isAndroid
                                            ? showDialog(
                                                context: context,
                                                barrierDismissible: false,
                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                            : showCupertinoDialog(
                                                context: context,
                                                barrierDismissible: false,
                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                      } else {
                                        if (_isLoadingMaterial == false && _isLoadingSAP == false) {
                                          if (_controllerNumberUse.text.isNotEmpty &&
                                              _dataRawMaterial != null &&
                                              _selectedSloc != null &&
                                              _selectStepCode != null) {
                                            if (double.parse(_controllerNumberUse.text) > _dataGetInventoryByListSOWBS!.quantity!) {
                                              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                  backgroundColor: Colors.black,
                                                  content: Text(
                                                    'Số lượng nhập không được vượt quá số lượng tồn SAP',
                                                    style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                  ),
                                                  duration: const Duration(seconds: 1)));
                                            } else {
                                              FocusScope.of(context).unfocus();
                                              MaterialUsedShift materialUsedShift = MaterialUsedShift(
                                                batchNumber: _dataRawMaterial!.batchNumber,
                                                slocId: _selectedSloc!.slocId,
                                                storageBinId: _selectedSloc!.defaultStorageBinId,
                                                rawMaterialCardId: _rawMaterialID,
                                                quantity: double.parse(_controllerNumberUse.text),
                                                unit: _dataGetInventoryByListSOWBS!.unit,
                                                stepCode: _selectStepCode!.key,
                                                sowbsQuantities: _lsSowbs,
                                              );
                                              debugPrint(jsonEncode(materialUsedShift));
                                              await MaterialUsedShiftFunction.getMessasgeMaterialUsedShift(materialUsedShift, widget.token, context);
                                            }
                                          } else {
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                backgroundColor: Colors.black,
                                                content: Text(
                                                  'Vui lòng nhập đủ thông tin!',
                                                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                ),
                                                duration: const Duration(seconds: 1)));
                                          }
                                        } else {
                                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              backgroundColor: Colors.black,
                                              content: Text(
                                                'Vui lòng chờ quá trình lấy thông tin hoàn thành!',
                                                style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                              ),
                                              duration: const Duration(seconds: 1)));
                                        }
                                      }
                                    },
                                    child: Container(
                                      margin: EdgeInsets.symmetric(vertical: 12.h),
                                      child: Text(
                                        'LƯU',
                                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                      ),
                                    ),
                                  ),
                                ),
                                // Container(
                                //   padding: EdgeInsets.symmetric(horizontal: 10.w),
                                //   width: double.infinity,
                                //   decoration: const BoxDecoration(),
                                //   child: ElevatedButton(
                                //     style: ButtonStyle(
                                //       shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                //           RoundedRectangleBorder(
                                //               borderRadius: BorderRadius.circular(5.r),
                                //               side: const BorderSide(color: Colors.white)
                                //           )),
                                //       side: MaterialStateProperty.all(
                                //         const BorderSide(
                                //           color: Color(0xff303F9F),
                                //         ),
                                //       ),
                                //       backgroundColor:
                                //       MaterialStateProperty.all(const Color(0xff303F9F)),
                                //     ),
                                //     onPressed: () {
                                //       showDialog(
                                //           context: context,
                                //           barrierDismissible: false,
                                //           builder: (BuildContext context) =>
                                //           const DialogStatisticsMaterials());
                                //     },
                                //     child: Container(
                                //       margin: EdgeInsets.symmetric(vertical: 12.h),
                                //       child: Text(
                                //         'Show',
                                //         style: TextStyle(
                                //             color: Colors.white,
                                //             fontWeight: FontWeight.bold,
                                //             fontSize: 13.sp),
                                //       ),
                                //     ),
                                //   ),
                                // ),
                              ],
                            )),
                          ));
  }
}
