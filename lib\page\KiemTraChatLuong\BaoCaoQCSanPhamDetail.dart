import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/HangMucKiemTraVm.dart';
import 'package:ttf/page/KiemTraChatLuong/element/CaNhanGayLoiParentTypeAhead.dart';
import 'package:ttf/page/KiemTraChatLuong/element/CaNhanGayLoiTypeAhead.dart';
import 'package:ttf/page/KiemTraChatLuong/element/LoadingOverlay.dart';
import 'package:ttf/page/KiemTraChatLuong/model/TypeAheadCaNhanGayLoi.dart';
import '../../Widget/dialogWidget/DialogErrorQuality.dart';
import '../../Widget/dialogWidget/DialogImage.dart';
import '../../Widget/dialogWidget/DialogQualityInformation.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../../element/ButtonAddNewCardErrorQuatity.dart';
import '../../element/DropdownQualityView.dart';
import '../../element/ImageQuatity.dart';
import '../../element/ListChoseFileQuality.dart';
import '../../element/QualityErrorValidate.dart';
import '../../element/QualityTitle.dart';
import '../../element/RowDetail.dart';
import '../../element/TitleQuality.dart';
import '../../element/listImagePicker.dart';
import '../../element/timeOut.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/QuantityInformationSelectedInfor.dart';
import '../../model/dropdownDefectLevel.dart';
import '../../model/mulitListImageFile.dart';
import '../../model/multiSelectedErrorQuality.dart';
import '../../model/qualityControlApi.dart';
import '../../model/rawMaterialCard.dart';
import '../../model/typeAheadErrorQuatity.dart';
import '../../model/userModel.dart';
import '../../repository/function/imageFunction.dart';
import '../../repository/function/importWareHouseFunction.dart';
import '../../repository/function/qualityControlDetailFunction.dart';
import '../../repository/function/qualityControlErrorFunction.dart';
import '../../repository/function/qualityControlFunction.dart';
import '../../repository/function/qualityControlInformationFunciton.dart';
import '../../utils/ui_helpers.dart';
import '../BottomNavigatorBarComponent.dart';
import '../LostConnect.dart';
import 'model/CongDoanInfoVm.dart';

const String _errorSelectCongDoanBTP = 'Vui lòng chọn công đoạn BTP';

// Kiểm tra chất lượng
class BaoCaoQCSanPhamDetail extends StatefulWidget {
  final String qualityControlId;
  final String dateTimeOld;
  final String qrCode;
  final String fromPage;
  final DataUser user;
  const BaoCaoQCSanPhamDetail(
      {Key? key, required this.qualityControlId, required this.dateTimeOld, required this.qrCode, required this.fromPage, required this.user})
      : super(key: key);

  @override
  State<BaoCaoQCSanPhamDetail> createState() => _BaoCaoQCSanPhamDetailState();
}

class _BaoCaoQCSanPhamDetailState extends State<BaoCaoQCSanPhamDetail> with WidgetsBindingObserver {
  // Optional variables
  QualityControlDetail? _qualityControlDetail;
  QualityControlModel? _qualityControlModel;
  CongDoanInfoVm? _congDoanInfoVm;
  QualityControl? _qualityControl;
  String _barcode = "";
  DataRawMeterial? _dataRawMaterial;
  TestMethodList? _selectedTestMethod;

  DropdownItemList? _selectedCongDoanNho;

  DropdownItemList? _selectedLimitCritical;
  DropdownItemList? _selectedLimitHigh;
  DropdownItemList? _selectedLimitLow;
  // DropdownItemList? _selectedCaNhanGayLoi;

  SamplingLevelList? _selectedLevel; // Mức độ lấy mẫu KT
  // ResultList? _selectedResult;
  ResultList? _selectedResultDetail;
  List<ThongTinKiemTra>? _lsThongTinKiemTraSelectMasterData;
  List<ErrorList>? _lsErrorList;

  // Lists with known types
  List<QualityTypeList> _qualityTypeList = [];
  List<TestMethodList> _lsTestMethodList = [];

  List<DropdownItemList> _lsCongDoanNhoMasterData = [];
  List<DropdownItemList> _lsCongDoanLoiMasterData = [];

  List<DropdownItemList> _lsPhuongAnXuLyMasterData = [];

  List<DropdownItemList> _lsLimitCriticalList = [];
  List<DropdownItemList> _lsLimitHighList = [];
  List<DropdownItemList> _lsLimitLowList = [];
  List<DropdownItemList> _lsCaNhanGayLoiMasterData = [];
  List<DropdownItemList> _lsCaNhanGayLoiAllMasterData = [];

  List<HangMucKiemTraVm> _lsHangMucKiemTraMasterData = []; // new

  List<ResultList> _lsResultList = [];
  List<SamplingLevelList> _lsSamplingLevelList = [];
  List<Error> _lsError = [];
  List<QualityControlInformation> _lsThongTinKiemTra = [];
  List<DataGetDefectLevel> _lsDataGetDefetchLevel = []; // NANG, NHE, NGHIEMTRONG

  // Lists with controller types
  TextEditingController _controllerMauHoanThien = TextEditingController();
  TextEditingController _controllerPONumber = TextEditingController();
  TextEditingController _controllerHideDetailLever = TextEditingController();
  TextEditingController _controllerMucChapNhan = TextEditingController();

  TextEditingController _controllerInspectLotQuantity = TextEditingController();
  TextEditingController _controllerInspectQuantityDetail = TextEditingController();

  TextEditingController _controllerTongSoSanPhamLoi = TextEditingController();

  TextEditingController _controllerLSXSAP = TextEditingController();

  bool _isLSXSAPSelected = false;
  bool _isTTFCode = false;

  List<TextEditingController> _lsControllerThongTinKiemTra = [];
  List<TextEditingController> _lsControllerSoSanPhamLoi = [];
  List<TextEditingController> _lsControllerGhiChu = [];

// Thông tin lỗi

  List<TextEditingController> _lsControllerError = [];
  List<ErrorList?> _lsSelectedError = [];
  List<TextEditingController> _lsControllerSoLuongLoi = [];
  List<DataGetDefectLevel?> _lsSelectedMucDoLoi = [];
  List<TextEditingController> _lsControllerGhiChuLoi = [];
  List<TextEditingController> _lsControllerCaNhanLoiParent = [];

// New
  List<TextEditingController> _lsControllerCaNhanLoi1QuanDoc = [];
  List<TextEditingController> _lsControllerCaNhanLoi2ToTruong = [];
  List<TextEditingController> _lsControllerCaNhanLoi3QAQC = [];
  List<TextEditingController> _lsControllerCaNhanLoi4KCS = [];

  List<List<File>> _lsFileHinhAnhLoi = [];

  List<DropdownItemList?> _lsSelectedCongDoanLoi = [];

  List<DropdownItemList?> _lsSelectedPhuongAnXuLy = [];

  List<TextEditingController> _lsControllerNhaMayLoi = [];
  List<TextEditingController> _lsControllerPhanXuongLoi = [];
  List<TextEditingController> _lsControllerToChuyenLoi = [];

  //  Cá nhân gây lỗi
  List<TextEditingController> _lsControllerCaNhanGayLoi = [];
  List<DropdownItemList?> _lsSelectedCaNhanGayLoi = [];

  //  Cá nhân gây lỗi new many
  List<List<TextEditingController>> _lsControllerCaNhanGayLoiMany = [];
  List<List<DropdownItemList?>> _lsSelectedCaNhanGayLoiMany = []; // New: 10 cá nhân gây lỗi

// End Thông tin lỗi

  List<ThongTinKiemTra?> _lsSelectedThongTinKiemTra = [];
  List<File> _lsFileHeader = [];
  List<List<File>> _lsFileThongTinKiemTra = [];

  List<bool> _lsErrorInfor = [];
  List<bool> _checkVisiButtonThongTinKiemTra = [];
  List<bool> _checkVisiButtonError = [];

// List with int types
  List<int> _lsGetIndexInfo = [];
  List<int> _lsGetIndexError = [];
  List<int> _lsGetIndexCaNhanGayLoi = [];

// Individual variables
  ImagePicker _pickerImage = ImagePicker();
  late int _indexError;
  late int _indexCaNhanGayLoi;
  late int _indexInfoTemp;
  late bool _timeOut;

  late String _hangTagId;

// Boolean variables
  bool _disableButtonTimeOut = false;
  bool _isNotWifi = false;
  bool _isLoading = false;
  bool _isLoadingCongDoan = false;
  bool _isError = false;
  bool _hideDetailLever = false;
  bool _errorSelectType = false;
  bool _errorP0 = false;
  bool _errorQuantityCheck = false;
  bool _errorSelectedResultQualityView = false;
  bool _errorTestMethodDetail = false;

  bool _errorCongDoanNho = false; // new

  bool _errorLimitCriticalDetail = false;
  bool _errorLimitHighDetail = false;
  bool _errorLimitLowDetail = false;

  // bool _errorLevelDetail = false;
  bool _errorAcceptableLevelDetail = false;
  bool _errorQuantityCheckDetail = false;
  bool _errorResultCheckDetail = false;
  bool _loadingGetQuanititySample = false;

  double _bottomPadding = 0;

  bool checkQualityControl() {
    if (_qualityControl != null) {
      return _qualityControl!.qualityControlId == null;
    }
    return true;
  }

  bool canSubmit() {
    return _isLSXSAPSelected == true && _isNewMode == true && _selectedCongDoanNho != null;
  }

  bool _isNewMode = true; // new mặc định là new

  DateTime? _qualityDate;
  String _qualityDateStr = " ";
  QualityCheckerInfo? _qualityChckerInfo;
  QualityTypeList? _selectedType;

  String title = "Phiếu kiểm tra sản phẩm";
  final String _errorNotChooseLSX = "Vui lòng nhập LSX SAP";
  final String _errorNotChooseCongDoan = "Vui lòng chọn công đoạn";

  // String radioViewSelected = "qc";
  String radioViewGroup = "QCSP";

  bool isLoaded = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    }

    setState(() {
      _timeOut = false;
      _barcode = widget.qrCode;
    });

    // _init();

    _loadDataAndSetDefault();

    isLoaded = true;
  }

  // Future<void> _init() async {
  //   // setState(() {
  //   //   _controllerTongSoSanPhamLoi.text = "0";
  //   // });
  // }

  // void _resetState(){
  //   setState(() {});
  // }
  Future<void> _getGetQuanititySample(BuildContext context) async {
    setState(() {
      _loadingGetQuanititySample = true;
    });

    if (_selectedLevel != null) {
      final data = await QualityControlFunction.fetchGetQuantitySample(
        _selectedLevel!.catalogCode.toString(),
        // _controllerInspectLotQuantity.text,
        _dataRawMaterial!.quantity!.round().toString(),
        widget.user.token.toString(),
        context,
        mounted,
      );

      if (!mounted) return;
      setState(() {
        _loadingGetQuanititySample = false;
        _controllerInspectQuantityDetail.text = data == null ? "" : data.quantitySample.toString();
        _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isNotEmpty ? false : true;
      });
      FocusManager.instance.primaryFocus?.unfocus();
    } else {
      // Handle the case where _selectedLevel is null
    }
  }

  // void _setMauHoanThien(String value) {
  //   setState(() {
  //     _controllerMauHoanThien.text = value;
  //   });
  // }

  void _checkValidate() {
    setState(() {
      // _errorSelectType = _selectedType == null || _selectedType!.catalogCode == " ";
      // _errorP0 = _qualityControl!.qcType != "NVL" && _controllerPONumber.text.isEmpty;
      // _errorQuantityCheck = _controllerInspectLotQuantity.text.isEmpty;
      // _errorSelectedResultQualityView = _selectedResult == null || _selectedResult!.catalogCode == " ";
      _errorTestMethodDetail = _selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ";
      // _errorCongDoanNho = _selectedCongDoanNho == null || _selectedCongDoanNho!.catalogCode == " ";
      // _errorLevelDetail = _selectedLevel == null || _selectedLevel!.catalogCode == " ";
      // _errorAcceptableLevelDetail = _controllerMucChapNhan.text.isEmpty;
      _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isEmpty;
      _errorResultCheckDetail = _selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ";

      debugPrint(_errorAcceptableLevelDetail.toString());

      for (int i = 0; i < _lsSelectedThongTinKiemTra.length; i++) {
        _lsErrorInfor[i] = _lsSelectedThongTinKiemTra[i] == null;
      }
    });
  }

  Future<void> _loadDataAndSetDefault() async {
    try {
      if (!isTokenLive(widget.dateTimeOld)) {
        setState(() {
          _timeOut = true;
        });
        return;
      }

      if (!mounted) return;

      await _getQualitityControl();

      if (!mounted) return;

      if (_qualityControl == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      updateValues();
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _isError = true;
        _timeOut = false;
      });
    }
  }

  void updateValues() {
    setState(() {
      _isLoading = false;
      _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
      _qualityChckerInfo = createQualityCheckerInfo();

      // updateSelectedType();
      // updateQualityControlDetail();
      // setupQualityControlInformation();

      setupError();
    });
  }

  QualityCheckerInfo createQualityCheckerInfo() {
    var employeeCode = "";
    var employeeFullname = "";

    if (_qualityControl!.qualityChecker != null) {
      employeeCode = _qualityControl!.qcSaleEmployee!.split(" | ")[0];
      employeeFullname = _qualityControl!.qcSaleEmployee!.split(" | ")[1];
    }

    var qualityCheckerList = QualityCheckerInfo(
      accountId: _qualityControl!.qualityChecker ?? widget.user.accountId.toString(),
      salesEmployeeCode: _qualityControl!.qualityChecker != null ? employeeCode : widget.user.employeeCode,
      salesEmployeeName: _qualityControl!.qualityChecker != null ? employeeFullname : widget.user.fullName,
    );
    return qualityCheckerList;
  }

  void updateSelectedType() {
    if (_qualityControl!.qualityType != null) {
      int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityType);
      _selectedType = _qualityTypeList[indexType];
    } else {
      int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == "SANPHAM");
      _selectedType = _qualityTypeList[indexType];
    }
  }

  void updateQualityControlDetail() {
    if (_qualityControl!.qualityControlDetail != null) {
      updateSelectedMethod();

      // updateCongDoanNhoMethod(); // New(),

      updateLimitMethod();
      updateSelectedLevel();
      updateControllerMucChapNhanText();
      updateControllerSoLuongKiemTraText();

      updateTongSoSanPhamLoi();

      updateSelectedResultDetail();
    }
  }

  void updateSelectedMethod() {
    if (_qualityControl!.qualityControlDetail!.testMethod != null) {
      int indexMethod = _lsTestMethodList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.testMethod);
      _selectedTestMethod = _lsTestMethodList[indexMethod];
    } else {
      _selectedTestMethod = null;
    }
  }

  void updateCongDoanNhoMethod() {
    if (_qualityControl?.qualityControlDetail?.congDoanNho != null) {
      int indexMethod = _lsCongDoanNhoMasterData.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.congDoanNho);
      _selectedCongDoanNho = _lsCongDoanNhoMasterData[indexMethod];
    } else {
      _selectedCongDoanNho = null;
    }
  }

  void updateLimitMethod() {
    if (_qualityControl != null && _qualityControl!.qualityControlDetail != null) {
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, _qualityControl!.qualityControlDetail!.limitCritical);
      _selectedLimitHigh = getSelectedLimit(_lsLimitHighList, _qualityControl!.qualityControlDetail!.limitHigh);
      _selectedLimitLow = getSelectedLimit(_lsLimitLowList, _qualityControl!.qualityControlDetail!.limitLow);
    }
  }

  DropdownItemList? getSelectedLimit(List<DropdownItemList> list, String? limit) {
    if (limit != null) {
      int index = list.indexWhere((element) => element.catalogCode == limit);
      return (index != -1) ? list[index] : null;
    } else {
      return null;
    }
  }

  void updateSelectedLevel() {
    if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel != "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
    } else if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel == "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
      _controllerHideDetailLever.text = _qualityControl!.qualityControlDetail!.samplingLevelName ?? "";
      _hideDetailLever = true;
    } else {
      _selectedLevel = null;
    }
  }

  void updateControllerMucChapNhanText() {
    if (_qualityControl!.qualityControlDetail!.acceptableLevel != null) {
      _controllerMucChapNhan.text = _qualityControl!.qualityControlDetail!.acceptableLevel.toString();
    } else {
      _controllerMucChapNhan.text = _controllerMucChapNhan.text;
    }
  }

  void updateControllerSoLuongKiemTraText() {
    if (_qualityControl!.qualityControlDetail!.inspectionQuantity != null) {
      _controllerInspectQuantityDetail.text = (_qualityControl!.qualityControlDetail!.inspectionQuantity!.round()).toString();
    } else {
      _controllerInspectQuantityDetail.text = _controllerInspectQuantityDetail.text;
    }
  }

  void updateTongSoSanPhamLoi() {
    if (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi != null) {
      _controllerTongSoSanPhamLoi.text = (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi!.round()).toString();
    } else {
      _controllerTongSoSanPhamLoi.text = _controllerTongSoSanPhamLoi.text;
    }
  }

  void updateSelectedResultDetail() {
    if (_qualityControl!.qualityControlDetail!.result != null) {
      int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.result);
      _selectedResultDetail = _lsResultList[indexResult];
    } else {
      _selectedResultDetail = null;
    }
  }

  void setupThongTinKiemtra() {
    if (_lsThongTinKiemTra.isNotEmpty) {
      for (int i = 0; i < _lsThongTinKiemTra.length; i++) {
        addThongTinKiemTraSaved(i);
      }
    } else {
      addDefaultThongTinKiemTra();
    }
  }

  void addThongTinKiemTraSaved(int i) {
    _lsControllerSoSanPhamLoi.add(TextEditingController());
    _lsControllerGhiChu.add(TextEditingController());
    _lsControllerThongTinKiemTra.add(TextEditingController());

    _lsControllerSoSanPhamLoi[i].text =
        (_lsThongTinKiemTra[i].soSanPhamLoi == null ? _lsControllerSoSanPhamLoi[i].text : _lsThongTinKiemTra[i].soSanPhamLoi?.round().toString())!;
    _lsControllerGhiChu[i].text = _lsThongTinKiemTra[i].notes ?? _lsControllerGhiChu[i].text;

    var isOldThongTin = _lsThongTinKiemTra[i].qualityControlInformationCode! < 2000;

    if (isOldThongTin) {
      _indexInfoTemp =
          _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId) != -1
              ? _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId)
              : _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == " ");

      _lsGetIndexInfo.add(_indexInfoTemp);
      _lsSelectedThongTinKiemTra.add(_lsThongTinKiemTraSelectMasterData![_lsGetIndexInfo[i]]);
      _lsControllerThongTinKiemTra[i].text = _lsSelectedThongTinKiemTra[i] == null ? "" : _lsSelectedThongTinKiemTra[i]!.name ?? "";
    } else {
      _indexInfoTemp = _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId) != -1
          ? _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId)
          : _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == " ");

      _lsGetIndexInfo.add(_indexInfoTemp);
      // _lsSelectedThongTinKiemTra.add(_lsHangMucKiemTraMasterData![_lsGetIndexInfo[i]]);
      var item = _lsHangMucKiemTraMasterData![_lsGetIndexInfo[i]];
      _lsSelectedThongTinKiemTra.add(ThongTinKiemTra(id: item.id, name: item.name));

      _lsControllerThongTinKiemTra[i].text = _lsSelectedThongTinKiemTra[i] == null ? "" : _lsSelectedThongTinKiemTra[i]!.name ?? "";
    }

    _lsFileThongTinKiemTra.add([]);
    _lsErrorInfor.add(false);
    _checkVisiButtonThongTinKiemTra.add(false);
  }

  void addEmptyThongTinKiemTra([bool canRemove = false]) {
    _checkVisiButtonThongTinKiemTra.add(canRemove);

    _lsThongTinKiemTra.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
    _lsControllerSoSanPhamLoi.add(TextEditingController());
    _lsControllerGhiChu.add(TextEditingController());
    _lsControllerThongTinKiemTra.add(TextEditingController());
    _indexInfoTemp = _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == " ");
    _lsGetIndexInfo.add(_indexInfoTemp);
    _lsFileThongTinKiemTra.add([]);
    _lsSelectedThongTinKiemTra.add(null);
    _lsErrorInfor.add(false);
  }

  void addDefaultThongTinKiemTra() {
    if (_lsHangMucKiemTraMasterData.isNotEmpty) {
      for (var i = 0; i < _lsHangMucKiemTraMasterData.length; i++) {
        var item = _lsHangMucKiemTraMasterData[i];

        _checkVisiButtonThongTinKiemTra.add(false);

        _lsThongTinKiemTra.add(QualityControlInformation(
            checkedFileViewModel: [],
            file: [],
            workCenterName: " ",
            qualityControlInformationCode: item.code,
            qualityControlInformationName: item.name,
            qualityControlQCInformationId: " ",
            qualityControlId: " ",
            qualityControlInformationId: item.id,
            workCenterCode: " ",
            notes: " "));

        _lsControllerThongTinKiemTra.add(TextEditingController(text: item.name));
        _lsControllerSoSanPhamLoi.add(TextEditingController());
        _lsControllerGhiChu.add(TextEditingController());
        // _indexInfoTemp = _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == item.id);
        _indexInfoTemp = _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == item.id);
        _lsGetIndexInfo.add(_indexInfoTemp);
        _lsFileThongTinKiemTra.add([]);
        _lsSelectedThongTinKiemTra.add(ThongTinKiemTra(id: item.id, name: item.name));
        _lsErrorInfor.add(false);
      }

      showToast(
        context: context,
        message: "Đã load " + _lsHangMucKiemTraMasterData.length.toString() + " checklist cần kiểm tra",
        duration: 2,
      );
    }
  }

  void setupError() {
    // Chỉ khi _lsError ko có dữ liệu mới lấy giá trị của công đoạn hiện tại
    // Khi _lsError có giá trị thì lấy từ thằng _lsError

    if (_lsError.isNotEmpty) {
      for (int i = 0; i < _lsError.length; i++) {
        addErrorFromSaved(i);
      }
    } else {
      // Update: không hiển thị thông tin lỗi mặc định, nếu muốn thêm thì phải thêm
      // addDefaultError();
    }
  }

  void addErrorFromSaved(int i) {
    _checkVisiButtonError.add(false);
    _lsSelectedMucDoLoi.add(null);
    _lsSelectedCongDoanLoi.add(null); // Nếu là select thì thêm null vô
    _lsSelectedPhuongAnXuLy.add(null); // Nếu là select thì thêm null vô
    _lsControllerSoLuongLoi.add(TextEditingController()); // TextInput thì thêm TextEditingController
    _lsControllerGhiChuLoi.add(TextEditingController());
    _lsControllerError.add(TextEditingController());

    _lsControllerNhaMayLoi.add(TextEditingController());
    _lsControllerPhanXuongLoi.add(TextEditingController());
    _lsControllerToChuyenLoi.add(TextEditingController());
    // _lsControllerCaNhanGayLoi.add(TextEditingController());

    _lsControllerSoLuongLoi[i].text = _lsError[i].quantityError == null ? (0.toString()) : ((_lsError[i].quantityError ?? 0.0).round()).toString();
    _lsSelectedMucDoLoi[i] = _lsError[i].levelError == null
        ? _lsSelectedMucDoLoi[i]
        : _lsDataGetDefetchLevel.firstWhereOrNull((element) => element.key == _lsError[i].levelError.toString());

    // Update 1 Lấy công đoạn nhỏ đang chọn
    _lsSelectedCongDoanLoi[i] = _lsError[i].congDoanLoi == null
        ? _lsCongDoanLoiMasterData.first
        : _lsCongDoanLoiMasterData.firstWhereOrNull((element) => element.catalogCode == _lsError[i].congDoanLoi.toString());

    _lsSelectedPhuongAnXuLy[i] = _lsError[i].phuongAnXuLy == null
        ? _lsSelectedPhuongAnXuLy[i]
        : _lsPhuongAnXuLyMasterData.firstWhereOrNull((element) => element.catalogCode == _lsError[i].phuongAnXuLy.toString());

    // New: nhà máy, phân xưởng, tổ chuyền lỗi
    _lsControllerNhaMayLoi[i].text = _lsError[i].nhaMayLoi == null ? _lsControllerNhaMayLoi[i].text : _lsError[i].nhaMayLoi.toString();
    _lsControllerPhanXuongLoi[i].text = _lsError[i].phanXuongLoi == null ? _lsControllerPhanXuongLoi[i].text : _lsError[i].phanXuongLoi.toString();
    _lsControllerToChuyenLoi[i].text = _lsError[i].toChuyenLoi == null ? _lsControllerToChuyenLoi[i].text : _lsError[i].toChuyenLoi.toString();
    // Update 1 lấy thông tin từ master
    // _lsControllerNhaMayLoi[i].text = _lsError[i].nhaMayLoi == null ? _qualityControl!.storeName! : _lsError[i].nhaMayLoi.toString();

    // _lsControllerPhanXuongLoi[i].text = _lsError[i].phanXuongLoi == null ? _congDoanInfoVm!.workShopName! : _lsError[i].phanXuongLoi.toString();
    // _lsControllerToChuyenLoi[i].text = _lsError[i].toChuyenLoi == null ? _congDoanInfoVm!.departmentName! : _lsError[i].toChuyenLoi.toString();

    _lsControllerGhiChuLoi[i].text = _lsError[i].notes == null ? _lsControllerGhiChuLoi[i].text : _lsError[i].notes.toString();

    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
    _lsGetIndexError.add(_indexError);
    _lsSelectedError.add(_lsGetIndexError[i] == -1 ? null : _lsErrorList![_lsGetIndexError[i]]);
    _lsControllerError[i].text = _lsSelectedError[i] == null ? "" : _lsSelectedError[i]!.catalogTextVi ?? "";

    _indexCaNhanGayLoi = _lsCaNhanGayLoiMasterData!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
    _lsGetIndexCaNhanGayLoi.add(_indexCaNhanGayLoi);

    _lsFileHinhAnhLoi.add([]);

    // Add default 1 cá nhân gây lỗi
    _lsControllerCaNhanGayLoi.add(TextEditingController());
    var foundCaNhanGayLoi =
        _lsCaNhanGayLoiMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].caNhanGayLoi, orElse: () => DropdownItemList());
    // if exists in data loaded then assign it, else empty
    _lsControllerCaNhanGayLoi[i].text = foundCaNhanGayLoi.catalogTextVi ?? "";

    List<String> caNhanGayLoiMany = _lsError[i].caNhanGayLoiMany == null ? [] : _lsError[i].caNhanGayLoiMany!.split(",");

    if (caNhanGayLoiMany.isEmpty) {
      // NEW
      // Cá nhân gây lỗi (multiple)
      _lsControllerCaNhanGayLoiMany.add([TextEditingController()]);

      // Add the default "Cá nhân gây lỗi" input
      _lsControllerCaNhanGayLoiMany[i][0].text = foundCaNhanGayLoi.catalogTextVi ?? "";
    } else {
      _lsControllerCaNhanGayLoiMany.add([]);
      caNhanGayLoiMany.forEach((element) {
        _lsControllerCaNhanGayLoiMany[i].add(TextEditingController());
        var foundCaNhanGayLoiMany = _lsCaNhanGayLoiMasterData!.firstWhere((item) => item.catalogCode == element, orElse: () => DropdownItemList());
        _lsControllerCaNhanGayLoiMany[i].last.text = foundCaNhanGayLoiMany.catalogTextVi ?? "";
      });
    }

    _lsSelectedCaNhanGayLoi.add(null);
    _lsSelectedCaNhanGayLoiMany.add([null]);

    // for (int i = 0; i < 10; i++) {}

    // if (_indexCaNhanGayLoi != -1) {
    //   _lsControllerCaNhanGayLoiMany[i][0].text = _lsCaNhanGayLoiMasterData[_indexCaNhanGayLoi].catalogTextVi ?? "";
    // }
    // _lsSelectedCaNhanGayLoiMany[i].add(null);

    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 0 | 1. Quản đốc
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 1 | 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 2 | 3. QA-QC
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 3 | 4. KCS

    _lsControllerCaNhanLoi1QuanDoc.add(TextEditingController(text: _lsError[i].quanDoc));

    var foundQuanDoc =
        _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].quanDoc, orElse: () => DropdownItemList());

    if (foundQuanDoc.catalogCode != null) {
      _lsControllerCaNhanLoi1QuanDoc[i].text = foundQuanDoc.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi2ToTruong.add(TextEditingController(text: _lsError[i].toTruong));

    var foundToTruong =
        _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].toTruong, orElse: () => DropdownItemList());

    if (foundToTruong.catalogCode != null) {
      _lsControllerCaNhanLoi2ToTruong[i].text = foundToTruong.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi3QAQC.add(TextEditingController(text: _lsError[i].qaqc));

    var foundQAQC = _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].qaqc, orElse: () => DropdownItemList());

    if (foundQAQC.catalogCode != null) {
      _lsControllerCaNhanLoi3QAQC[i].text = foundQAQC.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi4KCS.add(TextEditingController(text: _lsError[i].kcs));

    var foundKCS = _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].kcs, orElse: () => DropdownItemList());

    if (foundKCS.catalogCode != null) {
      _lsControllerCaNhanLoi4KCS[i].text = foundKCS.catalogTextVi ?? "";
    }
  }

  void addDefaultError([bool canRemove = false]) {
    _checkVisiButtonError.add(canRemove);
    _lsError.add(QualityControlErrorFunction.defaultListError);
    _lsControllerSoLuongLoi.add(TextEditingController());
    _lsSelectedMucDoLoi.add(null);
    _lsControllerGhiChuLoi.add(TextEditingController());
    // TODO: add master data
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 0 | 1. Quản đốc
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 1 | 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 2 | 3. QA-QC
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 3 | 4. KCS

    _lsControllerCaNhanLoi1QuanDoc.add(TextEditingController()); // 1. Quản đốc
    _lsControllerCaNhanLoi2ToTruong.add(TextEditingController()); // 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoi3QAQC.add(TextEditingController()); // 3. QA-QC
    _lsControllerCaNhanLoi4KCS.add(TextEditingController()); // 4. KCS

    _lsControllerError.add(TextEditingController());

    _lsControllerCaNhanGayLoi.add(TextEditingController());

    _lsControllerCaNhanGayLoiMany.add([
      TextEditingController(),
    ]);

    _lsSelectedCaNhanGayLoi.add(null);
    _lsSelectedCaNhanGayLoiMany.add([null]);

    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
    _lsGetIndexError.add(_indexError);
    _lsFileHinhAnhLoi.add([]);
    _lsSelectedError.add(null);
    _lsSelectedCaNhanGayLoi.add(null);
    _lsSelectedPhuongAnXuLy.add(null); // NEW

    // Công đoạn nhỏ
    // _lsSelectedCongDoanLoi.add(null);

    _lsSelectedCongDoanLoi.add(null);

    // New: nhà máy, phân xưởng, tổ chuyền lỗi
    _lsControllerNhaMayLoi.add(TextEditingController());
    _lsControllerPhanXuongLoi.add(TextEditingController());
    _lsControllerToChuyenLoi.add(TextEditingController());
  }

  void _setDate(DateTime? newDate) {
    if (!mounted) return;
    if (newDate != null) {
      setState(() {
        _qualityDate = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
        _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
        debugPrint(_qualityDate!.toIso8601String());
      });
    } else {
      return;
    }
  }

  Future<void> _refreshQCSanPhamData() async {
    setState(() {
      _isLoading = true;
    });

    _congDoanNhoNotSelected();

    await _getQualitityControl();

    // var data = await QualityControlFunction.fetchQualityControlSanPham(widget.qualityControlId, widget.user.token.toString(), widget.qrCode)
    //     as QualityControlModel;
    // qualityControlDataLoaded(data);
    // // var lsxSAP = data.po.toString();

    // if (data.qualityControl?.qualityControlId == null) {
    //   var header = await QualityControlFunction.fetchQualityControlHeader(data.po!, _barcode, widget.user.token.toString());

    //   if (header != null) {
    //     _setHeader(header);
    //   }
    // }

    // // TODO: clear then setup

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _getQualitityControl() async {
    setState(() {
      _timeOut = false;
      _isLoading = true;
      _isNotWifi = false;
    });

    // Call 2 request,
    //  1: get quality control,
    //  2: get defect level

    final listData = await Future.wait([
      // QualityControlFunction.fetchQualityControl(
      //   widget.qualityControlId,
      //   widget.user.token.toString(),
      //   widget.qrCode,
      //   widget.fromPage,
      // ),
      QualityControlFunction.fetchQualityControlSanPham(widget.qualityControlId, widget.user.token.toString(), widget.qrCode),
      QualityControlFunction.fetchGetDefectLevelApi(widget.user.token.toString())
    ]);

    if (!mounted) return;

    var resultQualityControl = listData[0] as QualityControlModel;
    var resultDefectLevel = listData[1];

    String? lsxSAP = "";

    QualityControlModel? header = null;

    if (resultQualityControl.isTTFBarcode == true) {
      // showToast(
      //   context: context,
      //   message: "TTF test",
      //   duration: 2,
      // );
      lsxSAP = resultQualityControl.po.toString();
    } else {
      lsxSAP = _controllerLSXSAP.text;
    }

    if (lsxSAP.isNotEmpty) {
      header = await QualityControlFunction.fetchQualityControlHeader(lsxSAP, _barcode, widget.user.token.toString());
    }

    if (resultQualityControl.qualityControl?.qualityChecker != null) {
      lsxSAP = resultQualityControl.qualityControl?.lsxsap ?? "";
    }

    setState(() {
      if (listData.isNotEmpty) {
        setDefectLevelList(resultDefectLevel);
        qualityControlDataLoaded(resultQualityControl);

        _controllerLSXSAP.text = lsxSAP ?? "";
        _isTTFCode = resultQualityControl.isTTFBarcode ?? false;
      }
    });

    if (header != null) {
      if (header.qualityControl == null) {
        // Error: Sản phẩm chưa xác nhận công đoạn đóng gói!
        // TTLSX chưa có xncd SF-DGO
        _qualityControl = null;
        return;
      }

      _setHeader(header);
    }
  }

  void setDefectLevelList(dynamic data) {
    if (data != null) {
      _lsDataGetDefetchLevel = data as List<DataGetDefectLevel>;
    }
  }

  // Set local data from return data
  void qualityControlDataLoaded(dynamic data) {
    if (data != null) {
      _qualityControlModel = data as QualityControlModel?;
      if (_qualityControlModel != null) {
        _qualityControl = _qualityControlModel!.qualityControl;

        if (_qualityControl!.barcode != null) {
          _barcode = _qualityControl!.barcode.toString();
        } else {
          _barcode = widget.qrCode;
        }

        _hangTagId = _qualityControl!.hangTagId.toString();

        _lsCaNhanGayLoiMasterData = _qualityControlModel!.caNhanGayLoiList!;

        if (_qualityControl!.lsxsap != null && _qualityControl!.lsxsap!.isNotEmpty) {
          _controllerLSXSAP.text = _qualityControl!.lsxsap!;
          _isLSXSAPSelected = true;

          _lsThongTinKiemTra = (_qualityControl?.qualityControlInformation?.isNotEmpty == true ? _qualityControl!.qualityControlInformation : [])!;
          _lsError = _qualityControl?.error?.isNotEmpty == true ? _qualityControl!.error! : [];

          _isNewMode = false;
        }

        setupMasterDataList();
        setErrorsList();

        updateCongDoanNhoMethod();

        setupThongTinKiemtra();
        setupError();

        setupQualityDate();

        updateQualityControlDetail();
      }
    } else {
      _isLoading = false;
    }
  }

  void setupMasterDataList() {
    _qualityTypeList = prependDefault(_qualityControlModel!.qualityTypeList, QualityControlFunction.defaultValueQualityTypeList);

    _lsTestMethodList = prependDefault(_qualityControlModel!.testMethodList, QualityControlDetailFunction.defaultValueTestMethodList);

    _lsCongDoanNhoMasterData = prependDefault(_qualityControlModel!.congDoanNhoList, QualityControlDetailFunction.defaultValueCongDoanNho);

    _lsCongDoanLoiMasterData = prependDefault(_qualityControlModel!.congDoanLoiList, QualityControlDetailFunction.defaultValueCongDoanNho);

    if (_qualityControlModel?.congDoanLoiList == null) {
      _lsCongDoanLoiMasterData = prependDefault(_qualityControlModel!.congDoanNhoList, QualityControlDetailFunction.defaultValueCongDoanNho);
    }

    _lsPhuongAnXuLyMasterData = prependDefault(_qualityControlModel!.phuongAnXuLyList, QualityControlDetailFunction.defaultValuePhuongAnXuLy);

    _lsLimitCriticalList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitHighList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitLowList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);

    _lsResultList = prependDefault(_qualityControlModel!.resultList, QualityControlDetailFunction.defaultResultList);
    _lsSamplingLevelList = prependDefault(_qualityControlModel!.samplingLevelList, QualityControlDetailFunction.defaultValueSamplingLevelList);
    _lsThongTinKiemTraSelectMasterData = _qualityControlModel!.qualityControlInformationIdList;

    // New
    _lsCaNhanGayLoiAllMasterData = _qualityControlModel!.caNhanGayLoiList!;
  }

  void updateQualityControlInformation() {
    _lsThongTinKiemTra = _qualityControl?.qualityControlInformation?.isNotEmpty == true ? _qualityControl!.qualityControlInformation! : [];
  }

  void updateErrors() {
    _lsError = _qualityControl?.error?.isNotEmpty == true ? _qualityControl!.error! : [];
  }

  void setupQualityDate() {
    if (_selectedCongDoanNho == null) {
      _qualityDate = DateFormat("yyyy-MM-dd HH:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()));
    } else {
      _qualityDate = _qualityControl?.qualityDate != null
          ? DateFormat("yyyy-MM-ddThh:mm:ss").parse(_qualityControl!.qualityDate!)
          : DateFormat("yyyy-MM-dd HH:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()));
    }

    _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
  }

  void setErrorsList() {
    _lsErrorList = _qualityControlModel!.errorList;
  }

  List<T> prependDefault<T>(List<T>? list, T defaultValue) {
    if (list == null) {
      return [defaultValue];
    } else {
      return [defaultValue, ...list];
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButtonTimeOut = true;
    });
  }

  void _deleteListImageTabCheck(int index) {
    if (!mounted) return;
    setState(() {
      _lsFileHeader.removeAt(index);
    });
  }

  void _pickFileImage(File file) {
    if (!mounted) return;
    setState(() {
      _lsFileHeader.add(file);
    });
  }

  void _pickFileImageInformation(MultiListImageFile multiListImageFile) {
    setState(() {
      _lsFileThongTinKiemTra[multiListImageFile.index].add(multiListImageFile.file);
    });
  }

  // void _getSelectedResult(ResultList? value) {
  //   setState(() {
  //     _selectedResult = value;
  //     if (_selectedResult == null || _selectedResult!.catalogCode == " ") {
  //       _errorSelectedResultQualityView = true;
  //     } else {
  //       _errorSelectedResultQualityView = false;
  //     }
  //   });
  // }

  void _getSelectedDefectLevel(DropdownDefetchLevel value) {
    setState(() {
      _lsSelectedMucDoLoi[value.index] = value.value;
    });
  }

  // void _getSelectedType(QualityTypeList? value) {
  //   setState(() {
  //     _selectedType = value;
  //     if (_selectedType == null || _selectedType!.catalogCode == " ") {
  //       _errorSelectType = true;
  //     } else {
  //       _errorSelectType = false;
  //     }
  //   });
  // }

  void _getSelectedResultDetail(ResultList? value) {
    setState(() {
      _selectedResultDetail = value;
      if (_selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ") {
        _errorResultCheckDetail = true;
      } else {
        _errorResultCheckDetail = false;
      }
    });
    // print(_selectedResultDetail!.catalogTextVi.toString());
  }

  void _getSelectedMethod(TestMethodList? value) {
    setState(() {
      _selectedTestMethod = value;
      if (_selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ") {
        _errorTestMethodDetail = true;
      } else {
        _errorTestMethodDetail = false;
      }
    });
  }

  void _congDoanNhoNotSelected() {
    setState(() {
      _selectedCongDoanNho = null;
      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();
      _qualityControl?.workShopName = null;
      _isNewMode = true;
      _isLoadingCongDoan = false;
    });
  }

  Future<void> _onCongDoanNhoChanged(DropdownItemList? value) async {
    setState(() {
      // _selectedCongDoanNho = value;
      _isLoadingCongDoan = true;
    });

    if (value != null && value.catalogCode == " ") {
      _congDoanNhoNotSelected();
      return;
    }

    // if (_qualityControl?.qualityControlId == null) {
    //   // Change cong doan nho only
    //   setState(() {
    //     _isLoadingCongDoan = false;
    //   });
    //   return;
    // }

    var congDoan = value!.catalogCode.toString();
    // final data = await QualityControlFunction.fetchCongDoanInfoSanPham(_qualityControl!.qualityControlId, congDoan, widget.user.token.toString());
    final data = await QualityControlFunction.fetchCongDoanInfoBTP(_controllerLSXSAP.text, congDoan, widget.user.token.toString());

    if (kDebugMode) {
      print(json.encode(data));
    }

    if (data == null) {
      showAlert(
        context: context,
        title: 'Thông báo',
        content: 'Công đoạn $congDoan chưa có thông tin QC',
        buttonsWithOnPressed: {
          'OK': () => Navigator.of(context).pop(),
        },
      );
      clearForNewCongDoan();
      setState(() {
        _isLoadingCongDoan = false;
      });
      return;
    }

    setState(() {
      _selectedCongDoanNho = value;

      if (_selectedCongDoanNho == null || _selectedCongDoanNho!.catalogCode == " ") {
        _errorCongDoanNho = true;
      } else {
        _errorCongDoanNho = false;
      }

      // Data của công đoạn nhỏ
      _congDoanInfoVm = data;

      // Gán data công đoạn nhỏ vào
      _qualityControl?.workShopName = _congDoanInfoVm!.workShopName;
      _qualityControl!.qualityControlDetail = _congDoanInfoVm!.qualityControlDetail;
      _qualityControl!.qualityControlId = _congDoanInfoVm!.qualityControl?.qualityControlId;
      _qualityControl!.confirmDate = _congDoanInfoVm!.qualityControl?.confirmDate;

      _qualityControl!.qualityDate = _congDoanInfoVm!.qualityControlDetail?.qualityDate;
      _qualityControl!.qcSaleEmployee = _congDoanInfoVm!.qualityControlDetail?.qcSaleEmployee;
      _qualityControl!.qualityChecker = _congDoanInfoVm!.qualityControlDetail?.qualityChecker;

      if (_congDoanInfoVm!.qualityControlDetail != null) {
        _isNewMode = false; // khi load có dữ liệu thì
      } else {
        _isNewMode = true;
      }

      // _lsCaNhanGayLoiMasterData = _congDoanInfoVm!.caNhanGayLoiList!;

      // New: checklist thong tin kiem tra
      // _lsHangMucKiemTraMasterData = _congDoanInfoVm!.hangMucKiemTraMasterData!;

      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();

      // New: load thong tin kiem tra
      _lsThongTinKiemTra = (_congDoanInfoVm?.qualityControlInformation?.isNotEmpty == true ? _congDoanInfoVm!.qualityControlInformation : [])!;

      _lsError = _congDoanInfoVm?.error?.isNotEmpty == true ? _congDoanInfoVm!.error! : [];

      if (_congDoanInfoVm?.qualityControl != null && _congDoanInfoVm?.qualityControl?.finishedColor != null) {
        _controllerMauHoanThien.text = _congDoanInfoVm!.qualityControl!.finishedColor!;
      }
      setupThongTinKiemtra();
      setupError();

      // if (_qualityControl!.qualityControlDetail != null) {
      //   updateSelectedMethod();
      // }

      updateQualityControlDetail();

      _qualityChckerInfo = createQualityCheckerInfo();
      setupQualityDate();

      // _lsTextEditingControllerCaNhanLoiParent[0].text = _congDoanInfoVm?.quanDocName ?? '';
      // _lsTextEditingControllerCaNhanLoiParent[1].text = _congDoanInfoVm?.toTruongName ?? '';
      // _lsTextEditingControllerCaNhanLoiParent[2].text = _congDoanInfoVm?.qaqcName ?? '';
      // _lsTextEditingControllerCaNhanLoiParent[3].text = _congDoanInfoVm?.kcsName ?? '';

      _isLoadingCongDoan = false;
    });

    if (_qualityControl!.qualityControlDetail != null) {
      showToast(
        context: context,
        message: "Đã load thông phiếu kiểm tra công đoạn $congDoan",
        duration: 4,
      );
    }
  }

  Future<void> _onCongDoanLoiChanged(DropdownItemList? value, int index) async {
    // showToast(context: context, message: jsonEncode(value));

    // Lấy danh sách công đoạn giống công đoạn lỗi
    // Load nhà máy, phân xưởng, tổ chuyền

    setState(() {
      _lsSelectedCongDoanLoi[index] = null;

      _lsControllerNhaMayLoi[index].clear();
      _lsControllerPhanXuongLoi[index].clear();
      _lsControllerToChuyenLoi[index].clear();
    });

    if (value!.catalogCode! == " ") {
      setState(() {});
      return;
    }

    var hangTagId = _hangTagId;
    var congDoan = value!.catalogCode.toString();
    // final congDoanModel = await QualityControlFunction.fetchCongDoanInfoSanPham(_qualityControl!.qualityControlId, congDoan, widget.user.token.toString());
    final congDoanModel = await QualityControlFunction.fetchCongDoanInfo(hangTagId, congDoan, widget.user.token.toString());

    if (kDebugMode) {
      print(json.encode(congDoanModel));
    }

    // if (congDoanModel == null) {
    //   // showAlert(
    //   //   context: context,
    //   //   title: 'Thông báo',
    //   //   content: 'Công đoạn $congDoan chưa xác nhận',
    //   //   buttonsWithOnPressed: {
    //   //     'OK': () => Navigator.of(context).pop(),
    //   //   },
    //   // );

    //   setState(() {
    //     _isLoadingCongDoan = false;
    //   });

    //   return;
    // }

    setState(() {
      // _lsCaNhanGayLoiMasterData = congDoanModel!.caNhanGayLoiList!; // Update

      _lsSelectedCongDoanLoi[index] = value;

      if (congDoanModel != null) {
        _lsControllerNhaMayLoi[index].text = congDoanModel.storeName!;
        _lsControllerPhanXuongLoi[index].text = congDoanModel.workShopName!;
        _lsControllerToChuyenLoi[index].text = congDoanModel.departmentName!;
      }
      _isLoadingCongDoan = false;
    });
  }

  Future<void> _onPhuongAnXuLyChanged(DropdownItemList? value, int index) async {
    if (value!.catalogCode! == " ") {
      _lsSelectedPhuongAnXuLy[index] = null;
    } else {
      _lsSelectedPhuongAnXuLy[index] = value;
    }
    setState(() {});
  }

  void clearSelectedLSX() {
    setState(() {
      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();

      _controllerLSXSAP.text = "";
      _isLSXSAPSelected = false;

      _qualityControl = QualityControl();

      // _qualityControl!.qualityChecker = null;
      // _qualityControl!.qualityDate = null;
      // _qualityChckerInfo = createQualityCheckerInfo();

      setupThongTinKiemtra();
      setupError();
      setupQualityDate();

      _isNewMode = true;
      // showToast(context: context, message: 'Thêm phiếu');
    });
  }

  void clearForNew() {
    setState(() {
      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();

      _qualityControl!.qualityChecker = null;
      _qualityControl!.qualityDate = null;
      _qualityChckerInfo = createQualityCheckerInfo();

      setupThongTinKiemtra();
      setupError();
      setupQualityDate();

      _isNewMode = true;
      // showToast(context: context, message: 'Thêm phiếu');
    });
  }

  void clearForNewCongDoan() {
    setState(() {
      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();

      _qualityControl!.qualityChecker = null;
      _qualityControl!.qualityDate = null;
      _qualityChckerInfo = createQualityCheckerInfo();

      setupThongTinKiemtra();
      setupError();
      setupQualityDate();

      _isNewMode = true;
      // showToast(context: context, message: 'Thêm phiếu');
    });
  }

  void clearErrors() {
    _checkVisiButtonError.clear();
    _lsError.clear();
    _lsControllerSoLuongLoi.clear();
    _lsSelectedMucDoLoi.clear();
    _lsSelectedCongDoanLoi.clear();
    _lsControllerGhiChuLoi.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();

// New
    _lsControllerCaNhanLoi1QuanDoc.clear();
    _lsControllerCaNhanLoi2ToTruong.clear();
    _lsControllerCaNhanLoi3QAQC.clear();
    _lsControllerCaNhanLoi4KCS.clear();

    _lsControllerError.clear();

    _lsControllerCaNhanGayLoi.clear();
    _lsControllerCaNhanGayLoiMany.clear();

    _lsGetIndexError.clear();
    _lsFileHinhAnhLoi.clear();
    _lsSelectedError.clear();

    _lsSelectedCaNhanGayLoi.clear();
    _lsSelectedCaNhanGayLoiMany.clear();

    _lsSelectedPhuongAnXuLy.clear();

    _lsSelectedCongDoanLoi.clear();

    _lsControllerNhaMayLoi.clear();
    _lsControllerPhanXuongLoi.clear();
    _lsControllerToChuyenLoi.clear();
  }

  void clearQualityControl() {
    _controllerMauHoanThien.clear();
  }

  void clearThongTinKiemTra() {
    _selectedTestMethod = null;
    // _lsSamplingLevelList.clear();
    _selectedLevel = QualityControlDetailFunction.defaultValueSamplingLevelList;
    _controllerInspectQuantityDetail.clear();

    _controllerTongSoSanPhamLoi.clear();

    _selectedResultDetail = null;

    _selectedLimitCritical = null;
    _selectedLimitHigh = null;
    _selectedLimitLow = null;

    _checkVisiButtonThongTinKiemTra.clear();

    _lsControllerThongTinKiemTra.clear();
    _lsControllerSoSanPhamLoi.clear();
    _lsControllerGhiChu.clear();

    _lsErrorInfor.clear();
    _lsSelectedThongTinKiemTra.clear();
    _lsGetIndexInfo.clear();

    _lsThongTinKiemTra.clear();
  }

  void _getSelectedLimitCritical(DropdownItemList? value) {
    setState(() {
      _selectedLimitCritical = value;
      if (_selectedLimitCritical == null || _selectedLimitCritical!.catalogCode == " ") {
        _errorLimitCriticalDetail = true;
      } else {
        _errorLimitCriticalDetail = false;
      }
    });
  }

  void _getSelectedLimitHigh(DropdownItemList? value) {
    setState(() {
      _selectedLimitHigh = value;
      if (_selectedLimitHigh == null || _selectedLimitHigh!.catalogCode == " ") {
        _errorLimitHighDetail = true;
      } else {
        _errorLimitHighDetail = false;
      }
    });
  }

  void _getSelectedLimitLow(DropdownItemList? value) {
    setState(() {
      _selectedLimitLow = value;
      if (_selectedLimitLow == null || _selectedLimitLow!.catalogCode == " ") {
        _errorLimitLowDetail = true;
      } else {
        _errorLimitLowDetail = false;
      }
    });
  }

  void _addNewThongTinKiemTra() {
    setState(() {
      // _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
      // _lsControllerSoSanPhamLoi.add(TextEditingController());
      // _lsControllerGhiChu.add(TextEditingController());
      // _lsControllerQualityControlInformationIdList.add(TextEditingController());
      // // _focusInformation.add(FocusNode());
      // _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
      // _lsGetIndexInfo.add(_indexInfo);
      // _lsFileThongTinKiemTra.add([]);
      // _lsSelectedInfo.add(null);
      // _lsErrorInfor.add(false);
      // _checkVisiButtonInformation.add(true);

      addEmptyThongTinKiemTra(true);
    });
  }

  void _deleteListQualityInformation(MultiListImageDeleteFile multiListImageDeleteFile) {
    setState(() {
      _lsFileThongTinKiemTra[multiListImageDeleteFile.index].removeAt(multiListImageDeleteFile.indexImage);
    });
  }

  // + Thêm lỗi
  void _addNewCardError() {
    setState(() {
      //   _lsError.add(QualityControlErrorFunction.defaultListError);
      //   _lsControllerSoLuongLoi.add(TextEditingController());
      //   _lsSelectedMucDoLoi.add(null);
      //   _lsSelectedCongDoanLoi.add(null);
      //   _lsControllerGhiChuLoi.add(TextEditingController());
      //   _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
      //   _lsControllerError.add(TextEditingController());

      //   _lsControllerCaNhanGayLoi.add(TextEditingController());
      //   _lsSelectedCaNhanGayLoi.add(null);

      //   // _focusError.add(FocusNode());
      //   _lsGetIndexError.add(_indexError);
      //   _lsFileHinhAnhLoi.add([]);
      //   _lsSelectedError.add(null);
      //   _checkVisiButtonError.add(true);

      addDefaultError(true);
    });
  }

  void _deleteListFileError(MultiDeleteImageErrorQuality multiDeleteImageErrorQuality) {
    setState(() {
      _lsFileHinhAnhLoi[multiDeleteImageErrorQuality.index].removeAt(multiDeleteImageErrorQuality.indexImageError);
    });
  }

  // void _getSelectedError(MultiSelectedErrorQuality multiSelectedErrorQuality) {
  //   setState(() {
  //     _lsSelectedError[multiSelectedErrorQuality.index] =
  //         multiSelectedErrorQuality.value!;
  //   });
  // }

  void _pickFileImageErrorQuality(MultiSelectImageErrorQuality multiSelectImageErrorQuality) {
    setState(() {
      _lsFileHinhAnhLoi[multiSelectImageErrorQuality.index].add(multiSelectImageErrorQuality.file);
    });
  }

  void _checkErrorQuantityView() {
    // setState(() {
    if (_controllerPONumber.text.isNotEmpty) {
      if (_errorP0 != false) {
        setState(() {
          _errorP0 = false;
        });
      }
    } else {
      if (_errorP0 != true) {
        setState(() {
          _errorP0 = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckerQuantityView() {
    setState(() {
      if (_controllerInspectLotQuantity.text.isNotEmpty) {
        if (_errorQuantityCheck != false) {
          _errorQuantityCheck = false;
        }
      } else {
        if (_errorQuantityCheck != true) {
          _errorQuantityCheck = true;
        }
      }
    });
  }

  // void _checkErrorHideDetailLevel() {
  //   if (_controllerHideDetailLever.text.isNotEmpty) {
  //     if (_errorHideDetail != false) {
  //       setState(() {
  //         _errorHideDetail = false;
  //       });
  //     }
  //   } else {
  //     if (_errorHideDetail != true) {
  //       setState(() {
  //         _errorHideDetail = true;
  //       });
  //     }
  //   }
  // }

  void _checkErrorAcceptableLevelDetail() {
    // setState(() {
    if (_controllerMucChapNhan.text.isNotEmpty) {
      if (_errorAcceptableLevelDetail != false) {
        setState(() {
          _errorAcceptableLevelDetail = false;
        });
      }
    } else {
      if (_errorAcceptableLevelDetail != true) {
        setState(() {
          _errorAcceptableLevelDetail = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckDetail() {
    // setState(() {
    if (_controllerInspectQuantityDetail.text.isNotEmpty) {
      if (_errorQuantityCheckDetail != false) {
        setState(() {
          _errorQuantityCheckDetail = false;
        });
      }
    } else {
      if (_errorQuantityCheckDetail != true) {
        setState(() {
          _errorQuantityCheckDetail = true;
        });
      }
    }
    // });
  }

  void _checkClearLsSelectedInfo(int index) {
    if (_lsSelectedThongTinKiemTra[index] != null) {
      setState(() {
        _lsSelectedThongTinKiemTra[index] = null;
      });
    }
  }

  void _setTypeAhead(TypeAheadErrorQuatity typeAheadErrorQuatity) {
    setState(() {
      _lsControllerError[typeAheadErrorQuatity.index ?? 0].text =
          typeAheadErrorQuatity.errorList == null ? "" : typeAheadErrorQuatity.errorList!.catalogTextVi ?? "";
      _lsSelectedError[typeAheadErrorQuatity.index ?? 0] = typeAheadErrorQuatity.errorList;
    });
  }

  void _setTypeAheadCaNhanGayLoi(TypeAheadCaNhanGayLoi typeAheadCaNhanGayLoi) {
    setState(() {
      _lsControllerCaNhanGayLoi[typeAheadCaNhanGayLoi.index ?? 0].text =
          typeAheadCaNhanGayLoi.selectedItem == null ? "" : typeAheadCaNhanGayLoi.selectedItem!.catalogTextVi ?? "";
      _lsSelectedCaNhanGayLoi[typeAheadCaNhanGayLoi.index ?? 0] = typeAheadCaNhanGayLoi.selectedItem;
    });
  }

  void _setTypeAheadCaNhanGayLoiMany(TypeAheadCaNhanGayLoi typeAheadCaNhanGayLoi) {
    var errorIndex = typeAheadCaNhanGayLoi.index ?? -1;
    var caNhanLoiIndex = typeAheadCaNhanGayLoi.caNhanLoiIndex ?? -1;

    debugPrint("errorIndex: $errorIndex, caNhanLoiIndex: $caNhanLoiIndex");

    if (errorIndex == -1 || caNhanLoiIndex == -1) {
      return;
    }

    // check on the list of the selected items already exists typeAheadCaNhanGayLoi.selectedItem!.catalogCode

    if (_lsSelectedCaNhanGayLoiMany[errorIndex].contains(typeAheadCaNhanGayLoi.selectedItem)) {
      showToast(context: context, message: "Cá nhân này đã được chọn");
      return;
    }

    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex][caNhanLoiIndex].text =
          typeAheadCaNhanGayLoi.selectedItem == null ? "" : typeAheadCaNhanGayLoi.selectedItem!.catalogTextVi ?? "";
      _lsSelectedCaNhanGayLoiMany[errorIndex][caNhanLoiIndex] = typeAheadCaNhanGayLoi.selectedItem;
    });
  }

  void _checkErrorSelectedInfo(QuantityInformationSelected quantityInformationSelected) {
    setState(() {
      _lsControllerThongTinKiemTra[quantityInformationSelected.index ?? 0].text =
          quantityInformationSelected.qualityControlInformationIdList!.name ?? "";
      _lsSelectedThongTinKiemTra[quantityInformationSelected.index ?? 0] = quantityInformationSelected.qualityControlInformationIdList;
      if (_lsSelectedThongTinKiemTra[quantityInformationSelected.index ?? 0] != null) {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != false) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = false;
        }
      } else {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != true) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = true;
        }
      }
    });
  }

  void _updateTongSoSanPhamLoi() {
    int total = 0;
    for (var controller in _lsControllerSoSanPhamLoi) {
      String text = controller.text;
      // In case the text is empty or is not a valid integer, skip the iteration.
      if (text.isEmpty || int.tryParse(text) == null) {
        continue;
      }
      total += int.parse(text);
    }

    // After the loop, you can check if the total is valid and set to _controllerTongSoSanPhamLoi
    if (total >= 0) {
      _controllerTongSoSanPhamLoi.text = total.toString();
    }
  }

  void _onRadioChanged(RadioValue? value, int index) {
    if (value == null) {
      return;
    }

    var statusTemp = getOutcomeStatus(value);
    setState(() {
      _lsThongTinKiemTra[index].outcomeStatus = statusTemp;
    });
  }

  // Khi thay đổi text thì clear giá trị đã chọn vì xài typeahead
  // Chỉ set value khi chọn item trong typeahead
  void _onDanhSachLoiChanged(int index) {
    if (_lsSelectedError[index] != null) {
      setState(() {
        _lsSelectedError[index] = null;
      });
    }
  }

  // Khi thay đổi text thì clear giá trị đã chọn vì xài typeahead
  // Chỉ set value khi chọn item trong typeahead
  void _clearCaNhanGayLoiIndex(int index) {
    if (_lsSelectedCaNhanGayLoi[index] != null) {
      setState(() {
        _lsSelectedCaNhanGayLoi[index] = null;
      });
    }
  }

  void _clearCaNhanGayLoiIndexMany(int caNhanLoiIndex, int index) {
    if (_lsSelectedCaNhanGayLoiMany[index][caNhanLoiIndex] != null) {
      setState(() {
        _lsSelectedCaNhanGayLoiMany[index][caNhanLoiIndex] = null;
      });
    }
  }

  // void _checkErrorSelectedInfo(int index) {
  //   if (_lsSelectedInfo[index] != null) {
  //     if (_lsErrorInfor[index] != false) {
  //       setState(() {
  //         _lsErrorInfor[index] = false;
  //       });
  //     }
  //   } else {
  //     if (_lsErrorInfor[index] != true) {
  //       setState(() {
  //         _lsErrorInfor[index] = true;
  //       });
  //     }
  //   }
  // }
  void _deleteItemListInformation(int index) {
    setState(() {
      _lsThongTinKiemTra.removeAt(index);
      _lsControllerSoSanPhamLoi.removeAt(index);
      _lsControllerGhiChu.removeAt(index);
      _lsControllerThongTinKiemTra.removeAt(index);
      // _focusInformation.removeAt(index);
      _lsGetIndexInfo.removeAt(index);
      _lsFileThongTinKiemTra.removeAt(index);
      _lsSelectedThongTinKiemTra.removeAt(index);
      _lsErrorInfor.removeAt(index);
      _checkVisiButtonThongTinKiemTra.removeAt(index);
    });
  }

  void _deleteItemListError(int index) {
    setState(() {
      _lsError.removeAt(index);
      _lsControllerSoLuongLoi.removeAt(index);
      _lsSelectedMucDoLoi.removeAt(index);
      _lsSelectedCongDoanLoi.add(null); // New
      _lsControllerGhiChuLoi.removeAt(index);
      _lsControllerError.removeAt(index);

      _lsControllerCaNhanGayLoi.removeAt(index);
      _lsSelectedCaNhanGayLoi.removeAt(index);

      _lsControllerCaNhanGayLoiMany.removeAt(index);
      _lsSelectedCaNhanGayLoiMany.removeAt(index);

      // _focusError.removeAt(index);
      _lsGetIndexError.removeAt(index);
      _lsFileHinhAnhLoi.removeAt(index);
      _lsSelectedError.removeAt(index);
      _lsSelectedPhuongAnXuLy.removeAt(index);
      _checkVisiButtonError.removeAt(index);
      _lsErrorList = _qualityControlModel!.errorList;
    });
  }

  bool canSendQualityControl() {
    // return _errorSelectType == false &&
    //         // _errorP0 == false &&
    //         _errorQuantityCheck == false &&
    //         _errorSelectedResultQualityView == false &&
    //         _errorTestMethodDetail == false &&
    //         _errorCongDoanNho == false &&
    //         //_errorLevelDetail == false &&
    //         _errorAcceptableLevelDetail == false &&
    //         _errorQuantityCheckDetail == false &&
    //         _errorResultCheckDetail == false
    //     // && _lsErrorInfor.where((element) => element == true).isEmpty
    //     ;

    bool ret = true;

    // if (_errorSelectType) {
    //   showToast(context: context, message: "Error: Select Type failed!");
    //   ret = false;
    // }

    if (_errorQuantityCheck) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    // if (_errorSelectedResultQualityView) {
    //   showToast(context: context, message: "Error: Selected Result Quality View failed!");
    //   ret = false;
    // }

    if (_errorTestMethodDetail) {
      showToast(context: context, message: "Vui lòng chọn phương pháp KT");
      ret = false;
    }

    // if (_errorCongDoanNho) {
    //   showToast(context: context, message: "Vui lòng chọn công đoạn nhỏ");
    //   ret = false;
    // }

    // Update không cần chọn công đoạn, chỉ QC sản phẩm
    // if (_errorAcceptableLevelDetail) {
    //   showToast(context: context, message: "Error: Acceptable Level Detail failed!");
    //   ret = false;
    // }

    if (_errorQuantityCheckDetail) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorResultCheckDetail) {
      showToast(context: context, message: "Vui lòng chọn kết quả");
      ret = false;
    }

    return ret;
  }

  void submitData() {
    if (!isTokenLive(widget.dateTimeOld)) {
      Platform.isAndroid
          ? showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
          : showCupertinoDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
      return;
    }

    _checkValidate();
    FocusManager.instance.primaryFocus?.unfocus();
    debugPrint(_errorAcceptableLevelDetail.toString());
    // debugPrint(_errorAcceptableLevelDetail.toString());
    // debugPrint(_lsTextEditingControllerError_1[0].text);
    var canSend = canSendQualityControl();

    if (canSend) {
      QualityControlFunction.postQualityControlSanPham(
        context,
        _lsFileHeader,
        _controllerInspectQuantityDetail.text,
        _qualityControl,
        _qualityChckerInfo,
        _qualityDate!.toIso8601String(),
        _selectedType,
        _selectedResultDetail,
        _controllerPONumber.text,
        _barcode,
        QualityControlDetailFunction.getSendQualityControlDetail2(
          _qualityControl,
          _selectedTestMethod,
          // Mức giới hạn
          _selectedLimitCritical,
          _selectedLimitHigh,
          _selectedLimitLow,

          _selectedLevel,
          _controllerMucChapNhan.text,
          _controllerInspectQuantityDetail.text,
          _controllerTongSoSanPhamLoi.text,

          _selectedResultDetail,
          _hideDetailLever,
          _controllerHideDetailLever.text,
          _selectedCongDoanNho,
          "", //_qualityControlModel!.qualityControl!.mauHoanThien!, // _controllerMauHoanThien.text, // new
          "",
          "",
          "",
        ),
        QualityControlInfoFunction.getLsSendQualityControlInformation2(
          _lsThongTinKiemTra,
          _qualityControl,
          _lsSelectedThongTinKiemTra,
          _lsControllerSoSanPhamLoi,
          _lsControllerGhiChu,
          _lsFileThongTinKiemTra,
        ),
        QualityControlErrorFunction.getLsError(
          _lsError,
          _qualityControl,
          _lsSelectedError,
          _lsSelectedMucDoLoi,
          _lsControllerSoLuongLoi,
          _lsControllerGhiChuLoi,
          _lsFileHinhAnhLoi,
          _lsSelectedCaNhanGayLoi,
          _lsSelectedCongDoanLoi,
          _lsSelectedPhuongAnXuLy,
          _lsControllerNhaMayLoi,
          _lsControllerPhanXuongLoi,
          _lsControllerToChuyenLoi,
          _lsSelectedCaNhanGayLoiMany,

          // New
          _lsControllerCaNhanLoi1QuanDoc,
          _lsControllerCaNhanLoi2ToTruong,
          _lsControllerCaNhanLoi3QAQC,
          _lsControllerCaNhanLoi4KCS,
        ),
        widget.user.token.toString(),
      );
    }
  }

  @override
  void dispose() {
    _controllerPONumber.dispose();
    _controllerInspectLotQuantity.dispose();
    _controllerMucChapNhan.dispose();
    _controllerInspectQuantityDetail.dispose();
    _controllerTongSoSanPhamLoi.dispose();
    _controllerHideDetailLever.dispose();
    for (var i in _lsControllerThongTinKiemTra) {
      i.dispose();
    }
    for (var i in _lsControllerSoSanPhamLoi) {
      i.dispose();
    }
    for (var i in _lsControllerGhiChu) {
      i.dispose();
    }
    // for(var i in _focusError){
    //   i.dispose();
    // }
    for (var i in _lsControllerError) {
      i.dispose();
    }
    for (var i in _lsControllerCaNhanGayLoi) {
      i.dispose();
    }
    for (var i in _lsControllerCaNhanGayLoiMany) {
      for (var j in i) {
        j.dispose();
      }
    }
    // for (var i in _focusInformation) {
    //   i.dispose();
    // }
    for (var i in _lsControllerSoLuongLoi) {
      i.dispose();
    }
    // for (var i in _lsTextEditingControllerError_2) {
    //   i.dispose();
    // }
    for (var i in _lsControllerGhiChuLoi) {
      i.dispose();
    }
    debugPrint('dispose');

    WidgetsBinding.instance!.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    if (!mounted) return; // Add this check

    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return; // Add this check

      final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      setState(() {
        _bottomPadding = keyboardHeight;
      });
    });
  }

  Text _buildTextNotChooseLSXSAP() {
    return Text(
      _errorNotChooseLSX,
      style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true ? _buildTimeOutView() : _buildTabControllerView(context);
  }

  Widget _buildTimeOutView() {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimeOut)));
  }

  Widget _buildTabControllerView(BuildContext context) {
    return DefaultTabController(
        initialIndex: 0,
        length: 4,
        child: WillPopScope(
            onWillPop: () async {
              Navigator.pop(context, false);
              return false;
            },
            child: _buildChildBasedOnCondition()));
  }

  Widget _buildChildBasedOnCondition() {
    if (_isLoading) {
      return _buildLoading();
    }

    if (_isError) {
      return _buildError();
    }

    if (_isNotWifi) {
      return _buildWifi();
    }

    if (_qualityControlModel == null || _qualityControl == null) {
      return _buildEmptyQualityModel();
    }

    return _buildTabContent();
  }

  Widget _buildLoading() {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context, false);
          },
        ),
        title: Text(
          title,
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildError() {
    return Scaffold(
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ErrorView(),
    );
  }

  Widget _buildWifi() {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: Text(
              title,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
            )),
        body: LostConnect(checkConnect: () => _loadDataAndSetDefault()));
  }

  Widget _buildEmptyQualityModel() {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ListNotFoundView(),
    );
  }

  Widget formGroup({
    required String title,
    required bool enabled,
    required TextEditingController controller,
    required Function(String) onChanged,
    required String errorText,
    required bool error,
    TextInputType? keyboardType,
    List<TextInputFormatter>? textInputFormatter,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                decoration: BoxDecoration(border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                child: TextFormField(
                  enabled: enabled,
                  maxLines: null,
                  textAlign: TextAlign.center,
                  controller: controller,
                  keyboardType: keyboardType,
                  inputFormatters: textInputFormatter,
                  style: TextStyle(fontSize: 12.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    fillColor: Colors.white,
                    hintStyle: TextStyle(fontSize: 12.sp),
                  ),
                  onChanged: onChanged,
                ),
              ),
              Visibility(
                visible: error,
                child: SizedBox(height: 10.h),
              ),
              QualityErrorValidate(text: errorText, error: error)
            ]),
          ),
        ],
      ),
    );
  }

  Widget buildLimitDropdown({
    required String title,
    required dynamic value,
    required void Function(DropdownItemList?)? onChanged,
    required List<DropdownMenuItem<DropdownItemList>> items,
    required String errorText,
    required DropdownButtonBuilder selectedItemBuilder,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<DropdownItemList>(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: value ?? QualityControlDetailFunction.defaultValueLimitList,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: onChanged, // dropdown will disabled if this is null
                    items: items,
                    selectedItemBuilder: selectedItemBuilder,
                  ),
                ),
              ),
              SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
              QualityErrorValidate(text: errorText, error: _errorTestMethodDetail)
            ]),
          )
        ],
      ),
    );
  }

  // Color determineColor(QualityControl? qualityControl) {
  //   if (qualityControl != null && qualityControl.qcType == "NVL") {
  //     if (qualityControl.qualityChecker != null) {
  //       return Colors.grey.shade400;
  //     } else {
  //       return const Color(0xff0052cc);
  //     }
  //   } else {
  //     return const Color(0xff0052cc);
  //   }
  // }

  Color determineColor() {
    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      if (_qualityControl!.qualityChecker != null) {
        return Colors.grey.shade400;
      } else {
        return const Color(0xff0052cc);
      }
    } else {
      return const Color(0xff0052cc);
    }
  }

  Future<List<String>> filterLSXSAP(String query) async {
    List<String> _lsLSXSAP = [];
    if (query.isEmpty || query.length < 3) {
      return _lsLSXSAP;
    }

    debugPrint(query);
    debugPrint(_controllerLSXSAP.text);

    // final response = await ImportWareHouseFunction.fetchSoToKhai(_controllerLSXSAP.text, widget.user.token!.toString());
    final response = await QualityControlDetailFunction.fetchLSXSAP(_controllerLSXSAP.text, widget.user.token!.toString());

    debugPrint('-- Tien filterLSXSAP');
    debugPrint(json.encode(response));

    _lsLSXSAP = response!;
    return _lsLSXSAP;
  }

  Future<void> _loadQualityControlByLSQSAP(String lsxSAP) async {
    setState(() {
      _timeOut = false;
      _isLoading = true;
      _isNotWifi = false;
    });

    // GetQCSanPhamInfoByLSX
    var data = await QualityControlFunction.fetchQualityControlHeader(lsxSAP, _barcode, widget.user.token.toString());
    // var data = await QualityControlFunction.fetchCongDoanInfo(hangTagId, congDoan, widget.user.token.toString());

    debugPrint("--- TIEN test _loadQualityControlByLSQSAP");
    debugPrint(json.encode(data));

    if (data == null || data.qualityControl == null) {
      showAlert(
        context: context,
        title: 'Thông báo',
        content: 'Sản phẩm chưa xác nhận hoàn thành',
        buttonsWithOnPressed: {
          'OK': () => Navigator.of(context).pop(),
        },
      );

      setState(() {
        _isLoading = false;
        _isLSXSAPSelected = false;
      });

      return;
    }

    _setHeader(data);
  }

  void _setHeader(QualityControlModel data) {
    int default100Method = _lsTestMethodList.indexWhere((element) => element.catalogCode == "100");

    setState(() {
      // _congDoanInfoVm = data?.congDoanInfo as CongDoanInfoVm;
      _qualityControl = data?.qualityControl;

      _hangTagId = _qualityControl!.hangTagId.toString();

      if (_lsCongDoanNhoMasterData.isEmpty || _lsCongDoanLoiMasterData.length == 1) {
        _lsCongDoanNhoMasterData = prependDefault(data?.congDoanNhoList, QualityControlDetailFunction.defaultValueCongDoanNho);
      }

      if (_lsCongDoanLoiMasterData.isEmpty || _lsCongDoanLoiMasterData.length == 1) {
        _lsCongDoanLoiMasterData = prependDefault(data?.congDoanLoiList, QualityControlDetailFunction.defaultValueCongDoanNho);
      }
      _selectedCongDoanNho = _lsCongDoanNhoMasterData.lastOrNull;

      _lsCaNhanGayLoiMasterData = data.caNhanGayLoiList!;

      // _qualityControl?.workShopName = _congDoanInfoVm!.workShopName;
      // _qualityControl!.qualityControlDetail = _congDoanInfoVm!.qualityControlDetail;
      // _qualityControl!.qualityControlId = _congDoanInfoVm!.qualityControl?.qualityControlId;
      // _qualityControl!.confirmDate = _congDoanInfoVm!.qualityControl?.confirmDate;

      // _qualityControl!.qualityDate = _congDoanInfoVm!.qualityControlDetail?.qualityDate;
      // _qualityControl!.qcSaleEmployee = _congDoanInfoVm!.qualityControlDetail?.qcSaleEmployee;
      // _qualityControl!.qualityChecker = _congDoanInfoVm!.qualityControlDetail?.qualityChecker;

      // await Future.delayed(const Duration(seconds: 2));

      _isLSXSAPSelected = true;

      _isLoading = false;

      // Update 1: Set default
      _selectedTestMethod = _lsTestMethodList[default100Method];
      _controllerInspectQuantityDetail.text = "1";
    });
  }

  Widget _buildTabContent() {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: TitleQuality(title: title),
            actions: [
              if (radioViewGroup != "VIEWBTP")
                IconButton(
                  disabledColor: Colors.black38,
                  color: Colors.white,
                  // highlightColor: Colors.transparent,
                  // splashColor: Colors.transparent,
                  // hoverColor: Colors.transparent,
                  icon: Icon(
                    Icons.library_add_rounded,
                    size: 19.sp,
                    // color: Colors.white,
                  ),
                  tooltip: 'Thêm phiếu mới',
                  onPressed: !_isNewMode
                      ? () async {
                          clearForNew();
                        }
                      : null,
                ),
            ]),
        bottomNavigationBar: SafeArea(
          child: TabBar(
            onTap: (_) => FocusManager.instance.primaryFocus?.unfocus(),
            unselectedLabelColor: Colors.black,
            labelColor: const Color(0xff0052cc),
            labelStyle: TextStyle(fontSize: 13.sp),
            indicatorColor: const Color(0xff0052cc),
            tabs: const <Widget>[
              Tab(
                icon: Icon(Icons.edit_note_rounded),
                text: 'Phiếu KT',
              ),
              Tab(
                icon: Icon(Icons.details_rounded),
                text: 'Mẫu CT',
              ),
              Tab(
                icon: Icon(Icons.list),
                text: "T.Tin KT",
              ),
              Tab(
                icon: Icon(Icons.error_outline_outlined),
                text: "T.Tin Lỗi",
              ),
            ],
          ),
        ),
        body: LoadingOverlay(
          isLoading: _isLoadingCongDoan,
          child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              children: <Widget>[
                // 1. Phiếu KT
                SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: _bottomPadding),
                  child: SafeArea(
                    minimum: EdgeInsets.symmetric(horizontal: 10.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 10.h),
                        LabeledDetailRow(
                            title: 'Barcode:',
                            text: _barcode,
                            textStyle: TextStyle(
                              fontSize: 12.sp,
                              // fontWeight: FontWeight.bold,
                            )),
                        SizedBox(height: 10.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(
                              flex: 3,
                              child: QualityTitleField(title: "LSX SAP:"),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: Column(
                                children: <Widget>[
                                  Container(
                                    // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: Stack(
                                      children: [
                                        TypeAheadField(
                                          minCharsForSuggestions: 3,
                                          suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                            constraints: BoxConstraints(
                                              minHeight: 200.h,
                                              maxHeight: 200.h,
                                            ),
                                          ),
                                          textFieldConfiguration: TextFieldConfiguration(
                                              enabled: checkQualityControl() && _isTTFCode != true,
                                              decoration: InputDecoration(
                                                labelStyle: TextStyle(fontSize: 11.sp),
                                                contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                                isDense: true,
                                                border: InputBorder.none,
                                                focusedBorder: InputBorder.none,
                                                enabledBorder: InputBorder.none,
                                                hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
                                                hintStyle: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic),
                                              ),
                                              style: TextStyle(fontSize: 12.sp),
                                              controller: _controllerLSXSAP,
                                              keyboardType: TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter.digitsOnly,
                                              ],
                                              // focusNode: focusError[index],
                                              onChanged: (value) {
                                                // _onDanhSachLoiChanged(index);
                                                setState(() {
                                                  _controllerLSXSAP.text = value;
                                                  _controllerLSXSAP.selection =
                                                      TextSelection.collapsed(offset: _controllerLSXSAP.text.length); // Keep cursor at the end
                                                });
                                              }),
                                          suggestionsCallback: (pattern) {
                                            // return QualityControlFunction.filterQualityControlErrorList(_lsErrorList ?? [], pattern);
                                            return filterLSXSAP(pattern);
                                          },
                                          itemBuilder: (context, suggestion) {
                                            return Container(
                                              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                                              child: Text((suggestion), style: TextStyle(fontSize: 12.sp)),
                                            );
                                          },
                                          onSuggestionSelected: (suggestion) async {
                                            if (_controllerLSXSAP.text == suggestion && _isLSXSAPSelected) {
                                              return;
                                            }

                                            setState(() {
                                              _controllerLSXSAP.text = suggestion;
                                              _isLSXSAPSelected = true;
                                            });
                                            await _loadQualityControlByLSQSAP(suggestion);
                                          },
                                          noItemsFoundBuilder: (value) {
                                            return Padding(
                                              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                              child: Text("Không tìm thấy LSX SAP", style: TextStyle(fontSize: 11.sp)),
                                            );
                                          },
                                        ),
                                        // X button to clear the text
                                        Visibility(
                                          visible: _checkCanClearLSX(),
                                          child: Positioned(
                                            top: 1,
                                            right: 5,
                                            child: InkWell(
                                              onTap: () async {
                                                setState(() {
                                                  _controllerLSXSAP.text = "";
                                                  _isLSXSAPSelected = false;
                                                  _qualityControl = QualityControl();
                                                  _lsCongDoanNhoMasterData = [QualityControlDetailFunction.defaultValueCongDoanNho];
                                                  _selectedCongDoanNho = QualityControlDetailFunction.defaultValueCongDoanNho;
                                                });

                                                clearForNew();
                                              },
                                              child: Padding(
                                                padding: const EdgeInsets.all(4.0),
                                                child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // SizedBox(height: _errorPO == true ? 10.h : 0),
                                  // QualityErrorValidate(error: _errorPO, text: "Vui lòng nhập SKU"),
                                ],
                              ),
                            )
                          ],
                        ),
                        SizedBox(height: 10.h),
                        // Row(
                        //   children: [
                        //     const Expanded(flex: 3, child: FieldQuantity(field: "Công đoạn:")),
                        //     SizedBox(width: 10.w),
                        //     Visibility(
                        //       visible: _isLSXSAPSelected,
                        //       child: Expanded(
                        //         flex: 7,
                        //         child: Column(children: <Widget>[
                        //           Container(
                        //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                        //             decoration: BoxDecoration(
                        //               border: Border.all(width: 0.5, color: Colors.grey.shade400),
                        //               borderRadius: BorderRadius.circular(3.r),
                        //             ),
                        //             child: DropdownButtonHideUnderline(
                        //               child: DropdownButton<DropdownItemList>(
                        //                 isExpanded: true,
                        //                 isDense: true,
                        //                 itemHeight: null,
                        //                 value: _selectedCongDoanNho ?? QualityControlDetailFunction.defaultValueCongDoanNho,
                        //                 iconSize: 15.sp,
                        //                 style: const TextStyle(color: Colors.white),
                        //                 onChanged: (value) {
                        //                   _onCongDoanNhoChanged(value);
                        //                 },
                        //                 items: _lsCongDoanNhoMasterData.map((DropdownItemList method) {
                        //                   return DropdownMenuItem<DropdownItemList>(
                        //                       value: method,
                        //                       child: Padding(
                        //                         padding: EdgeInsets.symmetric(vertical: 5.h),
                        //                         child: Text(
                        //                           method.catalogTextVi.toString(),
                        //                           style: TextStyle(color: Colors.black, fontSize: 11.sp),
                        //                         ),
                        //                       ));
                        //                 }).toList(),
                        //                 selectedItemBuilder: (BuildContext context) {
                        //                   return _lsCongDoanNhoMasterData.map<Widget>((DropdownItemList method) {
                        //                     return Text(
                        //                       method.catalogTextVi.toString(),
                        //                       style: TextStyle(color: Colors.black, fontSize: 11.sp),
                        //                       overflow: TextOverflow.ellipsis,
                        //                     );
                        //                   }).toList();
                        //                 },
                        //               ),
                        //             ),
                        //           ),
                        //           SizedBox(height: _errorCongDoanNho == true ? 10.h : 0),
                        //           QualityErrorValidate(text: _errorNotChooseCongDoan, error: _errorCongDoanNho)
                        //         ]),
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        Row(mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.center, children: <Widget>[
                          const Expanded(
                            flex: 3,
                            child: QualityTitleField(title: "Chức năng:"),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: Row(
                              children: <Widget>[
                                RadioHelper<String>(
                                  text: 'QC sản phẩm',
                                  value: 'QCSP',
                                  groupValue: radioViewGroup,
                                  enabled: _isLSXSAPSelected,
                                  onRadioChanged: (String? newValue) async {
                                    // Reload QC
                                    await _refreshQCSanPhamData();
                                    setState(() {
                                      radioViewGroup = newValue!;
                                    });
                                  },
                                ),
                                RadioHelper<String>(
                                  text: 'Xem lại QC BTP',
                                  value: 'VIEWBTP',
                                  groupValue: radioViewGroup,
                                  enabled: _isLSXSAPSelected,
                                  onRadioChanged: (String? newValue) {
                                    // Reload QC
                                    _congDoanNhoNotSelected();
                                    setState(() {
                                      radioViewGroup = newValue!;
                                    });
                                  },
                                ),
                              ],
                            ),
                          )
                        ]),

                        Visibility(
                          visible: radioViewGroup == 'VIEWBTP',
                          child: Row(
                            children: [
                              const Expanded(flex: 3, child: FieldQuantity(field: "Công đoạn BTP:")),
                              SizedBox(width: 10.w),
                              Expanded(
                                flex: 7,
                                child: Column(children: <Widget>[
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<DropdownItemList>(
                                        isExpanded: true,
                                        isDense: true,
                                        itemHeight: null,
                                        value: _selectedCongDoanNho ?? QualityControlDetailFunction.defaultValueCongDoanNho,
                                        iconSize: 15.sp,
                                        style: const TextStyle(color: Colors.white),
                                        onChanged: (value) {
                                          _onCongDoanNhoChanged(value);
                                        },
                                        items: _lsCongDoanNhoMasterData.map((DropdownItemList method) {
                                          return DropdownMenuItem<DropdownItemList>(
                                              value: method,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Text(
                                                  method.catalogTextVi.toString(),
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                ),
                                              ));
                                        }).toList(),
                                        selectedItemBuilder: (BuildContext context) {
                                          return _lsCongDoanNhoMasterData.map<Widget>((DropdownItemList method) {
                                            return Text(
                                              method.catalogTextVi.toString(),
                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                              overflow: TextOverflow.ellipsis,
                                            );
                                          }).toList();
                                        },
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: _errorCongDoanNho == true ? 10.h : 0),
                                  QualityErrorValidate(text: _errorNotChooseCongDoan, error: _errorCongDoanNho)
                                ]),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10.h),
                        // Nhà máy
                        // Phân xưởng
                        // Thời gian Confirm
                        // Khách hàng
                        // LSX ĐT
                        // ĐSX
                        // LSX SAP
                        // Sản phẩm
                        // Số lượng lô hàng
                        // Mã chi tiết
                        // Số lượng chi tiết/Cụm
                        // Đơn vị
                        InfoQCSanPham(qualityControl: _qualityControl),
                        // const FieldQuantity(field: "Tình trạng MT:"),
                        LabeledDetailRow(title: 'Tình trạng MT:', text: _qualityControl!.tinhTrangMoiTruong ?? ""),
                        SizedBox(height: 10.h),
                        LabeledDetailRow(title: 'Loại NVL:', text: _qualityControl!.productType ?? ""),
                        SizedBox(height: 10.h),
                        LabeledDetailRow(title: 'Màu hoàn thiện:', text: _qualityControl!.mauHoanThien ?? ""),
                        SizedBox(height: 10.h),
                        LabeledDetailRow(title: 'SO:', text: _qualityControl!.so ?? ""),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.center,
                        //   crossAxisAlignment: CrossAxisAlignment.center,
                        //   children: <Widget>[
                        //     const Expanded(flex: 3, child: FieldQuantity(field: "Màu hoàn thiện:")),
                        //     SizedBox(width: 10.w),
                        //     Expanded(
                        //         flex: 7,
                        //         child: Column(children: <Widget>[
                        //           Container(
                        //             // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                        //             decoration: BoxDecoration(
                        //                 border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                        //             child: TextFormField(
                        //               enabled: true,
                        //               maxLines: null,
                        //               textAlign: TextAlign.center,
                        //               controller: _controllerMauHoanThien,
                        //               style: TextStyle(fontSize: 12.sp),
                        //               decoration: InputDecoration(
                        //                 border: InputBorder.none,
                        //                 isDense: true,
                        //                 contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                        //                 errorBorder: InputBorder.none,
                        //                 disabledBorder: InputBorder.none,
                        //                 filled: true,
                        //                 fillColor: Colors.white,
                        //                 hintStyle: TextStyle(fontSize: 12.sp),
                        //               ),
                        //             ),
                        //           )
                        //         ]))
                        //   ],
                        // ),
                        SizedBox(height: 10.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 3, child: FieldQuantity(field: "Ngày kiểm tra:")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: GestureDetector(
                                  onTap: canSubmit()
                                      ? () async {
                                          // _pickDateIOS(context);
                                          if (Platform.isAndroid) {
                                            final getDate = await QualityControlFunction.pickDate(context, _qualityDate);
                                            if (!mounted) return;
                                            _setDate(getDate);
                                          } else {
                                            final getDateIOS = await QualityControlFunction.pickDateIOS(context);
                                            if (!mounted) return;
                                            _setDate(getDateIOS);
                                          }
                                        }
                                      : null,
                                  child: TitleDateTimeQuality(date: _qualityDateStr)),
                            )
                          ],
                        ),
                        SizedBox(height: 10.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 3, child: FieldQuantity(field: "NV kiểm tra")),
                            SizedBox(width: 10.w),
                            Expanded(flex: 7, child: TitleStaff(selectedStaff: _qualityChckerInfo))
                          ],
                        ),
                        SizedBox(height: 10.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 3, child: FieldQuantity(field: "MSNV kiểm tra")),
                            SizedBox(width: 10.w),
                            Expanded(flex: 7, child: TextBox(text: _qualityChckerInfo?.salesEmployeeCode ?? ''))
                          ],
                        ),
                        SizedBox(height: 10.h),
                        SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(
                              flex: 3,
                              child: Align(alignment: Alignment.centerLeft, child: QualityTitleField(title: "Hình ảnh")),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                                flex: 7,
                                child: _lsFileHeader.isEmpty
                                    ? Row(
                                        children: <Widget>[
                                          Container(
                                            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10.r),
                                              color: Colors.grey.shade100,
                                            ),
                                            child: InkWell(
                                              onTap: !canSubmit()
                                                  ? null
                                                  : () async {
                                                      final check = await QualityControlFunction.pickImage(context);
                                                      debugPrint(check.toString());
                                                      if (check != null) {
                                                        bool checkPermission = await ImageFunction.handlePermission(check);
                                                        if (checkPermission == true) {
                                                          if (check == true) {
                                                            List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                              maxWidth: globalImageConfig.maxWidth,
                                                              maxHeight: globalImageConfig.maxHeight,
                                                              imageQuality: globalImageConfig.imageQuality,
                                                            );
                                                            if (selectedImages.isEmpty) return;
                                                            for (var i in selectedImages) {
                                                              final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                              if (!mounted) return;
                                                              _pickFileImage(itemImage);
                                                            }
                                                          } else {
                                                            final image = await ImagePicker().pickImage(
                                                                maxWidth: globalImageConfig.maxWidth,
                                                                maxHeight: globalImageConfig.maxHeight,
                                                                imageQuality: globalImageConfig.imageQuality,
                                                                source: ImageSource.camera);
                                                            if (image == null) return;
                                                            final imageProfile = await ImageFunction.saveImage(image.path);
                                                            if (!mounted) return;
                                                            _pickFileImage(imageProfile);
                                                          }
                                                        }
                                                      }
                                                    },
                                              child: Text(
                                                "Chọn tệp",
                                                style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10.w),
                                          Center(
                                            child: Text(
                                              "Chưa chọn tệp nào",
                                              style: TextStyle(fontSize: 11.sp),
                                            ),
                                          ),
                                        ],
                                      )
                                    : Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: InkWell(
                                            onTap: () async {
                                              // debugPrint('yes');
                                              final check = await QualityControlFunction.pickImage(context);
                                              debugPrint(check.toString());
                                              if (check != null) {
                                                bool checkPermission = await ImageFunction.handlePermission(check);
                                                if (checkPermission == true) {
                                                  if (check == true) {
                                                    List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                      maxWidth: globalImageConfig.maxWidth,
                                                      maxHeight: globalImageConfig.maxHeight,
                                                      imageQuality: globalImageConfig.imageQuality,
                                                    );
                                                    if (selectedImages.isEmpty) return;
                                                    for (var i in selectedImages) {
                                                      final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                      _pickFileImage(itemImage);
                                                    }
                                                  } else {
                                                    final image = await ImagePicker().pickImage(
                                                        maxWidth: globalImageConfig.maxWidth,
                                                        maxHeight: globalImageConfig.maxHeight,
                                                        imageQuality: globalImageConfig.imageQuality,
                                                        source: ImageSource.camera);
                                                    if (image == null) return;
                                                    final imageProfile = await ImageFunction.saveImage(image.path);
                                                    _pickFileImage(imageProfile);
                                                  }
                                                }
                                              }
                                            },
                                            child: Text(
                                              "Chọn tệp",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: 10.h),
                                        ListChooseImage(lsFileTabCheck: _lsFileHeader, deleteListImageTabCheck: _deleteListImageTabCheck),
                                        SizedBox(height: 10.h),
                                      ])),
                          ],
                        ),
                        // SizedBox(height: 10.h),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.center,
                        //   crossAxisAlignment: CrossAxisAlignment.center,
                        //   children: <Widget>[
                        //     const Expanded(flex: 3, child: QualityTitleField(title: "Kết quả")),
                        //     SizedBox(width: 10.w),
                        //     Expanded(
                        //       flex: 7,
                        //       child: Column(children: <Widget>[
                        //         Container(
                        //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                        //             decoration: BoxDecoration(
                        //               border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                        //               borderRadius: BorderRadius.circular(3.r),
                        //             ),
                        //             child: DropdownQuantityResult(
                        //                 selectedResult: _selectedResult,
                        //                 lsResultList: _lsResultList,
                        //                 qualityControl: _qualityControl,
                        //                 onChangeResult: _getSelectedResult)),
                        //         SizedBox(height: _errorSelectedResultQualityView == true ? 10.h : 0),
                        //         QualityErrorValidate(error: _errorSelectedResultQualityView, text: "Vui lòng chọn kết quả"),
                        //       ]),
                        //     )
                        //   ],
                        // ),
                        Visibility(
                          visible: _qualityControl!.fileViewModel == null || _qualityControl!.fileViewModel!.isEmpty ? false : true,
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: ElevatedButton.icon(
                              icon: Icon(
                                Icons.image,
                                color: Colors.white,
                                size: 15.sp,
                              ),
                              style: ButtonStyle(
                                side: MaterialStateProperty.all(
                                  const BorderSide(
                                    color: Color(0xff0052cc),
                                  ),
                                ),
                                backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                              ),
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return DialogImage(title: 'HÌNH ẢNH KIỂM TRA', listImage: _qualityControl!.fileViewModel);
                                  },
                                );
                              },
                              label: Text(
                                'Hình ảnh',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 11.sp,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
                ),
                // 2. T.Tin KT
                SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: _bottomPadding),
                  child: SafeArea(
                    minimum: EdgeInsets.symmetric(horizontal: 5.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 10.h),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const QualityTitle(title: 'Mẫu thử chi tiết'),
                              !_isLSXSAPSelected
                                  ? Padding(
                                      padding: EdgeInsets.only(top: 15, bottom: 5),
                                      child: Center(child: _buildTextNotChooseLSXSAP()),
                                    )
                                  : _selectedCongDoanNho == null
                                      ? const Padding(
                                          padding: EdgeInsets.only(top: 15, bottom: 5),
                                          child: Center(
                                            child: Text(
                                              _errorSelectCongDoanBTP,
                                              style: TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                          ))
                                      : Column(children: [
                                          SizedBox(height: 10.h),
                                          Padding(
                                            padding: EdgeInsets.symmetric(horizontal: 15.w),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: <Widget>[
                                                const Expanded(flex: 4, child: QualityTitleField(title: "Phương pháp KT")),
                                                SizedBox(width: 10.w),
                                                Expanded(
                                                  flex: 6,
                                                  child: Column(
                                                    children: <Widget>[
                                                      Container(
                                                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                        decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                          borderRadius: BorderRadius.circular(3.r),
                                                        ),
                                                        child: DropdownButtonHideUnderline(
                                                          child: DropdownButton<TestMethodList>(
                                                            isExpanded: true,
                                                            isDense: true,
                                                            itemHeight: null,
                                                            value: _selectedTestMethod ?? QualityControlDetailFunction.defaultValueTestMethodList,
                                                            iconSize: 15.sp,
                                                            style: const TextStyle(color: Colors.white),
                                                            onChanged: !canSubmit() ? null : _getSelectedMethod,
                                                            items: _lsTestMethodList.map((TestMethodList method) {
                                                              return DropdownMenuItem<TestMethodList>(
                                                                  value: method,
                                                                  child: Padding(
                                                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                    child: Text(
                                                                      method.catalogTextVi.toString(),
                                                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                    ),
                                                                  ));
                                                            }).toList(),
                                                            selectedItemBuilder: (BuildContext context) {
                                                              return _lsTestMethodList.map<Widget>((TestMethodList method) {
                                                                return Text(method.catalogTextVi.toString(),
                                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                    overflow: TextOverflow.ellipsis);
                                                              }).toList();
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
                                                      QualityErrorValidate(text: "Vui lòng chọn phương pháp KT", error: _errorTestMethodDetail)
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: 5.h),
                                          formGroup(
                                            // InspectQuantity (Detail)
                                            title: "SL kiểm tra",
                                            // enabled: checkQualityControl(),
                                            enabled: canSubmit(),
                                            controller: _controllerInspectQuantityDetail,
                                            keyboardType: TextInputType.number,
                                            textInputFormatter: <TextInputFormatter>[
                                              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                            ],
                                            onChanged: (value) {
                                              _checkErrorQuantityCheckDetail();
                                            },
                                            errorText: "Vui lòng nhập SL kiểm tra",
                                            error: _errorQuantityCheckDetail,
                                          ),
                                          SizedBox(height: 5.h),
                                          Padding(
                                            padding: EdgeInsets.symmetric(horizontal: 15.w),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: <Widget>[
                                                const Expanded(flex: 4, child: FieldQuantity(field: "Tổng SL lỗi (SP/Chi tiết/)")),
                                                SizedBox(width: 10.w),
                                                Expanded(
                                                    flex: 6,
                                                    child: Container(
                                                      // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        // enabled: false,
                                                        readOnly: true,
                                                        maxLines: null,
                                                        controller: _controllerTongSoSanPhamLoi,
                                                        keyboardType: TextInputType.number,
                                                        inputFormatters: <TextInputFormatter>[
                                                          FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                        ],
                                                        textAlign: TextAlign.center,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          border: InputBorder.none,
                                                          isDense: true,
                                                          contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          filled: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                        ),
                                                      ),
                                                    ))
                                              ],
                                            ),
                                          ),
                                        ]),

                              // formGroup(
                              //   title: "Tổng số lượng SP lỗi",
                              //   enabled: checkQualityControl(),
                              //   controller: _controllerSLKiemTra,
                              //   onChanged: (value) {
                              //     _checkErrorQuantityCheckDetail();
                              //   },
                              //   errorText: "Vui lòng nhập SL kiểm tra",
                              //   error: _errorQuantityCheckDetail,
                              // ),
                              // SizedBox(height: 3.h),

                              SizedBox(height: 10.h),
                            ],
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
                ),
                // Checklist use SingleChildScrollView
                SingleChildScrollView(
                  padding: const EdgeInsets.only(bottom: 200),
                  child: SafeArea(
                    minimum: EdgeInsets.symmetric(horizontal: 5.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 10.h),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                            color: _selectedCongDoanNho == null ? Colors.white : Colors.grey.shade300,
                          ),
                          child: Column(
                            children: [
                              Column(
                                children: <Widget>[
                                  const QualityTitle(title: "Thông tin kiểm tra"), // Title
                                  // SizedBox(height: 10.h),
                                  !_isLSXSAPSelected
                                      ? Padding(
                                          padding: const EdgeInsets.only(top: 15, bottom: 0),
                                          child: _buildTextNotChooseLSXSAP(),
                                        )
                                      : _selectedCongDoanNho == null
                                          ? const Padding(
                                              padding: EdgeInsets.only(top: 15, bottom: 5),
                                              child: Center(
                                                child: Text(
                                                  _errorSelectCongDoanBTP,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ))
                                          : Column(
                                              children: List.generate(
                                                _lsThongTinKiemTra.length,
                                                (index) => Container(
                                                  color: Colors.white,
                                                  margin: EdgeInsets.symmetric(vertical: 4.h),
                                                  child: Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 1.w),
                                                    child: Column(
                                                      children: [
                                                        // itemHeader(),
                                                        // ItemThongTinKiemTra(index, context),
                                                        itemHeaderThongTinKiemTra(),
                                                        ItemThongTinKiemTraQAQC(
                                                          index: index,
                                                          qualityControl: _qualityControl,
                                                          lsGetFileInfor: _lsFileThongTinKiemTra,
                                                          lsThongTinKiemTra: _lsThongTinKiemTra,
                                                          lsThongTinKiemTraSelectMasterData: _lsThongTinKiemTraSelectMasterData,
                                                          checkVisiButtonInformation: _checkVisiButtonThongTinKiemTra,
                                                          pickFileImageInformation: _pickFileImageInformation,
                                                          deleteListQualityInformation: _deleteListQualityInformation,
                                                          deleteItemListInformation: _deleteItemListInformation,
                                                          lsControllerThongTinKiemTra: _lsControllerThongTinKiemTra,
                                                          lsControllerSoSanPhamLoi: _lsControllerSoSanPhamLoi,
                                                          lsControllerGhiChu: _lsControllerGhiChu,
                                                          checkClearLsSelectedInfo: _checkClearLsSelectedInfo,
                                                          pickerImage: _pickerImage,
                                                          lsErrorInfor: _lsErrorInfor,
                                                          checkErrorSelectedInfo: _checkErrorSelectedInfo,
                                                          updateTongSoSanPhamLoi: _updateTongSoSanPhamLoi,
                                                          onRadioChanged: _onRadioChanged,
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                                    child: IntrinsicHeight(
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                        children: <Widget>[
                                          Expanded(
                                            flex: 6,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                border: _selectedCongDoanNho == null ? null : Border.all(width: 0.5, color: Colors.grey.shade300),
                                              ),
                                              child: const Text(""),
                                            ),
                                          ),
                                          canSubmit()
                                              ? Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                                    height: 40.h,
                                                    decoration: BoxDecoration(
                                                      border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                    ),
                                                    child: Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                                                      child: ElevatedButton.icon(
                                                        icon: Icon(
                                                          Icons.add,
                                                          color: Colors.white,
                                                          size: 15.sp,
                                                        ),
                                                        style: ButtonStyle(
                                                          padding: MaterialStateProperty.all<EdgeInsets>(
                                                              EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w)),
                                                          side: MaterialStateProperty.all(
                                                            const BorderSide(
                                                              color: Colors.green,
                                                            ),
                                                          ),
                                                          backgroundColor: MaterialStateProperty.all(Colors.green),
                                                        ),
                                                        onPressed: !canSubmit()
                                                            ? null
                                                            : () {
                                                                _addNewThongTinKiemTra();
                                                              },
                                                        label: Text(
                                                          'Thêm',
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                            fontWeight: FontWeight.bold,
                                                            fontSize: 10.sp,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Container()
                                        ],
                                      ),
                                    ),
                                  ),
                                  // SizedBox(height: 10.h),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
                ),
                // 4. T.Tin Lỗi
                SingleChildScrollView(
                  padding: const EdgeInsets.only(bottom: 200),
                  child: SafeArea(
                    minimum: EdgeInsets.symmetric(horizontal: 5.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(height: 10.h),
                        Container(
                          color: const Color.fromRGBO(0, 0, 0, 0.1),
                          // decoration: BoxDecoration(
                          //   border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                          // ),
                          constraints: BoxConstraints(minHeight: 100.h),
                          child: Column(
                            children: <Widget>[
                              const QualityTitle(title: 'Thông tin lỗi'),
                              !_isLSXSAPSelected
                                  ? Padding(
                                      padding: const EdgeInsets.only(top: 15, bottom: 0),
                                      child: _buildTextNotChooseLSXSAP(),
                                    )
                                  : _selectedCongDoanNho == null
                                      ? const Padding(
                                          padding: EdgeInsets.only(top: 15, bottom: 5),
                                          child: Center(
                                            child: Text(
                                              _errorSelectCongDoanBTP,
                                              style: TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                          ))
                                      : Column(
                                          children: List.generate(
                                            _lsError.length,
                                            (index) {
                                              return Container(
                                                color: Colors.white,
                                                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 0.h),
                                                margin: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(horizontal: 0.w),
                                                  child: IntrinsicHeight(
                                                    child: Row(
                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                      children: <Widget>[
                                                        Expanded(
                                                          flex: 1,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                                                            ),
                                                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                                                            child: Center(
                                                              child: Text(
                                                                (index + 1).toString(),
                                                                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 12,
                                                          child: IntrinsicHeight(
                                                            child: Column(
                                                              mainAxisSize: MainAxisSize.min,
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                buildDanhSachLoiHeader(),
                                                                buildDanhSachLoi(index),
                                                                buildMucDoLoiHeader(),
                                                                buildMucDoLoi(index),
                                                                buildImagePickerList(index),
                                                                // Phương án xử lý
                                                                buidPhuongAnXuLy(index),
                                                                // Vị trí gây lỗi
                                                                buildViTriGayLoiHeader(),
                                                                Column(
                                                                  children: buildViTriGayLoiList(index),
                                                                ),
                                                                // Cá nhân gây lỗi
                                                                buildCaNhanGayLoiHeader(),
                                                                Column(
                                                                  children: buildCaNhanGayLoiList(index),
                                                                ),

                                                                // Delete button
                                                                buildDeleteButton(index),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                              canSubmit() ? ButtonAddNewCardError(addNewCardError: _addNewCardError, qualityControl: _qualityControl) : Container(),
                              SizedBox(height: 10.h),
                            ],
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              const Expanded(flex: 4, child: QualityTitleField(title: "Kết quả")),
                              SizedBox(width: 10.w),
                              Expanded(
                                flex: 6,
                                child: Column(children: <Widget>[
                                  DropdownDetailResult(
                                      getSelectedResultDetail: _getSelectedResultDetail,
                                      selectedResultDetail: _selectedResultDetail,
                                      lsResultList: _lsResultList,
                                      qualityControl: _qualityControl),
                                  SizedBox(height: _errorResultCheckDetail == true ? 10.h : 0),
                                  QualityErrorValidate(text: "Vui lòng chọn kết quả", error: _errorResultCheckDetail)
                                ]),
                              )
                            ],
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Visibility(
                          visible: radioViewGroup != "VIEWBTP",
                          child: GestureDetector(
                            onTap: canSubmit()
                                ? () {
                                    submitData();
                                  }
                                : null,
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 15.h),
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5.r),
                                ),
                                color: !canSubmit() ? Colors.grey.shade400 : const Color(0xff0052cc),
                              ),
                              child: Center(
                                child: Text(
                                  'Submit',
                                  style: TextStyle(color: Colors.white, fontSize: 12.sp, fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  IntrinsicHeight buildDanhSachLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text(
                "Danh sách lỗi",
                style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                ),
                child: Text(
                  "Số lượng lỗi",
                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                )),
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildDanhSachLoi(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 0.5,
                    color: Colors.grey.shade300,
                  ),
                ),
                child: Container(
                  child: Padding(
                    padding: REdgeInsets.all(5),
                    child: Center(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 0.5.w,
                            color: Colors.grey.shade400,
                          ),
                        ),
                        child: TypeAheadField(
                          suggestionsBoxDecoration: SuggestionsBoxDecoration(
                            constraints: BoxConstraints(
                              minWidth: 150.w,
                            ),
                          ),
                          textFieldConfiguration: TextFieldConfiguration(
                              // enabled: checkQualityControl(),
                              enabled: canSubmit(),
                              maxLines: null,
                              decoration: InputDecoration(
                                labelStyle: TextStyle(fontSize: 11.sp),
                                contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                isDense: true,
                                border: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                enabledBorder: InputBorder.none,
                              ),
                              controller: _lsControllerError[index],
                              // focusNode: focusError[index],
                              style: TextStyle(fontSize: 12.sp),
                              onChanged: (value) {
                                _onDanhSachLoiChanged(index);
                              }),
                          suggestionsCallback: (pattern) {
                            return QualityControlFunction.filterQualityControlErrorList(_lsErrorList ?? [], pattern);
                          },
                          itemBuilder: (context, suggestion) {
                            return ListTile(
                              title: Text((suggestion as ErrorList).catalogTextVi ?? " ", style: TextStyle(fontSize: 12.sp)),
                            );
                          },
                          onSuggestionSelected: (suggestion) {
                            _setTypeAhead(TypeAheadErrorQuatity(index: index, errorList: suggestion));
                          },
                          noItemsFoundBuilder: (value) {
                            return Padding(
                                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                          },
                        ),
                      ),
                    ),
                  ),
                )),
          ),
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Padding(
                padding: REdgeInsets.all(5),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                  ),
                  child: TextFormField(
                    // enabled: checkQualityControl(),
                    enabled: canSubmit(),
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                    controller: _lsControllerSoLuongLoi[index],
                    style: TextStyle(fontSize: 12.sp),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      filled: true,
                      isDense: true,
                      fillColor: Colors.white,
                      hintStyle: TextStyle(fontSize: 12.sp),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  IntrinsicHeight buildMucDoLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text(
                "Mức độ lỗi",
                style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                ),
                child: Text(
                  "Ghi chú",
                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                )),
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildMucDoLoi(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // Mức độ lỗi
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Column(
                children: <Widget>[
                  Padding(
                    padding: REdgeInsets.all(5),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DataGetDefectLevel>(
                          isExpanded: true,
                          isDense: true,
                          itemHeight: null,
                          value: _lsSelectedMucDoLoi[index],
                          iconSize: 15.sp,
                          style: const TextStyle(color: Colors.white),
                          onChanged: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                              ? null
                              : (DataGetDefectLevel? value) {
                                  _getSelectedDefectLevel(DropdownDefetchLevel(index: index, value: value));
                                },
                          items: _lsDataGetDefetchLevel.map((DataGetDefectLevel result) {
                            return DropdownMenuItem<DataGetDefectLevel>(
                              value: result,
                              child: Text(
                                result.value.toString(),
                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                              ),
                            );
                          }).toList(),
                          selectedItemBuilder: (BuildContext context) {
                            return _lsDataGetDefetchLevel.map(
                              (DataGetDefectLevel result) {
                                return Text(
                                  result.value.toString(),
                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                  overflow: TextOverflow.ellipsis,
                                );
                              },
                            ).toList();
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Ghi chú lỗi
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: REdgeInsets.all(5),
                    child: Container(
                      // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                      ),
                      child: TextFormField(
                        // enabled: checkQualityControl(),
                        enabled: canSubmit(),
                        maxLines: null,
                        textAlign: TextAlign.center,
                        controller: _lsControllerGhiChuLoi[index],
                        style: TextStyle(fontSize: 12.sp),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          filled: true,
                          isDense: true,
                          fillColor: Colors.white,
                          hintStyle: TextStyle(fontSize: 12.sp),
                          // contentPadding: EdgeInsets.zero,
                          contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> caNhanGayLoiTitleList = [
    "1. Quản đốc",
    "2. Tổ trưởng/Kỹ thuật",
    "3. QA-QC",
    "4. KCS (Gây lỗi)",
    "5. Cá nhân gây lỗi",
  ];

  List<String> viTriGayLoiTitleList = [
    "Công đoạn lỗi",
    "Nhà máy",
    "Phân xưởng",
    "Tổ chuyền",
  ];

  Widget customExpandedWidget(int flexValue, Widget childWidget, {bool showBorder = true}) {
    return Expanded(
      flex: flexValue,
      child: Container(
        constraints: const BoxConstraints(minHeight: inputHeight),
        decoration: const BoxDecoration(
            // border: Border.all(width: 0.5, color: Colors.grey.shade300),
            ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 5.w),
          child: Container(
            decoration: showBorder
                ? BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  )
                : null,
            child: Align(
              alignment: Alignment.centerLeft,
              child: childWidget,
            ),
          ),
        ),
      ),
    );
  }

  IntrinsicHeight buildCaNhanGayLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text("Cá nhân gây lỗi", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold)),
            ),
          ),
          // Expanded(
          //   flex: 6,
          //   child: Container(
          //       padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
          //       decoration: BoxDecoration(
          //         border: Border.all(width: 0.5, color: Colors.grey.shade300),
          //       ),
          //       child: Text("Ghi chú", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold))),
          // ),
        ],
      ),
    );
  }

  IntrinsicHeight buildViTriGayLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text("Vị trí gây lỗi", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold)),
            ),
          ),
          // Expanded(
          //   flex: 6,
          //   child: Container(
          //       padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
          //       decoration: BoxDecoration(
          //         border: Border.all(width: 0.5, color: Colors.grey.shade300),
          //       ),
          //       child: Text("Ghi chú", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold))),
          // ),
        ],
      ),
    );
  }

  TextEditingController _getControllerForCaNhanIndex(int caNhanIndex, int errorIndex) {
    switch (caNhanIndex) {
      case 0:
        return _lsControllerCaNhanLoi1QuanDoc[errorIndex];
      case 1:
        return _lsControllerCaNhanLoi2ToTruong[errorIndex];
      case 2:
        return _lsControllerCaNhanLoi3QAQC[errorIndex];
      case 3:
        return _lsControllerCaNhanLoi4KCS[errorIndex];
      default:
        throw Exception('Invalid caNhanIndex: $caNhanIndex');
    }
  }

  List<Widget> buildCaNhanGayLoiList(index) {
    var isMany = true;

    // List<Widget> listUICaNhanGayLoi = List<Widget>.generate(caNhanGayLoiTitleList.length, (i) => buildCaNhanGayLoiItem(i, index));
    List<Widget> listUICaNhanGayLoi; // = List<Widget>.generate(caNhanGayLoiTitleList.length, (i) => buildCaNhanGayLoiItem(i, index));

    if (isMany) {
      listUICaNhanGayLoi = [
        buildCaNhanGayLoiItem(0, index), // Quản đốc
        buildCaNhanGayLoiItem(1, index), // Tổ trưởng/KT
        buildCaNhanGayLoiItem(2, index), // QAQC
        buildCaNhanGayLoiItem(3, index), // KCS
        buildCaNhanGayLoiItemMany(4, index), // Cá nhân gây lỗi lít 10
      ];
    }

    return listUICaNhanGayLoi;
  }

  IntrinsicHeight buildCaNhanGayLoiItem(int caNhanIndex, int errorIndex) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // Ca nhan gay loi - Quan doc
          customExpandedWidget(3, Text(caNhanGayLoiTitleList[caNhanIndex], style: TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
            6,
            CaNhanGayLoiParentTypeAhead(
              enabled: canSubmit(),
              showRemoveButton: false,
              masterDataList: _lsCaNhanGayLoiAllMasterData ?? [],
              onSuggestionSelected: (selectedItem) {
                // _setTypeAheadCaNhanGayLoiMany(
                //   TypeAheadCaNhanGayLoi(
                //     index: errorIndex,
                //     selectedItem: selectedItem,
                //     caNhanLoiIndex: caNhanLoiIndex,
                //   ),
                // );
                debugPrint(caNhanIndex.toString() + " " + errorIndex.toString() + " " + selectedItem.catalogTextVi.toString());
                _getControllerForCaNhanIndex(caNhanIndex, errorIndex).text = selectedItem.catalogTextVi!;
              },
              // _getControllerForCaNhanIndex(caNhanIndex, errorIndex)
              controller: _getControllerForCaNhanIndex(caNhanIndex, errorIndex), // Assuming you have a matching controller for each widget
              onChanged: (value) {
                // _clearCaNhanGayLoiIndexMany(caNhanLoiIndex, errorIndex)
                debugPrint(caNhanIndex.toString() + " " + errorIndex.toString() + " " + value.toString());
              },
            ),
            showBorder: false,
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildCaNhanGayLoiItemMany(int i, int errorIndex) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // Ca nhan gay loi - Quan doc
          customExpandedWidget(3, Text(caNhanGayLoiTitleList[i], style: const TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
            6,
            Column(
              children: <Widget>[
                ...List.generate(
                  _lsControllerCaNhanGayLoiMany[errorIndex].length,
                  (caNhanLoiIndex) => CaNhanGayLoiTypeAhead(
                    // enabled: checkQualityControl(),
                    enabled: canSubmit(),
                    showRemoveButton: canSubmit(),
                    masterDataList: _getCaNhanGayLoiMaster(),
                    onSuggestionSelected: (selectedItem) {
                      _setTypeAheadCaNhanGayLoiMany(
                        TypeAheadCaNhanGayLoi(
                          index: errorIndex,
                          selectedItem: selectedItem,
                          caNhanLoiIndex: caNhanLoiIndex,
                        ),
                      );
                    },
                    controller: _lsControllerCaNhanGayLoiMany[errorIndex][caNhanLoiIndex], // Assuming you have a matching controller for each widget
                    onChanged: (value) => _clearCaNhanGayLoiIndexMany(caNhanLoiIndex, errorIndex),
                    caNhanLoiIndex: caNhanLoiIndex,
                    errorIndex: errorIndex, // errorIndex
                    onAddCaNhanLoi: () => _addCaNhanGayLoiMany(errorIndex),
                    onRemoveCaNhanLoi: () => _removeCaNhanGayLoiMany(errorIndex, caNhanLoiIndex),
                  ),
                )
              ],
            ),
            showBorder: false,
          ),
          // Expanded(
          //   // Existing or use Flexible for the red container
          //   flex: 1,
          //   child: Container(
          //     constraints: const BoxConstraints(minHeight: inputHeight),
          //     // color: Colors.red,
          //     child: addCaNhanGayLoiMany(errorIndex),
          //   ),
          // )
        ],
      ),
    );
  }

  _addCaNhanGayLoiMany(int errorIndex) {
    if (_lsControllerCaNhanGayLoiMany[errorIndex].length >= 10) {
      return;
    }
    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex].add(TextEditingController());
      _lsSelectedCaNhanGayLoiMany[errorIndex].add(null);
    });
  }

  _removeCaNhanGayLoiMany(int errorIndex, int caNhanLoiIndex) {
    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex].removeAt(caNhanLoiIndex);
      _lsSelectedCaNhanGayLoiMany[errorIndex].removeAt(caNhanLoiIndex);
    });
  }

  List<Widget> buildViTriGayLoiList(index) {
    return [
      // Vị trí gây lỗi - Công đoạn lỗi
      IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            customExpandedWidget(4, Text(viTriGayLoiTitleList[0], style: TextStyle(fontSize: 11)), showBorder: false),
            customExpandedWidget(
                6,
                DropdownButtonHideUnderline(
                  child: DropdownButton<DropdownItemList>(
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: _lsSelectedCongDoanLoi[index] ?? QualityControlDetailFunction.defaultValueCongDoanNho,
                    iconSize: 15.sp,
                    padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                    onChanged: canSubmit()
                        ? (value) {
                            _onCongDoanLoiChanged(value, index);
                          }
                        : null,
                    items: _lsCongDoanLoiMasterData.map((DropdownItemList method) {
                      return DropdownMenuItem<DropdownItemList>(
                          value: method,
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 5.h),
                            child: Text(
                              method.catalogTextVi.toString(),
                              style: TextStyle(color: Colors.black, fontSize: 10.sp),
                            ),
                          ));
                    }).toList(),
                    selectedItemBuilder: (BuildContext context) {
                      return _lsCongDoanLoiMasterData.map<Widget>((DropdownItemList method) {
                        return Text(
                          method.catalogTextVi.toString(),
                          style: TextStyle(color: Colors.black, fontSize: 10.sp),
                          overflow: TextOverflow.ellipsis,
                        );
                      }).toList();
                    },
                  ),
                )),
          ],
        ),
      ),
      // Vị trí gây lỗi - Nhà máy
      IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            customExpandedWidget(4, Text(viTriGayLoiTitleList[1], style: TextStyle(fontSize: 11)), showBorder: false),
            customExpandedWidget(
                6,
                TextFormField(
                  // enabled: checkQualityControl(),
                  enabled: false,
                  // readOnly: true,
                  maxLines: null,
                  textAlign: TextAlign.left,
                  controller: _lsControllerNhaMayLoi[index],
                  style: TextStyle(fontSize: 10.sp),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    isDense: true,
                    fillColor: Colors.white,
                    hintStyle: TextStyle(fontSize: 12),
                    contentPadding: EdgeInsets.symmetric(vertical: 4.0, horizontal: 4.0),
                  ),
                )),
          ],
        ),
      ),
      // Vị trí gây lỗi - Phân xưởng
      IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            customExpandedWidget(4, Text(viTriGayLoiTitleList[2], style: TextStyle(fontSize: 11)), showBorder: false),
            customExpandedWidget(
                6,
                TextFormField(
                  // enabled: checkQualityControl(),
                  enabled: false,
                  // readOnly: true,
                  maxLines: null,
                  textAlign: TextAlign.left,
                  controller: _lsControllerPhanXuongLoi[index],
                  style: TextStyle(fontSize: 10.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    isDense: true,
                    fillColor: Colors.white,
                    hintStyle: TextStyle(fontSize: 12),
                    contentPadding: EdgeInsets.symmetric(vertical: 4.0, horizontal: 4.0),
                  ),
                )),
          ],
        ),
      ),
      // Vị trí gây lỗi - Tổ chuyền
      IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            customExpandedWidget(4, Text(viTriGayLoiTitleList[3], style: TextStyle(fontSize: 11)), showBorder: false),
            customExpandedWidget(
                6,
                TextFormField(
                  // enabled: checkQualityControl(),
                  enabled: false,
                  // readOnly: true,
                  maxLines: null,
                  textAlign: TextAlign.left,
                  controller: _lsControllerToChuyenLoi[index],
                  style: TextStyle(fontSize: 10.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    isDense: true,
                    fillColor: Colors.white,
                    hintStyle: TextStyle(fontSize: 12),
                    contentPadding: EdgeInsets.symmetric(vertical: 4.0, horizontal: 4.0),
                  ),
                )),
          ],
        ),
      ),
    ];

    // return List<Widget>.generate(
    //   viTriGayLoiTitleList.length,
    //   (i) => buildViTriGayLoiItem(i, index),
    // );
  }

  IntrinsicHeight buildImagePickerList(int index) {
    return IntrinsicHeight(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              flex: 8,
              child: _lsFileHinhAnhLoi[index].isEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        GestureDetector(
                          onTap: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                              ? null
                              : () async {
                                  final check = await QualityControlFunction.pickImage(context);
                                  debugPrint(check.toString());
                                  if (check != null) {
                                    bool checkPermission = await ImageFunction.handlePermission(check);
                                    if (checkPermission == true) {
                                      if (check == true) {
                                        List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                          maxWidth: globalImageConfig.maxWidth,
                                          maxHeight: globalImageConfig.maxHeight,
                                          imageQuality: globalImageConfig.imageQuality,
                                        );
                                        if (selectedImages.isEmpty) return;
                                        for (var i in selectedImages) {
                                          final itemImage = await ImageFunction.saveImageMulti(i.path);
                                          _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: itemImage));
                                        }
                                      } else {
                                        final image = await ImagePicker().pickImage(
                                            maxWidth: globalImageConfig.maxWidth,
                                            maxHeight: globalImageConfig.maxHeight,
                                            imageQuality: globalImageConfig.imageQuality,
                                            source: ImageSource.camera);
                                        if (image == null) return;
                                        final imageProfile = await ImageFunction.saveImage(image.path);
                                        _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: imageProfile));
                                      }
                                    }
                                  }
                                },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: Colors.grey.shade100,
                            ),
                            child: Center(
                              child: Text(
                                "Chọn tệp",
                                style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Center(
                          child: Text(
                            "Chưa chọn tệp nào",
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        ),
                      ],
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            final check = await QualityControlFunction.pickImage(context);
                            debugPrint(check.toString());
                            if (check != null) {
                              bool checkPermission = await ImageFunction.handlePermission(check);
                              if (checkPermission == true) {
                                if (check == true) {
                                  List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                  );
                                  if (selectedImages.isEmpty) return;
                                  for (var i in selectedImages) {
                                    final itemImage = await ImageFunction.saveImageMulti(i.path);
                                    _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: itemImage));
                                  }
                                } else {
                                  final image = await ImagePicker().pickImage(
                                      maxWidth: globalImageConfig.maxWidth,
                                      maxHeight: globalImageConfig.maxHeight,
                                      imageQuality: globalImageConfig.imageQuality,
                                      source: ImageSource.camera);
                                  if (image == null) return;
                                  final imageProfile = await ImageFunction.saveImage(image.path);
                                  _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: imageProfile));
                                }
                              }
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: Colors.grey.shade100,
                            ),
                            child: Text(
                              "Chọn tệp",
                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Wrap(
                          spacing: 3.w,
                          runSpacing: 3.h,
                          children: List.generate(
                            _lsFileHinhAnhLoi[index].length,
                            (indexImageError) {
                              // String filenameError = basename(
                              //     lsGetFileError[index]
                              //             [indexImageError]
                              //         .path);
                              return SizedBox(
                                width: 50.w,
                                child: Stack(
                                  children: <Widget>[
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => ImageQuatity(lsImage: _lsFileHinhAnhLoi[index], index: indexImageError),
                                          ),
                                        );
                                      },
                                      child: ListImagePicker(fileImage: _lsFileHinhAnhLoi[index][indexImageError]),
                                    ),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: IconButton(
                                        highlightColor: Colors.transparent,
                                        hoverColor: Colors.transparent,
                                        constraints: const BoxConstraints(),
                                        iconSize: 17.sp,
                                        color: Colors.red.shade800,
                                        icon: const Icon(Icons.remove_circle),
                                        onPressed: () {
                                          _deleteListFileError(MultiDeleteImageErrorQuality(index: index, indexImageError: indexImageError));
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
            ),
            Visibility(
              visible: (_lsError[index].errorFileViewModel ?? []).isNotEmpty ? true : false,
              child: Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: () {
                    String title = "";
                    String error = _lsSelectedError[index] == null ? "" : _lsSelectedError[index]!.catalogTextVi.toString();
                    int indexErrorName = error.indexOf('|');
                    title = _lsSelectedError[index] == null
                        ? ""
                        : _lsSelectedError[index]!.catalogTextVi.toString().substring(indexErrorName + 2).toUpperCase();
                    // String error = lsSelectedError[index]!.catalogTextVi.toString();
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return DialogErrorQuality(title: 'HÌNH ẢNH LỖI ' + title, listImage: _lsError[index].errorFileViewModel ?? []);
                      },
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 5.h),
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    decoration: BoxDecoration(
                      color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.all(
                        Radius.circular(2.r),
                      ),
                    ),
                    child: Icon(
                      Icons.image,
                      color: Colors.white,
                      size: 15.sp,
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  IntrinsicHeight buidPhuongAnXuLy(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          customExpandedWidget(4, Text("Phương án xử lý", style: TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
              6,
              DropdownButtonHideUnderline(
                child: DropdownButton<DropdownItemList>(
                  isExpanded: true,
                  isDense: true,
                  itemHeight: null,
                  value: _lsSelectedPhuongAnXuLy[index] ?? QualityControlDetailFunction.defaultValuePhuongAnXuLy,
                  iconSize: 15.sp,
                  padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                  onChanged: canSubmit()
                      ? (value) {
                          _onPhuongAnXuLyChanged(value, index);
                        }
                      : null,
                  items: _lsPhuongAnXuLyMasterData.map((DropdownItemList method) {
                    return DropdownMenuItem<DropdownItemList>(
                        value: method,
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 5.h),
                          child: Text(
                            method.catalogTextVi.toString(),
                            style: TextStyle(color: Colors.black, fontSize: 10.sp),
                          ),
                        ));
                  }).toList(),
                  selectedItemBuilder: (BuildContext context) {
                    return _lsPhuongAnXuLyMasterData.map<Widget>((DropdownItemList method) {
                      return Text(
                        method.catalogTextVi.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 10.sp),
                        overflow: TextOverflow.ellipsis,
                      );
                    }).toList();
                  },
                ),
              )),
        ],
      ),
    );
  }

  Visibility buildDeleteButton(int index) {
    return Visibility(
      visible: _checkVisiButtonError[index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                _deleteItemListError(index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget itemHeaderText(int flexNum, String text) {
    return Expanded(
      flex: flexNum,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
        ),
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 2.w),
        child: Text(
          text,
          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  IntrinsicHeight itemHeaderThongTinKiemTra() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          itemHeaderText(1, "STT"),
          itemHeaderText(5, "Hạng mục kiểm tra"),
          itemHeaderText(3, "SL sản phẩm lỗi"),
          itemHeaderText(4, "Ghi chú"),
        ],
      ),
    );
  }

  List<DropdownItemList> _getCaNhanGayLoiMaster() {
    return _lsCaNhanGayLoiMasterData;
  }

  _checkCanClearLSX() {
    return _controllerLSXSAP.text.isNotEmpty && checkQualityControl() && _isTTFCode != true;
  }
}

class _ErrorView extends StatelessWidget {
  const _ErrorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)),
    );
  }
}

// class _ListNotFoundView extends StatelessWidget {
//   const _ListNotFoundView({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Center(child: Text("Không tìm thấy thông tin phiếu kiểm tra!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
//   }
// }

class _ListNotFoundView extends StatelessWidget {
  const _ListNotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text("Sản phẩm chưa xác nhận công đoạn đóng gói!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
  }
}
