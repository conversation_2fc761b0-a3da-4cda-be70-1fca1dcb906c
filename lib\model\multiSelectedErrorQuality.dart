import 'dart:io';

import 'package:ttf/model/qualityControlApi.dart';

class MultiSelectedErrorQuality{
  final int index;
  final ErrorList? value;

  MultiSelectedErrorQuality({required this.index, required this.value});
}
class MultiSelectImageErrorQuality{
  final File file;
  final int index;
  MultiSelectImageErrorQuality({required this.file, required this.index});
}
class MultiDeleteImageErrorQuality{
  final int indexImageError;
  final int index;
  MultiDeleteImageErrorQuality({required this.indexImageError, required this.index});
}
