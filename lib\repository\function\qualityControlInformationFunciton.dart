import 'dart:io';
import 'package:flutter/material.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/qualityControlApi.dart';

class QualityControlInfoFunction {
  static QualityControlInformation defaultValueQualityControlInformationQC() {
    return QualityControlInformation(
      checkedFileViewModel: [],
      file: [],
      workCenterName: " ",
      qualityControlInformationCode: 0,
      qualityControlInformationName: "--Vui lòng chọn--",
      qualityControlQCInformationId: " ",
      qualityControlId: " ",
      qualityControlInformationId: " ",
      workCenterCode: " ",
      notes: " ",
      outcomeStatus: "SKIP",
    );
  }

  // static QualityControlInformationIdList defaultValueQualityControlInformationIdList = QualityControlInformationIdList(
  //     id: " ", name: '--<PERSON><PERSON> lòng chọn--', workCenterCode: " ");
  static List<LsSendQualityControlInformation> getLsSendQualityControlInformation(
      List<QualityControlInformation>? lsQualityControlInformation,
      QualityControl? qualityControl,
      List<ThongTinKiemTra?> lsSelectedInfo,
      List<TextEditingController> lsGhiChuController,
      List<List<File>> lsGetFile) {
    List<LsSendQualityControlInformation> lsSendQualityControlInformation = [];
    LsSendQualityControlInformation? getInformation;

    for (int i = 0; i < lsQualityControlInformation!.length; i++) {
      if (qualityControl!.qualityControlInformation!.isNotEmpty) {
        getInformation = LsSendQualityControlInformation(
            lsQualityControlInformation[i].qualityControlQCInformationId == " "
                ? "null"
                : lsQualityControlInformation[i].qualityControlQCInformationId.toString(),
            lsSelectedInfo[i] != null
                ? lsSelectedInfo[i]!.id != " "
                    ? lsSelectedInfo[i]!.id.toString()
                    : "null"
                : lsQualityControlInformation[i].qualityControlInformationId.toString(),
            lsGhiChuController[i].text.isNotEmpty ? lsGhiChuController[i].text : lsQualityControlInformation[i].notes ?? "null",
            lsGetFile[i]);
        lsSendQualityControlInformation.add(getInformation);
      } else {
        getInformation = LsSendQualityControlInformation(
            "null",
            lsSelectedInfo[i] != null
                ? lsSelectedInfo[i]!.id != " "
                    ? lsSelectedInfo[i]!.id.toString()
                    : "null"
                : "null",
            lsGhiChuController[i].text.isEmpty ? "null" : lsGhiChuController[i].text,
            lsGetFile[i]);
        lsSendQualityControlInformation.add(getInformation);
      }
    }

    return lsSendQualityControlInformation;
  }

  static List<LsSendQualityControlInformation2> getLsSendQualityControlInformation2(
    List<QualityControlInformation>? lsThongTinKiemTra,
    QualityControl? qualityControl,
    List<ThongTinKiemTra?> lsSelectedInfo,
    List<TextEditingController> lsSoSanPhamLoiController,
    List<TextEditingController> lsGhiChuController,
    List<List<File>> lsGetFile,
  ) {
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation = [];
    LsSendQualityControlInformation2? getInformation;

    for (int i = 0; i < lsThongTinKiemTra!.length; i++) {
      // Set from saved
      if (qualityControl!.qualityControlInformation!.isNotEmpty) {
        getInformation = LsSendQualityControlInformation2(
            lsThongTinKiemTra[i].qualityControlQCInformationId == " " ? "null" : lsThongTinKiemTra[i].qualityControlQCInformationId.toString(),
            lsSelectedInfo[i] != null
                ? lsSelectedInfo[i]!.id != " "
                    ? lsSelectedInfo[i]!.id.toString()
                    : "null"
                : lsThongTinKiemTra[i].qualityControlInformationId.toString(),
            lsSoSanPhamLoiController[i].text.isNotEmpty ? lsSoSanPhamLoiController[i].text : lsThongTinKiemTra[i].soSanPhamLoi.toString() ?? "null",
            lsGhiChuController[i].text.isNotEmpty ? lsGhiChuController[i].text : lsThongTinKiemTra[i].notes ?? "null",
            lsThongTinKiemTra[i].outcomeStatus == " " ? "SKIP" : lsThongTinKiemTra[i].outcomeStatus ?? "SKIP",
            lsGetFile[i]);
        lsSendQualityControlInformation.add(getInformation);
      } else {
        // New
        getInformation = LsSendQualityControlInformation2(
            "null",
            lsSelectedInfo[i] != null
                ? lsSelectedInfo[i]!.id != " "
                    ? lsSelectedInfo[i]!.id.toString()
                    : "null"
                : "null",
            lsSoSanPhamLoiController[i].text.isEmpty ? "null" : lsSoSanPhamLoiController[i].text,
            lsGhiChuController[i].text.isEmpty ? "null" : lsGhiChuController[i].text,
            lsThongTinKiemTra[i].outcomeStatus == null ? "SKIP" : lsThongTinKiemTra[i].outcomeStatus ?? "SKIP",
            lsGetFile[i]);
        lsSendQualityControlInformation.add(getInformation);
      }
    }

    return lsSendQualityControlInformation;
  }
}
