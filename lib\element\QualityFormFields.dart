import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QualityFormField extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final bool enabled;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final bool required;
  final TextStyle? style;

  const QualityFormField({
    Key? key,
    required this.title,
    required this.controller,
    this.enabled = true,
    this.validator,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.required = true,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Text(
              title + (required ? ' *' : ''),
              style: TextStyle(fontSize: 11.sp),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(
              children: <Widget>[
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                  child: TextFormField(
                    enabled: enabled,
                    maxLines: maxLines,
                    textAlign: TextAlign.left,
                    controller: controller,
                    style: style ?? TextStyle(fontSize: 11.sp),
                    keyboardType: keyboardType,
                    inputFormatters: inputFormatters,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      filled: true,
                      fillColor: Colors.white,
                      hintStyle: TextStyle(fontSize: 11.sp),
                    ),
                    validator: validator ??
                        (value) {
                          if (required && (value == null || value.isEmpty)) {
                            return 'Vui lòng nhập ${title.toLowerCase()}';
                          }
                          return null;
                        },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class QualityDropdownField<T> extends StatelessWidget {
  final String title;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final bool enabled;
  final bool required;

  const QualityDropdownField({
    Key? key,
    required this.title,
    required this.value,
    required this.items,
    required this.onChanged,
    this.enabled = true,
    this.required = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Text(
              title + (required ? ' *' : ''),
              style: TextStyle(fontSize: 11.sp),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(3.r),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField<T>(
                  value: value,
                  isExpanded: true,
                  isDense: true,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: Colors.black,
                  ),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                    border: InputBorder.none,
                  ),
                  items: items,
                  onChanged: enabled ? onChanged : null,
                  validator: required
                      ? (value) {
                          if (value == null) {
                            return 'Vui lòng chọn ${title.toLowerCase()}';
                          }
                          return null;
                        }
                      : null,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
