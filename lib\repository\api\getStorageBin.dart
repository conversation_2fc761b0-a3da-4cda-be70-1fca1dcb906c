import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetStorageBinApi {
  static Future<http.Response> getStorageBinByParam(String storageBin, String token) async {
    Map<String, dynamic> data = {"StorageBin": storageBin};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // final url = Uri.https(baseUrl, "${UrlApi.baseUrlCommon}get-storage-bin", data);
    final fullUrl = "$baseUrl/${UrlApi.baseUrlCommon}get-storage-bin?StorageBin=${storageBin}";
    final url = Uri.parse(fullUrl);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
