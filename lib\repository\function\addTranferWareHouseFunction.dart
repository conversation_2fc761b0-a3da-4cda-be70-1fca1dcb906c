import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Widget/dialogWidget/DialogCompleteSendRequest.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/getListSOWBSByBatchTranferWarehouse.dart';
import '../../model/warehouseTranferNoReservation.dart';
import '../api/addTranferWareHouseApi.dart';

class AddTranferWareHouseFunction {
  static bool checkIsSend = false;
  static List<WarehouseTranferDetails> getListWarehouseTranferDetails(
      List<TextEditingController> lsQuantity, List<DataGetListSOWBSByBatchTranferWareHouse> lsDataGetListSOWBSByBatchTranferWareHouse) {
    List<WarehouseTranferDetails> lsWareHouseTranferDetails = [];
    for (int i = 0; i < lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      lsWareHouseTranferDetails.add(WarehouseTranferDetails(
          so: lsDataGetListSOWBSByBatchTranferWareHouse[i].so == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].so,
          soLine: lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine,
          wbs: lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs,
          quantity: lsQuantity[i].text.isEmpty ? null : double.parse(lsQuantity[i].text),
          unit: lsDataGetListSOWBSByBatchTranferWareHouse[i].unit,
          batchNumber: lsDataGetListSOWBSByBatchTranferWareHouse[i].batchNumber));
    }
    lsWareHouseTranferDetails.removeWhere((element) => element.quantity == null);
    return lsWareHouseTranferDetails;
  }
  // static Future<WarehouseTranferNoReservationMessage?> getWarehouseTranferNoReservation(
  //     WarehouseTranferNoReservation warehouseTranferNoReservation,
  //     String token) async {
  //   final response = await AddTranferWareHouseApi.postWarehouseTranferNoReservation(warehouseTranferNoReservation, token);
  //   if (response.statusCode == 200) {
  //     final getMessage = WarehouseTranferNoReservationMessage.fromJson(jsonDecode(response.body));
  //       return getMessage;
  //   } else {
  //     return null;
  //   }
  // }

  static Future<void> sendWarehouseTranferNoReservation(
      WarehouseTranferNoReservation warehouseTranferNoReservation, String token, BuildContext context, String plant) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await AddTranferWareHouseApi.postWarehouseTranferNoReservation(warehouseTranferNoReservation, token);
      // final message = await getWarehouseTranferNoReservation(warehouseTranferNoReservation, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final message = WarehouseTranferNoReservationMessage.fromJson(jsonDecode(response.body));
        if (message.code == 200 && message.isSuccess == true) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogCompleteSendRequest(message: message.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       message.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
          // Future.delayed(const Duration(seconds: 0), () {
          //   Navigator.pop(context,true);
          // });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: message.message.toString()));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }
}
