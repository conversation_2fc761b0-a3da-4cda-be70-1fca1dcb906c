import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

import '../element/TitleQuality.dart';
import '../repository/function/qualityControlDetailFunction.dart';

class LSXSAPSuggestionField extends StatefulWidget {
  final TextEditingController controller;
  final String token;
  final bool enabled;
  final Function(String) onSuggestionSelected;
  final VoidCallback? onClear;
  final bool showClearButton;
  final String? hintText;
  final String? title;
  final bool isRequired;
  final ValueChanged<String>? onChanged;

  const LSXSAPSuggestionField({
    Key? key,
    required this.controller,
    required this.token,
    required this.onSuggestionSelected,
    this.enabled = true,
    this.onClear,
    this.showClearButton = true,
    this.hintText,
    this.title = "LSX SAP:",
    this.isRequired = false,
    this.onChanged,
  }) : super(key: key);

  @override
  State<LSXSAPSuggestionField> createState() => _LSXSAPSuggestionFieldState();
}

class _LSXSAPSuggestionFieldState extends State<LSXSAPSuggestionField> {
  bool _isLoading = false;
  bool _isSelected = false;

  Future<List<String>> _filterLSXSAP(String query) async {
    List<String> lsLSXSAP = [];

    if (query.isEmpty || query.length < 3) {
      return lsLSXSAP;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await QualityControlDetailFunction.fetchLSXSAP(query, widget.token);
      lsLSXSAP = response ?? [];
    } catch (e) {
      debugPrint('Error fetching LSX SAP: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }

    return lsLSXSAP;
  }

  bool _checkCanClearLSX() {
    return widget.showClearButton && widget.controller.text.isNotEmpty && widget.enabled;
  }

  void _handleClear() {
    setState(() {
      widget.controller.text = "";
      _isSelected = false;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!("");
    }
  }

  void _handleSuggestionSelected(String suggestion) {
    if (widget.controller.text == suggestion && _isSelected) {
      return;
    }

    setState(() {
      widget.controller.text = suggestion;
      _isSelected = true;
    });

    widget.onSuggestionSelected(suggestion);
  }

  void _handleTextChanged(String value) {
    setState(() {
      widget.controller.text = value;
      widget.controller.selection = TextSelection.collapsed(
        offset: widget.controller.text.length,
      );
      _isSelected = false;
    });

    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        if (widget.title != null) ...[
          Expanded(
            flex: 3,
            child: QualityTitleField(
              title: widget.title! + (widget.isRequired ? " *" : ""),
            ),
          ),
          SizedBox(width: 10.w),
        ],
        Expanded(
          flex: widget.title != null ? 7 : 10,
          child: Column(
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: Stack(
                  children: [
                    TypeAheadField(
                      minCharsForSuggestions: 3,
                      suggestionsBoxDecoration: SuggestionsBoxDecoration(
                        constraints: BoxConstraints(
                          minHeight: 200.h,
                          maxHeight: 200.h,
                        ),
                      ),
                      textFieldConfiguration: TextFieldConfiguration(
                        enabled: widget.enabled,
                        decoration: InputDecoration(
                          labelStyle: TextStyle(fontSize: 11.sp),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 3.w,
                            vertical: 7.h,
                          ),
                          isDense: true,
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          hintText: widget.hintText ?? "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
                          hintStyle: TextStyle(
                            fontSize: 11.sp,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                        style: TextStyle(fontSize: 12.sp),
                        controller: widget.controller,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onChanged: _handleTextChanged,
                      ),
                      suggestionsCallback: (pattern) => _filterLSXSAP(pattern),
                      itemBuilder: (context, suggestion) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 8.h,
                            horizontal: 4.w,
                          ),
                          child: Text(
                            suggestion,
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        );
                      },
                      onSuggestionSelected: _handleSuggestionSelected,
                      noItemsFoundBuilder: (value) {
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 10.h,
                            horizontal: 5.w,
                          ),
                          child: Text(
                            "Không tìm thấy LSX SAP",
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        );
                      },
                      loadingBuilder: (context) {
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 10.h,
                            horizontal: 5.w,
                          ),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 16.w,
                                height: 16.w,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.blue,
                                  ),
                                ),
                              ),
                              SizedBox(width: 8.w),
                              Text(
                                "Đang tải...",
                                style: TextStyle(fontSize: 11.sp),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    // Clear button (X)
                    Visibility(
                      visible: _checkCanClearLSX(),
                      child: Positioned(
                        top: 1,
                        right: 5,
                        child: InkWell(
                          onTap: _handleClear,
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Icon(
                              Icons.close,
                              size: 20.sp,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
