﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MemberOfTargetGroupModel", Schema = "Marketing")]
    public partial class MemberOfTargetGroupModel
    {
        [Key]
        public Guid TargetGroupId { get; set; }
        [Key]
        public Guid ProfileId { get; set; }

        [ForeignKey("TargetGroupId")]
        [InverseProperty("MemberOfTargetGroupModel")]
        public virtual TargetGroupModel TargetGroup { get; set; }
    }
}