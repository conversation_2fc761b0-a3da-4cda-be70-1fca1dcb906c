import 'package:ttf/model/qualityControlApi.dart';
import 'package:ttf/model/userModel.dart';

class SaveAccountLogin {
  String? userName;
  String? password;
  String? company;
  String? saleOrg;
  GetListCompanyByUserName? selectedCompany;
  DropdownItemList? selectedEnvironment;
  List<String>? routingEnabledCompanyCodes;
  SaveAccountLogin({
    this.userName,
    this.password,
    this.company,
    this.saleOrg,
    this.selectedCompany,
    this.selectedEnvironment,
    this.routingEnabledCompanyCodes,
  });

  SaveAccountLogin.fromJson(Map<String, dynamic> json) {
    userName = json['userName'];
    password = json['password'];
    company = json['company'];
    saleOrg = json['saleOrg'];
    selectedCompany = json['selectedCompany'] != null ? GetListCompanyByUserName.fromJson(json['selectedCompany']) : null;
    selectedEnvironment = json['selectedEnvironment'] != null ? DropdownItemList.fromJson(json['selectedEnvironment']) : null;
    routingEnabledCompanyCodes = json['routingEnabledCompanyCodes'] != null ? List<String>.from(json['routingEnabledCompanyCodes']) : null;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userName'] = userName;
    data['password'] = password;
    data['company'] = company;
    data['saleOrg'] = saleOrg;
    data['selectedCompany'] = selectedCompany;
    data['selectedEnvironment'] = selectedEnvironment;
    data['routingEnabledCompanyCodes'] = routingEnabledCompanyCodes;
    return data;
  }
}
