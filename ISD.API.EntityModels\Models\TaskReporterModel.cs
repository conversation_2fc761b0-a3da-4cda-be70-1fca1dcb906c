﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskReporterModel", Schema = "Task")]
    public partial class TaskReporterModel
    {
        [Key]
        public Guid TaskReporterId { get; set; }
        public Guid? TaskId { get; set; }
        [StringLength(50)]
        public string SalesEmployeeCode { get; set; }
        [StringLength(10)]
        public string TaskAssignTypeCode { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [StringLength(50)]
        public string RolesCode { get; set; }
    }
}