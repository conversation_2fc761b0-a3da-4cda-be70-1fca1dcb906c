# Quality Control Module

The Quality Control (QC) module in TTF MES Mobile provides comprehensive tools for quality inspection and management throughout the manufacturing process. From incoming material inspection to final product quality checks, this module ensures that quality standards are maintained at every stage.

## Module Structure

The Quality Control module is located primarily in `lib/page/KiemTraChatLuong/` and consists of the following components:

### Main Entry Points

- `KiemTraChatLuong.dart`: Main entry point for quality control features
- `QCPassedStampScan.dart`: QR code scanning for quality passed stamps

### Quality Reports

- `BaoCaoDauVao.dart` and `BaoCaoDauVao2.dart`: Input inspection reports
- `BaoCaoDauVaoDetail.dart` and `BaoCaoDauVaoDetail2.dart`: Detailed input inspection reports
- `BaoCaoQAQCNghiemThu.dart`: Quality acceptance reports
- `BaoCaoQAQCNghiemThuDetail.dart`: Detailed quality acceptance reports
- `BaoCaoQC5S.dart`: 5S quality inspection reports
- `BaoCaoQC5SDetail.dart`: Detailed 5S inspection reports
- `BaoCaoQCGiaCong.dart`: Processing quality reports
- `BaoCaoQCGiaCongDetail.dart`: Detailed processing quality reports
- `BaoCaoQCHienTruong.dart`: On-site quality reports
- `BaoCaoQCHienTruongDetail.dart`: Detailed on-site quality reports
- `BaoCaoQCMau.dart`: Sample quality reports
- `BaoCaoQCMauDetail.dart`: Detailed sample quality reports
- `BaoCaoQCSanPham.dart`: Product quality reports
- `BaoCaoQCSanPhamDetail.dart`: Detailed product quality reports

### Quality Inspection Sheets

- `PhieuKCSCongDoan.dart`: Quality inspection sheets for process stages
- `PhieuKCSCongDoanDetail.dart`: Detailed quality inspection sheets

### Supporting Screens

- `ListQC.dart`: List of quality control tasks
- `ListQCNVL.dart`: List of material quality control tasks
- `QRCodePageListQC.dart` and `QRCodePageListQC2.dart`: QR code scanning for quality control lists

## Features

### Material Input Quality Control

The input quality control feature allows users to:

- Inspect incoming materials for quality issues
- Record inspection results with photos
- Approve or reject incoming materials
- Generate quality reports for traceability

### Manufacturing Process Quality Control

During the manufacturing process, users can:

- Perform quality checks at each production stage
- Record measurements and observations
- Capture photos of quality issues
- Issue non-conformance reports
- Track quality trends by process

### Final Product Quality Inspection

For finished products, the module enables:

- Final quality inspection against specifications
- Documentation of inspection results
- Approval or rejection of finished products
- QC stamp application for approved products

### 5S Quality Audits

The 5S quality audit feature allows:

- Workplace organization audits (Sort, Set in order, Shine, Standardize, Sustain)
- Scoring of 5S compliance
- Photo documentation of workplace conditions
- Improvement tracking over time

### Quality Issue Tracking

For any quality issues, the system provides:

- Issue logging with classification
- Root cause analysis documentation
- Corrective action tracking
- Resolution verification

## Workflows

### Input Material Inspection

1. Access the Input Inspection list (`BaoCaoDauVao.dart`)
2. Select material for inspection
3. Record inspection parameters in the detail view (`BaoCaoDauVaoDetail.dart`)
4. Take photos of any issues
5. Submit inspection results
6. Generate inspection report

### Process Quality Check

1. Access the Process Quality Control list (`BaoCaoQCGiaCong.dart`)
2. Select the production order to inspect
3. Record quality measurements
4. Document any issues with photos
5. Determine pass/fail status
6. Submit quality report

### Final Product Inspection

1. Access the Product Quality Report list (`BaoCaoQCSanPham.dart`)
2. Select product for final inspection
3. Complete all inspection points
4. Record measurements and observations
5. Take photos as needed
6. Determine overall pass/fail status
7. For passed products, generate QC passed stamp

## Data Models

The quality control module uses the following data models:

- **QCInputReport**: Input quality inspection data
- **QCProcessReport**: Process quality inspection data
- **QCFinalReport**: Final product quality data
- **QC5SReport**: 5S audit data
- **QCIssue**: Quality issue tracking data

## API Integration

Quality control data is synchronized with backend systems through the following API endpoints:

- `/api/qc/input`: Input quality inspection
- `/api/qc/process`: Process quality control
- `/api/qc/final`: Final product quality
- `/api/qc/5s`: 5S audit data
- `/api/qc/issues`: Quality issues tracking

## User Interfaces

### List Views
Quality control list views display:
- Item identifier
- Inspection status
- Due date/time
- Assigned inspector
- Priority level

### Detail Views
Quality control detail views include:
- Inspection parameters with acceptable ranges
- Actual measurements input fields
- Pass/fail indicators
- Photo attachment capabilities
- Notes and comments
- Signature fields for approval

### Report Views
Quality reports show:
- Summary statistics
- Pass/fail rates
- Trend analysis
- Issue categorization
- Action item tracking

## Integration with Other Modules

The Quality Control module integrates with other modules in the following ways:

- **Inventory Management**: Coordinates with inventory control for material disposition based on quality results
- **Production Management**: Links quality data to production orders and processes
- **Maintenance Management**: Triggers maintenance requests for quality issues related to equipment problems
- **Warehouse Management**: Controls material flow based on quality status

## Best Practices

For effective use of the Quality Control module:

1. Always complete all required inspection points
2. Document issues with clear photos
3. Include detailed notes for any failures
4. Report critical quality issues immediately
5. Review quality trends regularly
6. Use barcode/QR scanning for accurate identification
7. Ensure proper sign-off on all quality reports

## Future Enhancements

Planned improvements for the Quality Control module include:

1. Advanced statistical process control (SPC) charts
2. Machine learning for predictive quality analysis
3. Integration with IoT quality measurement devices
4. Enhanced photo annotation capabilities
5. Augmented reality assisted inspections 