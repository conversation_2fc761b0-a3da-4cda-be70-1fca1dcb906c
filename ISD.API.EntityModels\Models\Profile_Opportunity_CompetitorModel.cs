﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Profile_Opportunity_CompetitorModel", Schema = "Customer")]
    public partial class Profile_Opportunity_CompetitorModel
    {
        [Key]
        public Guid OpportunityCompetitorId { get; set; }
        public Guid? ProfileId { get; set; }
        public Guid? CompetitorId { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public bool? IsMain { get; set; }
        /// <summary>
        /// Trúng thầu
        /// </summary>
        public bool? SuccessfulBidder { get; set; }

        [ForeignKey("CompetitorId")]
        [InverseProperty("Profile_Opportunity_CompetitorModelCompetitor")]
        public virtual ProfileModel Competitor { get; set; }
        [ForeignKey("ProfileId")]
        [InverseProperty("Profile_Opportunity_CompetitorModelProfile")]
        public virtual ProfileModel Profile { get; set; }
    }
}