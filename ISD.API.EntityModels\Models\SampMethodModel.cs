﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SampMethodModel", Schema = "tMasterData")]
    public partial class SampMethodModel
    {
        [Key]
        public Guid SampMethodId { get; set; }
        [StringLength(50)]
        public string SampleSize { get; set; }
        public int? SampleSizeFrom { get; set; }
        public int? SampleSizeTo { get; set; }
        [StringLength(50)]
        public string SampleName { get; set; }
        public int? SampleQuantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        public bool? Actived { get; set; }
        public int? Aql025 { get; set; }
        public int? Aql040 { get; set; }
        public int? Aql065 { get; set; }
        public int? Aql100 { get; set; }
        public int? Aql150 { get; set; }
        public int? Aql250 { get; set; }
        public int? Aql400 { get; set; }
        public int? Aql650 { get; set; }
        public int? Aql1000 { get; set; }
        public int? Aql1500 { get; set; }
    }
}