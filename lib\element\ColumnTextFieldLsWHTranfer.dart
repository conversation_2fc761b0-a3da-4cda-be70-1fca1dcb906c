import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ColumnTextFieldLsWHTranfer extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final FocusNode focusNode;

  const ColumnTextFieldLsWHTranfer({Key? key, required this.title, required this.controller, required this.focusNode}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
          decoration: BoxDecoration(border: Border.all(width: 0.5.w, color: Colors.grey.shade500), borderRadius: BorderRadius.circular(3.r)),
          child: TextForm<PERSON>ield(
            maxLines: 1,
            textAlign: TextAlign.center,
            controller: controller,
            focusNode: focusNode,
            style: TextStyle(fontSize: 12.sp),
            decoration: InputDecoration(
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              border: InputBorder.none,
              // isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              filled: true,
              isDense: true,
              fillColor: Colors.white,
              hintStyle: TextStyle(fontSize: 12.sp),
            ),
          ),
        ),
      ],
    );
  }
}

class ColumnTextFieldLsWHTranferOnchange extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final FocusNode focusNode;
  final ValueChanged onChange;
  final bool isLoading;
  const ColumnTextFieldLsWHTranferOnchange(
      {Key? key, required this.title, required this.controller, required this.onChange, required this.focusNode, required this.isLoading})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
          decoration: BoxDecoration(border: Border.all(width: 0.5.w, color: Colors.grey.shade500), borderRadius: BorderRadius.circular(3.r)),
          child: TextFormField(
            maxLines: null,
            enabled: isLoading == true ? false : true,
            focusNode: focusNode,
            textAlign: TextAlign.left,
            controller: controller,
            style: TextStyle(fontSize: 12.sp),
            decoration: InputDecoration(
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              border: InputBorder.none,
              // isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              filled: true,
              isDense: true,
              fillColor: Colors.white,
              hintStyle: TextStyle(fontSize: 12.sp),
            ),
            onChanged: onChange,
          ),
        ),
      ],
    );
  }
}
