import 'getInventoryBySOWBS.dart';

class MaterialUsedShift {
  String? batchNumber;
  String? slocId;
  String? storageBinId;
  String? rawMaterialCardId;
  double? quantity;
  String? unit;
  String? stepCode;
  List<SowbSs>? sowbsQuantities;

  MaterialUsedShift(
      {this.batchNumber,
        this.slocId,
        this.storageBinId,
        this.rawMaterialCardId,
        this.quantity,
        this.unit,
        this.stepCode,
        this.sowbsQuantities});

  MaterialUsedShift.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
    slocId = json['slocId'];
    storageBinId = json['storageBinId'];
    rawMaterialCardId = json['rawMaterialCardId'];
    quantity = json['quantity'];
    unit = json['unit'];
    stepCode = json['stepCode'];
    if (json['sowbsQuantities'] != null) {
      sowbsQuantities = <SowbSs>[];
      json['sowbsQuantities'].forEach((v) {
        sowbsQuantities!.add(SowbSs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    data['slocId'] = slocId;
    data['storageBinId'] = storageBinId;
    data['rawMaterialCardId'] = rawMaterialCardId;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['stepCode'] = stepCode;
    if (sowbsQuantities != null) {
      data['sowbsQuantities'] =
          sowbsQuantities!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
class MessageMaterialUsedShift {
  int? code;
  bool? isSuccess;
  String? message;
  bool? data;


  MessageMaterialUsedShift(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  MessageMaterialUsedShift.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'];

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = this.data;
    return data;
  }
}
// class SowbsQuantities {
//   String? so;
//   String? soLine;
//   String? wbs;
//
//   SowbsQuantities({this.so, this.soLine, this.wbs});
//
//   SowbsQuantities.fromJson(Map<String, dynamic> json) {
//     so = json['so'];
//     soLine = json['soLine'];
//     wbs = json['wbs'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['so'] = so;
//     data['soLine'] = soLine;
//     data['wbs'] = wbs;
//     return data;
//   }
// }