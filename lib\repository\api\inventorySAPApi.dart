import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/getInventorySAP.dart';
import '../../urlApi/urlApi.dart';

class InventorySAPApi {
  static Future<http.Response> postInventorySap(PostInventorySAP postInventorySAP, String token) async {
    final dataPost = jsonEncode(postInventorySAP);
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "GetInventorySAP");
    if (kDebugMode) {
      print(url);
      print(dataPost);
    }
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
