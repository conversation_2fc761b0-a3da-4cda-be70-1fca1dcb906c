﻿using ISD.API.EntityModels.Data;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ISD.API.Core
{
    /// <summary>
    /// Base controller for API controllers
    /// - Accesses current user
    /// - Manages EntityDataContext
    /// - Handles unit of work operations
    /// </summary>
    public class ControllerBaseAPI : ControllerBase
    {
        protected readonly EntityDataContext _context;
        protected readonly UnitOfWork _unitOfWork;
        private static IHttpContextAccessor httpContextAccessor;

        public ControllerBaseAPI()
        {
            _context = new EntityDataContext();
            _unitOfWork = new UnitOfWork(_context);
        }

        public static void SetHttpContextAccessor(IHttpContextAccessor accessor)
        {
            httpContextAccessor = accessor;
        }

        public AppUserPrincipal CurrentUser
        {
            get
            {
                return new AppUserPrincipal(this.User as ClaimsPrincipal);
            }
        }
    }
}
