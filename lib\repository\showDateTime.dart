import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../Widget/modalBottomSheet/dateTimePickerIOS.dart';
import '../Widget/modalBottomSheet/datePickerIOS.dart';
import '../Widget/modalBottomSheet/timePickerIOS.dart';

class ShowDateTime{
  static Future<DateTime?> showDatePickerIOSToComplete(BuildContext context) async {
    final dataTime = await showCupertinoModalPopup<DateTime>(
        context: context,
        builder: (_) => const DatePickerIOS()
    );
    return dataTime;
  }
  static Future<DateTime?> showDatePickerIOSToCompleteDateTime(BuildContext context) async {
    final dataTime = await showCupertinoModalPopup<DateTime>(
        context: context,
        builder: (_) => const DateTimePickerIOS()
    );
    return dataTime;
  }
  static Future<DateTime?> showDatePickerLsWareHouseIOS(BuildContext context, DateTime dateRequest) async {
    final dataTime = await showCupertinoModalPopup<DateTime>(
        context: context,
        builder: (_) =>  DatePickerLsWareHouseHIOS(dateRequest: dateRequest)
    );
    return dataTime;
  }
  static Future<DateTime?> pickDateAndroid(BuildContext context, DateTime initialDate, DateTime? date) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: date ?? initialDate,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 50),
    );
    return newDate;
  }
  static Future<TimeOfDay?> showTime(BuildContext context,TimeOfDay initialTime,TimeOfDay? _timeTo) async{
    TimeOfDay? newTime = await showTimePicker(
    context: context,
    initialTime: _timeTo ?? initialTime,
  );
    return newTime;
  }
  static Future<DateTime?> showTimeIOS(BuildContext context,DateTime midnight) async {
    final dataTime = await showCupertinoModalPopup<DateTime>(
        context: context,
        builder: (_) => TimePickerIOS(dateTimeMidNight: midnight)
    );
    return dataTime;
  }


}