*&---------------------------------------------------------------------*
*& Include          ZPG_GANTTCHART_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& CLASS
*&---------------------------------------------------------------------*
CLASS clss_uu_tien DEFINITION.
  PUBLIC SECTION.
    CLASS-METHODS : get_data_uutien EXPORTING it_uutien TYPE gty_uutien
                                              error     TYPE char1.

ENDCLASS.
CLASS clss_uu_tien IMPLEMENTATION.
  METHOD get_data_uutien.

    DATA: lo_http_client TYPE REF TO if_http_client,
          lo_rest_client TYPE REF TO cl_rest_http_client,
          lv_url         TYPE        string,
          http_status    TYPE        string,
          lv_body        TYPE        string.

    cl_http_client=>create_by_destination(
     EXPORTING
       destination              = 'ZMES_API'    " Logical destination (specified in function call)
     IMPORTING
       client                   = lo_http_client    " HTTP Client Abstraction
     EXCEPTIONS
       argument_not_found       = 1
       destination_not_found    = 2
       destination_no_authority = 3
       plugin_not_active        = 4
       internal_error           = 5
       OTHERS                   = 6
    ).

    CREATE OBJECT lo_rest_client
      EXPORTING
        io_http_client = lo_http_client.

    lo_http_client->request->set_version( if_http_request=>co_protocol_version_1_0 ).

    IF lo_http_client IS BOUND AND lo_rest_client IS BOUND.
      lv_url = 'GetDSXByPriority'.
      cl_http_utility=>set_request_uri(
        EXPORTING
          request = lo_http_client->request    " HTTP Framework (iHTTP) HTTP Request
          uri     = lv_url                     " URI String (in the Form of /path?query-string)
      ).
    ENDIF.

    DATA: lo_json        TYPE REF TO cl_clb_parse_json,
          lo_response    TYPE REF TO if_rest_entity,
          lo_request     TYPE REF TO if_rest_entity,
          lo_sql         TYPE REF TO cx_sy_open_sql_db,
          status         TYPE  string,
          reason         TYPE  string,
          response       TYPE  string,
          content_length TYPE  string,
          location       TYPE  string,
          content_type   TYPE  string,
          lv_status      TYPE  i.

* Set Payload or body ( JSON or XML)
    lo_request = lo_rest_client->if_rest_client~create_request_entity( ).
    lo_request->set_content_type( iv_media_type = if_rest_media_type=>gc_appl_json ).
    lo_request->set_string_data( lv_body ).
*  call method lo_rest_client->if_rest_client~set_request_header
*    exporting
*      iv_name  = 'auth-token'
*      iv_value = token number. "Set your header .
* POST
    lo_rest_client->if_rest_resource~post( lo_request ).
* Collect response
    lo_response = lo_rest_client->if_rest_client~get_response_entity( ).
    http_status = lv_status = lo_response->get_header_field( '~status_code' ).
    reason = lo_response->get_header_field( '~status_reason' ).
    content_length = lo_response->get_header_field( 'content-length' ).
    location = lo_response->get_header_field( 'location' ).
    content_type = lo_response->get_header_field( 'content-type' ).
    response = lo_response->get_string_data( ).
* JSON to ABAP
    DATA lr_json_deserializer TYPE REF TO cl_trex_json_deserializer.
    CREATE OBJECT lr_json_deserializer.

    /ui2/cl_json=>deserialize( EXPORTING json = response  pretty_name = /ui2/cl_json=>pretty_mode-camel_case  CHANGING data = it_uutien ).

    SORT it_uutien BY lsxsap ASCENDING thutuuutien ASCENDING.
  ENDMETHOD.
ENDCLASS.
*&---------------------------------------------------------------------*
*& Form INIT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM init .
*  gr_werks    = VALUE #( sign = 'I' option = 'BT' low = '1000' high = '3000').

  gr_werkn[] = VALUE #( ( sign = 'I' option = 'EQ' low = '1000')
                        ( sign = 'I' option = 'EQ' low = '1100')
                        ( sign = 'I' option = 'EQ' low = '1200')
                        ( sign = 'I' option = 'EQ' low = '1300')
                        ( sign = 'I' option = 'EQ' low = '2000')
                        ( sign = 'I' option = 'EQ' low = '2100')
                        ( sign = 'I' option = 'EQ' low = '3000')
                         ).

*  s_gltri-sign = 'I'.
*  s_gltri-option = 'BT'.
*  s_gltri-low = sy-datum - 7.
*  s_gltri-high = sy-datum.
*  APPEND s_gltri.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form MAIN_PROC
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM main_proc .
  DATA: lv_view TYPE dd02v-tabname.

* select data from db
  PERFORM get_data.

* process data from db
  PERFORM process_data.

  PERFORM display_data.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM get_data .
  DATA : lr_data TYPE REF TO data,
         lr_kkbc TYPE REF TO data.
  FIELD-SYMBOLS  : <lt_data> TYPE ANY TABLE,
                   <lw_data> TYPE any,
                   <lt_kkbc> TYPE ANY TABLE,
                   <lw_kkbc> TYPE any.
  DATA: lr_systl TYPE rseloption WITH HEADER LINE.
  DATA: lr_sysh1 TYPE rseloption WITH HEADER LINE.
  DATA: ls_data  TYPE zst_ppr02_1,
        ls_data2 TYPE zst_ppr02_2.
*  DATA: ls_kkbc LIKE LINE OF gt_kkbc.
  DATA: ls_matnr LIKE LINE OF gt_matnr.
  DATA: ls_cbom LIKE LINE OF gt_cbom,
        ls_bom  LIKE LINE OF gt_bom.

  DATA lr_uutien TYPE REF TO clss_uu_tien.
  DATA: lv_error TYPE char1.

  lr_systl[] = VALUE #( ( sign = 'I' option = 'BT' low = 'TECO' )
                        ( sign = 'I' option = 'BT' low = 'DVL' )
                        ( sign = 'I' option = 'BT' low = 'CLSD' ) ).

  lr_sysh1[] = VALUE #( ( sign = 'I' option = 'BT' low = 'I0045' )
                        ( sign = 'I' option = 'BT' low = 'I0046' ) ).



  "Lấy dữ liệu ưu tiên
  CREATE OBJECT lr_uutien.
  CALL METHOD lr_uutien->get_data_uutien(
    IMPORTING
      it_uutien = gt_uutien
      error     = lv_error ).

  IF NOT s_zzlsx IS INITIAL.
    SELECT aufnr zzlsx gmein
      FROM afko
      INTO TABLE gt_afko
      WHERE zzlsx IN s_zzlsx.

    IF NOT gt_afko IS INITIAL.
      s_aufnr-sign = 'I'.
      s_aufnr-option = 'EQ'.
      LOOP AT gt_afko INTO DATA(ls_afko).
        s_aufnr-low = ls_afko-aufnr.
        APPEND s_aufnr.
      ENDLOOP.
    ENDIF.
  ELSE.
    IF NOT p_ut IS INITIAL.
      s_aufnr-sign = 'I'.
      s_aufnr-option = 'EQ'.
      LOOP AT gt_uutien INTO DATA(ls_uutien).
        s_aufnr-low = ls_uutien-lsxsap.
        APPEND s_aufnr.
      ENDLOOP.
    ENDIF.
  ENDIF.

*  LOOP AT lr_sysh1.
  "Submit data FAGLL03
  "Setting up runtime info
  cl_salv_bs_runtime_info=>set(    EXPORTING display  = abap_false
                                             metadata = abap_false
                                             data     = abap_true ).

  SUBMIT ppio_entry
    WITH ppio_entry_sc100-ppio_listtyp = 'PPIOH000'
*      WITH ppio_entry_sc1100-alv_variant = '/TRANG'
    WITH yx EQ 'X'
    WITH s_werks IN s_werks
    WITH s_aufnr IN s_aufnr
*      WITH s_auart IN s_auart
    WITH s_kdauf IN s_vbeln
    WITH s_matnr IN s_matnr
    WITH s_isten IN s_gltri
*      WITH p_syst1 EQ lr_systl-low
*      WITH p_sysh1 EQ lr_sysh1-low
    AND RETURN." EXPORTING LIST TO MEMORY.

*& Read the data from other program to here
  TRY.
      cl_salv_bs_runtime_info=>get_data_ref( IMPORTING r_data = lr_data ).
      ASSIGN lr_data->* TO <lt_data>.
    CATCH cx_salv_bs_sc_runtime_info.
      MESSAGE `Unable to retrieve ALV data` TYPE 'E'.
  ENDTRY.
  cl_salv_bs_runtime_info=>clear_all( ).

  IF <lt_data> IS ASSIGNED AND NOT <lt_data> IS INITIAL.
    gr_matnr-sign = gr_vbeln-sign = gr_aufnr-sign = gr_werks-sign = 'I'.
    gr_matnr-option = gr_vbeln-option = gr_aufnr-option = gr_werks-option = 'EQ'.
    LOOP AT <lt_data> ASSIGNING <lw_data>.
      MOVE-CORRESPONDING <lw_data> TO ls_data.

      CHECK ls_data-zzlsx IN s_zzlsx.
      IF NOT ls_data-matnr IS INITIAL.
        ls_matnr-matnr = gr_matnr-low = ls_data-matnr.
        APPEND gr_matnr.

        ls_matnr-werks = ls_data-werks.
        ls_matnr-verid = ls_data-verid.
        APPEND ls_matnr TO gt_matnr.
      ENDIF.

      IF NOT ls_data-zzvbeln IS INITIAL.
        gr_vbeln-low = ls_data-zzvbeln.
        APPEND gr_vbeln.
      ENDIF.

      IF NOT ls_data-aufnr IS INITIAL.
        gr_aufnr-low = ls_data-aufnr.
        APPEND gr_aufnr.

        gt_aufnr-zzlsx    = ls_data-zzlsx.
        gt_aufnr-aufnr    = ls_data-aufnr.
        gt_aufnr-zzpspnr  = ls_data-zzpspnr.
        IF gt_aufnr-zzpspnr IS INITIAL.
          gt_aufnr-zzpspnr = ls_data-projn.
        ENDIF.
        APPEND gt_aufnr.
      ENDIF.

      IF NOT ls_data-werks IS INITIAL.
        gr_werks-low = ls_data-werks.

        APPEND gr_werks.
      ENDIF.

      IF NOT p_ct IS INITIAL.
        APPEND ls_data TO gt_data.
      ELSE.
        MOVE-CORRESPONDING <lw_data> TO ls_data2.
        APPEND ls_data2 TO gt_data2.
      ENDIF.
    ENDLOOP.
    DELETE gt_data2 WHERE zzlsx IS INITIAL.
  ENDIF.

  CLEAR: lr_data.
  UNASSIGN: <lt_data>, <lw_data>.
*  ENDLOOP.

  IF gt_data IS INITIAL AND gt_data2 IS INITIAL.
    IF p_sb IS INITIAL.
      MESSAGE 'Not data found. Please try with other parameter. Thank you!' TYPE 'I' DISPLAY LIKE 'E'.
    ENDIF.
    LEAVE LIST-PROCESSING.
  ENDIF.

  SORT gt_data BY aufnr matnr werks ASCENDING.
  DELETE ADJACENT DUPLICATES FROM gt_data COMPARING ALL FIELDS.

  SORT gt_data2 BY zzlsx aufnr matnr werks ASCENDING.
  DELETE ADJACENT DUPLICATES FROM gt_data COMPARING ALL FIELDS.

  SORT gr_aufnr[] BY low ASCENDING.
  DELETE gr_aufnr WHERE low IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gr_aufnr[] COMPARING low.

  SORT gt_aufnr[] BY zzlsx aufnr ASCENDING.
  DELETE gt_aufnr WHERE aufnr IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gt_aufnr[] COMPARING ALL FIELDS.

  SORT gr_vbeln[] BY low ASCENDING.
  DELETE gr_aufnr WHERE low IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gr_vbeln[] COMPARING low.

  SORT gr_matnr[] BY low ASCENDING.
  DELETE gr_aufnr WHERE low IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gr_matnr COMPARING low.

  SORT gt_matnr BY matnr ASCENDING.
  DELETE gt_matnr[] WHERE matnr IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gt_matnr COMPARING matnr.

  SORT gr_werks[] BY low ASCENDING.
  DELETE gr_werks WHERE low IS INITIAL.
  DELETE ADJACENT DUPLICATES FROM gr_werks COMPARING low.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form PROCESS_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM process_data .

  " MSPR-pspnr + mard-labst, mseg-erfmg,

  DATA: BEGIN OF lt_mb52 OCCURS 0,
          werks TYPE  werks_d,
          matnr TYPE  matnr,
          lgort	TYPE lgort_d,
          labst TYPE labst,
          charg TYPE charg_d,
          pspnr TYPE posnr,
          zused TYPE xchar,
          sobkz TYPE sobkz,
        END OF lt_mb52,
        lt_mb52_n LIKE TABLE OF lt_mb52,
        BEGIN OF lt_mseg OCCURS 0,
          matnr      TYPE matnr,
          werks      TYPE werks_d,
          lgort      TYPE lgort_d,
          menge      TYPE menge_d,
          meins      TYPE meins,
          charg      TYPE charg_d,
          ps_psp_pnr TYPE ps_psp_pnr,
          aufnr      TYPE aufnr,
          mtart      TYPE mtart,
          matkl      TYPE matkl,
          ebeln      TYPE bstnr,
          banfn      TYPE banfn,
          zzlsx      TYPE zde_lsx,
          bldat      TYPE bldat,
          zused      TYPE xchar,
        END OF lt_mseg,
        lt_mseg2 LIKE TABLE OF lt_mseg,
        BEGIN OF lt_vbep OCCURS 0,
          vbeln      TYPE vbeln,
          matnr      TYPE matnr,
          meins      TYPE meins,
          dealdate_d TYPE zde_dealdate_d,
          edatu      TYPE edatu,
        END OF lt_vbep,
        BEGIN OF lt_mes004 OCCURS 0,
          aufnr   TYPE aufnr,
          matnr   TYPE matnr,
          zuutien TYPE dec10,
        END OF lt_mes004,
        BEGIN OF lt_mbew OCCURS 0,
          matnr TYPE matnr,
          bwkey TYPE bwkey,
          vprsv TYPE vprsv,
          stprs TYPE stprs,
          verpr TYPE verpr,
        END OF lt_mbew,
*        BEGIN OF lt_data2 OCCURS 0,
*          zzlsx TYPE zde_lsx,
*          gamng TYPE gamng,
*          gwemg TYPE gwemg,
*        END OF lt_data2,
        BEGIN OF lt_wm01 OCCURS 0,
          zlsxdt TYPE zde_lsx,
          matnr  TYPE matnr,
          bdmng  TYPE bdmng,
          meins  TYPE meins,
          budat  TYPE budat,
          status TYPE icon-id,
          sttxt  TYPE sgtxt,
          werks  TYPE werks_d,
        END OF lt_wm01,
        BEGIN OF lt_budat OCCURS 0,
          zlsxdt TYPE zde_lsx,
          budat  TYPE budat,
        END OF lt_budat,
        BEGIN OF lt_prpo OCCURS 0,
          banfn    TYPE banfn,
          bnfpo    TYPE bnfpo,
          ebeln    TYPE ebeln,
          ebelp    TYPE ebelp,
          matnr    TYPE matnr,
          werks    TYPE werks_d,
          menge_pr TYPE menge_d,
          menge_po TYPE menge_d,
          wemng    TYPE weemg,
          zzlsx    TYPE zde_lsx,
        END OF lt_prpo,
        BEGIN OF lt_marc OCCURS 0,
          matnr TYPE matnr,
          werks TYPE werks_d,
          plifz TYPE plifz,
        END OF lt_marc,
        BEGIN OF lt_ltime OCCURS 0,
          zzlsx TYPE zde_lsx,
          ltimg TYPE plifz,
          ltimv TYPE plifz,
          ltimb TYPE plifz,
          ltims TYPE plifz,
          ltimt TYPE plifz,
          ltimh TYPE plifz,
          ltimp TYPE plifz,
          ltimk TYPE plifz,
        END OF lt_ltime,
        ls_ltime LIKE LINE OF lt_ltime
        .

  DATA:
    lv_gstrp TYPE zst_ppr02_2-gstrp,
    lv_gltrp TYPE zst_ppr02_2-gltrp,
    lv_week  TYPE scal-week,
    lv_labst TYPE menge_d,
    lv_opmng TYPE menge_d,
    lv_nvldb TYPE menge_d,
    lv_rqmng TYPE menge_d,    lv_wdmng TYPE menge_d,    lv_dbmng TYPE menge_d,
    lv_rqmnv TYPE menge_d,    lv_wdmnv TYPE menge_d,    lv_dbmnv TYPE menge_d,
    lv_rqmnb TYPE menge_d,    lv_wdmnb TYPE menge_d,    lv_dbmnb TYPE menge_d,
    lv_rqmns TYPE menge_d,    lv_wdmns TYPE menge_d,    lv_dbmns TYPE menge_d,
    lv_rqmnt TYPE menge_d,    lv_wdmnt TYPE menge_d,    lv_dbmnt TYPE menge_d,
    lv_rqmnh TYPE menge_d,    lv_wdmnh TYPE menge_d,    lv_dbmnh TYPE menge_d,
    lv_rqmnp TYPE menge_d,    lv_wdmnp TYPE menge_d,    lv_dbmnp TYPE menge_d,
    lv_rqmnk TYPE menge_d,    lv_wdmnk TYPE menge_d,    lv_dbmnk TYPE menge_d
    .

  DATA: lt_tmp2  LIKE gt_data2,
        ls_budat LIKE LINE OF lt_budat,
        ls_tmp2  LIKE LINE OF gt_data2,
        ls_data2 LIKE LINE OF gt_data2.

  DATA:
    lr_mtartg  TYPE RANGE OF mara-mtart,
    lr_matklg  TYPE RANGE OF mara-matkl,

    lr_mtartv  TYPE RANGE OF mara-mtart,
    lr_matklv  TYPE RANGE OF mara-matkl,
    lr_matklvd TYPE RANGE OF mara-matkl,

    lr_mtartb  TYPE RANGE OF mara-mtart,
    lr_matklb  TYPE RANGE OF mara-matkl,

    lr_mtarts  TYPE RANGE OF mara-mtart,
    lr_matkls  TYPE RANGE OF mara-matkl,

    lr_mtartt  TYPE RANGE OF mara-mtart,
    lr_matklt  TYPE RANGE OF mara-matkl,

    lr_mtarth  TYPE RANGE OF mara-mtart,
    lr_matklh  TYPE RANGE OF mara-matkl,

    lr_mtartp  TYPE RANGE OF mara-mtart,
    lr_matklp  TYPE RANGE OF mara-matkl,
    lr_matklpd TYPE RANGE OF mara-matkl,

    lr_mtartk  TYPE RANGE OF mara-mtart,
    lr_matklk  TYPE RANGE OF mara-matkl,

    lr_lgort   TYPE RANGE OF mseg-lgort
    .

  DATA           :  lr_data   TYPE REF TO data.
  FIELD-SYMBOLS  : <lt_data> TYPE ANY TABLE,
                   <lw_data> TYPE any.

  DATA: lt_stpo TYPE STANDARD TABLE OF stpo_api02,
        ls_cbom LIKE LINE OF gt_cbom.

  DATA: lt_cbom LIKE TABLE OF gt_cbom,
*        lt_ck13n LIKE TABLE OF gt_ck13n,
        lr_cbom TYPE rseloption WITH HEADER LINE.

  DATA: ls_order_object TYPE bapi_pp_order_objects,
        lt_order_detail TYPE TABLE OF bapiret2,
        lt_header       TYPE TABLE OF bapi_order_header1 WITH HEADER LINE,
        lt_component    TYPE TABLE OF bapi_order_component,
        lt_lines        LIKE TABLE OF tline,
        ls_line         LIKE tline,
        lv_tdname       TYPE thead-tdname.

  DATA: lv_menge TYPE menge_d,
        lv_bdmng TYPE menge_d,
        lv_matnr TYPE matnr,
        lv_zzlsx TYPE zde_lsx,
        lv_budat TYPE budat.

  ls_order_object-header = 'X'.
  ls_order_object-components = 'X'.


  IF NOT gt_data IS INITIAL OR NOT gt_data2 IS INITIAL.



    SELECT vbeln
           kunnr
           auart
      INTO TABLE gt_vbak
      FROM vbak
      WHERE vbeln IN gr_vbeln[].

    SELECT partner
           bpext
           name_org1
      INTO TABLE gt_but000
      FROM but000.

    IF NOT gr_vbeln[] IS INITIAL.

      SELECT t1~vbeln,
             t2~matnr,
             t1~meins,
             t1~dealdate_d,
             t1~edatu
        FROM vbep AS t1
        LEFT OUTER JOIN vbap AS t2 ON t1~vbeln = t2~vbeln AND t1~posnr = t2~posnr
        INTO TABLE @lt_vbep
        WHERE t1~vbeln IN @gr_vbeln[]
          AND t2~matnr IN @gr_matnr[].
    ENDIF.

    IF NOT gt_matnr[] IS INITIAL.
      IF NOT p_ct IS INITIAL.
        SELECT
             t1~matnr,
             t1~werks,
             t1~verid,
             t1~stlal,
             t1~stlan,
             t2~aufnr,
             t2~zzlsx
        FROM mkal AS t1
        INNER JOIN @gt_data AS t2 ON t1~werks = t2~werks AND t1~matnr = t2~matnr AND t1~verid = t2~verid
        WHERE t1~matnr IN @gr_matnr[] AND t2~aufnr IN @gr_aufnr[]
          INTO TABLE @gt_mkal.
      ELSE.
        SELECT
         t1~matnr,
         t1~werks,
         t1~verid,
         t1~stlal,
         t1~stlan,
         t2~aufnr,
         t2~zzlsx
    FROM mkal AS t1
      LEFT OUTER JOIN @gt_data2 AS t2 ON t1~werks = t2~werks AND t1~matnr = t2~matnr AND t1~verid = t2~verid
    WHERE t1~matnr IN @gr_matnr[] AND t2~aufnr IN @gr_aufnr[]
      INTO TABLE @gt_mkal.
      ENDIF.
    ENDIF.


    IF NOT gt_aufnr[] IS INITIAL.
      IF gt_afko IS INITIAL.
        SELECT aufnr zzlsx gmein
          FROM afko
          INTO TABLE gt_afko
          WHERE aufnr IN gr_aufnr[].
      ENDIF.


      gr_cbmat-sign = 'I'.      gr_cbmat-option = 'EQ'.
      LOOP AT gt_aufnr.
        CLEAR: lt_header[], lt_component[].
        CALL FUNCTION 'BAPI_PRODORD_GET_DETAIL'
          EXPORTING
            number        = gt_aufnr-aufnr
            order_objects = ls_order_object
          TABLES
            header        = lt_header
            component     = lt_component.

        IF NOT lt_component IS INITIAL.
          CLEAR: lv_matnr.
          READ TABLE lt_header INDEX 1.
          SORT lt_component BY material ASCENDING.
          LOOP AT lt_component INTO DATA(ls_component).
            IF lv_matnr NE ls_component-material.
              IF sy-tabix GT 1.
                APPEND ls_cbom TO lt_cbom.
                CLEAR: ls_cbom.

                gr_cbmat-low = lv_matnr.
                APPEND gr_cbmat.
              ENDIF.

              lv_matnr = ls_component-material.
              ls_cbom-werks = ls_component-prod_plant.
              ls_cbom-meins = ls_component-base_uom.

              ls_cbom-matnr   = lt_header-material.
              ls_cbom-idnrk   = ls_component-material.
              ls_cbom-aufnr   = lt_header-order_number.
              ls_cbom-zzlsx   = gt_aufnr-zzlsx.
              ls_cbom-zzpspnr = gt_aufnr-zzpspnr.
            ENDIF.
            ls_cbom-menge = ls_cbom-menge + ls_component-req_quan.
            ls_cbom-obmng = ls_cbom-obmng + ls_component-req_quan - ls_component-withdrawn_quantity.
            ls_cbom-wdmng = ls_cbom-wdmng + ls_component-withdrawn_quantity.
          ENDLOOP.

          APPEND ls_cbom TO lt_cbom.
          CLEAR: ls_cbom.
        ENDIF.
      ENDLOOP.
      SORT gr_cbmat[] BY low ASCENDING.
      DELETE ADJACENT DUPLICATES FROM gr_cbmat[] COMPARING low.
    ENDIF.

    IF NOT gt_mkal[] IS INITIAL.
      IF p_dt IS INITIAL.
        gr_cbmat-sign = 'I'.      gr_cbmat-option = 'EQ'.
        LOOP AT gt_mkal.
          CALL FUNCTION 'CALO_INIT_API'
            EXCEPTIONS
              log_object_not_found     = 1
              log_sub_object_not_found = 2
              other_error              = 3
              OTHERS                   = 4.

          CALL FUNCTION 'CONVERSION_EXIT_MATN1_OUTPUT'
            EXPORTING
              input  = gt_mkal-matnr
            IMPORTING
              output = gt_mkal-matnr.

          CALL FUNCTION 'CSAP_MAT_BOM_READ'
            EXPORTING
              material    = gt_mkal-matnr
              plant       = gt_mkal-werks
              bom_usage   = gt_mkal-stlan
              alternative = gt_mkal-stlal
            TABLES
              t_stpo      = lt_stpo.

          CALL FUNCTION 'CONVERSION_EXIT_MATN1_INPUT'
            EXPORTING
              input  = gt_mkal-matnr
            IMPORTING
              output = gt_mkal-matnr.

          IF sy-subrc = 0.
            CLEAR: lv_matnr.
            SORT lt_stpo BY component ASCENDING.
            LOOP AT lt_stpo INTO DATA(ls_stpo).
              CALL FUNCTION 'CONVERSION_EXIT_MATN1_INPUT'
                EXPORTING
                  input  = ls_stpo-component
                IMPORTING
                  output = ls_stpo-component.

              IF lv_matnr NE ls_stpo-component.
                IF sy-tabix GT 1.
                  IF NOT lv_matnr IN gr_cbmat[].
                    APPEND ls_cbom TO lt_cbom.
                  ENDIF.

                  CLEAR: ls_cbom.
                  gr_cbmat-low = ls_cbom-idnrk.
                  APPEND gr_cbmat.
                ENDIF.

                lv_matnr = ls_stpo-component.

                ls_cbom-idnrk = ls_stpo-component.
                ls_cbom-werks = gt_mkal-werks.
                ls_cbom-meins = ls_stpo-comp_unit.

                ls_cbom-aufnr = gt_mkal-aufnr.
                ls_cbom-matnr = gt_mkal-matnr.

                ls_cbom-zzlsx = gt_mkal-zzlsx.
              ENDIF.

            ENDLOOP.

            APPEND ls_cbom TO lt_cbom.
            CLEAR: ls_cbom.
          ELSE.
* Implement suitable error handling here
          ENDIF.
        ENDLOOP.
      ENDIF.


      IF NOT lt_cbom IS INITIAL.
        SORT lt_cbom BY aufnr matnr werks idnrk ASCENDING menge DESCENDING obmng DESCENDING.
        DELETE ADJACENT DUPLICATES FROM lt_cbom COMPARING matnr werks idnrk.

        IF NOT p_ct IS INITIAL.
          SELECT DISTINCT
                         t1~aufnr,
                         t1~matnr,
                         t1~werks,
                         t2~maktx,
                         t3~mtart,
                         t3~matkl,
                         t1~idnrk,
                         t1~meins,
                         t4~wgbez,
                         t1~menge,
                         t1~ausch,
                         t1~wdmng,
                         t1~obmng,
                         t6~mtart AS mtart_r,
                         t6~matkl AS matkl_r,
                         t1~zzlsx,
                         t1~zzpspnr
                    FROM @lt_cbom AS t1
                    LEFT OUTER JOIN makt  AS t2 ON t1~idnrk = t2~matnr
                    LEFT OUTER JOIN mara  AS t3 ON t1~matnr = t3~matnr
                    LEFT OUTER JOIN t023t AS t4 ON t3~matkl = t4~matkl
                    LEFT OUTER JOIN stpo  AS t5 ON t1~idnrk = t5~idnrk
                    LEFT OUTER JOIN mara  AS t6 ON t1~idnrk = t6~matnr
                    INTO CORRESPONDING FIELDS OF TABLE @gt_cbom.

          SELECT
             t1~matnr,
             t1~werks,
             t2~vbeln,
             t2~pernr,
             t3~vorna,
             t3~nachn
            FROM resb AS t1
            LEFT OUTER JOIN ztb_re_header AS t2 ON t1~rsnum = t2~rsnum
            LEFT OUTER JOIN pa0002 AS t3 ON t2~pernr = t3~pernr
            FOR ALL ENTRIES IN @gt_cbom
            WHERE t1~matnr = @gt_cbom-idnrk AND
                  t1~werks = @gt_cbom-werks AND
                  t2~vbeln IN @gr_vbeln
            INTO CORRESPONDING FIELDS OF TABLE @gt_resb.
        ELSE.
          SELECT DISTINCT
                     t1~aufnr,
                     t1~matnr,
                     t1~werks,
                     t2~maktx,
                     t3~mtart,
                     t3~matkl,
                     t1~idnrk,
                     t1~meins,
                     t4~wgbez,
                     t1~menge,
                     t1~ausch,
                     t1~wdmng,
                     t1~obmng,
                     t6~mtart AS mtart_r,
                     t6~matkl AS matkl_r,
                     t1~zzlsx,
                     t1~zzpspnr
                FROM @lt_cbom AS t1
                LEFT OUTER JOIN makt  AS t2 ON t1~idnrk = t2~matnr
                LEFT OUTER JOIN mara  AS t3 ON t1~matnr = t3~matnr
                LEFT OUTER JOIN t023t AS t4 ON t3~matkl = t4~matkl
                LEFT OUTER JOIN stpo  AS t5 ON t1~idnrk = t5~idnrk
                LEFT OUTER JOIN mara  AS t6 ON t1~idnrk = t6~matnr
                INTO CORRESPONDING FIELDS OF TABLE @gt_cbom2.

          SELECT
             t1~matnr,
             t1~werks,
             t2~vbeln,
             t2~pernr,
             t3~vorna,
             t3~nachn
            FROM resb AS t1
            LEFT OUTER JOIN ztb_re_header AS t2 ON t1~rsnum = t2~rsnum
            LEFT OUTER JOIN pa0002 AS t3 ON t2~pernr = t3~pernr
            FOR ALL ENTRIES IN @gt_cbom2
            WHERE t1~matnr = @gt_cbom2-idnrk AND
                  t1~werks = @gt_cbom2-werks AND
                  t2~vbeln IN @gr_vbeln
            INTO CORRESPONDING FIELDS OF TABLE @gt_resb.
        ENDIF.

        DELETE gt_resb WHERE pernr IS INITIAL.
      ENDIF.
    ENDIF.

    DELETE gr_cbmat[] WHERE low NOT IN s_compo.
    DELETE gt_cbom WHERE idnrk NOT IN s_compo.
    DELETE gt_cbom2 WHERE idnrk NOT IN s_compo.

    IF NOT gr_cbmat[] IS INITIAL.
      cl_salv_bs_runtime_info=>set(    EXPORTING display  = abap_false
                                                 metadata = abap_false
                                                 data     = abap_true ).

      SORT gr_cbmat[] BY low ASCENDING.
      DELETE ADJACENT DUPLICATES FROM gr_cbmat[] COMPARING low.

      SUBMIT rm07mlbs                 "MB52
        WITH matnr IN gr_cbmat
*        WITH werks IN gr_werks
        AND RETURN." EXPORTING LIST TO MEMORY.

*& Read the data from other program to here
      TRY.
          cl_salv_bs_runtime_info=>get_data_ref( IMPORTING r_data = lr_data ).
          ASSIGN lr_data->* TO <lt_data>.
        CATCH cx_salv_bs_sc_runtime_info.
          MESSAGE `Unable to retrieve ALV data` TYPE 'E'.
      ENDTRY.
      cl_salv_bs_runtime_info=>clear_all( ).

      IF <lt_data> IS ASSIGNED AND NOT <lt_data> IS INITIAL.
        LOOP AT <lt_data> ASSIGNING <lw_data>.
          MOVE-CORRESPONDING <lw_data> TO lt_mb52.
          APPEND lt_mb52.
        ENDLOOP.
        DELETE lt_mb52 WHERE labst IS INITIAL.
        SORT lt_mb52 BY werks matnr ASCENDING.
      ENDIF.

      CLEAR: lr_data.
      UNASSIGN: <lt_data>, <lw_data>.

      IF NOT p_dt IS INITIAL.
        SELECT DISTINCT
          t2~banfn,
          t2~bnfpo,
          t2~ebeln,
          t2~ebelp,
          t2~matnr,
          t2~werks,
          t2~menge AS menge_pr,
          t1~menge AS menge_po,
          t1~wemng
          INTO CORRESPONDING FIELDS OF TABLE @lt_prpo
          FROM eban AS t2
          LEFT OUTER JOIN eket AS t1 ON t1~ebeln = t2~ebeln AND t1~ebelp = t2~ebelp
*          LEFT OUTER JOIN ekpo AS t3 ON t3~ebeln = t2~ebeln AND t3~ebelp = t2~ebelp AND t3~loekz EQ ''
          WHERE t2~matnr IN @gr_cbmat[]
            AND t2~werks IN @gr_werks[]
            AND t2~loekz EQ ''.

        IF NOT lt_prpo[] IS INITIAL.
          SORT lt_prpo BY banfn bnfpo ASCENDING.
          LOOP AT lt_prpo ASSIGNING FIELD-SYMBOL(<fs_prpo>).
            AT NEW banfn.
              CLEAR: lv_tdname, ls_line.
              REFRESH lt_lines.
              lv_tdname = <fs_prpo>-banfn.
              CALL FUNCTION 'READ_TEXT'
                EXPORTING
                  id                      = 'B08'
                  language                = 'E'
                  name                    = lv_tdname
                  object                  = 'EBANH'
                TABLES
                  lines                   = lt_lines
                EXCEPTIONS
                  id                      = 1
                  language                = 2
                  name                    = 3
                  not_found               = 4
                  object                  = 5
                  reference_check         = 6
                  wrong_access_to_archive = 7
                  OTHERS                  = 8.

              IF sy-subrc <> 0.
* Implement suitable error handling here
              ELSE.
                READ TABLE lt_lines INTO ls_line INDEX 1.
                IF sy-subrc IS INITIAL.
                  <fs_prpo>-zzlsx = ls_line-tdline.
                ENDIF.
              ENDIF.

            ENDAT.

            IF NOT ls_line-tdline IS INITIAL.
              <fs_prpo>-zzlsx = ls_line-tdline.
            ENDIF.

          ENDLOOP.

          SORT lt_prpo BY matnr ASCENDING.
          DELETE lt_prpo WHERE zzlsx IS INITIAL OR zzlsx NOT IN s_zzlsx OR ( NOT ebeln IS INITIAL AND ebeln(1) NE '4' ).
        ENDIF.
      ENDIF.


      IF NOT gr_aufnr[] IS INITIAL.
        IF NOT p_ct IS INITIAL.
          SELECT
              t1~matnr
              t1~werks
              t1~lgort
              t1~menge
              t1~meins
              t1~charg
              t1~ps_psp_pnr
              t1~aufnr
              t2~bldat
            FROM mseg AS t1
            LEFT OUTER JOIN mkpf AS t2 ON t1~mblnr = t2~mblnr
            INTO CORRESPONDING FIELDS OF TABLE lt_mseg
            WHERE matnr IN gr_cbmat[]
              AND aufnr IN gr_aufnr
              AND ( bwart = '261' OR bwart = '262' ).

          SORT lt_mseg BY aufnr bldat ASCENDING.
        ELSE.
          SELECT DISTINCT
            t1~matnr,
            t1~werks,
            t1~lgort,
            t1~menge,
            t1~meins,
            t1~charg,
            t1~ps_psp_pnr,
            t1~aufnr,
            t2~mtart,
            t2~matkl,
            t1~ebeln,
            t3~banfn,
            t4~bldat
        FROM mseg AS t1
        INNER JOIN mara AS t2 ON t1~matnr = t2~matnr
        LEFT OUTER JOIN ekpo AS t3 ON t1~ebeln = t3~ebeln
        LEFT OUTER JOIN mkpf AS t4 ON t1~mblnr = t4~mblnr
        INTO CORRESPONDING FIELDS OF TABLE @lt_mseg
        WHERE t1~matnr IN @gr_cbmat[]
          AND aufnr IN @gr_aufnr
          AND ( bwart = '261' OR bwart = '301' OR bwart = '302' OR bwart = '311' OR bwart = '312' ).

          SELECT DISTINCT
              t1~matnr,
              t1~werks,
              t1~lgort,
              t1~menge,
              t1~meins,
              t1~charg,
              t1~ps_psp_pnr,
              t1~aufnr,
              t2~mtart,
              t2~matkl,
              t1~ebeln,
              t3~banfn,
              t4~bldat
          FROM mseg AS t1
          INNER JOIN mara AS t2 ON t1~matnr = t2~matnr
          LEFT OUTER JOIN ekpo AS t3 ON t1~ebeln = t3~ebeln
          LEFT OUTER JOIN mkpf AS t4 ON t1~mblnr = t4~mblnr
          INTO CORRESPONDING FIELDS OF TABLE @lt_mseg2
          WHERE t1~matnr IN @gr_cbmat[]
*            AND aufnr IN @gr_aufnr
            AND ( bwart = '411' OR bwart = '412' OR bwart = '413' ).

          IF NOT lt_mseg[] IS INITIAL.
            LOOP AT lt_mseg ASSIGNING FIELD-SYMBOL(<fs_mseg>) WHERE NOT banfn IS INITIAL.
              lv_tdname = <fs_mseg>-banfn.

              CALL FUNCTION 'READ_TEXT'
                EXPORTING
                  id       = 'B08'
                  language = 'E'
                  name     = lv_tdname
                  object   = 'EBANH'
                TABLES
                  lines    = lt_lines.
              IF sy-subrc <> 0.
* Implement suitable error handling here
              ELSE.
                READ TABLE lt_lines INTO DATA(ls_lines) INDEX 1.
                IF sy-subrc IS INITIAL.
                  <fs_mseg>-zzlsx = ls_lines-tdline.
                ENDIF.
              ENDIF.

            ENDLOOP.
            SORT lt_mseg BY zzlsx aufnr matnr bldat ASCENDING.
          ENDIF.
        ENDIF.
      ENDIF.

      SELECT
        matnr
        bwkey
        vprsv
        stprs
        verpr
        FROM mbew
        INTO TABLE lt_mbew
        WHERE matnr IN gr_cbmat[]
          AND bwkey IN gr_werks.
    ENDIF.

    IF NOT gt_mm007 IS INITIAL.
      SELECT * INTO TABLE gt_ekkn
        FROM ekkn
        FOR ALL ENTRIES IN gt_mm007
        WHERE ebeln = gt_mm007-ebeln.

      SORT gt_ekkn BY ebeln ASCENDING ps_psp_pnr DESCENDING vbeln DESCENDING.
    ENDIF.

    IF NOT p_ct IS INITIAL AND NOT gt_data IS INITIAL.
      LOOP AT gt_data ASSIGNING FIELD-SYMBOL(<fs_data>).
        "(4) UT1
        READ TABLE gt_mm007 INTO DATA(ls_mm007) WITH KEY vbeln = <fs_data>-zzvbeln.
        IF sy-subrc IS INITIAL.
          LOOP AT gt_ekkn INTO DATA(ls_ekkn) WHERE ebeln = ls_mm007-ebeln AND ( NOT ps_psp_pnr IS INITIAL OR NOT vbeln IS INITIAL ).
            IF <fs_data>-kunnr IS INITIAL.
              READ TABLE gt_vbak INTO DATA(ls_vbak) WITH KEY vbeln = ls_ekkn-vbeln.
              IF sy-subrc IS INITIAL.
                <fs_data>-kunnr = ls_vbak-kunnr.
              ENDIF.
            ENDIF.
          ENDLOOP.
        ENDIF.

        "(4) UT2
        IF <fs_data>-kunnr IS INITIAL.
          READ TABLE gt_vbak INTO ls_vbak WITH KEY vbeln = <fs_data>-zzvbeln.
          IF sy-subrc IS INITIAL.
            READ TABLE gt_but000 INTO DATA(ls_but000) WITH KEY partner = ls_vbak-kunnr.
            IF sy-subrc IS INITIAL.
              <fs_data>-kunnr = ls_but000-bpext.
            ENDIF.
          ENDIF.
        ENDIF.

        "(6) (7)
        READ TABLE gt_vbak INTO ls_vbak WITH KEY vbeln = <fs_data>-zzvbeln.
        IF sy-subrc IS INITIAL.
          <fs_data>-kunag = ls_vbak-kunnr.
          <fs_data>-auart = ls_vbak-auart.
          READ TABLE gt_but000 INTO ls_but000 WITH KEY partner = ls_vbak-kunnr.
          IF sy-subrc IS INITIAL.
            <fs_data>-cname = ls_but000-name_org1.
          ENDIF.
        ENDIF.

        READ TABLE gt_uutien INTO DATA(ls_uutien) WITH KEY lsxdt = <fs_data>-zzlsx.
        IF sy-subrc IS INITIAL.
          <fs_data>-zuutien = ls_uutien-thutuuutien.
        ENDIF.

        READ TABLE lt_mseg INTO DATA(ls_mseg) WITH KEY aufnr = <fs_data>-aufnr.
        IF sy-subrc IS INITIAL.
          <fs_data>-wiptt = sy-datum - ls_mseg-bldat.  "(23)
        ENDIF.

        READ TABLE lt_vbep INTO DATA(ls_vbep) WITH KEY vbeln = <fs_data>-zzvbeln matnr = <fs_data>-matnr.
        IF sy-subrc IS INITIAL.
          <fs_data>-dealdate_d = ls_vbep-dealdate_d.              "(21)
          CALL FUNCTION 'DATE_CHECK_PLAUSIBILITY'
            EXPORTING
              date                      = <fs_data>-dealdate_d
            EXCEPTIONS
              plausibility_check_failed = 1
              OTHERS                    = 2.

          IF sy-subrc <> 0.
            <fs_data>-dealdate_d = ls_vbep-edatu.              "(21)
          ENDIF.
        ENDIF.

        <fs_data>-wipkh = <fs_data>-gltrp - <fs_data>-gstrp.    "(22)
        <fs_data>-tpcnk = <fs_data>-gamng - <fs_data>-gwemg.    "(24)
        <fs_data>-gmein = <fs_data>-meins = ls_vbep-meins.      "(25)

      ENDLOOP.

      LOOP AT gt_data INTO DATA(ls_data).
        DATA(ls_tmp) = ls_data.
        LOOP AT gt_cbom INTO ls_cbom WHERE aufnr = ls_data-aufnr AND matnr = ls_data-matnr AND werks = ls_data-werks.
          ls_data = ls_tmp.

          ls_data-obmng    = ls_cbom-obmng.
          ls_data-matnr_r  = ls_cbom-idnrk.  "(8)
          ls_data-maktx_r  = ls_cbom-maktx.  "(9)
          ls_data-kmpme    = ls_cbom-meins.  "(10)
          ls_data-slnvl_dm = ls_cbom-menge.  "(11)

          READ TABLE lt_mbew INTO DATA(ls_mbew) WITH KEY matnr = ls_data-matnr_r bwkey = ls_data-werks.
          IF sy-subrc IS INITIAL.
            IF ls_mbew-vprsv = 'S'.
              ls_data-preis = ls_mbew-stprs.
            ELSE.
              ls_data-preis = ls_mbew-verpr.
            ENDIF.
          ENDIF.
          ls_data-zgttt = ls_data-obmng * ls_data-preis.  "(13.1)

          DATA: lv_bldat TYPE bldat.
          LOOP AT  lt_mseg INTO ls_mseg WHERE aufnr = ls_data-aufnr AND matnr = ls_data-matnr_r.
            ls_data-slnvl_261 = ls_data-slnvl_261 + ls_mseg-menge.  "(12)
          ENDLOOP.

          ls_data-zsltt    = ls_cbom-obmng. "(13)

          IF NOT ls_data-projn IS INITIAL.
            READ TABLE lt_mb52 ASSIGNING FIELD-SYMBOL(<fs_mb52>) WITH KEY werks = ls_data-werks matnr = ls_data-matnr_r pspnr = ls_data-projn.
            IF sy-subrc IS INITIAL.
              ls_data-slnvl_wbs = <fs_mb52>-labst.  "(12.1)
              <fs_mb52>-zused = 'X'.
            ENDIF.
          ENDIF.

          APPEND ls_data TO gt_alv.
          CLEAR: ls_data.
        ENDLOOP.
      ENDLOOP.

      SORT gt_alv BY zuutien aufnr matnr matnr_r ASCENDING.
*      lt_mb52_n[] = lt_mb52[].
*      DELETE lt_mb52_n WHERE zused = 'X' OR sobkz = '' OR labst IS INITIAL.
*      SORT lt_mb52_n BY werks matnr ASCENDING.
*      LOOP AT lt_mb52_n INTO DATA(ls_mb52).
*        lv_labst = lv_labst + ls_mb52-labst.
*
*        AT END OF matnr.
*          LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<fs_alv>) WHERE werks = ls_mb52-werks AND matnr_r = ls_mb52-matnr AND slnvl_wbs IS INITIAL.
*            IF lv_labst > <fs_alv>-obmng.
*              <fs_alv>-slnvl_wbs = <fs_alv>-obmng.    "(12.1)
*              lv_labst = lv_labst - <fs_alv>-obmng.
*            ELSE.
*              <fs_alv>-slnvl_wbs = lv_labst.          "(12.1)
*              CLEAR: lv_labst.
*              EXIT.
*            ENDIF.
*          ENDLOOP.
*          CLEAR: lv_labst.
*        ENDAT.
*      ENDLOOP.

      CLEAR: lv_labst.
      SORT lt_mb52 BY werks matnr ASCENDING.
      LOOP AT lt_mb52 INTO DATA(ls_mb52).
        lv_labst = lv_labst + ls_mb52-labst.

        AT END OF matnr.
          LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<fs_alv>) WHERE werks = ls_mb52-werks AND matnr_r = ls_mb52-matnr AND slnvl_all IS INITIAL.
            IF lv_labst > <fs_alv>-slnvl_dm.
              <fs_alv>-slnvl_all = <fs_alv>-slnvl_dm.   "(12.2)
              lv_labst = lv_labst - <fs_alv>-slnvl_dm.
            ELSE.
              <fs_alv>-slnvl_all = lv_labst.            "(12.2)
              CLEAR: lv_labst.
              EXIT.
            ENDIF.
          ENDLOOP.
          CLEAR: lv_labst.
        ENDAT.
      ENDLOOP.


    ELSEIF NOT p_dt IS INITIAL AND NOT gt_data2 IS INITIAL.
      lr_mtartg[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = 'Z23' )
                             ( sign = 'I' option = 'EQ'  low = 'Z24' )
                             ( sign = 'I' option = 'EQ'  low = 'Z2P' )
                             ( sign = 'I' option = 'EQ'  low = 'Z21' )
                             ( sign = 'I' option = 'EQ'  low = 'Z20' )
                           ).

      lr_matklg[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '250010' )
                             ( sign = 'I' option = 'EQ'  low = '250000' )
                             ( sign = 'I' option = 'EQ'  low = '260000' )
                             ( sign = 'I' option = 'EQ'  low = '261000' )
                           ).

      lr_mtartv[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = 'Z25' )
                           ).

      lr_matklvd[] = VALUE #(
                            ( sign = 'I' option = 'EQ'  low = '250010' )
                            ( sign = 'I' option = 'EQ'  low = '250000' )
                          ).

      lr_matklv[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '288090' )
                             ( sign = 'I' option = 'EQ'  low = '280000' )
                             ( sign = 'I' option = 'EQ'  low = '275030' )
                             ( sign = 'I' option = 'EQ'  low = '275040' )
                             ( sign = 'I' option = 'EQ'  low = '275050' )
                             ( sign = 'I' option = 'EQ'  low = '275060' )
                             ( sign = 'I' option = 'EQ'  low = '275010' )
                             ( sign = 'I' option = 'EQ'  low = '270030' )
                             ( sign = 'I' option = 'EQ'  low = '292080' )
                           ).


      lr_matklb[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '294000' )
                             ( sign = 'I' option = 'EQ'  low = '295000' )
                             ( sign = 'I' option = 'EQ'  low = '420000' )
                             ( sign = 'I' option = 'CP'  low = '262*' )
                           ).

      lr_matkls[]  = VALUE #(
                             ( sign = 'I' option = 'CP'  low = '288*' )
                             ( sign = 'E' option = 'EQ'  low = '288090' )
                           ).

      lr_mtartt[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = 'Z29' )
                             ( sign = 'I' option = 'EQ'  low = 'Z2K' )
                             ( sign = 'I' option = 'EQ'  low = 'Z2M' )
                             ( sign = 'I' option = 'EQ'  low = 'Z2E' )
                             ( sign = 'I' option = 'EQ'  low = 'Z2B' )
                             ( sign = 'I' option = 'EQ'  low = 'Z30' )
                             ( sign = 'I' option = 'EQ'  low = 'Z60' )
                             ( sign = 'I' option = 'EQ'  low = 'Z70' )
                             ( sign = 'I' option = 'EQ'  low = 'Z90' )
                             ( sign = 'I' option = 'EQ'  low = 'Z91' )
                             ( sign = 'I' option = 'EQ'  low = 'Z99' )
                           ).

      lr_matklt[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '281000' )
                             ( sign = 'I' option = 'EQ'  low = '286000' )
                             ( sign = 'I' option = 'EQ'  low = '287000' )
                             ( sign = 'I' option = 'EQ'  low = '283000' )
                           ).

      lr_mtarth[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = 'Z27' )
                           ).

      lr_matklh[] = VALUE #(
                            ( sign = 'E' option = 'EQ'  low = '275020' )
                            ( sign = 'E' option = 'EQ'  low = '275000' )
                          ).

      lr_mtartp[]  = VALUE #(
                            ( sign = 'I' option = 'EQ'  low = 'Z2A' )
                            ( sign = 'I' option = 'EQ'  low = 'Z2F' )
                          ).

      lr_matklp[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '400000' )
                           ).

      lr_matklpd[]  = VALUE #(
                             ( sign = 'I' option = 'EQ'  low = '292080' )
                           ).

      lr_matklk[] = VALUE #(
                                  ( sign = 'I' option = 'EQ'  low = '275020' )
                                  ( sign = 'I' option = 'EQ'  low = '275000' )
                                ).

      lr_lgort[]  = VALUE #(
                                  ( sign = 'I' option = 'BT'  low = 'N001' high = 'N011')
                                  ( sign = 'I' option = 'EQ'  low = '10DL' ) ( sign = 'I' option = 'EQ'  low = '10KT' )
                                  ( sign = 'I' option = 'EQ'  low = '10TM' ) ( sign = 'I' option = 'EQ'  low = '10VA' )
                                  ( sign = 'I' option = 'EQ'  low = 'NK10' ) ( sign = 'I' option = 'EQ'  low = 'TM10' )
                                  ( sign = 'I' option = 'EQ'  low = 'V001' ) ( sign = 'I' option = 'EQ'  low = 'V002' )
                                  ( sign = 'I' option = 'EQ'  low = 'X011' ) ( sign = 'I' option = 'EQ'  low = 'HC10' )
                                  ( sign = 'I' option = 'EQ'  low = '11NL' ) ( sign = 'I' option = 'EQ'  low = 'NL11' )
                                  ( sign = 'I' option = 'EQ'  low = '11NK' ) ( sign = 'I' option = 'EQ'  low = 'NK11' )
                                  ( sign = 'I' option = 'EQ'  low = '11TT' ) ( sign = 'I' option = 'EQ'  low = 'TM11' )
                                  ( sign = 'I' option = 'EQ'  low = 'DP11' ) ( sign = 'I' option = 'EQ'  low = 'GT12' )
                                  ( sign = 'I' option = 'EQ'  low = '10DP' ) ( sign = 'I' option = 'EQ'  low = 'N012' )
                                  ( sign = 'I' option = 'EQ'  low = 'N013' ) ( sign = 'I' option = 'EQ'  low = 'N014' )
                                  ( sign = 'I' option = 'EQ'  low = '10HC' ) ( sign = 'I' option = 'EQ'  low = '10HQ' )
                                  ( sign = 'I' option = 'EQ'  low = '10NL' ) ( sign = 'I' option = 'EQ'  low = '10TP' )
                                  ( sign = 'I' option = 'EQ'  low = '10TT' ) ( sign = 'I' option = 'EQ'  low = '10VT' )
                                  ( sign = 'I' option = 'EQ'  low = 'DP11' ) ( sign = 'I' option = 'EQ'  low = '12FH' )
                                  ( sign = 'I' option = 'EQ'  low = '12GT' ) ( sign = 'I' option = 'EQ'  low = '12NL' )
                                  ( sign = 'I' option = 'EQ'  low = '12NZ' ) ( sign = 'I' option = 'EQ'  low = '12TT' )
                                  ( sign = 'I' option = 'EQ'  low = '12VA' ) ( sign = 'I' option = 'EQ'  low = '12VT' )
                                  ( sign = 'I' option = 'EQ'  low = 'DP12' ) ( sign = 'I' option = 'EQ'  low = 'FH12' )
                                  ( sign = 'I' option = 'EQ'  low = 'GT12' ) ( sign = 'I' option = 'EQ'  low = 'NK12' )
                                  ( sign = 'I' option = 'EQ'  low = 'NL12' ) ( sign = 'I' option = 'EQ'  low = 'NZ12' )
                                  ( sign = 'I' option = 'EQ'  low = 'SFNZ' ) ( sign = 'I' option = 'EQ'  low = 'TM12' )
                                  ( sign = 'I' option = 'EQ'  low = 'VA12' ) ( sign = 'I' option = 'EQ'  low = 'VT12' )
                                  ( sign = 'I' option = 'EQ'  low = '1211' ) ( sign = 'I' option = 'EQ'  low = '1212' )
                                  ( sign = 'I' option = 'EQ'  low = '1215' ) ( sign = 'I' option = 'EQ'  low = '10CD' )
                                  ( sign = 'I' option = 'EQ'  low = 'FSC1' ) ( sign = 'I' option = 'EQ'  low = 'KETO' )
                                  ( sign = 'I' option = 'EQ'  low = 'M002' ) ( sign = 'I' option = 'EQ'  low = 'M003' )
                                  ( sign = 'I' option = 'EQ'  low = 'NS01' ) ( sign = 'I' option = 'EQ'  low = 'QC01' )
                                  ( sign = 'I' option = 'EQ'  low = 'TL01' ) ( sign = 'I' option = 'EQ'  low = 'TP02' )
                                  ( sign = 'I' option = 'EQ'  low = 'TP03' ) ( sign = 'I' option = 'EQ'  low = 'TP04' )
                                  ( sign = 'I' option = 'EQ'  low = 'TP05' ) ( sign = 'I' option = 'EQ'  low = 'TP06' )
                                  ( sign = 'I' option = 'EQ'  low = 'TP07' ) ( sign = 'I' option = 'EQ'  low = 'TP08' )
                                  ( sign = 'I' option = 'EQ'  low = 'TP09' ) ( sign = 'I' option = 'EQ'  low = 'TP10' )
                                  ( sign = 'I' option = 'EQ'  low = 'TP01' ) ( sign = 'I' option = 'EQ'  low = '10KM' )
                                  ( sign = 'I' option = 'EQ'  low = 'TB03' ) ( sign = 'I' option = 'EQ'  low = '11BP' )
                                  ( sign = 'I' option = 'EQ'  low = '11CD' ) ( sign = 'I' option = 'EQ'  low = '11TP' )
                                  ( sign = 'I' option = 'EQ'  low = 'BP11' ) ( sign = 'I' option = 'EQ'  low = 'TP11' )
                                  ( sign = 'I' option = 'EQ'  low = '12TP' ) ( sign = 'I' option = 'EQ'  low = 'TP12' )
                                ).

*      SELECT t1~zzlsx,
*             SUM( t1~gamng ),
*             SUM( t1~gwemg )
*        FROM @gt_data2 AS t1
*        GROUP BY zzlsx,
*                 gamng,
*                 gwemg
*        INTO TABLE @lt_data2.

      IF NOT gr_cbmat IS INITIAL.
        cl_salv_bs_runtime_info=>set(    EXPORTING display  = abap_false
                                               metadata = abap_false
                                               data     = abap_true ).

        SORT gr_cbmat[] BY low ASCENDING.
        DELETE ADJACENT DUPLICATES FROM gr_cbmat[] COMPARING low.

        SUBMIT zwmr0001                                     "ZWMR0001
          WITH s_matnr IN gr_cbmat
*          WITH s_werks IN gr_werks
          AND RETURN." EXPORTING LIST TO MEMORY.

*& Read the data from other program to here
        TRY.
            cl_salv_bs_runtime_info=>get_data_ref( IMPORTING r_data = lr_data ).
            ASSIGN lr_data->* TO <lt_data>.
          CATCH cx_salv_bs_sc_runtime_info.
            MESSAGE `Unable to retrieve ALV data` TYPE 'E'.
        ENDTRY.
        cl_salv_bs_runtime_info=>clear_all( ).

        IF <lt_data> IS ASSIGNED AND NOT <lt_data> IS INITIAL.
          LOOP AT <lt_data> ASSIGNING <lw_data>.
            MOVE-CORRESPONDING <lw_data> TO lt_wm01.
            CHECK lt_wm01-zlsxdt IN s_zzlsx.
            IF lt_wm01-sttxt = 'Completed'.
              APPEND lt_wm01.
            ENDIF.
          ENDLOOP.
        ENDIF.

        CLEAR: lr_data.
        UNASSIGN: <lt_data>, <lw_data>.

        IF NOT lt_wm01 IS INITIAL.
          SORT lt_wm01 BY zlsxdt matnr budat DESCENDING.
          DATA(lt_tmp) = lt_wm01[].
          CLEAR: lt_wm01[], lt_wm01.

          LOOP AT lt_tmp INTO DATA(ls_tmp1).
            IF ls_tmp1-budat > lv_budat.
              lv_budat = ls_tmp1-budat.
            ENDIF.

            IF lv_matnr <> ls_tmp1-matnr.
              IF sy-tabix > 1.
                APPEND lt_wm01.
                CLEAR: lt_wm01.
              ENDIF.

              lv_matnr = ls_tmp1-matnr.
              MOVE-CORRESPONDING ls_tmp1 TO lt_wm01.
            ELSE.
              lt_wm01-bdmng = lt_wm01-bdmng + ls_tmp1-bdmng.
            ENDIF.

            AT END OF zlsxdt.
              ls_budat-zlsxdt = ls_tmp1-zlsxdt.
              ls_budat-budat = lv_budat.
              APPEND ls_budat TO lt_budat.
              CLEAR: ls_budat, lv_budat.
            ENDAT.
          ENDLOOP.

          IF NOT lt_wm01 IS INITIAL.
            APPEND lt_wm01.
          ENDIF.
        ENDIF.
      ENDIF.

      SORT gt_cbom2 BY zuutien zzlsx idnrk aufnr ASCENDING.
      DELETE gt_cbom2 WHERE matnr IS INITIAL AND idnrk IS INITIAL.

      SELECT matnr werks plifz
        INTO TABLE lt_marc
        FROM marc
        FOR ALL ENTRIES IN gt_cbom2
        WHERE marc~matnr = gt_cbom2-idnrk
          AND marc~werks = gt_cbom2-werks.

      LOOP AT lt_wm01 INTO DATA(ls_wm01).
        lv_bdmng = lv_bdmng + ls_wm01-bdmng.

        AT END OF matnr.
          LOOP AT gt_cbom2 ASSIGNING FIELD-SYMBOL(<fs_cbom2>) WHERE idnrk = ls_wm01-matnr.
            IF <fs_cbom2>-mtart_r IN lr_mtartg OR <fs_cbom2>-matkl_r IN lr_matklg.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbg IS INITIAL.
                <fs_cbom2>-sldbg = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbg = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF ( ( <fs_cbom2>-mtart_r IN lr_mtartv AND <fs_cbom2>-matkl_r NOT IN lr_matklvd ) OR <fs_cbom2>-matkl_r IN lr_matklv ).
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbv IS INITIAL.
                <fs_cbom2>-sldbv = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbv = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF <fs_cbom2>-matkl_r IN lr_matklb.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbb IS INITIAL.
                <fs_cbom2>-sldbb = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbb = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF <fs_cbom2>-matkl_r IN lr_matkls.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbs IS INITIAL.
                <fs_cbom2>-sldbs = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbs = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF <fs_cbom2>-mtart_r IN lr_mtartt OR <fs_cbom2>-matkl_r IN lr_matklt.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbt IS INITIAL.
                <fs_cbom2>-sldbt = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbt = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF <fs_cbom2>-mtart_r IN lr_mtarth AND <fs_cbom2>-matkl_r IN lr_matklh.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbh IS INITIAL.
                <fs_cbom2>-sldbh = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbh = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF ( <fs_cbom2>-mtart_r IN lr_mtartp AND <fs_cbom2>-matkl_r NOT IN lr_matklpd ) OR <fs_cbom2>-matkl_r IN lr_matklp.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbp IS INITIAL.
                <fs_cbom2>-sldbp = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbp = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ELSEIF <fs_cbom2>-matkl_r IN lr_matklk.
              IF lv_bdmng GE <fs_cbom2>-menge AND <fs_cbom2>-sldbk IS INITIAL.
                <fs_cbom2>-sldbk = <fs_cbom2>-menge.
                lv_bdmng = lv_bdmng - <fs_cbom2>-menge.
              ELSE.
                <fs_cbom2>-sldbk = lv_bdmng.
                CLEAR: lv_bdmng.
              ENDIF.
            ENDIF.

*            LOOP AT lt_mseg2 INTO DATA(ls_mseg2) WHERE aufnr = <fs_cbom2>-aufnr AND matnr = <fs_cbom2>-idnrk.
*              <fs_cbom2>-zttkx = <fs_cbom2>-zttkx + ls_mseg2-menge.   "(32)
*            ENDLOOP.

          ENDLOOP.
          CLEAR lv_bdmng.
        ENDAT.
      ENDLOOP.

      CLEAR: lv_zzlsx.
      LOOP AT gt_data2 ASSIGNING FIELD-SYMBOL(<fs_data2>).
        IF lv_zzlsx <> <fs_data2>-zzlsx.
          lv_zzlsx = <fs_data2>-zzlsx.
          REFRESH: gt_afko_t.
          gt_afko_t = gt_afko.
          DELETE gt_afko_t WHERE zzlsx NE <fs_data2>-zzlsx.
          DESCRIBE TABLE gt_afko_t LINES DATA(lv_lines).

          IF sy-tabix > 1.
            ls_ltime-zzlsx = <fs_data2>-zzlsx.
            APPEND ls_ltime TO lt_ltime.
            CLEAR: ls_ltime.
          ENDIF.
        ENDIF.

        IF lv_lines > 1.
          CLEAR <fs_data2>-gmein.
        ENDIF.

        <fs_data2>-tslkh = <fs_data2>-gamng.  "(7)
        <fs_data2>-tslnk = <fs_data2>-gwemg.  "(9)
        <fs_data2>-sltpcnk = <fs_data2>-tslkh - <fs_data2>-tslnk.   "(33)

        READ TABLE gt_uutien INTO DATA(ls_uutien2) WITH KEY lsxdt = <fs_data2>-zzlsx.
        IF sy-subrc IS INITIAL.
          <fs_data2>-zuutien = ls_uutien2-thutuuutien.
        ENDIF.

        CLEAR: lv_rqmng, lv_rqmnv, lv_rqmnb, lv_rqmns, lv_rqmnt, lv_rqmnp, lv_rqmnk, lv_rqmnh.
        lv_dbmng = lv_dbmnv = lv_dbmnb = lv_dbmnp = lv_dbmnk = lv_dbmns = lv_dbmnt = lv_dbmnh = 9999999999.

        LOOP AT gt_cbom2 INTO DATA(ls_cbom2) WHERE aufnr = <fs_data2>-aufnr.
          READ TABLE lt_marc INTO DATA(ls_marc) WITH KEY matnr = ls_cbom2-idnrk.
          IF ls_cbom2-mtart_r IN lr_mtartg OR ls_cbom2-matkl_r IN lr_matklg.
            lv_rqmng = 1.
            IF lv_dbmng <> 0..
              IF ls_cbom2-menge - ls_cbom2-sldbg < 1.
                lv_dbmng = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbg IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmng = 0.
              ELSE.
                lv_dbmng = ( ls_cbom2-sldbg / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbg IS INITIAL OR <fs_data2>-sldbg > lv_dbmng ).
              <fs_data2>-sldbg = lv_dbmng.
            ENDIF.

            IF ls_ltime-ltimg < ls_marc-plifz.
              ls_ltime-ltimg = ls_marc-plifz.
            ENDIF.
          ELSEIF ( ( ls_cbom2-mtart_r IN lr_mtartv AND ls_cbom2-matkl_r NOT IN lr_matklvd ) OR ls_cbom2-matkl_r IN lr_matklv ).
            lv_rqmnv = 1.
            IF lv_dbmnv <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbv < 1.
                lv_dbmnv = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbv IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnv = 0.
              ELSE.
                lv_dbmnv = ( ls_cbom2-sldbv / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbv IS INITIAL OR <fs_data2>-sldbv > lv_dbmnv ).
              <fs_data2>-sldbv = lv_dbmnv.
            ENDIF.

            IF ls_ltime-ltimv < ls_marc-plifz.
              ls_ltime-ltimv = ls_marc-plifz.
            ENDIF.
          ELSEIF ls_cbom2-matkl_r IN lr_matklb.
            lv_rqmnb = 1.
            IF lv_dbmnb <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbb < 1.
                lv_dbmnb = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbb IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnb = 0.
              ELSE.
                lv_dbmnb = ( ls_cbom2-sldbb / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbb IS INITIAL OR <fs_data2>-sldbb > lv_dbmnb ).
              <fs_data2>-sldbb = lv_dbmnb.
            ENDIF.

            IF ls_ltime-ltimb < ls_marc-plifz.
              ls_ltime-ltimb = ls_marc-plifz.
            ENDIF.
          ELSEIF ls_cbom2-matkl_r IN lr_matkls.
            lv_rqmns = 1.
            IF lv_dbmns <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbs < 1.
                lv_dbmns = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbs IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmns = 0.
              ELSE.
                lv_dbmns = ( ls_cbom2-sldbs / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbs IS INITIAL OR <fs_data2>-sldbs > lv_dbmns ).
              <fs_data2>-sldbs = lv_dbmns.
            ENDIF.

            IF ls_ltime-ltims < ls_marc-plifz.
              ls_ltime-ltims = ls_marc-plifz.
            ENDIF.
          ELSEIF ls_cbom2-mtart_r IN lr_mtartt OR ls_cbom2-matkl_r IN lr_matklt.
            lv_rqmnt = 1.
            IF lv_dbmnt <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbt < 1.
                lv_dbmnt = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbt IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnt = 0.
              ELSE.
                lv_dbmnt = ( ls_cbom2-sldbt / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbt IS INITIAL OR <fs_data2>-sldbt > lv_dbmnt ).
              <fs_data2>-sldbt = lv_dbmnt.
            ENDIF.

            IF ls_ltime-ltimt < ls_marc-plifz.
              ls_ltime-ltimt = ls_marc-plifz.
            ENDIF.
          ELSEIF ls_cbom2-mtart_r IN lr_mtarth AND ls_cbom2-matkl_r IN lr_matklh.
            lv_rqmnh = 1.
            IF lv_dbmnh <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbh < 1.
                lv_dbmnh = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbh IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnh = 0.
              ELSE.
                lv_dbmnh = ( ls_cbom2-sldbh / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbh IS INITIAL OR <fs_data2>-sldbh > lv_dbmnh ).
              <fs_data2>-sldbh = lv_dbmnh.
            ENDIF.

            IF ls_ltime-ltimh < ls_marc-plifz.
              ls_ltime-ltimh = ls_marc-plifz.
            ENDIF.
          ELSEIF ( ls_cbom2-mtart_r IN lr_mtartp AND ls_cbom2-matkl_r NOT IN lr_matklpd ) OR ls_cbom2-matkl_r IN lr_matklp.
            lv_rqmnp = 1.
            IF lv_dbmnp <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbp < 1.
                lv_dbmnp = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbp IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnp = 0.
              ELSE.
                lv_dbmnp = ( ls_cbom2-sldbp / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbp IS INITIAL OR <fs_data2>-sldbp > lv_dbmnp ).
              <fs_data2>-sldbp = lv_dbmnp.
            ENDIF.

            IF ls_ltime-ltimp < ls_marc-plifz.
              ls_ltime-ltimp = ls_marc-plifz.
            ENDIF.
          ELSEIF ls_cbom2-matkl_r IN lr_matklk.
            lv_rqmnk = 1.
            IF lv_dbmnk <> 0.
              IF ls_cbom2-menge - ls_cbom2-sldbk < 1.
                lv_dbmnk = <fs_data2>-gamng.
              ELSEIF ls_cbom2-sldbk IS INITIAL AND NOT ls_cbom2-menge IS INITIAL.
                lv_dbmnk = 0.
              ELSE.
                lv_dbmnk = ( ls_cbom2-sldbk / ls_cbom2-menge ) * <fs_data2>-gamng.
              ENDIF.
            ENDIF.

            IF ( <fs_data2>-sldbk IS INITIAL OR <fs_data2>-sldbk > lv_dbmnk ).
              <fs_data2>-sldbk = lv_dbmnk.
            ENDIF.

            IF ls_ltime-ltimk < ls_marc-plifz.
              ls_ltime-ltimk = ls_marc-plifz.
            ENDIF.
          ENDIF.
        ENDLOOP.

        IF lv_rqmng = 0. <fs_data2>-sldbg = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmnv = 0. <fs_data2>-sldbv = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmnb = 0. <fs_data2>-sldbb = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmns = 0. <fs_data2>-sldbs = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmnp = 0. <fs_data2>-sldbp = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmnk = 0. <fs_data2>-sldbk = <fs_data2>-tslkh. ENDIF.
        IF lv_rqmnh = 0. <fs_data2>-sldbh = <fs_data2>-tslkh. ENDIF.

      ENDLOOP.

****************************************************************************************************************************************

      SORT gt_data2 BY zzlsx matnr ASCENDING.
      lt_tmp2 = gt_data2.
      REFRESH: gt_data2.

      LOOP AT lt_tmp2 INTO ls_tmp2.
        IF ls_data2 IS INITIAL.
          MOVE-CORRESPONDING ls_tmp2 TO ls_data2.
          lv_gstrp = ls_data2-gstrp.
          lv_gltrp = ls_data2-gltrp.
        ELSE.

          ls_data2-tslkh   = ls_data2-tslkh + ls_tmp2-tslkh.
          ls_data2-tslnk   = ls_data2-tslnk + ls_tmp2-tslnk.
*          ls_data2-zttkx   = ls_data2-zttkx + ls_tmp2-zttkx.
          ls_data2-sltpcnk = ls_data2-sltpcnk + ls_tmp2-sltpcnk.

          ls_data2-sldbg = ls_data2-sldbg + ls_tmp2-sldbg.   "(11)
          ls_data2-sldbv = ls_data2-sldbv + ls_tmp2-sldbv.   "(12)
          ls_data2-sldbb = ls_data2-sldbb + ls_tmp2-sldbb.   "(13)
          ls_data2-sldbs = ls_data2-sldbs + ls_tmp2-sldbs.   "(14)
          ls_data2-sldbt = ls_data2-sldbt + ls_tmp2-sldbt.   "(15)
          ls_data2-sldbh = ls_data2-sldbh + ls_tmp2-sldbh.   "(16)
          ls_data2-sldbp = ls_data2-sldbp + ls_tmp2-sldbp.   "(17)
          ls_data2-sldbk = ls_data2-sldbk + ls_tmp2-sldbk.   "(17.1)

          IF lv_gstrp > ls_tmp2-gstrp.
            lv_gstrp = ls_tmp2-gstrp.
          ENDIF.

          IF lv_gltrp < ls_tmp2-gltrp.
            lv_gltrp = ls_tmp2-gltrp.
          ENDIF.
        ENDIF.

        AT END OF zzlsx.
          ls_data2-gstrp = lv_gstrp.
          ls_data2-gltrp = lv_gltrp.

          APPEND ls_data2 TO gt_data2.
          CLEAR: ls_data2.
        ENDAT.
      ENDLOOP.

      SORT gt_cbom2 BY zzlsx idnrk ASCENDING.
      LOOP AT gt_data2 INTO ls_data2.
        DATA(ls_alv2) = ls_data2.
**
*        ls_alv2-sldbb = ls_alv2-sldbb + ls_data2-sldbb.   "(13)
*        ls_alv2-sldbg = ls_alv2-sldbg + ls_data2-sldbg.   "(11)
*        ls_alv2-sldbv = ls_alv2-sldbv + ls_data2-sldbv.   "(12)
*        ls_alv2-sldbh = ls_alv2-sldbh + ls_data2-sldbh.   "(16)
*        ls_alv2-sldbp = ls_alv2-sldbp + ls_data2-sldbp.   "(17)
*        ls_alv2-sldbk = ls_alv2-sldbk + ls_data2-sldbk.   "(17.1)
*        ls_alv2-sldbt = ls_alv2-sldbt + ls_data2-sldbt.   "(15)
*        ls_alv2-sldbs = ls_alv2-sldbs + ls_data2-sldbs.   "(14)



*        CLEAR: ls_data2.
*        ls_data2 = ls_alv2.
        "Final code

        READ TABLE lt_ltime INTO ls_ltime WITH KEY zzlsx = ls_alv2-zzlsx.
        DELETE lt_mb52 WHERE lgort NOT IN lr_lgort[].
        LOOP AT gt_cbom2 INTO ls_cbom2 WHERE zzlsx = ls_data2-zzlsx. "AND matnr = ls_data2-matnr.
          ls_alv2-matnr_r    = ls_cbom2-idnrk.  "(18)
          ls_alv2-maktx_r    = ls_cbom2-maktx.  "(19)
          ls_alv2-einhe      = ls_cbom2-meins.  "(20)
          ls_alv2-slnvl_dm   = ls_alv2-slnvl_dm + ls_cbom2-menge.  "(21)
          ls_alv2-slnvl_261  = ls_alv2-slnvl_261 + ls_cbom2-wdmng.  "(22)
          ls_alv2-obmng      = ls_alv2-obmng + ls_cbom2-obmng.

          ls_alv2-dldbg      = ls_alv2-gltrp - 37.
          ls_alv2-dldbv      = ls_alv2-gltrp - 37.
          ls_alv2-dldbb      = ls_alv2-gltrp - 25.
          ls_alv2-dldbs      = ls_alv2-gltrp - 30.
          ls_alv2-dldbt      = ls_alv2-gltrp - 30.
          ls_alv2-dldbh      = ls_alv2-gltrp - 23.
          ls_alv2-dldbp      = ls_alv2-gltrp - 12.
          ls_alv2-dldbk      = ls_alv2-gltrp + 5.

*          IF ls_alv2-matgp IS INITIAL.
          IF ls_cbom2-mtart_r IN lr_mtartg OR ls_cbom2-matkl_r IN lr_matklg.
            ls_alv2-matgp = 'GỖ'.
            ls_alv2-ltime = ls_ltime-ltimg.
          ELSEIF ( ( ls_cbom2-mtart_r IN lr_mtartv AND ls_cbom2-matkl_r NOT IN lr_matklvd ) OR ls_cbom2-matkl_r IN lr_matklv ).
            ls_alv2-matgp = 'VÁN/ VER/ DDC/ KEO SX/ UV/CARTON TỔ ONG'.
            ls_alv2-ltime = ls_ltime-ltimv.
          ELSEIF ls_cbom2-matkl_r IN lr_matklb.
            ls_alv2-matgp = 'BTP/GC'.
            ls_alv2-ltime = ls_ltime-ltimb.
          ELSEIF ls_cbom2-matkl_r IN lr_matkls.
            ls_alv2-matgp = 'VẬT TƯ SOFA'.
            ls_alv2-ltime = ls_ltime-ltims.
          ELSEIF ls_cbom2-mtart_r IN lr_mtartt OR ls_cbom2-matkl_r IN lr_matklt.
            ls_alv2-matgp = 'VẬT TƯ'.
            ls_alv2-ltime = ls_ltime-ltimt.
          ELSEIF ls_cbom2-mtart_r IN lr_mtarth AND ls_cbom2-matkl_r IN lr_matklh.
            ls_alv2-matgp = 'HÓA CHẤT'.
            ls_alv2-ltime = ls_ltime-ltimh.
          ELSEIF ( ls_cbom2-mtart_r IN lr_mtartp AND ls_cbom2-matkl_r NOT IN lr_matklpd ) OR ls_cbom2-matkl_r IN lr_matklp.
            ls_alv2-matgp = 'BBPL'.
            ls_alv2-ltime = ls_ltime-ltimp.
          ELSEIF ls_cbom2-matkl_r IN lr_matklk.
            ls_alv2-matgp = 'KEO CT'.
            ls_alv2-ltime = ls_ltime-ltimk.
          ENDIF.
*          ENDIF.

          LOOP AT lt_mb52 ASSIGNING FIELD-SYMBOL(<fs_mb522>) WHERE matnr = ls_alv2-matnr_r AND pspnr = ls_cbom2-zzpspnr.
            <fs_mb522>-zused = 'X'.
            ls_alv2-slnvl_wbs = ls_alv2-slnvl_wbs + <fs_mb522>-labst. "(25) P1
          ENDLOOP.

          AT END OF idnrk.

            ls_alv2-sldbg = ls_alv2-sldbg + ls_alv2-slnvl_wbs.   "(11)
            ls_alv2-sldbv = ls_alv2-sldbv + ls_alv2-slnvl_wbs.   "(12)
            ls_alv2-sldbb = ls_alv2-sldbb + ls_alv2-slnvl_wbs.   "(13)
            ls_alv2-sldbs = ls_alv2-sldbs + ls_alv2-slnvl_wbs.   "(14)
            ls_alv2-sldbt = ls_alv2-sldbt + ls_alv2-slnvl_wbs.   "(15)
            ls_alv2-sldbh = ls_alv2-sldbh + ls_alv2-slnvl_wbs.   "(16)
            ls_alv2-sldbp = ls_alv2-sldbp + ls_alv2-slnvl_wbs.   "(17)
            ls_alv2-sldbk = ls_alv2-sldbk + ls_alv2-slnvl_wbs.   "(17.1)

            LOOP AT lt_wm01 INTO ls_wm01 WHERE zlsxdt = ls_alv2-zzlsx AND matnr = ls_alv2-matnr_r.
              ls_alv2-slnvl_ckx = ls_alv2-slnvl_ckx + ls_wm01-bdmng.  "(23)
            ENDLOOP.

            ls_alv2-slnvl_cck = ls_alv2-slnvl_dm - ls_alv2-slnvl_ckx. " - ls_alv2-zttkx.   "(24)


            ls_alv2-zcltk = ls_alv2-slnvl_cck - ls_alv2-slnvl_wbs.      "(26)

            LOOP AT lt_prpo INTO DATA(ls_prpo) WHERE zzlsx = ls_alv2-zzlsx AND matnr = ls_alv2-matnr_r.
*              IF ls_prpo-ebeln(1) = '4'.
              ls_alv2-slnvl_cnk = ls_alv2-slnvl_cnk + ls_prpo-menge_po - ls_prpo-wemng.   "(27)
              ls_alv2-slprcpo = ls_alv2-slprcpo  + ls_prpo-menge_pr - ls_prpo-menge_po.   "(28)
*              ELSEIF ls_prpo-ebeln IS INITIAL.
*                ls_alv2-slnvl_cnk = 0.
*                ls_alv2-slprcpo = ls_alv2-slprcpo  + ls_prpo-menge_pr.
*              ENDIF.
            ENDLOOP.

            ls_alv2-slcpr = ls_alv2-zcltk - ls_alv2-slnvl_cnk - ls_alv2-slprcpo.          "(29)

            IF ls_alv2-sldbp - ls_alv2-tslkh = 0.
              CLEAR: ls_wm01.
              READ TABLE lt_budat INTO ls_budat WITH KEY zlsxdt = ls_alv2-zzlsx.
              IF sy-subrc IS INITIAL.
                ls_alv2-budat = ls_budat-budat.      "(30)
              ENDIF.
            ENDIF.

            ls_alv2-zttkx     = ls_alv2-slnvl_cck - ls_alv2-slnvl_wbs.   "(32)

            "(33) GTCT
            READ TABLE lt_mbew INTO DATA(ls_mbew2) WITH KEY matnr = ls_alv2-matnr_r bwkey = ls_alv2-werks.
            IF sy-subrc IS INITIAL.
              IF ls_mbew2-vprsv = 'S'.
                ls_alv2-zgtct = ls_alv2-obmng * ls_mbew2-stprs.
              ELSE.
                ls_alv2-zgtct = ls_alv2-obmng * ls_mbew2-verpr.
              ENDIF.
            ENDIF.

            " New logic 241219
            IF ls_alv2-sldbg GE ls_alv2-tslkh.
              ls_alv2-sldbgt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbg = 0.
              ls_alv2-sldbgt = '0'.
            ELSE.
*              DATA(lv_sldbg) = ls_alv2-sldbg * 1000.
              ls_alv2-sldbgt = ls_alv2-sldbg.
              REPLACE '.' IN ls_alv2-sldbgt WITH ','.
            ENDIF.

            IF ls_alv2-sldbv GE ls_alv2-tslkh.
              ls_alv2-sldbvt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbv = 0.
              ls_alv2-sldbvt = '0'.
            ELSE.
*              DATA(lv_sldbv) = ls_alv2-sldbv * 1000.
              ls_alv2-sldbvt = ls_alv2-sldbv.
              REPLACE '.' IN ls_alv2-sldbvt WITH ','.
            ENDIF.

            IF ls_alv2-sldbb GE ls_alv2-tslkh.
              ls_alv2-sldbbt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbb = 0.
              ls_alv2-sldbbt = '0'.
            ELSE.
*              DATA(lv_sldbb) = ls_alv2-sldbb * 1000.
              ls_alv2-sldbbt = ls_alv2-sldbb.
              REPLACE '.' IN ls_alv2-sldbbt WITH ','.
            ENDIF.

            IF ls_alv2-sldbs GE ls_alv2-tslkh.
              ls_alv2-sldbst = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbs = 0.
              ls_alv2-sldbst = '0'.
            ELSE.
*              DATA(lv_sldbs) = ls_alv2-sldbs * 1000.
              ls_alv2-sldbst = ls_alv2-sldbs.
              REPLACE '.' IN ls_alv2-sldbst WITH ','.
            ENDIF.

            IF ls_alv2-sldbh GE ls_alv2-tslkh.
              ls_alv2-sldbht = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbh = 0.
              ls_alv2-sldbht = '0'.
            ELSE.
*              DATA(lv_sldbh) = ls_alv2-sldbh * 1000.
              ls_alv2-sldbht = ls_alv2-sldbh.
              REPLACE '.' IN ls_alv2-sldbht WITH ','.
            ENDIF.

            IF ls_alv2-sldbt  GE ls_alv2-tslkh.
              ls_alv2-sldbtt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbt = 0.
              ls_alv2-sldbtt = '0'.
            ELSE.
*              DATA(lv_sldbt) = ls_alv2-sldbt * 1000.
              ls_alv2-sldbtt = ls_alv2-sldbt.
              REPLACE '.' IN ls_alv2-sldbtt WITH ','.
            ENDIF.

            IF ls_alv2-sldbp GE ls_alv2-tslkh.
              ls_alv2-sldbpt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbp = 0.
              ls_alv2-sldbpt = '0'.
            ELSE.
*              DATA(lv_sldbp) = ls_alv2-sldbp * 1000.
              ls_alv2-sldbpt = ls_alv2-sldbp.
              REPLACE '.' IN ls_alv2-sldbpt WITH ','.
            ENDIF.

            IF ls_alv2-sldbk GE ls_alv2-tslkh.
              ls_alv2-sldbkt = 'Done'. " - ' ls_alv2-budat+6(2) '/' ls_alv2-budat+4(2) '/' ls_alv2-budat(4) INTO ls_alv2-sldbgt.
            ELSEIF ls_alv2-sldbk = 0.
              ls_alv2-sldbkt = '0'.
            ELSE.
*              DATA(lv_sldbk) = ls_alv2-sldbk * 1000.
              ls_alv2-sldbkt = ls_alv2-sldbk.
              REPLACE '.' IN ls_alv2-sldbkt WITH ','.
            ENDIF.

            APPEND ls_alv2 TO gt_alv2.
            CLEAR: ls_alv2.
            ls_alv2 = ls_data2.
          ENDAT.
        ENDLOOP.
      ENDLOOP.


    ENDIF.
  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form DISPLAY_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM display_data.
  DATA:
    lt_fcat   TYPE lvc_t_fcat,
    ls_layout TYPE lvc_s_layo.

  PERFORM build_catalog  CHANGING lt_fcat.

*  ls_layout-cwidth_opt = 'X'.
  ls_layout-info_fname = 'LINE_COLOR'.

  IF NOT p_ct IS INITIAL.
    CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
      EXPORTING
        i_callback_program          = sy-repid
        i_callback_pf_status_set    = 'SET_PF_STATUS'
*       i_callback_user_command     = 'HANDLE_USER_COMMAND'
        i_callback_html_top_of_page = 'HTML_TOP_OF_PAGE'
        i_html_height_top           = 13
        is_layout_lvc               = ls_layout
        it_fieldcat_lvc             = lt_fcat[]
        i_save                      = 'A'
      TABLES
        t_outtab                    = gt_alv
      EXCEPTIONS
        program_error               = 1
        OTHERS                      = 2.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid
            TYPE 'I'
          NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ELSE.
    IF p_sb IS INITIAL.
      CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
        EXPORTING
          i_callback_program          = sy-repid
          i_callback_pf_status_set    = 'SET_PF_STATUS'
*         i_callback_user_command     = 'HANDLE_USER_COMMAND'
          i_callback_html_top_of_page = 'HTML_TOP_OF_PAGE'
          i_html_height_top           = 13
          is_layout_lvc               = ls_layout
          it_fieldcat_lvc             = lt_fcat[]
          i_save                      = 'A'
        TABLES
          t_outtab                    = gt_alv2
        EXCEPTIONS
          program_error               = 1
          OTHERS                      = 2.
      IF sy-subrc <> 0.
        MESSAGE ID sy-msgid
              TYPE 'I'
            NUMBER sy-msgno
              WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
        LEAVE LIST-PROCESSING.
      ENDIF.
    ELSE.
      EXPORT gt_alv2 TO MEMORY ID 'ZPPR02'.
    ENDIF.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form BUILD_CATALOG
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LT_FCAT
*&---------------------------------------------------------------------*
FORM build_catalog  CHANGING ct_fcat TYPE lvc_t_fcat.
  DATA: lv_struct TYPE dd02l-tabname.
  REFRESH ct_fcat.

  IF NOT p_ct IS INITIAL.
    lv_struct = 'ZST_PPR02_1'.
  ELSE.
    lv_struct = 'ZST_PPR02_2'.
  ENDIF.

  CALL FUNCTION 'LVC_FIELDCATALOG_MERGE'
    EXPORTING
      i_structure_name       = lv_struct
*     i_bypassing_buffer     = 'X'
    CHANGING
      ct_fieldcat            = ct_fcat
    EXCEPTIONS
      inconsistent_interface = 1
      program_error          = 2
      OTHERS                 = 3.


  LOOP AT ct_fcat ASSIGNING FIELD-SYMBOL(<fs_fcat>).
    <fs_fcat>-col_opt = 'X'.
    CASE <fs_fcat>-fieldname.
      WHEN 'TPCNK'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SLTP chưa nhập kho'.

      WHEN 'WIPTT'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'WIP thực tế'.

      WHEN 'WIPKH'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'WIP KH'.

      WHEN 'ZGTTT'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Giá trị Thừa / thiếu so với định mức'.

      WHEN 'ZSLTT'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL Thừa / thiếu so với định mức'.

      WHEN 'SLNVL_ALL'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Tồn tất cả các kho (gồm tồn trơn)'.

      WHEN 'SLNVL_WBS'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Đang có tồn kho theo WBS (Không gồm kho xưởng)'.

      WHEN 'SLNVL_261'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số Lượng NVL đã cấp 261'.

      WHEN 'SLNVL_DM'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số Lượng NVL Theo Định Mức'.

      WHEN 'SLNVL_CKX'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số Lượng NVL đã chuyển kho xưởng'.

      WHEN 'SLNVL_CCK'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Còn lại chưa chuyển kho kho xưởng'.

      WHEN 'MATNR_R'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Component trong BOM'.

      WHEN 'MAKTX_R'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Diễn giải mã component'.

      WHEN 'TSLKH'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Tổng SLKH'.

      WHEN 'TSLNK'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Tổng SL đã NK'.

      WHEN 'SLDBGT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_Gỗ'.
      WHEN 'DLDBG'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_Gỗ'.
      WHEN 'SLDBVT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_VÁN/ VER/ DDC/ KEO SX/ UV/CARTON TỔ ONG'.
      WHEN 'DLDBV'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_VÁN/ VER/ DDC/ KEO SX/ UV'.
      WHEN 'SLDBBT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_BTP/GC'.
      WHEN 'DLDBB'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_BTP/GC'.
      WHEN 'SLDBPT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_BBPL'.
      WHEN 'DLDBP'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_BBPL'.
      WHEN 'SLDBTT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_VẬT TƯ'.
      WHEN 'DLDBT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_VẬT TƯ'.
      WHEN 'SLDBKT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_KEO CT'.
      WHEN 'DLDBK'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_KEO CT'.
      WHEN 'SLDBHT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_HÓA CHẤT'.
      WHEN 'DLDBH'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_HÓA CHẤT'.
      WHEN 'SLDBST'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL sản phẩm đồng bộ_VẬT TƯ SOFA'.
      WHEN 'DLDBS'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Deadline SP đồng bộ_VẬT TƯ SOFA'.
      WHEN 'MATNR'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Mã Thành Phẩm'.
        IF NOT p_dt IS INITIAL.
          <fs_fcat>-tech = 'X'.
        ENDIF.
      WHEN 'MAKTX'.
        <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Tên Mã Thành Phẩm'.
        IF NOT p_dt IS INITIAL.
          <fs_fcat>-tech = 'X'.
        ENDIF.
      WHEN 'GMEIN'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'ĐVT'.
      WHEN 'GAMNG'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số lượng TPKH'.
      WHEN 'GWEMG'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số lượng TPNK'.
      WHEN 'GSTRP'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Ngày bắt đầu LSX'.
      WHEN 'GLTRP'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Ngày kết thúc LSX'.
      WHEN 'ZCLTK'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Thừa / thiếu so với tồn kho'.
      WHEN 'SLNVL_CNK'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Số lượng còn lại chưa nhập kho theo PO (PO đã kéo) '.
      WHEN 'SLPRCPO'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL PR chưa kéo PO '.
      WHEN 'SLCPR'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'SL chưa kéo PR'.
      WHEN 'BUDAT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Ngày đồng bộ TT (chuyển xưởng)'.
      WHEN 'ZGTCT'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Giá trị còn thiếu'.
      WHEN 'ZTTKX'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Trừ tồn kho xưởng'.
      WHEN 'SLTPCNK'. <fs_fcat>-scrtext_s = <fs_fcat>-scrtext_m = <fs_fcat>-scrtext_l = 'Tổng SLTP chưa NK'.

      WHEN 'MENGE' OR 'OBMNG' OR 'WAERS' OR 'PREIS' OR 'LABST' OR 'VERID' OR 'STLAN' OR 'STLAL' OR 'STLNR' OR 'KUNNR' OR 'KUNAG' OR 'CNAME'
        OR 'SLDBG' OR 'SLDBV' OR 'SLDBB' OR 'SLDBS' OR 'SLDBT' OR 'SLDBH' OR 'SLDBP' OR 'SLDBK' OR 'AUFNR' OR 'ZZPSPNR' OR 'PSPNR' OR 'PROJN'.
*        IF NOT p_ct IS INITIAL.
        <fs_fcat>-tech = 'X'.
*        ENDIF.
*      WHEN 'MENGE' OR 'OBMNG' OR 'WAERS' OR 'PREIS' OR 'LABST' OR 'VERID' OR 'STLAN' OR 'STLAL' OR 'STLNR' OR 'KUNNR' OR 'KUNAG' OR 'CNAME'
*        OR 'AUFNR' OR 'ZZPSPNR' OR 'PSPNR' OR 'PROJN'.
*        IF  NOT p_dt IS INITIAL.
*          <fs_fcat>-tech = 'X'.
*        ENDIF.
    ENDCASE.
  ENDLOOP.
ENDFORM.

FORM html_top_of_page USING top TYPE REF TO cl_dd_document ##CALLED.
  DATA:
    lo_doctable TYPE REF TO cl_dd_table_element,
    lo_docarea  TYPE REF TO cl_dd_table_area,
    lo_column1  TYPE REF TO cl_dd_area.

  DATA: lv_temp TYPE char10,
        lv_name TYPE char255,
        lv_hdr  TYPE char255,
        lv_addr TYPE char255,
        lv_from TYPE char10,
        lv_to   TYPE char10,
        lv_date TYPE char255.

  CLEAR: lv_name,
         lv_addr.

  WRITE '1000'  TO lv_temp.
  CALL FUNCTION 'ZCORE_FM_GET_PROFILE'
    EXPORTING
      i_number  = lv_temp
      i_kind    = 'C'
    IMPORTING
      e_name    = lv_name
      e_address = lv_addr.

* Tạo bảng chứa dòng tên và địa chỉ
  CALL METHOD top->add_table
    EXPORTING
      no_of_columns               = 1
      border                      = '0'
      cell_background_transparent = 'X'
      width                       = '100%'
    IMPORTING
      table                       = lo_doctable
      tablearea                   = lo_docarea.
*  Thêm cột vào bảng
  CALL METHOD lo_doctable->add_column
    IMPORTING
      column = lo_column1.

* Chỉnh thuộc tính của cột
  CALL METHOD lo_column1->set_area_style
    EXPORTING
      sap_fontsize  = '10'
      sap_fontstyle = 'Arial'.

  CALL METHOD lo_column1->add_text
    EXPORTING
      text = lv_name.
  CALL METHOD lo_column1->new_line.
  CALL METHOD lo_column1->add_text
    EXPORTING
      text = lv_addr.


* Tạo bảng chứa tên báo cáo và ngày tháng
  CALL METHOD top->add_table
    EXPORTING
      no_of_columns               = 1
      border                      = '0'
      cell_background_transparent = 'X'
      width                       = '100%'
    IMPORTING
      table                       = lo_doctable
      tablearea                   = lo_docarea.

*  Thêm cột vào bảng
  CALL METHOD lo_doctable->add_column
    IMPORTING
      column = lo_column1.

  CALL METHOD lo_doctable->set_column_style
    EXPORTING
      col_no        = 1
      sap_align     = 'Center'
      sap_fontstyle = 'Arial'.

  CALL METHOD lo_column1->new_line.
  CALL METHOD lo_column1->add_text
    EXPORTING
      text         = TEXT-hd1
      sap_fontsize = '16'
      sap_style    = cl_dd_area=>heading.

ENDFORM .                    "FORM_TOP_OF_PAGE

*&---------------------------------------------------------------------*
*&      Form  SET_PF_STATUS
*&---------------------------------------------------------------------*
FORM set_pf_status USING rt_extab TYPE slis_t_extab.
  SET PF-STATUS 'ZSTANDARD_FULLSCREEN' EXCLUDING iexcluding.
ENDFORM.