// class GetQuantityByPO {
//   int? code;
//   bool? isSuccess;
//   String? message;
//   List<DataGetQuantityByP0>? data;
//
//
//   GetQuantityByPO(
//       {this.code,
//         this.isSuccess,
//         this.message,
//         this.data});
//
//   GetQuantityByPO.fromJson(Map<String, dynamic> json) {
//     code = json['code'];
//     isSuccess = json['isSuccess'];
//     message = json['message'];
//     if (json['data'] != null) {
//       data = <DataGetQuantityByP0>[];
//       json['data'].forEach((v) {
//         data!.add(DataGetQuantityByP0.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['code'] = code;
//     data['isSuccess'] = isSuccess;
//     data['message'] = message;
//     if (this.data != null) {
//       data['data'] = this.data!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
//
// class DataGetQuantityByP0 {
//   String? po;
//   String? poLine;
//   double? quantityByPO;
//   double? quantityReceived;
//   String? unit;
//
//   DataGetQuantityByP0(
//       {this.po,
//         this.poLine,
//         this.quantityByPO,
//         this.quantityReceived,
//         this.unit});
//
//   DataGetQuantityByP0.fromJson(Map<String, dynamic> json) {
//     po = json['po'];
//     poLine = json['poLine'];
//     quantityByPO = json['quantityByPO'];
//     quantityReceived = json['quantityReceived'];
//     unit = json['unit'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['po'] = po;
//     data['poLine'] = poLine;
//     data['quantityByPO'] = quantityByPO;
//     data['quantityReceived'] = quantityReceived;
//     data['unit'] = unit;
//     return data;
//   }
// }