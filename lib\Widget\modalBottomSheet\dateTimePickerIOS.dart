import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DateTimePickerIOS extends StatefulWidget {
  const DateTimePickerIOS({Key? key}) : super(key: key);

  @override
  State<DateTimePickerIOS> createState() => _DateTimePickerIOSState();
}

class _DateTimePickerIOSState extends State<DateTimePickerIOS> {
  DateTime? _todateComplete;

  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: [
        SizedBox(
            height: 250.h,
            child: CupertinoDatePicker(
                initialDateTime: DateTime.now(),
                mode: CupertinoDatePickerMode.dateAndTime,
                onDateTimeChanged: (val) {
                  setState(() {
                    _todateComplete = val;
                  });
                })),
      ],
      cancelButton: CupertinoButton(
        child: const Text('OK'),
        onPressed: () =>
            Navigator.pop(context, _todateComplete ?? DateTime.now()),
      ),
    );
  }
}

