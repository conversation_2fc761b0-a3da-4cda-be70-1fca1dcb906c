import 'package:flutter/foundation.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';
import 'package:http/http.dart' as http;

class GetCodeNameProductFTTran {
  static Future<http.Response> getCodeName(String valueSearch, String token) async {
    Map<String, dynamic> data = {"ProductCode": valueSearch};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlCommon}GetProduct?ProductCode=${Uri.encodeQueryComponent(data['ProductCode'].toString())}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getCodeNameDvt(String valueSearch, String token) async {
    Map<String, dynamic> data = {"ProductCode": valueSearch};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlCommon}GetProductDvt?ProductCode=${Uri.encodeQueryComponent(data['ProductCode'].toString())}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getPO(String valueSearch, String token) async {
    Map<String, dynamic> data = {"PO": valueSearch};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlCommon}GetPO?PO=${Uri.encodeQueryComponent(data['PO'].toString())}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getPODetails(String valueSearch, String token) async {
    Map<String, dynamic> data = {"PO": valueSearch};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlCommon}GetPODetails?PO=${Uri.encodeQueryComponent(data['PO'].toString())}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
