# Machine Maintenance Feature Request

## 1. Feature Overview

### Basic Information
- Feature Name: Machine Maintenance
- Purpose: Track and manage preventive and corrective maintenance activities for manufacturing equipment
- Target Users: Maintenance Engineers, Supervisors, Production Operators

### Core Functionality
Please implement the following core functions:
- List view of all maintenance records with pagination and filtering
- Detail view for creating/editing maintenance records
- Maintenance schedule calendar view
- Integration with machine master data and employee data
- Maintenance history tracking
- File attachments for maintenance reports

## 2. Technical Requirements

### Backend (.NET Core API)
Please create:
1. API Controllers with these endpoints:
   - GET /MaintenanceList (with pagination and filters)
   - GET /Maintenance/{id}
   - POST /Maintenance (create new)
   - PUT /Maintenance/{id} (update)
   - GET /MaintenanceSchedule (calendar view data)
   - POST /MaintenanceAttachment (file upload)
   - GET /MaintenanceStatistics (dashboard data)

2. Data Models with these key fields:
   ```csharp
   public class MaintenanceRecord
   {
       public string Id { get; set; }
       public string MachineId { get; set; }
       public string MaintenanceType { get; set; } // Preventive/Corrective
       public DateTime ScheduledDate { get; set; }
       public DateTime? StartTime { get; set; }
       public DateTime? EndTime { get; set; }
       public string Status { get; set; } // Scheduled/In Progress/Completed/Cancelled
       public string AssignedTechnician { get; set; }
       public string Description { get; set; }
       public string Priority { get; set; } // High/Medium/Low
       public List<string> AttachmentUrls { get; set; }
       public string CompanyCode { get; set; }
       public DateTime CreatedDate { get; set; }
       public string CreatedBy { get; set; }
       public DateTime? ModifiedDate { get; set; }
       public string ModifiedBy { get; set; }
   }
   ```

3. Repository layer with:
   - CRUD operations for maintenance records
   - Schedule conflict checking
   - Machine history queries
   - Statistical data queries
   - File storage operations

### Mobile App (Flutter)
Please create:
1. List Page (MaintenanceList) with:
   - Paginated list view (20 items per page)
   - Pull-to-refresh functionality
   - Search by machine ID/name
   - Filter by date range, status, type
   - Quick status update capability
   - Offline support with local storage
   - Error handling and retry mechanisms

2. Detail Page (MaintenanceDetail) with:
   - Form validation for required fields
   - Machine selection from master data
   - Technician assignment from employee list
   - Date/time pickers for schedule
   - File attachment handling
   - Status update with confirmation
   - Offline draft saving
   - Error handling and validation feedback

3. Calendar Page (MaintenanceSchedule) with:
   - Monthly/weekly view options
   - Color-coded maintenance types
   - Quick view of maintenance details
   - Schedule conflict detection
   - Drag-drop schedule adjustment (if authorized)

## 3. Technical Specifications

### API Structure
- Base URL: /api/v1/MES/Maintenance
- Response Format:
  ```json
  {
    "Code": 200,
    "IsSuccess": true,
    "Message": "Success",
    "Data": {
      // Response data here
    }
  }
  ```
- Authentication: JWT Token in Authorization header
- Error Handling: Standard HTTP status codes with detailed messages

### Mobile Implementation
- State Management: Local state with StatefulWidget
- Data Persistence: 
  - SQLite for offline data
  - Secure storage for credentials
  - File storage for attachments
- UI Components: 
  - Material Design 3
  - Custom calendar widget
  - File picker integration
  - Image preview capability

### Required Dependencies
Backend:
- EntityFramework Core 6.0+
- AutoMapper 12.0+
- Azure Blob Storage (for attachments)
- Hangfire (for scheduling)

Mobile:
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^0.13.0
  intl: ^0.17.0
  connectivity_plus: ^2.0.0
  sqflite: ^2.0.0
  file_picker: ^4.0.0
  table_calendar: ^3.0.0
  image_picker: ^0.8.0
  cached_network_image: ^3.0.0
  flutter_secure_storage: ^5.0.0
```

## 4. Additional Requirements

### Security
- Role-based access control:
  - MAINTENANCE_VIEW: View records
  - MAINTENANCE_CREATE: Create new records
  - MAINTENANCE_EDIT: Edit existing records
  - MAINTENANCE_ADMIN: Full access including deletion
- Data validation:
  - Required fields validation
  - Date range validation
  - Schedule conflict checking
  - File type and size validation
- Secure storage:
  - Encrypted local storage
  - Secure file handling
  - Token management

### Performance
- Pagination: 20 items per page
- Caching:
  - Master data cached for 30 minutes
  - Image caching with CachedNetworkImage
  - API response caching
- Lazy loading:
  - Image thumbnails
  - Historical data
  - File attachments
- Request optimization:
  - Debounce search (500ms)
  - Batch updates
  - Compressed file upload

### Error Handling
- Network errors:
  - Offline mode activation
  - Auto-retry mechanism
  - Background sync
- Validation errors:
  - Field-level error messages
  - Form validation summary
  - Status update restrictions
- Session handling:
  - Auto refresh token
  - Session timeout handling
  - Force logout on security breach

### UI/UX Requirements
- Responsive design:
  - Support for various screen sizes
  - Landscape/portrait adaptation
  - Split view on tablets
- Loading states:
  - Shimmer loading effect
  - Progress indicators
  - Upload progress
- Error feedback:
  - Toast messages
  - Error banners
  - Validation highlights
- Confirmation dialogs:
  - Status changes
  - Delete operations
  - Unsaved changes

## 5. Testing Requirements
- Unit tests:
  - Model validation
  - Business logic
  - Data transformation
  - Offline storage
- Widget tests:
  - Form validation
  - Calendar interactions
  - File handling
  - Error states
- Integration tests:
  - API endpoints
  - Offline/online switching
  - File upload/download
  - Push notifications

## 6. Documentation Requirements
Please provide:
- API documentation:
  - Endpoint specifications
  - Request/response examples
  - Error codes and handling
- Technical documentation:
  - Architecture overview
  - Data flow diagrams
  - State management
  - Offline capabilities
- User documentation:
  - Feature guides
  - Error resolution
  - Best practices
- Code documentation:
  - Class/method documentation
  - Complex logic explanation
  - Configuration guide