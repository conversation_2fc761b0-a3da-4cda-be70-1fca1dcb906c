﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskProductUsualErrorModel", Schema = "Task")]
    public partial class TaskProductUsualErrorModel
    {
        [Key]
        public Guid TaskProductUsualErrorId { get; set; }
        public Guid? TaskProductId { get; set; }
        [StringLength(50)]
        public string UsualErrorCode { get; set; }

        [ForeignKey("TaskProductId")]
        [InverseProperty("TaskProductUsualErrorModel")]
        public virtual TaskProductModel TaskProduct { get; set; }
    }
}