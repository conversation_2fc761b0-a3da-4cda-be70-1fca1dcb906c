import 'dart:convert';
import 'package:flutter/material.dart';
import '../Storage/storageSecureStorage.dart';
import '../model/userModel.dart';

/// Utility class for accessing user data across the application
class UserHelper {
  /// Singleton instance
  static final UserHelper _instance = UserHelper._internal();

  /// Private constructor for singleton
  UserHelper._internal();

  /// Factory constructor
  factory UserHelper() {
    return _instance;
  }

  /// Cache for user data to avoid repeated SecureStorage calls
  UserModel? _cachedUserModel;

  /// Cache for routing-enabled company codes
  List<String>? _cachedRoutingEnabledCompanies;

  /// Default routing-enabled company codes (fallback)
  static const List<String> _defaultRoutingEnabledCompanies = ['1000', '1100', '1010', '1200'];

  /// Gets the complete UserModel from secure storage
  /// Returns null if user data doesn't exist or is invalid
  Future<UserModel?> getUserModel() async {
    try {
      if (_cachedUserModel != null) {
        return _cachedUserModel;
      }

      final userString = await SecureStorage.getString("user", null);
      if (userString == null || userString.isEmpty) {
        return null;
      }

      final userData = jsonDecode(userString);
      final userModel = UserModel.fromJson(userData);

      // Cache for future use
      _cachedUserModel = userModel;

      return userModel;
    } catch (e) {
      debugPrint("Error retrieving user data: $e");
      return null;
    }
  }

  /// Gets the user data from the UserModel
  Future<DataUser?> getUserData() async {
    final userModel = await getUserModel();
    return userModel?.data;
  }

  /// Gets the company code from user data
  /// Returns null if not found
  Future<String?> getCompanyCode() async {
    try {
      // Try to get selectedCompany from SaveAccountLogin first
      final saveAccountLoginString = await SecureStorage.getString("saveAccountLogin", null);
      if (saveAccountLoginString != null && saveAccountLoginString.isNotEmpty) {
        try {
          final saveAccountLogin = jsonDecode(saveAccountLoginString);
          if (saveAccountLogin != null && saveAccountLogin['selectedCompany'] != null && saveAccountLogin['selectedCompany']['companyCode'] != null) {
            final selectedCompanyCode = saveAccountLogin['selectedCompany']['companyCode'];
            if (selectedCompanyCode != null && selectedCompanyCode.toString().isNotEmpty && selectedCompanyCode.toString() != ' ') {
              debugPrint("Using selectedCompany from SaveAccountLogin: $selectedCompanyCode");
              return selectedCompanyCode.toString();
            }
          }
        } catch (e) {
          debugPrint("Error parsing SaveAccountLogin data: $e");
        }
      }

      // Fall back to userData if SaveAccountLogin doesn't have a valid selectedCompany
      final userData = await getUserData();
      final companyCode = userData?.companyCode;

      if (companyCode != null && companyCode.isNotEmpty) {
        debugPrint("Using companyCode from UserData: $companyCode");
        return companyCode;
      }

      return null;
    } catch (e) {
      debugPrint("Error retrieving company code: $e");
      return null;
    }
  }

  /// Gets the user's full name
  Future<String?> getUserFullName() async {
    final userData = await getUserData();
    return userData?.fullName;
  }

  /// Gets the user's ID
  Future<String?> getUserId() async {
    final userData = await getUserData();
    return userData?.accountId;
  }

  /// Gets the user's username
  Future<String?> getUsername() async {
    final userData = await getUserData();
    return userData?.userName;
  }

  /// Clears the cached user data
  /// Call this when logging out or when user data changes
  void clearCache() {
    _cachedUserModel = null;
    _cachedRoutingEnabledCompanies = null;
  }

  /// Get the list of routing-enabled company codes from SecureStorage
  /// Returns the default list if not found
  Future<List<String>> getRoutingEnabledCompanyCodes() async {
    try {
      // Return cached value if available
      if (_cachedRoutingEnabledCompanies != null) {
        return _cachedRoutingEnabledCompanies!;
      }

      DataUser? user = await getUserData();

      if (user?.routingEnabledCompanyCodes != null && user!.routingEnabledCompanyCodes!.isNotEmpty) {
        try {
          final routingCodes = List<String>.from(user!.routingEnabledCompanyCodes!);
          _cachedRoutingEnabledCompanies = routingCodes;
          debugPrint("Loaded routing-enabled company codes from storage: $routingCodes");
          return routingCodes;
        } catch (e) {
          debugPrint("Error parsing routing company codes: $e");
        }
      }

      // Return default list if not found
      debugPrint("Using default routing-enabled company codes");
      return _defaultRoutingEnabledCompanies;
    } catch (e) {
      debugPrint("Error retrieving routing-enabled company codes: $e");
      return _defaultRoutingEnabledCompanies;
    }
  }

  /// Save the list of routing-enabled company codes to SecureStorage
  /// This should be called during login after receiving the data from the server
  static Future<void> saveRoutingEnabledCompanyCodes(List<String> companyCodes) async {
    try {
      final codesJson = jsonEncode(companyCodes);
      await SecureStorage.setString("routingEnabledCompanyCodes", codesJson, null);
      debugPrint("Saved routing-enabled company codes to storage: $companyCodes");

      // Update the singleton instance's cache
      UserHelper()._cachedRoutingEnabledCompanies = companyCodes;
    } catch (e) {
      debugPrint("Error saving routing-enabled company codes: $e");
    }
  }

  /// Determine if the company code is enabled for the routing feature
  /// Returns true if the company code is in the list of routing-enabled company codes
  Future<bool> isRoutingEnabledForCurrentCompany() async {
    final companyCode = await getCompanyCode();
    debugPrint("Checking if routing is enabled for company code: $companyCode");

    if (companyCode == null) return false;

    final routingEnabledCompanyCodes = await getRoutingEnabledCompanyCodes();
    return routingEnabledCompanyCodes.contains(companyCode);
  }

  /// Determine if a specific company code is enabled for the routing feature
  /// Returns true if the company code is in the list of routing-enabled company codes
  Future<bool> isRoutingEnabledForCompanyCode(String? companyCode) async {
    if (companyCode == null) return false;

    final routingEnabledCompanyCodes = await getRoutingEnabledCompanyCodes();
    return routingEnabledCompanyCodes.contains(companyCode);
  }
}
