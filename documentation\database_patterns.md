# Database Patterns and Standards

## 1. Table Naming Conventions
```yaml
naming_rules:
  tables:
    - "PascalCase"
    - "Plural form"
    - "Prefix with module name (e.g., MES_)"
  
  columns:
    - "PascalCase"
    - "Descriptive names"
    - "No abbreviations"
  
  constraints:
    - "PK_{TableName}"
    - "FK_{TableName}_{ReferencedTable}"
    - "UQ_{TableName}_{Columns}"
    - "IX_{TableName}_{Columns}"

examples:
  tables:
    - "MES_Downtimes"
    - "MES_Machines"
    - "MES_MaintenanceRecords"
  
  columns:
    - "Id"
    - "CreatedDate"
    - "CompanyCode"
```

## 2. Common Fields Pattern
```sql
-- Base fields for all tables
CREATE TABLE [dbo].[MES_BaseTable]
(
    [Id] NVARCHAR(50) NOT NULL,
    [CompanyCode] NVARCHAR(10) NOT NULL,
    [CreatedDate] DATETIME NOT NULL,
    [CreatedBy] NVARCHAR(50) NOT NULL,
    [ModifiedDate] DATETIME NULL,
    [ModifiedBy] NVARCHAR(50) NULL,
    CONSTRAINT [PK_BaseTable] PRIMARY KEY ([Id])
)
```

## 3. Data Types Standards
```yaml
string_fields:
  id: "NVARCHAR(50)"
  code: "NVARCHAR(20)"
  name: "NVARCHAR(100)"
  description: "NVARCHAR(500)"
  company_code: "NVARCHAR(10)"
  user_id: "NVARCHAR(50)"

date_fields:
  dates: "DATE"
  timestamps: "DATETIME"
  precise_timestamps: "DATETIME2"

numeric_fields:
  integers: "INT"
  large_numbers: "BIGINT"
  decimals: "DECIMAL(18,6)"
  money: "DECIMAL(18,2)"

other_fields:
  boolean: "BIT"
  status: "NVARCHAR(20)"
  json_data: "NVARCHAR(MAX)"
```

## 4. Index Design Pattern
```sql
-- Primary Key
CONSTRAINT [PK_TableName] PRIMARY KEY ([Id])

-- Foreign Keys with Index
CREATE INDEX [IX_TableName_ForeignKey] ON [TableName] ([ForeignKeyId])

-- Composite Index for Filtering
CREATE INDEX [IX_TableName_Filter] ON [TableName] 
(
    [CompanyCode],
    [Status],
    [CreatedDate]
)

-- Include Columns for Performance
CREATE INDEX [IX_TableName_Search] ON [TableName] 
(
    [SearchField]
)
INCLUDE 
(
    [DisplayField1],
    [DisplayField2]
)
```

## 5. Query Optimization Pattern
```sql
-- Efficient Pagination
SELECT *
FROM [TableName] t
WHERE t.CompanyCode = @CompanyCode
ORDER BY t.CreatedDate DESC
OFFSET (@PageNumber - 1) * @PageSize ROWS
FETCH NEXT @PageSize ROWS ONLY

-- Efficient Joins
SELECT t.*, r.Name as RelatedName
FROM [TableName] t
    INNER JOIN [RelatedTable] r ON t.RelatedId = r.Id
WHERE t.CompanyCode = @CompanyCode
    AND t.Status = @Status

-- Date Range Queries
SELECT *
FROM [TableName]
WHERE CompanyCode = @CompanyCode
    AND CreatedDate >= @FromDate
    AND CreatedDate < DATEADD(day, 1, @ToDate)
```

## 6. Stored Procedure Pattern
```sql
CREATE PROCEDURE [dbo].[usp_TableName_Operation]
    @CompanyCode NVARCHAR(10),
    @FromDate DATE,
    @ToDate DATE,
    @PageNumber INT,
    @PageSize INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Get Total Count
    SELECT COUNT(*)
    FROM [TableName]
    WHERE CompanyCode = @CompanyCode
        AND CreatedDate >= @FromDate
        AND CreatedDate < DATEADD(day, 1, @ToDate);
    
    -- Get Data
    SELECT *
    FROM [TableName]
    WHERE CompanyCode = @CompanyCode
        AND CreatedDate >= @FromDate
        AND CreatedDate < DATEADD(day, 1, @ToDate)
    ORDER BY CreatedDate DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
```

## 7. Transaction Pattern
```sql
BEGIN TRY
    BEGIN TRANSACTION;
    
    -- Insert main record
    INSERT INTO [MainTable]
    VALUES (@Id, @CompanyCode, ...);
    
    -- Insert related records
    INSERT INTO [RelatedTable]
    VALUES (@MainId, ...);
    
    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
    
    THROW;
END CATCH
```

## 8. Audit Trail Pattern
```sql
-- Audit Table
CREATE TABLE [dbo].[AuditLog]
(
    [Id] BIGINT IDENTITY(1,1) NOT NULL,
    [TableName] NVARCHAR(100) NOT NULL,
    [RecordId] NVARCHAR(50) NOT NULL,
    [Action] NVARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    [OldValue] NVARCHAR(MAX) NULL,
    [NewValue] NVARCHAR(MAX) NULL,
    [UserId] NVARCHAR(50) NOT NULL,
    [Timestamp] DATETIME2 NOT NULL,
    CONSTRAINT [PK_AuditLog] PRIMARY KEY ([Id])
)

-- Audit Trigger
CREATE TRIGGER [TR_TableName_Audit]
ON [TableName]
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO [AuditLog]
    SELECT
        'TableName',
        COALESCE(i.Id, d.Id),
        CASE
            WHEN i.Id IS NOT NULL AND d.Id IS NULL THEN 'INSERT'
            WHEN i.Id IS NOT NULL AND d.Id IS NOT NULL THEN 'UPDATE'
            ELSE 'DELETE'
        END,
        CASE WHEN d.Id IS NOT NULL 
            THEN (SELECT * FROM deleted WHERE Id = d.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
            ELSE NULL
        END,
        CASE WHEN i.Id IS NOT NULL
            THEN (SELECT * FROM inserted WHERE Id = i.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
            ELSE NULL
        END,
        SYSTEM_USER,
        GETDATE()
    FROM inserted i
    FULL OUTER JOIN deleted d ON i.Id = d.Id
END
```

## 9. Performance Optimization Pattern
```yaml
indexing_strategy:
  - "Create indexes based on query patterns"
  - "Include covering columns"
  - "Monitor index usage"
  - "Regular index maintenance"

query_optimization:
  - "Use appropriate JOIN types"
  - "Avoid SELECT *"
  - "Use parameterized queries"
  - "Implement proper WHERE clauses"

maintenance:
  - "Regular statistics update"
  - "Index defragmentation"
  - "Query plan monitoring"
  - "Performance metrics tracking"
```

## 10. Data Migration Pattern
```sql
-- Version Control Table
CREATE TABLE [dbo].[DatabaseVersion]
(
    [Version] INT NOT NULL,
    [AppliedDate] DATETIME2 NOT NULL,
    [Description] NVARCHAR(500) NOT NULL
)

-- Migration Script Template
BEGIN TRANSACTION;
BEGIN TRY
    -- Version Check
    IF EXISTS (SELECT 1 FROM [DatabaseVersion] WHERE [Version] >= 100)
        RETURN;
    
    -- Schema Changes
    ALTER TABLE [TableName]
    ADD [NewColumn] NVARCHAR(100) NULL;
    
    -- Data Migration
    UPDATE [TableName]
    SET [NewColumn] = [OldColumn];
    
    -- Cleanup
    ALTER TABLE [TableName]
    DROP COLUMN [OldColumn];
    
    -- Version Update
    INSERT INTO [DatabaseVersion]
    VALUES (100, GETDATE(), 'Added NewColumn to TableName');
    
    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    THROW;
END CATCH
```

## 11. Security Pattern
```sql
-- Role-Based Access
CREATE ROLE [FeatureRole]
GRANT SELECT ON [TableName] TO [FeatureRole]
GRANT EXECUTE ON [usp_TableName_Operation] TO [FeatureRole]

-- Row-Level Security
CREATE SECURITY POLICY [CompanyFilter]
ADD FILTER PREDICATE [dbo].[fn_CompanyAccess]([CompanyCode])
ON [TableName]

-- Data Encryption
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'YourStrongPassword'
CREATE CERTIFICATE SensitiveCert WITH SUBJECT = 'Data Protection'
CREATE SYMMETRIC KEY SensitiveKey
WITH ALGORITHM = AES_256
ENCRYPTION BY CERTIFICATE SensitiveCert
```

## 12. Backup and Recovery Pattern
```yaml
backup_strategy:
  full_backup:
    frequency: "Daily"
    retention: "30 days"
  
  differential_backup:
    frequency: "Every 6 hours"
    retention: "7 days"
  
  transaction_log_backup:
    frequency: "Every 15 minutes"
    retention: "24 hours"

recovery_strategy:
  rpo: "15 minutes"
  rto: "1 hour"
  testing: "Monthly recovery test"
```
