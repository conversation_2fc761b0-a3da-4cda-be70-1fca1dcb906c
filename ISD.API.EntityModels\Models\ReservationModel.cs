﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ReservationModel", Schema = "MES")]
    public partial class ReservationModel
    {
        [Key]
        public Guid ReservationId { get; set; }
        [StringLength(50)]
        public string RSNUM { get; set; }
        [StringLength(10)]
        public string RSPOS { get; set; }
        [StringLength(10)]
        public string BDART { get; set; }
        [StringLength(10)]
        public string RSSTA { get; set; }
        [StringLength(10)]
        public string XLOEK { get; set; }
        [StringLength(10)]
        public string XWAOK { get; set; }
        [StringLength(10)]
        public string KZEAR { get; set; }
        [StringLength(100)]
        public string MATNR { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(10)]
        public string LGORT { get; set; }
        [StringLength(50)]
        public string CHARG { get; set; }
        [StringLength(10)]
        public string SOBKZ { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BDTER { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? BDMNG { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [StringLength(10)]
        public string SHKZG { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ERFMG { get; set; }
        [StringLength(10)]
        public string ERFME { get; set; }
        [StringLength(50)]
        public string PLNUM { get; set; }
        [StringLength(50)]
        public string BANFN { get; set; }
        [StringLength(10)]
        public string BNFPO { get; set; }
        [StringLength(50)]
        public string AUFNR { get; set; }
        [StringLength(50)]
        public string KDAUF { get; set; }
        [StringLength(10)]
        public string KDPOS { get; set; }
        [StringLength(10)]
        public string KDEIN { get; set; }
        [StringLength(10)]
        public string BWART { get; set; }
        [StringLength(50)]
        public string SAKNR { get; set; }
        [StringLength(10)]
        public string UMWRK { get; set; }
        [StringLength(10)]
        public string UMLGO { get; set; }
        [StringLength(10)]
        public string POSTP { get; set; }
        [StringLength(10)]
        public string POSNR { get; set; }
        [StringLength(10)]
        public string STLNR { get; set; }
        [StringLength(10)]
        public string STLKN { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}