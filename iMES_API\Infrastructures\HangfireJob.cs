﻿using iMES_API.Infrastructures.JobSchedulers;
using iMES_API.Infrastructures.JobSchedulers.WarehouseSyncSAP;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace iMES_API.Infrastructures
{
    public static class HangfireJob
    {
        public static async void Run(IServiceProvider serviceProvider)
        {
            //await RemoveLogApiScheduler.Invoke(_serviceProvider);
            //await PushNotificationScheduler.Invoke(_serviceProvider);

            using (var scope = serviceProvider.CreateScope())
            {
                var scopedServiceProvider = scope.ServiceProvider;
                await RemoveLogApiScheduler.Invoke(scopedServiceProvider);
                await PushNotificationScheduler.Invoke(scopedServiceProvider);
                await AutoAllocateScheduler.Invoke(scopedServiceProvider);
            }
        }
    }
}
