import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../model/userModel.dart';

class DowntimeList extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;

  const DowntimeList({
    Key? key,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _DowntimeListState createState() => _DowntimeListState();
}

class _DowntimeListState extends State<DowntimeList> {
  List<Map<String, String>> dummyData = [
    {'id': '1', 'title': 'Downtime Record #1', 'date': '2024-01-01', 'status': 'Pending'},
    {'id': '2', 'title': 'Downtime Record #2', 'date': '2024-01-02', 'status': 'Completed'},
    {'id': '3', 'title': 'Downtime Record #3', 'date': '2024-01-03', 'status': 'In Progress'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          'Downtime',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/DowntimeDetail', arguments: {
            'id': '',
            'dateTimeOld': widget.dateTimeOld,
            'user': widget.user,
          });
        },
        backgroundColor: const Color(0xff4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: ListView.builder(
        itemCount: dummyData.length,
        itemBuilder: (context, index) {
          final item = dummyData[index];
          return Card(
            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            child: ListTile(
              title: Text(item['title']!),
              subtitle: Text('Date: ${item['date']}'),
              trailing: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: item['status'] == 'Completed'
                      ? Colors.green
                      : item['status'] == 'Pending'
                          ? Colors.orange
                          : Colors.blue,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  item['status']!,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                  ),
                ),
              ),
              onTap: () {
                Navigator.pushNamed(context, '/DowntimeDetail', arguments: {
                  'id': item['id'],
                  'dateTimeOld': widget.dateTimeOld,
                  'user': widget.user,
                });
              },
            ),
          );
        },
      ),
    );
  }
}
