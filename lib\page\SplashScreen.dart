import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/model/userModel.dart';
import '../Storage/storageSecureStorage.dart';
import '../screenArguments/screenArgumentMainPage.dart';
import '../service/navigatorService.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _changePage();
  }

  Future<void> _changePage() async {
    try {
      final userString = await SecureStorage.getString("user", null) ?? ' ';
      FlutterNativeSplash.remove();
      if (userString == ' ') {
        Future.delayed(const Duration(seconds: 0), () {
          NavigationService.instance.navigationKey!.currentState!.pushReplacementNamed('/Login');
        });
      } else {
        final getSaveUser = UserModel.fromJson(jsonDecode(userString));
        Future.delayed(const Duration(seconds: 0), () {
          NavigationService.instance.navigationKey!.currentState!
              .pushReplacementNamed('/mainPage', arguments: ScreenArgumentMainPage(getSaveUser.data ?? DataUser()));
        });
      }
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(onWillPop: () => Future.value(false), child: const Scaffold(backgroundColor: Colors.white, body: _SplashScreenView()));
  }
}

class _SplashScreenView extends StatelessWidget {
  const _SplashScreenView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.center, children: <Widget>[
      Center(child: Image.asset("assets/logo/logo.png", height: 200.h)),
      SizedBox(height: 15.h),
      const CircularProgressIndicator()
    ]);
  }
}
