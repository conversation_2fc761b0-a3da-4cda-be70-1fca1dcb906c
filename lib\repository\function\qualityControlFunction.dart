import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Widget/dialogWidget/DialogErrorValidate.dart';
import 'package:ttf/model/ApiResponse.dart';
import 'package:ttf/model/ApiResponseSingle.dart';
import 'package:ttf/model/HangMucKiemTraVm.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/modalBottomSheet/imagePickerModalBottomSheet.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/GetQuantitySampleModel.dart';
import '../../model/dataQualityControl.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/qualityControlApi.dart';
import '../../model/sendErrorQualityControl.dart';
import '../../model/sendQualityControlDetail.dart';
import '../../page/KiemTraChatLuong/model/CongDoanInfoVm.dart';
import '../api/getDefectLevel.dart';
import '../api/getQuantitySample.dart';
import '../api/qualityControlApi.dart';
import '../showDateTime.dart';

class QualityControlFunction {
  static bool checkIsSend = false;
  static QualityCheckerInfo defaultValueQC = QualityCheckerInfo(accountId: " ", salesEmployeeName: '--Vui lòng chọn--');
  static QualityTypeList defaultValueQualityTypeList = QualityTypeList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  /// Helper function to safely show SnackBar without causing memory leaks
  static void _safeShowSnackBar(BuildContext context, String message, {Color? backgroundColor}) {
    if (context.mounted) {
      try {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: backgroundColor ?? Colors.black,
          content: Text(
            message,
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
        ));
      } catch (e) {
        debugPrint('Error showing SnackBar: $e');
      }
    }
  }

  static Future<bool?> pickImage(BuildContext context) async {
    final appImageSource = Platform.isAndroid
        ? await showModalBottomSheet<bool>(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20.r))),
            context: context,
            builder: (BuildContext context) => const ImagePickerActionSheet(),
          )
        : await showCupertinoModalPopup<bool>(
            context: context,
            builder: (BuildContext context) => const ImagePickerActionSheet(),
          );
    if (appImageSource != null) {
      return appImageSource;
    } else {
      return null;
    }
  }

  static Future<DateTime?> pickDate(BuildContext context, DateTime? date) async {
    DateTime now = DateTime.now();
    final newDate = await showDatePicker(
      context: context,
      initialDate: date ?? now,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (newDate == null) return null;
    TimeOfDay initialTime = TimeOfDay(hour: now.hour, minute: now.minute);
    TimeOfDay? newTime = await ShowDateTime.showTime(context, initialTime, TimeOfDay(hour: (date ?? now).hour, minute: (date ?? now).minute));
    if (newTime == null) return null;
    return DateFormat("yyyy-MM-ddTHH:mm:ss")
        .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newTime.hour, newTime.minute)));
    // setState(() {
    //   _date = DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newTime.hour, newTime.minute)));
    //   _getDate = QualityControlFunction.getDate(_qualityControl, _date);
    //   debugPrint(_date!.toIso8601String());
    // });
  }

  static Future<DateTime?> pickDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToCompleteDateTime(context);
      if (newDate == null) return null;
      // setState(() {
      return DateFormat("yyyy-MM-ddTHH:mm:ss")
          .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
      // _getDate = QualityControlFunction.getDate(_qualityControl, _date);
      // debugPrint(_date!.toIso8601String());
      // });
    } catch (error) {
      _safeShowSnackBar(context, 'Xảy ra lỗi! Vui lòng thử lại sau');
      return null;
    }
  }

  static List<ThongTinKiemTra> filterQualityControlInformationIdList(
    List<ThongTinKiemTra> lsQualityControlInformationIdList,
    String query,
  ) {
    List<ThongTinKiemTra> getQualityControlInformationIdList = lsQualityControlInformationIdList
        .where((element) => element.name != null && element.name!.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return getQualityControlInformationIdList;
  }

  static List<ErrorList> filterQualityControlErrorList(List<ErrorList> lsQualityControlError, String query) {
    debugPrint(lsQualityControlError.length.toString());
    List<ErrorList> getQualityControlError = lsQualityControlError
        .where((element) => element.catalogTextVi != null && element.catalogTextVi!.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return getQualityControlError;
  }

  static List<DropdownItemList> filterCaNhanGayLoiList(List<DropdownItemList> lsCaNhanGayLoi, String query) {
    // debugPrint(lsQualityControlError.length.toString());
    List<DropdownItemList> getCaNhanGayLoi = lsCaNhanGayLoi
        .where((element) => element.catalogTextVi != null && element.catalogTextVi!.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return getCaNhanGayLoi;
  }

  static Future<void> sendQualityControl(
      BuildContext context,
      List<File> lsImageTabCheck,
      String controller_2Text,
      QualityControl? qualityControl,
      QualityCheckerInfo? selectedStaff,
      String dateNowFormat,
      QualityTypeList? selectedType,
      ResultList? selectedResult,
      String controller_1Text,
      SendQualityControlDetail? getSendQualityControlDetail,
      List<LsSendQualityControlInformation> getLsSendQualityControlInformation,
      List<SendErrorControl> getLsError,
      String token) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final value = await QuantityControlApi.postQuantityControl(
          qualityControl,
          lsImageTabCheck,
          qualityControl!.qualityControlId.toString(),
          selectedStaff!.accountId.toString(),
          dateNowFormat,
          selectedType!.catalogCode.toString(),
          (int.parse(controller_2Text)).round(),
          selectedResult!.catalogCode.toString(),
          controller_1Text,
          getSendQualityControlDetail!,
          getLsSendQualityControlInformation,
          getLsError,
          token);
      Navigator.pop(context);
      checkIsSend = true;

      debugPrint("TIEN TEST");
      debugPrint(json.encode(qualityControl));

      if (value.statusCode == 200) {
        final respStr = await value.stream.bytesToString();
        final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
        if (dataResponse.code == 202 && dataResponse.success == true) {
          _safeShowSnackBar(context, dataResponse.data.toString());
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context, true);
          });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(
          //     SnackBar(
          //       backgroundColor: Colors.black,
          //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
          //         style: TextStyle(fontSize: 15.sp,
          //             color: Colors.white),),
          //     ));
        }
      } else {
        final respStr = await value.stream.bytesToString();
        debugPrint("Error response: $respStr");

        // Try to parse validation errors
        String errorMessage = "Error occurred";
        try {
          final errorResponse = jsonDecode(respStr);
          if (errorResponse['errors'] != null) {
            // Handle validation errors
            Map<String, dynamic> errors = errorResponse['errors'];
            List<String> errorMessages = [];

            errors.forEach((field, messages) {
              if (messages is List) {
                for (var message in messages) {
                  errorMessages.add("$field: $message");
                }
              } else {
                errorMessages.add("$field: $messages");
              }
            });

            errorMessage = "Validation Errors:\n${errorMessages.join('\n')}";
          } else if (errorResponse['title'] != null) {
            errorMessage = errorResponse['title'].toString();
          } else {
            errorMessage = respStr;
          }
        } catch (e) {
          // If JSON parsing fails, use raw response
          errorMessage = respStr;
        }

        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: errorMessage));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> sendQualityControlDauVao(
      BuildContext context,
      List<File> lsImageTabCheck,
      String soLuongKiemTra,
      QualityControl? qualityControl,
      QualityCheckerInfo? selectedStaff,
      String dateNowFormat,
      QualityTypeList? selectedType,
      ResultList? selectedResult,
      String controllerSoPOText,
      String controllerSKUText,
      SendQualityControlDetail2? sendQualityControlDetail,
      List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
      List<SendErrorControl> getLsError,
      String token) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQuantityControlDauVao(
            qualityControl,
            lsImageTabCheck,
            qualityControl.qualityControlId.toString(),
            selectedStaff.accountId.toString(),
            dateNowFormat,
            selectedType!.catalogCode.toString(),
            (int.parse(soLuongKiemTra)).round(),
            selectedResult.catalogCode,
            controllerSoPOText,
            sendQualityControlDetail,
            getLsSendQualityControlInformation,
            getLsError,
            token,
            controllerSKUText);

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());
            Future.delayed(const Duration(seconds: 0), () {
              Navigator.pop(context, true);
            });
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> sendQualityControlDauVao3(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    String controllerSoPOText,
    String controllerSKUText,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
    String soLuongNhapKho,
    String soLuongBlock,
    String soLuongTraVe,
  ) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQuantityControlDauVao3(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          selectedType!.catalogCode.toString(),
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          controllerSoPOText,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
          controllerSKUText,
          soLuongNhapKho,
          soLuongBlock,
          soLuongTraVe,
        );

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());
            Future.delayed(const Duration(seconds: 0), () {
              Navigator.pop(context, true);
            });
          } else {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(
                message: dataResponse.data.toString(),
              ),
            );
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) => DialogError(message: respStr.toString()),
          );
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlCongDoan(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    String controllerSoPOText,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
  ) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      // if (kDebugMode) {
      //   return;
      // }

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlCongDoan(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          // selectedType!.catalogCode.toString(),
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          controllerSoPOText,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
        );

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            // Nếu debug thì không pop để test data liên tục
            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlQCNghiemThu(
      BuildContext context,
      List<File> lsImageTabCheck,
      String soLuongKiemTra,
      QualityControl? qualityControl,
      QualityCheckerInfo? selectedStaff,
      String dateNowFormat,
      QualityTypeList? selectedType,
      ResultList? selectedResult,
      String controllerSoPOText,
      String controllerSKUText,
      SendQualityControlDetail2? sendQualityControlDetail,
      List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
      List<SendErrorControl> getLsError,
      String token) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      // if (kDebugMode) {
      //   return;
      // }

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlQCNghiemThu(
            qualityControl,
            lsImageTabCheck,
            qualityControl.qualityControlId.toString(),
            selectedStaff.accountId.toString(),
            dateNowFormat,
            selectedType!.catalogCode.toString(),
            (int.parse(soLuongKiemTra)).round(),
            selectedResult.catalogCode,
            controllerSoPOText,
            sendQualityControlDetail,
            getLsSendQualityControlInformation,
            getLsError,
            token,
            controllerSKUText);

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            // Nếu debug thì không pop để test data liên tục
            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlQCMau(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    String controllerSoPOText,
    String controllerSKUText,
    String qualityType,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
    String loaiMau,
    String nhaMay,
    String phanXuong,
  ) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      // if (kDebugMode) {
      //   return;
      // }

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlQCMau(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          qualityType,
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          controllerSoPOText,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
          controllerSKUText,
          loaiMau,
          nhaMay,
          phanXuong,
        );

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlQCHienTruong(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    DropdownItemList? selectedPhanXuong,
    DropdownItemList? selectedCongDoan,
    String saleOrgCode,
    String note,
    String qualityType,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
  ) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      // if (kDebugMode) {
      //   return;
      // }

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlQCHienTruong(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          qualityType,
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
          selectedPhanXuong!.catalogCode,
          selectedCongDoan!.catalogCode,
          saleOrgCode,
          note,
        );

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlSanPham(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    String controllerSoPOText,
    String barcode,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
  ) async {
    try {
      // QuantityControlApi.test(getLsSendQualityControlInformation,getLsError,getSendQualityControlDetail!);
      checkIsSend = false;

      // if (kDebugMode) {
      //   return;
      // }

      if (!kDebugMode) {
        showDialog<String>(
          barrierDismissible: false,
          context: context,
          builder: (BuildContext context) => WillPopScope(
            onWillPop: () async => false,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      }

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlSanPham(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          // selectedType!.catalogCode.toString(),
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          controllerSoPOText,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
          barcode,
        );

        if (!kDebugMode) {
          Navigator.pop(context); // Close CircularProgressIndicator loading
        }

        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            // Nếu debug thì không pop để test data liên tục
            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()),
            );
            // ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       backgroundColor: Colors.black,
            //       content: Text('Cập nhật thất bại! Vui lòng thử lại sau',
            //         style: TextStyle(fontSize: 15.sp,
            //             color: Colors.white),),
            //     ));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        // Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static Future<void> postQualityControlMauDauChuyen(
    BuildContext context,
    List<File> lsImageTabCheck,
    String soLuongKiemTra,
    QualityControl? qualityControl,
    QualityCheckerInfo? selectedStaff,
    String dateNowFormat,
    QualityTypeList? selectedType,
    ResultList? selectedResult,
    String controllerSoPOText,
    String barcode,
    String qualityType,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> getLsSendQualityControlInformation,
    List<SendErrorControl> getLsError,
    String token,
    String loaiMau,
    String nhaMay,
    String phanXuong,
  ) async {
    try {
      checkIsSend = false;

      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      if (qualityControl != null && selectedResult != null && selectedStaff != null) {
        final value = await QuantityControlApi.postQualityControlMauDauChuyen(
          qualityControl,
          lsImageTabCheck,
          qualityControl.qualityControlId.toString(),
          selectedStaff.accountId.toString(),
          dateNowFormat,
          qualityType,
          (int.parse(soLuongKiemTra)).round(),
          selectedResult.catalogCode,
          controllerSoPOText,
          sendQualityControlDetail,
          getLsSendQualityControlInformation,
          getLsError,
          token,
          barcode,
          loaiMau,
          nhaMay,
          phanXuong,
        );

        Navigator.pop(context);
        debugPrint(json.encode(qualityControl));
        checkIsSend = true;

        debugPrint("TIEN TEST");
        debugPrint(json.encode(qualityControl));

        if (value.statusCode == 200) {
          final respStr = await value.stream.bytesToString();
          final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));
          if (dataResponse.code == 202 && dataResponse.success == true) {
            _safeShowSnackBar(context, dataResponse.data.toString());

            if (!kDebugMode) {
              Future.delayed(const Duration(seconds: 0), () {
                Navigator.pop(context, true);
              });
            }
          } else {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
          }
        } else {
          final respStr = await value.stream.bytesToString();
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
        }
      } else {
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Không có kết nối mạng');
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
    }
  }

  static String dateFormatConfirm(QualityControl? qualityControl) {
    if (qualityControl != null) {
      if (qualityControl.confirmDate != null) {
        DateTime stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(qualityControl.confirmDate!);
        String dateFormatStringConfirm = DateFormat('dd/MM/yyyy').format(stringToDateTimeConfirm);
        return dateFormatStringConfirm;
      } else {
        return "";
      }
    } else {
      return "";
    }
  }

  static String dateFormatConfirm2(QualityControl? qualityControl) {
    if (qualityControl != null) {
      if (qualityControl.confirmDate != null) {
        // DateTime stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(qualityControl.confirmDate!);
        DateTime stringToDateTimeConfirm = DateFormat("yyyy-MM-ddTHH:mm:ss").parse(qualityControl.confirmDate!);
        String dateFormatStringConfirm = DateFormat('dd/MM/yyyy HH:mm:ss').format(stringToDateTimeConfirm);
        return dateFormatStringConfirm;
      } else {
        return "";
      }
    } else {
      return "";
    }
  }

  static String getDateString(QualityControl? qualityControl, DateTime? date) {
    String? dateFormatStringQualityDate;
    if (qualityControl!.qualityDate != null && date == null) {
      dateFormatStringQualityDate =
          DateFormat("dd-MM-yyyy hh:mm").format(DateFormat("yyyy-MM-dd hh:mm").parse(qualityControl.qualityDate.toString()));
      // dateFormatStringQualityDate = DateFormat.yMMMMd('vi').format(stringToDateTimeQualityDate);
    } else if (qualityControl.qualityDate == null && date == null) {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm").format(DateTime.now());
    } else {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm").format(date!);
      // return '${date.month}/${date.day}/${date.year}';
    }
    return dateFormatStringQualityDate;
  }

  static String getDateString2(QualityControl? qualityControl, DateTime? date) {
    String? dateFormatStringQualityDate;
    if (qualityControl!.qualityDate != null && date == null) {
      dateFormatStringQualityDate =
          DateFormat("dd-MM-yyyy hh:mm a").format(DateFormat("yyyy-MM-dd hh:mm a").parse(qualityControl.qualityDate.toString()));
      // dateFormatStringQualityDate = DateFormat.yMMMMd('vi').format(stringToDateTimeQualityDate);
    } else if (qualityControl.qualityDate == null && date == null) {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.now());
    } else {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(date!);
      // return '${date.month}/${date.day}/${date.year}';
    }
    return dateFormatStringQualityDate;
  }

  // static String formatDatePost(QualityControl? qualityControl, DateTime? date){
  //    DateTime getFormatDateTime = DateFormat.yMMMMd('vi').parse(getDate(qualityControl,date));
  //    String getFormatDateString = DateFormat("yyyy-MM-dd").format(getFormatDateTime);
  //    return getFormatDateString;
  //  }

  static Future<CongDoanInfoVm?> fetchCongDoanInfo(String hangTagId, String stepCode, String token) async {
    var response = await QuantityControlApi.getCongDoanInfo(hangTagId, stepCode, token);

    if (response.statusCode == 200) {
      final ret = CongDoanInfoVm.fromJson(jsonDecode(response.body));
      return ret;
    } else {
      return null;
    }
  }

  static Future<CongDoanInfoVm?> fetchCongDoanInfoSanPham(String? qualityControlId, String stepCode, String token) async {
    if (qualityControlId == null) {
      return null;
    }

    var response = await QuantityControlApi.getCongDoanInfoSanPham(qualityControlId, stepCode, token);

    if (response.statusCode == 200) {
      final ret = CongDoanInfoVm.fromJson(jsonDecode(response.body));
      return ret;
    } else {
      return null;
    }
  }

  static Future<CongDoanInfoVm?> fetchCongDoanInfoBTP(String? lsxSAP, String stepCode, String token) async {
    if (lsxSAP!.isEmpty) {
      return null;
    }

    var response = await QuantityControlApi.getCongDoanInfoBTP(lsxSAP, stepCode, token);

    if (response.statusCode == 200) {
      final ret = CongDoanInfoVm.fromJson(jsonDecode(response.body));
      return ret;
    } else {
      return null;
    }
  }

  static Future<CongDoanInfoVm?> fetchNghiemThuInfo(String hangTagId, String loaiNghiemThu, String token) async {
    var response = await QuantityControlApi.getNghiemThuInfo(hangTagId, loaiNghiemThu, token);

    if (response.statusCode == 200) {
      final ret = CongDoanInfoVm.fromJson(jsonDecode(response.body));
      return ret;
    } else {
      return null;
    }
  }

  static Future<List<HangMucKiemTraVm>?> fetchChecklistMau(String loaiMau, String token) async {
    try {
      var response = await QuantityControlApi.getChecklistMau(loaiMau, token);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        // Check if the response has the expected structure
        if (jsonResponse != null && jsonResponse['data'] != null && jsonResponse['isSuccess'] == true) {
          // Parse the list of HangMucKiemTraVm items
          final List<dynamic> itemsJson = jsonResponse['data'];
          final List<HangMucKiemTraVm> items = itemsJson.map((itemJson) => HangMucKiemTraVm.fromJson(itemJson)).toList();

          return items;
        } else {
          debugPrint('API response does not have the expected format: ${response.body}');
          return [];
        }
      } else {
        debugPrint('API error: ${response.statusCode} - ${response.reasonPhrase}');
        return null;
      }
    } catch (e) {
      debugPrint('Error in fetchChecklistMau: $e');
      return [];
    }
  }

  static Future<QualityControlModel?> fetchQualityControl(String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    final response = await QuantityControlApi.getQuantityControl(qualityControlId, token, rawMaterialCardId, fromPage);
    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  // 1. Nghiệm thu đầu vào
  static Future<QualityControlModel?> fetchQualityControlDauVao(
      String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    var response;

    if (qualityControlId.isNotEmpty) {
      // onPress
      response = await QuantityControlApi.getQuantityControl(qualityControlId, token, "", fromPage);
    } else {
      // scan QR
      response = await QuantityControlApi.getQuantityControl2("", token, rawMaterialCardId, fromPage);
    }

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  // 2. KCS kiểm tra trên chuyền
  static Future<QualityControlModel?> fetchQualityControlKCS(String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    var response = await QuantityControlApi.getQuantityControlKCS(qualityControlId, token, rawMaterialCardId, fromPage);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  // 3. QAQC nghiệm thu
  static Future<QualityControlModel?> fetchQualityControlNghiemThu(
      String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    // rawMaterialCardId (lấy từ QR code)
    var response = await QuantityControlApi.getQuantityControlNghiemThu(qualityControlId, token, rawMaterialCardId, fromPage);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  static Future<List<DataGetDefectLevel>?> fetchGetDefectLevelApi(String token) async {
    final response = await GetDefectLevelApi.getDefectLevelApi(token);
    if (response.statusCode == 200) {
      final responseGetDefectLevel = jsonDecode(response.body);
      final getGetDefectLevelApi = GetDefectLevel.fromJson(responseGetDefectLevel);
      if (getGetDefectLevelApi.code == 200 && getGetDefectLevelApi.isSuccess == true) {
        return getGetDefectLevelApi.data;
      } else {
        return [];
      }
    } else {
      throw (response.body.toString());
    }
  }

  // 4. QC Mẫu
  static Future<QualityControlModel?> fetchQualityControlQCMau(String qualityControlId, String token, String? qualityType) async {
    // rawMaterialCardId (lấy từ QR code)
    var response = await QuantityControlApi.getQuantityControlQCMau(qualityControlId, token, qualityType);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  static Future<QualityControlModel?> fetchQualityControlQCHienTruong(String qualityControlId, String token, String? qualityType) async {
    // rawMaterialCardId (lấy từ QR code)
    var response = await QuantityControlApi.getQuantityControlQCHienTruong(qualityControlId, token, qualityType);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  // 5. QC Sản phẩm
  static Future<QualityControlModel?> fetchQualityControlSanPham(String qualityControlId, String token, String? barcode) async {
    // rawMaterialCardId (lấy từ QR code)
    var response = await QuantityControlApi.getQuantityControlQCSanPham(qualityControlId, token, barcode);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  // 6. QC Mẫu đầu chuyền
  static Future<Map<String, dynamic>> fetchPOHeaderMauDauChuyen(String qualityControlId, String token, String? barcode) async {
    // Load QC records with QualityType = "MAUDAUCHUYEN"
    var response = await QuantityControlApi.getPOHeaderMauDauChuyen(qualityControlId, token, barcode);

    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);

      // Check if the response has the API response structure
      if (responseBody is Map<String, dynamic> && responseBody.containsKey('isSuccess')) {
        final isSuccess = responseBody['isSuccess'] as bool?;
        final code = responseBody['code'] as int?;
        final message = responseBody['message'] as String?;

        if (isSuccess == true && code == 200) {
          // Success case - parse the data
          final qualityControlModel = QualityControlModel.fromJson(responseBody);
          return {
            'success': true,
            'data': qualityControlModel,
          };
        } else {
          // Error case - return error info
          return {
            'success': false,
            'message': message ?? 'Unknown error occurred',
            'code': code ?? 400,
          };
        }
      } else {
        // Fallback - try to parse as QualityControlModel directly
        try {
          final qualityControlModel = QualityControlModel.fromJson(responseBody);
          return {
            'success': true,
            'data': qualityControlModel,
          };
        } catch (e) {
          return {
            'success': false,
            'message': 'Failed to parse response data',
            'code': 500,
          };
        }
      }
    } else {
      return {
        'success': false,
        'message': 'Network error: ${response.statusCode}',
        'code': response.statusCode,
      };
    }
  }

  static Future<ApiResponseSingle?> fetchQCPassedStamp(String token, String? barcode) async {
    var response = await QuantityControlApi.fetchQCPassedStamp(token, barcode);

    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      return ApiResponseSingle.fromJson(responseBody);
    } else {
      return null;
    }
  }

  static Future<QualityControlModel?> fetchQualityControlHeader(String lsxSAP, String barcode, String token) async {
    // rawMaterialCardId (lấy từ QR code)
    var response = await QuantityControlApi.getQualityControlHeader(lsxSAP, barcode, token);

    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return QualityControlModel();
    }
  }

  static Future<DataGetQuantitySample?> fetchGetQuantitySample(
    String sampleName,
    String quantityCheck,
    String token,
    BuildContext context,
    bool checkMounted,
  ) async {
    try {
      final response = await GetQuantitySample.getQuantitySample(sampleName, quantityCheck, token);
      if (!checkMounted) return null;
      if (response.statusCode == 200) {
        final responseGetQuantitySample = jsonDecode(response.body);
        final getGetQuantitySample = GetQuantitySampleModel.fromJson(responseGetQuantitySample);
        if (getGetQuantitySample.code == 200 && getGetQuantitySample.isSuccess == true) {
          debugPrint(getGetQuantitySample.data.toString());
          return getGetQuantitySample.data;
        }

        _safeShowSnackBar(context, getGetQuantitySample.message.toString());

        return null;
      } else {
        _safeShowSnackBar(context, response.reasonPhrase.toString());
        return null;
      }
    } on SocketException catch (_) {
      if (!checkMounted) return null;
      _safeShowSnackBar(context, 'Không có kết nối mạng');
      return null;
    } catch (error) {
      if (!checkMounted) return null;
      debugPrint(error.toString());
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
      return null;
    }
  }

  static Future<DataGetQuantitySample?> fetchGetQuantitySample2(
    String sampleName,
    String quantityCheck,
    String token,
    BuildContext context,
    bool checkMounted,
  ) async {
    try {
      final response = await GetQuantitySample.getQuantitySample2(sampleName, quantityCheck, token);
      if (!checkMounted) return null;
      if (response.statusCode == 200) {
        final responseGetQuantitySample = jsonDecode(response.body);
        final getGetQuantitySample = GetQuantitySampleModel.fromJson(responseGetQuantitySample);
        if (getGetQuantitySample.code == 200 && getGetQuantitySample.isSuccess == true) {
          debugPrint(getGetQuantitySample.data.toString());
          return getGetQuantitySample.data;
        }

        _safeShowSnackBar(context, getGetQuantitySample.message.toString());

        return null;
      } else {
        _safeShowSnackBar(context, response.reasonPhrase.toString());
        return null;
      }
    } on SocketException catch (_) {
      if (!checkMounted) return null;
      _safeShowSnackBar(context, 'Không có kết nối mạng');
      return null;
    } catch (error) {
      if (!checkMounted) return null;
      debugPrint(error.toString());
      _safeShowSnackBar(context, 'Ứng dụng xày ra lỗi vui lòng thử lại sau');
      return null;
    }
  }

  static Future<CongDoanInfoVm?> fetchCongDoanInfoMauDauChuyen(String lsxSAP, String stepCode, String token) async {
    var response = await QuantityControlApi.getCongDoanInfoMauDauChuyen(lsxSAP, stepCode, token);

    if (response.statusCode == 200) {
      final ret = CongDoanInfoVm.fromJson(jsonDecode(response.body));
      return ret;
    } else {
      return null;
    }
  }

  // 7. QC Mẫu đầu chuyền by LSX - Header only
  static Future<Map<String, dynamic>> fetchQCMauDauChuyenByLSX(String lsxSAP, String token) async {
    // Load only header information for QC Mẫu đầu chuyền
    var response = await QuantityControlApi.getQCMauDauChuyenByLSX(lsxSAP, token);

    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);

      // Check if the response has the API response structure
      if (responseBody is Map<String, dynamic> && responseBody.containsKey('isSuccess')) {
        final isSuccess = responseBody['isSuccess'] as bool?;
        final code = responseBody['code'] as int?;
        final message = responseBody['message'] as String?;

        if (isSuccess == true && code == 200) {
          // Success case - parse the data
          final qualityControlModel = QualityControlModel.fromJson(responseBody);
          return {
            'success': true,
            'data': qualityControlModel,
          };
        } else {
          // Error case - return error info
          return {
            'success': false,
            'message': message ?? 'Unknown error occurred',
            'code': code ?? 400,
          };
        }
      } else {
        // Fallback - try to parse as QualityControlModel directly
        try {
          final qualityControlModel = QualityControlModel.fromJson(responseBody);
          return {
            'success': true,
            'data': qualityControlModel,
          };
        } catch (e) {
          return {
            'success': false,
            'message': 'Failed to parse response data',
            'code': 500,
          };
        }
      }
    } else {
      return {
        'success': false,
        'message': 'Network error: ${response.statusCode}',
        'code': response.statusCode,
      };
    }
  }
}
