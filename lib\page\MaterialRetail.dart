import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/GetStepByProductBach.dart';
import '../Storage/storageSharedPreferences.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/TableInfo.dart';
import '../element/timeOut.dart';
import '../model/dataSlocAddressList.dart';
import '../model/dataStepCodeList.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getQuantityMaterialUsedShift.dart';
import '../model/getSOWBSByTask.dart';
import '../model/materialRetailList.dart';
import '../model/postMaterialRetail.dart';
import '../model/switchingStagesApi.dart';
import '../repository/api/switchingStagesApi.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/materialRetailFunction.dart';
import '../repository/function/recordUnusedMaterialsFunction.dart';
import 'LostConnect.dart';
import 'package:http/http.dart' as http;

class MaterialRetail extends StatefulWidget {
  final String token;
  final String barcode;
  final String plant;
  final String dateTimeOld;
  const MaterialRetail({Key? key, required this.token, required this.barcode, required this.plant, required this.dateTimeOld}) : super(key: key);

  @override
  _MaterialRetailState createState() => _MaterialRetailState();
}

class _MaterialRetailState extends State<MaterialRetail> {
  final List<FocusNode> _lsFocusNode = [];
  final List<TextEditingController> _lsTextEditingController = [];
  final List<FocusNode> _lsFocusStageController = [];
  final List<TextEditingController> _lsStageController = [];
  final List<MaterialRetailList> _lsMaterialRetailList = [];
  // final List<DataQuantityMaterialUsedShift> _lsDataQuantityMaterialUsedShift = [];
  // final List<DataSlocAddressList> _lsDataSlocAddressList = [];
  final List<DataStepCodeList> _lsDataStepCodeList = [];
  // List<DataGetSlocByProduct> _lsDataGetSlocByProduct = [];
  final List<DataSlocQuantityUsedShift> _lsDataSlocQuantityUsedShift = [];
  final List<bool> _lsLoadingMaterial = [];
  final List<bool> _lsLoadingQuantity = [];
  final List<bool> _lsErrorController = [];
  final List<bool> _lsErrorSelectedSloc = [];
  final List<bool> _lsErrorStepCode = [];

  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _error = false;

  // DataGetBarcodeReceive?  _dataRawMaterial;
  // List<DataQuantityMaterialUsedShift>? _dataQuantityMaterialUsedShift;

  DataSwitchingStages? _switchingStateData;
  SwitchingStages? _switchingStages;
  // DataBatch? _getBatch;
  DataGetSOWBSByTask? _dataGetSOWBSByTask;

  bool _disableButton = false;
  late bool _timeOut;

  @override
  void initState() {
    super.initState();
    _getSwitchingStages();
  }

  Future<void> _getSwitchingStages() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _isNotWifi = false;
        });

        final data = await Future.wait([
          SwitchingStagesApi.getSwitchingStages(widget.barcode, widget.token),
          MaterialRetailFunction.fetchSOWBSByTask(widget.barcode, widget.token)
        ]);

        if (!mounted) return;
        setState(() {
          _isLoading = false;
          _lsDataStepCodeList.add(DataStepCodeList(lsDataStepCode: [], selectedStepCode: null));
          _lsTextEditingController.add(TextEditingController());
          _lsMaterialRetailList.add(MaterialRetailList());
          // _lsDataQuantityMaterialUsedShift.add(DataQuantityMaterialUsedShift());
          _lsFocusNode.add(FocusNode());
          _lsLoadingMaterial.add(false);
          _lsErrorController.add(false);
          _lsErrorSelectedSloc.add(false);
          _lsErrorStepCode.add(false);
          _lsLoadingQuantity.add(false);
          _lsDataSlocQuantityUsedShift.add(DataSlocQuantityUsedShift(dataQuantityMaterialUsedShift: [], selectedDataQuantityMaterialUsedShift: null));
          if (data.isNotEmpty) {
            if ((data[0] as http.Response).statusCode == 200) {
              if ((data[0] as http.Response).body.isNotEmpty) {
                final responseDataSwitchingStages = jsonDecode((data[0] as http.Response).body);
                _switchingStages = SwitchingStages.fromJson(responseDataSwitchingStages);
                if (_switchingStages!.isSuccess != false) {
                  _switchingStateData = _switchingStages!.data;
                } else {
                  _switchingStateData = null;
                }
              } else {
                _switchingStateData = null;
              }
            } else {
              _switchingStateData = null;
            }
            if ((data[1] as DataGetSOWBSByTask?) != null) {
              _dataGetSOWBSByTask = data[1] as DataGetSOWBSByTask?;
            }
          }
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = true;
        _timeOut = false;
      });
    }
  }

  Future<void> _addList() async {
    setState(() {
      _lsDataStepCodeList.add(DataStepCodeList(lsDataStepCode: [], selectedStepCode: null));
      _lsTextEditingController.add(TextEditingController());
      _lsStageController.add(TextEditingController());
      _lsMaterialRetailList.add(MaterialRetailList());
      // _lsDataQuantityMaterialUsedShift.add(DataQuantityMaterialUsedShift());
      _lsFocusNode.add(FocusNode());
      _lsFocusStageController.add(FocusNode());
      _lsLoadingMaterial.add(false);
      _lsDataSlocQuantityUsedShift.add(DataSlocQuantityUsedShift(dataQuantityMaterialUsedShift: [], selectedDataQuantityMaterialUsedShift: null));
      _lsErrorController.add(false);
      _lsErrorSelectedSloc.add(false);
      _lsErrorStepCode.add(false);
      _lsLoadingQuantity.add(false);
    });
  }

  Future<void> _getDataRawMaterial(String materialID, BuildContext context, int index) async {
    try {
      setState(() {
        _lsLoadingMaterial[index] = true;
        _lsDataSlocQuantityUsedShift[index].dataQuantityMaterialUsedShift = [];
        _lsDataSlocQuantityUsedShift[index].selectedDataQuantityMaterialUsedShift = null;
        _lsDataStepCodeList[index].lsDataStepCode = [];
        _lsDataStepCodeList[index].selectedStepCode = null;
      });
      final dataMaterial = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);
      if (dataMaterial != null) {
        final data = await RecordUnusedMaterialsFunction.fetchStepByProductBach(
            dataMaterial.productCode.toString(), dataMaterial.batchNumber.toString(), widget.token);
        if (data != null) {
          if (!mounted) return;
          setState(() {
            _lsLoadingMaterial[index] = false;
            _lsMaterialRetailList[index].material = dataMaterial.productCode ?? "";
            _lsMaterialRetailList[index].description = dataMaterial.productName ?? "";
            _lsMaterialRetailList[index].batchNumber = dataMaterial.batchNumber ?? "";
            _lsMaterialRetailList[index].rawMaterialCardId = materialID;
            _lsDataStepCodeList[index].lsDataStepCode = data;
          });
        } else {
          if (!mounted) return;
          setState(() {
            _lsMaterialRetailList[index].material = null;
            _lsMaterialRetailList[index].description = null;
            _lsMaterialRetailList[index].rawMaterialCardId = null;
            _lsMaterialRetailList[index].batchNumber = null;
            _lsLoadingMaterial[index] = false;
            _lsDataStepCodeList[index].lsDataStepCode = [];
            _lsDataStepCodeList[index].selectedStepCode = null;
          });

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Nguyên vật liệu chưa được quét khai báo sử dụng trong ngày!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1)));
        }
      } else {
        if (!mounted) return;
        setState(() {
          _lsLoadingMaterial[index] = false;
          _lsMaterialRetailList[index].material = null;
          _lsMaterialRetailList[index].description = null;
          _lsMaterialRetailList[index].rawMaterialCardId = null;
          _lsMaterialRetailList[index].batchNumber = null;
          _lsLoadingMaterial[index] = false;
          _lsDataStepCodeList[index].lsDataStepCode = [];
          _lsDataStepCodeList[index].selectedStepCode = null;
        });

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin pallet NVL!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _lsLoadingMaterial[index] = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      setState(() {
        _lsLoadingMaterial[index] = false;
      });
    }
  }

  void _checkError() {
    for (int i = 0; i < _lsTextEditingController.length; i++) {
      setState(() {
        if (_lsTextEditingController[i].text.isEmpty) {
          _lsErrorController[i] = true;
        } else {
          _lsErrorController[i] = false;
        }
        if (_lsDataSlocQuantityUsedShift[i].dataQuantityMaterialUsedShift!.isNotEmpty) {
          if (_lsDataSlocQuantityUsedShift[i].selectedDataQuantityMaterialUsedShift == null) {
            _lsErrorSelectedSloc[i] = true;
          } else {
            _lsErrorSelectedSloc[i] = false;
          }
        } else {
          _lsErrorSelectedSloc[i] = false;
        }
      });
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  Future<void> _setStepCode(Steps? value, int index) async {
    try {
      setState(() {
        _lsDataSlocQuantityUsedShift[index].dataQuantityMaterialUsedShift = [];
        _lsDataSlocQuantityUsedShift[index].selectedDataQuantityMaterialUsedShift = null;
        _lsDataStepCodeList[index].selectedStepCode = value!;
        if (_lsDataStepCodeList[index].selectedStepCode == null) {
          _lsErrorStepCode[index] = true;
        } else {
          _lsErrorStepCode[index] = false;
        }
        _lsLoadingQuantity[index] = true;
      });
      final data = await RecordUnusedMaterialsFunction.fetchQuantityMaterialUsedShift(_lsMaterialRetailList[index].material.toString(),
          _lsDataStepCodeList[index].selectedStepCode!.stepCode.toString(), _lsMaterialRetailList[index].batchNumber.toString(), widget.token);
      if (!mounted) return;
      setState(() {
        _lsLoadingQuantity[index] = false;
      });
      if (data != null) {
        setState(() {
          _lsDataSlocQuantityUsedShift[index].dataQuantityMaterialUsedShift = data;
        });
      } else {
        setState(() {
          _lsDataSlocQuantityUsedShift[index].dataQuantityMaterialUsedShift = [];
        });
      }
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  void dispose() {
    for (var i in _lsTextEditingController) {
      i.dispose();
    }
    for (var i in _lsFocusNode) {
      i.dispose();
    }
    for (var i in _lsFocusStageController) {
      i.dispose();
    }
    for (var i in _lsStageController) {
      i.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton)))
        : Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'Xác định NVL hàng lẻ',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            // floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,

            floatingActionButton: _error == false && _isNotWifi == false && _isLoading == true
                ? null
                : FloatingActionButton(
                    onPressed: () {
                      _addList();
                    },
                    backgroundColor: const Color(0xff4CAF50),
                    child: const Icon(Icons.add),
                  ),
            body: _error == true
                ? Center(child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)))
                : _isNotWifi == false
                    ? _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : _switchingStateData != null || _dataGetSOWBSByTask != null
                            ? SingleChildScrollView(
                                child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
                                    decoration: const BoxDecoration(color: Colors.white),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: <Widget>[
                                        TableInfo(
                                          textCL1: "LSX:",
                                          textCL2: _switchingStateData!.productionOrder.toString(),
                                          colorCL1: 0xff303F9F,
                                          colorCL2: 0xFFFFFFFF,
                                        ),
                                        TableInfoNoTop(
                                          textCL1: "SO/WBS:",
                                          textCL2: _dataGetSOWBSByTask == null
                                              ? ""
                                              : (_dataGetSOWBSByTask!.so != null && _dataGetSOWBSByTask!.so != "") &&
                                                      (_dataGetSOWBSByTask!.soLine != null && _dataGetSOWBSByTask!.soLine != "")
                                                  ? "${_dataGetSOWBSByTask!.so ?? ""}/${_dataGetSOWBSByTask!.soLine ?? ""}"
                                                  : _dataGetSOWBSByTask!.wbs != null && _dataGetSOWBSByTask!.wbs != ""
                                                      ? _dataGetSOWBSByTask!.wbs ?? ""
                                                      : "",
                                          colorCL1: 0xff303F9F,
                                          colorCL2: 0xFFFFFFFF,
                                        ),
                                        TableInfoNoTop(
                                          textCL1: "Mã thành phẩm:",
                                          textCL2: _switchingStateData!.productCode ?? "",
                                          colorCL1: 0xff303F9F,
                                          colorCL2: 0xFFFFFFFF,
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
                                    decoration: const BoxDecoration(color: Colors.white),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: <Widget>[
                                        Row(
                                          children: <Widget>[
                                            Flexible(
                                                flex: 5,
                                                child: Text(
                                                  "NVL SỬ DỤNG",
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                )),
                                            SizedBox(width: 5.w),
                                            // Flexible(
                                            //   flex: 5,
                                            //   child: Container(
                                            //     decoration: const BoxDecoration(),
                                            //     child: ElevatedButton(
                                            //       style: ButtonStyle(
                                            //         shape: MaterialStateProperty.all<
                                            //             RoundedRectangleBorder>(
                                            //             RoundedRectangleBorder(
                                            //                 borderRadius:
                                            //                 BorderRadius
                                            //                     .circular(5.r),
                                            //                 side: const BorderSide(
                                            //                     color:
                                            //                     Colors.white))),
                                            //         side: MaterialStateProperty.all(
                                            //           const BorderSide(
                                            //             color: Color(0xff4caf50),
                                            //           ),
                                            //         ),
                                            //         backgroundColor:
                                            //         MaterialStateProperty.all(
                                            //             const Color(
                                            //                 0xff4caf50)),
                                            //       ),
                                            //       onPressed: () {
                                            //         _addList();
                                            //       },
                                            //       child: Icon(Icons.add,
                                            //           size: 25.sp,
                                            //           color: Colors.white),
                                            //     ),
                                            //   ),
                                            // ),
                                          ],
                                        ),
                                        ListView.builder(
                                            physics: const NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: _lsTextEditingController.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              return Container(
                                                width: double.infinity,
                                                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
                                                decoration: const BoxDecoration(color: Colors.white),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: <Widget>[
                                                    Align(
                                                      alignment: Alignment.centerRight,
                                                      child: Container(
                                                        decoration: const BoxDecoration(),
                                                        child: ElevatedButton.icon(
                                                          style: ButtonStyle(
                                                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                                borderRadius: BorderRadius.circular(5.r),
                                                                side: const BorderSide(color: Colors.white))),
                                                            side: MaterialStateProperty.all(
                                                              const BorderSide(
                                                                color: Color(0xff303F9F),
                                                              ),
                                                            ),
                                                            backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                          ),
                                                          onPressed: () async {
                                                            String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                            DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                            DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                            if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                              Platform.isAndroid
                                                                  ? showDialog(
                                                                      context: context,
                                                                      barrierDismissible: false,
                                                                      builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                                  : showCupertinoDialog(
                                                                      context: context,
                                                                      barrierDismissible: false,
                                                                      builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                            } else {
                                                              if (_lsLoadingMaterial[index] == true) {
                                                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                                    backgroundColor: Colors.black,
                                                                    content: Text(
                                                                      'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
                                                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                                    ),
                                                                    duration: const Duration(seconds: 2)));
                                                              } else {
                                                                if (_lsFocusNode[index].hasFocus) {
                                                                  FocusScope.of(context).unfocus();
                                                                  await Future<void>.delayed(const Duration(milliseconds: 500));
                                                                  final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                                  if (data == null) return;
                                                                  if ((data as GetBackDataQRCodePage).isScan == true) {
                                                                    if (!mounted) return;
                                                                    await _getDataRawMaterial(data.materialID.toString(), context, index);
                                                                  }
                                                                } else {
                                                                  final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                                  if (data == null) return;
                                                                  if ((data as GetBackDataQRCodePage).isScan == true) {
                                                                    if (!mounted) return;
                                                                    await _getDataRawMaterial(data.materialID.toString(), context, index);
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                          label: Text(
                                                            "Quét mã",
                                                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(height: 5.h),
                                                    IntrinsicHeight(
                                                      child: Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            flex: 4,
                                                            child: Container(
                                                                height: double.infinity,
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                decoration: BoxDecoration(
                                                                  color: const Color(0xff303F9F),
                                                                  border: Border(
                                                                    top: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    left: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    right: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    bottom: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                  ),
                                                                ),
                                                                child: Text("Sloc:",
                                                                    style: TextStyle(
                                                                        fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold))),
                                                          ),
                                                          Expanded(
                                                            flex: 6,
                                                            child: Container(
                                                                height: double.infinity,
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                decoration: BoxDecoration(
                                                                  color: const Color(0xFFFFFFFF),
                                                                  border: Border(
                                                                    right: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    bottom: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    top: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                  ),
                                                                ),
                                                                child: _lsLoadingQuantity[index] == true
                                                                    ? Text(
                                                                        '...Loading',
                                                                        style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                                      )
                                                                    : Column(
                                                                        children: [
                                                                          Container(
                                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                                            decoration: BoxDecoration(
                                                                              border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                                              borderRadius: BorderRadius.circular(3.r),
                                                                            ),
                                                                            child: DropdownButtonHideUnderline(
                                                                              child: DropdownButton<DataQuantityMaterialUsedShift>(
                                                                                isExpanded: true,
                                                                                isDense: true,
                                                                                itemHeight: null,
                                                                                value: _lsDataSlocQuantityUsedShift[index]
                                                                                    .selectedDataQuantityMaterialUsedShift,
                                                                                iconSize: 15.sp,
                                                                                style: const TextStyle(color: Colors.white),
                                                                                onChanged: (DataQuantityMaterialUsedShift? value) {
                                                                                  setState(() {
                                                                                    _lsDataSlocQuantityUsedShift[index]
                                                                                        .selectedDataQuantityMaterialUsedShift = value!;
                                                                                    if (_lsDataSlocQuantityUsedShift[index]
                                                                                            .selectedDataQuantityMaterialUsedShift ==
                                                                                        null) {
                                                                                      _lsErrorSelectedSloc[index] = true;
                                                                                    } else {
                                                                                      _lsErrorSelectedSloc[index] = false;
                                                                                    }
                                                                                  });
                                                                                },
                                                                                items: _lsDataSlocQuantityUsedShift[index]
                                                                                    .dataQuantityMaterialUsedShift!
                                                                                    .map((DataQuantityMaterialUsedShift sloc) {
                                                                                  return DropdownMenuItem<DataQuantityMaterialUsedShift>(
                                                                                      value: sloc,
                                                                                      child: Padding(
                                                                                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                                        child: Text(
                                                                                          sloc.slocDisplay ?? " ",
                                                                                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                        ),
                                                                                      ));
                                                                                }).toList(),
                                                                                selectedItemBuilder: (BuildContext context) {
                                                                                  return _lsDataSlocQuantityUsedShift[index]
                                                                                      .dataQuantityMaterialUsedShift!
                                                                                      .map<Widget>((DataQuantityMaterialUsedShift sloc) {
                                                                                    return Text(sloc.slocDisplay ?? "",
                                                                                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                        overflow: TextOverflow.ellipsis);
                                                                                  }).toList();
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          SizedBox(height: _lsErrorSelectedSloc[index] == true ? 5.h : 0),
                                                                          ContainerError.widgetError(
                                                                              _lsErrorSelectedSloc[index], "Vui lòng chọn Sloc"),
                                                                        ],
                                                                      )),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    // TableInfo(
                                                    //   textCL1: "Sloc:",
                                                    //   textCL2: _lsDataSlocAddressList[
                                                    //   index]
                                                    //       .selectedDataSlocAddress ==
                                                    //       null
                                                    //       ? ""
                                                    //       : _lsDataSlocAddressList[
                                                    //   index]
                                                    //       .selectedDataSlocAddress!
                                                    //       .slocDisplay ??
                                                    //       "",
                                                    //   colorCL1: 0xff303F9F,
                                                    //   colorCL2: 0xFFFFFFFF,
                                                    // ),
                                                    TableInfoNoTop(
                                                      textCL1: "Material:",
                                                      textCL2: _lsLoadingMaterial[index] == true
                                                          ? '...Loading'
                                                          : _lsMaterialRetailList[index].material ?? "",
                                                      colorCL1: 0xff303F9F,
                                                      colorCL2: 0xFFFFFFFF,
                                                    ),
                                                    TableInfoNoTop(
                                                      textCL1: "Description:",
                                                      textCL2: _lsLoadingMaterial[index] == true
                                                          ? '...Loading'
                                                          : _lsMaterialRetailList[index].description ?? "",
                                                      colorCL1: 0xff303F9F,
                                                      colorCL2: 0xFFFFFFFF,
                                                    ),
                                                    IntrinsicHeight(
                                                      child: Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            flex: 4,
                                                            child: Container(
                                                              height: double.infinity,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xff303F9F),
                                                                border: Border(
                                                                  left: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                "Số lô:",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                              ),
                                                            ),
                                                          ),
                                                          Expanded(
                                                            flex: 6,
                                                            child: Container(
                                                              height: double.infinity,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xFFFFFFFF),
                                                                border: Border(
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                _lsLoadingMaterial[index] == true
                                                                    ? '...Loading'
                                                                    : _lsMaterialRetailList[index].batchNumber ?? "",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                              ),
                                                            ),
                                                          ),
                                                          // Expanded(
                                                          //   flex: 2,
                                                          //   child: Container(
                                                          //     height: double.infinity,
                                                          //     alignment: Alignment.centerLeft,
                                                          //     padding: EdgeInsets.symmetric(
                                                          //         horizontal: 3.w,
                                                          //         vertical: 3.h),
                                                          //     decoration:
                                                          //     BoxDecoration(
                                                          //       color: const Color(
                                                          //           0xFFFFFFFF),
                                                          //       border: Border(
                                                          //         right: BorderSide(
                                                          //           color: Colors.black,
                                                          //           width: 0.5.w,
                                                          //         ),
                                                          //         bottom: BorderSide(
                                                          //           color: Colors.black,
                                                          //           width: 0.5.w,
                                                          //         ),
                                                          //       ),
                                                          //     ),
                                                          //     child: Text(
                                                          //       "",
                                                          //       style: TextStyle(
                                                          //           fontSize: 11.sp,
                                                          //           color: Colors.black),
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                        ],
                                                      ),
                                                    ),
                                                    IntrinsicHeight(
                                                      child: Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            flex: 4,
                                                            child: Container(
                                                                height: double.infinity,
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                decoration: BoxDecoration(
                                                                  color: const Color(0xff303F9F),
                                                                  border: Border(
                                                                    left: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    right: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    bottom: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                  ),
                                                                ),
                                                                child: Text("Công đoạn sử dụng:",
                                                                    style: TextStyle(
                                                                        fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold))),
                                                          ),
                                                          Expanded(
                                                            flex: 6,
                                                            child: Container(
                                                                height: double.infinity,
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                decoration: BoxDecoration(
                                                                    color: const Color(0xFFFFFFFF),
                                                                    border: Border(
                                                                      right: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                      bottom: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                    )),
                                                                child: Column(
                                                                  children: [
                                                                    Container(
                                                                      padding: REdgeInsets.all(3),
                                                                      decoration: BoxDecoration(
                                                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                                        borderRadius: BorderRadius.circular(3.r),
                                                                      ),
                                                                      child: DropdownButtonHideUnderline(
                                                                        child: DropdownButton<Steps>(
                                                                          isExpanded: true,
                                                                          isDense: true,
                                                                          itemHeight: null,
                                                                          value: _lsDataStepCodeList[index].selectedStepCode,
                                                                          iconSize: 15.sp,
                                                                          style: const TextStyle(color: Colors.white),
                                                                          onChanged: (Steps? value) async {
                                                                            _setStepCode(value, index);
                                                                          },
                                                                          items: _lsDataStepCodeList[index].lsDataStepCode!.map((Steps steps) {
                                                                            return DropdownMenuItem<Steps>(
                                                                                value: steps,
                                                                                child: Padding(
                                                                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                                  child: Text(
                                                                                    steps.stepCodeDisplay ?? " ",
                                                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                  ),
                                                                                ));
                                                                          }).toList(),
                                                                          selectedItemBuilder: (BuildContext context) {
                                                                            return _lsDataStepCodeList[index]
                                                                                .lsDataStepCode!
                                                                                .map<Widget>((Steps steps) {
                                                                              return Text(steps.stepCodeDisplay ?? "",
                                                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                  overflow: TextOverflow.ellipsis);
                                                                            }).toList();
                                                                          },
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    SizedBox(height: _lsErrorStepCode[index] == true ? 5.h : 0),
                                                                    ContainerError.widgetError(_lsErrorStepCode[index], "Vui lòng chọn stepCode"),
                                                                  ],
                                                                )),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    IntrinsicHeight(
                                                      child: Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            flex: 4,
                                                            child: Container(
                                                              height: double.infinity,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xff303F9F),
                                                                border: Border(
                                                                  left: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                "SL sử dụng trong ngày:",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                              ),
                                                            ),
                                                          ),
                                                          Expanded(
                                                            flex: 6,
                                                            child: Container(
                                                              height: double.infinity,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xFFFFFFFF),
                                                                border: Border(
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                _lsDataSlocQuantityUsedShift[index].selectedDataQuantityMaterialUsedShift == null
                                                                    ? ""
                                                                    : _lsDataSlocQuantityUsedShift[index]
                                                                                .selectedDataQuantityMaterialUsedShift!
                                                                                .quantity ==
                                                                            null
                                                                        ? ""
                                                                        : _lsDataSlocQuantityUsedShift[index]
                                                                                .selectedDataQuantityMaterialUsedShift!
                                                                                .quantity!
                                                                                .toString() +
                                                                            " ${_lsDataSlocQuantityUsedShift[index].selectedDataQuantityMaterialUsedShift!.unit ?? ""}",
                                                                style: TextStyle(
                                                                  fontSize: 11.sp,
                                                                  color: Colors.red.shade900,
                                                                  fontWeight: FontWeight.bold,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    IntrinsicHeight(
                                                      child: Row(
                                                        children: <Widget>[
                                                          Expanded(
                                                            flex: 4,
                                                            child: Container(
                                                              height: double.infinity,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xff303F9F),
                                                                border: Border(
                                                                  left: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                "SL sử dụng cho LSX:",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                              ),
                                                            ),
                                                          ),
                                                          Expanded(
                                                              flex: 4,
                                                              child: Container(
                                                                height: double.infinity,
                                                                alignment: Alignment.centerRight,
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                decoration: BoxDecoration(
                                                                  color: const Color(0xFFFFFFFF),
                                                                  border: Border(
                                                                    right: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                    bottom: BorderSide(
                                                                      color: Colors.black,
                                                                      width: 0.5.w,
                                                                    ),
                                                                  ),
                                                                ),

                                                                child: Column(children: <Widget>[
                                                                  Container(
                                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                    decoration: BoxDecoration(
                                                                        border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                        borderRadius: BorderRadius.circular(3.r)),
                                                                    child: TextFormField(
                                                                      maxLines: null,
                                                                      focusNode: _lsFocusNode[index],
                                                                      textAlign: TextAlign.center,
                                                                      controller: _lsTextEditingController[index],
                                                                      style: TextStyle(fontSize: 12.sp),
                                                                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                      inputFormatters: <TextInputFormatter>[
                                                                        FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d*')),
                                                                        CommaTextInputFormatter()
                                                                      ],
                                                                      decoration: InputDecoration(
                                                                        border: InputBorder.none,
                                                                        isDense: true,
                                                                        contentPadding: EdgeInsets.zero,
                                                                        errorBorder: InputBorder.none,
                                                                        disabledBorder: InputBorder.none,
                                                                        filled: true,
                                                                        fillColor: Colors.white,
                                                                        hintStyle: TextStyle(fontSize: 12.sp),
                                                                      ),
                                                                      onChanged: (value) {
                                                                        if (_lsTextEditingController[index].text.isNotEmpty) {
                                                                          if (_lsErrorController[index] != false) {
                                                                            setState(() {
                                                                              _lsErrorController[index] = false;
                                                                            });
                                                                          }
                                                                        } else {
                                                                          if (_lsErrorController[index] != true) {
                                                                            setState(() {
                                                                              _lsErrorController[index] = true;
                                                                            });
                                                                          }
                                                                        }
                                                                      },
                                                                    ),
                                                                  ),
                                                                  SizedBox(height: _lsErrorController[index] == true ? 10.h : 0),
                                                                  ContainerError.widgetError(
                                                                      _lsErrorController[index], 'Vui lòng nhập SL sử dụng cho LSX'),
                                                                ]),
                                                                // TextFormField(
                                                                //   focusNode: focusNode,
                                                                //   textAlign: TextAlign.right,
                                                                //   maxLines: null,
                                                                //   keyboardType: TextInputType.number,
                                                                //   inputFormatters: <TextInputFormatter>[
                                                                //     FilteringTextInputFormatter
                                                                //         .allow(RegExp(
                                                                //         "[0-9]")),
                                                                //   ],
                                                                //   controller: textEditingController,
                                                                //   style: TextStyle(fontSize: 12.sp),
                                                                //   decoration: InputDecoration(
                                                                //     border: InputBorder.none,
                                                                //     focusedBorder: OutlineInputBorder(
                                                                //       borderRadius:
                                                                //       BorderRadius.circular(3.r),
                                                                //       borderSide: BorderSide(
                                                                //           width: 0.5.w,
                                                                //           color: Colors.grey.shade400),
                                                                //     ),
                                                                //     enabledBorder: OutlineInputBorder(
                                                                //       borderRadius:
                                                                //       BorderRadius.circular(3.r),
                                                                //       borderSide: BorderSide(
                                                                //           width: 0.5.w,
                                                                //           color: Colors.grey.shade400),
                                                                //     ),
                                                                //     errorBorder: InputBorder.none,
                                                                //     disabledBorder: InputBorder.none,
                                                                //     filled: true,
                                                                //     isDense: true,
                                                                //     fillColor: Colors.white,
                                                                //     hintStyle:
                                                                //     TextStyle(fontSize: 12.sp),
                                                                //     contentPadding: EdgeInsets.symmetric(
                                                                //         horizontal: 3.w, vertical: 0.h),
                                                                //   ),
                                                                // ),
                                                              )),
                                                          Expanded(
                                                            flex: 2,
                                                            child: Container(
                                                              height: double.infinity,
                                                              alignment: Alignment.centerLeft,
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                              decoration: BoxDecoration(
                                                                color: const Color(0xFFFFFFFF),
                                                                border: Border(
                                                                  right: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                  bottom: BorderSide(
                                                                    color: Colors.black,
                                                                    width: 0.5.w,
                                                                  ),
                                                                ),
                                                              ),
                                                              child: Text(
                                                                _lsDataSlocQuantityUsedShift[index].selectedDataQuantityMaterialUsedShift == null
                                                                    ? ""
                                                                    : _lsDataSlocQuantityUsedShift[index]
                                                                            .selectedDataQuantityMaterialUsedShift!
                                                                            .unit ??
                                                                        "",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            }),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                                    decoration: const BoxDecoration(),
                                    child: ElevatedButton(
                                      style: ButtonStyle(
                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                        side: MaterialStateProperty.all(
                                          BorderSide(
                                            color: _lsMaterialRetailList.where((element) => element.rawMaterialCardId == null).isEmpty &&
                                                    _lsDataStepCodeList.where((element) => element.lsDataStepCode!.isEmpty).isEmpty &&
                                                    _lsDataSlocQuantityUsedShift
                                                        .where((element) => element.dataQuantityMaterialUsedShift!.isEmpty)
                                                        .isEmpty
                                                ? const Color(0xff303F9F)
                                                : Colors.grey.shade300,
                                          ),
                                        ),
                                        backgroundColor: MaterialStateProperty.all(
                                            _lsMaterialRetailList.where((element) => element.rawMaterialCardId == null).isEmpty &&
                                                    _lsDataStepCodeList.where((element) => element.lsDataStepCode!.isEmpty).isEmpty &&
                                                    _lsDataSlocQuantityUsedShift
                                                        .where((element) => element.dataQuantityMaterialUsedShift!.isEmpty)
                                                        .isEmpty
                                                ? const Color(0xff303F9F)
                                                : Colors.grey.shade300),
                                      ),
                                      onPressed: _lsMaterialRetailList.where((element) => element.rawMaterialCardId == null).isEmpty &&
                                              _lsDataStepCodeList.where((element) => element.lsDataStepCode!.isEmpty).isEmpty &&
                                              _lsDataSlocQuantityUsedShift.where((element) => element.dataQuantityMaterialUsedShift!.isEmpty).isEmpty
                                          ? () async {
                                              String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                              DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                              DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                              if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                Platform.isAndroid
                                                    ? showDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                    : showCupertinoDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                              } else {
                                                _checkError();
                                                if (_lsErrorController.where((element) => element == true).isEmpty &&
                                                    _lsErrorSelectedSloc.where((element) => element == true).isEmpty &&
                                                    _lsErrorStepCode.where((element) => element == true).isEmpty) {
                                                  FocusScope.of(context).unfocus();
                                                  List<MaterialRetailDetails>? materialRetailDetails =
                                                      MaterialRetailFunction.getMaterialRetailDetails(_lsTextEditingController, _lsMaterialRetailList,
                                                          _lsDataSlocQuantityUsedShift, _dataGetSOWBSByTask, _lsDataStepCodeList);
                                                  PostMaterialRetail postMaterialRetail = PostMaterialRetail(
                                                    taskId: widget.barcode,
                                                    materialRetailDetails: materialRetailDetails,
                                                  );
                                                  debugPrint(jsonEncode(postMaterialRetail));
                                                  await MaterialRetailFunction.sendMaterialRetail(postMaterialRetail, widget.token, context);
                                                }
                                              }
                                            }
                                          : null,
                                      child: Container(
                                        margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                        child: Text(
                                          "Lưu",
                                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ))
                            : Center(
                                child: Text(
                                _switchingStateData == null
                                    ? 'Thẻ treo không hợp lệ, Do chưa được ghi nhận thẻ treo này lần nào, Anh / Chị vui lòng sử dụng chức năng "PDA - Ghi nhận sản lượng!'
                                    : "Không tìm thấy thông tin LSX!",
                                style: TextStyle(fontSize: 15.sp),
                                textAlign: TextAlign.center,
                              ))
                    : LostConnect(checkConnect: () => _getSwitchingStages()));
  }
}
