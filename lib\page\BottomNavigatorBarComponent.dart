import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/QuantityInformationSelectedInfor.dart';
import 'package:ttf/utils/ui_helpers.dart';

import '../Widget/dialogWidget/DialogQualityInformation.dart';
import '../element/ImageQuatity.dart';
import '../element/QualityErrorValidate.dart';
import '../element/listImagePicker.dart';
import '../model/mulitListImageFile.dart';
import '../model/qualityControlApi.dart';
import '../repository/function/imageFunction.dart';
import '../repository/function/qualityControlFunction.dart';

class ItemThongTinKiemTra extends StatelessWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsQualityControlInformation;
  final List<ThongTinKiemTra>? lsQualityControlInformationIdList;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;
  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;
  final List<TextEditingController> lsControllerInformation;
  final List<TextEditingController> lsTextEditingController;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;
  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  ItemThongTinKiemTra({
    required this.index,
    required this.qualityControl,
    required this.lsGetFileInfor,
    required this.lsQualityControlInformation,
    required this.lsQualityControlInformationIdList,
    required this.checkVisiButtonInformation,
    required this.pickFileImageInformation,
    required this.deleteListQualityInformation,
    required this.deleteItemListInformation,
    required this.lsControllerInformation,
    required this.lsTextEditingController,
    required this.checkClearLsSelectedInfo,
    required this.pickerImage,
    required this.lsErrorInfor,
    required this.checkErrorSelectedInfo,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                ),
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                child: Center(
                  child: Text(
                    (index + 1).toString(),
                    style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              )),
          Expanded(
            flex: 9,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        Expanded(
                            flex: 5,
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  width: 0.5,
                                  color: Colors.grey.shade300,
                                ),
                              ),
                              child: Padding(
                                padding: REdgeInsets.all(5),
                                child: Column(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      ),
                                      child: Center(
                                        child: TypeAheadField(
                                          suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                            constraints: BoxConstraints(
                                              minWidth: 150.w,
                                            ),
                                          ),
                                          textFieldConfiguration: TextFieldConfiguration(
                                              enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                                              decoration: InputDecoration(
                                                labelStyle: TextStyle(fontSize: 11.sp),
                                                contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                isDense: true,
                                                border: InputBorder.none,
                                                focusedBorder: InputBorder.none,
                                                enabledBorder: InputBorder.none,
                                              ),
                                              controller: lsControllerInformation[index],
                                              // focusNode: focusInformation[index],
                                              style: TextStyle(fontSize: 12.sp),
                                              onChanged: (value) {
                                                checkClearLsSelectedInfo(index);
                                              }),
                                          suggestionsCallback: (pattern) {
                                            return QualityControlFunction.filterQualityControlInformationIdList(
                                                lsQualityControlInformationIdList ?? [], pattern);
                                          },
                                          itemBuilder: (context, suggestion) {
                                            return ListTile(
                                              title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                                            );
                                          },
                                          onSuggestionSelected: (suggestion) {
                                            checkErrorSelectedInfo(
                                                QuantityInformationSelected(index: index, qualityControlInformationIdList: suggestion));
                                          },
                                          noItemsFoundBuilder: (value) {
                                            return Padding(
                                                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                                          },
                                        ),
                                      ),
                                    ),
                                    Visibility(
                                      visible: lsErrorInfor[index],
                                      child: SizedBox(height: 10.h),
                                    ),
                                    QualityErrorValidate(
                                      error: lsErrorInfor[index],
                                      text: "Vui lòng chọn thông tin kiểm tra",
                                    )
                                  ],
                                ),
                              ),
                            )),
                        Expanded(
                          flex: 4,
                          child: Container(
                            // key: UniqueKey(),
                            decoration: BoxDecoration(
                              border: Border.all(width: 0.5, color: Colors.grey.shade300),
                            ),
                            child: Padding(
                              padding: REdgeInsets.all(5),
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                ),
                                child: TextFormField(
                                  enabled: qualityControl!.qcType == "NVL"
                                      ? qualityControl!.qualityChecker != null
                                          ? false
                                          : true
                                      : true,
                                  maxLines: null,
                                  textAlign: TextAlign.center,
                                  controller: lsTextEditingController[index],
                                  style: TextStyle(fontSize: 12.sp),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    filled: true,
                                    isDense: true,
                                    fillColor: Colors.white,
                                    hintStyle: TextStyle(fontSize: 12.sp),
                                    contentPadding: EdgeInsets.zero,
                                  ),
                                  // onChanged: (value){
                                  //   // resetState();
                                  // },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 8,
                            child: lsGetFileInfor[index].isEmpty
                                ? Row(
                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                    children: <Widget>[
                                      GestureDetector(
                                        onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                                            ? null
                                            : () async {
                                                final check = await QualityControlFunction.pickImage(context);
                                                debugPrint(check.toString());
                                                if (check != null) {
                                                  bool checkPermission = await ImageFunction.handlePermission(check);
                                                  if (checkPermission == true) {
                                                    if (check == true) {
                                                      List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                                        maxWidth: globalImageConfig.maxWidth,
                                                        maxHeight: globalImageConfig.maxHeight,
                                                        imageQuality: globalImageConfig.imageQuality,
                                                      );
                                                      // if (!mounted) return;
                                                      if (selectedImages.isEmpty) return;
                                                      for (var i in selectedImages) {
                                                        final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                        pickFileImageInformation(MultiListImageFile(index: index, file: itemImage));
                                                      }
                                                    } else {
                                                      final image = await ImagePicker().pickImage(
                                                          maxWidth: globalImageConfig.maxWidth,
                                                          maxHeight: globalImageConfig.maxHeight,
                                                          imageQuality: globalImageConfig.imageQuality,
                                                          source: ImageSource.camera);
                                                      // if (!mounted) return;
                                                      if (image == null) return;
                                                      final imageProfile = await ImageFunction.saveImage(image.path);
                                                      pickFileImageInformation(MultiListImageFile(index: index, file: imageProfile));
                                                    }
                                                  }
                                                }
                                              },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: Center(
                                            child: Text(
                                              "Chọn tệp",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      Center(
                                        child: Text(
                                          "Chưa chọn tệp nào",
                                          style: TextStyle(fontSize: 11.sp),
                                        ),
                                      ),
                                    ],
                                  )
                                : Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      GestureDetector(
                                        onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                                            ? null
                                            : () async {
                                                final check = await QualityControlFunction.pickImage(context);
                                                debugPrint(check.toString());
                                                if (check != null) {
                                                  bool checkPermission = await ImageFunction.handlePermission(check);
                                                  if (checkPermission == true) {
                                                    if (check == true) {
                                                      List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                                        maxWidth: globalImageConfig.maxWidth,
                                                        maxHeight: globalImageConfig.maxHeight,
                                                        imageQuality: globalImageConfig.imageQuality,
                                                      );
                                                      if (selectedImages.isEmpty) return;
                                                      for (var i in selectedImages) {
                                                        final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                        // if (!mounted) return;
                                                        pickFileImageInformation(MultiListImageFile(index: index, file: itemImage));
                                                      }
                                                    } else {
                                                      final image = await ImagePicker().pickImage(
                                                          maxWidth: globalImageConfig.maxWidth,
                                                          maxHeight: globalImageConfig.maxHeight,
                                                          imageQuality: globalImageConfig.imageQuality,
                                                          source: ImageSource.camera);
                                                      // if (!mounted) return;
                                                      if (image == null) return;
                                                      final imageProfile = await ImageFunction.saveImage(image.path);
                                                      pickFileImageInformation(MultiListImageFile(index: index, file: imageProfile));
                                                    }
                                                  }
                                                }
                                              },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: Text(
                                            "Chọn tệp",
                                            style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.h),
                                      Wrap(
                                          spacing: 3.w,
                                          runSpacing: 3.h,
                                          children: List.generate(lsGetFileInfor[index].length, (indexImage) {
                                            // String filename = basename(
                                            //     lsGetFile[index][indexImage]
                                            //         .path);
                                            return SizedBox(
                                              width: 50.w,
                                              child: Stack(children: <Widget>[
                                                GestureDetector(
                                                  onTap: () {
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) => ImageQuatity(lsImage: lsGetFileInfor[index], index: indexImage),
                                                      ),
                                                    );
                                                  },
                                                  child: ListImagePicker(fileImage: lsGetFileInfor[index][indexImage]),
                                                ),
                                                Align(
                                                  alignment: Alignment.topRight,
                                                  child: IconButton(
                                                    highlightColor: Colors.transparent,
                                                    hoverColor: Colors.transparent,
                                                    constraints: const BoxConstraints(),
                                                    iconSize: 17.sp,
                                                    color: Colors.red.shade800,
                                                    icon: const Icon(Icons.remove_circle),
                                                    onPressed: () {
                                                      deleteListQualityInformation(MultiListImageDeleteFile(index: index, indexImage: indexImage));
                                                    },
                                                  ),
                                                )
                                              ]),
                                            );
                                          })),
                                      SizedBox(height: 10.h),
                                    ],
                                  ),
                          ),
                          Visibility(
                            visible: (lsQualityControlInformation[index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
                            child: Expanded(
                              flex: 2,
                              child: GestureDetector(
                                onTap: () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return DialogImageQualityInformation(
                                        title: 'THÔNG TIN KIỂM TRA ' + lsQualityControlInformation[index].qualityControlInformationCode.toString(),
                                        listImage: lsQualityControlInformation[index].checkedFileViewModel ?? [],
                                      );
                                    },
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 5.h),
                                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                                  decoration: BoxDecoration(
                                    color: const Color(0xff0052cc),
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(2.r),
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.image,
                                    color: Colors.white,
                                    size: 15.sp,
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: checkVisiButtonInformation[index],
                    child: IntrinsicHeight(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade300),
                        ),
                        child: Align(
                          alignment: Alignment.topRight,
                          child: IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            constraints: const BoxConstraints(),
                            iconSize: 17.sp,
                            color: Colors.red.shade800,
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              deleteItemListInformation(index);
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ItemThongTinKiemTra2 extends StatelessWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsThongTinKiemTra;
  final List<ThongTinKiemTra>? lsThongTinKiemTraMasterData;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;

  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;

  final List<TextEditingController> lsControllerThongTinKiemTra;
  final List<TextEditingController> lsControllerSoSanPhamLoi;
  final List<TextEditingController> lsControllerGhiChu;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;

  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  final Function updateTongSoSanPhamLoi;

  ItemThongTinKiemTra2(
      {required this.index,
      required this.qualityControl,
      required this.lsGetFileInfor,
      required this.lsThongTinKiemTra,
      required this.lsThongTinKiemTraMasterData,
      required this.checkVisiButtonInformation,
      required this.pickFileImageInformation,
      required this.deleteListQualityInformation,
      required this.deleteItemListInformation,
      required this.lsControllerThongTinKiemTra,
      required this.lsControllerSoSanPhamLoi,
      required this.lsControllerGhiChu,
      required this.checkClearLsSelectedInfo,
      required this.pickerImage,
      required this.lsErrorInfor,
      required this.checkErrorSelectedInfo,
      required this.updateTongSoSanPhamLoi});

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 1. STT
          Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                ),
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                child: Center(
                  child: Text(
                    (index + 1).toString(),
                    style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              )),
          Expanded(
            flex: 12,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // 2. Hạng mục kiểm tra
                        Expanded(
                            flex: 5,
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  width: 0.5,
                                  color: Colors.grey.shade300,
                                ),
                              ),
                              child: Padding(
                                padding: REdgeInsets.all(5),
                                child: Column(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      ),
                                      child: Center(
                                        child: TypeAheadField(
                                          suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                            constraints: BoxConstraints(
                                              minWidth: 150.w,
                                            ),
                                          ),
                                          textFieldConfiguration: TextFieldConfiguration(
                                              enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                                              maxLines: null,
                                              decoration: InputDecoration(
                                                labelStyle: TextStyle(fontSize: 11.sp),
                                                contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                isDense: true,
                                                border: InputBorder.none,
                                                focusedBorder: InputBorder.none,
                                                enabledBorder: InputBorder.none,
                                              ),
                                              controller: lsControllerThongTinKiemTra[index],
                                              // focusNode: focusInformation[index],
                                              style: TextStyle(fontSize: 12.sp),
                                              onChanged: (value) {
                                                checkClearLsSelectedInfo(index);
                                              }),
                                          suggestionsCallback: (pattern) {
                                            return QualityControlFunction.filterQualityControlInformationIdList(
                                                lsThongTinKiemTraMasterData ?? [], pattern);
                                          },
                                          itemBuilder: (context, suggestion) {
                                            return ListTile(
                                              title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                                            );
                                          },
                                          onSuggestionSelected: (suggestion) {
                                            checkErrorSelectedInfo(
                                                QuantityInformationSelected(index: index, qualityControlInformationIdList: suggestion));
                                          },
                                          noItemsFoundBuilder: (value) {
                                            return Padding(
                                                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                                          },
                                        ),
                                      ),
                                    ),
                                    Visibility(
                                      visible: lsErrorInfor[index],
                                      child: SizedBox(height: 10.h),
                                    ),
                                    QualityErrorValidate(
                                      error: lsErrorInfor[index],
                                      text: "Vui lòng chọn hạng mục kiểm tra",
                                    )
                                  ],
                                ),
                              ),
                            )),
                        // 3. SL sản phẩm lỗi
                        Expanded(
                          flex: 3,
                          child: Container(
                            // key: UniqueKey(),
                            decoration: BoxDecoration(
                              border: Border.all(width: 0.5, color: Colors.grey.shade300),
                            ),
                            child: Padding(
                              padding: REdgeInsets.all(5),
                              child: Container(
                                // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                ),
                                child: TextFormField(
                                  enabled: qualityControl!.qcType == "NVL"
                                      ? qualityControl!.qualityChecker != null
                                          ? false
                                          : true
                                      : true,
                                  maxLines: null,
                                  keyboardType: TextInputType.number,
                                  inputFormatters: <TextInputFormatter>[
                                    FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                  ],
                                  textAlign: TextAlign.center,
                                  controller: lsControllerSoSanPhamLoi[index],
                                  style: TextStyle(fontSize: 12.sp),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    filled: true,
                                    isDense: true,
                                    fillColor: Colors.white,
                                    hintStyle: TextStyle(fontSize: 12.sp),
                                    // contentPadding: EdgeInsets.zero,
                                    contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
                                  ),
                                  onChanged: (value) {
                                    updateTongSoSanPhamLoi();
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                        // 4. Ghi chú
                        Expanded(
                          flex: 4,
                          child: Container(
                            // key: UniqueKey(),
                            decoration: BoxDecoration(
                              border: Border.all(width: 0.5, color: Colors.grey.shade300),
                            ),
                            child: Padding(
                              padding: REdgeInsets.all(5),
                              child: Container(
                                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                ),
                                child: TextFormField(
                                  enabled: qualityControl!.qcType == "NVL"
                                      ? qualityControl!.qualityChecker != null
                                          ? false
                                          : true
                                      : true,
                                  maxLines: null,
                                  textAlign: TextAlign.center,
                                  controller: lsControllerGhiChu[index],
                                  style: TextStyle(fontSize: 12.sp),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    filled: true,
                                    isDense: true,
                                    fillColor: Colors.white,
                                    hintStyle: TextStyle(fontSize: 12.sp),
                                    // contentPadding: EdgeInsets.zero,
                                    contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
                                  ),
                                  // onChanged: (value){
                                  //   // resetState();
                                  // },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 8,
                            child: lsGetFileInfor[index].isEmpty
                                ? Row(
                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                    children: <Widget>[
                                      GestureDetector(
                                        onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                                            ? null
                                            : () async {
                                                final check = await QualityControlFunction.pickImage(context);
                                                debugPrint(check.toString());
                                                if (check != null) {
                                                  bool checkPermission = await ImageFunction.handlePermission(check);
                                                  if (checkPermission == true) {
                                                    if (check == true) {
                                                      List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                                        maxWidth: globalImageConfig.maxWidth,
                                                        maxHeight: globalImageConfig.maxHeight,
                                                        imageQuality: globalImageConfig.imageQuality,
                                                      );
                                                      // if (!mounted) return;
                                                      if (selectedImages.isEmpty) return;
                                                      for (var i in selectedImages) {
                                                        final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                        pickFileImageInformation(MultiListImageFile(index: index, file: itemImage));
                                                      }
                                                    } else {
                                                      final image = await ImagePicker().pickImage(
                                                          maxWidth: globalImageConfig.maxWidth,
                                                          maxHeight: globalImageConfig.maxHeight,
                                                          imageQuality: globalImageConfig.imageQuality,
                                                          source: ImageSource.camera);
                                                      // if (!mounted) return;
                                                      if (image == null) return;
                                                      final imageProfile = await ImageFunction.saveImage(image.path);
                                                      pickFileImageInformation(MultiListImageFile(index: index, file: imageProfile));
                                                    }
                                                  }
                                                }
                                              },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: Center(
                                            child: Text(
                                              "Chọn tệp",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      Center(
                                        child: Text(
                                          "Chưa chọn tệp nào",
                                          style: TextStyle(fontSize: 11.sp),
                                        ),
                                      ),
                                    ],
                                  )
                                : Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      GestureDetector(
                                        onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                                            ? null
                                            : () async {
                                                final check = await QualityControlFunction.pickImage(context);
                                                debugPrint(check.toString());
                                                if (check != null) {
                                                  bool checkPermission = await ImageFunction.handlePermission(check);
                                                  if (checkPermission == true) {
                                                    if (check == true) {
                                                      List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                                        maxWidth: globalImageConfig.maxWidth,
                                                        maxHeight: globalImageConfig.maxHeight,
                                                        imageQuality: globalImageConfig.imageQuality,
                                                      );
                                                      if (selectedImages.isEmpty) return;
                                                      for (var i in selectedImages) {
                                                        final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                        // if (!mounted) return;
                                                        pickFileImageInformation(MultiListImageFile(index: index, file: itemImage));
                                                      }
                                                    } else {
                                                      final image = await ImagePicker().pickImage(
                                                          maxWidth: globalImageConfig.maxWidth,
                                                          maxHeight: globalImageConfig.maxHeight,
                                                          imageQuality: globalImageConfig.imageQuality,
                                                          source: ImageSource.camera);
                                                      // if (!mounted) return;
                                                      if (image == null) return;
                                                      final imageProfile = await ImageFunction.saveImage(image.path);
                                                      pickFileImageInformation(MultiListImageFile(index: index, file: imageProfile));
                                                    }
                                                  }
                                                }
                                              },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: Text(
                                            "Chọn tệp",
                                            style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.h),
                                      Wrap(
                                          spacing: 3.w,
                                          runSpacing: 3.h,
                                          children: List.generate(lsGetFileInfor[index].length, (indexImage) {
                                            // String filename = basename(
                                            //     lsGetFile[index][indexImage]
                                            //         .path);
                                            return SizedBox(
                                              width: 50.w,
                                              child: Stack(children: <Widget>[
                                                GestureDetector(
                                                  onTap: () {
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) => ImageQuatity(lsImage: lsGetFileInfor[index], index: indexImage),
                                                      ),
                                                    );
                                                  },
                                                  child: ListImagePicker(fileImage: lsGetFileInfor[index][indexImage]),
                                                ),
                                                Align(
                                                  alignment: Alignment.topRight,
                                                  child: IconButton(
                                                    highlightColor: Colors.transparent,
                                                    hoverColor: Colors.transparent,
                                                    constraints: const BoxConstraints(),
                                                    iconSize: 17.sp,
                                                    color: Colors.red.shade800,
                                                    icon: const Icon(Icons.remove_circle),
                                                    onPressed: () {
                                                      deleteListQualityInformation(MultiListImageDeleteFile(index: index, indexImage: indexImage));
                                                    },
                                                  ),
                                                )
                                              ]),
                                            );
                                          })),
                                      SizedBox(height: 10.h),
                                    ],
                                  ),
                          ),
                          Visibility(
                            visible: (lsThongTinKiemTra[index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
                            child: Expanded(
                              flex: 2,
                              child: GestureDetector(
                                onTap: () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return DialogImageQualityInformation(
                                        title: 'THÔNG TIN KIỂM TRA ' + lsThongTinKiemTra[index].qualityControlInformationCode.toString(),
                                        listImage: lsThongTinKiemTra[index].checkedFileViewModel ?? [],
                                      );
                                    },
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 5.h),
                                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                                  decoration: BoxDecoration(
                                    color: const Color(0xff0052cc),
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(2.r),
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.image,
                                    color: Colors.white,
                                    size: 15.sp,
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: checkVisiButtonInformation[index],
                    child: IntrinsicHeight(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade300),
                        ),
                        child: Align(
                          alignment: Alignment.topRight,
                          child: IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            constraints: const BoxConstraints(),
                            iconSize: 17.sp,
                            color: Colors.red.shade800,
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              deleteItemListInformation(index);
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ItemThongTinKiemTraQAQC extends StatefulWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsThongTinKiemTra;
  final List<ThongTinKiemTra>? lsThongTinKiemTraSelectMasterData;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;

  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;

  final List<TextEditingController> lsControllerThongTinKiemTra;
  final List<TextEditingController> lsControllerSoSanPhamLoi;
  final List<TextEditingController> lsControllerGhiChu;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;

  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  final Function updateTongSoSanPhamLoi;
  final Function(RadioValue?, int) onRadioChanged;

  const ItemThongTinKiemTraQAQC({
    super.key,
    required this.index,
    required this.qualityControl,
    required this.lsGetFileInfor,
    required this.lsThongTinKiemTra,
    required this.lsThongTinKiemTraSelectMasterData,
    required this.checkVisiButtonInformation,
    required this.pickFileImageInformation,
    required this.deleteListQualityInformation,
    required this.deleteItemListInformation,
    required this.lsControllerThongTinKiemTra,
    required this.lsControllerSoSanPhamLoi,
    required this.lsControllerGhiChu,
    required this.checkClearLsSelectedInfo,
    required this.pickerImage,
    required this.lsErrorInfor,
    required this.checkErrorSelectedInfo,
    required this.updateTongSoSanPhamLoi,
    required this.onRadioChanged,
  });

  @override
  State<ItemThongTinKiemTraQAQC> createState() => _ItemThongTinKiemTraQAQCState();
}

class _ItemThongTinKiemTraQAQCState extends State<ItemThongTinKiemTraQAQC> {
  RadioValue _radioValue = RadioValue.khongKiemTra;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 1. STT
          buildStt(),
          Expanded(
            flex: 12,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // 2. Hạng mục kiểm tra
                        buildHangMucKiemTra(),
                        // 3. SL sản phẩm lỗi
                        buildSoSanPhamLoi(),
                        // 4. Ghi chú
                        buildGhiChu(),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          buildImagePicker(context),
                          buildImageViewer(context),
                        ],
                      ),
                    ),
                  ),
                  IntrinsicHeight(
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: <Widget>[
                            buildRadioGesture(RadioValue.dat, _radioValue, "Đạt"),
                            buildRadioGesture(RadioValue.khongDat, _radioValue, "Không đạt"),
                            buildRadioGesture(RadioValue.khongKiemTra, _radioValue, "Không kiểm tra"),
                          ],
                        )
                      ],
                    ),
                  ),
                  buildDeleteButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRadioGesture(RadioValue value, RadioValue groupValue, String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        children: <Widget>[
          Radio<RadioValue>(
            visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            value: value,
            // groupValue: groupValue,
            groupValue: getRadioValue(widget.lsThongTinKiemTra[widget.index].outcomeStatus),
            onChanged: (RadioValue? newValue) {
              setState(() {
                _radioValue = newValue!;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(newValue!);
              });
              widget.onRadioChanged(newValue, widget.index);
            },
            activeColor: const Color(0xff0052cc),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _radioValue = value;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(value!);
              });
              widget.onRadioChanged(value, widget.index);
            },
            child: RadioLabel(text: text),
          ),
        ],
      ),
    );
  }

  Visibility buildDeleteButton() {
    return Visibility(
      visible: widget.checkVisiButtonInformation[widget.index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                widget.deleteItemListInformation(widget.index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Visibility buildImageViewer(BuildContext context) {
    return Visibility(
      visible: (widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
      child: Expanded(
        flex: 2,
        child: GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return DialogImageQualityInformation(
                  title: 'THÔNG TIN KIỂM TRA ' + widget.lsThongTinKiemTra[widget.index].qualityControlInformationCode.toString(),
                  listImage: widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? [],
                );
              },
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5.h),
            margin: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: BoxDecoration(
              color: const Color(0xff0052cc),
              borderRadius: BorderRadius.all(
                Radius.circular(2.r),
              ),
            ),
            child: Icon(
              Icons.image,
              color: Colors.white,
              size: 15.sp,
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildImagePicker(BuildContext context) {
    return Expanded(
      flex: 8,
      child: widget.lsGetFileInfor[widget.index].isEmpty
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                // if (!mounted) return;
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Center(
                      child: Text(
                        "Chọn tệp",
                        style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Center(
                  child: Text(
                    "Chưa chọn tệp nào",
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  // if (!mounted) return;
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Text(
                      "Chọn tệp",
                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                Wrap(
                    spacing: 3.w,
                    runSpacing: 3.h,
                    children: List.generate(widget.lsGetFileInfor[widget.index].length, (indexImage) {
                      // String filename = basename(
                      //     lsGetFile[index][indexImage]
                      //         .path);
                      return SizedBox(
                        width: 50.w,
                        child: Stack(children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ImageQuatity(lsImage: widget.lsGetFileInfor[widget.index], index: indexImage),
                                ),
                              );
                            },
                            child: ListImagePicker(fileImage: widget.lsGetFileInfor[widget.index][indexImage]),
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              highlightColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              constraints: const BoxConstraints(),
                              iconSize: 17.sp,
                              color: Colors.red.shade800,
                              icon: const Icon(Icons.remove_circle),
                              onPressed: () {
                                widget.deleteListQualityInformation(MultiListImageDeleteFile(index: widget.index, indexImage: indexImage));
                              },
                            ),
                          )
                        ]),
                      );
                    })),
                SizedBox(height: 10.h),
              ],
            ),
    );
  }

  Expanded buildGhiChu() {
    return Expanded(
      flex: 4,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              textAlign: TextAlign.center,
              controller: widget.lsControllerGhiChu[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              // onChanged: (value){
              //   // resetState();
              // },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildSoSanPhamLoi() {
    return Expanded(
      flex: 3,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              keyboardType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              textAlign: TextAlign.center,
              controller: widget.lsControllerSoSanPhamLoi[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              onChanged: (value) {
                widget.updateTongSoSanPhamLoi();
              },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildHangMucKiemTra() {
    return Expanded(
        flex: 5,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              width: 0.5,
              color: Colors.grey.shade300,
            ),
          ),
          child: Padding(
            padding: REdgeInsets.all(5),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    // border: checkVisiButtonInformation[index] ? Border.all(width: 0.5, color: Colors.grey.shade400) : null,
                  ),
                  child: Center(
                    child: TypeAheadField(
                      suggestionsBoxDecoration: SuggestionsBoxDecoration(
                        constraints: BoxConstraints(
                          minWidth: 150.w,
                        ),
                      ),
                      textFieldConfiguration: TextFieldConfiguration(
                          // enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                          enabled: widget.checkVisiButtonInformation[widget.index],
                          maxLines: null,
                          decoration: InputDecoration(
                            labelStyle: TextStyle(fontSize: 11.sp),
                            contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                            isDense: true,
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                          ),
                          controller: widget.lsControllerThongTinKiemTra[widget.index],
                          // focusNode: focusInformation[index],
                          // style: TextStyle(fontSize: 12.sp),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 11.sp,
                          ),
                          onChanged: (value) {
                            widget.checkClearLsSelectedInfo(widget.index);
                          }),
                      suggestionsCallback: (pattern) {
                        return QualityControlFunction.filterQualityControlInformationIdList(widget.lsThongTinKiemTraSelectMasterData ?? [], pattern);
                      },
                      itemBuilder: (context, suggestion) {
                        return ListTile(
                          title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        widget.checkErrorSelectedInfo(QuantityInformationSelected(index: widget.index, qualityControlInformationIdList: suggestion));
                      },
                      noItemsFoundBuilder: (value) {
                        return Padding(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                            child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                      },
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.lsErrorInfor[widget.index],
                  child: SizedBox(height: 10.h),
                ),
                QualityErrorValidate(
                  error: widget.lsErrorInfor[widget.index],
                  text: "Vui lòng chọn hạng mục kiểm tra",
                )
              ],
            ),
          ),
        ));
  }

  Expanded buildStt() {
    return Expanded(
        flex: 1,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
          ),
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
          child: Center(
            child: Text(
              (widget.index + 1).toString(),
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ));
  }
}

class ItemThongTinKiemTraMau extends StatefulWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsThongTinKiemTra;
  final List<ThongTinKiemTra>? lsThongTinKiemTraSelectMasterData;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;

  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;

  final List<TextEditingController> lsControllerThongTinKiemTra;
  final List<TextEditingController> lsControllerSoSanPhamLoi;
  final List<TextEditingController> lsControllerGhiChu;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;

  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  final Function updateTongSoSanPhamLoi;
  final Function(RadioValue?, int) onRadioChanged;

  const ItemThongTinKiemTraMau({
    super.key,
    required this.index,
    required this.qualityControl,
    required this.lsGetFileInfor,
    required this.lsThongTinKiemTra,
    required this.lsThongTinKiemTraSelectMasterData,
    required this.checkVisiButtonInformation,
    required this.pickFileImageInformation,
    required this.deleteListQualityInformation,
    required this.deleteItemListInformation,
    required this.lsControllerThongTinKiemTra,
    required this.lsControllerSoSanPhamLoi,
    required this.lsControllerGhiChu,
    required this.checkClearLsSelectedInfo,
    required this.pickerImage,
    required this.lsErrorInfor,
    required this.checkErrorSelectedInfo,
    required this.updateTongSoSanPhamLoi,
    required this.onRadioChanged,
  });

  @override
  State<ItemThongTinKiemTraMau> createState() => _ItemThongTinKiemTraMauState();
}

class _ItemThongTinKiemTraMauState extends State<ItemThongTinKiemTraMau> {
  RadioValue _radioValue = RadioValue.khongKiemTra;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 1. STT
          buildStt(),
          Expanded(
            flex: 10,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // 2. Hạng mục kiểm tra
                        buildHangMucKiemTra(),
                        // 3. SL sản phẩm lỗi
                        // buildSoSanPhamLoi(),
                        // 4. Ghi chú
                        buildGhiChu(),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          buildImagePicker(context),
                          buildImageViewer(context),
                        ],
                      ),
                    ),
                  ),
                  IntrinsicHeight(
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: <Widget>[
                            buildRadioGesture(RadioValue.dat, _radioValue, "Đạt"),
                            buildRadioGesture(RadioValue.khongDat, _radioValue, "Không đạt"),
                            buildRadioGesture(RadioValue.khongKiemTra, _radioValue, "Không kiểm tra"),
                          ],
                        )
                      ],
                    ),
                  ),
                  buildDeleteButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRadioGesture(RadioValue value, RadioValue groupValue, String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        children: <Widget>[
          Radio<RadioValue>(
            visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            value: value,
            // groupValue: groupValue,
            groupValue: getRadioValue(widget.lsThongTinKiemTra[widget.index].outcomeStatus),
            onChanged: (RadioValue? newValue) {
              setState(() {
                _radioValue = newValue!;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(newValue!);
              });
              widget.onRadioChanged(newValue, widget.index);
            },
            activeColor: const Color(0xff0052cc),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _radioValue = value;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(value!);
              });
              widget.onRadioChanged(value, widget.index);
            },
            child: RadioLabel(text: text),
          ),
        ],
      ),
    );
  }

  Visibility buildDeleteButton() {
    return Visibility(
      visible: widget.checkVisiButtonInformation[widget.index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                widget.deleteItemListInformation(widget.index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Visibility buildImageViewer(BuildContext context) {
    return Visibility(
      visible: (widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
      child: Expanded(
        flex: 2,
        child: GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return DialogImageQualityInformation(
                  title: 'THÔNG TIN KIỂM TRA ' + widget.lsThongTinKiemTra[widget.index].qualityControlInformationCode.toString(),
                  listImage: widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? [],
                );
              },
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5.h),
            margin: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: BoxDecoration(
              color: const Color(0xff0052cc),
              borderRadius: BorderRadius.all(
                Radius.circular(2.r),
              ),
            ),
            child: Icon(
              Icons.image,
              color: Colors.white,
              size: 15.sp,
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildImagePicker(BuildContext context) {
    return Expanded(
      flex: 8,
      child: widget.lsGetFileInfor[widget.index].isEmpty
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                // if (!mounted) return;
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Center(
                      child: Text(
                        "Chọn tệp",
                        style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Center(
                  child: Text(
                    "Chưa chọn tệp nào",
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  // if (!mounted) return;
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Text(
                      "Chọn tệp",
                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                Wrap(
                    spacing: 3.w,
                    runSpacing: 3.h,
                    children: List.generate(widget.lsGetFileInfor[widget.index].length, (indexImage) {
                      // String filename = basename(
                      //     lsGetFile[index][indexImage]
                      //         .path);
                      return SizedBox(
                        width: 50.w,
                        child: Stack(children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ImageQuatity(lsImage: widget.lsGetFileInfor[widget.index], index: indexImage),
                                ),
                              );
                            },
                            child: ListImagePicker(fileImage: widget.lsGetFileInfor[widget.index][indexImage]),
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              highlightColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              constraints: const BoxConstraints(),
                              iconSize: 17.sp,
                              color: Colors.red.shade800,
                              icon: const Icon(Icons.remove_circle),
                              onPressed: () {
                                widget.deleteListQualityInformation(MultiListImageDeleteFile(index: widget.index, indexImage: indexImage));
                              },
                            ),
                          )
                        ]),
                      );
                    })),
                SizedBox(height: 10.h),
              ],
            ),
    );
  }

  Expanded buildGhiChu() {
    return Expanded(
      flex: 4,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              textAlign: TextAlign.left,
              controller: widget.lsControllerGhiChu[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              // onChanged: (value){
              //   // resetState();
              // },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildSoSanPhamLoi() {
    return Expanded(
      flex: 3,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              keyboardType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              textAlign: TextAlign.center,
              controller: widget.lsControllerSoSanPhamLoi[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              onChanged: (value) {
                widget.updateTongSoSanPhamLoi();
              },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildHangMucKiemTra() {
    return Expanded(
        flex: 6,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              width: 0.5,
              color: Colors.grey.shade300,
            ),
          ),
          child: Padding(
            padding: REdgeInsets.all(5),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    // border: checkVisiButtonInformation[index] ? Border.all(width: 0.5, color: Colors.grey.shade400) : null,
                  ),
                  child: Center(
                    child: TypeAheadField(
                      suggestionsBoxDecoration: SuggestionsBoxDecoration(
                        constraints: BoxConstraints(
                          minWidth: 150.w,
                        ),
                      ),
                      textFieldConfiguration: TextFieldConfiguration(
                          // enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                          enabled: widget.checkVisiButtonInformation[widget.index],
                          maxLines: null,
                          decoration: InputDecoration(
                            labelStyle: TextStyle(fontSize: 11.sp),
                            contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                            isDense: true,
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                          ),
                          controller: widget.lsControllerThongTinKiemTra[widget.index],
                          // focusNode: focusInformation[index],
                          // style: TextStyle(fontSize: 12.sp),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 11.sp,
                          ),
                          onChanged: (value) {
                            widget.checkClearLsSelectedInfo(widget.index);
                          }),
                      suggestionsCallback: (pattern) {
                        return QualityControlFunction.filterQualityControlInformationIdList(widget.lsThongTinKiemTraSelectMasterData ?? [], pattern);
                      },
                      itemBuilder: (context, suggestion) {
                        return ListTile(
                          title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        widget.checkErrorSelectedInfo(QuantityInformationSelected(index: widget.index, qualityControlInformationIdList: suggestion));
                      },
                      noItemsFoundBuilder: (value) {
                        return Padding(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                            child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                      },
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.lsErrorInfor[widget.index],
                  child: SizedBox(height: 10.h),
                ),
                QualityErrorValidate(
                  error: widget.lsErrorInfor[widget.index],
                  text: "Vui lòng chọn hạng mục kiểm tra",
                )
              ],
            ),
          ),
        ));
  }

  Expanded buildStt() {
    return Expanded(
        flex: 1,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
          ),
          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 2.w),
          child: Center(
            child: Text(
              (widget.index + 1).toString(),
              style: TextStyle(fontSize: 9.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ));
  }
}

class ItemThongTinKiemTraKCS extends StatefulWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsThongTinKiemTra;
  final List<ThongTinKiemTra>? lsThongTinKiemTraSelectMasterData;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;

  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;

  final List<TextEditingController> lsControllerThongTinKiemTra;
  final List<TextEditingController> lsControllerSoSanPhamLoi;
  final List<TextEditingController> lsControllerGhiChu;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;

  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  final Function updateTongSoSanPhamLoi;
  final Function(RadioValue, int) onRadioChanged;

  const ItemThongTinKiemTraKCS({
    super.key,
    required this.index,
    required this.qualityControl,
    required this.lsGetFileInfor,
    required this.lsThongTinKiemTra,
    required this.lsThongTinKiemTraSelectMasterData,
    required this.checkVisiButtonInformation,
    required this.pickFileImageInformation,
    required this.deleteListQualityInformation,
    required this.deleteItemListInformation,
    required this.lsControllerThongTinKiemTra,
    required this.lsControllerSoSanPhamLoi,
    required this.lsControllerGhiChu,
    required this.checkClearLsSelectedInfo,
    required this.pickerImage,
    required this.lsErrorInfor,
    required this.checkErrorSelectedInfo,
    required this.updateTongSoSanPhamLoi,
    required this.onRadioChanged,
  });

  @override
  State<ItemThongTinKiemTraKCS> createState() => _ItemThongTinKiemTraKCSState();
}

class _ItemThongTinKiemTraKCSState extends State<ItemThongTinKiemTraKCS> {
  RadioValue _radioValue = RadioValue.khongKiemTra;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 1. STT
          buildStt(),
          Expanded(
            flex: 12,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // 2. Hạng mục kiểm tra
                        buildHangMucKiemTra(),
                        // 3. SL sản phẩm lỗi
                        buildSoSanPhamLoi(),
                        // 4. Ghi chú
                        buildGhiChu(),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          buildImagePicker(context),
                          buildImageViewer(context),
                        ],
                      ),
                    ),
                  ),
                  IntrinsicHeight(
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: <Widget>[
                            buildRadioGesture(RadioValue.dat, _radioValue, "Đạt"),
                            buildRadioGesture(RadioValue.khongDat, _radioValue, "Không đạt"),
                            buildRadioGesture(RadioValue.khongKiemTra, _radioValue, "Không kiểm tra"),
                          ],
                        )
                      ],
                    ),
                  ),
                  buildDeleteButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRadioGesture(RadioValue value, RadioValue groupValue, String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        children: <Widget>[
          Radio<RadioValue>(
            visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            value: value,
            // groupValue: groupValue,
            groupValue: getRadioValue(widget.lsThongTinKiemTra[widget.index].outcomeStatus),
            onChanged: (RadioValue? newValue) {
              setState(() {
                _radioValue = newValue!;
              });
              widget.onRadioChanged(value, widget.index);
            },
            activeColor: const Color(0xff0052cc),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _radioValue = value;
              });
              widget.onRadioChanged(value, widget.index);
            },
            child: RadioLabel(text: text),
          ),
        ],
      ),
    );
  }

  Visibility buildDeleteButton() {
    return Visibility(
      visible: widget.checkVisiButtonInformation[widget.index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                widget.deleteItemListInformation(widget.index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Visibility buildImageViewer(BuildContext context) {
    return Visibility(
      visible: (widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
      child: Expanded(
        flex: 2,
        child: GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return DialogImageQualityInformation(
                  title: 'THÔNG TIN KIỂM TRA ' + widget.lsThongTinKiemTra[widget.index].qualityControlInformationCode.toString(),
                  listImage: widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? [],
                );
              },
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5.h),
            margin: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: BoxDecoration(
              color: const Color(0xff0052cc),
              borderRadius: BorderRadius.all(
                Radius.circular(2.r),
              ),
            ),
            child: Icon(
              Icons.image,
              color: Colors.white,
              size: 15.sp,
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildImagePicker(BuildContext context) {
    return Expanded(
      flex: 8,
      child: widget.lsGetFileInfor[widget.index].isEmpty
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                // if (!mounted) return;
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Center(
                      child: Text(
                        "Chọn tệp",
                        style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Center(
                  child: Text(
                    "Chưa chọn tệp nào",
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  // if (!mounted) return;
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Text(
                      "Chọn tệp",
                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                Wrap(
                    spacing: 3.w,
                    runSpacing: 3.h,
                    children: List.generate(widget.lsGetFileInfor[widget.index].length, (indexImage) {
                      // String filename = basename(
                      //     lsGetFile[index][indexImage]
                      //         .path);
                      return SizedBox(
                        width: 50.w,
                        child: Stack(children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ImageQuatity(lsImage: widget.lsGetFileInfor[widget.index], index: indexImage),
                                ),
                              );
                            },
                            child: ListImagePicker(fileImage: widget.lsGetFileInfor[widget.index][indexImage]),
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              highlightColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              constraints: const BoxConstraints(),
                              iconSize: 17.sp,
                              color: Colors.red.shade800,
                              icon: const Icon(Icons.remove_circle),
                              onPressed: () {
                                widget.deleteListQualityInformation(MultiListImageDeleteFile(index: widget.index, indexImage: indexImage));
                              },
                            ),
                          )
                        ]),
                      );
                    })),
                SizedBox(height: 10.h),
              ],
            ),
    );
  }

  Expanded buildGhiChu() {
    return Expanded(
      flex: 4,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              textAlign: TextAlign.center,
              controller: widget.lsControllerGhiChu[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              // onChanged: (value){
              //   // resetState();
              // },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildSoSanPhamLoi() {
    return Expanded(
      flex: 3,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              keyboardType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              textAlign: TextAlign.center,
              controller: widget.lsControllerSoSanPhamLoi[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              onChanged: (value) {
                widget.updateTongSoSanPhamLoi();
              },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildHangMucKiemTra() {
    return Expanded(
        flex: 5,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              width: 0.5,
              color: Colors.grey.shade300,
            ),
          ),
          child: Padding(
            padding: REdgeInsets.all(5),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    // border: checkVisiButtonInformation[index] ? Border.all(width: 0.5, color: Colors.grey.shade400) : null,
                  ),
                  child: Center(
                    child: TypeAheadField(
                      suggestionsBoxDecoration: SuggestionsBoxDecoration(
                        constraints: BoxConstraints(
                          minWidth: 150.w,
                        ),
                      ),
                      textFieldConfiguration: TextFieldConfiguration(
                          // enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                          enabled: widget.checkVisiButtonInformation[widget.index],
                          maxLines: null,
                          decoration: InputDecoration(
                            labelStyle: TextStyle(fontSize: 11.sp),
                            contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                            isDense: true,
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                          ),
                          controller: widget.lsControllerThongTinKiemTra[widget.index],
                          // focusNode: focusInformation[index],
                          // style: TextStyle(fontSize: 12.sp),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 11.sp,
                          ),
                          onChanged: (value) {
                            widget.checkClearLsSelectedInfo(widget.index);
                          }),
                      suggestionsCallback: (pattern) {
                        return QualityControlFunction.filterQualityControlInformationIdList(widget.lsThongTinKiemTraSelectMasterData ?? [], pattern);
                      },
                      itemBuilder: (context, suggestion) {
                        return ListTile(
                          title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        widget.checkErrorSelectedInfo(QuantityInformationSelected(index: widget.index, qualityControlInformationIdList: suggestion));
                      },
                      noItemsFoundBuilder: (value) {
                        return Padding(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                            child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                      },
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.lsErrorInfor[widget.index],
                  child: SizedBox(height: 10.h),
                ),
                QualityErrorValidate(
                  error: widget.lsErrorInfor[widget.index],
                  text: "Vui lòng chọn hạng mục kiểm tra",
                )
              ],
            ),
          ),
        ));
  }

  Expanded buildStt() {
    return Expanded(
        flex: 1,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
          ),
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
          child: Center(
            child: Text(
              (widget.index + 1).toString(),
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ));
  }
}

class ItemThongTinKiemTraHienTruong extends StatefulWidget {
  final int index;
  final QualityControl? qualityControl;
  final List<List<File>> lsGetFileInfor;
  final List<QualityControlInformation> lsThongTinKiemTra;
  final List<ThongTinKiemTra>? lsThongTinKiemTraSelectMasterData;
  final List<bool> checkVisiButtonInformation;
  final Function pickFileImageInformation;

  final Function deleteListQualityInformation;
  final Function deleteItemListInformation;

  final List<TextEditingController> lsControllerThongTinKiemTra;
  final List<TextEditingController> lsControllerSoSanPhamLoi;
  final List<TextEditingController> lsControllerGhiChu;
  final Function checkClearLsSelectedInfo;
  final ImagePicker pickerImage;

  final List<bool> lsErrorInfor;
  final Function checkErrorSelectedInfo;

  final Function updateTongSoSanPhamLoi;
  final Function(RadioValue?, int) onRadioChanged;

  const ItemThongTinKiemTraHienTruong({
    super.key,
    required this.index,
    required this.qualityControl,
    required this.lsGetFileInfor,
    required this.lsThongTinKiemTra,
    required this.lsThongTinKiemTraSelectMasterData,
    required this.checkVisiButtonInformation,
    required this.pickFileImageInformation,
    required this.deleteListQualityInformation,
    required this.deleteItemListInformation,
    required this.lsControllerThongTinKiemTra,
    required this.lsControllerSoSanPhamLoi,
    required this.lsControllerGhiChu,
    required this.checkClearLsSelectedInfo,
    required this.pickerImage,
    required this.lsErrorInfor,
    required this.checkErrorSelectedInfo,
    required this.updateTongSoSanPhamLoi,
    required this.onRadioChanged,
  });

  @override
  State<ItemThongTinKiemTraHienTruong> createState() => _ItemThongTinKiemTraHienTruongState();
}

class _ItemThongTinKiemTraHienTruongState extends State<ItemThongTinKiemTraHienTruong> {
  RadioValue _radioValue = RadioValue.khongKiemTra;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // 1. STT
          buildStt(),
          Expanded(
            flex: 9,
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // 2. Hạng mục kiểm tra
                        buildHangMucKiemTra(),
                        // 3. SL sản phẩm lỗi
                        // buildSoSanPhamLoi(),
                        // 4. Ghi chú
                        buildGhiChu(),
                      ],
                    ),
                  ),
                  IntrinsicHeight(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                      ),
                      child: Row(
                        children: <Widget>[
                          buildImagePicker(context),
                          buildImageViewer(context),
                        ],
                      ),
                    ),
                  ),
                  IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: Column(
                            children: <Widget>[
                              Flexible(child: buildRadioGesture(RadioValue.dat, _radioValue, "Đạt yêu cầu")),
                              Flexible(child: buildRadioGesture(RadioValue.khongDat, _radioValue, "Không đạt yêu cầu")),
                              // Flexible(child: buildRadioGesture(RadioValue.khongKiemTra, _radioValue, "Không kiểm tra")),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  buildDeleteButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRadioGesture(RadioValue value, RadioValue groupValue, String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        children: <Widget>[
          Radio<RadioValue>(
            visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            value: value,
            // groupValue: groupValue,
            groupValue: getRadioValue(widget.lsThongTinKiemTra[widget.index].outcomeStatus),
            onChanged: (RadioValue? newValue) {
              setState(() {
                _radioValue = newValue!;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(newValue!);
              });
              widget.onRadioChanged(newValue, widget.index);
            },
            activeColor: const Color(0xff0052cc),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _radioValue = value;
                // widget.lsThongTinKiemTra[widget.index].outcomeStatus = getOutcomeStatus(value!);
              });
              widget.onRadioChanged(value, widget.index);
            },
            child: RadioLabel(text: text),
          ),
        ],
      ),
    );
  }

  Visibility buildDeleteButton() {
    return Visibility(
      visible: widget.checkVisiButtonInformation[widget.index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                widget.deleteItemListInformation(widget.index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Visibility buildImageViewer(BuildContext context) {
    return Visibility(
      visible: (widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? []).isNotEmpty ? true : false,
      child: Expanded(
        flex: 2,
        child: GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return DialogImageQualityInformation(
                  title: 'THÔNG TIN KIỂM TRA ' + widget.lsThongTinKiemTra[widget.index].qualityControlInformationCode.toString(),
                  listImage: widget.lsThongTinKiemTra[widget.index].checkedFileViewModel ?? [],
                );
              },
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5.h),
            margin: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: BoxDecoration(
              color: const Color(0xff0052cc),
              borderRadius: BorderRadius.all(
                Radius.circular(2.r),
              ),
            ),
            child: Icon(
              Icons.image,
              color: Colors.white,
              size: 15.sp,
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildImagePicker(BuildContext context) {
    return Expanded(
      flex: 8,
      child: widget.lsGetFileInfor[widget.index].isEmpty
          ? Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                // if (!mounted) return;
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Center(
                      child: Text(
                        "Chọn tệp",
                        style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Center(
                  child: Text(
                    "Chưa chọn tệp nào",
                    style: TextStyle(fontSize: 11.sp),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                GestureDetector(
                  onTap: widget.qualityControl!.qcType == "NVL" && widget.qualityControl!.qualityChecker != null
                      ? null
                      : () async {
                          final check = await QualityControlFunction.pickImage(context);
                          debugPrint(check.toString());
                          if (check != null) {
                            bool checkPermission = await ImageFunction.handlePermission(check);
                            if (checkPermission == true) {
                              if (check == true) {
                                List<XFile>? selectedImages = await widget.pickerImage.pickMultiImage(
                                  maxWidth: globalImageConfig.maxWidth,
                                  maxHeight: globalImageConfig.maxHeight,
                                  imageQuality: globalImageConfig.imageQuality,
                                );
                                if (selectedImages.isEmpty) return;
                                for (var i in selectedImages) {
                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                  // if (!mounted) return;
                                  widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: itemImage));
                                }
                              } else {
                                final image = await ImagePicker().pickImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                    source: ImageSource.camera);
                                // if (!mounted) return;
                                if (image == null) return;
                                final imageProfile = await ImageFunction.saveImage(image.path);
                                widget.pickFileImageInformation(MultiListImageFile(index: widget.index, file: imageProfile));
                              }
                            }
                          }
                        },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.grey.shade100,
                    ),
                    child: Text(
                      "Chọn tệp",
                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                Wrap(
                    spacing: 3.w,
                    runSpacing: 3.h,
                    children: List.generate(widget.lsGetFileInfor[widget.index].length, (indexImage) {
                      // String filename = basename(
                      //     lsGetFile[index][indexImage]
                      //         .path);
                      return SizedBox(
                        width: 50.w,
                        child: Stack(children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ImageQuatity(lsImage: widget.lsGetFileInfor[widget.index], index: indexImage),
                                ),
                              );
                            },
                            child: ListImagePicker(fileImage: widget.lsGetFileInfor[widget.index][indexImage]),
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              highlightColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              constraints: const BoxConstraints(),
                              iconSize: 17.sp,
                              color: Colors.red.shade800,
                              icon: const Icon(Icons.remove_circle),
                              onPressed: () {
                                widget.deleteListQualityInformation(MultiListImageDeleteFile(index: widget.index, indexImage: indexImage));
                              },
                            ),
                          )
                        ]),
                      );
                    })),
                SizedBox(height: 10.h),
              ],
            ),
    );
  }

  Expanded buildGhiChu() {
    return Expanded(
      flex: 4,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              textAlign: TextAlign.left,
              controller: widget.lsControllerGhiChu[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              // onChanged: (value){
              //   // resetState();
              // },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildSoSanPhamLoi() {
    return Expanded(
      flex: 3,
      child: Container(
        // key: UniqueKey(),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Padding(
          padding: REdgeInsets.all(5),
          child: Container(
            // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
            decoration: BoxDecoration(
              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
            ),
            child: TextFormField(
              enabled: widget.qualityControl!.qcType == "NVL"
                  ? widget.qualityControl!.qualityChecker != null
                      ? false
                      : true
                  : true,
              maxLines: null,
              keyboardType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              textAlign: TextAlign.center,
              controller: widget.lsControllerSoSanPhamLoi[widget.index],
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                filled: true,
                isDense: true,
                fillColor: Colors.white,
                hintStyle: TextStyle(fontSize: 12.sp),
                // contentPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
              ),
              onChanged: (value) {
                widget.updateTongSoSanPhamLoi();
              },
            ),
          ),
        ),
      ),
    );
  }

  Expanded buildHangMucKiemTra() {
    return Expanded(
        flex: 5,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              width: 0.5,
              color: Colors.grey.shade300,
            ),
          ),
          child: Padding(
            padding: REdgeInsets.all(5),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    // border: checkVisiButtonInformation[index] ? Border.all(width: 0.5, color: Colors.grey.shade400) : null,
                  ),
                  child: Center(
                    child: TypeAheadField(
                      suggestionsBoxDecoration: SuggestionsBoxDecoration(
                        constraints: BoxConstraints(
                          minWidth: 150.w,
                        ),
                      ),
                      textFieldConfiguration: TextFieldConfiguration(
                          // enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false : true,
                          enabled: widget.checkVisiButtonInformation[widget.index],
                          maxLines: null,
                          decoration: InputDecoration(
                            labelStyle: TextStyle(fontSize: 11.sp),
                            contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                            isDense: true,
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                          ),
                          controller: widget.lsControllerThongTinKiemTra[widget.index],
                          // focusNode: focusInformation[index],
                          // style: TextStyle(fontSize: 12.sp),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 11.sp,
                          ),
                          onChanged: (value) {
                            widget.checkClearLsSelectedInfo(widget.index);
                          }),
                      suggestionsCallback: (pattern) {
                        return QualityControlFunction.filterQualityControlInformationIdList(widget.lsThongTinKiemTraSelectMasterData ?? [], pattern);
                      },
                      itemBuilder: (context, suggestion) {
                        return ListTile(
                          title: Text((suggestion).name ?? " ", style: TextStyle(fontSize: 12.sp)),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        widget.checkErrorSelectedInfo(QuantityInformationSelected(index: widget.index, qualityControlInformationIdList: suggestion));
                      },
                      noItemsFoundBuilder: (value) {
                        return Padding(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                            child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                      },
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.lsErrorInfor[widget.index],
                  child: SizedBox(height: 10.h),
                ),
                QualityErrorValidate(
                  error: widget.lsErrorInfor[widget.index],
                  text: "Vui lòng chọn hạng mục kiểm tra",
                )
              ],
            ),
          ),
        ));
  }

  Expanded buildStt() {
    return Expanded(
        flex: 1,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
          ),
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
          child: Center(
            child: Text(
              (widget.index + 1).toString(),
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ));
  }
}
