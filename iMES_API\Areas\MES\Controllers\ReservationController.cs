﻿using ISD.API.Core;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    public class ReservationController : ControllerBaseAPI
    {
        #region Tìm kiếm danh sách Reservation
        /// <summary>API Search "Tìm kiếm danh sách Reservation"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/Reservation/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///             "reservationNumber": "",
        ///             "plant": "",
        ///             "reqComponentDate": "",
        ///             "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///             "code": 200,
        ///             "isSuccess": true,
        ///             "message": null,
        ///             "data": [
        ///                     {
        ///                     "stt": 1,
        ///                     "reservationId": "cb208837-7d26-4507-b743-079298241d07",
        ///                     "reservationNumber": "123",
        ///                     "plant": "123",
        ///                     "reqComponentDate": "20220719",
        ///                     "actived": true
        ///                     }
        ///                  ],
        ///                     "additionalData": {
        ///                     "draw": 1,
        ///                     "recordsTotal": 1,
        ///                     "recordsFiltered": 1
        ///             }
        ///     }
        /// </remarks>
        [HttpPost("Search")]
        public ActionResult GetReservation([FromBody] ReservationSearchViewModel vm)
        {
            var query = _context.ReservationHeaderModel.Where(x => (!string.IsNullOrEmpty(vm.ReservationNumber) ? x.ReservationNumber.Trim().Contains(vm.ReservationNumber) : true) &&
                                                                   (!string.IsNullOrEmpty(vm.Plant) ? x.RIPlant == vm.Plant : true) &&
                                                                   (vm.ReqDate.HasValue ? x.RequestDate == vm.ReqDate : true) &&
                                                                   (vm.IsApprovedBool == null || (vm.IsApprovedBool == true ?  x.IsApproved == true : x.IsApproved == false || x.IsApproved == null))
                                                                )
                                                        .Include(x => x.MaterialReservationModel)
                                                        .OrderByDescending(x => x.RequestDate)
                                                        .AsNoTracking();

            query = query.Where(x => x.MaterialReservationModel.Count(e => e.ItemDeleted != "X" &&
                                                                           e.FinalIssue != "X" &&
                                                                           e.ReservationGoodsMove == "X") > 0);

            var rs = query.Select(p => new ReservationResultViewModel
            {
                Id = p.ReservationHeaderId,
                //Số reservation
                ReservationNumber = p.ReservationNumber,
                //Ngày yêu cầu
                ReqDate = p.RequestDate,
                //Loại giao dịch kho
                MovementType = p.MovementType,
                //Người nhận
                GoodsRecipient = p.GoodsRecipient,
                //Mã chi phí
                CostCenter = p.CostCenter,
                //Mã tài sản
                AssetNumber = p.AssetNumber,
                //Mã customer
                AccountNumberCustomer = p.AccountNumberCustomer,
                //Lệnh sản xuất
                OrderNumber = p.OrderNumber,
                //Số SO
                SalesOrderNumber = p.SalesOrderNumber,
                //Item trên SO
                SalesOrderNumberItem = p.SalesOrderNumberItem,
                //Nhà máy nhận
                RIPlant = p.RIPlant,
                //Kho nhận
                RIStorageLocation = p.RIStorageLocation,
                //Mã WBS
                WBSCode = p.WBSElement,
                //Phê duyệt
                IsApproved = p.IsApproved
            });

            var datatblModel = new DatatableViewModel()
            {
                draw = vm.Paging.draw,
                start = vm.Paging.start,
                length = vm.Paging.length,
                order = vm.Paging.order,
                columns = vm.Paging.columns,
            };

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = NewCustomSearchRepository.CustomSearchFunc<ReservationResultViewModel>(datatblModel, out filteredResultsCount, out totalResultsCount, rs, "stt");

            if (res != null && res.Count() > 0)
            {
                int i = vm.Paging.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<ReservationResultViewModel>>
            {
                Data = res,
                Draw = vm.Paging.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        #region Chi tiết reservation
        /// <summary>API Search "Get detail reservation"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/Reservation/GetDetail
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "reservationHeaderId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 1
        ///                 }
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": null,
        ///                 "data": [
        ///                     {
        ///                       "stt": 1,
        ///                       "materialReservationId": "8e5caf7c-261d-46d2-88b6-003b6af10b6a",
        ///                       "reservationNumber": "8391",
        ///                       "reservationItemNumber": "12",
        ///                       "reservationGoodsMove": null,
        ///                       "materialNumber": "000000000270000003",
        ///                       "plant": "1000",
        ///                       "storageLocation": "",
        ///                       "batchNumber": "",
        ///                       "specialStockIndicator": "E",
        ///                       "reqComponentDate": null,
        ///                       "reqQuantity": 2.5,
        ///                       "measureUnit": null,
        ///                       "unitEntry": "KG",
        ///                       "unitEntryQuantity": 0,
        ///                       "orderNumber": "000275000082",
        ///                       "salesOrderNumber": "2200000047",
        ///                       "salesOrderNumberItem": "10",
        ///                       "riPlant": "",
        ///                       "riStorageLocation": "",
        ///                       "wbsElement": "0"
        ///                     }
        ///                 ],
        ///                 "additionalData": {
        ///                   "draw": 1,
        ///                   "recordsTotal": 1,
        ///                   "recordsFiltered": 784
        ///                 }
        ///             }
        ///          
        /// </remarks>
        [HttpPost("GetDetail")]
        public IActionResult GetDetailReservation([FromBody] DetailReservationSearchViewModel vm)
        {
            //Chi tiết reservation theo reservation header
            var querySearch = _context.MaterialReservationModel.Where(x => x.ReservationHeaderId == vm.ReservationHeaderId &&
                                                                           x.ItemDeleted != "X" &&
                                                                           x.FinalIssue != "X" &&
                                                                           x.ReservationGoodsMove == "X")
                                                               .Select(x => new ReservationDetailResultViewModel
                                                               {
                                                                   //Id
                                                                   MaterialReservationId = x.ReservationId,
                                                                   //Số reservation
                                                                   ReservationNumber = x.ReservationNumber,
                                                                   //Số dòng
                                                                   ReservationItemNumber = x.ReservationItemNumber,
                                                                   //Mã material
                                                                   MaterialNumber = x.MaterialNumber,
                                                                   //Nhà mát
                                                                   Plant = x.Plant,
                                                                   //Kho
                                                                   StorageLocation = x.StorageLocation,
                                                                   //Số lô
                                                                   BatchNumber = x.BatchNumber,
                                                                   //Tồn kho đặc biệt
                                                                   SpecialStockIndicator = x.SpecialStockIndicator,
                                                                   //Số lượng yêu cầu
                                                                   ReqQuantity = x.ReqQuantity,
                                                                   //Đơn vị yêu cầu
                                                                   UnitEntry = x.UnitEntry,
                                                                   //Lệnh sản xuất
                                                                   OrderNumber = x.OrderNumber,
                                                                   //Số SO
                                                                   SalesOrderNumber = x.SalesOrderNumber,
                                                                   //Item SO
                                                                   SalesOrderNumberItem = x.SalesOrderNumberItem,
                                                                   //Nhà máy nhận
                                                                   RIPlant = x.RIPlant,
                                                                   //Kho nhận
                                                                   RIStorageLocation = x.RIStorageLocation,
                                                                   //WBS
                                                                   WBSElement = x.WBSElement,
                                                                   //Ngày cần thực hiện nghiệp vụ
                                                                   ReqComponentDate = x.ReqComponentDate,
                                                                   //Số lương theo ĐVT yêu cầu
                                                                   UnitEntryQuantity = x.UnitEntryQuantity,
                                                                   //ĐVT
                                                                   MeasureUnit = x.MeasureUnit,

                                                                   Note = x.Note
                                                               });

            //Search theo key word
            querySearch = querySearch.Where(x => !string.IsNullOrEmpty(vm.ReservationNumber) ? x.ReservationNumber == vm.ReservationNumber : true &&
                                                 !string.IsNullOrEmpty(vm.OrderNumber) ? x.OrderNumber == vm.OrderNumber : true &&
                                                 !string.IsNullOrEmpty(vm.SaleOrderNumber) ? x.SalesOrderNumber == vm.SaleOrderNumber : true &&
                                                 !string.IsNullOrEmpty(vm.SaleOrderItemNumber) ? x.SalesOrderNumberItem == vm.SaleOrderItemNumber : true &&
                                                 !string.IsNullOrEmpty(vm.RIPlant) ? x.RIPlant == vm.RIPlant : true &&
                                                 !string.IsNullOrEmpty(vm.RIStorageLocation) ? x.RIStorageLocation == vm.RIStorageLocation : true);

            var datatblModel = new DatatableViewModel()
            {
                draw = vm.Paging.draw,
                start = vm.Paging.start,
                length = vm.Paging.length,
            };

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = CustomSearchRepository.CustomSearchFunc<ReservationDetailResultViewModel>(datatblModel, out filteredResultsCount, out totalResultsCount, querySearch, "STT");

            if (res.Any())
            {
                int i = datatblModel.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<ReservationDetailResultViewModel>>
            {
                Data = res,
                Draw = datatblModel.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        #region Duyệt lệnh cấp vật tư
        [HttpPost("ApproveReservation")]
        public IActionResult ApproveReservation([FromBody] ApproveReservationRequest request)
        {
            var accountId = request.AccountId;
            var currentDate = DateTime.Now;

            // Check reservation
            if (!request.ReservationIds.Any())
                return Ok(new ApiResponse { Data = false, Code = 400, IsSuccess = false, Message = MESP2Resource.REQUIRED_ITEM });

            // Danh sách reservation
            var listReservation = _context.ReservationHeaderModel.Where(x => request.ReservationIds.Contains(x.ReservationHeaderId));

            foreach (var item in listReservation)
            {
                // Cập nhật trạng thái phê duyệt của reservation
                item.IsApproved = true;
                item.ApprovedBy = accountId;
                item.ApprovedDate = currentDate;
            }
            _context.SaveChanges();

            return Ok(new ApiSuccessResponse<bool>
            {
                Data = true,
                Message = string.Format(CommonResource.Msg_Succes, "Phê duyệt")
            });
        }
        #endregion
    }
}