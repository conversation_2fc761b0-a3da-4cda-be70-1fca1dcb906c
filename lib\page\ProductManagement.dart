import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../element/ButtonInventoryMNG.dart';
import '../model/userModel.dart';
import '../screenArguments/CommonScreenPermissionArgument.dart';

class ProductManagement extends StatelessWidget {
  const ProductManagement({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
    required this.user,
  }) : super(key: key);
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  final DataUser user;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              size: 14.sp,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            'Quản lý TP',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          ),
        ),
        body: _SingleChildProductManagement(
          permission: permission,
          token: token,
          dateTimeOld: dateTimeOld,
          plant: plant,
          accountId: accountId,
          user: user,
        ));
  }
}

class _SingleChildProductManagement extends StatelessWidget {
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  final DataUser user;

  const _SingleChildProductManagement({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _ProductManagementView(
            permission: permission,
            token: token,
            dateTimeOld: dateTimeOld,
            plant: plant,
            accountId: accountId,
            user: user,
          )),
    );
  }
}

class _ProductManagementView extends StatelessWidget {
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  final DataUser user;

  const _ProductManagementView({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        SizedBox(height: 10.h),
        ButtonInventoryMNG(
          txt: "Nhập kho TP",
          icon: Icon(
            Icons.label_important_rounded,
            size: 20.sp,
            color: Colors.white,
          ),
          route: () async {
            Navigator.pushNamed(context, '/ImportProduct',
                arguments: CommonScreenPermissionArgument(
                  permission!,
                  token.toString(),
                  plant,
                  dateTimeOld,
                  accountId,
                  user,
                ));
          },
        ),
      ],
    );
  }
}
