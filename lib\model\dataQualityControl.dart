class DataQualityControl {
  int? code;
  bool? success;
  String? data;

  DataQualityControl({this.code, this.success, this.data});

  DataQualityControl.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    data['data'] = this.data;
    return data;
  }
}