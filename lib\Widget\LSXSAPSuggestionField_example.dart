import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'LSXSAPSuggestionField.dart';

/// Example usage of LSXSAPSuggestionField widget
///
/// This file demonstrates different ways to use the LSXSAPSuggestionField
/// in various scenarios across the application.
class LSXSAPSuggestionFieldExample extends StatefulWidget {
  final String userToken;

  const LSXSAPSuggestionFieldExample({
    Key? key,
    required this.userToken,
  }) : super(key: key);

  @override
  State<LSXSAPSuggestionFieldExample> createState() => _LSXSAPSuggestionFieldExampleState();
}

class _LSXSAPSuggestionFieldExampleState extends State<LSXSAPSuggestionFieldExample> {
  late TextEditingController _lsxSapController;
  late TextEditingController _lsxSapController2;
  late TextEditingController _lsxSapController3;

  @override
  void initState() {
    super.initState();
    _lsxSapController = TextEditingController();
    _lsxSapController2 = TextEditingController();
    _lsxSapController3 = TextEditingController();
  }

  @override
  void dispose() {
    _lsxSapController.dispose();
    _lsxSapController2.dispose();
    _lsxSapController3.dispose();
    super.dispose();
  }

  void _handleLSXSAPSelected(String lsxSap) {
    debugPrint('LSX SAP selected: $lsxSap');
    // Handle the selected LSX SAP - call your API to load related data
    // Example:
    // await _loadQualityControlByLSXSAP(lsxSap);
  }

  void _handleClear() {
    debugPrint('LSX SAP cleared');
    // Handle clearing - reset related data
    // Example:
    // setState(() {
    //   _qualityControl = QualityControl();
    //   _selectedCongDoanNho = defaultValue;
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('LSX SAP Field Examples'),
        backgroundColor: const Color(0xff0052cc),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Basic usage with title
            Text(
              'Example 1: Basic usage with title',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.h),
            LSXSAPSuggestionField(
              controller: _lsxSapController,
              token: widget.userToken,
              onSuggestionSelected: _handleLSXSAPSelected,
              onClear: _handleClear,
            ),
            SizedBox(height: 30.h),

            // Example 2: Without title, required field
            Text(
              'Example 2: Without title, required field',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.h),
            LSXSAPSuggestionField(
              controller: _lsxSapController2,
              token: widget.userToken,
              title: null, // No title
              isRequired: true,
              onSuggestionSelected: _handleLSXSAPSelected,
              hintText: "Nhập mã LSX SAP...",
            ),
            SizedBox(height: 30.h),

            // Example 3: Disabled field without clear button
            Text(
              'Example 3: Disabled field without clear button',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.h),
            LSXSAPSuggestionField(
              controller: _lsxSapController3,
              token: widget.userToken,
              enabled: false,
              showClearButton: false,
              title: "LSX SAP (Disabled)",
              onSuggestionSelected: _handleLSXSAPSelected,
              onChanged: (value) {
                debugPrint('Text changed: $value');
              },
            ),
            SizedBox(height: 30.h),

            // Usage in Row layout (like the original implementation)
            Text(
              'Example 4: Usage in form layout',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.h),
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  // Other form fields would go here
                  SizedBox(height: 15.h),

                  // LSX SAP field
                  LSXSAPSuggestionField(
                    controller: _lsxSapController,
                    token: widget.userToken,
                    title: "LSX SAP",
                    isRequired: true,
                    enabled: true, // This would be your condition: checkQualityControl() && _isTTFCode != true
                    onSuggestionSelected: (lsxSap) async {
                      // Your implementation here
                      _handleLSXSAPSelected(lsxSap);
                    },
                    onClear: () {
                      // Your clear implementation here
                      _handleClear();
                    },
                  ),

                  SizedBox(height: 15.h),
                  // Other form fields would go here
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/*
USAGE NOTES:

1. Basic Usage:
   ```dart
   LSXSAPSuggestionField(
     controller: _controllerLSXSAP,
     token: widget.user.token!.toString(),
     onSuggestionSelected: (lsxSap) async {
       await _loadQualityControlByLSQSAP(lsxSap);
     },
     onClear: () {
       // Reset your state here
       setState(() {
         _qualityControl = QualityControl();
         // Reset other related fields
       });
     },
   )
   ```

2. In Form Layout (replacing the original TypeAheadField):
   ```dart
   Row(
     mainAxisAlignment: MainAxisAlignment.center,
     crossAxisAlignment: CrossAxisAlignment.center,
     children: <Widget>[
       // Just use the widget directly, it handles the Row layout internally
       Expanded(
         child: LSXSAPSuggestionField(
           controller: _controllerLSXSAP,
           token: widget.user.token!.toString(),
           enabled: checkQualityControl() && _isTTFCode != true,
           onSuggestionSelected: (lsxSap) async {
             await _loadQualityControlByLSQSAP(lsxSap);
           },
           onClear: () {
             setState(() {
               _controllerLSXSAP.text = "";
               _isLSXSAPSelected = false;
               _qualityControl = QualityControl();
               // Reset other fields
             });
             clearForNew();
           },
         ),
       ),
     ],
   )
   ```

3. Without Title:
   ```dart
   LSXSAPSuggestionField(
     controller: _controllerLSXSAP,
     token: widget.user.token!.toString(),
     title: null,
     onSuggestionSelected: _handleSelection,
   )
   ```

4. Custom Styling:
   ```dart
   LSXSAPSuggestionField(
     controller: _controllerLSXSAP,
     token: widget.user.token!.toString(),
     title: "Custom LSX SAP",
     hintText: "Enter your custom hint here",
     isRequired: true,
     showClearButton: true,
     onSuggestionSelected: _handleSelection,
     onChanged: (value) {
       // Handle text changes
     },
   )
   ```
*/
