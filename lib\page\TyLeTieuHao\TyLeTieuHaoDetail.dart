import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:intl/intl.dart';
import 'package:ttf/element/LoadingScreen.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../../model/tyLeTieuHaoModel.dart';
import '../../repository/function/tyLeTieuHaoFunction.dart';
import '../../Storage/storageSecureStorage.dart';
import '../../element/FormLayout.dart';
import '../../element/timeOut.dart';
// import '../../element/QualityFormFields.dart';
import '../LostConnect.dart';
import '../../page/Downtime/element/AutoCompleteField.dart';
import '../../page/Downtime/element/EmployeeTypeAhead.dart';
import '../../model/downtimeModel.dart';

class TyLeTieuHaoDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;

  const TyLeTieuHaoDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
  }) : super(key: key);

  @override
  _TyLeTieuHaoDetailState createState() => _TyLeTieuHaoDetailState();

  bool get isViewMode => this.viewMode ?? id.isNotEmpty;
}

class _TyLeTieuHaoDetailState extends State<TyLeTieuHaoDetail> {
  // static const bool USE_MOCKUP = true; // Toggle this for testing
  static const bool USE_MOCKUP = false;

  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;
  bool _timeOut = false;

  // Controllers
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _lsxSAPController = TextEditingController();
  final TextEditingController _dotSanXuatController = TextEditingController();
  final TextEditingController _productInfoController = TextEditingController();
  final TextEditingController _materialInfoController = TextEditingController();
  final TextEditingController _dvtController = TextEditingController();
  final TextEditingController _slThoSuDungController = TextEditingController();
  final TextEditingController _slSPLamDuocController = TextEditingController();
  final TextEditingController _slM2DinhMucController = TextEditingController();
  final TextEditingController _tyLeTheoDMController = TextEditingController();
  final TextEditingController _personInChargeController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  final TextEditingController _soLuongKHController = TextEditingController();
  final TextEditingController _dinhMucSanPhamController = TextEditingController();

  // Employee fields
  List<TextEditingController> _employeeControllers = [];
  List<EmployeeRecord?> _selectedEmployees = [];
  List<EmployeeRecord> _employeeSuggestions = [];

  // Autocomplete suggestions
  List<String> _lsxSAPSuggestions = [];
  List<String> _materialSuggestions = [];
  bool _isLoadingLSXSAP = false;
  bool _isLoadingMaterial = false;

  List<String> get _selectedEmployeeIds =>
      _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeId).where((id) => id != null).map((id) => id!).toList();

  // Add this constant at the top of the class
  static const suggestionItemPadding = EdgeInsets.symmetric(vertical: 5, horizontal: 5);

  @override
  void initState() {
    super.initState();

    // Check token timeout first
    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    }

    setState(() {
      _timeOut = false;
    });

    _addEmployeeField();
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      // Load master data first
      await _loadMasterData();

      // Then load downtime record if editing
      if (widget.id.isNotEmpty) {
        await _loadData();
      } else {
        // Set default date to today when creating new record
        _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
      showToast(
        context: context,
        message: 'Có lỗi xảy ra khi tải dữ liệu',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMasterData() async {
    try {
      // Load employees first since we need them for mapping
      final employeesResponse = await TyLeTieuHaoFunction.getEmployees(
        widget.user.token!,
        widget.user.companyCode ?? '',
      );

      if (!mounted) return;

      if (employeesResponse?.employees != null) {
        setState(() {
          _employeeSuggestions = employeesResponse!.employees!
              .where((emp) => emp.employeeId != null && emp.employeeName != null)
              .map((emp) => EmployeeRecord(
                    employeeId: emp.employeeId!,
                    employeeName: emp.employeeName!,
                  ))
              .toList();
        });
        debugPrint('Loaded employees: ${_employeeSuggestions.length}');
      }

      // Load other master data if needed...
    } catch (error) {
      debugPrint('Error loading master data: $error');
      rethrow;
    }
  }

  void _initializeControllers() {
    // Initialize with mockup data if USE_MOCKUP is true
    if (USE_MOCKUP) {
      _dateController.text = '2024-11-19';
      _lsxSAPController.text = '3500010130';
      _dotSanXuatController.text = 'DT-169-24-CRZ-HAI-PYCSXDT-58-24-CRZ';
      _productInfoController.text = '295010583 | Vật tư phụ trội';
      _materialInfoController.text = '280010968 | CRZ Da bò thật Charme Cigar dày 09->13mm';
      _dvtController.text = 'M2';
      _slThoSuDungController.text = '27';
      _slSPLamDuocController.text = '3';
      _slM2DinhMucController.text = '24';
      _tyLeTheoDMController.text = '1.13';
      _personInChargeController.text = '10000254 | Trần Thị Nguyệt';
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      final token = widget.user.token;
      if (token == null) {
        showMessage(context, 'Error', 'Token not found');
        return;
      }

      final record = await TyLeTieuHaoFunction.fetchTyLeTieuHaoDetail(token, widget.id!);
      if (record != null) {
        _populateFields(record);
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _loadData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (e) {
      debugPrint("Error in _loadData: $e");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
      showMessage(context, 'Error', e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _populateFields(TyLeTieuHaoRecord record) {
    if (record.date != null) {
      try {
        final date = DateTime.parse(record.date!);
        _dateController.text = DateFormat('dd/MM/yyyy').format(date);
      } catch (e) {
        _dateController.text = record.date ?? '';
      }
    }
    _lsxSAPController.text = record.lsxSAP ?? '';
    _dotSanXuatController.text = record.dotSanXuat ?? '';
    _productInfoController.text = "${record.maSanPham ?? ''} | ${record.tenSanPham ?? ''}";
    _materialInfoController.text = "${record.maNVL ?? ''} | ${record.tenNVL ?? ''}";
    _dvtController.text = record.dvt ?? '';
    _slThoSuDungController.text = record.slThoSuDung?.toString() ?? '';
    _slSPLamDuocController.text = record.slSPLamDuoc?.toString() ?? '';
    _slM2DinhMucController.text = record.slM2DinhMuc?.toString() ?? '';
    _tyLeTheoDMController.text = record.tyLeTheoDM?.toString() ?? '';
    _noteController.text = record.note ?? '';
    _soLuongKHController.text = record.soLuongKH?.toString() ?? '';

    // Handle employee codes
    if (record.personInChargeCodeMany?.isNotEmpty == true) {
      final employeeCodes = record.personInChargeCodeMany!.split(',');
      _employeeControllers.clear();
      _selectedEmployees.clear();

      for (var code in employeeCodes.where((code) => code.trim().isNotEmpty)) {
        final trimmedCode = code.trim();
        final employee = _employeeSuggestions.firstWhere(
          (e) => e.employeeId == trimmedCode,
          orElse: () => EmployeeRecord(
            employeeId: trimmedCode,
            employeeName: 'Unknown Employee',
          ),
        );

        final controller = TextEditingController(text: "${employee.employeeId} | ${employee.employeeName}");
        _employeeControllers.add(controller);
        _selectedEmployees.add(employee);
      }
    }

    if (_employeeControllers.isEmpty) {
      _addEmployeeField();
    }
  }

  void _calculateDerivedValues() {
    final slSPLamDuoc = double.tryParse(_slSPLamDuocController.text) ?? 0;
    final dinhMucSanPham = (double.tryParse(_dinhMucSanPhamController.text) ?? 0) / 100;

    if (slSPLamDuoc > 0 && dinhMucSanPham > 0) {
      // Calculate SL m2 định mức using the new formula and round to 2 decimals
      final slM2DinhMuc = (slSPLamDuoc * dinhMucSanPham).toStringAsFixed(2);
      _slM2DinhMucController.text = slM2DinhMuc;

      // Calculate tỷ lệ theo DM if slThoSuDung is available
      final slThoSuDung = double.tryParse(_slThoSuDungController.text) ?? 0;
      if (slThoSuDung > 0) {
        // This is already using: Tỷ lệ theo ĐM = SL thô sử dụng / SL m2 định mức
        final tyLeTheoDM = slThoSuDung / double.parse(slM2DinhMuc);
        _tyLeTheoDMController.text = tyLeTheoDM.toStringAsFixed(2);
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    if (widget.isViewMode) return;

    debugPrint("selectDate");

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = DateFormat('dd/MM/yyyy').format(pickedDate);
      });
    }
  }

  void _addEmployeeField() {
    setState(() {
      TextEditingController controller = TextEditingController();
      _employeeControllers.add(controller);
      _selectedEmployees.add(null);
    });
  }

  void _removeEmployeeField(int index) {
    setState(() {
      _employeeControllers[index].dispose();
      _employeeControllers.removeAt(index);
      _selectedEmployees.removeAt(index);
    });
  }

  bool _validateForm() {
    if (_lsxSAPController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập LSX SAP',
      );
      return false;
    }

    if (_dotSanXuatController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập đợt sản xuất',
      );
      return false;
    }

    if (_productInfoController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn sản phẩm',
      );
      return false;
    }

    if (_materialInfoController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn nguyên vật liệu',
      );
      return false;
    }

    if (_slThoSuDungController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập số lượng thô sử dụng',
      );
      return false;
    }

    final slThoSuDung = double.tryParse(_slThoSuDungController.text);
    if (slThoSuDung == null || slThoSuDung <= 0) {
      showToast(
        context: context,
        message: 'Số lượng thô sử dụng phải lớn hơn 0',
      );
      return false;
    }

    if (_slSPLamDuocController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập số lượng sản phẩm làm được',
      );
      return false;
    }

    final slSPLamDuoc = double.tryParse(_slSPLamDuocController.text);
    if (slSPLamDuoc == null || slSPLamDuoc <= 0) {
      showToast(
        context: context,
        message: 'Số lượng sản phẩm làm được phải lớn hơn 0',
      );
      return false;
    }

    // if (_selectedEmployees.where((e) => e != null).isEmpty) {
    //   showToast(
    //     context: context,
    //     message: 'Vui lòng chọn người phụ trách',
    //   );
    //   return false;
    // }

    return true;
  }

  Future<void> _save() async {
    if (!_validateForm()) return;

    setState(() => _isSaving = true);

    try {
      final token = widget.user.token;
      if (token == null) {
        showToast(
          context: context,
          message: 'Token not found',
        );
        return;
      }

      String serverDate = _dateController.text;
      try {
        final date = DateFormat('dd/MM/yyyy').parse(_dateController.text);
        serverDate = DateFormat('yyyy-MM-dd').format(date);
      } catch (e) {
        debugPrint('Date parsing error: $e');
      }

      final record = TyLeTieuHaoRecord(
        id: widget.id,
        date: serverDate,
        lsxSAP: _lsxSAPController.text,
        dotSanXuat: _dotSanXuatController.text,
        maSanPham: _productInfoController.text.split(' | ')[0],
        tenSanPham: _productInfoController.text.split(' | ')[1],
        maNVL: _materialInfoController.text.split(' | ')[0],
        tenNVL: _materialInfoController.text.split(' | ')[1],
        dvt: _dvtController.text,
        slThoSuDung: double.tryParse(_slThoSuDungController.text),
        slSPLamDuoc: double.tryParse(_slSPLamDuocController.text),
        slM2DinhMuc: double.tryParse(_slM2DinhMucController.text),
        tyLeTheoDM: double.tryParse(_tyLeTheoDMController.text),
        personInChargeCodeMany: _selectedEmployees.where((e) => e != null).map((e) => e!.employeeId!).join(','),
        companyCode: widget.user.companyCode,
        note: _noteController.text,
      );

      debugPrint("Saving record: $record");

      final success = await TyLeTieuHaoFunction.saveTyLeTieuHao(token, record);

      debugPrint("Save operation success: $success");

      if (!mounted) return;

      if (success) {
        showToast(
          context: context,
          message: widget.id.isEmpty ? 'Tạo thành công' : 'Cập nhật thành công',
        );

        Navigator.pop(context, true);
      } else {
        showToast(
          context: context,
          message: 'Lưu thất bại',
        );
      }
    } catch (e) {
      debugPrint("Error in _save: $e");
      showToast(
        context: context,
        message: 'Đã xảy ra lỗi',
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  Widget _buildEmployeeSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Tên công nhân',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 7,
            child: Column(
              children: List.generate(
                _employeeControllers.length,
                (index) => EmployeeTypeAhead(
                  enabled: !widget.isViewMode,
                  textStyle: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                  ),
                  showRemoveButton: _employeeControllers.length > 1 && !widget.isViewMode,
                  totalEmployees: _employeeControllers.length,
                  masterDataList: _employeeSuggestions,
                  excludedEmployeeIds: _selectedEmployeeIds,
                  controller: _employeeControllers[index],
                  employeeIndex: index,
                  onChanged: (value) {
                    debugPrint('Employee value changed: $value');
                    setState(() {
                      _selectedEmployees[index] = null;
                    });
                  },
                  onSuggestionSelected: (suggestion) {
                    debugPrint('Employee suggestion selected: ${suggestion.employeeId} - ${suggestion.employeeName}');
                    setState(() {
                      _employeeControllers[index].text = "${suggestion.employeeId} | ${suggestion.employeeName}";
                      _selectedEmployees[index] = suggestion;
                    });
                  },
                  onAddEmployee: () {
                    if (_employeeControllers.length < 5) {
                      _addEmployeeField();
                    }
                  },
                  onRemoveEmployee: () => _removeEmployeeField(index),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_timeOut) {
      return Scaffold(
        backgroundColor: Colors.grey[100],
        body: TimeOutView(
          setButton: () {
            setState(() {
              _timeOut = false;
            });
          },
          disableButton: false,
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[100],
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.id == null ? 'Thêm tỷ lệ tiêu hao' : 'Chi tiết tỷ lệ tiêu hao',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: SafeArea(
        child: KeyboardDismissOnTap(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 350.h),
                    child: _buildBody(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _dateController.dispose();
    _lsxSAPController.dispose();
    _dotSanXuatController.dispose();
    _productInfoController.dispose();
    _materialInfoController.dispose();
    _dvtController.dispose();
    _slThoSuDungController.dispose();
    _slSPLamDuocController.dispose();
    _slM2DinhMucController.dispose();
    _tyLeTheoDMController.dispose();
    _personInChargeController.dispose();
    _noteController.dispose();
    _soLuongKHController.dispose();
    _dinhMucSanPhamController.dispose();
    for (var controller in _employeeControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingScreen();
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormLayout(
          title: "Thông tin tỷ lệ tiêu hao",
          children: [
            // Date field
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Ngày',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    flex: 7,
                    child: GestureDetector(
                      onTap: widget.isViewMode
                          ? () => showToast(
                                context: context,
                                message: 'Không thể chọn ngày',
                              )
                          : () => _selectDate(context),
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(3.r),
                          color: Colors.white,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                _dateController.text.isEmpty ? 'Chọn ngày' : _dateController.text,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.blue),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),

            // LSX SAP field
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Expanded(
                    flex: 3,
                    child: Text(
                      'LSX SAP *',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    flex: 7,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade400),
                        borderRadius: BorderRadius.circular(3.r),
                        color: Colors.white,
                      ),
                      child: Stack(
                        children: [
                          TypeAheadField<String>(
                            minCharsForSuggestions: 3,
                            suggestionsBoxDecoration: SuggestionsBoxDecoration(
                              constraints: BoxConstraints(
                                minHeight: 200.h,
                                maxHeight: 200.h,
                              ),
                            ),
                            textFieldConfiguration: TextFieldConfiguration(
                              enabled: !widget.isViewMode,
                              decoration: InputDecoration(
                                labelStyle: TextStyle(fontSize: 12.sp),
                                contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                isDense: true,
                                border: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
                                hintStyle: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic),
                              ),
                              style: TextStyle(fontSize: 12.sp),
                              controller: _lsxSAPController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                            noItemsFoundBuilder: (value) {
                              return Padding(
                                padding: suggestionItemPadding,
                                child: Text("Không tìm thấy LSX SAP", style: TextStyle(fontSize: 12.sp)),
                              );
                            },
                            suggestionsCallback: (pattern) async {
                              if (pattern.length < 3) return [];

                              try {
                                final token = widget.user.token;
                                if (token == null) return [];

                                final suggestions = await TyLeTieuHaoFunction.fetchLSXSAPSuggestions(
                                  token,
                                  pattern,
                                  widget.user.companyCode ?? '',
                                );

                                return suggestions
                                    .map((s) => "${s.lsxSAP} | ${s.maSanPham} - ${s.tenSanPham}###${s.dotSanXuat} | ${s.soLuongKH}")
                                    .toList();
                              } catch (e) {
                                debugPrint('Error fetching LSX SAP suggestions: $e');
                                return [];
                              }
                            },
                            onSuggestionSelected: (String suggestion) {
                              // Split by hidden separator first
                              final mainParts = suggestion.split('###');
                              if (mainParts.length != 2) return;

                              final dotSanXuatParts = mainParts[1].split(' | ');
                              final dotSanXuat = dotSanXuatParts[0];
                              final soLuongKH = dotSanXuatParts[1];

                              final parts = mainParts[0].split(' | ');
                              if (parts.length != 2) return;

                              final lsxSAP = parts[0];
                              final productParts = parts[1].split(' - ');
                              if (productParts.length != 2) return;

                              setState(() {
                                _lsxSAPController.text = lsxSAP;
                                _dotSanXuatController.text = dotSanXuat;
                                _productInfoController.text = "${productParts[0]} | ${productParts[1]}";
                                _soLuongKHController.text = soLuongKH;
                                _materialInfoController.clear();
                                _dvtController.clear();
                              });
                            },
                            itemBuilder: (context, String suggestion) {
                              final visiblePart = suggestion.split('###')[0];
                              return Container(
                                padding: suggestionItemPadding,
                                child: Text(
                                  visiblePart,
                                  style: TextStyle(fontSize: 11.sp),
                                ),
                              );
                            },
                          ),
                          if (_isLoadingLSXSAP)
                            const Positioned(
                              right: 8,
                              top: 8,
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              ),
                            ),
                          Visibility(
                            visible: _lsxSAPController.text.isNotEmpty && !widget.isViewMode,
                            child: Positioned(
                              top: 1,
                              right: 5,
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _lsxSAPController.clear();
                                    _dotSanXuatController.clear();
                                    _productInfoController.clear();
                                    _materialInfoController.clear();
                                    _dvtController.clear();
                                    _soLuongKHController.clear();

                                    _slThoSuDungController.clear();
                                    _slSPLamDuocController.clear();
                                    _slM2DinhMucController.clear();
                                    _tyLeTheoDMController.clear();
                                  });
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),

            // Material field
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FormInputField(
                  label: 'LSX DT',
                  controller: _dotSanXuatController,
                  enabled: false,
                  maxLines: null,
                  style: TextStyle(fontSize: 11.sp, color: Colors.black),
                ),
                // if (_dotSanXuatController.text.isNotEmpty)
                //   Padding(
                //     padding: EdgeInsets.only(left: 15.w + 10.w + (MediaQuery.of(context).size.width * 0.3)),
                //     child: Text(
                //       _dotSanXuatController.text.split(' | ').last,
                //       style: TextStyle(
                //         fontSize: 10.sp,
                //         color: Colors.grey[600],
                //         fontStyle: FontStyle.italic,
                //       ),
                //     ),
                //   ),
              ],
            ),
            SizedBox(height: 10.h),

            // Fix for product code and name fields
            FormInputField(
              label: 'Tên SP',
              controller: _productInfoController,
              enabled: false,
              maxLines: null,
              style: TextStyle(fontSize: 11.sp, color: Colors.black),
            ),
            if (_soLuongKHController.text.isNotEmpty)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  children: [
                    const Expanded(flex: 3, child: SizedBox()),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: Text(
                        'SLKH: ${double.tryParse(_soLuongKHController.text)?.round() ?? _soLuongKHController.text}',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            SizedBox(height: 10.h),

            // Material field
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Tên NVL *',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    flex: 7,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Stack(
                            children: [
                              TypeAheadField<MaterialSuggestion>(
                                minCharsForSuggestions: 3,
                                textFieldConfiguration: TextFieldConfiguration(
                                  enabled: !widget.isViewMode && _productInfoController.text.isNotEmpty,
                                  decoration: InputDecoration(
                                    labelStyle: TextStyle(fontSize: 12.sp),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h).copyWith(right: 25.w),
                                    isDense: true,
                                    border: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    hintText:
                                        _productInfoController.text.isEmpty ? "Vui lòng chọn LSX SAP trước" : "Nhập để tìm NVL (ít nhất 3 kí tự)",
                                    hintStyle: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic),
                                  ),
                                  style: TextStyle(fontSize: 11.sp),
                                  controller: _materialInfoController,
                                  maxLines: null,
                                ),
                                noItemsFoundBuilder: (value) {
                                  return Padding(
                                    padding: suggestionItemPadding,
                                    child: Text("Không tìm thấy NVL", style: TextStyle(fontSize: 12.sp)),
                                  );
                                },
                                suggestionsCallback: (pattern) async {
                                  if (_productInfoController.text.isEmpty) return [];

                                  try {
                                    final token = widget.user.token;
                                    if (token == null) return [];

                                    final productCode = _productInfoController.text.split(' | ')[0];
                                    final suggestions = await TyLeTieuHaoFunction.fetchMaterialSuggestions(
                                      token,
                                      productCode,
                                      widget.user.companyCode ?? '',
                                      query: pattern,
                                    );

                                    return suggestions;
                                  } catch (e) {
                                    debugPrint('Error fetching material suggestions: $e');
                                    return [];
                                  }
                                },
                                onSuggestionSelected: (MaterialSuggestion suggestion) {
                                  setState(() {
                                    _materialInfoController.text = "${suggestion.maNVL} | ${suggestion.tenNVL}";
                                    _dvtController.text = suggestion.dvt;
                                    _dinhMucSanPhamController.text = suggestion.dinhMucSanPham?.toString() ?? '';
                                  });
                                },
                                itemBuilder: (context, MaterialSuggestion suggestion) {
                                  return Container(
                                    padding: suggestionItemPadding,
                                    child: Text(
                                      "${suggestion.maNVL} | ${suggestion.tenNVL}",
                                      style: TextStyle(fontSize: 11.sp),
                                    ),
                                  );
                                },
                              ),
                              Visibility(
                                visible: _materialInfoController.text.isNotEmpty && !widget.isViewMode,
                                child: Positioned(
                                  top: 1,
                                  right: 5,
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _materialInfoController.clear();
                                        _dvtController.clear();
                                      });
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (_materialInfoController.text.isNotEmpty && _dinhMucSanPhamController.text.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.only(top: 4.h),
                            child: Text(
                              'ĐM 1 SP: ${((double.tryParse(_dinhMucSanPhamController.text) ?? 0) / 100).toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: Colors.grey[600],
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),

            // Material field
            FormInputField(
              label: 'ĐVT',
              controller: _dvtController,
              enabled: false,
              maxLines: 1,
              style: TextStyle(fontSize: 11.sp, color: Colors.black),
            ),
            SizedBox(height: 10.h),

            // Quantity fields
            FormInputField(
              label: 'SL thô sử dụng',
              controller: _slThoSuDungController,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Vui lòng nhập SL thô sử dụng';
                final number = double.tryParse(value!);
                if (number == null) return 'Vui lòng nhập số hợp lệ';
                if (number <= 0) return 'Vui lòng nhập số lớn hơn 0';
                return null;
              },
              onChanged: (_) => _calculateDerivedValues(),
            ),
            SizedBox(height: 10.h),

            FormInputField(
              label: 'SL SP làm được',
              controller: _slSPLamDuocController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$'))],
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Vui lòng nhập SL SP làm được';
                final number = double.tryParse(value!);
                if (number == null) return 'Vui lòng nhập số hợp lệ';
                if (number <= 0) return 'Vui lòng nhập số lớn hơn 0';
                return null;
              },
              onChanged: (_) => _calculateDerivedValues(),
            ),
            SizedBox(height: 10.h),

            // Read-only calculated fields
            FormInputField(
              label: 'SL m2 định mức',
              controller: _slM2DinhMucController,
              enabled: false,
            ),
            SizedBox(height: 10.h),

            FormInputField(
              label: 'Tỷ lệ theo DM',
              controller: _tyLeTheoDMController,
              enabled: false,
            ),
            SizedBox(height: 10.h),

            // Employee selection
            _buildEmployeeSection(),
            SizedBox(height: 10.h),

            // Note field
            FormInputField(
              label: 'Ghi chú',
              controller: _noteController,
              maxLines: 3,
              required: false,
            ),
          ],
        ),
        if (!widget.isViewMode)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: FormSubmitButton(
              text: widget.id.isEmpty ? 'Tạo mới' : 'Cập nhật',
              onPressed: _save,
              isLoading: _isSaving,
            ),
          ),
      ],
    );
  }
}
