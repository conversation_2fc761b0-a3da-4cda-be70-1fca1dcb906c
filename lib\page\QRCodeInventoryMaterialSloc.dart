import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:xml/xml.dart';

import '../model/GetBackDataInventMaterialSloc.dart';

class QRCodeInventoryMaterialSloc extends StatefulWidget {
  const QRCodeInventoryMaterialSloc({Key? key}) : super(key: key);

  @override
  _QRCodeInventoryMaterialSlocState createState() => _QRCodeInventoryMaterialSlocState();
}

class _QRCodeInventoryMaterialSlocState extends State<QRCodeInventoryMaterialSloc> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    }
    controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          await Future.delayed(const Duration(milliseconds: 500));
          await controller!.pauseCamera();
          Navigator.pop(context, GetBackDataInventMaterialSloc(isScan: false, rawMaterialID: '', productID: ''));
          return false;
        },
        child: Scaffold(
            body: Stack(alignment: Alignment.bottomCenter, children: <Widget>[
          _buildQrView(context),
          Positioned(
            child: buildButton(context),
          )
        ])));
  }

  Widget buildButton(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 30.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            IconButton(
                onPressed: () async {
                  await controller?.toggleFlash();
                  setState(() {});
                },
                icon: FutureBuilder<bool?>(
                  future: controller?.getFlashStatus(),
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      return Icon(snapshot.data! ? Icons.flash_on : Icons.flash_off, color: Colors.white, size: 30.sp);
                    } else {
                      return Container();
                    }
                  },
                )),
            GestureDetector(
                onTap: () async {
                  await Future.delayed(const Duration(milliseconds: 500));
                  await controller!.pauseCamera();
                  Navigator.pop(context, GetBackDataInventMaterialSloc(isScan: false, rawMaterialID: '', productID: ''));
                },
                child: Text(
                  'Thoát',
                  style: TextStyle(color: Colors.blueAccent, fontSize: 18.sp),
                ))
          ],
        ));
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: (QRViewController controller) => _onQRViewCreated(controller, context),
      overlay: QrScannerOverlayShape(borderColor: Colors.red, borderRadius: 10.r, borderLength: 30, borderWidth: 10, cutOutSize: 300.w),
    );
  }
  // Widget _buildQrViewLandscape(BuildContext context) {
  //   return QRView(
  //     key: qrKey,
  //     onQRViewCreated: (QRViewController controller) =>
  //         _onQRViewCreated(controller, context),
  //     overlay: QrScannerOverlayShape(
  //         borderColor: Colors.red,
  //         borderRadius: 10.r,
  //         borderLength: 30,
  //         borderWidth: 10,
  //         cutOutSize: 150.w),
  //
  //   );
  // }

  Future<void> _onQRViewCreated(QRViewController controller, BuildContext context) async {
    setState(() {
      this.controller = controller;
    });
    if (Platform.isAndroid) {
      await this.controller!.resumeCamera();
    }

    controller.scannedDataStream.listen((scanData) {
      _changPage(scanData.code, context);
    });
  }

  Future<void> _changPage(String? barCode, BuildContext context) async {
    try {
      if (barCode != null) {
        await controller!.pauseCamera();
        String convertBarCode = '''<?xml version="1.0"?><data>$barCode</data>''';
        var getBackDataInventMaterialSloc = getQRCodeMaterial(convertBarCode);
        Navigator.pop(context, getBackDataInventMaterialSloc);
      }
    } catch (error) {
      debugPrint(error.toString());
      controller!.resumeCamera();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.blue[900],
          content: Text(
            'Thẻ không tồn tại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  GetBackDataInventMaterialSloc getQRCodeMaterial(String convertBarCode) {
    XmlDocument document = XmlDocument.parse(convertBarCode);

    var t2Element = document.findAllElements('T2').firstOrNull;

    GetBackDataInventMaterialSloc data = GetBackDataInventMaterialSloc(
      isScan: true,
      rawMaterialID: document.findAllElements('T1').first.text,
      productID: t2Element?.text,
    );
    return data;
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
