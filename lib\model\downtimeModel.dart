class DowntimeModel {
  List<DowntimeRecord>? data;
  String? message;
  bool? status;

  DowntimeModel({this.data, this.message, this.status});

  DowntimeModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <DowntimeRecord>[];
      json['data'].forEach((v) {
        data!.add(DowntimeRecord.fromJson(v));
      });
    }
    message = json['message'];
    status = json['status'];
  }
}

class DowntimeRecord {
  String? id;
  String? date;
  String? departmentCode;
  String? departmentName;
  String? stepCode;
  String? stepName;
  String? startTime;
  String? endTime;
  String? reason;
  String? responsibleTeam;
  String? responsibleDepartment;
  String? personCausedDowntimeCodeMany;
  String? note;
  String? tds;
  String? status;
  String? verificationStatus;
  String? createdDate;
  String? createBy;
  String? updatedDate;
  String? updateBy;
  List<DowntimeHistory>? history;
  String? companyCode;

  DowntimeRecord({
    this.id,
    this.date,
    this.departmentCode,
    this.departmentName,
    this.stepCode,
    this.stepName,
    this.startTime,
    this.endTime,
    this.reason,
    this.responsibleTeam,
    this.responsibleDepartment,
    this.personCausedDowntimeCodeMany,
    this.note,
    this.tds,
    this.status,
    this.verificationStatus,
    this.createdDate,
    this.createBy,
    this.updatedDate,
    this.updateBy,
    this.history,
    this.companyCode,
  });

  DowntimeRecord.fromJson(Map<String, dynamic> json) {
    id = json['downtimeId']?.toString();
    date = json['date'];
    departmentCode = json['departmentCode'];
    departmentName = json['departmentName'];
    stepCode = json['stepCode'];
    stepName = json['stepName'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    reason = json['reason'];
    responsibleTeam = json['responsibleTeam'];
    responsibleDepartment = json['responsibleDepartment'];
    personCausedDowntimeCodeMany = json['personCausedDowntimeCodeMany'];
    note = json['note'];
    tds = json['tds'];
    status = json['status'];
    verificationStatus = json['verificationStatus'];
    createdDate = json['createdDate']?.toString();
    createBy = json['createBy']?.toString();
    updatedDate = json['updatedDate']?.toString();
    updateBy = json['updateBy']?.toString();
    companyCode = json['companyCode'];
    if (json['history'] != null) {
      history = <DowntimeHistory>[];
      json['history'].forEach((v) {
        history!.add(DowntimeHistory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'downtimeId': id,
      'date': date,
      'departmentCode': departmentCode,
      'departmentName': departmentName,
      'stepCode': stepCode,
      'stepName': stepName,
      'startTime': startTime,
      'endTime': endTime,
      'reason': reason,
      'responsibleTeam': responsibleTeam,
      'responsibleDepartment': responsibleDepartment,
      'personCausedDowntimeCodeMany': personCausedDowntimeCodeMany,
      'note': note,
      'tds': tds,
      'status': status,
      'verificationStatus': verificationStatus,
      'createdDate': createdDate,
      'createBy': createBy,
      'updatedDate': updatedDate,
      'updateBy': updateBy,
      'history': history?.map((h) => h.toJson()).toList(),
      'companyCode': companyCode,
    };
  }
}

class EmployeeRecord {
  String? employeeId;
  String? accountId;
  String? employeeCode;
  String? employeeName;

  EmployeeRecord({
    this.employeeId,
    this.accountId,
    this.employeeCode,
    this.employeeName,
  });

  EmployeeRecord.fromJson(Map<String, dynamic> json) {
    employeeId = json['employeeId'];
    accountId = json['accountId'];
    employeeCode = json['employeeCode'];
    employeeName = json['employeeName'];
  }

  Map<String, dynamic> toJson() {
    return {
      'employeeId': employeeId,
      'accountId': accountId,
      'employeeCode': employeeCode,
      'employeeName': employeeName,
    };
  }
}

class DowntimeHistory {
  final String? historyId;
  final String? downtimeId;
  final String? action;
  final String? actionDisplay;
  final String? oldStatus;
  final String? oldStatusDisplay;
  final String? newStatus;
  final String? newStatusDisplay;
  final String? changedBy;
  final String? changedByName;
  final String? changedDate;
  final String? verifierRole;
  final String? comment;

  DowntimeHistory({
    this.historyId,
    this.downtimeId,
    this.action,
    this.actionDisplay,
    this.oldStatus,
    this.oldStatusDisplay,
    this.newStatus,
    this.newStatusDisplay,
    this.changedBy,
    this.changedByName,
    this.changedDate,
    this.verifierRole,
    this.comment,
  });

  factory DowntimeHistory.fromJson(Map<String, dynamic> json) {
    return DowntimeHistory(
      historyId: json['historyId']?.toString(),
      downtimeId: json['downtimeId']?.toString(),
      action: json['action'],
      actionDisplay: json['actionDisplay'],
      oldStatus: json['oldStatus'],
      oldStatusDisplay: json['oldStatusDisplay'],
      newStatus: json['newStatus'],
      newStatusDisplay: json['newStatusDisplay'],
      changedBy: json['changedBy'],
      changedByName: json['changedByName'],
      changedDate: json['changedDate'],
      verifierRole: json['verifierRole'],
      comment: json['comment'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'historyId': historyId,
      'downtimeId': downtimeId,
      'action': action,
      'actionDisplay': actionDisplay,
      'oldStatus': oldStatus,
      'oldStatusDisplay': oldStatusDisplay,
      'newStatus': newStatus,
      'newStatusDisplay': newStatusDisplay,
      'changedBy': changedBy,
      'changedByName': changedByName,
      'changedDate': changedDate,
      'verifierRole': verifierRole,
      'comment': comment,
    };
  }
}

class DowntimeMasterDataResponse {
  List<DepartmentItem>? departments;
  List<StepCodeItem>? steps;
  List<WorkshopItem>? workshops;
  List<EmployeeRecord>? employees;
  String? message;
  bool? status;
  dynamic data;

  DowntimeMasterDataResponse({this.departments, this.steps, this.workshops, this.employees, this.message, this.status, this.data});

  DowntimeMasterDataResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    data = json['data'];

    if (json['data'] != null) {
      if (json['data'] is List) {
        var items = json['data'] as List;
        // Check first item to determine type
        if (items.isNotEmpty) {
          if (items[0].containsKey('departmentCode')) {
            departments = items.map((v) => DepartmentItem.fromJson(v)).toList();
          } else if (items[0].containsKey('stepCode')) {
            steps = items.map((v) => StepCodeItem.fromJson(v)).toList();
          } else if (items[0].containsKey('workShopCode')) {
            workshops = items.map((v) => WorkshopItem.fromJson(v)).toList();
          } else if (items[0].containsKey('employeeCode')) {
            employees = items.map((v) => EmployeeRecord.fromJson(v)).toList();
          }
        }
      }
    }
  }
}

class DepartmentItem {
  String? departmentCode;
  String? departmentName;

  DepartmentItem({this.departmentCode, this.departmentName});

  DepartmentItem.fromJson(Map<String, dynamic> json) {
    departmentCode = json['departmentCode'];
    departmentName = json['departmentName'];
  }
}

class StepCodeItem {
  String? stepCode;
  String? stepName;

  StepCodeItem({this.stepCode, this.stepName});

  StepCodeItem.fromJson(Map<String, dynamic> json) {
    stepCode = json['stepCode'];
    stepName = json['stepName'];
  }
}

class WorkshopItem {
  String? workShopCode;
  String? workShopName;
  String? saleOrgCode;
  String? storeName;

  WorkshopItem({this.workShopCode, this.workShopName, this.saleOrgCode, this.storeName});

  WorkshopItem.fromJson(Map<String, dynamic> json) {
    workShopCode = json['workShopCode'];
    workShopName = json['workShopName'];
    saleOrgCode = json['saleOrgCode'];
    storeName = json['storeName'];
  }
}

class DowntimeSearchModel {
  String? departmentCode;
  String? stepCode;
  String? reason;
  DateTime? fromDate;
  DateTime? toDate;
  String? status;
  String? verificationStatus;
  String companyCode;
  int pageNumber;
  int pageSize;

  DowntimeSearchModel({
    this.departmentCode,
    this.stepCode,
    this.reason,
    this.fromDate,
    this.toDate,
    this.status,
    this.verificationStatus,
    required this.companyCode,
    this.pageNumber = 1,
    this.pageSize = 20,
  });

  Map<String, dynamic> toJson() => {
        'departmentCode': departmentCode,
        'stepCode': stepCode,
        'reason': reason,
        'fromDate': fromDate?.toIso8601String(),
        'toDate': toDate?.toIso8601String(),
        'status': status,
        'verificationStatus': verificationStatus,
        'companyCode': companyCode,
        'pageNumber': pageNumber,
        'pageSize': pageSize,
      };
}
