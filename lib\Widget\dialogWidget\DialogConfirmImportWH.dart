import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DialogConfirmImportWH extends StatelessWidget {
  const DialogConfirmImportWH({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(

      content: SingleChildScrollView(
          child:ListBody(
              children:<Widget>[
                Text("Bạn có chắc xác nhận pallet này về nhà máy không ?",
                  style: TextStyle(fontSize: 15.sp,color: Colors.grey.shade700),textAlign: TextAlign.center),
              ])),
      actions: <Widget>[
        Row(
          children: <Widget> [
            Expanded(
              flex: 5,
              child: TextButton(
                onPressed: () => Navigator.pop(context,false),
                style:  ButtonStyle(
                  overlayColor:  MaterialStateProperty.all(const Color(0xff0052cc).withOpacity(0.1)),
                ),
                child: Text('Không',style: TextStyle(fontSize: 14.sp, color: const Color(0xff0052cc)),
                ),
              ),
            ),
            Expanded(
              flex: 5,
              child: TextButton(
                onPressed: () => Navigator.pop(context, true),
                style:  ButtonStyle(
                  overlayColor:  MaterialStateProperty.all(const Color(0xff0052cc).withOpacity(0.1)),
                ),
                child: Text('Có',style: TextStyle(fontSize: 14.sp, color: const Color(0xff0052cc)),
              ),
            ),
            ),
          ],
        )
      ],
    );
  }
}