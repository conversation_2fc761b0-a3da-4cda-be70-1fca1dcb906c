# TTF MES Mobile Application Screen Documentation

This document provides a comprehensive overview of all screens in the TTF MES Mobile application, organized by main features.

## Table of Contents
1. [<PERSON><PERSON> nhận sản lượ<PERSON> (Production Recording)](#1-ghi-nhận-sản-lượng-production-recording)
2. [<PERSON><PERSON><PERSON><PERSON> lý NVL (Raw Material Management)](#2-quản-lý-nvl-raw-material-management)
3. [<PERSON><PERSON><PERSON><PERSON> lý TP (Product Management)](#3-quản-lý-tp-product-management)
4. [<PERSON><PERSON><PERSON> tra chất lượ<PERSON> (Quality Control)](#4-kiểm-tra-chất-lượng-quality-control)
5. [QC Passed Stamp](#5-qc-passed-stamp)
6. [Downtime](#6-downtime)
7. [Maintenance Order](#7-maintenance-order)
8. [Tỷ lệ tiêu hao (Consumption Rate)](#8-tỷ-lệ-tiêu-hao-consumption-rate)
9. [Tr<PERSON> hàng NCC (Supplier Return)](#9-trả-hàng-ncc-supplier-return)

---

## 1. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sả<PERSON> l<PERSON> (Production Recording)

### Main Screens
- **SwitchingStages**: Records production progress between stages
- **CompleteTheBigStage**: Completes major production stages
- **InforSwitchingStages**: Displays information about stage transitions
- **DetailReport**: Shows detailed production reports

### Screen Flow
1. **SwitchingStage**
   - Path: `/SwitchingStage`
   - Purpose: Allows users to record transitions between production stages
   - Parameters: 
     - `switchingStateData`: Data about the current switching state
     - `token`: Authentication token
     - `switchingStages`: Stages information
     - `dateTimeOld`: Timestamp
     - `toBarcode`: Target barcode

2. **CompleteTheBigStage**
   - Path: `/CompleteTheBigStage`
   - Purpose: Marks completion of major production stages
   - Parameters:
     - `getData`: Data to be processed
     - `token`: Authentication token
     - `confirmWorkCenter`: Work center confirmation data
     - `dateTimeOld`: Timestamp

3. **DetailReport**
   - Path: `/DetailReport`
   - Purpose: Displays detailed production reports
   - Parameters:
     - `getData`: Report data
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

## 2. Quản lý NVL (Raw Material Management)

### Main Screens
- **InventoryManagement**: Main inventory management screen
- **ImportWarehouseSAP**: Imports materials from SAP
- **ListTranferMaterial**: Lists material transfers
- **ExportWarehouse**: Exports materials from warehouse
- **AddTranferWarehouse**: Adds new material transfers
- **MaterialRetail**: Manages retail materials
- **MaterialUnused**: Manages unused materials
- **StatisticsMaterials**: Shows material statistics

### Screen Flow
1. **InventoryManagement**
   - Path: `/InventoryManagement`
   - Purpose: Main screen for inventory management
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID

2. **ImportWarehouseSAP**
   - Path: `/ImportWarehouse`
   - Purpose: Imports materials from SAP system
   - Parameters:
     - `barcode`: Material barcode
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `fromPage`: Source page

3. **ListTranferMaterial** and **ListTranferMaterial2**
   - Paths: `/ListTranferMaterial` and `/ListTranferMaterial2`
   - Purpose: Lists all material transfers
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID

4. **ExportWarehouse** and **ExportWarehouse2**
   - Paths: `/ExportWarehouse` and `/ExportWarehouse2`
   - Purpose: Exports materials from warehouse
   - Parameters:
     - `dataListWarehouseTranfer`: Transfer data
     - `token`: Authentication token
     - `plant`: Plant information
     - `lsDataSlocAddress`: Storage location addresses
     - `dateTimeOld`: Timestamp

5. **AddTranferWarehouse**
   - Path: `/AddTranferWarehouse`
   - Purpose: Adds new material transfers
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `lsDataSlocAddress`: Storage location addresses
     - `dateTimeOld`: Timestamp

6. **MaterialRetail**
   - Path: `/MaterialRetail`
   - Purpose: Manages retail materials
   - Parameters:
     - `token`: Authentication token
     - `barcode`: Material barcode
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

7. **MaterialUnused**
   - Path: `/MaterialUnused`
   - Purpose: Manages unused materials
   - Parameters:
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

8. **StatisticsMaterials**
   - Path: `/StatisticsMaterials`
   - Purpose: Shows material statistics
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

9. **InventoryMaterialSloc**
   - Path: `/InventoryMaterialSloc`
   - Purpose: Manages inventory by storage location
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

## 3. Quản lý TP (Product Management)

### Main Screens
- **ProductManagement**: Main product management screen
- **ImportProduct**: Imports finished products

### Screen Flow
1. **ProductManagement**
   - Path: `/ProductManagement`
   - Purpose: Main screen for product management
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID
     - `user`: User information

2. **ImportProduct**
   - Path: `/ImportProduct`
   - Purpose: Imports finished products
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID
     - `user`: User information

## 4. Kiểm tra chất lượng (Quality Control)

### Main Screens
- **KiemTraChatLuong**: Main quality control screen
- **ListQC** and **ListQCNVL**: Lists quality control records
- **BaoCaoDauVao** and **BaoCaoDauVao2**: Input quality reports
- **PhieuKCSCongDoan**: Process quality control reports
- **BaoCaoQAQCNghiemThu**: QA/QC acceptance reports
- **BaoCaoQCMau**: Sample quality control reports
- **BaoCaoQCGiaCong**: Processing quality control reports
- **BaoCaoQCSanPham**: Product quality control reports
- **BaoCaoQCHienTruong**: Field quality control reports
- **BaoCaoQC5S**: 5S quality control reports

### Screen Flow
1. **KiemTraChatLuong**
   - Path: `/KiemTraChatLuong`
   - Purpose: Main screen for quality control
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID
     - `user`: User information

2. **ListQC** and **ListQCNVL**
   - Paths: `/ListQC` and `/ListQCNVL`
   - Purpose: Lists quality control records
   - Parameters:
     - `dateTimeOld`: Timestamp
     - `userModel`/`user`: User information

3. **BaoCaoDauVao** and **BaoCaoDauVao2**
   - Paths: `/BaoCaoDauVao` and `/BaoCaoDauVao2`
   - Purpose: Input quality reports
   - Parameters:
     - `userModel`: User information
     - `dateTimeOld`: Timestamp

4. **BaoCaoDauVaoDetail** and **BaoCaoDauVaoDetail2**
   - Paths: `/BaoCaoDauVaoDetail` and `/BaoCaoDauVaoDetail2`
   - Purpose: Detailed input quality reports
   - Parameters:
     - `qualityControlId`: Quality control ID
     - `dateTimeOld`: Timestamp
     - `fromPage`: Source page
     - `qrCode`: QR code information
     - `user`: User information

5. **PhieuKCSCongDoan**
   - Path: `/PhieuKCSCongDoan`
   - Purpose: Process quality control reports
   - Parameters:
     - `user`: User information
     - `dateTimeOld`: Timestamp

6. **PhieuKCSCongDoanDetail**
   - Path: `/PhieuKCSCongDoanDetail`
   - Purpose: Detailed process quality control reports
   - Parameters:
     - `qualityControlId`: Quality control ID
     - `dateTimeOld`: Timestamp
     - `fromPage`: Source page
     - `qrCode`: QR code information
     - `user`: User information

7. **BaoCaoQAQCNghiemThu**
   - Path: `/BaoCaoQAQCNghiemThu`
   - Purpose: QA/QC acceptance reports
   - Parameters:
     - `user`: User information
     - `dateTimeOld`: Timestamp

8. **BaoCaoQAQCNghiemThuDetail**
   - Path: `/BaoCaoQAQCNghiemThuDetail`
   - Purpose: Detailed QA/QC acceptance reports
   - Parameters:
     - `qualityControlId`: Quality control ID
     - `dateTimeOld`: Timestamp
     - `fromPage`: Source page
     - `qrCode`: QR code information
     - `user`: User information

9. **BaoCaoQCMau**
   - Path: `/BaoCaoQCMau`
   - Purpose: Sample quality control reports
   - Parameters:
     - `user`: User information
     - `dateTimeOld`: Timestamp

10. **BaoCaoQCMauDetail**
    - Path: `/BaoCaoQCMauDetail`
    - Purpose: Detailed sample quality control reports
    - Parameters:
      - `qualityControlId`: Quality control ID
      - `dateTimeOld`: Timestamp
      - `fromPage`: Source page
      - `qualityType`: "QCMAU"
      - `user`: User information

11. **BaoCaoQCGiaCong**
    - Path: `/BaoCaoQCGiaCong`
    - Purpose: Processing quality control reports
    - Parameters:
      - `user`: User information
      - `dateTimeOld`: Timestamp

12. **BaoCaoQCGiaCongDetail**
    - Path: `/BaoCaoQCGiaCongDetail`
    - Purpose: Detailed processing quality control reports
    - Parameters:
      - `qualityControlId`: Quality control ID
      - `dateTimeOld`: Timestamp
      - `fromPage`: Source page
      - `qualityType`: "QCGIACONG"
      - `user`: User information

13. **BaoCaoQCSanPham**
    - Path: `/BaoCaoQCSanPham`
    - Purpose: Product quality control reports
    - Parameters:
      - `user`: User information
      - `dateTimeOld`: Timestamp

14. **BaoCaoQCSanPhamDetail**
    - Path: `/BaoCaoQCSanPhamDetail`
    - Purpose: Detailed product quality control reports
    - Parameters:
      - `qualityControlId`: Quality control ID
      - `dateTimeOld`: Timestamp
      - `fromPage`: Source page
      - `qrCode`: QR code information
      - `user`: User information

15. **BaoCaoQCHienTruong**
    - Path: `/BaoCaoQCHienTruong`
    - Purpose: Field quality control reports
    - Parameters:
      - `user`: User information
      - `dateTimeOld`: Timestamp

16. **BaoCaoQCHienTruongDetail**
    - Path: `/BaoCaoQCHienTruongDetail`
    - Purpose: Detailed field quality control reports
    - Parameters:
      - `qualityControlId`: Quality control ID
      - `dateTimeOld`: Timestamp
      - `fromPage`: Source page
      - `qualityType`: "HIENTRUONG"
      - `user`: User information

17. **BaoCaoQC5S**
    - Path: `/BaoCaoQC5S`
    - Purpose: 5S quality control reports
    - Parameters:
      - `user`: User information
      - `dateTimeOld`: Timestamp

18. **BaoCaoQC5SDetail**
    - Path: `/BaoCaoQC5SDetail`
    - Purpose: Detailed 5S quality control reports
    - Parameters:
      - `qualityControlId`: Quality control ID
      - `dateTimeOld`: Timestamp
      - `fromPage`: Source page
      - `qualityType`: "QC5S"
      - `user`: User information

## 5. QC Passed Stamp

### Main Screens
- **QCPassedStampScan**: Scans and validates QC passed stamps

### Screen Flow
1. **QCPassedStampScan**
   - Path: `/QCPassedStampScan`
   - Purpose: Scans and validates QC passed stamps
   - Parameters:
     - `barcode`: Product barcode
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

## 6. Downtime

### Main Screens
- **DowntimeList**: Lists downtime records
- **DowntimeDetail**: Shows downtime details

### Screen Flow
1. **DowntimeList**
   - Path: `/Downtime`
   - Purpose: Lists all downtime records
   - Parameters:
     - `dateTimeOld`: Timestamp
     - `user`: User information

2. **DowntimeDetail**
   - Path: `/DowntimeDetail`
   - Purpose: Shows detailed information about a downtime record
   - Parameters:
     - `id`: Downtime record ID
     - `dateTimeOld`: Timestamp
     - `user`: User information

## 7. Maintenance Order

### Main Screens
- **MaintenanceOrderList**: Lists maintenance orders
- **MaintenanceOrderDetail**: Shows maintenance order details

### Screen Flow
1. **MaintenanceOrderList**
   - Path: `/MaintenanceOrder`
   - Purpose: Lists all maintenance orders
   - Parameters:
     - `dateTimeOld`: Timestamp
     - `user`: User information

2. **MaintenanceOrderDetail**
   - Path: `/MaintenanceOrderDetail`
   - Purpose: Shows detailed information about a maintenance order
   - Parameters:
     - `id`: Maintenance order ID
     - `dateTimeOld`: Timestamp
     - `user`: User information
     - `viewMode`: View mode flag (optional)
     - `maintenanceOrder`: Maintenance order data (optional)

## 8. Tỷ lệ tiêu hao (Consumption Rate)

### Main Screens
- **TyLeTieuHaoList**: Lists consumption rate records
- **TyLeTieuHaoDetail**: Shows consumption rate details

### Screen Flow
1. **TyLeTieuHaoList**
   - Path: `/TyLeTieuHao`
   - Purpose: Lists all consumption rate records
   - Parameters:
     - `dateTimeOld`: Timestamp
     - `user`: User information

2. **TyLeTieuHaoDetail**
   - Path: `/TyLeTieuHaoDetail`
   - Purpose: Shows detailed information about a consumption rate record
   - Parameters:
     - `id`: Consumption rate record ID
     - `dateTimeOld`: Timestamp
     - `user`: User information
     - `viewMode`: View mode flag (optional)

## 9. Trả hàng NCC (Supplier Return)

### Main Screens
- **TraHangNCC**: Main supplier return screen
- **CreateNewTraHangNCC**: Creates new supplier return

### Screen Flow
1. **TraHangNCC**
   - Path: `/TraHangNCC`
   - Purpose: Main screen for supplier returns
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID

2. **CreateNewTraHangNCC**
   - Path: `/CreateNewTraHangNCC`
   - Purpose: Creates new supplier return
   - Parameters:
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp
     - `id`: Return ID

## Utility Screens

### QR Code Scanning Screens
- **QrCodePage**: General QR code scanning
- **QRCodePageChooseAnAddress**: Scans QR code to choose an address
- **QRcodePageGetWorkShopDepartment**: Gets workshop department from QR code
- **QRCodePageListQC** and **QRCodePageListQC2**: QR code for quality control lists
- **QRcodePageTranferMaterial**: QR code for material transfers
- **QRCodePageGetSlocChooseAnTranferWareHouse**: QR code for storage location in transfers
- **QRCodePageGetSlocExport**: QR code for storage location in exports
- **QRCodeInventoryMaterialSloc**: QR code for inventory by storage location

### Navigation and Common Screens
- **SplashScreen**: Application splash screen
- **Login**: User login screen
- **BottomNavigatorBar**: Bottom navigation bar
- **NotificationPage**: Notifications screen
- **Infor**: Information screen

## Screen Relationships

The application follows a hierarchical structure where:

1. After login, users are directed to the main page
2. From the main page, users can access different feature modules
3. Each feature module has list screens and detail screens
4. QR code scanning is used throughout the application for data entry
5. The bottom navigation bar provides quick access to common features

## Common Parameters

Most screens in the application accept the following common parameters:
- `token`: Authentication token
- `dateTimeOld`: Timestamp for tracking
- `user`/`userModel`: User information
- `permission`: User permissions
- `plant`: Plant information
- `accountId`: User account ID

## Navigation Patterns

The application uses several navigation patterns:
1. **List-Detail Pattern**: Lists of items that navigate to detail screens
2. **QR Code Scanning**: Many operations start with scanning a QR code
3. **Form-Based Entry**: Data entry through forms
4. **Bottom Navigation**: Quick access to main features