import 'dart:io';
import 'package:flutter/material.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/qualityControlApi.dart';
import '../../model/sendErrorQualityControl.dart';

class QualityControlErrorFunction {
  // static ErrorList defaultErrorList = ErrorList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static Error defaultListError =
      Error(qualityControlErrorId: " ", qualityControlId: " ", catalogCode: " ", levelError: " ", quantityError: 0.0, notes: " ");
  static List<SendErrorControl> getLsError(
    List<Error>? lsError,
    QualityControl? qualityControl,
    List<ErrorList?> lsSelectedError,
    List<DataGetDefectLevel?> lselectDataGetDefectLevel,
    List<TextEditingController> lsControllerSoLuong<PERSON><PERSON>,
    List<TextEditingController> lsControllerGhi<PERSON>huL<PERSON>,
    List<List<File>> lsGetFileError,
    List<DropdownItemList?> lsSelectedCaNhanGayLoi,
    List<DropdownItemList?> lsSelectedCongDoanLoi,
    List<DropdownItemList?> lsSelectedPhuongAnXuLy,
    List<TextEditingController> lsControllerNhaMayLoi,
    List<TextEditingController> lsControllerPhanXuongLoi,
    List<TextEditingController> lsControllerToChuyenLoi,
    List<List<DropdownItemList?>> lsSelectedCaNhanGayLoiMany,
    //
    List<TextEditingController> _lsControllerCaNhanLoi1QuanDoc,
    List<TextEditingController> _lsControllerCaNhanLoi2ToTruong,
    List<TextEditingController> _lsControllerCaNhanLoi3QAQC,
    List<TextEditingController> _lsControllerCaNhanLoi4KCS,
  ) {
    List<SendErrorControl> lsErrorSend = [];
    SendErrorControl? sendErrorControl;
    for (int i = 0; i < lsError!.length; i++) {
      // Có thông tin error

// for each lsSelectedCaNhanGayLoiMany item, get all catalog code and join it with , separator
      String selectedCaNhanGayLoiMany = "";

      if (lsSelectedCaNhanGayLoiMany.isNotEmpty && lsSelectedCaNhanGayLoiMany[i].isNotEmpty) {
        selectedCaNhanGayLoiMany = lsSelectedCaNhanGayLoiMany[i].map((e) => e?.catalogCode).where((element) => element != null).join(",");
      }

      if (qualityControl!.error!.isNotEmpty) {
        sendErrorControl = SendErrorControl(
          lsError[i].qualityControlErrorId == null
              ? "null"
              : lsError[i].qualityControlErrorId == " "
                  ? "null"
                  : lsError[i].qualityControlErrorId.toString(),
          lsSelectedError[i] != null
              ? lsSelectedError[i]!.catalogCode != " "
                  ? lsSelectedError[i]!.catalogCode
                  : "null"
              : lsError[i].catalogCode ?? "null",
          lselectDataGetDefectLevel[i] == null ? lsError[i].levelError ?? "null" : lselectDataGetDefectLevel[i]!.key.toString(),
          lsControllerSoLuongLoi[i].text.isNotEmpty
              ? int.parse(lsControllerSoLuongLoi[i].text)
              : lsError[i].quantityError == null
                  ? -1
                  : lsError[i].quantityError!.round(),
          lsControllerGhiChuLoi[i].text.isEmpty ? lsError[i].notes ?? "null" : lsControllerGhiChuLoi[i].text,
          lsGetFileError[i],
          lsSelectedCaNhanGayLoi[i] != null
              ? lsSelectedCaNhanGayLoi[i]!.catalogCode != " "
                  ? lsSelectedCaNhanGayLoi[i]!.catalogCode
                  : "null"
              : lsError[i].catalogCode ?? "null",
          lsSelectedCongDoanLoi[i] != null
              ? lsSelectedCongDoanLoi[i]!.catalogCode != " "
                  ? lsSelectedCongDoanLoi[i]!.catalogCode
                  : "null"
              : lsError[i].congDoanLoi ?? "null",
          lsSelectedPhuongAnXuLy[i] != null
              ? lsSelectedPhuongAnXuLy[i]!.catalogCode != " "
                  ? lsSelectedPhuongAnXuLy[i]!.catalogCode
                  : "null"
              : lsError[i].phuongAnXuLy ?? "null",
          // TODO: load thông tin ra thêm thông tin name
          lsControllerNhaMayLoi[i].text.isEmpty ? lsError[i].nhaMayLoi ?? "" : extractCode(lsControllerNhaMayLoi[i].text),
          lsControllerPhanXuongLoi[i].text.isEmpty ? lsError[i].phanXuongLoi ?? "" : extractCode(lsControllerPhanXuongLoi[i].text),
          lsControllerToChuyenLoi[i].text.isEmpty ? lsError[i].toChuyenLoi ?? "" : extractCode(lsControllerToChuyenLoi[i].text),
          selectedCaNhanGayLoiMany,
          _lsControllerCaNhanLoi1QuanDoc.isNotEmpty
              ? _lsControllerCaNhanLoi1QuanDoc[i].text.isEmpty
                  ? lsError[i].quanDoc ?? "null"
                  : extractCode(_lsControllerCaNhanLoi1QuanDoc[i].text)
              : lsError[i].quanDoc ?? "null",
          _lsControllerCaNhanLoi2ToTruong.isNotEmpty
              ? _lsControllerCaNhanLoi2ToTruong[i].text.isEmpty
                  ? lsError[i].toTruong ?? "null"
                  : extractCode(_lsControllerCaNhanLoi2ToTruong[i].text)
              : lsError[i].toTruong ?? "null",
          _lsControllerCaNhanLoi3QAQC.isNotEmpty
              ? _lsControllerCaNhanLoi3QAQC[i].text.isEmpty
                  ? lsError[i].qaqc ?? "null"
                  : extractCode(_lsControllerCaNhanLoi3QAQC[i].text)
              : lsError[i].qaqc ?? "null",
          _lsControllerCaNhanLoi4KCS.isNotEmpty
              ? _lsControllerCaNhanLoi4KCS[i].text.isEmpty
                  ? lsError[i].kcs ?? "null"
                  : extractCode(_lsControllerCaNhanLoi4KCS[i].text)
              : lsError[i].kcs ?? "null",
        );
        lsErrorSend.add(sendErrorControl);
      } else {
        // Không có thông tin error, gán mặc định
        sendErrorControl = SendErrorControl(
          "null",
          lsSelectedError[i] != null
              ? lsSelectedError[i]!.catalogCode != " "
                  ? lsSelectedError[i]!.catalogCode.toString()
                  : "null"
              : "null",
          lselectDataGetDefectLevel[i] != null ? lselectDataGetDefectLevel[i]!.key : "null",
          lsControllerSoLuongLoi[i].text.isEmpty ? -1 : int.parse(lsControllerSoLuongLoi[i].text),
          lsControllerGhiChuLoi[i].text.isEmpty ? "" : lsControllerGhiChuLoi[i].text,
          lsGetFileError[i],
          lsSelectedCaNhanGayLoi.isEmpty
              ? ""
              : lsSelectedCaNhanGayLoi[i] != null
                  ? lsSelectedCaNhanGayLoi[i]!.catalogCode != " "
                      ? lsSelectedCaNhanGayLoi[i]!.catalogCode.toString()
                      : "null"
                  : "null",
          lsSelectedCongDoanLoi.isEmpty
              ? ""
              : lsSelectedCongDoanLoi[i] != null
                  ? lsSelectedCongDoanLoi[i]!.catalogCode != " "
                      ? lsSelectedCongDoanLoi[i]!.catalogCode.toString()
                      : "null"
                  : "null",
          lsSelectedPhuongAnXuLy.isEmpty
              ? ""
              : lsSelectedPhuongAnXuLy[i] != null
                  ? lsSelectedPhuongAnXuLy[i]!.catalogCode != " "
                      ? lsSelectedPhuongAnXuLy[i]!.catalogCode.toString()
                      : "null"
                  : "null",
          lsControllerNhaMayLoi.isNotEmpty
              ? lsControllerNhaMayLoi[i].text.isEmpty
                  ? "null"
                  : extractCode(lsControllerNhaMayLoi[i].text)
              : "null",
          lsControllerPhanXuongLoi.isNotEmpty
              ? lsControllerPhanXuongLoi[i].text.isEmpty
                  ? "null"
                  : extractCode(lsControllerPhanXuongLoi[i].text)
              : "null",
          lsControllerToChuyenLoi.isNotEmpty
              ? lsControllerToChuyenLoi[i].text.isEmpty
                  ? "null"
                  : extractCode(lsControllerToChuyenLoi[i].text)
              : "null",
          selectedCaNhanGayLoiMany, // selectedCaNhanGayLoiMany
          _lsControllerCaNhanLoi1QuanDoc.isNotEmpty
              ? _lsControllerCaNhanLoi1QuanDoc[i].text.isEmpty
                  ? "null"
                  : extractCode(_lsControllerCaNhanLoi1QuanDoc[i].text)
              : "null",
          _lsControllerCaNhanLoi2ToTruong.isNotEmpty
              ? _lsControllerCaNhanLoi2ToTruong[i].text.isEmpty
                  ? "null"
                  : extractCode(_lsControllerCaNhanLoi2ToTruong[i].text)
              : "null",
          _lsControllerCaNhanLoi3QAQC.isNotEmpty
              ? _lsControllerCaNhanLoi3QAQC[i].text.isEmpty
                  ? "null"
                  : extractCode(_lsControllerCaNhanLoi3QAQC[i].text)
              : "null",
          _lsControllerCaNhanLoi4KCS.isNotEmpty
              ? _lsControllerCaNhanLoi4KCS[i].text.isEmpty
                  ? "null"
                  : extractCode(_lsControllerCaNhanLoi4KCS[i].text)
              : "null",
        );
        lsErrorSend.add(sendErrorControl);
      }
    } // end for
    return lsErrorSend;
  }
}

String extractCode(String text) {
  List<String> parts = text.split(' | ');
  if (parts.length >= 2) {
    return parts[0]; // Return code if format is correct.
  } else {
    return ""; // Return an empty string if format is not correct.
  }
}
