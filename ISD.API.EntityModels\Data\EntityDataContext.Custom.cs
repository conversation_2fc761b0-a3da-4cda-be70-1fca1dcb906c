﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Data
{
    public partial class EntityDataContext : DbContext
    {
        public EntityDataContext(string nameOrConnectionString)
            : base(new DbContextOptionsBuilder<EntityDataContext>().UseSqlServer(nameOrConnectionString).Options)
        {

        }
    }
}
