using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ISD.API.ViewModels.MaiDao;
using ISD.API.EntityModels.Models;

namespace ISD.API.Repositories.Interfaces
{
    public interface IMaiDaoRepository
    {
        Task<MaiDaoListResponse> GetMaiDaoListAsync(MaiDaoSearchModel model);
        Task<MaiDaoResponse> GetMaiDaoByIdAsync(Guid maiDaoId);
        Task<MaiDaoResponse> CreateMaiDaoAsync(MaiDaoRecordVM model, Guid userId);
        Task<MaiDaoResponse> UpdateMaiDaoAsync(Guid maiDaoId, MaiDaoRecordVM model, Guid userId);
        Task<List<EquipmentItem>> GetEquipmentAsync(string companyCode, string searchTerm);
        Task<List<MaterialItem>> GetMaterialsAsync(string companyCode, string searchTerm);
        Task<List<EmployeeItem>> GetEmployeesAsync(string companyCode);
        Task<MaiDaoRecordVM> CheckExistingRecordAsync(string equipmentCode, string companyCode);
        Task<List<MaiDaoHistoryModel>> GetMaiDaoHistoryAsync(Guid maiDaoId);
    }
} 