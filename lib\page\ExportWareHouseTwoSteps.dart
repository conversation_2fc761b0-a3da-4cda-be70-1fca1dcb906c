// import 'dart:io';
// import 'package:collection/collection.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import '../model/getBatch.dart';
// import '../model/getInventorySAP.dart';
// import '../model/getReservation.dart';
// import '../model/listWarehouseTranfer.dart';
// import '../model/rawMaterialCard.dart';
// import '../model/slocAddresse.dart';
// import '../model/warehouseTranfer.dart';
// import '../repository/function/exportWarehouseFunction.dart';
// import '../repository/function/importWareHouseFunction.dart';
// import 'LostConnect.dart';
//
// class ExportWarehouseTwoSteps extends StatefulWidget {
//   final DataListWarehouseTranfer dataListWarehouseTranfer;
//   final String token;
//   final String plant;
//   final List<DataSlocAddress> lsDataSlocAddress;
//   final bool isCheckTest;
//   const ExportWarehouseTwoSteps(
//       {Key? key,
//         required this.dataListWarehouseTranfer,
//         required this.token,
//         required this.plant,
//         required this.lsDataSlocAddress,
//         required this.isCheckTest
//       }) : super(key: key);
//   @override
//   _ExportWarehouseTwoStepsState createState() => _ExportWarehouseTwoStepsState();
// }
//
// class _ExportWarehouseTwoStepsState extends State<ExportWarehouseTwoSteps> {
//   final _controllerQuantity = TextEditingController();
//   final _focusQuantity = FocusNode();
//   final _storageBinController = TextEditingController();
//   final _focusStorageBin = FocusNode();
//   final _storageBinControllerImport = TextEditingController();
//   final _focusStorageBinImport = FocusNode();
//
//   bool _isLoading = false;
//   bool _isNotWifi = false;
//   bool _isLoadingRawMaterial = false;
//
//   DataSlocAddress? _selectedSloc;
//   DataSlocAddress? _selectedSlocInput;
//   DataBatch? _getBatch;
//   DataReservation? _dataReservation;
//   bool _errorSelectedSloc = false;
//
//
//   bool _disableDropdown = false;
//   bool _disableDropdownInput = false;
//   bool _isLoadingInventory = false;
//   bool _errorQuantity = false;
//   String? _materialID;
//   String? _defaultStorageBinIDExport;
//   String? _defaultStorageBinIDImport;
//   Data? _inventorySAPApi;
//   late List<DataSlocAddress?> _getLsDataSlocAddress;
//   DataRawMeterial? _dataRawMaterial;
//
//   @override
//   void initState() {
//     super.initState();
//     _cloneList();
//   }
//
//   Future<void> _getReservation() async {
//     setState(() {
//       _isLoading = true;
//       _isNotWifi = false;
//     });
//     final data = await ExportWareHouseFunction.fetchReservation(widget.dataListWarehouseTranfer.reservationId.toString(),  widget.token);
//     if(!mounted) return;
//     setState(() {
//       _isLoading = false;
//     });
//     if (data != null) {
//       _dataReservation = data;
//     }
//   }
//   Future<void> _getDataRawMaterial(String materialID,BuildContext context) async {
//     try {
//       setState(() {
//         _isLoadingRawMaterial = true;
//         _materialID = materialID;
//       });
//       final data = await Future.wait([
//         ImportWareHouseFunction.fetchRawMaterial(materialID, widget.token,widget.isCheckTest),
//         ExportWareHouseFunction.fetchBatch(materialID, widget.token,widget.isCheckTest),
//       ]);
//       if(!mounted) return;
//       setState(() {
//         _isLoadingRawMaterial = false;
//       });
//       if (data.isNotEmpty) {
//         if(data[0] != null) {
//           _dataRawMaterial = data[0] as DataRawMeterial?;
//         }else{
//           _dataRawMaterial = null;
//           ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//               backgroundColor: Colors.black,
//               content: Text(
//                 'Không tìm thấy thông tin pallet NVL!',
//                 style: TextStyle(fontSize: 15.sp, color: Colors.white),
//               ),
//               duration: const Duration(seconds: 1)));
//         }
//         if(data[1] != null){
//           _getBatch = data[1] as DataBatch?;
//         }else{
//           _getBatch = null;
//         }
//       }
//     } on SocketException catch (_) {
//       if (!mounted) return;
//       setState(() {
//         _isLoading = false;
//       });
//       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           backgroundColor: Colors.black,
//           content: Text(
//             'Không có kết nối mạng',
//             style: TextStyle(fontSize: 15.sp, color: Colors.white),
//           ),
//           duration: const Duration(seconds: 1)));
//     } catch (error) {
//       debugPrint(error.toString());
//       if (!mounted) return;
//       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           backgroundColor: Colors.black,
//           content: Text(
//             'Xảy ra lỗi! Vui lòng thử lại sau',
//             style: TextStyle(fontSize: 15.sp, color: Colors.white),
//           ),
//           duration: const Duration(seconds: 1)));
//       setState(() {
//         _isLoading = false;
//         _dataRawMaterial = null;
//       });
//     }
//   }
//   Future<void> _checkQRCode(String materialID,BuildContext context) async{
//     await _getDataRawMaterial(materialID,context);
//     if(_dataRawMaterial != null){
//       if(_dataRawMaterial!.productCode != _dataReservation!.materialCode){
//         if (!mounted) return;
//         setState(() {
//           _dataRawMaterial = null;
//         });
//         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             backgroundColor: Colors.black,
//             content: Text(
//               'Mã NVL không đúng vui lòng quét lại!',
//               style: TextStyle(fontSize: 15.sp, color: Colors.white),
//             ),
//             duration: const Duration(seconds: 1)));
//       }
//     }
//   }
//   Future<void> _cloneList() async {
//     try {
//       await _getReservation();
//       _getLsDataSlocAddress = widget.lsDataSlocAddress.map((e) => DataSlocAddress.clone(e)).toList();
//       if (widget.dataListWarehouseTranfer.riStorageLocation != null && widget.dataListWarehouseTranfer.riStorageLocation != "") {
//         _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == widget.dataListWarehouseTranfer.riStorageLocation);
//         _disableDropdownInput = true;
//       } else {
//         _disableDropdownInput = false;
//       }
//     }on SocketException catch (_) {
//       if (!mounted) return;
//       setState(() {
//         _isNotWifi = true;
//       });
//     } catch (error) {
//       print(error);
//       if (!mounted) return;
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//   Future<void> _setAddressQRCode(DataSlocAddress? data, BuildContext context) async {
//     try {
//       if(!mounted) return;
//       setState(() {
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data!.sloc)]!.defaultStorageBin = data!.defaultStorageBin;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId = data.defaultStorageBinId;
//         _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
//         _isLoadingInventory = true;
//         if(_selectedSloc != null){
//           _errorSelectedSloc = false;
//           _disableDropdown = true;
//         }
//       });
//       PostInventorySAP postInventorySAP = PostInventorySAP(
//           // materialCode: _dataRawMaterial!.productCode,
//           // plant: widget.plant,
//           // sloc: _selectedSloc!.sloc,
//           // storageBin: _selectedSloc!.defaultStorageBinId
//       );
//       final dataInventory = await ExportWareHouseFunction.fetchInventorySAP(
//           postInventorySAP, widget.token);
//       if (!mounted) return;
//       setState(() {
//         _isLoadingInventory = false;
//       });
//       if (dataInventory != null) {
//         _inventorySAPApi = dataInventory;
//       }
//     } catch (error) {
//       // print(error);
//       if(!mounted) return;
//       setState(() {
//         _inventorySAPApi = null;
//         _selectedSloc = null;
//         _isLoadingInventory = false;
//       });
//       debugPrint("error: $error");
//     }
//   }
//
//   Future<void> _setSloc(DataSlocAddress? value, BuildContext context) async {
//     try {
//       if(_isLoadingInventory == true){
//         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             backgroundColor: Colors.black,
//             content: Text(
//               'Vui lòng chờ quá trình lấy tồn kho SAP hoàn thành',
//               style: TextStyle(fontSize: 15.sp, color: Colors.white),
//             ),
//             duration: const Duration(seconds: 1)));
//       }
//       else {
//         if (_dataRawMaterial == null) {
//           ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//               backgroundColor: Colors.black,
//               content: Text(
//                 'Vui lòng quét mã thông tin NVL',
//                 style: TextStyle(fontSize: 15.sp, color: Colors.white),
//               ),
//               duration: const Duration(seconds: 1)));
//         }
//         else {
//           setState(() {
//             _selectedSloc = value!;
//             _isLoadingInventory = true;
//               if (_selectedSloc == null) {
//                 _errorSelectedSloc = true;
//               } else {
//                 _errorSelectedSloc = false;
//             }
//           });
//           PostInventorySAP postInventorySAP = PostInventorySAP(
//               // materialCode: _dataRawMaterial!.productCode,
//               // plant: widget.plant,
//               // sloc: _selectedSloc!.sloc,
//               // storageBin: _selectedSloc!.defaultStorageBinId
//           );
//           final data = await ExportWareHouseFunction.fetchInventorySAP(
//               postInventorySAP, widget.token);
//           if (!mounted) return;
//           setState(() {
//             _isLoadingInventory = false;
//           });
//           if (data != null) {
//             _inventorySAPApi = data;
//           }
//         }
//       }
//     } on SocketException catch (_) {
//       if(!mounted) return;
//       setState(() {
//         _selectedSloc = value!;
//         if (_selectedSloc == null) {
//           _errorSelectedSloc = true;
//         } else {
//           _errorSelectedSloc = false;
//         }
//         _isLoadingInventory = false;
//       });
//     } catch (error) {
//       print(error);
//       if(!mounted) return;
//       setState(() {
//         _selectedSloc = value!;
//         if (_selectedSloc == null) {
//           _errorSelectedSloc = true;
//         } else {
//           _errorSelectedSloc = false;
//         }
//         _isLoadingInventory = false;
//       });
//
//     }
//   }
//   void _setSlocInput(DataSlocAddress? value){
//     setState(() {
//       _selectedSlocInput = value!;
//     });
//   }
//   void _checkError() {
//     setState(() {
//         if (_selectedSloc == null) {
//           _errorSelectedSloc = true;
//         }else{
//           _errorSelectedSloc = false;
//         }
//
//       if(_controllerQuantity.text.isEmpty){
//         _errorQuantity = true;
//       }else{
//         _errorQuantity = false;
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     _controllerQuantity.dispose();
//     _focusQuantity.dispose();
//     _storageBinController.dispose();
//     _focusStorageBin.dispose();
//     _storageBinControllerImport.dispose();
//     _focusStorageBinImport.dispose();
//     super.dispose();
//   }
//
//
//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//         onWillPop: () async {
//           Navigator.pop(context,false);
//           return false;
//         },
//         child:Scaffold(
//           backgroundColor: Colors.grey.shade200,
//           appBar: AppBar(
//             titleSpacing: 0,
//             automaticallyImplyLeading: false,
//             backgroundColor: const Color(0xff0052cc),
//             elevation: 0,
//             centerTitle: true,
//             leading: IconButton(
//               splashColor: Colors.transparent,
//               highlightColor: Colors.transparent,
//               padding: EdgeInsets.zero,
//               constraints: const BoxConstraints(),
//               icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
//               onPressed: () {
//                 Navigator.pop(context,false);
//               },
//             ),
//             title: Text(
//               "Xuất Kho NVL",
//               style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                   fontSize: 14.sp,
//                   color: Colors.white),
//             ),
//           ),
//           body: Container()
//           // _isNotWifi
//           //     ? LostConnect(checkConnect: () => _cloneList())
//           //     : _isLoading == true
//           //     ? const Center(child: CircularProgressIndicator()):
//           // SingleChildScrollView(
//           //   child: Column(
//           //     crossAxisAlignment: CrossAxisAlignment.start,
//           //     mainAxisAlignment: MainAxisAlignment.start,
//           //     children: <Widget>[
//           //       Container(
//           //         padding: EdgeInsets.symmetric(vertical: 10.h),
//           //         decoration: const BoxDecoration(color: Colors.white),
//           //         child: Align(
//           //           alignment: Alignment.center,
//           //           child: Text(
//           //             "CHUYỂN - XUẤT KHO NVL CÓ LỆNH CẤP",
//           //             style: TextStyle(
//           //                 fontSize: 13.sp,
//           //                 fontWeight: FontWeight.bold,
//           //                 color: Colors.grey),
//           //             textAlign: TextAlign.center,
//           //           ),
//           //         ),
//           //       ),
//           //       SizedBox(height: 10.h),
//           //       Container(
//           //         width: double.infinity,
//           //         padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//           //         decoration: const BoxDecoration(color: Colors.white),
//           //         child: Column(
//           //           crossAxisAlignment: CrossAxisAlignment.start,
//           //           mainAxisAlignment: MainAxisAlignment.start,
//           //           children: <Widget> [
//           //             Text(
//           //               "I. LỆNH CẤP VẬT TƯ",
//           //               style: TextStyle(
//           //                 fontSize: 14.sp,
//           //                 fontWeight: FontWeight.bold,
//           //               ),
//           //             ),
//           //             SizedBox(
//           //               height: 15.h,
//           //             ),
//           //             TableInfo(
//           //               textCL1:"Số reservation:",
//           //               textCL2:widget.dataListWarehouseTranfer.reservationNumber ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Item:",
//           //               textCL2:widget.dataListWarehouseTranfer.item ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Mã material:",
//           //               textCL2: _dataReservation != null ?_dataReservation!.materialCode ?? " ": " ",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Nhà máy:",
//           //               textCL2: widget.dataListWarehouseTranfer.plant ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Kho xuất:",
//           //               textCL2: widget.dataListWarehouseTranfer.storageLocation ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Số lô:",
//           //               textCL2:  widget.dataListWarehouseTranfer.batchNumber ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Số lượng yêu cầu:",
//           //               textCL2:  (widget.dataListWarehouseTranfer.quantity != null ? widget.dataListWarehouseTranfer.quantity!.round().toString() : "0") ,
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"ĐVT:",
//           //               textCL2:  widget.dataListWarehouseTranfer.unit.toString() ,
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Nhà máy nhận:",
//           //               textCL2:  widget.dataListWarehouseTranfer.riPlant ?? " ",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"Kho nhận:",
//           //               textCL2:  widget.dataListWarehouseTranfer.riStorageLocation ?? " ",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"SO/ SO Line:",
//           //               textCL2:  widget.dataListWarehouseTranfer.so ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"WBS:",
//           //               textCL2:  widget.dataListWarehouseTranfer.wbs ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             TableInfoNoTop(
//           //               textCL1:"LSX:",
//           //               textCL2:  widget.dataListWarehouseTranfer.lsx ?? "",
//           //               colorCL1: 0xff303F9F,
//           //               colorCL2: 0xffffffff,
//           //             ),
//           //             // RowDetail(
//           //             //     title: "Số reservation:",
//           //             //     trailing: widget.dataListWarehouseTranfer.reservationNumber ?? ""),
//           //             // SizedBox(
//           //             //   height: 10.h,
//           //             // ),
//           //             //  RowDetail(
//           //             //     title: "Item:",
//           //             //     trailing: widget.dataListWarehouseTranfer.item ?? ""),
//           //             // SizedBox(
//           //             //   height: 10.h,
//           //             // ),
//           //             // RowDetail(
//           //             //     title: "Mã material:",
//           //             //     trailing: _dataReservation != null ?_dataReservation!.materialCode ?? " ": " "),
//           //             // SizedBox(
//           //             //   height: 10.h,
//           //             // ),
//           //             // RowDetail(
//           //             //     title: "Nhà máy:",
//           //             //     trailing: widget.dataListWarehouseTranfer.plant ?? ""),
//           //             // SizedBox(
//           //             //   height: 10.h,
//           //             // ),
//           //             // RowDetail(
//           //             //      title: "Kho xuất:",
//           //             //      trailing: widget.dataListWarehouseTranfer.storageLocation ?? ""),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             // RowDetail(
//           //             //      title: "Số lô:",
//           //             //      trailing: widget.dataListWarehouseTranfer.batchNumber ?? ""),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //  RowDetail(
//           //             //      title: "Số lượng yêu cầu:",
//           //             //      trailing: (widget.dataListWarehouseTranfer.quantity != null ? widget.dataListWarehouseTranfer.quantity!.round().toString() : "0") ),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //  RowDetail(
//           //             //      title: "ĐVT:",
//           //             //      trailing: widget.dataListWarehouseTranfer.unit.toString()),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //   RowDetail(
//           //             //      title: "Nhà máy nhận:",
//           //             //      trailing: widget.dataListWarehouseTranfer.riPlant ?? " "),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //  RowDetail(
//           //             //      title: "Kho nhận:",
//           //             //      trailing: widget.dataListWarehouseTranfer.riStorageLocation ?? " "),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //  RowDetail(
//           //             //      title: "SO/ SO Line:",
//           //             //      trailing: widget.dataListWarehouseTranfer.so ?? ""),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             //  RowDetail(
//           //             //      title: "WBS:",
//           //             //      trailing: widget.dataListWarehouseTranfer.wbs ?? ""),
//           //             //  SizedBox(
//           //             //    height: 10.h,
//           //             //  ),
//           //             // RowDetail(
//           //             //      title: "LSX:",
//           //             //      trailing: widget.dataListWarehouseTranfer.lsx ?? ""),
//           //             // SizedBox(
//           //             //   height: 10.h,
//           //             // ),
//           //             // Align(
//           //             //     alignment: Alignment.centerRight,
//           //             //     child: Text(
//           //             //         "Được tạo bởi 20002013 vào lúc 22/08/2022 13:44:06",
//           //             //         style: TextStyle(fontSize: 12.sp, color: Colors.grey),
//           //             //         textAlign: TextAlign.end)),
//           //           ],
//           //         ),
//           //       ),
//           //       SizedBox(height: 10.h),
//           //       Container(
//           //         width: double.infinity,
//           //         padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//           //         decoration: const BoxDecoration(color: Colors.white),
//           //         child: Column(
//           //           crossAxisAlignment: CrossAxisAlignment.start,
//           //           mainAxisAlignment: MainAxisAlignment.start,
//           //           children: <Widget> [
//           //             Text(
//           //               "II. THÔNG TIN GIAO DỊCH CHUYỂN KHO",
//           //               style: TextStyle(
//           //                 fontSize: 14.sp,
//           //                 fontWeight: FontWeight.bold,
//           //               ),
//           //             ),
//           //             Container(
//           //               decoration: const BoxDecoration(),
//           //               child: ElevatedButton.icon(
//           //                 style: ButtonStyle(
//           //                   shape: MaterialStateProperty.all<
//           //                       RoundedRectangleBorder>(
//           //                       RoundedRectangleBorder(
//           //                           borderRadius: BorderRadius.circular(5.r),
//           //                           side: const BorderSide(
//           //                               color: Colors.white))),
//           //                   side: MaterialStateProperty.all(
//           //                     const BorderSide(
//           //                       color: Color(0xff303F9F),
//           //                     ),
//           //                   ),
//           //                   backgroundColor: MaterialStateProperty.all(
//           //                       const Color(0xff303F9F)),
//           //                 ),
//           //                 onPressed: () async {
//           //                   if (_focusQuantity.hasFocus) {
//           //                     FocusScope.of(context).unfocus();
//           //                     await Future<void>.delayed(const Duration(milliseconds: 500));
//           //                     if (_dataRawMaterial == null) {
//           //                       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           //                           backgroundColor: Colors.black,
//           //                           content: Text(
//           //                             'Vui lòng quét mã thông tin NVL',
//           //                             style: TextStyle(fontSize: 15.sp, color: Colors.white),
//           //                           ),
//           //                           duration: const Duration(seconds: 1)));
//           //                     }else {
//           //                       final data = await Navigator.pushNamed(
//           //                           context, '/QRCodePageChooseAnAddress');
//           //                       if (data == null) return;
//           //                       _setAddressQRCode(data as DataSlocAddress?, context);
//           //                     }
//           //                   }else {
//           //                     if (_dataRawMaterial == null) {
//           //                       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           //                           backgroundColor: Colors.black,
//           //                           content: Text(
//           //                             'Vui lòng quét mã thông tin NVL',
//           //                             style: TextStyle(
//           //                                 fontSize: 15.sp, color: Colors.white),
//           //                           ),
//           //                           duration: const Duration(seconds: 1)));
//           //                     } else {
//           //                       final data = await Navigator.pushNamed(
//           //                           context, '/QRCodePageChooseAnAddress');
//           //                       if (data == null) return;
//           //                       _setAddressQRCode(data as DataSlocAddress?, context);
//           //                     }
//           //                   }
//           //                 },
//           //                 icon: Icon(Icons.camera_alt_outlined,
//           //                     size: 25.sp, color: Colors.white),
//           //                 label: Text(
//           //                   "Quét mã",
//           //                   style: TextStyle(
//           //                       color: Colors.white,
//           //                       fontWeight: FontWeight.bold,
//           //                       fontSize: 13.sp),
//           //                 ),
//           //               ),
//           //             ),
//           //             const RowDetail(
//           //                 title: "Loại giao dịch:",
//           //                 trailing:  "Chuyển kho 2 bước: Chuyển xuất"),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             const HeaderTableImExportWH(
//           //                 textCL1: "Kho xuất",
//           //                 textCL2: "Kho nhập",
//           //                 colorCL2: 0xff303F9F,
//           //                 colorCL1: 0xff303F9F),
//           //             TableImExportWHDD(
//           //               textCL1: "Sloc",
//           //               selectedSloc:  _selectedSloc,
//           //               onChange:(value) => _setSloc(value,context),
//           //               disableDropdown:_disableDropdown,
//           //               disableDropdownInput:_disableDropdownInput,
//           //               lsDataSlocAddress:_getLsDataSlocAddress,
//           //               textCL3: "Sloc",
//           //               selectedSlocInput:  _selectedSlocInput,
//           //               onChangeInput:(value) => _setSlocInput(value),
//           //               colorCL1: 0xffEEEEEE,
//           //               colorCL2: 0xffFFFFFF,
//           //               colorCL3: 0xffEEEEEE,
//           //               colorCL4: 0xffFFFFFF,
//           //             ),
//           //             TableImExportWHText(
//           //               textCL1: "Warehouse No",
//           //               textCL2: _selectedSloc != null ?_selectedSloc!.warehouseNo ?? "": "",
//           //               textCL3: "Warehouse No",
//           //               textCL4: _selectedSlocInput != null ?_selectedSlocInput!.warehouseNo ?? "": "",
//           //               colorCL1: 0xffEEEEEE,
//           //               colorCL2: 0xffFFFFFF,
//           //               colorCL3: 0xffEEEEEE,
//           //               colorCL4: 0xffFFFFFF,
//           //             ),
//           //             // TableImExportWHText(
//           //             //   textCL1: "Storage Bin",
//           //             //   textCL2: _selectedSloc != null ?_selectedSloc!.defaultStorageBin ?? "": "",
//           //             //   textCL3: "Storage Bin",
//           //             //   textCL4: _selectedSlocInput != null ?_selectedSlocInput!.defaultStorageBin ?? "": "",
//           //             //   colorCL1: 0xffEEEEEE,
//           //             //   colorCL2: 0xffFFFFFF,
//           //             //   colorCL3: 0xffEEEEEE,
//           //             //   colorCL4: 0xffFFFFFF,
//           //             // ),
//           //             IntrinsicHeight(
//           //               child: Row(
//           //                 children: <Widget> [
//           //                   Expanded(
//           //                     flex: 3,
//           //                     child: Container(
//           //                       height: double.infinity,
//           //                       padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//           //                       decoration: BoxDecoration(
//           //                           color: const Color(0xffEEEEEE),
//           //                           border: Border(
//           //                             left: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                             right: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                             bottom: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                           )
//           //                       ),
//           //                       child: Text(
//           //                         "Storage Bin", style: TextStyle(fontSize: 12.sp),
//           //                       ),
//           //                     ),
//           //                   ),
//           //                   Expanded(
//           //                     flex: 2,
//           //                     child: Container(
//           //                       height: double.infinity,
//           //                       padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//           //                       decoration: BoxDecoration(
//           //                           color: const Color(0xFFFFFFFF),
//           //                           border: Border(
//           //                             right: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                             bottom: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                           )
//           //                       ),
//           //                       child: TypeAheadField(
//           //                         suggestionsBoxDecoration: SuggestionsBoxDecoration(
//           //                           offsetX: -45.w,
//           //                           constraints: BoxConstraints(
//           //                             minWidth: 150.w,
//           //                           ),
//           //                         ),
//           //                         textFieldConfiguration: TextFieldConfiguration(
//           //                             decoration:  InputDecoration(
//           //                               labelStyle: TextStyle(fontSize: 11.sp),
//           //                               contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
//           //                               isDense: true,
//           //                               border: OutlineInputBorder(
//           //                                 borderRadius: BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                               focusedBorder: OutlineInputBorder(
//           //                                 borderRadius: BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                               enabledBorder: OutlineInputBorder(
//           //                                 borderRadius:
//           //                                 BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                             ),
//           //                             enabled: _selectedSloc != null,
//           //                             controller: _storageBinController,
//           //                             focusNode: _focusStorageBin,
//           //                             style: TextStyle(fontSize: 12.sp)
//           //                         ),
//           //                         suggestionsCallback: (pattern) {
//           //                           return ExportWareHouseFunction.filterDataSlocAddress(_getLsDataSlocAddress, pattern);
//           //                         },
//           //                         itemBuilder: (context, suggestion) {
//           //                           return ListTile(
//           //                             title: Text((suggestion as DataSlocAddress).defaultStorageBin?? " ",style: TextStyle(fontSize: 12.sp)),
//           //                           );
//           //                         },
//           //                         onSuggestionSelected: (suggestion) {
//           //                           _storageBinController.text = (suggestion as DataSlocAddress).defaultStorageBin ?? " ";
//           //                           _defaultStorageBinIDExport = (suggestion).defaultStorageBinId ?? " ";
//           //                         },
//           //                       ),
//           //                     ),
//           //                   ),
//           //                   Expanded(
//           //                     flex: 3,
//           //                     child: Container(
//           //                       height: double.infinity,
//           //                       padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//           //                       decoration: BoxDecoration(
//           //                           color: const Color(0xffEEEEEE),
//           //                           border: Border(
//           //                             right: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                             bottom: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                           )
//           //                       ),
//           //                       child: Text(
//           //                         "Storage Bin", style: TextStyle(fontSize: 12.sp),
//           //                       ),
//           //                     ),
//           //                   ),
//           //                   Expanded(
//           //                     flex: 2,
//           //                     child: Container(
//           //                       height: double.infinity,
//           //                       padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//           //                       decoration: BoxDecoration(
//           //                           color: const Color(0xFFFFFFFF),
//           //                           border: Border(
//           //                             right: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                             bottom: BorderSide(
//           //                               color: Colors.black,
//           //                               width: 0.5.w,
//           //                             ),
//           //                           )
//           //                       ),
//           //                       child: TypeAheadField(
//           //                         suggestionsBoxDecoration: SuggestionsBoxDecoration(
//           //                           offsetX: -85.w,
//           //                           constraints: BoxConstraints(
//           //                             minWidth: 150.w,
//           //                           ),
//           //                         ),
//           //                         textFieldConfiguration: TextFieldConfiguration(
//           //                             decoration:  InputDecoration(
//           //                               labelStyle: TextStyle(fontSize: 11.sp),
//           //                               contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
//           //                               isDense: true,
//           //                               border: OutlineInputBorder(
//           //                                 borderRadius: BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                               focusedBorder: OutlineInputBorder(
//           //                                 borderRadius: BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                               enabledBorder: OutlineInputBorder(
//           //                                 borderRadius:
//           //                                 BorderRadius.circular(3.r),
//           //                                 borderSide: BorderSide(
//           //                                     width: 0.5,
//           //                                     color: Colors.grey.shade400),
//           //                               ),
//           //                             ),
//           //                             enabled: _selectedSlocInput != null,
//           //                             controller: _storageBinControllerImport,
//           //                             focusNode: _focusStorageBinImport,
//           //                             style: TextStyle(fontSize: 12.sp)
//           //                         ),
//           //                         suggestionsCallback: (pattern) {
//           //                           return ExportWareHouseFunction.filterDataSlocAddress(_getLsDataSlocAddress, pattern);
//           //                         },
//           //                         itemBuilder: (context, suggestion) {
//           //                           return ListTile(
//           //                             title: Text((suggestion as DataSlocAddress).defaultStorageBin?? " ",style: TextStyle(fontSize: 12.sp)),
//           //                           );
//           //                         },
//           //                         onSuggestionSelected: (suggestion) {
//           //                           _storageBinControllerImport.text = (suggestion as DataSlocAddress).defaultStorageBin ?? " ";
//           //                           _defaultStorageBinIDImport = (suggestion).defaultStorageBinId ?? " ";
//           //                         },
//           //                       ),
//           //                     ),
//           //                   ),
//           //                 ],
//           //               ),
//           //             ),
//           //             // Align(
//           //             //     alignment: Alignment.centerRight,
//           //             //     child: Text(
//           //             //         "Được tạo bởi 20002013 vào lúc 22/08/2022 13:44:06",
//           //             //         style: TextStyle(fontSize: 12.sp, color: Colors.grey),
//           //             //         textAlign: TextAlign.end)),
//           //             SizedBox(height:  _errorSelectedSloc == true  ? 10.h : 0),
//           //             ContainerError.widgetError(_errorSelectedSloc, 'Kho xuất chưa được nhập')
//           //           ],
//           //         ),
//           //       ),
//           //
//           //       SizedBox(height: 10.h),
//           //       Container(
//           //         width: double.infinity,
//           //         padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//           //         decoration: const BoxDecoration(color: Colors.white),
//           //         child: Column(
//           //           crossAxisAlignment: CrossAxisAlignment.start,
//           //           mainAxisAlignment: MainAxisAlignment.start,
//           //           children: <Widget> [
//           //
//           //             Container(
//           //               decoration: const BoxDecoration(),
//           //               child: ElevatedButton.icon(
//           //                 style: ButtonStyle(
//           //                   shape: MaterialStateProperty.all<
//           //                       RoundedRectangleBorder>(
//           //                       RoundedRectangleBorder(
//           //                           borderRadius: BorderRadius.circular(5.r),
//           //                           side: const BorderSide(
//           //                               color: Colors.white))),
//           //                   side: MaterialStateProperty.all(
//           //                     const BorderSide(
//           //                       color: Color(0xff0052cc),
//           //                     ),
//           //                   ),
//           //                   backgroundColor: MaterialStateProperty.all(
//           //                       const Color(0xff0052cc)),
//           //                 ),
//           //                 onPressed: () async {
//           //                   if (_focusQuantity.hasFocus) {
//           //                     FocusScope.of(context).unfocus();
//           //                     await Future<void>.delayed(const Duration(milliseconds: 500));
//           //                     final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
//           //                     if (data == null) return;
//           //                     if((data as GetBackDataQRCodePage).isScan == true) {
//           //                       if(!mounted) return;
//           //                       await _checkQRCode(data.materialID.toString(),context);
//           //                     }
//           //                   }else{
//           //                     final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
//           //                     if (data == null) return;
//           //                     if((data as GetBackDataQRCodePage).isScan == true) {
//           //                       if(!mounted) return;
//           //                       await _checkQRCode(data.materialID.toString(),context);
//           //                     }
//           //                   }
//           //                 },
//           //                 icon: Icon(Icons.camera_alt_outlined,
//           //                     size: 25.sp, color: Colors.white),
//           //                 label: Text(
//           //                   "Quét mã",
//           //                   style: TextStyle(
//           //                       color: Colors.white,
//           //                       fontWeight: FontWeight.bold,
//           //                       fontSize: 13.sp),
//           //                 ),
//           //               ),
//           //             ),
//           //             SizedBox(height: 10.h),
//           //             Text(
//           //               "III. THÔNG TIN NVL CHUYỂN KHO",
//           //               style: TextStyle(
//           //                 fontSize: 14.sp,
//           //                 fontWeight: FontWeight.bold,
//           //               ),
//           //             ),
//           //             SizedBox(
//           //               height: 15.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Tên NCC:",
//           //                 trailing: _isLoadingRawMaterial == true ? '...Loading' :_dataRawMaterial == null ? " ":_dataRawMaterial!.vendorName ?? " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             Row(
//           //               crossAxisAlignment: CrossAxisAlignment.start,
//           //               mainAxisAlignment:
//           //               MainAxisAlignment.spaceBetween,
//           //               children: <Widget>[
//           //                 Expanded(
//           //                   flex: 3,
//           //                   child: Align(
//           //                     alignment: Alignment.centerLeft,
//           //                     child: Text(
//           //                       "Số PO:",
//           //                       style: TextStyle(
//           //                         fontSize: 12.sp,
//           //                         fontWeight: FontWeight.bold,
//           //                       ),
//           //                     ),
//           //                   ),
//           //                 ),
//           //                 SizedBox(width: 10.w),
//           //                 Expanded(
//           //                     flex: 7,
//           //                     child: Align(
//           //                       alignment: Alignment.centerLeft,
//           //                       child: _isLoadingRawMaterial == true ?Text(
//           //                         "...Loading",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ):_dataRawMaterial == null ? Text(
//           //                         " ",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ):Column(
//           //                         crossAxisAlignment:
//           //                         CrossAxisAlignment.start,
//           //                         mainAxisAlignment:
//           //                         MainAxisAlignment.start,
//           //                         children: List.generate(
//           //                           (_dataRawMaterial!.poDetails ??
//           //                               _lsNullPoDetails)
//           //                               .length,
//           //                               (index) => Text(
//           //                             (_dataRawMaterial!.poDetails ??
//           //                                 _lsNullPoDetails)[
//           //                             index]
//           //                                 .item ??
//           //                                 " ",
//           //                             style: TextStyle(
//           //                               fontSize: 12.sp,
//           //                             ),
//           //                           ),
//           //                         ),
//           //                       ),
//           //                     ))
//           //               ],
//           //             ),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Mã hàng (mã SAP):",
//           //                 trailing: _isLoadingRawMaterial == true ? "...Loading" :_dataRawMaterial == null ? " " :_dataRawMaterial!.productCode ?? " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Tên hàng:",
//           //                 trailing: _isLoadingRawMaterial == true ? "...Loading" :_dataRawMaterial == null ? " " : _dataRawMaterial!.productName ?? " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             Row(
//           //               crossAxisAlignment: CrossAxisAlignment.start,
//           //               mainAxisAlignment:
//           //               MainAxisAlignment.spaceBetween,
//           //               children: <Widget>[
//           //                 Expanded(
//           //                   flex: 3,
//           //                   child: Align(
//           //                     alignment: Alignment.centerLeft,
//           //                     child: Text(
//           //                       "SO/SO Line:",
//           //                       style: TextStyle(
//           //                         fontSize: 12.sp,
//           //                         fontWeight: FontWeight.bold,
//           //                       ),
//           //                     ),
//           //                   ),
//           //                 ),
//           //                 SizedBox(width: 10.w),
//           //                 Expanded(
//           //                     flex: 7,
//           //                     child: Align(
//           //                       alignment: Alignment.centerLeft,
//           //                       child:  _isLoadingRawMaterial == true ?Text(
//           //                         "...Loading",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ):_dataRawMaterial == null ?Text(
//           //                         " ",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ) :Column(
//           //                         crossAxisAlignment:
//           //                         CrossAxisAlignment.start,
//           //                         mainAxisAlignment:
//           //                         MainAxisAlignment.start,
//           //                         children: List.generate(
//           //                           (_dataRawMaterial!.soDetails ??
//           //                               _lsNullSoDetail)
//           //                               .length,
//           //                               (index) => Text(
//           //                             (_dataRawMaterial!.soDetails ??
//           //                                 _lsNullSoDetail)[
//           //                             index]
//           //                                 .item ??
//           //                                 " ",
//           //                             style: TextStyle(
//           //                               fontSize: 12.sp,
//           //                             ),
//           //                           ),
//           //                         ),
//           //                       ),
//           //                     ))
//           //               ],
//           //             ),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             Row(
//           //               crossAxisAlignment: CrossAxisAlignment.start,
//           //               mainAxisAlignment:
//           //               MainAxisAlignment.spaceBetween,
//           //               children: <Widget>[
//           //                 Expanded(
//           //                   flex: 3,
//           //                   child: Align(
//           //                     alignment: Alignment.centerLeft,
//           //                     child: Text(
//           //                       "WBS:",
//           //                       style: TextStyle(
//           //                         fontSize: 12.sp,
//           //                         fontWeight: FontWeight.bold,
//           //                       ),
//           //                     ),
//           //                   ),
//           //                 ),
//           //                 SizedBox(width: 10.w),
//           //                 Expanded(
//           //                     flex: 7,
//           //                     child: Align(
//           //                       alignment: Alignment.centerLeft,
//           //                       child: _isLoadingRawMaterial == true ?Text(
//           //                         "...Loading",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ):_dataRawMaterial == null ? Text(
//           //                         " ",
//           //                         style: TextStyle(
//           //                           fontSize: 12.sp,
//           //                         ),
//           //                       ): Column(
//           //                         crossAxisAlignment:
//           //                         CrossAxisAlignment.start,
//           //                         mainAxisAlignment:
//           //                         MainAxisAlignment.start,
//           //                         children: List.generate(
//           //                           (_dataRawMaterial!.wbsDetails ??
//           //                               _lsNullWbsDetails)
//           //                               .length,
//           //                               (index) => Text(
//           //                             (_dataRawMaterial!.wbsDetails ??
//           //                                 _lsNullWbsDetails)[
//           //                             index]
//           //                                 .item ??
//           //                                 " ",
//           //                             style: TextStyle(
//           //                               fontSize: 12.sp,
//           //                             ),
//           //                           ),
//           //                         ),
//           //                       ),
//           //                     ))
//           //               ],
//           //             ),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Số lô:",
//           //                 trailing: _getBatch != null  ? _getBatch!.batchNumber ?? " ": " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Ngày sản xuất:",
//           //                 trailing: _isLoadingRawMaterial == true ? "...Loading":_dataRawMaterial == null ? " " :_dataRawMaterial!.manufacturingDateStr ?? " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "Tồn kho SAP:",
//           //                 trailing: _isLoadingInventory == true  ? "Loading":_inventorySAPApi != null ?_inventorySAPApi!.quantity != null ?_inventorySAPApi!.quantity!.round().toString(): " ": " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             Row(
//           //               children: [
//           //                 Expanded(
//           //                   flex: 3,
//           //                   child: Text(
//           //                     "Số lượng thực chuyển:",
//           //                     style: TextStyle(
//           //                       fontSize: 12.sp,
//           //                       fontWeight: FontWeight.bold,
//           //                     ),
//           //                   ),
//           //                 ),
//           //                 SizedBox(width: 10.w),
//           //                 Expanded(
//           //                   flex: 7,
//           //                   child: SizedBox(
//           //                     height: 33.h,
//           //                     child: TextFormField(
//           //                       textAlign: TextAlign.center,
//           //                       keyboardType:
//           //                       TextInputType.number,
//           //                       inputFormatters: <
//           //                           TextInputFormatter>[
//           //                         FilteringTextInputFormatter
//           //                             .allow(
//           //                             RegExp("[0-9|]")),
//           //                       ],
//           //                       focusNode: _focusQuantity,
//           //                       controller: _controllerQuantity,
//           //                       style: TextStyle(fontSize: 12.sp),
//           //                       decoration: InputDecoration(
//           //                         border: InputBorder.none,
//           //                         focusedBorder: OutlineInputBorder(
//           //                           borderRadius: BorderRadius.circular(0),
//           //                           borderSide: BorderSide(
//           //                               width: 0.5.w,
//           //                               color:
//           //                               // _error_3 ==
//           //                               //     true
//           //                               //     ? const Color(
//           //                               //     0xFFD32F2F)
//           //                               //     :
//           //                               Colors.grey.shade400),
//           //                         ),
//           //                         enabledBorder: OutlineInputBorder(
//           //                           borderRadius: BorderRadius.circular(0),
//           //                           borderSide: BorderSide(
//           //                               width: 0.5,
//           //                               color:
//           //                               // _error_3 ==
//           //                               //     true
//           //                               //     ? const Color(
//           //                               //     0xFFD32F2F)
//           //                               //     :
//           //                               Colors.grey.shade400),
//           //                         ),
//           //                         errorBorder: InputBorder.none,
//           //                         disabledBorder: InputBorder.none,
//           //                         filled: true,
//           //                         fillColor: Colors.white,
//           //                         hintStyle: TextStyle(fontSize: 12.sp),
//           //                         contentPadding:
//           //                         EdgeInsets.symmetric(horizontal: 20.w),
//           //                       ),
//           //                       onChanged: (value){
//           //                         if(_controllerQuantity.text.isEmpty){
//           //                           if(_errorQuantity != true){
//           //                             setState(() {
//           //                               _errorQuantity = true;
//           //                             });
//           //                           }
//           //                         }else{
//           //                           if(_errorQuantity != false){
//           //                             setState(() {
//           //                               _errorQuantity = false;
//           //                             });
//           //                           }
//           //
//           //                         }
//           //                       },
//           //                     ),
//           //                   ),
//           //                 ),
//           //               ],
//           //             ),
//           //             SizedBox(height: _errorQuantity == true ? 10.h : 0),
//           //             ContainerError.widgetError(_errorQuantity, 'Bạn chưa nhập số lượng thực chuyển'),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //             RowDetail(
//           //                 title: "ĐVT:",
//           //                 trailing: _isLoadingRawMaterial == true ? "...Loading" :_dataRawMaterial == null ? " " :_dataRawMaterial!.poQuantityUnit ?? " "),
//           //             SizedBox(
//           //               height: 10.h,
//           //             ),
//           //           ],
//           //         ),
//           //       ),
//           //       SizedBox(
//           //         height: 20.h,
//           //       ),
//           //       Align(
//           //         alignment: Alignment.centerRight,
//           //         child: Container(
//           //           padding: EdgeInsets.symmetric(horizontal: 10.w),
//           //           width: double.infinity,
//           //           decoration: const BoxDecoration(),
//           //           child: ElevatedButton(
//           //             style: ButtonStyle(
//           //               shape: MaterialStateProperty.all<
//           //                   RoundedRectangleBorder>(
//           //                   RoundedRectangleBorder(
//           //                       borderRadius: BorderRadius.circular(5.r),
//           //                       side: const BorderSide(color: Colors.white))),
//           //               side: MaterialStateProperty.all(
//           //                 const BorderSide(
//           //                   color: Color(0xff303F9F),
//           //                 ),
//           //               ),
//           //               backgroundColor: MaterialStateProperty.all(
//           //                   const Color(0xff303F9F)),
//           //             ),
//           //             onPressed: () {
//           //               if(_dataRawMaterial == null) {
//           //                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           //                     backgroundColor: Colors.black,
//           //                     content: Text(
//           //                       'Vui lòng quét mã để lấy thông tin NVL chuyển kho',
//           //                       style: TextStyle(
//           //                           fontSize: 15.sp, color: Colors.white),
//           //                     ),
//           //                     duration: const Duration(seconds: 2)));
//           //               }else {
//           //                 if (_isLoadingRawMaterial == true) {
//           //                   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           //                       backgroundColor: Colors.black,
//           //                       content: Text(
//           //                         'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
//           //                         style: TextStyle(
//           //                             fontSize: 15.sp, color: Colors.white),
//           //                       ),
//           //                       duration: const Duration(seconds: 2)));
//           //                 } else {
//           //                   _checkError();
//           //                   if (_errorSelectedSloc == false && _errorQuantity == false) {
//           //                     if (_isLoadingInventory != true) {
//           //                       if (_focusQuantity.hasFocus) {
//           //                         FocusScope.of(context).unfocus();
//           //                         WarehouseTranfer warehouseTranfer = WarehouseTranfer(reservationId: widget.dataListWarehouseTranfer.reservationId,
//           //                             slocExportId: _selectedSloc!.slocId,
//           //                             storageBinExportId: _selectedSloc!.defaultStorageBinId,
//           //                             slocImportId: _selectedSlocInput!.slocId,
//           //                             storageBinImportId: _selectedSlocInput!.defaultStorageBinId,
//           //                             quantity: int.parse(_controllerQuantity.text),
//           //                             unit: widget.dataListWarehouseTranfer.unit.toString(),
//           //                             rawMaterialCardId: _materialID
//           //                         );
//           //                         ExportWareHouseFunction.sendWarehouseFuncion(warehouseTranfer, widget.token, context, widget.plant);
//           //                       } else {
//           //                         WarehouseTranfer warehouseTranfer = WarehouseTranfer(reservationId: widget.dataListWarehouseTranfer.reservationId,
//           //                             slocExportId: _selectedSloc!.slocId,
//           //                             storageBinExportId: _selectedSloc!.defaultStorageBinId,
//           //                             slocImportId: _selectedSlocInput!.slocId,
//           //                             storageBinImportId: _selectedSlocInput!.defaultStorageBinId,
//           //                             quantity: int.parse(_controllerQuantity.text),
//           //                             unit: widget.dataListWarehouseTranfer.unit.toString(),
//           //                             rawMaterialCardId: _materialID
//           //                         );
//           //                         ExportWareHouseFunction.sendWarehouseFuncion(
//           //                             warehouseTranfer, widget.token, context,
//           //                             widget.plant);
//           //                       }
//           //                     } else {
//           //                       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           //                           backgroundColor: Colors.black,
//           //                           content: Text(
//           //                             'Vui lòng chờ quá trình lấy số tồn kho SAP hoàn thành',
//           //                             style: TextStyle(
//           //                                 fontSize: 15.sp, color: Colors.white),
//           //                           ),
//           //                           duration: const Duration(seconds: 2)));
//           //                     }
//           //                   }
//           //                 }
//           //               }
//           //             },
//           //             child: Container(
//           //               margin: EdgeInsets.symmetric(
//           //                   vertical: 12.h, horizontal: 15.w),
//           //               child: Text(
//           //                 "Chuyển kho",
//           //                 style: TextStyle(
//           //                     color: Colors.white,
//           //                     fontWeight: FontWeight.bold,
//           //                     fontSize: 13.sp),
//           //               ),
//           //             ),
//           //           ),
//           //         ),
//           //       ),
//           //       SizedBox(
//           //         height: 20.h,
//           //       ),
//           //     ],
//           //   ),
//           // ),
//         ));
//   }
// }
