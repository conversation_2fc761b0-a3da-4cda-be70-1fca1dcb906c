import 'dart:io';

import 'package:flutter/material.dart';
import 'package:ttf/model/maintenanceOrderModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/page/ExportWarehouse2.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoDauVao2.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoDauVaoDetail2.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQAQCNghiemThuDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQC5S.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQC5SDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCGiaCong.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCGiaCongDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCHienTruong.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCHienTruongDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCMau.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCMauDauChuyen.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCMauDauChuyenDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCMauDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCSanPham.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCSanPhamDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCTestLab.dart';
import 'package:ttf/page/KiemTraChatLuong/BaoCaoQCTestLabDetail.dart';
import 'package:ttf/page/KiemTraChatLuong/KiemTraChatLuong.dart';
import 'package:ttf/page/KiemTraChatLuong/QCPassedStampScan.dart';
import 'package:ttf/page/ListTranferMaterial2.dart';
import 'package:ttf/page/MaiDao/MaiDaoDetail.dart';
import 'package:ttf/page/MaiDao/MaiDaoList.dart';
import 'package:ttf/page/NotificationPage.dart';
import 'package:ttf/page/ProductManagement.dart';
import 'package:ttf/screenArguments/CommonScreenPermissionArgument.dart';
import 'package:upgrader/upgrader.dart';
import '../page/AddTranferWarehouse.dart';
import '../page/BottomNavigatorBar.dart';
import '../page/CreateNewTraHangNCC.dart';
import '../page/DetailReservation.dart';
import '../page/ExportWarehouse.dart';
import '../page/ImportWarehouseSAP.dart';
import '../page/InventoryManagement.dart';
import '../page/InventoryMaterialSloc.dart';
import '../page/KiemTraChatLuong/BaoCaoDauVao.dart';
import '../page/KiemTraChatLuong/BaoCaoDauVaoDetail.dart';
import '../page/KiemTraChatLuong/BaoCaoQAQCNghiemThu.dart';
import '../page/KiemTraChatLuong/PhieuKCSCongDoan.dart';
import '../page/KiemTraChatLuong/PhieuKCSCongDoanDetail.dart';
import '../page/ListQCNVL.dart';
import '../page/ListTranferMaterial.dart';
import '../page/MaterialRetail.dart';
import '../page/MaterialUnused.dart';
import '../page/QRCodeInventoryMaterialSloc.dart';
import '../page/QRCodePageChooseAnAddress.dart';
import '../page/QRCodePageGetSlocExport.dart';
import '../page/QRCodePageListQC.dart';
import '../page/QRCodePageListQC2.dart';
import '../page/QRcodePageGetWorkShopDepartment.dart';
import '../page/QRcodePageTranferMaterial.dart';
import '../page/QrCodePageGetSlocAddTranferWareHouse.dart';
import '../page/InforSwitchingStages.dart';
import '../page/DetailReport.dart';
import '../page/ListQC.dart';
import '../page/QRcodePage.dart';
import '../page/CompleteTheBigStage.dart';
import '../page/Info.dart';
import '../page/Login.dart';
import '../page/Mainpage.dart';
import '../page/SplashScreen.dart';
import '../page/StatisticsMaterials.dart';
import '../page/SwitchingStages.dart';
import '../page/TraHangNCC.dart';
import '../screenArguments/CommonScreenUserArgument.dart';
import '../screenArguments/ScreenArgumentInventMSloc.dart';
import '../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../screenArguments/ScreenArgumentsTraHangNCC.dart';
import '../screenArguments/screenArgumentQRCodePageListQC.dart';
import '../screenArguments/screenArgumentAddTranferWareHouse.dart';
import '../screenArguments/screenArgumentCompleteTheBigStage.dart';
import '../screenArguments/screenArgumentDetailReport.dart';
import '../screenArguments/screenArgumentDetailReservationImport.dart';
import '../screenArguments/screenArgumentExportWareHouse.dart';
import '../screenArguments/screenArgumentImportWareHouse.dart';
import '../screenArguments/screenArgumentInfor.dart';
import '../screenArguments/screenArgumentInforSwitchingStages.dart';
import '../screenArguments/screenArgumentInventoryManagement.dart';
import '../screenArguments/screenArgumentListQC.dart';
import '../screenArguments/screenArgumentListTranferMaterial.dart';
import '../screenArguments/screenArgumentMainPage.dart';
import '../screenArguments/screenArgumentMaterialRetail.dart';
import '../screenArguments/screenArgumentMaterialUnused.dart';
import '../screenArguments/screenArgumentQRPage.dart';
import '../screenArguments/screenArgumentStatisticsMaterials.dart';
import '../screenArguments/screenArgumentSwitchingStage.dart';
import '../screenArguments/screenArgumentsCreateNewTraHangNCC.dart';
import '../page/Downtime/DowntimeList.dart';
import '../page/Downtime/DowntimeDetail.dart';
import '../page/MaintenanceOrder/MaintenanceOrderList.dart';
import '../page/MaintenanceOrder/MaintenanceOrderDetail.dart';
import '../page/TyLeTieuHao/TyLeTieuHaoList.dart';
import '../page/TyLeTieuHao/TyLeTieuHaoDetail.dart';
import '../page/ImportProduct.dart';
import 'package:ttf/model/maiDaoModel.dart';

Widget withUpgradeAlert(Widget child) {
  return UpgradeAlert(
    upgrader: Upgrader(
      // debugDisplayAlways: true, // always display even if version is newest for debug
      canDismissDialog: true,
      durationUntilAlertAgain: const Duration(seconds: 3), // test
      // durationUntilAlertAgain: const Duration(seconds: 30),
      dialogStyle: Platform.isIOS ? UpgradeDialogStyle.cupertino : UpgradeDialogStyle.material,
      countryCode: 'VN',
      languageCode: 'vi',
      shouldPopScope: () => true,
      messages: UpgraderMessages(code: 'vi'), // force Vietnamese
    ),
    child: child,
  );
}

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;
    switch (settings.name) {
      case '/':
        return MaterialPageRoute(settings: const RouteSettings(name: '/'), builder: (_) => const SplashScreen());
      case '/Login':
        return MaterialPageRoute(settings: const RouteSettings(name: '/Login'), builder: (_) => withUpgradeAlert(const Login()));
      case '/mainPage':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/mainPage'),
            builder: (_) {
              ScreenArgumentMainPage argument = args as ScreenArgumentMainPage;
              return withUpgradeAlert(MainPage(getSaveUser: argument.getSaveUser));
            });
      // case '/notification':
      //   return MaterialPageRoute(
      //       settings: const RouteSettings(name: '/notification'),
      //       builder: (_) {
      //         CommonScreenPermissionArgument argument = args as CommonScreenPermissionArgument;
      //         return NotificationPage(
      //           permission: argument.permission,
      //           token: argument.token,
      //           plant: argument.plant,
      //           dateTimeOld: argument.dateTimeOld,
      //           accountId: argument.accountId,
      //         );
      //       });

      case '/info':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/info'),
            builder: (_) {
              ScreenArgumentInfor argument = args as ScreenArgumentInfor;
              return Infor(
                barcode: argument.barcode,
                token: argument.token,
                dateTimeOld: argument.dateTmeOld,
              );
            });

      case '/QCPassedStampScan':
        // return MaterialPageRoute(
        //     settings: const RouteSettings(name: '/QCPassedStampScan'),
        //     builder: (_) {
        //       ScreenArgumentInfor argument = args as ScreenArgumentInfor;
        //       return QCPassedStampScan(barcode: argument.barcode, token: argument.token, dateTimeOld: argument.dateTmeOld);
        //     });

        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QCPassedStampScan'),
            builder: (_) {
              ScreenArgumentInfor argument = args as ScreenArgumentInfor;
              return QCPassedStampScan(
                barcode: argument.barcode,
                token: argument.token,
                dateTimeOld: argument.dateTmeOld,
              );
            });

      case '/DetailReport':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/DetailReport'),
            builder: (_) {
              ScreenArgumentDetailReport argument = args as ScreenArgumentDetailReport;
              return DetailReport(getData: argument.getData, token: argument.token, dateTimeOld: argument.dateTimeOld);
            });
      case '/QRCodePage':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePage'),
            builder: (_) {
              ScreenArgumentQRPage argument = args as ScreenArgumentQRPage;
              return QrCodePage(
                fromPage: argument.fromPage,
                token: argument.token,
                permission: argument.permission,
                plant: argument.plant,
                dateTimeOld: argument.dateTimeOld,
              );
            });
      case '/SwitchingStage':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/SwitchingStage'),
            builder: (_) {
              ScreenArgumentSwitchingStage argument = args as ScreenArgumentSwitchingStage;
              return SwitchingStage(
                  dataSwitchingStages: argument.switchingStateData,
                  token: argument.token,
                  switchingStages: argument.switchingStages,
                  dateTimeOld: argument.dateTimeOld,
                  toBardCode: argument.toBarcode);
            });
      // case '/InfoSwitchingStages':
      //   return MaterialPageRoute(
      //       settings: const RouteSettings(
      //           name: '/InfoSwitchingStages'),
      //       builder: (_) {
      //         ScreenArgumentInfoSwitchingStages argument = args as ScreenArgumentInfoSwitchingStages;
      //         return InfoSwitchingStages(
      //             barcode:argument.barcode,
      //             token:argument.token,
      //             dateTimeOld: argument.dateTimeOld
      //         );
      //       });
      case '/CompleteTheBigStage':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/CompleteTheBigStage'),
            builder: (_) {
              ScreenArgumentCompleteTheBigStage argument = args as ScreenArgumentCompleteTheBigStage;
              return CompleteTheBigStage(
                  getData: argument.getData, token: argument.token, confirmWorkCenter: argument.confirmWorkCenter, dateTimeOld: argument.dateTimeOld);
            });
      case '/ListQC':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ListQC'),
            builder: (_) {
              ScreenArgumentListQC argument = args as ScreenArgumentListQC;
              return ListQC(dateTimeOld: argument.dateTimeOld, user: argument.userModel);
            });
      case '/ListQCNVL':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ListQCNVL'),
            builder: (_) {
              ScreenArgumentListQC argument = args as ScreenArgumentListQC;
              return ListQCNVL(dateTimeOld: argument.dateTimeOld, userModel: argument.userModel);
            });
      case '/QRcodePageGetWorkShopDepartment':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRcodePageGetWorkShopDepartment'),
            builder: (_) {
              return const QRcodePageGetWorkShopDepartment();
            });
      case '/InventoryManagement':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/InventoryManagement'),
            builder: (_) {
              ScreenArgumentInventoryManagement argument = args as ScreenArgumentInventoryManagement;
              return InventoryManagement(
                  permission: argument.permission,
                  token: argument.token,
                  plant: argument.plant,
                  dateTimeOld: argument.dateTimeOld,
                  accountId: argument.accountId);
            });
      case '/ImportWarehouseSAP':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ImportWarehouse'),
            builder: (_) {
              ScreenArgumentImportWareHouse argument = args as ScreenArgumentImportWareHouse;
              return ImportWarehouseSAP(
                barcode: argument.barcode,
                token: argument.token,
                plant: argument.plant,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
              );
            });
      case '/QRCodePageChooseAnAddress':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePageChooseAnAddress'),
            builder: (_) {
              return const QRCodePageChooseAnAddress();
            });
      case '/ListTranferMaterial':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ListTranferMaterial'),
            builder: (_) {
              ScreenArgumentListTranferMaterial argument = args as ScreenArgumentListTranferMaterial;
              return ListTranferMaterial(
                token: argument.token,
                plant: argument.plant,
                dateTimeOld: argument.dateTimeOld,
                accountId: argument.accountId,
              );
            });
      case '/ListTranferMaterial2':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ListTranferMaterial2'),
            builder: (_) {
              ScreenArgumentListTranferMaterial argument = args as ScreenArgumentListTranferMaterial;
              return ListTranferMaterial2(
                token: argument.token,
                plant: argument.plant,
                dateTimeOld: argument.dateTimeOld,
                accountId: argument.accountId,
              );
            });
      case '/ExportWarehouse':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ExportWarehouse'),
            builder: (_) {
              ScreenArgumentExportWareHouse argument = args as ScreenArgumentExportWareHouse;
              return ExportWarehouse(
                  dataListWarehouseTranfer: argument.dataListWarehouseTranfer,
                  token: argument.token,
                  plant: argument.plant,
                  lsDataSlocAddress: argument.lsDataSlocAddress,
                  dateTimeOld: argument.dateTimeOld);
            });
      case '/ExportWarehouse2':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/ExportWarehouse2'),
            builder: (_) {
              ScreenArgumentExportWareHouse argument = args as ScreenArgumentExportWareHouse;
              return ExportWarehouse2(
                  dataListWarehouseTranfer: argument.dataListWarehouseTranfer,
                  token: argument.token,
                  plant: argument.plant,
                  lsDataSlocAddress: argument.lsDataSlocAddress,
                  dateTimeOld: argument.dateTimeOld);
            });
      case '/AddTranferWarehouse':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/AddTranferWarehouse'),
            builder: (_) {
              ScreenArgumentAddTranferWareHouse argument = args as ScreenArgumentAddTranferWareHouse;
              return AddTranferWarehouse(
                  token: argument.token, plant: argument.plant, lsDataSlocAddress: argument.lsDataSlocAddress, dateTimeOld: argument.dateTimeOld);
            });
      case '/StatisticsMaterials':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/StatisticsMaterials'),
            builder: (_) {
              ScreenArgumentStatisticsMaterial argument = args as ScreenArgumentStatisticsMaterial;
              return StatisticsMaterials(token: argument.token, plant: argument.plant, dateTimeOld: argument.dateTimeOld);
            });
      case '/QRcodePageTranferMaterial':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRcodePageTranferMaterial'),
            builder: (_) {
              return const QRcodePageTranferMaterial();
            });
      // case '/WarehouseTranferImport':
      //   return MaterialPageRoute(
      //       settings: const RouteSettings(
      //           name: '/WarehouseTranferImport'),
      //       builder: (_) {
      //         ScreenArgumentWareHouseTranfeImport argument = args as  ScreenArgumentWareHouseTranfeImport;
      //         return WarehouseTranferImport(
      //             plant: argument.plant,
      //             lsDataSlocAddress: argument.lsDataSlocAddress,
      //             token: argument.token,
      //             dataListWarehouseTranfer: argument.dataListWarehouseTranfer,
      //             barcode: "");
      //       });
      case '/QRCodePageGetSlocChooseAnTranferWareHouse':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePageGetSlocChooseAnTranferWareHouse'),
            builder: (_) {
              return const QRCodePageGetSlocChooseAnTranferWareHouse();
            });
      case '/QRCodePageGetSlocExport':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePageGetSlocExport'),
            builder: (_) {
              return const QRCodePageGetSlocExport();
            });
      case '/MaterialRetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/MaterialRetail'),
            builder: (_) {
              ScreenArgumentMaterialRetail argument = args as ScreenArgumentMaterialRetail;
              return MaterialRetail(token: argument.token, barcode: argument.barcode, plant: argument.plant, dateTimeOld: argument.dateTimeOld);
            });
      case '/DetailReservation':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/DetailReservation'),
            builder: (_) {
              ScreenArgumentDetailReservationImport argument = args as ScreenArgumentDetailReservationImport;
              return DetailReservation(
                  dataListWarehouseTranfer: argument.dataListWarehouseTranfer, token: argument.token, dateTimeOld: argument.dateTimeOld);
            });
      // case '/ExportWarehouseTwoSteps':
      //   return MaterialPageRoute(
      //       settings: const RouteSettings(
      //           name: '/ExportWarehouseTwoSteps'),
      //       builder: (_) {
      //         ScreenArgumentExportWareHouse argument = args as  ScreenArgumentExportWareHouse;
      //         return  ExportWarehouseTwoSteps(
      //             dataListWarehouseTranfer: argument.dataListWarehouseTranfer,
      //             token: argument.token,
      //             plant: argument.plant,
      //             lsDataSlocAddress: argument.lsDataSlocAddress
      //         );
      //       });
      case '/MaterialUnused':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/MaterialUnused'),
            builder: (_) {
              ScreenArgumentMaterialUnused argument = args as ScreenArgumentMaterialUnused;
              return MaterialUnused(token: argument.token, dateTimeOld: argument.dateTimeOld);
            });
      case '/BottomNavigatorBar':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BottomNavigatorBar'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BottomNavigatorBar(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/QRCodePageListQC':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePageListQC'),
            builder: (_) {
              // ScreenArgumentQRCodePageListQC argument = args as ScreenArgumentQRCodePageListQC ;
              return const QRCodePageListQC(
                  // token:argument.token,
                  // accountID: argument.accountID,
                  // dateTimeOld: argument.dateTimeOld,
                  );
            });
      case '/QRCodePageListQC2':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodePageListQC2'),
            builder: (_) {
              return const QRCodePageListQC2();
            });
      case '/InventoryMaterialSloc':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/InventoryMaterialSloc'),
            builder: (_) {
              ScreenArgumentInventMSloc argument = args as ScreenArgumentInventMSloc;
              return InventoryMaterialSloc(
                token: argument.token,
                plant: argument.plant,
                dateTimeOld: argument.dateTimeOld,
              );
            });
      case '/QRCodeInventoryMaterialSloc':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/QRCodeInventoryMaterialSloc'),
            builder: (_) {
              return const QRCodeInventoryMaterialSloc();
            });
      case '/TraHangNCC':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/TraHangNCC',
          ),
          builder: (_) {
            ScreenArgumentsTraHangNCC argument = args as ScreenArgumentsTraHangNCC;
            return TraHangNCC(
              permission: argument.permission,
              token: argument.token,
              plant: argument.plant,
              dateTimeOld: argument.dateTimeOld,
              accountId: argument.accountId,
            );
          },
        );
      case '/Notification':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/Notification',
          ),
          builder: (_) {
            CommonScreenPermissionArgument argument = args as CommonScreenPermissionArgument;
            return NotificationPage(
              permission: argument.permission,
              token: argument.token,
              plant: argument.plant,
              dateTimeOld: argument.dateTimeOld,
              accountId: argument.accountId,
            );
          },
        );
      case '/KiemTraChatLuong':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/KiemTraChatLuong',
          ),
          builder: (_) {
            CommonScreenPermissionArgument argument = args as CommonScreenPermissionArgument;
            return KiemTraChatLuong(
              permission: argument.permission,
              token: argument.token,
              plant: argument.plant,
              dateTimeOld: argument.dateTimeOld,
              accountId: argument.accountId,
              user: argument.user,
            );
          },
        );
      case '/BaoCaoDauVao':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/BaoCaoDauVao',
          ),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoDauVao(
                // permission: argument.permission,
                // token: argument.token,
                // plant: argument.plant,
                userModel: argument.user,
                dateTimeOld: argument.dateTimeOld.toString()
                // accountId: argument.accountId
                );
          },
        );
      case '/BaoCaoDauVao2':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/BaoCaoDauVao2',
          ),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoDauVao2(
                // permission: argument.permission,
                // token: argument.token,
                // plant: argument.plant,
                userModel: argument.user,
                dateTimeOld: argument.dateTimeOld.toString()
                // accountId: argument.accountId
                );
          },
        );
      case '/BaoCaoDauVaoDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoDauVaoDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoDauVaoDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/BaoCaoDauVaoDetail2':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoDauVaoDetail2'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoDauVaoDetail2(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/PhieuKCSCongDoan':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/PhieuKCSCongDoan',
          ),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return PhieuKCSCongDoan(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );
      case '/PhieuKCSCongDoanDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/PhieuKCSCongDoanDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return PhieuKCSCongDoanDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });

      case '/BaoCaoQAQCNghiemThu':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQAQCNghiemThu'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQAQCNghiemThu(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );
      case '/BaoCaoQAQCNghiemThuDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQAQCNghiemThuDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQAQCNghiemThuDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/BaoCaoQCMau':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCMau'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCMau(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );

      case '/BaoCaoQCMauDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCMauDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCMauDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qualityType: "QCMAU",
                user: argument.user,
              );
            });
      case '/BaoCaoQCGiaCong':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCGiaCong'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCGiaCong(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );

      case '/BaoCaoQCGiaCongDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCGiaCongDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCGiaCongDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qualityType: "QCGIACONG",
                user: argument.user,
              );
            });
      case '/BaoCaoQCSanPham':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCSanPham'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCSanPham(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );
      case '/BaoCaoQCSanPhamDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCSanPhamDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCSanPhamDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/BaoCaoQCHienTruong':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCHienTruong'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCHienTruong(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );
      case '/BaoCaoQCHienTruongDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCHienTruongDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCHienTruongDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qualityType: "HIENTRUONG",
                user: argument.user,
              );
            });
      case '/BaoCaoQC5S':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQC5S'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQC5S(
              // permission: argument.permission,
              // token: argument.token,
              // plant: argument.plant,
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
              // accountId: argument.accountId
            );
          },
        );
      case '/BaoCaoQC5SDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQC5SDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQC5SDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qualityType: "QC5S",
                user: argument.user,
              );
            });
      case '/BaoCaoQCMauDauChuyen':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCMauDauChuyen'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCMauDauChuyen(
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
            );
          },
        );
      case '/BaoCaoQCMauDauChuyenDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCMauDauChuyenDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCMauDauChuyenDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qrCode: argument.qrCode,
                user: argument.user,
              );
            });
      case '/BaoCaoQCTestLab':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/BaoCaoQCTestLab'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return BaoCaoQCTestLab(
              user: argument.user,
              dateTimeOld: argument.dateTimeOld.toString(),
            );
          },
        );

      case '/BaoCaoQCTestLabDetail':
        return MaterialPageRoute(
            settings: const RouteSettings(name: '/BaoCaoQCTestLabDetail'),
            builder: (_) {
              ScreenArgumentNavigatorBar argument = args as ScreenArgumentNavigatorBar;
              return BaoCaoQCTestLabDetail(
                qualityControlId: argument.qualityControlId,
                dateTimeOld: argument.dateTimeOld,
                fromPage: argument.fromPage,
                qualityType: "QCMAU",
                user: argument.user,
              );
            });
      case '/CreateNewTraHangNCC':
        return MaterialPageRoute(
          settings: const RouteSettings(
            name: '/CreateNewTraHangNCC',
          ),
          builder: (_) {
            ScreenArgumentsCreateNewTraHangNCC argument = args as ScreenArgumentsCreateNewTraHangNCC;
            return CreateNewTraHangNCC(token: argument.token, dateTimeOld: argument.dateTimeOld, id: argument.id);
          },
        );
      case '/Downtime':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/Downtime'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return DowntimeList(
              dateTimeOld: argument.dateTimeOld.toString(),
              user: argument.user,
            );
          },
        );

      case '/DowntimeDetail':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/DowntimeDetail'),
          builder: (_) {
            final Map<String, dynamic> argument = args as Map<String, dynamic>;
            return DowntimeDetail(
              id: argument['id'] as String,
              dateTimeOld: argument['dateTimeOld'] as String,
              user: argument['user'] as DataUser,
            );
          },
        );

      case '/MaintenanceOrder':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/MaintenanceOrder'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return MaintenanceOrderList(
              dateTimeOld: argument.dateTimeOld.toString(),
              user: argument.user,
            );
          },
        );

      case '/MaintenanceOrderDetail':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/MaintenanceOrderDetail'),
          builder: (_) {
            final Map<String, dynamic> argument = args as Map<String, dynamic>;
            return MaintenanceOrderDetail(
              id: argument['id'] as String,
              dateTimeOld: argument['dateTimeOld'] as String,
              user: argument['user'] as DataUser,
              viewMode: argument['viewMode'] as bool?,
              maintenanceOrder: argument['maintenanceOrder'] as MaintenanceOrder?,
            );
          },
        );

      case '/TyLeTieuHao':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/TyLeTieuHao'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return TyLeTieuHaoList(
              dateTimeOld: argument.dateTimeOld.toString(),
              user: argument.user,
            );
          },
        );

      case '/TyLeTieuHaoDetail':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/TyLeTieuHaoDetail'),
          builder: (_) {
            final Map<String, dynamic> argument = args as Map<String, dynamic>;
            return TyLeTieuHaoDetail(
              id: argument['id'] as String,
              dateTimeOld: argument['dateTimeOld'] as String,
              user: argument['user'] as DataUser,
              viewMode: argument['viewMode'] as bool?,
            );
          },
        );

      // MaiDao routes
      case '/MaiDaoList':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/MaiDaoList'),
          builder: (_) {
            CommonScreenUserArgument argument = args as CommonScreenUserArgument;
            return MaiDaoList(
              dateTimeOld: argument.dateTimeOld.toString(),
              user: argument.user,
            );
          },
        );

      case '/MaiDaoDetail':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/MaiDaoDetail'),
          builder: (_) {
            final Map<String, dynamic> argument = args as Map<String, dynamic>;
            return MaiDaoDetail(
              id: argument['id'] as String,
              dateTimeOld: argument['dateTimeOld'] as String,
              user: argument['user'] as DataUser,
              viewMode: argument['viewMode'] as bool?,
              maiDaoRecord: argument['maiDaoRecord'] as MaiDaoRecord?,
            );
          },
        );

      case '/ProductManagement':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/ProductManagement'),
          builder: (_) {
            CommonScreenPermissionArgument argument = args as CommonScreenPermissionArgument;
            return ProductManagement(
              permission: argument.permission,
              token: argument.token,
              plant: argument.plant,
              dateTimeOld: argument.dateTimeOld,
              accountId: argument.accountId,
              user: argument.user,
            );
          },
        );

      case '/ImportProduct':
        return MaterialPageRoute(
          settings: const RouteSettings(name: '/ImportProduct'),
          builder: (_) {
            CommonScreenPermissionArgument argument = args as CommonScreenPermissionArgument;
            return ImportProduct(
              permission: argument.permission,
              token: argument.token,
              plant: argument.plant,
              dateTimeOld: argument.dateTimeOld,
              accountId: argument.accountId,
              user: argument.user,
            );
          },
        );

      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(builder: (_) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('LỖI'),
        ),
        body: const Center(
          child: Text('KHÔNG TÌM THẤY TRANG. VUI LÒNG QUAY LẠI !!!'),
        ),
      );
    });
  }
}
