﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StockTransferRequestDetailModel", Schema = "Warehouse")]
    public partial class StockTransferRequestDetailModel
    {
        [Key]
        public Guid Id { get; set; }
        public Guid? StockTransferRequestId { get; set; }
        public Guid? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RequestQuantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? TransferredQuantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitPrice { get; set; }
        public bool? isDeleted { get; set; }
        [StringLength(500)]
        public string Note { get; set; }

        [ForeignKey("ProductId")]
        [InverseProperty("StockTransferRequestDetailModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("StockTransferRequestId")]
        [InverseProperty("StockTransferRequestDetailModel")]
        public virtual StockTransferRequestModel StockTransferRequest { get; set; }
    }
}