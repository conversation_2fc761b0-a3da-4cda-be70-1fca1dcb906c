import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:ttf/model/saveAccountLogin.dart';
import 'package:ttf/screenArguments/screenArgumentMainPage.dart';
import 'package:ttf/utils/appConfig.dart';
import '../Storage/storageSecureStorage.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../model/userModel.dart';
import '../repository/function/loginFunction.dart';

class Login extends StatefulWidget {
  const Login({Key? key}) : super(key: key);

  @override
  _LoginState createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _controllerUserName = TextEditingController();
  final _controllerPassWord = TextEditingController();
  bool _obscureText = true;
  String? _companyCode;
  // final String _keyUser = 'user';
  // final String _keyPassword = 'password';
  // final String _keyAccount = 'account';
  // final String _keyCompany = 'company';
  // final String _keySaleOrg = 'saleOrg';
  // final String _keySelectedCompany = 'selectedCompany';
  // final String _keyDateTimeNow = 'datetimeNow';

  bool _saveLocal = false;
  GetListCompanyByUserName? _selectedCompany;
  List<GetListCompanyByUserName> _companyData = [];

  GetSaleOrgByCompanyId? _saleOrgData;
  bool? _error;
  bool? _error_2;
  bool? _error_3;

  // String? _selectedEnvironment;
  DropdownItemList? _selectedEnvironment;

  final List<DropdownItemList> _environments = [
    DropdownItemList(catalogCode: 'PRD', catalogTextVi: 'PRD (800)'),
    DropdownItemList(catalogCode: 'QAS', catalogTextVi: 'QAS (300)')
  ];

  @override
  void initState() {
    super.initState();
    _init();
  }

  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  Future<void> _init() async {
    final dataStorage = await SecureStorage.getString("saveAccountLogin", _controllerUserName.text.isNotEmpty ? _controllerUserName.text : null);
    if (dataStorage != null) {
      SaveAccountLogin saveAccountLogin = SaveAccountLogin.fromJson(jsonDecode(dataStorage));
      if (!mounted) return;

      setState(() {
        _controllerUserName.text = saveAccountLogin.userName ?? "";
        _controllerPassWord.text = saveAccountLogin.password ?? "";
        if (saveAccountLogin.company != null && saveAccountLogin.selectedCompany != null) {
          _companyData = List<GetListCompanyByUserName>.from(jsonDecode(saveAccountLogin.company!).map((i) => GetListCompanyByUserName.fromJson(i)));
          _selectedCompany = _companyData.firstWhere((element) => element.companyCode == saveAccountLogin.selectedCompany!.companyCode);
          _companyCode = _selectedCompany!.companyCode;
        } else {
          _companyData = [];
          _selectedCompany = null;
          _companyCode = null;
        }
        if (saveAccountLogin.saleOrg != null) {
          _saleOrgData = GetSaleOrgByCompanyId.fromJson(jsonDecode(saveAccountLogin.saleOrg!));
        } else {
          _saleOrgData = null;
        }

        _selectedEnvironment = _environments.firstWhere((element) => element.catalogCode == saveAccountLogin.selectedEnvironment?.catalogCode,
            orElse: () => _environments.first);
        _setEnvironment(_selectedEnvironment);
      });
    } else {
      _setEnvironment(_environments[0]);
    }
  }

  void _setDisableButton() {
    setState(() {
      _saveLocal = true;
    });
  }

  void _clearErrorCompanyData(List<GetListCompanyByUserName>? data) {
    if (!mounted) return;
    setState(() {
      _error = false;
      if (data != null) {
        _companyData = data;
        _companyData.insert(0, (LoginFunction.defaultValue));
      } else {
        _companyData = [];
      }
    });
  }

  void _setError() {
    setState(() {
      _error = true;
    });
  }

  void _setError_2() {
    setState(() {
      if (_controllerPassWord.text.isEmpty) {
        _error_2 = true;
      } else {
        _error_2 = false;
      }
      if (_companyData.isNotEmpty && _companyCode == null) {
        _error_3 = true;
      } else {
        _error_3 = false;
      }
    });
  }

  void _clearPasswordCompany() {
    if (_companyData.isNotEmpty && _controllerPassWord.text.isNotEmpty) {
      _controllerPassWord.clear();
    }
    if (_selectedCompany != null || _companyCode != null || _saleOrgData != null || _companyData.isNotEmpty) {
      setState(() {
        if (_companyData.isNotEmpty) {
          _companyData = [];
        }
        if (_selectedCompany != null) {
          _selectedCompany = null;
        }
        if (_companyCode != null) {
          _companyCode = null;
        }
        if (_saleOrgData != null) {
          _saleOrgData = null;
        }
      });
    } else {
      return;
    }
  }

  void _setCompanyData(GetListCompanyByUserName? value) {
    setState(() {
      _selectedCompany = value!;
      if (_selectedCompany!.companyName != "-- Công ty --") {
        _companyCode = value.companyCode.toString();
      } else {
        _companyCode = null;
      }
    });
  }

  Future<void> _setEnvironment(DropdownItemList? newValue) async {
    setState(() {
      _selectedEnvironment = newValue;
    });

    // Save environment to storage

    var data = newValue!.catalogCode.toString();

    await SecureStorage.setString(data, "environment", null);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('environment', data);

    // final test = prefs.getString('environment') ?? "QAS";

    // debugPrint("prefs.getString");
    // debugPrint(test);
  }

  @override
  void dispose() {
    _controllerUserName.dispose();
    _controllerPassWord.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              // await _checkConnectNetwork();
              FocusScope.of(context).unfocus();
              final check = await LoginFunction.checkConnectNetwork();
              if (check != ConnectivityResult.none) {
                if (_companyData.isEmpty) {
                  if (_controllerUserName.text.isNotEmpty) {
                    final data = await LoginFunction.getCompany(context, _controllerUserName.text);
                    _clearErrorCompanyData(data);
                  } else {
                    _setError();
                  }
                } else {
                  return;
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    backgroundColor: Colors.black,
                    content: Text(
                      'Không có kết nối mạng!',
                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                    ),
                    duration: const Duration(seconds: 1)));
              }
            },
            child: Scaffold(
                backgroundColor: Colors.white,
                body: SingleChildScrollView(
                  child: SafeArea(
                      minimum: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          SizedBox(height: 70.h),
                          const _LogoLogin(),
                          SizedBox(height: 50.h),
                          const _TitleLogin(title: "Đăng nhập"),
                          SizedBox(height: 5.h),
                          Container(
                            // padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
                            decoration: BoxDecoration(
                              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(5.r),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<DropdownItemList>(
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
                                isExpanded: true,
                                isDense: true,
                                itemHeight: null,
                                value: _selectedEnvironment,
                                iconSize: 15.sp,
                                style: const TextStyle(color: Colors.white),
                                onChanged: (DropdownItemList? newValue) {
                                  _setEnvironment(newValue);
                                },
                                items: _environments.map((DropdownItemList item) {
                                  return DropdownMenuItem<DropdownItemList>(
                                      value: item,
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: 5.h),
                                        child: Text(
                                          item.catalogTextVi.toString(),
                                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                        ),
                                      ));
                                }).toList(),
                                selectedItemBuilder: (BuildContext context) {
                                  return _environments.map<Widget>((DropdownItemList item) {
                                    return Text(
                                      item.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  }).toList();
                                },
                              ),
                            ),
                          ),
                          SizedBox(height: 15.h),
                          TextFormField(
                            controller: _controllerUserName,
                            style: TextStyle(fontSize: 12.sp),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                borderRadius: BorderRadius.all(Radius.circular(5.r)),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                borderRadius: BorderRadius.all(Radius.circular(5.r)),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                borderRadius: BorderRadius.all(Radius.circular(5.r)),
                              ),
                              errorBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              hintText: "Nhập tài khoản",
                              filled: true,
                              isDense: true,
                              fillColor: Colors.white,
                              hintStyle: TextStyle(fontSize: 12.sp),
                              contentPadding: REdgeInsets.all(10),
                            ),
                            onChanged: (value) {
                              _clearPasswordCompany();
                            },
                            onFieldSubmitted: (value) async {
                              FocusScope.of(context).unfocus();
                              final check = await LoginFunction.checkConnectNetwork();
                              if (check != ConnectivityResult.none) {
                                if (_companyData.isEmpty) {
                                  if (_controllerUserName.text.isNotEmpty) {
                                    // await _getCompany(context);
                                    final data = await LoginFunction.getCompany(context, _controllerUserName.text);
                                    _clearErrorCompanyData(data);
                                  } else {
                                    _setError();
                                  }
                                }
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    backgroundColor: Colors.black,
                                    content: Text(
                                      'Không có kết nối mạng!',
                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                    ),
                                    duration: const Duration(seconds: 1)));
                              }
                            },
                          ),
                          Visibility(visible: _error ?? false, child: SizedBox(height: 10.h)),
                          ContainerError.widgetError(_error ?? false, 'Username không được để trống'),
                          SizedBox(height: _error == true ? 10.h : 15.h),
                          IgnorePointer(
                            ignoring: _companyData.isEmpty ? true : false,
                            child: Stack(
                              children: [
                                TextFormField(
                                  controller: _controllerPassWord,
                                  obscureText: _obscureText,
                                  style: TextStyle(fontSize: 12.sp),
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                      borderRadius: BorderRadius.all(Radius.circular(5.r)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                      borderRadius: BorderRadius.all(Radius.circular(5.r)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                      borderRadius: BorderRadius.all(Radius.circular(5.r)),
                                    ),
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    hintText: "Nhập mật khẩu",
                                    filled: true,
                                    isDense: true,
                                    fillColor: Colors.white,
                                    hintStyle: TextStyle(fontSize: 12.sp),
                                    // contentPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                    contentPadding: REdgeInsets.all(10),
                                  ),
                                ),
                                Positioned(
                                  top: 0.h,
                                  right: 0.w,
                                  left: 280.w,
                                  bottom: 0.h,
                                  child: IconButton(
                                    splashColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onPressed: () {
                                      _toggle();
                                    },
                                    iconSize: 15.sp,
                                    padding: EdgeInsets.zero,
                                    icon: Icon(
                                      _obscureText ? Icons.visibility : Icons.visibility_off,
                                      color: Colors.grey.shade400,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Visibility(visible: _error_2 ?? false, child: SizedBox(height: 10.h)),
                          ContainerError.widgetError(_error_2 ?? false, 'Password không được để trống'),
                          SizedBox(height: _error_2 == true ? 10.h : 15.h),
                          Visibility(
                              visible: _companyData.isNotEmpty ? true : false,
                              child: _DropdownCompany(
                                  companyData: _companyData,
                                  onChangeCompanyData: _setCompanyData,
                                  error_3: _error_3 ?? false,
                                  selectedCompany: _selectedCompany)),
                          Visibility(visible: _error_3 ?? false, child: SizedBox(height: 10.h)),
                          ContainerError.widgetError(_error_3 ?? false, 'Bạn chưa chọn công ty'),
                          SizedBox(height: _error_3 == true ? 40.h : 30.h),
                          IgnorePointer(
                            ignoring: _companyData.isEmpty ? true : false,
                            child: Center(
                              child: Container(
                                width: double.infinity,
                                decoration: const BoxDecoration(),
                                child: ElevatedButton(
                                  style: ButtonStyle(
                                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(38.r), side: const BorderSide(color: Colors.white))),
                                    side: MaterialStateProperty.all(
                                      const BorderSide(
                                        color: Color(0xff0052cc),
                                      ),
                                    ),
                                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                    padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 0.h)),
                                  ),
                                  onPressed: _saveLocal == false
                                      ? () async {
                                          final check = await LoginFunction.checkConnectNetwork();
                                          if (!mounted) return;
                                          if (check != ConnectivityResult.none) {
                                            _setError_2();
                                            if (_controllerUserName.text.isNotEmpty &&
                                                _controllerPassWord.text.isNotEmpty &&
                                                (_companyData.isNotEmpty && _companyCode != null)) {
                                              FocusScope.of(context).unfocus();
                                              final user = await LoginFunction.login(
                                                  context,
                                                  _controllerUserName.text,
                                                  _controllerPassWord.text,
                                                  _companyCode,
                                                  _companyData,
                                                  _selectedCompany,
                                                  _selectedEnvironment,
                                                  "user",
                                                  "account",
                                                  "password",
                                                  "datetimeNow",
                                                  "company",
                                                  "saleOrg",
                                                  "selectedCompany",
                                                  "selectedEnvironment");
                                              if (user != null) {
                                                _setDisableButton();
                                                if (!mounted) return;
                                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                    backgroundColor: Colors.black,
                                                    content: Text(
                                                      'Chào mừng ${user.data!.userName.toString()}',
                                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                    ),
                                                    duration: const Duration(seconds: 1)));
                                                Future.delayed(const Duration(seconds: 0), () {
                                                  Navigator.pushReplacementNamed(context, "/mainPage", arguments: ScreenArgumentMainPage(user.data!));
                                                });
                                              }
                                            } else {
                                              return;
                                            }
                                          } else {
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                backgroundColor: Colors.black,
                                                content: Text(
                                                  'Không có kết nối mạng',
                                                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                ),
                                                duration: const Duration(seconds: 1)));
                                          }
                                        }
                                      : null,
                                  child: const _ButtonLoginText(),
                                ),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(top: 10.h),
                            child: Align(
                              alignment: Alignment.center,
                              child: Text(
                                AppConfig.appVersion,
                                style: TextStyle(
                                  fontSize: 9.sp,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          )
                        ],
                      )),
                ))));
  }
}

class _DropdownCompany extends StatelessWidget {
  final bool error_3;
  final GetListCompanyByUserName? selectedCompany;
  final ValueChanged<GetListCompanyByUserName?> onChangeCompanyData;
  final List<GetListCompanyByUserName> companyData;
  const _DropdownCompany(
      {Key? key, required this.error_3, required this.selectedCompany, required this.onChangeCompanyData, required this.companyData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(width: 0.5.w, color: error_3 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400)),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<GetListCompanyByUserName>(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
          isExpanded: true,
          isDense: true,
          itemHeight: null,
          iconSize: 0.0,
          value: selectedCompany ?? LoginFunction.defaultValue,
          style: const TextStyle(color: Colors.black),
          onChanged: onChangeCompanyData,
          items: companyData.map((GetListCompanyByUserName company) {
            return DropdownMenuItem<GetListCompanyByUserName>(
                value: company,
                child: Padding(
                    padding: EdgeInsets.only(left: 15.w, right: 5.w),
                    child: Text(
                      company.companyName.toString(),
                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                    )));
          }).toList(),
          selectedItemBuilder: (BuildContext context) {
            return companyData.map<Widget>((GetListCompanyByUserName company) {
              return Text(company.companyName.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
            }).toList();
          },
        ),
      ),
    );
  }
}

class _ButtonLoginText extends StatelessWidget {
  const _ButtonLoginText({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 15.h),
      child: Text(
        'Đăng nhập',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
      ),
    );
  }
}

class _TitleLogin extends StatelessWidget {
  final String title;
  const _TitleLogin({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(fontSize: 17.sp, fontWeight: FontWeight.bold, color: const Color(0xff0052cc)),
    );
  }
}

class _LogoLogin extends StatelessWidget {
  const _LogoLogin({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Image.asset(
        "assets/logo/logo.png",
        height: 130.h,
        width: 130.w,
      ),
    );
  }
}
