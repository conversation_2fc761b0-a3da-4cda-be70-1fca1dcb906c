import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postFilterWareHouseTranfer.dart';
import '../../urlApi/urlApi.dart';

class ListWareHouseTranferApi {
  static Future<http.Response> getListWareHouseTranfer(String token, PostFilterWareHouseTranfer postFilterWareHouseTranfer, Paging paging) async {
    Map<String, dynamic> data = {
      "paging": paging,
      "plant": postFilterWareHouseTranfer.plant,
      "slocExport": postFilterWareHouseTranfer.slocExport,
      "slocImport": postFilterWareHouseTranfer.slocImport,
      "reservationCode": postFilterWareHouseTranfer.reservationCode,
      "statusReservation": postFilterWareHouseTranfer.statusReservation == "all" ? null : postFilterWareHouseTranfer.statusReservation,
      "productCode": postFilterWareHouseTranfer.productCode,
      "statusWarehouse": postFilterWareHouseTranfer.statusWarehouse == "all" ? null : postFilterWareHouseTranfer.statusWarehouse,
      "requestDateFrom": postFilterWareHouseTranfer.requestDateFrom == null ? null : postFilterWareHouseTranfer.requestDateFrom!.toIso8601String(),
      "requestDateTo": postFilterWareHouseTranfer.requestDateTo == null ? null : postFilterWareHouseTranfer.requestDateTo!.toIso8601String(),
      "documentDateFrom": postFilterWareHouseTranfer.documentDateFrom == null ? null : postFilterWareHouseTranfer.documentDateFrom!.toIso8601String(),
      "documentDateTo": postFilterWareHouseTranfer.documentDateTo == null ? null : postFilterWareHouseTranfer.documentDateTo!.toIso8601String()
    };
    final body = json.encode(data);
    if (kDebugMode) {
      print(body);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "ListWarehouseTranfer");
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersToken(token), body: body);
    return response;
  }

  static Future<http.Response> getStatusReservation(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + '/' + UrlApi.baseUrlCommon + 'GetStatusReservation';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getStatusWarehouseTranfer(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + '/' + UrlApi.baseUrlCommon + 'GetStatusWarehouseTranfer';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }
}
