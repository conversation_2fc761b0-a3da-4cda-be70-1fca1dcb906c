﻿using ISD.API.Constant.Common;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using SAPGetLSXDT;
using SAPGetStock;
using SAPWarehouseReceive;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    //[ISDWebAuthorization]
    public class PurchaseOrderController : ControllerBaseAPI
    {
        private readonly IHostEnvironment _env;

        public PurchaseOrderController(IHostEnvironment env)
        {
            _env = env;
        }

        #region Search - Tìm kiếm danh sách PurchaseOrder
        /// <summary>API Search "Tìm kiếm danh sách PurchaseOrder"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "purchaseOrderCode": "",
        ///                 "companyCode": "",
        ///                 "documentType": "",
        ///                 "deletionInd": "",
        ///                 "vendorNumber": "",
        ///                 "purchasingOrg": "",
        ///                 "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": [
        ///         {
        ///             "stt": 1,
        ///             "purchaseOrderCode": "4200000007",
        ///             "companyCode": "1000",
        ///             "documentType": "ZPO2",
        ///             "deletionInd": "",
        ///             "vendorNumber": "0020010126",
        ///             "purchasingOrg": "1000",
        ///             "actived": true,
        ///             "createTime": "2022-07-13T11:49:20.613",
        ///             "lastEditTime": null,
        ///             "purchaseOrderId": "54504f05-bdc4-456e-af24-0007a92f6c5e"
        ///         }
        ///        ]
        ///     }
        /// </remarks>
        [HttpPost("Search")]
        public ActionResult GetPurchaseOrder([FromBody] PurchaseOrderSearchViewModel searchVm)
        {
            //Danh sách document type
            //var documentTypeArr = new[]  { "ZPO1", "ZPO2", "ZPO7" };


            var queryPOTemp = _context.PurchaseOrderMasterModel.Where(p => p.Actived == true
                                               && p.ReleaseIndicator == "X"
                                               //&& documentTypeArr.Contains(p.DocumentType)
                                               && (!searchVm.PurchaseOrderCode.IsNullOrEmpty() ? searchVm.PurchaseOrderCode.Contains(p.PurchaseOrderCode) : true)
                                               && (!string.IsNullOrEmpty(searchVm.DocumentType) ? p.DocumentType == searchVm.DocumentType : true)
                                               && (!string.IsNullOrEmpty(searchVm.DeletionInd) ? p.DeletionInd == searchVm.DeletionInd : true)
                                               //&& (searchVm.DocumentDateFrom.HasValue ? p.DocumentDate >= searchVm.DocumentDateFrom && p.DocumentDate <= searchVm.DocumentDateTo : true)
                                               && (searchVm.DocumentDateFrom.HasValue ? p.DocumentDate >= searchVm.DocumentDateFrom : true)
                                               && (searchVm.DocumentDateTo.HasValue ? p.DocumentDate <= searchVm.DocumentDateTo : true)
                                               && (!string.IsNullOrEmpty(searchVm.PurchasingOrg) ? p.PurchasingOrg == searchVm.PurchasingOrg : true)
                                               && (p.PurchaseOrderDetailModel.Count(e => e.DelivCompl != "X") > 0));

            var accountId = CurrentUser?.AccountId;

            //Nếu user là Nhà cung cấp chỉ được xem của nhà cung cấp đó
            var user = _context.AccountModel.FirstOrDefault(x => x.AccountId == accountId);
            if (!string.IsNullOrEmpty(user?.VendorNumber))
            {
                queryPOTemp = queryPOTemp.Where(x => x.VendorNumber == user.VendorNumber && x.IsVendorView == true);
            }
            //Nếu là nhân viên where theo api gửi lên
            else
            {
                //Company
                if (!string.IsNullOrEmpty(searchVm.CompanyCode))
                {
                    queryPOTemp = queryPOTemp.Where(x => x.CompanyCode == searchVm.CompanyCode || x.PurchasingOrg == searchVm.CompanyCode);
                }

                if (!string.IsNullOrEmpty(searchVm.VendorNumber))
                {
                    queryPOTemp = queryPOTemp.Where(x => x.VendorNumber == searchVm.VendorNumber);
                }
            }

            var productCode = "";

            if (!string.IsNullOrEmpty(searchVm.ProductCode))
            {
                productCode = searchVm.ProductCode.Split('|')[0];

                // Help me join PurchaseOrderMasterModels with PurchaseOrderDetailModel and only get PurchaseOrderDetailModel.ProductCode = productCode
                queryPOTemp = queryPOTemp.Where(p => p.PurchaseOrderDetailModel.Any(d => d.ProductCode.Trim() == productCode));
            }

            var total = queryPOTemp.Count();

            var queryPO = queryPOTemp
                                   .OrderByDescending(x => x.PurchaseOrderCode) // Ensure there's an order before pagination
                                   .Skip(searchVm.Paging.start)
                                   .Take(searchVm.Paging.length);


            //Danh sách product
            //var products = _context.ProductModel.AsNoTracking();
            //Danh sách vendor

            var vendorIdList = queryPO.Select(p => p.VendorNumber).ToArray();

            var vendors = _context.VendorModel.AsNoTracking().Where(v => vendorIdList.Contains(v.SupplierNumber));

            var responseTemp = queryPO.Select(p => new PurchaseOrderMasterViewModel
            {
                PurchaseOrderId = p.PurchaseOrderId,
                PurchaseOrderCode = p.PurchaseOrderCode,
                CompanyCode = p.CompanyCode,
                DocumentType = p.DocumentType,
                DeletionInd = p.DeletionInd,
                VendorNumber = p.VendorNumber,
                VendorName = vendors.FirstOrDefault(x => x.SupplierNumber == p.VendorNumber) == null ? null : vendors.FirstOrDefault(x => x.SupplierNumber == p.VendorNumber).LongName,
                PurchasingOrg = p.PurchasingOrg,
                DocumentDate = p.DocumentDate,
                Actived = p.Actived,
                IsVendorView = p.IsVendorView != true ? false : true,
                //ProductCodeName = p.PurchaseOrderDetailModel.Any() ? p.PurchaseOrderDetailModel.Where(x => string.IsNullOrEmpty(searchVm.ProductCode) ? true : x.ProductCode.Contains(searchVm.ProductCode)).Select(x => $"{x.ProductCode} | {products.FirstOrDefault(k => k.ProductCode == x.ProductCode).ProductName}").ToList() : null
            });

            //response = response.OrderByDescending(x => x.PurchaseOrderCode);

            //var res = response.ToList();

            var response = responseTemp.ToList();

            var poIdList = responseTemp.Select(p => p.PurchaseOrderId).ToArray();
            var poDetailList = _context.PurchaseOrderDetailModel.Where(p => poIdList.Contains((Guid)p.PurchaseOrderId)).ToList();

            foreach (var item in response)
            {
                var found = poDetailList.Where(i => i.PurchaseOrderId == item.PurchaseOrderId).ToList();

                if (found.Count > 0)
                {
                    var productCodeName = found.Select(x => $"{x.ProductCode} | {x.ShortText}").Distinct().ToList();
                    item.ProductCodeName = productCodeName;
                }
            }

            var res = response.Where(r => r.ProductCodeName != null).ToList();

            if (res != null && res.Count() > 0)
            {
                int i = searchVm.Paging.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            //var RecordsFiltered = filteredResultsCount;
            //var RecordsFiltered = queryPO.Count();
            var RecordsFiltered = total;

            //var RecordsTotal = totalResultsCount;
            var RecordsTotal = searchVm.Paging.length;

            return Ok(new ApiSuccessResponse<List<PurchaseOrderMasterViewModel>>
            {
                Data = res,
                Draw = searchVm.Paging.draw,
                RecordsFiltered = RecordsFiltered,
                RecordsTotal = RecordsTotal
            });
        }

        /// <summary>
        /// Get danh sách dropdown NCC
        /// </summary>
        /// <param name="VendorCode"></param>
        /// <returns></returns>
        [HttpGet("ListVendor")]
        public async Task<IActionResult> GetListVendor([FromQuery] string VendorCode)
        {
            var response = await _context.VendorModel.Where(x => !string.IsNullOrEmpty(VendorCode) ? (x.SupplierNumber.Trim().ToLower().Contains(VendorCode.Trim().ToLower()) ||
                                                                                                      x.LongName.Trim().ToLower().Contains(VendorCode.Trim().ToLower())) : true)
                                                     .Take(20).AsNoTracking()
                                                     .Select(x => new
                                                     {
                                                         VendorCode = x.SupplierNumber,
                                                         VendorDisplay = $"{x.SupplierNumber} | {x.ShortName}"
                                                     }).OrderBy(x => x.VendorCode).ToListAsync();

            return Ok(new ApiResponse
            {
                Data = response,
                IsSuccess = true
            });
        }
        #endregion

        #region Duyệt cho nhà cung cấp xem
        [HttpPost("ApproveViewVendor")]
        public async Task<IActionResult> ApproveViewVendor([FromBody] ApproveViewVendorRequest vm)
        {
            //Check số lượng nhập
            if (!vm.PurchaseOrderIds.Any())
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.REQUIRED_ITEM });

            var purchaseOrders = _context.PurchaseOrderMasterModel.Where(x => vm.PurchaseOrderIds.Contains(x.PurchaseOrderId));
            //Check tồn tại
            if (!purchaseOrders.Any())
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Danh sách PO") });

            foreach (var item in purchaseOrders)
            {
                item.IsVendorView = true;
            }

            await _context.SaveChangesAsync();
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = true,
                Message = string.Format(CommonResource.Msg_Succes, "Duyệt PO cho NCC xem")
            });
        }
        #endregion

        #region Detail - Lấy thông tin PurchaseOrder (Master && Detail)
        /// <summary>API Detail "Tìm kiếm thông tin PurchaseOrder (Master - Detail) theo Guid"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/GetDetail?PurchaseOrderId={purchaseOrderId}
        ///     Params: 
        ///             + version : 2
        ///             + purchaseOrderId : 54504f05-bdc4-456e-af24-0007a92f6c5e
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///       "code": 200,
        ///       "message": null,
        ///       "data": [
        ///         {
        ///           "stt": 1,
        ///           "poDetailId":"",
        ///           "poCode": "6000000272",
        ///           "poItem": "10",
        ///           "materialCode": "000000000230000001",
        ///           "productName": "Gỗ Tràm 22x41x500, P/cấp:X",
        ///           "storageLocation": "",
        ///           "poQuantity": 0,
        ///           "orderUnit": "M3",
        ///           "sdDocument": "",
        ///           "wbs": "",
        ///           "cummulativeQuantity": "0",
        ///           "plant": ""
        ///         }
        ///       ],
        ///       "draw": 1,
        ///       "recordsTotal": 1,
        ///       "recordsFiltered": 1
        ///     }
        /// 
        /// </remarks>
        [HttpPost("GetDetail")]
        public async Task<IActionResult> GetPurchaseDetailOrder([FromBody] DetailPOViewModel vm)
        {

            //Check danh sách ID Purchase Order
            if (!vm.PurchaseOrderIds.Any()) return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.REQUIRED_ITEM });

            //Danh sách chi tiết PO
            var poDetails = await _context.PurchaseOrderMasterModel.Where(x => vm.PurchaseOrderIds.Contains(x.PurchaseOrderId)).AsNoTracking().ToListAsync();

            //Check danh sách PO Header tồn tại
            if (!poDetails.Any())
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Danh sách PO Header") });

            //Danh sách NCC
            var NCC = poDetails.Select(x => x.VendorNumber).ToList();

            //Check NCC của các item phải giống nhau
            for (int i = 0; i < NCC.Count(); i++)
            {
                for (int j = i + 1; j < NCC.Count(); j++)
                {
                    if (NCC[i] != NCC[j])
                        return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.CHECK_ITEM, "NCC") });
                }
            }

            //Danh sách PO detail theo header
            var queryByPO = _context.PurchaseOrderDetailModel
                .Where(x => (vm.PurchaseOrderIds.Contains(x.PurchaseOrderId)) &&
                            (x.DeletionInd != "L") &&
                            (x.DelivCompl != "X"))
                .OrderBy(x => x.PurchaseOrderCode)
                .ThenBy(x => x.POItemInt)
                .AsEnumerable();

            var response2 = new List<DetailPOResultViewModel>();


            foreach (var x in queryByPO)
            {
                var obj = new DetailPOResultViewModel
                {
                    //PODetail Id
                    PODetailId = x.PurchaseOrderDetailId,
                    //Số PO
                    POCode = x.PurchaseOrderCode,
                    //Dòng
                    POItem = x.POItem,
                    POItemNum = x.POItemInt,
                    //Mã material
                    MaterialCode = x.ProductCode,
                    //Tên hàng hóa
                    ProductName = $"{x.ShortText} | {x.BasicDataText}",
                    //Kho
                    StorageLocation = x.StorageLocation,
                    //Số lượng
                    POQuantity = x.POQuantity,
                    //Đơn vị tính
                    OrderUnit = x.OrderUnit,
                    //SO
                    SDDocument = x.SDDocument,
                    //SOLine
                    SOLine = x.Item,
                    //WBS
                    WBS = x.WBSElement,
                    //Nhà máy
                    Plant = x.Plant,
                    //Ngày giao hàng
                    DeliveryDate = x.DeliveryDate?.ToString("dd/MM/yyyy")
                };

                //try
                //{
                //Call SAP get số lượng đã nhập
                var querySAP = await _unitOfWork.SAPAPIRepository.SyncQuantityReceiveToSAP(new ZFM_MES_PURCHASE_ORDER
                {
                    EBELN = x.PurchaseOrderCode,
                    EBELP = x.POItem,
                    WERKS = CurrentUser?.SaleOrg
                });

                //Số lượng đã nhập kho SAP
                obj.CummulativeQuantity = querySAP?.ZFM_MES_PURCHASE_ORDERResponse?.LS_OUTPUT.LIST_DETAIL?.Sum(x => x.CUMULATIVE_QUANTITY).ToString();

                //Flag giao hàng
                obj.DeliveryCompleted = querySAP?.ZFM_MES_PURCHASE_ORDERResponse?.LS_OUTPUT.LIST_DETAIL?.FirstOrDefault()?.DELIV_COMPL;
                response2.Add(obj);
                //}
                //catch (Exception ex)
                //{
                //    throw;
                //}

            }

            var responseEnumerable = response2.AsEnumerable();

            var datatblModel = new DatatableViewModel
            {
                draw = vm.Paging.draw,
                start = vm.Paging.start,
                length = vm.Paging.length,
            };

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var response = CustomSearchRepository.CustomSearchFunc<DetailPOResultViewModel>(datatblModel, out filteredResultsCount, out totalResultsCount, responseEnumerable.AsQueryable(), "STT");

            if (response.Any())
            {
                int i = datatblModel.start;
                foreach (var item in response)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<DetailPOResultViewModel>>
            {
                Data = response,
                Draw = datatblModel.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        #region Save Barcode + Tạo QC
        /// <summary>API save Barcode</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// HttpPost
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/SaveBarcode
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///          {
        ///            "materialType": "string",
        ///            "vendorCode": "string",
        ///            "plant": "string",
        ///            "productCode": "string",
        ///            "poQuantity": 0,
        ///            "poQuantityUnit": "string",
        ///            "specifications": "string",
        ///            "unit": "string",
        ///            "quantityConversion": 0,
        ///            "quantityConversionUnit": "string",
        ///            "weight": 0,
        ///            "weightUnit": "string",
        ///            "manufacturingDate": "2022-09-10T09:31:22.603Z",
        ///            "expirationDate": "2022-09-10T09:31:22.603Z",
        ///            "poDetailQuantitys": [
        ///              {
        ///                "po": "string",
        ///                "poLine": "string",
        ///                "so": "string",
        ///                "soLine": "string",
        ///                "wbs": "string",
        ///                "quantity": 0
        ///              }
        ///            ]
        ///          }
        /// OUT PUT
        /// 
        ///          {
        ///              "code": 200,
        ///              "isSuccess": true,
        ///              "message": "Thêm mới barcode thành công",
        ///              "data": "2C2D0418-3C3D-484C-BA0E-38AC632296E4",
        ///              "additionalData": null
        ///          }
        /// </remarks>
        [HttpPost("SaveBarcode")]

        public async Task<IActionResult> SaveBarcode([FromBody] SaveBarCodeViewModel vm)
        {
            try
            {
                //Check số lượng nhập
                if (!vm.PODetailQuantitys.Any())
                    return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_RequierdFile, "Số lượng") });

                #region Tạo RawMaterialCardModel
                //Tạo mới thẻ treo pallet NVL
                var palletNVL = new RawMaterialCardModel
                {
                    RawMaterialCardId = Guid.NewGuid(),
                    //Loại NVL
                    RawMaterialCardType = vm.MaterialType, // Card type
                    //Mã NCC
                    VendorCode = vm.VendorCode,
                    //Store
                    Plant = vm.Plant,
                    //Mã hàng
                    ProductCode = vm.ProductCode,
                    //Số lượng đặt hàng và ĐVT 
                    POQuantity = vm.POQuantity,
                    POQuantityUnit = vm.POQuantityUnit,
                    //Số lượng và ĐVT
                    Unit = vm.Unit,
                    //Trọng lượng và ĐVT
                    Quantity2 = vm.Weight,
                    Quantity2Unit = vm.WeightUnit,
                    //Số lượng quy đổi và ĐVT (Chỉ có ở nvl chính)
                    Quantity3Unit = vm.MaterialType == MESP2Resource.MAIN_MATERIAL ? vm.QuantityConversionUnit : null,
                    Quantity3 = vm.MaterialType == MESP2Resource.MAIN_MATERIAL ? vm.QuantityConversion : null,
                    //Quy cách
                    Specifications = vm.Specifications,
                    //Ngày sản xuất (NVL hóa chất không có)
                    ManufacturingDate = vm.MaterialType != MESP2Resource.CHEMICAL_MATERIAL ? vm.ManufacturingDate : null,
                    //Ngày hết hạn (Chỉ nvl hóa chất có)
                    ExpirationDate = vm.MaterialType == MESP2Resource.CHEMICAL_MATERIAL ? vm.ExpirationDate : null,
                    //Trạng thái 
                    Actived = true,
                    //Tình trạng giao hàng
                    IsReceive = false,
                    //Thời gian tạo 
                    CreateTime = DateTime.Now,
                    //Người tạo 
                    CreateBy = CurrentUser?.AccountId
                };

                //Tạo thông tin barcode gồm thông tin: Id và loại nguyên vật liệu
                string barCode = $"<T1>{palletNVL.RawMaterialCardId}</T1>";

                //Create barcode
                var imageBC = _unitOfWork.UtilitiesRepository.GenerateQRCode("RawMaterialCardQRcode", palletNVL.RawMaterialCardId.ToString(), barCode);

                //Path barcode
                palletNVL.BarcodePath = imageBC;

                _context.RawMaterialCardModel.Add(palletNVL);

                await _context.SaveChangesAsync();

                //Save list PO Detail
                var listPODetail = new List<PurchaseOrderDetailModel>();

                //Check list PO Detail
                if (vm.PODetailQuantitys.Any())
                {
                    foreach (var x in vm.PODetailQuantitys)
                    {
                        //Search Detail PO từ PO Code và PO line
                        var purchaseOrder = await _context.PurchaseOrderDetailModel.FirstOrDefaultAsync(e => e.PurchaseOrderCode == x.PO && e.POItem == x.POLine);
                        _context.RawMaterial_PurchaseOrderDetail_Mapping.Add(new RawMaterial_PurchaseOrderDetail_Mapping
                        {
                            //ID
                            RawMaterialCardDetailId = Guid.NewGuid(),
                            //ID thẻ treo
                            RawMaterialCardId = palletNVL.RawMaterialCardId,
                            //PO Detail
                            PurchaseOrderDetailId = purchaseOrder.PurchaseOrderDetailId,
                            //Mã NVL
                            ProductCode = palletNVL.ProductCode,
                            //Số lượng ncc chuyển về ttf
                            Quantity = x.Quantity,
                            //Đơn vị tính
                            Unit = x.Unit
                        });
                    }
                }

                await _context.SaveChangesAsync();

                try
                {
                    // get list of RawMaterialCardModel where Plant is null then update Plant based on this
                    // Get the list of RawMaterialCardModel where Plant is null
                    var rawMaterialsToUpdate = (from rawMaterial in _context.RawMaterialCardModel
                                                join mappingRawDetail in _context.RawMaterial_PurchaseOrderDetail_Mapping on rawMaterial.RawMaterialCardId equals mappingRawDetail.RawMaterialCardId
                                                where rawMaterial.Plant == null
                                                select rawMaterial).ToList();

                    // For each RawMaterialCardModel in the list
                    foreach (var rawMaterial in rawMaterialsToUpdate)
                    {
                        // Get the corresponding Plant value from the database
                        var plantFromDb = (from mappingRawDetail in _context.RawMaterial_PurchaseOrderDetail_Mapping
                                           join podm in _context.PurchaseOrderDetailModel
                                             on mappingRawDetail.PurchaseOrderDetailId equals podm.PurchaseOrderDetailId
                                           where mappingRawDetail.RawMaterialCardId == rawMaterial.RawMaterialCardId
                                           select podm.Plant).FirstOrDefault();

                        // Update the Plant value
                        if (plantFromDb != null)
                        {
                            rawMaterial.Plant = plantFromDb;
                        }
                    }
                }
                catch (Exception ex)
                {
                }

                await _context.SaveChangesAsync();
                #endregion

                #region Tạo QC từ RawMateialCard
                var qc = _unitOfWork.QualityControlRepository.CreateQualityControlFromPalletNVL(new QualityControlPalletNVLCreateViewModel { PalletNVLId = palletNVL.RawMaterialCardId });
                #endregion



                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = palletNVL.RawMaterialCardId,
                    Message = string.Format(CommonResource.Msg_Create_Succes, LanguageResource.Barcode)
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }
        #endregion

        #region Save Barcode Manual
        /// <summary>API save Barcode Manual</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// HttpPost
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/SaveBarcode
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///          {
        ///            "productCode": "string",
        ///            "batch": "string",
        ///          }
        /// OUT PUT
        /// 
        ///          {
        ///              "code": 200,
        ///              "isSuccess": true,
        ///              "message": "Thêm mới barcode thành công",
        ///              "data": "2C2D0418-3C3D-484C-BA0E-38AC632296E4",
        ///              "additionalData": null
        ///          }
        /// </remarks>
        [HttpPost("SaveBarcodeManual")]

        public async Task<IActionResult> SaveBarcodeManual([FromBody] SaveBarcodeManualRequest request)
        {
            try
            {
                var manualRawMaterialCard = new RawMaterialCardManualModel
                {
                    RawMaterialCardManualId = Guid.NewGuid(),
                    ProductCode = request.ProductCode,
                    Batch = request.Batch,
                    Actived = true,
                    CreateTime = DateTime.Now,
                    CreateBy = CurrentUser?.AccountId
                };

                //Tạo thông tin barcode gồm thông tin
                string barCode = $"<T1>{manualRawMaterialCard.RawMaterialCardManualId}</T1>" +
                                 $"<T2>{manualRawMaterialCard.ProductCode}</T2>\"" +
                                 $"<T3>{manualRawMaterialCard.Batch}</T3>\"";

                //Create barcode
                string imageBC = _unitOfWork.UtilitiesRepository.GenerateQRCode("RawMaterialCardQRcode", manualRawMaterialCard.RawMaterialCardManualId.ToString(), barCode);

                //Path barcode
                manualRawMaterialCard.BarcodePath = imageBC;

                var response = new GetBarcodeManualResponse
                {
                    //ID
                    RawMaterialCardManualId = manualRawMaterialCard.RawMaterialCardManualId,
                    //Product
                    ProductCode = request.ProductCode,
                    ProductName = _context.ProductModel
                                            .Where(x => x.ProductCode == request.ProductCode)
                                            .OrderByDescending(x => x.CreateTime)
                                            .FirstOrDefault(x => x.ProductCode == request.ProductCode)?.ProductName,
                    //Số lô
                    Batch = request.Batch,
                    //Image barcode
                    BarcodePath = imageBC
                };

                //Chọn sloc thì get SO/SOLine|WBS và LSXDT
                if (!string.IsNullOrEmpty(request.Sloc))
                {

                    //Call SAP get danh sách SO/SOLine
                    var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                    {
                        //SLOC
                        LGORT = request.Sloc,
                        //Mã NVL
                        MATNR = request.ProductCode,
                        //Nhà máy
                        WERKS = CurrentUser?.SaleOrg,
                        //Số lô
                        CHARG = request.Batch
                    });

                    var responseToSAP = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.LABST > 0);
                    if (responseToSAP.Any())
                    {
                        foreach (var x in responseToSAP)
                        {

                            //SSNum có 14 kí tự, kí tự thứ 11 là dấu / và kí tự thứ 12 là khoảng trắng => SO/ SOLine
                            if (x.SSNUM.Length == 14 && x.SSNUM.IndexOf("/") == 10 && x.SSNUM.IndexOf(" ") == 11)
                            {
                                //Cắt dấu /
                                var SOSOLine = x.SSNUM.Trim().Split("/");

                                //SO
                                var so = SOSOLine[0].Trim();

                                //SO Line
                                var soLine = SOSOLine[1].Trim();

                                //Get soSoLine
                                ZST_MES_SOLINE[] soSoLines = new ZST_MES_SOLINE[]
                                {
                                    new ZST_MES_SOLINE
                                    {
                                        //SO
                                        VBELN = so,
                                        //SOLine
                                        POSNR = soLine
                                    }
                                };

                                response.ListSOWBS.Add(new SOWBSBarcodeManual
                                {
                                    SO = so,
                                    SOLine = soLine,
                                    WBS = null
                                });

                                //Get lsx đại trà
                                var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                                {
                                    //SO và SOLine
                                    SOLINES = soSoLines,
                                    WERKS = CurrentUser?.SaleOrg
                                });

                                if (!string.IsNullOrEmpty(so))
                                {
                                    var LSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse?.LIST_LSXDT?.FirstOrDefault()?.ZZLSX;
                                    response.LSXDT.Add(LSXDT);
                                }
                            }
                            else if (string.IsNullOrEmpty(x.SSNUM))
                            {

                                response.ListSOWBS.Add(new SOWBSBarcodeManual // 1. Thêm dòng tồn trơn vào danh sách
                                {
                                    WBS = null,
                                    SO = null,
                                    SOLine = null
                                });
                                response.LSXDT.Add("");
                            }
                            else
                            {
                                //Get soSoLine
                                ZST_MES_PROJN[] WBS = new ZST_MES_PROJN[] // // 2. Thêm dòng SO/SO line vào danh sách
                                {
                                    new ZST_MES_PROJN
                                    {
                                        //WBS
                                        PROJN = x.SSNUM
                                    }
                                };

                                response.ListSOWBS.Add(new SOWBSBarcodeManual
                                {
                                    WBS = x.SSNUM
                                });

                                //Get lsx đại trà
                                var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                                {
                                    //SO và SOLine
                                    PROJN = WBS,
                                    WERKS = CurrentUser?.SaleOrg
                                });

                                var LSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse?.LIST_LSXDT?.FirstOrDefault()?.ZZLSX;
                                response.LSXDT.Add(LSXDT);
                            }
                        }
                    }
                }

                _context.RawMaterialCardManualModel.Add(manualRawMaterialCard);

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Create_Succes, LanguageResource.Barcode)
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        /// <summary>
        /// Get barcode manual
        /// </summary>
        /// <param name="RawMaterialCardManualId"></param>
        /// <returns></returns>
        [HttpGet("GetBarcodeManual")]
        public async Task<IActionResult> GetBarcodeManual([FromQuery] Guid RawMaterialCardManualId)
        {
            //Thẻ treo nvl manual
            var rawPalletNVL = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == RawMaterialCardManualId);
            if (rawPalletNVL is null)
                return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });
            var response = new
            {
                Id = RawMaterialCardManualId,
                ProductCode = rawPalletNVL.ProductCode,
                ProductName = _context.ProductModel.FirstOrDefault(x => x.ProductCode == rawPalletNVL.ProductCode)?.ProductName,
                Batch = rawPalletNVL.Batch,
                BarcodePath = rawPalletNVL.BarcodePath
            };

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = response,
            });
        }
        #endregion

        #region Get Barcode
        /// <summary>API Get Barcode</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/GetBarcode?RawMaterialCardId=D58D7300-1D35-4DDC-8F80-34A75C9C9668
        ///     Params: 
        ///             + version : 2
        /// OUT PUT
        /// 
        ///          {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": {
        ///              "materialType": "1",
        ///              "vendorCode": "0010000449",
        ///              "vendorName": "CTY CP CÔNG NGHIỆP VÀ TM LIDOVIT",
        ///              "plant": "1000",
        ///              "productCode": "230017974",
        ///              "productName": "Gỗ Solid Tràm",
        ///              "poQuantity": 200,                 //Số lượng đặt hàng
        ///              "poQuantityUnit": "M3",            
        ///              "specifications": null,
        ///              "quantity": 60,                    //Số lượng đã nhập kho SAP
        ///              "sumQuantityReceived": 90,         //Số lượng giao hàng
        ///              "sumQuantityImported": 40,         //Số lượng đã nhập kho (Với barcode đã nhập kho)
        ///              "unit": "M3",
        ///              "quantity2": null,                 //Trọng lượng
        ///              "quantity2Unit": "Kgs",
        ///              "quantity3": null,                 //Số lượng quy đổi
        ///              "quantity3Unit": "",
        ///              "manufacturingDate": null,
        ///              "manufacturingDateStr": null,
        ///              "expirationDate": null,
        ///              "expirationDateStr": null,
        ///              "barcodePath": "Upload/RawMaterialCardQRcode/202209/2022-09-13T09-03-028fa093cd-25c5-457b-9051-0220e9d1e60a.png",
        ///              "createTime": "2022-09-13T09:03:02.627",
        ///              "createTimeStr": "13-09-2022",
        ///              "createBy": null,
        ///              "isReceive": 1,
        ///              "poDetailResponses": [
        ///                {
        ///                  "po": "4100002087",
        ///                  "poLine": "10",
        ///                  "so": "2100000179",
        ///                  "soLine": "20",
        ///                  "wbs": "",
        ///                  "quantityImported": 20,
        ///                  "quantity": 20,
        ///                  "quantityByPO": 100,
        ///                  "quantityReceived": 30,
        ///                  "remainQuantity": 70,
        ///                  "unit": "M3"
        ///                },
        ///                {
        ///                  "po": "4100002087",
        ///                  "poLine": "20",
        ///                  "so": "2100000179",
        ///                  "soLine": "30",
        ///                  "wbs": "",
        ///                  "quantityImported": 20,
        ///                  "quantity": 40,
        ///                  "quantityByPO": 100,
        ///                  "quantityReceived": 60,
        ///                  "remainQuantity": 40,
        ///                  "unit": "M3"
        ///                }
        ///              ]
        ///            },
        ///            "additionalData": null
        ///          }
        /// </remarks>
        [HttpGet("GetBarcode")]
        public async Task<IActionResult> GetBarcode([FromQuery] Guid? RawMaterialCardId, int? RawMaterialCardCode)
        {

            try
            {
                var response = new RawMarterialResponse();

                var palletNVL = new RawMaterialCardModel();

                if (RawMaterialCardId.HasValue)
                {
                    //Get thẻ treo pallet NVL
                    palletNVL = await _context.RawMaterialCardModel.Where(x => x.RawMaterialCardId == RawMaterialCardId)
                                                                       .Include(x => x.RawMaterial_PurchaseOrderDetail_Mapping)
                                                                       .ThenInclude(x => x.PurchaseOrderDetail)
                                                                       .FirstOrDefaultAsync();
                    if (palletNVL is null)
                    {
                        //Check thẻ treo manual
                        palletNVL = await _context.RawMaterialCardManualModel.Where(x => x.RawMaterialCardManualId == RawMaterialCardId)
                                                                       .Select(x => new RawMaterialCardModel
                                                                       {
                                                                           ProductCode = x.ProductCode
                                                                       }).FirstOrDefaultAsync();

                        if (string.IsNullOrEmpty(palletNVL?.ProductCode))
                            return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });
                    }
                }
                else
                {
                    //Get thẻ treo pallet NVL
                    palletNVL = await _context.RawMaterialCardModel.Where(x => x.RawMaterialCardCode == RawMaterialCardCode)
                                                                       .Include(x => x.RawMaterial_PurchaseOrderDetail_Mapping)
                                                                       .ThenInclude(x => x.PurchaseOrderDetail)
                                                                       .FirstOrDefaultAsync();
                }




                if (palletNVL == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });

                //Id
                var rawMaterialCardId = palletNVL?.RawMaterialCardId;
                //var rawMaterialCardId = RawMaterialCardId;

                response.RawMaterialCardId = rawMaterialCardId;
                response.RawMaterialCardCode = palletNVL?.RawMaterialCardCode;

                //Loại NVL
                response.MaterialType = palletNVL?.RawMaterialCardType;

                //Mã NCC
                var vendorCode = palletNVL?.VendorCode;
                response.VendorCode = vendorCode;
                //Tên NCC
                response.VendorName = _context.VendorModel.FirstOrDefault(x => x.SupplierNumber == vendorCode)?.LongName;
                //Store
                response.Plant = palletNVL?.Plant;


                //List id PO Detail
                var poRawDetails = _context.RawMaterial_PurchaseOrderDetail_Mapping.Where(x => x.RawMaterialCardId == rawMaterialCardId)
                                                                                  .Include(x => x.PurchaseOrderDetail)
                                                                                  .AsNoTracking()
                                                                                  .ToList();

                //List SO, SOLine
                var listSOSOline = poRawDetails.Where(x => (x.PurchaseOrderDetail == null ? true : (!string.IsNullOrEmpty(x.PurchaseOrderDetail.SDDocument)) && !string.IsNullOrEmpty(x.PurchaseOrderDetail.Item)))
                                                .Select(x => new ZST_MES_SOLINE
                                                {
                                                    //SO
                                                    VBELN = x.PurchaseOrderDetail == null ? null : x.PurchaseOrderDetail.SDDocument,
                                                    //SO Line
                                                    POSNR = x.PurchaseOrderDetail == null ? null : x.PurchaseOrderDetail.Item,
                                                }).ToArray();

                //List WBS
                var listWBS = poRawDetails.Where(x => x.PurchaseOrderDetail == null ? true : !string.IsNullOrEmpty(x.PurchaseOrderDetail.WBSElement)).Select(x => new ZST_MES_PROJN
                {
                    //SO
                    PROJN = x.PurchaseOrderDetail == null ? null : x.PurchaseOrderDetail.WBSElement,
                }).ToArray();

                var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                {
                    //SO và SOLine
                    SOLINES = listSOSOline,
                    PROJN = listWBS,
                    WERKS = CurrentUser?.SaleOrg
                });

                var listLSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse.LIST_LSXDT;


                //LSXĐT của SO/SOLine
                foreach (var item in listSOSOline)
                {
                    var lsxdtSOSOLine = listLSXDT.Where(k => k.VBELN == item.VBELN && k.POSNR == item.POSNR).Select(x => x.ZZLSX).ToList();
                    response.ListLSXDT.AddRange(lsxdtSOSOLine);
                }

                //LSXĐT của WBS
                foreach (var item in listWBS)
                {
                    var lsxdtWBS = listLSXDT.Where(k => k.PROJN == item.PROJN).Select(x => x.ZZLSX).ToList();
                    response.ListLSXDT.AddRange(lsxdtWBS);
                }

                //Thông tin đã nhập kho của thẻ treo              

                var wTs = await _context.WarehouseTransactionModel.Where(x => x.ReferenceDocumentId == palletNVL.RawMaterialCardId && x.MovementType == MovementType.Receive).ToListAsync();

                //Với thẻ treo đã nhập khp => GET thông tin đã nhập kho
                if (palletNVL.IsReceive == true)
                {
                    if (wTs.Any())
                    {
                        //Thông tin nhập kho
                        var wT = wTs.FirstOrDefault();

                        //Kho
                        var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == wT.SlocId);
                        //Vị trị kho
                        var storageBin = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == wT.StorageBinId);

                        //Thông tin nhập kho
                        response.InforReceived = new InforBarcodeReceived
                        {
                            //Số lô
                            BatchNumber = wT?.Batch,
                            //Nhà máy
                            Plant = sloc?.Plant,
                            //Kho
                            Sloc = sloc?.Sloc,
                            WarehouseNo = sloc?.Sloc,
                            //Vị trí kho
                            StorageBin = storageBin?.StorageBin
                        };
                    }
                }

                var poQuantityByPOs = new List<GetQuantityByPOResponse>();
                foreach (var x in poRawDetails)
                {
                    //Số lượng đặt hàng, giao hàng theo PO
                    var poQuantity = new GetQuantityByPOResponse
                    {
                        //PO và POLine
                        PO = x.PurchaseOrderDetail?.PurchaseOrderCode,
                        POLine = x.PurchaseOrderDetail?.POItem,
                        //SO và SOLine
                        SO = x.PurchaseOrderDetail?.SDDocument,
                        SOLine = x.PurchaseOrderDetail?.Item == "0" ? null : x.PurchaseOrderDetail?.Item,
                        //WBS
                        WBS = x.PurchaseOrderDetail?.WBSElement,
                        //Số lượng đặt hàng
                        QuantityByPO = x.PurchaseOrderDetail?.POQuantity,
                        Unit = x.PurchaseOrderDetail?.OrderUnit
                    };

                    //Call SAP get số lượng đã nhập
                    var querySAP = await _unitOfWork.SAPAPIRepository.SyncQuantityReceiveToSAP(new ZFM_MES_PURCHASE_ORDER
                    {
                        EBELN = x.PurchaseOrderDetail?.PurchaseOrderCode,
                        EBELP = x.PurchaseOrderDetail?.POItem,
                        WERKS = CurrentUser?.SaleOrg
                    });
                    //SO và SOLine null thì không có WBS và ngược lại
                    if (!string.IsNullOrEmpty(poQuantity.SO))
                    {
                        //LSX đại trà
                        poQuantity.LSXDT = listLSXDT.Where(k => k.VBELN == poQuantity.SO && k.POSNR == poQuantity.SOLine).FirstOrDefault()?.ZZLSX;
                    }
                    if (!string.IsNullOrEmpty(poQuantity.WBS))
                    {
                        //LSX đại trà
                        poQuantity.LSXDT = listLSXDT.Where(k => k.PROJN == poQuantity.WBS).FirstOrDefault()?.ZZLSX;
                    }


                    //Số lượng giao từ NCC tới TTF
                    poQuantity.Quantity = x.Quantity;

                    //Số lượng đã nhập kho SAP
                    poQuantity.QuantityReceived = querySAP.ZFM_MES_PURCHASE_ORDERResponse.LS_OUTPUT.LIST_DETAIL.Sum(x => x.CUMULATIVE_QUANTITY);

                    //Số lượng còn lại
                    poQuantity.RemainQuantity = poQuantity.QuantityByPO - poQuantity.QuantityReceived;

                    //Với thẻ treo đã nhập kho trả ra số lượng đã nhập ở màn hình nhập kho
                    poQuantity.QuantityImported = wTs.FirstOrDefault(e => e.PO == x.PurchaseOrderDetail.PurchaseOrderCode)?.Quantity;

                    poQuantityByPOs.Add(poQuantity);
                }

                poQuantityByPOs = poQuantityByPOs.OrderBy(p => p.PO).ThenBy(p => p.POLine).ToList();

                //Danh sách số lượng theo PO và POLine
                response.PODetailResponses = poQuantityByPOs;

                //Mã hàng
                var productCode = palletNVL?.ProductCode;
                response.ProductCode = productCode;

                //Tên hàng
                response.ProductName = $"{poRawDetails?.FirstOrDefault()?.PurchaseOrderDetail?.ShortText} | " +
                                        $"{poRawDetails?.FirstOrDefault()?.PurchaseOrderDetail?.BasicDataText}";

                if (response.ProductName == " | ")
                {
                    var latestProduct = _context.ProductLatestModel.FirstOrDefault(p => p.ProductCode == productCode);
                    if (latestProduct != null)
                    {
                        response.ProductName = $"{latestProduct.ProductCode} | {latestProduct.ProductName}";
                    }
                }

                //Số lượng đặt hàng và ĐVT
                response.POQuantity = palletNVL?.POQuantity;
                response.POQuantityUnit = palletNVL?.POQuantityUnit;

                //Số lượng giao hàng
                response.Quantity = palletNVL?.RawMaterial_PurchaseOrderDetail_Mapping.Sum(x => x.Quantity);

                //Số lượng đã nhập kho SAP
                response.SumQuantityReceived = poQuantityByPOs.Sum(x => x.QuantityReceived);

                //Số lượng đã nhập kho ở màn hình nhập kho 
                response.SumQuantityImported = poQuantityByPOs.Sum(x => x.QuantityImported);

                response.Unit = palletNVL?.Unit;

                //Trọng lượng
                response.Quantity2 = palletNVL?.Quantity2;
                response.Quantity2Unit = palletNVL?.Quantity2Unit;

                //Quy cách
                response.Specifications = palletNVL?.Quantity2.ToString() ?? palletNVL?.Specifications;

                //Số lượng quy đổi
                response.Quantity3 = palletNVL.Quantity3 ?? null;
                response.Quantity3Unit = palletNVL.Quantity3Unit ?? null;

                //Ngày sản xuất
                response.ManufacturingDate = palletNVL?.ManufacturingDate;

                //Ngày hết hạn
                response.ExpirationDate = palletNVL.ExpirationDate;

                //Path barcode
                response.BarcodePath = palletNVL?.BarcodePath;

                //Thời gian tạo 
                response.CreateTime = palletNVL?.CreateTime;
                //Người tạo 
                var createBy = palletNVL?.CreateBy;
                response.CreateBy = _context.AccountModel.FirstOrDefault(x => x.AccountId == createBy)?.FullName;

                //Trạng thái nhập kho (1 đã nhập, 0 chưa nhập)
                response.IsReceive = palletNVL?.IsReceive == true ? 1 : 0;

                //Ngày về hàng
                response.IsGoodsArrive = palletNVL?.isGoodsArrive != true ? false : true;
                response.GoodsArriveDate = palletNVL?.GoodsArriveDate;

                //Plant
                var plant = CurrentUser?.SaleOrg;
                //Ngày hiện tại
                var dateNow = DateTime.Now;
                var yearNow = DateTime.Now.Year.ToString();

                //Danh sách phiếu nhập kho
                var goodsReceivedNoteNames = await _context.ReceiveInformationModel.Where(x => x.YearReceive == yearNow &&
                                                                                     x.ReceiveDate.Value.Date == dateNow.Date &&
                                                                                     x.Plant == plant)
                                                                         .Select(x => x.GoodsReceivedNote)
                                                                         .ToListAsync();

                response.GoodsReceivedNotes = goodsReceivedNoteNames;


                var latestValidTdsNumber = _context.ReceiveInformationModel
                                                        .Where(r => !string.IsNullOrEmpty(r.TdsNumber))
                                                        .OrderByDescending(r => r.ReceiveDate)  // Assuming ReceiveDate indicates when TdsNumber was set
                                                        .Select(r => r.TdsNumber)
                                                        .FirstOrDefault();

                response.LatestTDS = latestValidTdsNumber;

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        #endregion

        [AllowAnonymous]
        [HttpGet("GetSoToKhai")]
        public IActionResult GetSoToKhai([FromQuery] string SoToKhai)
        {
            try
            {
                //return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });

                List<string> soToKhais = new List<string>();

                soToKhais = _context.SoToKhaiModel.Where(x => x.Import == true && x.SoToKhai.Contains(SoToKhai))
                                                  .Distinct().Take(20)
                                                  .Select(s => s.SoToKhai)
                                                  .ToList();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = soToKhais
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        #region GET Thông tin thẻ treo cần tạo theo PO Item đã chọn

        /// <summary>API GET Thông tin thẻ treo cần tạo theo PO Item đã chọn</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/PurchaseOrder/GetListItemPO
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///          [
        ///             "BE81D2CB-BCFF-4FCD-886C-0272C2D00B61","50CB2F28-C9DA-4EB4-8222-0141CD7C3175"
        ///          ]    
        /// OUT PUT
        /// 
        ///         {
        ///             "code": 200,
        ///             "isSuccess": true,
        ///             "message": null,
        ///             "data": {
        ///               "vendorName": "CÔNG TY TNHH VĂN NĂM",
        ///               "poDetails": [
        ///                    {
        ///                      "item": "4100002080/20"
        ///                    },
        ///                    {
        ///                      "item": "4100002080/10"
        ///                    }
        ///                 ],
        ///               "productName": null,
        ///               "poQuantity": 400000,
        ///               "poQuantityUnit": "KG",
        ///               "soDetails": [
        ///                     {
        ///                         "item": "2250000318/10"
        ///                     },
        ///                     {
        ///                         "item": "2250000318/10"
        ///                     }
        ///                 ],
        ///               "wbsDetails": [
        ///                     {
        ///                         "item": "0"
        ///                     },
        ///                     {
        ///                         "item": "0"
        ///                     }
        ///                 ]
        ///             },
        ///             "additionalData": null
        ///         }
        /// </remarks>
        [HttpPost("GetListItemPO")]
        public async Task<IActionResult> GetListItemPO([FromBody] List<Guid> PODetailIds)
        {
            try
            {
                //Check danh sách ID Purchase Order
                if (!PODetailIds.Any())
                {
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.REQUIRED_ITEM });
                }

                //Danh sách chi tiết PO
                var poDetails = await _context.PurchaseOrderDetailModel.Where(x => PODetailIds.Contains(x.PurchaseOrderDetailId))
                                                                       .OrderBy(x => x.PurchaseOrderCode)
                                                                       .ThenBy(x => x.POItemInt)
                                                                       .AsNoTracking()
                                                                       .ToListAsync();

                //Check danh sách Material và Vendor
                if (poDetails.Any())
                {
                    //Danh sách Material
                    var materials = poDetails.Select(x => x.Material).ToList();

                    //Check PurcharseOrderCode của các item phải giống nhau
                    for (int i = 0; i < materials.Count(); i++)
                    {
                        for (int j = i + 1; j < materials.Count(); j++)
                        {
                            if (materials[i] != materials[j])
                            {
                                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.CHECK_ITEM, "Material") });
                            }
                        }
                    }
                }

                //Response
                var response = new GetPODetailResponse();

                //Purchase Order
                var purchaseOrder = await _context.PurchaseOrderMasterModel.FirstOrDefaultAsync(x => x.PurchaseOrderId == poDetails.Select(x => x.PurchaseOrderId).FirstOrDefault());

                //Tên nhà cung cấp
                response.VendorName = _context.VendorModel.FirstOrDefault(x => x.SupplierNumber == purchaseOrder.VendorNumber)?.LongName;
                response.VendorCode = purchaseOrder.VendorNumber;

                //Sản phẩm
                var productCode = poDetails.FirstOrDefault().ProductCode;
                response.ProductCode = productCode;
                response.ProductName = $"{poDetails.FirstOrDefault().ShortText} | {poDetails.FirstOrDefault().BasicDataText}";


                //Chi tiết PO
                var poQuantityByPOs = new List<PODetailResponse>();
                var poDetailViews = new List<PODetailView>();
                var soDetailViews = new List<SODetailView>();
                var wbsDetailViews = new List<WBSDetailView>();
                foreach (var x in poDetails)
                {
                    //Số lượng đặt hàng, giao hàng theo PO
                    var poQuantity = new PODetailResponse
                    {
                        //PO và POLine
                        PO = x.PurchaseOrderCode,
                        POLine = x.POItem,
                        //SO và SOLine
                        SO = x.SDDocument,
                        SOLine = x.Item == "0" ? null : x.Item,
                        //WBS
                        WBS = x.WBSElement,
                        //Số lượng đặt hàng
                        POQuantity = x.POQuantity,
                        Unit = x.OrderUnit,
                        //Ngày giao hàng
                        DeliveryDate = x.DeliveryDate?.ToString("dd/MM/yyyy")
                    };

                    //Danh sách PO
                    var poDetail = new PODetailView()
                    {
                        PO = x.PurchaseOrderCode,
                        POLine = x.POItem
                    };
                    poDetailViews.Add(poDetail);

                    //Danh sách SO
                    var SoDetail = new SODetailView()
                    {
                        SO = x.SDDocument,
                        SOLine = x.Item == "0" ? null : x.Item
                    };
                    soDetailViews.Add(SoDetail);

                    //Danh sách WBS
                    var wbsDetail = new WBSDetailView()
                    {
                        WBS = x.WBSElement
                    };
                    wbsDetailViews.Add(wbsDetail);

                    //Call SAP get số lượng đã nhập
                    var querySAP = await _unitOfWork.SAPAPIRepository.SyncQuantityReceiveToSAP(new ZFM_MES_PURCHASE_ORDER
                    {
                        EBELN = x.PurchaseOrderCode,
                        EBELP = x.POItem,
                        WERKS = CurrentUser?.SaleOrg
                    });

                    //Số lượng đã nhập kho
                    poQuantity.QuantityReceived = querySAP.ZFM_MES_PURCHASE_ORDERResponse.LS_OUTPUT.LIST_DETAIL.Sum(x => x.CUMULATIVE_QUANTITY);

                    //Số lượng còn lại
                    poQuantity.RemainQuantity = poQuantity.POQuantity - poQuantity.QuantityReceived;

                    //Flag giao hàng
                    poQuantity.DeliveryCompleted = querySAP.ZFM_MES_PURCHASE_ORDERResponse.LS_OUTPUT.LIST_DETAIL.FirstOrDefault()?.DELIV_COMPL;

                    poQuantityByPOs.Add(poQuantity);
                }
                response.PurchaseOrderDetails = poQuantityByPOs;
                response.poDetails = poDetailViews;
                response.soDetails = soDetailViews;
                response.wbsDetails = wbsDetailViews;

                //Số lượng đặt hàng và ĐVT
                response.POQuantityUnit = poDetails.Select(x => x.OrderUnit).FirstOrDefault();



                //Số lượng đã nhập kho
                response.ReceiveQuantity = poQuantityByPOs.Sum(x => x.QuantityReceived);

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }
        #endregion     
    }
}