import 'package:ttf/model/postFilterWareHouseTranfer.dart';
import 'listWarehouseTranfer.dart';

class DrawerFilterTranferMaterial{
  PostFilterWareHouseTranfer? postFilterWareHouseTranfer;
  List<DataListWarehouseTranfer>? lsDataWarehouseTranfe;
  int? start;
  int? draw;
  int? length;
  bool? resetList;
  DrawerFilterTranferMaterial({
    this.lsDataWarehouseTranfe,
    this.postFilterWareHouseTranfer,
    this.start,
    this.draw,
    this.length,
    this.resetList
  });
}