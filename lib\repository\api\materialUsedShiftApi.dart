import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/materialUsedShift.dart';
import '../../urlApi/urlApi.dart';

class MaterialUsedShiftApi {
  static Future<http.Response> postMaterialUsedShift(MaterialUsedShift materialUsedShift, String token) async {
    final dataPost = jsonEncode(materialUsedShift);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "MaterialUsedShift");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
