import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../element/FillterListQCNVL.dart';
import '../../element/errorViewPost.dart';
import '../../element/timeOut.dart';
import '../../model/drawerFilterListQCNVL.dart';
import '../../model/getLstQCNVLByFilter.dart';
import '../../model/getStatusGoodsArrive.dart';
import '../../model/qrCodePageListQCModel.dart';
import '../../model/userModel.dart';
import '../../repository/function/endDrawerListQCNVLFunction.dart';
import '../../repository/function/listQcFunction.dart';
import '../../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../../utils/ui_helpers.dart';
import '../LostConnect.dart';
import 'package:flutter/foundation.dart';

class BaoCaoDauVao2 extends StatefulWidget {
  final DataUser userModel;
  final String dateTimeOld;
  const BaoCaoDauVao2({Key? key, required this.userModel, required this.dateTimeOld}) : super(key: key);

  @override
  _BaoCaoDauVao2State createState() => _BaoCaoDauVao2State();
}

class _BaoCaoDauVao2State extends State<BaoCaoDauVao2> {
  List<DataLstQCNVLByFilter> _lsDataLstQCNVLByFilter = [];

  GetLstQCNVLByFilter? _getLstQCNVLByFilter;
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _listNoMore = false;
  final _controllerListView = ScrollController();
  bool _isLoadingScroll = false;
  int _pageNumber = 1;
  final int _pageSize = 5;
  late bool _timeOut;
  bool? _disableButton;
  String _error = "";
  List<DataGetStatusGoodsArrive> _lsDataGetStatusGoodArrive = [];
  String title = "Danh sách nghiệm thu đầu vào 2";

  @override
  void initState() {
    super.initState();

    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    } else {
      setState(() {
        _timeOut = false;
      });
      _postListQCApi();
      _controllerListView.addListener(_goScroll);
    }
  }

  Future<void> _checkAuth() async {}

  Future<void> _postListQCApi() async {
    try {
      setState(() {
        _timeOut = false;
        _isLoading = true;
        _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: true);
      });
      final dataResponse = await Future.wait([
        ListQCFunction.postLstQCNVLByFilter2(
          _getLstQCNVLByFilter ?? GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: true),
          widget.userModel.token.toString(),
        ),

        // GoodsArrive	GR1 Đang giao hàng
        // GoodsArrive	GR2	Hàng đã về nhà máy
        EndDrawerListQCNVLFunction.fetchStatusGoodsArrive(widget.userModel.token.toString())
      ]);
      // final dataListQC = await ListQCFunction.postLstQCNVLByFilter(_getLstQCNVLByFilter ?? GetLstQCNVLByFilter(
      //     pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false), widget.token,_isCheckTest);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataResponse.isNotEmpty) {
        if ((dataResponse[0] as List<DataLstQCNVLByFilter>?) != null) {
          setState(() {
            _pageNumber += 1;
            _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false);
            _lsDataLstQCNVLByFilter = dataResponse[0] as List<DataLstQCNVLByFilter>? ?? [];
            _lsDataLstQCNVLByFilter.sort((a, b) {
              return b.barcodeCreateDate!.compareTo(a.barcodeCreateDate!);
            });
          });
        }
        _lsDataGetStatusGoodArrive = dataResponse[1] as List<DataGetStatusGoodsArrive>;
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _timeOut = false;
        _error = error.toString();
      });
    }
  }

  void _goScroll() {
    if (_controllerListView.position.maxScrollExtent == _controllerListView.offset && _isLoadingScroll == false) {
      if (_listNoMore == false) {
        _fetchScrollListView();
      }
    }
  }

  Future<void> _refresh() async {
    setState(() {
      _pageNumber = 1;
      _isLoading = true;
      _getLstQCNVLByFilter!.pageNumber = _pageNumber;
      _getLstQCNVLByFilter!.pageSize = _lsDataLstQCNVLByFilter.length;
    });
    final data = await ListQCFunction.postLstQCNVLByFilter(_getLstQCNVLByFilter!, widget.userModel.token.toString());
    if (!mounted) return;
    setState(() {
      _isLoading = false;
    });
    if (data != null) {
      setState(() {
        _pageNumber += 1;
        _getLstQCNVLByFilter!.pageNumber = _pageNumber;
        _getLstQCNVLByFilter!.pageSize = _pageSize;
        _lsDataLstQCNVLByFilter = data;
        _lsDataLstQCNVLByFilter.sort((a, b) {
          return b.barcodeCreateDate!.compareTo(a.barcodeCreateDate!);
        });
        // _listDayTimeQC = ListDayTimeQC(
        //     lsActualDeliveryDay: data.map((e) => e.actualDeliveryDate == null ? "":DateFormat("dd/MM/yyyy").format(e.actualDeliveryDate!)).toList(),
        //     lsDayBarcode: data.map((e) => e.barcodeCreateDate == null ? "":DateFormat("dd/MM/yyyy").format(e.barcodeCreateDate!)).toList(),
        //     lsTimeBarcode: data.map((e) => e.barcodeCreateDate == null ? "":DateFormat("HH:mm").format(e.barcodeCreateDate!)).toList(),
        //     lsDayQC: data.map((e) => e.qcCreateTime == null ? "":DateFormat("dd/MM/yyyy").format(e.qcCreateTime!)).toList(),
        // );
      });
    } else {
      _lsDataLstQCNVLByFilter = [];
      // _listDayTimeQC.lsActualDeliveryDay = [];
      // _listDayTimeQC.lsDayBarcode = [];
      // _listDayTimeQC.lsTimeBarcode = [];
      // _listDayTimeQC.lsDayQC = [];
    }
  }

  Future<void> _fetchScrollListView() async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = true;
      });
      final data = await ListQCFunction.postLstQCNVLByFilter(_getLstQCNVLByFilter!, widget.userModel.token.toString());
      if (!mounted) return;
      if (data != null) {
        setState(() {
          _pageNumber += 1;
          _getLstQCNVLByFilter!.pageNumber = _pageNumber;
          _isLoadingScroll = false;
          if (data.isEmpty) {
            _listNoMore = true;
          }
          _lsDataLstQCNVLByFilter.addAll(data);
          _lsDataLstQCNVLByFilter.sort((a, b) {
            return b.barcodeCreateDate!.compareTo(a.barcodeCreateDate!);
          });
          // _listDayTimeQC.lsActualDeliveryDay!.addAll(data.map((e) => e.actualDeliveryDate == null ? "":DateFormat("dd/MM/yyyy").format(e.actualDeliveryDate!)).toList());
          // _listDayTimeQC.lsDayQC!.addAll(data.map((e) => e.qcCreateTime == null ? "":DateFormat("dd/MM/yyyy").format(e.qcCreateTime!)).toList());
          // _listDayTimeQC.lsDayBarcode!.addAll(data.map((e) => e.barcodeCreateDate == null ? "":DateFormat("dd/MM/yyyy").format(e.barcodeCreateDate!)).toList());
          // _listDayTimeQC.lsTimeBarcode!.addAll(data.map((e) => e.barcodeCreateDate == null ? "":DateFormat("HH:mm").format(e.barcodeCreateDate!)).toList());
        });
        // debugPrint((_listDayTimeQC.lsActualDeliveryDay!.length == _lsDataLstQCNVLByFilter.length).toString());
        // debugPrint(_lsDataLstQCNVLByFilter.length.toString());
      } else {
        setState(() {
          _lsDataLstQCNVLByFilter = [];
          // _listDayTimeQC.lsActualDeliveryDay = [];
          // _listDayTimeQC.lsDayBarcode = [];
          // _listDayTimeQC.lsTimeBarcode = [];
          // _listDayTimeQC.lsDayQC = [];
          _isLoadingScroll = false;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = false;
        _lsDataLstQCNVLByFilter = [];
        // _listDayTimeQC.lsActualDeliveryDay = [];
        // _listDayTimeQC.lsDayBarcode = [];
        // _listDayTimeQC.lsTimeBarcode = [];
        // _listDayTimeQC.lsDayQC = [];
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = false;
        _lsDataLstQCNVLByFilter = [];
        // _listDayTimeQC.lsActualDeliveryDay = [];
        // _listDayTimeQC.lsDayBarcode = [];
        // _listDayTimeQC.lsTimeBarcode = [];
        // _listDayTimeQC.lsDayQC = [];
      });
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }

  @override
  void dispose() {
    _controllerListView.removeListener(_goScroll);
    _controllerListView.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
        : Scaffold(
            endDrawerEnableOpenDragGesture: false,
            backgroundColor: _lsDataLstQCNVLByFilter.isNotEmpty ? Colors.grey.shade100 : Colors.white,
            // key: _key,
            endDrawer: FillterListQCNVL(
              token: widget.userModel.token.toString(),
              onFilterSelected: (DrawerFilterListQCNVL filter) {
                setState(() {
                  _pageNumber = filter.getLstQCNVLByFilter!.pageNumber!;
                  _lsDataLstQCNVLByFilter = filter.lsDataLstQCNVLByFilter ?? [];
                  _getLstQCNVLByFilter = filter.getLstQCNVLByFilter!;
                  _listNoMore = false;
                  // _listDayTimeQC = ListDayTimeQC(
                  //     lsActualDeliveryDay: filter.lsDataLstQCNVLByFilter!.map((e) => e.actualDeliveryDate == null ? "":DateFormat("dd/MM/yyyy").format(e.actualDeliveryDate!)).toList(),
                  //     lsDayBarcode: filter.lsDataLstQCNVLByFilter!.map((e) => e.barcodeCreateDate == null ? "":DateFormat("dd/MM/yyyy").format(e.barcodeCreateDate!)).toList(),
                  //     lsTimeBarcode: filter.lsDataLstQCNVLByFilter!.map((e) => e.barcodeCreateDate == null ? "":DateFormat("HH:mm").format(e.barcodeCreateDate!)).toList(),
                  //     lsDayQC: filter.lsDataLstQCNVLByFilter!.map((e) => e.qcCreateTime == null ? "":DateFormat("dd/MM/yyyy").format(e.qcCreateTime!)).toList(),
                  // );
                });
              },
              lsDataGetStatusGoodArrive: _lsDataGetStatusGoodArrive,
            ),
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              actions: [
                IconButton(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  icon: Icon(
                    Icons.camera_alt,
                    size: 19.sp,
                    color: Colors.white,
                  ),
                  onPressed: _isLoading == true
                      ? null
                      : () async {
                          final check = await Navigator.pushNamed(
                            context,
                            "/QRCodePageListQC",
                          );
                          if (!mounted) return;
                          if (check == null) return;
                          if ((check as QRCodePageListQCModel).isScan == true) {
                            final checkSave = await Navigator.pushNamed(
                              context,
                              '/BaoCaoDauVaoDetail2',
                              arguments: ScreenArgumentNavigatorBar(
                                "",
                                widget.dateTimeOld,
                                check.qrCode.toString(),
                                "qr",
                                widget.userModel,
                              ),
                            );
                            if (!mounted) return;
                            if (checkSave == null) return;
                            if (checkSave == true) {
                              _refresh();
                            }
                          }
                        },
                ),
                Builder(
                  builder: (BuildContext context) {
                    return IconButton(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: Icon(Icons.search_outlined, size: 19.sp, color: Colors.white),
                      onPressed: () {
                        Scaffold.of(context).openEndDrawer();
                      },
                    );
                  },
                ),
              ],
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: _isNotWifi == true
                ? LostConnect(checkConnect: () => _postListQCApi())
                : _error != ""
                    ? ErrorViewPost(error: _error)
                    : _isLoading == true
                        ? _buildLoading()
                        : _buildDataFilteredWithHeader());
  }

  _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  _buildDataFilteredWithHeader() {
    List<Map<String, String>> dummyTheTreo = [
      {'PO': '4100041905', 'qualityControlId': '37a31264-cf28-41ab-a236-a39e63675776'},
      {'PO': '4100047120', 'qualityControlId': 'a59a1380-415e-4461-a903-1ea2647606f7'},
      {'PO': 'Test 330 282 312', 'qualityControlId': '26beedba-6405-4c57-91b4-3176f1d05552'},
      {'PO': 'Test 3 info', 'qualityControlId': 'cbb3d41b-0faf-4251-a7d4-68ed6352a0b0'},
      {'PO': 'Test 4', 'qualityControlId': '7c27a107-7c59-40a3-933d-05475b84657a'},
      // {'PO': '1', 'qualityControlId': 'fd8ecdf0-c2f9-4b8f-93b4-87837fe4743d'},

      // {'PO': '4100047120', 'qualityControlId': 'a59a1380-415e-4461-a903-1ea2647606f7'},
      // {'PO': 'Test 330 282 312', 'qualityControlId': '26beedba-6405-4c57-91b4-3176f1d05552'},
      // {'PO': 'Test 3 info', 'qualityControlId': 'cbb3d41b-0faf-4251-a7d4-68ed6352a0b0'},
      // {'PO': 'Test 4', 'qualityControlId': '7c27a107-7c59-40a3-933d-05475b84657a'},
      // {'PO': 'Dauvao error', 'qualityControlId': 'cef5ca8c-68f3-45f4-9d78-1b5ebf518875'},

      {'PO': 'not found', 'qualityControlId': 'c06ea4e8-da33-4bc3-b523-126d7a5cdde2'},
      {'PO': 'test', 'qualityControlId': '19330d8f-c323-44dc-8e5a-a64c20f1094e'},
      {'PO': '5000187672', 'qualityControlId': 'f1e6b3c7-250d-49ee-b28f-0cf3b6bac885'},
    ];

    return Column(
      children: <Widget>[
        kDebugMode
            ? RenderDebugButtons(dummyTheTreo, (item) {
                Navigator.pushNamed(
                  context,
                  '/BaoCaoDauVaoDetail2',
                  arguments: ScreenArgumentNavigatorBar(
                    item['qualityControlId']!,
                    widget.dateTimeOld,
                    "",
                    "normal",
                    widget.userModel,
                  ),
                );
              }, 'PO')

            // Wrap(
            //     alignment: WrapAlignment.start,
            //     runAlignment: WrapAlignment.start,
            //     spacing: 2.w,
            //     children: dummyTheTreo.map((item) {
            //       return ElevatedButton(
            //         style: ElevatedButton.styleFrom(
            //           padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
            //           minimumSize: const Size(0, 0), // Add this
            //         ),
            //         onPressed: () {
            //           Navigator.pushNamed(
            //             context,
            //             '/BaoCaoDauVaoDetail2',
            //             arguments: ScreenArgumentNavigatorBar(
            //               item['qualityControlId']!,
            //               widget.dateTimeOld,
            //               "",
            //               "normal",
            //               widget.userModel,
            //             ),
            //           );
            //         },
            //         child: Text(item['PO'].toString(), style: TextStyle(fontSize: 10)),
            //       );
            //     }).toList(),
            //   )
            : Container(),
        Expanded(
          child: _buildDataFiltered(),
        )
      ],
    );
  }

  _buildDataFiltered() {
    return _lsDataLstQCNVLByFilter.isNotEmpty
        ? Stack(
            alignment: Alignment.topCenter,
            children: [
              ListView.separated(
                physics: _isLoadingScroll == true ? const NeverScrollableScrollPhysics() : const ScrollPhysics(),
                key: ObjectKey(_lsDataLstQCNVLByFilter[0]),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _lsDataLstQCNVLByFilter.length,
                controller: _controllerListView,
                separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                itemBuilder: (context, index) {
                  final item = _lsDataLstQCNVLByFilter[index];
                  return GestureDetector(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          '/BaoCaoDauVaoDetail2',
                          arguments: ScreenArgumentNavigatorBar(
                            item.qualityControlId.toString(),
                            widget.dateTimeOld,
                            "",
                            "normal",
                            widget.userModel,
                          ),
                        ).then((value) {
                          if (value == true) {
                            _refresh();
                          }
                        }).onError((error, stackTrace) {
                          debugPrint('Lỗi:$error');
                        });
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            RichTextWidget(
                              title: "Thời gian tạo Barcode: ",
                              content: item.barcodeCreateDate == null ? "" : DateFormat("HH:mm dd/MM/yyyy").format(item.barcodeCreateDate!),
                            ),
                            const Divider(),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 6,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichTextWidget(
                                        title: "Số PO: ",
                                        content: item.poNumber == null ? "" : item.poNumber.toString(),
                                      ),
                                      RichTextWidget(
                                        title: "NCC: ",
                                        content: item.vendor == null ? "" : item.vendor.toString(),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: 4,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                    decoration: BoxDecoration(
                                      color: item.qcStatus == "Chưa kiểm tra" ? Colors.amber : Colors.grey.shade500,
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: Center(
                                      child: Text(
                                        item.qcStatus == null ? "" : item.qcStatus.toString(),
                                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10.h),
                            item.qcStatus != "Chưa kiểm tra"
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        flex: 6,
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            RichTextWidget(
                                              title: "Mã NVL | Tên NVL: ",
                                              content: item.material == null ? "" : item.material.toString(),
                                            ),
                                            RichTextWidget(
                                              title: "SO/WBS: ",
                                              content: item.soWbs == null ? "" : item.soWbs.toString(),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        flex: 4,
                                        child: Visibility(
                                          visible: item.qcStatus != "Chưa kiểm tra",
                                          child: Container(
                                            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                            decoration: BoxDecoration(
                                              color: item.qcResult == "Pass" ? Colors.green : Colors.red.shade800,
                                              borderRadius: BorderRadius.circular(3.r),
                                            ),
                                            child: Center(
                                              child: Text(
                                                item.qcResult == null ? "" : item.qcResult.toString(),
                                                style: TextStyle(fontSize: 12.sp, color: Colors.white),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichTextWidget(
                                        title: "Mã NVL | Tên NVL: ",
                                        content: item.material == null ? "" : item.material.toString(),
                                      ),
                                      RichTextWidget(
                                        title: "SO/WBS: ",
                                        content: item.soWbs == null ? "" : item.soWbs.toString(),
                                      ),
                                    ],
                                  ),
                            SizedBox(height: 10.h),
                            RichTextWidget(
                              title: "Ngày giao hàng thực tế: ",
                              content: item.actualDeliveryDate == null ? "" : DateFormat("HH:mm dd/MM/yyyy").format(item.actualDeliveryDate!),
                            ),
                            SizedBox(height: 10.h),
                            RichTextWidget(
                              title: "Thời gian QC: ",
                              content: item.qcCreateTime == null ? "" : DateFormat("HH:mm dd/MM/yyyy").format(item.qcCreateTime!),
                            ),
                            SizedBox(height: 10.h),
                            RichTextWidget(
                              title: "Trạng thái về nhà máy: ",
                              content: _lsDataLstQCNVLByFilter[index].isGoodsArrive == null || _lsDataLstQCNVLByFilter[index].isGoodsArrive == false
                                  ? "Vừa in Barcode"
                                  : "Đã về nhà máy",
                            ),
                            SizedBox(height: 10.h),
                            RichTextWidget(
                              title: "Ngày về nhà máy: ",
                              content: _lsDataLstQCNVLByFilter[index].actualDeliveryDate != null
                                  ? DateFormat('dd-MM-yyyy').format(_lsDataLstQCNVLByFilter[index].actualDeliveryDate!)
                                  : "",
                            ),
                            Visibility(
                              visible: item.qcStatus != "Chưa kiểm tra",
                              child: const Divider(),
                            ),
                            Visibility(
                              visible: item.qcStatus != "Chưa kiểm tra",
                              child: RichTextWidget(
                                title: "Nhân viên QC: ",
                                content: item.qcEmployee ?? "",
                              ),
                            ),
                          ],
                        ),
                      ));
                },
              ),
              Visibility(
                visible: _isLoadingScroll,
                child: Align(
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.5),
                          spreadRadius: 3,
                          blurRadius: 20,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                        backgroundColor: Colors.white,
                        child: Padding(
                          padding: REdgeInsets.all(6),
                          child: const CircularProgressIndicator(),
                        )),
                  ),
                ),
              ),
            ],
          )
        : const _NotFoundView();
  }
}

class _NotFoundView extends StatelessWidget {
  final String emptyListText = "Danh sách rỗng";

  const _NotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            emptyListText,
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}

class RichTextWidget extends StatelessWidget {
  final String title;
  final String content;
  const RichTextWidget({Key? key, required this.title, required this.content}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: "",
        style: TextStyle(fontSize: 12.sp, color: Colors.black),
        children: <TextSpan>[
          TextSpan(text: title, style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold)),
          TextSpan(text: content, style: TextStyle(fontSize: 12.sp)),
        ],
      ),
    );
  }
}
