import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

class UrlApi {
  static Future<bool> isEmulator() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return !androidInfo.isPhysicalDevice;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return !iosInfo.isPhysicalDevice;
    }
    return false; // Default to not emulator
  }

  // DEV
  // static const baseUrl = '192.168.1.208:3000';
  // static const baseUrl_2 = 'http://192.168.1.208:3000';
  // static const urlImage = 'http://192.168.1.208:3002';

  // DEV T9
  // static const baseUrl = '192.168.1.123:3000';
  // static const baseUrl_2 = 'http://192.168.1.123:3000';
  // static const urlImage = 'http://192.168.1.123:3002';
  // Define your isEmulator function here

  // QAS DEV T9
  static const baseUrlQas_DEBUG = '192.168.1.123:3000';
  static const baseUrl_2Qas_DEBUG = 'http://192.168.1.123:3000';
  static const urlImageQas_DEBUG = 'http://192.168.1.123:3002';

  // static const baseUrlQas_DEBUGEmulator = '10.0.2.2:5009';
  // static const baseUrl_2Qas_DEBUGEmulator = 'http://10.0.2.2:5009';
  // static const urlImageQas_DEBUGEmulator = 'http://10.0.2.2:54103';

  static const baseUrlQas_DEBUGEmulator = '192.168.1.123:3000';
  static const baseUrl_2Qas_DEBUGEmulator = 'http://192.168.1.123:3000';
  static const urlImageQas_DEBUGEmulator = 'http://192.168.1.123:3002';

  // QAS
  static const baseUrlQas_QQAS = 'qas-imes-api.truongthanh.com';
  static const baseUrl_2Qas_QAS = 'https://qas-imes-api.truongthanh.com';
  static const urlImageQas_QAS = 'https://qas-imes.truongthanh.com';

  // Auto detect
  static Future<String> get baseUrlQas async => kDebugMode ? (await isEmulator() ? baseUrlQas_DEBUGEmulator : baseUrlQas_DEBUG) : baseUrlQas_QQAS;
  static Future<String> get baseUrl_2Qas async =>
      kDebugMode ? (await isEmulator() ? baseUrl_2Qas_DEBUGEmulator : baseUrl_2Qas_DEBUG) : baseUrl_2Qas_QAS;
  static Future<String> get urlImageQas async => kDebugMode ? (await isEmulator() ? urlImageQas_DEBUGEmulator : urlImageQas_DEBUG) : urlImageQas_QAS;

  // QAS qas-imes
  // static String get baseUrlQas => baseUrlQas_QQAS;
  // static String get baseUrl_2Qas => baseUrl_2Qas_QAS;
  // static String get urlImageQas => urlImageQas_QAS;

  // PRD for test
  // static String get baseUrlQas => baseUrlPrd;
  // static String get baseUrl_2Qas => baseUrl_2Prd;
  // static String get urlImageQas => urlImagePrd;

  // PRD
  static const baseUrlPrd = 'imes-api.truongthanh.com';
  static const baseUrl_2Prd = 'https://imes-api.truongthanh.com';
  static const urlImagePrd = 'https://imes.truongthanh.com';

  static const baseUrlLogin = '/api/v1/Permission/Auth/';
  static const baseUrlProductionRecord = '/api/v1/MES/ProductionRecording/';
  static const baseUrlSwitchingStage = '/api/v1/MES/SwitchingStage/';
  static const baseUrlConfirmWorkCenter = '/api/v1/MES/ConfirmWorkCenter/';
  static const baseUrlQualityControl = '/api/v1/MES/QualityControl/';
  static const baseUrlRequestReturnVendor = '/api/v1/MES/RequestReturnVendor/';
  static const baseUrlNotification = '/api/v1/MES/Notification/';
  static const baseUrlCommonDate = '/api/v1/Utilities/CommonDate/';
  static const baseUrlPurchaseOrder = 'api/v2/MES/PurchaseOrder/';
  static const baseUrlCommon = 'api/v1/MES/Common/';
  static const baseUrlCommon_2 = '/api/v1/MES/Common/';
  static const baseUrlWarehouseTransaction = '/api/v1/MES/WarehouseTransaction/';
  static const baseUrlWarehousTransaction_2 = 'api/v1/MES/WarehouseTransaction/';
  static const baseUrlUtilities = "/api/v1/Utilities/Catalog/";
  static const baseUrlBatch = "/api/v1/MES/Batch/";
  static const baseUrlConfiguration = "/api/v1/Configuration/";
  static const Map<String, String> headers = {
    "Content-Type": "application/json",
  };

  static Map<String, String> headersToken(String token) {
    Map<String, String> headers = {
      "Content-Type": "application/json",
      'Authorization': 'Bearer $token',
      'Connection': 'Keep-Alive',
    };
    return headers;
  }

  static Map<String, String> headersTokenUrlencoded(String token) {
    Map<String, String> headers = {
      // "Content-Type": "application/json",
      "Content-Type": "application/x-www-form-urlencoded",
      'Authorization': 'Bearer $token',
      'Connection': 'Keep-Alive',
    };
    return headers;
  }
}
