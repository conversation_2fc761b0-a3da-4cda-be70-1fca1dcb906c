import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ContainerError {
  static widgetError(bool hide, String text) =>
      Visibility(
          visible: hide,
          child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Flexible(
                  flex: 1,
                  child: Icon(Icons.error_outline,
                      size: 13.sp, color: Colors.red[700]),
                ),
                SizedBox(width: 5.w),
                Flexible(
                    flex: 8,
                    child: Text(text,
                        style:
                        TextStyle(fontSize: 11.sp, color: Colors.red[700])))
              ]));
}