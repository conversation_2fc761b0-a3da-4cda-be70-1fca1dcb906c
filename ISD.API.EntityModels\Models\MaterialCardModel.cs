﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MaterialCardModel", Schema = "MES")]
    public partial class MaterialCardModel
    {
        [Key]
        public Guid Id { get; set; }
        public int? MaterialType { get; set; }
        [StringLength(50)]
        public string ReservationCode { get; set; }
        [StringLength(50)]
        public string SupplierCode { get; set; }
        [StringLength(200)]
        public string SupplierName { get; set; }
        [StringLength(20)]
        public string POCode { get; set; }
        [StringLength(20)]
        public string ProductCode { get; set; }
        [StringLength(200)]
        public string ProductName { get; set; }
        [StringLength(20)]
        public string SOWBS { get; set; }
        public int? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [StringLength(200)]
        public string Specifications { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? Mass { get; set; }
        [StringLength(50)]
        public string MassUnit { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? Weight { get; set; }
        [StringLength(10)]
        public string WeightUnit { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ManufacturingDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpirationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public int? InventorySAP { get; set; }
    }
}