﻿using Hangfire;
using System.Linq.Expressions;
using System;
using ISD.API.EntityModels.Data;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ISD.API.EntityModels.Models;
using System.Collections.Generic;
using System.Globalization;

namespace iMES_API.Infrastructures
{
    public static class HangfireHelper
    {
        // Add or update a recurring job based on SettingJob table
        public static async Task AddOrUpdateRecurringJobAsync<T>(string jobName, Expression<Action<T>> methodCall, EntityDataContext context, string timeDescription) where T : class // New parameter for time description
        {
            var jobSetting = await context.SettingJob.FirstOrDefaultAsync(j => j.JobName == jobName);

            if (jobSetting != null)
            {
                // Update job setting with new time description


                // Convert time description to cron expression
                var cronExpression = ConvertToCronExpression(timeDescription);
                if (!string.IsNullOrEmpty(cronExpression))
                {
                    RecurringJob.AddOrUpdate(jobName, methodCall, cronExpression);
                }

                jobSetting.Config = cronExpression;
                jobSetting.ConfigDescription = timeDescription;

                await context.SaveChangesAsync();
            }
            else
            {
                var cronExpression = ConvertToCronExpression(timeDescription);

                var newJobSetting = new SettingJob
                {
                    JobName = jobName,
                    Config = cronExpression,
                    ConfigDescription = timeDescription,
                };

                context.SettingJob.Add(newJobSetting);
                await context.SaveChangesAsync();
            }
        }
        private static string ConvertToCronExpression(string description)
        {
            // Daily at a specific time (e.g., "Daily at 3 AM")
            if (description.StartsWith("Daily at "))
            {
                var timePart = description.Replace("Daily at ", "").Trim();
                var time = DateTime.ParseExact(timePart, "h tt", CultureInfo.InvariantCulture);
                return $"{time.Minute} {time.Hour} * * *"; // Every day at specified time
            }

            // Weekly on a specific day (e.g., "Weekly on Monday")
            if (description.StartsWith("Weekly on "))
            {
                var dayPart = description.Replace("Weekly on ", "").Trim();
                return $"0 0 * * {dayPart}"; // Every specified weekday at midnight
            }

            // Monthly on a specific date (e.g., "Monthly on the 15th")
            if (description.StartsWith("Monthly on the "))
            {
                var dayPart = description.Replace("Monthly on the ", "").Replace("th", "").Trim();
                return $"0 0 {dayPart} * *"; // Specific date of each month at midnight
            }

            // Hourly (e.g., "Every hour")
            if (description.Equals("Every hour", StringComparison.OrdinalIgnoreCase))
            {
                return "0 * * * *"; // Every hour
            }

            // Every X minutes (e.g., "Every 30 minutes")
            if (description.StartsWith("Every ") && description.EndsWith(" minutes"))
            {
                var minutePart = description.Replace("Every ", "").Replace(" minutes", "").Trim();
                return $"*/{minutePart} * * * *"; // Every X minutes
            }

            // Every X seconds - Note: This is a workaround as standard cron expressions do not support second-level scheduling
            if (description.StartsWith("Every ") && description.EndsWith(" seconds"))
            {
                var secondPart = description.Replace("Every ", "").Replace(" seconds", "").Trim();
                int seconds = int.Parse(secondPart);
                if (seconds < 60)
                {
                    // If the interval is less than a minute, schedule the job to run every minute
                    // and handle the second-level logic within the job itself
                    return "* * * * *";
                }
                else
                {
                    // Convert seconds to minutes for intervals longer than 59 seconds
                    int minutes = seconds / 60;
                    return $"*/{minutes} * * * *";
                }
            }

            // Default case for unknown descriptions
            return null; // Return null if no matching description is found
        }


        // List all job settings
        public static async Task<List<SettingJob>> GetAllJobSettingsAsync(EntityDataContext context)
        {
            return await context.SettingJob.ToListAsync();
        }

        // Schedule a job to run at a specific time
        public static string ScheduleJob<T>(Expression<Action<T>> methodCall, DateTimeOffset enqueueAt) where T : class
        {
            return BackgroundJob.Schedule(methodCall, enqueueAt);
        }

        // Enqueue a job to run as soon as possible
        public static string EnqueueJob<T>(Expression<Action<T>> methodCall) where T : class
        {
            return BackgroundJob.Enqueue(methodCall);
        }

        // Add or update a recurring job
        public static void AddOrUpdateRecurringJob<T>(string recurringJobId, Expression<Action<T>> methodCall, string cronExpression) where T : class
        {
            RecurringJob.AddOrUpdate(recurringJobId, methodCall, cronExpression);
        }

        // Remove a recurring job
        public static void RemoveRecurringJob(string recurringJobId)
        {
            RecurringJob.RemoveIfExists(recurringJobId);
        }

        // Trigger a job immediately
        public static bool TriggerJob(string jobId)
        {
            try
            {
                using var connection = JobStorage.Current.GetConnection();
                var jobData = connection.GetJobData(jobId);
                if (jobData == null) return false;

                BackgroundJob.Requeue(jobId);
                return true;
            }
            catch
            {
                // Handle exceptions or logging here
                return false;
            }
        }

        // Delete a job
        public static bool DeleteJob(string jobId)
        {
            return BackgroundJob.Delete(jobId);
        }

        // Check if a job exists
        public static bool DoesJobExist(string jobId)
        {
            using var connection = JobStorage.Current.GetConnection();
            var jobData = connection.GetJobData(jobId);
            return jobData != null;
        }

        // Trigger a recurring job immediately
        public static bool TriggerRecurringJob(string recurringJobId)
        {
            try
            {
                RecurringJob.Trigger(recurringJobId);
                return true;
            }
            catch
            {
                // Handle exceptions or logging here
                return false;
            }
        }
    }

}
