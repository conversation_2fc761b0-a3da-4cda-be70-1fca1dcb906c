import 'dart:io';
import '../model/qualityControlApi.dart';
import '../model/sendQualityControlDetail.dart';

class ScreenArgumentQualityInformation {
  final String token;
  final QualityControlDetail? qualityControlDetail;
  final QualityControl? qualityControl;
  final List<TestMethodList>? lsTestMethodList;
  final List<ResultList>? lsResultList;
  final List<SamplingLevelList>? lsSamplingLevelList;
  final List<Error>? lsError;
  final List<ThongTinKiemTra>? lsQualityControlInformationIdList;
  final List<QualityControlInformation>? lsQualityControlInformation;
  final List<ErrorList>? lsErrorList;
  final SendQualityControlDetail? getSendQualityControlDetail;
  final QualityCheckerInfo? selectedStaff;
  final QualityTypeList? selectedType;
  final String pO;
  final String quantityCheck;
  final ResultList? selectedResult;
  final List<File> lsFileTabCheck;
  final String formatDatePost;
  final String dateTimeOld;
  ScreenArgumentQualityInformation(
      this.token,
      this.qualityControlDetail,
      this.qualityControl,
      this.lsTestMethodList,
      this.lsResultList,
      this.lsSamplingLevelList,
      this.lsError,
      this.lsQualityControlInformationIdList,
      this.lsQualityControlInformation,
      this.lsErrorList,
      this.getSendQualityControlDetail,
      this.selectedStaff,
      this.selectedType,
      this.pO,
      this.quantityCheck,
      this.selectedResult,
      this.lsFileTabCheck,
      this.formatDatePost,
      this.dateTimeOld);
}
