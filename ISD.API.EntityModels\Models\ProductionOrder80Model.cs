﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductionOrder80Model", Schema = "MES")]
    public partial class ProductionOrder80Model
    {
        [Key]
        public Guid ProductionOrderId { get; set; }
        [StringLength(50)]
        public string AUFNR { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? GLTRP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? GSTRP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? FTRMS { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? GLTRS { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? GSTRS { get; set; }
        public int? RSNUM { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GASMG { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GAMNG { get; set; }
        [StringLength(50)]
        public string KDAUF { get; set; }
        [StringLength(50)]
        public string KDPOS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? PSMNG { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? WEMNG { get; set; }
        [StringLength(10)]
        public string AMEIN { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [StringLength(100)]
        public string MATNR { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? PAMNG { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? PGMNG { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LTRMI { get; set; }
        [Column(TypeName = "decimal(18, 1)")]
        public decimal? UEBTO { get; set; }
        [StringLength(10)]
        public string UEBTK { get; set; }
        [Column(TypeName = "decimal(18, 1)")]
        public decimal? UNTTO { get; set; }
        [StringLength(10)]
        public string INSMK { get; set; }
        [StringLength(10)]
        public string DWERK { get; set; }
        [StringLength(10)]
        public string DAUAT { get; set; }
        [StringLength(50)]
        public string ZZSLANSUA { get; set; }
        [StringLength(500)]
        public string ZZGHICHU { get; set; }
        [StringLength(100)]
        public string ZZLSX { get; set; }
        [StringLength(10)]
        public string VERID { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(50)]
        public string AUFNR_MES { get; set; }
        [StringLength(50)]
        public string KDPOS_MES { get; set; }
        [StringLength(500)]
        public string PROJN { get; set; }
        [StringLength(10)]
        public string ZCLOSE { get; set; }
        public bool? isSyncWithSAP { get; set; }
        [StringLength(10)]
        public string LOEKZ { get; set; }
        [StringLength(500)]
        public string ZSTATUS { get; set; }
    }
}