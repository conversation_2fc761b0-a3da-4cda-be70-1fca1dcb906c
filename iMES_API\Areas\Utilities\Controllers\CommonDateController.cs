﻿using Microsoft.AspNetCore.Mvc;
using ISD.API.Core;
using System;

namespace iMES_API.Areas.Utilities.Controllers
{
    [Route("api/v{version:apiVersion}/Utilities/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    public class CommonDateController : ControllerBaseAPI
    {
        [HttpGet("GetDateByCommonDate")]
        [MapToApiVersion("1.0")]
        [ApiExplorerSettings(GroupName = "mobile")]
        public ActionResult Get(string CommonDate)
        {
            DateTime? fromDate;
            DateTime? toDate;

            _unitOfWork.CommonDateRepository.GetDateBy(CommonDate, out fromDate, out toDate);

            return Ok(new
            {
                FromDate = string.Format("{0:yyyy-MM-dd}", fromDate),
                ToDate = string.Format("{0:yyyy-MM-dd}", toDate)
            });
        }
        
        [HttpGet("GetDateByCommonDate")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        public ActionResult Get2(string CommonDate)
        {
            DateTime? fromDate;
            DateTime? toDate;

            _unitOfWork.CommonDateRepository.GetDateBy(CommonDate, out fromDate, out toDate);

            return Ok(new
            {
                FromDate = string.Format("{0:yyyy-MM-dd}", fromDate),
                ToDate = string.Format("{0:yyyy-MM-dd}", toDate)
            });
        }
    }
}
