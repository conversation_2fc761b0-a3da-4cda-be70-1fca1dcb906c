﻿using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using System;
using Newtonsoft.Json;
using System.Text;
using RestSharp;
using ISD.API.EntityModels.Models;

namespace iMES_API.Infrastructures
{
    public interface IOneSignalService
    {
        Task<bool> SendToExternalIdsAsync(string title, string message, List<string> externalIds);
        Task<bool> SendToExternalIdsAsync(PushNotificationModel notificationModel);

        Task SendAsync(string title, string message, string url);
        Task<string> GetAllUsers();
        Task<bool> SendSilentRequest(string[] userIdArray);
    }

    public class OneSignalService : IOneSignalService
    {
        private readonly IConfiguration _configuration;
        //private readonly ILogger _logger;

        public OneSignalService(IConfiguration configuration
            //ILogger logger
            )
        {
            _configuration = configuration;
            //_logger = logger;
        }

        public async Task<bool> SendSilentRequest(string[] listUsers)
        {
            var obj = new
            {
                app_id = _configuration["OneSignal:AppId"],
                //headings = new { en = title },
                //contents = new { en = message },
                channel_for_external_user_ids = "push",
                //include_external_user_ids = new string[] {
                //    "6392d91a-b206-4b7b-a620-cd68e32c3a76",
                //    "76ece62b-bcfe-468c-8a78-839aeaa8c5fa",
                //    "8e0f21fa-9a5a-4ae7-a9a6-ca1f24294b86"
                //}
                include_external_user_ids = listUsers,
                content_available = true,
                mutable_content = true,
                data = new { wakeup = "1" }
            };

            //_logger.Debug("SendSilentRequest: " + JsonConvert.SerializeObject(obj));

            var ret = await PostToOneSignal(obj);

            return ret;
        }

        private async Task<bool> PostToOneSignal(object obj)
        {
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;
            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json; charset=utf-8";

            request.Headers.Add("authorization", "Basic " + _configuration["OneSignal:AppSecret"]);

            //var param = serializer.Serialize(obj);
            var param = JsonConvert.SerializeObject(obj);

            byte[] byteArray = Encoding.UTF8.GetBytes(param);

            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }

                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                    }
                }

                return true;
            }
            catch (WebException ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());

                return false;
            }

            //System.Diagnostics.Debug.WriteLine(responseContent);
        }

        public async Task<bool> SendToExternalIdsAsync(string title, string message, List<string> externalIds)
        {
            //var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            //request.KeepAlive = true;
            //request.Method = "POST";
            //request.ContentType = "application/json; charset=utf-8";

            //request.Headers.Add("authorization", "Basic " + _configuration["OneSignal:AppSecret"]);

            var obj = new
            {
                app_id = _configuration["OneSignal:AppId"],
                headings = new { en = title },
                contents = new { en = message },
                channel_for_external_user_ids = "push",
                //include_external_user_ids = new string[] {
                //    "6392d91a-b206-4b7b-a620-cd68e32c3a76",
                //    "76ece62b-bcfe-468c-8a78-839aeaa8c5fa",
                //    "8e0f21fa-9a5a-4ae7-a9a6-ca1f24294b86"
                //}
                include_external_user_ids = externalIds.ToArray()
            };

            var ret = await PostToOneSignal(obj);

            return ret;
        }

        public async Task<bool> SendToExternalIdsAsync(PushNotificationModel notificationModel)
        {
            var externalIds = new List<string>() {
                notificationModel.ExternalId
            };
            var obj = new
            {
                app_id = _configuration["OneSignal:AppId"],
                headings = new { en = notificationModel.Title },
                contents = new { en = notificationModel.Message },
                channel_for_external_user_ids = "push",
                //include_external_user_ids = new string[] {
                //    "6392d91a-b206-4b7b-a620-cd68e32c3a76",
                //    "76ece62b-bcfe-468c-8a78-839aeaa8c5fa",
                //    "8e0f21fa-9a5a-4ae7-a9a6-ca1f24294b86"
                //}
                include_external_user_ids = externalIds.ToArray()
            };

            var ret = await PostToOneSignal(obj);

            return ret;
        }

        public async Task SendAsync(string title, string message, string url)
        {
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;
            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json; charset=utf-8";
            request.Headers.Add("authorization", "Basic " + _configuration["OneSignal:AppSecret"]);

            var obj = new
            {
                app_id = _configuration["OneSignal:AppId"],
                headings = new { en = title },
                contents = new { en = message },
                included_segments = new[] { "All" },
                url
            };
            var param = JsonConvert.SerializeObject(obj);
            byte[] byteArray = Encoding.UTF8.GetBytes(param);

            string responseContent = null;

            try
            {
                using (var writer = await request.GetRequestStreamAsync())
                {
                    await writer.WriteAsync(byteArray, 0, byteArray.Length);
                }

                using (var response = await request.GetResponseAsync() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = await reader.ReadToEndAsync();
                    }
                }
            }
            catch (WebException ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
            }
        }

        public async Task<string> GetAllUsers()
        {
            string AppId = _configuration["OneSignal:AppId"]; // "************************************";
            string AppSecret = _configuration["OneSignal:AppSecret"]; // "************************************************";

            string url = $"https://onesignal.com/api/v1/players?app_id={AppId}&limit=300&offset=0";


            var cancellationToken = new System.Threading.CancellationToken();
            var client = new RestClient(url);

            // Create a request using a URL that can receive a post.
            var myUri = new Uri(url);
            var myWebRequest = WebRequest.Create(myUri);
            var myHttpWebRequest = (HttpWebRequest)myWebRequest;
            myHttpWebRequest.PreAuthenticate = true;
            myHttpWebRequest.Headers.Add("Authorization", "Basic " + AppSecret);
            myHttpWebRequest.Accept = "application/json";
            try
            {

                var myWebResponse = myWebRequest.GetResponse();
                var responseStream = myWebResponse.GetResponseStream();
                if (responseStream == null)
                {
                    return "";
                };

                var myStreamReader = new StreamReader(responseStream, Encoding.Default);
                var json = myStreamReader.ReadToEnd();

                responseStream.Close();
                myWebResponse.Close();
                return json;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
            }
            return "";
        }

        
    }
}
