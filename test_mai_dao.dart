import 'dart:io';
import 'package:flutter/material.dart';
import 'lib/repository/function/maiDaoFunction.dart';
import 'lib/model/maiDaoModel.dart';

/// Simple test runner for MaiDaoFunction
/// Run this with: dart test_mai_dao.dart
void main() async {
  print('🧪 Starting MaiDao Function Tests...\n');

  // Test helper methods first
  await testHelperMethods();
  print('');

  // Test data models
  await testDataModels();
  print('');

  // Test API functions (these will fail without server, but we can see the structure)
  await testApiFunctions();
  print('');

  print('✅ Test execution completed!');
  print('Note: API tests will show errors if server is not running - this is expected for testing structure.');
}

Future<void> testHelperMethods() async {
  print('📋 Testing Helper Methods...');

  // Test status colors
  print('  Status Colors:');
  print('    Created: ${MaiDaoFunction.getStatusColor('Created')}');
  print('    Confirmed: ${MaiDaoFunction.getStatusColor('Confirmed')}');
  print('    Completed: ${MaiDaoFunction.getStatusColor('Completed')}');
  print('    Cancelled: ${MaiDaoFunction.getStatusColor('Cancelled')}');

  // Test status texts
  print('  Status Texts:');
  print('    Created: ${MaiDaoFunction.getStatusText('Created')}');
  print('    Confirmed: ${MaiDaoFunction.getStatusText('Confirmed')}');
  print('    Completed: ${MaiDaoFunction.getStatusText('Completed')}');
  print('    Cancelled: ${MaiDaoFunction.getStatusText('Cancelled')}');

  print('  ✅ Helper methods test completed');
}

Future<void> testDataModels() async {
  print('📄 Testing Data Models...');

  // Test MaiDaoRecord creation and JSON serialization
  final record = MaiDaoRecord(
    id: 'test-123',
    date: '25/04/2025',
    equipmentCode: 'EQ001',
    equipmentName: 'Test Equipment',
    materialCode: 'MAT001',
    materialName: 'Test Material',
    operationType: 'Mài dao',
    employeeCodes: 'NV001,NV002',
    employeeNames: 'Employee 1, Employee 2',
    requestingEmployeeCode: 'NV003',
    requestingEmployeeName: 'Requesting Employee',
    note: 'Test note',
    status: 'Created',
    companyCode: '1000',
    createdDate: '2025-04-25T08:00:00',
  );

  print('  Original Record:');
  print('    ID: ${record.id}');
  print('    Equipment: ${record.equipmentCode} - ${record.equipmentName}');
  print('    Material: ${record.materialCode} - ${record.materialName}');
  print('    Operation: ${record.operationType}');
  print('    Status: ${record.status}');

  // Test JSON serialization
  try {
    final json = record.toJson();
    final recreatedRecord = MaiDaoRecord.fromJson(json);

    print('  JSON Serialization Test:');
    print('    ✅ Serialization successful');
    print('    ✅ Deserialization successful');
    print('    ✅ ID matches: ${recreatedRecord.id == record.id}');
    print('    ✅ Equipment matches: ${recreatedRecord.equipmentCode == record.equipmentCode}');
    print('    ✅ Status matches: ${recreatedRecord.status == record.status}');
  } catch (e) {
    print('    ❌ JSON serialization error: $e');
  }

  // Test MaiDaoSearchModel
  final searchModel = MaiDaoSearchModel(
    companyCode: '1000',
    equipmentCode: 'EQ001',
    materialCode: 'MAT001',
    operationType: 'Mài dao',
    status: 'Created',
    fromDate: DateTime(2025, 4, 1),
    toDate: DateTime(2025, 4, 30),
    pageNumber: 1,
    pageSize: 20,
  );

  print('  Search Model:');
  print('    Company: ${searchModel.companyCode}');
  print('    Equipment: ${searchModel.equipmentCode}');
  print('    Material: ${searchModel.materialCode}');
  print('    Operation: ${searchModel.operationType}');
  print('    Status: ${searchModel.status}');
  print('    Page: ${searchModel.pageNumber}/${searchModel.pageSize}');

  print('  ✅ Data models test completed');
}

Future<void> testApiFunctions() async {
  print('🌐 Testing API Functions...');
  print('  Note: These tests will show connection errors if server is not running');

  const testToken = 'test_token_123';
  const testCompanyCode = '1000';
  const testEquipmentCode = 'EQ001';

  // Test fetchMaiDaoList
  print('  1. Testing fetchMaiDaoList...');
  try {
    final searchModel = MaiDaoSearchModel(
      companyCode: testCompanyCode,
      pageNumber: 1,
      pageSize: 5,
    );

    final result = await MaiDaoFunction.fetchMaiDaoList(testToken, searchModel);
    if (result != null) {
      print('    ✅ API call successful');
      print('    📊 Found ${result.data?.length ?? 0} records');
      if (result.data?.isNotEmpty == true) {
        final first = result.data!.first;
        print('    📄 First record: ${first.equipmentCode} - ${first.status}');
      }
    } else {
      print('    ⚠️  API returned null (server not available or authentication failed)');
    }
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test fetchEquipment
  print('  2. Testing fetchEquipment...');
  try {
    final equipment = await MaiDaoFunction.fetchEquipment(testToken, testCompanyCode, 'test');
    print('    📊 Found ${equipment.length} equipment items');
    for (var item in equipment.take(3)) {
      print('    🔧 ${item.equipmentCode}: ${item.equipmentName}');
    }
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test fetchMaterials
  print('  3. Testing fetchMaterials...');
  try {
    final materials = await MaiDaoFunction.fetchMaterials(testToken, testCompanyCode, 'test');
    print('    📊 Found ${materials.length} material items');
    for (var item in materials.take(3)) {
      print('    🧰 ${item.materialCode}: ${item.materialName}');
    }
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test fetchEmployees
  print('  4. Testing fetchEmployees...');
  try {
    final employees = await MaiDaoFunction.fetchEmployees(testToken, testCompanyCode);
    print('    📊 Found ${employees.length} employees');
    for (var emp in employees.take(3)) {
      print('    👤 ${emp.employeeCode}: ${emp.employeeName}');
    }
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test checkExistingRecord
  print('  5. Testing checkExistingRecord...');
  try {
    final existing = await MaiDaoFunction.checkExistingRecord(testToken, testEquipmentCode, testCompanyCode);
    if (existing != null) {
      print('    ✅ Found existing record: ${existing.id}');
      print('    📄 Equipment: ${existing.equipmentCode}');
      print('    📋 Status: ${existing.status}');
    } else {
      print('    ℹ️  No existing incomplete record found');
    }
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test saveMaiDao (create)
  print('  6. Testing saveMaiDao (create)...');
  try {
    final newRecord = MaiDaoRecord(
      id: null, // New record
      date: '25/04/2025',
      equipmentCode: 'TEST001',
      equipmentName: 'Test Equipment',
      materialCode: 'TESTMAT001',
      materialName: 'Test Material',
      operationType: 'Mài dao',
      employeeCodes: 'TESTEMP001',
      employeeNames: 'Test Employee',
      status: 'Created',
      companyCode: testCompanyCode,
    );

    final success = await MaiDaoFunction.saveMaiDao(testToken, newRecord);
    print('    📝 Create operation result: $success');
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  // Test saveMaiDao (update)
  print('  7. Testing saveMaiDao (update)...');
  try {
    final existingRecord = MaiDaoRecord(
      id: 'existing-123',
      date: '25/04/2025',
      equipmentCode: 'TEST001',
      equipmentName: 'Updated Test Equipment',
      materialCode: 'TESTMAT001',
      materialName: 'Updated Test Material',
      operationType: 'Đắp dao',
      employeeCodes: 'TESTEMP001,TESTEMP002',
      employeeNames: 'Test Employee 1, Test Employee 2',
      status: 'Confirmed',
      companyCode: testCompanyCode,
    );

    final success = await MaiDaoFunction.saveMaiDao(testToken, existingRecord);
    print('    📝 Update operation result: $success');
  } catch (e) {
    print('    ⚠️  Expected error: $e');
  }

  print('  ✅ API functions test completed');
}

/// Example of how to test specific scenarios
void runSpecificTests() {
  print('\n🎯 Running Specific Test Scenarios...');

  // Test status workflow
  final statuses = ['Created', 'Confirmed', 'Completed', 'Cancelled'];
  print('Status Workflow Test:');
  for (var status in statuses) {
    final color = MaiDaoFunction.getStatusColor(status);
    final text = MaiDaoFunction.getStatusText(status);
    print('  $status → $text (Color: $color)');
  }

  // Test edge cases
  print('\nEdge Cases:');
  print('  Empty status: "${MaiDaoFunction.getStatusText('')}"');
  print('  Null status: "${MaiDaoFunction.getStatusText('null')}"');
  print('  Unknown status: "${MaiDaoFunction.getStatusText('UNKNOWN')}"');
}
