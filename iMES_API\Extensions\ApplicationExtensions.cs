﻿using iMES_API.Configures;
using iMES_API.Infrastructures;
using iMES_API.Middlewares.Logging;
using ISD.API.Core;
using ISD.Middlewares;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;

namespace iMES_API.Extensions
{
    public static class ApplicationExtensions
    {
        private static readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public static void UseInfrastructure(this IApplicationBuilder app, IHostEnvironment env, IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider, IConfiguration configuration)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwaggerAuthorized();
                app.UseSwagger(options =>
                {
                    options.SerializeAsV2 = true;
                });
                app.UseSwaggerUI(options =>
                {
                    options.SwaggerEndpoint("/swagger/mobile/swagger.json", "TTF - iMes Mobile");
                    options.SwaggerEndpoint("/swagger/web/swagger.json", "ISD - Web Frontend");
                    options.SwaggerEndpoint("/swagger/integrate/swagger.json", "ISD - Integrate");
                });
            }

            //Environment.SetEnvironmentVariable("PATH", $"{env.ContentRootPath};{Environment.GetEnvironmentVariable("PATH")}");

            //app.UseExceptionMiddleware();

            // TODO: remove if server added HTTPS
            //if (!env.IsDevelopment())
            //{
            //    app.UseHttpsRedirection();
            //}

            app.UseRouting();

            app.UseAuthorization();
            app.UseAuthentication();

            app.UseCors(MyAllowSpecificOrigins);

            app.UseMiddleware<JwtMiddleware>();

            app.UseRequestResponseLogging();

            ControllerBaseAPI.SetHttpContextAccessor(httpContextAccessor);

            // Get configuration
            var is300 = configuration.GetValue<bool>("Is300");

            if (!env.IsDevelopment())
            {
                HangfireConfig.HangfireMiddleware(app);

                //Run jobs
                HangfireJob.Run(serviceProvider);
            }

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
