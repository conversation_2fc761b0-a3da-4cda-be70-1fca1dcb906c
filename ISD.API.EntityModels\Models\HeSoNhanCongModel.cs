﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("HeSoNhanCongModel", Schema = "MES")]
    public partial class HeSoNhanCongModel
    {
        [Key]
        public long ID { get; set; }
        [Required]
        [StringLength(10)]
        public string Plant { get; set; }
        [Required]
        [StringLength(50)]
        public string SalesEmployeeCode { get; set; }
        [StringLength(50)]
        public string Routing { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        [Column(TypeName = "decimal(3, 2)")]
        public decimal? SkillCoefficient { get; set; }
        [Column(TypeName = "decimal(3, 2)")]
        public decimal? JobCompletionCoefficient { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? PricePerMinute { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        public Guid? CreatedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedTime { get; set; }
        public Guid? LastModifiedUser { get; set; }
    }
}