﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DowntimeHistoryModel", Schema = "MES")]
    public partial class DowntimeHistoryModel
    {
        [Key]
        public Guid HistoryId { get; set; }
        public Guid DowntimeId { get; set; }
        [StringLength(20)]
        public string Action { get; set; }
        [StringLength(20)]
        public string OldStatus { get; set; }
        [StringLength(20)]
        public string NewStatus { get; set; }
        [StringLength(100)]
        public string ChangedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime ChangedDate { get; set; }
        [StringLength(50)]
        public string VerifierRole { get; set; }
        [StringLength(500)]
        public string Comment { get; set; }

        [ForeignKey("DowntimeId")]
        [InverseProperty("DowntimeHistoryModel")]
        public virtual DowntimeModel Downtime { get; set; }
    }
}