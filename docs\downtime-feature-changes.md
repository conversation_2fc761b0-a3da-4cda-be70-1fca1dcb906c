# Downtime Feature Implementation Changes

## Overview
This document tracks the changes made to implement the Downtime feature in the TTF_MES_Mobile app.

## Change History

### 1. Model Updates
- Created `DowntimeModel` to match API structure
- Updated field names to match database:
  - `team` → `departmentCode`
  - `workstation` → `stepCode`
  - `approvalStep` → `verifierRole`
- Added missing fields from API model
- Implemented proper JSON serialization

### 2. API Integration
- Added mock/production toggle functionality
- Implemented environment-based URL selection
- Added proper error handling and logging
- Updated API endpoints to match backend
- Added SecureStorage for environment checking

### 3. UI Updates
- Updated field mappings in DowntimeList and DowntimeDetail
- Added verification status display
- Improved history panel styling
- Fixed date/time format handling
- Added employee code handling

## Implementation Steps

1. **Initial Model Review**
   - Reviewed API models vs mobile models
   - Identified field mismatches
   - Updated model structure

2. **Function Updates**
   - Added mock data support
   - Implemented API calls
   - Added environment checking
   - Added detailed logging

3. **UI Fixes**
   - Updated field references
   - Fixed employee handling
   - Added verification status
   - Improved history display

## Key Files Modified

1. `lib/model/downtimeModel.dart`
   - Updated model structure
   - Fixed field names
   - Added proper serialization

2. `lib/repository/function/downtimeFunction.dart`
   - Added mock/API toggle
   - Implemented environment checking
   - Added proper error handling

3. `lib/page/Downtime/DowntimeList.dart`
   - Updated field references
   - Added verification status
   - Improved list display

4. `lib/page/Downtime/DowntimeDetail.dart`
   - Fixed field mappings
   - Updated employee handling
   - Improved history display

## Testing Notes

1. **Mock Data Testing**
   - Set `useMockData = true`
   - Test CRUD operations
   - Verify history display

2. **API Testing**
   - Set `useMockData = false`
   - Test with QAS environment
   - Test with PRD environment

## Configuration

### Environment Selection 

## Optimal AI Guidance Prompts

### Model & Structure Review
1. "Review the model structure between mobile (.dart) and API (.cs) files to identify mapping mismatches"
2. "Compare field names and types between mobile and API models"
3. "Check if any required fields are missing in the mobile model"

### API Integration
4. "Review the API endpoints and URL structure in the mobile app"
5. "Check environment handling for QAS/PRD in the API calls"
6. "Verify the mock data structure matches the API response format"

### UI Implementation
7. "Review field mappings in the UI components"
8. "Check date/time format handling between UI and API"
9. "Verify the history panel displays all required information"

### Error Handling
10. "Review error handling in API calls"
11. "Check validation messages in forms"
12. "Verify network connectivity handling"

### Testing & Validation
13. "List test cases for CRUD operations"
14. "Verify environment switching behavior"
15. "Check data persistence and state management"

## Prompt Structure Best Practices

1. **Context First**
   "Given the API model in DowntimeModel.cs, review..."

2. **Specific Focus**
   "Check the field mapping for departmentCode and stepCode..."

3. **Expected Outcome**
   "Update the model to match the API structure while maintaining..."

4. **File References**
   "Looking at downtimeFunction.dart, modify the API calls..."

5. **Sequential Changes**
   "First update the model, then modify the API calls, finally update the UI..."

## Example Prompt Sequences

### For Model Updates
1. "Review current model structure"
2. "Identify mismatches"
3. "Propose changes"
4. "Implement updates"
5. "Verify mappings"

### For API Integration
1. "Review API endpoints"
2. "Check environment handling"
3. "Update mock data"
4. "Implement API calls"
5. "Add error handling"

### For UI Updates
1. "Review current UI structure"
2. "Check field mappings"
3. "Update display formats"
4. "Add validation"
5. "Implement error messages"

## Tips for Effective AI Guidance

1. **Be Specific**
   - Mention exact file names
   - Reference specific fields or functions
   - Indicate expected behavior

2. **Provide Context**
   - Include relevant file contents
   - Explain the purpose of changes
   - Reference related components

3. **Request Incremental Changes**
   - Break down large changes
   - Review each step
   - Verify before proceeding

4. **Include Examples**
   - Show expected formats
   - Provide sample data
   - Demonstrate use cases

5. **Request Verification**
   - Ask for validation checks
   - Request test cases
   - Confirm mappings

## Advanced Prompt Sequences

### Mobile UI Design & CRUD Implementation
1. "Review existing UI patterns in the app for list and detail views"
2. "Design a list view layout following app's style guide"
3. "Design a detail view layout with form validation"
4. "Create mock data structure for testing"
5. "Implement list view with mock data"
6. "Implement detail view with CRUD operations"
7. "Add loading states and error handling"
8. "Implement search and filter functionality"
9. "Add pull-to-refresh and pagination"
10. "Implement proper state management"

### API & Database Design
1. "Design database schema based on UI requirements"
2. "Create SQL scripts for tables and relationships"
3. "Define API endpoints and response formats"
4. "Implement API controllers with proper validation"
5. "Add authentication and authorization"
6. "Implement sorting and filtering"
7. "Add pagination support"
8. "Create stored procedures for complex queries"
9. "Add audit logging"
10. "Implement proper error handling"

### Mobile-API Integration
1. "Review API response format vs mobile model"
2. "Check field name consistency"
3. "Verify data types and formats"
4. "Update mobile models to match API"
5. "Implement API service layer"
6. "Add proper error handling"
7. "Implement retry logic"
8. "Add offline support"
9. "Implement proper caching"
10. "Add request/response logging"

### Admin Dashboard Implementation
1. "Design admin dashboard layout"
2. "Create data grid with CRUD operations"
3. "Implement sorting and filtering"
4. "Add Excel export functionality"
5. "Create Excel import template"
6. "Implement Excel import validation"
7. "Add batch operations support"
8. "Implement audit logging"
9. "Add user activity tracking"
10. "Create admin reports"

## Example Prompt Sequences

### For Mobile UI Design

## Specific Example Prompts

### 1. Mobile UI Design

Sure! Here are additional prompts tailored to your specific cases. These prompts are designed to help you effectively utilize AI for designing mobile UIs, creating APIs with SQL scripts, mapping and reviewing integrations, and developing an Admin dashboard with CRUD functionalities including Excel export/import.

---

## 1. Design Mobile UI and Implement CRUD Operations with Mock Data

### a. Design Mobile UI
- **Prompt:**  
  _"Design a mobile user interface for the Downtime feature in the TTF_MES_Mobile app, including a list page and a details page. Ensure the UI follows best practices for usability and responsiveness."_

### b. Create CRUD for List Page
- **Prompt:**  
  _"Create a Flutter `DowntimeListPage` that displays a list of downtime records using mock data. Implement best practice CRUD operations including Create, Read, Update, and Delete functionalities."_

### c. Create CRUD for Details Page
- **Prompt:**  
  _"Develop a Flutter `DowntimeDetailPage` that shows detailed information for a selected downtime record. Include edit and delete options, and ensure it interacts seamlessly with the `DowntimeListPage` using mock data."_

### d. Integration of Mock Data
- **Prompt:**  
  _"Implement mock data services for the Downtime feature in the TTF_MES_Mobile app. Ensure the data can be easily replaced with real API data in the future."_

---

## 2. Create API Based on Mobile UI with SQL Script

### a. Design API Endpoints
- **Prompt:**  
  _"Create RESTful API endpoints for managing downtime records based on the mobile UI design. Include endpoints for creating, retrieving, updating, and deleting downtime data."_

### b. Implement API Controllers
- **Prompt:**  
  _"Develop ASP.NET Core API controllers for the Downtime feature. Ensure proper validation, error handling, and adherence to RESTful principles."_

### c. SQL Script for Database Table
- **Prompt:**  
  _"Write a SQL script to create the `Downtime` table with fields that match the `DowntimeModel` used in the TTF_MES_Mobile app. Include appropriate data types, primary keys, and relationships."_

```sql
CREATE TABLE Downtime (
    DowntimeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    DepartmentCode NVARCHAR(50) NOT NULL,
    StepCode NVARCHAR(50) NOT NULL,
    VerifierRole NVARCHAR(50) NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NULL,
    Reason NVARCHAR(255),
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE()
);
```

### d. API Documentation
- **Prompt:**  
  _"Provide comprehensive documentation for the Downtime API endpoints, including request and response formats, parameters, and example usages."_

---

## 3. Map, Review, and Fix Mobile UI and API Integration

### a. Map Mobile UI to API
- **Prompt:**  
  _"Map the fields from the mobile `DowntimeModel` in `downtimeModel.dart` to the corresponding API model in `DowntimeModel.cs`. Identify any discrepancies and suggest necessary adjustments."_

### b. Review Data Mapping
- **Prompt:**  
  _"Review the data mapping between the TTF_MES_Mobile app and the API for the Downtime feature. Ensure that all fields are correctly mapped and that data types are consistent."_

### c. Fix Mapping Issues
- **Prompt:**  
  _"Identify and fix any mapping issues between the mobile UI (`downtimeModel.dart`) and the API (`DowntimeModel.cs`). Update the models and serialization logic to ensure data integrity."_

### d. Validate API Integration
- **Prompt:**  
  _"Perform a thorough review of the API integration in `downtimeFunction.dart`. Ensure that all API calls are correctly implemented, handle errors gracefully, and align with the backend endpoints."_

---

## 4. Create Admin Dashboard with CRUD and Excel Export/Import

### a. Design Admin Dashboard UI
- **Prompt:**  
  _"Design an Admin Dashboard UI for managing downtime records. The dashboard should include a list view with CRUD operations and options to export and import data via Excel."_

### b. Implement CRUD Operations
- **Prompt:**  
  _"Develop the Admin Dashboard pages in `AdminDashboardPage.dart` with functionalities to create, read, update, and delete downtime records. Ensure seamless integration with the existing API."_

### c. Export to Excel
- **Prompt:**  
  _"Implement an export feature in the Admin Dashboard that allows administrators to export downtime records to an Excel file. Ensure the exported data includes all relevant fields and is properly formatted."_

### d. Import from Excel
- **Prompt:**  
  _"Create an import feature in the Admin Dashboard to allow administrators to upload Excel files and populate the downtime records. Include validation to check data integrity before importing."_

### e. SQL Script for Admin Features
- **Prompt:**  
  _"Provide SQL scripts necessary to support the Admin Dashboard functionalities, including any additional tables or stored procedures required for exporting and importing Excel data."_

```sql
-- Stored Procedure for Exporting Downtime Data
CREATE PROCEDURE usp_ExportDowntimeData
AS
BEGIN
    SELECT 
        DowntimeId,
        DepartmentCode,
        StepCode,
        VerifierRole,
        StartTime,
        EndTime,
        Reason,
        CreatedAt,
        UpdatedAt
    FROM Downtime
END
```

### f. API Endpoints for Admin Operations
- **Prompt:**  
  _"Develop additional API endpoints required for the Admin Dashboard, such as bulk import and export operations. Ensure these endpoints are secure and efficient."_

---

## Example Prompt Sequences for Each Case

### 1. Mobile UI Design & CRUD Implementation
1. _"Design the mobile UI for the Downtime feature, including list and detail pages."_
2. _"Implement CRUD operations for the Downtime list page using mock data."_
3. _"Develop the Downtime detail page with edit and delete functionalities using mock data."_
4. _"Ensure the mobile UI adheres to best practices for usability and responsiveness."_

### 2. API & Database Creation
1. _"Create RESTful API endpoints for managing downtime records based on the mobile UI design."_
2. _"Develop API controllers with proper validation and error handling for the Downtime feature."_
3. _"Write SQL scripts to create the Downtime table with fields matching the mobile model."_
4. _"Document the Downtime API endpoints with example requests and responses."_

### 3. Mapping and Review
1. _"Map the fields from `downtimeModel.dart` to `DowntimeModel.cs` and identify any discrepancies."_
2. _"Review the data mapping between the mobile app and API to ensure consistency."_
3. _"Fix any identified mapping issues and update the models accordingly."_
4. _"Validate the API integration in `downtimeFunction.dart` for correct implementation."_

### 4. Admin Dashboard Development
1. _"Design the Admin Dashboard UI for managing downtime records with CRUD functionalities."_
2. _"Implement CRUD operations in the Admin Dashboard, ensuring integration with the API."_
3. _"Add an export feature to the Admin Dashboard to download downtime records as Excel files."_
4. _"Create an import feature in the Admin Dashboard to upload Excel files and populate downtime records."_
5. _"Provide SQL scripts and API endpoints necessary for export and import operations in the Admin Dashboard."_

---

## Best Practices for Crafting AI Prompts

1. **Be Specific**
   - Mention exact file names and locations.
   - Reference specific fields, functions, or UI components.
   - Indicate the expected behavior or outcome.

2. **Provide Context**
   - Include relevant code snippets or documentation excerpts.
   - Explain the purpose and scope of the changes.
   - Reference related components or modules.

3. **Request Incremental Changes**
   - Break down large tasks into smaller, manageable steps.
   - Review and verify each step before proceeding to the next.

4. **Include Examples**
   - Provide sample data or expected formats.
   - Demonstrate use cases to clarify requirements.

5. **Request Verification**
   - Ask for validation checks and test cases.
   - Confirm mappings and data integrity between components.

---

Feel free to adjust these prompts to better fit your project's specific needs. If you require further customization or assistance with specific parts of your project, let me know!