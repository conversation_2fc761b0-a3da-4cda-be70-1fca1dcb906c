﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ZTB_PPR02", Schema = "AI_Production")]
    public partial class ZTB_PPR02
    {
        public int? Uu_tien { get; set; }
        [Key]
        [StringLength(50)]
        public string Dot_san_xuat { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Ngay_bat_dau_LSX { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Ngay_ket_thuc_LSX { get; set; }
        [Key]
        [StringLength(20)]
        public string SD_Document { get; set; }
        [Key]
        [StringLength(20)]
        public string Component_trong_BOM { get; set; }
        [StringLength(255)]
        public string Dien_giai_ma_component { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? So_Luong_NVL_Theo_Dinh_Muc { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? So_Luong_NVL_da_cap_261 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Ton_kho_theo_WBS { get; set; }
        [StringLength(10)]
        public string Plant { get; set; }
        public int? ID { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Total_Order_Quantity_1 { get; set; }
        [StringLength(10)]
        public string Unit_of_measure { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Total_Order_Quantity_2 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Delivered_quantity_1 { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_Go { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_Go { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_VAN_VER_DDC_KEO_S { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Delivered_quantity_2 { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_VAN_VER_DDC_KEO_S { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_BTP_GC { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_BTP_GC { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_VAT_TU_SOFA { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_VAT_TU_SOFA { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_VAT_TU { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_VAT_TU { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_HOA_CHAT { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_HOA_CHAT { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_BBPL { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_BBPL { get; set; }
        [StringLength(50)]
        public string SL_san_pham_dong_bo_KEO_CT { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Deadline_SP_dong_bo_KEO_CT { get; set; }
        [StringLength(10)]
        public string Base_Unit_of_Measure { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? So_Luong_NVL_da_chuyen_kho_xuong { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Con_lai_chua_chuyen_kho_xuong { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Thua_thieu_so_voi_ton_kho { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? So_luong_con_lai_chua_nhap_kho_theo_PO { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? SL_PR_chua_keo_PO { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? SL_chua_keo_PR { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Ngay_dong_bo_TT_chuyen_xuong { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Gia_tri_con_thieu { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Tru_ton_kho_xuong { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Tong_SLTP_chua_NK { get; set; }
        [StringLength(100)]
        public string Material_Group { get; set; }
        public int? Leadtime { get; set; }
        [StringLength(20)]
        public string Ma_HCM { get; set; }
        [StringLength(100)]
        public string Ten_HCM { get; set; }
    }
}