﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Profile_Opportunity_InternalModel", Schema = "Customer")]
    public partial class Profile_Opportunity_InternalModel
    {
        [Key]
        public Guid OpportunityInternalId { get; set; }
        public Guid? ProfileId { get; set; }
        public Guid? InternalId { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public bool? IsMain { get; set; }

        [ForeignKey("InternalId")]
        [InverseProperty("Profile_Opportunity_InternalModelInternal")]
        public virtual ProfileModel Internal { get; set; }
        [ForeignKey("ProfileId")]
        [InverseProperty("Profile_Opportunity_InternalModelProfile")]
        public virtual ProfileModel Profile { get; set; }
    }
}