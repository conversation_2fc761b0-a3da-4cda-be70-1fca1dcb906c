﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SearchResultDetailTemplateModel", Schema = "utilities")]
    public partial class SearchResultDetailTemplateModel
    {
        [Key]
        public Guid SearchResultDetailTemplateId { get; set; }
        public Guid? SearchResultTemplateId { get; set; }
        public int? PivotArea { get; set; }
        [StringLength(200)]
        public string FieldName { get; set; }
        [StringLength(200)]
        public string Caption { get; set; }
        [StringLength(200)]
        public string CellFormat_FormatType { get; set; }
        [StringLength(200)]
        public string CellFormat_FormatString { get; set; }
        public int? AreaIndex { get; set; }
        public bool? Visible { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public bool? Resize { get; set; }
        public bool? Tree { get; set; }

        [ForeignKey("SearchResultTemplateId")]
        [InverseProperty("SearchResultDetailTemplateModel")]
        public virtual SearchResultTemplateModel SearchResultTemplate { get; set; }
    }
}