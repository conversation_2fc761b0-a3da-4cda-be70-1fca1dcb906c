﻿using iMES_API.Infrastructures;
using iMES_API.Infrastructures.JobSchedulers;
using iMES_API.ServiceExtensions;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.DataProtection.Repositories;
using Microsoft.Extensions.DependencyInjection;
using NuGet.Protocol.Core.Types;
using System.Configuration;
using ISD.API.Repositories.Utility;

namespace ISD.API.ServiceExtensions
{
    public static class ServicesConfiguration
    {

        public static void AddUnitOfwork(this IServiceCollection services)
        {

            //services.AddSingleton<EntityDataContext>();
            //services.AddSingleton<UnitOfWork>();
            services.AddScoped<UnitOfWork>();
            services.AddScoped<UserTokens>();
            services.AddScoped<IOneSignalService, OneSignalService>();
            services.AddScoped<SapConnectService>();


            services.AddTransient<EmailService>();
        }
        public static void AddSyncData(this IServiceCollection services)
        {

        }

        public static void AddRepositories(this IServiceCollection services)
        {
            services.AddTransient<IQuestionBankRepository, QuestionBankRepository>();
            services.AddTransient<IUploadFilesLibrary, UploadFilesLibrary>();
            services.AddScoped<IDowntimeRepository, DowntimeRepository>();
            services.AddScoped<ITyLeTieuHaoRepository, TyLeTieuHaoRepository>();
            // IMaintenanceOrderRepository
            services.AddScoped<IMaintenanceOrderRepository, MaintenanceOrderRepository>();
            // SAP Table Service
            services.AddScoped<ISapTableService, SapTableService>();
        }

        public static void AddCustomServices(this IServiceCollection services)
        {
            services.AddScoped<IQuestionBankService, QuestionBankService>();

        }
    }
}
