﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PagePermissionModel", Schema = "pms")]
    public partial class PagePermissionModel
    {
        [Key]
        public Guid RolesId { get; set; }
        [Key]
        public Guid PageId { get; set; }
        [Key]
        [StringLength(50)]
        public string FunctionId { get; set; }

        [ForeignKey("FunctionId")]
        [InverseProperty("PagePermissionModel")]
        public virtual FunctionModel Function { get; set; }
        [ForeignKey("PageId")]
        [InverseProperty("PagePermissionModel")]
        public virtual PageModel Page { get; set; }
        [ForeignKey("RolesId")]
        [InverseProperty("PagePermissionModel")]
        public virtual RolesModel Roles { get; set; }
    }
}