class GetQuantitySampleModel {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetQuantitySample? data;

  GetQuantitySampleModel({this.code, this.isSuccess, this.message, this.data});

  GetQuantitySampleModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetQuantitySample.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetQuantitySample {
  String? sampMethodId;
  String? sampleSize;
  int? sampleSizeFrom;
  int? sampleSizeTo;
  String? sampleName;
  int? sampleQuantity;
  DateTime? createTime;
  String? createBy;
  bool? actived;
  int? aql025;
  int? aql040;
  int? aql065;
  int? aql100;
  int? aql150;
  int? aql250;
  int? aql400;
  int? aql650;
  int? aql1000;
  int? aql1500;
  int? quantitySample;

  DataGetQuantitySample({
    this.sampMethodId,
    this.sampleSize,
    this.sampleSizeFrom,
    this.sampleSizeTo,
    this.sampleName,
    this.sampleQuantity,
    this.createTime,
    this.createBy,
    this.actived,
    this.aql025,
    this.aql040,
    this.aql065,
    this.aql100,
    this.aql150,
    this.aql250,
    this.aql400,
    this.aql650,
    this.aql1000,
    this.aql1500,
    this.quantitySample,
  });

  dynamic getProperty(String name) {
    switch (name) {
      case 'sampMethodId':
        return sampMethodId;
      case 'sampleSize':
        return sampleSize;
      case 'sampleSizeFrom':
        return sampleSizeFrom;
      case 'sampleSizeTo':
        return sampleSizeTo;
      case 'sampleName':
        return sampleName;
      case 'sampleQuantity':
        return sampleQuantity;
      case 'createTime':
        return createTime;
      case 'createBy':
        return createBy;
      case 'actived':
        return actived;
      case 'aql025':
        return aql025;
      case 'aql040':
        return aql040;
      case 'aql065':
        return aql065;
      case 'aql100':
        return aql100;
      case 'aql150':
        return aql150;
      case 'aql250':
        return aql250;
      case 'aql400':
        return aql400;
      case 'aql650':
        return aql650;
      case 'aql1000':
        return aql1000;
      case 'aql1500':
        return aql1500;
      case 'quantitySample':
        return quantitySample;
      default:
        return null;
    }
  }

  DataGetQuantitySample.fromJson(Map<String, dynamic> json) {
    sampMethodId = json['sampMethodId'];
    sampleSize = json['sampleSize'];
    sampleSizeFrom = json['sampleSizeFrom'];
    sampleSizeTo = json['sampleSizeTo'];
    sampleName = json['sampleName'];
    sampleQuantity = json['sampleQuantity'];
    createTime = (json['createTime'] != null) ? DateTime.parse(json['createTime']) : null;
    createBy = json['createBy'];
    actived = json['actived'];
    aql025 = json['aql025'];
    aql040 = json['aql040'];
    aql065 = json['aql065'];
    aql100 = json['aql100'];
    aql150 = json['aql150'];
    aql250 = json['aql250'];
    aql400 = json['aql400'];
    aql650 = json['aql650'];
    aql1000 = json['aql1000'];
    aql1500 = json['aql1500'];
    quantitySample = json['quantitySample'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sampMethodId'] = sampMethodId;
    data['sampleSize'] = sampleSize;
    data['sampleSizeFrom'] = sampleSizeFrom;
    data['sampleSizeTo'] = sampleSizeTo;
    data['sampleName'] = sampleName;
    data['sampleQuantity'] = sampleQuantity;
    data['createTime'] = createTime?.toIso8601String();
    data['createBy'] = createBy;
    data['actived'] = actived;
    data['aql025'] = aql025;
    data['aql040'] = aql040;
    data['aql065'] = aql065;
    data['aql100'] = aql100;
    data['aql150'] = aql150;
    data['aql250'] = aql250;
    data['aql400'] = aql400;
    data['aql650'] = aql650;
    data['aql1000'] = aql1000;
    data['aql1500'] = aql1500;
    data['quantitySample'] = quantitySample;
    return data;
  }
}
