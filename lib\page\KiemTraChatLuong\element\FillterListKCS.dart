import 'dart:async';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/element/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import '../../../model/drawerFilterQC.dart';
import '../../../model/getListQCByFilter.dart';
import '../../../model/postFilterQC.dart';
import '../../../repository/function/listQcFunction.dart';
import '../../../repository/showDateTime.dart';
import '../../../element/DropdownLsQC.dart';
import '../../../element/RowTextFieldLsQC.dart';
import '../../../element/RowTimeLsQC.dart';
import '../../../model/FilterLsQC.dart';

class FillterListKCS extends StatefulWidget {
  final List<SalesOrgCodes>? salesOrgCodes;
  final List<WorkCenters>? workCenters;
  final List<WorkShops>? workShops;
  final List<CommonDates>? commonDates;
  final List<ResultsDataQC>? results;
  final List<StatusData>? lsStatus;
  final CommonDateModel commonDateModel;
  final FilterQCModel filterLSQC;
  final String token;
  final void Function(List<DrawerFilterQC>) onFilterSelected;
  const FillterListKCS(
      {Key? key,
      required this.salesOrgCodes,
      required this.workCenters,
      required this.workShops,
      required this.commonDates,
      required this.results,
      required this.lsStatus,
      required this.commonDateModel,
      required this.filterLSQC,
      required this.token,
      required this.onFilterSelected})
      : super(key: key);

  @override
  _FillterListKCSState createState() => _FillterListKCSState();
}

class _FillterListKCSState extends State<FillterListKCS> {
  final _controllerCustomerName = TextEditingController();
  final _controllerCodeProduct = TextEditingController();
  final _controllerCodeCustomer = TextEditingController();
  final _controllerLSXSAP = TextEditingController();
  final _controllerProduct = TextEditingController();
  final _focusCustomerName = FocusNode();
  final _focusCodeProduct = FocusNode();
  final _focusCodeCustomer = FocusNode();
  final _focusLSXSAP = FocusNode();
  final _focusProduct = FocusNode();

  SalesOrgCodes? _selectedSalesOrgCodes;
  WorkCenters? _selectedWorkCenters;
  WorkShops? _selectedWorkShops;
  CommonDates? _selectedCommonDates;
  ResultsDataQC? _selectedResultsDataQC;
  StatusData? _selectedStatusData;
  CommonDates? _selectedTimeQC;
  String? _fromTime;
  String? _fromTimeQC;
  String? _toTime;
  String? _toTimeQC;
  bool _isLoading = false;
  bool _isLoadingTimeQC = false;
  bool _isLoadingFilter = false;
  bool _showErrorNoti = false;
  String _messageErrorNoti = "";

  List<DataListQC>? _getDataFilter;
  String _notFoundFilter = "";
  final List<DrawerFilterQC> _lsDrawerFilterQC = [];
  FilterQCVm? _postFilterQC;

  DateTime? _fromdateComplete;
  DateTime? _todateComplete;
  DateTime? _fromdateQC;
  DateTime? _todateQC;

  // late final KeyboardVisibilityController _keyboardVisibilityController;
  // late StreamSubscription<bool> _keyboardSubscription;

  void _setSelectedSalesOrgCodes(SalesOrgCodes? value) {
    setState(() {
      _selectedSalesOrgCodes = value!;
    });
  }

  void _setSelectedworkCenters(WorkCenters? value) {
    setState(() {
      _selectedWorkCenters = value!;
    });
  }

  void _setSelectedWorkShops(WorkShops? value) {
    setState(() {
      _selectedWorkShops = value!;
    });
  }

  Future<void> _setSelectedCommonDates(CommonDates? value, BuildContext context) async {
    setState(() {
      _selectedCommonDates = value!;
    });
    if (_selectedCommonDates!.catalogCode != "Custom") {
      await _getCommonDate(_selectedCommonDates, context);
    } else {
      return;
    }
  }

  void _setSelectedResultsDataQC(ResultsDataQC? value) {
    setState(() {
      _selectedResultsDataQC = value!;
    });
  }

  void _setSelectedStatusData(StatusData? value) {
    setState(() {
      _selectedStatusData = value!;
    });
  }

  Future<void> _setSelectedTimeQC(CommonDates? value, BuildContext context) async {
    setState(() {
      _selectedTimeQC = value!;
    });
    if (_selectedTimeQC!.catalogCode != "Custom") {
      await _getCommonDateQC(_selectedTimeQC, context);
    } else {
      return;
    }
  }

  Future<void> _getCommonDate(CommonDates? selectedCommonDates, BuildContext context) async {
    try {
      setState(() {
        _isLoading = true;
      });
      final getCommonDateModel = await ListQCFunction.getCommonDateModel(selectedCommonDates!.catalogCode.toString(), widget.token);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _fromTime = getCommonDateModel.fromDate;
        _toTime = getCommonDateModel.toDate;
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _messageErrorNoti = "Không có kết nối mạng!";
        _showErrorNoti = true;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _messageErrorNoti = error.toString();
        _showErrorNoti = true;
      });
    }
  }

  Future<void> _getCommonDateQC(CommonDates? selectedCommonDates, BuildContext context) async {
    try {
      setState(() {
        _isLoadingTimeQC = true;
      });
      final getCommonDateModel = await ListQCFunction.getCommonDateModel(selectedCommonDates!.catalogCode!, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoadingTimeQC = false;
        setState(() {
          _fromTimeQC = getCommonDateModel.fromDate;
          _toTimeQC = getCommonDateModel.toDate;
        });
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingTimeQC = false;
        _showErrorNoti = true;
        _messageErrorNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingTimeQC = false;
        _messageErrorNoti = error.toString();
        _showErrorNoti = true;
      });
    }
  }

  Future<void> _postListQCApi(BuildContext context) async {
    try {
      setState(() {
        _isLoadingFilter = true;
        _postFilterQC = FilterQCVm(
          (_selectedSalesOrgCodes ??
                  widget.salesOrgCodes!.firstWhere((element) => element.saleOrgCode == widget.filterLSQC.additionalData!.selectedSalesOrgCode))
              .saleOrgCode,
          (_selectedWorkShops ?? widget.workShops!.firstWhere((element) => element.workShopCode == null)).workShopCode,
          (_selectedWorkCenters ?? widget.workCenters!.firstWhere((element) => element.workCenterCode == null)).workCenterCode,
          _controllerCodeCustomer.text.isNotEmpty ? _controllerCodeCustomer.text : null,
          _controllerCustomerName.text.isNotEmpty ? _controllerCustomerName.text : null,
          _controllerLSXSAP.text.isNotEmpty ? _controllerLSXSAP.text : null,
          _controllerCodeProduct.text.isNotEmpty ? _controllerCodeProduct.text : null,
          _controllerProduct.text.isNotEmpty ? _controllerProduct.text : null,
          (_selectedStatusData ?? widget.lsStatus!.firstWhere((element) => element.status == null)).status,
          (_selectedResultsDataQC ?? widget.results!.firstWhere((element) => element.catalogCode == null)).catalogCode,
          (_selectedCommonDates ??
                  widget.commonDates!.firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedConfirmCommonDate))
              .catalogCode,
          _fromTime ?? widget.commonDateModel.fromDate,
          _toTime ?? widget.commonDateModel.toDate,
          (_selectedTimeQC ??
                  widget.commonDates!.firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedQCCommonDate))
              .catalogCode,
          _fromTimeQC,
          _toTimeQC,
          null,
        );
      });
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final dataResponse = await ListQCFunction.fetchQCKCSFiltered(widget.token, _postFilterQC!);
      Navigator.pop(context);
      if (dataResponse != null) {
        if (!mounted) return;
        setState(() {
          _isLoadingFilter = false;
          _getDataFilter = dataResponse.data;
        });
        final dataDrawerFilterQC = DrawerFilterQC(getDataFilter: _getDataFilter, notFoundFilter: _notFoundFilter, postFilterQC: _postFilterQC);
        _lsDrawerFilterQC.add(dataDrawerFilterQC);
        widget.onFilterSelected(_lsDrawerFilterQC);
        Navigator.pop(context);
      } else {
        if (!mounted) return;
        setState(() {
          _isLoadingFilter = false;
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
        final dataDrawerFilterQC = DrawerFilterQC(getDataFilter: _getDataFilter, notFoundFilter: _notFoundFilter, postFilterQC: _postFilterQC);
        _lsDrawerFilterQC.add(dataDrawerFilterQC);
        widget.onFilterSelected(_lsDrawerFilterQC);
        Navigator.pop(context);
      }
    } on SocketException catch (_) {
      if (_isLoadingFilter == true) {
        Navigator.pop(context);
      }
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _isLoading = false;
        _messageErrorNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      Navigator.pop(context); // hide spinner
      setState(() {
        _showErrorNoti = true;
        _isLoading = false;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateToComplete(BuildContext context) async {
    try {
      DateTime initialDate = DateTime.now();
      final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _todateComplete);
      if (newDate == null) return;
      setState(() {
        _todateComplete = newDate;
        _toTime = DateFormat("yyyy-MM-dd").format(_todateComplete!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateFromComplete(BuildContext context) async {
    try {
      DateTime initialDate = DateTime.now();
      final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _fromdateComplete);
      if (newDate == null) return;
      setState(() {
        _fromdateComplete = newDate;
        _fromTime = DateFormat("yyyy-MM-dd").format(_fromdateComplete!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateToQC(BuildContext context) async {
    try {
      DateTime initialDate = DateTime.now();
      final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _todateQC);
      if (newDate == null) return;
      setState(() {
        _todateQC = newDate;
        _toTimeQC = DateFormat("yyyy-MM-dd").format(_todateQC!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateFromQC(BuildContext context) async {
    try {
      DateTime initialDate = DateTime.now();
      final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _fromdateQC);
      if (newDate == null) return;
      setState(() {
        _fromdateQC = newDate;
        _fromTimeQC = DateFormat("yyyy-MM-dd").format(_fromdateQC!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateIOSToComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _todateComplete = newDate;
        _toTime = DateFormat("yyyy-MM-dd").format(_todateComplete!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateIOSFormComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _fromdateComplete = newDate;
        _fromTime = DateFormat("yyyy-MM-dd").format(_fromdateComplete!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateIOSToQC(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _todateQC = newDate;
        _toTimeQC = DateFormat("yyyy-MM-dd").format(_todateQC!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _pickDateIOSFromQC(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _fromdateQC = newDate;
        _fromTimeQC = DateFormat("yyyy-MM-dd").format(_fromdateQC!);
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  @override
  void dispose() {
    _focusCustomerName.dispose();
    _focusCodeProduct.dispose();
    _focusCodeCustomer.dispose();
    _focusLSXSAP.dispose();
    _focusProduct.dispose();
    _controllerCustomerName.dispose();
    _controllerCodeProduct.dispose();
    _controllerCodeCustomer.dispose();
    _controllerLSXSAP.dispose();
    _controllerProduct.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);

            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Container(
            margin: EdgeInsets.only(top: 40.h),
            child: Drawer(
              backgroundColor: Colors.white,
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                          color: const Color(0xff0052cc),
                          child: Text(
                            "Tìm kiếm phiếu QC",
                            style: TextStyle(fontSize: 15.sp, color: Colors.white),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              DropdownSalesOrgCodes(
                                title: 'Nhà máy',
                                onTap: (value) => _setSelectedSalesOrgCodes(value),
                                lsSalesOrgCodes: widget.salesOrgCodes,
                                selectedSalesOrgCodes: _selectedSalesOrgCodes ??
                                    widget.salesOrgCodes!
                                        .firstWhere((element) => element.saleOrgCode == widget.filterLSQC.additionalData!.selectedSalesOrgCode),
                              ),
                              SizedBox(height: 10.h),
                              DropdownworkShops(
                                title: 'Phân xưởng',
                                onTap: (value) => _setSelectedWorkShops(value),
                                lsWorkShops: widget.workShops,
                                selectedWorkShops: _selectedWorkShops ?? widget.workShops!.firstWhere((element) => element.workShopCode == null),
                              ),
                              SizedBox(height: 10.h),
                              DropdownworkCenters(
                                title: 'Công đoạn lớn',
                                onTap: (value) => _setSelectedworkCenters(value),
                                lsWorkCenters: widget.workCenters,
                                selectedWorkCenters:
                                    _selectedWorkCenters ?? widget.workCenters!.firstWhere((element) => element.workCenterCode == null),
                              ),
                              SizedBox(height: 10.h),
                              RowTextFieldLsQC(controller: _controllerCodeCustomer, title: 'Mã KH', focusNode: _focusCodeCustomer),
                              SizedBox(height: 10.h),
                              RowTextFieldLsQC(controller: _controllerCustomerName, title: 'Tên KH', focusNode: _focusCustomerName),
                              SizedBox(height: 10.h),
                              RowTextFieldLsQC(controller: _controllerLSXSAP, title: 'LSX SAP', focusNode: _focusLSXSAP),
                              SizedBox(height: 10.h),
                              RowTextFieldLsQC(controller: _controllerCodeProduct, title: 'Mã sản phẩm', focusNode: _focusCodeProduct),
                              SizedBox(height: 10.h),
                              RowTextFieldLsQC(controller: _controllerProduct, title: 'Sản phẩm', focusNode: _focusProduct),
                              SizedBox(height: 10.h),
                              DropdownStatusData(
                                title: 'Trạng thái',
                                onTap: (value) => _setSelectedStatusData(value),
                                lsStatusData: widget.lsStatus,
                                selectedStatusData: _selectedStatusData ?? widget.lsStatus!.firstWhereOrNull((element) => element.status == null),
                              ),
                              SizedBox(height: 10.h),
                              Dropdownresults(
                                title: 'Kết quả',
                                onTap: (value) => _setSelectedResultsDataQC(value),
                                lsResultsDataQC: widget.results,
                                selectedResultsDataQC: _selectedResultsDataQC ?? widget.results!.firstWhere((element) => element.catalogCode == null),
                              ),
                              SizedBox(height: 10.h),
                              DropdownCommonDates(
                                title: 'Thời gian hoàn thành công đoạn lớn',
                                onTap: (value) => _setSelectedCommonDates(value, context),
                                lsWorkCommonDates: widget.commonDates,
                                selectedCommonDates: _selectedCommonDates ??
                                    widget.commonDates!
                                        .firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedConfirmCommonDate),
                              ),
                              SizedBox(height: 10.h),
                              (_selectedCommonDates != null
                                      ? _selectedCommonDates!.catalogCode == "Custom"
                                      : widget.commonDates!
                                              .firstWhere(
                                                  (element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedConfirmCommonDate)
                                              .catalogCode ==
                                          "Custom")
                                  ? ColumnDateLsQC(
                                      title: 'Từ ngày',
                                      date: () => Platform.isAndroid ? _pickDateFromComplete(context) : _pickDateIOSFormComplete(context),
                                      displayDate: _fromTime == null
                                          ? "mm/dd/yyyy"
                                          : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!)))
                                  : RowTimeLsQC(
                                      title: 'Từ ngày',
                                      time: _isLoading == true
                                          ? "Loading..."
                                          : DateFormat("dd/MM/yyyy")
                                              .format(DateFormat("yyyy-MM-dd").parse(_fromTime ?? widget.commonDateModel.fromDate.toString())),
                                    ),
                              SizedBox(height: 10.h),
                              (_selectedCommonDates != null
                                      ? _selectedCommonDates!.catalogCode == "Custom"
                                      : widget.commonDates!
                                              .firstWhere(
                                                  (element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedConfirmCommonDate)
                                              .catalogCode ==
                                          "Custom")
                                  ? ColumnDateLsQC(
                                      title: 'Đến ngày',
                                      date: () => Platform.isAndroid ? _pickDateToComplete(context) : _pickDateIOSToComplete(context),
                                      displayDate:
                                          _toTime == null ? "mm/dd/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!)),
                                    )
                                  : RowTimeLsQC(
                                      title: 'Đến ngày',
                                      time: _isLoading == true
                                          ? "Loading..."
                                          : DateFormat("dd/MM/yyyy")
                                              .format(DateFormat("yyyy-MM-dd").parse(_toTime ?? widget.commonDateModel.toDate.toString())),
                                    ),
                              SizedBox(height: 10.h),
                              DropdownTimeQC(
                                title: 'Thời gian QC',
                                onTap: (value) => _setSelectedTimeQC(value, context),
                                lsTimeQC: widget.commonDates,
                                selectedTimeQC: _selectedTimeQC ??
                                    widget.commonDates!
                                        .firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedQCCommonDate),
                              ),
                              SizedBox(height: 10.h),
                              (_selectedTimeQC != null
                                      ? _selectedTimeQC!.catalogCode == "Custom"
                                      : widget.commonDates!
                                              .firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedQCCommonDate)
                                              .catalogCode ==
                                          "Custom")
                                  ? ColumnDateLsQC(
                                      title: 'Từ ngày',
                                      date: () => Platform.isAndroid ? _pickDateFromQC(context) : _pickDateIOSFromQC(context),
                                      displayDate: _fromTimeQC == null
                                          ? "mm/dd/yyyy"
                                          : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTimeQC!)),
                                    )
                                  : RowTimeLsQC(
                                      title: 'Từ ngày',
                                      time: _isLoadingTimeQC == true
                                          ? "Loading..."
                                          : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTimeQC.toString())),
                                    ),
                              SizedBox(height: 10.h),
                              (_selectedTimeQC != null
                                      ? _selectedTimeQC!.catalogCode == "Custom"
                                      : widget.commonDates!
                                              .firstWhere((element) => element.catalogCode == widget.filterLSQC.additionalData!.selectedQCCommonDate)
                                              .catalogCode ==
                                          "Custom")
                                  ? ColumnDateLsQC(
                                      title: 'Đến ngày',
                                      date: () => Platform.isAndroid ? _pickDateToQC(context) : _pickDateIOSToQC(context),
                                      displayDate: _toTimeQC == null
                                          ? "mm/dd/yyyy"
                                          : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTimeQC!)),
                                    )
                                  : RowTimeLsQC(
                                      title: 'Đến ngày',
                                      time: _isLoadingTimeQC == true
                                          ? "Loading..."
                                          : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTimeQC.toString())),
                                    ),
                              SizedBox(height: 10.h),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  style: ButtonStyle(
                                    padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                    side: MaterialStateProperty.all(
                                      const BorderSide(
                                        color: Color(0xff0052cc),
                                      ),
                                    ),
                                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                  ),
                                  onPressed: () async {
                                    await _postListQCApi(context);
                                  },
                                  icon: Icon(
                                    Icons.search,
                                    size: 23.sp,
                                    color: Colors.white,
                                  ),
                                  label: Text(
                                    "Tìm kiếm",
                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              SizedBox(height: 100.h),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: _showErrorNoti,
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                      width: double.infinity,
                      decoration: BoxDecoration(color: Colors.red.shade900),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 9,
                            child: Text(
                              _messageErrorNoti,
                              style: TextStyle(fontSize: 12.sp, color: Colors.white),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: IconButton(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                onPressed: () {
                                  setState(() {
                                    _showErrorNoti = false;
                                  });
                                },
                                icon: const Icon(Icons.cancel),
                                iconSize: 15.sp,
                                color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
