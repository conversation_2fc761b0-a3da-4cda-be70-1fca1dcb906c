class GetSOWBSByTask {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetSOWBSByTask? data;


  GetSOWBSByTask(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetSOWBSByTask.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetSOWBSByTask.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetSOWBSByTask {
  String? so;
  String? soLine;
  String? wbs;

  DataGetSOWBSByTask({this.so, this.soLine, this.wbs});

  DataGetSOWBSByTask.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    return data;
  }
}