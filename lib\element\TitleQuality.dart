import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/qualityControlApi.dart';

class TitleQuality extends StatelessWidget {
  final String title;
  const TitleQuality({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
    );
  }
}

class TitleDateTimeQuality extends StatelessWidget {
  final String date;
  const TitleDateTimeQuality({Key? key, required this.date}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 9,
              child: Text(
                date,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
            Expanded(flex: 1, child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue)),
          ],
        ));
  }
}

class TextBox extends StatelessWidget {
  final String? text;
  const TextBox({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
      decoration: BoxDecoration(
        border: Border.all(width: 0.5, color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(3.r),
      ),
      child: Text(
        text!,
        style: TextStyle(color: Colors.black, fontSize: 11.sp),
      ),
    );
  }
}

class TitleStaff extends StatelessWidget {
  final QualityCheckerInfo? selectedStaff;
  const TitleStaff({
    Key? key,
    required this.selectedStaff,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
      decoration: BoxDecoration(
        border: Border.all(width: 0.5, color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(3.r),
      ),
      child: Text(
        selectedStaff == null ? "" : selectedStaff!.salesEmployeeName ?? "",
        style: TextStyle(color: Colors.black, fontSize: 11.sp),
      ),
    );
  }
}

class QualityTitleField extends StatelessWidget {
  final String title;
  const QualityTitleField({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
    );
  }
}
