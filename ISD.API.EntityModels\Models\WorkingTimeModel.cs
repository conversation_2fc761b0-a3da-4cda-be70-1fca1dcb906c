﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WorkingTimeModel", Schema = "ghService")]
    public partial class WorkingTimeModel
    {
        public WorkingTimeModel()
        {
            WorkingTimeDetailModel = new HashSet<WorkingTimeDetailModel>();
        }

        [Key]
        public Guid WorkingTimeId { get; set; }
        public int? DayOfWeek { get; set; }
        public TimeSpan? FromTime { get; set; }
        public TimeSpan? ToTime { get; set; }

        [InverseProperty("WorkingTime")]
        public virtual ICollection<WorkingTimeDetailModel> WorkingTimeDetailModel { get; set; }
    }
}