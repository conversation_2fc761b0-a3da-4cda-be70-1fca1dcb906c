﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StockModel", Schema = "Warehouse")]
    public partial class StockModel
    {
        public StockModel()
        {
            DeliveryDetailModel = new HashSet<DeliveryDetailModel>();
            StockReceivingDetailModel = new HashSet<StockReceivingDetailModel>();
            StockTransferRequestModelFromStockNavigation = new HashSet<StockTransferRequestModel>();
            StockTransferRequestModelToStockNavigation = new HashSet<StockTransferRequestModel>();
            Stock_Store_Mapping = new HashSet<Stock_Store_Mapping>();
            TransferDetailModel = new HashSet<TransferDetailModel>();
        }

        [Key]
        public Guid StockId { get; set; }
        [StringLength(50)]
        public string StockCode { get; set; }
        [StringLength(100)]
        public string StockName { get; set; }
        [StringLength(500)]
        public string Description { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }

        [InverseProperty("Stock")]
        public virtual ICollection<DeliveryDetailModel> DeliveryDetailModel { get; set; }
        [InverseProperty("Stock")]
        public virtual ICollection<StockReceivingDetailModel> StockReceivingDetailModel { get; set; }
        [InverseProperty("FromStockNavigation")]
        public virtual ICollection<StockTransferRequestModel> StockTransferRequestModelFromStockNavigation { get; set; }
        [InverseProperty("ToStockNavigation")]
        public virtual ICollection<StockTransferRequestModel> StockTransferRequestModelToStockNavigation { get; set; }
        [InverseProperty("Stock")]
        public virtual ICollection<Stock_Store_Mapping> Stock_Store_Mapping { get; set; }
        [InverseProperty("ToStock")]
        public virtual ICollection<TransferDetailModel> TransferDetailModel { get; set; }
    }
}