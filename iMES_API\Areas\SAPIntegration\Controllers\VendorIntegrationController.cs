﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class VendorIntegrationController : ControllerBaseAPI
    {
        //VendorIntegration
        #region VendorIntegration
        /// <summary>API "Tích hợp thông tin Vendor" - Thêm / Cậ<PERSON> nhật</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/VendorIntegration/Vendor
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        ///  
        ///      {
        ///        "supplierNumber": "test",
        ///        "shortName": "Le",
        ///        "longName": "LeAB",
        ///        "address": "HCM",
        ///        "telephone": "123456789"
        ///      }
        ///     
        /// OUT PUT
        /// 
        ///     {
        ///          "code": 200,
        ///          "isSuccess": true,
        ///          "message": "Tích hợp thành công Vendor",
        ///          "data": null
        ///     }
        ///</remarks>
        [HttpPost("Vendor")]
        public async Task<IActionResult> VendorIntegrationAsync([FromBody] VendorIntegrationViewModel IntegrationModel)
        {
            //check Vendor is exist
            var existVD = await _context.VendorModel.FirstOrDefaultAsync(x => x.SupplierNumber.Trim() == IntegrationModel.SupplierNumber.Trim());

            if (existVD != null)
            {
                #region Update
                //update Shortname
                existVD.ShortName = IntegrationModel.ShortName;
                //update LongName
                existVD.LongName = IntegrationModel.LongName;
                //update StorageBin
                existVD.Address = IntegrationModel.Address;
                //update StorageType
                existVD.Telephone = IntegrationModel.Telephone;
                //update LastEditIime
                existVD.LastEditTime = DateTime.Now;

                await _context.SaveChangesAsync();
                #endregion
            }
            else
            {
                #region Create

                existVD = new VendorModel();
                existVD.VendorId = Guid.NewGuid();
                existVD.SupplierNumber = IntegrationModel.SupplierNumber;
                existVD.ShortName = IntegrationModel.ShortName;
                existVD.LongName = IntegrationModel.LongName;
                existVD.Address = IntegrationModel.Address;
                existVD.Telephone = IntegrationModel.Telephone;
                existVD.CreateTime = DateTime.Now;
                existVD.Actived = true;

                _context.VendorModel.Add(existVD);  

                await _context.SaveChangesAsync();
                #endregion
            }
            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Tích hợp thành công Vendor", Data = existVD });
        }
        #endregion
    }
}
