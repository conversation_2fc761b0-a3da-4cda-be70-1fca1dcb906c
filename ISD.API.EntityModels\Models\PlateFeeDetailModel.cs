﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PlateFeeDetailModel", Schema = "tSale")]
    public partial class PlateFeeDetailModel
    {
        [Key]
        public Guid PlateFeeDetailId { get; set; }
        public Guid? PlateFeeId { get; set; }
        [StringLength(50)]
        public string Province { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? Price { get; set; }

        [ForeignKey("PlateFeeId")]
        [InverseProperty("PlateFeeDetailModel")]
        public virtual PlateFeeModel PlateFee { get; set; }
    }
}