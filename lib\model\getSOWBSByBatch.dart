class GetSOWBSByBatch {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataSOWBSByBatch>? data;

  GetSOWBSByBatch(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetSOWBSByBatch.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataSOWBSByBatch>[];
      json['data'].forEach((v) {
        data!.add(DataSOWBSByBatch.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataSOWBSByBatch {
  String? so;
  String? soLine;
  String? wbs;

  DataSOWBSByBatch({this.so, this.soLine, this.wbs});

  DataSOWBSByBatch.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    return data;
  }
}