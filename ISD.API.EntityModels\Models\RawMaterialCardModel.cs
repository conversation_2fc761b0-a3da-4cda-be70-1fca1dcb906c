﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RawMaterialCardModel", Schema = "MESP2")]
    public partial class RawMaterialCardModel
    {
        public RawMaterialCardModel()
        {
            RawMaterial_PurchaseOrderDetail_Mapping = new HashSet<RawMaterial_PurchaseOrderDetail_Mapping>();
        }

        [Key]
        public Guid RawMaterialCardId { get; set; }
        public int RawMaterialCardCode { get; set; }
        [StringLength(50)]
        public string RawMaterialCardType { get; set; }
        [StringLength(50)]
        public string VendorCode { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? POQuantity { get; set; }
        [StringLength(50)]
        public string POQuantityUnit { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity2 { get; set; }
        [StringLength(50)]
        public string Quantity2Unit { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity3 { get; set; }
        [StringLength(50)]
        public string Quantity3Unit { get; set; }
        [StringLength(50)]
        public string Specifications { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ManufacturingDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpirationDate { get; set; }
        [StringLength(500)]
        public string BarcodePath { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ReceiveDate { get; set; }
        public bool? IsReceive { get; set; }
        public bool? Actived { get; set; }
        public bool? isGoodsArrive { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? GoodsArriveDate { get; set; }
        [StringLength(50)]
        public string TDSCode { get; set; }

        [InverseProperty("RawMaterialCard")]
        public virtual RawMaterialCard_SO_Mapping RawMaterialCard_SO_Mapping { get; set; }
        [InverseProperty("RawMaterialCard")]
        public virtual RawMaterialCard_WBS_Mapping RawMaterialCard_WBS_Mapping { get; set; }
        [InverseProperty("RawMaterialCard")]
        public virtual ICollection<RawMaterial_PurchaseOrderDetail_Mapping> RawMaterial_PurchaseOrderDetail_Mapping { get; set; }
    }
}