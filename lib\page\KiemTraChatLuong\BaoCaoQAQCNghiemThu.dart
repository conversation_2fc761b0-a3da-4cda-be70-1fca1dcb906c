import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../../element/errorViewPost.dart';
import '../../element/timeOut.dart';
import '../../model/drawerFilterQC.dart';
import '../../model/getListQCByFilter.dart';
import '../../model/postFilterQC.dart';
import '../../element/FilterLsQC.dart';
import '../../model/qrCodePageListQCModel.dart';
import '../../repository/function/listQcFunction.dart';
import '../../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../LostConnect.dart';

class BaoCaoQAQCNghiemThu extends StatefulWidget {
  // final String token;
  // final String accountID;
  final String dateTimeOld;
  final DataUser user;
  const BaoCaoQAQCNghiemThu(
      {Key? key,
      // required this.token, required this.accountID,
      required this.dateTimeOld,
      required this.user})
      : super(key: key);

  @override
  _BaoCaoQAQCNghiemThuState createState() => _BaoCaoQAQCNghiemThuState();
}

class _BaoCaoQAQCNghiemThuState extends State<BaoCaoQAQCNghiemThu> {
  bool? _isLoading;
  ConnectivityResult _result = ConnectivityResult.none;
  DateTime? _stringToDateTimeConfirm;
  String? _dateFormatStringConfirm;
  DateTime? _stringToDateTimeQualityDate;
  String? _dateFormatStringQualityDate;
  bool _isNotWifi = false;
  FilterQCModel? _filterLSQC;
  CommonDateModel? _commonDateModel;
  List<DataListQC>? _defaultDataFilter = [];
  List<SalesOrgCodes>? _salesOrgCodes;
  List<WorkCenters>? _workCenters;
  List<WorkShops>? _workShops;
  List<CommonDates>? _commonDates;
  List<ResultsDataQC>? _results;
  String _notFoundFilter = " ";
  FilterQCVm? _postFilterQC;
  String _error = "";
  late bool _timeOut;
  bool? _disableButton;

  String title = 'Danh sách QAQC nghiệm thu';

  @override
  void initState() {
    super.initState();
    _fetchApi();
  }

  Future<void> _getDropDownListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _timeOut = false;
      });
      final dataDropdown = await ListQCFunction.getDefaultKCSFilter(widget.user.token.toString());
      if (dataDropdown == null) {
        if (!mounted) return;
        setState(() {
          _isLoading = true;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _filterLSQC = dataDropdown;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
        _timeOut = false;
      });
    }
  }

  Future<void> _getCommonDate() async {
    try {
      final dataCommonDateModel =
          await ListQCFunction.getCommonDateModel(_filterLSQC!.additionalData!.selectedConfirmCommonDate.toString(), widget.user.token.toString());
      if (!mounted) return;
      setState(() {
        _commonDateModel = dataCommonDateModel;
        _salesOrgCodes = _filterLSQC!.data!.salesOrgCodes;
        _salesOrgCodes!.insert(0, (ListQCFunction.defaultSalesOrgCodes));
        _workCenters = _filterLSQC!.data!.workCenters;
        _workCenters!.insert(0, (ListQCFunction.defaultWorkCenters));
        _results = _filterLSQC!.data!.results;
        _results!.insert(0, (ListQCFunction.defaultResultsDataQC));
        _workShops = _filterLSQC!.data!.workShops;
        _workShops!.insert(0, (ListQCFunction.defaultWorkShops));
        _commonDates = _filterLSQC!.data!.commonDates;
        _postFilterQC = FilterQCVm(
          _filterLSQC!.additionalData!.selectedSalesOrgCode,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          _filterLSQC!.additionalData!.selectedConfirmCommonDate,
          _commonDateModel!.fromDate,
          _commonDateModel!.toDate,
          null,
          null,
          null,
          null,
        );
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _postListQCApi() async {
    try {
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        debugPrint(_error);
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _rePostListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
      });
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _fetchApi() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
    } else {
      await _getDropDownListQCApi();
      if (_filterLSQC != null) {
        await _getCommonDate();
        if (_commonDateModel != null) {
          _postListQCApi();
        }
      }
    }
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
        : _isNotWifi == true
            ? Scaffold(
                backgroundColor: Colors.grey.shade200,
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Danh Sách Kiểm Tra BTP',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: LostConnect(checkConnect: () => _fetchApi()))
            : Scaffold(
                endDrawerEnableOpenDragGesture: false,
                backgroundColor: _defaultDataFilter != null && _defaultDataFilter!.isNotEmpty ? Colors.grey.shade200 : Colors.white,
                // key: _key,
                endDrawer: _isLoading == true
                    ? null
                    : FilterLsQC(
                        filterLSQC: _filterLSQC!,
                        lsStatus: ListQCFunction.lsStatus,
                        results: _results,
                        commonDates: _commonDates,
                        workCenters: _workCenters,
                        commonDateModel: _commonDateModel ?? CommonDateModel(),
                        salesOrgCodes: _salesOrgCodes,
                        workShops: _workShops,
                        token: widget.user.token.toString(),
                        onFilterSelected: (List<DrawerFilterQC> filter) {
                          setState(() {
                            _defaultDataFilter = filter[0].getDataFilter;
                            _notFoundFilter = filter[0].notFoundFilter ?? " ";
                            _postFilterQC = filter[0].postFilterQC;
                          });
                        },
                      ),
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  actions: [
                    IconButton(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: Icon(
                        Icons.camera_alt,
                        size: 19.sp,
                        color: Colors.white,
                      ),
                      onPressed: _isLoading == true
                          ? null
                          : () async {
                              final check = await Navigator.pushNamed(context, "/QRCodePageListQC2");
                              if (!mounted) return;
                              if (check == null) return;
                              if ((check as QRCodePageListQCModel).isScan == true) {
                                final checkSave = await Navigator.pushNamed(
                                  context,
                                  '/BaoCaoQAQCNghiemThuDetail',
                                  arguments: ScreenArgumentNavigatorBar(
                                    "",
                                    widget.dateTimeOld,
                                    check.qrCode.toString(),
                                    "qr",
                                    widget.user,
                                  ),
                                );
                                if (checkSave == null) return;
                                if (checkSave == true) {
                                  _rePostListQCApi();
                                }
                              }
                            },
                    ),
                    Builder(
                      builder: (BuildContext context) {
                        return IconButton(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          icon: Icon(
                            Icons.search_outlined,
                            size: 19.sp,
                            color: Colors.white,
                          ),
                          onPressed: _isLoading == true
                              ? null
                              : () async {
                                  await _checkConnectNetwork();
                                  if (!mounted) return;
                                  if (_result != ConnectivityResult.none) {
                                    Scaffold.of(context).openEndDrawer();
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                      backgroundColor: Colors.black,
                                      content: Text(
                                        'Tính năng cần có kết nối internet để sử dụng',
                                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                      ),
                                    ));
                                  }
                                },
                        );
                      },
                    ),
                  ],
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: _error != ""
                    ? ErrorViewPost(error: _error)
                    : _isLoading == true
                        ? _buildLoading()
                        : _buildDataFilteredWithHeader());
  }

  Widget _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildDataFilteredWithHeader() {
    List<Map<String, String>> dummyTheTreo = [
      // {'LSX': '150002278 | 0101.b.4 | 140', 'hangTagId': '39909676-fabe-414c-a270-1d7ea64c8f17'},
      // {'LSX': '150002278 | 0101 | 148', 'hangTagId': 'e8e0a2b0-70b6-44eb-a175-8db7541e8f3c'},
      // {'LSX': '520011035 CAT RO1', 'hangTagId': 'cea0bd31-33d9-4a1a-8535-30001460a16e'},
      // {'LSX': '3500008065', 'hangTagId': 'ee338608-9bfb-4e47-b14f-d8b005964302'},
      // {'LSX': 'test SOT', 'hangTagId': 'e1a8fb60-77de-479c-b8cc-2a11b4a82def'},
      // {'LSX': 'test sai Workshop', 'hangTagId': 'e4b0ddd6-8154-4c9e-8a0e-e5c7c4459267'},
      // {'LSX': 'Test 2', 'hangTagId': '3d6156b6-6c01-4a89-a7a7-f09169e16b53'},
      // {'LSX': 'Fail', 'hangTagId': 'a0230d8d-7280-47b4-848b-7aeff7fde4fe'},
      // {'LSX': 'Passed', 'hangTagId': '6f278cc3-ab2a-4aee-b72f-ab72d28fff52'},
      // {'LSX': 'Mid Test ', 'hangTagId': 'd41f9ee9-5ccd-4a0b-9d23-7fb20bd10614'},
      // {'LSX': 'Inline 7747 2 info', 'hangTagId': '26ea7ecd-e22e-436e-943d-49192639d9e1'},
      // {'LSX': 'Inline 9 info 1 error', 'hangTagId': 'd10ef197-bb0f-48e9-8f45-e6bd738563d8'},
      // {'LSX': 'Not found', 'hangTagId': 'd10ef197-bb0f-48e9-2-e6bd738563d8'},
      // {'LSX': 'Workshop', 'hangTagId': '8dd78d63-f4fc-4100-9570-e4545be95e9f'},

      // {'LSX': '0.', 'hangTagId': '66e32cff-0c7c-407f-aa44-458b920c43f5'},

      // {'LSX': 'test sync to SAP', 'hangTagId': '4a2c453c-9171-4554-8722-34813332a1ad'},

      // {'LSX': 'error', 'hangTagId': '8ACC32BC-3E23-420D-BD54-2A122C68865C'},

      // {'LSX': '175012937 1', 'hangTagId': 'e0792471-e0b6-435c-a98a-dd2762fce937'},

      {'LSX': '175012937 1', 'hangTagId': 'e0792471-e0b6-435c-a98a-dd2762fce937'},
      {'LSX': '175012952 2', 'hangTagId': 'dad8504f-164d-4fb4-a17e-58938d6cb672'},

      {'LSX': '175012941 3', 'hangTagId': '0318f049-db55-4069-8985-4a23a603c666'},
      {'LSX': '175012938 4', 'hangTagId': '1fc6fd44-552e-40fe-ad91-9bf2d5ec77d2'},

      // {'LSX': '175012952 5', 'hangTagId': 'f2adafd9-402a-417f-b0da-27616fc0bd67'},
      // {'LSX': '175012952 6', 'hangTagId': '92ffe8b3-7fa3-495f-922f-9122a84abefc'},
      // {'LSX': '175012952 7', 'hangTagId': 'cd8cc3a6-1b03-42dd-a415-f6431723b123'},
      // {'LSX': '175012952 8', 'hangTagId': '56c6513d-7d7e-4017-9a77-ba953e149073'},
      // {'LSX': '175012952 9', 'hangTagId': '86f1424f-068b-4a87-b3d4-85e35f8c384e'},
      // {'LSX': '175012952 10', 'hangTagId': '4e73a24b-8432-48f6-b9aa-a2c787a2442d'},
      // {'LSX': '175012952 11', 'hangTagId': '266e50c4-4b92-4cfc-ab78-bc98e308f063'},
      // {'LSX': '175012952 12', 'hangTagId': '50325d62-884d-4a64-9941-61367ba4311d'},
      // {'LSX': '175012952 13', 'hangTagId': 'b36cc77c-646f-498e-9337-419c5ff10297'},
      // {'LSX': '175012952 14', 'hangTagId': 'e7f4d2f5-dd47-460e-890c-4f09643396f2'},
      // {'LSX': '175012952 15', 'hangTagId': '226621a9-f358-4d1b-8cd6-ed1587025154'},
      // {'LSX': '175012952 16', 'hangTagId': '30bae6f9-2438-4a18-bc8b-debc380a9c31'},
      // {'LSX': '175012952 17', 'hangTagId': '2bedbeac-88be-4582-bd7c-a32a24af99dd'},
      // {'LSX': '175012952 18', 'hangTagId': 'd39a4bfc-9324-41f2-bd2f-9810523017b4'},
      // {'LSX': '175012952 19', 'hangTagId': '8af22636-699d-40fe-acb3-e6610cf3c262'},
      // {'LSX': '175012952 20', 'hangTagId': 'e98a5b0e-1840-4c0a-928b-4f5e7693f726'},
      // {'LSX': '175012952 20', 'hangTagId': 'e98a5b0e-1840-4c0a-928b-4f5e7693f726'},

      {'LSX': '175012952 20', 'hangTagId': '0eb15386-8db7-4086-9bf9-c44d33b8595e'},
    ];

    return Column(
      children: <Widget>[
        kDebugMode
            ? RenderDebugButtons(dummyTheTreo, (item) {
                Navigator.pushNamed(
                  context,
                  '/BaoCaoQAQCNghiemThuDetail',
                  arguments: ScreenArgumentNavigatorBar(
                    "",
                    widget.dateTimeOld,
                    item['hangTagId']!, // thẻ treo
                    "qr",
                    widget.user,
                  ),
                );
              }, 'LSX')
            : Container(),

        // Your ListView.builder:
        Expanded(child: _buildDataFiltered())
      ],
    );
  }

  _buildDataFiltered() {
    if (_defaultDataFilter != null && _defaultDataFilter!.isNotEmpty) {
      return ListView.builder(
          key: ObjectKey(_defaultDataFilter![0]),
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemCount: _defaultDataFilter!.length,
          itemBuilder: (context, index) {
            final item = _defaultDataFilter![index];
            if (item.confirmDate != null) {
              _stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(item.confirmDate.toString());
              _dateFormatStringConfirm = DateFormat('dd/MM/yyyy ').format(_stringToDateTimeConfirm!);
            }
            if (item.qualityDate != null) {
              _stringToDateTimeQualityDate = DateFormat("yyyy-MM-dd").parse(item.qualityDate.toString());
              _dateFormatStringQualityDate = DateFormat('dd-MM-yyyy').format(_stringToDateTimeQualityDate!);
            } else {
              _dateFormatStringQualityDate = item.qualityDate;
            }
            return GestureDetector(
                onTap: () async {
                  debugPrint(item.qualityControlId);
                  Navigator.pushNamed(
                    context,
                    '/BaoCaoQAQCNghiemThuDetail',
                    arguments: ScreenArgumentNavigatorBar(
                      item.qualityControlId.toString(),
                      widget.dateTimeOld,
                      "",
                      "normal",
                      widget.user,
                    ),
                  ).then((value) {
                    // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                    // final check = arguments.refresh;
                    // if(GlobalValue.refreshListQC == true){
                    if (value == true) {
                      _rePostListQCApi();
                    }
                  }).onError((error, stackTrace) {
                    debugPrint('Lỗi:$error');
                  });
                  // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                  // if (arguments.refresh == true) {
                  // print(GlobalValue.refresh);
                  // if (GlobalValue.refresh == true) {
                  //   _rePostListQCApi();
                  // } else {
                  //   return;
                  // }

                  // }else{
                  //   return;
                  // }
                  // if (data == null) return;
                  // if (data == true) {
                  //   getListQCApi();
                  // }
                },
                child: _ListQCView(
                    item: item, dateFormatStringConfirm: _dateFormatStringConfirm, dateFormatStringQualityDate: _dateFormatStringQualityDate));
          });
    }

    return _NotFoundFilterView(
      notFoundFilter: _notFoundFilter,
    );
  }
}

class _NotFoundFilterView extends StatelessWidget {
  final String notFoundFilter;
  const _NotFoundFilterView({Key? key, required this.notFoundFilter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            notFoundFilter,
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}

class _ListQCView extends StatelessWidget {
  final DataListQC item;
  final String? dateFormatStringConfirm;
  final String? dateFormatStringQualityDate;
  const _ListQCView({Key? key, required this.item, required this.dateFormatStringConfirm, required this.dateFormatStringQualityDate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Nhà máy: ${item.saleOrgCode.toString()}",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "LSX SAP: ${item.lsxsap.toString()}",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    decoration: BoxDecoration(
                      color: item.status == false ? Colors.deepOrange : const Color(0xff0052cc),
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "DSX: ${item.dsx.toString()}",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "LSX DT: ${item.lsxdt.toString()} ",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    decoration: BoxDecoration(
                      color: item.result != null
                          ? item.result == 'Fail'
                              ? Colors.red.shade800
                              : Colors.green
                          : Colors.yellow.shade800,
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.result != null ? item.result.toString() : "Chưa có kết quả",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          SizedBox(height: 10.h),
          Text(
            item.productName.toString(),
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            "Công đoạn lớn: ${item.workCenterCode.toString()}",
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
          const Divider(),
          // SizedBox(height: 10.h),
          Row(
            children: <Widget>[
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày Confirm",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      // dateFormatStringConfirm,
                      dateFormatStringConfirm.toString(),
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Text("|"),
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày kiểm tra",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      dateFormatStringQualityDate ?? " ",
                      style: TextStyle(
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Expanded(
              //   flex: 2,
              //   child: Icon(Icons.arrow_forward_ios_rounded, size: 18.sp, color: Colors.grey.shade600),
              // ),
            ],
          ),
        ],
      ),
    );
  }
}
