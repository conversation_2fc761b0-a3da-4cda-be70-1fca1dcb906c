﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TyLeTieuHaoModel", Schema = "MES")]
    public partial class TyLeTieuHaoModel
    {
        [Key]
        public Guid TyLeTieuHaoId { get; set; }
        [Required]
        [StringLength(10)]
        public string Date { get; set; }
        [Required]
        [StringLength(50)]
        public string LSXSAP { get; set; }
        [Required]
        [StringLength(50)]
        public string DotSanXuat { get; set; }
        [Required]
        [StringLength(50)]
        public string MaSan<PERSON>ham { get; set; }
        [Required]
        [StringLength(500)]
        public string TenSanPham { get; set; }
        [Required]
        [StringLength(50)]
        public string MaNVL { get; set; }
        [Required]
        [StringLength(500)]
        public string TenNVL { get; set; }
        [Required]
        [StringLength(20)]
        public string DVT { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal SLThoSuDung { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal SLSPLamDuoc { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal SLM2DinhMuc { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal TyLeTheoDM { get; set; }
        [StringLength(500)]
        public string PersonInChargeCodeMany { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedDate { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdateBy { get; set; }
        [StringLength(10)]
        public string CompanyCode { get; set; }
        [StringLength(20)]
        public string Status { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
    }
}