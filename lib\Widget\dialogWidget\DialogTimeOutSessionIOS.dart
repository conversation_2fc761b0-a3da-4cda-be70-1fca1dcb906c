import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '/Storage/storageSecureStorage.dart';
import '/Storage/storageSharedPreferences.dart';

class DiaLogTimeOutSessionIOS extends StatelessWidget {

  const DiaLogTimeOutSessionIOS({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text('Hết phiên đăng nhập', style: TextStyle(fontSize: 15.sp)),
      content: Text('Bạn đã hết phiên đăng nhập! Vui lòng đăng nhập trở lại.',
          style: TextStyle(fontSize: 15.sp)),
      actions: <CupertinoDialogAction>[
        CupertinoDialogAction(
          child: Text(
            'OK',
            style: TextStyle(fontSize: 15.sp),
          ),
          onPressed: () async {
            await Future.wait([
              StorageSharedPreferences.removeShared("id"),
              StorageSharedPreferences.removeShared("datetimeNow"),
              SecureStorage.removeSecure("user", null)
            ]);
            Navigator.of(context).pushNamedAndRemoveUntil(
                '/Login', (Route<dynamic> route) => false);
          },
        )
        // GestureDetector(
        //   onTap: () async {
        //     await Future.wait([
        //       StorageSharedPreferences.removeShared("id"),
        //       StorageSharedPreferences.removeShared("datetimeNow"),
        //       SecureStorage.removeSecure("user", null)
        //     ]);
        //     Navigator.of(context).pushNamedAndRemoveUntil(
        //         '/Login', (Route<dynamic> route) => false);
        //   },
        //   child: Text(
        //     'OK',
        //     style: TextStyle(fontSize: 15.sp),
        //   ),
        // ),
      ],
    );
  }
}
