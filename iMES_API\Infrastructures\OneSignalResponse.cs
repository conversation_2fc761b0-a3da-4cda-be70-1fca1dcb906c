﻿using System.Collections.Generic;

namespace iMES_API.Infrastructures
{
    public class OneSignalResponse
    {
        public int? total_count { get; set; }
        public int? offset { get; set; }
        public int? limit { get; set; }
        public List<Player> players { get; set; }
    }

    public class Player
    {
        public string id { get; set; }
        public string identifier { get; set; }
        public int? session_count { get; set; }
        public string language { get; set; }
        public int? timezone { get; set; }
        public string game_version { get; set; }
        public string device_os { get; set; }
        public int? device_type { get; set; }
        public string device_model { get; set; }
        public object ad_id { get; set; }
        public int? last_active { get; set; }
        public double amount_spent { get; set; }
        public int? created_at { get; set; }
        public bool invalid_identifier { get; set; }
        public int? badge_count { get; set; }
        public string sdk { get; set; }
        public object test_type { get; set; }
        public string ip { get; set; }
        public string external_user_id { get; set; }
    }


}
