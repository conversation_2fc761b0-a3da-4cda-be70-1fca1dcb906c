class RawMaterialCard {
  int? code;
  bool? isSuccess;
  String? message;
  DataRawMeterial? data;

  RawMaterialCard({this.code, this.isSuccess, this.message, this.data});

  RawMaterialCard.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataRawMeterial.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataRawMeterial {
  String? materialType;
  String? vendorCode;
  String? vendorName;
  String? plant;
  String? productCode;
  String? productName;
  double? poQuantity;
  String? poQuantityUnit;
  String? specifications;
  double? quantity;
  double? sumQuantityReceived;
  double? sumQuantityImported;
  String? unit;
  double? quantity2;
  String? quantity2Unit;
  double? quantity3;
  String? quantity3Unit;
  String? manufacturingDate;
  String? manufacturingDateStr;
  String? expirationDate;
  String? expirationDateStr;
  String? barcodePath;
  String? createTime;
  String? createBy;
  int? isReceive;
  List<PoDetailResponses>? poDetailResponses;
  InforReceived? inforReceived;
  bool? isGoodsArrive;
  String? goodsArriveDate;
  String? goodsArriveDateStr;
  List<String>? goodsReceivedNotes;
  String? latestTDS;

  DataRawMeterial({
    this.materialType,
    this.vendorCode,
    this.vendorName,
    this.plant,
    this.productCode,
    this.productName,
    this.poQuantity,
    this.poQuantityUnit,
    this.specifications,
    this.quantity,
    this.sumQuantityReceived,
    this.sumQuantityImported,
    this.unit,
    this.quantity2,
    this.quantity2Unit,
    this.quantity3,
    this.quantity3Unit,
    this.manufacturingDate,
    this.manufacturingDateStr,
    this.expirationDate,
    this.expirationDateStr,
    this.barcodePath,
    this.createTime,
    this.createBy,
    this.isReceive,
    this.poDetailResponses,
    this.inforReceived,
    this.isGoodsArrive,
    this.goodsArriveDate,
    this.goodsArriveDateStr,
    this.goodsReceivedNotes,
    this.latestTDS,
  });

  DataRawMeterial.fromJson(Map<String, dynamic> json) {
    materialType = json['materialType'];
    vendorCode = json['vendorCode'];
    vendorName = json['vendorName'];
    plant = json['plant'];
    productCode = json['productCode'];
    productName = json['productName'];
    poQuantity = json['poQuantity'];
    poQuantityUnit = json['poQuantityUnit'];
    specifications = json['specifications'];
    quantity = json['quantity'];
    sumQuantityReceived = json['sumQuantityReceived'];
    sumQuantityImported = json['sumQuantityImported'];
    unit = json['unit'];
    quantity2 = json['quantity2'];
    quantity2Unit = json['quantity2Unit'];
    quantity3 = json['quantity3'];
    quantity3Unit = json['quantity3Unit'];
    manufacturingDate = json['manufacturingDate'];
    manufacturingDateStr = json['manufacturingDateStr'];
    expirationDate = json['expirationDate'];
    expirationDateStr = json['expirationDateStr'];
    barcodePath = json['barcodePath'];
    createTime = json['createTime'];
    createBy = json['createBy'];
    isReceive = json['isReceive'];
    if (json['poDetailResponses'] != null) {
      poDetailResponses = <PoDetailResponses>[];
      json['poDetailResponses'].forEach((v) {
        poDetailResponses!.add(PoDetailResponses.fromJson(v));
      });
    }
    inforReceived = json['inforReceived'] != null ? InforReceived.fromJson(json['inforReceived']) : null;
    isGoodsArrive = json['isGoodsArrive'];
    goodsArriveDate = json['goodsArriveDate'];
    goodsArriveDateStr = json['goodsArriveDateStr'];
    goodsReceivedNotes = json['goodsReceivedNotes'].cast<String>();
    latestTDS = json['latestTDS'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['materialType'] = materialType;
    data['vendorCode'] = vendorCode;
    data['vendorName'] = vendorName;
    data['plant'] = plant;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['poQuantity'] = poQuantity;
    data['poQuantityUnit'] = poQuantityUnit;
    data['specifications'] = specifications;
    data['quantity'] = quantity;
    data['sumQuantityReceived'] = sumQuantityReceived;
    data['sumQuantityImported'] = sumQuantityImported;
    data['unit'] = unit;
    data['quantity2'] = quantity2;
    data['quantity2Unit'] = quantity2Unit;
    data['quantity3'] = quantity3;
    data['quantity3Unit'] = quantity3Unit;
    data['manufacturingDate'] = manufacturingDate;
    data['manufacturingDateStr'] = manufacturingDateStr;
    data['expirationDate'] = expirationDate;
    data['expirationDateStr'] = expirationDateStr;
    data['barcodePath'] = barcodePath;
    data['createTime'] = createTime;
    data['createBy'] = createBy;
    data['isReceive'] = isReceive;
    if (poDetailResponses != null) {
      data['poDetailResponses'] = poDetailResponses!.map((v) => v.toJson()).toList();
    }
    if (inforReceived != null) {
      data['inforReceived'] = inforReceived!.toJson();
    }
    data['isGoodsArrive'] = isGoodsArrive;
    data['goodsArriveDate'] = goodsArriveDate;
    data['goodsArriveDateStr'] = goodsArriveDateStr;
    data['goodsReceivedNotes'] = goodsReceivedNotes;
    data['latestTDS'] = latestTDS;
    return data;
  }
}

class PoDetailResponses {
  double? quantity;
  String? po;
  String? poLine;
  String? so;
  String? soLine;
  String? wbs;
  String? lsxdt;
  double? quantityImported;
  double? quantityByPO;
  double? quantityReceived;
  double? remainQuantity;
  String? unit;

  PoDetailResponses(
      {this.quantity,
      this.po,
      this.poLine,
      this.so,
      this.soLine,
      this.wbs,
      this.lsxdt,
      this.quantityImported,
      this.quantityByPO,
      this.quantityReceived,
      this.remainQuantity,
      this.unit});

  PoDetailResponses.fromJson(Map<String, dynamic> json) {
    quantity = json['quantity'];
    po = json['po'];
    poLine = json['poLine'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    lsxdt = json['lsxdt'];
    quantityImported = json['quantityImported'];
    quantityByPO = json['quantityByPO'];
    quantityReceived = json['quantityReceived'];
    remainQuantity = json['remainQuantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantity'] = quantity;
    data['po'] = po;
    data['poLine'] = poLine;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['lsxdt'] = lsxdt;
    data['quantityImported'] = quantityImported;
    data['quantityByPO'] = quantityByPO;
    data['quantityReceived'] = quantityReceived;
    data['remainQuantity'] = remainQuantity;
    data['unit'] = unit;
    return data;
  }
}

class InforReceived {
  String? batchNumber;
  String? plant;
  String? sloc;
  String? warehouseNo;
  String? storageBin;

  InforReceived({this.batchNumber, this.plant, this.sloc, this.warehouseNo, this.storageBin});

  InforReceived.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
    plant = json['plant'];
    sloc = json['sloc'];
    warehouseNo = json['warehouseNo'];
    storageBin = json['storageBin'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['warehouseNo'] = warehouseNo;
    data['storageBin'] = storageBin;
    return data;
  }
}
