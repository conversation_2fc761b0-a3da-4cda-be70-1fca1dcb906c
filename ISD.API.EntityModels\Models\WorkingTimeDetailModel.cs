﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WorkingTimeDetailModel", Schema = "ghService")]
    public partial class WorkingTimeDetailModel
    {
        [Key]
        public Guid WorkingTimeDetailId { get; set; }
        public Guid? WorkingTimeId { get; set; }
        public Guid? StoreId { get; set; }
        public TimeSpan? TimeFrameFrom { get; set; }
        public TimeSpan? TimeFrameTo { get; set; }
        public int? Amount { get; set; }

        [ForeignKey("WorkingTimeId")]
        [InverseProperty("WorkingTimeDetailModel")]
        public virtual WorkingTimeModel WorkingTime { get; set; }
    }
}