import 'dart:io';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:flutter/material.dart';

class LsAddInfoTest {
  ThongTinKiemTra? selectedInfoTest;
  final TextEditingController textEditingController;
  final List<File> lsImageFile;
  QualityControlInformation? selectedQualityControlQCInformation;
  LsAddInfoTest(this.selectedInfoTest, this.textEditingController, this.lsImageFile, this.selectedQualityControlQCInformation);
}
