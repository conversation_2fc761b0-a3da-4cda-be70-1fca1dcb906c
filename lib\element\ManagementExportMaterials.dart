import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/getListSOWBS.dart';

class ManagementExportMaterials extends StatelessWidget {
  final List<ListSOWBS> lsSOWBS;
  final ListSOWBS? selectedSOWBS;
  final TextEditingController controllerAmountExport;
  final VoidCallback onTap;
  final ValueChanged<ListSOWBS?> setSelectedSOWBS;
  final String errorTextAmountExport;
  final IconData? errorIconAmountExport;
  final bool errorAmountExport;
  final bool hideAmountExport;
  final String scanMat;
  final String scanDes;
  final String scanBatch;
  final String errorTextDropdownExport;
  final IconData? errorIconDropdownExport;
  final bool errorDropdownExport;
  final bool hideDropdownExport;
  final String amountExport;
  const ManagementExportMaterials({
    Key? key,
    required this.lsSOWBS,
    required this.selectedSOWBS,
    required this.controllerAmountExport,
    required this.onTap,
    required this.setSelectedSOWBS,
    required this.errorTextAmountExport,
    required this.errorIconAmountExport,
    required this.errorAmountExport,
    required this.hideAmountExport,
    required this.scanMat,
    required this.scanDes,
    required this.scanBatch,
    required this.errorTextDropdownExport,
    required this.errorIconDropdownExport,
    required this.errorDropdownExport,
    required this.hideDropdownExport,
    required this.amountExport
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Mat:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade400,
                ),
              ),
              child: Text(
                scanMat,
                style: TextStyle(color: Colors.black, fontSize: 11.sp),
              )),
          // SizedBox(height: error == true ? 15.h : 0),
          // ContaineError.widgetError(
          //     errorContainerWidth,
          //     hide,
          //     errorIcon,
          //     15.sp,
          //     Colors.red[700],
          //     5.w,
          //     0.w,
          //     0.h,
          //     0.h,
          //     errorText,
          //     11.sp,
          //     Colors.red[700]),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Des:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade400,
                ),
              ),
              child: Text(
                scanDes,
                style: TextStyle(color: Colors.black, fontSize: 11.sp),
              )),
          // SizedBox(height: error_2 == true ? 15.h : 0),
          // ContaineError.widgetError(
          //     errorContainerWidth_2,
          //     hide_2,
          //     errorIcon_2,
          //     15.sp,
          //     Colors.red[700],
          //     5.w,
          //     0.w,
          //     0.h,
          //     0.h,
          //     errorText_2,
          //     11.sp,
          //     Colors.red[700]),
          SizedBox(
            height: 15.h,
          ),

          Text(
            "Batch:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
            decoration: BoxDecoration(
              border: Border.all(
                width: 0.5,
                color: Colors.grey.shade400,
              ),
            ),
            child: Text(
              scanBatch,
              style: TextStyle(color: Colors.black, fontSize: 11.sp),
            ),
          ),
          SizedBox(
            height: 15.h,
          ),
          Center(
            child: OutlinedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r),
                        side: const BorderSide(color: Colors.white))),
                side: MaterialStateProperty.all(
                  const BorderSide(
                    color: Color(0xff0052cc),
                  ),
                ),
                backgroundColor: MaterialStateProperty.all(Colors.white),
              ),
              onPressed: () async {},
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 12.h),
                child: Icon(
                  Icons.camera_alt_outlined,
                  size: 19.sp,
                  color: const Color(0xff0052cc),
                ),
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            "SO/WBS:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                  color: errorDropdownExport == true
                  ?const Color(0xFFD32F2F)
                  :Colors.grey.shade400,
                  width: 0.5.w
              ),
              borderRadius: BorderRadius.circular(1.r),
            ),
            child: DropdownButtonHideUnderline(
                child: DropdownButton<ListSOWBS?>(
              isExpanded: true,
              value: selectedSOWBS ?? lsSOWBS[0],
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: setSelectedSOWBS,
              items: lsSOWBS.map((ListSOWBS? sowbs) {
                return DropdownMenuItem<ListSOWBS>(
                    value: sowbs,
                    child: Center(
                      child: Text(
                        sowbs!.name.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
            )),
          ),
          hideDropdownExport == true
              ? Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10.h),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Icon(
                      errorIconDropdownExport,
                      size: 15.sp,
                      color: const Color(0xFFD32F2F),
                    ),
                  ),
                  Expanded(
                    flex: 9,
                    child: Text(
                      errorTextDropdownExport,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: const Color(0xFFD32F2F),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
              : Container(),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Tồn SAP:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                flex: 9,
                child: Container(
                    // width: double.infinity,
                    padding:
                        EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        width: 0.5,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    child: Text(
                      amountExport,
                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                    )),
              ),
              Expanded(
                flex: 1,
                child: Center(
                  child: Text(
                    "M3",
                    style:
                        TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Số lượng sử dụng:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                flex: 9,
                child: SizedBox(
                  height: 40.h,
                  child: TextFormField(
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                    controller: controllerAmountExport,
                    style: TextStyle(fontSize: 12.sp),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(0),
                        borderSide: BorderSide(
                            width: 0.5,
                            color: errorAmountExport == true
                                ? const Color(0xFFD32F2F)
                                : Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(0),
                        borderSide: BorderSide(
                            width: 0.5,
                            color: errorAmountExport == true
                                ? const Color(0xFFD32F2F)
                                : Colors.grey.shade400),
                      ),
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      filled: true,
                      fillColor: Colors.white,
                      hintStyle: TextStyle(fontSize: 12.sp),
                      contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Center(
                  child: Text(
                    "M3",
                    style:
                        TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
          hideAmountExport == true
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 10.h),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Icon(
                            errorIconAmountExport,
                            size: 15.sp,
                            color: const Color(0xFFD32F2F),
                          ),
                        ),
                        Expanded(
                          flex: 9,
                          child: Text(
                            errorTextAmountExport,
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: const Color(0xFFD32F2F),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : Container(),
          SizedBox(
            height: 10.h,
          ),
          Container(
            width: double.infinity,
            child: ElevatedButton(
              style: ButtonStyle(
                padding: MaterialStateProperty.all<EdgeInsets>(
                    EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r),
                        side: const BorderSide(color: Colors.white))),
                side: MaterialStateProperty.all(
                  const BorderSide(
                    color: Color(0xff0052cc),
                  ),
                ),
                backgroundColor:
                    MaterialStateProperty.all(const Color(0xff0052cc)),
              ),
              onPressed: onTap,
              child: Text(
                "Lưu",
                style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          )
        ]);
  }
}
