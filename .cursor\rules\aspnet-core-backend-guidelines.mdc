---
description: 
globs: 
alwaysApply: false
---
# ASP.NET Core Backend Development Guidelines

This guide covers best practices and common patterns for the iMES_API backend to ensure consistency and avoid common errors.

## Database Access Patterns

### Entity Context
- Always use `EntityDataContext` for database access, not `AppDbContext`
- Inject via constructor: `private readonly EntityDataContext _context;`

### SAP Field Naming
SAP tables use specific field naming conventions different from our models:

- Equipment codes: 
  - `EQUNR` (not `EquipmentCode`) - 18 chars with leading zeros
  - `EQKTX` (not `EquipmentName`) - equipment description
  - `IWERK` (not `WERKS`) - company/plant code

### Data Format Handling
Equipment codes in SAP require special handling:

```csharp
// When querying SAP tables
var paddedCode = code.PadLeft(18, '0');
query = query.Where(e => e.EQUNR == paddedCode);

// When displaying to client
EquipmentCode = e.EQUNR.TrimStart('0')
```

### Employee Data Access
- Use `_context.SalesEmployeeModel` (not `_context.Employees`)
- Field mapping:
  - `SalesEmployeeCode` (not `EmployeeCode`)
  - `SalesEmployeeName` (not `EmployeeName`)

```csharp
var employees = await _context.SalesEmployeeModel
    .Where(e => employeeCodes.Contains(e.SalesEmployeeCode))
    .Select(e => new { 
        EmployeeCode = e.SalesEmployeeCode, 
        EmployeeName = e.SalesEmployeeName 
    })
    .ToListAsync();
```

## Project Structure

### Controller Placement
- Place controllers in `iMES_API/iMES_API/Areas/MES/Controllers/`
- Add area attribute: `[Area("MES")]`
- Use consistent route pattern: `[Route("api/v1/MES/[controller]")]`

### Code Reuse
Prefer reusing existing functionality:

```csharp
// Good: Reuse CommonController for employee data
[HttpGet("GetEmployees")]
public async Task<IActionResult> GetEmployees([FromQuery] string companyCode)
{
    var commonController = new CommonController();
    commonController.ControllerContext = this.ControllerContext;
    return await commonController.GetEmployees(companyCode);
}
```

### Service Registration
Register services in `Startup.cs`:

```csharp
services.AddScoped<ISD.API.Repositories.Interfaces.IMaiDaoRepository, 
                  ISD.API.Repositories.Implementations.MaiDaoRepository>();
```

## Error Prevention Checklist

Before implementing:
1. Examine entity classes for correct field names
2. Look for similar patterns in `CommonController`
3. Verify data context and table references
4. Check for special formatting requirements (padding, etc.)
5. Follow project structure and routing conventions

