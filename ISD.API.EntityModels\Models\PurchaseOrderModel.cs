﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PurchaseOrderModel", Schema = "MES")]
    public partial class PurchaseOrderModel
    {
        [Key]
        public Guid PurchaseOrderId { get; set; }
        [StringLength(50)]
        public string VBELN { get; set; }
        [StringLength(50)]
        public string EBELN { get; set; }
        [StringLength(10)]
        public string EBELP { get; set; }
        [StringLength(10)]
        public string BUKRS { get; set; }
        [StringLength(10)]
        public string BSART { get; set; }
        [StringLength(10)]
        public string LOEKZ { get; set; }
        [StringLength(50)]
        public string LIFNR { get; set; }
        [StringLength(10)]
        public string ZTERM { get; set; }
        [StringLength(10)]
        public string EKORG { get; set; }
        [StringLength(10)]
        public string EKGRP { get; set; }
        [StringLength(10)]
        public string WAERS { get; set; }
        [StringLength(50)]
        public string WKURS { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BEDAT { get; set; }
        [StringLength(10)]
        public string RESWK { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(10)]
        public string LGORT { get; set; }
        [StringLength(50)]
        public string MATKL { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? MENGE { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? UMREZ { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? UMREN { get; set; }
        [StringLength(10)]
        public string ELIKZ { get; set; }
        [StringLength(10)]
        public string PSTYP { get; set; }
        [StringLength(10)]
        public string KNTTP { get; set; }
        [StringLength(10)]
        public string RETPO { get; set; }
        [StringLength(50)]
        public string BANFN { get; set; }
        [StringLength(10)]
        public string BNFPO { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GR_QTY { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? isDeleted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DeletedTime { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? MENGE_UOM { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GR_QTY_UOM { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EINDT { get; set; }
        [StringLength(10)]
        public string LMEIN { get; set; }
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(10)]
        public string MTART { get; set; }
    }
}