﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StorageBinModel", Schema = "tMasterData")]
    public partial class StorageBinModel
    {
        public StorageBinModel()
        {
            WarehouseExportModel = new HashSet<WarehouseExportModel>();
            WarehouseTranferModelStorageBinExport = new HashSet<WarehouseTranferModel>();
            WarehouseTranferModelStorageBinImport = new HashSet<WarehouseTranferModel>();
        }

        [Key]
        public Guid StorageBinId { get; set; }
        [StringLength(3)]
        public string WarehouseNo { get; set; }
        [StringLength(3)]
        public string StorageType { get; set; }
        [StringLength(10)]
        public string StorageBin { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("StorageBin")]
        public virtual ICollection<WarehouseExportModel> WarehouseExportModel { get; set; }
        [InverseProperty("StorageBinExport")]
        public virtual ICollection<WarehouseTranferModel> WarehouseTranferModelStorageBinExport { get; set; }
        [InverseProperty("StorageBinImport")]
        public virtual ICollection<WarehouseTranferModel> WarehouseTranferModelStorageBinImport { get; set; }
    }
}