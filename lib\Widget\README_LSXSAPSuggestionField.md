# LSXSAPSuggestionField Widget

A reusable widget for LSX SAP input with auto-suggestion functionality that can be used across multiple screens in the TTF MES Mobile application.

## Features

- **Auto-suggestion**: Provides real-time suggestions as the user types (minimum 3 characters)
- **Clear functionality**: Optional clear button (X) to reset the field
- **Loading states**: Shows loading indicators during API calls
- **Customizable**: Supports custom titles, hints, and validation
- **Consistent styling**: Follows the app's design patterns and styling
- **Error handling**: Graceful error handling for network issues
- **Form integration**: Easy integration with existing form layouts

## Usage

### Basic Usage

```dart
import 'package:ttf/Widget/LSXSAPSuggestionField.dart';

LSXSAPSuggestionField(
  controller: _controllerLSXSAP,
  token: widget.user.token!.toString(),
  onSuggestionSelected: (lsxSap) async {
    await _loadQualityControlByLSXSAP(lsxSap);
  },
  onClear: () {
    setState(() {
      _controllerLSXSAP.text = "";
      _isLSXSAPSelected = false;
      _qualityControl = QualityControl();
      // Reset other related fields
    });
    clearForNew();
  },
)
```

### Replacing Existing TypeAheadField

Replace your existing LSX SAP TypeAheadField implementation:

**Before:**
```dart
Row(
  mainAxisAlignment: MainAxisAlignment.center,
  crossAxisAlignment: CrossAxisAlignment.center,
  children: <Widget>[
    const Expanded(
      flex: 3,
      child: QualityTitleField(title: "LSX SAP:"),
    ),
    SizedBox(width: 10.w),
    Expanded(
      flex: 7,
      child: Column(
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              border: Border.all(width: 0.5, color: Colors.grey.shade400),
              borderRadius: BorderRadius.circular(3.r),
            ),
            child: Stack(
              children: [
                TypeAheadField(
                  // ... lots of boilerplate code
                ),
                // Clear button implementation
              ],
            ),
          ),
        ],
      ),
    )
  ],
),
```

**After:**
```dart
LSXSAPSuggestionField(
  controller: _controllerLSXSAP,
  token: widget.user.token!.toString(),
  enabled: checkQualityControl() && _isTTFCode != true,
  onSuggestionSelected: (lsxSap) async {
    await _loadQualityControlByLSQSAP(lsxSap);
  },
  onClear: () {
    setState(() {
      _controllerLSXSAP.text = "";
      _isLSXSAPSelected = false;
      _qualityControl = QualityControl();
      _lsCongDoanNhoMasterData = [QualityControlDetailFunction.defaultValueCongDoanNho];
      _selectedCongDoanNho = QualityControlDetailFunction.defaultValueCongDoanNho;
    });
    clearForNew();
  },
)
```

## API Reference

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `controller` | `TextEditingController` | Controller for the text input field |
| `token` | `String` | User authentication token for API calls |
| `onSuggestionSelected` | `Function(String)` | Callback when a suggestion is selected |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enabled` | `bool` | `true` | Whether the field is enabled for input |
| `onClear` | `VoidCallback?` | `null` | Callback when clear button is tapped |
| `showClearButton` | `bool` | `true` | Whether to show the clear (X) button |
| `hintText` | `String?` | `"Vui lòng nhập LSX SAP (ít nhất 3 kí tự)"` | Placeholder text |
| `title` | `String?` | `"LSX SAP:"` | Title label (set to `null` to hide) |
| `isRequired` | `bool` | `false` | Whether to show required asterisk (*) |
| `onChanged` | `ValueChanged<String>?` | `null` | Callback for text changes |

## Examples

### 1. With Custom Title and Required Field
```dart
LSXSAPSuggestionField(
  controller: _controller,
  token: token,
  title: "Production Order",
  isRequired: true,
  onSuggestionSelected: _handleSelection,
)
```

### 2. Without Title
```dart
LSXSAPSuggestionField(
  controller: _controller,
  token: token,
  title: null,
  hintText: "Enter LSX SAP code...",
  onSuggestionSelected: _handleSelection,
)
```

### 3. Disabled Field
```dart
LSXSAPSuggestionField(
  controller: _controller,
  token: token,
  enabled: false,
  showClearButton: false,
  onSuggestionSelected: _handleSelection,
)
```

### 4. With Text Change Handling
```dart
LSXSAPSuggestionField(
  controller: _controller,
  token: token,
  onSuggestionSelected: _handleSelection,
  onChanged: (value) {
    // Handle text changes
    debugPrint('LSX SAP text changed: $value');
  },
)
```

## Integration Notes

1. **Import the widget**: Add the import statement at the top of your file
2. **Replace existing code**: Remove the TypeAheadField implementation and replace with this widget
3. **Update callbacks**: Ensure your callback functions match the expected signatures
4. **Test thoroughly**: Verify that all functionality works as expected

## Dependencies

This widget depends on:
- `flutter_typeahead` package
- `flutter_screenutil` for responsive sizing
- `QualityControlDetailFunction.fetchLSXSAP()` for API calls
- `QualityTitleField` for consistent title styling

## Error Handling

The widget includes built-in error handling:
- Network errors are caught and logged
- Loading states are managed automatically
- Empty responses are handled gracefully
- User-friendly error messages are displayed

## Benefits

- **Reduced code duplication**: Use the same widget across multiple screens
- **Consistent UX**: Uniform behavior and styling
- **Easier maintenance**: Updates in one place affect all usages
- **Better error handling**: Centralized error management
- **Cleaner code**: Less boilerplate code in your screens 