﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("CatalogModel", Schema = "tMasterData")]
    public partial class CatalogModel
    {
        public CatalogModel()
        {
            CampaignModel = new HashSet<CampaignModel>();
            QuestionBankModelDepartment = new HashSet<QuestionBankModel>();
            QuestionBankModelQuestionCategory = new HashSet<QuestionBankModel>();
        }

        [Key]
        public Guid CatalogId { get; set; }
        [StringLength(100)]
        public string CatalogTypeCode { get; set; }
        [StringLength(100)]
        public string CatalogCode { get; set; }
        [StringLength(1000)]
        public string CatalogText_en { get; set; }
        [StringLength(1000)]
        public string CatalogText_vi { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        [StringLength(50)]
        public string ErrorGroup { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string SectorCode { get; set; }
        [StringLength(200)]
        public string SectorName { get; set; }
        [StringLength(50)]
        public string ErrorLevel { get; set; }
        [StringLength(100)]
        public string CatalogCodeOld { get; set; }
        [StringLength(100)]
        public string StepCode { get; set; }
        [StringLength(50)]
        public string QCReports { get; set; }

        [InverseProperty("StatusNavigation")]
        public virtual ICollection<CampaignModel> CampaignModel { get; set; }
        [InverseProperty("Department")]
        public virtual ICollection<QuestionBankModel> QuestionBankModelDepartment { get; set; }
        [InverseProperty("QuestionCategory")]
        public virtual ICollection<QuestionBankModel> QuestionBankModelQuestionCategory { get; set; }
    }
}