import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class TableDetailReport extends StatelessWidget {
  final String stt;
  final String cluster;
  final String date;
  final String completedStage;
  final String dKD;
  final String timeStart;
  final String timeEnd;
  final String creator;
  final String creatorTime;
  final VoidCallback onTap;
  final VoidCallback onTap_2;
  final int? selectedIndex;
  final double? quantityD;
  final double? quantityKD;
  final int getIndex;
  final int? selectedIndex_2;

  const TableDetailReport({Key? key,
    required this.stt,
    required this.cluster,
    required this.date,
    required this.completedStage,
    required this.dKD,
    required this.timeStart,
    required this.timeEnd,
    required this.creator,
    required this.creatorTime,
    required this.onTap,
    required this.selectedIndex,
    required this.quantityD,
    required this.quantityKD,
    required this.getIndex,
    required this.onTap_2,
    required this.selectedIndex_2})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Table(
      border: TableBorder.all(color: Colors.grey.shade400, width: 0.5),
      columnWidths: const <int, TableColumnWidth>{
        0: FlexColumnWidth(2),
        1: FlexColumnWidth(4),
        2: FlexColumnWidth(4),
      },
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: <TableRow>[
        TableRow(
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Center(
                child: Text(
                  stt,
                  style: TextStyle(fontSize: 10.sp),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Text(
                cluster,
                style: TextStyle(fontSize: 10.sp),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Text(
                date,
                style: TextStyle(fontSize: 10.sp),
              ),
            ),
          ],
        ),
        TableRow(
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Text(
                completedStage,
                style: TextStyle(fontSize: 10.sp),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                    child: Text(
                      dKD,
                      style: TextStyle(fontSize: 10.sp),
                    ),
                  ),
                  selectedIndex != getIndex
                      ? GestureDetector(
                    onTap: onTap,
                    child: Icon(Icons.visibility,
                        size: 13.sp,
                        color: quantityKD != null ||
                            (quantityKD ?? 0.0).round() != 0
                            ? const Color(0xfff9a825)
                            : const Color(0xff0052cc)),
                  )
                      : Text('Loading...',
                      style: TextStyle(fontSize: 7.sp, color: Colors.blue))
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                    child: Text(
                      timeStart + " " + " -> " + timeEnd,
                      style: TextStyle(fontSize: 10.sp),
                    ),
                  ),
                  selectedIndex_2 != getIndex
                      ? GestureDetector(
                    onTap: onTap_2,
                    child: Icon(
                      Icons.info_rounded,
                      size: 13.sp,
                      color: Colors.black,
                    ),
                  )
                      : Text('Loading...',
                      style: TextStyle(fontSize: 7.sp, color: Colors.blue)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

