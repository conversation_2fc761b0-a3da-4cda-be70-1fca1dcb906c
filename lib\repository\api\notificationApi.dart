import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import 'package:ttf/model/EmployeeVm.dart';
import 'package:ttf/model/qualityControlApi.dart';
import '../../model/RequestTraHangNCC.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/sendErrorQualityControl.dart';
import '../../model/sendQualityControlDetail.dart';
import '../../urlApi/urlApi.dart';

class NotificationApi {
  static Future<http.Response> getListData(String token, String accountId) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlNotification}GetAllById?accountId=${accountId}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getListDataNew(String token, String accountId, int pageIndex, int pageSize) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // final urlString = '${baseUrl}${UrlApi.baseUrlNotification}GetAllById?accountId=${accountId}';
    final urlString = '${baseUrl}${UrlApi.baseUrlNotification}GetAllById?accountId=${accountId}&pageIndex=${pageIndex}&pageSize=${pageSize}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getListEmployee(String token, String plant) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlNotification}GetAllEmployeeByPlant?plant=${plant}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postForward(String token, String forwardById, String notificationId, List<EmployeeVm> forwardList) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlNotification}ForwardNotification';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());

    Map data = {};
    data = {
      "forwardById": forwardById,
      "notificationId": notificationId,
      "forwardList": forwardList,
      // "forwardList": forwardList.map((employee) => employee.toJson()).toList(),
    };
    var body = json.encode(data);

    final response = await http.post(url, headers: UrlApi.headersToken(token), body: body);
    debugPrint(jsonEncode(response.body));
    return response;
  }

  // static Future<http.Response> getData(String token, String id) async {
  //   final data = {"RequestReturnVendorId": id};
  //   if (kDebugMode) {
  //     print(data);
  //   }

  //   final environment = await SecureStorage.getString("environment", null);
  //   final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

  //   final urlString = baseUrl + UrlApi.baseUrlRequestReturnVendor + "GetReturnRequest" + "?RequestReturnVendorId=${data['RequestReturnVendorId']}";
  //   final url = Uri.parse(urlString);

  //   debugPrint(url.toString());
  //   final response = await http.get(url, headers: UrlApi.headersToken(token));
  //   return response;
  // }
}
