import 'dart:convert';
import '../../model/FilterLsQC.dart';
import '../../model/commonDateModel.dart';
import '../../model/getListQCByFilter.dart';
import '../../model/getLstQCNVLByFilter.dart';
import '../../model/postFilterQC.dart';
import '../api/commonDateApi.dart';
import '../api/filterDropdownQC.dart';
import '../api/getListQCTicketByFilterAPI.dart';
import '../api/getLstQCNVLByFilter.dart';

class ListQCFunction {
  static List<StatusData> lsStatus = [
    StatusData(nameStatus: '-- Tất Cả --', status: null),
    StatusData(nameStatus: 'Chưa kiểm tra', status: false),
    StatusData(nameStatus: 'Đã kiểm tra', status: true),
  ];
  static SalesOrgCodes defaultSalesOrgCodes = SalesOrgCodes(saleOrgCode: null, storeName: '-- Tất cả --');
  static WorkCenters defaultWorkCenters = WorkCenters(workCenterCode: null, workCenterName: '-- Tất cả --');
  static ResultsDataQC defaultResultsDataQC = ResultsDataQC(catalogCode: null, catalogTextVi: '-- Tất cả --');
  static WorkShops defaultWorkShops = WorkShops(workShopCode: null, workShopName: '-- Tất cả --');

  // static Future<DateTime?> pickDateAndroid(BuildContext context, DateTime initialDate, DateTime? date) async {
  //   DateTime? newDate = await showDatePicker(
  //     context: context,
  //     initialDate: date ?? initialDate,
  //     firstDate: DateTime(DateTime.now().year - 50),
  //     lastDate: DateTime(DateTime.now().year + 50),
  //   );
  //   return newDate;
  // }

  static Future<GetListQCTicketByFilter?> postFilterList(String token, FilterQCVm? postFilterQC) async {
    final dataResponse = await GetListQCTicketByFilterApi.getListQCTicketByFilter(token, postFilterQC!);
    if (dataResponse.statusCode == 200) {
      final responseDataCommonDate = jsonDecode(dataResponse.body);
      final getListQCTicketByFilter = GetListQCTicketByFilter.fromJson(responseDataCommonDate);
      if (getListQCTicketByFilter.code == 200 && getListQCTicketByFilter.isSuccess == true) {
        return getListQCTicketByFilter;
      } else {
        return null;
      }
    } else if (dataResponse.statusCode == 404) {
      return null;
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  static Future<GetListQCTicketByFilter?> fetchQCKCSFiltered(String token, FilterQCVm? postFilterQC) async {
    final dataResponse = await GetListQCTicketByFilterApi.getQCKCSFiltered(token, postFilterQC!);
    if (dataResponse.statusCode == 200) {
      final responseDataCommonDate = jsonDecode(dataResponse.body);
      final getListQCTicketByFilter = GetListQCTicketByFilter.fromJson(responseDataCommonDate);
      if (getListQCTicketByFilter.code == 200 && getListQCTicketByFilter.isSuccess == true) {
        return getListQCTicketByFilter;
      } else {
        return null;
      }
    } else if (dataResponse.statusCode == 404) {
      return null;
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  // static Future<List<DataLstQCNVLByFilter>?> postLstQCNVLByFilter(GetLstQCNVLByFilter getLstQCNVLByFilter, String token) async{
  //   final dataResponse = await GetLstQCNVLByFilterApi.postLstQCNVLByFilter(getLstQCNVLByFilter, token);
  //   if (dataResponse.statusCode == 200) {
  //     final responseQCNVL = jsonDecode(dataResponse.body);
  //     final getLstQCNVLByFilter = LstQCNVLByFilter.fromJson(responseQCNVL);
  //     if(getLstQCNVLByFilter.code == 200 && getLstQCNVLByFilter.isSuccess == true){
  //       return getLstQCNVLByFilter.data;
  //     }else{
  //       return null;
  //     }
  //   }else {
  //     throw Exception(dataResponse.reasonPhrase);
  //   }
  // }

  static Future<List<DataLstQCNVLByFilter>?> postLstQCNVLByFilter(GetLstQCNVLByFilter filter, String token) async {
    // Request data from API
    final response = await GetLstQCNVLByFilterApi.postLstQCNVLByFilter(filter, token);

    // If response status is not 200, throw an exception
    if (response.statusCode != 200) {
      throw Exception(response.reasonPhrase);
    }

    // Parse response
    final responseBody = jsonDecode(response.body);
    final filterResults = LstQCNVLByFilter.fromJson(responseBody);

    // If code is not 200 or success is not true, return null
    if (filterResults.code != 200 && filterResults.isSuccess == false) {
      return null;
    }

    // Return data
    return filterResults.data;
  }

  static Future<List<DataLstQCNVLByFilter>?> postLstQCNVLByFilter2(GetLstQCNVLByFilter filter, String token) async {
    final response = await GetLstQCNVLByFilterApi.postLstQCNVLByFilter2(filter, token);

    if (response.statusCode != 200) {
      throw Exception(response.reasonPhrase);
    }

    final responseBody = jsonDecode(response.body);
    final filterResults = LstQCNVLByFilter.fromJson(responseBody);

    if (filterResults.code != 200 && filterResults.isSuccess == false) {
      return null;
    }
    return filterResults.data;
  }

  // static List<DataLstQCNVLByFilter> lsSortDescQCNVL(List<DataLstQCNVLByFilter> lsDataLstQCNVLByFilter){
  //   List<DataLstQCNVLByFilter> getLsSortDescQCNVL = [];
  //   getLsSortDescQCNVL = lsDataLstQCNVLByFilter;
  //   getLsSortDescQCNVL.sort((a, b) {
  //     final stringToTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(a.barcodeCreateDate.toString());
  //     final stringToTime_2 = DateFormat("yyyy-MM-ddThh:mm:ss").parse(b.barcodeCreateDate.toString());
  //     return stringToTime.compareTo(stringToTime_2);
  //   });
  //   return getLsSortDescQCNVL;
  // }

  static Future<CommonDateModel> getCommonDateModel(String catalogCode, String token) async {
    final dataResponse = await CommonDateApi.getCommonDate(catalogCode, token);
    if (dataResponse.statusCode == 200) {
      final responseDataCommonDate = jsonDecode(dataResponse.body);
      final commonDateModel = CommonDateModel.fromJson(responseDataCommonDate);
      return commonDateModel;
    } else {
      throw (dataResponse.reasonPhrase.toString());
    }
  }

  static Future<FilterQCModel?> getDropDownListQCApi(String token) async {
    final dataResponse = await FilterDropdownQC.getFilterDropdownQC(token);
    if (dataResponse.statusCode == 200) {
      final responseDataFilterQC = jsonDecode(dataResponse.body);
      final filterLSQC = FilterQCModel.fromJson(responseDataFilterQC);
      if (filterLSQC.code == 200 && filterLSQC.isSuccess == true) {
        return filterLSQC;
      } else {
        return null;
      }
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  static Future<FilterQCModel?> getDefaultKCSFilter(String token) async {
    final dataResponse = await FilterDropdownQC.getDefaultKCSFilter(token);
    if (dataResponse.statusCode == 200) {
      final responseDataFilterQC = jsonDecode(dataResponse.body);
      final filterLSQC = FilterQCModel.fromJson(responseDataFilterQC);
      if (filterLSQC.code == 200 && filterLSQC.isSuccess == true) {
        return filterLSQC;
      } else {
        return null;
      }
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  static Future<FilterQCModel?> getDefaultQCMauFilter(String token) async {
    final dataResponse = await FilterDropdownQC.getDefaultQCMauFilter(token);
    if (dataResponse.statusCode == 200) {
      final responseDataFilterQC = jsonDecode(dataResponse.body);
      final filterLSQC = FilterQCModel.fromJson(responseDataFilterQC);
      if (filterLSQC.code == 200 && filterLSQC.isSuccess == true) {
        return filterLSQC;
      } else {
        return null;
      }
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  static Future<FilterQCModel?> getDefaultQCHienTruongFilter(String token) async {
    final dataResponse = await FilterDropdownQC.getDefaultQCHienTruongFilter(token);
    if (dataResponse.statusCode == 200) {
      final responseDataFilterQC = jsonDecode(dataResponse.body);
      final filterLSQC = FilterQCModel.fromJson(responseDataFilterQC);
      if (filterLSQC.code == 200 && filterLSQC.isSuccess == true) {
        return filterLSQC;
      } else {
        return null;
      }
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }

  static Future<FilterQCModel?> getDefaultQCSanPhamFilter(String token) async {
    final dataResponse = await FilterDropdownQC.getDefaultQCSanPhamFilter(token);
    if (dataResponse.statusCode == 200) {
      final responseDataFilterQC = jsonDecode(dataResponse.body);
      final filterLSQC = FilterQCModel.fromJson(responseDataFilterQC);
      if (filterLSQC.code == 200 && filterLSQC.isSuccess == true) {
        return filterLSQC;
      } else {
        return null;
      }
    } else {
      throw Exception(dataResponse.reasonPhrase);
    }
  }
}
