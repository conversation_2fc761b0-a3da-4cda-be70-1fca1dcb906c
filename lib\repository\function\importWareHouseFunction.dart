import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Widget/dialogWidget/DialogCompleteSendRequest.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/GetInfoReceiSap.dart';
import '../../model/GetListCatalog.dart';
import '../../model/GetListCatalogTypeCode.dart';
import '../../model/PostSuccessReceiveIntegrationSAP.dart';
import '../../model/getAutoBatch.dart';
import '../../model/getStorageBin.dart';
import '../../model/postQuantityByP0.dart';
import '../../model/rawMaterialCard.dart';
import '../../model/receiveIntegrationSAP.dart';
import '../../model/slocAddresse.dart';
import '../../model/poInfo.dart';
import '../api/getAutoBatchApi.dart';
import '../api/getInfoReceiSap.dart';
import '../api/getListCatalogApi.dart';
import '../api/getListCatalogTypeCodeApi.dart';
import '../api/getStorageBin.dart';
import '../api/importWareHouseApi.dart';
import '../api/updateStatusArrive.dart';

class ImportWareHouseFunction {
  static bool checkIsSend = false;
  // static bool checkIsFormatDouble(List<TextEditingController> lsNumber) {
  //   List<bool> lsCheckDouble = [];
  //   for(var i in lsNumber){
  //     bool check = double.tryParse(i.text) == null;
  //     lsCheckDouble.add(check);
  //   }
  //   return lsCheckDouble.where((element) => element == true).isEmpty;
  //
  // }
  // static bool checkIsFormatDoubleSingle(String number) {
  //     bool check = double.tryParse(number) == null;
  //   return check;
  //
  // }

  static double amount(List<TextEditingController> lsNumber) {
    List<double> convertListInt = lsNumber
        .map((e) => double.parse(double.tryParse(e.text) == null
            ? "0.0"
            : e.text.isNotEmpty
                ? e.text.trim()
                : "0.0"))
        .toList();
    double sum = convertListInt.reduce((a, b) => a + b);
    return sum;
  }

  static double amountListNumber(List<double> lsNumber) {
    // List<double> lsNumber= [];
    // lsNumber.add(number.isEmpty ? 0.0 :double.parse(number));
    double sum = lsNumber.reduce((a, b) => a + b);
    return sum;
  }

  // static double sumListQuantity(List<DataGetQuantityByP0> lsDataGetQuantityByP0){
  //   List <double> convertListInt = lsDataGetQuantityByP0.map((e) => (e.quantityReceived ?? 0.0)).toList();
  //   double sum = convertListInt.reduce((value, element) => value + element);
  //   debugPrint(convertListInt.length.toString());
  //   return sum;
  // }

  static Future<DataRawMeterial?> fetchRawMaterial(String rawMaterialCardId, String token, String fromPage) async {
    final response = await ImportWareHouseApi.getRawMaterialCard(rawMaterialCardId, token, fromPage);
    if (response.statusCode == 200) {
      final responseRawMaterial = jsonDecode(response.body);
      if (responseRawMaterial != null) {
        final getRawMaterial = RawMaterialCard.fromJson(responseRawMaterial);
        if (getRawMaterial.code == 200 && getRawMaterial.isSuccess == true) {
          return getRawMaterial.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<String>?> fetchSoToKhai(String soToKhai, String token) async {
    final response = await ImportWareHouseApi.getListSoToKhai(soToKhai, token);
    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      if (responseBody != null) {
        if (responseBody['data'] != null) {
          final List<String> listSoToKhai = List<String>.from(responseBody['data']);
          return listSoToKhai;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  // static Future<DataRawMeterial?> fetchRawMaterial(String rawMaterialCardId, String token, String fromPage) async {
  //   final response = await ImportWareHouseApi.getRawMaterialCard(rawMaterialCardId, token, fromPage);
  //   if (response.statusCode == 200) {
  //     final responseRawMaterial = jsonDecode(response.body);
  //     if (responseRawMaterial != null) {
  //       final getRawMaterial = RawMaterialCard.fromJson(responseRawMaterial);
  //       if (getRawMaterial.code == 200 && getRawMaterial.isSuccess == true) {
  //         return getRawMaterial.data;
  //       } else {
  //         return null;
  //       }
  //     } else {
  //       return null;
  //     }
  //   } else {
  //     return null;
  //   }
  // }

  static Future<List<DataGetListCatalog>> fetchGetListCatalog(String companyCode, String token) async {
    final response = await GetListCatalogApi.getDetailReservationImportedApi(companyCode, token);
    if (response.statusCode == 200) {
      final responseGetListCatalog = jsonDecode(response.body);
      final getListCatalog = GetListCatalog.fromJson(responseGetListCatalog);
      if (getListCatalog.code == 200 && getListCatalog.isSuccess == true) {
        return getListCatalog.data ?? [];
      } else {
        return [];
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<List<DataGetListCatalogTypeCode>?> fetchGetListCatalogTypeCodeApi(String token) async {
    final response = await GetListCatalogTypeCodeApi.getFilterDropdownQC(token);
    if (response.statusCode == 200) {
      final responseGetListCatalogTypeCode = jsonDecode(response.body);
      final getGetListCatalogTypeCode = GetListCatalogTypeCode.fromJson(responseGetListCatalogTypeCode);
      if (getGetListCatalogTypeCode.code == 200 && getGetListCatalogTypeCode.isSuccess == true) {
        return getGetListCatalogTypeCode.data;
      } else {
        return [];
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<List<DataSlocAddress>?> fetchSlocAddress(String plant, String token) async {
    final response = await ImportWareHouseApi.getSlocAddress(plant, token);
    if (response.statusCode == 200) {
      final responseSlocAddress = jsonDecode(response.body);
      if (responseSlocAddress != null) {
        final getSlocAddress = SlocAddress.fromJson(responseSlocAddress);
        if (getSlocAddress.code == 200 && getSlocAddress.isSuccess == true) {
          return getSlocAddress.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataGetStorageBin>?> fetchStorageBinByParam(String storageBin, String token) async {
    final response = await GetStorageBinApi.getStorageBinByParam(storageBin, token);
    if (response.statusCode == 200) {
      final responseStorageBinByParam = jsonDecode(response.body);
      if (responseStorageBinByParam != null) {
        final getStorageBinByParam = GetStorageBin.fromJson(responseStorageBinByParam);
        if (getStorageBinByParam.code == 200 && getStorageBinByParam.isSuccess == true) {
          // List<DataGetStorageBin> test = [
          //   DataGetStorageBin(key: 'test1',value: 'test')
          // ];
          // return test;
          return getStorageBinByParam.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  // static Future<List<DataGetQuantityByP0>?> fetchQuantityByPO(List<PostQuantityByPO> lsPostQuantityByP0, String token) async{
  //   final response = await ImportWareHouseApi.getQuantityByPO(lsPostQuantityByP0, token);
  //   if (response.statusCode == 200) {
  //     final responseQuantityByPO= jsonDecode(response.body);
  //     if (responseQuantityByPO != null) {
  //       final getQuantityByPO = GetQuantityByPO.fromJson(responseQuantityByPO);
  //
  //       if (getQuantityByPO.code == 200 && getQuantityByPO.isSuccess == true) {
  //         return getQuantityByPO.data;
  //       } else {
  //         return null;
  //       }
  //     } else {
  //       return null;
  //     }
  //   } else {
  //     return null;
  //   }
  // }
  static List<PostQuantityByPO>? listDataGetQuantityByPO(List<PoDetailResponses>? _poDetailResponses) {
    List<PostQuantityByPO>? lsDataGetQuantityByPO = [];
    for (var i in _poDetailResponses!) {
      lsDataGetQuantityByPO.add(PostQuantityByPO(po: i.po, poLine: i.poLine));
    }
    return lsDataGetQuantityByPO;
  }

  static Future<DataGetAutoBatchData?> fetchAutoBatch(GetAutoBatch getAutoBatch, String token) async {
    final response = await GetAuToBatchApi.postAutoBATCH(getAutoBatch, token);
    if (response.statusCode == 200) {
      final responseAutoBatch = jsonDecode(response.body);
      final getAutoBatch = GetAutoBatchData.fromJson(responseAutoBatch);
      if (getAutoBatch.code == 200 && getAutoBatch.isSuccess == true) {
        return getAutoBatch.data;
      } else {
        return null;
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<void> postReceiveIntegrationSAP(ReceiveIntegrationSAP receiveIntegrationSAP, String token, bool viTab, BuildContext context) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await ImportWareHouseApi.receiveIntegrationSAP(receiveIntegrationSAP, token, viTab);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final postMessage = PostSuccessReceiveIntegrationSAP.fromJson(jsonDecode(response.body));
        if (postMessage.code == 200 && postMessage.isSuccess == true) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogCompleteSendRequest(message: postMessage.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       postMessage.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
          // Future.delayed(const Duration(seconds: 0), () {
          //   Navigator.pop(context);
          // });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: postMessage.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       postMessage.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
          // showDialog(
          //     context: context,a
          //     barrierDismissible: false,
          //     builder: (BuildContext context) =>
          //         DialogError(message: postMessage.message.toString()));
        }
      } else if (response.statusCode == 400) {
        // final postMessage = ErrorMessage.fromJson(jsonDecode(response.body));
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static List<QuantityImports> postQuantityImports(
    List<TextEditingController> lsController,
    List<PoDetailResponses>? _poDetailResponses,
    DataRawMeterial? dataRawMeterial,
  ) {
    List<QuantityImports> lsQuantityImports = [];
    for (int i = 0; i < _poDetailResponses!.length; i++) {
      final quantitySOWBS = QuantityImports(
          po: _poDetailResponses[i].po,
          poLine: _poDetailResponses[i].poLine,
          so: _poDetailResponses[i].so == "" ? null : _poDetailResponses[i].so,
          soLine: _poDetailResponses[i].soLine == "" ? null : _poDetailResponses[i].soLine,
          wbs: _poDetailResponses[i].wbs == "" ? null : _poDetailResponses[i].wbs,
          quantity: double.parse(lsController[i].text),
          unit: _poDetailResponses[i].unit);
      lsQuantityImports.add(quantitySOWBS);
    }
    return lsQuantityImports;
  }

  static Future<void> sendUpdateStatusService(String rawMaterialCardId, String token, BuildContext context) async {
    final response = await UpdateStatusArriveApi.updateStatusArrive(rawMaterialCardId, token);
    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          "Cập nhật thành công!",
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    } else if (response.statusCode == 400) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          "Cập nhật thất bại! NVL không tồn tại",
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    } else {
      showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
    }
  }

  static Future<void> sendImportWareHouse(
    List<PoDetailResponses>? _poDetailResponses,
    List<TextEditingController> lsControllers,
    DataRawMeterial? dataRawMeterial,
    String barcode,
    DataSlocAddress? selectedSloc,
    String controllerNumberLotText,
    String token,
    String? storageBinID,
    AutoBatch autoBatch,
    bool viTab,
    String tds,
    String soToKhai,
    BuildContext context,
  ) async {
    final quantityImports = postQuantityImports(lsControllers, _poDetailResponses, dataRawMeterial);
    final receiveIntegrationSAP = ReceiveIntegrationSAP(
      rawMaterialCardId: barcode,
      quantityImports: quantityImports,
      slocId: selectedSloc!.slocId,
      storageBinId: storageBinID,
      batch: controllerNumberLotText.isEmpty ? null : controllerNumberLotText,
      autoBatch: autoBatch,
      tds: tds,
      soToKhai: soToKhai,
    );
    // debugPrint(jsonEncode(receiveIntegrationSAP));
    await postReceiveIntegrationSAP(receiveIntegrationSAP, token, viTab, context);
  }

  static Future<DataGetInfoReceiSap?> fetchGetInfoReceiSapApi(String goodsReceivedNote, String token) async {
    final response = await GetInfoReceiSapApi.getGoodsReceivedNote(goodsReceivedNote, token);

    if (response.statusCode == 200) {
      final responseGetInfoReceiSapApi = jsonDecode(response.body);
      final getGetInfoReceiSapApi = GetInfoReceiSap.fromJson(responseGetInfoReceiSapApi);
      if (getGetInfoReceiSapApi.code == 200 && getGetInfoReceiSapApi.isSuccess == true) {
        return getGetInfoReceiSapApi.data;
      } else {
        return null;
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<DataGetInfoReceiSap?> takeGetInfoReceiSapApi(String goodsReceivedNote, String token, BuildContext context, bool checkMounted) async {
    try {
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      checkIsSend = false;
      final getData = await fetchGetInfoReceiSapApi(goodsReceivedNote, token);
      if (!checkMounted) return null;
      Navigator.pop(context);
      checkIsSend = true;
      return getData;
    } on SocketException catch (_) {
      if (!checkMounted) return null;
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      return null;
    } catch (error) {
      if (!checkMounted) return null;
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          error.toString(),
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
      return null;
    }
  }

  static List<DataSlocAddress> filterDataSlocAddress(List<DataSlocAddress> lsDataSlocAddress, String query) {
    List<DataSlocAddress> getFilterDataSlocAddress = lsDataSlocAddress
        .where((element) => element.defaultStorageBin != null && element.defaultStorageBin!.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return getFilterDataSlocAddress;
  }

  static Future<void> importFinishedProduct(Map<String, dynamic> importData, String token, BuildContext context) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      final response = await ImportWareHouseApi.importFinishedProduct(importData, token);
      Navigator.pop(context);
      checkIsSend = true;

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['code'] == 200 && responseData['isSuccess'] == true) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogCompleteSendRequest(message: responseData['message'].toString(), route: "/ProductManagement"));
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: responseData['message'].toString()));
        }
      } else if (response.statusCode == 400) {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static Future<POInfo?> fetchPOInfoBySerial(String serial, String token) async {
    try {
      final response = await ImportWareHouseApi.getPOInfoBySerial(serial, token);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData != null && responseData['isSuccess'] == true && responseData['data'] != null) {
          return POInfo.fromJson(responseData['data']);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching PO info: $e');
      return null;
    }
  }
}
