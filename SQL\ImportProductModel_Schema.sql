-- Create ImportProductModel table for tracking finished product imports
CREATE TABLE [MES].[ImportProductModel](
    [ImportProductId] [uniqueidentifier] NOT NULL DEFAULT NEWID(),
    [PO] [nvarchar](50) NOT NULL,                          -- Production Order (LSX SAP)
    [ProductCode] [nvarchar](50) NOT NULL,                 -- Product Code (Mã TP)
    [ProductName] [nvarchar](500) NULL,                    -- Product Name (Tên TP)
    [SO] [nvarchar](50) NULL,                              -- Sales Order
    [SOItem] [nvarchar](10) NULL,                          -- Sales Order Item
    [WBS] [nvarchar](50) NULL,                             -- Work Breakdown Structure
    [Plant] [nvarchar](10) NOT NULL,                       -- Plant Code
    [SlocId] [uniqueidentifier] NULL,                  -- Storage Location ID
    [StorageBinId] [uniqueidentifier] NULL,                -- Storage Bin ID
    [Batch] [nvarchar](50) NOT NULL,                       -- Batch Number
    [ImportQuantity] [int] NOT NULL,                       -- Quantity being imported in this transaction
    [Unit] [nvarchar](10) NOT NULL DEFAULT 'CAI',          -- Unit of measure
    [ImportSequence] [int] NOT NULL DEFAULT 1,             -- Import sequence number for the same PO (1st, 2nd, 3rd import)
    [DocumentDate] [datetime2](7) NOT NULL DEFAULT GETDATE(), -- Import document date
    [SAPDocumentNumber] [nvarchar](50) NULL,               -- SAP document number from response
    [SAPYear] [nvarchar](4) NULL,                          -- SAP document year
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Pending',   -- Status: Pending, Success, Failed
    [SendToSAPTime] [datetime2](7) NULL,                   -- When sent to SAP
    [SAPResponse] [nvarchar](max) NULL,                    -- SAP response message
    [CreateTime] [datetime2](7) NOT NULL DEFAULT GETDATE(),
    [CreateBy] [uniqueidentifier] NULL,
    [LastEditTime] [datetime2](7) NULL,
    [LastEditBy] [uniqueidentifier] NULL,
    [Actived] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ImportProductModel] PRIMARY KEY CLUSTERED ([ImportProductId] ASC),
    CONSTRAINT [FK_ImportProductModel_SlocModel] FOREIGN KEY([SlocId]) REFERENCES Warehouse.StockModel ([StockId]),
    CONSTRAINT [FK_ImportProductModel_StorageBinModel] FOREIGN KEY([StorageBinId]) REFERENCES tMasterData.StorageBinModel ([StorageBinId])
);

-- Create indexes for better performance
CREATE NONCLUSTERED INDEX [IX_ImportProductModel_PO] ON [MES].[ImportProductModel]([PO]);
CREATE NONCLUSTERED INDEX [IX_ImportProductModel_ProductCode] ON [MES].[ImportProductModel]([ProductCode]);
CREATE NONCLUSTERED INDEX [IX_ImportProductModel_Plant] ON [MES].[ImportProductModel]([Plant]);
CREATE NONCLUSTERED INDEX [IX_ImportProductModel_CreateTime] ON [MES].[ImportProductModel]([CreateTime]);
CREATE NONCLUSTERED INDEX [IX_ImportProductModel_PO_Status] ON [MES].[ImportProductModel]([PO], [Status]);

-- Add computed column for total imported quantity per PO
-- This will be used in queries to calculate remaining quantity 