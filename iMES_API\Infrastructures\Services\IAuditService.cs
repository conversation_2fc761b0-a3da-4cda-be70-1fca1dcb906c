using System.Threading.Tasks;

namespace iMES_API.Infrastructures.Services
{
    public interface IAuditService
    {
        Task LogAuditAsync(
            string userId,
            string username,
            string action,
            string entityName,
            string entityId,
            string oldValue,
            string newValue,
            string ipAddress,
            string deviceName);
    }
} 