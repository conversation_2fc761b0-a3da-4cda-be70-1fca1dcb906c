import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ErrorWidgetColumn extends StatelessWidget {
  final String messageError;
  const ErrorWidgetColumn({Key? key,required this.messageError}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children:<Widget> [
            Icon(Icons.warning_rounded,size: 50.sp),
            SizedBox(height: 10.h),
            Text(messageError,style: TextStyle(fontSize: 15.sp,color: Colors.black))
          ],
        );

  }
}
