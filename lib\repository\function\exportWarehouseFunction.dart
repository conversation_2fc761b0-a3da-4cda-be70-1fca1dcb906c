import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Widget/dialogWidget/DialogCompleteSendRequest.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/GetBarcodeReceive.dart';
import '../../model/getBatch.dart';
import '../../model/getInventorySAP.dart';
import '../../model/getListSOWBSByBatchTranferWarehouse.dart';
import '../../model/getReservation.dart';
import '../../model/postGetListSOWBSByBatch.dart';
import '../../model/slocAddresse.dart';
import '../../model/warehouseTranfer.dart';
import '../api/exportWareHouseApi.dart';
import '../api/getBarcodeReceiveApi.dart';
import '../api/getListSOWBSByBatchApi.dart';
import '../api/inventorySAPApi.dart';

class ExportWareHouseFunction {
  static bool checkIsSend = false;

  static bool checkIsFormatDouble(List<TextEditingController> lsNumber) {
    List<bool> lsCheckDouble = [];
    for (var i in lsNumber) {
      bool check = double.tryParse(i.text) == null;
      lsCheckDouble.add(check);
    }
    return lsCheckDouble.where((element) => element == true).isEmpty;
  }

  static bool checkIsFormatDoubleSingle(String number) {
    bool check = double.tryParse(number) == null;
    return check;
  }

  static Future<DataGetBarcodeReceive?> fetchBarcodeReceive(String rawMaterialCardId, String token) async {
    final response = await GetBarcodeReceiveApi.getBarcodeReceive(rawMaterialCardId, token);
    if (response.statusCode == 200) {
      final responseBarcodeReceive = jsonDecode(response.body);
      if (responseBarcodeReceive != null && responseBarcodeReceive['data'] != false) {
        final getBarcodeReceive = GetBarcodeReceiveModel.fromJson(responseBarcodeReceive);
        if (getBarcodeReceive.code == 200 && getBarcodeReceive.isSuccess == true) {
          return getBarcodeReceive.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static List<WarehouseTranferDetails> getListWarehouseTranferDetails(
    List<TextEditingController> lsQuantity,
    List<DataGetListSOWBSByBatchTranferWareHouse> lsDataGetListSOWBSByBatchTranferWareHouse,
  ) {
    List<WarehouseTranferDetails> lsWareHouseTranferDetails = [];
    for (int i = 0; i < lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      lsWareHouseTranferDetails.add(WarehouseTranferDetails(
        productCode: lsDataGetListSOWBSByBatchTranferWareHouse[i].productCode == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].productCode,
        so: lsDataGetListSOWBSByBatchTranferWareHouse[i].so == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].so,
        soLine: lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine,
        wbs: lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs,
        quantity: lsQuantity[i].text.isEmpty ? null : double.parse(lsQuantity[i].text),
        unit: lsDataGetListSOWBSByBatchTranferWareHouse[i].unit,
        batchNumber: lsDataGetListSOWBSByBatchTranferWareHouse[i].batchNumber,
        rawMaterialCardId: lsDataGetListSOWBSByBatchTranferWareHouse[i].rawMaterialCardId,
      ));
    }
    lsWareHouseTranferDetails.removeWhere((element) => element.quantity == null);
    return lsWareHouseTranferDetails;
  }

  static List<WarehouseTranferDetails> getListWarehouseTranferDetails2(
    List<TextEditingController> lsQuantity,
    List<DataGetListSOWBSByBatchTranferWareHouse> lsDataGetListSOWBSByBatchTranferWareHouse,
    List<TextEditingController> lsSTTLine,
  ) {
    List<WarehouseTranferDetails> lsWareHouseTranferDetails = [];
    for (int i = 0; i < lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      lsWareHouseTranferDetails.add(WarehouseTranferDetails(
        productCode: lsDataGetListSOWBSByBatchTranferWareHouse[i].productCode == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].productCode,
        so: lsDataGetListSOWBSByBatchTranferWareHouse[i].so == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].so,
        soLine: lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].soLine,
        wbs: lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs == "" ? null : lsDataGetListSOWBSByBatchTranferWareHouse[i].wbs,
        quantity: lsQuantity[i].text.isEmpty ? null : double.parse(lsQuantity[i].text),
        unit: lsDataGetListSOWBSByBatchTranferWareHouse[i].unit,
        batchNumber: lsDataGetListSOWBSByBatchTranferWareHouse[i].batchNumber,
        rawMaterialCardId: lsDataGetListSOWBSByBatchTranferWareHouse[i].rawMaterialCardId,
        sttLine: lsSTTLine[i].text,
      ));
    }
    lsWareHouseTranferDetails.removeWhere((element) => element.quantity == null);
    return lsWareHouseTranferDetails;
  }

  static Future<DataBatch?> fetchBatch(String rawMaterialCardId, String token) async {
    final response = await ExportWareHouseApi.getBatch(rawMaterialCardId, token);
    if (response.statusCode == 200) {
      final responseBatch = jsonDecode(response.body);
      if (responseBatch != null && responseBatch['data'] != false) {
        final getBatch = GetBatchNumberByPallet.fromJson(responseBatch);
        if (getBatch.code == 200 && getBatch.isSuccess == true) {
          return getBatch.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<DataReservation?> fetchReservation(String materialReservationId, String token) async {
    final response = await ExportWareHouseApi.getReservation(materialReservationId, token);
    if (response.statusCode == 200) {
      final responseReservation = jsonDecode(response.body);
      if (responseReservation != null) {
        final getReservation = GetReservation.fromJson(responseReservation);
        if (getReservation.code == 200 && getReservation.isSuccess == true) {
          return getReservation.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataReservation>?> fetchReservationItems(String materialReservationId, String token) async {
    final response = await ExportWareHouseApi.getReservationItems(materialReservationId, token);

    if (response.statusCode == 200) {
      final responseReservation = jsonDecode(response.body);

      if (responseReservation != null) {
        final getReservation = GetReservationItems.fromJson(responseReservation);

        if (getReservation.code == 200 && getReservation.isSuccess == true) {
          return getReservation.data ?? [];
        }
      }
    }
    return null;
  }

  static Future<List<DataGetListSOWBSByBatchTranferWareHouse>?> fetchListSOWBSBYBatchTranfer(
      PostGetListSOWBSByBatch postGetListSOWBSByBatch, String token) async {
    final response = await GetListSOWBSByBatchApi.getListSOWBSBYBatchTranfer(postGetListSOWBSByBatch, token);
    if (response.statusCode == 200) {
      final responseListSOWBS = jsonDecode(response.body);
      if (responseListSOWBS != null) {
        final getListSOWBS = GetListSOWBSByBatchTranferWareHouse.fromJson(responseListSOWBS);
        if (getListSOWBS.code == 200 && getListSOWBS.isSuccess == true) {
          return getListSOWBS.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  // static Future<MessageWarehouseTranfer?> saveWarehouseFunction(WarehouseTranfer warehouseTranfer, String token) async {
  //   final response = await ExportWareHouseApi.postWarehouseTranfer(warehouseTranfer, token);
  //   if (response.statusCode == 200) {
  //     final getMessage = MessageWarehouseTranfer.fromJson(jsonDecode(response.body));
  //       return getMessage;
  //   } else {
  //     return null;
  //   }
  // }
  static List<double> getListDouble(String batchNumber, List<DataGetListSOWBSByBatchTranferWareHouse> lsDataGetListSOWBSByBatchTranferWareHouse,
      List<TextEditingController> lsController) {
    List<double> lsNumber = [];
    for (int i = 0; i < lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      if (lsDataGetListSOWBSByBatchTranferWareHouse[i].batchNumber == batchNumber) {
        lsNumber.add(lsController[i].text.isEmpty ? 0.0 : double.parse(lsController[i].text));
      }
    }
    return lsNumber;
  }

  static List<double> getListDouble2(
    String batchNumber,
    List<DataGetListSOWBSByBatchTranferWareHouse> lsDataGetListSOWBSByBatchTranferWareHouse,
    List<TextEditingController> lsController,
    String? productCode,
  ) {
    List<double> lsNumber = [];
    for (int i = 0; i < lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
      var item = lsDataGetListSOWBSByBatchTranferWareHouse[i];
      if (item.batchNumber == batchNumber && item.productCode == productCode) {
        lsNumber.add(lsController[i].text.isEmpty ? 0.0 : double.parse(lsController[i].text));
      }
    }
    return lsNumber;
  }

  static Future<void> sendWarehouseFuncion(WarehouseTranfer warehouseTranfer, String token, BuildContext context, String plant) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await ExportWareHouseApi.postWarehouseTranfer(warehouseTranfer, token);
      // final message = await saveWarehouseFunction(warehouseTranfer, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final message = MessageWarehouseTranfer.fromJson(jsonDecode(response.body));
        if (message.code == 200 && message.isSuccess == true) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogCompleteSendRequest(message: message.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       message.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
          // Future.delayed(const Duration(seconds: 0), () {
          //   Navigator.pop(context, true);
          // });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: message.message.toString()));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static Future<void> sendWarehouseFuncion2(WarehouseTranfer warehouseTranfer, String token, BuildContext context, String plant) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await ExportWareHouseApi.postWarehouseTranfer2(warehouseTranfer, token);
      // final message = await saveWarehouseFunction(warehouseTranfer, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final message = MessageWarehouseTranfer.fromJson(jsonDecode(response.body));
        if (message.code == 200 && message.isSuccess == true) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogCompleteSendRequest(message: message.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       message.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
          // Future.delayed(const Duration(seconds: 0), () {
          //   Navigator.pop(context, true);
          // });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: message.message.toString()));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static Future<Data?> fetchInventorySAP(PostInventorySAP postInventorySAP, String token) async {
    final response = await InventorySAPApi.postInventorySap(postInventorySAP, token);
    if (response.statusCode == 200) {
      final getInventorySap = GetInventorySAP.fromJson(jsonDecode(response.body));
      if (getInventorySap.code == 200 && getInventorySap.isSuccess == true) {
        debugPrint(getInventorySap.data.toString());
        return getInventorySap.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static List<DataSlocAddress?> filterDataSlocAddress(List<DataSlocAddress?> lsDataSlocAddress, String query) {
    List<DataSlocAddress?> getFilterDataSlocAddress = lsDataSlocAddress
        .where((element) => element!.defaultStorageBin != null && element.defaultStorageBin!.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return getFilterDataSlocAddress;
  }
}
