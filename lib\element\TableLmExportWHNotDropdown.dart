import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/slocAddresse.dart';

class TalbeLmEXportWHNotDropDown extends StatelessWidget {
  final String textCL1;
  final DataSlocAddress? selectedSloc;
  final ValueChanged<DataSlocAddress?> onChange;
  final List<DataSlocAddress>? lsDataSlocAddress;
  final String textCL3;
  final String textCL4;
  final int colorCL1;
  final int colorCL2;
  final int colorCL3;
  final int colorCL4;
  const TalbeLmEXportWHNotDropDown({Key? key,
    required this.textCL1,
    required this.selectedSloc,
    required this.onChange,
    required this.lsDataSlocAddress,
    required this.textCL3,
    required this.textCL4,
    required this.colorCL1,
    required this.colorCL2,
    required this.colorCL3,
    required this.colorCL4,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget> [
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )
              ),
              child: Text(
                textCL1, style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                    color: Colors.grey.shade400,
                    width: 0.5.w),
                borderRadius:
                BorderRadius.circular(5.r),
              ),
              child: DropdownButtonHideUnderline(
                child:
                DropdownButton<DataSlocAddress>(
                  isExpanded: true,
                  value: selectedSloc,
                  iconSize: 15.sp,
                  style: const TextStyle(
                      color: Colors.white),
                  onChanged: onChange,
                  items: lsDataSlocAddress!.map(
                          (DataSlocAddress wareHouse) {
                        return DropdownMenuItem<
                            DataSlocAddress>(
                          value: wareHouse,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 15.w),
                            child: Center(
                              child: Text(
                                wareHouse.sloc.toString(),
                                style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 11.sp),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL3),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )
              ),
              child: Text(
                textCL3, style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL4),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )
              ),
              child: Text(
                textCL4, style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HeaderTableImExportWH extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const HeaderTableImExportWH({Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget> [
          Expanded(
            flex: 5,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border.all(
                  color: Colors.black,
                  width: 0.5.w,
                ),
              ),
              child: Center(
                child: Text(
                  textCL1, style: TextStyle(fontSize: 12.sp, color: Colors.white),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    top: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )
              ),
              child: Center(
                child: Text(
                  textCL2, style: TextStyle(fontSize: 12.sp, color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}