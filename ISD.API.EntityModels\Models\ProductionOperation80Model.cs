﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductionOperation80Model", Schema = "MES")]
    public partial class ProductionOperation80Model
    {
        [Key]
        public Guid ProductOperationId { get; set; }
        [StringLength(10)]
        public string VORNR { get; set; }
        public int? AUFPL { get; set; }
        public int? APLZL { get; set; }
        [StringLength(10)]
        public string STEUS { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(100)]
        public string LTXA1 { get; set; }
        [StringLength(100)]
        public string LTXA2 { get; set; }
        [StringLength(50)]
        public string LAR01 { get; set; }
        [StringLength(50)]
        public string LAR02 { get; set; }
        [StringLength(50)]
        public string LAR03 { get; set; }
        [StringLength(50)]
        public string LAR04 { get; set; }
        [StringLength(50)]
        public string LAR05 { get; set; }
        [StringLength(50)]
        public string LAR06 { get; set; }
        [StringLength(50)]
        public string ZERMA { get; set; }
        [StringLength(50)]
        public string VGWTS { get; set; }
        public int? RUECK { get; set; }
        [StringLength(10)]
        public string VERID { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}