﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DepartmentModel", Schema = "tMasterData")]
    public partial class DepartmentModel
    {
        public DepartmentModel()
        {
            Department_Routing_Mapping = new HashSet<Department_Routing_Mapping>();
            SalesEmployeeModel = new HashSet<SalesEmployeeModel>();
            StockReceivingDetailModel = new HashSet<StockReceivingDetailModel>();
        }

        [Key]
        public Guid DepartmentId { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? StoreId { get; set; }
        public Guid? WorkShopId { get; set; }
        [StringLength(100)]
        public string DepartmentCode { get; set; }
        [StringLength(100)]
        public string DepartmentName { get; set; }
        public int? OrderIndex { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        [StringLength(50)]
        public string ToTruongCode { get; set; }

        [ForeignKey("StoreId")]
        [InverseProperty("DepartmentModel")]
        public virtual StoreModel Store { get; set; }
        [InverseProperty("Department")]
        public virtual ICollection<Department_Routing_Mapping> Department_Routing_Mapping { get; set; }
        [InverseProperty("Department")]
        public virtual ICollection<SalesEmployeeModel> SalesEmployeeModel { get; set; }
        [InverseProperty("Department")]
        public virtual ICollection<StockReceivingDetailModel> StockReceivingDetailModel { get; set; }
    }
}