# ApiSuggestionField Usage Guide

## Replacing LSX SAP Implementation

### Before (Original Code)
```dart
// Original LSX SAP implementation with TypeAheadField
Row(
  children: [
    const Expanded(flex: 3, child: QualityTitleField(title: "LSX SAP:")),
    SizedBox(width: 10.w),
    Expanded(
      flex: 7,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Stack(
          children: [
            TypeAheadField(
              minCharsForSuggestions: 3,
              // ... lots of configuration code
            ),
            // Clear button implementation
            Visibility(
              visible: _checkCanClearLSX(),
              child: Positioned(/* ... */),
            ),
          ],
        ),
      ),
    ),
  ],
)
```

### After (Using ApiSuggestionField)
```dart
// New implementation using ApiSuggestionField
ApiSuggestionField(
  label: "LSX SAP:",
  controller: _controllerLSXSAP,
  enabled: checkQualityControl() && _isTTFCode != true,
  minCharsForSuggestions: 3,
  hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
  keyboardType: TextInputType.number,
  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
  canClear: () => _checkCanClearLSX(),
  suggestionsCallback: (pattern) async {
    return await QualityControlDetailFunction.fetchLSXSAP(
      pattern, 
      widget.user.token.toString()
    );
  },
  onSuggestionSelected: (suggestion) async {
    if (_controllerLSXSAP.text == suggestion && _isLSXSAPSelected) {
      return;
    }
    setState(() {
      _controllerLSXSAP.text = suggestion;
      _isLSXSAPSelected = true;
    });
    await _loadQualityControlByLSQSAP(suggestion);
  },
  onClear: () {
    setState(() {
      _controllerLSXSAP.text = "";
      _isLSXSAPSelected = false;
      _qualityControl = QualityControl();
      _lsCongDoanNhoMasterData = [QualityControlDetailFunction.defaultValueCongDoanNho];
      _selectedCongDoanNho = QualityControlDetailFunction.defaultValueCongDoanNho;
    });
    clearForNew();
  },
)
```

## Other Use Cases

### 1. Product Code Suggestion
```dart
ApiSuggestionField(
  label: "Mã sản phẩm:",
  controller: _productCodeController,
  hintText: "Nhập mã sản phẩm",
  suggestionsCallback: (pattern) async {
    return await ProductService.searchProductCodes(pattern);
  },
  onSuggestionSelected: (productCode) async {
    await _loadProductDetails(productCode);
  },
)
```

### 2. Customer Search
```dart
ApiSuggestionField(
  label: "Khách hàng:",
  controller: _customerController,
  minCharsForSuggestions: 2,
  hintText: "Tìm khách hàng",
  noItemsFoundText: "Không tìm thấy khách hàng",
  suggestionsCallback: (pattern) async {
    return await CustomerService.searchCustomers(pattern);
  },
  onSuggestionSelected: (customer) async {
    await _selectCustomer(customer);
  },
)
```

### 3. Department with Custom Styling
```dart
ApiSuggestionField(
  label: "Phòng ban:",
  controller: _departmentController,
  borderColor: Colors.blue,
  borderRadius: BorderRadius.circular(8.r),
  textStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
  suggestionsCallback: (pattern) async {
    return await DepartmentService.searchDepartments(pattern);
  },
  onSuggestionSelected: (department) async {
    await _selectDepartment(department);
  },
)
```

### 4. With Loading State Management
```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  bool _isLoadingEmployees = false;
  TextEditingController _employeeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ApiSuggestionField(
      label: "Nhân viên:",
      controller: _employeeController,
      isLoading: _isLoadingEmployees,
      suggestionsCallback: (pattern) async {
        setState(() => _isLoadingEmployees = true);
        try {
          return await EmployeeService.searchEmployees(pattern);
        } finally {
          setState(() => _isLoadingEmployees = false);
        }
      },
      onSuggestionSelected: (employee) async {
        await _selectEmployee(employee);
      },
    );
  }
}
```

### 5. With Camera Scan (QR Code/Barcode)
```dart
ApiSuggestionField(
  label: "Storage Bin:",
  controller: _storageBinController,
  showCameraButton: true,
  canShowCamera: () => _selectedSloc != null, // Show camera only when condition is met
  suggestionsCallback: (pattern) async {
    return await StorageService.searchStorageBins(pattern);
  },
  onSuggestionSelected: (storageBin) async {
    await _selectStorageBin(storageBin);
  },
  onCameraScan: () async {
    final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
    if (data == null) return;
    
    var storageBin = data as DataSlocAddress;
    if (storageBin.defaultStorageBin != null) {
      setState(() {
        _storageBinController.text = storageBin.defaultStorageBin!;
        _storageBinID = storageBin.defaultStorageBinId!;
      });
    }
  },
)
```

### 6. Custom Camera Icon and Tooltip
```dart
ApiSuggestionField(
  label: "Product Code:",
  controller: _productController,
  showCameraButton: true,
  cameraIcon: Icon(Icons.qr_code_scanner, size: 20.sp, color: Colors.blue),
  cameraTooltip: "Quét mã sản phẩm",
  suggestionsCallback: (pattern) async {
    return await ProductService.searchProducts(pattern);
  },
  onSuggestionSelected: (product) async {
    await _selectProduct(product);
  },
  onCameraScan: () async {
    final result = await Navigator.pushNamed(context, '/barcode_scanner');
    if (result != null) {
      _productController.text = result as String;
      await _loadProductByCode(result);
    }
  },
)
```

### 7. Both Clear and Camera Buttons
```dart
ApiSuggestionField(
  label: "LSX SAP:",
  controller: _lsxController,
  showClearButton: true,
  showCameraButton: true,
  canClear: () => _lsxController.text.isNotEmpty,
  canShowCamera: () => widget.enabled,
  suggestionsCallback: (pattern) async {
    return await QualityControlDetailFunction.fetchLSXSAP(pattern, token);
  },
  onSuggestionSelected: (lsx) async {
    await _handleLSXSelection(lsx);
  },
  onClear: () {
    setState(() {
      _lsxController.clear();
      _resetLSXData();
    });
  },
  onCameraScan: () async {
    final scannedCode = await Navigator.pushNamed(context, '/qr_scanner');
    if (scannedCode != null) {
      _lsxController.text = scannedCode as String;
      await _handleLSXSelection(scannedCode);
    }
  },
)
```

## Key Benefits

### 🚀 Reduced Code Duplication
- **Before**: ~120 lines per implementation
- **After**: ~20 lines per implementation
- **Savings**: 80% less code

### 🎨 Consistent UI/UX
- Same look and feel across all suggestion fields
- Standardized loading states and error handling
- Unified clear button behavior

### 🔧 Easy Customization
- Configurable styling (colors, fonts, borders)
- Custom loading widgets
- Flexible validation logic

### 🛠️ Better Maintainability
- Single source of truth for suggestion field logic
- Easier to fix bugs and add features
- Type-safe callback functions

## Migration Steps

1. **Import the component**:
   ```dart
   import 'package:ttf/element/ApiSuggestionField.dart';
   ```

2. **Replace existing TypeAheadField implementations**:
   - Extract controller, API call, and selection logic
   - Use ApiSuggestionField with appropriate callbacks

3. **Test thoroughly**:
   - Verify API calls work correctly
   - Check loading states and error handling
   - Ensure clear button functionality

4. **Update documentation**:
   - Add usage examples for new implementations
   - Update style guide with new patterns 