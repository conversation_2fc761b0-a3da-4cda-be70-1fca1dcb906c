﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SMSModel", Schema = "Customer")]
    public partial class SMSModel
    {
        [Key]
        public Guid SMSId { get; set; }
        [StringLength(4000)]
        public string SMSContent { get; set; }
        [StringLength(50)]
        public string SMSTo { get; set; }
        [StringLength(4000)]
        public string ResponseText { get; set; }
        public bool? isSent { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateDate { get; set; }
        [StringLength(100)]
        public string BrandName { get; set; }
        [StringLength(200)]
        public string ErrorMessage { get; set; }
    }
}