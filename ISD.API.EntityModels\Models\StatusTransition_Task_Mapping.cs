﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StatusTransition_Task_Mapping", Schema = "Task")]
    public partial class StatusTransition_Task_Mapping
    {
        [Key]
        public Guid TaskTransitionLogId { get; set; }
        public Guid? TaskId { get; set; }
        public Guid? FromStatusId { get; set; }
        public Guid? ToStatusId { get; set; }
        [StringLength(4000)]
        public string Note { get; set; }
        public Guid? ApproveBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ApproveTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
    }
}