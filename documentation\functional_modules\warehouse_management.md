# Warehouse Management Module

The Warehouse Management module in TTF MES Mobile provides comprehensive tools for managing material flow, inventory transfers, and warehouse operations. This module ensures accurate tracking of materials throughout the manufacturing facility.

## Module Structure

The Warehouse Management module is distributed across several files in the `lib/page/` directory:

### Main Components

- `ExportWarehouse.dart` and `ExportWarehouse2.dart`: Material export operations
- `AddTranferWarehouse.dart`: Add transfer operations between warehouses
- `ListTranferMaterial.dart` and `ListTranferMaterial2.dart`: Material transfer lists
- `ImportWarehouseSAP.dart`: SAP integration for warehouse imports

### QR Code Integration

- `QRCodePageGetSlocExport.dart`: QR scanning for storage location export
- `QRcodePageTranferMaterial.dart`: QR scanning for material transfers
- `QrCodePageGetSlocAddTranferWareHouse.dart`: QR scanning for storage location transfers

### Supporting Screens

- `TraHangNCC.dart`: Supplier return management
- `CreateNewTraHangNCC.dart`: Create new supplier returns
- `DetailReservation.dart`: Reservation details for materials

## Features

### Material Transfers

The material transfer functionality allows users to:

- Transfer materials between storage locations
- Record transfer details including quantity and reason
- Scan QR codes to identify materials and locations
- Track transfer history and status
- Generate transfer documentation

### Warehouse Exports

The warehouse export feature enables:

- Selection of materials for export
- Quantity specification for export
- Export documentation generation
- Export approval workflow
- Integration with inventory management

### Warehouse Imports

The import functionality supports:

- Receiving materials into the warehouse
- SAP integration for import operations
- Quality inspection connection for received materials
- Barcode/QR code scanning for efficient receiving
- Import documentation generation

### Supplier Returns

For material returns to suppliers, the module provides:

- Return request creation
- Return reason documentation
- Return quantity specification
- Return status tracking
- Integration with inventory adjustment

### Reservation Management

The reservation system allows:

- Material reservation for production orders
- Reservation status tracking
- Detailed view of reserved materials
- Fulfillment of material reservations
- Connection to production requirements

## Workflows

### Material Transfer Process

1. Access the Material Transfer list (`ListTranferMaterial.dart`)
2. Create a new transfer using Add Transfer (`AddTranferWarehouse.dart`)
3. Scan QR codes or manually enter source location
4. Scan QR codes or manually enter destination location
5. Select materials and specify quantities
6. Add transfer reason and notes
7. Submit transfer for processing
8. Update inventory in both locations

### Warehouse Export Process

1. Navigate to Export Warehouse (`ExportWarehouse.dart`)
2. Scan location or select manually
3. Select materials for export
4. Specify export quantities
5. Add documentation information
6. Submit export operation
7. Generate export documentation

### Warehouse Import Process

1. Access Import Warehouse SAP (`ImportWarehouseSAP.dart`)
2. Scan incoming material codes or enter manually
3. Verify material details against documentation
4. Specify received quantities
5. Assign storage locations
6. Connect to quality inspection if required
7. Complete import operation

### Supplier Return Process

1. Navigate to Supplier Return (`TraHangNCC.dart`)
2. Create new return (`CreateNewTraHangNCC.dart`)
3. Select supplier information
4. Select materials to return
5. Specify return quantities and reasons
6. Add documentation and notes
7. Submit return request
8. Generate return documentation

## Data Models

The warehouse management module uses the following data models:

- **TransferModel**: Material transfer data
- **WarehouseExportModel**: Export operation data
- **WarehouseImportModel**: Import operation data
- **SupplierReturnModel**: Return request data
- **ReservationModel**: Material reservation data
- **StorageLocationModel**: Storage location information

## API Integration

Warehouse operations are synchronized with backend systems through the following API endpoints:

- `/api/warehouse/transfer`: Material transfer operations
- `/api/warehouse/export`: Export operations
- `/api/warehouse/import`: Import operations
- `/api/warehouse/return`: Supplier return operations
- `/api/warehouse/reservation`: Reservation management

## User Interfaces

### List Views
Warehouse list views display:
- Material identification
- Location information
- Operation status
- Quantities
- Timestamps

### Detail Views
Warehouse operation detail views include:
- Material specifications
- Source and destination locations
- Quantity information
- Operation status
- Approval information
- Documentation references

### QR Code Scanning
QR code scanning interfaces provide:
- Camera view for scanning
- Manual entry option
- Scan history
- Confirmation of scanned data
- Error handling for invalid codes

## Integration with Other Modules

The Warehouse Management module integrates with other modules in the following ways:

- **Inventory Management**: Updates inventory levels based on warehouse operations
- **Quality Control**: Connects quality inspection to received materials
- **Production Management**: Fulfills material requirements for production
- **Material Consumption**: Tracks consumption of materials from warehouses

## Best Practices

For effective use of the Warehouse Management module:

1. Always use QR scanning when available to reduce errors
2. Verify material information before completing transfers
3. Document all non-standard operations with detailed notes
4. Regularly reconcile physical inventory with system records
5. Follow proper approval workflows for material movements
6. Use appropriate reason codes for all operations
7. Ensure proper documentation is generated for audit purposes

## Future Enhancements

Planned improvements for the Warehouse Management module include:

1. Advanced inventory forecasting
2. RFID integration for automatic tracking
3. Enhanced barcode scanning capabilities
4. Mobile printing for labels and documents
5. Real-time inventory visualization
6. Batch and serial number tracking improvements
7. Integration with supplier portal for returns 