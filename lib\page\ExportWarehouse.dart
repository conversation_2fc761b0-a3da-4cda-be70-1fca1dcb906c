import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountAndroid.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountiOS.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/RowDetail.dart';
import '../element/TableImExportWH.dart';
import '../element/TableInfo.dart';
import '../element/timeOut.dart';
import '../model/GetBarcodeReceive.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getListSOWBSByBatchTranferWarehouse.dart';
import '../model/getReservation.dart';
import '../model/getStorageBin.dart';
import '../model/listWarehouseTranfer.dart';
import '../model/postGetListSOWBSByBatch.dart';
import '../model/slocAddresse.dart';
import '../model/sumAmountExport.dart';
import '../model/warehouseTranfer.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/importWareHouseFunction.dart';
import 'LostConnect.dart';

class ExportWarehouse extends StatefulWidget {
  final DataListWarehouseTranfer dataListWarehouseTranfer;
  final String token;
  final String plant;
  final List<DataSlocAddress> lsDataSlocAddress;
  final String dateTimeOld;
  const ExportWarehouse(
      {Key? key,
      required this.dataListWarehouseTranfer,
      required this.token,
      required this.plant,
      required this.lsDataSlocAddress,
      required this.dateTimeOld})
      : super(key: key);
  @override
  _ExportWarehouseState createState() => _ExportWarehouseState();
}

class _ExportWarehouseState extends State<ExportWarehouse> {
  final _storageBinController = TextEditingController();
  final _storageBinControllerImport = TextEditingController();
  final _focusStorageBinController = FocusNode();
  final _focusStorageBinControllerImport = FocusNode();
  List<TextEditingController> _lsControllers = [];
  List<FocusNode> _lsFocusNode = [];
  List<DataGetListSOWBSByBatchTranferWareHouse> _lsDataGetListSOWBSByBatchTranferWareHouse = [];
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isLoadingRawMaterial = false;
  List<DataGetBarcodeReceive> _dataGetBarcodeReceive = [];

  DataSlocAddress? _selectedSloc;
  DataSlocAddress? _selectedSlocInput;

  DataReservation? _dataReservation;
  String? _defaultStorageBinIDExport;
  String? _defaultStorageBinIDImport;
  List<SumAmountExport> _sumAmountEntered = [];
  bool _errorSelectedSlocImport = false;
  bool _disableDropdown = false;
  bool _disableDropdownInput = false;
  // bool _errorStorageBinExistenceExport = false;
  // bool _errorStorageBinExistenceImport = false;
  bool _errorQuantity = false;
  bool? _disableButton;
  String _message = "";
  String? _materialID;
  List<DataSlocAddress?> _getLsDataSlocAddress = [];
  late bool _timeOut;
  List<DataGetStorageBin> _lsDataGetStorageBn = [];
  List<DataGetStorageBin> _lsDataGetStorageBnImport = [];
  bool _errorStorageBin = false;
  bool _errorStorageBinImport = false;
  Timer? _timer;
  Timer? _timerImport;
  bool _isLoadingStorageBin = false;
  bool _isLoadingStorageBinImport = false;

  @override
  void initState() {
    super.initState();
    _init();
  }

  void _setFilter(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _storageBinController.text = dataGetStorageBin.value ?? " ";
      _defaultStorageBinIDExport = dataGetStorageBin.key ?? " ";
      _lsDataGetStorageBn = [];
    });
  }

  void _setFilterImport(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _storageBinControllerImport.text = dataGetStorageBin.value ?? " ";
      _defaultStorageBinIDImport = dataGetStorageBin.key ?? " ";
      _lsDataGetStorageBnImport = [];
    });
  }

  Future<void> _onChangeNVL(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBin != true) {
          _errorStorageBin = true;
        }
      } else {
        if (_errorStorageBin != false) {
          _errorStorageBin = false;
        }
      }
    });
    if (!mounted) return;
    _timer?.cancel();
    if (_errorStorageBin == false) {
      _getAutocompleteStorageBin(value);
    }
  }

  Future<void> _onChangeNVLImport(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBinImport != true) {
          _errorStorageBinImport = true;
        }
      } else {
        if (_errorStorageBinImport != false) {
          _errorStorageBinImport = false;
        }
      }
    });
    if (!mounted) return;
    _timerImport?.cancel();
    if (_errorStorageBinImport == false) {
      _getAutocompleteStorageBinImport(value);
    }
  }

  Future<void> _getAutocompleteStorageBin(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = false;
              if (data != null) {
                _lsDataGetStorageBn = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _getAutocompleteStorageBinImport(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timerImport = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBinImport = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBinImport = false;
              if (data != null) {
                _lsDataGetStorageBnImport = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _getDataRawMaterial(String materialID, BuildContext context) async {
    try {
      setState(() {
        _isLoadingRawMaterial = true;
      });
      final dataBarcodeReceive = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);
      if (!mounted) return;
      if (dataBarcodeReceive != null) {
        if (dataBarcodeReceive.productCode != _dataReservation!.materialCode) {
          setState(() {
            _errorQuantity = false;
            _isLoadingRawMaterial = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Mã NVL không đúng vui lòng quét lại!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1)));
        } else {
          if (_dataGetBarcodeReceive.firstWhereOrNull((element) => element.batchNumber == dataBarcodeReceive.batchNumber) != null) {
            setState(() {
              _isLoadingRawMaterial = false;
            });
          } else {
            PostGetListSOWBSByBatch postGetListSOWBSByBatch = PostGetListSOWBSByBatch(
                productCode: dataBarcodeReceive.productCode,
                plant: widget.plant,
                sloc: _selectedSloc!.sloc,
                batchNumber: dataBarcodeReceive.batchNumber);
            final getListSOWBS = await ExportWareHouseFunction.fetchListSOWBSBYBatchTranfer(postGetListSOWBSByBatch, widget.token);
            if (!mounted) return;
            if (getListSOWBS != null) {
              for (int i = 0; i < getListSOWBS.length; i++) {
                if (getListSOWBS.isNotEmpty) {
                  getListSOWBS[i].batchNumber = dataBarcodeReceive.batchNumber;
                }
              }
              setState(() {
                _errorQuantity = false;
                // _lsControllers = [];
                // _lsFocusNode = [];
                _materialID = materialID;
                _isLoadingRawMaterial = false;
                // if(_dataGetBarcodeReceive.firstWhereOrNull((element) => element.batchNumber == dataBarcodeReceive.batchNumber) == null){
                _dataGetBarcodeReceive.add(dataBarcodeReceive);
                _sumAmountEntered.add(SumAmountExport(code: dataBarcodeReceive.batchNumber.toString(), sum: 0.0));
                // }

                _lsDataGetListSOWBSByBatchTranferWareHouse.addAll(getListSOWBS);
                _lsDataGetListSOWBSByBatchTranferWareHouse.removeWhere((element) =>
                    (element.so == null || element.so == "") &&
                    (element.soLine == null || element.soLine == "") &&
                    (element.wbs == null || element.wbs == "") &&
                    (element.quantity == null || element.quantity == 0.0));
                if (_lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty) {
                  for (int i = 0; i < _lsDataGetListSOWBSByBatchTranferWareHouse.length; i++) {
                    _lsControllers.add(TextEditingController());
                    _lsFocusNode.add(FocusNode());
                  }
                }
              });
            } else {
              setState(() {
                // _lsDataGetListSOWBSByBatchTranferWareHouse = [];
                // _materialID = null;
                // _selectedSloc = null;
                // if(_dataGetBarcodeReceive.firstWhereOrNull((element) => element.batchNumber == dataBarcodeReceive.batchNumber) == null){
                _dataGetBarcodeReceive.add(dataBarcodeReceive);
                _sumAmountEntered.add(SumAmountExport(code: dataBarcodeReceive.batchNumber.toString(), sum: 0.0));
                // }
                _isLoadingRawMaterial = false;
                // _lsControllers = [];
                // _lsFocusNode = [];
              });
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  backgroundColor: Colors.black,
                  content: Text(
                    'Không tìm thấy thông tin danh sách SO/WBS',
                    style: TextStyle(fontSize: 15.sp, color: Colors.white),
                  ),
                  duration: const Duration(seconds: 1)));
            }
          }
        }
      } else {
        setState(() {
          // _lsDataGetListSOWBSByBatchTranferWareHouse = [];
          // _materialID = null;
          // _selectedSloc = null;
          // _dataGetBarcodeReceive = null;
          _isLoadingRawMaterial = false;
          // _lsControllers = [];
          // _lsFocusNode =  [];
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin NVL chuyển kho!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 1)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingRawMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoadingRawMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi vui lòng thử lại sau!',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  // Future<void> _checkQRCode(String materialID,BuildContext context) async{
  //   await _getDataRawMaterial(materialID,context);
  //   if(_dataGetBarcodeReceive.isNotEmpty){
  //     if(_dataGetBarcodeReceive.firstWhereOrNull((element) => element.productCode == _dataReservation!.materialCode) == null){
  //       if (!mounted) return;
  //       setState(() {
  //         // _dataGetBarcodeReceive = null;
  //         // _materialID = null;
  //         // _lsDataGetListSOWBSByBatchTranferWareHouse = [];
  //         // _lsControllers = [];
  //         // _lsFocusNode =  [];
  //         // _sumAmountEntered = 0.0;
  //       });
  //       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //           backgroundColor: Colors.black,
  //           content: Text(
  //             'Mã NVL không đúng vui lòng quét lại!',
  //             style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //           ),
  //           duration: const Duration(seconds: 1)));
  //     }
  //   }
  // }

  void _setSelectedSlocOnInit() {
    // CLEAR
    // _selectedSlocInput = null;
    // _storageBinControllerImport.text = "";
    // _defaultStorageBinIDImport = null;

    // SELECT
    // _selectedSlocInput = selectedSlocItem;
    // _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
    // _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
    // if (_selectedSlocInput == null) {
    //   _errorSelectedSlocImport = true;
    // } else {
    //   _errorSelectedSlocImport = false;
    // }

    // Kho xuất
    if (_dataReservation!.storageLocation!.isNotEmpty) {
      var khoXuatData = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == _dataReservation!.storageLocation);

      if (khoXuatData != null) {
        _selectedSloc = khoXuatData;
        _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
        _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
        _lsDataGetStorageBn = [];
        _errorStorageBin = false;
      }
    }

    // Kho nhập
    if (_dataReservation!.riStorageLocation!.isNotEmpty) {
      var khoNhapData = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == _dataReservation!.riStorageLocation);

      if (khoNhapData != null) {
        _selectedSlocInput = khoNhapData;
        _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
        _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
        _lsDataGetStorageBnImport = [];
        _errorStorageBinImport = false;
      }
    }
  }

  Future<void> _init() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _isNotWifi = false;
        });
        final data = await ExportWareHouseFunction.fetchReservation(widget.dataListWarehouseTranfer.reservationId.toString(), widget.token);
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          if (data != null) {
            _dataReservation = data;
          }
          _getLsDataSlocAddress = widget.lsDataSlocAddress.map((e) => DataSlocAddress.clone(e)).toList();
        });
        // print("_dataReservation");
        // print(json.encode(_dataReservation));
        // print("_getLsDataSlocAddress");
        // print(json.encode(_getLsDataSlocAddress));
        _setSelectedSlocOnInit();
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _timeOut = false;
      });
    }
  }

  void _setAddressQRCode(DataSlocAddress? data, BuildContext context, String isFrom) {
    try {
      if (isFrom == 'export') {
        if (_dataReservation!.storageLocation != null && _dataReservation!.storageLocation != "") {
          if (_dataReservation!.storageLocation != data!.sloc) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho xuất không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBin =
                  data.defaultStorageBin;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                  data.defaultStorageBinId;
              _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
              if (_selectedSloc != null) {
                _disableDropdown = true;
                _materialID = null;
                _dataGetBarcodeReceive = [];
                _lsDataGetListSOWBSByBatchTranferWareHouse = [];
                _lsControllers = [];
                _lsFocusNode = [];
                _sumAmountEntered = [];
              }
              _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
              _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
              _lsDataGetStorageBn = [];
              _errorStorageBin = false;
            });
          }
        } else {
          if (!mounted) return;
          setState(() {
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data!.sloc)]!.defaultStorageBin =
                data!.defaultStorageBin;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                data.defaultStorageBinId;
            _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
            if (_selectedSloc != null) {
              _disableDropdown = true;
              _materialID = null;
              _dataGetBarcodeReceive = [];
              _lsDataGetListSOWBSByBatchTranferWareHouse = [];
              _lsControllers = [];
              _lsFocusNode = [];
              _sumAmountEntered = [];
            }
            _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
            _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
            _lsDataGetStorageBn = [];
            _errorStorageBin = false;
          });
        }
      } else {
        if (_dataReservation!.riStorageLocation != null && _dataReservation!.riStorageLocation != "") {
          if (_dataReservation!.riStorageLocation != data!.sloc) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho nhập không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBin =
                  data.defaultStorageBin;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
              _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                  data.defaultStorageBinId;
              _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
              if (_selectedSlocInput != null) {
                _errorSelectedSlocImport = false;
              }
              if (_selectedSlocInput != null) {
                _disableDropdownInput = true;
              }
              _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
              _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
              _lsDataGetStorageBnImport = [];
              _errorStorageBinImport = false;
            });
          }
        } else {
          if (!mounted) return;
          setState(() {
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data!.sloc)]!.defaultStorageBin =
                data!.defaultStorageBin;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.warehouseNo = data.warehouseNo;
            _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
                data.defaultStorageBinId;
            _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
            if (_selectedSlocInput != null) {
              _errorSelectedSlocImport = false;
            }
            if (_selectedSlocInput != null) {
              _disableDropdownInput = true;
            }
            _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
            _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
            _lsDataGetStorageBnImport = [];
            _errorStorageBinImport = false;
          });
        }
      }
    } catch (error) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
      debugPrint("error: $error");
    }
  }

  void _setSloc(DataSlocAddress? value, BuildContext context) {
    try {
      if (_dataReservation!.storageLocation != null && _dataReservation!.storageLocation != "") {
        if (value!.slocDisplay == "--Bỏ chọn--") {
          _selectedSloc = null;
          _storageBinController.text = "";
          _defaultStorageBinIDExport = null;
          _materialID = null;
          _dataGetBarcodeReceive = [];
          _lsDataGetListSOWBSByBatchTranferWareHouse = [];
          _lsControllers = [];
          _lsFocusNode = [];
          _sumAmountEntered = [];
        } else {
          if (_dataReservation!.storageLocation != value.sloc) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.black,
                content: Text(
                  'Sloc kho xuất không đúng, vui lòng chọn lại!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          } else {
            if (!mounted) return;
            setState(() {
              _selectedSloc = value;
              _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
              _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
              _materialID = null;
              _dataGetBarcodeReceive = [];
              _lsDataGetListSOWBSByBatchTranferWareHouse = [];
              _lsControllers = [];
              _lsFocusNode = [];
              _sumAmountEntered = [];
            });
          }
        }
      } else {
        if (!mounted) return;
        setState(() {
          if (value!.slocDisplay == "--Bỏ chọn--") {
            _selectedSloc = null;
            _storageBinController.text = "";
            _defaultStorageBinIDExport = null;
            _materialID = null;
            _dataGetBarcodeReceive = [];
            _lsDataGetListSOWBSByBatchTranferWareHouse = [];
            _lsControllers = [];
            _lsFocusNode = [];
            _sumAmountEntered = [];
          } else {
            _selectedSloc = value;
            _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
            _defaultStorageBinIDExport = _selectedSloc!.defaultStorageBinId;
            _materialID = null;
            _dataGetBarcodeReceive = [];
            _lsDataGetListSOWBSByBatchTranferWareHouse = [];
            _lsControllers = [];
            _lsFocusNode = [];
            _sumAmountEntered = [];
          }
        });
      }
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _selectedSloc = null;
        _storageBinController.text = "";
        _defaultStorageBinIDExport = null;
        _materialID = null;
        _dataGetBarcodeReceive = [];
        _lsDataGetListSOWBSByBatchTranferWareHouse = [];
        _lsControllers = [];
        _lsFocusNode = [];
        _sumAmountEntered = [];
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setSlocInput(DataSlocAddress? selectedSlocItem, BuildContext context) {
    if (_dataReservation!.riStorageLocation != null && _dataReservation!.riStorageLocation != "") {
      if (selectedSlocItem!.slocDisplay == "--Bỏ chọn--") {
        _selectedSlocInput = null;
        _storageBinControllerImport.text = "";
        _defaultStorageBinIDImport = null;
      } else {
        if (_dataReservation!.riStorageLocation != selectedSlocItem.sloc) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Sloc kho nhập không đúng, vui lòng chọn lại!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1)));
        } else {
          setState(() {
            _selectedSlocInput = selectedSlocItem;
            _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
            _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
            if (_selectedSlocInput == null) {
              _errorSelectedSlocImport = true;
            } else {
              _errorSelectedSlocImport = false;
            }
          });
        }
      }
    } else {
      setState(() {
        if (selectedSlocItem!.slocDisplay == "--Bỏ chọn--") {
          _selectedSlocInput = null;
          _storageBinControllerImport.text = "";
          _defaultStorageBinIDImport = null;
        } else {
          _selectedSlocInput = selectedSlocItem;
          _storageBinControllerImport.text = _selectedSlocInput!.defaultStorageBin ?? "";
          _defaultStorageBinIDImport = _selectedSlocInput!.defaultStorageBinId;
          if (_selectedSlocInput == null) {
            _errorSelectedSlocImport = true;
          } else {
            _errorSelectedSlocImport = false;
          }
        }
      });
    }
  }

  void _checkError() {
    setState(() {
      if (_selectedSlocInput == null) {
        _errorSelectedSlocImport = true;
      } else {
        _errorSelectedSlocImport = false;
      }
      // if (_storageBinController.text.isEmpty) {
      //   if( _errorStorageBinExistenceExport != false){
      //     _errorStorageBinExistenceExport = false;
      //   }
      // }else{
      //   if(_getLsDataSlocAddress.firstWhereOrNull((element) => element != null && element.defaultStorageBin == _storageBinController.text) != null){
      //     _errorStorageBinExistenceExport = false;
      //   }else{
      //     _errorStorageBinExistenceExport = true;
      //   }
      // }
      if (_lsControllers.where((element) => element.text.isNotEmpty).isNotEmpty) {
        if (_lsControllers.where((element) => element.text.isNotEmpty && double.parse(element.text) <= 0.0).isNotEmpty) {
          _errorQuantity = true;
          _message = "Số lượng chuyển không được nhỏ hơn 0.0";
        } else {
          _errorQuantity = false;
          _message = "";
        }
      } else {
        _errorQuantity = true;
        _message = "Vui lòng nhập số lượng chuyển";
      }

      // if (_storageBinControllerImport.text.isEmpty) {
      //   if( _errorStorageBinExistenceImport != false){
      //     _errorStorageBinExistenceImport = false;
      //   }
      // }else{
      //   if(_getLsDataSlocAddress.firstWhereOrNull((element) => element != null && element.defaultStorageBin == _storageBinControllerImport.text) != null){
      //     _errorStorageBinExistenceImport = false;
      //   }else{
      //     _errorStorageBinExistenceImport = true;
      //   }
      // }
      // debugPrint(ImportWareHouseFunction.checkIsFormatDouble(_lsControllers).toString());
      // if(ImportWareHouseFunction.checkIsFormatDouble(_lsControllers) == false){
      //   _errorFormatView = true;
      // }else{
      //   _errorFormatView = false;
      // }
    });
  }

  Future<void> _sendPost(BuildContext context) async {
    List<WarehouseTranferDetails>? lsWareHouseTranferDetails =
        ExportWareHouseFunction.getListWarehouseTranferDetails(_lsControllers, _lsDataGetListSOWBSByBatchTranferWareHouse);
    WarehouseTranfer warehouseTranfer = WarehouseTranfer(
      reservationId: widget.dataListWarehouseTranfer.reservationId,
      slocExportId: _selectedSloc!.slocId,
      storageBinExportId: _storageBinController.text.isEmpty ? null : _defaultStorageBinIDExport,
      slocImportId: _selectedSlocInput!.slocId,
      storageBinImportId: _storageBinControllerImport.text.isEmpty ? null : _defaultStorageBinIDImport,
      rawMaterialCardId: _materialID,
      // batchNumber: _dataGetBarcodeReceive!.batchNumber,
      warehouseTranferDetails: lsWareHouseTranferDetails,
    );
    // debugPrint(jsonEncode(warehouseTranfer));
    await ExportWareHouseFunction.sendWarehouseFuncion(warehouseTranfer, widget.token, context, widget.plant);
  }

  Future<void> _checkQuantityExceed(BuildContext context) async {
    if (_isLoadingRawMaterial == true) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 2)));
    } else {
      _checkError();
      if (_errorSelectedSlocImport == false && _errorQuantity == false
          // &&
          // _errorFormatView == false
          ) {
        FocusScope.of(context).unfocus();
        double numberSum = _sumAmountEntered.map((e) => e.sum).toList().reduce((a, b) => a + b);
        if (numberSum != _dataReservation!.reqQuantityRnd!) {
          if (Platform.isAndroid) {
            bool? check = await showDialog<bool>(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => const DialogExceedTheAmountAndroid(
                      message: 'Số lượng chuyển khác số lượng yêu cầu, bạn có muốn tiếp tục?',
                    ));
            if (!mounted) return;
            if (check == null) return;
            if (check == true) {
              _sendPost(context);
            }
          } else {
            bool? check = await showCupertinoDialog<bool>(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) => const DialogExceedTheAmountIos(
                      title: 'SL chuyển khác SL yêu cầu',
                      message: 'Số lượng chuyển khác số lượng yêu cầu, bạn có muốn tiếp tục?',
                    ));
            if (!mounted) return;
            if (check == null) return;
            if (check == true) {
              _sendPost(context);
            }
          }
        } else {
          _sendPost(context);
        }
      }
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  @override
  void dispose() {
    for (var i in _lsControllers) {
      i.dispose();
    }
    _storageBinController.dispose();
    _storageBinControllerImport.dispose();
    _focusStorageBinController.dispose();
    _focusStorageBinControllerImport.dispose();
    for (var i in _lsFocusNode) {
      i.dispose();
    }
    if (_timer != null) {
      _timer!.cancel();
    }
    if (_timerImport != null) {
      _timerImport!.cancel();
    }
    // _keyboardSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, false);
          return false;
        },
        child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: _timeOut == true
                ? WillPopScope(
                    onWillPop: () => Future.value(false),
                    child: Scaffold(
                        backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
                : Scaffold(
                    backgroundColor: Colors.grey.shade200,
                    appBar: AppBar(
                      titleSpacing: 0,
                      automaticallyImplyLeading: false,
                      backgroundColor: const Color(0xff0052cc),
                      elevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                        onPressed: () {
                          Navigator.pop(context, false);
                        },
                      ),
                      title: Text(
                        "Chuyển Kho NVL",
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                      ),
                    ),
                    body: _isNotWifi
                        ? LostConnect(checkConnect: () => _init())
                        : _isLoading == true
                            ? const Center(child: CircularProgressIndicator())
                            : SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: <Widget>[
                                    const _TitleExportWareHouse(),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          const _HeaderExportWareHouse(title: "I. LỆNH CẤP VẬT TƯ"),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          TableInfoExport(
                                            textCL1: "Số reservation:",
                                            textCL2: _dataReservation!.reservationCode ?? "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          // TableInfoNoTopExport(
                                          //   textCL1:"Item:",
                                          //   textCL2:_dataReservation!.item ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          TableInfoNoTopExport(
                                            textCL1: "Mã NVL:",
                                            textCL2: _dataReservation != null ? _dataReservation!.materialCode ?? " " : " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),

                                          // Nhà máy xuất, nhà máy nhập
                                          _PlantExportImport(dataReservation: _dataReservation),
                                          // Kho xuất, kho nhập
                                          _WareHouseExportImport(dataReservation: _dataReservation),

                                          // TableInfoNoTop(
                                          //   textCL1:"Nhà máy xuất:",
                                          //   textCL2: _dataReservation!.plant ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Kho xuất:",
                                          //   textCL2: _dataReservation!.storageLocation ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Số lô:",
                                          //   textCL2:  _dataReservation!.batch ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          TableInfoNoTopExport(
                                            textCL1: "Số lượng yêu cầu:",
                                            textCL2: _dataReservation!.reqQuantityRnd == null ? "" : (_dataReservation!.reqQuantityRnd.toString()),
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTopExport(
                                            textCL1: "ĐVT:",
                                            textCL2: _dataReservation!.unit ?? "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Nhà máy nhập:",
                                          //   textCL2:  _dataReservation!.riPlant ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Kho nhập:",
                                          //   textCL2:  _dataReservation!.riStorageLocation ?? " ",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"SO/ SO Line:",
                                          //   textCL2:  _dataReservation!.sosoLine ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"WBS:",
                                          //   textCL2:  _dataReservation!.wbs ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                          // TableInfoNoTop(
                                          //   textCL1:"LSX:",
                                          //   textCL2:  _dataReservation!.lsx ?? "",
                                          //   colorCL1: 0xff303F9F,
                                          //   colorCL2: 0xffffffff,
                                          // ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          const _HeaderExportWareHouse(title: "THÔNG TIN GIAO DỊCH CHUYỂN KHO"),
                                          // Text(
                                          //   "II. THÔNG TIN GIAO DỊCH CHUYỂN KHO",
                                          //   style: TextStyle(
                                          //     fontSize: 14.sp,
                                          //     fontWeight: FontWeight.bold,
                                          //   ),
                                          // ),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Flexible(
                                                flex: 5,
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton.icon(
                                                    style: ButtonStyle(
                                                      // padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 0.0, horizontal: 12.0)),
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                      side: MaterialStateProperty.all(
                                                        const BorderSide(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                    ),
                                                    onPressed: () async {
                                                      FocusScope.of(context).unfocus();
                                                      final data = await Navigator.pushNamed(context, '/QRCodePageGetSlocExport');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      _setAddressQRCode(data as DataSlocAddress, context, 'export');
                                                    },
                                                    icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                    label: Text(
                                                      "Quét kho xuất",
                                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Flexible(
                                                flex: 5,
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton.icon(
                                                    style: ButtonStyle(
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                      side: MaterialStateProperty.all(
                                                        const BorderSide(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                    ),
                                                    onPressed: () async {
                                                      FocusScope.of(context).unfocus();
                                                      final data = await Navigator.pushNamed(context, '/QRCodePageGetSlocExport');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      _setAddressQRCode(data as DataSlocAddress, context, 'import');
                                                    },
                                                    icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                    label: Text(
                                                      "Quét kho nhập",
                                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                          const LabeledDetailRow(title: "Loại giao dịch:", text: "Chuyển kho 1 bước"),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          ContainerError.widgetError(_errorSelectedSlocImport, 'Kho nhập chưa được nhập'),
                                          SizedBox(height: _errorSelectedSlocImport == true ? 10.h : 0),
                                          const HeaderTableImExportWH(
                                              textCL1: "Kho xuất", textCL2: "Kho nhập", colorCL2: 0xff303F9F, colorCL1: 0xff303F9F),
                                          TableImExportWHDD(
                                            textCL1: "Sloc",
                                            selectedSloc: _selectedSloc,
                                            onChange: (value) => _setSloc(value, context),
                                            disableDropdown: _disableDropdown,
                                            disableDropdownInput: _disableDropdownInput,
                                            lsDataSlocAddress: _getLsDataSlocAddress,
                                            textCL3: "Sloc",
                                            selectedSlocInput: _selectedSlocInput,
                                            onChangeInput: (value) => _setSlocInput(value, context),
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          TableImExportWHText(
                                            textCL1: "Warehouse No",
                                            textCL2: _selectedSloc != null ? _selectedSloc!.warehouseNo ?? "" : "",
                                            textCL3: "Warehouse No",
                                            textCL4: _selectedSlocInput != null ? _selectedSlocInput!.warehouseNo ?? "" : "",
                                            colorCL1: 0xffEEEEEE,
                                            colorCL2: 0xffFFFFFF,
                                            colorCL3: 0xffEEEEEE,
                                            colorCL4: 0xffFFFFFF,
                                          ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 3,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xffEEEEEE),
                                                        border: Border(
                                                          left: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child: Text(
                                                      "Storage Bin",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 2,
                                                    child: Container(
                                                      height: double.infinity,
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                      decoration: BoxDecoration(
                                                          color: const Color(0xFFFFFFFF),
                                                          border: Border(
                                                            right: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                            bottom: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                          )),
                                                      child:
                                                          //     TypeAheadField(
                                                          // suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                                          //   offsetX: -45.w,
                                                          //   constraints: BoxConstraints(
                                                          //     minWidth: 150.w,
                                                          //   ),
                                                          // ),
                                                          //   textFieldConfiguration: TextFieldConfiguration(
                                                          //       decoration:  InputDecoration(
                                                          //         labelStyle: TextStyle(fontSize: 11.sp),
                                                          //         contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                          //         isDense: true,
                                                          //         border: OutlineInputBorder(
                                                          //           borderRadius: BorderRadius.circular(3.r),
                                                          //           borderSide: BorderSide(
                                                          //               width: 0.5,
                                                          //               color: Colors.grey.shade400),
                                                          //         ),
                                                          //         focusedBorder: OutlineInputBorder(
                                                          //           borderRadius: BorderRadius.circular(3.r),
                                                          //           borderSide: BorderSide(
                                                          //               width: 0.5,
                                                          //               color: Colors.grey.shade400),
                                                          //         ),
                                                          //         enabledBorder: OutlineInputBorder(
                                                          //           borderRadius:
                                                          //           BorderRadius.circular(3.r),
                                                          //           borderSide: BorderSide(
                                                          //               width: 0.5,
                                                          //               color: Colors.grey.shade400),
                                                          //         ),
                                                          //       ),
                                                          //       enabled: _selectedSloc == null ? false : _selectedSloc!.warehouseNo != null ,
                                                          //       controller: _storageBinController,
                                                          //       focusNode: _focusStorageBinController,
                                                          //       style: TextStyle(fontSize: 12.sp)
                                                          //   ),
                                                          //   suggestionsCallback: (pattern) {
                                                          //     return ExportWareHouseFunction.filterDataSlocAddress(_getLsDataSlocAddress, pattern);
                                                          //   },
                                                          //   itemBuilder: (context, suggestion) {
                                                          //     return ListTile(
                                                          //       title: Text((suggestion as DataSlocAddress).defaultStorageBin?? " ",style: TextStyle(fontSize: 12.sp)),
                                                          //     );
                                                          //   },
                                                          //   onSuggestionSelected: (suggestion) {
                                                          //     _storageBinController.text = (suggestion as DataSlocAddress).defaultStorageBin ?? " ";
                                                          //     _defaultStorageBinIDExport = (suggestion).defaultStorageBinId ?? " ";
                                                          //   },
                                                          //       noItemsFoundBuilder: (value) {
                                                          //         return Padding(
                                                          //             padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                          //             child: Text(
                                                          //                 "Không tìm thấy kết quả",
                                                          //                 style: TextStyle(fontSize: 11.sp)));
                                                          //       },
                                                          // ),
                                                          Container(
                                                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                        decoration: BoxDecoration(
                                                            border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                            borderRadius: BorderRadius.circular(3.r)),
                                                        child: TextFormField(
                                                          maxLines: null,
                                                          textAlign: TextAlign.center,
                                                          controller: _storageBinController,
                                                          focusNode: _focusStorageBinController,
                                                          style: TextStyle(fontSize: 12.sp),
                                                          decoration: InputDecoration(
                                                            focusedBorder: InputBorder.none,
                                                            enabledBorder: InputBorder.none,
                                                            border: InputBorder.none,
                                                            contentPadding: EdgeInsets.zero,
                                                            errorBorder: InputBorder.none,
                                                            disabledBorder: InputBorder.none,
                                                            filled: true,
                                                            isDense: true,
                                                            fillColor: Colors.white,
                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                          ),
                                                          onChanged: _onChangeNVL,
                                                        ),
                                                      ),
                                                    )),
                                                Expanded(
                                                  flex: 3,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xffEEEEEE),
                                                        border: Border(
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child: Text(
                                                      "Storage Bin",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                        color: const Color(0xFFFFFFFF),
                                                        border: Border(
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        )),
                                                    child:
                                                        //     TypeAheadField(
                                                        //   suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                                        //     offsetX: -85.w,
                                                        //     constraints: BoxConstraints(
                                                        //       minWidth: 150.w,
                                                        //     ),
                                                        //   ),
                                                        //   textFieldConfiguration: TextFieldConfiguration(
                                                        //       decoration:  InputDecoration(
                                                        //         labelStyle: TextStyle(fontSize: 11.sp),
                                                        //         contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                        //         isDense: true,
                                                        //         border: OutlineInputBorder(
                                                        //           borderRadius: BorderRadius.circular(3.r),
                                                        //           borderSide: BorderSide(
                                                        //               width: 0.5,
                                                        //               color: Colors.grey.shade400),
                                                        //         ),
                                                        //         focusedBorder: OutlineInputBorder(
                                                        //           borderRadius: BorderRadius.circular(3.r),
                                                        //           borderSide: BorderSide(
                                                        //               width: 0.5,
                                                        //               color: Colors.grey.shade400),
                                                        //         ),
                                                        //         enabledBorder: OutlineInputBorder(
                                                        //           borderRadius:
                                                        //           BorderRadius.circular(3.r),
                                                        //           borderSide: BorderSide(
                                                        //               width: 0.5,
                                                        //               color: Colors.grey.shade400),
                                                        //         ),
                                                        //       ),
                                                        //       enabled: _selectedSlocInput == null ? false : _selectedSlocInput!.warehouseNo != null,
                                                        //       controller: _storageBinControllerImport,
                                                        //       focusNode: _focusStorageBinControllerImport,
                                                        //       style: TextStyle(fontSize: 12.sp)
                                                        //   ),
                                                        //   suggestionsCallback: (pattern) {
                                                        //     return ExportWareHouseFunction.filterDataSlocAddress(_getLsDataSlocAddress, pattern);
                                                        //   },
                                                        //   itemBuilder: (context, suggestion) {
                                                        //     return ListTile(
                                                        //       title: Text((suggestion as DataSlocAddress).defaultStorageBin?? " ",style: TextStyle(fontSize: 12.sp)),
                                                        //     );
                                                        //   },
                                                        //   onSuggestionSelected: (suggestion) {
                                                        //     _storageBinControllerImport.text = (suggestion as DataSlocAddress).defaultStorageBin ?? " ";
                                                        //     _defaultStorageBinIDImport = (suggestion).defaultStorageBinId ?? " ";
                                                        //     },
                                                        //       noItemsFoundBuilder: (value) {
                                                        //         return Padding(
                                                        //             padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                        //             child: Text(
                                                        //                 "Không tìm thấy kết quả",
                                                        //                 style: TextStyle(fontSize: 11.sp)));
                                                        //       },
                                                        // ),
                                                        Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        textAlign: TextAlign.center,
                                                        controller: _storageBinControllerImport,
                                                        focusNode: _focusStorageBinControllerImport,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          focusedBorder: InputBorder.none,
                                                          enabledBorder: InputBorder.none,
                                                          border: InputBorder.none,
                                                          contentPadding: EdgeInsets.zero,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          filled: true,
                                                          isDense: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                        ),
                                                        onChanged: _onChangeNVLImport,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: 5.h),
                                          Row(
                                            children: [
                                              Expanded(
                                                flex: 5,
                                                child: _ListSuggestions(
                                                    lsDataGetStorageBn: _lsDataGetStorageBn,
                                                    setFilter: (DataGetStorageBin value) {
                                                      _setFilter(value);
                                                    },
                                                    isLoadingStorageBin: _isLoadingStorageBin),
                                              ),
                                              Expanded(
                                                flex: 5,
                                                child: _ListSuggestions(
                                                    lsDataGetStorageBn: _lsDataGetStorageBnImport,
                                                    setFilter: (DataGetStorageBin value) {
                                                      _setFilterImport(value);
                                                    },
                                                    isLoadingStorageBin: _isLoadingStorageBinImport),
                                              ),
                                            ],
                                          ),
                                          // SizedBox(height:  _errorStorageBinExistenceExport == true || _errorStorageBinExistenceImport == true ? 5.h : 0),
                                          // Row(
                                          //   children:<Widget>[
                                          //     Expanded(
                                          //       flex: 5,
                                          //         child:ContainerError.widgetError(_errorStorageBinExistenceExport, "Vui lòng chọn Storage Bin khác"),
                                          //     ),
                                          //     Expanded(
                                          //       flex: 5,
                                          //       child:ContainerError.widgetError(_errorStorageBinExistenceImport,  "Vui lòng chọn Storage Bin khác"),
                                          //     )
                                          //   ]
                                          // )
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "III. THÔNG TIN NVL CHUYỂN KHO",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Container(
                                            decoration: const BoxDecoration(),
                                            child: ElevatedButton.icon(
                                              style: ButtonStyle(
                                                shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                side: MaterialStateProperty.all(
                                                  BorderSide(
                                                    color: _selectedSloc != null ? const Color(0xff303F9F) : const Color(0xffe0e0e0),
                                                  ),
                                                ),
                                                backgroundColor: MaterialStateProperty.all(
                                                    _selectedSloc != null ? const Color(0xff303F9F) : const Color(0xffe0e0e0)),
                                              ),
                                              onPressed: _selectedSloc == null || _isLoadingRawMaterial == true
                                                  ? null
                                                  : () async {
                                                      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                        Platform.isAndroid
                                                            ? showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                            : showCupertinoDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                      } else {
                                                        FocusScope.of(context).unfocus();
                                                        final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                        if (!mounted) return;
                                                        if (data == null) return;
                                                        if ((data as GetBackDataQRCodePage).isScan == true) {
                                                          if (!mounted) return;
                                                          await _getDataRawMaterial(data.materialID.toString(), context);
                                                        }
                                                      }
                                                    },
                                              icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                              label: Text(
                                                _isLoadingRawMaterial == true ? "..." : "Quét mã",
                                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 5.h,
                                          ),
                                          // RowDetail(
                                          //       title: "Mã NVL:",
                                          //       trailing: _isLoadingRawMaterial == true ? "...Loading" :_dataRawMaterial == null ? " " :_dataRawMaterial!.productCode ?? " "),
                                          //   SizedBox(
                                          //     height: 10.h,
                                          //   ),
                                          //  RowDetail(
                                          //       title: "Tên NVL:",
                                          //       trailing: _isLoadingRawMaterial == true ? "...Loading" :_dataRawMaterial == null ? " " : _dataRawMaterial!.productName ?? " "),
                                          //   SizedBox(
                                          //     height: 10.h,
                                          //   ),
                                          //   RowDetail(
                                          //       title: "Số lô:",
                                          //       trailing: _isLoadingRawMaterial == true ? "...Loading":_getBatch != null  ? _getBatch!.batchNumber ?? " ": " "),
                                          //   SizedBox(
                                          //     height: 10.h,
                                          //   ),
                                          TableInfo(
                                            textCL1: "Mã NVL:",
                                            textCL2: _dataGetBarcodeReceive.isEmpty ? " " : _dataGetBarcodeReceive[0].productCode ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Tên NVL:",
                                            textCL2: _dataGetBarcodeReceive.isEmpty ? " " : _dataGetBarcodeReceive[0].productName ?? " ",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xffffffff,
                                          ),
                                          SizedBox(height: 10.h),
                                          Column(
                                              children: List.generate(
                                                  _dataGetBarcodeReceive.length,
                                                  (index) => Container(
                                                        margin: REdgeInsets.symmetric(vertical: 5),
                                                        child: Column(
                                                          children: <Widget>[
                                                            TableInfo(
                                                              textCL1: "Số lô:",
                                                              textCL2: _dataGetBarcodeReceive.isNotEmpty
                                                                  ? _dataGetBarcodeReceive[index].batchNumber ?? " "
                                                                  : " ",
                                                              colorCL1: 0xff303F9F,
                                                              colorCL2: 0xffffffff,
                                                            ),
                                                            TableInfoNoTop(
                                                              textCL1: "Số lượng chuyển:",
                                                              textCL2:
                                                                  _sumAmountEntered.isEmpty ? "0.0" : _sumAmountEntered[index].sum.toStringAsFixed(2),
                                                              colorCL1: 0xff303F9F,
                                                              colorCL2: 0xffffffff,
                                                            ),
                                                          ],
                                                        ),
                                                      )))

                                          // Row(
                                          //   crossAxisAlignment: CrossAxisAlignment.start,
                                          //   mainAxisAlignment:
                                          //   MainAxisAlignment.spaceBetween,
                                          //   children: <Widget>[
                                          //     Expanded(
                                          //       flex: 3,
                                          //       child: Align(
                                          //         alignment: Alignment.centerLeft,
                                          //         child: Text(
                                          //           "SO/SO Line:",
                                          //           style: TextStyle(
                                          //             fontSize: 12.sp,
                                          //             fontWeight: FontWeight.bold,
                                          //           ),
                                          //         ),
                                          //       ),
                                          //     ),
                                          //     SizedBox(width: 10.w),
                                          //     Expanded(
                                          //         flex: 7,
                                          //         child: Align(
                                          //           alignment: Alignment.centerLeft,
                                          //           child:  _isLoadingRawMaterial == true ?Text(
                                          //             "...Loading",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           ):_inventorySAPApi == null ?Text(
                                          //             " ",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           ) :Text(
                                          //             (_inventorySAPApi!.so != null &&  _inventorySAPApi!.so != "") &&
                                          //                 (_inventorySAPApi!.soLine != null &&  _inventorySAPApi!.soLine != "") ?
                                          //             "${_inventorySAPApi!.so ?? ""}/${_inventorySAPApi!.soLine ?? ""}":"",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           )
                                          //         ))
                                          //   ],
                                          // ),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                          // Row(
                                          //   crossAxisAlignment: CrossAxisAlignment.start,
                                          //   mainAxisAlignment:
                                          //   MainAxisAlignment.spaceBetween,
                                          //   children: <Widget>[
                                          //     Expanded(
                                          //       flex: 3,
                                          //       child: Align(
                                          //         alignment: Alignment.centerLeft,
                                          //         child: Text(
                                          //           "WBS:",
                                          //           style: TextStyle(
                                          //             fontSize: 12.sp,
                                          //             fontWeight: FontWeight.bold,
                                          //           ),
                                          //         ),
                                          //       ),
                                          //     ),
                                          //     SizedBox(width: 10.w),
                                          //     Expanded(
                                          //         flex: 7,
                                          //         child: Align(
                                          //           alignment: Alignment.centerLeft,
                                          //           child: _isLoadingRawMaterial == true ?Text(
                                          //             "...Loading",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           ):_inventorySAPApi == null ? Text(
                                          //             " ",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           ): Text(
                                          //             _inventorySAPApi!.wbs != null &&  _inventorySAPApi!.wbs != "" ?
                                          //             _inventorySAPApi!.wbs ?? "":"",
                                          //             style: TextStyle(
                                          //               fontSize: 12.sp,
                                          //             ),
                                          //           )
                                          //         ))
                                          //   ],
                                          // ),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                          //  RowDetail(
                                          //     title: "Tồn kho SAP:",
                                          //     trailing: _isLoadingRawMaterial == true  ? "Loading":_inventorySAPApi != null ?_inventorySAPApi!.quantity != null ?_inventorySAPApi!.quantity!.round().toString(): " ": " "),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                          // Row(
                                          //   children: [
                                          //     Expanded(
                                          //       flex: 3,
                                          //       child: Text(
                                          //         "Số lượng thực chuyển:",
                                          //         style: TextStyle(
                                          //           fontSize: 12.sp,
                                          //           fontWeight: FontWeight.bold,
                                          //         ),
                                          //       ),
                                          //     ),
                                          //     SizedBox(width: 10.w),
                                          //     Expanded(
                                          //       flex: 7,
                                          //       child: SizedBox(
                                          //         height: 33.h,
                                          //         child: TextFormField(
                                          //           textAlign: TextAlign.center,
                                          //           keyboardType:
                                          //           TextInputType.number,
                                          //           inputFormatters: <
                                          //               TextInputFormatter>[
                                          //             FilteringTextInputFormatter
                                          //                 .allow(
                                          //                 RegExp("[0-9|]")),
                                          //           ],
                                          //           focusNode: _focusQuantity,
                                          //           controller: _controllerQuantity,
                                          //           style: TextStyle(fontSize: 12.sp),
                                          //           decoration: InputDecoration(
                                          //             border: InputBorder.none,
                                          //             focusedBorder: OutlineInputBorder(
                                          //               borderRadius: BorderRadius.circular(0),
                                          //               borderSide: BorderSide(
                                          //                   width: 0.5.w,
                                          //                   color:
                                          //                   // _error_3 ==
                                          //                   //     true
                                          //                   //     ? const Color(
                                          //                   //     0xFFD32F2F)
                                          //                   //     :
                                          //                   Colors.grey.shade400),
                                          //             ),
                                          //             enabledBorder: OutlineInputBorder(
                                          //               borderRadius: BorderRadius.circular(0),
                                          //               borderSide: BorderSide(
                                          //                   width: 0.5,
                                          //                   color:
                                          //                   // _error_3 ==
                                          //                   //     true
                                          //                   //     ? const Color(
                                          //                   //     0xFFD32F2F)
                                          //                   //     :
                                          //                   Colors.grey.shade400),
                                          //             ),
                                          //             errorBorder: InputBorder.none,
                                          //             disabledBorder: InputBorder.none,
                                          //             filled: true,
                                          //             fillColor: Colors.white,
                                          //             hintStyle: TextStyle(fontSize: 12.sp),
                                          //             contentPadding:
                                          //             EdgeInsets.symmetric(horizontal: 20.w),
                                          //           ),
                                          //           onChanged: (value){
                                          //             if(_controllerQuantity.text.isEmpty){
                                          //               if(_errorQuantity != true){
                                          //                 setState(() {
                                          //                   _errorQuantity = true;
                                          //                 });
                                          //               }
                                          //             }else{
                                          //               if(_errorQuantity != false){
                                          //                 setState(() {
                                          //                   _errorQuantity = false;
                                          //                 });
                                          //               }
                                          //             }
                                          //           },
                                          //         ),
                                          //       ),
                                          //     ),
                                          //   ],
                                          // ),
                                          // SizedBox(height:  _errorQuantity == true ? 10.h : 0),
                                          // ContainerError.widgetError(_errorQuantity, 'Bạn chưa nhập số lượng thực chuyển'),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                          // RowDetail(
                                          //     title: "ĐVT:",
                                          //     trailing: _isLoadingRawMaterial == true ? "...Loading" :_inventorySAPApi == null ? " " :_inventorySAPApi!.unit ?? " "),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 10.h),
                                          Text(
                                            "IV. SỐ LƯỢNG CHUYỂN KHO CHI TIẾT",
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 15.h,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.only(left: 3.w),
                                            child: SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Table(
                                                    border: TableBorder.all(width: 0.5.w),
                                                    columnWidths: <int, TableColumnWidth>{
                                                      0: FixedColumnWidth(100.w),
                                                      1: FixedColumnWidth(100.w),
                                                      2: FixedColumnWidth(100.w),
                                                      3: FixedColumnWidth(100.w),
                                                      4: FixedColumnWidth(70.w),
                                                      5: FixedColumnWidth(70.w),
                                                      6: FixedColumnWidth(70.w),
                                                    },
                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                    children: const <TableRow>[
                                                      TableRow(
                                                        decoration: BoxDecoration(
                                                          color: Color(0xff303F9F),
                                                        ),
                                                        children: <Widget>[
                                                          _TitleTableIV(text: "Số lô"),
                                                          _TitleTableIV(text: "SO/SOLine"),
                                                          _TitleTableIV(text: "WBS"),
                                                          _TitleTableIV(text: "LSX Đại trà"),
                                                          _TitleTableIV(text: "Số lượng chuyển"),
                                                          _TitleTableIV(text: "SL tồn"),
                                                          _TitleTableIV(text: "ĐVT"),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    children: List.generate(
                                                      (_lsDataGetListSOWBSByBatchTranferWareHouse).length,
                                                      (index) => Table(
                                                        border: TableBorder(
                                                          left: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          verticalInside: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        ),
                                                        columnWidths: <int, TableColumnWidth>{
                                                          0: FixedColumnWidth(100.w),
                                                          1: FixedColumnWidth(100.w),
                                                          2: FixedColumnWidth(100.w),
                                                          3: FixedColumnWidth(100.w),
                                                          4: FixedColumnWidth(70.w),
                                                          5: FixedColumnWidth(70.w),
                                                          6: FixedColumnWidth(70.w),
                                                        },
                                                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                        children: <TableRow>[
                                                          TableRow(
                                                            children: <Widget>[
                                                              _ColumnTableIV(
                                                                  text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].batchNumber ?? ""),
                                                              Container(
                                                                  margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  child: (_lsDataGetListSOWBSByBatchTranferWareHouse[index].so != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].so != "") &&
                                                                          (_lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine != "")
                                                                      ? Column(
                                                                          children: <Widget>[
                                                                            Text(
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].so ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                            Text(
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].soLine ?? "",
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                          ],
                                                                        )
                                                                      : _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs != null &&
                                                                              _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs != ""
                                                                          ? Center(child: Text("", style: TextStyle(fontSize: 12.sp)))
                                                                          : Center(child: Text("Tồn trơn", style: TextStyle(fontSize: 12.sp)))),
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].wbs ?? ""),
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].lsxdt ?? ""),
                                                              Padding(
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                child: Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  decoration: BoxDecoration(
                                                                      border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                      borderRadius: BorderRadius.circular(3.r)),
                                                                  child: TextFormField(
                                                                    enabled: _lsDataGetListSOWBSByBatchTranferWareHouse[index].quantity != 0.0,
                                                                    maxLines: null,
                                                                    textAlign: TextAlign.center,
                                                                    controller: _lsControllers[index],
                                                                    focusNode: _lsFocusNode[index],
                                                                    style: TextStyle(fontSize: 12.sp),
                                                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                    inputFormatters: <TextInputFormatter>[
                                                                      FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d{0,3}')),
                                                                      CommaTextInputFormatter()
                                                                    ],
                                                                    decoration: InputDecoration(
                                                                      border: InputBorder.none,
                                                                      isDense: true,
                                                                      contentPadding: EdgeInsets.zero,
                                                                      errorBorder: InputBorder.none,
                                                                      disabledBorder: InputBorder.none,
                                                                      filled: true,
                                                                      fillColor: Colors.white,
                                                                      hintStyle: TextStyle(fontSize: 12.sp),
                                                                    ),
                                                                    onChanged: (value) {
                                                                      String batchNumber =
                                                                          _lsDataGetListSOWBSByBatchTranferWareHouse[index].batchNumber.toString();
                                                                      List<double> getListDouble = ExportWareHouseFunction.getListDouble(
                                                                          batchNumber, _lsDataGetListSOWBSByBatchTranferWareHouse, _lsControllers);
                                                                      double sum = ImportWareHouseFunction.amountListNumber(getListDouble);
                                                                      int getIndex =
                                                                          _sumAmountEntered.indexWhere((element) => element.code == batchNumber);
                                                                      SumAmountExport newSumAmountExport =
                                                                          SumAmountExport(code: _sumAmountEntered[getIndex].code, sum: sum);
                                                                      setState(() {
                                                                        _sumAmountEntered.removeAt(getIndex);
                                                                        _sumAmountEntered.insert(getIndex, newSumAmountExport);
                                                                        if (_lsControllers.where((element) => element.text.isNotEmpty).isNotEmpty) {
                                                                          if (_lsControllers
                                                                              .where((element) =>
                                                                                  element.text.isNotEmpty && double.parse(element.text) <= 0.0)
                                                                              .isNotEmpty) {
                                                                            if (_errorQuantity != true) {
                                                                              _errorQuantity = true;
                                                                            }
                                                                            _message = "Số lượng chuyển không được nhỏ hơn 0.0";
                                                                          } else {
                                                                            if (_errorQuantity != false) {
                                                                              _errorQuantity = false;
                                                                            }
                                                                            _message = "";
                                                                          }
                                                                        } else {
                                                                          if (_errorQuantity != true) {
                                                                            _errorQuantity = true;
                                                                          }
                                                                          _message = "Vui lòng nhập số lượng chuyển!";
                                                                        }
                                                                      });
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                              _ColumnTableIV(
                                                                  text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].quantity == null
                                                                      ? ""
                                                                      : _lsDataGetListSOWBSByBatchTranferWareHouse[index]
                                                                          .quantity!
                                                                          .toStringAsFixed(3)),
                                                              _ColumnTableIV(text: _lsDataGetListSOWBSByBatchTranferWareHouse[index].unit ?? ""),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: _errorQuantity == true ? 10.h : 0),
                                          ContainerError.widgetError(_errorQuantity, _message)
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                                        width: double.infinity,
                                        decoration: const BoxDecoration(),
                                        child: ElevatedButton(
                                          style: ButtonStyle(
                                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                            side: MaterialStateProperty.all(
                                              BorderSide(
                                                  color: _lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty
                                                      ? const Color(0xff303F9F)
                                                      : const Color(0xffe0e0e0)),
                                            ),
                                            backgroundColor: MaterialStateProperty.all(_lsDataGetListSOWBSByBatchTranferWareHouse.isNotEmpty
                                                ? const Color(0xff303F9F)
                                                : const Color(0xffe0e0e0)),
                                          ),
                                          onPressed: _lsDataGetListSOWBSByBatchTranferWareHouse.isEmpty
                                              ? null
                                              : () {
                                                  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                  if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                    Platform.isAndroid
                                                        ? showDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                        : showCupertinoDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                  } else {
                                                    _checkQuantityExceed(context);
                                                  }
                                                },
                                          child: Container(
                                            margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                            child: Text(
                                              "Chuyển kho",
                                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                  ],
                                ),
                              ),
                  )));
  }
}

class _ColumnTableIV extends StatelessWidget {
  final String text;
  const _ColumnTableIV({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        child: Text(text, style: TextStyle(fontSize: 12.sp), textAlign: TextAlign.center));
  }
}

class _TitleTableIV extends StatelessWidget {
  final String text;
  const _TitleTableIV({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _TitleExportWareHouse extends StatelessWidget {
  const _TitleExportWareHouse({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          "CHUYỂN KHO NVL CÓ LỆNH CẤP",
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class _ListSuggestions extends StatelessWidget {
  final ValueChanged<DataGetStorageBin> setFilter;
  final List<DataGetStorageBin> lsDataGetStorageBn;
  final bool isLoadingStorageBin;
  const _ListSuggestions({Key? key, required this.setFilter, required this.lsDataGetStorageBn, required this.isLoadingStorageBin}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: lsDataGetStorageBn.isNotEmpty || isLoadingStorageBin == true,
      child: SizedBox(
        height: 75.h,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 7,
                offset: const Offset(0, 1), // changes position of shadow
              ),
            ],
          ),
          child: isLoadingStorageBin == true
              ? const Center(child: CircularProgressIndicator())
              : Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Colors.white),
                  child: SingleChildScrollView(
                    child: Column(
                      children: List.generate(lsDataGetStorageBn.length, (index) {
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 5.h),
                          child: InkWell(
                            onTap: () {
                              setFilter(lsDataGetStorageBn[index]);
                            },
                            child: Text(
                              lsDataGetStorageBn[index].value.toString(),
                              style: TextStyle(fontSize: 13.sp, color: Colors.black),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}

class _HeaderExportWareHouse extends StatelessWidget {
  final String title;
  const _HeaderExportWareHouse({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

class _PlantExportImport extends StatelessWidget {
  final DataReservation? dataReservation;
  const _PlantExportImport({Key? key, required this.dataReservation}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Nhà máy xuất:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation == null ? "" : dataReservation!.plant ?? "",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Nhà máy nhập:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation == null ? "" : dataReservation!.riPlant ?? " ",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _WareHouseExportImport extends StatelessWidget {
  final DataReservation? dataReservation;
  const _WareHouseExportImport({Key? key, required this.dataReservation}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Kho xuất:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation!.storageLocation ?? "",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                "Kho nhập:",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                dataReservation!.riStorageLocation ?? " ",
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
