PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - gallery_saver (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - onesignal_flutter (5.2.0):
    - Flutter
    - OneSignalXCFramework (= 5.2.0)
  - OneSignalXCFramework (5.2.0):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.0)
  - OneSignalXCFramework/OneSignal (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.0):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.0)
  - OneSignalXCFramework/OneSignalExtension (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.0):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.0):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.0):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - ReachabilitySwift (5.2.3)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - gallery_saver (from `.symlinks/plugins/gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner
    - OneSignalXCFramework
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  gallery_saver:
    :path: ".symlinks/plugins/gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_native_splash: 9e672d3818957718ee006a491730c09deeecace9
  flutter_secure_storage: 2c2ff13db9e0a5647389bff88b0ecac56e3f3418
  gallery_saver: 1d68d1818df11b1afa84a97d1a530463753e92e3
  image_picker_ios: afb77645f1e1060a27edb6793996ff9b42256909
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  onesignal_flutter: d86795eb74c65854b23169f131b4fb1ca3146659
  OneSignalXCFramework: bdf74fdc06888f9466dc21e826fe1549ed143095
  package_info_plus: 5076a1ce937258be9d2d41781a0e9cd71f19a82f
  path_provider_foundation: 6e19d6c976248fe436937fbb19ff17b597ee725d
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  ReachabilitySwift: 7f151ff156cea1481a8411701195ac6a984f4979
  shared_preferences_foundation: 4e65c567e7877037d328829a522222c938bf308c
  url_launcher_ios: b7f13d9491e07f5230bf3e056ab68678f896980a

PODFILE CHECKSUM: 1536dcdfbd64d7815fdd47c9fa9aca157ccfd3fc

COCOAPODS: 1.16.2
