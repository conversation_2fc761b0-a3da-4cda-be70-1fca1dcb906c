import 'dart:convert';

import '../../model/detailReservationImported.dart';
import '../api/detailReservationImportedApi.dart';

class DetailReservationImportedFuncion{

  static Future<DataDetailReservationImported?> getWarehouseTranferNoReservation(String reservationId, String token) async {
    final response = await DetailReservationImportedApi.getDetailReservationImportedApi(reservationId, token);
    if (response.statusCode == 200) {
      final getMessage = DetailReservationImported.fromJson(jsonDecode(response.body));
      if (getMessage.code == 200 && getMessage.isSuccess == true) {
        return getMessage.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}