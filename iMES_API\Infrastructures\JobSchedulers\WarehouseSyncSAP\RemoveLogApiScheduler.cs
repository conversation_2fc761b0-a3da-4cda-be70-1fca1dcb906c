﻿using Hangfire;
using ISD.API.Constant.MESP2;
using ISD.API.EntityModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers.WarehouseSyncSAP
{
    public static class RemoveLogApiScheduler
    {
        public static async Task Invoke(IServiceProvider serviceProvider)
        {
            //Create scope context
            //using var scope = _serviceProvider.CreateScope();
            using var dbContext = serviceProvider.GetRequiredService<EntityDataContext>();

            //Check trạng thái run job
            var settingJob = await dbContext.SettingJob.SingleOrDefaultAsync(x => x.JobName == "RemoveLogApi");
            if (settingJob?.IsRun == false) return;

            var service = new RemoveLogApiService(serviceProvider);

            try
            {
    
                // Get GMT+7 timezone (SE Asia Standard Time)
                TimeZoneInfo gmt7 = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");
                
                // Xóa job nếu tồn tại trước khi thêm mới
                RecurringJob.RemoveIfExists("RemoveLogApi");
                RecurringJob.RemoveIfExists(SettingSyncSAPConstant.WarehouseSyncSAP);
                
                // Thêm mới job và run với timezone GMT+7
                if (settingJob?.Config != null)
                {
                    RecurringJob.AddOrUpdate(
                        "RemoveLogApi",
                        () => service.Run(),
                        settingJob.Config,
                        gmt7 // GMT+7 Timezone
                    );
                }
                else
                {
                    // Default to daily at 0:35 AM GMT+7 if no config is provided
                    RecurringJob.AddOrUpdate(
                        "RemoveLogApi",
                        () => service.Run(),
                        Cron.Daily(0, 35),
                        gmt7 // GMT+7 Timezone
                    );
                }
                
                return;

                // //Xóa job nếu tồn tại trước khi thêm mới
                // RecurringJob.RemoveIfExists("RemoveLogApi");
                // RecurringJob.RemoveIfExists(SettingSyncSAPConstant.WarehouseSyncSAP);

                // //Thêm mới job và run
                // RecurringJob.AddOrUpdate("RemoveLogApi", () => service.Run(), settingJob?.Config);
            }
            catch (Exception)
            {
                return;
            }
        }
    }
}
