// class ErrorMessage {
//   Errors? errors;
//   String? type;
//   String? title;
//   int? status;
//   String? traceId;
//
//   ErrorMessage({this.errors, this.type, this.title, this.status, this.traceId});
//
//   ErrorMessage.fromJson(Map<String, dynamic> json) {
//     errors =
//     json['errors'] != null ? Errors.fromJson(json['errors']) : null;
//     type = json['type'];
//     title = json['title'];
//     status = json['status'];
//     traceId = json['traceId'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     if (errors != null) {
//       data['errors'] = errors!.toJson();
//     }
//     data['type'] = type;
//     data['title'] = title;
//     data['status'] = status;
//     data['traceId'] = traceId;
//     return data;
//   }
// }
//
// class Errors {
//   List<String>? batch;
//
//   Errors({this.batch});
//
//   Errors.fromJson(Map<String, dynamic> json) {
//     batch = json['Batch'].cast<String>();
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['Batch'] = batch;
//     return data;
//   }
// }