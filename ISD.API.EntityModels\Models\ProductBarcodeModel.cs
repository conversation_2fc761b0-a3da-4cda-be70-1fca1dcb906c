﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductBarcodeModel", Schema = "MES")]
    public partial class ProductBarcodeModel
    {
        [Key]
        public Guid ProductBarcodeId { get; set; }
        [StringLength(255)]
        [Unicode(false)]
        public string FriendlyCode { get; set; }
        [Required]
        [StringLength(50)]
        [Unicode(false)]
        public string PO { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string SO { get; set; }
        [Required]
        [StringLength(20)]
        [Unicode(false)]
        public string ProductCode { get; set; }
        [Required]
        [StringLength(10)]
        [Unicode(false)]
        public string Plant { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string Status { get; set; }
        [StringLength(255)]
        [Unicode(false)]
        public string Notes { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string CheckDigit { get; set; }
        [StringLength(255)]
        public string BarcodePath { get; set; }
        [StringLength(13)]
        [Unicode(false)]
        public string EAN13 { get; set; }
    }
}