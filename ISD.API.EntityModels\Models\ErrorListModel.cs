﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ErrorListModel", Schema = "tMasterData")]
    public partial class ErrorListModel
    {
        [Key]
        public Guid ErrorListId { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        public Guid? DepartmentId { get; set; }
        [StringLength(50)]
        public string ErrorListCode { get; set; }
        [StringLength(500)]
        public string ErrorListName { get; set; }
        [StringLength(500)]
        public string Personnel { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
    }
}