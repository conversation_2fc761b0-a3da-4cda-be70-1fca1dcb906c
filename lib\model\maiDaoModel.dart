class MaiDaoModel {
  List<MaiDaoRecord>? data;
  String? message;
  bool? status;

  MaiDaoModel({this.data, this.message, this.status});

  MaiDaoModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <MaiDaoRecord>[];
      json['data'].forEach((v) {
        data!.add(MaiDaoRecord.fromJson(v));
      });
    }
    message = json['message'];
    status = json['status'];
  }
}

class MaiDaoRecord {
  String? id;
  String? date;
  String? equipmentCode;
  String? equipmentName;
  String? materialCode;
  String? materialName;
  String? materialBatch;
  String? operationType;
  String? employeeCodes;
  String? employeeNames;
  String? requestingEmployeeCode;
  String? requestingEmployeeName;
  String? note;
  String? status;
  String? createdDate;
  String? createBy;
  String? updatedDate;
  String? updateBy;
  String? companyCode;

  MaiDaoRecord({
    this.id,
    this.date,
    this.equipmentCode,
    this.equipmentName,
    this.materialCode,
    this.materialName,
    this.materialBatch,
    this.operationType,
    this.employeeCodes,
    this.employeeNames,
    this.requestingEmployeeCode,
    this.requestingEmployeeName,
    this.note,
    this.status,
    this.createdDate,
    this.createBy,
    this.updatedDate,
    this.updateBy,
    this.companyCode,
  });

  MaiDaoRecord.fromJson(Map<String, dynamic> json) {
    id = json['maiDaoId']?.toString();
    date = json['date'];
    equipmentCode = json['equipmentCode'];
    equipmentName = json['equipmentName'];
    materialCode = json['materialCode'];
    materialName = json['materialName'];
    materialBatch = json['materialBatch'];
    operationType = json['operationType'];
    employeeCodes = json['employeeCodes'];
    employeeNames = json['employeeNames'];
    requestingEmployeeCode = json['requestingEmployeeCode'];
    requestingEmployeeName = json['requestingEmployeeName'];
    note = json['note'];
    status = json['status'];
    createdDate = json['createdDate']?.toString();
    createBy = json['createBy']?.toString();
    updatedDate = json['updatedDate']?.toString();
    updateBy = json['updateBy']?.toString();
    companyCode = json['companyCode'];
  }

  Map<String, dynamic> toJson() {
    return {
      'maiDaoId': id,
      'date': date,
      'equipmentCode': equipmentCode,
      'equipmentName': equipmentName,
      'materialCode': materialCode,
      'materialName': materialName,
      'materialBatch': materialBatch,
      'operationType': operationType,
      'employeeCodes': employeeCodes,
      'employeeNames': employeeNames,
      'requestingEmployeeCode': requestingEmployeeCode,
      'requestingEmployeeName': requestingEmployeeName,
      'note': note,
      'status': status,
      'createdDate': createdDate,
      'createBy': createBy,
      'updatedDate': updatedDate,
      'updateBy': updateBy,
      'companyCode': companyCode,
    };
  }
}

class EmployeeRecord {
  String? employeeId;
  String? accountId;
  String? employeeCode;
  String? employeeName;

  EmployeeRecord({
    this.employeeId,
    this.accountId,
    this.employeeCode,
    this.employeeName,
  });

  EmployeeRecord.fromJson(Map<String, dynamic> json) {
    employeeId = json['employeeId'];
    accountId = json['accountId'];
    employeeCode = json['employeeCode'];
    employeeName = json['employeeName'];
  }

  Map<String, dynamic> toJson() {
    return {
      'employeeId': employeeId,
      'accountId': accountId,
      'employeeCode': employeeCode,
      'employeeName': employeeName,
    };
  }
}

class EquipmentItem {
  String? equipmentCode;
  String? equipmentName;

  EquipmentItem({this.equipmentCode, this.equipmentName});

  EquipmentItem.fromJson(Map<String, dynamic> json) {
    equipmentCode = json['equipmentCode'];
    equipmentName = json['equipmentName'];
  }
}

class MaterialItem {
  String? materialCode;
  String? materialName;

  MaterialItem({this.materialCode, this.materialName});

  MaterialItem.fromJson(Map<String, dynamic> json) {
    materialCode = json['materialCode'];
    materialName = json['materialName'];
  }
}

class MaiDaoMasterDataResponse {
  List<EquipmentItem>? equipments;
  List<MaterialItem>? materials;
  List<EmployeeRecord>? employees;
  String? message;
  bool? status;
  dynamic data;

  MaiDaoMasterDataResponse({
    this.equipments,
    this.materials,
    this.employees,
    this.message,
    this.status,
    this.data,
  });

  MaiDaoMasterDataResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    data = json['data'];

    if (json['data'] != null) {
      if (json['data'] is List) {
        var items = json['data'] as List;
        if (items.isNotEmpty) {
          if (items[0].containsKey('equipmentCode')) {
            equipments = items.map((v) => EquipmentItem.fromJson(v)).toList();
          } else if (items[0].containsKey('materialCode')) {
            materials = items.map((v) => MaterialItem.fromJson(v)).toList();
          } else if (items[0].containsKey('employeeCode')) {
            employees = items.map((v) => EmployeeRecord.fromJson(v)).toList();
          }
        }
      }
    }
  }
}

class MaiDaoSearchModel {
  String? equipmentCode;
  String? materialCode;
  String? operationType;
  String? status;
  DateTime? fromDate;
  DateTime? toDate;
  String companyCode;
  int pageNumber;
  int pageSize;

  MaiDaoSearchModel({
    this.equipmentCode,
    this.materialCode,
    this.operationType,
    this.status,
    this.fromDate,
    this.toDate,
    required this.companyCode,
    this.pageNumber = 1,
    this.pageSize = 20,
  });

  Map<String, dynamic> toJson() => {
        'equipmentCode': equipmentCode,
        'materialCode': materialCode,
        'operationType': operationType,
        'status': status,
        'fromDate': fromDate?.toIso8601String(),
        'toDate': toDate?.toIso8601String(),
        'companyCode': companyCode,
        'pageNumber': pageNumber,
        'pageSize': pageSize,
      };
}
