﻿using GetSOWBSByLSX;
using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories.MES;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SAPWarehouseTransaction;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    [ISDWebAuthorization]
    public class AllocateMaterialsController : ControllerBaseAPI
    {
        private readonly IHostEnvironment _env;

        public AllocateMaterialsController(IHostEnvironment env)
        {
            _env = env;

        }

        #region Search danh sách phân bổ
        /// <summary>
        /// Danh sách phân bổ
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("search")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public IActionResult Search(SearchAllocateMaterialsRequest request)
        {

            var allocateRepo = new AllocateRepository(_context);

            //Call sp
            //var data = GeDataAllocateByDayPlant(request.AllocateDate, CurrentUser?.SaleOrg);
            var data = allocateRepo.GeDataAllocateByDayPlant(request.AllocateDate, request.Plant);
            var dataWbs = allocateRepo.GeDataAllocateByDayPlantWbs(request.AllocateDate, request.Plant);


            //Search theo trạng thái chưa phân bổ
            if (request.Status == 1)
            {
                data = data.Where(x => x.StatusPB == StatusAllocate.CHUAPHANBO).ToList();
            }

            //Search theo trạng thái đã phân bổ
            if (request.Status == 2)
            {
                data = data.Where(x => x.StatusPB == StatusAllocate.LUUBANGTAM).ToList();
            }

            //Search theo trạng thái đã phân bổ
            if (request.Status == 3)
            {
                data = data.Where(x => x.StatusPB == StatusAllocate.DAPHANBO).ToList();
            }

            if (!string.IsNullOrEmpty(request.Sloc))
            {
                data = data.Where(x => x.Sloc == request.Sloc).ToList();
            }

            //var products = _context.ProductModel.AsNoTracking();
            var productCodes = data.Select(p => p.ProductCode).ToList();
            var filteredProducts = _context.ProductLatestModel.Where(p => productCodes.Contains(p.ProductCode)).ToList();

            //Đánh dấu STT
            if (data.Any())
            {
                int i = 0;
                foreach (var item in data)
                {
                    i = i + 1;
                    item.STT = i;

                    //item.ProductName = products.FirstOrDefault(x => x.ProductCode == item.ProductCode)?.ProductName;
                    item.ProductName = filteredProducts.FirstOrDefault(x => x.ProductCode == item.ProductCode)?.ProductName;

                    var unit = item.Unit;

                    if (unit == "DAY")
                    {
                        item.Unit = "DAA";
                    }

                    var wbsList = dataWbs.Where(x =>
                            x.ProductCode == item.ProductCode
                            && x.StepCode == item.StepCode
                            && x.Batch == item.Batch
                            && x.Sloc == item.Sloc
                        ).Select(x => new { SoWbs = x.SoWbs + (x.AllocateAmountWbs != null ? " | " + x.AllocateAmountWbs : "") }).Select(x => x.SoWbs).Distinct().OrderBy(x => x).ToList();
                    string wbsString = string.Join(",", wbsList);
                    item.SoWbs = wbsString;
                }
            }

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = data });

        }
        #endregion

        #region Chi tiết phân bổ
        /// <summary>
        /// Chi tiết phân bổ
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("detail")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> Detail(DetailAllocateMaterialsRequest request)
        {
            var response = new AllocateMaterialsByLSXResponse();

            #region Data header

            var allocateRepo = new AllocateRepository(_context);

            response = allocateRepo.GetAllocateMaterialsByLSX(request);

            #endregion

            #region Data detail

            var detail = allocateRepo.GetAllocateMaterialsByLSXDetail(request.AllocateDate, response.StepCode, response.Unit);

            var zMes = new ZMES_PRODUCTION_ORDER02
            {
                IT_INPUT = detail.Select(x => new ZST_MES_INPUT_LSX
                {
                    AUFNR = x.LSX,
                    WERKS = CurrentUser?.SaleOrg
                }).ToArray()
            };

            var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(zMes);
            try
            {
                var responeToSAP = await _unitOfWork.SAPAPIRepository.GetSOWBSByLSX(zMes);
                var resSAPs = responeToSAP.ZMES_PRODUCTION_ORDER02Response.ET_OUTPUT.ToList();

                if (resSAPs.Any())
                {
                    foreach (var item in detail)
                    {
                        //Data SAP
                        //var soWBS = resSAPs.FirstOrDefault(x => x.AUFNR == $"000{item.LSX}");
                        var soWBS = resSAPs.FirstOrDefault(x => x.AUFNR.TrimStart('0') == item.LSX.TrimStart('0'));
                        item.Unit = response.Unit;

                        //Ưu tiên lấy WBS
                        if (!string.IsNullOrEmpty(soWBS?.PROJN))
                        {
                            //WBS
                            item.WBS = soWBS?.PROJN_TEXT;
                            //SO và SOLine
                            item.SONumber = null;
                            item.SOLine = null;
                        }

                        if (!string.IsNullOrEmpty(soWBS?.KDAUF))
                        {
                            //SO và SOLine
                            item.SONumber = soWBS?.KDAUF;
                            item.SOLine = soWBS?.KDPOS;
                            //WBS
                            item.WBS = null;
                        }
                    }
                }
            }
            catch
            {
                return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP!", IsSuccess = false });
            }

            // Create a mock object for detail
            var mockItems = new List<DetailAllocateMaterialsByLSXResponse>
            {
                new DetailAllocateMaterialsByLSXResponse
                {
                    TaskId = Guid.NewGuid(),
                    LSX = "600008660",
                    ProductCode = "400013367",
                    ProductName = "Cụm trán ốp ngoài (ĐG)-15x190x1040",
                    LSXQty = 20,
                    SLDAPB = 0,
                    Unit = "TAM",
                    SONumber = "2200002865",
                    SOLine = "000150"
                },
                new DetailAllocateMaterialsByLSXResponse
                {
                    TaskId = Guid.NewGuid(),
                    LSX = "600008660",
                    ProductCode = "400013367",
                    ProductName = "Cụm trán ốp trong (ĐG)-15x190x1040",
                    LSXQty = 30,
                    SLDAPB = 0,
                    Unit = "TAM",
                    SONumber = "2200002865",
                    SOLine = "150"
                },
                new DetailAllocateMaterialsByLSXResponse
                {
                    TaskId = Guid.NewGuid(),
                    LSX = "600008660",
                    ProductCode = "400013367",
                    ProductName = "Cụm trán ốp ngoài (ĐG)-15x190x1040",
                    LSXQty = 35,
                    SLDAPB = 0,
                    Unit = "TAM",
                    SONumber = "2200002865",
                    SOLine = "170"
                },
                new DetailAllocateMaterialsByLSXResponse
                {
                    TaskId = Guid.NewGuid(),
                    LSX = "600008660",
                    ProductCode = "400013367",
                    ProductName = "Cụm trán ốp ngoài (ĐG)-15x190x1040",
                    LSXQty = 40,
                    SLDAPB = 0,
                    Unit = "TAM",
                    SONumber = "2200002865",
                    SOLine = "000170"
                }
            };

            //if (_env.IsDevelopment())
            //{
            //    response.DetailAllocateMaterials = detail.Concat(mockItems).ToList();
            //    response.AllocateAmount = 18 + 10 + 5;
            //}
            //else
            //{


            //response.DetailAllocateMaterials = detail;

            // Update 1: only display productcode
            response.DetailAllocateMaterials = detail
                                            .Where(d => d.MATNR == request.ProductCode)
                                            .ToList();
            //}

            #endregion


            var phanBoWBSDetail = allocateRepo.GetPhanBoDetail(request.ProductCode, request.AllocateDate, response.StepCode);

            response.PhanBoWBSDetail = phanBoWBSDetail;

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = response });
        }
        #endregion

        #region Save data phân bổ BẢNG TẠM và đẩy lên SAP 
        /// <summary>Save data phân bổ BẢNG TẠM và đẩy lên SAP  </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/AllocateMaterials/save
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///               "detailAllocates": [
        ///                 {
        ///                   "taskId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                   "productCode": "string",
        ///                   "allocateAmount": 0,
        ///                   "unit": "string",
        ///                   "slocId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                   "batch": "string",
        ///                   "allocateDate": "2022-11-14T06:15:31.513Z",
        ///                   "stepCode": "string"
        ///                 }
        ///               ],
        ///               "totalAllocateAmount": 0
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": [
        ///         {
        ///             "purchaseOrderCode": "**********",
        ///             "poItem": "10",
        ///             "material": "*********"
        ///         }
        ///        ]
        ///     }
        /// </remarks>
        [HttpPost("save")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> Save(SaveAllocateMaterialsRequest request)
        {
            var accountId = CurrentUser.AccountId;

            var user = await _context.AccountModel.Include(a => a.Roles).FirstOrDefaultAsync(p => p.AccountId == accountId && p.Actived == true);

            if (user == null)
            {
                return Ok(new ApiResponse { IsSuccess = false, Message = "User không không tồn tại" });
            }

            var permissionOnly = _context.PageModel
                .Where(x => x.PageName == "Phân bổ NVL cho LSX")
                .Select(x => new PageViewModel
                {
                    PageId = x.PageId,
                    PageUrl = x.PageUrl,
                    PageName = x.PageName,
                    MenuId = x.MenuId.Value
                })
                .ToList();

            var roles = user.Roles.Select(a => a.RolesId).ToList();

            var pagePermissions = _context.PagePermissionModel
                .Where(pp => roles.Contains(pp.RolesId) && permissionOnly.Select(p => p.PageId).Contains(pp.PageId))
                .OrderBy(pp => pp.PageId)
                .Select(pp => new PagePermissionViewModel { PageId = pp.PageId, FunctionId = pp.FunctionId })
                .ToList();


            // Check if the user has the "EDIT" permission
            bool hasEditPermission = pagePermissions.Any(permission => permission.FunctionId == "EDIT");

            if (!hasEditPermission)
            {
                return Ok(new ApiResponse { IsSuccess = false, Message = "User không được phân quyền phân bổ NVL" });
            }

            //Tổng phân bổ
            decimal total = 0;
            foreach (var item in request.DetailAllocates)
            {
                total = (decimal)(total + item.AllocateAmount ?? 0);
            }

            //Nếu số lượng nhập vào không đúng với ố lượng phân bổ thì trả về fálse
            if (request.TotalAllocateAmount != total)
            {
                return Ok(new ApiResponse { IsSuccess = false, Message = "'SL phân bổ' khác với 'SL cần phân bổ vào LSX' vui lòng kiểm tra lại!" });
            }

            var listWExport = new List<WarehouseExportModel>();

            Guid? headerId = Guid.NewGuid();

            //Type = 1, lưu bảng tạm
            if (request.Type == 1)
            {
                // Lưu SQL
                foreach (var item in request.DetailAllocates)
                {
                    var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == item.Sloc);

                    var unit = item.Unit;

                    if (unit == "DAY")
                    {
                        unit = "DAA";
                    }

                    //Save data phân bổ
                    var wExport = new WarehouseExportModel
                    {
                        WarhouseExportId = Guid.NewGuid(),
                        //Loại giao dịch
                        MovementType = MovementType.Allocate,
                        //NVL
                        ProductCode = item.ProductCode,
                        //Ngày chứng từ
                        DocumentDate = item.AllocateDate,
                        //LSX
                        ReferenceDocumentId = item.TaskId,
                        //Số lô
                        Batch = item.Batch,
                        //Kho                    
                        SlocId = sloc?.Id,
                        //Số lượng phân bổ
                        Quantity = item.AllocateAmount,
                        HeaderIdSAP = headerId,
                        //ĐVT
                        Unit = unit,
                        Plant = CurrentUser?.SaleOrg,
                        //Công đoạn
                        StepCode = item.StepCode,
                        //So và SO LINE
                        SONumber = item.SONumber,
                        SOLine = item.SOLine,
                        WBS = item.WBS,
                        //Common
                        CreateBy = CurrentUser.AccountId,
                        CreateTime = DateTime.Now,
                        Actived = true,

                        DateKey = int.Parse(item.AllocateDate?.ToString("yyyyMMdd")),
                        IsPhanBoSAP = false
                    };

                    listWExport.Add(wExport);
                }

                _context.WarehouseExportModel.AddRange(listWExport);
            }
            //Đồng bộ dữ liệu lên SAP
            else
            {
                var slocs = _context.SlocModel.AsNoTracking();

                var tasks = _context.TaskModel.AsNoTracking();

                var taskId = request.DetailAllocates.FirstOrDefault().TaskId;


                var batch = request.DetailAllocates.FirstOrDefault().Batch;
                var stepCode = request.DetailAllocates.FirstOrDefault().StepCode;
                var productCode = request.DetailAllocates.FirstOrDefault().ProductCode;
                var dateKey = int.Parse(request.DetailAllocates.FirstOrDefault().AllocateDate.Value.ToString("yyyyMMdd"));

                //Cập nhật trạng thái đã phân bổ về sap của data phân bổ trong ngày
                var dataAllocates = await _context.WarehouseExportModel.Where(x => x.ProductCode == productCode &&
                                                                                  x.DateKey == dateKey &&
                                                                                  x.StepCode == stepCode &&
                                                                                  x.Batch == batch &&
                                                                                  x.MovementType == "262")
                                                                       .ToListAsync();

                if (dataAllocates.Any())
                {
                    headerId = dataAllocates.FirstOrDefault().HeaderIdSAP;
                }


                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == request.DetailAllocates.FirstOrDefault().Sloc);

                foreach (var item in request.DetailAllocates)
                {

                    var unit = item.Unit;

                    if (unit == "DAY")
                    {
                        unit = "DAA";
                    }
                    //Save data phân bổ
                    var wExport = new WarehouseExportModel
                    {
                        WarhouseExportId = Guid.NewGuid(),
                        //Loại giao dịch
                        MovementType = MovementType.Allocate,
                        //NVL
                        ProductCode = item.ProductCode,
                        //Ngày chứng từ
                        DocumentDate = item.AllocateDate,
                        //LSX
                        ReferenceDocumentId = item.TaskId,
                        //Số lô
                        Batch = item.Batch,
                        Plant = CurrentUser?.SaleOrg,
                        //Kho                    
                        SlocId = sloc?.Id,
                        //Số lượng phân bổ
                        Quantity = item.AllocateAmount,
                        HeaderIdSAP = headerId,
                        //ĐVT
                        Unit = unit,
                        //Công đoạn
                        StepCode = item.StepCode,
                        //So và SO LINE
                        SONumber = item.SONumber,
                        SOLine = item.SOLine,
                        WBS = item.WBS,
                        //Common
                        CreateBy = CurrentUser.AccountId,
                        CreateTime = DateTime.Now,
                        Actived = true,

                        DateKey = int.Parse(item.AllocateDate?.ToString("yyyyMMdd")),
                        IsPhanBoSAP = false
                    };

                    listWExport.Add(wExport);

                    //_context.WarehouseExportModel.AddRange(listWExport);
                }

                //Request to SAP
                var allocateRepo = new AllocateRepository(_context);
                var zMes = allocateRepo.CreateZMES_FM_INF_TRANS_Input(listWExport, headerId, tasks, sloc, CurrentUser?.SaleOrg);

                ////SEND SAP
                var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(zMes);

                XmlSerializer xsSubmit = new XmlSerializer(typeof(ZMES_FM_INF_TRANS));
                var xml = "";

                using (var sww = new StringWriter())
                {
                    using (XmlWriter writer = XmlWriter.Create(sww))
                    {
                        xsSubmit.Serialize(writer, zMes);
                        xml = sww.ToString(); // Your XML
                    }
                }

#if DEBUG

                return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
#endif

                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(zMes);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    //Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "PB",
                        FuntionnName = "Phân bổ",
                        RequestSAP = jsonString,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId
                    });

                    await _context.SaveChangesAsync();

                    //List response to SAP
                    if (resSAPs.Any())
                    {
                        //Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            //Danh sách msg lỗi
                            var msgErrArr = resSAPs.Select(x => x.MESSAGE).ToArray();
                            var msgError = msgErrArr.Any() ? string.Join(", ", msgErrArr) : null;
                            return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });
                        }

                        //Cập nhật trạng thái đã phân bổ về sap của data phân bổ trong ngày
                        foreach (var item in dataAllocates)
                        {
                            item.IsPhanBoSAP = true;
                        }
                    }
                }
                catch (Exception)
                {
                    return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
                }
            }

            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Phân bổ thành công" }); ;
        }
        #endregion

        #region Get dữ liệu đã lưu bảng tạm theo ngày
        /// <summary>
        /// Get dữ liệu đã lưu bảng tạm theo ngày
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("get-allocate")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDataAllocate(DetailAllocateMaterialsRequest request)
        {
            //Sloc
            var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == request.Sloc);
            var slocId = sloc?.Id;

            var allocateDate = int.Parse(request.AllocateDate.ToString(DateTimeFormat.DateKey));

            //Data phân bổ lưu vào bảng tạm trong ngày
            var dataAllocate = await _context.WarehouseExportModel.Where(x => x.ProductCode == request.ProductCode &&
                                                                              x.DateKey == allocateDate &&
                                                                              x.StepCode == request.StepCode &&
                                                                              x.Batch == request.Batch &&
                                                                              x.MovementType == MovementType.Allocate).ToListAsync();

            //Data phân bổ
            var response = dataAllocate.Select(x => new
            {
                Quantity = x.Quantity,
                SO = x.SONumber,
                SOLine = x.SOLine,
                WBS = x.WBS,
                TaskId = x.ReferenceDocumentId
            }).Distinct().ToList();

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = response });
        }

        /// <summary>
        /// Edit dữ liệu đã lưu bảng tạm theo ngày
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("update-allocate")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> EditDataAllocate([FromBody] EditDetailAllocateRequest request)
        {

            //Date key
            var allocateDate = int.Parse(request.AllocateDate.ToString(DateTimeFormat.DateKey));

            //Data phân bổ lưu vào bảng tạm trong ngày
            var dataAllocate = _context.WarehouseExportModel.Where(x => x.ProductCode == request.ProductCode &&
                                                                              x.StepCode == request.StepCode &&
                                                                              x.DateKey == allocateDate &&
                                                                              x.Batch == request.Batch &&
                                                                              x.MovementType == MovementType.Allocate).AsNoTracking();
            //Check
            if (!dataAllocate.Any())
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Dữ liệu phân bổ lưu vào bảng tạm") });

            //Data phân bổ
            foreach (var item in request.DetailQuantityEdits)
            {
                if (!string.IsNullOrEmpty(item?.SONumber))
                {
                    var allocate = await dataAllocate.FirstOrDefaultAsync(x => x.SOLine == item.SOLine &&
                                                                                x.SONumber == x.SONumber &&
                                                                                x.ReferenceDocumentId == item.TaskId);
                    if (allocate != null)
                    {
                        //Chỉnh sửa số lượng
                        allocate.Quantity = item?.Quantity;
                    }

                }
                else
                {
                    var allocate = await dataAllocate.FirstOrDefaultAsync(x => x.WBS == item.WBS &&
                                                                               x.ReferenceDocumentId == item.TaskId);

                    //Chỉnh sửa số lượng
                    if (allocate != null)
                    {
                        //Chỉnh sửa số lượng
                        allocate.Quantity = item?.Quantity;
                    }
                }
            }

            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = true, Message = string.Format(CommonResource.Msg_Succes, "Cập nhật dữ liệu phân bổ") });
        }

        #endregion

        #region Báo cáo phân bổ theo tháng/năm
        /// <summary>
        /// Báo cáo phân bổ theo tháng/năm
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("report-allocate")]
        [MapToApiVersion("2.0")]
        [ApiExplorerSettings(GroupName = "web")]
        [AllowAnonymous]
        public async Task<IActionResult> ReportAllocate([FromBody] ReportAllocateRequest request)
        {
            var response = new ReportAllocateResponse();

            //Data xưởng
            var slocs = _context.SlocModel.Where(x => (!string.IsNullOrEmpty(request.Plant) ? x.Plant == request.Plant : x.Plant == CurrentUser.SaleOrg) &&
                                                        x.ProductionWarehouse == true && x.Actived == true)
                                          .OrderBy(x => x.Sloc)
                                          .AsNoTracking();

            response.Slocs = await slocs.Select(x => x.Sloc).ToListAsync();

            var user = _context.AccountModel.AsNoTracking();

            var products = _context.ProductModel.AsNoTracking();

            var routing = _context.RoutingModel.AsNoTracking();

            string plant = !string.IsNullOrEmpty(request.Plant) ? request.Plant : CurrentUser?.SaleOrg;

            //Data phân bổ
            var query = await _context.WarehouseExportModel.Where(x => (string.IsNullOrEmpty(request.Plant) ? true : x.Plant == request.Plant) &&
                                                                       (x.MovementType == MovementType.Allocate) &&
                                                                       (x.DocumentDate.Value.Month == request.AllocateDate.Month) &&
                                                                       (x.DocumentDate.Value.Year == request.AllocateDate.Year))
                                                           .AsNoTracking()
                                                           .ToListAsync();

            //Tất cả các ngày của tháng
            var dayInMonth = Enumerable.Range(1, DateTime.DaysInMonth(request.AllocateDate.Year, request.AllocateDate.Month))  // Days: 1, 2 ... 31 etc.
                    .Select(day => new DateTime(request.AllocateDate.Year, request.AllocateDate.Month, day, 0, 0, 0)) // Map each day to a date
                    .ToList(); // Load dates into a list

            var data = query.GroupBy(x => new { x.DocumentDate.Value.Year, x.DocumentDate.Value.Month, x.DocumentDate.Value.Day, x.Batch, x.StepCode, x.ProductCode, x.Plant, x.SlocId })
                            .Select(x => new
                            {
                                //Ngày phân bổ
                                AllocateDate = new DateTime(x.Key.Year, x.Key.Month, x.Key.Day, 0, 0, 0),
                                //Số lượng phân bổ
                                Quantity = x.Sum(e => e.Quantity),
                                //Plant 
                                Plant = x.Key.Plant,
                                //Người phân bổ
                                AllocateBy = user.FirstOrDefault(e => e.AccountId == (x.FirstOrDefault().CreateBy))?.FullName,
                                //Công đoạn
                                StepCode = x.Key.StepCode,
                                StepName = routing.FirstOrDefault(p => p.StepCode == x.Key.StepCode)?.StepName,
                                //Product 
                                ProductName = $"{x.Key.ProductCode} | {products.FirstOrDefault(p => p.ProductCode == x.Key.ProductCode)?.ProductName}",
                                Batch = x.Key.Batch,
                                SlocId = x.Key.SlocId,
                            }).ToList();

            var wareHouseExports = _context.WarehouseExportModel.Where(x => x.MovementType == MovementType.Allocate).AsNoTracking();

            var allocateRepo = new AllocateRepository(_context);

            //Data phân bổ ngày trong tháng
            foreach (var item in dayInMonth)
            {
                var dateKey = int.Parse(item.ToString(DateTimeFormat.DateKey));

                //Call sp
                var dataSPByDatePlant = allocateRepo.GeDataAllocateByDayPlant(item, plant);

                //Data phân bổ theo ngày của tất cả các kho xưởng
                var detailAllocates = data.Where(x => x.AllocateDate == item).Select(x => new DetailReportAllocateReponse
                {
                    IsAllocate = true,
                    ProductCode = x.ProductName,
                    SlocId = x.SlocId,
                    Quantity = x.Quantity,
                    StepCode = x.StepCode,
                    Plant = x.Plant
                }).ToList();

                var reportAllocateSummary = new ReportAllocateSummary
                {
                    //Ngày phân bổ
                    AllocateDate = item
                };

                //Data phân bổ theo ngày của tất cả các kho xưởng
                foreach (var allocate in detailAllocates)
                {
                    //Danh sách data cần phân bổ theo key: Plant, ProductCode, Sloc, StepCode, Plant, Date, Batch
                    var dataCanPB = dataSPByDatePlant
                                                      //.Where(x => x.Plant == allocate.Plant &&
                                                      //                                         x.SlocId == allocate.SlocId &&
                                                      //                                         x.StepCode == allocate.StepCode &&
                                                      //                                         x.ProductCode == x.ProductCode)
                                                      .ToList();

                    //Data đã phân bổ lưu ở table WarehouseExportModel theo key: Plant, ProductCode, Sloc, StepCode, Plant, Batch và IsPhanBo = 1
                    var dataPBed = wareHouseExports.Where(x => x.Plant == allocate.Plant &&
                                                               x.SlocId == allocate.SlocId &&
                                                               x.StepCode == allocate.StepCode &&
                                                               x.ProductCode == x.ProductCode &&
                                                               x.IsPhanBoSAP == true &&
                                                               x.DateKey == dateKey)
                                                   .GroupBy(x => new { x.Plant, x.SlocId, x.StepCode, x.ProductCode, x.Batch })
                                                   .Select(x => new SearchAllocateMaterialsResponse
                                                   {
                                                       SlocId = x.Key.SlocId,
                                                       Batch = x.Key.Batch,
                                                       StepCode = x.Key.StepCode,
                                                       ProductCode = x.Key.ProductCode,
                                                       Plant = x.Key.Plant
                                                   }).ToList();

                    //Nội dung ở trong là màu sắc:
                    //Xanh: là đã phân bổ hết
                    //Cam: đã phân bổ 1 phần
                    //Đỏ: là chưa làm
                    string color = null;
                    string msg = null;

                    //Data cần phân bổ == data đã phân bổ: Xanh
                    if (dataCanPB.Count() == dataPBed.Count())
                    {
                        color = "#4db6ac";
                        msg = "Đã phân bổ hết";

                    }
                    //Data cần phân bổ > data đã phân bổ: Cam
                    if (dataCanPB.Count() > dataPBed.Count())
                    {
                        color = "#ffb74d";
                        msg = "Đã phân bổ 1 phần";
                    }

                    //Data đã phân bổ = null: Đỏ
                    if (!dataPBed.Any())
                    {
                        color = "#acacac";
                        msg = "Chưa phân bổ";
                    }

                    //Chi tiết phân bổ từng kho
                    var detailReportAllocateSummary = new DetailReportAllocateSummary
                    {
                        SlocCode = slocs.FirstOrDefault(x => x.Id == allocate.SlocId)?.Sloc,
                        Color = color,
                        MsgDisplay = msg,
                        Percent = $"{dataPBed.Count()}/{dataCanPB.Count()}"
                    };

                    reportAllocateSummary.SlocReportAllocates.Add(detailReportAllocateSummary);
                }

                //Danh sách những sloc đã phân bổ
                var slocDaPBs = new List<string>();
                foreach (var detail in reportAllocateSummary.SlocReportAllocates)
                {
                    slocDaPBs.Add(detail.SlocCode);
                }

                //Thêm data cứng cho những sloc chưa phân bổ
                var slocChuaPBs = slocs.Where(x => !slocDaPBs.Contains(x.Sloc)).ToList();

                //Danh sách data cần phân bổ
                reportAllocateSummary.SlocReportAllocates.AddRange(slocChuaPBs.Select(x => new DetailReportAllocateSummary
                {
                    SlocCode = x.Sloc,
                    Color = "#acacac",
                    MsgDisplay = "Chưa phân bổ",
                    Percent = $"0/{dataSPByDatePlant.Count(e => e.Sloc == x.Sloc)}"
                }));

                reportAllocateSummary.SlocReportAllocates = reportAllocateSummary.SlocReportAllocates.OrderBy(x => x.SlocCode).ToList();

                response.DetailReports.Add(reportAllocateSummary);

            }
            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = response });
        }
        #endregion
    }
}
