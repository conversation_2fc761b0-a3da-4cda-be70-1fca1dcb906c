﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("LaborModel", Schema = "ghMasterData")]
    public partial class LaborModel
    {
        public LaborModel()
        {
            MaterialModel = new HashSet<MaterialModel>();
        }

        [Key]
        [StringLength(50)]
        public string LaborCode { get; set; }
        [StringLength(400)]
        public string LaborName { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("LaborCodeNavigation")]
        public virtual ICollection<MaterialModel> MaterialModel { get; set; }
    }
}