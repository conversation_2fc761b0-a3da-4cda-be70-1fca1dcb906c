# GenericQRScanner Usage Guide

## Overview
A reusable QR/barcode scanner component with built-in validation, error handling, and customizable UI elements.

## Features
- ✅ **Data Validation**: Custom validation logic with error messages
- ✅ **Error Handling**: Built-in error display with auto-retry
- ✅ **Loading States**: Shows processing indicator during validation
- ✅ **Flash Toggle**: Optional flashlight control
- ✅ **Custom Styling**: Configurable colors, text, and overlay
- ✅ **Auto/Manual Return**: Choose automatic or manual result handling
- ✅ **Vibration Feedback**: Optional haptic feedback on scan

## Basic Usage

### 1. Simple QR Code Scanner
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét mã QR',
      onDataScanned: (scannedData) async {
        // Simple validation - just return the data
        return scannedData;
      },
    ),
  ),
).then((result) {
  if (result != null) {
    print('Scanned: $result');
    _handleScannedData(result);
  }
});
```

### 2. Storage Bin Scanner (Current Use Case)
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét Storage Bin',
      subtitle: 'Hướng camera vào mã QR của Storage Bin',
      onDataScanned: (scannedData) async {
        try {
          // Parse XML data like the original implementation
          String convertedBarCode = '''<?xml version="1.0"?><data>$scannedData</data>''';
          final document = XmlDocument.parse(convertedBarCode);
          
          final t1 = document.findAllElements('T1').first.text;
          final t2 = int.parse(document.findAllElements('T2').first.text);
          final t3 = document.findAllElements('T3').first.text;
          final t4 = int.parse(document.findAllElements('T4').first.text);
          final t5 = document.findAllElements('T5').first.text;
          final t6 = document.findAllElements('T6').first.text;
          
          final qrCodeData = QRCodeChooseAddress(
            storageID: t1,
            plant: t2,
            sloc: t3,
            wareHouseNO: t4,
            storageType: t5,
            storageBin: t6,
          );
          
          return DataSlocAddress(
            slocId: "",
            sloc: qrCodeData.sloc,
            plant: qrCodeData.plant.toString(),
            warehouseNo: qrCodeData.wareHouseNO.toString(),
            defaultStorageBin: qrCodeData.storageBin,
            defaultStorageBinId: qrCodeData.storageID,
          );
        } catch (e) {
          throw Exception('Mã QR không hợp lệ hoặc không đúng định dạng Storage Bin');
        }
      },
      getErrorMessage: (error) {
        if (error.toString().contains('FormatException')) {
          return 'Mã QR không đúng định dạng XML';
        }
        return 'Mã QR không hợp lệ cho Storage Bin';
      },
    ),
  ),
).then((data) {
  if (data != null) {
    var storageBin = data as DataSlocAddress;
    setState(() {
      _storageBinController.text = storageBin.defaultStorageBin!;
      _storageBinID = storageBin.defaultStorageBinId!;
    });
  }
});
```

### 3. Product Code Scanner with API Validation
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét mã sản phẩm',
      subtitle: 'Quét mã vạch hoặc QR code sản phẩm',
      overlayBorderColor: Colors.blue,
      onDataScanned: (scannedData) async {
        // Validate product code with API
        final isValid = await ProductService.validateProductCode(scannedData);
        if (!isValid) {
          throw Exception('Mã sản phẩm không tồn tại trong hệ thống');
        }
        
        // Get product details
        final product = await ProductService.getProductByCode(scannedData);
        return product;
      },
      getErrorMessage: (error) {
        if (error.toString().contains('không tồn tại')) {
          return 'Sản phẩm không có trong hệ thống. Vui lòng kiểm tra lại.';
        } else if (error.toString().contains('network')) {
          return 'Lỗi kết nối mạng. Vui lòng thử lại.';
        }
        return 'Không thể xác thực mã sản phẩm';
      },
    ),
  ),
).then((product) {
  if (product != null) {
    _handleProductSelection(product);
  }
});
```

### 4. Employee ID Scanner with Manual Confirmation
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét mã nhân viên',
      subtitle: 'Quét QR code trên thẻ nhân viên',
      autoReturn: false, // Don't auto-return, show confirmation dialog
      onDataScanned: (scannedData) async {
        // Validate employee ID format
        if (!RegExp(r'^EMP\d{6}$').hasMatch(scannedData)) {
          throw Exception('Mã nhân viên không đúng định dạng (EMPxxxxxx)');
        }
        
        // Check if employee exists
        final employee = await EmployeeService.getEmployeeById(scannedData);
        if (employee == null) {
          throw Exception('Nhân viên không tồn tại');
        }
        
        return employee;
      },
      getErrorMessage: (error) {
        if (error.toString().contains('định dạng')) {
          return 'Mã nhân viên phải có định dạng EMPxxxxxx';
        }
        return 'Không tìm thấy thông tin nhân viên';
      },
    ),
  ),
).then((employee) {
  if (employee != null) {
    _selectEmployee(employee);
  }
});
```

### 5. Custom Styled Scanner
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Scan Barcode',
      subtitle: 'Position the barcode within the frame',
      backgroundColor: Colors.grey[900],
      overlayBorderColor: Colors.green,
      cutOutSize: 250,
      titleStyle: TextStyle(
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
        color: Colors.green,
      ),
      subtitleStyle: TextStyle(
        fontSize: 14.sp,
        color: Colors.white70,
        fontStyle: FontStyle.italic,
      ),
      backButtonText: 'Cancel',
      showFlashToggle: false,
      pauseDurationAfterScan: Duration(seconds: 3),
      onDataScanned: (scannedData) async {
        // Process barcode
        return await BarcodeProcessor.process(scannedData);
      },
    ),
  ),
);
```

### 6. LSX SAP Scanner (Quality Control)
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét LSX SAP',
      subtitle: 'Quét mã QR để lấy thông tin LSX SAP',
      onDataScanned: (scannedData) async {
        // Validate LSX SAP format (numbers only)
        if (!RegExp(r'^\d+$').hasMatch(scannedData)) {
          throw Exception('LSX SAP chỉ được chứa số');
        }
        
        // Check if LSX exists in system
        final lsxData = await QualityControlDetailFunction.fetchLSXSAP(
          scannedData, 
          widget.user.token.toString()
        );
        
        if (lsxData.isEmpty) {
          throw Exception('LSX SAP không tồn tại trong hệ thống');
        }
        
        return scannedData;
      },
      getErrorMessage: (error) {
        if (error.toString().contains('chỉ được chứa số')) {
          return 'LSX SAP phải là một chuỗi số';
        } else if (error.toString().contains('không tồn tại')) {
          return 'LSX SAP này không có trong hệ thống';
        }
        return 'Không thể xác thực LSX SAP';
      },
    ),
  ),
).then((lsxSap) {
  if (lsxSap != null) {
    setState(() {
      _controllerLSXSAP.text = lsxSap;
      _isLSXSAPSelected = true;
    });
    _loadQualityControlByLSQSAP(lsxSap);
  }
});
```

## Integration with ApiSuggestionField

You can now update the `ApiSuggestionField` camera scan functionality to use this generic scanner:

```dart
ApiSuggestionField(
  label: "Storage Bin:",
  controller: _storageBinController,
  showCameraButton: true,
  suggestionsCallback: (pattern) => StorageService.searchStorageBins(pattern),
  onSuggestionSelected: (bin) => _selectStorageBin(bin),
  onCameraScan: () async {
    final data = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GenericQRScanner(
          title: 'Quét Storage Bin',
          onDataScanned: (scannedData) async {
            // Use the same validation logic as before
            String convertedBarCode = '''<?xml version="1.0"?><data>$scannedData</data>''';
            final document = XmlDocument.parse(convertedBarCode);
            // ... parsing logic
            return parsedData;
          },
          getErrorMessage: (error) => 'Storage Bin QR code không hợp lệ',
        ),
      ),
    );
    
    if (data != null) {
      var storageBin = data as DataSlocAddress;
      _storageBinController.text = storageBin.defaultStorageBin!;
    }
  },
)
```

## Key Benefits

### 🚀 **Reusability**
- Single component for all scanning needs
- Consistent UI/UX across the app
- Standardized error handling

### 🎨 **Flexibility**  
- Custom validation logic for any data type
- Configurable styling and behavior
- Support for both QR codes and barcodes

### 🛡️ **Robustness**
- Built-in error handling and display
- Loading states during processing
- Auto-retry mechanism after errors

### 🔧 **Easy Integration**
- Drop-in replacement for existing QR scanners
- Works seamlessly with `ApiSuggestionField`
- Minimal code changes required

## Migration from QRCodePageChooseAnAddress

### Before (Original)
```dart
final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
```

### After (Generic Scanner)
```dart
final data = await Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => GenericQRScanner(
      title: 'Quét Storage Bin',
      onDataScanned: (scannedData) async {
        // Move the _getAddresse logic here
        return _parseStorageBinQR(scannedData);
      },
    ),
  ),
);
```

This provides a much more maintainable and flexible solution for QR code scanning throughout your app! 