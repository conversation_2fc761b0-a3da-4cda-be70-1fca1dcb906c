import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class UserLogin {
  static Future<http.Response> getListCompanyUsername(String username) async {
    final data = {"username": username};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlLogin}GetListCompanyByUsername?username=${data['username']}';

    // parse the string into a Uri
    final url = Uri.parse(urlString);

    debugPrint(environment);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headers);
    return response;
  }

  static Future<http.Response> getSaleOrgByCompanyId(String companyCode, String username) async {
    final data = {"CompanyCode": companyCode, "UserName": username};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlLogin}GetSaleOrgByCompanyCode?CompanyCode=${data['CompanyCode']}&UserName=${data['UserName']}';

    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(
      url,
      headers: UrlApi.headers,
    );
    debugPrint(response.body.toString());
    return response;
  }

  static Future<http.Response> performLogin(String username, String password, String saleOrg, String companyCode) async {
    Map<String, dynamic> data = {"userName": username, "password": password, "saleOrg": saleOrg, "companyCode": companyCode};
    final body = json.encode(data);
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlLogin + "Authenticate");
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headers, body: body);
    return response;
  }

  /// Fetches the list of company codes that should have routing functionality enabled
  /// This should be called during login to ensure the latest configuration
  static Future<http.Response> getRoutingEnabledCompanyCodes() async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // Use the Auth controller endpoint instead of Configuration
    final urlString = '${baseUrl}${UrlApi.baseUrlLogin}GetRoutingEnabledCompanyCodes';

    final url = Uri.parse(urlString);

    debugPrint("Fetching routing-enabled company codes: ${url.toString()}");
    final response = await http.get(
      url,
      headers: UrlApi.headers,
    );
    debugPrint("Routing-enabled company codes response: ${response.body.toString()}");
    return response;
  }
}
