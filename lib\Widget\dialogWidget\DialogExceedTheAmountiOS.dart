
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class DialogExceedTheAmountIos extends StatelessWidget {
  final String message;
  final String title;
  const DialogExceedTheAmountIos({Key? key, required this.message,required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  CupertinoAlertDialog(
      title: Text(title, style: TextStyle(fontSize: 15.sp)),
      content: Text(message,
          style: TextStyle(fontSize: 15.sp)),
      actions: <CupertinoDialogAction>[
        CupertinoDialogAction(
          child: Text(
            'Hủy',
            style: TextStyle(fontSize: 15.sp),
          ),
          onPressed: () {
            Navigator.pop(context, false);
          },
        ),
        CupertinoDialogAction(
          child: Text(
            'Tiếp tục',
            style: TextStyle(fontSize: 15.sp),
          ),
          onPressed: () {
            Navigator.pop(context, true);
          },
        )
      ],
    );
  }
}
