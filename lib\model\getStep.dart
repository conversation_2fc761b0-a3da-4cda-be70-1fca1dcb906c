class GetStep {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetStep>? data;

  GetStep(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetStep.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetStep>[];
      json['data'].forEach((v) {
        data!.add(DataGetStep.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetStep {
  String? key;
  String? value;

  DataGetStep({this.key, this.value});

  DataGetStep.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}