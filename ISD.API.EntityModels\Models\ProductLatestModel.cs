﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductLatestModel", Schema = "tSale")]
    public partial class ProductLatestModel
    {
        [Key]
        [StringLength(50)]
        public string ProductCode { get; set; }
        [Required]
        [StringLength(4000)]
        public string ProductName { get; set; }
        [StringLength(50)]
        public string MEINS { get; set; }
        [StringLength(50)]
        public string GROES { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(250)]
        public string MauHoanThien { get; set; }
        [StringLength(50)]
        public string MTART { get; set; }
    }
}