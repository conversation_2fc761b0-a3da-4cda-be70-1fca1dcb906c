﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProfileModel", Schema = "Customer")]
    public partial class ProfileModel
    {
        public ProfileModel()
        {
            AddressBookModel = new HashSet<AddressBookModel>();
            DeliveryModel = new HashSet<DeliveryModel>();
            PartnerModelPartnerProfile = new HashSet<PartnerModel>();
            PartnerModelProfile = new HashSet<PartnerModel>();
            PersonInChargeModel = new HashSet<PersonInChargeModel>();
            ProductWarrantyModel = new HashSet<ProductWarrantyModel>();
            ProfileEmailModel = new HashSet<ProfileEmailModel>();
            ProfileGroupModel = new HashSet<ProfileGroupModel>();
            ProfilePhoneModel = new HashSet<ProfilePhoneModel>();
            Profile_File_Mapping = new HashSet<Profile_File_Mapping>();
            Profile_Opportunity_CompetitorModelCompetitor = new HashSet<Profile_Opportunity_CompetitorModel>();
            Profile_Opportunity_CompetitorModelProfile = new HashSet<Profile_Opportunity_CompetitorModel>();
            Profile_Opportunity_InternalModelInternal = new HashSet<Profile_Opportunity_InternalModel>();
            Profile_Opportunity_InternalModelProfile = new HashSet<Profile_Opportunity_InternalModel>();
            Profile_Opportunity_MaterialModel = new HashSet<Profile_Opportunity_MaterialModel>();
            Profile_Opportunity_PartnerModelPartner = new HashSet<Profile_Opportunity_PartnerModel>();
            Profile_Opportunity_PartnerModelProfile = new HashSet<Profile_Opportunity_PartnerModel>();
            RoleInChargeModel = new HashSet<RoleInChargeModel>();
            StockReceivingMasterModel = new HashSet<StockReceivingMasterModel>();
            TemplateAndGiftMemberModel = new HashSet<TemplateAndGiftMemberModel>();
        }

        [Key]
        public Guid ProfileId { get; set; }
        public int ProfileCode { get; set; }
        [StringLength(20)]
        public string ProfileForeignCode { get; set; }
        public bool? isForeignCustomer { get; set; }
        [StringLength(20)]
        public string CustomerTypeCode { get; set; }
        [StringLength(10)]
        public string Title { get; set; }
        [StringLength(255)]
        public string ProfileName { get; set; }
        [StringLength(255)]
        public string ProfileShortName { get; set; }
        [StringLength(50)]
        public string AbbreviatedName { get; set; }
        public int? DayOfBirth { get; set; }
        public int? MonthOfBirth { get; set; }
        public int? YearOfBirth { get; set; }
        [StringLength(10)]
        public string Age { get; set; }
        [StringLength(50)]
        public string Phone { get; set; }
        [StringLength(100)]
        public string SAPPhone { get; set; }
        [StringLength(500)]
        public string Email { get; set; }
        [StringLength(4000)]
        public string Address { get; set; }
        [StringLength(10)]
        public string CountryCode { get; set; }
        [StringLength(10)]
        public string SaleOfficeCode { get; set; }
        public Guid? ProvinceId { get; set; }
        public Guid? DistrictId { get; set; }
        public Guid? WardId { get; set; }
        [StringLength(1000)]
        public string Note { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? VisitDate { get; set; }
        public bool? Actived { get; set; }
        [StringLength(1000)]
        public string ImageUrl { get; set; }
        [StringLength(20)]
        public string CreateByEmployee { get; set; }
        [StringLength(50)]
        public string CreateAtCompany { get; set; }
        [StringLength(50)]
        public string CreateAtSaleOrg { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(50)]
        public string CustomerAccountGroupCode { get; set; }
        [StringLength(50)]
        public string CustomerGroupCode { get; set; }
        [StringLength(50)]
        public string PaymentTermCode { get; set; }
        [StringLength(50)]
        public string CustomerAccountAssignmentGroupCode { get; set; }
        [StringLength(50)]
        public string CashMgmtGroupCode { get; set; }
        [StringLength(50)]
        public string ReconcileAccountCode { get; set; }
        [StringLength(50)]
        public string CustomerSourceCode { get; set; }
        [StringLength(50)]
        public string TaxNo { get; set; }
        [StringLength(50)]
        public string Website { get; set; }
        [StringLength(50)]
        public string CustomerCareerCode { get; set; }
        [StringLength(50)]
        public string CompanyNumber { get; set; }
        [StringLength(50)]
        public string AddressTypeCode { get; set; }
        public int? ProjectCode { get; set; }
        [StringLength(50)]
        public string ProjectStatusCode { get; set; }
        [StringLength(50)]
        public string QualificationLevelCode { get; set; }
        [StringLength(50)]
        public string ProjectSourceCode { get; set; }
        /// <summary>
        /// Chủ Đầu Tư
        /// </summary>
        public Guid? ReferenceProfileId { get; set; }
        /// <summary>
        /// Tư vấn &amp; TK
        /// </summary>
        public Guid? ReferenceProfileId2 { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? ContractValue { get; set; }
        public string Text1 { get; set; }
        public string Text2 { get; set; }
        public string Text3 { get; set; }
        public string Text4 { get; set; }
        public string Text5 { get; set; }
        [StringLength(50)]
        public string Dropdownlist1 { get; set; }
        [StringLength(50)]
        public string Dropdownlist2 { get; set; }
        [StringLength(50)]
        public string Dropdownlist3 { get; set; }
        [StringLength(50)]
        public string Dropdownlist4 { get; set; }
        [StringLength(50)]
        public string Dropdownlist5 { get; set; }
        [StringLength(50)]
        public string Dropdownlist6 { get; set; }
        [StringLength(50)]
        public string Dropdownlist7 { get; set; }
        [StringLength(50)]
        public string Dropdownlist8 { get; set; }
        [StringLength(50)]
        public string Dropdownlist9 { get; set; }
        [StringLength(50)]
        public string Dropdownlist10 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date1 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date2 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date3 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date4 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date5 { get; set; }
        [StringLength(4000)]
        public string ProjectLocation { get; set; }
        public bool? IsAnCuongAccessory { get; set; }
        public string Laminate { get; set; }
        public string MFC { get; set; }
        public string Veneer { get; set; }
        public string Flooring { get; set; }
        public string Accessories { get; set; }
        public string KitchenEquipment { get; set; }
        public string OtherBrand { get; set; }
        public string HandoverFurniture { get; set; }
        /// <summary>
        /// yêu cầu tạo khách ở ECC
        /// </summary>
        public bool? isCreateRequest { get; set; }
        /// <summary>
        /// Thời gian yêu cầu tạo khách ở ECC
        /// </summary>
        [Column(TypeName = "datetime")]
        public DateTime? CreateRequestTime { get; set; }
        [StringLength(50)]
        public string PaymentMethodCode { get; set; }
        [StringLength(50)]
        public string PartnerFunctionCode { get; set; }
        [StringLength(50)]
        public string CurrencyCode { get; set; }
        [StringLength(50)]
        public string TaxClassificationCode { get; set; }
        [StringLength(50)]
        public string Manager { get; set; }
        [StringLength(50)]
        public string DebsEmployee { get; set; }

        [InverseProperty("Profile")]
        public virtual ICollection<AddressBookModel> AddressBookModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<DeliveryModel> DeliveryModel { get; set; }
        [InverseProperty("PartnerProfile")]
        public virtual ICollection<PartnerModel> PartnerModelPartnerProfile { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<PartnerModel> PartnerModelProfile { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<PersonInChargeModel> PersonInChargeModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<ProductWarrantyModel> ProductWarrantyModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<ProfileEmailModel> ProfileEmailModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<ProfileGroupModel> ProfileGroupModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<ProfilePhoneModel> ProfilePhoneModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<Profile_File_Mapping> Profile_File_Mapping { get; set; }
        [InverseProperty("Competitor")]
        public virtual ICollection<Profile_Opportunity_CompetitorModel> Profile_Opportunity_CompetitorModelCompetitor { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<Profile_Opportunity_CompetitorModel> Profile_Opportunity_CompetitorModelProfile { get; set; }
        [InverseProperty("Internal")]
        public virtual ICollection<Profile_Opportunity_InternalModel> Profile_Opportunity_InternalModelInternal { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<Profile_Opportunity_InternalModel> Profile_Opportunity_InternalModelProfile { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<Profile_Opportunity_MaterialModel> Profile_Opportunity_MaterialModel { get; set; }
        [InverseProperty("Partner")]
        public virtual ICollection<Profile_Opportunity_PartnerModel> Profile_Opportunity_PartnerModelPartner { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<Profile_Opportunity_PartnerModel> Profile_Opportunity_PartnerModelProfile { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<RoleInChargeModel> RoleInChargeModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<StockReceivingMasterModel> StockReceivingMasterModel { get; set; }
        [InverseProperty("Profile")]
        public virtual ICollection<TemplateAndGiftMemberModel> TemplateAndGiftMemberModel { get; set; }
    }
}