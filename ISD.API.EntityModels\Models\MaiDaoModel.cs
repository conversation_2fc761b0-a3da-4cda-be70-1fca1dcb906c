﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MaiDaoModel", Schema = "MES")]
    [Index("CompanyCode", Name = "IX_MaiDaoModel_CompanyCode")]
    [Index("CreatedDate", Name = "IX_MaiDaoModel_CreatedDate")]
    [Index("EquipmentCode", Name = "IX_MaiDaoModel_EquipmentCode")]
    [Index("Status", Name = "IX_MaiDaoModel_Status")]
    public partial class MaiDaoModel
    {
        public MaiDaoModel()
        {
            MaiDaoHistoryModel = new HashSet<MaiDaoHistoryModel>();
        }

        [Key]
        public Guid MaiDaoId { get; set; }
        [Required]
        [StringLength(10)]
        public string Date { get; set; }
        [Required]
        [StringLength(100)]
        public string EquipmentCode { get; set; }
        [StringLength(200)]
        public string EquipmentName { get; set; }
        [StringLength(100)]
        public string MaterialCode { get; set; }
        [StringLength(200)]
        public string MaterialName { get; set; }
        [Required]
        [StringLength(20)]
        public string OperationType { get; set; }
        [StringLength(500)]
        public string EmployeeCodes { get; set; }
        [StringLength(1000)]
        public string EmployeeNames { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string RequestingEmployeeCode { get; set; }
        [StringLength(200)]
        public string RequestingEmployeeName { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string Status { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedDate { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdateBy { get; set; }
        [StringLength(10)]
        public string CompanyCode { get; set; }
        [StringLength(50)]
        public string MaterialBatch { get; set; }

        [InverseProperty("MaiDao")]
        public virtual ICollection<MaiDaoHistoryModel> MaiDaoHistoryModel { get; set; }
    }
}