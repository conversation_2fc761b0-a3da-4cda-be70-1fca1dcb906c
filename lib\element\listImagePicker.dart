import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ListImagePicker extends StatelessWidget {
  final File fileImage;
  const ListImagePicker({Key? key, required this.fileImage}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Image.file(fileImage, height: 60.h,width: 50.w,fit: BoxFit.cover);
  }
}

