import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:ttf/repository/function/qualityControlFunction.dart';
import 'package:ttf/utils/ui_helpers.dart';
import 'package:ttf/page/KiemTraChatLuong/element/LoadingOverlay.dart'; // Add this import

class QCPassedStampScan extends StatefulWidget {
  final String barcode;
  final String token;
  final String dateTimeOld;

  const QCPassedStampScan({Key? key, required this.barcode, required this.token, required this.dateTimeOld}) : super(key: key);

  @override
  State<QCPassedStampScan> createState() => _QCPassedStampScanState();
}

class _QCPassedStampScanState extends State<QCPassedStampScan> with WidgetsBindingObserver {
  QCPassedStampModel? data;
  String _errorMessage = '';

  late bool _timeOut;
  bool isLoaded = false;
  bool _isNotWifi = false;
  bool _isLoading = false;
  bool _isError = false;

  bool _mounted = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeData();
  }

  @override
  void dispose() {
    _mounted = false;
    WidgetsBinding.instance.removeObserver(this);
    // Cancel any ongoing operations here
    super.dispose();
  }

  Future<void> _initializeData() async {
    if (!isTokenLive(widget.dateTimeOld)) {
      if (_mounted) {
        setState(() {
          _timeOut = true;
        });
      }
      return;
    }

    if (_mounted) {
      setState(() {
        _timeOut = false;
        _isLoading = true;
      });
    }

    await _loadDataAndSetDefault();

    if (_mounted) {
      setState(() {
        isLoaded = true;
        _isLoading = false;
      });
    }
  }

  Future<void> _loadDataAndSetDefault() async {
    try {
      if (!isTokenLive(widget.dateTimeOld)) {
        setState(() {
          _timeOut = true;
          _errorMessage = 'Phiên đăng nhập đã hết hạn';
        });
        return;
      }

      if (!_mounted) return;

      var apiResponse = await QualityControlFunction.fetchQCPassedStamp(widget.token.toString(), widget.barcode);

      if (apiResponse == null) {
        setState(() {
          _isError = true;
          _errorMessage = 'Có lỗi xảy ra! vui lòng thử lại sau';
        });
        return;
      }

      if (apiResponse.isSuccess != true) {
        setState(() {
          _isError = true;
          _errorMessage = apiResponse.message ?? 'Không tìm thấy thông tin!';
        });
        return;
      }

      if (!_mounted) return;

      if (apiResponse.data != null && apiResponse.data!.isNotEmpty) {
        setState(() {
          data = QCPassedStampModel.fromJson(apiResponse.data);
        });
      } else {
        setState(() {
          _isError = true;
          _errorMessage = 'Không có dữ liệu!';
        });
      }
    } on SocketException catch (_) {
      if (!_mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
        _errorMessage = 'Không có kết nối mạng';
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!_mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _isError = true;
        _timeOut = false;
        _errorMessage = 'Có lỗi xảy ra! vui lòng thử lại sau';
      });
    }
  }

  // QCPassedStampModel _fetchProductData(String barcode) {
  //   // Updated mock data with the requested fields
  //   return QCPassedStampModel(
  //     'po': 'PO123456',
  //     'productCode': 'PC789',
  //     'productName': 'Widget X',
  //     'lsxSap': 'LSX${barcode.substring(0, 3)}',
  //     'serial': 'SER987654',
  //     'qualityControlTime': '2023-04-15 14:30',
  //     'workshop': 'Workshop A',
  //     'sku': 'SKU456789',
  //     'dateOfManufacture': '2023-04-10',
  //     'placeOfManufacture': 'Factory 1, City Y'
  //   };
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text('QC passed stamp info', style: TextStyle(fontSize: 14.sp)),
        titleSpacing: 0,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
      ),
      body: Stack(
        children: [
          _buildContent(),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isError || _isNotWifi || _timeOut) {
      return _ErrorView(message: _errorMessage);
    }
    return _buildTabContent();
  }

  Widget _buildTabContent() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(5.r),
        child: SafeArea(
          minimum: EdgeInsets.symmetric(horizontal: 5.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 5.h),
              if (data != null)
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.h),
                        decoration: const BoxDecoration(
                          color: Color(0xff0052cc),
                        ),
                        child: Text(
                          "Thông tin sản phẩm",
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: Column(
                          children: [
                            // _buildInfoRow('PO', data!.po ?? ''),
                            _buildInfoRow('Serial', data!.serial ?? ''),
                            _buildInfoRow('Mã sản phẩm', data!.productCode ?? ''),
                            _buildInfoRow('Tên sản phẩm', data!.productName ?? ''),
                            _buildInfoRow('LSX SAP', data!.lsxSap ?? ''),
                            _buildInfoRow('PO khách', data!.poKhach ?? ''),
                            // _buildInfoRow('Thời gian QC', data!.qualityControlTime ?? ''),
                            // _buildInfoRow('Phân xưởng', data!.workshop ?? ''),
                            _buildInfoRow('SKU', data!.sku ?? ''),
                            _buildInfoRow('Ngày sản xuất', data!.dateOfManufacture ?? ''),
                            _buildInfoRow('Nơi sản xuất', data!.placeOfManufacture ?? ''),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90.w,
            child: Text('$label:', style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold)),
          ),
          Expanded(
            child: Text(value, style: TextStyle(fontSize: 12.sp)),
          ),
        ],
      ),
    );
  }
}

class _ErrorView extends StatelessWidget {
  final String message;

  const _ErrorView({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        message,
        style: TextStyle(fontSize: 15.sp, color: Colors.black),
        textAlign: TextAlign.center,
      ),
    );
  }
}
