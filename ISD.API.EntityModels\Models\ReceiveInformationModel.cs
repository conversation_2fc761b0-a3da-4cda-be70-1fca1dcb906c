﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ReceiveInformationModel", Schema = "MESP2")]
    public partial class ReceiveInformationModel
    {
        [Key]
        public Guid ReceiveInfoId { get; set; }
        [StringLength(50)]
        public string GoodsReceivedNote { get; set; }
        [StringLength(50)]
        public string YearReceive { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        public Guid? SlocId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ReceiveDate { get; set; }
        [StringLength(50)]
        public string MaterialType { get; set; }
        [StringLength(50)]
        public string ImportExportType { get; set; }
        [StringLength(50)]
        public string MaterialGroup { get; set; }
        [StringLength(50)]
        public string MaterialStatus { get; set; }
        public int? NSXHSD { get; set; }
        [StringLength(50)]
        public string SkinType { get; set; }
        [StringLength(50)]
        public string SkinColor { get; set; }
        [StringLength(50)]
        public string CaseCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool? Actived { get; set; }
        public Guid? RawMaterialCardId { get; set; }
        [StringLength(10)]
        public string TdsNumber { get; set; }
        [StringLength(50)]
        public string SoToKhai { get; set; }

        [ForeignKey("SlocId")]
        [InverseProperty("ReceiveInformationModel")]
        public virtual SlocModel Sloc { get; set; }
    }
}