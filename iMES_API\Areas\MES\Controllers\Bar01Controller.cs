﻿using ISD.API.Constant;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    public class Bar01Controller : ControllerBaseAPI
    {
        #region Get detail barcode PO
        /// <summary>API Search "Lấy thông tin Purchase Order khi quét Barcode"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/Bar/GetDetailBarcodePO
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 'purchaseOrderId': "565BD15A-43A9-4D74-B36C-0272C64F016E" 
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": [
        ///         {
        ///             "purchaseOrderCode": "4100001351",
        ///             "poItem": "10",
        ///             "material": "200000001"
        ///         }
        ///        ]
        ///     }
        /// </remarks>
        [HttpGet("GetDetailBarcodePO")]
        public async Task<IActionResult> GetDetailBarcodePO(Guid PurchaseOrderId)
        {
            var detail = await _context.PurchaseOrderDetailModel.FirstOrDefaultAsync(p => p.PurchaseOrderId == PurchaseOrderId);
            if (detail == null)
            {
                return Ok(new ApiResponse
                {
                    Code = 404,
                    IsSuccess = false,
                    Message = "Không tìm thấy thông tin Barcode!"
                });
            }
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new
                {
                    PurchaseOrderCode = detail.PurchaseOrderCode,
                    POItem = detail.POItem,
                    Material = int.Parse(detail.Material),
                },
                AdditionalData = null,
            });
        }
        #endregion

        #region Get detail barcode NVL
        /// <summary>API Search "Lấy thông tin NVL khi quét Barcode"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/Bar/GetDetailBarcodeNVL
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 'purchaseOrderId': "565BD15A-43A9-4D74-B36C-0272C64F016E" 
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": [
        ///         {
        ///         "material": 200000001,
        ///         "description": "Gỗ tràm tròn hoành (7079)cm dài 180Up",
        ///         "wbsso": "0/"
        ///         }
        ///        ]
        ///     }
        /// </remarks>
        [HttpGet("GetDetailBarcodeNVL")]
        public async Task<IActionResult> GetDetailBarcodeNVL(Guid PurchaseOrderId)
        {
            var detail = await _context.PurchaseOrderDetailModel.FirstOrDefaultAsync(p => p.PurchaseOrderId == PurchaseOrderId);

            if (detail == null)
            {
                return Ok(new ApiResponse
                {
                    Code = 404,
                    IsSuccess = false,
                    Message = "Không tìm thấy thông tin Barcode!"
                });
            }
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new
                {
                    Material = int.Parse(detail.Material),
                    Description = detail.ShortText,
                    WBSSO = string.Format("{0}/{1}", detail.WBSElement, detail.SDDocument),
                },
                AdditionalData = null,
            });
        }
        #endregion

        #region GET
        [HttpGet("GetQC")] 
        public ActionResult Get()
        {
            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);
            // danh sách lỗi
            var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
                                                         .Select(x => new CatalogDropdownVm
                                                         {
                                                             CatalogCode = x.CatalogCode,
                                                             CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
                                                         })
                                                         .ToList();

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new
                {
                    QCEmployeeList = QCEmployeeList,
                    ErrorList = ErrorList,
                }
            });
        }
        #endregion
    }
}
