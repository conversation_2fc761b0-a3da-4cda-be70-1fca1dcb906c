﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SpecificationsProductModel", Schema = "tSale")]
    public partial class SpecificationsProductModel
    {
        [Key]
        public Guid SpecificationsProductId { get; set; }
        public Guid ProductId { get; set; }
        public Guid SpecificationsId { get; set; }
        public string Description { get; set; }

        [ForeignKey("ProductId")]
        [InverseProperty("SpecificationsProductModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("SpecificationsId")]
        [InverseProperty("SpecificationsProductModel")]
        public virtual SpecificationsModel Specifications { get; set; }
    }
}