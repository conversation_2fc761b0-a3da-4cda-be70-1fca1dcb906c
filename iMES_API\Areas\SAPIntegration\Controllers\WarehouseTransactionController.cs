﻿using ISD.API.Constant.Common;
using ISD.API.Core;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class WarehouseTransactionController : ControllerBaseAPI
    {
        /// <summary><PERSON><PERSON><PERSON> chứng từ giao dịch kho</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/WarehouseTransaction/CancelTransaction
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        ///  
        ///     {
        ///       "headerTransactionId": "9F6F473E-0C50-425E-8461-8CF24CF7A11A",
        ///       "detailTransactions": [
        ///         {
        ///           "detailTransactionId": "261FEF7A-EBAB-47E6-8561-053B473A3944"
        ///         }
        ///       ]
        ///     }
        ///     
        /// OUT PUT
        /// 
        ///     {
        ///          "code": 200,
        ///          "isSuccess": true,
        ///          "message": "Hủy giao dịch kho thành công",
        ///          "data": null
        ///     }
        ///</remarks>
        [HttpPost("CancelTransaction")]
        public async Task<IActionResult> CancelTransaction([FromBody] CancelTransactionRequest request)
        {
            var dateNow = DateTime.Now;

            //Convert Id string to Guid
            var wtId = Guid.Parse(request.HeaderTransactionId);
            var listDetailTran = request.DetailTransactions.Select(x => Guid.Parse(x.DetailTransactionId));

            //Tìm danh sách chứng từ cần hủy theo ReferenceDocumentId
            var listWT = _context.WarehouseTransactionModel.Where(x => x.ReferenceDocumentId == wtId && listDetailTran.Contains(x.WarhouseTransactionId));
                                                            
            if (!listWT.Any())
                return Ok(new ApiResponse { Code = 400, Data = false, Message = string.Format(CommonResource.Msg_NotFound, "Chứng từ giao dịch kho") });

            //Loại giao dịch kho
            var movementType = listWT.FirstOrDefault().MovementType;

            //Chứng từ
            var referenceDocument = listWT.FirstOrDefault().ReferenceDocumentId;

            //Cập nhật chứng từ giao dịch kho
            foreach (var item in listWT)
            {
                item.Actived = false;
                item.LastEditTime = dateNow;
            }           

            //Chứng từ là nhập kho
            if(movementType == MovementType.DeliverOneStep)
            {
                //Hủy phiếu nhập kho
                var wareHouseTranfer = await _context.WarehouseTranferModel.FirstOrDefaultAsync(x => x.WarehouseTranferId == wtId);
                wareHouseTranfer.Actived = false;
                wareHouseTranfer.LastEditTime = dateNow;
            }          

            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, Data = true, IsSuccess = true, Message = string.Format(CommonResource.Msg_Succes, "Hủy chứng từ giao dịch kho") });
        }
    }
}
