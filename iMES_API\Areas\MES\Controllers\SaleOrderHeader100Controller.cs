﻿using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.ViewModels;
using ISD.Repositories;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;


namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class SaleOrderHeader100Controller : ControllerBaseAPI
    {
        [HttpPost("GetListSO")]
        public ActionResult GetListSO(SO100ReportSeachModel searchmodel)
        {
            int filteredResultsCount;
            int totalResultsCount;
            var model = searchmodel.paging;
            var searchViewModel = searchmodel.SearchModel;
            var query = _unitOfWork.SaleOrderHeader100Repository.Search(searchViewModel);
            var res = CustomSearchRepository.CustomSearchFunc<SaleOrderHeader100ViewModel>(model, out filteredResultsCount, out totalResultsCount, query, "STT");
            if (res != null && res.Count() > 0)
            {
                int i = model.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new
                {
                    draw = model.draw,
                    recordsTotal = totalResultsCount,
                    recordsFiltered = filteredResultsCount,
                    data = res
                }

            });
        }

        [HttpGet("ViewSO")]
        public ActionResult ViewSO(string Id)
        {
            // Lấy thông tin Sale Order
            var SaleOrder = _unitOfWork.SaleOrderHeader100Repository.GetSaleOrderHeader100BySaleOrder(Id);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = SaleOrder
            });
        }


        [HttpGet("ViewDetailSO")]
        public ActionResult ViewDetailSO(string VBELN, string POSNR)
        {
            // Lấy thông tin Sale Order
            var SaleOrder = _unitOfWork.SaleOrderHeader100Repository.GetSaleOrderItem100BySaleOrderAndLine(VBELN, POSNR);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = SaleOrder
            });
        }

        [HttpGet("SOItemList")]
        public ActionResult SaleOrderItemList(string VBELN)
        {

            //Danh sách sale order item
            var SaleOrderItemList = _unitOfWork.SaleOrderHeader100Repository.GetSaleOrderItem100BySaleOrder(VBELN);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = SaleOrderItemList
            });
        }

        [HttpGet("ScheduleLines")]
        public ActionResult ScheduleLines(string VBELN, string POSNR)
        {
            // Lấy thông tin Sale Order
            var SaleOrder = _unitOfWork.SaleOrderHeader100Repository.GetScheduleLines(VBELN, POSNR);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = SaleOrder
            });
        }

        [HttpGet("GetSaleOrderItem100BySaleOrder")]
        public ActionResult GetSaleOrderItem100BySaleOrder(string VBELN)
        {

            var data = (
                //SO Item 100
                from i in _context.SaleOrderItem100Model
                    //Product
                join p in _context.ProductModel on i.MATNR equals p.ProductCode
                //Company
                join c in _context.CompanyModel on p.CompanyId equals c.CompanyId
                //VBELN: SO Number
                //WERKS: Company Code
                where i.VBELN == VBELN && c.CompanyCode == i.WERKS
                //Lấy theo trạng thái đang actived
                && (i.isDeleted == null || i.isDeleted == false)
                select new SaleOrderItem100ViewModel
                {
                    //Sale order
                    VBELN = i.VBELN,
                    //Sale order item
                    POSNR = i.POSNR,
                    //Material Number
                    MATNR = i.MATNR,
                    //Item Description
                    ItemDescription = p.ProductName,
                    //UPMAT: mã cha
                    UPMAT = i.UPMAT,
                    //Short Text for Sales Order Item
                    ARKTX = i.ARKTX,
                    //Requirement type
                    BEDAE = i.BEDAE,
                    //Plant
                    WERKS = i.WERKS,
                    //Comulative Confirmed Quantity in Sales Unit
                    KBMENG = i.KBMENG,
                    //Comulative Confirmed Quantity in Base Unit
                    KLMENG = i.KLMENG,
                    //Special stock
                    SOBKZ = i.SOBKZ,
                    //Work Breakdown Structure Element (WBS Element)
                    PS_PSP_PNR = i.PS_PSP_PNR,
                    PS_PSP_PNR_OUTPUT = i.PS_PSP_PNR_OUTPUT,
                    //Numerator (factor) for conversion of sales quantity into SKU
                    UMVKZ = i.UMVKZ,
                    //Denominator (Divisor) for Conversion of Sales Qty into SKU
                    UMVKN = i.UMVKN,
                    //Based Unit
                    MEINS = i.MEINS,
                    //Reason for Rejection of Quotations and Sales Orders
                    ABGRU = i.ABGRU,
                    //Overall Processing Status of the SD Document Item
                    GBSTA = i.GBSTA,
                    //Created on
                    ERDAT = i.ERDAT,
                    //Created By
                    ERNAM = i.ERNAM,
                    //Time
                    ERZET = i.ERZET,
                    //Id
                    SO100ItemId = i.SO100ItemId,
                    //Danh sách nguyên vật liệu
                }).ToList();
            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = data });
        }

        [HttpGet("GetBOMDetailWithSaleOrder")]
        public ActionResult GetBOMDetailWithSaleOrder(string VBELN, string POSNR = null, string STLAN = null)
        {
            string sqlQuery = "[MES].[GetBomDetailWithSaleOrder100]";
            DbHelper dBHelper = new DbHelper(_context);
            Microsoft.Data.SqlClient.SqlParameter[] parameters = new Microsoft.Data.SqlClient.SqlParameter[] {
                dBHelper.CreateParameter("@VBELN", VBELN ?? (object)DBNull.Value, SqlDbType.NVarChar),
                dBHelper.CreateParameter("@POSNR", POSNR ?? (object)DBNull.Value, SqlDbType.NVarChar),
                dBHelper.CreateParameter("@STLAN", STLAN ?? (object)DBNull.Value, SqlDbType.NVarChar),

            };
            var ds = dBHelper.ExecuteDataset(sqlQuery, parameters);
            var BOMList = DataTableConverter.ConvertDataTable<BomDetailViewModel>(ds.Tables[0]);
            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = BOMList });
        }
    }
}