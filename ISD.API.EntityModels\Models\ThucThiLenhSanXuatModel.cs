﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ThucThiLenhSanXuatModel", Schema = "MES")]
    [Index("Summary", Name = "IX_ThucThiLenhSanXuatModel_Summary")]
    [Index("Summary", Name = "IX_ThucThiLenhSanXuatModel_Summary_TaskId")]
    public partial class ThucThiLenhSanXuatModel
    {
        public ThucThiLenhSanXuatModel()
        {
            StockReceivingDetailModel = new HashSet<StockReceivingDetailModel>();
        }

        [Key]
        public Guid TaskId { get; set; }
        public Guid? ParentTaskId { get; set; }
        [StringLength(4000)]
        public string Summary { get; set; }
        public Guid WorkFlowId { get; set; }
        public Guid? TaskStatusId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ReceiveDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EstimateEndDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        public string Property1 { get; set; }
        public string Property2 { get; set; }
        public string Property3 { get; set; }
        public string Property4 { get; set; }
        public string Property5 { get; set; }
        public string Property6 { get; set; }
        public Guid? StockId { get; set; }
        [StringLength(50)]
        public string SubtaskCode { get; set; }
        public Guid? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Qty { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        public Guid? Barcode { get; set; }
        public bool? IsWorkCenterCompleted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WorkCenterConfirmTime { get; set; }
        public int? ConfirmDatekey { get; set; }
        [StringLength(50)]
        public string ConfirmWorkCenter { get; set; }
        public Guid? ConfirmBy { get; set; }
        public bool? Actived { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? ToTaskId { get; set; }
        public Guid? ToStockId { get; set; }
        [StringLength(50)]
        public string ToStockCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? TransferTime { get; set; }
        [StringLength(500)]
        public string ProductAttributes { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ProductAttributesQty { get; set; }

        [ForeignKey("ParentTaskId")]
        [InverseProperty("ThucThiLenhSanXuatModel")]
        public virtual TaskModel ParentTask { get; set; }
        [InverseProperty("CustomerReferenceNavigation")]
        public virtual ICollection<StockReceivingDetailModel> StockReceivingDetailModel { get; set; }
    }
}