# MaiDao Function Testing Guide

This guide explains how to test the MaiDaoFunction without running the full mobile application.

## Test Files Created

### 1. Unit Tests
**File**: `test/repository/function/mai_dao_function_test.dart`
- Standard Flutter unit tests
- Tests helper methods and data models
- Can be run with `flutter test`

### 2. Integration Test Runner
**File**: `test_mai_dao.dart`
- Simple standalone test runner
- Tests actual API calls (if server is running)
- Shows test output with emojis and clear formatting

## How to Run Tests

### Option 1: Run Unit Tests (Recommended for CI/CD)
```bash
# Navigate to the project directory
cd TTF_MES_Mobile

# Run all tests
flutter test

# Run only Mai<PERSON>ao tests
flutter test test/repository/function/mai_dao_function_test.dart
```

### Option 2: Run Integration Test Runner
```bash
# Navigate to the project directory
cd TTF_MES_Mobile

# Run the standalone test runner
dart test_mai_dao.dart
```

## What Gets Tested

### ✅ Helper Methods (Always Work)
- `getStatusColor()` - Returns correct colors for each status
- `getStatusText()` - Returns correct Vietnamese text for each status

### ✅ Data Models (Always Work)
- `MaiDaoRecord` JSON serialization/deserialization
- `MaiDaoSearchModel` creation and property validation
- Edge cases for empty/null values

### ⚠️ API Functions (Require Server)
- `fetchMaiDaoList()` - Get list of MaiDao records
- `fetchMaiDaoDetail()` - Get single record by ID
- `createMaiDao()` - Create new record
- `updateMaiDao()` - Update existing record
- `saveMaiDao()` - Smart create/update based on ID
- `fetchEquipment()` - Get equipment suggestions
- `fetchMaterials()` - Get material suggestions
- `fetchEmployees()` - Get employee list
- `checkExistingRecord()` - Check for incomplete records

## Expected Results

### When Server is NOT Running:
```
🧪 Starting MaiDao Function Tests...

📋 Testing Helper Methods...
  Status Colors:
    Created: Color(0xffff9800)
    Confirmed: Color(0xff2196f3)
    Completed: Color(0xff4caf50)
    Cancelled: Color(0xfff44336)
  Status Texts:
    Created: Mới tạo
    Confirmed: Đã xác nhận
    Completed: Đã mài xong
    Cancelled: Đã hủy
  ✅ Helper methods test completed

📄 Testing Data Models...
  Original Record:
    ID: test-123
    Equipment: EQ001 - Test Equipment
    Material: MAT001 - Test Material
    Operation: Mài dao
    Status: Created
  JSON Serialization Test:
    ✅ Serialization successful
    ✅ Deserialization successful
    ✅ ID matches: true
    ✅ Equipment matches: true
    ✅ Status matches: true
  ✅ Data models test completed

🌐 Testing API Functions...
  Note: These tests will show connection errors if server is not running
  1. Testing fetchMaiDaoList...
    ⚠️  Expected error: Connection failed
  2. Testing fetchEquipment...
    ⚠️  Expected error: Connection failed
  [etc...]
```

### When Server IS Running:
```
🌐 Testing API Functions...
  1. Testing fetchMaiDaoList...
    ✅ API call successful
    📊 Found 5 records
    📄 First record: EQ001 - Created
  2. Testing fetchEquipment...
    📊 Found 15 equipment items
    🔧 EQ001: Máy mài dao CNC
    🔧 EQ002: Máy đắp mũi khoan
    🔧 EQ003: Máy mài lưỡi cắt
  [etc...]
```

## Test Configuration

### API Endpoints Tested:
- `GET /api/v1/MES/MaiDao/MaiDaoList` (POST with search model)
- `GET /api/v1/MES/MaiDao/{id}`
- `POST /api/v1/MES/MaiDao/CreateMaiDao`
- `PUT /api/v1/MES/MaiDao/{id}`
- `GET /api/v1/MES/MaiDao/GetEquipment`
- `GET /api/v1/MES/MaiDao/GetMaterials`
- `GET /api/v1/MES/MaiDao/GetEmployees`
- `GET /api/v1/MES/MaiDao/CheckExistingRecord`

### Test Data Used:
- **Company Code**: `1000`
- **Test Token**: `test_token_123`
- **Equipment Code**: `EQ001`
- **Search Term**: `search`

## Customizing Tests

### Change Test Configuration:
Edit the constants in `test_mai_dao.dart`:
```dart
const testToken = 'your_real_token_here';
const testCompanyCode = 'your_company_code';
const testEquipmentCode = 'your_equipment_code';
```

### Add New Test Cases:
Add new test functions to either file:
```dart
Future<void> testCustomScenario() async {
  print('🎯 Testing Custom Scenario...');
  
  try {
    // Your test code here
    final result = await MaiDaoFunction.someFunction();
    print('    ✅ Test passed: $result');
  } catch (e) {
    print('    ❌ Test failed: $e');
  }
}
```

## Benefits of This Testing Approach

### ✅ Quick Feedback
- Test core logic without app startup time
- Immediate feedback on API structure
- Easy to run in CI/CD pipelines

### ✅ Isolated Testing
- Test individual functions independently
- No UI dependencies
- No navigation or state management complexity

### ✅ Debug API Issues
- See exact error messages from API calls
- Verify request/response structure
- Test different scenarios quickly

### ✅ Development Workflow
- Test new features before UI implementation
- Verify API changes work correctly
- Quick regression testing

## Troubleshooting

### Common Issues:

1. **Import Errors**:
   - Make sure all model files exist in `lib/model/`
   - Check that function files exist in `lib/repository/function/`

2. **API Connection Errors**:
   - Normal when server is not running
   - Check server URL in function file
   - Verify authentication token

3. **JSON Serialization Errors**:
   - Check model `toJson()` and `fromJson()` methods
   - Verify all required fields are present

## Next Steps

1. Run the tests to verify current functionality
2. Add more specific test cases as needed
3. Integrate into your CI/CD pipeline
4. Use for regression testing when making changes 