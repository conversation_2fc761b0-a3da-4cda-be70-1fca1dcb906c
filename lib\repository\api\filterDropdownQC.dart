import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class FilterDropdownQC {
  static Future<http.Response> getFilterDropdownQC(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlQualityControl + 'GetQCFilter';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getDefaultKCSFilter(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlQualityControl + 'GetDefaultKCSFilter';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getDefaultQCMauFilter(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlQualityControl + 'GetDefaultQCMauFilter';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getDefaultQCHienTruongFilter(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlQualityControl + 'GetDefaultQCHienTruongFilter';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getDefaultQCSanPhamFilter(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlQualityControl + 'GetDefaultQCSanPhamFilter';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }
}
