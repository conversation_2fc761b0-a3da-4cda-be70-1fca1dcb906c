class GetInforMaterialUsedShift {
  int? code;
  bool? isSuccess;
  String? message;
  DataInforMaterialUsedShift? data;

  GetInforMaterialUsedShift(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetInforMaterialUsedShift.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataInforMaterialUsedShift.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataInforMaterialUsedShift {
  String? quantity;
  String? unit;

  DataInforMaterialUsedShift({this.quantity, this.unit});

  DataInforMaterialUsedShift.fromJson(Map<String, dynamic> json) {
    quantity = json['Quantity'];
    unit = json['Unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Quantity'] = quantity;
    data['Unit'] = unit;
    return data;
  }
}