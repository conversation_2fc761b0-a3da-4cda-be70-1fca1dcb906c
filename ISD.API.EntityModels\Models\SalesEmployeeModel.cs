﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SalesEmployeeModel", Schema = "tMasterData")]
    public partial class SalesEmployeeModel
    {
        public SalesEmployeeModel()
        {
            DeliveryModel = new HashSet<DeliveryModel>();
            PersonInChargeModel = new HashSet<PersonInChargeModel>();
            StockReceivingMasterModel = new HashSet<StockReceivingMasterModel>();
            TransferModel = new HashSet<TransferModel>();
        }

        [Key]
        [StringLength(50)]
        public string SalesEmployeeCode { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? StoreId { get; set; }
        public Guid? DepartmentId { get; set; }
        [StringLength(1000)]
        public string SalesEmployeeName { get; set; }
        [StringLength(100)]
        public string AbbreviatedName { get; set; }
        [StringLength(100)]
        public string Email { get; set; }
        [StringLength(100)]
        public string Phone { get; set; }
        [StringLength(50)]
        public string SerialTag { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        public int? LevelCode { get; set; }
        [StringLength(50)]
        public string StepCode { get; set; }
        [StringLength(4)]
        [Unicode(false)]
        public string SubGroup { get; set; }
        [StringLength(16)]
        [Unicode(false)]
        public string OrganizationalUnit_ORGEH { get; set; }
        [StringLength(16)]
        [Unicode(false)]
        public string Position_PLANS { get; set; }

        [ForeignKey("DepartmentId")]
        [InverseProperty("SalesEmployeeModel")]
        public virtual DepartmentModel Department { get; set; }
        [InverseProperty("SalesEmployeeCodeNavigation")]
        public virtual ICollection<DeliveryModel> DeliveryModel { get; set; }
        [InverseProperty("SalesEmployeeCodeNavigation")]
        public virtual ICollection<PersonInChargeModel> PersonInChargeModel { get; set; }
        [InverseProperty("SalesEmployeeCodeNavigation")]
        public virtual ICollection<StockReceivingMasterModel> StockReceivingMasterModel { get; set; }
        [InverseProperty("SalesEmployeeCodeNavigation")]
        public virtual ICollection<TransferModel> TransferModel { get; set; }
    }
}