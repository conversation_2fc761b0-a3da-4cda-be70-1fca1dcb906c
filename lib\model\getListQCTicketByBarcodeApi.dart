class GetListQCTicketByBarcode {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetListQCTicketByBarcode>? data;
  String? additionalData;

  GetListQCTicketByBarcode(
      {this.code,
        this.isSuccess,
        this.message,
        this.data,
        this.additionalData});

  GetListQCTicketByBarcode.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetListQCTicketByBarcode>[];
      json['data'].forEach((v) {
        data!.add(DataGetListQCTicketByBarcode.fromJson(v));
      });
    }
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class DataGetListQCTicketByBarcode {
  String? qualityControlId;
  String? saleOrgCode;
  String? workCenterCode;
  String? lsxdt;
  String? lsxsap;
  String? dsx;
  String? productCode;
  String? productName;
  String? confirmDate;
  String? qualityDate;
  bool? status;
  String? result;

  DataGetListQCTicketByBarcode(
      {this.qualityControlId,
        this.saleOrgCode,
        this.workCenterCode,
        this.lsxdt,
        this.lsxsap,
        this.dsx,
        this.productCode,
        this.productName,
        this.confirmDate,
        this.qualityDate,
        this.status,
        this.result});

  DataGetListQCTicketByBarcode.fromJson(Map<String, dynamic> json) {
    qualityControlId = json['qualityControlId'];
    saleOrgCode = json['saleOrgCode'];
    workCenterCode = json['workCenterCode'];
    lsxdt = json['lsxdt'];
    lsxsap = json['lsxsap'];
    dsx = json['dsx'];
    productCode = json['productCode'];
    productName = json['productName'];
    confirmDate = json['confirmDate'];
    qualityDate = json['qualityDate'];
    status = json['status'];
    result = json['result'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlId'] = qualityControlId;
    data['saleOrgCode'] = saleOrgCode;
    data['workCenterCode'] = workCenterCode;
    data['lsxdt'] = lsxdt;
    data['lsxsap'] = lsxsap;
    data['dsx'] = dsx;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['confirmDate'] = confirmDate;
    data['qualityDate'] = qualityDate;
    data['status'] = status;
    data['result'] = result;
    return data;
  }
}
