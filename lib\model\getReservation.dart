class GetReservation {
  int? code;
  bool? isSuccess;
  String? message;
  DataReservation? data;

  GetReservation({this.code, this.isSuccess, this.message, this.data});

  GetReservation.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataReservation.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class GetReservationItems {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataReservation>? data;

  GetReservationItems({
    this.code,
    this.isSuccess,
    this.message,
    this.data,
  });

  GetReservationItems.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = (json['data'] as List).map((i) => DataReservation.fromJson(i)).toList();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((i) => i.toJson()).toList();
    }
    return data;
  }
}

class DataReservation {
  String? reservationId;
  String? movementType;
  String? reservationCode;
  String? item;
  String? materialCode;
  String? plant;
  String? storageLocation;
  String? batch;
  double? reqQuantity;
  double? reqQuantityRnd;
  String? unit;
  String? riPlant;
  String? riStorageLocation;
  String? sosoLine;
  String? wbs;
  String? lsx;
  String? note;

  DataReservation({
    this.reservationId,
    this.movementType,
    this.reservationCode,
    this.item,
    this.materialCode,
    this.plant,
    this.storageLocation,
    this.batch,
    this.reqQuantity,
    this.reqQuantityRnd,
    this.unit,
    this.riPlant,
    this.riStorageLocation,
    this.sosoLine,
    this.wbs,
    this.lsx,
    this.note,
  });

  DataReservation.fromJson(Map<String, dynamic> json) {
    reservationId = json['reservationId'];
    movementType = json['movementType'];
    reservationCode = json['reservationCode'];
    item = json['item'];
    materialCode = json['materialCode'];
    plant = json['plant'];
    storageLocation = json['storageLocation'];
    batch = json['batch'];
    reqQuantity = json['reqQuantity'];
    reqQuantityRnd = json["reqQuantityRnd"];
    unit = json['unit'];
    riPlant = json['riPlant'];
    riStorageLocation = json['riStorageLocation'];
    sosoLine = json['sosoLine'];
    wbs = json['wbs'];
    lsx = json['lsx'];
    note = json['note'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reservationId'] = reservationId;
    data['movementType'] = movementType;
    data['reservationCode'] = reservationCode;
    data['item'] = item;
    data['materialCode'] = materialCode;
    data['plant'] = plant;
    data['storageLocation'] = storageLocation;
    data['batch'] = batch;
    data['reqQuantity'] = reqQuantity;
    data['reqQuantityRnd'] = reqQuantityRnd;
    data['unit'] = unit;
    data['riPlant'] = riPlant;
    data['riStorageLocation'] = riStorageLocation;
    data['sosoLine'] = sosoLine;
    data['wbs'] = wbs;
    data['lsx'] = lsx;
    data['note'] = note;
    return data;
  }
}
