import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path/path.dart';

class ImageFunction {
  static Future<bool> handleCameraPermission() async {
    var statusCamera = Platform.isAndroid ? await Permission.camera.request() : await Permission.camera.request();
    if (statusCamera != PermissionStatus.granted) {
      return false;
    } else {
      return true;
    }
  }

  static Future<bool> handlePhotosPermission() async {
    PermissionStatus statusGallery;

    // debugPrint(deviceInfo)
    if (Platform.isAndroid) {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;

      // statusGallery = await Permission.storage.request(); // Android <13

      // if (statusGallery == PermissionStatus.denied) {
      //   statusGallery = await Permission.photos.request(); // Android 13
      // }

      if (deviceInfo.version.sdkInt < 32) {
        statusGallery = await Permission.storage.request();
      } else {
        statusGallery = await Permission.photos.request();
      }
    } else {
      statusGallery = await Permission.photos.request();
    }

    if (statusGallery != PermissionStatus.granted) {
      return false;
    } else {
      return true;
    }
  }

  static Future<bool> handlePermission(bool isGallery) async {
    if (isGallery == true) {
      return await handlePhotosPermission();
    } else if (isGallery == false) {
      return await handleCameraPermission();
    } else {
      return false;
    }
  }

  static Future<File> saveImageMulti(String path) async {
    final directory = await getApplicationDocumentsDirectory();
    final nameImage = basename(path);
    final imageMulti = File('${directory.path}/$nameImage');
    return File(path).copy(imageMulti.path);
  }

  static Future<File> saveImage(String imagePath) async {
    final directory = await getApplicationDocumentsDirectory();
    final name = basename(imagePath);
    final image = File('${directory.path}/$name');
    return File(imagePath).copy(image.path);
  }
}
