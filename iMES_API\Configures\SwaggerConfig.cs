﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System.IO;
using System.Reflection;
using System;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace iMES_API.Configures
{
    public class SwaggerConfig
    {
        public static void Configure(IServiceCollection services, IConfiguration configuration)
        {
            services.AddSwaggerGen(option =>
            {
                option.SwaggerDoc("mobile", new OpenApiInfo
                {
                    Title = "TTF - iMes Mobile API",
                    Version = "v1",
                    Description = "<hr>" +
                    "<h2>*** Note *** </h2></br>" +
                    "<h2>- Với API TTF - iMes Mobile cần nhập Version là 1</h2>"
                });
                option.SwaggerDoc("web", new OpenApiInfo
                {
                    Title = "ISD - Frontend API",
                    Version = "v2",
                    Description = "<hr>" +
                    "<h2>*** Note *** </h2></br>" +
                    "<h2>- Với API ISD - Frontend cần nhập Version là 2</h2>"
                });
                option.SwaggerDoc("integrate", new OpenApiInfo
                {
                    Title = "ISD - Citek Cloud Data Integration",
                    Version = "v3",
                    Description = "<hr>" +
                    "<h2>*** Note *** </h2></br>" +
                    "<h2>- Với ISD - Citek Cloud Data Integration cần nhập Version là 3</h2>"
                });

                option.OperationFilter<AddAuthorizationHeader>();

                var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                option.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));      
            });
        }

        public class AddAuthorizationHeader : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                operation.Parameters?.Add(new OpenApiParameter
                {
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Description = "Bearer \"{Token}\"",
                    Required = false,
                    Schema = new OpenApiSchema
                    {
                        Type = "string"
                    }
                });
            }
        }
    }
}
