class ListWarehouseTranfer {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataListWarehouseTranfer>? data;
  AdditionalData? additionalData;
  ListWarehouseTranfer({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  ListWarehouseTranfer.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataListWarehouseTranfer>[];
      json['data'].forEach((v) {
        data!.add(DataListWarehouseTranfer.fromJson(v));
      });
    }
    additionalData = json['additionalData'] != null
        ? AdditionalData.fromJson(json['additionalData'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (additionalData != null) {
      data['additionalData'] = additionalData!.toJson();
    }
    return data;
  }
}

class DataListWarehouseTranfer {
  int? stt;
  String? reservationId;
  String? plant;
  String? storageLocation;
  String? reservationNumber;
  String? statusReservation;
  String? productCodeAndName;
  double? quantity;
  String? unit;
  String? so;
  String? wbs;
  String? lsx;
  String? riPlant;
  String? riStorageLocation;
  String? item;
  String? statusWarehouse;
  String? batchNumber;
  String? movementType;
  String? reqDate;

  DataListWarehouseTranfer(
      {
        this.stt,
        this.reservationId,
        this.plant,
        this.storageLocation,
        this.reservationNumber,
        this.statusReservation,
        this.productCodeAndName,
        this.quantity,
        this.unit,
        this.so,
        this.wbs,
        this.lsx,
        this.riPlant,
        this.riStorageLocation,
        this.item,
        this.statusWarehouse,
        this.batchNumber,
        this.movementType,
        this.reqDate
      });

  DataListWarehouseTranfer.fromJson(Map<String, dynamic> json) {
    stt = json['stt'];
    reservationId = json['reservationId'];
    plant = json['plant'];
    storageLocation = json['storageLocation'];
    reservationNumber = json['reservationNumber'];
    statusReservation = json['statusReservation'];
    productCodeAndName = json['productCodeAndName'];
    quantity = json['quantity'];
    unit = json['unit'];
    so = json['so'];
    wbs = json['wbs'];
    lsx = json['lsx'];
    riPlant = json['riPlant'];
    riStorageLocation = json['riStorageLocation'];
    item = json['item'];
    statusWarehouse = json['statusWarehouse'];
    batchNumber = json['batchNumber'];
    movementType = json['movementType'];
    reqDate = json['reqDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stt'] = stt;
    data['reservationId'] = reservationId;
    data['plant'] = plant;
    data['storageLocation'] = storageLocation;
    data['reservationNumber'] = reservationNumber;
    data['statusReservation'] = statusReservation;
    data['productCodeAndName'] = productCodeAndName;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['so'] = so;
    data['wbs'] = wbs;
    data['lsx'] = lsx;
    data['riPlant'] = riPlant;
    data['riStorageLocation'] = riStorageLocation;
    data['item'] = item;
    data['statusWarehouse'] = statusWarehouse;
    data['batchNumber'] = batchNumber;
    data['movementType'] = movementType;
    data['reqDate'] = reqDate;
    return data;
  }
}
class AdditionalData {
  int? draw;
  int? recordsTotal;
  int? recordsFiltered;

  AdditionalData({this.draw, this.recordsTotal, this.recordsFiltered});

  AdditionalData.fromJson(Map<String, dynamic> json) {
    draw = json['draw'];
    recordsTotal = json['recordsTotal'];
    recordsFiltered = json['recordsFiltered'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['draw'] = draw;
    data['recordsTotal'] = recordsTotal;
    data['recordsFiltered'] = recordsFiltered;
    return data;
  }
}