# Core Features

This document outlines the core features and functionality that serve as the foundation for the TTF MES Mobile application.

## Authentication and User Management

The application implements a secure authentication system to ensure only authorized users can access manufacturing data.

### Login System

Authentication is implemented in `lib/page/Login.dart` with the following features:

- Username and password authentication
- Token-based authentication
- Secure storage of credentials
- Session timeout handling
- Automatic logout on session expiration

### User Model

The user model (`lib/model/userModel.dart`) stores essential user information:

- User ID
- Username
- Roles and permissions
- Plant/location assignment
- Authentication token

### Security Features

- Secure storage of authentication tokens using `flutter_secure_storage`
- Token expiration and refresh mechanism
- Protection against unauthorized access
- Session timeout for inactive users

### Code Example

```dart
// Authentication flow
Future<void> login() async {
  // Call API for authentication
  final response = await repository.login(username, password);
  
  // Parse response to user model
  final user = UserModel.fromJson(response);
  
  // Store authentication tokens securely
  await SecureStorage.setString("user", j<PERSON><PERSON><PERSON><PERSON>(user), null);
  
  // Navigate to main page
  NavigationService.instance.navigationKey!.currentState!
      .pushReplacementNamed('/mainPage', arguments: ScreenArgumentMainPage(user.data));
}
```

## Navigation and Routing

The application uses Flutter's named route navigation system, with a centralized route configuration.

### Route Configuration

Routes are defined in `lib/route/route.dart` and include:

- Named routes for all screens
- Route arguments for passing data between screens
- Route guards for authenticated routes

### Navigation Service

The application implements a navigation service (`lib/service/navigatorService.dart`) that provides:

- Centralized navigation handling
- Access to navigation from non-context classes
- Navigation history tracking

### Screen Arguments

Dedicated classes in the `lib/screenArguments/` directory handle passing data between screens:

- Type-safe data passing
- Parameter validation
- Organized argument structures for different screens

### Navigation Patterns

- Bottom navigation for main app sections
- Stack-based navigation for drill-down flows
- Modal screens for focused tasks
- Dialog-based interactions for confirmations

### Code Example

```dart
// Navigate to a screen using the navigation service
NavigationService.instance.navigationKey!.currentState!.pushNamed(
  '/DetailReport',
  arguments: ScreenArgumentDetailReport(getData, token, dateTimeOld)
);
```

## Storage and Data Management

The application uses multiple storage mechanisms for different types of data:

### Secure Storage

Implemented in `lib/Storage/storageSecureStorage.dart` using `flutter_secure_storage`:

- Storage of sensitive data (authentication tokens, credentials)
- Encrypted data store
- OS-level security

### Shared Preferences

Implemented in `lib/Storage/storageSharedPreferences.dart` using `shared_preferences`:

- Storage of application settings
- User preferences
- Non-sensitive cached data
- First-run detection

### Memory Cache

- In-memory storage for frequently accessed data
- Temporary data storage

### File Storage

- Local storage for downloaded files
- Image caching
- Temporary file handling

### Code Example

```dart
// Secure Storage
await SecureStorage.setString("user", jsonEncode(user), null);
final userString = await SecureStorage.getString("user", null);

// Shared Preferences
await StorageSharedPreferences.init();
StorageSharedPreferences.setBool('first_run', false);
final isFirstRun = StorageSharedPreferences.getBool('first_run') ?? true;
```

## Connectivity Handling

The application is designed to handle varying connectivity conditions in manufacturing environments:

### Connectivity Detection

Using `connectivity_plus` package to:

- Detect network status (WiFi, cellular, none)
- Monitor network changes
- Adjust application behavior based on connectivity

### Offline Capability

- Critical features work offline
- Data caching for offline access
- Queue operations for later synchronization

### Error Handling

- Graceful handling of network errors
- User-friendly error messages
- Retry mechanisms for failed network operations

### Synchronization

- Data synchronization when connection is restored
- Conflict resolution for concurrent edits
- Background synchronization to minimize user disruption

### Code Example

```dart
// Check connectivity before operation
final connectivityResult = await Connectivity().checkConnectivity();
if (connectivityResult == ConnectivityResult.none) {
  // Handle offline mode
  showOfflineMessage();
  saveOperationToQueue();
} else {
  // Proceed with online operation
  try {
    final result = await repository.sendData(data);
    handleSuccess(result);
  } catch (e) {
    handleError(e);
  }
}
```

## Error Handling and Logging

The application implements comprehensive error handling and logging mechanisms:

### Error Handling

- Try-catch blocks for potential failure points
- Graceful degradation of functionality
- User-friendly error messages
- Error recovery mechanisms

### Error Display

Consistent error presentation using:
- Toast messages for non-critical errors
- Modal dialogs for blocking errors
- Inline error messages in forms
- Error screens for critical failures

### Logging

- Debug logging during development
- Error logging for troubleshooting
- User action logging for analysis
- Performance monitoring

### Code Example

```dart
try {
  final result = await repository.fetchData();
  updateUI(result);
} catch (e) {
  debugPrint('Error fetching data: ${e.toString()}');
  showErrorMessage('Unable to load data. Please try again.');
}
```

These core features provide the foundation for all other functionality in the TTF MES Mobile application, ensuring a robust, secure, and user-friendly experience for manufacturing users. 