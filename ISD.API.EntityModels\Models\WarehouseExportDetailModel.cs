﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseExportDetailModel", Schema = "MESP2")]
    public partial class WarehouseExportDetailModel
    {
        [Key]
        public Guid WarhouseExportId { get; set; }
        [Key]
        [StringLength(50)]
        public string SOWBS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }

        [ForeignKey("WarhouseExportId")]
        [InverseProperty("WarehouseExportDetailModel")]
        public virtual WarehouseExportModel WarhouseExport { get; set; }
    }
}