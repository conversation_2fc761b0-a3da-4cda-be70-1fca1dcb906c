{
  "JsonWebTokenKeys": {
    "ValidateIssuerSigningKey": true,
    "IssuerSigningKey": "64A63153-11C1-4919-9133-EFAF99A9B456",
    "ValidateIssuer": true,
    "ValidIssuer": "https://localhost:44390",
    "ValidateAudience": true,
    "ValidAudience": "https://localhost:44390",
    "RequireExpirationTime": true,
    "ValidateLifetime": true
  },
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "common",
    "CallbackPath": "/signin-oidc"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "DEFAUTL_MVC_HOST": "https://localhost:44331",
  "DEFAUTL_USERNAME": "sysadmin",
  "DEFAUTL_PASSWORD": "M2aH48iNXV",
  "DEFAUTL_SALE_ORG": "1000",
  "DEFAUTL_COMPANYCODE": "1000",

  "OneSignal": {
    "AppId": "************************************",
    "AppSecret": "MzE4YjJmNTctMWFkMS00MDAyLThhNDAtMWQzN2E3ZGU2ZWVl" // Rest API Key
  },

  "SAP": {
    "Account": {
      "API": {
        // DEV
        "Username300": "ttf_mes",
        "Password300": "*********",

        // PRD
        "Username": "ttf_rv",
        "Password": "V9JQqEHz0Sl0hvtV2Xca"
      }
    },
    "PRD": {
      "SAPname": "PRD",
      "SAPusername": "ttf_rv",
      "SAPpassword": "V9JQqEHz0Sl0hvtV2Xca",
      "SAPclient": "800",
      "SAPlanguage": "EN",
      "SAPappServerHost": "*********",
      "SAPsystemNumber": "02",
      "SAPmaxPoolSize": "20",
      "SAPidleTimeout": "10",
      "SAPsapRouter": ""
    },
    "DEV": {
      "SAPname": "DEV",
      "SAPusername": "ttf_rv",
      "SAPpassword": "ttf@270922",
      "SAPclient": "300",
      "SAPlanguage": "EN",
      "SAPappServerHost": "*********",
      "SAPsystemNumber": "01",
      "SAPmaxPoolSize": "20",
      "SAPidleTimeout": "10",
      "SAPsapRouter": ""
    }
  },
  "AllowedHosts": "*",

  //"Is300": true,
  "Is300": false,

  "ConnectionStrings": {
    //"DefaultConnection": "Data Source=.\\SQLEXPRESSDEV;Initial Catalog=TTF_MES_DEV;User ID=sa;Password=********",

    "DefaultConnection_PRD": "Data Source=*********;Initial Catalog=TTF_MES;User ID=imes;Password=********",

    //QAS
    //"DefaultConnection": "Data Source=*********;Initial Catalog=TTF_MES_DEV;User ID=imes;Password=********"

    //PRD WARNING WHEN USAGE
    "DefaultConnection": "Data Source=*********;Initial Catalog=TTF_MES;User ID=imes;Password=********"

  },
  "AllowedOrigins": ";https://localhost:44387;http://localhost:54103;https://localhost:44390;https://localhost:44331;https://localhost:3000;http://localhost:3000;http://localhost:3004;http://localhost:3005;http://*************:3002;https://localhost:44326",
  //"DocumentDomain": "D:/WebData/imes.isdcorp.vn/",
  //"DocumentDomain": "E:/_WORKING/TTF_DEV/iMES_WebData/",
  //"DocumentDomain": "E:/_WORKING/TTF_DEV/iMES/SourceCode/ISD.Admin/",
  "DocumentDomain": "E:/_WORKING/TTF_DEV/iMES/SourceCode/ISD.Admin/", // PC T9
  "SwaggerAccount": {
    "Username": "ISDSwagger",
    "Password": "isdcorp686868"
  }
}
 