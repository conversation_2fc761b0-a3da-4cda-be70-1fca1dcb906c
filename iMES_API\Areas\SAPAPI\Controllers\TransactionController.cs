﻿using iMES_API.ServiceExtensions;
using iMES_API.ViewModel.SAP;
using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.Core;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SapNwRfc;
using SapNwRfc.Pooling;
using SAPWarehouseReceive;
using SAPWarehouseTransaction;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPAPI.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]
    public class TransactionController : ControllerBaseAPI
    {
        private IConfiguration _configuration;
        private SapConnectService _sapConnectService;
        private ISapPooledConnection _sapPooledConnection;

        public TransactionController(
                IConfiguration configuration,
                SapConnectService sapConnectService,
                ISapPooledConnection sapPooledConnection
            )
        {
            _configuration = configuration;
            _sapConnectService = sapConnectService;
            _sapPooledConnection = sapPooledConnection;
        }

        [HttpGet("GetQuantityReceiveToSAP")]
        public IActionResult GetQuantityReceive([FromQuery] ZFM_MES_PURCHASE_ORDER syncModel)
        {
            var querySAP = _unitOfWork.SAPAPIRepository.SyncQuantityReceiveToSAP(syncModel);

            var listDetail = querySAP.Result.ZFM_MES_PURCHASE_ORDERResponse.LS_OUTPUT.LIST_DETAIL.Sum(x => x.CUMULATIVE_QUANTITY);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = listDetail
            });
        }

        [AllowAnonymous]
        [HttpGet("TestCallSAP")]
        public IActionResult TestCallSAP([FromQuery] string testParam)
        {
            string tableName;
            DataTable adoTable;
            tableName = "ZTB_INV";
            //tableName = "AUFK"; // Error: SapNwRfc.Exceptions.SapException: 'SAP RFC Error: RFC_ABAP_EXCEPTION with message:  Number:000'
            adoTable = new DataTable(tableName);
            try
            {
                //// Option 1: Use SapConnectService
                //var connection = _sapConnectService.GetSapConnection();
                //var function = connection.CreateFunction("RFC_READ_TABLE");
                //var result = function.Invoke<SapOutput>(new SapInputParam
                //{
                //    QUERY_TABLE = tableName,
                //    DELIMITER = "|",
                //    ROWCOUNT = "10"
                //});

                //Option 2: Use SapPooledConnection
                var result = _sapPooledConnection.InvokeFunction<SapOutput>("RFC_READ_TABLE", new SapInputParam
                {
                    QUERY_TABLE = tableName,
                    DELIMITER = "|",
                    ROWCOUNT = "10"
                });

                var sapTable = result.DATA;

                var sapFields = result.FIELDS;

                foreach (var row in sapFields)
                {
                    adoTable.Columns.Add(row.FIELDNAME, typeof(string));
                }

                foreach (var row in sapTable)
                {
                    var values = row.WA.Split('|');
                    adoTable.Rows.Add(values);
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = adoTable
                });
            }
            catch (Exception ex)
            {
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = ex.Message
                });

            }

        }


    }


}
