﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ModuleModel", Schema = "pms")]
    public partial class ModuleModel
    {
        public ModuleModel()
        {
            MenuModel = new HashSet<MenuModel>();
        }

        [Key]
        public Guid ModuleId { get; set; }
        [StringLength(100)]
        public string ModuleName { get; set; }
        [StringLength(100)]
        public string Url { get; set; }
        public bool? isSystemModule { get; set; }
        public int? OrderIndex { get; set; }
        [StringLength(100)]
        public string Icon { get; set; }
        [StringLength(100)]
        public string ImageUrl { get; set; }
        [StringLength(1000)]
        public string Description { get; set; }
        [StringLength(4000)]
        public string Details { get; set; }

        [InverseProperty("Module")]
        public virtual ICollection<MenuModel> MenuModel { get; set; }
    }
}