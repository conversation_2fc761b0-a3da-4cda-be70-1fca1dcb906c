import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/maiDaoModel.dart';
import '../../urlApi/urlApi.dart';
import '../function/commonFunction.dart';

class MaiDaoFunction {
  // Flag to switch between mock and API data
  static const bool useMockData = false; // Set to false for production API calls

  // Add URL variable
  static const String _baseEndpoint = '/api/v1/MES/';
  static const String _maiDaoEndpoint = '${_baseEndpoint}MaiDao/';
  static const String _equipmentEndpoint = '${_maiDaoEndpoint}GetEquipment';
  static const String _materialEndpoint = '${_maiDaoEndpoint}GetMaterials';
  static const String _employeeEndpoint = '${_maiDaoEndpoint}GetEmployees';

  // Status helper methods - moved here to avoid duplication
  static Color getStatusColor(String status) {
    switch (status) {
      case 'Created':
        return Colors.orange;
      case 'Confirmed':
        return Colors.blue;
      case 'Completed':
        return Colors.green;
      case 'Cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  static String getStatusText(String status) {
    switch (status) {
      case 'Created':
        return 'Mới tạo';
      case 'Confirmed':
        return 'Đã xác nhận';
      case 'Completed':
        return 'Đã hoàn thành';
      case 'Cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  }

  // Helper method to get base URL based on environment
  static Future<String> _getBaseUrl() async {
    final environment = await SecureStorage.getString("environment", null);
    return environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
  }

  // Mock in-memory storage
  static final List<MaiDaoRecord> _mockDb = [
    MaiDaoRecord(
      id: "1",
      date: "2024-03-25",
      equipmentCode: "EQ001",
      equipmentName: "Máy mài 001",
      materialCode: "MAT001",
      materialName: "Dao cụ 001",
      operationType: "Mài dao",
      employeeCodes: "NV001,NV002",
      employeeNames: "Nguyễn Văn A, Trần Thị B",
      note: "Hoàn thành đúng hạn",
      createdDate: "2024-03-25T08:00:00",
      createBy: "user1",
      updatedDate: "2024-03-25T10:30:00",
      updateBy: "user1",
      companyCode: "1000",
    ),
    // Add more mock records as needed...
  ];

  // Public Interface - These functions will use either mock or API based on useMockData flag
  static Future<MaiDaoModel?> fetchMaiDaoList(String token, MaiDaoSearchModel searchModel) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maiDaoEndpoint}MaiDaoList';

      debugPrint('MaiDaoFunction.fetchMaiDaoList | url: $url');
      debugPrint('MaiDaoFunction.fetchMaiDaoList | searchModel: ${jsonEncode(searchModel.toJson())}');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: jsonEncode(searchModel.toJson()),
      );

      debugPrint('MaiDaoFunction.fetchMaiDaoList | response.statusCode: ${response.statusCode}');
      debugPrint('MaiDaoFunction.fetchMaiDaoList | response.body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          // Transform the API response to match MaiDaoModel structure
          final transformedResponse = {'data': jsonResponse['data'], 'message': jsonResponse['message'], 'status': jsonResponse['isSuccess']};

          return MaiDaoModel.fromJson(transformedResponse);
        } else {
          debugPrint('API response not successful: ${jsonResponse['message']}');
          return MaiDaoModel(data: [], message: jsonResponse['message'], status: false);
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('Error fetching maidao list: $e');
      return null;
    }
  }

  static Future<MaiDaoRecord?> fetchMaiDaoDetail(String token, String id) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_maiDaoEndpoint$id';
      debugPrint('MaiDaoFunction.fetchMaiDaoDetail | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return MaiDaoRecord.fromJson(jsonResponse['data']);
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('MaiDaoFunction.fetchMaiDaoDetail | error: $e');
      return null;
    }
  }

  static Future<bool> createMaiDao(String token, MaiDaoRecord record) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maiDaoEndpoint}CreateMaiDao';
      debugPrint('MaiDaoFunction.createMaiDao | url: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: json.encode(record.toJson()),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse['isSuccess'] == true;
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return false;
    } catch (e) {
      debugPrint('MaiDaoFunction.createMaiDao | error: $e');
      return false;
    }
  }

  static Future<bool> updateMaiDao(String token, MaiDaoRecord record) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_maiDaoEndpoint${record.id}';
      debugPrint('MaiDaoFunction.updateMaiDao | url: $url');

      final response = await http.put(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: json.encode(record.toJson()),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse['isSuccess'] == true;
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return false;
    } catch (e) {
      debugPrint('MaiDaoFunction.updateMaiDao | error: $e');
      return false;
    }
  }

  static Future<bool> saveMaiDao(String token, MaiDaoRecord record) {
    debugPrint("--- MaiDaoFunction.saveMaiDao | useMockData: $useMockData");
    debugPrint("Operation: ${record.id == null || record.id!.isEmpty ? 'Create' : 'Update'}");

    if (record.id == null || record.id!.isEmpty) {
      return createMaiDao(token, record);
    } else {
      return updateMaiDao(token, record);
    }
  }

  static Future<List<EquipmentItem>> fetchEquipment(String token, String companyCode, String searchTerm) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_equipmentEndpoint?companyCode=$companyCode&searchTerm=$searchTerm';
      debugPrint('MaiDaoFunction.fetchEquipment | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return (jsonResponse['data'] as List)
              .map((item) => EquipmentItem(
                    equipmentCode: item['equipmentCode'],
                    equipmentName: item['equipmentName'],
                  ))
              .toList();
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return [];
    } catch (e) {
      debugPrint('MaiDaoFunction.fetchEquipment | error: $e');
      return [];
    }
  }

  static Future<List<MaterialItem>> fetchMaterials(String token, String companyCode, String searchTerm) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_materialEndpoint?companyCode=$companyCode&searchTerm=$searchTerm';
      debugPrint('MaiDaoFunction.fetchMaterials | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return (jsonResponse['data'] as List)
              .map((item) => MaterialItem(
                    materialCode: item['materialCode'],
                    materialName: item['materialName'],
                  ))
              .toList();
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return [];
    } catch (e) {
      debugPrint('MaiDaoFunction.fetchMaterials | error: $e');
      return [];
    }
  }

  static Future<List<EmployeeRecord>> fetchEmployees(String token, String companyCode) async {
    try {
      final response = await CommonFunction.fetchEmployees(token, companyCode);
      if (response.employees != null) {
        return response.employees!
            .map((emp) => EmployeeRecord(
                  employeeId: emp.employeeId,
                  employeeCode: emp.employeeId,
                  employeeName: emp.employeeName,
                ))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('MaiDaoFunction.fetchEmployees | error: $e');
      return [];
    }
  }

  static Future<MaiDaoRecord?> checkExistingRecord(String token, String equipmentCode, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maiDaoEndpoint}CheckExistingRecord?equipmentCode=$equipmentCode&companyCode=$companyCode';
      debugPrint('MaiDaoFunction.checkExistingRecord | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return MaiDaoRecord.fromJson(jsonResponse['data']);
        }
      }

      debugPrint('API Info: No existing incomplete record found');
      return null;
    } catch (e) {
      debugPrint('MaiDaoFunction.checkExistingRecord | error: $e');
      return null;
    }
  }

  static Future<ProductInfo?> fetchProductByCode(String token, String productCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_baseEndpoint}Common/GetProductLatestByCode?productCode=$productCode';
      debugPrint('MaiDaoFunction.fetchProductByCode | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('MaiDaoFunction.fetchProductByCode | response.statusCode: ${response.statusCode}');
      debugPrint('MaiDaoFunction.fetchProductByCode | response.body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return ProductInfo.fromJson(jsonResponse['data']);
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('MaiDaoFunction.fetchProductByCode | error: $e');
      return null;
    }
  }
}

// Helper classes for the Employee response
class EmployeeResponse {
  final List<Employee>? employees;
  final String? message;
  final bool status;

  EmployeeResponse({this.employees, this.message, this.status = true});

  factory EmployeeResponse.fromJson(Map<String, dynamic> json) {
    return EmployeeResponse(
      employees: (json['data'] as List?)?.map((e) => Employee.fromJson(e)).toList(),
      message: json['message'],
      status: json['status'] ?? true,
    );
  }
}

class Employee {
  final String? employeeId;
  final String? employeeName;

  Employee({this.employeeId, this.employeeName});

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      employeeId: json['employeeId'],
      employeeName: json['employeeName'],
    );
  }
}

// Helper class for Product information
class ProductInfo {
  final String? productCode;
  final String? productName;

  ProductInfo({this.productCode, this.productName});

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      productCode: json['productCode'],
      productName: json['productName'],
    );
  }
}
