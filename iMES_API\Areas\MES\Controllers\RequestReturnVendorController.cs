﻿using ISD.API.Constant;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using ZXing;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class RequestReturnVendorController : ControllerBaseAPI
    {

        public RequestReturnVendorController()
        {
        }


        [HttpGet("GetReturnRequest")]
        public async Task<ActionResult> GET([FromQuery] string requestReturnVendorId)
        {

            var idGuid = Guid.Parse(requestReturnVendorId);

            var ret = (
                        from master in _context.RequestReturnVendorModel
                            //join b in _context.RequestReturnVendorDetailModel on master.Id equals b.RequestReturnVendorModelId
                        join d in _context.AccountModel on master.CreatorId equals d.AccountId
                        join e in _context.AccountModel on master.WarehouseStaffId equals e.AccountId into e1
                        from e in e1.DefaultIfEmpty()
                        join f in _context.AccountModel on master.AccountantId equals f.AccountId into f1
                        from f in f1.DefaultIfEmpty()
                        where master.Id == idGuid
                        select new RequestReturnVendorViewModel
                        {
                            Id = master.Id,
                            VendorCode = master.VendorCode,
                            VendorName = master.VendorName,
                            RequestReturnVendorCode = master.RequestReturnVendorCode,
                            CreateTime = master.CreateTime,
                            CreatorId = master.CreatorId,
                            CreatorName = d.FullName,
                            WarehouseStaffId = master.WarehouseStaffId,
                            WarehouseStaffName = e.FullName,
                            WarehouseVerifyTime = master.WarehouseVerifyTime,
                            AccountantId = master.AccountantId,
                            AccountantName = f.FullName,
                            AccountantVerifyTime = master.AccountantVerifyTime,
                            ReturnType = master.ReturnType,
                            DataList = (from detail in _context.RequestReturnVendorDetailModel
                                        where detail.RequestReturnVendorModelId == master.Id
                                        select new DataTraHangNCCViewModel
                                        {
                                            Stt = detail.Stt,
                                            Id = detail.Id,
                                            MaNVL = detail.MaNVL,
                                            TenNVL = detail.TenNVL,
                                            PoNumber = detail.PoNumber,
                                            SoLuongTra = detail.SoLuongTra.ToString(),
                                            SoLuongThucTe = detail.SoLuongThucTe.ToString(),
                                            SoLuongPO = detail.SoLuongPO.ToString(),
                                            Dvt = detail.Dvt,
                                        }).ToList()
                        }).FirstOrDefault();


            if (ret != null)
            {
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = ret,
                });
            }
            else
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu yêu cầu"
                });
            }
        }


        [HttpGet("GetListReturnRequest")]
        public async Task<ActionResult> GET([FromForm] List<RequestReturnVendorViewModel> searchModel)
        {

            var ret = (
                        from a in _context.RequestReturnVendorModel
                        join b in _context.RequestReturnVendorDetailModel on a.Id equals b.RequestReturnVendorModelId
                        join d in _context.AccountModel on a.CreatorId equals d.AccountId
                        join e in _context.AccountModel on a.WarehouseStaffId equals e.AccountId into e1
                        from e in e1.DefaultIfEmpty()
                        join f in _context.AccountModel on a.AccountantId equals f.AccountId into f1
                        from f in f1.DefaultIfEmpty()
                        where a.CompanyCode == CurrentUser.CompanyCode
                        select new { a, b, d, e, f }
                    ).AsEnumerable()
                    .GroupBy(x => new { x.a.Id, x.a.VendorCode, x.a.VendorName, x.a.RequestReturnVendorCode, x.a.CreateTime, x.a.CreatorId, x.d.FullName, x.a.WarehouseStaffId, WarehouseStaffName = x.e?.FullName, x.a.WarehouseVerifyTime, x.a.AccountantId, AccountantName = x.f?.FullName, x.a.AccountantVerifyTime, x.a.ReturnType })
                    .OrderBy(g => g.Key.CreateTime)
                    .Select(g => new RequestReturnVendorViewModel
                    {
                        Id = g.Key.Id,
                        VendorCode = g.Key.VendorCode,
                        VendorName = g.Key.VendorName,
                        RequestReturnVendorCode = g.Key.RequestReturnVendorCode,
                        CreateTime = g.Key.CreateTime,
                        CreatorId = g.Key.CreatorId,
                        CreatorName = g.Key.FullName,
                        WarehouseStaffId = g.Key.WarehouseStaffId,
                        WarehouseStaffName = g.Key.WarehouseStaffName,
                        WarehouseVerifyTime = g.Key.WarehouseVerifyTime,
                        AccountantId = g.Key.AccountantId,
                        AccountantName = g.Key.AccountantName,
                        AccountantVerifyTime = g.Key.AccountantVerifyTime,
                        ReturnType = g.Key.ReturnType,
                        ListPo = string.Join(", ", g.Select(x => x.b.PoNumber).Distinct())
                    })
                    .OrderByDescending(x => x.CreateTime)
                    .ToList();



            if (ret == null || ret.Count() > 0)
            {
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = ret,
                });
            }
            else
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu yêu cầu nào!"
                });
            }
        }


        #region Trả hàng NCC - Lưu thông tin

        /// <summary>API "Tạo yêu cầu trả hàng NCC" - Lưu thông tin </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/QualityControl/QualityControl
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///             {
        ///                 "qualityControlId": "e59103f5-e697-4bb8-9010-c5b1a62ffb3d",
        ///                 "qualityChecker": "8d3993a5-bfff-41a0-ae58-3136aa2171a3",
        ///                 "qualityDate": "2022-05-14",
        ///                 "qualityType": "Material",
        ///                 "inspectionLotQuantity": 1,
        ///                 "result": "QualityControl_Result_Pass",
        ///                 "po": "po",
        ///                 "qualityControlDetail": {
        ///                   "qualityControlDetailId": "98db6e43-dba8-452f-ac19-2d40667a6fbf",
        ///                   "testMethod": "AQL",
        ///                   "samplingLevel": "S-1",
        ///                   "acceptableLevel": "muc chap nhan",
        ///                   "inspectionQuantity": 1,
        ///                   "result": "QualityControl_Result_Pass",
        ///                   "samplingLevelName": "noi dung detail neu samplingLevel == OTHER"
        ///                 },
        ///                 "qualityControlInformation": [
        ///                   {
        ///                     "qualityControlInformationId": "0ff9c910-22cb-45d0-af7e-e1278480f926",
        ///                     "qualityControl_QCInformation_Id": "6d8e7b63-3926-4bd2-831d-54b119016ba8",
        ///                     "notes": "kiem thu chat luong note co san"
        ///                   },
        ///                   {      
        ///                       "qualityControlInformationId": "0ff9c910-22cb-45d0-af7e-e1278480f926",
        ///                       "notes": "kiem thu chat luong note them moi"
        ///                   }
        ///                 ],
        ///                 "error": [
        ///                   {
        ///                     "quanlityControl_Error_Id": "820f1f6b-5eb1-43f7-9fe6-650fa2d7a71b",
        ///                     "catalogCode": "Q001",
        ///                     "levelError": "mucdoloi",
        ///                     "quantityError": 1,
        ///                     "notes": "loi ghi chu co san"
        ///                   },
        ///                   {
        ///                   "catalogCode": "Q001",
        ///                     "levelError": "mucdoloi 2",
        ///                     "quantityError": 1,
        ///                     "notes": "loi ghi chu them moi"
        ///                   }
        ///                 ]
        ///               }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 202,
        ///         "success": true,
        ///         "data": "Cập nhật phiếu kiểm tra chất lượng thành công"
        ///     }
        ///     
        ///</remarks>
        [HttpPost("RequestReturnVendor")]

        public async Task<IActionResult> POST([FromBody] List<TraHangNCCViewModel> viewModels)
        {

            if (viewModels == null)
            {
                return Ok(new
                {
                    Code = (int)HttpStatusCode.BadRequest,
                    Success = false,
                    Data = "Vui lòng nhập đầy đủ thông tin"
                });
            }

            if (viewModels.Count == 0)
            {
                return Ok(new
                {
                    Code = (int)HttpStatusCode.BadRequest,
                    Success = false,
                    Data = "Vui lòng nhập đầy đủ thông tin"
                });
            }


            if (!string.IsNullOrEmpty(viewModels[0].RequestReturnVendorId))
            {
                // Kho xác nhận và cập nhật số lượng trả hàng
                var requestReturnVendorId = Guid.Parse(viewModels[0].RequestReturnVendorId);
                foreach (var inputItem in viewModels)
                {
                    foreach (DataTraHangNCCViewModel itemDetail in inputItem.DataList)
                    {
                        var item = await _context.RequestReturnVendorDetailModel
                            .Where(x => x.RequestReturnVendorModelId == requestReturnVendorId && x.Id == itemDetail.Id)
                            .FirstOrDefaultAsync();

                        if (item != null)
                        {
                            item.SoLuongThucTe = decimal.Parse(itemDetail.SoLuongTra);
                        }
                        // use EntityState.Modified to update the item in the database
                        _context.Entry(item).State = EntityState.Modified;
                    }

                }


                var master = await _context.RequestReturnVendorModel
                        .Where(x => x.Id == requestReturnVendorId)
                        .FirstOrDefaultAsync();

                master.WarehouseStaffId = (Guid)CurrentUser.AccountId;
                master.WarehouseVerifyTime = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Code = HttpStatusCode.Accepted,
                    Success = true,
                    Data = "Xác nhận thành công"
                });
            }

            // Thêm mới

            // Get the total number of RequestReturnVendorModels in the database
            var count = _context.RequestReturnVendorModel.Count();
            var requestReturnVendorCode = $"YC{count + 1000001}";

            // Aggregate all DataList elements from all viewModels into master single list
            var allData = viewModels.SelectMany(viewModel => viewModel.DataList).ToList();


            // Check if PoNumber is null or empty
            var poNumber = viewModels.Where(v => string.IsNullOrEmpty(v.PoNumber));
            if (poNumber.Any())
            {
                return Ok(new
                {
                    Code = (int)HttpStatusCode.BadRequest,
                    Success = false,
                    Data = "Vui lòng nhập số PO"
                });
            }

            var poNumberToFindVendor = viewModels[0].PoNumber;
            var vendorNumber = _context.PurchaseOrderMasterModel
                    .Where(pomm => pomm.PurchaseOrderCode == poNumberToFindVendor)
                    .Select(pomm => pomm.VendorNumber)
                    .FirstOrDefault();


            var supplierName = _context.VendorModel
                .Where(vm => vm.SupplierNumber == vendorNumber)
                .Select(vm => vm.ShortName)
                .FirstOrDefault();

            // Create list of RequestReturnVendorDetailModels from viewModels where MaNVL is not null or empty
            var requestReturnVendorDetailModels = viewModels
                .SelectMany(vm => vm.DataList
                    .Where(data => !string.IsNullOrEmpty(data.MaNVL))
                    .Select(data => new RequestReturnVendorDetailModel
                    {
                        Stt = data.Stt,
                        PoNumber = vm.PoNumber,
                        MaNVL = data.MaNVL,
                        TenNVL = data.TenNVL,
                        SoLuongTra = !string.IsNullOrEmpty(data.SoLuongTra) ? decimal.Parse(data.SoLuongTra) : null,
                        SoLuongThucTe = !string.IsNullOrEmpty(data.SoLuongThucTe) ? decimal.Parse(data.SoLuongThucTe) : null,
                        SoLuongPO = !string.IsNullOrEmpty(data.SoLuongPO) ? decimal.Parse(data.SoLuongPO) : null,
                        Dvt = data.Dvt
                    }))
                .ToList();

            // Create new RequestReturnVendorModel instance and assign the RequestReturnVendorDetailModels to it
            var requestReturnVendorModel = new RequestReturnVendorModel
            {
                Id = Guid.NewGuid(),
                RequestReturnVendorCode = requestReturnVendorCode,
                VendorName = supplierName,
                VendorCode = vendorNumber,
                CompanyCode = CurrentUser?.CompanyCode,
                ReturnType = viewModels[0].ReturnType,
                CreateTime = DateTime.Now, // DateTime.UtcNow,
                CreatorId = (Guid)CurrentUser.AccountId,
                RequestReturnVendorDetailModel = requestReturnVendorDetailModels
            };

            // Add the new entity to the context
            _context.RequestReturnVendorModel.Add(requestReturnVendorModel);

            // Save changes to the database
            await _context.SaveChangesAsync();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Tạo yêu cầu trả hàng NCC thành công, mã " + requestReturnVendorCode
            });
        }

    }
    #endregion

}