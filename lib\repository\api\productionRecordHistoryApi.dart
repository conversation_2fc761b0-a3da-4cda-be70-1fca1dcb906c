import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class ProductionRecordHistoryFetchApi {
  static Future<http.Response> getProductionRecordHistory(
      String ttlsx, String fromTime, String toTime, String itmno, String stepCode, String token) async {
    final data = {"TTLSX": ttlsx, "fromTime": fromTime, "toTime": toTime, "itmno": itmno, "StepCode": stepCode};
    if (kDebugMode) {
      print(data);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}${UrlApi.baseUrlProductionRecord}ProductionRecordHistory?TTLSX=${Uri.encodeQueryComponent(data['TTLSX'].toString())}&fromTime=${Uri.encodeQueryComponent(data['fromTime'].toString())}&toTime=${Uri.encodeQueryComponent(data['toTime'].toString())}&itmno=${Uri.encodeQueryComponent(data['itmno'].toString())}&StepCode=${Uri.encodeQueryComponent(data['StepCode'].toString())}';

    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getGetDepartentBy(String ttlsx, String fromTime, String toTime, String itmno, String stepCode, String token) async {
    final data = {"TTLSX": ttlsx, "fromTime": fromTime, "toTime": toTime, "itmno": itmno, "StepCode": stepCode};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}${UrlApi.baseUrlProductionRecord}GetDepartentBy?TTLSX=${Uri.encodeQueryComponent(data['TTLSX'].toString())}&fromTime=${Uri.encodeQueryComponent(data['fromTime'].toString())}&toTime=${Uri.encodeQueryComponent(data['toTime'].toString())}&itmno=${Uri.encodeQueryComponent(data['itmno'].toString())}&StepCode=${Uri.encodeQueryComponent(data['StepCode'].toString())}';

    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
