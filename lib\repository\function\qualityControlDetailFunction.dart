import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:ttf/repository/api/qualityControlApi.dart';

import '../../model/qualityControlApi.dart';
import '../../model/sendQualityControlDetail.dart';

class QualityControlDetailFunction {
  static TestMethodList defaultValueTestMethodList = TestMethodList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static DropdownItemList defaultValueCongDoanNho = DropdownItemList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static DropdownItemList defaultValuePhuongAnXuLy = DropdownItemList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static DropdownItemList defaultValuePhanXuong = DropdownItemList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  static QualityTypeList defaultValueLoaiNghiemThu = QualityTypeList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  static DropdownItemList defaultValueLimitCriticalList = DropdownItemList(catalogCode: "KHONGCHAPNHAN", catalogTextVi: 'Không chấp nhận');
  static DropdownItemList defaultValueLimitList = DropdownItemList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static SamplingLevelList defaultValueSamplingLevelList = SamplingLevelList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');
  static ResultList defaultResultList = ResultList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  static SendQualityControlDetail? getSendQualityControlDetail(
      QualityControl? qualityControl,
      TestMethodList? selectedMethod,
      SamplingLevelList? selectedLevel,
      String controller3Text,
      String controller4Text,
      ResultList? selectedResult,
      bool hideDetailLever,
      String controllerHideDetailLeverText) {
    // debugPrint(selectedResult == null ? "null":selectedResult.catalogTextVi);
    SendQualityControlDetail? sendQualityControlDetail;
    if (qualityControl!.qualityControlDetail != null) {
      sendQualityControlDetail = SendQualityControlDetail(
          qualityControl.qualityControlDetail!.qualityControlDetailId.toString(),
          selectedMethod == null
              ? "null"
              : selectedMethod.catalogCode == " "
                  ? qualityControl.qualityControlDetail!.testMethod
                  : selectedMethod.catalogCode,
          selectedLevel == null
              ? "null"
              : selectedLevel.catalogCode == " "
                  ? qualityControl.qualityControlDetail!.samplingLevel
                  : selectedLevel.catalogCode,
          controller3Text.isNotEmpty ? controller3Text : qualityControl.qualityControlDetail!.acceptableLevel,
          double.parse(controller4Text.isNotEmpty
              ? controller4Text
              : qualityControl.qualityControlDetail!.inspectionQuantity == null
                  ? "0"
                  : qualityControl.qualityControlDetail!.inspectionQuantity!.round().toString()),
          selectedResult == null
              ? "null"
              : selectedResult.catalogCode == " "
                  ? qualityControl.qualityControlDetail!.result
                  : selectedResult.catalogCode,
          hideDetailLever == false
              ? selectedLevel == null
                  ? "null"
                  : selectedLevel.catalogTextVi == "--Vui lòng chọn--"
                      ? qualityControl.qualityControlDetail!.samplingLevelName
                      : selectedLevel.catalogTextVi
              : controllerHideDetailLeverText.isEmpty
                  ? "null"
                  : controllerHideDetailLeverText);
    } else {
      sendQualityControlDetail = SendQualityControlDetail(
          "null",
          selectedMethod?.catalogCode.toString(),
          selectedLevel?.catalogCode.toString(),
          controller3Text.toString(),
          double.parse(controller4Text.isEmpty ? "0" : controller4Text),
          selectedResult?.catalogCode.toString(),
          selectedLevel?.catalogCode != "OTHER"
              ? selectedLevel?.catalogTextVi.toString()
              : controllerHideDetailLeverText.isEmpty
                  ? "null"
                  : controllerHideDetailLeverText);
    }

    return sendQualityControlDetail;
  }

  static SendQualityControlDetail2 getSendQualityControlDetail2(
    QualityControl? qualityControl,
    TestMethodList? selectedMethod,
    DropdownItemList? selectedLimitCritical,
    DropdownItemList? selectedLimitHigh,
    DropdownItemList? selectedLimitLow,
    SamplingLevelList? selectedLevel,
    String controllerMucChapNhan,
    String controllerInspectQuantityDetail,
    String controllerTongSoSanPhamLoi,
    ResultList? selectedResult,
    bool hideDetailLever,
    String controllerHideDetailLeverText,
    DropdownItemList? selectedCongDoanNho,
    String controllerMauHoanThien,
    String controllerSLLoiNangChapNhan,
    String controllerSLLoiNheChapNhan,
    String controllerCheckingTimes,
  ) {
    // debugPrint(selectedResult == null ? "null":selectedResult.catalogTextVi);
    // TODO: thêm ghi chú ở đây
    SendQualityControlDetail2? sendQualityControlDetail;

    if (qualityControl!.qualityControlDetail != null) {
      sendQualityControlDetail = SendQualityControlDetail2(
        qualityControl.qualityControlDetail!.qualityControlDetailId,
        selectedMethod?.catalogCode ?? qualityControl.qualityControlDetail!.testMethod,
        selectedLimitCritical?.catalogCode ?? qualityControl.qualityControlDetail!.limitCritical,
        selectedLimitHigh?.catalogCode ?? qualityControl.qualityControlDetail!.limitHigh,
        selectedLimitLow?.catalogCode ?? qualityControl.qualityControlDetail!.limitLow,
        selectedLevel?.catalogCode ?? qualityControl.qualityControlDetail!.samplingLevel,
        controllerMucChapNhan,
        double.parse(controllerInspectQuantityDetail ?? "0"),
        double.parse(controllerTongSoSanPhamLoi != null && controllerTongSoSanPhamLoi != "" ? controllerTongSoSanPhamLoi : "0"),
        selectedResult?.catalogCode ?? qualityControl.qualityControlDetail!.result,
        selectedLevel?.catalogTextVi ?? controllerHideDetailLeverText,
        selectedCongDoanNho?.catalogCode ?? qualityControl.qualityControlDetail!.congDoanNho,
        controllerMauHoanThien,
        controllerSLLoiNangChapNhan,
        controllerSLLoiNheChapNhan,
        controllerCheckingTimes,
      );
    } else {
      sendQualityControlDetail = SendQualityControlDetail2(
        "null",
        selectedMethod?.catalogCode,
        selectedLimitCritical?.catalogCode,
        selectedLimitHigh?.catalogCode,
        selectedLimitLow?.catalogCode,
        selectedLevel?.catalogCode,
        controllerMucChapNhan,
        double.parse(controllerInspectQuantityDetail ?? "0"),
        double.parse(controllerTongSoSanPhamLoi != null && controllerTongSoSanPhamLoi != "" ? controllerTongSoSanPhamLoi : "0"),
        selectedResult?.catalogCode,
        selectedLevel?.catalogTextVi ?? controllerHideDetailLeverText,
        selectedCongDoanNho?.catalogCode,
        controllerMauHoanThien,
        controllerSLLoiNangChapNhan,
        controllerSLLoiNheChapNhan,
        controllerCheckingTimes,
      );
    }

    return sendQualityControlDetail;
  }

  static Future<List<String>?> fetchLSXSAP(String lsxSAP, String token) async {
    final response = await QuantityControlApi.getListLSXSAP(lsxSAP, token);
    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      if (responseBody != null) {
        if (responseBody['data'] != null) {
          final List<String> listSoToKhai = List<String>.from(responseBody['data']);
          return listSoToKhai;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DropdownItemList>?> fetchPhanXuong(String companyCode, String token) async {
    final response = await QuantityControlApi.fetchPhanXuong(companyCode, token);
    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      if (responseBody != null) {
        if (responseBody['data'] != null) {
          final List<DropdownItemList> listPhanXuong =
              List<DropdownItemList>.from(responseBody['data'].map((item) => DropdownItemList.fromJson(item)));
          return listPhanXuong;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<Map<String, List<DropdownItemList>>?> fetchPhanXuongCongDoan(String companyCode, String token) async {
    final response = await QuantityControlApi.fetchPhanXuongCongDoan(companyCode, token);
    if (response.statusCode == 200) {
      final responseBody = jsonDecode(response.body);
      if (responseBody != null) {
        if (responseBody['data'] != null) {
          final Map<String, List<DropdownItemList>> phanXuongCongDoan = {
            'PhanXuong': List<DropdownItemList>.from(responseBody['data']['phanXuong'].map((item) => DropdownItemList.fromJson(item))),
            'CongDoan': List<DropdownItemList>.from(responseBody['data']['congDoan'].map((item) => DropdownItemList.fromJson(item))),
          };
          return phanXuongCongDoan;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}
