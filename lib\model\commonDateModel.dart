class CommonDateModel {
  String? fromDate;
  String? toDate;

  CommonDateModel({this.fromDate, this.toDate});

  CommonDateModel.fromJson(Map<String, dynamic> json) {
    fromDate = json['fromDate'];
    toDate = json['toDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fromDate'] = fromDate;
    data['toDate'] = toDate;
    return data;
  }
}