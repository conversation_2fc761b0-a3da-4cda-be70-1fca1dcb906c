import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/element/FormLayout.dart';
import 'package:ttf/element/QualityFormFields.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';
import 'package:ttf/model/maintenanceOrderModel.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../LostConnect.dart';
import 'package:ttf/repository/function/maintenanceOrderFunction.dart';

class MaintenanceOrderDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;
  final MaintenanceOrder? maintenanceOrder;

  const MaintenanceOrderDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
    this.maintenanceOrder,
  }) : super(key: key);

  @override
  _MaintenanceOrderDetailState createState() => _MaintenanceOrderDetailState();

  bool get isViewMode => viewMode ?? id.isNotEmpty;
}

class MaintenanceOrderDetailArguments {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;
  final MaintenanceOrder? maintenanceOrder;

  MaintenanceOrderDetailArguments({
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
    this.maintenanceOrder,
  });

  factory MaintenanceOrderDetailArguments.fromMap(Map<String, dynamic> map) {
    return MaintenanceOrderDetailArguments(
      id: map['id'] as String,
      dateTimeOld: map['dateTimeOld'] as String,
      user: map['user'] as DataUser,
      viewMode: map['viewMode'] as bool?,
      maintenanceOrder: map['maintenanceOrder'] as MaintenanceOrder?,
    );
  }
}

class _MaintenanceOrderDetailState extends State<MaintenanceOrderDetail> {
  void _noOp(String _) {} // Helper function for disabled onChanged
  void _noOpSuggestion(String _) {} // Helper function for disabled onSuggestionSelected

  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;
  bool _isLoadingEquipment = false;

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _orderNumberController = TextEditingController();
  final TextEditingController _orderTypeController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _plantController = TextEditingController();
  final TextEditingController _locationPlantController = TextEditingController();
  final TextEditingController _costCenterController = TextEditingController();
  final TextEditingController _workCenterController = TextEditingController();
  final TextEditingController _equipmentNumberController = TextEditingController();
  final TextEditingController _equipmentNameController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  String _workCenterDescription = '';

  String? _status;
  final List<String> _statusOptions = [
    'CREATED',
    'IN_PROCESS',
    'COMPLETED',
  ];

  // Mock data for suggestions - replace with API calls later
  final List<String> _plantSuggestions = ['1000', '1200', '1300'];
  List<String> _workCenterSuggestions = [];
  List<String> _equipmentSuggestions = [];

  @override
  void initState() {
    super.initState();
    debugPrint('MaintenanceOrder in initState: ${widget.maintenanceOrder?.toJson()}');
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      debugPrint('Initializing data with ID: ${widget.id}');
      debugPrint('MaintenanceOrder: ${widget.maintenanceOrder?.toJson()}');

      // Set default date and plant when creating new record
      if (widget.id.isEmpty) {
        _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
        _status = 'CREATED';
        _plantController.text = widget.user.companyCode ?? '';

        // Fetch workcenters after setting plant
        await _fetchWorkCenters();
      } else if (widget.maintenanceOrder != null) {
        // Use the passed maintenance order
        final record = widget.maintenanceOrder!;
        debugPrint('Using passed maintenance order: ${record.toJson()}');

        _orderNumberController.text = record.orderNumber ?? '';
        _orderTypeController.text = record.orderType ?? '';
        _descriptionController.text = record.description ?? '';
        _plantController.text = record.plant ?? widget.user.companyCode ?? '';
        _locationPlantController.text = record.locationPlant ?? '';
        _costCenterController.text = record.costCenter ?? '';
        _workCenterController.text = record.mainWorkCenter ?? '';
        _equipmentNumberController.text = record.equipmentNumber ?? '';
        _equipmentNameController.text = record.equipmentName ?? '';
        _locationController.text = record.location ?? record.locationPlant ?? '';
        _status = record.status ?? 'CREATED';

        debugPrint('Received maintenance order details: ${record.toJson()}');

        if (record.creationDate != null) {
          try {
            // Parse the date using dd/MM/yyyy format
            final date = DateFormat('dd/MM/yyyy').parse(record.creationDate!);
            _dateController.text = DateFormat('dd/MM/yyyy').format(date);
          } catch (e) {
            debugPrint('Error parsing date: $e');
            // Don't set today's date in view mode
          }
        }

        // Fetch workcenters after setting plant
        await _fetchWorkCenters();
      } else {
        // Fallback to fetching full details if data wasn't passed
        debugPrint('Fetching maintenance order details for ID: ${widget.id}');
        final record = await MaintenanceOrderFunction.fetchMaintenanceOrderDetail(
          widget.user.token!,
          widget.id,
        );

        if (record != null) {
          debugPrint('Received maintenance order details: ${record.toJson()}');
          _orderNumberController.text = record.orderNumber ?? '';
          _orderTypeController.text = record.orderType ?? '';
          _descriptionController.text = record.description ?? '';
          _plantController.text = record.plant ?? widget.user.companyCode ?? '';
          _locationPlantController.text = record.locationPlant ?? '';
          _costCenterController.text = record.costCenter ?? '';
          _workCenterController.text = record.mainWorkCenter ?? '';
          _equipmentNumberController.text = record.equipmentNumber ?? '';
          _equipmentNameController.text = record.equipmentName ?? '';
          _locationController.text = record.location ?? record.locationPlant ?? '';

          if (record.creationDate != null) {
            try {
              // Parse the date using dd/MM/yyyy format
              final date = DateFormat('dd/MM/yyyy').parse(record.creationDate!);
              _dateController.text = DateFormat('dd/MM/yyyy').format(date);
            } catch (e) {
              debugPrint('Error parsing date: $e');
              // Don't set today's date in view mode
            }
          }

          _status = record.status ?? 'CREATED';

          // Fetch workcenters after setting plant
          await _fetchWorkCenters();
        }
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
      showToast(
        context: context,
        message: 'Có lỗi xảy ra khi tải dữ liệu',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    if (widget.isViewMode) return;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = DateFormat('dd/MM/yyyy').format(pickedDate);
      });
    }
  }

  bool _validateForm() {
    if (_plantController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Nhà máy',
      );
      return false;
    }

    if (_descriptionController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập Mô tả',
      );
      return false;
    }

    if (_equipmentNumberController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Thiết bị',
      );
      return false;
    }

    return true;
  }

  Future<void> _saveData() async {
    if (!_validateForm()) return;

    try {
      setState(() => _isSaving = true);

      // Create the model for the API request
      final model = CreateMaintenanceOrderModel(
        plant: _plantController.text,
        equipment: _equipmentNumberController.text,
        shortText: _descriptionController.text,
        workCenter: _workCenterController.text,
        operationDescription: _descriptionController.text,
        locationPlant: _locationPlantController.text,
        costCenter: _costCenterController.text,
      );

      // Call the API
      final response = await MaintenanceOrderFunction.createMaintenanceOrder(
        widget.user.token!,
        model,
      );

      if (!mounted) return;

      if (response.success) {
        // Show success dialog instead of toast
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Thành công',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: Text(
                'Tạo thành công${response.orderNumber != null ? '\nSố phiếu: ${response.orderNumber}' : ''}',
                style: TextStyle(fontSize: 13.sp),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.pop(context, true); // Return to previous screen
                  },
                  child: Text(
                    'Đóng',
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      } else {
        // Show error dialog for better visibility
        await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Lỗi',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              content: Text(
                response.message,
                style: TextStyle(fontSize: 13.sp),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Đóng',
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      debugPrint("Error in _saveData: $error");
      if (!mounted) return;

      // Show error dialog for better visibility
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              'Lỗi',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            content: Text(
              'Đã xảy ra lỗi khi tạo phiếu',
              style: TextStyle(fontSize: 13.sp),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Đóng',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          );
        },
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  Future<void> _handleCloseOrder() async {
    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Xác nhận',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Xác nhận đóng lệnh',
            style: TextStyle(fontSize: 13.sp),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Hủy',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.grey[600],
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Xác nhận',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        setState(() => _isSaving = true);

        // Call the API to close the order
        final response = await MaintenanceOrderFunction.closeMaintenanceOrder(
          widget.user.token!,
          widget.maintenanceOrder?.orderNumber ?? '',
        );

        if (!mounted) return;

        if (response.success) {
          // Show success dialog
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text(
                  'Thành công',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: Text(
                  'Đóng lệnh thành công',
                  style: TextStyle(fontSize: 13.sp),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Close dialog
                      Navigator.pop(context, true); // Return to previous screen with refresh flag
                    },
                    child: Text(
                      'Đóng',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        } else {
          // Show error dialog
          await showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text(
                  'Lỗi',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                content: Text(
                  response.message,
                  style: TextStyle(fontSize: 13.sp),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Đóng',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }
      } catch (error) {
        debugPrint("Error closing order: $error");
        if (!mounted) return;

        // Show error dialog
        await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Lỗi',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              content: Text(
                'Đã xảy ra lỗi khi đóng lệnh',
                style: TextStyle(fontSize: 13.sp),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Đóng',
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      } finally {
        if (mounted) {
          setState(() => _isSaving = false);
        }
      }
    }
  }

  Future<void> _fetchWorkCenters() async {
    try {
      final workcenters = await MaintenanceOrderFunction.fetchWorkCenters(
        widget.user.token!,
        _plantController.text,
      );

      setState(() {
        _workCenterSuggestions = workcenters;
      });
    } catch (e) {
      debugPrint('Error fetching workcenters: $e');
    }
  }

  Future<void> _fetchEquipmentSuggestions(String searchTerm) async {
    if (searchTerm.length < 3) {
      setState(() {
        _equipmentSuggestions = [];
        _isLoadingEquipment = false;
      });
      return;
    }

    setState(() {
      _isLoadingEquipment = true;
    });

    try {
      final suggestions = await MaintenanceOrderFunction.fetchEquipment(
        widget.user.token!,
        widget.user.companyCode ?? '',
        searchTerm,
      );

      if (!mounted) return;

      setState(() {
        _equipmentSuggestions = suggestions;
        _isLoadingEquipment = false;
      });
    } catch (e) {
      debugPrint('Error fetching equipment suggestions: $e');
      if (mounted) {
        setState(() {
          _equipmentSuggestions = [];
          _isLoadingEquipment = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.id.isEmpty ? 'Tạo Maintenance Order (M2)' : 'Chi tiết Maintenance Order (M2)',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          // Unfocus when tapping outside any text field
          FocusScope.of(context).unfocus();
        },
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _initializeData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            children: [
              FormLayout(
                title: "Thông tin Maintenance Order",
                children: [
                  if (widget.id.isNotEmpty) ...[
                    FormInputField(
                      label: 'Số phiếu',
                      controller: _orderNumberController,
                      enabled: false,
                    ),
                    SizedBox(height: 10.h),
                  ],
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Expanded(
                          flex: 3,
                          child: Text(
                            'Ngày',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          flex: 7,
                          child: GestureDetector(
                            onTap: widget.isViewMode ? null : () => _selectDate(context),
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                                color: Colors.white,
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      _dateController.text.isEmpty ? 'Chọn ngày' : _dateController.text,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.blue),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10.h),
                  FormInputField(
                    label: 'Nhà máy',
                    controller: _plantController,
                    enabled: false,
                  ),
                  SizedBox(height: 10.h),
                  FormInputField(
                    label: 'Mô tả *',
                    controller: _descriptionController,
                    enabled: !widget.isViewMode,
                    maxLines: 3,
                  ),
                  SizedBox(height: 10.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Expanded(
                          flex: 3,
                          child: Text(
                            'Work Center *',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          flex: 7,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AutoCompleteField(
                                label: '',
                                enabled: !widget.isViewMode,
                                suggestions: _workCenterSuggestions,
                                controller: _workCenterController,
                                onChanged: (value) {
                                  debugPrint('Work Center value changed: $value');
                                  if (value.isEmpty) {
                                    setState(() {
                                      _workCenterDescription = '';
                                    });
                                  }
                                },
                                onSuggestionSelected: (suggestion) {
                                  final parts = suggestion.split(' | ');
                                  setState(() {
                                    _workCenterController.text = parts[0];
                                    _workCenterDescription = parts.length > 1 ? parts[1] : '';
                                  });
                                },
                              ),
                              if (_workCenterDescription.isNotEmpty) ...[
                                SizedBox(height: 4.h),
                                Text(
                                  _workCenterDescription,
                                  style: TextStyle(
                                    fontSize: 11.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: AutoCompleteField(
                      label: 'Mã thiết bị *',
                      enabled: !widget.isViewMode,
                      suggestions: _equipmentSuggestions,
                      controller: _equipmentNumberController,
                      isLoading: _isLoadingEquipment,
                      onChanged: widget.isViewMode
                          ? _noOp
                          : (value) {
                              debugPrint('Equipment value changed: $value');
                              _fetchEquipmentSuggestions(value);
                              setState(() {
                                _equipmentNameController.text = '';
                              });
                            },
                      onSuggestionSelected: widget.isViewMode
                          ? _noOpSuggestion
                          : (suggestion) {
                              debugPrint('Equipment suggestion selected: $suggestion');
                              final parts = suggestion.split(' | ');
                              setState(() {
                                _equipmentNumberController.text = parts[0];
                                _equipmentNameController.text = parts.length > 1 ? parts[1] : '';
                              });
                            },
                    ),
                  ),
                  if (_equipmentNameController.text.isNotEmpty) ...[
                    SizedBox(height: 10.h),
                    FormInputField(
                      label: 'Tên thiết bị',
                      controller: _equipmentNameController,
                      enabled: false,
                    ),
                  ],
                  SizedBox(height: 10.h),
                  if (widget.maintenanceOrder?.functionalLocation != null) ...[
                    FormInputField(
                      label: 'Functional Location',
                      controller: TextEditingController(text: widget.maintenanceOrder?.functionalLocation),
                      enabled: false,
                    ),
                    SizedBox(height: 10.h),
                  ],
                  if (widget.id.isNotEmpty) ...[
                    FormInputField(
                      label: 'Vị trí',
                      controller: _locationController,
                      enabled: !widget.isViewMode,
                      required: false,
                    ),
                    SizedBox(height: 10.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                              'Trạng thái',
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: widget.isViewMode
                                ? Container(
                                    padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                      color: Colors.white,
                                    ),
                                    child: Text(
                                      _getStatusText(_status ?? 'CREATED'),
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: Colors.black,
                                      ),
                                    ),
                                  )
                                : Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 4.w),
                                        isDense: true,
                                        isExpanded: true,
                                        value: _status ?? 'CREATED',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Colors.black,
                                        ),
                                        items: _statusOptions
                                            .map((status) => DropdownMenuItem(
                                                  value: status,
                                                  child: Text(
                                                    _getStatusText(status),
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                ))
                                            .toList(),
                                        onChanged: (value) {
                                          setState(() {
                                            _status = value;
                                          });
                                        },
                                      ),
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              if (!widget.isViewMode)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                  child: SizedBox(
                    height: 40.h,
                    width: double.infinity,
                    child: FormSubmitButton(
                      text: widget.id.isEmpty ? 'Tạo mới' : 'Cập nhật',
                      onPressed: _saveData,
                      isLoading: _isSaving,
                    ),
                  ),
                ),
              if (_status == 'IN_PROCESS')
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                  child: SizedBox(
                    height: 40.h,
                    width: double.infinity,
                    child: FormSubmitButton(
                      text: 'Đóng lệnh',
                      onPressed: _handleCloseOrder,
                      isLoading: _isSaving,
                    ),
                  ),
                ),
              SizedBox(height: 150.h),
            ],
          ),
        ),
      ],
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'CREATED':
        return 'Mới tạo';
      case 'IN_PROCESS':
        return 'Đang thực hiện';
      case 'COMPLETED':
        return 'Đã hoàn thành';
      default:
        return status;
    }
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _orderTypeController.dispose();
    _descriptionController.dispose();
    _plantController.dispose();
    _locationPlantController.dispose();
    _costCenterController.dispose();
    _workCenterController.dispose();
    _equipmentNumberController.dispose();
    _equipmentNameController.dispose();
    _locationController.dispose();
    _dateController.dispose();
    super.dispose();
  }
}
