import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DiaLogCompleteSendRequest extends StatelessWidget {
  final String message;
  final String? route;
  const DiaLogCompleteSendRequest({Key? key, required this.message, this.route}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: SingleChildScrollView(
          child: Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
        Icon(Icons.air_rounded, size: 50.sp, color: Colors.greenAccent),
        SizedBox(height: 15.h),
        Text(
          message,
          style: TextStyle(fontSize: 15.sp),
          textAlign: TextAlign.center,
        )
      ])),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.popUntil(context, ModalRoute.withName(route ?? '/InventoryManagement')),
          child: Text(
            'Xác nhận',
            style: TextStyle(fontSize: 15.sp),
          ),
        ),
      ],
    );
  }
}
