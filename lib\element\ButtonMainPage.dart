import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class ButtonMainPage extends StatelessWidget {
  final String txt;
  final GestureTapCallback route;
  final Icon icon;

  const ButtonMainPage({Key? key, required this.txt, required this.route, required this.icon}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: route,
      child: Container(
        padding: REdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(width: 0.5.w, color: Colors.white),
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade400.withOpacity(0.15),
              spreadRadius: 5,
              blurRadius: 3,
            ),
          ],
        ),
        child: Row(
          children: <Widget>[
            Flexible(
              flex: 2,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 13.w),
                decoration: BoxDecoration(
                  color: const Color(0xff0052cc).withOpacity(0.8),
                  borderRadius: BorderRadius.circular(10.r),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xff0052cc).withOpacity(0.15),
                      spreadRadius: 2,
                      blurRadius: 7,
                      offset: const Offset(0, 3), // changes position of shadow
                    ),
                  ],
                ),
                child: icon,
              ),
            ),
            SizedBox(width: 5.w),
            Expanded(
              flex: 7,
              child: Text(
                txt,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11.sp),
              ),
            ),
            Expanded(
              flex: 1,
              child: Icon(Icons.arrow_forward_ios_rounded, size: 15.sp, color: Colors.grey.shade400),
            ),
          ],
        ),
      ),
    );

    //   Container(
    //   width: double.infinity,
    //   decoration: const BoxDecoration(
    //   ),
    //   child: ElevatedButton.icon(
    //     style: ButtonStyle(
    //       shape: MaterialStateProperty.all<
    //           RoundedRectangleBorder>(
    //           RoundedRectangleBorder(
    //               borderRadius: BorderRadius.circular(5.r),
    //               side: const BorderSide(color: Colors.white))),
    //       side: MaterialStateProperty.all(
    //         const BorderSide(
    //           color: Color(0xff0052cc),
    //         ),
    //       ),
    //       backgroundColor:
    //       MaterialStateProperty.all(const Color(0xff0052cc)),
    //     ),
    //     onPressed: route,
    //     icon: icon,
    //     label:Container(
    //       margin: EdgeInsets.symmetric(vertical: 12.h),
    //       child: Text(
    //             txt,
    //             style: TextStyle(
    //                 color: Colors.white,
    //                 fontWeight: FontWeight.bold,
    //                 fontSize: 13.sp),
    //             textAlign: TextAlign.center,
    //           ),
    //     ),
    //
    //   ),
    // );
  }
}
