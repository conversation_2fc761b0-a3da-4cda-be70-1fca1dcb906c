import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/getListSOWBS.dart';

class ManagementReturnMaterials extends StatelessWidget {
  final List<ListSOWBS> lsSOWBS;
  final ListSOWBS? selectedSOWBSReturn;
  final TextEditingController controllerAmountReturn;
  final VoidCallback onTap;
  final ValueChanged<ListSOWBS?> setSelectedSOWBSReturn;
  final String errorTextAmountReturn;
  final IconData? errorIconAmountReturn;
  final bool errorAmountReturn;
  final bool hideAmountReturn;
  final String scanMatReturn;
  final String scanDesReturn;
  final String scanBatchReturn;
  final String errorTextDropdownReturn;
  final IconData? errorIconDropdownReturn;
  final bool errorDropdownReturn;
  final bool hideDropdownReturn;
  final String amountUse;
  const ManagementReturnMaterials({
    Key? key,
    required this.lsSOWBS,
    required this.selectedSOWBSReturn,
    required this.controllerAmountReturn,
    required this.onTap,
    required this.setSelectedSOWBSReturn,
    required this.errorTextAmountReturn,
    required this.errorIconAmountReturn,
    required this.errorAmountReturn,
    required this.hideAmountReturn,
    required this.scanMatReturn,
    required this.scanDesReturn,
    required this.scanBatchReturn,
    required this.errorTextDropdownReturn,
    required this.errorIconDropdownReturn,
    required this.errorDropdownReturn,
    required this.hideDropdownReturn,
    required this.amountUse,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Mat:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade400,
                ),
              ),
              child: Text(
                scanMatReturn,
                style: TextStyle(color: Colors.black, fontSize: 11.sp),
              )),
          // SizedBox(height: error == true ? 15.h : 0),
          // ContaineError.widgetError(
          //     errorContainerWidth,
          //     hide,
          //     errorIcon,
          //     15.sp,
          //     Colors.red[700],
          //     5.w,
          //     0.w,
          //     0.h,
          //     0.h,
          //     errorText,
          //     11.sp,
          //     Colors.red[700]),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Des:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade400,
                ),
              ),
              child: Text(
                scanDesReturn,
                style: TextStyle(color: Colors.black, fontSize: 11.sp),
              )),
          // SizedBox(height: error_2 == true ? 15.h : 0),
          // ContaineError.widgetError(
          //     errorContainerWidth_2,
          //     hide_2,
          //     errorIcon_2,
          //     15.sp,
          //     Colors.red[700],
          //     5.w,
          //     0.w,
          //     0.h,
          //     0.h,
          //     errorText_2,
          //     11.sp,
          //     Colors.red[700]),
          SizedBox(
            height: 15.h,
          ),

          Text(
            "Batch:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
            decoration: BoxDecoration(
              border: Border.all(
                width: 0.5,
                color: Colors.grey.shade400,
              ),
            ),
            child: Text(
              scanBatchReturn,
              style: TextStyle(color: Colors.black, fontSize: 11.sp),
            ),
          ),
          SizedBox(
            height: 15.h,
          ),
          Center(
            child: OutlinedButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r),
                        side: const BorderSide(color: Colors.white))),
                side: MaterialStateProperty.all(
                  const BorderSide(
                    color: Color(0xff0052cc),
                  ),
                ),
                backgroundColor: MaterialStateProperty.all(Colors.white),
              ),
              onPressed: () async {},
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 12.h),
                child: Icon(
                  Icons.camera_alt_outlined,
                  size: 19.sp,
                  color: const Color(0xff0052cc),
                ),
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            "SO/WBS:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                  color: errorDropdownReturn == true
                      ?const Color(0xFFD32F2F)
                      :Colors.grey.shade400,
                  width: 0.5.w
              ),
              borderRadius: BorderRadius.circular(1.r),
            ),
            child: DropdownButtonHideUnderline(
                child: DropdownButton<ListSOWBS?>(
                  isExpanded: true,
                  value: selectedSOWBSReturn ?? lsSOWBS[0],
                  iconSize: 15.sp,
                  style: const TextStyle(color: Colors.black),
                  onChanged: setSelectedSOWBSReturn,
                  items: lsSOWBS.map((ListSOWBS? sowbs) {
                    return DropdownMenuItem<ListSOWBS>(
                        value: sowbs,
                        child: Center(
                          child: Text(
                            sowbs!.name.toString(),
                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                          ),
                        ));
                  }).toList(),
                )),
          ),
          hideDropdownReturn == true
              ? Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10.h),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Icon(
                      errorIconDropdownReturn,
                      size: 15.sp,
                      color: const Color(0xFFD32F2F),
                    ),
                  ),
                  Expanded(
                    flex: 9,
                    child: Text(
                      errorTextDropdownReturn,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: const Color(0xFFD32F2F),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
              : Container(),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Số lượng SD trong ngày:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                flex: 9,
                child: Container(
                  // width: double.infinity,
                    padding:
                    EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        width: 0.5,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    child: Text(
                      amountUse,
                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                    )),
              ),
              Expanded(
                flex: 1,
                child: Center(
                  child: Text(
                    "M3",
                    style:
                    TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 15.h,
          ),
          Text(
            "Số lượng trả:",
            style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Expanded(
                flex: 9,
                child: SizedBox(
                  height: 40.h,
                  child: TextFormField(
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                    controller: controllerAmountReturn,
                    style: TextStyle(fontSize: 12.sp),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(0),
                        borderSide: BorderSide(
                            width: 0.5,
                            color: errorAmountReturn == true
                                ? const Color(0xFFD32F2F)
                                : Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(0),
                        borderSide: BorderSide(
                            width: 0.5,
                            color: errorAmountReturn == true
                                ? const Color(0xFFD32F2F)
                                : Colors.grey.shade400),
                      ),
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      filled: true,
                      fillColor: Colors.white,
                      hintStyle: TextStyle(fontSize: 12.sp),
                      contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Center(
                  child: Text(
                    "M3",
                    style:
                    TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
          hideAmountReturn == true
              ? Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10.h),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Icon(
                      errorIconAmountReturn,
                      size: 15.sp,
                      color: const Color(0xFFD32F2F),
                    ),
                  ),
                  Expanded(
                    flex: 9,
                    child: Text(
                      errorTextAmountReturn,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: const Color(0xFFD32F2F),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
              : Container(),
          SizedBox(
            height: 10.h,
          ),
          Container(
            width: double.infinity,
            child: ElevatedButton(
              style: ButtonStyle(
                padding: MaterialStateProperty.all<EdgeInsets>(
                    EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r),
                        side: const BorderSide(color: Colors.white))),
                side: MaterialStateProperty.all(
                  const BorderSide(
                    color: Color(0xff0052cc),
                  ),
                ),
                backgroundColor:
                MaterialStateProperty.all(const Color(0xff0052cc)),
              ),
              onPressed: onTap,
              child: Text(
                "Lưu",
                style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          )
        ]);
  }
}
