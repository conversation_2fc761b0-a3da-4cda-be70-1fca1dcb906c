﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels;
using ISD.API.ViewModels.Integration;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class SlocIntegrationController : ControllerBaseAPI
    {
        /// <summary>API "Tích hợp thông tin Sloc" - Thêm / Cập nhật</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/SlocIntegration/Sloc
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        ///  
        ///     {
        ///             "sloc": "123",
        ///             "plant": "123",
        ///             "warehouseNo": "123"
        ///     }
        ///     
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": "Tích hợp thành công Sloc",
        ///         "data": null
        ///     }
        ///</remarks>
        [HttpPost("Sloc")]
        public async Task<IActionResult> SlocIntegrationAsync([FromBody] SlocIntegrationViewModel intergrationModel)
        {
            //Check Sloc is exist
            var exitSloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Plant == intergrationModel.Plant &&
                                                                  x.Sloc == intergrationModel.Sloc);

            // Sloc exists => update
            if (exitSloc != null)
            {
                #region Update

                //Update Sloc
                exitSloc.Sloc = intergrationModel.Sloc;

                //Update Plant
                exitSloc.Plant = intergrationModel.Plant;

                exitSloc.SlocName = intergrationModel.SlocName;

                //Update WarehouseNo
                exitSloc.WarehouseNo = intergrationModel.WarehouseNo;

                //Update EditTime
                exitSloc.LastEditTime = DateTime.Now;
                _context.SaveChanges();
                #endregion
            }

            // Sloc not exists => create
            else
            {
                #region Create

                _context.SlocModel.Add(new SlocModel
                {
                    Id = Guid.NewGuid(),
                    Sloc = intergrationModel.Sloc,
                    Plant = intergrationModel.Plant,
                    SlocName = intergrationModel.SlocName,  
                    WarehouseNo = intergrationModel.WarehouseNo,
                    CreateTime = DateTime.Now,
                    Actived = true
                });

                _context.SaveChanges();
                #endregion
            }

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Tích hợp thành công Sloc", Data = exitSloc });
        }
    }
}
