Here's the documentation for the token timeout handling pattern in the TTF_MES_Mobile app:

# Token Timeout Pattern Documentation

## Overview

The token timeout pattern is used throughout the TTF_MES_Mobile app to handle session expiration and force users to re-authenticate when their token expires. This pattern consists of three main components:

1. Token validation check
2. Timeout state management 
3. Conditional rendering of timeout view

## Implementation Details

### 1. Token Validation Function

The `isTokenLive()` utility function checks if the token is still valid by comparing dates:


```118:124:TTF_MES_Mobile/lib/utils/ui_helpers.dart
bool isTokenLive(String oldDateString) {
  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(oldDateString);

  return convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld);
}
```


### 2. Timeout Check Pattern

The typical pattern for checking token timeout involves:

1. Checking token validity
2. Setting timeout state
3. Early return if timed out

Example implementation:

```dart
@override
void initState() {
  super.initState();
  
  if (!isTokenLive(widget.dateTimeOld)) {
    setState(() {
      _timeOut = true;
    });
    return;
  }

  setState(() {
    _timeOut = false;
  });
  
  // Continue with normal initialization
  _loadData();
}
```

### 3. Conditional Rendering

The UI conditionally renders either the timeout view or normal content based on `_timeOut`:

```dart
@override
Widget build(BuildContext context) {
  return _timeOut 
    ? TimeOutView(
        setButton: _setButton,
        disableButton: _disableButtonTimeout
      )
    : Scaffold(
        // Normal screen content
      );
}
```

## Components

### TimeOutView Widget

The `TimeOutView` component displays the timeout message and logout button:


```6:93:TTF_MES_Mobile/lib/element/timeOut.dart
class TimeOutView extends StatelessWidget {
  final bool disableButton;
  final VoidCallback setButton;
  const TimeOutView({Key? key, required this.disableButton, required this.setButton}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(Icons.access_time_filled_rounded, size: 45.sp),
        SizedBox(height: 10.h),
        Text(
          "Hết phiên đăng nhập!",
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10.h),
        Text(
          "Bạn đã hết phiên đăng nhập! Vui lòng đăng nhập trở lại",
          style: TextStyle(
            fontSize: 13.sp,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 20.h,
        ),
        ElevatedButton.icon(
          style: ButtonStyle(
            padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 5.h, horizontal: 30.w)),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r), side: const BorderSide(color: Colors.white))),
            side: MaterialStateProperty.all(
              const BorderSide(
                color: Color(0xff000000),
              ),
            ),
            backgroundColor: MaterialStateProperty.all(const Color(0xff000000)),
          ),
          onPressed: disableButton == true
              ? null
              : () async {
                  showDialog<String>(
                    barrierDismissible: false,
                    context: context,
                    builder: (BuildContext context) => WillPopScope(
                      onWillPop: () async => false,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                  bool check = await MainPageFunction.removeCurrentUser("id", "datetimeNow", "user");
                  Navigator.pop(context);
                  if (check == true) {
                    setButton();
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thành công',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 1)));
                    Future.delayed(const Duration(seconds: 0), () {
                      Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
                    });
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thất bại! Ứng dụng xảy ra lỗi!',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 2)));
                  }
                },
          icon: Icon(Icons.refresh_rounded, color: Colors.white, size: 17.sp),
          label: Text(
            "Đăng xuất",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 15.sp),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    ));
  }
}
```


### Platform-Specific Dialogs

The app shows platform-specific timeout dialogs:

- Android: `DiaLogTimeOutSessionAndroid`
- iOS: `DiaLogTimeOutSessionIOS`

Example dialog trigger:


```1964:1975:TTF_MES_Mobile/lib/page/ExportWarehouse2.dart
                                                      if (!isTokenLive(widget.dateTimeOld)) {
                                                        Platform.isAndroid
                                                            ? showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                            : showCupertinoDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                        return;
                                                      }
```


## Usage Example

A complete example of the timeout pattern implementation can be found in:


```237:251:TTF_MES_Mobile/lib/page/KiemTraChatLuong/PhieuKCSCongDoanDetail.dart
...
    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    }

    setState(() {
      _timeOut = false;
    });
    _init();
    _loadDataAndSetDefault();
  }
```


## Best Practices

1. Always check token validity in `initState()`
2. Set timeout state immediately when detected
3. Return early to prevent unnecessary initialization
4. Use platform-specific dialogs for better UX
5. Provide clear feedback to users about session expiration
6. Implement proper cleanup in logout process

## Error Handling

The timeout handling includes:

1. Clear session data on logout
2. Show appropriate error messages
3. Navigate user back to login screen
4. Handle platform-specific behaviors

## Related Components

- `TimeOutView`: Main timeout display component
- `DiaLogTimeOutSessionAndroid`: Android timeout dialog
- `DiaLogTimeOutSessionIOS`: iOS timeout dialog
- `isTokenLive()`: Token validation utility

This pattern ensures consistent session timeout handling across the application while providing a smooth user experience for re-authentication.
