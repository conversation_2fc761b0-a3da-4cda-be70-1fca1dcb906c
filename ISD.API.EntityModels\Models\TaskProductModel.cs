﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskProductModel", Schema = "Task")]
    public partial class TaskProductModel
    {
        public TaskProductModel()
        {
            TaskProductAccessoryModel = new HashSet<TaskProductAccessoryModel>();
            TaskProductUsualErrorModel = new HashSet<TaskProductUsualErrorModel>();
        }

        [Key]
        public Guid TaskProductId { get; set; }
        public Guid? TaskId { get; set; }
        public Guid? ProductId { get; set; }
        public int? Qty { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(50)]
        public string WarrantyProductTypeCode { get; set; }
        [StringLength(50)]
        public string ErrorTypeCode { get; set; }
        [StringLength(50)]
        public string ErrorCode { get; set; }
        [StringLength(50)]
        public string ProductLevelCode { get; set; }
        [StringLength(50)]
        public string ProductColorCode { get; set; }
        [StringLength(50)]
        public string ProductCategoryCode { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? WarrantyValue { get; set; }

        [ForeignKey("TaskId")]
        [InverseProperty("TaskProductModel")]
        public virtual TaskModel Task { get; set; }
        [InverseProperty("TaskProduct")]
        public virtual ICollection<TaskProductAccessoryModel> TaskProductAccessoryModel { get; set; }
        [InverseProperty("TaskProduct")]
        public virtual ICollection<TaskProductUsualErrorModel> TaskProductUsualErrorModel { get; set; }
    }
}