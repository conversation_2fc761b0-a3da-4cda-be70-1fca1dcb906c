class WarehouseTranferNoReservation {
  String? slocExportId;
  String? storageBinExportId;
  String? slocImportId;
  String? storageBinImportId;
  List<WarehouseTranferDetails>? warehouseTranferDetails;
  String? rawMaterialCardId;
  // String? batchNumber;

  WarehouseTranferNoReservation(
      {this.slocExportId,
        this.storageBinExportId,
        this.slocImportId,
        this.storageBinImportId,
        this.warehouseTranferDetails,
        this.rawMaterialCardId,
        // this.batchNumber
      });

  WarehouseTranferNoReservation.fromJson(Map<String, dynamic> json) {
    slocExportId = json['slocExportId'];
    storageBinExportId = json['storageBinExportId'];
    slocImportId = json['slocImportId'];
    storageBinImportId = json['storageBinImportId'];
    if (json['warehouseTranferDetails'] != null) {
      warehouseTranferDetails = <WarehouseTranferDetails>[];
      json['warehouseTranferDetails'].forEach((v) {
        warehouseTranferDetails!.add(WarehouseTranferDetails.fromJson(v));
      });
    }
    rawMaterialCardId = json['rawMaterialCardId'];
    // batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['slocExportId'] = slocExportId;
    data['storageBinExportId'] = storageBinExportId;
    data['slocImportId'] = slocImportId;
    data['storageBinImportId'] = storageBinImportId;
    if (warehouseTranferDetails != null) {
      data['warehouseTranferDetails'] =
          warehouseTranferDetails!.map((v) => v.toJson()).toList();
    }
    data['rawMaterialCardId'] = rawMaterialCardId;
    // data['batchNumber'] = batchNumber;
    return data;
  }
}

class WarehouseTranferDetails {
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;
  String? batchNumber;

  WarehouseTranferDetails(
      {this.so, this.soLine, this.wbs, this.quantity, this.unit, required this.batchNumber});

  WarehouseTranferDetails.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantity = json['quantity'];
    unit = json['unit'];
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['batchNumber'] = batchNumber;
    return data;
  }
}
class WarehouseTranferNoReservationMessage {
  int? code;
  bool? isSuccess;
  String? message;
  bool? dataCheck;

  WarehouseTranferNoReservationMessage(
      {this.code,
        this.isSuccess,
        this.message,
        this.dataCheck});

  WarehouseTranferNoReservationMessage.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    dataCheck = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = dataCheck;
    return data;
  }
}