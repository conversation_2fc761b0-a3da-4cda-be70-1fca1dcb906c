# MaiDao External API Integration Guide

## Overview

This guide explains how to integrate with the TTF iMES MaiDao (Knife Grinding) API using API key authentication. The MaiDao system tracks knife grinding operations, equipment maintenance, material usage, and employee activities in manufacturing operations.

## What is MaiDao?

MaiDao (Mài dao) refers to knife grinding operations in manufacturing. This system tracks:
- **Equipment**: Which machines/tools need knife grinding
- **Materials**: Grinding materials and consumables used
- **Operations**: Different types of grinding operations
- **Employees**: Who performed and requested the operations
- **Status**: Current state of grinding operations
- **History**: Complete audit trail of all activities

## Authentication

Uses the same API key authentication system as other external APIs. Contact your system administrator to create an API key with `MaiDao.Read` scope.

## Base URL and Endpoints

**Base URL**: `https://your-api-domain.com/api/v1/External/MaiDaoExternal`

All endpoints require API key authentication via `X-API-Key` header.

## Available Endpoints

### 1. Get MaiDao Records (with Filtering)

Retrieve paginated list of MaiDao records with comprehensive filtering options.

```http
GET /api/v1/External/MaiDaoExternal/MaiDao
```

**Query Parameters:**
- `equipmentCode` (optional): Filter by equipment code
- `materialCode` (optional): Filter by material code
- `operationType` (optional): Filter by operation type (e.g., "Mài dao", "Bảo dưỡng")
- `status` (optional): Filter by status
- `fromDate` (optional): Start date filter (ISO 8601)
- `toDate` (optional): End date filter (ISO 8601)
- `pageIndex` (optional): Page number (default: 1)
- `pageSize` (optional): Records per page (default: 20, max: 100)

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/MaiDao?equipmentCode=EQ001&operationType=Mài dao&pageIndex=1&pageSize=20" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "maiDaoId": "123e4567-e89b-12d3-a456-************",
        "date": "15/11/2024",
        "equipmentCode": "EQ001",
        "equipmentName": "Máy cắt gỗ số 1",
        "materialCode": "MAT001",
        "materialName": "Đá mài carbide",
        "materialBatch": "LOT2024001",
        "operationType": "Mài dao",
        "employeeCodes": "NV001,NV002",
        "employeeNames": "Nguyễn Văn A, Trần Thị B",
        "requestingEmployeeCode": "NV003",
        "requestingEmployeeName": "Lê Văn C",
        "note": "Mài dao định kỳ",
        "status": "Completed",
        "createdDate": "2024-11-15T08:30:00",
        "updatedDate": "2024-11-15T10:45:00",
        "companyCode": "1000"
      }
    ],
    "pagination": {
      "pageIndex": 1,
      "pageSize": 20,
      "totalCount": 150,
      "totalPages": 8,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### 2. Get MaiDao Record by ID

Retrieve detailed information for a specific MaiDao record.

```http
GET /api/v1/External/MaiDaoExternal/MaiDao/{maiDaoId}
```

**Parameters:**
- `maiDaoId` (required): GUID of the MaiDao record

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/MaiDao/123e4567-e89b-12d3-a456-************" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "maiDaoId": "123e4567-e89b-12d3-a456-************",
    "date": "15/11/2024",
    "equipmentCode": "EQ001",
    "equipmentName": "Máy cắt gỗ số 1",
    "materialCode": "MAT001",
    "materialName": "Đá mài carbide",
    "materialBatch": "LOT2024001",
    "operationType": "Mài dao",
    "employeeCodes": "NV001,NV002",
    "employeeNames": "Nguyễn Văn A, Trần Thị B",
    "requestingEmployeeCode": "NV003",
    "requestingEmployeeName": "Lê Văn C",
    "note": "Mài dao định kỳ theo kế hoạch bảo dưỡng",
    "status": "Completed",
    "createdDate": "2024-11-15T08:30:00",
    "updatedDate": "2024-11-15T10:45:00",
    "companyCode": "1000"
  }
}
```

### 3. Get MaiDao Records by Equipment

Retrieve MaiDao records for a specific piece of equipment.

```http
GET /api/v1/External/MaiDaoExternal/MaiDao/ByEquipment/{equipmentCode}
```

**Parameters:**
- `equipmentCode` (required): Equipment code
- `fromDate` (optional): Start date filter
- `toDate` (optional): End date filter
- `pageIndex` (optional): Page number (default: 1)
- `pageSize` (optional): Records per page (default: 20, max: 100)

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/MaiDao/ByEquipment/EQ001?fromDate=2024-11-01&toDate=2024-11-30" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "equipmentCode": "EQ001",
    "items": [
      {
        "maiDaoId": "123e4567-e89b-12d3-a456-************",
        "date": "15/11/2024",
        "equipmentCode": "EQ001",
        "equipmentName": "Máy cắt gỗ số 1",
        "materialCode": "MAT001",
        "materialName": "Đá mài carbide",
        "operationType": "Mài dao",
        "employeeNames": "Nguyễn Văn A, Trần Thị B",
        "status": "Completed",
        "createdDate": "2024-11-15T08:30:00"
      }
    ],
    "pagination": {
      "pageIndex": 1,
      "pageSize": 20,
      "totalCount": 25,
      "totalPages": 2,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### 4. Get Equipment List

Retrieve list of equipment available for MaiDao operations.

```http
GET /api/v1/External/MaiDaoExternal/Equipment
```

**Query Parameters:**
- `searchTerm` (optional): Search term for filtering equipment

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/Equipment?searchTerm=máy cắt" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": [
    {
      "equipmentCode": "EQ001",
      "equipmentName": "Máy cắt gỗ số 1"
    },
    {
      "equipmentCode": "EQ002",
      "equipmentName": "Máy cắt gỗ số 2"
    }
  ]
}
```

### 5. Get Materials List

Retrieve list of materials used in MaiDao operations.

```http
GET /api/v1/External/MaiDaoExternal/Materials
```

**Query Parameters:**
- `searchTerm` (optional): Search term for filtering materials

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/Materials?searchTerm=đá mài" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": [
    {
      "materialCode": "MAT001",
      "materialName": "Đá mài carbide"
    },
    {
      "materialCode": "MAT002",
      "materialName": "Đá mài kim cương"
    }
  ]
}
```

### 6. Get MaiDao Statistics

Retrieve statistical summary of MaiDao operations for a date range.

```http
GET /api/v1/External/MaiDaoExternal/Statistics
```

**Query Parameters:**
- `fromDate` (optional): Start date (defaults to 30 days ago)
- `toDate` (optional): End date (defaults to today)

**Example Request:**
```bash
curl -X GET "https://your-api-domain.com/api/v1/External/MaiDaoExternal/Statistics?fromDate=2024-11-01&toDate=2024-11-30" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "period": {
      "fromDate": "2024-11-01T00:00:00",
      "toDate": "2024-11-30T00:00:00"
    },
    "totalRecords": 145,
    "byOperationType": {
      "Mài dao": 120,
      "Bảo dưỡng": 25
    },
    "byStatus": {
      "Completed": 140,
      "In Progress": 3,
      "Pending": 2
    },
    "byEquipment": {
      "EQ001": 45,
      "EQ002": 38,
      "EQ003": 32,
      "EQ004": 30
    },
    "summary": {
      "averagePerDay": 4.83,
      "mostCommonOperationType": "Mài dao",
      "mostActiveEquipment": "EQ001"
    }
  }
}
```

## Common Operation Types

The MaiDao system typically tracks these operation types:
- **Mài dao**: Knife grinding operations
- **Bảo dưỡng**: Maintenance operations
- **Thay dao**: Knife replacement
- **Kiểm tra**: Inspection
- **Sửa chữa**: Repair

## Common Status Values

- **Created**: Newly created record
- **In Progress**: Operation is currently being performed
- **Completed**: Operation finished successfully
- **Cancelled**: Operation was cancelled
- **Pending**: Waiting for approval or resources

## Power BI Integration

### Power Query M Code for MaiDao Data

```m
let
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/MaiDaoExternal/MaiDao?pageSize=100&fromDate=2024-01-01",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    )),
    data = Source[data],
    items = data[items],
    #"Converted to Table" = Table.FromList(items, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Data" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"maiDaoId", "date", "equipmentCode", "equipmentName", 
         "materialCode", "materialName", "operationType", "employeeNames", 
         "status", "createdDate"}, 
        {"MaiDao ID", "Date", "Equipment Code", "Equipment Name", 
         "Material Code", "Material Name", "Operation Type", "Employees", 
         "Status", "Created Date"}),
    #"Changed Type" = Table.TransformColumnTypes(#"Expanded Data",{
        {"Created Date", type datetime}, 
        {"Date", type text}
    })
in
    #"Changed Type"
```

### Sample Power BI Measures

```dax
// Total MaiDao Operations
Total MaiDao Operations = COUNT('MaiDao'[MaiDao ID])

// Completed Operations Rate
Completion Rate = 
DIVIDE(
    CALCULATE(COUNT('MaiDao'[MaiDao ID]), 'MaiDao'[Status] = "Completed"),
    COUNT('MaiDao'[MaiDao ID])
) * 100

// Average Operations per Equipment
Avg Operations per Equipment = 
DIVIDE(
    COUNT('MaiDao'[MaiDao ID]),
    DISTINCTCOUNT('MaiDao'[Equipment Code])
)

// Most Active Equipment
Most Active Equipment = 
CALCULATE(
    VALUES('MaiDao'[Equipment Code]),
    FILTER(
        'MaiDao',
        CALCULATE(COUNT('MaiDao'[MaiDao ID])) = 
        MAXX(
            VALUES('MaiDao'[Equipment Code]),
            CALCULATE(COUNT('MaiDao'[MaiDao ID]))
        )
    )
)
```

## Error Handling

### HTTP Status Codes

- `200 OK`: Successful request
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Invalid or missing API key
- `403 Forbidden`: API key lacks required permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

### Error Response Format

```json
{
  "isSuccess": false,
  "code": 400,
  "message": "Equipment code is required",
  "data": null
}
```

## Rate Limiting

- Default: 1000 requests per hour per API key
- Rate limits are configurable per API key
- Exceeded limits return HTTP 429 status

## Security Features

- **Company Isolation**: API keys restricted to specific company data
- **Read-Only Access**: External API provides read-only operations
- **Data Filtering**: Sensitive internal fields are filtered out
- **Audit Logging**: All API requests are logged
- **IP Restrictions**: Optional IP address whitelisting

## Sample Integration Code

### C# Example

```csharp
using System;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class MaiDaoApiClient
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly string _baseUrl;

    public MaiDaoApiClient(string apiKey, string baseUrl)
    {
        _httpClient = new HttpClient();
        _apiKey = apiKey;
        _baseUrl = baseUrl;
    }

    public async Task<MaiDaoResponse> GetMaiDaoListAsync(
        string equipmentCode = null, 
        string operationType = null, 
        DateTime? fromDate = null,
        int pageIndex = 1, 
        int pageSize = 20)
    {
        var queryString = $"?pageIndex={pageIndex}&pageSize={pageSize}";
        
        if (!string.IsNullOrEmpty(equipmentCode))
            queryString += $"&equipmentCode={equipmentCode}";
            
        if (!string.IsNullOrEmpty(operationType))
            queryString += $"&operationType={Uri.EscapeDataString(operationType)}";
            
        if (fromDate.HasValue)
            queryString += $"&fromDate={fromDate.Value:yyyy-MM-dd}";

        var request = new HttpRequestMessage(HttpMethod.Get, 
            $"{_baseUrl}/api/v1/External/MaiDaoExternal/MaiDao{queryString}");
        
        request.Headers.Add("X-API-Key", _apiKey);

        var response = await _httpClient.SendAsync(request);
        var content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            return JsonConvert.DeserializeObject<MaiDaoResponse>(content);
        }
        
        throw new Exception($"API call failed: {response.StatusCode} - {content}");
    }

    public async Task<MaiDaoStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        var queryString = "";
        if (fromDate.HasValue)
            queryString += $"?fromDate={fromDate.Value:yyyy-MM-dd}";
        if (toDate.HasValue)
            queryString += $"{(queryString.Length > 0 ? "&" : "?")}toDate={toDate.Value:yyyy-MM-dd}";

        var request = new HttpRequestMessage(HttpMethod.Get, 
            $"{_baseUrl}/api/v1/External/MaiDaoExternal/Statistics{queryString}");
        
        request.Headers.Add("X-API-Key", _apiKey);

        var response = await _httpClient.SendAsync(request);
        var content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            var apiResponse = JsonConvert.DeserializeObject<ApiResponse<MaiDaoStatistics>>(content);
            return apiResponse.Data;
        }
        
        throw new Exception($"API call failed: {response.StatusCode} - {content}");
    }
}

public class MaiDaoResponse
{
    public bool IsSuccess { get; set; }
    public int Code { get; set; }
    public string Message { get; set; }
    public MaiDaoListData Data { get; set; }
}

public class MaiDaoListData
{
    public List<MaiDaoRecord> Items { get; set; }
    public PaginationInfo Pagination { get; set; }
}

public class MaiDaoRecord
{
    public Guid MaiDaoId { get; set; }
    public string Date { get; set; }
    public string EquipmentCode { get; set; }
    public string EquipmentName { get; set; }
    public string MaterialCode { get; set; }
    public string MaterialName { get; set; }
    public string OperationType { get; set; }
    public string EmployeeNames { get; set; }
    public string Status { get; set; }
    public DateTime CreatedDate { get; set; }
}
```

### Python Example

```python
import requests
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, List

class MaiDaoApiClient:
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }

    def get_maidao_list(self, equipment_code: Optional[str] = None, 
                       operation_type: Optional[str] = None,
                       from_date: Optional[datetime] = None,
                       to_date: Optional[datetime] = None,
                       page_index: int = 1, page_size: int = 20) -> Dict:
        """Get paginated list of MaiDao records"""
        
        url = f"{self.base_url}/api/v1/External/MaiDaoExternal/MaiDao"
        
        params = {
            'pageIndex': page_index,
            'pageSize': page_size
        }
        
        if equipment_code:
            params['equipmentCode'] = equipment_code
        if operation_type:
            params['operationType'] = operation_type
        if from_date:
            params['fromDate'] = from_date.strftime('%Y-%m-%d')
        if to_date:
            params['toDate'] = to_date.strftime('%Y-%m-%d')
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

    def get_maidao_by_equipment(self, equipment_code: str, 
                               from_date: Optional[datetime] = None,
                               to_date: Optional[datetime] = None) -> Dict:
        """Get MaiDao records for specific equipment"""
        
        url = f"{self.base_url}/api/v1/External/MaiDaoExternal/MaiDao/ByEquipment/{equipment_code}"
        
        params = {}
        if from_date:
            params['fromDate'] = from_date.strftime('%Y-%m-%d')
        if to_date:
            params['toDate'] = to_date.strftime('%Y-%m-%d')
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

    def get_statistics(self, from_date: Optional[datetime] = None, 
                      to_date: Optional[datetime] = None) -> Dict:
        """Get MaiDao statistics for date range"""
        
        # Default to last 30 days
        if not to_date:
            to_date = datetime.now()
        if not from_date:
            from_date = to_date - timedelta(days=30)
        
        url = f"{self.base_url}/api/v1/External/MaiDaoExternal/Statistics"
        
        params = {
            'fromDate': from_date.strftime('%Y-%m-%d'),
            'toDate': to_date.strftime('%Y-%m-%d')
        }
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

    def get_equipment_list(self, search_term: str = "") -> List[Dict]:
        """Get list of available equipment"""
        
        url = f"{self.base_url}/api/v1/External/MaiDaoExternal/Equipment"
        
        params = {}
        if search_term:
            params['searchTerm'] = search_term
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()['data']
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

# Usage example
if __name__ == "__main__":
    client = MaiDaoApiClient("ttf_your_api_key_here", "https://your-api-domain.com")
    
    # Get recent MaiDao operations
    operations = client.get_maidao_list(
        operation_type="Mài dao",
        from_date=datetime(2024, 11, 1),
        page_size=50
    )
    
    print(f"Found {operations['data']['pagination']['totalCount']} operations")
    
    # Get statistics for last month
    stats = client.get_statistics()
    print(f"Total operations last 30 days: {stats['data']['totalRecords']}")
    print(f"Most common operation: {stats['data']['summary']['mostCommonOperationType']}")
```

## Use Cases

### Manufacturing Analytics
- Track knife grinding frequency by equipment
- Monitor material consumption patterns
- Analyze employee productivity
- Equipment maintenance scheduling

### Maintenance Management
- Predict when equipment needs knife grinding
- Track maintenance costs and materials
- Monitor equipment downtime
- Plan preventive maintenance

### Quality Control Integration
- Correlate knife condition with product quality
- Track grinding operations before quality checks
- Monitor tool life cycles
- Equipment performance analysis

### Reporting and Dashboards
- Real-time operational dashboards
- Monthly maintenance reports
- Equipment utilization analysis
- Cost tracking and optimization

## Support and Contact

For technical support, API key requests, or integration assistance:
- **Email**: <EMAIL>
- **Technical Documentation**: https://docs.ttf-mes.com
- **System Status**: https://status.ttf-mes.com

---

*This guide covers MaiDao-specific integration. For general API authentication and setup, refer to the main Third-Party API Integration Guide.*