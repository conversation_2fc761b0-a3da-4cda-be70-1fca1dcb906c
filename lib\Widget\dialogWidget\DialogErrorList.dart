import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class DialogErrorList extends StatelessWidget {
  final List<String> message;
  const DialogErrorList({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(

      content: SingleChildScrollView(
        child:Column(
          mainAxisSize: MainAxisSize.min,
          children:List.generate(message.length, (index) =>
            // Icon(Icons.warning_rounded,size: 50.sp),
            // SizedBox(height: 15.h),
            Text(message[index],
              style: TextStyle(fontSize: 15.sp),textAlign: TextAlign.center,)
          ))),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Hủy',style: TextStyle(fontSize: 15.sp),),
        ),
      ],
    );
  }
}
