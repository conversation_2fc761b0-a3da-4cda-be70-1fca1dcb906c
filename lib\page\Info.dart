import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/confirmWorkCenterApi.dart';
import 'package:ttf/screenArguments/screenArgumentCompleteTheBigStage.dart';
import '../Storage/storageSharedPreferences.dart';
import '../element/RowDetail.dart';
import '../element/buttonSwitchingStages.dart';
import '../element/timeOut.dart';
import '../model/productionRecord.dart';
import '../model/switchingStagesApi.dart';
import '../repository/api/confirmWorkCenterApi.dart';
import '../repository/api/productionRecordApi.dart';
import '../repository/api/switchingStagesApi.dart';
import '../repository/function/infoFunction.dart';
import '../repository/function/inforSwitchingStagesFunction.dart';
import '../screenArguments/screenArgumentDetailReport.dart';
import 'LostConnect.dart';

class Infor extends StatefulWidget {
  final String barcode;
  final String token;
  final String dateTimeOld;

  const Infor({Key? key, required this.barcode, required this.token, required this.dateTimeOld}) : super(key: key);

  @override
  _InforState createState() => _InforState();
}

class _InforState extends State<Infor> {
  bool? _isLoading;
  bool? _checkBarcode;
  Data? _productionRecordData;

  DataConfirmWorkCenter? _confirmWorkCenterData;
  ConfirmWorkCenter? _confirmWorkCenter;
  ProductionRecordTTF? _productionRecordTTF;
  DataSwitchingStages? _switchingStateData;
  SwitchingStages? _switchingStages;
  bool? _isNotWifi;
  late bool _disableButton;
  bool _error = false;
  String _convertDateCreate = " ";
  late bool _timeOut;
  bool? _disableButtonTimeout;

  @override
  void initState() {
    super.initState();
    _disableButton = StorageSharedPreferences.getBool("disableButton${widget.barcode}") ?? true;
    _checkGetApi();
  }

  Future<void> _checkGetApi() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
    } else {
      await _getProductRecodingApi();
      if (!mounted) return;
      if (_productionRecordData != null && _productionRecordData!.productionRecord!.stepCode != null) {
        _getDataConfirmAndSwitching();
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _getDataConfirmAndSwitching() async {
    try {
      final responses = await ConfirmWorkCenterApi.getConfirmWorkCenter(widget.barcode, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _isNotWifi = false;
        if (responses.statusCode == 200) {
          _confirmWorkCenter = ConfirmWorkCenter.fromJson(jsonDecode(responses.body));
          if (_confirmWorkCenter!.isSuccess != false) {
            _confirmWorkCenterData = _confirmWorkCenter!.data;
          }
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _error = true;
        _timeOut = false;
      });
    }
  }

  Future<void> _getProductRecodingApi() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _timeOut = false;
      });
      final responses = await Future.wait(
          [ProductionRecordApi.getProduction(widget.barcode, widget.token), SwitchingStagesApi.getSwitchingStages(widget.barcode, widget.token)]);
      if (!mounted) return;
      setState(() {
        if (responses[0].statusCode == 200) {
          final responseDataProductionRecord = jsonDecode(responses[0].body);
          _productionRecordTTF = ProductionRecordTTF.fromJson(responseDataProductionRecord);
          if (_productionRecordTTF!.isSuccess != false) {
            _checkBarcode = false;
            _productionRecordData = _productionRecordTTF!.data;
            _convertDateCreate = InfoFunction.convertDateCreate(_productionRecordData);
          } else {
            _isLoading = false;
            _checkBarcode = true;
          }
        } else {
          _isLoading = false;
          _checkBarcode = true;
        }
        if (responses[1].statusCode == 200) {
          _switchingStages = SwitchingStages.fromJson(jsonDecode(responses[1].body));
          if (_switchingStages!.isSuccess != false) {
            _switchingStateData = _switchingStages!.data;
            debugPrint(_switchingStateData?.productName ?? "");
            _convertDateCreate = InfoSwitchingStagesFunction.convertDateCreate(_switchingStateData);
          }
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _isNotWifi = true;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _isNotWifi = true;
        _error = true;
        _timeOut = false;
      });
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }

  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButtonTimout = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }
  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(
                backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimeout ?? false)))
        : Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'Thông tin',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: _error == true
                ? Center(child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)))
                : _isNotWifi == false
                    ? _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : _checkBarcode == false
                            ? _productionRecordData != null
                                ? SingleChildScrollView(
                                    child: Column(
                                      children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
                                          decoration: const BoxDecoration(color: Colors.white),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: <Widget>[
                                              Text(
                                                "DANH SÁCH GHI NHẬN SẢN LƯỢNG",
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              SizedBox(
                                                height: 30.h,
                                              ),
                                              LabeledDetailRow(
                                                  title: 'LSX ĐT:', text: _productionRecordData!.productionRecord!.productionOrder.toString()),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(title: 'Đợt SX:', text: _productionRecordData!.productionRecord!.summary.toString()),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(
                                                  title: 'LSX SAP:', text: _productionRecordData!.productionRecord!.productionOrderSAP.toString()),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(
                                                  title: 'Mã SP:', text: _productionRecordData!.productionRecord!.productCode.toString()),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(
                                                  title: 'Tên SP:', text: _productionRecordData!.productionRecord!.productName.toString()),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(title: 'CĐ hoàn tất:', text: _productionRecordData!.productionRecord!.stepCode ?? " "),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(
                                                  title: 'Chi tiết/Cụm:',
                                                  text:
                                                      '${_productionRecordData!.productionRecord!.productAttributes} | ${_productionRecordData!.productionRecord!.productAttributesName} (Ð: ${(_productionRecordData!.productionRecordRouting!.quantityDLD ?? 0.0).round().toString()}, KÐ: ${(_productionRecordData!.productionRecordRouting!.quantityDLKD ?? 0.0).round().toString()})	'),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(
                                                title: 'Người tạo:',
                                                text: _productionRecordData!.productionRecord!.createByFullName == null
                                                    ? " "
                                                    : _productionRecordData!.productionRecord!.createByFullName.toString(),
                                              ),
                                              SizedBox(
                                                height: 15.h,
                                              ),
                                              LabeledDetailRow(title: 'Ngày tạo:', text: _convertDateCreate),
                                              SizedBox(
                                                height: 25.h,
                                              ),
                                              Container(
                                                width: double.infinity,
                                                decoration: const BoxDecoration(),
                                                child: ElevatedButton(
                                                  style: ButtonStyle(
                                                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xff0052cc)))),
                                                    side: MaterialStateProperty.all(
                                                      const BorderSide(
                                                        color: Color(0xff0052cc),
                                                      ),
                                                    ),
                                                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                  ),
                                                  onPressed: () async {
                                                    final data = await Navigator.pushNamed(context, "/DetailReport",
                                                        arguments:
                                                            ScreenArgumentDetailReport(_productionRecordData!, widget.token, widget.dateTimeOld));
                                                    if (!mounted) return;
                                                    if (data == null) return;
                                                    if (data == true) {
                                                      // await checkGetApi();
                                                      await Future.wait([
                                                        _checkGetApi(),
                                                        StorageSharedPreferences.setBool("disableButton${widget.barcode}", false)
                                                      ]);
                                                      _disableButton = StorageSharedPreferences.getBool("disableButton${widget.barcode}") ?? true;
                                                    }
                                                  },
                                                  child: Container(
                                                    margin: EdgeInsets.symmetric(vertical: 5.h),
                                                    child: Text(
                                                      'Ghi nhận sản lượng',
                                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Visibility(visible: _confirmWorkCenter == null ? false : true, child: SizedBox(height: 3.h)),
                                              Visibility(
                                                visible: _confirmWorkCenter == null ? false : true,
                                                child: Container(
                                                  width: double.infinity,
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton(
                                                    style: ButtonStyle(
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r),
                                                          side: BorderSide(
                                                              color: _disableButton == false ? const Color(0xff0052cc) : const Color(0xFFD6D6D6)))),
                                                      side: MaterialStateProperty.all(
                                                        BorderSide(
                                                          color: _disableButton == false ? const Color(0xff0052cc) : const Color(0xFFD6D6D6),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(
                                                          _disableButton == false ? const Color(0xff0052cc) : const Color(0xFFD6D6D6)),
                                                    ),
                                                    onPressed: _disableButton == false
                                                        ? () async {
                                                            final data = await Navigator.pushNamed(context, "/CompleteTheBigStage",
                                                                arguments: ScreenArgumentCompleteTheBigStage(
                                                                    _confirmWorkCenterData!,
                                                                    widget.token,
                                                                    _productionRecordTTF!.data!.productionRecord!.confirmWorkCenter.toString(),
                                                                    widget.dateTimeOld));
                                                            if (!mounted) return;
                                                            if (data == null) return;
                                                            if (data == true) {
                                                              await Future.wait([
                                                                _checkGetApi(),
                                                                StorageSharedPreferences.removeShared("disableButton${widget.barcode}"),
                                                              ]);
                                                              _disableButton =
                                                                  StorageSharedPreferences.getBool("disableButton${widget.barcode}") ?? true;
                                                            }
                                                          }
                                                        : null,
                                                    child: Container(
                                                      margin: EdgeInsets.symmetric(vertical: 5.h),
                                                      child: Text(
                                                        'Hoàn tất công đoạn lớn',
                                                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(height: 3.h),
                                              ButtonSwitchingStages(
                                                  dateTimeOld: widget.dateTimeOld,
                                                  token: widget.token,
                                                  switchingStateData: _switchingStateData,
                                                  switchingStages: _switchingStages,
                                                  refersh: _checkGetApi,
                                                  toBarcode: widget.barcode)
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  )
                                : Center(
                                    child: Text(
                                    "Không tim thấy thông tin thẻ treo ghi nhận!",
                                    style: TextStyle(fontSize: 15.sp),
                                    textAlign: TextAlign.center,
                                  ))
                            : Center(child: Text("Thẻ treo không hợp lệ!", style: TextStyle(fontSize: 15.sp)))
                    : LostConnect(checkConnect: () => _checkGetApi()));
  }
}
