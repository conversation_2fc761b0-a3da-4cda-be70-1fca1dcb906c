﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ConsumableMaterialsDeliveryModel", Schema = "MES")]
    public partial class ConsumableMaterialsDeliveryModel
    {
        [Key]
        public Guid ConsumableMaterialsDeliveryId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocumentDate { get; set; }
        [StringLength(50)]
        public string ProductionOrderCode { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(50)]
        public string ProductDetailCode { get; set; }
        [StringLength(4000)]
        public string ProductDetailName { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ProductDetailActualQty { get; set; }
        [StringLength(4000)]
        public string BOM { get; set; }
        [StringLength(50)]
        public string BOMUnit { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? BOMQty { get; set; }
        [StringLength(500)]
        public string StepName { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ActualQty { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
    }
}