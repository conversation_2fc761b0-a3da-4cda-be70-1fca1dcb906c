﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductionComponent80Model", Schema = "MES")]
    public partial class ProductionComponent80Model
    {
        [Key]
        public Guid ProductComponentId { get; set; }
        [StringLength(50)]
        public string AUFNR { get; set; }
        public int? RSNUM { get; set; }
        public int? RSPOS { get; set; }
        [StringLength(10)]
        public string XLOEK { get; set; }
        [StringLength(10)]
        public string XWAOK { get; set; }
        [StringLength(500)]
        public string MATNR { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(50)]
        public string LGORT { get; set; }
        [StringLength(10)]
        public string SOBKZ { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BDTER { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? BDMNG { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ERFMG { get; set; }
        [StringLength(10)]
        public string ERFME { get; set; }
        [StringLength(50)]
        public string KDAUF { get; set; }
        [StringLength(50)]
        public string KDPOS { get; set; }
        [StringLength(10)]
        public string SHKZG { get; set; }
        [StringLength(10)]
        public string VERID { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}