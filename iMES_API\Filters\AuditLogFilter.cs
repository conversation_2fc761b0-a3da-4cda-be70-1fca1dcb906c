using iMES_API.Infrastructures.Services;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.IO;
using Microsoft.AspNetCore.Http;

namespace iMES_API.Filters
{
    public class AuditLogFilter : IAsyncActionFilter
    {
        private readonly IAuditService _auditService;

        public AuditLogFilter(IAuditService auditService)
        {
            _auditService = auditService;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var controllerName = context.RouteData.Values["controller"]?.ToString();
            
            // Skip logging for Shared controller
            if (controllerName?.Equals("Shared", StringComparison.OrdinalIgnoreCase) == true)
            {
                await next();
                return;
            }

            // Common data collection
            var action = context.RouteData.Values["action"]?.ToString();
            var ipAddress = context.HttpContext.Connection.RemoteIpAddress?.ToString();
            var deviceName = context.HttpContext.Request.Headers["User-Agent"].ToString();
            string requestData = null;

            // Capture request data based on HTTP method
            if (context.HttpContext.Request.Method == "GET")
            {
                requestData = context.HttpContext.Request.QueryString.ToString();
            }
            else if (context.HttpContext.Request.Method == "POST")
            {
                // also capture query string
                var queryString = context.HttpContext.Request.QueryString.ToString();
                requestData = await CapturePostData(context);
                requestData += $" QueryString: {queryString}";
            }

            if (_auditService != null)
            {
                try
                {
                    await _auditService.LogAuditAsync(
                        context.HttpContext.User.Identity.IsAuthenticated 
                            ? context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value 
                            : null,
                        context.HttpContext.User.Identity.IsAuthenticated 
                            ? context.HttpContext.User.Identity.Name 
                            : "Unauthorized",
                        $"{controllerName}/{action}",
                        controllerName,
                        null,
                        null,
                        requestData,
                        ipAddress,
                        deviceName
                    );
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Audit logging failed: {ex}");
                }
            }

            await next();
        }

        private static async Task<string> CapturePostData(ActionExecutingContext context)
        {
            try
            {
                var request = context.HttpContext.Request;
                
                // Handle JSON content
                if (request.ContentType?.ToLower().Contains("application/json") == true)
                {
                    request.EnableBuffering();
                    
                    // Ensure we start from the beginning of the stream
                    request.Body.Position = 0;
                    
                    using var reader = new StreamReader(request.Body, leaveOpen: true);
                    var jsonData = await reader.ReadToEndAsync();
                    
                    // Reset position for subsequent reads
                    request.Body.Position = 0;
                    
                    return jsonData;
                }
                // Handle form data
                else
                {
                    var form = await request.ReadFormAsync();
                    if (form != null && form.Count > 0)
                    {
                        return string.Join(", ", form.Select(x => $"{x.Key}: {x.Value}"));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to capture POST data: {ex}");
            }
            return null;
        }
    }
} 