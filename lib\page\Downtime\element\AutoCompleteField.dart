import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

class AutoCompleteField extends StatefulWidget {
  final bool enabled;
  final List<String> suggestions;
  final Function(String) onSuggestionSelected;
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  final String label;
  final bool isLoading;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;

  const AutoCompleteField({
    Key? key,
    required this.enabled,
    required this.suggestions,
    required this.onSuggestionSelected,
    required this.controller,
    required this.onChanged,
    required this.label,
    this.isLoading = false,
    this.keyboardType,
    this.inputFormatters,
  }) : super(key: key);

  @override
  State<AutoCompleteField> createState() => _AutoCompleteFieldState();
}

class _AutoCompleteFieldState extends State<AutoCompleteField> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();
  final SuggestionsBoxController _suggestionsBoxController = SuggestionsBoxController();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
        if (!_isFocused) {
          _suggestionsBoxController.close();
        }
      });
    });

    // Listen to changes in the TextEditingController to update the UI accordingly
    widget.controller.addListener(_handleControllerChange);
  }

  void _handleControllerChange() {
    if (mounted) {
      setState(() {
        // This will rebuild the widget to show/hide the IconButton based on text
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    widget.controller.removeListener(_handleControllerChange);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        if (widget.label.isNotEmpty) ...[
          Expanded(
            flex: 3,
            child: Text(
              widget.label,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 10.w),
        ],
        Expanded(
          flex: widget.label.isEmpty ? 1 : 7,
          child: Stack(
            children: [
              TypeAheadField<String>(
                suggestionsBoxController: _suggestionsBoxController,
                hideOnEmpty: false,
                hideOnLoading: false,
                hideSuggestionsOnKeyboardHide: true,
                keepSuggestionsOnLoading: true,
                textFieldConfiguration: TextFieldConfiguration(
                  focusNode: _focusNode,
                  enabled: widget.enabled,
                  controller: widget.controller,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                  ),
                  maxLines: 2,
                  minLines: 1,
                  keyboardType: widget.keyboardType ?? TextInputType.text,
                  inputFormatters: widget.inputFormatters,
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      vertical: 7.h,
                      horizontal: 4.w,
                    ).copyWith(
                      right: widget.isLoading || (widget.controller.text.isNotEmpty && _isFocused) ? 24.w : 4.w,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(3.r),
                      borderSide: BorderSide(
                        width: 0.5,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(3.r),
                      borderSide: BorderSide(
                        width: 0.5,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(3.r),
                      borderSide: BorderSide(
                        width: 0.5,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    // Removed the suffixIcon to use absolute positioning instead
                  ),
                  onChanged: (value) {
                    widget.onChanged(value);
                    if (_focusNode.hasFocus) {
                      Future.delayed(Duration(milliseconds: 100), () {
                        if (mounted && _focusNode.hasFocus) {
                          _suggestionsBoxController.open();
                        }
                      });
                    }
                  },
                ),
                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                  constraints: BoxConstraints(maxHeight: 200.h),
                ),
                suggestionsCallback: (pattern) {
                  if (!_focusNode.hasFocus) return [];
                  if (widget.isLoading) {
                    return ['loading...'];
                  }
                  if (pattern.isEmpty && widget.suggestions.isNotEmpty) {
                    return widget.suggestions;
                  }
                  return widget.suggestions.where((item) => item.toLowerCase().contains(pattern.toLowerCase())).toList();
                },
                itemBuilder: (context, suggestion) {
                  if (suggestion == 'loading...') {
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            'Đang tải...',
                            style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  }

                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                    child: Text(
                      suggestion.toString(),
                      style: TextStyle(fontSize: 12.sp),
                    ),
                  );
                },
                onSuggestionSelected: (suggestion) {
                  if (suggestion != 'loading...') {
                    widget.onSuggestionSelected(suggestion);
                  }
                },
                noItemsFoundBuilder: (context) => widget.isLoading
                    ? Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 16.w,
                              height: 16.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Đang tải...',
                              style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                        child: Text(
                          'Không tìm thấy kết quả',
                          style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                        ),
                      ),
              ),
              // Loading spinner or clear button
              if (widget.isLoading || (widget.controller.text.isNotEmpty && _isFocused))
                Positioned(
                  right: 8.w,
                  top: 0,
                  bottom: 0,
                  child: widget.isLoading
                      ? Center(
                          child: SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                        )
                      : IconButton(
                          icon: Icon(Icons.close, size: 16.sp, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              widget.controller.clear();
                              widget.onChanged('');
                              if (_focusNode.hasFocus) {
                                _suggestionsBoxController.open();
                              }
                            });
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
