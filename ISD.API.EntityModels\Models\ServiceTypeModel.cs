﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ServiceTypeModel", Schema = "ghMasterData")]
    public partial class ServiceTypeModel
    {
        public ServiceTypeModel()
        {
            ServiceOrderDetailServiceModel = new HashSet<ServiceOrderDetailServiceModel>();
        }

        [Key]
        [StringLength(50)]
        public string ServiceTypeCode { get; set; }
        [StringLength(50)]
        public string ServiceTypeName { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("ServiceTypeCodeNavigation")]
        public virtual ICollection<ServiceOrderDetailServiceModel> ServiceOrderDetailServiceModel { get; set; }
    }
}