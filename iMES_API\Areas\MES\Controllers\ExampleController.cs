using iMES_API.Helpers;
using iMES_API.Infrastructures.JobSchedulers;
using iMES_API.Services;
using ISD.API.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    public class ExampleController : ControllerBaseAPI
    {
        private readonly DatabaseService _databaseService;
        private readonly IConfiguration _configuration;
        private IServiceProvider serviceProvider;

        public ExampleController(DatabaseService databaseService, IConfiguration configuration)
        {
            _databaseService = databaseService;
            _configuration = configuration;
        }

        // Test auto phan bo AutoAllocateService
        [HttpGet("TestAutoAllocateService")]
        public async Task<IActionResult> TestAutoAllocateService()
        {
            var service = new AutoAllocateService(serviceProvider);
            await service.Run();

            return Ok(1);
        }


            //[HttpGet("test-connection")]
            //public async Task<IActionResult> TestConnection(string environment = "dev")
            //{
            //    // Get the appropriate connection name based on environment
            //    string connectionName = environment.ToLower() == "prod" 
            //        ? "DefaultConnection_PRD" 
            //        : "DefaultConnection";

            //    // Test the connection
            //    bool canConnect = await _databaseService.TestConnection(connectionName);

            //    return Ok(new { 
            //        Environment = environment,
            //        ConnectionName = connectionName,
            //        CanConnect = canConnect
            //    });
            //}

            //[HttpGet("using-context")]
            //public IActionResult UsingContext(string environment = "dev")
            //{
            //    // Get the appropriate context based on environment
            //    var context = _databaseService.GetContextByEnvironment(environment);

            //    // Use the context to get some data (for example purposes)
            //    int accountCount = context.AccountModel.Count();

            //    return Ok(new { 
            //        Environment = environment,
            //        AccountCount = accountCount
            //    });
            //}
        }
} 