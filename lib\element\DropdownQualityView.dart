import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/qualityControlApi.dart';
import '../repository/function/qualityControlDetailFunction.dart';
import '../repository/function/qualityControlFunction.dart';

class DropdownType extends StatelessWidget {
  final QualityTypeList? selectedType;
  final QualityControl? qualityControl;
  final ValueChanged<QualityTypeList?> onChangeType;
  final List<QualityTypeList>? qualityTypeList;
  const DropdownType({Key? key, required this.selectedType, required this.qualityControl, required this.onChangeType, required this.qualityTypeList})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<QualityTypeList>(
        isExpanded: true,
        isDense: true,
        itemHeight: null,
        value: selectedType ?? QualityControlFunction.defaultValueQualityTypeList,
        iconSize: 15.sp,
        style: const TextStyle(color: Colors.white),
        onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : onChangeType,
        items: qualityTypeList!.map((QualityTypeList type) {
          return DropdownMenuItem<QualityTypeList>(
              value: type,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  type.catalogTextVi.toString(),
                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                ),
              ));
        }).toList(),
        selectedItemBuilder: (BuildContext context) {
          return qualityTypeList!.map<Widget>((QualityTypeList type) {
            return Text(type.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
          }).toList();
        },
      ),
    );
  }
}

class DropdownQuantityResult extends StatelessWidget {
  final ResultList? selectedResult;
  final QualityControl? qualityControl;
  final ValueChanged<ResultList?> onChangeResult;
  final List<ResultList>? lsResultList;
  const DropdownQuantityResult(
      {Key? key, required this.selectedResult, required this.qualityControl, required this.onChangeResult, required this.lsResultList})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<ResultList>(
        isExpanded: true,
        isDense: true,
        itemHeight: null,
        value: selectedResult ?? QualityControlDetailFunction.defaultResultList,
        iconSize: 15.sp,
        style: const TextStyle(color: Colors.white),
        onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : onChangeResult,
        items: lsResultList!.map((ResultList result) {
          return DropdownMenuItem<ResultList>(
              value: result,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  result.catalogTextVi.toString(),
                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                ),
              ));
        }).toList(),
        selectedItemBuilder: (BuildContext context) {
          return lsResultList!.map<Widget>((ResultList result) {
            return Text(result.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
          }).toList();
        },
      ),
    );
  }
}

class DropdownDetailResult extends StatelessWidget {
  final ResultList? selectedResultDetail;
  final QualityControl? qualityControl;
  final ValueChanged<ResultList?> getSelectedResultDetail;
  final List<ResultList> lsResultList;
  const DropdownDetailResult({
    Key? key,
    required this.selectedResultDetail,
    required this.qualityControl,
    required this.getSelectedResultDetail,
    required this.lsResultList,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
      decoration: BoxDecoration(
        border: Border.all(width: 0.5, color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(3.r),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<ResultList>(
          isExpanded: true,
          isDense: true,
          itemHeight: null,
          value: selectedResultDetail ?? QualityControlDetailFunction.defaultResultList,
          iconSize: 15.sp,
          style: const TextStyle(color: Colors.white),
          onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : getSelectedResultDetail,
          items: lsResultList.map((ResultList result) {
            return DropdownMenuItem<ResultList>(
                value: result,
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 5.h),
                  child: Text(
                    result.catalogTextVi.toString(),
                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                  ),
                ));
          }).toList(),
          selectedItemBuilder: (BuildContext context) {
            return lsResultList.map((ResultList result) {
              return Text(result.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
            }).toList();
          },
        ),
      ),
    );
  }
}
