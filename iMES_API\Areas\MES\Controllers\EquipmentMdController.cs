﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class EquipmentMdController : ControllerBaseAPI
    {
        //Get barcode
        /// <summary>API Get Barcode "Lấy barcode"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/EquipmentMd/GetBarcode?equipmentMdId={equipmentMdId}
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "id" : 484FAC30-C966-4304-A58B-DAEAB9C9B533
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///          {
        ///              "code": 200,
        ///              "isSuccess": true,
        ///              "message": null,
        ///              "data": "Upload/EquipmentMdQRCode/202207/2022-07-21T09-01-21484fac30-c966-4304-a58b-daeab9c9b533.png",
        ///              "additionalData": 
        ///              {
        ///                 "plant": "test",
        ///                 "sloc": "test",
        ///                 "warehouseNo": "123",
        ///                 "storageType": "123",
        ///                  "storageBin": "123"
        ///              }
        ///          }
        /// </remarks>
        // RESULT:  https://localhost:49101/api/v2/MES/EquipmentMd/GetBarcode?EquipmentMdId=0893bb20-f59f-4e7b-99ff-00e96574587c
        [HttpGet("GetBarcode")]
        public async Task<ActionResult> GetBarcode(Guid equipmentMdId)
        {
            var queryBC = await _context.EquipmentMdModel.FirstOrDefaultAsync(p => p.EquipmentMdId == equipmentMdId);
            var barCode = "";

            if (queryBC != null)
            {

                //EQUNR
                //IWERK
                //GEWRK
                //EQKTX
                //ILOAN
                //TPLNR
                //STORT
                //KOSTL

                var formatedEQUNR = queryBC.EQUNR.TrimStart('0');

                barCode = String.Format("<T1>{0}</T1><T2>{1}</T2><T7>{2}</T7><T8>{3}</T8><T9>{4}</T9><T10>{5}</T10><T11>{6}</T11><T12>{7}</T12><T13>{8}</T13>",
                                        queryBC.EquipmentMdId, // T1
                                        formatedEQUNR, // T2
                                        queryBC.EQKTX, // T7
                                        queryBC.ARBPL, // T8 last update: queryBC.GEWRK
                                                       //queryBC.ILOAN,// T9
                                        queryBC.ANLNR,// T9
                                        queryBC.TPLNR, // T10
                                        queryBC.STORT, // T11
                                        queryBC.KOSTL, // T12
                                        queryBC.IWERK // T13
                                        );
                var imageBC = _unitOfWork.UtilitiesRepository.GenerateQRCode("EquipmentMdQRCode", equipmentMdId.ToString(), barCode);

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = new List<object>() { new {
                        queryBC.EquipmentMdId,
                        EQUNR = formatedEQUNR,
                        queryBC.EQKTX,
                        queryBC.ARBPL, // queryBC.GEWRK
                        //queryBC.ILOAN,
                        queryBC.ANLNR,
                        queryBC.TPLNR,
                        queryBC.STORT,
                        queryBC.KOSTL,
                        queryBC.IWERK,
                        QRCode = imageBC,
                    }}
                });
            }
            return Ok(new ApiResponse
            {
                Code = 500,
                IsSuccess = false,
                Message = LanguageResource.ErrorBarCode
            });

        }

        public class EquipmentMdDto
        {
            public Guid? EquipmentMdId { get; set; }
            public string userName { get; set; }
            public string scanDate { get; set; }
            public string postDate { get; set; }
            public string EQUNR { get; set; }
            public string IWERK { get; set; }
            public string GEWRK { get; set; }
            public string EQKTX { get; set; }
            public string ILOAN { get; set; }
            public string TPLNR { get; set; }
            public string STORT { get; set; }
            public string KOSTL { get; set; }
            public int? bookQuantity { get; set; }
            public string ANLNR { get; set; }
            public string ARBPL { get; set; }
            public int id { get; set; }
        }

        [HttpPost("PostKiemKe")]
        public async Task<ActionResult> PostKiemKe([FromBody] List<EquipmentMdDto> input)
        {
            // POST data 

            foreach (var item in input)
            {
                var model = new KiemKeEquipmentMdModel();
                model.KiemKeEquipmentMdModelId = Guid.NewGuid();
                model.userName = item.userName;
                model.scanDate = item.scanDate;
                model.postDate = item.postDate;
                model.EQUNR = item.EQUNR;
                model.IWERK = item.IWERK;
                model.GEWRK = item.GEWRK;
                model.EQKTX = item.EQKTX;
                model.ILOAN = item.ILOAN;
                model.TPLNR = item.TPLNR;
                model.STORT = item.STORT;
                model.KOSTL = item.KOSTL;
                model.ANLNR = item.ANLNR; // New
                model.ARBPL = item.ARBPL; // New
                model.LineId = item.id; // New
                model.EquipmentMdId = item.EquipmentMdId; // New
                model.ActualQuantity = item.bookQuantity;
                model.CreateTime = DateTime.Now;

                _context.KiemKeEquipmentMdModel.Add(model);
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                return Ok(new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new
                {
                    Status = "Success",
                    Message = "Equipment Kiem Ke data posted successfully"
                }
            });

        }

        [AllowAnonymous]
        [HttpGet("GetEquipmentInfo")]
        public async Task<ActionResult> GetEquipmentInfo([FromQuery] string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return Ok(new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = "Input is null or empty"
                });
            }

            var idGuid = Guid.Parse(id);

            var equipment = await _context.EquipmentMdModel.FirstOrDefaultAsync(p => p.EquipmentMdId == idGuid);

            if (equipment == null)
            {
                return Ok(new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = "Equipment not found"
                });
            }


            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = equipment
            });

        }
    }
}