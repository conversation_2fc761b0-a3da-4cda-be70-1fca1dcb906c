﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    public partial class FaceCheckInOutModel
    {
        [Key]
        [StringLength(500)]
        [Unicode(false)]
        public string FaceId { get; set; }
        [StringLength(250)]
        public string PersonName { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Date { get; set; }
        public TimeSpan? CheckinTime { get; set; }
        [StringLength(50)]
        public string AliasID { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string PlaceID { get; set; }
        [StringLength(150)]
        [Unicode(false)]
        public string PersonID { get; set; }
        [StringLength(500)]
        [Unicode(false)]
        public string avatar { get; set; }
        [StringLength(250)]
        public string Place { get; set; }
        public int? type { get; set; }
        [StringLength(250)]
        public string DeviceID { get; set; }
        [StringLength(250)]
        public string DeviceName { get; set; }
    }
}