﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("HangTagModel", Schema = "MES")]
    public partial class HangTagModel
    {
        [Key]
        public Guid HangTagId { get; set; }
        [StringLength(50)]
        public string MassProductionOrder { get; set; }
        public int? BatchPrinting { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EffectiveDate { get; set; }
        [StringLength(100)]
        public string QRCode { get; set; }
        [StringLength(100)]
        public string CreatedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedTime { get; set; }
        [StringLength(100)]
        public string LastModifiedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedTime { get; set; }
        public Guid? CustomerReference { get; set; }
        [StringLength(50)]
        public string ProductAttribute { get; set; }
    }
}