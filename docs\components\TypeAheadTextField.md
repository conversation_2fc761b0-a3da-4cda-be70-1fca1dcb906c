# TypeAhead TextField Component

## Overview
A custom TypeAhead text input field that provides real-time suggestions based on user input. This component is specifically designed for LSX SAP input in the Quality Control (QC) Product screen.

## Features
- **Minimum Characters**: Requires at least 3 characters before showing suggestions
- **Real-time API Calls**: Dynamically fetches suggestions from the server
- **Clear Button**: X button to clear the input text
- **Auto-complete**: Selects suggestion and triggers detail loading
- **Input Validation**: Only accepts numeric input for LSX SAP
- **Responsive Design**: Adapts to different screen sizes

## Implementation

### Dependencies
```dart
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:flutter/services.dart';
```

### Basic Structure
```dart
Container(
  decoration: BoxDecoration(
    border: Border.all(width: 0.5, color: Colors.grey.shade400),
    borderRadius: BorderRadius.circular(3.r),
  ),
  child: Stack(
    children: [
      TypeAheadField(
        // Configuration...
      ),
      // Clear button
      Visibility(
        visible: _checkCanClearLSX(),
        child: Positioned(
          top: 1,
          right: 5,
          child: InkWell(
            onTap: () => _clearTextField(),
            child: Icon(Icons.close, size: 20.sp, color: Colors.red),
          ),
        ),
      ),
    ],
  ),
)
```

## Configuration Options

### TypeAheadField Properties

| Property | Type | Description | Value |
|----------|------|-------------|-------|
| `minCharsForSuggestions` | `int` | Minimum characters to trigger suggestions | `3` |
| `suggestionsBoxDecoration` | `SuggestionsBoxDecoration` | Styling for suggestions dropdown | Custom constraints |
| `textFieldConfiguration` | `TextFieldConfiguration` | Input field configuration | See below |
| `suggestionsCallback` | `Function` | API call function for suggestions | `filterLSXSAP(pattern)` |
| `itemBuilder` | `Function` | Builds suggestion list items | Custom container |
| `onSuggestionSelected` | `Function` | Handles suggestion selection | API call + state update |
| `noItemsFoundBuilder` | `Function` | Widget when no suggestions found | "Không tìm thấy LSX SAP" |

### TextFieldConfiguration Properties

| Property | Type | Description | Value |
|----------|------|-------------|-------|
| `enabled` | `bool` | Enable/disable input | `checkQualityControl() && _isTTFCode != true` |
| `controller` | `TextEditingController` | Text controller | `_controllerLSXSAP` |
| `keyboardType` | `TextInputType` | Keyboard type | `TextInputType.number` |
| `inputFormatters` | `List<TextInputFormatter>` | Input restrictions | `[FilteringTextInputFormatter.digitsOnly]` |
| `onChanged` | `Function` | Text change handler | Updates state and cursor position |

## API Integration

### Suggestions API Call
```dart
Future<List<String>> filterLSXSAP(String query) async {
  List<String> _lsLSXSAP = [];
  if (query.isEmpty || query.length < 3) {
    return _lsLSXSAP;
  }

  final response = await QualityControlDetailFunction.fetchLSXSAP(
    _controllerLSXSAP.text, 
    widget.user.token!.toString()
  );
  
  _lsLSXSAP = response!;
  return _lsLSXSAP;
}
```

### Detail Loading API Call
```dart
Future<void> _loadQualityControlByLSQSAP(String lsxSAP) async {
  setState(() {
    _timeOut = false;
    _isLoading = true;
    _isNotWifi = false;
  });

  var data = await QualityControlFunction.fetchQualityControlHeader(
    lsxSAP, 
    _barcode, 
    widget.user.token.toString()
  );

  if (data == null || data.qualityControl == null) {
    // Handle error
    return;
  }

  _setHeader(data);
}
```

## State Management

### Required State Variables
```dart
TextEditingController _controllerLSXSAP = TextEditingController();
bool _isLSXSAPSelected = false;
bool _isTTFCode = false;
bool _isLoading = false;
```

### State Update Methods
```dart
void _clearTextField() {
  setState(() {
    _controllerLSXSAP.text = "";
    _isLSXSAPSelected = false;
    _qualityControl = QualityControl();
    _lsCongDoanNhoMasterData = [QualityControlDetailFunction.defaultValueCongDoanNho];
    _selectedCongDoanNho = QualityControlDetailFunction.defaultValueCongDoanNho;
  });
  clearForNew();
}

bool _checkCanClearLSX() {
  return _controllerLSXSAP.text.isNotEmpty && 
         checkQualityControl() && 
         _isTTFCode != true;
}
```

## Styling

### Container Decoration
```dart
decoration: BoxDecoration(
  border: Border.all(width: 0.5, color: Colors.grey.shade400),
  borderRadius: BorderRadius.circular(3.r),
)
```

### Input Field Styling
```dart
decoration: InputDecoration(
  labelStyle: TextStyle(fontSize: 11.sp),
  contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
  isDense: true,
  border: InputBorder.none,
  focusedBorder: InputBorder.none,
  enabledBorder: InputBorder.none,
  hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
  hintStyle: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic),
)
```

### Suggestions Box Styling
```dart
suggestionsBoxDecoration: SuggestionsBoxDecoration(
  constraints: BoxConstraints(
    minHeight: 200.h,
    maxHeight: 200.h,
  ),
)
```

## Usage Example

### Complete Implementation
```dart
Row(
  children: [
    const Expanded(
      flex: 3,
      child: QualityTitleField(title: "LSX SAP:"),
    ),
    SizedBox(width: 10.w),
    Expanded(
      flex: 7,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Stack(
          children: [
            TypeAheadField(
              minCharsForSuggestions: 3,
              suggestionsBoxDecoration: SuggestionsBoxDecoration(
                constraints: BoxConstraints(
                  minHeight: 200.h,
                  maxHeight: 200.h,
                ),
              ),
              textFieldConfiguration: TextFieldConfiguration(
                enabled: checkQualityControl() && _isTTFCode != true,
                controller: _controllerLSXSAP,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(
                  hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
                  // ... other decoration properties
                ),
                onChanged: (value) {
                  setState(() {
                    _controllerLSXSAP.text = value;
                    _controllerLSXSAP.selection = TextSelection.collapsed(
                      offset: _controllerLSXSAP.text.length
                    );
                  });
                },
              ),
              suggestionsCallback: (pattern) => filterLSXSAP(pattern),
              itemBuilder: (context, suggestion) {
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                  child: Text(suggestion, style: TextStyle(fontSize: 12.sp)),
                );
              },
              onSuggestionSelected: (suggestion) async {
                if (_controllerLSXSAP.text == suggestion && _isLSXSAPSelected) {
                  return;
                }
                setState(() {
                  _controllerLSXSAP.text = suggestion;
                  _isLSXSAPSelected = true;
                });
                await _loadQualityControlByLSQSAP(suggestion);
              },
              noItemsFoundBuilder: (value) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                  child: Text("Không tìm thấy LSX SAP", style: TextStyle(fontSize: 11.sp)),
                );
              },
            ),
            // Clear button
            Visibility(
              visible: _checkCanClearLSX(),
              child: Positioned(
                top: 1,
                right: 5,
                child: InkWell(
                  onTap: _clearTextField,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  ],
)
```

## Best Practices

### Performance Optimization
1. **Debouncing**: Consider adding debouncing to reduce API calls
2. **Caching**: Cache recent suggestions to improve performance
3. **Error Handling**: Implement proper error handling for API failures

### User Experience
1. **Loading States**: Show loading indicators during API calls
2. **Empty States**: Provide clear messages when no results found
3. **Validation**: Validate input before making API calls
4. **Accessibility**: Add proper accessibility labels

### Security
1. **Input Sanitization**: Sanitize input before API calls
2. **Token Management**: Ensure proper token handling
3. **Rate Limiting**: Implement client-side rate limiting

## Troubleshooting

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| Suggestions not showing | Minimum characters not met | Ensure input has at least 3 characters |
| API call failures | Network/token issues | Check network connection and token validity |
| Clear button not visible | Conditions not met | Verify `_checkCanClearLSX()` conditions |
| Input disabled | Control conditions | Check `checkQualityControl()` and `_isTTFCode` |

### Debug Tips
1. Use `debugPrint()` to log API responses
2. Check network connectivity before API calls
3. Verify token validity and expiration
4. Monitor state variables during selection

## File Location
- **Implementation**: `lib/page/KiemTraChatLuong/BaoCaoQCSanPhamDetail.dart`
- **Lines**: 2481-2600
- **Related Functions**: Lines 2140-2160 (filterLSXSAP), Lines 2162-2200 (_loadQualityControlByLSQSAP) 