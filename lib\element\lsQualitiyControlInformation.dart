// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:path/path.dart';
// import '../Widget/dialogWidget/DialogQualityInformation.dart';
// import '../model/QuantityInformationSelectedInfor.dart';
// import '../model/mulitListImageFile.dart';
// import '../model/qualityControlApi.dart';
// import '../repository/function/imageFunction.dart';
// import '../repository/function/qualityControlFunction.dart';
// import 'ImageQuatity.dart';
// import 'imageGalleryInformationQuality.dart';
// import 'listImagePicker.dart';
//
// class LsQualityControlInformationView extends StatelessWidget {
//   final ImagePicker pickerImage;
//   final int index;
//   final List<QualityControlInformationIdList?> lsSelectedInfo;
//   final List<QualityControlInformationIdList>?lsQualityControlInformationIdList;
//   final List<int> lsGetIndexInfo;
//   final List<TextEditingController> lsTextEditingController;
//   final List<List<File>> lsGetFile;
//   final ValueChanged<MultiListImageFile> pickImage;
//   final ValueChanged<MultiListImageDeleteFile> deleteImage;
//   final List<TextEditingController> lsControllerInformation;
//   // final List<FocusNode> focusInformation;
//   final List<CheckedFileViewModel>? fileViewModel;
//   final String code;
//   final QualityControl? qualityControl;
//   final bool errorInfor;
//   final ValueChanged<int> checkErrorInformationCheck;
//   final ValueChanged<QuantityInformationSelected> checkErrorSelectedInfo;
//   final List<bool> checkVisiButtonInformation;
//   final ValueChanged<int> deleteItemList;
//   final VoidCallback resetState;
//   const LsQualityControlInformationView(
//       {Key? key,
//         required this.pickerImage,
//         required this.index,
//         required this.lsSelectedInfo,
//         required this.lsQualityControlInformationIdList,
//         required this.lsGetIndexInfo,
//         required this.lsTextEditingController,
//         required this.lsGetFile,
//         required this.pickImage,
//         required this.deleteImage,
//         required this.lsControllerInformation,
//         // required this.focusInformation,
//         required this.fileViewModel,
//         required this.code,
//         required this.qualityControl,
//         required this.errorInfor,
//         required this.checkErrorInformationCheck,
//         required this.checkErrorSelectedInfo,
//         required this.checkVisiButtonInformation,
//         required this.deleteItemList,
//         required this.resetState
//       })
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.symmetric(vertical: 5.h),
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 15.w),
//         child: Column(
//           children: [
//             IntrinsicHeight(
//                 child: Row(
//                   crossAxisAlignment:
//                   CrossAxisAlignment.stretch,
//                   children: <Widget>[
//                     Expanded(
//                       flex: 1,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           border: Border.all(
//                               color: Colors.grey.shade300,
//                               width: 0.5.w),
//                         ),
//                         padding: EdgeInsets.symmetric(
//                             vertical: 10.h,
//                             horizontal: 5.w),
//                         child: Text(
//                           "Stt",
//                           style: TextStyle(
//                               fontSize: 10.sp,
//                               fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     ),
//                     Expanded(
//                       flex: 5,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           border: Border.all(
//                               color: Colors.grey.shade300,
//                               width: 0.5.w),
//                         ),
//                         padding: EdgeInsets.symmetric(
//                             vertical: 10.h,
//                             horizontal: 10.w),
//                         child: Text(
//                           "Thông tin kiểm tra",
//                           style: TextStyle(
//                               fontSize: 10.sp,
//                               fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     ),
//                     Expanded(
//                       flex: 4,
//                       child: Container(
//                         decoration: BoxDecoration(
//                           border: Border.all(
//                               color: Colors.grey.shade300,
//                               width: 0.5.w),
//                         ),
//                         padding: EdgeInsets.symmetric(
//                             vertical: 10.h,
//                             horizontal: 10.w),
//                         child: Text(
//                           "Ghi chú",
//                           style: TextStyle(
//                               fontSize: 10.sp,
//                               fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             IntrinsicHeight(
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: <Widget>[
//                   Expanded(
//                     flex: 1,
//                     child: Container(
//                       decoration: BoxDecoration(
//                         border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
//                       ),
//                       padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
//                       child: Center(
//                         child: Text(
//                           (index + 1).toString(),
//                           style:
//                           TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     )
//                   ),
//                   Expanded(
//                     flex: 9,
//                     child: IntrinsicHeight(
//                       child: Column(
//                         mainAxisSize: MainAxisSize.min,
//                         crossAxisAlignment: CrossAxisAlignment.stretch,
//                         children: <Widget>[
//
//                           IntrinsicHeight(
//                             child: Row(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: <Widget>[
//                                 Expanded(
//                                     flex: 5,
//                                     child: _ListTypeAHead(
//                                         qualiticontrol: qualityControl, lsControllerInformation: lsControllerInformation, index: index, lsQualityControlInformationIdList: lsQualityControlInformationIdList,
//                                         lsSelectedInfo: lsSelectedInfo,
//                                         checkErrorSelectedInfo:checkErrorSelectedInfo
//                                         ,errorInfor: errorInfor, checkErrorInformationCheck: checkErrorInformationCheck),
//                                 ),
//                                 Expanded(
//                                   flex: 4,
//                                   child: Container(
//                                     // key: UniqueKey(),
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                           width: 0.5, color: Colors.grey.shade300),
//                                     ),
//                                     child: Padding(
//                                       padding: REdgeInsets.all(5),
//                                       child: Container(
//                                         padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
//                                         decoration: BoxDecoration(
//                                           border: Border.all(width: 0.5.w,  color: Colors.grey.shade400),
//                                         ),
//                                         child: TextFormField(
//                                           enabled: qualityControl!.qcType == "NVL" ? qualityControl!.qualityChecker != null?false:true:true,
//                                           maxLines: null,
//                                           textAlign: TextAlign.center,
//                                           controller: lsTextEditingController[index],
//                                           style: TextStyle(fontSize: 12.sp),
//                                           decoration: InputDecoration(
//                                             border: InputBorder.none,
//                                             focusedBorder: InputBorder.none,
//                                             enabledBorder: InputBorder.none,
//                                             errorBorder: InputBorder.none,
//                                             disabledBorder: InputBorder.none,
//                                             filled: true,
//                                             isDense: true,
//                                             fillColor: Colors.white,
//                                             hintStyle: TextStyle(fontSize: 12.sp),
//                                             contentPadding: EdgeInsets.zero,
//                                           ),
//                                           onChanged: (value){
//                                             resetState();
//                                           },
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           IntrinsicHeight(
//                             child: Container(
//                                 padding: EdgeInsets.symmetric(
//                                     vertical: 5.h, horizontal: 5.w),
//                                 decoration: BoxDecoration(
//                                   border: Border.all(
//                                       width: 0.5, color: Colors.grey.shade300),
//                                 ),
//                                 child: Row(
//                                   children: <Widget> [
//                                     Expanded(
//                                       flex: 8,
//                                 child: lsGetFile[index].isEmpty
//                                     ? _ChooseFile(qualityControl: qualityControl, pickImage: pickImage,index: index,pickerImage:pickerImage)
//                                     : _ListImage(pickImage: pickImage, lsGetFile: lsGetFile, index: index, deleteImage: deleteImage, pickerImage: pickerImage, qualityControl: qualityControl),
//                                     ),
//                                     Visibility(
//                                       visible: fileViewModel!.isNotEmpty ? true: false,
//                                       child:
//                               Expanded(
//                                 flex: 2,
//                                 child: GestureDetector(
//                                   onTap: () {
//                                     showDialog(
//                                       context: context,
//                                       builder: (BuildContext context) {
//                                         return  DialogImageQualityInformation(
//                                           title: 'THÔNG TIN KIỂM TRA ' + code,
//                                           listImage:fileViewModel,
//                                         );
//                                       },
//                                     );
//                                   },
//                                   child:Container(
//                                     padding: EdgeInsets.symmetric(vertical: 5.h),
//                                     margin: EdgeInsets.symmetric(horizontal: 5.w),
//                                     decoration:  BoxDecoration(
//                                       color: const Color(0xff0052cc),
//                                         borderRadius: BorderRadius.all(
//                                             Radius.circular(2.r),
//                                         ),
//                                     ),
//                                     child: Icon(
//                                     Icons.image,
//                                     color: Colors.white,
//                                     size: 15.sp,
//                                   ),
//                                   ),
//                                 ),
//                               ),
//                                     )],
//                                 ),
//                             ),
//                           ),
//                           Visibility(
//                             visible: checkVisiButtonInformation[index] ,
//                             child: IntrinsicHeight(
//                               child: Container(
//                                 decoration: BoxDecoration(
//                                   border: Border.all(
//                                       width: 0.5, color: Colors.grey.shade300),
//                                 ),
//                                 child: Align(
//                                     alignment: Alignment.topRight,
//                                     child: IconButton(
//                                       highlightColor: Colors.transparent,
//                                       hoverColor: Colors.transparent,
//                                       constraints: const BoxConstraints(),
//                                       iconSize: 17.sp,
//                                       color: Colors.red.shade800,
//                                       icon: const Icon(Icons.delete),
//                                       onPressed: () {
//                                         deleteItemList(index);
//                                       },
//                                     ),
//                             ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _ListTypeAHead extends StatelessWidget {
//    const _ListTypeAHead({
//     Key? key,
//     required this.qualiticontrol,
//     required this.lsControllerInformation,
//     required this.index,
//     // required this.focusInformation,
//     required this.lsQualityControlInformationIdList,
//     required this.lsSelectedInfo,
//     required this.errorInfor,
//     required this.checkErrorInformationCheck,
//     required this.checkErrorSelectedInfo
//   }) : super(key: key);
//   final QualityControl? qualiticontrol;
//   final List<TextEditingController> lsControllerInformation;
//   final int index;
//   // final List<FocusNode> focusInformation;
//   final List<QualityControlInformationIdList>? lsQualityControlInformationIdList;
//   final List<QualityControlInformationIdList?> lsSelectedInfo;
//   final bool errorInfor;
//   final ValueChanged<int> checkErrorInformationCheck;
//   final ValueChanged<QuantityInformationSelected> checkErrorSelectedInfo;
//
//    @override
//    Widget build(BuildContext context) {
//      return Container(
//        // key: UniqueKey(),
//        decoration: BoxDecoration(
//          border: Border.all(
//            width: 0.5,
//            color: Colors.grey.shade300,
//          ),
//        ),
//        child: Padding(
//          padding: REdgeInsets.all(5),
//          child:  Column(
//            children: [
//              Container(
//                decoration: BoxDecoration(
//                  border: Border.all(width: 0.5,color: Colors.grey.shade400),
//                ),
//                child: Center(
//                  child: TypeAheadField(
//                    suggestionsBoxDecoration: SuggestionsBoxDecoration(
//                      constraints: BoxConstraints(
//                        minWidth: 150.w,
//                      ),
//                    ),
//                    textFieldConfiguration: TextFieldConfiguration(
//                        enabled: qualiticontrol!.qcType == "NVL" && qualiticontrol!.qualityChecker != null ? false:true,
//                        decoration:  InputDecoration(
//                          labelStyle: TextStyle(fontSize: 11.sp),
//                          contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
//                          isDense: true,
//                          border: InputBorder.none,
//                          focusedBorder: InputBorder.none,
//                          enabledBorder: InputBorder.none,
//                        ),
//                        controller: lsControllerInformation[index],
//                        // focusNode: focusInformation[index],
//                        style: TextStyle(fontSize: 12.sp),
//                        onChanged: (value){
//                          checkErrorInformationCheck(index);
//                        }
//                    ),
//                    suggestionsCallback: (pattern) {
//                      return QualityControlFunction.filterQualityControlInformationIdList(lsQualityControlInformationIdList ?? [], pattern);
//                    },
//                    itemBuilder: (context, suggestion) {
//                      return ListTile(
//                        title: Text((suggestion as QualityControlInformationIdList).name ?? " ",style: TextStyle(fontSize: 12.sp)),
//                      );
//                    },
//                    onSuggestionSelected: (suggestion) {
//                      // lsControllerInformation[index].text = (suggestion as QualityControlInformationIdList).name ?? "";
//                      // lsSelectedInfo[index] = suggestion;
//                      checkErrorSelectedInfo(QuantityInformationSelected(index: index,qualityControlInformationIdList:suggestion as QualityControlInformationIdList));
//                    },
//                    noItemsFoundBuilder: (value) {
//                      return Padding(
//                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
//                          child: Text(
//                              "Không tìm thấy kết quả",
//                              style: TextStyle(fontSize: 11.sp)));
//                    },
//                  ),
//                ),
//
//
//              ),
//              SizedBox(
//                  height:  errorInfor == true
//                      ? 10.h
//                      : 0),
//              _ErrorValidateView(error: errorInfor ,text: "Vui lòng chọn thông tin kiểm tra",)
//            ],
//          ),
//
//        ),
//      );
//    }
// }
//
// // class _ListController extends StatelessWidget {
// //   const _ListController({
// //     Key? key,
// //     required this.qualiticontrol,
// //     required this.lsTextEditingController,
// //     required this.index,
// //     required this.resetState
// //
// //   }) : super(key: key);
// //
// //   final QualityControl? qualiticontrol;
// //   final List<TextEditingController> lsTextEditingController;
// //   final int index;
// //   final VoidCallback resetState;
// //
// //
// //   @override
// //   Widget build(BuildContext context) {
// //     return Container(
// //       // key: UniqueKey(),
// //       decoration: BoxDecoration(
// //         border: Border.all(
// //             width: 0.5, color: Colors.grey.shade300),
// //       ),
// //       child: Padding(
// //         padding: REdgeInsets.all(5),
// //         child: Container(
// //           padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
// //           decoration: BoxDecoration(
// //               border: Border.all(width: 0.5.w,  color: Colors.grey.shade400),
// //           ),
// //           child: TextFormField(
// //             enabled: qualiticontrol!.qcType == "NVL" ?qualiticontrol!.qualityChecker != null?false:true:true,
// //             maxLines: null,
// //             textAlign: TextAlign.center,
// //             controller: lsTextEditingController[index],
// //             style: TextStyle(fontSize: 12.sp),
// //             decoration: InputDecoration(
// //               border: InputBorder.none,
// //               focusedBorder: InputBorder.none,
// //               enabledBorder: InputBorder.none,
// //               errorBorder: InputBorder.none,
// //               disabledBorder: InputBorder.none,
// //               filled: true,
// //               isDense: true,
// //               fillColor: Colors.white,
// //               hintStyle: TextStyle(fontSize: 12.sp),
// //               contentPadding: EdgeInsets.zero,
// //             ),
// //             onChanged: (value){
// //               resetState();
// //             },
// //           ),
// //         ),
// //       ),
// //     );
// //   }
// // }
//
// // class _IndexNumber extends StatelessWidget {
// //   const _IndexNumber({
// //     Key? key,
// //     required this.index,
// //   }) : super(key: key);
// //
// //   final int index;
// //
// //   @override
// //   Widget build(BuildContext context) {
// //     return Container(
// //       decoration: BoxDecoration(
// //         border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
// //       ),
// //       padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
// //       child: Center(
// //         child: Text(
// //           (index + 1).toString(),
// //           style:
// //               TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
// //         ),
// //       ),
// //     );
// //   }
// // }
//
// class _ChooseFile extends StatelessWidget {
//
//   const _ChooseFile({
//     Key? key,
//     required this.qualityControl,
//     required this.pickImage,
//     required this.pickerImage,
//     required this.index
//   }) : super(key: key);
//
//   final QualityControl? qualityControl;
//   final ValueChanged<MultiListImageFile> pickImage;
//   final ImagePicker pickerImage;
//   final int index;
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//         crossAxisAlignment:
//             CrossAxisAlignment.stretch,
//         children: <Widget>[
//           GestureDetector(
//               onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null:() async {
//                 final check = await QualityControlFunction.pickImage(context);
//                 debugPrint(check.toString());
//                 if(check != null) {
//                   bool checkPermission = await ImageFunction.handlePermission(check);
//                   if (checkPermission == true) {
//                     if (check == true) {
//                       List<XFile>? selectedImages = await pickerImage.pickMultiImage(
//                                maxWidth: globalImageConfig.maxWidth,
//                                maxHeight: globalImageConfig.maxHeight,
//                                imageQuality: globalImageConfig.imageQuality,
//                              );
//                       if (selectedImages!.isEmpty) return;
//                       for (var i in selectedImages) {
//                         final itemImage = await ImageFunction.saveImageMulti(i.path);
//                         pickImage(MultiListImageFile(index: index,file: itemImage));
//                       }
//                     } else {
//                       final image = await ImagePicker().pickImage(maxWidth: globalImageConfig.maxWidth, maxHeight: globalImageConfig.maxHeight, imageQuality: globalImageConfig.imageQuality, source: ImageSource.camera);
//                       if (image == null) return;
//                       final imageProfile = await ImageFunction.saveImage(image.path);
//                       pickImage(MultiListImageFile(index: index,file: imageProfile));
//                     }
//                   }
//                 }
//               },
//               child: Container(
//                 padding: EdgeInsets.symmetric(
//                     vertical: 3.h, horizontal: 9.w),
//                 decoration: BoxDecoration(
//                   borderRadius:
//                   BorderRadius.circular(10.r),
//                   color: Colors.grey.shade100,
//                 ),
//                 child: Center( child: Text(
//                 "Chọn tệp",
//                 style: TextStyle(
//                     fontSize: 11.sp,
//                     color: Colors.blue),
//               ),
//                 ),
//               ),
//             ),
//           SizedBox(width: 10.w),
//           Center(
//             child: Text(
//               "Chưa chọn tệp nào",
//               style: TextStyle(fontSize: 11.sp),
//             ),
//           ),
//         ],
//       );
//   }
// }
//
// class _ListImage extends StatelessWidget {
//   const _ListImage({
//     Key? key,
//     required this.pickImage,
//     required this.lsGetFile,
//     required this.index,
//     required this.deleteImage,
//     required this.qualityControl,
//     required this.pickerImage
//   }) : super(key: key);
//
//   final ValueChanged<MultiListImageFile> pickImage;
//   final List<List<File>> lsGetFile;
//   final int index;
//   final ValueChanged<MultiListImageDeleteFile> deleteImage;
//   final QualityControl? qualityControl;
//   final ImagePicker pickerImage;
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: <Widget>[
//              GestureDetector(
//                 onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null:() async {
//                   final check = await QualityControlFunction.pickImage(context);
//                   debugPrint(check.toString());
//                   if(check != null) {
//                     bool checkPermission = await ImageFunction.handlePermission(check);
//                     if (checkPermission == true) {
//                       if (check == true) {
//                         List<XFile>? selectedImages = await pickerImage.pickMultiImage(
//                                maxWidth: globalImageConfig.maxWidth,
//                                maxHeight: globalImageConfig.maxHeight,
//                                imageQuality: globalImageConfig.imageQuality,
//                              );
//                         if (selectedImages!.isEmpty) return;
//                         for (var i in selectedImages) {
//                           final itemImage = await ImageFunction.saveImageMulti(i.path);
//                           pickImage(MultiListImageFile(index: index,file: itemImage));
//                         }
//                       } else {
//                         final image = await ImagePicker().pickImage(maxWidth: globalImageConfig.maxWidth, maxHeight: globalImageConfig.maxHeight, imageQuality: globalImageConfig.imageQuality, source: ImageSource.camera);
//                         if (image == null) return;
//                         final imageProfile = await ImageFunction.saveImage(image.path);
//                         pickImage(MultiListImageFile(index: index,file: imageProfile));
//                       }
//                     }
//                   }
//                 },
//                 child: Container(
//                   padding: EdgeInsets.symmetric(
//                       vertical: 3.h, horizontal: 9.w),
//                   decoration: BoxDecoration(
//                     borderRadius:
//                     BorderRadius.circular(10.r),
//                     color: Colors.grey.shade100,
//                   ),
//                   child: Text(
//                   "Chọn tệp",
//                   style: TextStyle(
//                       fontSize: 11.sp,
//                       color: Colors.blue),
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//             ),
//             SizedBox(height: 10.h),
//             Wrap(
//                 spacing: 3.w,
//                 runSpacing: 3.h,
//                 children: List.generate(
//                     lsGetFile[index].length,
//                     (indexImage) {
//                   // String filename = basename(
//                   //     lsGetFile[index][indexImage]
//                   //         .path);
//                   return SizedBox(
//                     width: 50.w,
//                     child: Stack(
//                         children: <Widget>[
//                        GestureDetector(
//                          onTap: (){
//                            Navigator.push(
//                              context,
//                              MaterialPageRoute(
//                                builder: (context) => ImageQuatity(
//                                   lsImage: lsGetFile[index],
//                                    index:indexImage
//                                ),
//                              ),
//                            );
//                          },
//                          child: ListImagePicker(
//                               fileImage: lsGetFile[index][indexImage]),
//                        ),
//                       Align(
//                         alignment: Alignment.topRight,
//                         child: IconButton(
//                           highlightColor: Colors.transparent,
//                           hoverColor: Colors.transparent,
//                           constraints: const BoxConstraints(),
//                           iconSize: 17.sp,
//                           color: Colors.red.shade800,
//                           icon: const Icon(Icons.remove_circle),
//                           onPressed: () {
//                             deleteImage(MultiListImageDeleteFile(index: index, indexImage: indexImage));
//                           },
//                         ),
//                       )
//                     ]),
//                   );
//                 })),
//             SizedBox(height: 10.h),
//           ],
//     );
//   }
// }
// class _ErrorValidateView extends StatelessWidget {
//   final bool error;
//   final String text;
//   const _ErrorValidateView({Key? key,required this.error, required this.text}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return  Visibility(
//         visible: error,
//         child: Row(
//           mainAxisAlignment:
//           MainAxisAlignment.start,
//           children: <Widget>[
//             Flexible(
//               flex: 1,
//               child: Icon(
//                   Icons.error_outline,
//                   size: 13.sp,
//                   color:
//                   Colors.red[700]),
//             ),
//             SizedBox(width: 5.w),
//             Flexible(
//                 flex: 8,
//                 child: Text(
//                     text,
//                     style: TextStyle(
//                         fontSize: 11.sp,
//                         color: Colors
//                             .red[700])))
//           ],
//         ));
//   }
// }
