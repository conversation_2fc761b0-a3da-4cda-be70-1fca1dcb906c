import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetInfoReceiSapApi {
  static Future<http.Response> getGoodsReceivedNote(String goodsReceivedNote, String token) async {
    Map<String, dynamic> data = {"GoodsReceivedNote": goodsReceivedNote};
    if (kDebugMode) {
      print(data);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehouseTransaction}GetInfoReceiSAP", data);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
