# Backend Development Patterns and Standards

## 1. Architecture Overview
```yaml
pattern: "Clean Architecture"
layers:
  presentation:
    - "API Controllers"
    - "DTOs (Data Transfer Objects)"
    - "API Response Models"
  
  application:
    - "Services"
    - "Interfaces"
    - "ViewModels"
    - "Validators"
  
  domain:
    - "Entities"
    - "Repositories Interfaces"
    - "Domain Services"
    - "Value Objects"
  
  infrastructure:
    - "Repository Implementations"
    - "Database Context"
    - "External Services"
    - "Logging"
```

## 2. API Controller Standards
```csharp
[ApiController]
[Route("api/v1/MES/[controller]")]
public class FeatureController : ControllerBase
{
    // Standard endpoint patterns
    [HttpGet]
    [Route("List")]
    public async Task<ApiResponse<List<TViewModel>>> GetList([FromQuery] SearchModel model)
    
    [HttpGet]
    [Route("{id}")]
    public async Task<ApiResponse<TViewModel>> GetById(string id)
    
    [HttpPost]
    public async Task<ApiResponse<TViewModel>> Create([FromBody] TPostModel model)
    
    [HttpPut]
    [Route("{id}")]
    public async Task<ApiResponse<TViewModel>> Update(string id, [FromBody] TPostModel model)
}
```

## 3. Response Format
```json
{
  "Code": 200,
  "IsSuccess": true,
  "Message": "Success message",
  "Data": {
    // Response data here
  }
}
```

## 4. Model Conventions
```yaml
naming_conventions:
  entities:
    - "Singular noun (e.g., Employee)"
    - "PascalCase"
    - "No prefix/suffix"
  
  viewmodels:
    - "Suffix with 'ViewModel'"
    - "Include only necessary fields"
  
  search_models:
    - "Suffix with 'SearchModel'"
    - "Include pagination fields"
  
  post_models:
    - "Suffix with 'PostModel'"
    - "Exclude computed fields"

common_fields:
  base_entity:
    - "Id: string"
    - "CompanyCode: string"
    - "CreatedDate: DateTime"
    - "CreatedBy: string"
    - "ModifiedDate: DateTime?"
    - "ModifiedBy: string?"
  
  search_model:
    - "PageNumber: int"
    - "PageSize: int"
    - "FromDate: DateTime?"
    - "ToDate: DateTime?"
```

## 5. Repository Pattern
```csharp
public interface IGenericRepository<T> where T : class
{
    Task<T> GetByIdAsync(string id);
    Task<List<T>> GetListAsync(Expression<Func<T, bool>> filter = null);
    Task<T> AddAsync(T entity);
    Task<T> UpdateAsync(T entity);
    Task DeleteAsync(string id);
}

public class GenericRepository<T> : IGenericRepository<T> where T : class
{
    protected readonly DbContext _context;
    
    // Implementation of interface methods
}
```

## 6. Service Layer Pattern
```csharp
public interface IFeatureService
{
    Task<ApiResponse<List<TViewModel>>> GetListAsync(SearchModel model);
    Task<ApiResponse<TViewModel>> GetByIdAsync(string id);
    Task<ApiResponse<TViewModel>> CreateAsync(TPostModel model);
    Task<ApiResponse<TViewModel>> UpdateAsync(string id, TPostModel model);
}

public class FeatureService : IFeatureService
{
    private readonly IFeatureRepository _repository;
    private readonly IMapper _mapper;
    
    // Implementation of interface methods with business logic
}
```

## 7. Error Handling
```yaml
exception_types:
  validation_exception:
    code: 400
    message_pattern: "Validation failed: {details}"
  
  not_found_exception:
    code: 404
    message_pattern: "{entity} not found with id: {id}"
  
  business_exception:
    code: 422
    message_pattern: "{custom message}"

error_handling_pattern:
  controller_level:
    - "Try-catch block"
    - "Map to ApiResponse"
    - "Log error details"
  
  global_level:
    - "ExceptionMiddleware"
    - "Standard error response"
    - "Logging"
```

## 8. Validation Pattern
```csharp
public class FeatureValidator : AbstractValidator<TPostModel>
{
    public FeatureValidator()
    {
        RuleFor(x => x.RequiredField)
            .NotEmpty()
            .WithMessage("Field is required");
            
        RuleFor(x => x.DateField)
            .Must(BeValidDate)
            .WithMessage("Invalid date format");
    }
}
```

## 9. Authorization Pattern
```yaml
authorization:
  attributes:
    - "[Authorize]"
    - "[RequirePermission(PermissionCode)]"
  
  permission_codes:
    format: "FEATURE_ACTION"
    examples:
      - "FEATURE_VIEW"
      - "FEATURE_CREATE"
      - "FEATURE_EDIT"
      - "FEATURE_DELETE"

  implementation:
    - "JWT Token validation"
    - "Role-based access"
    - "Permission checking"
```

## 10. Testing Pattern
```yaml
test_categories:
  unit_tests:
    - "Service layer tests"
    - "Validator tests"
    - "Helper function tests"
  
  integration_tests:
    - "Repository tests"
    - "API endpoint tests"
    - "Authorization tests"

test_naming:
  pattern: "MethodName_Scenario_ExpectedResult"
  example: "GetById_WithValidId_ReturnsEntity"

test_structure:
  arrange:
    - "Set up test data"
    - "Configure mocks"
  act:
    - "Execute method under test"
  assert:
    - "Verify results"
    - "Check mock interactions"
```

## 11. Logging Pattern
```yaml
log_levels:
  information:
    - "Method entry/exit"
    - "Successful operations"
  warning:
    - "Handled exceptions"
    - "Business rule violations"
  error:
    - "Unhandled exceptions"
    - "System failures"

log_format:
  - "Timestamp"
  - "Correlation ID"
  - "User ID"
  - "Action"
  - "Details"
```

## 12. Performance Patterns
```yaml
caching:
  implementation: "IDistributedCache"
  patterns:
    - "Cache-Aside"
    - "Cache invalidation"
  
query_optimization:
  - "Eager loading with Include()"
  - "Async/await patterns"
  - "Pagination"
  - "Index usage"

batch_operations:
  - "Bulk insert"
  - "Bulk update"
  - "Transaction management"
```
