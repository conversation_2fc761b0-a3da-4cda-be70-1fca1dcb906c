using System.ComponentModel.DataAnnotations;

namespace ISD.API.ViewModels.MES
{
    public class POInfoViewModel
    {
        /// <summary>
        /// Production Order (LSX SAP)
        /// </summary>
        [Required]
        public string PO { get; set; }

        /// <summary>
        /// Total quantity in the PO
        /// </summary>
        [Required]
        public int TotalQuantity { get; set; }

        /// <summary>
        /// Already imported quantity (sum of all previous imports)
        /// </summary>
        public int ImportedQuantity { get; set; }

        /// <summary>
        /// Remaining quantity that can be imported
        /// </summary>
        public int RemainingQuantity => TotalQuantity - ImportedQuantity;

        /// <summary>
        /// Product Code (Mã TP)
        /// </summary>
        [Required]
        public string ProductCode { get; set; }

        /// <summary>
        /// Product Name (Tên TP)
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Sales Order
        /// </summary>
        public string SO { get; set; }

        /// <summary>
        /// Sales Order Item
        /// </summary>
        public string SOItem { get; set; }

        /// <summary>
        /// Work Breakdown Structure
        /// </summary>
        public string WBS { get; set; }

        /// <summary>
        /// Number of times this PO has been imported
        /// </summary>
        public int ImportCount { get; set; }

        /// <summary>
        /// Last import sequence number
        /// </summary>
        public int LastImportSequence { get; set; }
    }
} 