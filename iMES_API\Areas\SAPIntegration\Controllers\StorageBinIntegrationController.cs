﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class StorageBinIntegrationController : ControllerBaseAPI
    {
        /// <summary>API "Tích hợp thông tin StorageBin" - Thêm / Cập nhật</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/StorageBinIntegration/StorageBin
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        ///  
        ///     {
        ///             "warehouseNo": "123",
        ///             "storageType": "123",
        ///             "storageBin": "123"
        ///     }
        ///     
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": "Tích hợp thành công StorageBin",
        ///         "data": null
        ///     }
        ///</remarks>
        [HttpPost("StorageBin")]
        public async Task<IActionResult> StorageBinIntegrationAsync([FromBody] StorageBinIntegrationViewModel IntergrationModel)
        {
            //check StorageBin is exist
            var existSB = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.WarehouseNo == IntergrationModel.WarehouseNo &&
                                                                                  x.StorageType == IntergrationModel.StorageType &&
                                                                                  x.StorageBin == IntergrationModel.StorageBin);
            if (existSB != null)
            {
                #region Update
                //update StorageBin
                existSB.StorageBin = IntergrationModel.StorageBin;
                //update StorageType
                existSB.StorageType = IntergrationModel.StorageType;
                //update LastEditIime
                existSB.LastEditTime = DateTime.Now;

                _context.Entry(existSB).State = EntityState.Modified;
                _context.SaveChanges();
                #endregion
            }
            else
            {
                #region Create
                
                existSB = new StorageBinModel();
                //create StorageBinId
                existSB.StorageBinId = Guid.NewGuid();
                //create WarehouseNo
                existSB.WarehouseNo = IntergrationModel.WarehouseNo;
                //create StorageBin
                existSB.StorageBin = IntergrationModel.StorageBin;
                //create StorageType
                existSB.StorageType = IntergrationModel.StorageType;
                //create CreateTime
                existSB.CreateTime = DateTime.Now;
                //create Actived
                existSB.Actived = true;

                _context.Entry(existSB).State = EntityState.Added;
                _context.SaveChanges();
                #endregion
            }
            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Tích hợp thành công StorageBin", Data = existSB });
        }
    }
}
