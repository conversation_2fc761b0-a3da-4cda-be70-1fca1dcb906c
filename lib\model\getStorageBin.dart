class GetStorageBin {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetStorageBin>? data;

  GetStorageBin({this.code, this.isSuccess, this.message, this.data});

  GetStorageBin.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetStorageBin>[];
      json['data'].forEach((v) {
        data!.add(DataGetStorageBin.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetStorageBin {
  String? key;
  String? value;

  DataGetStorageBin({this.key, this.value});

  DataGetStorageBin.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}