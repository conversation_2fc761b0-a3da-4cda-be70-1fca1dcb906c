import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import '../../../model/userModel.dart';
import '../../../model/maiDaoModel.dart';
import '../../../model/commonDateModel.dart';
import '../../../repository/function/listQcFunction.dart';
import '../../../utils/ui_helpers.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';
import 'package:ttf/element/RowTimeLsQC.dart';
import 'package:ttf/element/DropdownLsQC.dart';
import 'package:ttf/repository/showDateTime.dart';
import 'package:ttf/repository/function/loginFunction.dart';
import 'package:ttf/repository/function/maiDaoFunction.dart';
import 'package:ttf/utils/ui_utils.dart';
import 'package:ttf/element/ApiSuggestionField.dart';

// Custom class for operation types, similar to SalesOrgCodes
class OperationType {
  final String? code; // nullable for "All" option
  final String name;

  OperationType({this.code, required this.name});
}

// Custom class for status types
class StatusType {
  final String? code; // nullable for "All" option
  final String name;

  StatusType({this.code, required this.name});
}

// Custom dropdown for operation types
class DropdownOperationTypes extends StatelessWidget {
  final String title;
  final Function(OperationType?) onTap;
  final List<OperationType>? operationTypes;
  final OperationType? selectedOperationType;

  const DropdownOperationTypes({
    Key? key,
    required this.title,
    required this.onTap,
    this.operationTypes,
    this.selectedOperationType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<OperationType>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedOperationType,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              hint: Text(
                'Chọn $title',
                style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
              ),
              items: operationTypes?.map((type) {
                return DropdownMenuItem<OperationType>(
                  value: type,
                  child: Text(
                    type.name,
                    style: TextStyle(fontSize: 11.sp),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                onTap(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}

// Custom dropdown for status types
class DropdownStatusTypes extends StatelessWidget {
  final String title;
  final Function(StatusType?) onTap;
  final List<StatusType>? statusTypes;
  final StatusType? selectedStatusType;

  const DropdownStatusTypes({
    Key? key,
    required this.title,
    required this.onTap,
    this.statusTypes,
    this.selectedStatusType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<StatusType>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedStatusType,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              hint: Text(
                'Chọn $title',
                style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
              ),
              items: statusTypes?.map((type) {
                return DropdownMenuItem<StatusType>(
                  value: type,
                  child: Text(
                    type.name,
                    style: TextStyle(fontSize: 11.sp),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                onTap(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}

class FilterListMaiDao extends StatefulWidget {
  final String token;
  final MaiDaoSearchModel? searchModel;
  final DataUser user;
  final Function(MaiDaoSearchModel) onFilterSelected;

  const FilterListMaiDao({
    Key? key,
    required this.token,
    required this.searchModel,
    required this.user,
    required this.onFilterSelected,
  }) : super(key: key);

  @override
  _FilterListMaiDaoState createState() => _FilterListMaiDaoState();
}

class _FilterListMaiDaoState extends State<FilterListMaiDao> {
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _showErrorNoti = false;
  String _messageErrorNoti = "";
  bool _isLoadingEquipment = false;

  String? _fromTime;
  String? _toTime;
  CommonDates? _selectedCommonDates;
  List<CommonDates>? _commonDates;
  CommonDateModel? _commonDateModel;

  final TextEditingController _equipmentController = TextEditingController();
  final TextEditingController _equipmentNameController = TextEditingController();
  List<String> _equipmentSuggestions = [];
  OperationType? _selectedOperationType;
  List<OperationType> _operationTypes = [];

  StatusType? _selectedStatusType;
  List<StatusType> _statusTypes = [];

  SalesOrgCodes? _selectedSalesOrgCodes;
  List<SalesOrgCodes>? _salesOrgCodes;

  @override
  void initState() {
    super.initState();
    // Initialize operation types
    _operationTypes = [
      OperationType(code: null, name: "Tất cả"),
      OperationType(code: "Mài dao", name: "Mài dao"),
      OperationType(code: "Đắp dao", name: "Đắp dao"),
    ];

    // Initialize status types
    _statusTypes = [
      StatusType(code: null, name: "Tất cả"),
      StatusType(code: "Created", name: "Mới tạo"),
      StatusType(code: "Confirmed", name: "Đã xác nhận"),
      StatusType(code: "Completed", name: "Đã mài xong"),
      StatusType(code: "Cancelled", name: "Đã hủy"),
    ];

    // Set default status to "Tất cả"
    _selectedStatusType = _statusTypes.first;

    // Set default operation type to "Tất cả"
    _selectedOperationType = _operationTypes.first;

    _initializeData();
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      // Load company list
      final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);

      if (!mounted) return;

      if (companyList != null) {
        setState(() {
          _salesOrgCodes = companyList.map((company) => SalesOrgCodes(saleOrgCode: company.companyCode, storeName: company.companyName)).toList();

          if (_salesOrgCodes!.isNotEmpty) {
            _selectedSalesOrgCodes = widget.searchModel?.companyCode != null
                ? _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.searchModel!.companyCode,
                    orElse: () => _salesOrgCodes!.firstWhere(
                      (element) => element.saleOrgCode == widget.user.companyCode,
                      orElse: () => _salesOrgCodes!.first,
                    ),
                  )
                : _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.user.companyCode,
                    orElse: () => _salesOrgCodes!.first,
                  );
          }
        });
      }

      // Load common dates
      final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.token);

      if (!mounted) return;

      if (dataDropdown != null) {
        setState(() {
          _commonDates = dataDropdown.data?.commonDates;

          // If we have existing search model with dates, try to match with a common date
          if (widget.searchModel?.fromDate != null && widget.searchModel?.toDate != null) {
            _fromTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.fromDate!);
            _toTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.toDate!);

            // Try to find matching common date by comparing date ranges
            _selectedCommonDates = UIUtils.findMatchingCommonDate(
              _commonDates,
              widget.searchModel!.fromDate!,
              widget.searchModel!.toDate!,
            );
          } else {
            _selectedCommonDates = _commonDates?.firstWhere(
              (element) => element.catalogCode == "ThisWeek",
              orElse: () => _commonDates!.first,
            );
            _getCommonDate(_selectedCommonDates);
          }
        });
      }

      // Initialize other filters if they exist
      if (widget.searchModel != null) {
        _equipmentController.text = widget.searchModel!.equipmentCode ?? '';
        // Initialize equipment name if we have the code
        if (widget.searchModel!.equipmentCode != null && widget.searchModel!.equipmentCode!.isNotEmpty) {
          // You might want to fetch the equipment name based on the code
          // For now, just set the code
          _equipmentNameController.text = '';
        }
        if (widget.searchModel!.operationType != null) {
          _selectedOperationType = _operationTypes.firstWhere(
            (element) => element.code == widget.searchModel!.operationType,
            orElse: () => _operationTypes.first,
          );
        }
        // Initialize status if it exists in search model
        if (widget.searchModel!.status != null) {
          _selectedStatusType = _statusTypes.firstWhere(
            (element) => element.code == widget.searchModel!.status,
            orElse: () => _statusTypes.first, // Default to "Tất cả"
          );
        }
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _getCommonDate(CommonDates? selectedCommonDates) async {
    if (!mounted || selectedCommonDates == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      if (selectedCommonDates.catalogCode == "Custom") {
        setState(() {
          _isLoading = false;
          _fromTime = null;
          _toTime = null;
        });
        return;
      }

      final getCommonDateModel = await ListQCFunction.getCommonDateModel(
        selectedCommonDates.catalogCode!,
        widget.token,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _commonDateModel = getCommonDateModel;
        if (getCommonDateModel != null) {
          _fromTime = getCommonDateModel.fromDate;
          _toTime = getCommonDateModel.toDate;
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _showErrorNoti = true;
        _messageErrorNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _messageErrorNoti = error.toString();
        _showErrorNoti = true;
      });
    }
  }

  void _applyFilter() {
    final searchModel = MaiDaoSearchModel(
      equipmentCode: _equipmentController.text.isNotEmpty ? _equipmentController.text : null,
      operationType: _selectedOperationType?.code,
      fromDate: _fromTime != null ? DateTime.parse(_fromTime!) : null,
      toDate: _toTime != null ? DateTime.parse(_toTime!) : null,
      companyCode: _selectedSalesOrgCodes?.saleOrgCode ?? widget.user.companyCode ?? '',
      status: _selectedStatusType?.code,
    );

    widget.onFilterSelected(searchModel);
    Navigator.pop(context);
  }

  Future<void> _setSelectedSalesOrgCodes(SalesOrgCodes? value) async {
    if (value == null) return;

    setState(() {
      _selectedSalesOrgCodes = value;
    });

    // Fetch common dates after updating sales org codes
    await _getCommonDate(_selectedCommonDates);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => Future.value(false),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 40.h),
          child: Drawer(
            backgroundColor: Colors.white,
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                        color: const Color(0xff0052cc),
                        child: Text(
                          "Tìm kiếm Mài dao",
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            DropdownSalesOrgCodes(
                              title: 'Nhà máy',
                              onTap: _setSelectedSalesOrgCodes,
                              lsSalesOrgCodes: _salesOrgCodes,
                              selectedSalesOrgCodes: _selectedSalesOrgCodes,
                            ),
                            SizedBox(height: 10.h),
                            // Equipment Field
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "Thiết bị:",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                ApiSuggestionField(
                                  label: "",
                                  controller: _equipmentController,
                                  enabled: true,
                                  minCharsForSuggestions: 3,
                                  showCameraButton: false,
                                  hintText: "Nhập mã thiết bị (ít nhất 3 ký tự)",
                                  isLoading: _isLoadingEquipment,
                                  suggestionsCallback: (pattern) async {
                                    final suggestions = await MaiDaoFunction.fetchEquipment(
                                      widget.token,
                                      _selectedSalesOrgCodes?.saleOrgCode ?? widget.user.companyCode ?? '',
                                      pattern,
                                    );

                                    return suggestions
                                        .map((item) => item.equipmentCode != null && item.equipmentName != null
                                            ? '${item.equipmentCode} | ${item.equipmentName}'
                                            : item.equipmentCode ?? '')
                                        .toList();
                                  },
                                  onSuggestionSelected: (suggestion) async {
                                    final parts = suggestion.split(' | ');
                                    final equipmentCode = parts[0];
                                    final equipmentName = parts.length > 1 ? parts[1] : '';

                                    setState(() {
                                      _equipmentController.text = equipmentCode;
                                      _equipmentNameController.text = equipmentName;
                                    });
                                  },
                                  onChanged: (value) {
                                    setState(() {
                                      _equipmentNameController.text = '';
                                    });
                                  },
                                ),
                              ],
                            ),

                            if (_equipmentNameController.text.isNotEmpty) ...[
                              SizedBox(height: 5.h),
                              Text(
                                "Tên thiết bị:",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 5.h),
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(3.r),
                                  color: Colors.grey[100],
                                ),
                                child: Text(
                                  _equipmentNameController.text,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ],
                            SizedBox(height: 10.h),
                            DropdownOperationTypes(
                              title: 'Nghiệp vụ',
                              onTap: (value) {
                                setState(() {
                                  _selectedOperationType = value;
                                });
                              },
                              operationTypes: _operationTypes,
                              selectedOperationType: _selectedOperationType,
                            ),
                            SizedBox(height: 10.h),
                            DropdownStatusTypes(
                              title: 'Trạng thái',
                              onTap: (value) {
                                setState(() {
                                  _selectedStatusType = value;
                                });
                              },
                              statusTypes: _statusTypes,
                              selectedStatusType: _selectedStatusType,
                            ),
                            SizedBox(height: 10.h),
                            _buildDateSelection(),
                            SizedBox(height: 100.h),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (_showErrorNoti)
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.red.shade900),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 9,
                          child: Text(
                            _messageErrorNoti,
                            style: TextStyle(fontSize: 12.sp, color: Colors.white),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: IconButton(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () {
                              setState(() {
                                _showErrorNoti = false;
                              });
                            },
                            icon: const Icon(Icons.cancel),
                            iconSize: 15.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white)),
                          ),
                          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                        ),
                        onPressed: _applyFilter,
                        icon: Icon(Icons.search, size: 23.sp, color: Colors.white),
                        label: Text(
                          "Tìm kiếm",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      children: [
        DropdownCommonDates(
          title: 'Khoảng thời gian',
          onTap: (value) async {
            setState(() {
              _selectedCommonDates = value;
            });
            if (value?.catalogCode != "Custom") {
              await _getCommonDate(value);
            } else {
              setState(() {
                _fromTime = null;
                _toTime = null;
              });
            }
          },
          lsWorkCommonDates: _commonDates,
          selectedCommonDates: _selectedCommonDates,
        ),
        SizedBox(height: 10.h),
        if (_selectedCommonDates?.catalogCode == "Custom") ...[
          ColumnDateLsQC(
            title: 'Từ ngày',
            date: () => Platform.isAndroid ? _pickDateFromComplete(context) : _pickDateIOSFormComplete(context),
            displayDate: _fromTime == null ? "mm/dd/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!)),
          ),
          SizedBox(height: 10.h),
          ColumnDateLsQC(
            title: 'Đến ngày',
            date: () => Platform.isAndroid ? _pickDateToComplete(context) : _pickDateIOSToComplete(context),
            displayDate: _toTime == null ? "mm/dd/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!)),
          ),
        ] else ...[
          RowTimeLsQC(
            title: 'Từ ngày',
            time: _isLoading
                ? "Loading..."
                : _fromTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!))
                    : "",
          ),
          SizedBox(height: 10.h),
          RowTimeLsQC(
            title: 'Đến ngày',
            time: _isLoading
                ? "Loading..."
                : _toTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!))
                    : "",
          ),
        ],
      ],
    );
  }

  // Add date picker methods similar to FilterListDowntime
  Future<void> _pickDateFromComplete(BuildContext context) async {
    try {
      DateTime initialDate = _fromTime != null ? DateTime.parse(_fromTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        // If toTime exists, validate the date range
        if (_toTime != null) {
          final endDate = DateTime.parse(_toTime!);
          if (picked.isAfter(endDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày bắt đầu không thể sau ngày kết thúc";
            });
            return;
          }
        }

        setState(() {
          _fromTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateToComplete(BuildContext context) async {
    try {
      DateTime initialDate = _toTime != null ? DateTime.parse(_toTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        // If fromTime exists, validate the date range
        if (_fromTime != null) {
          final startDate = DateTime.parse(_fromTime!);
          if (picked.isBefore(startDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày kết thúc không thể trước ngày bắt đầu";
            });
            return;
          }
        }

        setState(() {
          _toTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateIOSFormComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _fromTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateIOSToComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _toTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  @override
  void dispose() {
    _equipmentController.dispose();
    _equipmentNameController.dispose();
    super.dispose();
  }
}
