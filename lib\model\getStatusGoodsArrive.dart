class GetStatusGoodsArrive {
  int? code;
  bool? isSuccess;
  // Null? message;
  List<DataGetStatusGoodsArrive>? data;
  // Null? additionalData;

  GetStatusGoodsArrive(
      {this.code,
        this.isSuccess,
        // this.message,
        this.data
        // this.additionalData
      });

  GetStatusGoodsArrive.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    // message = json['message'];
    if (json['data'] != null) {
      data = <DataGetStatusGoodsArrive>[];
      json['data'].forEach((v) {
        data!.add(DataGetStatusGoodsArrive.fromJson(v));
      });
    }
    // additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    // data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    // data['additionalData'] = this.additionalData;
    return data;
  }
}

class DataGetStatusGoodsArrive {
  String? key;
  String? value;

  DataGetStatusGoodsArrive({this.key, this.value});

  DataGetStatusGoodsArrive.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}