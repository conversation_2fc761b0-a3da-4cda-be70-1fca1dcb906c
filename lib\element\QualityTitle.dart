import 'dart:ffi';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:multiple_search_selection/helpers/extensions.dart';
import 'package:ttf/element/TitleQuality.dart';
import 'package:ttf/model/userModel.dart';

import '../model/qualityControlApi.dart';
import '../model/rawMaterialCard.dart';
import '../repository/function/qualityControlFunction.dart';
import 'RowDetail.dart';
import 'TableInfo.dart';

class QualityTitle extends StatelessWidget {
  final String title;
  const QualityTitle({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.h),
      decoration: const BoxDecoration(
        color: Color(0xff0052cc),
      ),
      child: Text(
        title,
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
      ),
    );
  }
}

class FieldQuantity extends StatelessWidget {
  final String field;
  const FieldQuantity({Key? key, required this.field}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      field,
      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
    );
  }
}

class InfoBTP extends StatelessWidget {
  final QualityControl? qualityControl;
  const InfoBTP({Key? key, required this.qualityControl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        LabeledDetailRow(title: 'Nhà máy:', text: qualityControl!.storeName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Phân xưởng:', text: qualityControl!.workShopName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Thời gian Confirm:', text: QualityControlFunction.dateFormatConfirm(qualityControl)),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Khách hàng:', text: qualityControl!.profileName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'LSX ĐT:', text: qualityControl!.lsxdt ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'ĐSX:', text: qualityControl!.dsx ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'LSX SAP:', text: qualityControl!.lsxsap ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Sản phẩm:', text: qualityControl!.productName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(
            title: 'Mã chi tiết:',
            text: '(${(qualityControl!.qty ?? 0.0).round().toString()} ${qualityControl!.unit ?? ""}) ${qualityControl!.productAttribute ?? ""}'),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Công đoạn lớn:', text: qualityControl!.workCenterName ?? ""),
        SizedBox(height: 15.h),
      ],
    );
  }
}

class InfoBTPUpdate extends StatelessWidget {
  final QualityControl? qualityControl;

  final bool isQCQANghiemThu;

  const InfoBTPUpdate({
    Key? key,
    required this.qualityControl,
    this.isQCQANghiemThu = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        LabeledDetailRow(title: 'Nhà máy:', text: qualityControl!.storeName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Phân xưởng:', text: qualityControl!.workShopName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Thời gian Confirm:', text: QualityControlFunction.dateFormatConfirm2(qualityControl)),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Khách hàng:', text: qualityControl!.profileName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'LSX ĐT:', text: qualityControl!.lsxdt ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'ĐSX:', text: qualityControl!.dsx ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'LSX SAP:', text: qualityControl!.lsxsap ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Sản phẩm:', text: qualityControl!.productName ?? ""),
        SizedBox(height: 10.h),
        if (isQCQANghiemThu) ...[
          // RowDetail(title: 'Mã chi tiết:', trailing: qualityControl!.productAttribute ?? ""),
          // SizedBox(height: 10.h),
          LabeledDetailRow(title: 'Số lượng lô hàng:', text: formatText(qualityControl!.qty.toString(), qualityControl!.unit.toString())),
          SizedBox(height: 10.h)
        ] else ...[
          LabeledDetailRow(title: 'Mã chi tiết:', text: qualityControl!.productAttribute ?? ""),
          SizedBox(height: 10.h),
          LabeledDetailRow(title: 'Số lượng chi tiết/Cụm:', text: formatText(qualityControl!.qty.toString(), qualityControl!.unit.toString())),
          SizedBox(height: 10.h),
          LabeledDetailRow(title: 'Đơn vị:', text: qualityControl!.unit ?? ""),
          SizedBox(height: 10.h),
        ],
      ],
    );
  }
}

class InfoQCSanPham extends StatelessWidget {
  final QualityControl? qualityControl;

  final bool isQCQANghiemThu;

  const InfoQCSanPham({
    Key? key,
    required this.qualityControl,
    this.isQCQANghiemThu = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        LabeledDetailRow(title: 'Nhà máy:', text: qualityControl!.storeName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Phân xưởng:', text: qualityControl!.workShopName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Thời gian Confirm:', text: QualityControlFunction.dateFormatConfirm2(qualityControl)),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Khách hàng:', text: qualityControl!.profileName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'LSX ĐT:', text: qualityControl!.lsxdt ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'ĐSX:', text: qualityControl!.dsx ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'LSX SAP:', text: qualityControl!.lsxsap ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(title: 'Sản phẩm:', text: qualityControl!.productName ?? ""),
        SizedBox(height: 10.h),
        LabeledDetailRow(
            title: 'Số lượng lô hàng:',
            text: qualityControl!.qty != null ? formatText(qualityControl!.qty.toString(), qualityControl!.unit.toString()) : ""),
        SizedBox(height: 10.h)
      ],
    );
  }
}

class InputQCMauHeader extends StatefulWidget {
  final QualityControl? qualityControl;
  // final ValueChanged<QualityControl?>? onUpdate;
  final Function(QualityControl) onQualityControlChanged; // Callback function

  InputQCMauHeader({
    Key? key,
    required this.qualityControl,
    // this.onUpdate,
    required this.onQualityControlChanged,
  }) : super(key: key);

  @override
  State<InputQCMauHeader> createState() => _InputQCMauHeaderState();
}

class _InputQCMauHeaderState extends State<InputQCMauHeader> {
  DateTime? _confirmDate;
  String _confirmDateString = "";

  // initState
  @override
  void initState() {
    super.initState();
    if (widget.qualityControl?.confirmDate != null) {
      DateTime formatedConfirmDate;

      if (widget.qualityControl!.confirmDate.charAt(10) == "T") {
        formatedConfirmDate = DateFormat("yyyy-MM-ddThh:mm:ss").parse(widget.qualityControl!.confirmDate!);
      } else {
        formatedConfirmDate = DateFormat("dd-MM-yyyy HH:mm").parse(widget.qualityControl!.confirmDate!);
      }

      _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, formatedConfirmDate);
    }
  }

  void updateQualityControl(QualityControl? newQualityControl) {
    widget.onQualityControlChanged(newQualityControl!);
  }

  void _setDate(DateTime? newDate) {
    if (newDate != null) {
      setState(() {
        _confirmDate = newDate;
        _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, _confirmDate);
        widget.qualityControl?.confirmDate = _confirmDateString;
        debugPrint(_confirmDate!.toIso8601String());
      });

      debugPrint("Confirm Date string: $_confirmDateString");
    } else {
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        // InputDetailRow(model: widget.qualityControl, title: 'Nhà gia công', property: "nhaGiaCong"),
        // Padding(
        //   padding: EdgeInsets.only(bottom: 10.h),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: <Widget>[
        //       const Expanded(flex: 3, child: FieldQuantity(field: "Thời gian Confirm:")),
        //       SizedBox(width: 10.w),
        //       Expanded(
        //         flex: 7,
        //         child: GestureDetector(
        //             onTap: widget.qualityControl?.qualityControlId != null
        //                 ? null
        //                 : () async {
        //                     // _pickDateIOS(context);
        //                     if (Platform.isAndroid) {
        //                       final getDate = await QualityControlFunction.pickDate(context, _confirmDate);
        //                       if (!mounted) return;
        //                       _setDate(getDate);
        //                     } else {
        //                       final getDateIOS = await QualityControlFunction.pickDateIOS(context);
        //                       if (!mounted) return;
        //                       _setDate(getDateIOS);
        //                     }
        //                   },
        //             child: TitleDateTimeQuality(date: _confirmDateString)),
        //       )
        //     ],
        //   ),
        // ),
        InputDetailRow(model: widget.qualityControl, title: 'Khách hàng (ID)', property: "khachHangId"),
        InputDetailRow(model: widget.qualityControl, title: 'Nhà cung cấp', property: "nhaCungCap"),
        InputDetailRow(model: widget.qualityControl, title: 'PO', property: "po"),
        // InputDetailRow(model: widget.qualityControl, title: 'LSX SAP', property: "lsxsap"),
        InputDetailRow(model: widget.qualityControl, title: 'LSX SAP', property: "lsxsap"),
        InputDetailRow(model: widget.qualityControl, title: 'Sản phẩm', property: "productName"),
        InputDetailRowDouble(model: widget.qualityControl, title: 'Số lượng lô hàng', property: "qty"),
        InputDetailRow(model: widget.qualityControl, title: 'Tình trạng MT', property: "tinhTrangMoiTruong"),
        InputDetailRow(model: widget.qualityControl, title: 'Loại NVL', property: "productType"),
        InputDetailRow(model: widget.qualityControl, title: 'Màu hoàn thiện', property: "mauHoanThien"),
      ],
    );
  }
}

class InputQCHienTruongHeader extends StatefulWidget {
  final QualityControl? qualityControl;
  // final ValueChanged<QualityControl?>? onUpdate;
  final Function(QualityControl) onQualityControlChanged; // Callback function
  final DataUser userData;

  const InputQCHienTruongHeader({
    Key? key,
    required this.qualityControl,
    // this.onUpdate,
    required this.onQualityControlChanged,
    required this.userData,
  }) : super(key: key);

  @override
  State<InputQCHienTruongHeader> createState() => _InputQCHienTruongHeaderState();
}

class _InputQCHienTruongHeaderState extends State<InputQCHienTruongHeader> {
  DateTime? _confirmDate;
  String _confirmDateString = "";

  // initState
  @override
  void initState() {
    super.initState();
    if (widget.qualityControl?.confirmDate != null) {
      DateTime formatedConfirmDate;

      if (widget.qualityControl!.confirmDate.charAt(10) == "T") {
        formatedConfirmDate = DateFormat("yyyy-MM-ddThh:mm:ss").parse(widget.qualityControl!.confirmDate!);
      } else {
        formatedConfirmDate = DateFormat("dd-MM-yyyy HH:mm").parse(widget.qualityControl!.confirmDate!);
      }

      _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, formatedConfirmDate);
    }
  }

  void updateQualityControl(QualityControl? newQualityControl) {
    widget.onQualityControlChanged(newQualityControl!);
  }

  void _setDate(DateTime? newDate) {
    if (newDate != null) {
      setState(() {
        _confirmDate = newDate;
        _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, _confirmDate);
        widget.qualityControl?.confirmDate = _confirmDateString;
        debugPrint(_confirmDate!.toIso8601String());
      });

      debugPrint("Confirm Date string: $_confirmDateString");
    } else {
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        // InputDetailRow(model: widget.userData.saleOrg, title: 'Nhà máy', property: ""),
        InputDetailRowInfo(title: 'Nhà máy', value: widget.userData.saleOrg.toString() + " | " + widget.userData.saleOrgName.toString()),
        // InputDetailRow(model: widget.qualityControl, title: 'Phân xưởng', property: ""),
        // InputDetailRow(model: widget.qualityControl, title: 'Công đoạn', property: ""),
        // InputDetailRow(model: widget.qualityControl, title: 'Ghi chú thêm', property: "", line: 3),
      ],
    );
  }
}

class InputQCNGCHeader extends StatefulWidget {
  final QualityControl? qualityControl;
  // final ValueChanged<QualityControl?>? onUpdate;
  final Function(QualityControl) onQualityControlChanged; // Callback function

  InputQCNGCHeader({
    Key? key,
    required this.qualityControl,
    // this.onUpdate,
    required this.onQualityControlChanged,
  }) : super(key: key);

  @override
  State<InputQCNGCHeader> createState() => _InputQCNGCHeaderState();
}

class _InputQCNGCHeaderState extends State<InputQCNGCHeader> {
  DateTime? _confirmDate;
  String _confirmDateString = "";

  // initState
  @override
  void initState() {
    super.initState();
    if (widget.qualityControl?.confirmDate != null) {
      DateTime formatedConfirmDate;

      if (widget.qualityControl!.confirmDate.charAt(10) == "T") {
        formatedConfirmDate = DateFormat("yyyy-MM-ddThh:mm:ss").parse(widget.qualityControl!.confirmDate!);
      } else {
        formatedConfirmDate = DateFormat("dd-MM-yyyy HH:mm").parse(widget.qualityControl!.confirmDate!);
      }

      _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, formatedConfirmDate);
    }
  }

  void updateQualityControl(QualityControl? newQualityControl) {
    widget.onQualityControlChanged(newQualityControl!);
  }

  void _setDate(DateTime? newDate) {
    if (newDate != null) {
      setState(() {
        _confirmDate = newDate;
        _confirmDateString = QualityControlFunction.getDateString2(widget.qualityControl, _confirmDate);
        widget.qualityControl?.confirmDate = _confirmDateString;
        debugPrint(_confirmDate!.toIso8601String());
      });

      debugPrint("Confirm Date string: $_confirmDateString");
    } else {
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        // InputDetailRow(model: widget.qualityControl, title: 'Nhà gia công', property: "nhaGiaCong"),
        // Padding(
        //   padding: EdgeInsets.only(bottom: 10.h),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: <Widget>[
        //       const Expanded(flex: 3, child: FieldQuantity(field: "Thời gian Confirm:")),
        //       SizedBox(width: 10.w),
        //       Expanded(
        //         flex: 7,
        //         child: GestureDetector(
        //             onTap: widget.qualityControl?.qualityControlId != null
        //                 ? null
        //                 : () async {
        //                     // _pickDateIOS(context);
        //                     if (Platform.isAndroid) {
        //                       final getDate = await QualityControlFunction.pickDate(context, _confirmDate);
        //                       if (!mounted) return;
        //                       _setDate(getDate);
        //                     } else {
        //                       final getDateIOS = await QualityControlFunction.pickDateIOS(context);
        //                       if (!mounted) return;
        //                       _setDate(getDateIOS);
        //                     }
        //                   },
        //             child: TitleDateTimeQuality(date: _confirmDateString)),
        //       )
        //     ],
        //   ),
        // ),
        InputDetailRow(model: widget.qualityControl, title: 'Khách hàng (ID)', property: "khachHangId"),
        InputDetailRow(model: widget.qualityControl, title: 'Nhà cung cấp', property: "nhaCungCap"),
        InputDetailRow(model: widget.qualityControl, title: 'PO', property: "po"),
        InputDetailRow(model: widget.qualityControl, title: 'LSX SAP', property: "lsxsap"),
        InputDetailRow(model: widget.qualityControl, title: 'Sản phẩm', property: "productName"),
        InputDetailRowDouble(model: widget.qualityControl, title: 'Số lượng lô hàng', property: "qty"),
        InputDetailRow(model: widget.qualityControl, title: 'Tình trạng MT', property: "tinhTrangMoiTruong"),
        InputDetailRow(model: widget.qualityControl, title: 'Loại NVL', property: "productType"),
        InputDetailRow(model: widget.qualityControl, title: 'Màu hoàn thiện', property: "mauHoanThien"),
      ],
    );
  }
}

class InputDetailRow extends StatefulWidget {
  final QualityControl? model;
  final String title;
  final String property;
  final Function(String)? onChanged;
  final int? line;
  final bool isNumber;

  const InputDetailRow({
    Key? key,
    required this.model,
    required this.title,
    required this.property,
    this.onChanged,
    this.line,
    this.isNumber = false,
  }) : super(key: key);

  @override
  _InputDetailRowState createState() => _InputDetailRowState();
}

class _InputDetailRowState extends State<InputDetailRow> {
  late String _initValue;

  @override
  void initState() {
    super.initState();
    // _initValue = widget.model[widget.title] ?? "";
    _initValue = widget.model?.getPropertyValue(widget.property) ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.title,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 7,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.h),
                    decoration: BoxDecoration(
                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: TextFormField(
                      maxLines: widget.line,
                      initialValue: _initValue,
                      textAlign: TextAlign.left,
                      style: TextStyle(fontSize: 12.sp),
                      enabled: widget.model?.qualityControlId == null,
                      keyboardType: widget.isNumber ? TextInputType.number : TextInputType.text,
                      inputFormatters: widget.isNumber
                          ? <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                            ]
                          : null,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 7.h),
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        filled: true,
                        fillColor: Colors.white,
                        hintStyle: TextStyle(fontSize: 12.sp),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _initValue = value;
                        });

                        // Always store as string, but validate if it's a number when isNumber is true
                        widget.model?.setPropertyValue(widget.property, value);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InputDetailRowInfo extends StatelessWidget {
  final String title;
  final String value;
  final int? line;

  const InputDetailRowInfo({
    Key? key,
    required this.title,
    required this.value,
    this.line,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 7,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.h),
                    // decoration: BoxDecoration(
                    //   border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    //   borderRadius: BorderRadius.circular(3.r),
                    // ),
                    child: TextFormField(
                      maxLines: line,
                      initialValue: value,
                      textAlign: TextAlign.left,
                      style: TextStyle(fontSize: 12.sp),
                      readOnly: true,
                      // enabled: false,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 7.h),
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        filled: true,
                        fillColor: Colors.white,
                        hintStyle: TextStyle(fontSize: 12.sp),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InputDetailRowDouble extends StatefulWidget {
  final QualityControl? model;
  final String title;
  final String property;
  final Function(String)? onChanged;

  const InputDetailRowDouble({
    Key? key,
    required this.model,
    required this.title,
    required this.property,
    this.onChanged,
  }) : super(key: key);

  @override
  _InputDetailRowDoubleState createState() => _InputDetailRowDoubleState();
}

class _InputDetailRowDoubleState extends State<InputDetailRowDouble> {
  late double? _initValue;

  @override
  void initState() {
    super.initState();
    // _initValue = widget.model[widget.title] ?? "";
    _initValue = widget.model?.getPropertyValue(widget.property);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.title,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 7,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.h),
                    decoration: BoxDecoration(
                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: TextFormField(
                      maxLines: null,
                      initialValue: _initValue?.round().toString(),
                      textAlign: TextAlign.left,
                      style: TextStyle(fontSize: 12.sp),
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                      ],
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 7.h),
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        filled: true,
                        fillColor: Colors.white,
                        hintStyle: TextStyle(fontSize: 12.sp),
                      ),
                      onChanged: (value) {
                        var valueDouble = double.tryParse(value);
                        setState(() {
                          _initValue = valueDouble; // double.tryParse(value) ?? 0.0; // double.parse(value) ?? 0.0;
                        });

                        widget.model?.setPropertyValue(widget.property, valueDouble);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InfoNVL extends StatelessWidget {
  final DataRawMeterial? dataRawMaterial;
  const InfoNVL({Key? key, required this.dataRawMaterial}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        TableInfo(
          textCL1: "Tên NCC:",
          textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.vendorName ?? "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
        ),
        TableInfoNoTop(
            textCL1: "Mã NVL:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.productCode ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        TableInfoNoTop(
            textCL1: "Tên NVL:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.productName ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        TableInfoNoTop3T(
          textCL1: "Số lượng đặt hàng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.poQuantity != null
                  ? dataRawMaterial!.poQuantity!.toStringAsFixed(2)
                  : ' ',
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng đã nhận:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.sumQuantityReceived == null
                  ? ""
                  : dataRawMaterial!.sumQuantityReceived!.toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng giao hàng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity != null
                  ? dataRawMaterial!.quantity!.toStringAsFixed(2)
                  : " ",
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Trọng lượng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity2 == null
                  ? ""
                  : dataRawMaterial!.quantity2!.toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.quantity2Unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng quy đổi:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity3 == null
                  ? ""
                  : (dataRawMaterial!.quantity3 ?? 0.0).toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.quantity3Unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        // TableInfoNoTop(
        //     textCL1: "Quy cách:",
        //     textCL2: "Nhám băng GXK-51-TQ 7''x86''xP100",
        //     colorCL1: 0xff303F9F,
        //     colorCL2: 0xFFFFFFFF
        // ),
        TableInfoNoTop(
            textCL1: "Ngày sản xuất:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.manufacturingDateStr ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        SizedBox(height: 5.h),
        // Text(
        //   "Được tạo bởi " + _dataRawMaterial!.createBy.toString() + " vào lúc " + _dataRawMaterial!.createTime.toString(),
        //   style: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade400),
        // ),
        SizedBox(height: 15.h),
        Text(
          "Số lượng giao hàng theo PO/PO Line:",
          style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 5.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Table(
                border: TableBorder.all(width: 0.5.w),
                columnWidths: <int, TableColumnWidth>{
                  0: FixedColumnWidth(100.w),
                  1: FixedColumnWidth(100.w),
                  2: FixedColumnWidth(70.w),
                  3: FixedColumnWidth(70.w),
                  4: FixedColumnWidth(70.w),
                  5: FixedColumnWidth(70.w),
                  6: FixedColumnWidth(70.w),
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: <TableRow>[
                  TableRow(
                    decoration: const BoxDecoration(
                      color: Color(0xff0052cc),
                    ),
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "PO/POLine",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SO /WBS",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL giao hàng",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "ĐVT",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL theo PO/PO Line",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL đã giao",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL còn lại",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: List.generate(
                  (dataRawMaterial == null ? [] : dataRawMaterial!.poDetailResponses ?? []).length,
                  (index) => Table(
                    border: TableBorder(
                      left: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      right: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      bottom: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      verticalInside: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                    ),
                    columnWidths: <int, TableColumnWidth>{
                      0: FixedColumnWidth(100.w),
                      1: FixedColumnWidth(100.w),
                      2: FixedColumnWidth(70.w),
                      3: FixedColumnWidth(70.w),
                      4: FixedColumnWidth(70.w),
                      5: FixedColumnWidth(70.w),
                      6: FixedColumnWidth(70.w),
                    },
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    children: <TableRow>[
                      TableRow(
                        children: <Widget>[
                          Container(
                              margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                              child: Column(children: <Widget>[
                                Text(
                                  dataRawMaterial!.poDetailResponses![index].po ?? "",
                                  style: TextStyle(fontSize: 12.sp),
                                  textAlign: TextAlign.center,
                                ),
                                Text(
                                  dataRawMaterial!.poDetailResponses![index].poLine ?? "",
                                  style: TextStyle(fontSize: 12.sp),
                                  textAlign: TextAlign.center,
                                ),
                              ])),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: dataRawMaterial!.poDetailResponses == null
                                ? Text(" ", style: TextStyle(fontSize: 12.sp))
                                : (dataRawMaterial!.poDetailResponses![index].so != null && dataRawMaterial!.poDetailResponses![index].so != "") &&
                                        (dataRawMaterial!.poDetailResponses![index].soLine != null &&
                                            dataRawMaterial!.poDetailResponses![index].soLine != "") &&
                                        (dataRawMaterial!.poDetailResponses![index].wbs == null ||
                                            dataRawMaterial!.poDetailResponses![index].wbs == "")
                                    ? Column(children: <Widget>[
                                        Text(
                                          dataRawMaterial!.poDetailResponses![index].so ?? "",
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.center,
                                        ),
                                        Text(
                                          dataRawMaterial!.poDetailResponses![index].soLine ?? "",
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.center,
                                        ),
                                      ])
                                    : (dataRawMaterial!.poDetailResponses![index].so == null ||
                                                dataRawMaterial!.poDetailResponses![index].so == "") &&
                                            (dataRawMaterial!.poDetailResponses![index].soLine == null ||
                                                dataRawMaterial!.poDetailResponses![index].soLine == "") &&
                                            (dataRawMaterial!.poDetailResponses![index].wbs != null &&
                                                dataRawMaterial!.poDetailResponses![index].wbs != "")
                                        ? Text(
                                            dataRawMaterial!.poDetailResponses![index].wbs ?? "",
                                            style: TextStyle(fontSize: 12.sp),
                                            textAlign: TextAlign.center,
                                          )
                                        : Text(
                                            "Tồn trơn",
                                            style: TextStyle(fontSize: 12.sp),
                                            textAlign: TextAlign.center,
                                          ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityImported == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityImported!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].unit ?? "",
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityByPO == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityByPO!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityReceived == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityReceived!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].remainQuantity == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].remainQuantity!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 15.h),
              // Text(
              //   "Được tạo bởi " + _dataRawMaterial!.createBy.toString() + " vào lúc " + _dataRawMaterial!.createTime.toString(),
              //   style: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade400),
              // ),
            ],
          ),
        ),
      ],
    );
  }
}

String formatText(String text, String unit) {
  bool isDecimal = false;
  if (unit == 'M' || unit == 'M2' || unit == 'M3') {
    isDecimal = true;
  }
  double? number = double.tryParse(text);
  if (number != null) {
    if (isDecimal) {
      return number.toStringAsFixed(3);
    } else {
      return number.round().toString();
    }
  }
  return text;
}

class InfoNVLUpdate extends StatelessWidget {
  final DataRawMeterial? dataRawMaterial;

  const InfoNVLUpdate({Key? key, required this.dataRawMaterial}) : super(key: key);

  Widget buildTableInfo(String textCL1, String textCL2, [bool isNoTop = true]) {
    if (isNoTop) {
      return TableInfoNoTop(
        textCL1: textCL1,
        textCL2: textCL2,
        colorCL1: 0xff0052cc,
        colorCL2: 0xFFFFFFFF,
      );
    }
    return TableInfo(
      textCL1: textCL1,
      textCL2: textCL2,
      colorCL1: 0xff0052cc,
      colorCL2: 0xFFFFFFFF,
    );
  }

  Widget buildTableInfoNoTop3T(String textCL1, String textCL2, String unit) {
    return TableInfoNoTop3T(
      textCL1: formatText(textCL1, unit),
      textCL2: formatText(textCL2, unit),
      textCL3: formatText(unit, ""),
      colorCL1: 0xff0052cc,
      colorCL2: 0xFFFFFFFF,
      colorCL3: 0xFFFFFFFF,
    );
  }

  Widget buildHeaderCell(String text) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }

  TableRow buildHeaderRow() {
    return TableRow(
      decoration: const BoxDecoration(color: Color(0xff0052cc)),
      children: [
        buildHeaderCell("PO/POLine"),
        buildHeaderCell("SO /WBS"),
        buildHeaderCell("SL giao hàng"),
        buildHeaderCell("ĐVT"),
        buildHeaderCell("SL theo PO/PO Line"),
        buildHeaderCell("SL đã giao"),
        buildHeaderCell("SL còn lại"),
      ],
    );
  }

  Widget buildTextContainer(dynamic text, {double? fontSize}) {
    String displayText;
    if (text is double) {
      if (text > 0.0 && text < 1.0) {
        displayText = text.toStringAsFixed(3);
      } else {
        displayText = text.round().toString();
      }
    } else {
      displayText = text ?? "";
    }

    return Container(
      margin: const EdgeInsets.all(0.0),
      child: Text(
        displayText,
        style: TextStyle(fontSize: fontSize ?? 12.sp),
        textAlign: TextAlign.center,
      ),
    );
  }

  TableRow buildRow(PoDetailResponses item) {
    return TableRow(
      children: <Widget>[
        Container(
          margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
          child: Column(children: <Widget>[
            buildTextContainer(item.po),
            buildTextContainer(item.poLine),
          ]),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
          child: item.so != null && item.so != "" && item.soLine != null && item.soLine != "" && (item.wbs == null || item.wbs == "")
              ? Column(children: <Widget>[
                  buildTextContainer(item.so),
                  buildTextContainer(item.soLine),
                ])
              : item.so == null || item.so == "" && item.soLine == null || item.soLine == "" && item.wbs != null && item.wbs != ""
                  ? buildTextContainer(item.wbs, fontSize: 10.sp)
                  : buildTextContainer("Tồn trơn"),
        ),
        buildTextContainer(item.quantityImported),
        buildTextContainer(item.unit),
        buildTextContainer(item.quantityByPO),
        buildTextContainer(item.quantityReceived),
        buildTextContainer(item.remainQuantity),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Table headerTable = Table(
      border: TableBorder.all(width: 0.5.w),
      columnWidths: <int, TableColumnWidth>{
        0: FixedColumnWidth(100.w),
        1: FixedColumnWidth(100.w),
        2: FixedColumnWidth(70.w),
        3: FixedColumnWidth(70.w),
        4: FixedColumnWidth(70.w),
        5: FixedColumnWidth(70.w),
        6: FixedColumnWidth(70.w),
      },
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: [buildHeaderRow()],
    );

    List<Widget> rowTables = (dataRawMaterial?.poDetailResponses ?? [])
        .map((item) => Table(
              border: TableBorder(
                left: BorderSide(
                  color: Colors.black,
                  width: 0.5.w,
                ),
                right: BorderSide(
                  color: Colors.black,
                  width: 0.5.w,
                ),
                bottom: BorderSide(
                  color: Colors.black,
                  width: 0.5.w,
                ),
                verticalInside: BorderSide(
                  color: Colors.black,
                  width: 0.5.w,
                ),
              ),
              columnWidths: <int, TableColumnWidth>{
                0: FixedColumnWidth(100.w),
                1: FixedColumnWidth(100.w),
                2: FixedColumnWidth(70.w),
                3: FixedColumnWidth(70.w),
                4: FixedColumnWidth(70.w),
                5: FixedColumnWidth(70.w),
                6: FixedColumnWidth(70.w),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [buildRow(item)],
            ))
        .toList();

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        buildTableInfo("Tên NCC:", dataRawMaterial?.vendorName ?? "", false),
        buildTableInfo("Mã NVL:", dataRawMaterial?.productCode ?? ""),
        buildTableInfo("Tên NVL:", dataRawMaterial?.productName ?? ""),
        buildTableInfoNoTop3T("Số lượng đặt hàng:", dataRawMaterial?.poQuantity?.toString() ?? ' ', dataRawMaterial?.unit ?? ""),
        buildTableInfoNoTop3T("Số lượng đã nhận:", dataRawMaterial?.sumQuantityReceived?.toString() ?? "", dataRawMaterial?.unit ?? ""),
        buildTableInfoNoTop3T("Số lượng giao hàng:", dataRawMaterial?.quantity?.toString() ?? " ", dataRawMaterial?.unit ?? ""),
        buildTableInfoNoTop3T("Trọng lượng:", dataRawMaterial?.quantity2?.toString() ?? "", dataRawMaterial?.quantity2Unit ?? ""),
        buildTableInfoNoTop3T("Số lượng quy đổi:", dataRawMaterial?.quantity3?.toString() ?? "", dataRawMaterial?.quantity3Unit ?? ""),
        buildTableInfo("Ngày sản xuất:", dataRawMaterial?.manufacturingDateStr ?? ""),
        buildTableInfo("Tình trạng giao hàng:", dataRawMaterial?.manufacturingDateStr ?? ""),
        SizedBox(height: 5.h),
        SizedBox(height: 15.h),
        Text(
          "Số lượng giao hàng theo PO/PO Line:",
          style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 5.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[headerTable, ...rowTables, SizedBox(height: 15.h)],
          ),
        ),
      ],
    );
  }
}
