﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("FavoriteReportModel", Schema = "tMasterData")]
    public partial class FavoriteReportModel
    {
        [Key]
        public Guid Id { get; set; }
        public Guid AccountId { get; set; }
        public Guid PageId { get; set; }

        [ForeignKey("AccountId")]
        [InverseProperty("FavoriteReportModel")]
        public virtual AccountModel Account { get; set; }
        [ForeignKey("PageId")]
        [InverseProperty("FavoriteReportModel")]
        public virtual PageModel Page { get; set; }
    }
}