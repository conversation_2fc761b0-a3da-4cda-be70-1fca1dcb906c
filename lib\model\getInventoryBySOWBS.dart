import 'dart:ffi';

class GetInventoryBySOWBS {
  String? productCode;
  String? plant;
  String? sloc;
  String? batchNumber;
  List<SowbSs>? sowbSs;

  GetInventoryBySOWBS({this.productCode, this.plant, this.sloc, this.batchNumber, this.sowbSs});

  GetInventoryBySOWBS.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    plant = json['plant'];
    sloc = json['sloc'];
    batchNumber = json['batchNumber'];
    if (json['sowbSs'] != null) {
      sowbSs = <SowbSs>[];
      json['sowbSs'].forEach((v) {
        sowbSs!.add(SowbSs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['batchNumber'] = batchNumber;
    if (sowbSs != null) {
      data['sowbSs'] = sowbSs!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SowbSs {
  String? so;
  String? soLine;
  String? wbs;
  double? soLuong;
  double? soLuongSX;

  SowbSs({
    this.so,
    this.soLine,
    this.wbs,
    this.soLuong,
    this.soLuongSX,
  });

  SowbSs.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    soLuong = json['soLuong'];
    soLuongSX = json['soLuongSX'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['soLuong'] = soLuong;
    data['soLuongSX'] = soLuongSX;
    return data;
  }
}
