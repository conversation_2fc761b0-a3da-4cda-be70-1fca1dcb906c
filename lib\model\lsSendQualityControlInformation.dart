import 'dart:io';

class LsSendQualityControlInformation {
  final String qualityControlQCInformationId;
  final String qualityControlInformationId;
  final String notes;
  final List<File> lsImageFile;
  const LsSendQualityControlInformation(
    this.qualityControlQCInformationId,
    this.qualityControlInformationId,
    this.notes,
    this.lsImageFile,
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlInformationId'] = qualityControlInformationId;
    data['notes'] = notes;
    data['file'] = lsImageFile;
    data['qualityControl_QCInformation_Id'] = qualityControlQCInformationId;
    return data;
  }
}

class LsSendQualityControlInformation2 {
  final String qualityControlQCInformationId;
  final String qualityControlInformationId;
  final String soSanPhamLoi;
  final String notes;
  final String outcomeStatus;
  final List<File> lsImageFile;
  const LsSendQualityControlInformation2(
    this.qualityControlQCInformationId,
    this.qualityControlInformationId,
    this.soSanPhamLoi,
    this.notes,
    this.outcomeStatus,
    this.lsImageFile,
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlInformationId'] = qualityControlInformationId;
    data['soSanPhamLoi'] = soSanPhamLoi;
    data['notes'] = notes;
    data['file'] = lsImageFile;
    data['qualityControl_QCInformation_Id'] = qualityControlQCInformationId;
    return data;
  }
}
