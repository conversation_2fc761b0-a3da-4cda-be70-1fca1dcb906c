﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StatusTransitionModel", Schema = "Task")]
    public partial class StatusTransitionModel
    {
        [Key]
        public Guid StatusTransitionId { get; set; }
        [StringLength(200)]
        public string TransitionName { get; set; }
        public string Description { get; set; }
        public Guid? WorkFlowId { get; set; }
        public Guid? FromStatusId { get; set; }
        public Guid? ToStatusId { get; set; }
        public bool? isAssigneePermission { get; set; }
        public bool? isReporterPermission { get; set; }
        [StringLength(50)]
        public string Color { get; set; }
        public bool? isRequiredComment { get; set; }
        public bool? isCreateUserPermission { get; set; }
        [StringLength(20)]
        [Unicode(false)]
        public string StatusTransitionIn { get; set; }
        [StringLength(20)]
        [Unicode(false)]
        public string StatusTransitionOut { get; set; }
        public bool? isAutomaticTransitions { get; set; }
        [StringLength(200)]
        public string BranchName { get; set; }
        public int? BranchPositionLeft { get; set; }
        public int? BranchPositionRight { get; set; }
        [StringLength(200)]
        [Unicode(false)]
        public string BranchIn { get; set; }
        [StringLength(200)]
        [Unicode(false)]
        public string BranchOut { get; set; }
        [StringLength(200)]
        public string unsignedBranchName { get; set; }

        [ForeignKey("FromStatusId")]
        [InverseProperty("StatusTransitionModelFromStatus")]
        public virtual TaskStatusModel FromStatus { get; set; }
        [ForeignKey("ToStatusId")]
        [InverseProperty("StatusTransitionModelToStatus")]
        public virtual TaskStatusModel ToStatus { get; set; }
        [ForeignKey("WorkFlowId")]
        [InverseProperty("StatusTransitionModel")]
        public virtual WorkFlowModel WorkFlow { get; set; }
    }
}