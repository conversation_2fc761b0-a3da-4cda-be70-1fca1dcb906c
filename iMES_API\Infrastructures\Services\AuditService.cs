using ISD.API.EntityModels.Models;
using ISD.API.EntityModels.Data;
using System;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.Services
{
    public class AuditService : IAuditService
    {
        private readonly EntityDataContext _context;

        public AuditService(EntityDataContext context)
        {
            _context = context;
        }

        public async Task LogAuditAsync(
            string userId,
            string username,
            string action,
            string entityName,
            string entityId,
            string oldValue,
            string newValue,
            string ipAddress,
            string deviceName)
        {
            var audit = new AuditLogModel
            {
                UserId = userId,
                Username = username,
                Action = action,
                EntityName = entityName,
                EntityId = entityId,
                OldValue = oldValue,
                NewValue = newValue,
                IpAddress = ipAddress,
                DeviceName = deviceName,
                Timestamp = DateTime.UtcNow
            };

            _context.AuditLogModel.Add(audit);
            await _context.SaveChangesAsync();
        }
    }
} 