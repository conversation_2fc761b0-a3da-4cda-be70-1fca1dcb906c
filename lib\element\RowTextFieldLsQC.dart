import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class RowTextFieldLsQC extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final FocusNode focusNode;

  const RowTextFieldLsQC({Key? key, required this.title, required this.controller, required this.focusNode}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          decoration: BoxDecoration(border: Border.all(width: 0.5.w, color: Colors.grey.shade500), borderRadius: BorderRadius.circular(3.r)),
          child: TextForm<PERSON>ield(
            maxLines: null,
            focusNode: focusNode,
            controller: controller,
            style: TextStyle(fontSize: 12.sp),
            decoration: InputDecoration(
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              // hintText: "Nhập tài khoản",
              filled: true,
              isDense: true,
              fillColor: Colors.white,
              hintStyle: TextStyle(fontSize: 12.sp),
              contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
            ),
          ),
        ),
      ],
    );
  }
}
