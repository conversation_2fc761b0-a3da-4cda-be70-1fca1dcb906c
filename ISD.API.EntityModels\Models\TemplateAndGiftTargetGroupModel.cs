﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TemplateAndGiftTargetGroupModel", Schema = "Marketing")]
    public partial class TemplateAndGiftTargetGroupModel
    {
        public TemplateAndGiftTargetGroupModel()
        {
            TemplateAndGiftCampaignModel = new HashSet<TemplateAndGiftCampaignModel>();
            TemplateAndGiftMemberModel = new HashSet<TemplateAndGiftMemberModel>();
        }

        [Key]
        public Guid Id { get; set; }
        public int TargetGroupCode { get; set; }
        [StringLength(50)]
        public string TargetGroupName { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool? Actived { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }

        [ForeignKey("CreateBy")]
        [InverseProperty("TemplateAndGiftTargetGroupModelCreateByNavigation")]
        public virtual AccountModel CreateByNavigation { get; set; }
        [ForeignKey("LastEditBy")]
        [InverseProperty("TemplateAndGiftTargetGroupModelLastEditByNavigation")]
        public virtual AccountModel LastEditByNavigation { get; set; }
        [InverseProperty("TemplateAndGiftTargetGroup")]
        public virtual ICollection<TemplateAndGiftCampaignModel> TemplateAndGiftCampaignModel { get; set; }
        [InverseProperty("TemplateAndGiftTargetGroup")]
        public virtual ICollection<TemplateAndGiftMemberModel> TemplateAndGiftMemberModel { get; set; }
    }
}