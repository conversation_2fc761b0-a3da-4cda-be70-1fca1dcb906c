/*
Change History:
--------------
2024-03-25 | <PERSON> | Initial creation
2024-03-25 | <PERSON> | Added form layout and styling to match BaoCaoQCGiaCongDetail
2024-03-25 | <PERSON> | Added date picker and time picker functionality
2024-03-25 | <PERSON> | Fixed date/time format display and server communication
2024-03-25 | <PERSON> | Adjusted UI components to match design system

Summary of Changes:
------------------
- Implemented form layout using FormLayout widget
- Added date picker with dd-MM-yyyy format
- Added time pickers with hh:mm AM/PM format
- Fixed date/time format conversion between UI and server
- Styled form fields to match design system
- Added validation and error handling
*/

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../../model/userModel.dart';
import '../../model/downtimeModel.dart';
import '../../repository/function/downtimeFunction.dart';
import '../../element/QualityFormFields.dart';
import '../LostConnect.dart';
import '../../element/FormLayout.dart';
import 'package:intl/intl.dart';
import 'package:ttf/page/Downtime/element/EmployeeTypeAhead.dart';
import 'package:ttf/model/downtimeMasterData.dart';

class DowntimeDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;

  const DowntimeDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _DowntimeDetailState createState() => _DowntimeDetailState();
}

class _DowntimeDetailState extends State<DowntimeDetail> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  final TextEditingController _workstationController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();
  final TextEditingController _responsibleTeamController = TextEditingController();
  final TextEditingController _responsibleDepartmentController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();

  String? _status;

  List<DowntimeHistory>? _history;
  bool _isHistoryExpanded = false;

  // Replace the mock suggestions with API calls
  final List<String> _departmentSuggestions = []; // Will be populated from API
  final List<String> _stepCodeSuggestions = []; // Will be populated from API

  List<TextEditingController> _employeeControllers = [];
  List<EmployeeRecord?> _selectedEmployees = [];

  // Replace the mock employee suggestions with an empty list
  List<EmployeeRecord> _employeeSuggestions = []; // Will be populated from API

  List<String> get _selectedEmployeeIds =>
      _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeId).where((id) => id != null).map((id) => id!).toList();

  // Add to the state class variables
  final List<String> _statusOptions = [
    'Created',
    'Pending',
    'In Progress',
    'Completed',
  ];

  @override
  void initState() {
    super.initState();
    // Initialize with one empty employee field
    _addEmployeeField();
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      // Load master data first
      await _loadMasterData();

      // Then load downtime record if editing
      if (widget.id.isNotEmpty) {
        await _loadData();
      } else {
        // Set default date to today when creating new record
        _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
      showToast(
        context: context,
        message: 'Có lỗi xảy ra khi ti dữ liệu',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMasterData() async {
    try {
      // Load employees first since we need them for mapping
      final employeesResponse = await DowntimeFunction.fetchEmployees(
        widget.user.token!,
        widget.user.companyCode ?? '',
      );

      if (!mounted) return;

      if (employeesResponse?.employees != null) {
        setState(() {
          _employeeSuggestions = employeesResponse!.employees!
              .where((emp) => emp.employeeId != null && emp.employeeName != null)
              .map((emp) => EmployeeRecord(
                    employeeId: emp.employeeId!,
                    employeeName: emp.employeeName!,
                  ))
              .toList();
        });
        debugPrint('Loaded employees: ${_employeeSuggestions.length}');
      }

      // Load departments
      final departmentsResponse = await DowntimeFunction.fetchDepartments(
        widget.user.token!,
        widget.user.companyCode ?? '',
      );

      if (!mounted) return;

      if (departmentsResponse?.departments != null) {
        setState(() {
          _departmentSuggestions.clear();
          _departmentSuggestions.addAll(departmentsResponse!.departments!
              .where((dept) => dept.departmentCode != null && dept.departmentName != null)
              .map((dept) => "${dept.departmentCode} | ${dept.departmentName}")
              .toList());
        });
        debugPrint('Loaded departments: ${_departmentSuggestions.length}');
      }

      // Load step codes
      final stepsResponse = await DowntimeFunction.fetchStepCodes(
        widget.user.token!,
        widget.user.companyCode ?? '',
      );

      if (!mounted) return;

      if (stepsResponse?.steps != null) {
        setState(() {
          _stepCodeSuggestions.clear();
          _stepCodeSuggestions.addAll(stepsResponse!.steps!
              .where((step) => step.stepCode != null && step.stepName != null)
              .map((step) => "${step.stepCode} | ${step.stepName}")
              .toList());
        });
        debugPrint('Loaded steps: ${_stepCodeSuggestions.length}');
      }
    } catch (error) {
      debugPrint('Error loading master data: $error');
      rethrow;
    }
  }

  void _addEmployeeField() {
    setState(() {
      _employeeControllers.add(TextEditingController());
      _selectedEmployees.add(null);
    });
  }

  void _removeEmployeeField(int index) {
    setState(() {
      _employeeControllers[index].dispose();
      _employeeControllers.removeAt(index);
      _selectedEmployees.removeAt(index);
    });
  }

  Future<void> _loadData() async {
    try {
      debugPrint("Fetching downtime detail with ID: ${widget.id}");
      final record = await DowntimeFunction.fetchDowntimeDetail(
        widget.user.token!,
        widget.id,
      );
      debugPrint("Fetched downtime detail: $record");

      if (!mounted) return;

      if (record != null) {
        setState(() {
          if (record.date != null) {
            try {
              final date = DateTime.parse(record.date!);
              _dateController.text = DateFormat('dd/MM/yyyy').format(date);
            } catch (e) {
              _dateController.text = record.date ?? '';
            }
          }

          if (record.startTime != null) {
            try {
              final time = DateFormat('HH:mm').parse(record.startTime!);
              _startTimeController.text = DateFormat('hh:mm a').format(time);
            } catch (e) {
              _startTimeController.text = record.startTime ?? '';
            }
          }

          if (record.endTime != null) {
            try {
              final time = DateFormat('HH:mm').parse(record.endTime!);
              _endTimeController.text = DateFormat('hh:mm a').format(time);
            } catch (e) {
              _endTimeController.text = record.endTime ?? '';
            }
          }

          _departmentController.text = record.departmentCode ?? '';
          if (record.stepCode != null && record.stepName != null) {
            _workstationController.text = "${record.stepCode} | ${record.stepName}";
          } else {
            _workstationController.text = record.stepCode ?? '';
          }
          _reasonController.text = record.reason ?? '';
          _responsibleTeamController.text = record.responsibleTeam ?? '';
          _responsibleDepartmentController.text = record.responsibleDepartment ?? '';
          _noteController.text = record.note ?? '';
          _status = record.status;

          _history = record.history;
          debugPrint("History loaded: ${_history?.length ?? 0} items");

          // Handle personCausedDowntimeCodeMany with employee names
          if (record.personCausedDowntimeCodeMany?.isNotEmpty == true) {
            final employeeCodes = record.personCausedDowntimeCodeMany!.split(',');
            _employeeControllers.clear();
            _selectedEmployees.clear();

            for (var code in employeeCodes.where((code) => code.trim().isNotEmpty)) {
              final trimmedCode = code.trim();
              // Find the employee in suggestions by matching employeeId
              final employee = _employeeSuggestions.firstWhere(
                (e) => e.employeeId == trimmedCode,
                orElse: () => EmployeeRecord(
                  employeeId: trimmedCode,
                  employeeName: 'Unknown Employee', // Fallback name if not found
                ),
              );

              final controller = TextEditingController(text: "${employee.employeeId} | ${employee.employeeName}");
              _employeeControllers.add(controller);
              _selectedEmployees.add(employee);

              debugPrint('Mapped employee: ${employee.employeeId} - ${employee.employeeName}');
            }
          }

          if (_employeeControllers.isEmpty) {
            _addEmployeeField();
          }
        });
      }
    } catch (error) {
      debugPrint("Error in _loadData: $error");
      rethrow;
    }
  }

  bool _validateForm() {
    if (_departmentController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Tổ',
      );
      return false;
    }

    if (_startTimeController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Thời gian bắt đầu',
      );
      return false;
    }

    if (_endTimeController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Thời gian kết thúc',
      );
      return false;
    }

    if (_reasonController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng nhập Nguyên nhân',
      );
      return false;
    }

    // Validate that end time is after start time
    try {
      final startTime = DateFormat('hh:mm a').parse(_startTimeController.text);
      final endTime = DateFormat('hh:mm a').parse(_endTimeController.text);

      if (endTime.isBefore(startTime)) {
        showToast(
          context: context,
          message: 'Thời gian kết thúc phải sau thời gian bắt đầu',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error validating times: $e');
      return false;
    }

    return true;
  }

  Future<void> _saveData() async {
    if (!_validateForm()) return;

    try {
      setState(() => _isSaving = true);

      String serverDate = _dateController.text;
      try {
        final date = DateFormat('dd/MM/yyyy').parse(_dateController.text);
        serverDate = DateFormat('yyyy-MM-dd').format(date);
      } catch (e) {
        debugPrint('Date parsing error: $e');
      }

      String? serverStartTime;
      String? serverEndTime;

      if (_startTimeController.text.isNotEmpty) {
        final startTime = DateFormat('hh:mm a').parse(_startTimeController.text);
        serverStartTime = DateFormat('HH:mm').format(startTime);
      }

      if (_endTimeController.text.isNotEmpty) {
        final endTime = DateFormat('hh:mm a').parse(_endTimeController.text);
        serverEndTime = DateFormat('HH:mm').format(endTime);
      }

      // Extract just the step code from the combined string
      final stepCode = _workstationController.text.split(' | ')[0];

      // Extract just the employee codes from the combined strings
      final employeeCodes = _selectedEmployees.where((e) => e != null && e.employeeId != null).map((e) => e!.employeeId!).join(',');

      final record = DowntimeRecord(
        id: widget.id,
        date: serverDate,
        departmentCode: _departmentController.text,
        stepCode: stepCode,
        startTime: serverStartTime,
        endTime: serverEndTime,
        reason: _reasonController.text,
        responsibleTeam: _responsibleTeamController.text,
        responsibleDepartment: _responsibleDepartmentController.text,
        personCausedDowntimeCodeMany: employeeCodes,
        note: _noteController.text,
        status: widget.id.isEmpty ? 'Created' : _status,
        verificationStatus: widget.id.isEmpty ? 'Created' : 'Pending',
        companyCode: widget.user.companyCode,
      );

      debugPrint("Saving downtime record: $record");

      final success = await DowntimeFunction.saveDowntime(
        widget.user.token!,
        record,
      );

      debugPrint("Save operation success: $success");

      if (!mounted) return;

      if (success) {
        showToast(
          context: context,
          message: widget.id.isEmpty ? 'Tạo thành công' : 'Cập nhật thành công',
        );
        Navigator.pop(context, true);
      } else {
        showToast(
          context: context,
          message: 'Lưu thất bại',
        );
      }
    } catch (error) {
      debugPrint("Error in _saveData: $error");
      showToast(
        context: context,
        message: 'Đã xảy ra lỗi',
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  Future<void> _selectStartTime(BuildContext context) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );
      setState(() {
        _startTimeController.text = DateFormat('hh:mm a').format(dateTime);
      });
    }
  }

  Future<void> _selectEndTime(BuildContext context) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );
      setState(() {
        _endTimeController.text = DateFormat('hh:mm a').format(dateTime);
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = DateFormat('dd/MM/yyyy').format(pickedDate);
      });
    }
  }

  // Add this helper method to calculate time span
  String _calculateTimeSpan() {
    try {
      if (_startTimeController.text.isEmpty || _endTimeController.text.isEmpty) {
        return '';
      }

      final startTime = DateFormat('hh:mm a').parse(_startTimeController.text);
      final endTime = DateFormat('hh:mm a').parse(_endTimeController.text);

      if (endTime.isBefore(startTime)) {
        return '';
      }

      final difference = endTime.difference(startTime);
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours == 0) {
        return '$minutes phút';
      } else if (minutes == 0) {
        return '$hours tiếng';
      } else {
        return '$hours tiếng $minutes phút';
      }
    } catch (e) {
      debugPrint('Error calculating time span: $e');
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.id.isEmpty ? 'Thêm Downtime' : 'Chi tiết Downtime',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          // Unfocus when tapping outside any text field
          FocusScope.of(context).unfocus();
        },
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormLayout(
            title: "Thông tin Downtime",
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Ngày',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: GestureDetector(
                        onTap: () => _selectDate(context),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _dateController.text.isEmpty ? 'Chọn ngày' : _dateController.text,
                                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                ),
                              ),
                              Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: AutoCompleteField(
                  label: 'Tổ *',
                  enabled: true,
                  suggestions: _departmentSuggestions,
                  controller: _departmentController,
                  onChanged: (value) {
                    debugPrint('Department value changed: $value');
                  },
                  onSuggestionSelected: (suggestion) {
                    debugPrint('Department suggestion selected: $suggestion');
                    setState(() {
                      _departmentController.text = suggestion;
                    });
                  },
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: AutoCompleteField(
                  label: 'Công đoạn',
                  enabled: true,
                  suggestions: _stepCodeSuggestions,
                  controller: _workstationController,
                  onChanged: (value) {
                    debugPrint('Step code value changed: $value');
                  },
                  onSuggestionSelected: (suggestion) {
                    debugPrint('Step code suggestion selected: $suggestion');
                    setState(() {
                      _workstationController.text = suggestion;
                    });
                  },
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Thời gian\nbắt đầu *',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: GestureDetector(
                        onTap: () => _selectStartTime(context),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _startTimeController.text.isEmpty ? 'Chọn giờ' : _startTimeController.text,
                                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                ),
                              ),
                              Icon(Icons.access_time, size: 18.sp, color: Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Expanded(
                          flex: 3,
                          child: Text(
                            'Thời gian\nkết thúc *',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          flex: 7,
                          child: GestureDetector(
                            onTap: () => _selectEndTime(context),
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                                color: Colors.white,
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      _endTimeController.text.isEmpty ? 'Chọn giờ' : _endTimeController.text,
                                      style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                    ),
                                  ),
                                  Icon(Icons.access_time, size: 18.sp, color: Colors.blue),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Add time span display
                    if (_calculateTimeSpan().isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          'Thời gian: ${_calculateTimeSpan()}',
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: Colors.blue[700],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: AutoCompleteField(
                  label: 'Nguyên nhân *',
                  enabled: true,
                  suggestions: DowntimeMasterData.reasonSuggestions,
                  controller: _reasonController,
                  onChanged: (value) {
                    // Handle text changes if needed
                  },
                  onSuggestionSelected: (suggestion) {
                    setState(() {
                      _reasonController.text = suggestion;
                    });
                  },
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: AutoCompleteField(
                  label: 'Bộ phận chịu trách nhiệm',
                  enabled: true,
                  suggestions: _departmentSuggestions,
                  controller: _responsibleTeamController,
                  onChanged: (value) {
                    debugPrint('Responsible team value changed: $value');
                  },
                  onSuggestionSelected: (suggestion) {
                    debugPrint('Responsible team suggestion selected: $suggestion');
                    setState(() {
                      _responsibleTeamController.text = suggestion;
                    });
                  },
                ),
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Tỉ lệ\ntrách nhiệm',
                controller: _responsibleDepartmentController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Ghi chú',
                controller: _noteController,
                maxLines: 3,
                required: false,
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Cá nhân chịu trách nhiệm',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: Column(
                        children: List.generate(
                          _employeeControllers.length,
                          (index) => EmployeeTypeAhead(
                            enabled: true,
                            showRemoveButton: _employeeControllers.length > 1,
                            totalEmployees: _employeeControllers.length,
                            masterDataList: _employeeSuggestions,
                            excludedEmployeeIds: _selectedEmployeeIds,
                            controller: _employeeControllers[index],
                            employeeIndex: index,
                            onChanged: (value) {
                              debugPrint('Employee value changed: $value');
                              setState(() {
                                _selectedEmployees[index] = null;
                              });
                            },
                            onSuggestionSelected: (EmployeeRecord suggestion) {
                              debugPrint('Employee suggestion selected: ${suggestion.employeeId} - ${suggestion.employeeName}');
                              setState(() {
                                _employeeControllers[index].text = "${suggestion.employeeId} | ${suggestion.employeeName}";
                                _selectedEmployees[index] = suggestion;
                              });
                            },
                            onAddEmployee: () {
                              if (_employeeControllers.length < 5) {
                                _addEmployeeField();
                              }
                            },
                            onRemoveEmployee: () => _removeEmployeeField(index),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.id.isNotEmpty) ...[
                SizedBox(height: 10.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Trạng thái',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 4.w),
                              isDense: true,
                              isExpanded: true,
                              value: _status ?? 'Created', // Provide default value
                              items: _statusOptions
                                  .map((status) => DropdownMenuItem(
                                        value: status,
                                        child: Text(
                                          _getStatusText(status),
                                          style: TextStyle(fontSize: 12.sp),
                                        ),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  _status = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: FormSubmitButton(
              text: widget.id.isEmpty ? 'Tạo mới' : 'Cập nhật',
              onPressed: _saveData,
              isLoading: _isSaving,
            ),
          ),
          if (widget.id.isNotEmpty && _history != null && _history!.isNotEmpty) ...[
            SizedBox(height: 20.h), // Increased spacing
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Text(
                'Lịch sử thay đổi',
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ),
            SizedBox(height: 10.h),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              itemCount: _history!.length,
              itemBuilder: (context, index) => _buildHistoryItem(_history![index]),
            ),
          ],
          SizedBox(height: 150.h),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(DowntimeHistory historyItem) {
    final isApproval = historyItem.action == 'APPROVAL';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: isApproval ? Colors.green.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              isApproval ? Icons.approval : Icons.history,
              size: 14.sp,
              color: isApproval ? Colors.green : Colors.blue,
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: TextStyle(fontSize: 11.sp, color: Colors.black),
                    children: [
                      TextSpan(
                        text: historyItem.changedBy ?? '',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      if (isApproval) ...[
                        const TextSpan(text: ' đã '),
                        TextSpan(
                          text: historyItem.newStatus == 'APPROVED' ? 'phê duyệt' : 'từ chối',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: historyItem.newStatus == 'APPROVED' ? Colors.green : Colors.red,
                          ),
                        ),
                        if (historyItem.verifierRole != null) ...[
                          const TextSpan(text: ' với vai trò '),
                          TextSpan(
                            text: _getApprovalStepText(historyItem.verifierRole!),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ] else ...[
                        const TextSpan(text: ' đã thay đổi trạng thi từ '),
                        TextSpan(
                          text: historyItem.oldStatus ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const TextSpan(text: ' sang '),
                        TextSpan(
                          text: historyItem.newStatus ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ],
                  ),
                ),
                if (historyItem.comment != null && historyItem.comment!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      historyItem.comment!,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
                SizedBox(height: 2.h),
                Text(
                  DateFormat('dd/MM/yyyy HH:mm').format(
                    DateTime.parse(historyItem.changedDate!),
                  ),
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getApprovalStepText(String role) {
    switch (role) {
      case 'TEAM_LEADER':
        return 'Tổ trưởng';
      case 'MANAGER':
        return 'Quản lý';
      case 'DIRECTOR':
        return 'Giám đốc';
      default:
        return role;
    }
  }

  // Add this helper method to convert status codes to display text
  String _getStatusText(String status) {
    switch (status) {
      case 'Created':
        return 'Mới tạo';
      case 'Pending':
        return 'Chờ duyệt';
      case 'In Progress':
        return 'Đang xử lý';
      case 'Completed':
        return 'Hoàn thành';
      default:
        return status;
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _departmentController.dispose();
    _workstationController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _reasonController.dispose();
    _responsibleTeamController.dispose();
    _responsibleDepartmentController.dispose();
    _noteController.dispose();
    for (var controller in _employeeControllers) {
      controller.dispose();
    }
    super.dispose();
  }
}
