import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/TyLeTieuHaoSearchModel.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/page/TyLeTieuHao/element/FilterListTyLeTieuHao.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import '../../model/tyLeTieuHaoModel.dart';
import '../../repository/function/tyLeTieuHaoFunction.dart';
import 'TyLeTieuHaoDetail.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../LostConnect.dart';

class TyLeTieuHaoList extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;

  const TyLeTieuHaoList({
    Key? key,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _TyLeTieuHaoListState createState() => _TyLeTieuHaoListState();
}

class _TyLeTieuHaoListState extends State<TyLeTieuHaoList> {
  // Toggle this for mockup/production. Set to false for production.
  static const bool useMockData = false;

  bool _isInitialLoading = true;
  bool _isRefreshing = false;
  bool _isNotWifi = false;
  bool _isError = false;
  List<TyLeTieuHaoRecord>? _records;
  ConnectivityResult _result = ConnectivityResult.none;
  TyLeTieuHaoSearchModel? _searchModel;
  CommonDateModel? _commonDateModel;
  List<CommonDates>? _commonDates;
  FilterQCModel? _filterLSQC;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        _isInitialLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      if (useMockData) {
        // Use mock data with simulated network delay
        await Future.delayed(const Duration(milliseconds: 500));
        if (!mounted) return;
        setState(() {
          _records = _getMockRecords();
        });
        return;
      }

      // Initialize default search model if not exists
      if (_searchModel == null) {
        final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.user.token!);
        if (dataDropdown != null) {
          _filterLSQC = dataDropdown;
          _commonDates = dataDropdown.data?.commonDates;

          // Get default date range (ThisWeek)
          final defaultCommonDate = _commonDates?.firstWhere(
            (element) => element.catalogCode == "ThisWeek",
            orElse: () => _commonDates!.first,
          );

          if (defaultCommonDate != null) {
            final commonDateModel = await ListQCFunction.getCommonDateModel(
              defaultCommonDate.catalogCode!,
              widget.user.token!,
            );

            _searchModel = TyLeTieuHaoSearchModel(
              companyCode: widget.user.companyCode ?? '',
              pageNumber: 1,
              pageSize: 20,
              fromDate: commonDateModel?.fromDate != null ? DateTime.parse(commonDateModel!.fromDate!) : null,
              toDate: commonDateModel?.toDate != null ? DateTime.parse(commonDateModel!.toDate!) : null,
            );
          }
        }
      }

      if (_searchModel != null) {
        final data = await TyLeTieuHaoFunction.fetchTyLeTieuHaoList(
          widget.user.token!,
          _searchModel!,
        );

        if (!mounted) return;

        setState(() {
          _records = data?.data;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isInitialLoading = false;
        });
      }
    }
  }

  Future<void> _loadData() async {
    if (_isRefreshing) return;

    try {
      setState(() {
        _isRefreshing = true;
        _isNotWifi = false;
        _isError = false;
      });

      if (useMockData) {
        // Use mock data with simulated network delay
        await Future.delayed(const Duration(milliseconds: 500));
        if (!mounted) return;
        setState(() {
          _records = _getMockRecords();
        });
        return;
      }

      if (_searchModel != null) {
        final data = await TyLeTieuHaoFunction.fetchTyLeTieuHaoList(
          widget.user.token!,
          _searchModel!,
        );

        if (!mounted) return;

        setState(() {
          _records = data?.data;
        });
      }
    } on SocketException catch (_) {
      debugPrint("SocketException caught while fetching ty le tieu hao list.");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _loadData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  List<TyLeTieuHaoRecord> _getMockRecords() {
    return [
      TyLeTieuHaoRecord(
        id: '1',
        date: '2024-11-19',
        lsxSAP: '3500010130',
        dotSanXuat: 'DT-169-24-CRZ-HAI-PYCSXDT-58-24-CRZ',
        maSanPham: '295010583',
        tenSanPham: 'Vật tư phụ trội',
        maNVL: '280010968',
        tenNVL: 'CRZ Da bò thật Charme Cigar dày 09->13mm',
        slThoSuDung: 27,
        slSPLamDuoc: 3,
        slM2DinhMuc: 24,
        tyLeTheoDM: 1.13,
      ),
      TyLeTieuHaoRecord(
        id: '2',
        date: '2024-11-18',
        lsxSAP: '3500010131',
        dotSanXuat: 'DT-170-24-CRZ-HAI-PYCSXDT-59-24-CRZ',
        maSanPham: '295010584',
        tenSanPham: 'Vật tư phụ trội loại 2',
        maNVL: '280010969',
        tenNVL: 'CRZ Da bò thật Charme Cigar dày 10->14mm',
        slThoSuDung: 30,
        slSPLamDuoc: 4,
        slM2DinhMuc: 18,
        tyLeTheoDM: 1.67,
      ),
      TyLeTieuHaoRecord(
        id: '3',
        date: '2024-11-17',
        lsxSAP: '3500010132',
        dotSanXuat: 'DT-171-24-CRZ-HAI-PYCSXDT-60-24-CRZ',
        maSanPham: '295010585',
        tenSanPham: 'Vật tư phụ trội loại 3',
        maNVL: '280010970',
        tenNVL: 'CRZ Da bò thật Charme Cigar dày 11->15mm',
        slThoSuDung: 25,
        slSPLamDuoc: 5,
        slM2DinhMuc: 15,
        tyLeTheoDM: 1.67,
      ),
    ];
  }

  void _navigateToDetail([String? id]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TyLeTieuHaoDetail(
          id: id ?? '',
          dateTimeOld: widget.dateTimeOld,
          user: widget.user,
          viewMode: true,
        ),
      ),
    );
    if (result == true) {
      // reload data
      _loadData();
    }
  }

  String _formatDate(String? date) {
    if (date == null) return '';
    try {
      final dt = DateTime.parse(date);
      return DateFormat('dd/MM/yyyy').format(dt);
    } catch (e) {
      return date;
    }
  }

  // Add this helper method to format numbers
  String _formatNumber(double? value, {int decimals = 2}) {
    if (value == null) return '';
    if (decimals == 0) {
      return value.round().toString();
    }
    return value.toStringAsFixed(decimals);
  }

  Widget _buildListItem(TyLeTieuHaoRecord record) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(12.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main info row
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'LSX SAP: ${record.lsxSAP ?? ""}',
                    style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'ĐSX: ${record.dotSanXuat ?? ""}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[700],
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // Smaller font size for product info
                  Text(
                    'Tên SP: ${record.maSanPham ?? ""} | ${record.tenSanPham ?? ""}',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // Smaller font size for material info
                  Text(
                    'Tên NVL: ${record.maNVL ?? ""} | ${record.tenNVL ?? ""}',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              Divider(height: 16.h),
              // Sub info section with smaller text
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'SL thô',
                          style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          '${_formatNumber(record.slThoSuDung, decimals: 0)}',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'SL SP',
                          style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          '${_formatNumber(record.slSPLamDuoc, decimals: 0)}',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'SL M2 DM',
                          style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          '${_formatNumber(record.slM2DinhMuc)}',
                          style: TextStyle(fontSize: 12.sp),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          'Tỷ lệ theo DM',
                          style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          '${_formatNumber(record.tyLeTheoDM)}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              // Date at the bottom
              Text(
                'Ngày tạo: ${_formatDate(record.date)}',
                style: TextStyle(
                  fontSize: 11.sp,
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    ).onTap(() async {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TyLeTieuHaoDetail(
            id: record.id ?? '',
            dateTimeOld: widget.dateTimeOld,
            user: widget.user,
            viewMode: true,
          ),
        ),
      );
      if (result == true) {
        _loadData();
      }
    });
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Builder(
            builder: (BuildContext context) {
              return IconButton(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                hoverColor: Colors.transparent,
                icon: Icon(
                  Icons.search_outlined,
                  size: 19.sp,
                  color: Colors.white,
                ),
                onPressed: _isInitialLoading == true
                    ? null
                    : () async {
                        await _checkConnectNetwork();
                        if (!mounted) return;
                        if (_result != ConnectivityResult.none) {
                          Scaffold.of(context).openEndDrawer();
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            backgroundColor: Colors.black,
                            content: Text(
                              'Tính năng cần có kết nối internet để sử dụng',
                              style: TextStyle(fontSize: 15.sp, color: Colors.white),
                            ),
                          ));
                        }
                      },
              );
            },
          ),
        ],
        title: Text(
          'Tỷ lệ tiêu hao',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      endDrawer: _isInitialLoading == true
          ? null
          : FilterListTyLeTieuHao(
              searchModel: _searchModel,
              token: widget.user.token!,
              user: widget.user,
              onFilterSelected: (TyLeTieuHaoSearchModel filter) {
                setState(() {
                  _searchModel = filter;
                });
                _loadData(); // Reload data with new filters
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TyLeTieuHaoDetail(
                id: '',
                dateTimeOld: widget.dateTimeOld,
                user: widget.user,
                viewMode: false,
              ),
            ),
          );
          if (result == true) {
            _loadData();
          }
        },
        backgroundColor: const Color(0xff4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isInitialLoading && _records == null) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    if (_records == null || _records!.isEmpty) {
      return RefreshIndicator(
        onRefresh: () => _loadData(),
        child: ListView(
          children: [
            SizedBox(height: MediaQuery.of(context).size.height / 3),
            Center(
              child: Text(
                'Không có dữ liệu',
                style: TextStyle(fontSize: 15.sp),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadData(),
      child: ListView.builder(
        itemCount: _records!.length,
        itemBuilder: (context, index) {
          final record = _records![index];
          return _buildListItem(record);
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}

extension TapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }
}
