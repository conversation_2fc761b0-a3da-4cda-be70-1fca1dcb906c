class CodeNameProduct {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataCodenameProduct>? data;
  DataCodenameProduct? additionalData;

  CodeNameProduct({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  CodeNameProduct.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataCodenameProduct>[];
      json['data'].forEach((v) {
        data!.add(DataCodenameProduct.fromJson(v));
      });
    }
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class DataCodenameProduct {
  String? key;
  String? value;
  String? data;

  DataCodenameProduct({this.key, this.value, this.data});

  DataCodenameProduct.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> ret = <String, dynamic>{};
    ret['key'] = key;
    ret['value'] = value;
    ret['data'] = data;
    return ret;
  }
}
