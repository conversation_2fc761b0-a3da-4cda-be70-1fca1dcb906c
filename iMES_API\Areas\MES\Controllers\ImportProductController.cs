using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.Repositories.MES;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Warehouse;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using SAPWarehouseTransaction;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    [ISDWebAuthorization]
    public class ImportProductController : ControllerBaseAPI
    {
        private IHostEnvironment _env;

        public ImportProductController(IHostEnvironment env)
        {
            _env = env;
        }

        #region Nhập kho thành phẩm

        /// <summary>API nhập kho thành phẩm tích hợp SAP (quantity-based)</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/ImportProduct/ImportFinishedProduct
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "plant": "1000",
        ///                "sloc": "N001",
        ///                "slocId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                "storageBin": "G1C334-01",
        ///                "storageBinId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                "batch": "N2806241DH",
        ///                "products": [
        ///                  {
        ///                    "sapOrder": "3500010156",
        ///                    "productCode": "550010609",
        ///                    "productName": "Ghế Harrison Chair-Alcala Wheat",
        ///                    "importQuantity": 5,
        ///                    "batch": "N2806241DH",
        ///                    "location": "G1C334-01",
        ///                    "so": "SO12345",
        ///                    "soItem": "10",
        ///                    "wbs": "WBS12345",
        ///                    "unit": "CAI"
        ///                  }
        ///                ]
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                    "code": 200,
        ///                    "isSuccess": true,
        ///                    "message": "Nhập kho thành công.",
        ///                    "data": true,
        ///                    "additionalData": null
        ///               }
        /// </remarks>
        [HttpPost("ImportFinishedProduct")]
        public async Task<IActionResult> ImportFinishedProduct([FromBody] FinishedProductImportViewModel vm)
        {
            try
            {
                // Validate input
                if (vm.Products == null || !vm.Products.Any())
                {
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng nhập ít nhất một sản phẩm", IsSuccess = false });
                }

                // Kiểm tra kho
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == vm.SlocId);
                if (sloc == null)
                {
                    return Ok(new ApiResponse { Code = 400, Message = string.Format(CommonResource.Msg_NotFound, "Kho"), IsSuccess = false });
                }

                // Kiểm tra vị trí kho nếu kho có quản lý vị trí
                if (sloc.WarehouseNo == "100" && !vm.StorageBinId.HasValue)
                {
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những Sloc có quản lý vị trí!", IsSuccess = false });
                }

                // Kiểm tra vị trí kho
                var storageBin = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == vm.StorageBinId);

                // Validate quantities for each PO
                var validationErrors = new List<string>();
                foreach (var product in vm.Products)
                {
                    // Check if PO exists and get total quantity
                    var poTask = await _context.TaskModel
                        .Where(t => t.Summary == product.SapOrder && t.ProductId != null)
                        .FirstOrDefaultAsync();

                    if (poTask == null)
                    {
                        validationErrors.Add($"Không tìm thấy PO {product.SapOrder}");
                        continue;
                    }

                    // Calculate already imported quantity
                    var importedQuantity = await _context.ImportProductModel
                        .Where(imp => imp.PO == product.SapOrder && imp.Status == "Success" && imp.Actived == true)
                        .SumAsync(imp => imp.ImportQuantity);

                    var remainingQuantity = (int)poTask.Qty - importedQuantity;

                    if (product.ImportQuantity > remainingQuantity)
                    {
                        validationErrors.Add($"PO {product.SapOrder}: Số lượng nhập ({product.ImportQuantity}) vượt quá số lượng còn lại ({remainingQuantity})");
                    }
                }

                if (validationErrors.Any())
                {
                    return Ok(new ApiResponse { Code = 400, Message = string.Join("; ", validationErrors), IsSuccess = false });
                }

                // Tạo danh sách giao dịch kho và import records
                var listWT = new List<WarehouseTransactionModel>();
                var listImportRecords = new List<ImportProductModel>();

                // Tạo ID giao dịch chung cho tất cả sản phẩm
                var transactionId = Guid.NewGuid();

                // Tạo giao dịch kho cho từng sản phẩm với số lượng
                foreach (var product in vm.Products)
                {
                    // Get next import sequence for this PO
                    var lastSequence = await _context.ImportProductModel
                        .Where(imp => imp.PO == product.SapOrder && imp.Actived == true)
                        .MaxAsync(imp => (int?)imp.ImportSequence) ?? 0;

                    // Create multiple warehouse transactions for the quantity
                    for (int i = 0; i < product.ImportQuantity; i++)
                    {
                        var wt = CreateWarehouseTransaction(vm, product);
                        listWT.Add(wt);
                    }

                    // Create single import record for the batch
                    var importRecord = new ImportProductModel
                    {
                        ImportProductId = Guid.NewGuid(),
                        PO = product.SapOrder,
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        SO = product.SO,
                        SOItem = product.SOItem,
                        WBS = product.WBS,
                        Plant = vm.Plant,
                        SlocId = vm.SlocId.Value,
                        StorageBinId = vm.StorageBinId,
                        Batch = product.Batch,
                        ImportQuantity = product.ImportQuantity,
                        Unit = product.Unit ?? "CAI",
                        ImportSequence = lastSequence + 1,
                        DocumentDate = DateTime.Now,
                        Status = "Pending",
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId,
                        Actived = true
                    };

                    listImportRecords.Add(importRecord);
                }

                // Lưu danh sách thông tin nhập kho và import records
                _context.WarehouseTransactionModel.AddRange(listWT);
                _context.ImportProductModel.AddRange(listImportRecords);

                // Chuẩn bị dữ liệu gửi đến SAP - group by PO to send consolidated quantities
                var sapDetails = new List<ZST_MES_INF_TRANS>();
                
                foreach (var product in vm.Products)
                {
                    sapDetails.Add(new ZST_MES_INF_TRANS
                    {
                        HEADER_ID = transactionId.ToString(),
                        // Use first warehouse transaction ID for this product
                        DOCUMENT_NUMBER = listWT.First(wt => wt.PO == product.SapOrder).WarhouseTransactionId.ToString(),
                        // Mã sản phẩm
                        MATNR = product.ProductCode,
                        // Nhà máy
                        WERKS = vm.Plant,
                        // Lệnh sản xuất
                        AUFNR = product.SapOrder, // SAP Order
                        // Số lượng thực tế nhập
                        ERFMG = product.ImportQuantity,
                        ERFME = product.Unit ?? "CAI", // Đơn vị tính
                        // Lô
                        CHARG = product.Batch,
                        // Sloc
                        LGORT = sloc.Sloc,
                        // WarehouseNo
                        LGNUM = string.IsNullOrEmpty(sloc.WarehouseNo) ? null : sloc.WarehouseNo,
                        // Storage Bin
                        LGPLA = storageBin?.StorageBin,
                        // Storage Type
                        LGTYP = storageBin?.StorageType,
                    });
                }

                var ZMES_FM_INF_TRANS = new ZMES_FM_INF_TRANS
                {
                    INF_TRAN = new ZST_MES_INF_TRANS_HEADER
                    {
                        // ID giao dịch
                        HEADER_ID = transactionId.ToString(),
                        // Ngày chứng từ
                        BLDAT = DateTime.Now.ToString(DateTimeFormat.DateFormatString),
                        BUDAT_MKPF = DateTime.Now.ToString(DateTimeFormat.DateFormatString),
                        // Loại giao dịch kho - NKSX: Nhập kho thành phẩm
                        DOCUMENT_TYPE = "NKSX", // NKSX
                        // Chi tiết nhập kho
                        DETAILS = sapDetails.ToArray()
                    }
                };

                // Phiếu nhập kho
                string grNote = null;

                XmlSerializer xsSubmit = new XmlSerializer(typeof(ZMES_FM_INF_TRANS));
                var xml = "";

                using (var sww = new StringWriter())
                {
                    using (XmlWriter writer = XmlWriter.Create(sww))
                    {
                        xsSubmit.Serialize(writer, ZMES_FM_INF_TRANS);
                        xml = sww.ToString(); // Your XML
                    }
                }

//#if DEBUG

//                return Ok(new ApiResponse
//                {
//                    Code = 200,
//                    IsSuccess = true,
//                    Data = true,
//                    //Message = $"Nhập kho thành công. Số phiếu nhập kho {grNote}"
//                    Message = $"Nhập kho thành công"
//                });
//                //return Ok(new ApiResponse
//                //{
//                //    Code = 400,
//                //    IsSuccess = false,
//                //    Data = xml,
//                //    Message = $"TEST"
//                //});
//#endif

                // SEND SAP
                var jsonStringRequest = Newtonsoft.Json.JsonConvert.SerializeObject(ZMES_FM_INF_TRANS);
                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(ZMES_FM_INF_TRANS);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    // Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "NKTP",
                        FuntionnName = "Nhập kho thành phẩm",
                        RequestSAP = jsonStringRequest,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId
                    });

                    await _context.SaveChangesAsync();

                    // Xử lý phản hồi từ SAP
                    if (resSAPs.Any())
                    {
                        // Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            // Danh sách msg lỗi
                            var msgErrArr = resSAPs.Select(x => x.MESSAGE).ToArray();
                            var msgError = msgErrArr.Any() ? string.Join(", ", msgErrArr) : null;

                            for (int i = 0; i < listWT.Count; i++)
                            {
                                var wt = listWT[i];
                                var resSAP = resSAPs[i];

                                wt.SendToSAPError = resSAP.MESSAGE;
                            }

                            // Update ImportProductModel records to Failed status
                            foreach (var importRecord in listImportRecords)
                            {
                                importRecord.Status = "Failed";
                                importRecord.SAPResponse = msgError;
                            }

                            //_context.WarehouseTransactionModel.UpdateRange(listWT);
                            await _context.SaveChangesAsync();

                            return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });
                        }

                        // Cập nhật trạng thái thành công và save dữ liệu nhập kho vào MES
                        foreach (var item in listWT)
                        {
                            // Flag send SAP
                            item.isSendToSAP = true;
                            item.SendToSAPTime = DateTime.Now;
                        }

                        // Update ImportProductModel records to Success status
                        foreach (var importRecord in listImportRecords)
                        {
                            importRecord.Status = "Success";
                            importRecord.SendToSAPTime = DateTime.Now;
                            // Could also store SAP response info if needed
                        }

                        //// Phiếu nhập kho
                        //var goodsReceivedNote = resSAPs.FirstOrDefault().MESSAGE_V1;
                        //grNote = goodsReceivedNote;
                        //// Năm nhập kho
                        //var yearRecive = resSAPs.FirstOrDefault().MESSAGE_V2;

                        //// Lưu thông tin phiếu nhập kho
                        //var infoReceive = await _context.ReceiveInformationModel.FirstOrDefaultAsync(x => x.GoodsReceivedNote == goodsReceivedNote && x.YearReceive == yearRecive);
                        //if (infoReceive is null)
                        //{
                        //    // Thêm mới thông tin giao dịch kho
                        //    _context.ReceiveInformationModel.Add(new ReceiveInformationModel
                        //    {
                        //        // Id
                        //        ReceiveInfoId = Guid.NewGuid(),
                        //        // Số phiếu nhập kho
                        //        GoodsReceivedNote = goodsReceivedNote,
                        //        Plant = vm.Plant,
                        //        // Năm nhập kho
                        //        YearReceive = yearRecive,
                        //        // Kho nhập
                        //        SlocId = sloc?.Id,
                        //        // Ngày nhập kho
                        //        ReceiveDate = DateTime.Now,
                        //        // Common
                        //        CreateTime = DateTime.Now,
                        //        CreateBy = CurrentUser?.AccountId,
                        //        Actived = true
                        //    });
                        //}
                        //else
                        //{
                        //    // Cập nhật thông tin nếu đã tồn tại
                        //    infoReceive.SlocId = sloc?.Id;
                        //    infoReceive.ReceiveDate = DateTime.Now;
                        //    infoReceive.Plant = vm.Plant;
                        //    infoReceive.LastEditTime = DateTime.Now;
                        //    infoReceive.LastEditBy = CurrentUser?.AccountId;
                        //}
                    }
                }
                catch (Exception ex)
                {
                    // Update ImportProductModel records to Failed status on exception
                    foreach (var importRecord in listImportRecords)
                    {
                        importRecord.Status = "Failed";
                        importRecord.SAPResponse = "Exception: " + ex.Message;
                    }
                    
                    await _context.SaveChangesAsync();
                    
                    return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP ! " + ex.Message, IsSuccess = false });
                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    //Message = $"Nhập kho thành công. Số phiếu nhập kho {grNote}"
                    Message = $"Nhập kho thành công"
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        private WarehouseTransactionModel CreateWarehouseTransaction(FinishedProductImportViewModel vm, FinishedProductViewModel product)
        {
            // Date now
            var dateNow = DateTime.Now;
            var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

            return new WarehouseTransactionModel
            {
                WarhouseTransactionId = Guid.NewGuid(),
                // Mã sản phẩm
                ProductCode = product.ProductCode,
                // Reference document text - using PO since we no longer track individual serials
                ReferenceDocumentText = product.SapOrder,
                // SAP Order (Lệnh sản xuất)
                PO = product.SapOrder,
                // Kho
                SlocId = vm.SlocId,
                // Vị trí kho
                StorageBinId = vm.StorageBinId,
                // Số lô
                Batch = product.Batch,
                // Nhà máy
                Plant = vm.Plant,
                // Loại giao dịch kho - Nhập kho
                MovementType = MovementType.Receive,
                // Ngày nhập kho
                DocumentDate = dateNow,
                DateKey = int.Parse(dateNowStr),
                // Số lượng và đơn vị tính
                Quantity = 1, // Each warehouse transaction represents 1 unit
                Unit = product.Unit ?? "CAI", // Use product unit or default to CAI
                // Common
                CreateTime = dateNow,
                CreateBy = CurrentUser?.AccountId,
                Actived = true
            };
        }

        #endregion

        [HttpGet("GetPOInfoBySerial/{serial}")]
        public async Task<IActionResult> GetPOInfoBySerial(string serial)
        {
            try
            {
                var poNumber = "";

                // Check if the serial exists in ProductBarcodeModel
                var productBarcode = await _context.ProductBarcodeModel
                    .Where(pb => pb.FriendlyCode == serial)
                    .FirstOrDefaultAsync();

                if (productBarcode == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 404,
                        Message = "Không tìm thấy thông tin PO",
                        IsSuccess = false
                    });
                }

                poNumber = productBarcode.PO;

                // Get PO information from TaskModel
                var taskQuery = from task in _context.TaskModel
                            join product in _context.ProductModel on task.ProductId equals product.ProductId
                            join productLatest in _context.ProductLatestModel on product.ProductCode equals productLatest.ProductCode
                            where task.ProductId != null && task.Summary == poNumber
                            select new
                            {
                                PO = task.Summary,
                                TotalQuantity = (int)task.Qty,
                                ProductCode = product.ProductCode,
                                ProductName = productLatest.ProductName,
                                // Get SO, WBS info from task if available
                                //SO = task.WorkOrderCode, // Assuming this maps to SO
                                //WBS = task.WBSElement    // Assuming this maps to WBS
                            };

                var taskInfo = await taskQuery.FirstOrDefaultAsync();

                if (taskInfo == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 404,
                        Message = "Không tìm thấy thông tin PO",
                        IsSuccess = false
                    });
                }

                // Calculate imported quantity from ImportProductModel
                var importedQuantity = await _context.ImportProductModel
                    .Where(imp => imp.PO == poNumber && imp.Status == "Success" && imp.Actived == true)
                    .SumAsync(imp => imp.ImportQuantity);

                // Get import statistics
                var importStats = await _context.ImportProductModel
                    .Where(imp => imp.PO == poNumber && imp.Actived == true)
                    .GroupBy(imp => imp.PO)
                    .Select(g => new
                    {
                        ImportCount = g.Count(),
                        LastImportSequence = g.Max(x => x.ImportSequence)
                    })
                    .FirstOrDefaultAsync();

                var poInfoResult = new POInfoViewModel
                {
                    PO = taskInfo.PO,
                    TotalQuantity = taskInfo.TotalQuantity,
                    ImportedQuantity = importedQuantity,
                    ProductCode = taskInfo.ProductCode,
                    ProductName = taskInfo.ProductName,
                    //SO = taskInfo.SO,
                    //WBS = taskInfo.WBS,
                    //SOItem = "10", // Default SO Item - could be enhanced to get from actual data
                    ImportCount = importStats?.ImportCount ?? 0,
                    LastImportSequence = importStats?.LastImportSequence ?? 0
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = poInfoResult,
                    Message = "Lấy thông tin PO thành công"
                });
            }
            catch (Exception ex)
            {
                return Ok(new ApiResponse
                {
                    Code = 500,
                    Message = "Lỗi hệ thống: " + ex.Message,
                    IsSuccess = false
                });
            }
        }
    }
}