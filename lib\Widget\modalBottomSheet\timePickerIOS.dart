import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class TimePickerIOS extends StatefulWidget {
   final DateTime? dateTimeMidNight;
  const TimePickerIOS({Key? key,required this.dateTimeMidNight}) : super(key: key);

  @override
  State<TimePickerIOS> createState() => _TimePickerIOSState();
}

class _TimePickerIOSState extends State<TimePickerIOS> {
  DateTime? _timeOfDay;


  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: [
        SizedBox(
            height: 250.h,
            child: CupertinoDatePicker(
                initialDateTime: widget.dateTimeMidNight,
                mode: CupertinoDatePickerMode.time,
                onDateTimeChanged: (val) {
                  setState(() {
                    _timeOfDay = val;
                  });
                })),
      ],
      cancelButton: CupertinoButton(
        child: const Text('OK'),
        onPressed: () {
            // final timeConvert = DateFormat('hh:mm').format(_timeOfDay ?? DateTime(DateTime.now().hour,DateTime.now().minute));
            Navigator.pop(context, _timeOfDay ?? widget.dateTimeMidNight);
        }),
    );
  }
}
