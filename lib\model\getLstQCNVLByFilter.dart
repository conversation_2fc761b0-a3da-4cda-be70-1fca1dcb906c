class GetLstQCNVLByFilter {
  int? pageNumber;
  int? pageSize;
  String? productCode;
  String? poNumber;
  String? so;
  String? vendorNumber;
  String? wbs;
  String? createBarcodeFromDate;
  String? createBarcodeToDate;
  bool? deliveryStatus;
  String? deliveryFromDate;
  String? deliveryToDate;
  bool? qcStatus;
  bool? qcResult;
  String? qcFromDate;
  String? qcToDate;
  DateTime? goodsArriveToDate;
  DateTime? goodsArriveFromDate;
  String? goodsArrive;

  GetLstQCNVLByFilter(
      {this.pageNumber,
        this.pageSize,
        this.productCode,
        this.poNumber,
        this.so,
        this.vendorNumber,
        this.wbs,
        this.createBarcodeFromDate,
        this.createBarcodeToDate,
        this.deliveryStatus,
        this.deliveryFromDate,
        this.deliveryToDate,
        this.qcStatus,
        this.qcResult,
        this.qcFromDate,
        this.qcToDate,
        this.goodsArriveToDate,
        this.goodsArriveFromDate,
        this.goodsArrive
      });

  GetLstQCNVLByFilter.fromJson(Map<String, dynamic> json) {
    pageNumber = json['pageNumber'];
    pageSize = json['pageSize'];
    productCode = json['productCode'];
    poNumber = json['poNumber'];
    so = json['so'];
    vendorNumber = json['vendorNumber'];
    wbs = json['wbs'];
    createBarcodeFromDate = json['createBarcodeFromDate'];
    createBarcodeToDate = json['createBarcodeToDate'];
    deliveryStatus = json['deliveryStatus'];
    deliveryFromDate = json['deliveryFromDate'];
    deliveryToDate = json['deliveryToDate'];
    qcStatus = json['qcStatus'];
    qcResult = json['qcResult'];
    qcFromDate = json['qcFromDate'];
    qcToDate = json['qcToDate'];
    goodsArriveToDate = json['goodsArriveToDate'] != null ? DateTime.parse(json['goodsArriveToDate']): null;
    goodsArriveFromDate = json['goodsArriveToDate'] != null ? DateTime.parse(json['goodsArriveFromDate']):null;
    goodsArrive = json['goodsArrive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pageNumber'] = pageNumber;
    data['pageSize'] = pageSize;
    data['productCode'] = productCode;
    data['poNumber'] = poNumber;
    data['so'] = so;
    data['vendorNumber'] = vendorNumber;
    data['wbs'] = wbs;
    data['createBarcodeFromDate'] = createBarcodeFromDate;
    data['createBarcodeToDate'] = createBarcodeToDate;
    data['deliveryStatus'] = deliveryStatus;
    data['deliveryFromDate'] = deliveryFromDate;
    data['deliveryToDate'] = deliveryToDate;
    data['qcStatus'] = qcStatus;
    data['qcResult'] = qcResult;
    data['qcFromDate'] = qcFromDate;
    data['qcToDate'] = qcToDate;
    data['goodsArriveToDate'] = goodsArriveToDate == null ? null:goodsArriveToDate!.toIso8601String();
    data['goodsArriveFromDate'] = goodsArriveFromDate == null ? null :goodsArriveFromDate!.toIso8601String();
    data['goodsArrive'] = goodsArrive;
    return data;
  }
}
class LstQCNVLByFilter {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataLstQCNVLByFilter>? data;
  String? additionalData;

  LstQCNVLByFilter(
      {this.code,
        this.isSuccess,
        this.message,
        this.data,
        this.additionalData});

  LstQCNVLByFilter.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataLstQCNVLByFilter>[];
      json['data'].forEach((v) {
        data!.add(DataLstQCNVLByFilter.fromJson(v));
      });
    }
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class DataLstQCNVLByFilter {
  int? stt;
  String? qualityControlId;
  DateTime? barcodeCreateDate;
  String? poNumber;
  String? vendor;
  String? material;
  String? soWbs;
  DateTime? actualDeliveryDate;
  DateTime? qcCreateTime;
  String? qcStatus;
  String? qcResult;
  String? qcEmployee;
  bool? isGoodsArrive;
  DateTime? goodsArriveDate;
  String? goodsArriveDateStr;

  DataLstQCNVLByFilter(
      {this.stt,
        this.qualityControlId,
        this.barcodeCreateDate,
        this.poNumber,
        this.vendor,
        this.material,
        this.soWbs,
        this.actualDeliveryDate,
        this.qcCreateTime,
        this.qcStatus,
        this.qcResult,
        this.qcEmployee,
        this.isGoodsArrive,
        this.goodsArriveDate,
        this.goodsArriveDateStr
      });

  DataLstQCNVLByFilter.fromJson(Map<String, dynamic> json) {
    stt = json['stt'];
    qualityControlId = json['qualityControlId'];
    barcodeCreateDate =json['barcodeCreateDate'] == null || json['barcodeCreateDate'] == "" ? null :DateTime.parse(json['barcodeCreateDate']);
    poNumber = json['poNumber'];
    vendor = json['vendor'];
    material = json['material'];
    soWbs = json['soWbs'];
    actualDeliveryDate = json["actualDeliveryDate"] == null || json["actualDeliveryDate"] == ""? null : DateTime.parse(json["actualDeliveryDate"]);
    qcCreateTime = json['qcCreateTime'] == null || json['qcCreateTime'] == ""? null :DateTime.parse(json['qcCreateTime']);
    qcStatus = json['qcStatus'];
    qcResult = json['qcResult'];
    qcEmployee = json['qcEmployee'];
    isGoodsArrive = json['isGoodsArrive'];
    goodsArriveDate = json['goodsArriveDate'] == null || json['goodsArriveDate'] == "" ? null : DateTime.parse(json['goodsArriveDate']);
    goodsArriveDateStr = json['goodsArriveDateStr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stt'] = stt;
    data['qualityControlId'] = qualityControlId;
    data['barcodeCreateDate'] = barcodeCreateDate != null ? barcodeCreateDate!.toIso8601String():null;
    data['poNumber'] = poNumber;
    data['vendor'] = vendor;
    data['material'] = material;
    data['soWbs'] = soWbs;
    data['actualDeliveryDate'] = actualDeliveryDate != null ? actualDeliveryDate!.toIso8601String():null;
    data['qcCreateTime'] = qcCreateTime != null ? qcCreateTime!.toIso8601String(): null;
    data['qcStatus'] = qcStatus;
    data['qcResult'] = qcResult;
    data['qcEmployee'] = qcEmployee;
    data['isGoodsArrive'] = isGoodsArrive;
    data['goodsArriveDate'] = goodsArriveDate != null ? goodsArriveDate!.toIso8601String():null;
    data['goodsArriveDateStr'] = goodsArriveDateStr;
    return data;
  }
}