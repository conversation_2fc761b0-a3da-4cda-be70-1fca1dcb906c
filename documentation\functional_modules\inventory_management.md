# Inventory Management Module

The Inventory Management module in TTF MES Mobile provides comprehensive tools for tracking, controlling, and optimizing inventory throughout the manufacturing facility. This module ensures accurate material counts, efficient storage utilization, and proper inventory control.

## Module Structure

The Inventory Management module is implemented through several key files in the `lib/page/` directory:

### Main Components

- `InventoryManagement.dart`: Core inventory management functionality
- `InventoryMaterialSloc.dart`: Storage location-specific inventory management
- `MaterialRetail.dart`: Management of retail/small quantity materials
- `MaterialUnused.dart`: Tracking and management of unused materials

### QR Code Integration

- `QRCodeInventoryMaterialSloc.dart`: QR scanning for storage location inventory
- `QRCodePageListQC.dart`: QR scanning for inventory quality control
- `QRCodePageListQC2.dart`: Enhanced QR scanning for inventory quality control

### Supporting Screens

- `StatisticsMaterials.dart`: Material statistics and analytics
- `DetailReservation.dart`: Details of material reservations
- `ProductManagement.dart`: Product inventory management

## Features

### Inventory Tracking

The inventory tracking functionality allows users to:

- Monitor current inventory levels by location
- Track material movements in real-time
- Receive alerts for low inventory levels
- View inventory history and trends
- Generate inventory reports

### Storage Location Management

The storage location management feature enables:

- Assignment of materials to specific storage locations
- Optimization of storage space utilization
- Location capacity monitoring
- Location-specific inventory rules
- Barcode/QR code-based location identification

### Material Management

The material management functionality supports:

- Material identification and registration
- Material categorization and classification
- Unit of measure conversion
- Material status tracking (available, reserved, damaged)
- Material expiration date monitoring

### Inventory Counting

The inventory counting system allows:

- Scheduled inventory counts
- Cycle counting
- Count discrepancy resolution
- Count documentation
- Variance analysis and reporting

### Inventory Analysis

The analysis functionality provides:

- Inventory turnover metrics
- Slow-moving inventory identification
- Excess inventory reports
- ABC analysis for inventory prioritization
- Material consumption trend analysis

## Workflows

### Inventory Check Process

1. Access the Inventory Management (`InventoryManagement.dart`)
2. Select storage location to check
3. Scan location QR code or enter manually
4. View current inventory at the location
5. Verify physical count against system count
6. Record discrepancies if any
7. Submit inventory verification

### Material Movement Process

1. Navigate to Inventory Management (`InventoryManagement.dart`)
2. Select source location
3. Scan or select material to move
4. Specify quantity to move
5. Select destination location
6. Record reason for movement
7. Submit movement transaction
8. Update inventory in both locations

### Inventory Count Process

1. Access Material Sloc (`InventoryMaterialSloc.dart`)
2. Select location for inventory count
3. View expected inventory
4. Perform physical count
5. Enter actual count results
6. Record count variances
7. Submit count for approval
8. Update inventory if approved

### Unused Material Management

1. Navigate to Material Unused (`MaterialUnused.dart`)
2. View list of unused materials
3. Select material for disposition
4. Choose disposition action (return, relocate, scrap)
5. Record disposition details
6. Submit disposition request
7. Update inventory records

## Data Models

The inventory management module uses the following data models:

- **InventoryModel**: Inventory item information
- **StorageLocationModel**: Storage location data
- **MaterialModel**: Material information
- **InventoryTransactionModel**: Inventory movement data
- **CountModel**: Inventory count data
- **ReservationModel**: Material reservation information

## API Integration

Inventory operations are synchronized with backend systems through the following API endpoints:

- `/api/inventory/items`: Inventory item management
- `/api/inventory/locations`: Storage location operations
- `/api/inventory/transactions`: Inventory movements
- `/api/inventory/counts`: Inventory counting
- `/api/inventory/analysis`: Inventory analysis

## User Interfaces

### List Views
Inventory list views display:
- Material identification
- Current inventory levels
- Location information
- Material status
- Critical indicators (low stock, expiration)

### Detail Views
Inventory detail views include:
- Detailed material specifications
- Current quantity and location
- Movement history
- Reservation information
- Quality status
- Documentation references

### Analysis Views
Inventory analysis views provide:
- Inventory metrics visualization
- Trend graphs
- ABC classification displays
- Turnover analysis
- Slow-moving inventory highlights

## Integration with Other Modules

The Inventory Management module integrates with other modules in the following ways:

- **Warehouse Management**: Coordinates with location assignments and transfers
- **Quality Control**: Shows quality status of inventory items
- **Production Management**: Provides material availability for production
- **Material Consumption**: Tracks material usage and consumption rates
- **Maintenance Management**: Manages spare parts inventory

## Best Practices

For effective use of the Inventory Management module:

1. Perform regular inventory counts to ensure accuracy
2. Always use barcode/QR scanning to minimize data entry errors
3. Maintain accurate storage location information
4. Promptly record all inventory movements
5. Review inventory reports regularly to identify issues
6. Implement cycle counting for critical materials
7. Keep material master data up to date

## Future Enhancements

Planned improvements for the Inventory Management module include:

1. Advanced inventory forecasting
2. AI-driven inventory optimization
3. Enhanced barcode/QR scanning capabilities
4. RFID integration for automated tracking
5. Real-time inventory visualization
6. Advanced lot and serial number tracking
7. Expiration date management and alerts 