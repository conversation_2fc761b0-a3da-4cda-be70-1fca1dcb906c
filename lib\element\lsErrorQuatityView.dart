// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_typeahead/flutter_typeahead.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:path/path.dart';
// import '../Widget/dialogWidget/DialogErrorQuality.dart';
// import '../model/GetDefectLevel.dart';
// import '../model/dropdownDefectLevel.dart';
// import '../model/multiSelectedErrorQuality.dart';
// import '../model/qualityControlApi.dart';
// import '../model/typeAheadErrorQuatity.dart';
// import '../repository/function/imageFunction.dart';
// import '../repository/function/qualityControlFunction.dart';
// import 'ImageQuatity.dart';
// import 'listImagePicker.dart';
//
// class LsErrorQuatityView extends StatelessWidget {
//   final int index;
//   final List<ErrorList?> lsSelectedError;
//   final List<ErrorList>? lsErrorList;
//   final List<int> lsGetIndexError;
//   // final ValueChanged<MultiSelectedErrorQuality> onTap;
//   final List<TextEditingController> lsTextEditingControllerError_1;
//   final List<TextEditingController> lsTextEditingControllerError_3;
//   final List<List<File>> lsGetFileError;
//   final ValueChanged<MultiSelectImageErrorQuality> pickImage;
//   final ValueChanged<MultiDeleteImageErrorQuality> deleteImage;
//   final ImagePicker pickerImage;
//   final  List<ErrorFileViewModel>? fileViewModel;
//   final QualityControl? qualityControl;
//
//   // final List<FocusNode> focusError;
//   final List<TextEditingController> lsControllerError;
//   final ValueChanged<int>checkErrorClear;
//   final List<bool> checkVisiButtonError;
//   final ValueChanged<int> deleteItemListError;
//   final ValueChanged<TypeAheadErrorQuatity> valueChanged;
//   final ValueChanged<DropdownDefetchLevel> valueChangedDropdownDefetchLevel;
//   final List<DataGetDefectLevel?> lselectDataGetDefectLevel;
//   final List<DataGetDefectLevel> lsDataGetDefectLevel;
//   const LsErrorQuatityView({
//     Key? key,
//     required this.index,
//     required this.lsSelectedError,
//     required this.lsErrorList,
//     required this.lsGetIndexError,
//     // required this.onTap,
//     required this.lsTextEditingControllerError_1,
//     required this.lsTextEditingControllerError_3,
//     required this.lsGetFileError,
//     required this.pickImage,
//     required this.deleteImage,
//     required this.fileViewModel,
//     required this.qualityControl,
//     required this.pickerImage,
//     // required this.focusError,
//     required this.lsControllerError,
//     required this.checkErrorClear,
//     required this.checkVisiButtonError,
//     required this.deleteItemListError,
//     required this.valueChanged,
//     required this.valueChangedDropdownDefetchLevel,
//     required this.lselectDataGetDefectLevel,
//     required this.lsDataGetDefectLevel
//   }) : super(key: key);
//
//
//   @override
//   Widget build(BuildContext context) {
//
//     return Container(
//       margin: EdgeInsets.symmetric(vertical: 5.h),
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 15.w),
//         child: IntrinsicHeight(
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: <Widget>[
//                   Expanded(
//                     flex: 1,
//                     child: Container(
//                       decoration: BoxDecoration(
//                         border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
//                       ),
//                       padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
//                       child: Center(
//                         child: Text(
//                           (index + 1).toString(),
//                           style:
//                               TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     ),
//                   ),
//                   Expanded(
//                     flex: 9,
//                     child: IntrinsicHeight(
//                       child: Column(
//                         mainAxisSize: MainAxisSize.min,
//                         crossAxisAlignment: CrossAxisAlignment.stretch,
//                         children: <Widget>[
//                           IntrinsicHeight(
//                             child: Row(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: <Widget>[
//                                 Expanded(
//                                   flex: 5,
//                                   child: Container(
//                                     padding: EdgeInsets.symmetric(
//                                         vertical: 5.h, horizontal: 5.w),
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                         width: 0.5,
//                                         color: Colors.grey.shade300,
//                                       ),
//                                     ),
//                                     child: Text(
//                                       "Danh sách lỗi",
//                                       style: TextStyle(
//                                           fontSize: 11.sp,
//                                           fontWeight: FontWeight.bold),
//                                     ),
//                                   ),
//                                 ),
//                                 Expanded(
//                                   flex: 4,
//                                   child: Container(
//                                       padding: EdgeInsets.symmetric(
//                                           vertical: 5.h, horizontal: 5.w),
//                                       decoration: BoxDecoration(
//                                         border: Border.all(
//                                             width: 0.5, color: Colors.grey.shade300),
//                                       ),
//                                       child: Text(
//                                         "Số lượng lỗi",
//                                         style: TextStyle(
//                                             fontSize: 11.sp,
//                                             fontWeight: FontWeight.bold),
//                                       )),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           IntrinsicHeight(
//                             child: Row(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: <Widget>[
//                                 Expanded(
//                                   flex: 5,
//                                   child: Container(
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                         width: 0.5,
//                                         color: Colors.grey.shade300,
//                                       ),
//                                     ),
//                                     child: _ListTypeAHead(
//                                       // lsSelectedError: lsSelectedError,
//                                       index: index,
//                                       lsErrorList: lsErrorList,
//                                       checkErrorClear: checkErrorClear,
//                                       // focusError: focusError,
//                                       lsControllerError: lsControllerError,
//                                       qualityControl: qualityControl,
//                                       valueChanged: valueChanged,
//                                     )
//                                     // Column(
//                                     //   children: <Widget>[
//                                         // Padding(
//                                         //   padding: REdgeInsets.all(5),
//                                         //   child: Container(
//                                         //     padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
//                                         //     decoration: BoxDecoration(
//                                         //       border: Border.all(
//                                         //           width: 0.5,
//                                         //           color: Colors.grey.shade400),
//                                         //     ),
//                                         //     child: DropdownButtonHideUnderline(
//                                         //       child: DropdownButton<ErrorList>(
//                                         //         isExpanded: true,
//                                         //         itemHeight: null,
//                                         //         isDense: true,
//                                         //         value: lsSelectedError[index],
//                                         //         iconSize: 15.sp,
//                                         //         style: const TextStyle(
//                                         //             color: Colors.black),
//                                         //         onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null :(value){
//                                         //           onTap(MultiSelectedErrorQuality(value: value,index: index));
//                                         //         },
//                                         //         items: lsErrorList!.map((ErrorList error) {
//                                         //           return DropdownMenuItem<ErrorList>(
//                                         //               value: error,
//                                         //               child: Padding(
//                                         //                 padding: EdgeInsets.symmetric(vertical: 5.h),
//                                         //                 child: Text(
//                                         //                   error.catalogTextVi.toString(),
//                                         //                   style: TextStyle(color: Colors.black, fontSize: 11.sp),
//                                         //                 ),
//                                         //               ));
//                                         //         }).toList(),
//                                         //         selectedItemBuilder: (BuildContext context) {
//                                         //           return lsErrorList!.map<Widget>((ErrorList error) {
//                                         //             return Text(error.catalogTextVi.toString(), style: TextStyle(
//                                         //                 color: Colors.black,
//                                         //                 fontSize: 11.sp), overflow: TextOverflow.ellipsis);
//                                         //           }).toList();
//                                         //         },
//                                         //       ),
//                                         //     ),
//                                         //   ),
//                                         // ),
//                                         // SizedBox(height: error == true ? 1.h : 0),
//                                         // Visibility(
//                                         //     visible: error,
//                                         //     child: Row(
//                                         //       mainAxisAlignment:
//                                         //           MainAxisAlignment.start,
//                                         //       children: <Widget>[
//                                         //         Flexible(
//                                         //           flex: 1,
//                                         //           child: Icon(Icons.error_outline,
//                                         //               size: 13.sp,
//                                         //               color: Colors.red[700]),
//                                         //         ),
//                                         //         SizedBox(width: 5.w),
//                                         //         Flexible(
//                                         //             flex: 8,
//                                         //             child: Text("Bạn chưa chọn Lỗi",
//                                         //                 style: TextStyle(
//                                         //                     fontSize: 11.sp,
//                                         //                     color: Colors.red[700])))
//                                         //       ],
//                                         //     )),
//                                         // SizedBox(height: error == true ? 1.h : 0),
//                                     //   ],
//                                     // ),
//                                   ),
//                                 ),
//                                 Expanded(
//                                   flex: 4,
//                                   child: Container(
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                           width: 0.5, color: Colors.grey.shade300),
//                                     ),
//                                     child: Padding(
//                                           padding: REdgeInsets.all(5),
//                                           child: Container(
//                                             padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
//                                             decoration: BoxDecoration(
//                                               border: Border.all(width: 0.5.w,  color: Colors.grey.shade400),
//                                             ),
//                                             child: TextFormField(
//                                               enabled:qualityControl!.qcType == "NVL" ?qualityControl!.qualityChecker != null?false:true:true,
//                                               textAlign: TextAlign.center,
//                                               keyboardType: TextInputType.number,
//                                               inputFormatters: <TextInputFormatter>[
//                                                 FilteringTextInputFormatter.allow(RegExp("[0-9]")),
//                                               ],
//                                               controller: lsTextEditingControllerError_1[index],
//                                               style: TextStyle(fontSize: 12.sp),
//                                               decoration: InputDecoration(
//                                                 border: InputBorder.none,
//                                                 focusedBorder: InputBorder.none,
//                                                 enabledBorder: InputBorder.none,
//                                                 errorBorder: InputBorder.none,
//                                                 disabledBorder: InputBorder.none,
//                                                 filled: true,
//                                                 isDense: true,
//                                                 fillColor: Colors.white,
//                                                 hintStyle: TextStyle(fontSize: 12.sp),
//                                                 contentPadding: EdgeInsets.zero,
//                                               ),
//                                             ),
//                                           ),
//                                     ),
//                                   ),
//                                 )
//                               ],
//                             ),
//                           ),
//                           IntrinsicHeight(
//                             child: Row(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: <Widget>[
//                                 Expanded(
//                                   flex: 5,
//                                   child: Container(
//                                     padding: EdgeInsets.symmetric(
//                                         vertical: 5.h, horizontal: 5.w),
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                         width: 0.5,
//                                         color: Colors.grey.shade300,
//                                       ),
//                                     ),
//                                     child: Text(
//                                       "Mức độ lỗi",
//                                       style: TextStyle(
//                                           fontSize: 11.sp,
//                                           fontWeight: FontWeight.bold),
//                                     ),
//                                   ),
//                                 ),
//                                 Expanded(
//                                   flex: 4,
//                                   child: Container(
//                                       padding: EdgeInsets.symmetric(
//                                           vertical: 5.h, horizontal: 5.w),
//                                       decoration: BoxDecoration(
//                                         border: Border.all(
//                                             width: 0.5, color: Colors.grey.shade300),
//                                       ),
//                                       child: Text(
//                                         "Ghi chú",
//                                         style: TextStyle(
//                                             fontSize: 11.sp,
//                                             fontWeight: FontWeight.bold),
//                                       )),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           IntrinsicHeight(
//                             child: Row(
//                               crossAxisAlignment: CrossAxisAlignment.stretch,
//                               children: <Widget>[
//                                 Expanded(
//                                   flex: 5,
//                                   child: Container(
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                           width: 0.5, color: Colors.grey.shade300),
//                                     ),
//                                     child: Column(
//                                       children: <Widget>[
//                                         Padding(
//                                           padding: REdgeInsets.all(5),
//                                           child: Container(
//                                             padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 3.h),
//                                             decoration: BoxDecoration(
//                                               border: Border.all(width: 0.5.w,  color: Colors.grey.shade400),
//                                             ),
//                                             child:
//                                             DropdownButtonHideUnderline(
//                                               child: DropdownButton<DataGetDefectLevel>(
//                                                 isExpanded: true,
//                                                 isDense: true,
//                                                 itemHeight: null,
//                                                 value: lselectDataGetDefectLevel[index],
//                                                 iconSize: 15.sp,
//                                                 style: const TextStyle(color: Colors.white),
//                                                 onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : (DataGetDefectLevel? value){
//                                                   valueChangedDropdownDefetchLevel(DropdownDefetchLevel(index: index,value: value));
//                                                 },
//                                                 items: lsDataGetDefectLevel.map((DataGetDefectLevel result) {
//                                                   return DropdownMenuItem<DataGetDefectLevel>(
//                                                       value: result,
//                                                       child: Padding(
//                                                         padding: EdgeInsets.symmetric(vertical: 5.h),
//                                                         child: Text(
//                                                           result.value.toString(),
//                                                           style: TextStyle(
//                                                               color: Colors.black,
//                                                               fontSize: 11.sp),
//                                                         ),
//                                                       ));
//                                                 }).toList(),
//                                                 selectedItemBuilder: (BuildContext context) {
//                                                   return lsDataGetDefectLevel.map((DataGetDefectLevel result) {
//                                                     return Text(result.value.toString(),style: TextStyle(
//                                                         color: Colors.black,
//                                                         fontSize: 11.sp), overflow: TextOverflow.ellipsis);
//                                                   }).toList();
//                                                 },
//                                               ),
//                                             ),
//                                             // TextFormField(
//                                             //   enabled: qualityControl!.qcType == "NVL" ?qualityControl!.qualityChecker != null?false:true:true,
//                                             //   textAlign: TextAlign.center,
//                                             //   controller: lsTextEditingControllerError_2[index],
//                                             //   style: TextStyle(fontSize: 12.sp),
//                                             //   decoration: InputDecoration(
//                                             //     border: InputBorder.none,
//                                             //     focusedBorder: InputBorder.none,
//                                             //     enabledBorder: InputBorder.none,
//                                             //     errorBorder: InputBorder.none,
//                                             //     disabledBorder: InputBorder.none,
//                                             //     filled: true,
//                                             //     isDense: true,
//                                             //     fillColor: Colors.white,
//                                             //     hintStyle: TextStyle(fontSize: 12.sp),
//                                             //     contentPadding: EdgeInsets.zero,
//                                             //   ),
//                                             // ),
//                                           ),
//                                         ),
//                                         // SizedBox(height: error_2 == true ? 1.h : 0),
//                                         // Visibility(
//                                         //     visible: error_2,
//                                         //     child: Row(
//                                         //       mainAxisAlignment:
//                                         //           MainAxisAlignment.start,
//                                         //       children: <Widget>[
//                                         //         Flexible(
//                                         //           flex: 1,
//                                         //           child: Icon(Icons.error_outline,
//                                         //               size: 13.sp,
//                                         //               color: Colors.red[700]),
//                                         //         ),
//                                         //         SizedBox(width: 5.w),
//                                         //         Flexible(
//                                         //             flex: 8,
//                                         //             child: Text("Bạn chưa nhập mức độ lỗi",
//                                         //                 style: TextStyle(
//                                         //                     fontSize: 11.sp,
//                                         //                     color: Colors.red[700])))
//                                         //       ],
//                                         //     )),
//                                         // SizedBox(height: error_2 == true ? 1.h : 0),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                                 // muc do loi controller
//                                 Expanded(
//                                   flex: 4,
//                                   child: Container(
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                           width: 0.5, color: Colors.grey.shade300),
//                                     ),
//                                     child: Column(
//                                       children: [
//                                         Padding(
//                                           padding: REdgeInsets.all(5),
//                                           child: Container(
//                                             padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
//                                             decoration: BoxDecoration(
//                                               border: Border.all(width: 0.5.w,  color: Colors.grey.shade400),
//                                             ),
//                                             child: TextFormField(
//                                               enabled: qualityControl!.qcType == "NVL" ?qualityControl!.qualityChecker != null?false:true:true,
//                                               maxLines: null,
//                                               textAlign: TextAlign.center,
//                                               controller: lsTextEditingControllerError_3[index],
//                                               style: TextStyle(fontSize: 12.sp),
//                                               decoration: InputDecoration(
//                                                 border: InputBorder.none,
//                                                 focusedBorder: InputBorder.none,
//                                                 enabledBorder: InputBorder.none,
//                                                 errorBorder: InputBorder.none,
//                                                 disabledBorder: InputBorder.none,
//                                                 filled: true,
//                                                 isDense: true,
//                                                 fillColor: Colors.white,
//                                                 hintStyle: TextStyle(fontSize: 12.sp),
//                                                 contentPadding: EdgeInsets.zero,
//                                               ),
//                                             ),
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           IntrinsicHeight(
//                             child: Container(
//                               padding: EdgeInsets.symmetric(
//                                   vertical: 5.h, horizontal: 5.w),
//                               decoration: BoxDecoration(
//                                 border: Border.all(
//                                     width: 0.5, color: Colors.grey.shade300),
//                               ),
//                               child: Row(
//                               children: <Widget> [
//                                 Expanded(
//                                 flex: 8,
//                                 child: lsGetFileError[index].isEmpty
//                                   ? Row(
//                                       crossAxisAlignment: CrossAxisAlignment.stretch,
//                                       children: <Widget>[
//                                         GestureDetector(
//                                             onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null :() async {
//                                               final check = await QualityControlFunction.pickImage(context);
//                                               debugPrint(check.toString());
//                                               if(check != null) {
//                                                 bool checkPermission = await ImageFunction.handlePermission(check);
//                                                 if (checkPermission == true) {
//                                                   if (check == true) {
//                                                     List<XFile>? selectedImages = await pickerImage.pickMultiImage(
//                                                      maxWidth: globalImageConfig.maxWidth,
//                                                      maxHeight: globalImageConfig.maxHeight,
//                                                      imageQuality: globalImageConfig.imageQuality,
//                                                    );
//                                                     if (selectedImages!.isEmpty) return;
//                                                     for (var i in selectedImages) {
//                                                       final itemImage = await ImageFunction.saveImageMulti(i.path);
//                                                       pickImage(MultiSelectImageErrorQuality(index: index,file: itemImage));
//                                                     }
//                                                   } else {
//                                                     final image = await ImagePicker().pickImage(maxWidth: globalImageConfig.maxWidth, maxHeight: globalImageConfig.maxHeight, imageQuality: globalImageConfig.imageQuality, source: ImageSource.camera);
//                                                     if (image == null) return;
//                                                     final imageProfile = await ImageFunction.saveImage(image.path);
//                                                     pickImage(MultiSelectImageErrorQuality(index: index,file: imageProfile));
//                                                   }
//                                                 }
//                                               }
//                                             },
//                                             child: Container(
//                                               padding: EdgeInsets.symmetric(
//                                                   vertical: 3.h, horizontal: 9.w),
//                                               decoration: BoxDecoration(
//                                                 borderRadius: BorderRadius.circular(10.r),
//                                                 color: Colors.grey.shade100,
//                                               ),
//                                               child: Center( child: Text(
//                                               "Chọn tệp",
//                                               style: TextStyle(
//                                                   fontSize: 11.sp,
//                                                   color: Colors.blue),
//                                             ),
//                                               ),
//                                           ),
//                                         ),
//                                         SizedBox(width: 10.w),
//                                         Center(
//                                           child: Text(
//                                             "Chưa chọn tệp nào",
//                                             style: TextStyle(fontSize: 11.sp),
//                                           ),
//                                         ),
//                                       ],
//                                     )
//                                   : Column(
//                                       crossAxisAlignment: CrossAxisAlignment.start,
//                                       children: <Widget>[
//                                   GestureDetector(
//                                             onTap: ()async{
//                                                 final check = await QualityControlFunction.pickImage(context);
//                                                 debugPrint(check.toString());
//                                                 if(check != null) {
//                                                   bool checkPermission = await ImageFunction.handlePermission(check);
//                                                   if (checkPermission == true) {
//                                                     if (check == true) {
//                                                       List<XFile>? selectedImages = await pickerImage.pickMultiImage(
//                                                            maxWidth: globalImageConfig.maxWidth,
//                                                            maxHeight: globalImageConfig.maxHeight,
//                                                            imageQuality: globalImageConfig.imageQuality,
//                                                          );
//                                                       if (selectedImages!.isEmpty) return;
//                                                       for (var i in selectedImages) {
//                                                         final itemImage = await ImageFunction.saveImageMulti(i.path);
//                                                         pickImage(MultiSelectImageErrorQuality(index: index,file: itemImage));
//                                                       }
//                                                     } else {
//                                                       final image = await ImagePicker().pickImage(maxWidth: globalImageConfig.maxWidth, maxHeight: globalImageConfig.maxHeight, imageQuality: globalImageConfig.imageQuality, source: ImageSource.camera);
//                                                       if (image == null) return;
//                                                       final imageProfile = await ImageFunction.saveImage(image.path);
//                                                       pickImage(MultiSelectImageErrorQuality(index: index,file: imageProfile));
//                                                     }
//                                                   }
//                                                 }
//                                             },
//                                             child: Container(
//                                               padding: EdgeInsets.symmetric(
//                                                   vertical: 3.h, horizontal: 9.w),
//                                               decoration: BoxDecoration(
//                                                 borderRadius: BorderRadius.circular(10.r),
//                                                 color: Colors.grey.shade100,
//                                               ),
//                                               child: Text(
//                                               "Chọn tệp",
//                                               style: TextStyle(
//                                                   fontSize: 11.sp,
//                                                   color: Colors.blue),
//                                             ),
//                                             ),
//                                           ),
//                                         SizedBox(height: 10.h),
//                                         Wrap(
//                                           spacing: 3.w,
//                                           runSpacing: 3.h,
//                                           children: List.generate(
//                                             lsGetFileError[index].length,
//                                             (indexImageError) {
//                                               // String filenameError = basename(
//                                               //     lsGetFileError[index]
//                                               //             [indexImageError]
//                                               //         .path);
//                                               return SizedBox(
//                                                 width: 50.w,
//                                                 child: Stack(
//                                                   children: <Widget>[
//                                                      GestureDetector(
//                                                        onTap: (){
//                                                          Navigator.push(
//                                                            context,
//                                                            MaterialPageRoute(
//                                                              builder: (context) => ImageQuatity(
//                                                                lsImage: lsGetFileError[index],
//                                                                index:indexImageError
//                                                              ),
//                                                            ),
//                                                          );
//                                                        },
//                                                        child: ListImagePicker(
//                                                             fileImage:  lsGetFileError[index][indexImageError]),
//                                                      ),
//                                                     Align(
//                                                       alignment: Alignment.topRight,
//                                                       child: IconButton(
//                                                         highlightColor: Colors.transparent,
//                                                         hoverColor: Colors.transparent,
//                                                         constraints: const BoxConstraints(),
//                                                         iconSize: 17.sp,
//                                                         color: Colors.red.shade800,
//                                                         icon: const Icon(Icons.remove_circle),
//                                                         onPressed: () {
//                                                           deleteImage(MultiDeleteImageErrorQuality(index: index, indexImageError: indexImageError));
//                                                         },
//                                                       ),
//                                                     )
//                                                   ],
//                                                 ),
//                                               );
//                                             },
//                                           ),
//                                         ),
//                                         SizedBox(height: 10.h),
//                                       ],
//                                     ),
//                                 ),
//                                 Visibility(
//                                   visible: fileViewModel!.isNotEmpty ? true: false,
//                                   child:
//                                   Expanded(
//                                     flex: 2,
//                                     child: GestureDetector(
//                                       onTap: () {
//                                           String title = "";
//                                           String error = lsSelectedError[index] == null ? "":lsSelectedError[index]!.catalogTextVi.toString();
//                                           int indexErrorName = error.indexOf('|');
//                                           title = lsSelectedError[index] == null ? "":lsSelectedError[index]!.catalogTextVi.toString().substring(indexErrorName + 2).toUpperCase();
//                                         // String error = lsSelectedError[index]!.catalogTextVi.toString();
//                                         showDialog(
//                                           context: context,
//                                           builder: (BuildContext context) {
//                                             return   DialogErrorQuality(
//                                               title: 'HÌNH ẢNH LỖI ' + title,
//                                               listImage:fileViewModel
//                                             );
//                                           },
//                                         );
//                                       },
//                                       child:Container(
//                                         padding: EdgeInsets.symmetric(vertical: 5.h),
//                                         margin: EdgeInsets.symmetric(horizontal: 5.w),
//                                         decoration:  BoxDecoration(
//                                           color: const Color(0xff0052cc),
//                                           borderRadius: BorderRadius.all(
//                                             Radius.circular(2.r),
//                                           ),
//                                         ),
//                                       child: Icon(
//                                         Icons.image,
//                                         color: Colors.white,
//                                         size: 15.sp,
//                                       ),
//                                       ),
//                                     ),
//                                   ),
//                                 )
//                             ],
//                               ),
//                             ),
//                           ),
//                           Visibility(
//                             visible: checkVisiButtonError[index],
//                             child: IntrinsicHeight(
//                               child: Container(
//                                 decoration: BoxDecoration(
//                                   border: Border.all(
//                                       width: 0.5, color: Colors.grey.shade300),
//                                 ),
//                                 child: Align(
//                                   alignment: Alignment.topRight,
//                                   child: IconButton(
//                                     highlightColor: Colors.transparent,
//                                     hoverColor: Colors.transparent,
//                                     constraints: const BoxConstraints(),
//                                     iconSize: 17.sp,
//                                     color: Colors.red.shade800,
//                                     icon: const Icon(Icons.delete),
//                                     onPressed: () {
//                                       deleteItemListError(index);
//                                     },
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//       ),
//     );
//   }
// }
// class _ListTypeAHead extends StatelessWidget {
//   const _ListTypeAHead({
//     Key? key,
//     required this.qualityControl,
//     required this.lsControllerError,
//     required this.index,
//     // required this.focusError,
//     // required this.lsSelectedError,
//     required this.lsErrorList,
//     required this.checkErrorClear,
//     required this.valueChanged
//   }) : super(key: key);
//
//   final QualityControl? qualityControl;
//   final List<TextEditingController> lsControllerError;
//   final int index;
//   // final List<FocusNode> focusError;
//   // final List<ErrorList?> lsSelectedError;
//   final List<ErrorList>? lsErrorList;
//   final ValueChanged<int> checkErrorClear;
//   final ValueChanged<TypeAheadErrorQuatity> valueChanged;
//
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       // key: UniqueKey(),
//       child: Padding(
//         padding: REdgeInsets.all(5),
//         child: Center(
//           child: Container(
//             decoration: BoxDecoration(
//               border: Border.all(
//                 width: 0.5.w,
//                 color: Colors.grey.shade400,
//               ),
//             ),
//             child: TypeAheadField(
//               suggestionsBoxDecoration: SuggestionsBoxDecoration(
//                 constraints: BoxConstraints(
//                   minWidth: 150.w,
//                 ),
//               ),
//               textFieldConfiguration: TextFieldConfiguration(
//                   enabled: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? false:true,
//                   decoration:  InputDecoration(
//                     labelStyle: TextStyle(fontSize: 11.sp),
//                     contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
//                     isDense: true,
//                     border: InputBorder.none,
//                     focusedBorder: InputBorder.none,
//                     enabledBorder: InputBorder.none,
//                   ),
//                   controller: lsControllerError[index],
//                   // focusNode: focusError[index],
//                   style: TextStyle(fontSize: 12.sp),
//                   onChanged: (value){
//                     checkErrorClear(index);
//                   }
//               ),
//               suggestionsCallback: (pattern) {
//                 return QualityControlFunction.filterQualityControlErrorList(lsErrorList ?? [], pattern);
//               },
//               itemBuilder: (context, suggestion) {
//                 return ListTile(
//                   title: Text((suggestion as ErrorList).catalogTextVi ?? " ",style: TextStyle(fontSize: 12.sp)),
//                 );
//               },
//               onSuggestionSelected: (suggestion) {
//                 valueChanged(TypeAheadErrorQuatity(index: index,errorList: suggestion as ErrorList));
//                 // lsControllerError[index].text = (suggestion as ErrorList).catalogTextVi ?? "";
//                 // lsSelectedError[index] = suggestion;
//                 // print(lsSelectedError[index]!.catalogTextVi.toString());
//               },
//               noItemsFoundBuilder: (value) {
//                 return Padding(
//                     padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
//                     child: Text(
//                         "Không tìm thấy kết quả",
//                         style: TextStyle(fontSize: 11.sp)));
//               },
//             ),
//           ),
//         ),
//         ),
//     );
//   }
// }
// class _ErrorValidateView extends StatelessWidget {
//   final bool error;
//   final String text;
//   const _ErrorValidateView({Key? key,required this.error, required this.text}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return  Visibility(
//         visible: error,
//         child: Row(
//           mainAxisAlignment:
//           MainAxisAlignment.start,
//           children: <Widget>[
//             Flexible(
//               flex: 1,
//               child: Icon(
//                   Icons.error_outline,
//                   size: 13.sp,
//                   color:
//                   Colors.red[700]),
//             ),
//             SizedBox(width: 5.w),
//             Flexible(
//                 flex: 8,
//                 child: Text(
//                     text,
//                     style: TextStyle(
//                         fontSize: 11.sp,
//                         color: Colors
//                             .red[700])))
//           ],
//         ));
//   }
// }