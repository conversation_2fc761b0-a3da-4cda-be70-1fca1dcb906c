# ImportProduct Screen Analysis & Execution Flow

## Overview
The ImportProduct screen is a complex Flutter widget that handles finished product warehouse import operations with **quantity-based tracking**. It manages QR scanning for PO identification, quantity input validation, batch generation, and data submission with extensive caching mechanisms.

## Key Changes in Quantity-Based Approach
- **Tracking by PO instead of individual serials**: Each scanned QR code identifies a Production Order (PO)
- **Quantity input**: Users can specify how many units to import for each PO
- **Import validation**: System prevents importing more than the remaining quantity (Total PO quantity - Already imported)
- **Multiple imports per PO**: Supports partial imports with import sequence tracking
- **Enhanced data model**: Includes SO, SO Item, WBS information for better traceability

## Flowchart

```mermaid
graph TD
    A[initState] --> B[loadInitialData]
    B --> C{Session Timeout Check}
    C -->|Valid| D[Setup Plant/Company List]
    C -->|Expired| E[Show Timeout Screen]
    D --> F[Load Sloc Addresses API]
    F --> G[Generate Initial Batch Number]
    G --> H[UI Ready State]
    
    H --> I[User Interaction Phase]
    I --> J{User Action}
    
    J -->|Select Plant| K[Update Plant Selection]
    K --> L[Reload Sloc for New Plant]
    L --> M[Update Batch Number]
    
    J -->|Select Product Group| N[Update Product Group]
    N --> O[Regenerate Batch Number]
    
    J -->|QR Scan Location| P[Open Address Scanner]
    P --> Q[Set Address from QR Code]
    Q --> R[Update Storage Location]
    
    J -->|Type Storage Bin| S[Storage Bin Changed]
    S --> T[Debounced Timer 1s]
    T --> U[API: fetchStorageBinByParam]
    U --> V[Show Autocomplete Options]
    
    J -->|Scan Products| W{Validation Check}
    W -->|Fail| X[Show Error Toast]
    W -->|Pass| Y[Open ContinuousQRScannerPage]
    Y --> Z[Return Scanned Serials]
    Z --> AA[Add Scanned Product]
    
    AA --> BB{Duplicate Check}
    BB -->|Duplicate| CC[Show Duplicate Toast]
    BB -->|New| DD[Fetch PO Information]
    
    DD --> EE{Cache Check}
    EE -->|Hit| FF[Use Cached Data]
    EE -->|Miss| GG[API: fetchPOInfoBySerial]
    GG --> HH[Update Cache & Quantities]
    
    FF --> II[Add to Scanned Products]
    HH --> II
    II --> JJ[Update UI Summary]
    
    J -->|Submit| KK[Validate and Submit]
    KK --> LL{Validation}
    LL -->|Fail| MM[Show Validation Error]
    LL -->|Pass| NN[Create FinishedProductImport]
    NN --> OO[API: importFinishedProduct]
    OO --> PP{Success}
    PP -->|Success| QQ[Clear Form & Show Success]
    PP -->|Error| RR[Show Error Message]
```


## Key Components & State Variables

### Core Controllers
```dart
// Form validation
final _formKey = GlobalKey<FormState>();

// Text input controllers
final TextEditingController _warehouseController = TextEditingController();
final TextEditingController _sapOrderController = TextEditingController();
final TextEditingController _productCodeController = TextEditingController();
final TextEditingController _productNameController = TextEditingController();
final TextEditingController _serialController = TextEditingController();
final TextEditingController _productGroupController = TextEditingController();
final TextEditingController _batchController = TextEditingController();
final TextEditingController _locationController = TextEditingController();
```

### State Management Variables
```dart
// UI State
bool _isLoading = false;
bool _isNotWifi = false;
bool _isError = false;
bool _isSaving = false;
bool _disableButton = false;
late bool _timeOut;
String _error = "";

// Plant/Company Selection
SalesOrgCodes? _selectedSalesOrgCodes;
List<SalesOrgCodes>? _salesOrgCodes;

// Storage Location Data
String? _storageBinID;
DataSlocAddress? _selectedSloc;
List<DataSlocAddress> _getLsDataSlocAddress = [];
List<DataGetStorageBin> _lsDataGetStorageBn = [];

// Product Data
List<ScannedProduct> _scannedProducts = []; // Now tracks by PO with quantities
Map<String, POInfo> _poInfoCache = {};      // Cache PO information including imported quantities
String? _selectedProductGroup;
```

## Execution Flow

### 1. Initialization (`initState()`)
```
initState() 
  ↓
_loadInitialData()
  ↓
Session Timeout Check
  ↓
Plant/Company List Setup
  ↓
Load Sloc Addresses
  ↓
Generate Initial Batch Number
```

#### Session Timeout Validation
- Compares current date with `widget.dateTimeOld`
- If dates don't match, shows timeout screen
- Prevents stale sessions from proceeding

#### Plant Selection Logic
- Creates default plant list from user data
- Attempts to load extended company list from storage
- Falls back gracefully if storage access fails
- Sets `_selectedSalesOrgCodes` to current user's plant

#### Sloc Address Loading
- Calls `ImportWareHouseFunction.fetchSlocAddress(plant, token)`
- Populates `_getLsDataSlocAddress` for dropdown selection

### 2. Batch Number Generation Algorithm
```dart
String _generateBatchNumber() {
  // Format: N + ProductGroup(AA) + Day(DD) + Month(M) + Year(YY) + Status(D) + Company(C)
  // Example: NNĐ24D24DH
  
  const prefix = "N";                           // Always "N"
  final productGroup = _selectedProductGroup;   // AA: Product group code
  final day = now.day.toString().padLeft(2, '0'); // DD: Day with zero padding
  
  // M: Month encoding (1-9, O=10, N=11, D=12)
  String month = now.month <= 9 ? now.month.toString() : 
                 now.month == 10 ? 'O' : 
                 now.month == 11 ? 'N' : 'D';
                 
  final year = now.year.toString().substring(2); // YY: Last 2 digits of year
  const status = "D";                           // B: Always "D" for status
  
  // C: Company code mapping
  String companyCode = "H"; // Default
  // Maps plant codes to company letters via _companyCodes
}
```

### 3. Storage Location Selection Flow
```
User Scans QR / Manual Selection
  ↓
_setAddressQRCode()
  ↓
Update _selectedSloc
  ↓
Auto-populate warehouse controller
  ↓
Validate storage bin requirements
  ↓
Update error states
```

#### Storage Bin Auto-completion
```
User Types in Storage Bin
  ↓
_onStorageBinChanged()
  ↓
Debounced Timer (1 second)
  ↓
_getAutocompleteStorageBin()
  ↓
API Call: fetchStorageBinByParam()
  ↓
Update _lsDataGetStorageBn
  ↓
Display dropdown suggestions
```

### 4. Product Scanning Workflow

#### QR Scanning Process
```
User Clicks "Quét TP" Button
  ↓
Validation Checks (Plant, Product Group, Sloc)
  ↓
Navigate to ContinuousQRScannerPage
  ↓
Return with scanned serials
  ↓
Process each serial via _addScannedProduct()
```

#### Product Addition Flow
```
_addScannedProduct(serial)
  ↓
Duplicate Check (prevent same serial twice)
  ↓
_fetchPOInformation(serial)
  ↓
Cache Check (_poInfoCache)
  ↓
API Call (if cache miss): fetchPOInfoBySerial()
  ↓
Update Cache & PO Quantities
  ↓
Add to _scannedProducts list
  ↓
Update UI with success toast
```

#### Caching Strategy
```dart
// Production Order Extraction
String productionOrder = serial.length > 5 ? 
    serial.substring(0, serial.length - 5) : serial;

// Cache Key: Production Order (not full serial)
// Cache Value: POInfo object
_poInfoCache[productionOrder] = poInfo;

// Quantity Tracking: By PO Number
_poQuantities[poInfo.po] = poInfo.quantity;
```

### 5. Data Validation & Submission

#### Pre-submission Validation
```
_validateAndSubmit()
  ↓
Check Plant Selection
  ↓
Check Sloc Selection  
  ↓
Check Storage Bin (if required)
  ↓
Check Product Count (> 0)
  ↓
Create FinishedProductImport object
  ↓
API Submission
```

#### Data Structure for API
```dart
class FinishedProductImport {
  final String plant;        // Selected plant code
  final String sloc;         // Storage location code
  final String? slocId;      // Storage location ID
  final String? storageBin;  // Storage bin code
  final String? storageBinId;// Storage bin ID
  final String batch;        // Generated batch number
  final List<ScannedProduct> products; // Products with quantities (not individual serials)
}

class ScannedProduct {
  final String sapOrder;     // Production Order (PO)
  final String productCode;  // Product code
  final String productName;  // Product name
  int importQuantity;        // Quantity to import (user-editable)
  final String so;           // Sales Order
  final String soItem;       // Sales Order Item
  final String wbs;          // Work Breakdown Structure
  final String unit;         // Unit of measure
  final int totalQuantity;   // Total quantity in PO
  final int importedQuantity;// Already imported quantity
  // ... other fields
}

class POInfo {
  final String po;
  final int totalQuantity;
  final int importedQuantity;
  final int remainingQuantity; // Computed: totalQuantity - importedQuantity
  final String? so;
  final String? soItem;
  final String? wbs;
  final int importCount;       // Number of times this PO has been imported
  final int lastImportSequence;
  // ... other fields
}
```

## Data Processing Patterns

### 1. Reactive State Updates
- Controllers trigger immediate validation
- Timer-based debouncing for API calls
- Error state management with visual feedback

### 2. Caching Strategy
- **PO Info Cache**: Keyed by production order (serial minus last 5 chars)
- **Quantity Tracking**: Maps PO numbers to total quantities
- **Cross-page Persistence**: Cache shared with ContinuousQRScannerPage

### 3. Validation Hierarchy
```
Session Timeout (Highest Priority)
  ↓
Required Field Validation
  ↓
Data Consistency Checks
  ↓
Business Logic Validation
  ↓
API Submission
```

## UI Components & Sections

### Section I: THÔNG TIN NHẬP KHO THÀNH PHẨM
- **Date Field**: Auto-populated, read-only
- **Plant Dropdown**: Single or multi-option based on user permissions
- **Product Group Dropdown**: Hardcoded options with key-value pairs

### Section II: CHỌN VỊ TRÍ KHO NHẬP
- **QR Scan Button**: Opens address scanner
- **Plant Display**: Shows selected plant
- **Sloc Dropdown**: Populated from API
- **Storage Bin**: Auto-complete with debounced search

### Section III: CHI TIẾT NHẬP KHO
- **PO Summary**: Real-time calculation of scanned vs total quantities
- **QR Scan Button**: Opens continuous product scanner
- **Product Table**: Scrollable table with delete functionality
- **Submit Button**: Validates and submits data

## Error Handling & Edge Cases

### 1. Network Issues
- Graceful fallback for company list loading
- Error states for API failures
- Retry mechanisms for critical operations

### 2. Data Integrity
- Duplicate serial prevention
- Cache consistency checks
- Production order validation

### 3. User Experience
- Loading overlays during API calls
- Toast messages for user feedback
- Form validation with error highlighting

## Debug Features (Development Mode)
- Mock QR scan buttons with predefined serials
- Multiple PO scenarios for testing
- Visual indicators for different product groups

## Key Dependencies
- `ImportWareHouseFunction`: API integration
- `ContinuousQRScannerPage`: Product scanning
- `POInfo` model: Product information structure
- Various utility functions for formatting and validation

## API Endpoints Summary

| API Method | Purpose | When Called | Data Flow |
|------------|---------|-------------|-----------|
| `ImportWareHouseFunction.fetchSlocAddress()` | Load storage locations for plant | Screen initialization & plant change | Plant → Sloc dropdown options |
| `ImportWareHouseFunction.fetchStorageBinByParam()` | Auto-complete storage bins | User typing in storage bin field (debounced) | Search term → Storage bin suggestions |
| `ImportWareHouseFunction.fetchPOInfoBySerial()` | Get PO details with imported quantities | QR scanning (cache miss) | Serial → POInfo with SO/WBS info + import history |
| `ImportWareHouseFunction.importFinishedProduct()` | Submit quantity-based warehouse import | Form submission | FinishedProductImport with quantities → Success/Error |
| `LoginFunction.getCompanyListFromStorage()` | Load user's accessible companies | Screen initialization (fallback) | Username → Company list for plant dropdown |

## Caching Behavior

| Cache Type | Key | Value | Lifecycle | Shared With |
|------------|-----|-------|-----------|-------------|
| `_poInfoCache` | Production Order (serial[0:-5]) | POInfo object with import history | Screen lifetime | ContinuousQRScannerPage |
| Company List Storage | Username | List of companies | Persistent | Multiple screens |

## Database Changes

### New Table: ImportProductModel
```sql
CREATE TABLE ImportProductModel (
    ImportProductId uniqueidentifier PRIMARY KEY,
    PO nvarchar(50) NOT NULL,                -- Production Order
    ProductCode nvarchar(50) NOT NULL,       -- Product Code
    ImportQuantity int NOT NULL,             -- Quantity imported in this transaction
    ImportSequence int NOT NULL,             -- Import sequence (1st, 2nd, 3rd import for same PO)
    SO nvarchar(50) NULL,                    -- Sales Order
    SOItem nvarchar(10) NULL,                -- Sales Order Item  
    WBS nvarchar(50) NULL,                   -- Work Breakdown Structure
    Status nvarchar(20) NOT NULL,            -- Pending/Success/Failed
    Plant nvarchar(10) NOT NULL,
    SlocId uniqueidentifier NOT NULL,
    StorageBinId uniqueidentifier NULL,
    Batch nvarchar(50) NOT NULL,
    -- ... other audit fields
);
```

This table enables:
- **Multiple imports per PO**: Same PO can be imported multiple times
- **Import history tracking**: Complete audit trail of who imported what and when
- **Quantity validation**: Backend calculates remaining quantity = PO total - sum of successful imports
- **Enhanced traceability**: SO, SOItem, WBS fields for better supply chain tracking

This analysis provides a comprehensive understanding of the ImportProduct screen's architecture, data flow, and processing patterns for future AI assistance and development reference. 
 
```mermaid
graph LR
    A[Serial Input: 350001015618616] --> B[Extract Production Order]
    B --> C[Production Order: 3500010156]
    C --> D{Check Cache}
    D -->|Cache Hit| E[Return Cached POInfo]
    D -->|Cache Miss| F[API Call: fetchPOInfoBySerial]
    F --> G[POInfo Response]
    G --> H[Cache by Production Order]
    H --> I[Store PO Quantities]
    E --> J[Add to Scanned Products]
    I --> J
    
    K[poInfoCache Map] --> L[Key: Production Order<br/>Value: POInfo Object]
    M[poQuantities Map] --> N[Key: PO Number<br/>Value: Total Quantity]
    
    O[UI Summary Section] --> P[Group by PO Number]
    P --> Q[Calculate Scanned vs Total]
    Q --> R[Display Progress for each PO]
    
    S[Batch Generation] --> T[Prefix: N]
    T --> U[Product Group: Selected Value]
    U --> V[Date Components: DD/M/YY]
    V --> W[Status: D]
    W --> X[Company: From Plant Mapping]
    X --> Y[Final Batch: Example NND24D24DH]
    ```