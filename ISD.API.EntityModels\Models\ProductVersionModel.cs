﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductVersionModel", Schema = "MES")]
    public partial class ProductVersionModel
    {
        [Key]
        public Guid ProductVersionId { get; set; }
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(50)]
        public string WERKS { get; set; }
        [StringLength(50)]
        public string VERID { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BDATU { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ADATU { get; set; }
        [StringLength(50)]
        public string STLAL { get; set; }
        [StringLength(50)]
        public string PLNNR { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? BSTMI { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? BSTMA { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}