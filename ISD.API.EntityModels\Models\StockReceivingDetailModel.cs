﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StockReceivingDetailModel", Schema = "Warehouse")]
    [Index("CustomerReference", "StockRecevingType", Name = "IX_StockReceiving_Covering")]
    public partial class StockReceivingDetailModel
    {
        [Key]
        public Guid StockReceivingDetailId { get; set; }
        public Guid? StockReceivingId { get; set; }
        public Guid? ProductId { get; set; }
        [StringLength(500)]
        public string ProductAttributes { get; set; }
        public Guid? StockId { get; set; }
        public int? DateKey { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitPrice { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
        public Guid? CustomerReference { get; set; }
        [StringLength(10)]
        public string StockRecevingType { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? FromTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ToTime { get; set; }
        public int? Phase { get; set; }
        [StringLength(50)]
        public string MovementType { get; set; }
        public Guid? DepartmentId { get; set; }
        public bool? isDeleted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        public bool? isSendToSAP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SendToSAPTime { get; set; }
        public string SendToSAPError { get; set; }
        public bool? IsWorkCenterCompleted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WorkCenterConfirmTime { get; set; }
        public int? ConfirmDatekey { get; set; }
        [StringLength(50)]
        public string ConfirmWorkCenter { get; set; }
        public Guid? ConfirmBy { get; set; }

        [ForeignKey("CustomerReference")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual ThucThiLenhSanXuatModel CustomerReferenceNavigation { get; set; }
        [ForeignKey("DateKey")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual DimDateModel DateKeyNavigation { get; set; }
        [ForeignKey("DepartmentId")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual DepartmentModel Department { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("StockId")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual StockModel Stock { get; set; }
        [ForeignKey("StockReceivingId")]
        [InverseProperty("StockReceivingDetailModel")]
        public virtual StockReceivingMasterModel StockReceiving { get; set; }
    }
}