import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/getLstQCNVLByFilter.dart';
import '../../urlApi/urlApi.dart';

class GetLstQCNVLByFilterApi {
  static Future<http.Response> postLstQCNVLByFilter(GetLstQCNVLByFilter getLstQCNVLByFilter, String token) async {
    final dataPost = jsonEncode(getLstQCNVLByFilter);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + 'GetLstQCNVLByFilter');
    debugPrint(url.toString());
    // debugPrint("--- Tien test token");
    debugPrint(token.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> postLstQCNVLByFilter2(GetLstQCNVLByFilter getLstQCNVLByFilter, String token) async {
    final dataPost = jsonEncode(getLstQCNVLByFilter);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + 'GetLstQCNVLByFilter2');
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
