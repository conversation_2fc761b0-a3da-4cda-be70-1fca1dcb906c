import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/getDepartentByApi.dart';
import '../../model/postProduction.dart';
import '../../model/productionRecord.dart';
import '../../model/productionRecordHistoryApi.dart';
import '../../model/workShopDepartment.dart';
import '../api/getWorkShopDepartment.dart';
import '../api/productionRecordApi.dart';
import '../api/productionRecordHistoryApi.dart';

class DetailReportFunction {
  static bool checkIsSend = false;
  static ListStepCode defaultListStepCode = ListStepCode(value: " ", text: "--Vui lòng chọn--", orderIndex: " ");
  static String getDateTo(DateTime? dateTo) {
    if (dateTo == null) {
      return DateFormat('dd/MM/yyyy').format(DateTime.now());
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTo);
      // return '${date.month}/${date.day}/${date.year}';
    }
  }

  static String formatDateTo(DateTime? dateTo) {
    DateTime getFormatDateTime = DateFormat("dd/MM/yyyy").parse(getDateTo(dateTo));
    String getFormatDateString = DateFormat("yyyy-MM-dd").format(getFormatDateTime);
    return getFormatDateString;
  }

  static String getDateFrom(DateTime? dateFrom) {
    if (dateFrom == null) {
      return DateFormat('dd/MM/yyyy').format(DateTime.now());
    } else {
      return DateFormat('dd/MM/yyyy').format(dateFrom);
      // return '${date.month}/${date.day}/${date.year}';
    }
  }

  static String formatDateForm(DateTime? dateFrom) {
    DateTime getFormatDateTime = DateFormat("dd/MM/yyyy").parse(getDateFrom(dateFrom));
    String getFormatDateString = DateFormat("yyyy-MM-dd").format(getFormatDateTime);
    return getFormatDateString;
  }

  static String getTimeTo(TimeOfDay? timeTo) {
    if (timeTo == null) {
      return '00:00:00 AM';
    } else {
      String? period = timeTo.period.toString().toUpperCase().split('.')[1];
      String? hours = timeTo.hour.toString().padLeft(2, '0');
      String? minutes = timeTo.minute.toString().padLeft(2, '0');
      return '$hours:$minutes:00 $period';
    }
  }

  static String viewTimeTo(TimeOfDay? timeTo) {
    if (timeTo == null) {
      return '00:00 AM';
    } else {
      String? period = timeTo.period.toString().toUpperCase().split('.')[1];
      String? hours = timeTo.hour.toString().padLeft(2, '0');
      String? minutes = timeTo.minute.toString().padLeft(2, '0');
      return '$hours:$minutes $period';
    }
  }

  static String viewTimeFrom(TimeOfDay? timeFrom) {
    if (timeFrom == null) {
      return '00:00 AM';
    } else {
      String? hours = timeFrom.hour.toString().padLeft(2, '0');
      String? minutes = timeFrom.minute.toString().padLeft(2, '0');
      String? period = timeFrom.period.toString().toUpperCase().split('.')[1];
      return '$hours:$minutes $period';
    }
  }

  static String viewTimeFromIos(DateTime now, TimeOfDay? timeFrom) {
    if (timeFrom == null) {
      return '00:00 AM';
    } else {
      final dt = DateTime(now.year, now.month, now.day, timeFrom.hour, timeFrom.minute);
      return DateFormat.jm().format(dt);
    }
  }

  static String viewTimeToIos(DateTime now, TimeOfDay? timeTo) {
    if (timeTo == null) {
      return '00:00 AM';
    } else {
      final dt = DateTime(now.year, now.month, now.day, timeTo.hour, timeTo.minute);
      return DateFormat.jm().format(dt);
    }
  }

  static String getTimeFrom(TimeOfDay? timeFrom) {
    if (timeFrom == null) {
      return '00:00:00 AM';
    } else {
      String? hours = timeFrom.hour.toString().padLeft(2, '0');
      String? minutes = timeFrom.minute.toString().padLeft(2, '0');
      String? period = timeFrom.period.toString().toUpperCase().split('.')[1];
      return '$hours:$minutes:00 $period';
    }
  }

  static ProductionOrderViewModel getProductionOrderViewModel(bool visible, Data getData, String controller1Text, ListStepCode? selectedStepCode,
      ListDepartment? selectedDepartment, DateTime? dateTo, DateTime? dateFrom) {
    ProductionOrderViewModel productionOrderViewModel = visible != true
        ? ProductionOrderViewModel(
            taskId: getData.productionRecord!.taskId.toString(),
            productId: getData.productionRecord!.productId.toString(),
            productAttributes: getData.productionRecord!.productAttributes.toString(),
            phase: int.parse(controller1Text),
            stepCode: getData.productionRecord!.stepCode ?? selectedStepCode!.value.toString(),
            departmentId: selectedDepartment!.departmentId.toString(),
            workDate: formatDateForm(dateFrom))
        : ProductionOrderViewModel(
            taskId: getData.productionRecord!.taskId.toString(),
            productId: getData.productionRecord!.productId.toString(),
            productAttributes: getData.productionRecord!.productAttributes.toString(),
            phase: int.parse(controller1Text),
            stepCode: getData.productionRecord!.stepCode ?? selectedStepCode!.value.toString(),
            departmentId: selectedDepartment!.departmentId.toString(),
            fromDate: formatDateForm(dateFrom),
            toDate: formatDateTo(dateTo),
            workDate: formatDateForm(dateFrom));
    return productionOrderViewModel;
  }

  static List<ProductionOrderDetailOld> getProductionOrderDetailOld(
      DateTime? dateFrom, TimeOfDay? timeFrom, TimeOfDay? timeTo, DateTime? dateTo, String controller2Text, String controller3Text, bool visible) {
    List<ProductionOrderDetailOld> lsGetProductionOrderDetailOld = [];
    ProductionOrderDetailOld productionOrderDetailOld = ProductionOrderDetailOld(
        fromTime: "${formatDateForm(dateFrom)} ${getTimeFrom(timeFrom)}",
        toTime: visible == true ? "${formatDateTo(dateTo)} ${getTimeTo(timeTo)}" : "${formatDateTo(dateFrom)} ${getTimeTo(timeTo)}",
        quantityD: double.parse(controller2Text),
        quantityKD: double.parse(controller3Text));
    lsGetProductionOrderDetailOld.add(productionOrderDetailOld);
    return lsGetProductionOrderDetailOld;
  }

  static List<UsageQuantityViewModels> getUsageQuantityViewModels(Data getData, List<TextEditingController> lsGetTextEditingControllerListView) {
    List<UsageQuantityViewModels> lsGetUsageQuantityViewModels = [];
    if (getData.productionRecord!.stepCode != null) {
      for (int i = 0; i < getData.usageQuantity!.length; i++) {
        final item = getData.usageQuantity![i];
        final usageQuantityViewModels = UsageQuantityViewModels(
            productAttributes: item.productAttributes.toString(),
            itmno: item.itmno.toString(),
            bmschdc: int.parse(lsGetTextEditingControllerListView[i].text));
        lsGetUsageQuantityViewModels.add(usageQuantityViewModels);
      }
    }
    return lsGetUsageQuantityViewModels;
  }

  static Future<void> postProductionRecord(
      BuildContext context,
      Data getData,
      String token,
      ListStepCode? selectedStepCode,
      bool visible,
      String controller1Text,
      ListDepartment? selectedDepartment,
      DateTime? dateTo,
      DateTime? dateFrom,
      TimeOfDay? timeFrom,
      TimeOfDay? timeTo,
      String controller2Text,
      String controller3Text,
      List<TextEditingController> lsGetTextEditingControllerListView) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await ProductionRecordApi.postProduction(
          getProductionOrderViewModel(visible, getData, controller1Text, selectedStepCode, selectedDepartment, dateTo, dateFrom),
          getProductionOrderDetailOld(dateFrom, timeFrom, timeTo, dateTo, controller2Text, controller3Text, visible),
          getUsageQuantityViewModels(getData, lsGetTextEditingControllerListView),
          getData,
          token,
          selectedStepCode);
      Navigator.pop(context);
      checkIsSend = true;
      final check = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (check['code'] == 201 && check['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                check['data'].toString(),
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 3)));
          // FocusManager.instance.primaryFocus?.unfocus();
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context, true);
          });
        } else {
          // FocusManager.instance.primaryFocus?.unfocus();
          showDialog(
              context: context, barrierDismissible: false, builder: (BuildContext context) => DiaLogErrorValidate(message: check['data'].toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     'Lưu thất bại, vui lòng thử lại',
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          //     duration: const Duration(seconds: 1)
          // ));
        }
      } else {
        // FocusManager.instance.primaryFocus?.unfocus();
        if (check['code'] == 304 && check['success'] == false) {
          showDialog(
              context: context, barrierDismissible: false, builder: (BuildContext context) => DiaLogErrorValidate(message: check['data'].toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     check['data'].toString(),
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          //     duration: const Duration(seconds: 1)
          // ));
        } else {
          showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     'Lưu thất bại, vui lòng thử lại',
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          //     duration: const Duration(seconds: 1)
          // ));
        }
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  static String getCreateTime(Data getData) {
    DateTime stringToDateTime = DateFormat("yyyy-MM-ddThh:mm").parse(getData.productionRecord!.createTime.toString());
    String dateToString = DateFormat("dd/MM/yyyy hh:mm").format(stringToDateTime);
    return dateToString;
  }

  static List<ListDetail> orderASCListDetail(Data getData) {
    List<ListDetail> lsGetOrderASCLisDetail = [];
    if (getData.productionRecord!.listDetail!.isNotEmpty) {
      lsGetOrderASCLisDetail = getData.productionRecord!.listDetail!;
      lsGetOrderASCLisDetail.sort((a, b) {
        final stringToTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(a.toTime.toString());
        final stringToTime_2 = DateFormat("yyyy-MM-ddThh:mm:ss").parse(b.toTime.toString());
        return stringToTime.compareTo(stringToTime_2);
      });
      return lsGetOrderASCLisDetail;
    } else {
      return [];
    }
  }

  static List<ListStepCode>? getListStepCode(Data getData) {
    List<ListStepCode> lsStepCode = [];
    if (getData.productionRecord!.stepCode == null) {
      lsStepCode.insert(0, (defaultListStepCode));
      for (var i in getData.listStepCode!) {
        lsStepCode.add(i);
      }
    }
    return lsStepCode;
  }

  static Future<List<ProductionRecordHistoryApi>?> postProductionRecordHistoryApi(
      String ttlsx, String fromTime, String toTime, String itmno, String stepCode, String token) async {
    final response = await ProductionRecordHistoryFetchApi.getProductionRecordHistory(ttlsx, fromTime, toTime, itmno, stepCode, token);
    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final getProductionRecordHistoryApi = List<ProductionRecordHistoryApi>.from(responseData.map((i) => ProductionRecordHistoryApi.fromJson(i)));
      return getProductionRecordHistoryApi;
    } else {
      return null;
    }
  }

  static Future<GetDepartentByApi?> fetchDepartentByApi(
      String ttlsx, String fromTime, String toTime, String itmno, String stepCode, String token) async {
    final response = await ProductionRecordHistoryFetchApi.getGetDepartentBy(ttlsx, fromTime, toTime, itmno, stepCode, token);
    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      final getDepartentByApi = GetDepartentByApi.fromJson(responseData);
      if (getDepartentByApi.code == 200 && getDepartentByApi.isSuccess == true) {
        return getDepartentByApi;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<DataWorkShop?> getWorkShopApi(String token) async {
    final response = await GetWorkShopDepartmentApi.getWorkShop(token);
    if (response.statusCode == 200) {
      final responseDataWorkShopDepartment = jsonDecode(response.body);
      final workShopDepartment = WorkShopDepartment.fromJson(responseDataWorkShopDepartment);
      if (workShopDepartment.isSuccess != false) {
        final workShopData = workShopDepartment.dataWorkShop;
        return workShopData;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  // static List<bool> getListFocus(List<FocusNode> lsFocusNode){
  //   List<bool> lsGetFocus = [];
  //   for(var i in lsFocusNode){
  //     if(i.hasFocus){
  //       lsGetFocus.add(i.hasFocus);
  //     }
  //   }
  //     return lsGetFocus;
  // }
}
