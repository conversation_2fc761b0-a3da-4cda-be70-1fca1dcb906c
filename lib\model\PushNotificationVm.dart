import 'dart:convert';

class PushNotificationVm {
  String? id;
  String? title;
  String? message;
  String? url;
  String? notificationType;
  String? targetUser;
  String? externalId;
  DateTime? sentAt;
  int? status;
  String? createdBy;
  DateTime? createTime;
  DateTime? updatedAt;
  bool? isForwarded;
  String? forwardedUsers;

  PushNotificationVm({
    this.id,
    this.title,
    this.message,
    this.url,
    this.notificationType,
    this.targetUser,
    this.externalId,
    this.sentAt,
    this.status,
    this.createdBy,
    this.createTime,
    this.updatedAt,
    this.isForwarded,
    this.forwardedUsers,
  });

  factory PushNotificationVm.fromJson(Map<String, dynamic> json) {
    return PushNotificationVm(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      url: json['url'],
      notificationType: json['notificationType'],
      targetUser: json['targetUser'],
      externalId: json['externalId'],
      sentAt: json['sentAt'] != null ? DateTime.parse(json['sentAt']) : null,
      status: json['status'],
      createdBy: json['createdBy'],
      createTime: json['createTime'] != null ? DateTime.parse(json['createTime']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      isForwarded: json['isForwarded'],
      forwardedUsers: json['forwardedUsers'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Id': id,
      'Title': title,
      'Message': message,
      'Url': url,
      'NotificationType': notificationType,
      'TargetUser': targetUser,
      'ExternalId': externalId,
      'SentAt': sentAt?.toIso8601String(),
      'Status': status,
      'CreatedBy': createdBy,
      'CreateTime': createTime?.toIso8601String(),
      'UpdatedAt': updatedAt?.toIso8601String(),
      'IsForwarded': isForwarded,
      'ForwardedUsers': forwardedUsers,
    };
  }
}
