﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProductHierarchyModel", Schema = "ghMasterData")]
    public partial class ProductHierarchyModel
    {
        public ProductHierarchyModel()
        {
            MaterialModel = new HashSet<MaterialModel>();
        }

        [Key]
        [StringLength(50)]
        public string ProductHierarchyCode { get; set; }
        [StringLength(400)]
        public string ProductHierarchyName { get; set; }
        public int? LevelNo { get; set; }
        [StringLength(50)]
        public string ProfitCenterCode { get; set; }
        [StringLength(400)]
        public string ImageUrl { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("ProductHierarchyCodeNavigation")]
        public virtual ICollection<MaterialModel> MaterialModel { get; set; }
    }
}