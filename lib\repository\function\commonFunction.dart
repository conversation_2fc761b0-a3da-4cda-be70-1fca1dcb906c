import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../model/downtimeModel.dart';
import '../../urlApi/urlApi.dart';
import '../../Storage/storageSecureStorage.dart';

class EmployeeResponse {
  final List<EmployeeRecord>? employees;
  final bool status;
  final String message;

  EmployeeResponse({
    this.employees,
    this.status = true,
    this.message = 'Success',
  });
}

class CommonFunction {
  static const String _baseEndpoint = '/api/v1/MES/';
  static const String _commonEndpoint = '${_baseEndpoint}Common/';
  static const String _employeeEndpoint = '${_commonEndpoint}GetEmployees';

  static Future<String> _getBaseUrl() async {
    final environment = await SecureStorage.getString("environment", null);
    return environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
  }

  /// Fetches employee data from the API
  /// Returns a list of EmployeeRecord objects and status information
  static Future<EmployeeResponse> fetchEmployees(String token, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = Uri.parse('$baseUrl$_employeeEndpoint?companyCode=$companyCode');

      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      debugPrint('Response Status: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');
      debugPrint('Request URL: ${response.request?.url}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return EmployeeResponse(
            employees: (jsonResponse['data'] as List?)
                ?.map((item) => EmployeeRecord(
                      employeeId: item['employeeCode'],
                      employeeName: item['employeeName'],
                    ))
                .toList(),
            status: jsonResponse['isSuccess'] ?? true,
            message: jsonResponse['message'] ?? 'Success');
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return EmployeeResponse(employees: null, status: false, message: 'Failed to fetch employees');
    } catch (e) {
      debugPrint('CommonFunction.fetchEmployees | error: $e');
      return EmployeeResponse(employees: null, status: false, message: e.toString());
    }
  }
}
