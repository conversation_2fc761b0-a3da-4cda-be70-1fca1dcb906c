import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postQuantityByP0.dart';
import '../../model/receiveIntegrationSAP.dart';
import '../../urlApi/urlApi.dart';

class ImportWareHouseApi {
  static Future<http.Response> getRawMaterialCard(String rawMaterialCardId, String token, String fromPage) async {
    Map<String, dynamic> data = {};
    if (fromPage == "qr") {
      debugPrint(rawMaterialCardId);
      data = {"RawMaterialCardId": rawMaterialCardId};
    } else {
      debugPrint(rawMaterialCardId);
      data = {"RawMaterialCardCode": rawMaterialCardId};
    }

    final key = data.keys.first;
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlPurchaseOrder}GetBarcode?$key=${data[key]}';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }

  static Future<http.Response> getListSoToKhai(String soToKhai, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlPurchaseOrder}GetSoToKhai?soToKhai=$soToKhai';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }

  static Future<http.Response> getSlocAddress(String plant, String token) async {
    Map<String, dynamic> data = {"Plant": plant};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlCommon}GetSloc?Plant=${data['Plant']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityImported(String rawMaterialCardId, String token) async {
    Map<String, dynamic> data = {"RawMaterialCardId": rawMaterialCardId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetQuantityImported", data);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityByPO(List<PostQuantityByPO> lsPostQuantityByP0, String token) async {
    final dataPost = jsonEncode(lsPostQuantityByP0);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "GetQuantityByPO");
    if (kDebugMode) {
      print(url);
    }
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> receiveIntegrationSAP(ReceiveIntegrationSAP receiveIntegrationSAP, String token, bool viTab) async {
    Map<String, dynamic> data = {
      "rawMaterialCardId": receiveIntegrationSAP.rawMaterialCardId,
      "quantityImports": receiveIntegrationSAP.quantityImports,
      "slocId": receiveIntegrationSAP.slocId,
      "storageBinId": receiveIntegrationSAP.storageBinId,
      "batch": receiveIntegrationSAP.batch,
      "tds": receiveIntegrationSAP.tds,
      "soToKhai": receiveIntegrationSAP.soToKhai,
    };

    if (viTab == true) {
      data["autoBatch"] = receiveIntegrationSAP.autoBatch;
    }
    final dataPost = json.encode(data);
    // final dataPost =  jsonEncode(receiveIntegrationSAP);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "ReceiveIntegrationSAP");
    // print(url);
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    if (kDebugMode) {
      print(response.body);
    }
    return response;
  }

  static Future<http.Response> importFinishedProduct(Map<String, dynamic> importData, String token) async {
    final dataPost = jsonEncode(importData);
    if (kDebugMode) {
      print(dataPost);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/ImportProduct/ImportFinishedProduct';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }

  static Future<http.Response> getPOInfoBySerial(String serial, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/ImportProduct/GetPOInfoBySerial/$serial';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
      print(response.body);
    }

    return response;
  }
}
