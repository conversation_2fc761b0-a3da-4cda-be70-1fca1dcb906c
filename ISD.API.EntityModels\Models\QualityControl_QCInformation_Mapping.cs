﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("QualityControl_QCInformation_Mapping", Schema = "MES")]
    public partial class QualityControl_QCInformation_Mapping
    {
        [Key]
        public Guid QualityControl_QCInformation_Id { get; set; }
        public Guid? QualityControlId { get; set; }
        public Guid? QualityControlInformationId { get; set; }
        [StringLength(50)]
        public string WorkCenterCode { get; set; }
        public string Notes { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? SoSanPhamLoi { get; set; }
        public Guid? QualityControlDetailId { get; set; }
        [StringLength(50)]
        public string OutcomeStatus { get; set; }
        public int? OrderIndex { get; set; }
    }
}