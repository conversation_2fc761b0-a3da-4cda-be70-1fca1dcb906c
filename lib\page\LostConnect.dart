import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LostConnect extends StatelessWidget {
  final VoidCallback checkConnect;
  const LostConnect({Key? key, required this.checkConnect}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(Icons.signal_wifi_off_rounded, size: 45.sp),
          Text(
            "Ứng dụng cần kết nối mạng",
            style: TextStyle(fontSize: 13.sp),
          ),
          SizedBox(
            height: 20.h,
          ),
          ElevatedButton.icon(
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsets>(
                  EdgeInsets.symmetric(vertical: 5.h, horizontal: 30.w)),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.r),
                      side: const BorderSide(color: Colors.white))),
              side: MaterialStateProperty.all(
                const BorderSide(
                  color: Color(0xff000000),
                ),
              ),
              backgroundColor:
              MaterialStateProperty.all(const Color(0xff000000)),
            ),
            onPressed: checkConnect,
            icon: Icon(Icons.refresh_rounded, size: 17.sp),
            label: Text(
              "Tải lại",
              style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 15.sp),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

