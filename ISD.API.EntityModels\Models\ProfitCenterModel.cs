﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ProfitCenterModel", Schema = "ghMasterData")]
    public partial class ProfitCenterModel
    {
        public ProfitCenterModel()
        {
            MaterialModel = new HashSet<MaterialModel>();
        }

        [Key]
        [StringLength(50)]
        public string ProfitCenterCode { get; set; }
        [StringLength(400)]
        public string ProfitCenterName { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("ProfitCenterCodeNavigation")]
        public virtual ICollection<MaterialModel> MaterialModel { get; set; }
    }
}