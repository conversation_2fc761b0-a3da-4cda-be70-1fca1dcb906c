import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ImageQuatity extends StatefulWidget {
  final PageController pageController;
  final List<File> lsImage;
  final int index;

  ImageQuatity({Key? key, required this.lsImage, required this.index}) : pageController = PageController(initialPage:  index), super(key: key);

  @override
  _ImageQuatityWidgetState createState() => _ImageQuatityWidgetState();
}

class _ImageQuatityWidgetState extends State<ImageQuatity> {
  late int _index;
  @override
  void initState() {
    super.initState();
    _index = widget.index;
  }
  void _setIndex(int index){
    setState(() {
      _index = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        body: _ListImageView(lsImage: widget.lsImage, index: _index, pageController: widget.pageController, setIndex: _setIndex)
    );
  }
}
class _ListImageView extends StatelessWidget {
  final PageController pageController;
  final List<File> lsImage;
  final int index;
  final ValueChanged<int> setIndex;
  const _ListImageView({Key? key, required this.pageController, required this.lsImage, required this.index, required this.setIndex}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Column(
            children: [
              Container(
                color: Colors.black,
                alignment: Alignment.topLeft,
                child:IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    }),
              ),
              Expanded(
                child: PhotoViewGallery.builder(
                  pageController: pageController,
                  itemCount: lsImage.length,
                  builder: (context,index){

                    return PhotoViewGalleryPageOptions(
                        imageProvider: FileImage(lsImage[index]),
                        minScale: PhotoViewComputedScale.contained,
                        maxScale: PhotoViewComputedScale.contained * 4
                    );
                  },
                  onPageChanged: setIndex
                ),
              ),
              Container(
                  alignment: Alignment.bottomCenter,
                  padding: EdgeInsets.symmetric(vertical: 16.h,horizontal: 16.w),
                  child:Text('Hình ${index + 1} / ${lsImage.length}',
                    style: TextStyle(color: Colors.white,fontSize: 15.sp),)
              ),


            ]));
  }
}
