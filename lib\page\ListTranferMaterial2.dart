import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/postFilterWareHouseTranfer.dart';
import '../element/EndDrawerListWarehouseTranfer.dart';
import '../element/errorViewPost.dart';
import '../element/timeOut.dart';
import '../model/drawerFilterTranferMaterial.dart';
import '../model/getStatusReservation.dart';
import '../model/getStatusWarehouseTranfer.dart';
import '../model/listWarehouseTranfer.dart';
import '../model/slocAddresse.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../repository/function/listWarehouseTranferFunction.dart';
import '../screenArguments/screenArgumentAddTranferWareHouse.dart';
import '../screenArguments/screenArgumentDetailReservationImport.dart';
import '../screenArguments/screenArgumentExportWareHouse.dart';
import 'LostConnect.dart';

class ListTranferMaterial2 extends StatefulWidget {
  const ListTranferMaterial2({Key? key, required this.token, required this.plant, required this.dateTimeOld, required this.accountId})
      : super(key: key);
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  @override
  _ListTranferMaterial2State createState() => _ListTranferMaterial2State();
}

class _ListTranferMaterial2State extends State<ListTranferMaterial2> {
  bool viButton = true;
  bool _isLoading = false;

  bool _isNotWifi = false;
  bool _listNoMore = false;

  final DataGetStatusReservation _defaultDataGetStatusReservation = DataGetStatusReservation(key: "all", value: "Tất cả");
  final DataGetStatusWarehouseTranfer _defaultDataGetStatusWarehouseTranfer = DataGetStatusWarehouseTranfer(key: "all", value: "Tất cả");
  final _controllerListView = ScrollController();
  List<DataListWarehouseTranfer> _lsDataWarehouseTranfe = [];
  List<DataGetStatusReservation> _lsDataGetStatusReservation = [];
  List<DataGetStatusWarehouseTranfer> _lsDataGetStatusWarehouseTranfer = [];
  List<DataSlocAddress> _lsDataSlocAddress = [];
  PostFilterWareHouseTranfer? _postFilterWareHouseTranfer;

  ConnectivityResult _result = ConnectivityResult.none;
  int _start = 0;
  int _draw = 0;
  final int _length = 5;

  bool _isLoadingScroll = false;
  late bool _timeOut;
  bool? _disableButton;
  String _error = "";

  @override
  void initState() {
    super.initState();
    _checkFreshApi();
    _controllerListView.addListener(_goScroll);
  }

  Future<void> _checkFreshApi() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
    } else {
      await _getListDataWarehouseTranfe();
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  Future<void> _getListDataWarehouseTranfe() async {
    // debugPrint(DateTime(DateTime.now().year, DateTime.now().month, 1).toIso8601String());
    try {
      setState(() {
        _timeOut = false;
        _isLoading = true;
        _isNotWifi = false;
        _postFilterWareHouseTranfer = PostFilterWareHouseTranfer(widget.plant, null, null, null, "1", null, null,
            DateTime(DateTime.now().year, DateTime.now().month, 1), DateTime(DateTime.now().year, DateTime.now().month + 1, 0), null, null);
      });
      final data = await Future.wait([
        ListWarehouseTranferFunction.fetchListWareHouseTranfer(widget.token, _postFilterWareHouseTranfer!, _start, _draw, _length),
        ListWarehouseTranferFunction.fechStatusReservation(widget.token),
        ListWarehouseTranferFunction.fechStatusWarehouseTranfer(widget.token),
        ImportWareHouseFunction.fetchSlocAddress(widget.plant, widget.token)
      ]);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _start += 5;
        _draw++;
        debugPrint(_start.toString());
        debugPrint(_draw.toString());
        if (data.isNotEmpty) {
          if (data[0] != null) {
            _lsDataWarehouseTranfe = data[0] as List<DataListWarehouseTranfer>;
          } else {
            _lsDataWarehouseTranfe = [];
          }
          if (data[1] != null) {
            _lsDataGetStatusReservation = data[1] as List<DataGetStatusReservation>;
            _lsDataGetStatusReservation.insert(0, (_defaultDataGetStatusReservation));
          } else {
            _lsDataGetStatusReservation = [];
          }

          if (data[2] != null) {
            _lsDataGetStatusWarehouseTranfer = data[2] as List<DataGetStatusWarehouseTranfer>;
            _lsDataGetStatusWarehouseTranfer.insert(0, (_defaultDataGetStatusWarehouseTranfer));
          } else {
            _lsDataGetStatusWarehouseTranfer = [];
          }
          if (data[3] != null) {
            _lsDataSlocAddress = data[3] as List<DataSlocAddress>;
            _lsDataSlocAddress.sort((a, b) {
              return a.sloc!.toLowerCase().compareTo(b.sloc!.toLowerCase());
            });
            _lsDataSlocAddress.insert(0, DataSlocAddress.empty);
          } else {
            _lsDataSlocAddress = [];
          }
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _timeOut = false;
        _error = _error.toString();
      });
    }
  }

  void _goScroll() {
    if (_controllerListView.position.maxScrollExtent == _controllerListView.offset && _isLoadingScroll == false) {
      if (_listNoMore == false) {
        _fetchScrollListView();
      }
    }
  }

  Future<void> _fetchScrollListView() async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = true;
      });
      final data = await ListWarehouseTranferFunction.fetchListWareHouseTranfer(widget.token, _postFilterWareHouseTranfer!, _start, _draw, _length);
      if (!mounted) return;
      if (data != null) {
        setState(() {
          _start += 5;
          _draw++;
          _isLoadingScroll = false;
          if (data.isEmpty) {
            _listNoMore = true;
          }
          _lsDataWarehouseTranfe.addAll(data);
        });
        debugPrint(_lsDataWarehouseTranfe.length.toString());
      } else {
        setState(() {
          _lsDataWarehouseTranfe = [];
          _isLoadingScroll = false;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = false;
        _lsDataWarehouseTranfe = [];
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingScroll = false;
        _lsDataWarehouseTranfe = [];
      });
    }
  }

  Future<void> _refresh() async {
    setState(() {
      _isLoading = true;
    });
    final data =
        await ListWarehouseTranferFunction.fetchListWareHouseTranfer(widget.token, _postFilterWareHouseTranfer!, 0, 0, _lsDataWarehouseTranfe.length);
    if (!mounted) return;
    setState(() {
      _isLoading = false;
    });
    if (data != null) {
      setState(() {
        // _start = 0;
        // _draw = 0;
        _lsDataWarehouseTranfe = data;
      });
      debugPrint(_lsDataWarehouseTranfe.length.toString());
    } else {
      _lsDataWarehouseTranfe = [];
    }
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }
  @override
  void dispose() {
    _controllerListView.removeListener(_goScroll);
    _controllerListView.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
        : Scaffold(
            backgroundColor: _lsDataWarehouseTranfe.isNotEmpty ? Colors.grey.shade200 : Colors.white,
            endDrawer: _isLoading == true || _error.isNotEmpty
                ? null
                : EndDrawerListWarehouseTranfer(
                    lsDataGetStatusReservation: _lsDataGetStatusReservation,
                    lsDataGetStatusWarehouseTranfer: _lsDataGetStatusWarehouseTranfer,
                    plant: widget.plant,
                    token: widget.token,
                    lsDataSlocAddress: _lsDataSlocAddress,
                    onFilterSelected: (DrawerFilterTranferMaterial filter) {
                      setState(() {
                        _lsDataWarehouseTranfe = filter.lsDataWarehouseTranfe ?? [];
                        _postFilterWareHouseTranfer = filter.postFilterWareHouseTranfer!;
                        _draw = filter.draw!;
                        _start = filter.start!;
                        _listNoMore = filter.resetList!;
                      });
                      // if(_lsDataWarehouseTranfe.isNotEmpty) {
                      //   _controllerListView.jumpTo(_controllerListView.position.minScrollExtent);
                      // }
                    },
                    accountId: widget.accountId,
                  ),
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                "Danh sách chuyển kho (nhiều line)",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
              actions: [
                Builder(
                  builder: (BuildContext context) {
                    return IconButton(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: Icon(
                        Icons.search_outlined,
                        size: 19.sp,
                        color: Colors.white,
                      ),
                      onPressed: _isLoading == true || _error.isNotEmpty
                          ? null
                          : () async {
                              await _checkConnectNetwork();
                              if (!mounted) return;
                              if (_result != ConnectivityResult.none) {
                                Scaffold.of(context).openEndDrawer();
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                  backgroundColor: Colors.black,
                                  content: Text(
                                    "Tính năng cần có kết nối internet để sử dụng",
                                    style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                  ),
                                ));
                              }
                            },
                    );
                  },
                ),
              ],
            ),
            floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
            floatingActionButton: _isNotWifi == false && _isLoading == true && _error.isEmpty
                ? null
                : FloatingActionButton(
                    onPressed: () {
                      Navigator.pushNamed(context, "/AddTranferWarehouse",
                              arguments: ScreenArgumentAddTranferWareHouse(widget.token, widget.plant, _lsDataSlocAddress, widget.dateTimeOld))
                          .then((value) {
                        if (value == null) return;
                        if (value == true) {
                          _refresh();
                        }
                      }).onError((error, stackTrace) {
                        debugPrint(error.toString());
                      });
                    },
                    backgroundColor: const Color(0xff4CAF50),
                    child: const Icon(Icons.add, color: Colors.white),
                  ),
            body: _error.isNotEmpty
                ? ErrorViewPost(error: _error)
                : _isNotWifi == true
                    ? LostConnect(checkConnect: () => _checkFreshApi())
                    : _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : _lsDataWarehouseTranfe.isNotEmpty
                            ? _ListTranferMaterialView(
                                lsDataWarehouseTranfe: _lsDataWarehouseTranfe,
                                isLoadingScroll: _isLoadingScroll,
                                controllerListView: _controllerListView,
                                token: widget.token,
                                plant: widget.plant,
                                lsDataSlocAddress: _lsDataSlocAddress,
                                dateTimeOld: widget.dateTimeOld,
                                refresh: _refresh)
                            : const _ViewListNull());
  }
}

class _ListTranferMaterialView extends StatelessWidget {
  final List<DataListWarehouseTranfer> lsDataWarehouseTranfe;
  final bool isLoadingScroll;
  final ScrollController controllerListView;
  final String token;
  final String plant;
  final List<DataSlocAddress> lsDataSlocAddress;
  final String dateTimeOld;
  final VoidCallback refresh;
  const _ListTranferMaterialView(
      {Key? key,
      required this.lsDataWarehouseTranfe,
      required this.isLoadingScroll,
      required this.controllerListView,
      required this.token,
      required this.plant,
      required this.lsDataSlocAddress,
      required this.dateTimeOld,
      required this.refresh})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> dummyReservations = [
      /// ReservationNumber, MaterialReservationModel.ReservationId

      {'reservationNumber': '1697199', 'reservationId': '9fb84c4e-8a49-4785-b827-0105beeacfe5'},
      // {'reservationNumber': '1707138', 'reservationId': '2c416651-267e-46b8-bf03-5f898dd60d22'},
      // {'reservationNumber': '1707806', 'reservationId': 'e69d0fa8-d38a-45a8-8b1b-ef41c554065c'},
      // {'reservationNumber': '1708832', 'reservationId': '048bb59e-ff39-4e91-a1dd-08cc04d0601c'},
      // {'reservationNumber': '1697065', 'reservationId': 'bbe0e6b5-8126-4025-b0a7-7dd72f906c1b'},
      // {'reservationNumber': '1866202', 'reservationId': '3b6a56a3-c3a2-4c96-a758-efe14ad65e4b'},
      {'reservationNumber': '2230231', 'reservationId': 'a2cb25f1-d993-4dae-9f84-30ba49451511'},
    ];

    return Stack(alignment: Alignment.topCenter, children: [
      SingleChildScrollView(
        key: ObjectKey(lsDataWarehouseTranfe[0]),
        physics: isLoadingScroll == true ? const NeverScrollableScrollPhysics() : const ScrollPhysics(),
        controller: controllerListView,
        child: Column(
          children: [
            kDebugMode
                ? Wrap(
                    alignment: WrapAlignment.start,
                    runAlignment: WrapAlignment.start,
                    spacing: 2.w,
                    children: dummyReservations.map((reservation) {
                      return ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
                          minimumSize: const Size(0, 0), // Add this
                        ),
                        onPressed: () {
                          var data = DataListWarehouseTranfer();
                          data.reservationId = reservation['reservationId'];

                          debugPrint(data.reservationId.toString());

                          Navigator.pushNamed(context, "/ExportWarehouse2",
                                  arguments: ScreenArgumentExportWareHouse(data, token, plant, lsDataSlocAddress, dateTimeOld))
                              .then((value) {});
                        },
                        child: Text(reservation['reservationNumber'].toString(), style: TextStyle(fontSize: 10)),
                      );
                    }).toList(),
                  )
                : Container(),
            ListView.separated(
              itemCount: lsDataWarehouseTranfe.length,
              // padding: EdgeInsets.zero,
              padding: const EdgeInsets.only(bottom: 150.0), // <-- Added bottom padding here
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (BuildContext context, int index) => Container(height: 10.h),
              itemBuilder: (BuildContext context, int index) {
                // 1 Line
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                  decoration: const BoxDecoration(color: Colors.white),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 5.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 5,
                            child: lsDataWarehouseTranfe[index].statusReservation == "Đã duyệt"
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Flexible(
                                        flex: 5,
                                        child: Text(
                                          "Ngày yêu cầu: ",
                                          style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Flexible(
                                        flex: 5,
                                        child: Text(
                                          lsDataWarehouseTranfe[index].reqDate ?? "",
                                          style: TextStyle(fontSize: 11.sp),
                                        ),
                                      ),
                                    ],
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Flexible(
                                        flex: 5,
                                        child: Text(
                                          "Nhà máy: ",
                                          style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Flexible(
                                        flex: 5,
                                        child: Text(
                                          lsDataWarehouseTranfe[index].plant != null ? lsDataWarehouseTranfe[index].plant.toString() : "",
                                          style: TextStyle(fontSize: 11.sp),
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                          Expanded(
                            flex: 5,
                            child: Visibility(
                              visible: lsDataWarehouseTranfe[index].statusReservation == "Đã duyệt",
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                      RoundedRectangleBorder(borderRadius: BorderRadius.circular(3.r), side: const BorderSide(color: Colors.white))),
                                  side: MaterialStateProperty.all(
                                    BorderSide(
                                      color: lsDataWarehouseTranfe[index].statusWarehouse == "Chưa chuyển kho "
                                          ? const Color(0xff43A047)
                                          : const Color(0xff0052cc),
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                    lsDataWarehouseTranfe[index].statusWarehouse == "Chưa chuyển kho "
                                        ? const Color(0xff43A047)
                                        : const Color(0xff0052cc),
                                  ),
                                ),
                                onPressed: () async {
                                  if (lsDataWarehouseTranfe[index].statusWarehouse != null) {
                                    if (lsDataWarehouseTranfe[index].movementType == "311" &&
                                        lsDataWarehouseTranfe[index].statusWarehouse == "Chưa chuyển kho ") {
                                      Navigator.pushNamed(context, "/ExportWarehouse2",
                                              arguments: ScreenArgumentExportWareHouse(
                                                  lsDataWarehouseTranfe[index], token, plant, lsDataSlocAddress, dateTimeOld))
                                          .then((value) {
                                        if (value == null) return;
                                        if (value == true) {
                                          refresh();
                                        }
                                      });
                                    }
                                    if (lsDataWarehouseTranfe[index].movementType == "313" &&
                                        lsDataWarehouseTranfe[index].statusWarehouse == "Chưa chuyển kho ") {
                                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                          backgroundColor: Colors.black,
                                          content: Text(
                                            'Tính năng chuyển kho 2 bước chưa phát triển',
                                            style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                          ),
                                          duration: const Duration(seconds: 2)));
                                      //   Navigator.pushNamed(
                                      //       context, "/ExportWarehouseTwoSteps",
                                      //       arguments: ScreenArgumentExportWareHouse(
                                      //           _lsDataWarehouseTranfe[index],
                                      //           widget.token,
                                      //           widget.plant,
                                      //           _lsDataSlocAddress
                                      //       )).then((value) {
                                      //     if(value == null) return;
                                      //     if(value == true){
                                      //       _refresh();
                                      //     }
                                      //   });
                                    }
                                    if (lsDataWarehouseTranfe[index].statusWarehouse == "Đã chuyển kho") {
                                      Navigator.pushNamed(context, "/DetailReservation",
                                          arguments: ScreenArgumentDetailReservationImport(lsDataWarehouseTranfe[index], token, dateTimeOld));
                                    }
                                  }
                                },
                                onLongPress: () {
                                  if (kDebugMode) {
                                    print(lsDataWarehouseTranfe[index]);
                                  }

                                  Navigator.pushNamed(context, "/ExportWarehouse2",
                                          arguments: ScreenArgumentExportWareHouse(
                                              lsDataWarehouseTranfe[index], token, plant, lsDataSlocAddress, dateTimeOld))
                                      .then((value) {
                                    if (value == null) return;
                                    if (value == true) {
                                      refresh();
                                    }
                                  });
                                },
                                child: Container(
                                  // margin: EdgeInsets.symmetric(vertical: 12.h),
                                  child: Text(
                                    lsDataWarehouseTranfe[index].statusWarehouse == "Chưa chuyển kho " ? "Chuyển kho" : "Xem chi tiết",
                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "Số reservation: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].reservationNumber != null
                                        ? lsDataWarehouseTranfe[index].reservationNumber.toString()
                                        : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "Item: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].item != null ? lsDataWarehouseTranfe[index].item.toString() : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            flex: 5,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        "Nhà máy xuất: ",
                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        lsDataWarehouseTranfe[index].plant != null ? lsDataWarehouseTranfe[index].plant.toString() : "",
                                        style: TextStyle(fontSize: 11.sp),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 5.h),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        "Kho xuất: ",
                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        lsDataWarehouseTranfe[index].storageLocation != null
                                            ? lsDataWarehouseTranfe[index].storageLocation.toString()
                                            : "",
                                        style: TextStyle(fontSize: 11.sp),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 5,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        "Nhà máy nhập: ",
                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        lsDataWarehouseTranfe[index].riPlant != null ? lsDataWarehouseTranfe[index].riPlant.toString() : "",
                                        style: TextStyle(fontSize: 11.sp),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 5.h),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        "Kho nhập: ",
                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    Flexible(
                                      flex: 5,
                                      child: Text(
                                        lsDataWarehouseTranfe[index].riStorageLocation != null
                                            ? lsDataWarehouseTranfe[index].riStorageLocation.toString()
                                            : "",
                                        style: TextStyle(fontSize: 11.sp),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "Trạng thái reservation: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].statusReservation != null
                                        ? lsDataWarehouseTranfe[index].statusReservation.toString()
                                        : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "Trạng thái: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].statusWarehouse != null
                                        ? lsDataWarehouseTranfe[index].statusWarehouse.toString()
                                        : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Flexible(
                            flex: 3,
                            child: Text(
                              "Mã NVL |Tên NVL: ",
                              style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                          Flexible(
                            flex: 7,
                            child: Text(
                              lsDataWarehouseTranfe[index].productCodeAndName != null
                                  ? lsDataWarehouseTranfe[index].productCodeAndName.toString()
                                  : "",
                              style: TextStyle(fontSize: 11.sp),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "Số lượng: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].quantity != null ? lsDataWarehouseTranfe[index].quantity!.toString() : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 5,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    "ĐVT: ",
                                    style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Text(
                                    lsDataWarehouseTranfe[index].unit != null ? lsDataWarehouseTranfe[index].unit.toString() : "",
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "SO: ",
                                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  lsDataWarehouseTranfe[index].so != null ? lsDataWarehouseTranfe[index].so.toString() : "",
                                  style: TextStyle(fontSize: 11.sp),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "WBS: ",
                                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  lsDataWarehouseTranfe[index].wbs != null ? lsDataWarehouseTranfe[index].wbs.toString() : "",
                                  style: TextStyle(fontSize: 11.sp),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "LSX: ",
                                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  lsDataWarehouseTranfe[index].lsx != null ? lsDataWarehouseTranfe[index].lsx.toString() : "",
                                  style: TextStyle(fontSize: 11.sp),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
      Visibility(
        visible: isLoadingScroll,
        child: Align(
          alignment: Alignment.center,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 3,
                  blurRadius: 20,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: CircleAvatar(
                backgroundColor: Colors.white,
                child: Padding(
                  padding: REdgeInsets.all(6),
                  child: const CircularProgressIndicator(),
                )),
          ),
        ),
      ),
    ]);
  }
}

class _ViewListNull extends StatelessWidget {
  const _ViewListNull({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            "Không tìm thấy phiếu chuyển kho nào",
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}
// class _ErrorView extends StatelessWidget {
//   final String message;
//   const _ErrorView({Key? key, required this.message}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Center(
//         child: SingleChildScrollView(
//         child:Column(
//           children: [
//             Text('Có lỗi xảy ra! vui lòng thử lại sau',
//                 style: TextStyle(fontSize: 15.sp, color: Colors.black)),
//             SizedBox(height: 10.h),
//             Text(message,
//                 style: TextStyle(fontSize: 15.sp, color: Colors.black))
//           ],
//         )));
//   }
// }
