import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import '../../Widget/dialogWidget/DialogErrorQuality.dart';
import '../../Widget/dialogWidget/DialogImage.dart';
import '../../Widget/dialogWidget/DialogQualityInformation.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../../element/ButtonAddNewCardErrorQuatity.dart';
import '../../element/DropdownQualityView.dart';
import '../../element/ImageQuatity.dart';
import '../../element/ListChoseFileQuality.dart';
import '../../element/QualityErrorValidate.dart';
import '../../element/QualityTitle.dart';
import '../../element/TitleQuality.dart';
import '../../element/listImagePicker.dart';
import '../../element/timeOut.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/QuantityInformationSelectedInfor.dart';
import '../../model/dropdownDefectLevel.dart';
import '../../model/mulitListImageFile.dart';
import '../../model/multiSelectedErrorQuality.dart';
import '../../model/qualityControlApi.dart';
import '../../model/rawMaterialCard.dart';
import '../../model/typeAheadErrorQuatity.dart';
import '../../model/userModel.dart';
import '../../repository/function/imageFunction.dart';
import '../../repository/function/importWareHouseFunction.dart';
import '../../repository/function/qualityControlDetailFunction.dart';
import '../../repository/function/qualityControlErrorFunction.dart';
import '../../repository/function/qualityControlFunction.dart';
import '../../repository/function/qualityControlInformationFunciton.dart';
import '../../utils/ui_helpers.dart';
import '../BottomNavigatorBarComponent.dart';
import '../LostConnect.dart';

// Kiểm tra chất lượng
class BaoCaoDauVaoDetail2 extends StatefulWidget {
  final String qualityControlId;
  final String dateTimeOld;
  final String qrCode;
  final String fromPage;
  final DataUser user;
  const BaoCaoDauVaoDetail2(
      {Key? key, required this.qualityControlId, required this.dateTimeOld, required this.qrCode, required this.fromPage, required this.user})
      : super(key: key);

  @override
  State<BaoCaoDauVaoDetail2> createState() => _BaoCaoDauVaoDetail2State();
}

class _BaoCaoDauVaoDetail2State extends State<BaoCaoDauVaoDetail2> with WidgetsBindingObserver {
  // Optional variables
  QualityControlDetail? _qualityControlDetail;
  QualityControlModel? _qualityControlModel;
  QualityControl? _qualityControl;
  DataRawMeterial? _dataRawMaterial;
  TestMethodList? _selectedTestMethod;

  DropdownItemList? _selectedLimitCritical;
  DropdownItemList? _selectedLimitHigh;
  DropdownItemList? _selectedLimitLow;

  SamplingLevelList? _selectedLevel; // Mức độ lấy mẫu KT
  // ResultList? _selectedResult;
  ResultList? _selectedResultDetail;
  List<ThongTinKiemTra>? _lsQualityControlInformationIdList;
  List<ErrorList>? _lsErrorList;

  // Lists with known types
  List<QualityTypeList> _qualityTypeList = [];
  List<TestMethodList> _lsTestMethodList = [];

  List<DropdownItemList> _lsLimitCriticalList = [];
  List<DropdownItemList> _lsLimitHighList = [];
  List<DropdownItemList> _lsLimitLowList = [];

  List<ResultList> _lsResultList = [];
  List<SamplingLevelList> _lsSamplingLevelList = [];
  List<Error> _lsError = [];
  List<QualityControlInformation> _lsQualityControlInformation = [];
  List<DataGetDefectLevel?> _lselectDataGetDefectLevel = [];
  List<DataGetDefectLevel> _lsDataGetDefetchLevel = []; // NANG, NHE, NGHIEMTRONG

  // Lists with controller types
  TextEditingController _controllerPONumber = TextEditingController();
  TextEditingController _controllerHideDetailLever = TextEditingController();
  TextEditingController _controllerMucChapNhan = TextEditingController();

  TextEditingController _controllerInspectLotQuantity = TextEditingController();
  TextEditingController _controllerInspectQuantityDetail = TextEditingController();

  TextEditingController _controllerTongSoSanPhamLoi = TextEditingController();

  List<TextEditingController> _lsControllerError = [];
  List<TextEditingController> _lsControllerInformation = [];
  List<TextEditingController> _lsSoSanPhamLoiController = [];
  List<TextEditingController> _lsGhiChuController = [];
  List<TextEditingController> _lsTextEditingControllerError_1 = [];
  List<TextEditingController> _lsTextEditingControllerError_3 = [];

  // Lists with mixed types
  List<ThongTinKiemTra?> _lsSelectedInfo = [];
  List<ErrorList?> _lsSelectedError = [];
  List<List<File>> _lsGetFileInfor = [];
  List<List<File>> _lsGetFileError = [];
  List<File> _lsFileTabCheck = [];

// List with boolean types
  List<bool> _lsErrorInfor = [];
  List<bool> _checkVisiButtonInformation = [];
  List<bool> _checkVisiButtonError = [];

// List with int types
  List<int> _lsGetIndexInfo = [];
  List<int> _lsGetIndexError = [];

// Individual variables
  ImagePicker _pickerImage = ImagePicker();
  late int _indexError;
  late int _indexInfo;
  late bool _timeOut;

// Boolean variables
  bool _disableButtonTimeOut = false;
  bool _isNotWifi = false;
  bool _isLoading = false;
  bool _isError = false;
  bool _hideDetailLever = false;
  bool _errorSelectType = false;
  bool _errorP0 = false;
  bool _errorQuantityCheck = false;
  bool _errorSelectedResultQualityView = false;
  bool _errorTestMethodDetail = false;

  bool _errorLimitCriticalDetail = false;
  bool _errorLimitHighDetail = false;
  bool _errorLimitLowDetail = false;

  bool _errorLevelDetail = false;
  bool _errorAcceptableLevelDetail = false;
  bool _errorQuantityCheckDetail = false;
  bool _errorResultCheckDetail = false;
  bool _loadingGetQuanititySample = false;

  double _bottomPadding = 0;

  bool checkQualityControl() {
    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      return _qualityControl!.qualityChecker == null;
    }
    return true;
  }

  bool isNVLDoneQC() {
    return _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null;
  }

  DateTime? _qualityDate;
  String _qualityDateStr = " ";
  QualityCheckerInfo? _qualityChckerInfo;
  QualityTypeList? _selectedType;

  String title = "Báo cáo nghiệm thu đầu vào 2";

  String _loaiDauVao = "NVL";

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance!.addObserver(this);

    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    } else {
      setState(() {
        _timeOut = false;
      });
      _init();
      _loadDataAndSetDefault();
    }
  }

  Future<void> _init() async {
    setState(() {
      _controllerTongSoSanPhamLoi.text = "0";
    });
  }

  TextEditingController _controllerSoLuongNhapKho = TextEditingController();
  TextEditingController _controllerSoLuongBlock = TextEditingController();
  TextEditingController _controllerSoLuongTraVe = TextEditingController();

  bool _errorSoLuongNhapKho = false;
  bool _errorSoLuongBlock = false;
  bool _errorSoLuongTraVe = false;

  // void _resetState(){
  //   setState(() {});
  // }
  Future<void> _getGetQuanititySample(BuildContext context) async {
    setState(() {
      _loadingGetQuanititySample = true;
    });

    if (_selectedLevel != null) {
      final data = await QualityControlFunction.fetchGetQuantitySample(
        _selectedLevel!.catalogCode.toString(),
        // _controllerInspectLotQuantity.text,
        _dataRawMaterial!.quantity!.round().toString(),
        widget.user.token.toString(),
        context,
        mounted,
      );

      if (!mounted) return;
      setState(() {
        _loadingGetQuanititySample = false;
        _controllerInspectQuantityDetail.text = data == null ? "" : data.quantitySample.toString();
        _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isNotEmpty ? false : true;
      });
      FocusManager.instance.primaryFocus?.unfocus();
    } else {
      // Handle the case where _selectedLevel is null
    }
  }

  void _checkValidate() {
    setState(() {
      // _errorSelectType = _selectedType == null || _selectedType!.catalogCode == " ";
      _errorP0 = _qualityControl!.qcType != "NVL" && _controllerPONumber.text.isEmpty;
      // _errorQuantityCheck = _controllerInspectLotQuantity.text.isEmpty;
      // _errorSelectedResultQualityView = _selectedResult == null || _selectedResult!.catalogCode == " ";
      _errorTestMethodDetail = _selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ";
      _errorLevelDetail = _selectedLevel == null || _selectedLevel!.catalogCode == " ";
      // _errorAcceptableLevelDetail = _controllerMucChapNhan.text.isEmpty;
      _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isEmpty;
      _errorResultCheckDetail = _selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ";

      debugPrint(_errorAcceptableLevelDetail.toString());

      for (int i = 0; i < _lsSelectedInfo.length; i++) {
        _lsErrorInfor[i] = _lsSelectedInfo[i] == null;
      }
    });
  }

  Future<void> _loadDataAndSetDefault() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      _timeOut = !convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld);
      debugPrint(_timeOut.toString());

      if (!_timeOut) {
        if (!mounted) return;
        // 1. Get QC first
        // If QC have value then set value for QC
        // If QC NOT have value then, set default
        await _getQuantityControl();

        if (!mounted) return;

        // View QC
        if (_qualityControl != null) {
          _qualityControl!.qcType == "NVL" ? _getMaterial() : updateValues();
        } else {
          if (!mounted) return;

          // New QC
          setState(() {
            setDefaultOnInit();
            _isLoading = false;
          });
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _isError = true;
        _timeOut = false;
      });
    }
  }

  void updateValues() {
    setState(() {
      _isLoading = false;
      _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
      _qualityChckerInfo = createSelectedStaff();
      updateSelectedType();
      updateController1Text();
      // updateController2Text();
      // updateSelectedResult();
      updateQualityControlDetail();
      setupQualityControlInformation();
      setupError();
    });
  }

  void setDefaultOnInit() {
    _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, QualityControlDetailFunction.defaultValueLimitCriticalList.catalogCode);
  }

  QualityCheckerInfo createSelectedStaff() {
// this is the format qcSaleEmployee: "employeeCode | employeeFullname"
    // get employeeCode only and get employeeFullname only

    var employeeCode = "";
    var employeeFullname = "";

    if (_qualityControl!.qualityChecker != null) {
      employeeCode = _qualityControl!.qcSaleEmployee!.split(" | ")[0];
      employeeFullname = _qualityControl!.qcSaleEmployee!.split(" | ")[1];
    }

    var qualityCheckerList = QualityCheckerInfo(
      accountId: _qualityControl!.qualityChecker ?? widget.user.accountId.toString(),
      salesEmployeeCode: _qualityControl!.qualityChecker != null ? employeeCode : widget.user.employeeCode,
      salesEmployeeName: _qualityControl!.qualityChecker != null ? employeeFullname : widget.user.fullName,
    );
    return qualityCheckerList;
  }

  void updateSelectedType() {
    if (_dataRawMaterial!.poDetailResponses!.isNotEmpty) {
      bool isPoStartsWith49 = _dataRawMaterial!.poDetailResponses![0].po!.startsWith("49");

      if (isPoStartsWith49) {
        int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == "GIACONG");
        _selectedType = _qualityTypeList[indexType];
        _loaiDauVao = "BTP gia công";
      } else {
        int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == "DAUVAO");
        _selectedType = _qualityTypeList[indexType];
        _loaiDauVao = "NVL";
      }
    }
  }

  void updateController1Text() {
    if (_qualityControl!.po != null) {
      _controllerPONumber.text = _qualityControl!.po.toString();
    }
  }

  // void updateController2Text() {
  //   if (_qualityControl!.inspectionLotQuantity != null) {
  //     _controllerInspectLotQuantity.text = (_qualityControl!.inspectionLotQuantity!.round()).toString();
  //   }
  // }

  // void updateSelectedResult() {
  //   if (_qualityControl!.result != null) {
  //     int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.result);
  //     _selectedResult = _lsResultList[indexResult];
  //   }
  // }

  void updateQualityControlDetail() {
    if (_qualityControl!.qualityControlDetail != null) {
      updateSelectedMethod();
      updateLimitMethod();
      updateSelectedLevel();
      updateController3Text();
      updateController4Text();

      updateTongSoSanPhamLoi();

      updateSelectedResultDetail();
    }
  }

  void updateSelectedMethod() {
    if (_qualityControl!.qualityControlDetail!.testMethod != null) {
      int indexMethod = _lsTestMethodList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.testMethod);
      _selectedTestMethod = _lsTestMethodList[indexMethod];
    } else {
      _selectedTestMethod = null;
    }
  }

  void updateLimitMethod() {
    if (_qualityControl != null && _qualityControl!.qualityControlDetail != null) {
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, _qualityControl!.qualityControlDetail!.limitCritical);

      _selectedLimitHigh = getSelectedLimit(_lsLimitHighList, _qualityControl!.qualityControlDetail!.limitHigh);
      _selectedLimitLow = getSelectedLimit(_lsLimitLowList, _qualityControl!.qualityControlDetail!.limitLow);
    } else {
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, QualityControlDetailFunction.defaultValueLimitCriticalList.catalogCode);
    }
  }

  DropdownItemList? getSelectedLimit(List<DropdownItemList> list, String? limit) {
    if (limit != null) {
      int index = list.indexWhere((element) => element.catalogCode == limit);
      return (index != -1) ? list[index] : null;
    } else {
      return null;
    }
  }

  void updateSelectedLevel() {
    if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel != "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
    } else if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel == "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
      _controllerHideDetailLever.text = _qualityControl!.qualityControlDetail!.samplingLevelName ?? "";
      _hideDetailLever = true;
    } else {
      _selectedLevel = null;
    }
  }

  void updateController3Text() {
    if (_qualityControl!.qualityControlDetail!.acceptableLevel != null) {
      _controllerMucChapNhan.text = _qualityControl!.qualityControlDetail!.acceptableLevel.toString();
    } else {
      _controllerMucChapNhan.text = _controllerMucChapNhan.text;
    }
  }

  void updateController4Text() {
    if (_qualityControl!.qualityControlDetail!.inspectionQuantity != null) {
      _controllerInspectQuantityDetail.text = (_qualityControl!.qualityControlDetail!.inspectionQuantity!.round()).toString();
    } else {
      _controllerInspectQuantityDetail.text = _controllerInspectQuantityDetail.text;
    }
  }

  void updateTongSoSanPhamLoi() {
    if (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi != null) {
      _controllerTongSoSanPhamLoi.text = (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi!.round()).toString();
    } else {
      _controllerTongSoSanPhamLoi.text = _controllerTongSoSanPhamLoi.text;
    }
  }

  void updateSelectedResultDetail() {
    if (_qualityControl!.qualityControlDetail!.result != null) {
      int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.result);
      _selectedResultDetail = _lsResultList[indexResult];
    } else {
      _selectedResultDetail = null;
    }
  }

  void setupQualityControlInformation() {
    if (_lsQualityControlInformation.isNotEmpty) {
      for (int i = 0; i < _lsQualityControlInformation.length; i++) {
        addQualityControlInformation(i);
      }
    } else {
      addDefaultQualityControlInformation();
    }
  }

  void addQualityControlInformation(int i) {
    _lsSoSanPhamLoiController.add(TextEditingController());
    _lsGhiChuController.add(TextEditingController());
    _lsControllerInformation.add(TextEditingController());

    _lsSoSanPhamLoiController[i].text = (_lsQualityControlInformation[i].soSanPhamLoi == null
        ? _lsSoSanPhamLoiController[i].text
        : _lsQualityControlInformation[i].soSanPhamLoi?.round().toString())!;
    _lsGhiChuController[i].text = _lsQualityControlInformation[i].notes ?? _lsGhiChuController[i].text;
    _indexInfo =
        _lsQualityControlInformationIdList!.indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId) != -1
            ? _lsQualityControlInformationIdList!.indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId)
            : _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
    _lsGetIndexInfo.add(_indexInfo);
    _lsSelectedInfo.add(_lsQualityControlInformationIdList![_lsGetIndexInfo[i]]);
    _lsControllerInformation[i].text = _lsSelectedInfo[i] == null ? "" : _lsSelectedInfo[i]!.name ?? "";
    _lsGetFileInfor.add([]);
    _lsErrorInfor.add(false);
    _checkVisiButtonInformation.add(false);
  }

  void addDefaultQualityControlInformation() {
    _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
    _lsSoSanPhamLoiController.add(TextEditingController());
    _lsGhiChuController.add(TextEditingController());
    _lsControllerInformation.add(TextEditingController());
    _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
    _lsGetIndexInfo.add(_indexInfo);
    _lsGetFileInfor.add([]);
    _lsSelectedInfo.add(null);
    _lsErrorInfor.add(false);
    _checkVisiButtonInformation.add(false);
  }

  void setupError() {
    if (_lsError.isNotEmpty) {
      for (int i = 0; i < _lsError.length; i++) {
        addError(i);
      }
    } else {
      addDefaultError();
    }
  }

  void addError(int i) {
    _checkVisiButtonError.add(false);
    _lsTextEditingControllerError_1.add(TextEditingController());
    _lselectDataGetDefectLevel.add(null);
    _lsTextEditingControllerError_3.add(TextEditingController());
    _lsControllerError.add(TextEditingController());
    _lsTextEditingControllerError_1[i].text =
        _lsError[i].quantityError == null ? (0.toString()) : ((_lsError[i].quantityError ?? 0.0).round()).toString();
    _lselectDataGetDefectLevel[i] = _lsError[i].levelError == null
        ? _lselectDataGetDefectLevel[i]
        : _lsDataGetDefetchLevel.firstWhereOrNull((element) => element.key == _lsError[i].levelError.toString());
    _lsTextEditingControllerError_3[i].text = _lsError[i].notes == null ? _lsTextEditingControllerError_3[i].text : _lsError[i].notes.toString();
    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
    _lsGetIndexError.add(_indexError);
    _lsGetFileError.add([]);
    _lsSelectedError.add(_lsGetIndexError[i] == -1 ? null : _lsErrorList![_lsGetIndexError[i]]);
    _lsControllerError[i].text = _lsSelectedError[i] == null ? "" : _lsSelectedError[i]!.catalogTextVi ?? "";
  }

  void addDefaultError() {
    _checkVisiButtonError.add(false);
    _lsError.add(QualityControlErrorFunction.defaultListError);
    _lsTextEditingControllerError_1.add(TextEditingController());
    _lselectDataGetDefectLevel.add(null);
    _lsTextEditingControllerError_3.add(TextEditingController());
    _lsControllerError.add(TextEditingController());
    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
    _lsGetIndexError.add(_indexError);
    _lsGetFileError.add([]);
    _lsSelectedError.add(null);
  }

  Future<void> _getMaterial() async {
    final data = await ImportWareHouseFunction.fetchRawMaterial(
      _qualityControl!.customerReference.toString(),
      widget.user.token.toString(),
      "qr",
    );
    if (!mounted) return;

    if (data != null) {
      setState(() {
        _isLoading = false;
        _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
        _dataRawMaterial = data;

        _qualityChckerInfo = createSelectedStaff();
        updateSelectedType();

        updateLimitMethod();

        _updateTongSoSanPhamLoi();

        updateController1Text();
        // updateController2Text();
        // updateSelectedResult();
        updateQualityControlDetail();
        setupQualityControlInformation();
        setupError();

        _controllerSoLuongNhapKho.text = _qualityControl?.soLuongNhapKho?.toString() ?? '';
        _controllerSoLuongBlock.text = _qualityControl?.soLuongBlock?.toString() ?? '';
        _controllerSoLuongTraVe.text = _qualityControl?.soLuongTraVe?.toString() ?? '';
      });
    }
  }

  void _setDate(DateTime? newDate) {
    if (!mounted) return;
    if (newDate != null) {
      setState(() {
        _qualityDate = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
        _qualityDateStr = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
        debugPrint(_qualityDate!.toIso8601String());
      });
    } else {
      return;
    }
  }

  Future<void> _getQuantityControl() async {
    setState(() {
      _timeOut = false;
      _isLoading = true;
      _isNotWifi = false;
    });

    final listData = await Future.wait([
      // QualityControlFunction.fetchQualityControl(
      //   widget.qualityControlId,
      //   widget.user.token.toString(),
      //   widget.qrCode,
      //   widget.fromPage,
      // ),
      QualityControlFunction.fetchQualityControlDauVao(
        widget.qualityControlId,
        widget.user.token.toString(),
        widget.qrCode,
        widget.fromPage,
      ),
      QualityControlFunction.fetchGetDefectLevelApi(widget.user.token.toString())
    ]);

    if (!mounted) return;

    setState(() {
      if (listData.isNotEmpty) {
        setDefectLevelList(listData[1]);
        qualityControlDataLoaded(listData[0]);
      }
    });
  }

  void setDefectLevelList(dynamic data) {
    if (data != null) {
      _lsDataGetDefetchLevel = data as List<DataGetDefectLevel>;
    }
  }

  void qualityControlDataLoaded(dynamic data) {
    if (data != null) {
      _qualityControlModel = data as QualityControlModel?;
      if (_qualityControlModel != null) {
        _qualityControl = _qualityControlModel!.qualityControl;

        setupMasterDataList();
        updateQualityControlInformation();
        updateErrors();
        setupQualityDate();
        setErrorsList();
      }
    } else {
      _isLoading = false;
    }
  }

  // Set master data list for selection
  void setupMasterDataList() {
    _qualityTypeList = prependDefault(_qualityControlModel!.qualityTypeList, QualityControlFunction.defaultValueQualityTypeList);

    _lsTestMethodList = prependDefault(_qualityControlModel!.testMethodList, QualityControlDetailFunction.defaultValueTestMethodList);

    _lsLimitCriticalList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitHighList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitLowList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);

    _lsResultList = prependDefault(_qualityControlModel!.resultList, QualityControlDetailFunction.defaultResultList);
    _lsSamplingLevelList = prependDefault(_qualityControlModel!.samplingLevelList, QualityControlDetailFunction.defaultValueSamplingLevelList);
    _lsQualityControlInformationIdList = _qualityControlModel!.qualityControlInformationIdList;
  }

  void updateQualityControlInformation() {
    _lsQualityControlInformation = _qualityControl?.qualityControlInformation?.isNotEmpty == true ? _qualityControl!.qualityControlInformation! : [];
  }

  void updateErrors() {
    _lsError = _qualityControl?.error?.isNotEmpty == true ? _qualityControl!.error! : [];
  }

  void setupQualityDate() {
    _qualityDate = _qualityControl?.qualityDate != null
        ? DateFormat("yyyy-MM-ddThh:mm:ss").parse(_qualityControl!.qualityDate!)
        : DateFormat("yyyy-MM-dd HH:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()));
  }

  void setErrorsList() {
    _lsErrorList = _qualityControlModel!.errorList;
  }

  List<T> prependDefault<T>(List<T>? list, T defaultValue) {
    if (list == null) {
      return [defaultValue];
    } else {
      return [defaultValue, ...list];
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButtonTimeOut = true;
    });
  }

  void _deleteListImageTabCheck(int index) {
    if (!mounted) return;
    setState(() {
      _lsFileTabCheck.removeAt(index);
    });
  }

  void _pickFileImage(File file) {
    if (!mounted) return;
    setState(() {
      _lsFileTabCheck.add(file);
    });
  }

  void _pickFileImageInformation(MultiListImageFile multiListImageFile) {
    setState(() {
      _lsGetFileInfor[multiListImageFile.index].add(multiListImageFile.file);
    });
  }

  // void _getSelectedResult(ResultList? value) {
  //   setState(() {
  //     _selectedResult = value;
  //     if (_selectedResult == null || _selectedResult!.catalogCode == " ") {
  //       _errorSelectedResultQualityView = true;
  //     } else {
  //       _errorSelectedResultQualityView = false;
  //     }
  //   });
  // }

  void _getSelectedDefectLevel(DropdownDefetchLevel value) {
    setState(() {
      _lselectDataGetDefectLevel[value.index] = value.value;
    });
  }

  // void _getSelectedType(QualityTypeList? value) {
  //   setState(() {
  //     _selectedType = value;
  //     if (_selectedType == null || _selectedType!.catalogCode == " ") {
  //       _errorSelectType = true;
  //     } else {
  //       _errorSelectType = false;
  //     }
  //   });
  // }

  void _getSelectedResultDetail(ResultList? value) {
    setState(() {
      _selectedResultDetail = value;
      if (_selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ") {
        _errorResultCheckDetail = true;
      } else {
        _errorResultCheckDetail = false;
      }
    });
    // print(_selectedResultDetail!.catalogTextVi.toString());
  }

  void _getSelectedLevel(SamplingLevelList? value) {
    if (value!.catalogCode != "OTHER") {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = false;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    } else {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = true;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    }
  }

  void _getSelectedMethod(TestMethodList? value) {
    setState(() {
      _selectedTestMethod = value;
      if (_selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ") {
        _errorTestMethodDetail = true;
      } else {
        _errorTestMethodDetail = false;
      }
    });
  }

  void _getSelectedLimitCritical(DropdownItemList? value) {
    setState(() {
      _selectedLimitCritical = value;
      if (_selectedLimitCritical == null || _selectedLimitCritical!.catalogCode == " ") {
        _errorLimitCriticalDetail = true;
      } else {
        _errorLimitCriticalDetail = false;
      }
    });
  }

  void _getSelectedLimitHigh(DropdownItemList? value) {
    setState(() {
      _selectedLimitHigh = value;
      if (_selectedLimitHigh == null || _selectedLimitHigh!.catalogCode == " ") {
        _errorLimitHighDetail = true;
      } else {
        _errorLimitHighDetail = false;
      }
    });
  }

  void _getSelectedLimitLow(DropdownItemList? value) {
    setState(() {
      _selectedLimitLow = value;
      if (_selectedLimitLow == null || _selectedLimitLow!.catalogCode == " ") {
        _errorLimitLowDetail = true;
      } else {
        _errorLimitLowDetail = false;
      }
    });
  }

  void _addNewCard() {
    setState(() {
      _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
      _lsSoSanPhamLoiController.add(TextEditingController());
      _lsGhiChuController.add(TextEditingController());
      _lsControllerInformation.add(TextEditingController());
      // _focusInformation.add(FocusNode());
      _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
      _lsGetIndexInfo.add(_indexInfo);
      _lsGetFileInfor.add([]);
      _lsSelectedInfo.add(null);
      _lsErrorInfor.add(false);
      _checkVisiButtonInformation.add(true);
    });
  }

  void _deleteListQualityInformation(MultiListImageDeleteFile multiListImageDeleteFile) {
    setState(() {
      _lsGetFileInfor[multiListImageDeleteFile.index].removeAt(multiListImageDeleteFile.indexImage);
    });
  }

  void _addNewCardError() {
    setState(() {
      _lsError.add(QualityControlErrorFunction.defaultListError);
      _lsTextEditingControllerError_1.add(TextEditingController());
      _lselectDataGetDefectLevel.add(null);
      _lsTextEditingControllerError_3.add(TextEditingController());
      _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
      _lsControllerError.add(TextEditingController());
      // _focusError.add(FocusNode());
      _lsGetIndexError.add(_indexError);
      _lsGetFileError.add([]);
      _lsSelectedError.add(null);
      _checkVisiButtonError.add(true);
    });
  }

  void _deleteListFileError(MultiDeleteImageErrorQuality multiDeleteImageErrorQuality) {
    setState(() {
      _lsGetFileError[multiDeleteImageErrorQuality.index].removeAt(multiDeleteImageErrorQuality.indexImageError);
    });
  }

  // void _getSelectedError(MultiSelectedErrorQuality multiSelectedErrorQuality) {
  //   setState(() {
  //     _lsSelectedError[multiSelectedErrorQuality.index] =
  //         multiSelectedErrorQuality.value!;
  //   });
  // }

  void _pickFileImageErrorQuality(MultiSelectImageErrorQuality multiSelectImageErrorQuality) {
    setState(() {
      _lsGetFileError[multiSelectImageErrorQuality.index].add(multiSelectImageErrorQuality.file);
    });
  }

  void _checkErrorQuantityView() {
    // setState(() {
    if (_controllerPONumber.text.isNotEmpty) {
      if (_errorP0 != false) {
        setState(() {
          _errorP0 = false;
        });
      }
    } else {
      if (_errorP0 != true) {
        setState(() {
          _errorP0 = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckerQuantityView() {
    setState(() {
      if (_controllerInspectLotQuantity.text.isNotEmpty) {
        if (_errorQuantityCheck != false) {
          _errorQuantityCheck = false;
        }
      } else {
        if (_errorQuantityCheck != true) {
          _errorQuantityCheck = true;
        }
      }
    });
  }

  // void _checkErrorHideDetailLevel() {
  //   if (_controllerHideDetailLever.text.isNotEmpty) {
  //     if (_errorHideDetail != false) {
  //       setState(() {
  //         _errorHideDetail = false;
  //       });
  //     }
  //   } else {
  //     if (_errorHideDetail != true) {
  //       setState(() {
  //         _errorHideDetail = true;
  //       });
  //     }
  //   }
  // }

  void _checkErrorAcceptableLevelDetail() {
    // setState(() {
    if (_controllerMucChapNhan.text.isNotEmpty) {
      if (_errorAcceptableLevelDetail != false) {
        setState(() {
          _errorAcceptableLevelDetail = false;
        });
      }
    } else {
      if (_errorAcceptableLevelDetail != true) {
        setState(() {
          _errorAcceptableLevelDetail = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckDetail() {
    // setState(() {
    if (_controllerInspectQuantityDetail.text.isNotEmpty) {
      if (_errorQuantityCheckDetail != false) {
        setState(() {
          _errorQuantityCheckDetail = false;
        });
      }
    } else {
      if (_errorQuantityCheckDetail != true) {
        setState(() {
          _errorQuantityCheckDetail = true;
        });
      }
    }
    // });
  }

  void _checkClearLsSelectedInfo(int index) {
    if (_lsSelectedInfo[index] != null) {
      setState(() {
        _lsSelectedInfo[index] = null;
      });
    }
  }

  void _setTypeAhead(TypeAheadErrorQuatity typeAheadErrorQuatity) {
    setState(() {
      _lsControllerError[typeAheadErrorQuatity.index ?? 0].text =
          typeAheadErrorQuatity.errorList == null ? "" : typeAheadErrorQuatity.errorList!.catalogTextVi ?? "";
      _lsSelectedError[typeAheadErrorQuatity.index ?? 0] = typeAheadErrorQuatity.errorList;
    });
  }

  void _checkErrorSelectedInfo(QuantityInformationSelected quantityInformationSelected) {
    setState(() {
      _lsControllerInformation[quantityInformationSelected.index ?? 0].text = quantityInformationSelected.qualityControlInformationIdList!.name ?? "";
      _lsSelectedInfo[quantityInformationSelected.index ?? 0] = quantityInformationSelected.qualityControlInformationIdList;
      if (_lsSelectedInfo[quantityInformationSelected.index ?? 0] != null) {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != false) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = false;
        }
      } else {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != true) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = true;
        }
      }
    });
  }

  void _updateTongSoSanPhamLoi() {
    int total = 0;
    for (var controller in _lsSoSanPhamLoiController) {
      String text = controller.text;
      // In case the text is empty or is not a valid integer, skip the iteration.
      if (text.isEmpty || int.tryParse(text) == null) {
        continue;
      }
      total += int.parse(text);
    }

    // After the loop, you can check if the total is valid and set to _controllerTongSoSanPhamLoi
    if (total != null && total >= 0) {
      _controllerTongSoSanPhamLoi.text = total.toString();
    }
  }

  void _checkClearLsSelectedError(int index) {
    if (_lsSelectedError[index] != null) {
      setState(() {
        _lsSelectedError[index] = null;
      });
    }
  }

  // void _checkErrorSelectedInfo(int index) {
  //   if (_lsSelectedInfo[index] != null) {
  //     if (_lsErrorInfor[index] != false) {
  //       setState(() {
  //         _lsErrorInfor[index] = false;
  //       });
  //     }
  //   } else {
  //     if (_lsErrorInfor[index] != true) {
  //       setState(() {
  //         _lsErrorInfor[index] = true;
  //       });
  //     }
  //   }
  // }
  void _deleteItemListInformation(int index) {
    setState(() {
      _lsQualityControlInformation.removeAt(index);
      _lsSoSanPhamLoiController.removeAt(index);
      _lsGhiChuController.removeAt(index);
      _lsControllerInformation.removeAt(index);
      // _focusInformation.removeAt(index);
      _lsGetIndexInfo.removeAt(index);
      _lsGetFileInfor.removeAt(index);
      _lsSelectedInfo.removeAt(index);
      _lsErrorInfor.removeAt(index);
      _checkVisiButtonInformation.removeAt(index);
    });
  }

  void _deleteItemListError(int index) {
    setState(() {
      _lsError.removeAt(index);
      _lsTextEditingControllerError_1.removeAt(index);
      _lselectDataGetDefectLevel.removeAt(index);
      _lsTextEditingControllerError_3.removeAt(index);
      _lsControllerError.removeAt(index);
      // _focusError.removeAt(index);
      _lsGetIndexError.removeAt(index);
      _lsGetFileError.removeAt(index);
      _lsSelectedError.removeAt(index);
      _checkVisiButtonError.removeAt(index);
      _lsErrorList = _qualityControlModel!.errorList;
    });
  }

  bool canSendQualityControl() {
    //  _errorSelectType == false &&
    // _errorP0 == false &&
    // _errorQuantityCheck == false &&
    // _errorSelectedResultQualityView == false &&
    // _errorTestMethodDetail == false &&
    // _errorLevelDetail == false &&
    // _errorAcceptableLevelDetail == false &&
    // _errorQuantityCheckDetail == false &&
    // _errorResultCheckDetail == false &&
    // _lsErrorInfor.where((element) => element == true).isEmpty

    bool ret = true;

    if (_errorQuantityCheck) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorTestMethodDetail) {
      showToast(context: context, message: "Vui lòng chọn phương pháp KT");
      ret = false;
    }

    if (_errorLevelDetail) {
      showToast(context: context, message: "Vui lòng chọn mức độ lấy mẫu KT");
      ret = false;
    }

    // if (_errorAcceptableLevelDetail) {
    //   showToast(context: context, message: "Error: Acceptable Level Detail failed!");
    //   ret = false;
    // }

    if (_errorQuantityCheckDetail) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorResultCheckDetail) {
      showToast(context: context, message: "Vui lòng chọn kết quả");
      ret = false;
    }

    if (_lsErrorInfor.where((element) => element == true).isNotEmpty) {
      showToast(context: context, message: "Vui lòng chọn hạng mục kiểm tra");
      ret = false;
    }

    return ret;
  }

  void submitData() {
    if (!isTokenLive(widget.dateTimeOld)) {
      Platform.isAndroid
          ? showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
          : showCupertinoDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
      return;
    }

    _checkValidate();
    FocusManager.instance.primaryFocus?.unfocus();
    debugPrint(_errorAcceptableLevelDetail.toString());
    // debugPrint(_lsTextEditingControllerError_1[0].text);
    var canSend = canSendQualityControl();

    if (canSend) {
      QualityControlFunction.sendQualityControlDauVao3(
        context,
        _lsFileTabCheck,
        _controllerInspectQuantityDetail.text,
        _qualityControl,
        _qualityChckerInfo,
        _qualityDate!.toIso8601String(),
        _selectedType,
        _selectedResultDetail,
        _controllerPONumber.text,
        "",
        QualityControlDetailFunction.getSendQualityControlDetail2(
          _qualityControl,
          _selectedTestMethod,
          // Mức giới hạn
          _selectedLimitCritical, // new
          _selectedLimitHigh, // new
          _selectedLimitLow, // new

          _selectedLevel,
          _controllerMucChapNhan.text,
          _controllerInspectQuantityDetail.text,
          _controllerTongSoSanPhamLoi.text, // new

          _selectedResultDetail,
          _hideDetailLever,
          _controllerHideDetailLever.text,
          null,
          "",
          "",
          "",
          "",
        ),
        QualityControlInfoFunction.getLsSendQualityControlInformation2(
          _lsQualityControlInformation,
          _qualityControl,
          _lsSelectedInfo,
          _lsSoSanPhamLoiController,
          _lsGhiChuController,
          _lsGetFileInfor,
        ),
        QualityControlErrorFunction.getLsError(
          _lsError,
          _qualityControl,
          _lsSelectedError,
          _lselectDataGetDefectLevel,
          _lsTextEditingControllerError_1,
          _lsTextEditingControllerError_3,
          _lsGetFileError,
          [],
          [],
          [],
          [],
          [],
          [],
          [],
          [],
          [],
          [],
          [],
        ),
        widget.user.token.toString(),
        _controllerSoLuongNhapKho.text,
        _controllerSoLuongBlock.text,
        _controllerSoLuongTraVe.text,
      );
    }
  }

  @override
  void dispose() {
    _controllerPONumber.dispose();
    _controllerInspectLotQuantity.dispose();
    _controllerMucChapNhan.dispose();
    _controllerInspectQuantityDetail.dispose();
    _controllerTongSoSanPhamLoi.dispose();
    _controllerHideDetailLever.dispose();
    for (var i in _lsControllerInformation) {
      i.dispose();
    }
    for (var i in _lsSoSanPhamLoiController) {
      i.dispose();
    }
    for (var i in _lsGhiChuController) {
      i.dispose();
    }
    // for(var i in _focusError){
    //   i.dispose();
    // }
    for (var i in _lsControllerError) {
      i.dispose();
    }
    // for (var i in _focusInformation) {
    //   i.dispose();
    // }
    for (var i in _lsTextEditingControllerError_1) {
      i.dispose();
    }
    // for (var i in _lsTextEditingControllerError_2) {
    //   i.dispose();
    // }
    for (var i in _lsTextEditingControllerError_3) {
      i.dispose();
    }
    debugPrint('dispose');

    WidgetsBinding.instance!.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      setState(() {
        _bottomPadding = keyboardHeight;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true ? _buildTimeOutView() : _buildTabControllerView(context);
  }

  Widget _buildTimeOutView() {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimeOut)));
  }

  Widget _buildTabControllerView(BuildContext context) {
    return DefaultTabController(
        initialIndex: 0,
        length: 4,
        child: WillPopScope(
            onWillPop: () async {
              Navigator.pop(context, false);
              return false;
            },
            child: _buildChildBasedOnCondition()));
  }

  Widget _buildChildBasedOnCondition() {
    if (_isLoading) {
      return _buildLoading();
    }

    if (_isError) {
      return _buildError();
    }

    if (_isNotWifi) {
      return _buildWifi();
    }

    if (_qualityControlModel == null || _qualityControl == null) {
      return _buildEmptyQualityModel();
    }

    return _buildTabContent();
  }

  Widget _buildLoading() {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context, false);
          },
        ),
        title: Text(
          title,
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildError() {
    return Scaffold(
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ErrorView(),
    );
  }

  Widget _buildWifi() {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: Text(
              title,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
            )),
        body: LostConnect(checkConnect: () => _loadDataAndSetDefault()));
  }

  Widget _buildEmptyQualityModel() {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ListNotFoundView(),
    );
  }

  Widget formGroup({
    required String title,
    required bool enabled,
    required TextEditingController controller,
    required Function(String) onChanged,
    required String errorText,
    required bool error,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                decoration: BoxDecoration(border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                child: TextFormField(
                  enabled: enabled,
                  maxLines: null,
                  textAlign: TextAlign.center,
                  controller: controller,
                  style: TextStyle(fontSize: 12.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    fillColor: Colors.white,
                    hintStyle: TextStyle(fontSize: 12.sp),
                  ),
                  onChanged: onChanged,
                ),
              ),
              Visibility(
                visible: error,
                child: SizedBox(height: 10.h),
              ),
              QualityErrorValidate(text: errorText, error: error)
            ]),
          ),
        ],
      ),
    );
  }

  Widget buildSamplingLevelListDropdown({
    required String title,
    required void Function(SamplingLevelList?)? onChanged,
    required List<DropdownMenuItem<SamplingLevelList>> items,
    required DropdownButtonBuilder selectedItemBuilder,
    required Widget error,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<SamplingLevelList>(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: _selectedLevel ?? QualityControlDetailFunction.defaultValueSamplingLevelList,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: onChanged, // dropdown will disabled if this is null
                    items: items,
                    selectedItemBuilder: selectedItemBuilder,
                  ),
                ),
              ),
              Visibility(
                visible: _errorLevelDetail,
                child: SizedBox(height: 10.h),
              ),
              error,
            ]),
          )
        ],
      ),
    );
  }

  Widget buildLimitDropdown({
    required String title,
    required dynamic value,
    required void Function(DropdownItemList?)? onChanged,
    required List<DropdownMenuItem<DropdownItemList>> items,
    required String errorText,
    required DropdownButtonBuilder selectedItemBuilder,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<DropdownItemList>(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: value ?? QualityControlDetailFunction.defaultValueLimitList,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: onChanged, // dropdown will disabled if this is null
                    items: items,
                    selectedItemBuilder: selectedItemBuilder,
                  ),
                ),
              ),
              SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
              QualityErrorValidate(text: errorText, error: _errorTestMethodDetail)
            ]),
          )
        ],
      ),
    );
  }

  // Color determineColor(QualityControl? qualityControl) {
  //   if (qualityControl != null && qualityControl.qcType == "NVL") {
  //     if (qualityControl.qualityChecker != null) {
  //       return Colors.grey.shade400;
  //     } else {
  //       return const Color(0xff0052cc);
  //     }
  //   } else {
  //     return const Color(0xff0052cc);
  //   }
  // }

  Color determineColor() {
    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      if (_qualityControl!.qualityChecker != null) {
        return Colors.grey.shade400;
      } else {
        return const Color(0xff0052cc);
      }
    } else {
      return const Color(0xff0052cc);
    }
  }

  Widget qualityControlWidget() {
    Widget widgetToReturn = InfoBTPUpdate(qualityControl: _qualityControl);

    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      widgetToReturn = Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Expanded(flex: 5, child: FieldQuantity(field: "Loại hàng hóa đầu vào")),
              Expanded(flex: 5, child: TextBox(text: _loaiDauVao)),
            ],
          ),
          SizedBox(height: 10.h),
          InfoNVLUpdate(dataRawMaterial: _dataRawMaterial),
        ],
      );
    }

    return widgetToReturn;
  }

  Widget _buildTabContent() {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: TitleQuality(title: title)),
        bottomNavigationBar: SafeArea(
          child: TabBar(
            onTap: (_) => FocusManager.instance.primaryFocus?.unfocus(),
            unselectedLabelColor: Colors.black,
            labelColor: const Color(0xff0052cc),
            labelStyle: TextStyle(fontSize: 13.sp),
            indicatorColor: const Color(0xff0052cc),
            tabs: const <Widget>[
              Tab(
                icon: Icon(Icons.edit_note_rounded),
                text: 'Phiếu KT',
              ),
              Tab(
                icon: Icon(Icons.details_rounded),
                text: 'Mẫu CT',
              ),
              Tab(
                icon: Icon(Icons.info_outline_rounded),
                text: "T.Tin KT",
              ),
              Tab(
                icon: Icon(Icons.error_outline_outlined),
                text: "T.Tin Lỗi",
              ),
            ],
          ),
        ),
        body: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: TabBarView(
            physics: const NeverScrollableScrollPhysics(),
            children: <Widget>[
              SingleChildScrollView(
                padding: EdgeInsets.only(bottom: _bottomPadding),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 15.h),
                      qualityControlWidget(),
                      Visibility(visible: _qualityControl!.qcType != "NVL", child: const FieldQuantity(field: "Tình trạng MT:")),
                      SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "Ngày kiểm tra:")),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: GestureDetector(
                                onTap: isNVLDoneQC()
                                    ? null
                                    : () async {
                                        // _pickDateIOS(context);
                                        if (Platform.isAndroid) {
                                          final getDate = await QualityControlFunction.pickDate(context, _qualityDate);
                                          if (!mounted) return;
                                          _setDate(getDate);
                                        } else {
                                          final getDateIOS = await QualityControlFunction.pickDateIOS(context);
                                          if (!mounted) return;
                                          _setDate(getDateIOS);
                                        }
                                      },
                                child: TitleDateTimeQuality(date: _qualityDateStr)),
                          )
                        ],
                      ),
                      SizedBox(height: 15.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "NV kiểm tra")),
                          SizedBox(width: 10.w),
                          Expanded(flex: 7, child: TitleStaff(selectedStaff: _qualityChckerInfo))
                        ],
                      ),
                      SizedBox(height: 15.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "MSNV kiểm tra")),
                          SizedBox(width: 10.w),
                          Expanded(flex: 7, child: TextBox(text: _qualityChckerInfo?.salesEmployeeCode ?? ''))
                        ],
                      ),
                      SizedBox(height: 15.h),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   crossAxisAlignment: CrossAxisAlignment.center,
                      //   children: <Widget>[
                      //     const Expanded(flex: 3, child: FieldQuantity(field: "Loại kiểm tra")),
                      //     SizedBox(width: 10.w),
                      //     Expanded(
                      //       flex: 7,
                      //       child: Column(children: <Widget>[
                      //         Container(
                      //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                      //             decoration: BoxDecoration(
                      //               border: Border.all(width: 0.5, color: Colors.grey.shade400),
                      //               borderRadius: BorderRadius.circular(3.r),
                      //             ),
                      //             child: DropdownType(
                      //                 qualityControl: _qualityControl,
                      //                 onChangeType: _getSelectedType,
                      //                 qualityTypeList: _qualityTypeList,
                      //                 selectedType: _selectedType)),
                      //         SizedBox(height: _errorSelectType == true ? 10.h : 0),
                      //         QualityErrorValidate(error: _errorSelectType, text: "Bạn chưa chọn loại kiểm tra")
                      //       ]),
                      //     )
                      //   ],
                      // ),
                      SizedBox(height: 15.h),
                      Visibility(
                          visible: _qualityControl!.qcType != "NVL",
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              const Expanded(
                                flex: 3,
                                child: QualityTitleField(title: "PO"),
                              ),
                              SizedBox(width: 10.w),
                              Expanded(
                                flex: 7,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Column(children: <Widget>[
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                      decoration: BoxDecoration(
                                          border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                                      child: TextFormField(
                                        maxLines: null,
                                        keyboardType: TextInputType.number,
                                        inputFormatters: <TextInputFormatter>[
                                          FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                        ],
                                        textAlign: TextAlign.center,
                                        controller: _controllerPONumber,
                                        style: TextStyle(fontSize: 12.sp),
                                        decoration: InputDecoration(
                                          border: InputBorder.none,
                                          isDense: true,
                                          contentPadding: EdgeInsets.zero,
                                          errorBorder: InputBorder.none,
                                          disabledBorder: InputBorder.none,
                                          filled: true,
                                          fillColor: Colors.white,
                                          hintStyle: TextStyle(fontSize: 12.sp),
                                        ),
                                        onChanged: (value) {
                                          _checkErrorQuantityView();
                                        },
                                      ),
                                    ),
                                    SizedBox(height: _errorP0 == true ? 10.h : 0),
                                    QualityErrorValidate(error: _errorP0, text: "Vui lòng nhập PO"),
                                  ]),
                                ),
                              )
                            ],
                          )),
                      SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   crossAxisAlignment: CrossAxisAlignment.center,
                      //   children: <Widget>[
                      //     const Expanded(
                      //       flex: 3,
                      //       child: Align(
                      //         alignment: Alignment.centerLeft,
                      //         child: QualityTitleField(title: "SL kiểm tra"),
                      //       ),
                      //     ),
                      //     SizedBox(width: 10.w),
                      //     Expanded(
                      //       flex: 7,
                      //       child: Align(
                      //         alignment: Alignment.centerLeft,
                      //         child: Column(children: <Widget>[
                      //           Container(
                      //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                      //             decoration: BoxDecoration(
                      //                 border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                      //             child: TextFormField(
                      //               enabled: checkQualityControl(),
                      //               maxLines: null,
                      //               textAlign: TextAlign.center,
                      //               // ÍnpectionLotQuantiy (Master)
                      //               controller: _controller_2,
                      //               style: TextStyle(fontSize: 12.sp),
                      //               keyboardType: TextInputType.number,
                      //               inputFormatters: <TextInputFormatter>[
                      //                 FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                      //               ],
                      //               decoration: InputDecoration(
                      //                 border: InputBorder.none,
                      //                 isDense: true,
                      //                 contentPadding: EdgeInsets.zero,
                      //                 errorBorder: InputBorder.none,
                      //                 disabledBorder: InputBorder.none,
                      //                 filled: true,
                      //                 fillColor: Colors.white,
                      //                 hintStyle: TextStyle(fontSize: 12.sp),
                      //               ),
                      //               onChanged: (value) {
                      //                 _checkErrorQuantityCheckerQuantityView();
                      //               },
                      //             ),
                      //           ),
                      //           Visibility(
                      //             visible: _errorQuantityCheck,
                      //             child: SizedBox(height: 10.h),
                      //           ),
                      //           QualityErrorValidate(error: _errorQuantityCheck, text: "Vui lòng nhập SL kiểm tra"),
                      //         ]),
                      //       ),
                      //     )
                      //   ],
                      // ),
                      // SizedBox(height: 15.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(
                            flex: 3,
                            child: Align(alignment: Alignment.centerLeft, child: QualityTitleField(title: "Hình ảnh")),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                              flex: 7,
                              child: _lsFileTabCheck.isEmpty
                                  ? Row(
                                      children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: InkWell(
                                            onTap: isNVLDoneQC()
                                                ? null
                                                : () async {
                                                    final check = await QualityControlFunction.pickImage(context);
                                                    debugPrint(check.toString());
                                                    if (check != null) {
                                                      bool checkPermission = await ImageFunction.handlePermission(check);
                                                      if (checkPermission == true) {
                                                        if (check == true) {
                                                          List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                            maxWidth: globalImageConfig.maxWidth,
                                                            maxHeight: globalImageConfig.maxHeight,
                                                            imageQuality: globalImageConfig.imageQuality,
                                                          );
                                                          if (selectedImages.isEmpty) return;
                                                          for (var i in selectedImages) {
                                                            final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                            if (!mounted) return;
                                                            _pickFileImage(itemImage);
                                                          }
                                                        } else {
                                                          final image = await ImagePicker().pickImage(
                                                              maxWidth: globalImageConfig.maxWidth,
                                                              maxHeight: globalImageConfig.maxHeight,
                                                              imageQuality: globalImageConfig.imageQuality,
                                                              source: ImageSource.camera);
                                                          if (image == null) return;
                                                          final imageProfile = await ImageFunction.saveImage(image.path);
                                                          if (!mounted) return;
                                                          _pickFileImage(imageProfile);
                                                        }
                                                      }
                                                    }
                                                  },
                                            child: Text(
                                              "Chọn tệp",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10.w),
                                        Center(
                                          child: Text(
                                            "Chưa chọn tệp nào",
                                            style: TextStyle(fontSize: 11.sp),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                                      Container(
                                        padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10.r),
                                          color: Colors.grey.shade100,
                                        ),
                                        child: InkWell(
                                          onTap: () async {
                                            // debugPrint('yes');
                                            final check = await QualityControlFunction.pickImage(context);
                                            debugPrint(check.toString());
                                            if (check != null) {
                                              bool checkPermission = await ImageFunction.handlePermission(check);
                                              if (checkPermission == true) {
                                                if (check == true) {
                                                  List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                    maxWidth: globalImageConfig.maxWidth,
                                                    maxHeight: globalImageConfig.maxHeight,
                                                    imageQuality: globalImageConfig.imageQuality,
                                                  );
                                                  if (selectedImages.isEmpty) return;
                                                  for (var i in selectedImages) {
                                                    final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                    _pickFileImage(itemImage);
                                                  }
                                                } else {
                                                  final image = await ImagePicker().pickImage(
                                                      maxWidth: globalImageConfig.maxWidth,
                                                      maxHeight: globalImageConfig.maxHeight,
                                                      imageQuality: globalImageConfig.imageQuality,
                                                      source: ImageSource.camera);
                                                  if (image == null) return;
                                                  final imageProfile = await ImageFunction.saveImage(image.path);
                                                  _pickFileImage(imageProfile);
                                                }
                                              }
                                            }
                                          },
                                          child: Text(
                                            "Chọn tệp",
                                            style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.h),
                                      ListChooseImage(lsFileTabCheck: _lsFileTabCheck, deleteListImageTabCheck: _deleteListImageTabCheck),
                                      SizedBox(height: 10.h),
                                    ])),
                        ],
                      ),
                      // SizedBox(height: 15.h),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   crossAxisAlignment: CrossAxisAlignment.center,
                      //   children: <Widget>[
                      //     const Expanded(flex: 3, child: QualityTitleField(title: "Kết quả")),
                      //     SizedBox(width: 10.w),
                      //     Expanded(
                      //       flex: 7,
                      //       child: Column(children: <Widget>[
                      //         Container(
                      //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                      //             decoration: BoxDecoration(
                      //               border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                      //               borderRadius: BorderRadius.circular(3.r),
                      //             ),
                      //             child: DropdownQuantityResult(
                      //                 selectedResult: _selectedResult,
                      //                 lsResultList: _lsResultList,
                      //                 qualityControl: _qualityControl,
                      //                 onChangeResult: _getSelectedResult)),
                      //         SizedBox(height: _errorSelectedResultQualityView == true ? 10.h : 0),
                      //         QualityErrorValidate(error: _errorSelectedResultQualityView, text: "Vui lòng chọn kết quả"),
                      //       ]),
                      //     )
                      //   ],
                      // ),
                      Visibility(
                        visible: _qualityControl!.fileViewModel == null || _qualityControl!.fileViewModel!.isEmpty ? false : true,
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: ElevatedButton.icon(
                            icon: Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 15.sp,
                            ),
                            style: ButtonStyle(
                              side: MaterialStateProperty.all(
                                const BorderSide(
                                  color: Color(0xff0052cc),
                                ),
                              ),
                              backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                            ),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return DialogImage(title: 'HÌNH ẢNH KIỂM TRA', listImage: _qualityControl!.fileViewModel);
                                },
                              );
                            },
                            label: Text(
                              'Hình ảnh',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 11.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 15.h),
                    ],
                  ),
                ),
              ),
              SingleChildScrollView(
                padding: EdgeInsets.only(bottom: _bottomPadding),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 15.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.h),
                              decoration: const BoxDecoration(
                                color: Color(0xff0052cc),
                              ),
                              child: Text(
                                "Mẫu thử chi tiết",
                                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
                              ),
                            ),
                            SizedBox(height: 15.h),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  const Expanded(flex: 4, child: QualityTitleField(title: "Phương pháp KT")),
                                  SizedBox(width: 10.w),
                                  Expanded(
                                    flex: 6,
                                    child: Column(children: <Widget>[
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                        decoration: BoxDecoration(
                                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                          borderRadius: BorderRadius.circular(3.r),
                                        ),
                                        child: DropdownButtonHideUnderline(
                                          child: DropdownButton<TestMethodList>(
                                            isExpanded: true,
                                            isDense: true,
                                            itemHeight: null,
                                            value: _selectedTestMethod ?? QualityControlDetailFunction.defaultValueTestMethodList,
                                            iconSize: 15.sp,
                                            style: const TextStyle(color: Colors.white),
                                            onChanged: isNVLDoneQC() ? null : _getSelectedMethod,
                                            items: _lsTestMethodList.map((TestMethodList method) {
                                              return DropdownMenuItem<TestMethodList>(
                                                  value: method,
                                                  child: Padding(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                                    child: Text(
                                                      method.catalogTextVi.toString(),
                                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                    ),
                                                  ));
                                            }).toList(),
                                            selectedItemBuilder: (BuildContext context) {
                                              return _lsTestMethodList.map<Widget>((TestMethodList method) {
                                                return Text(method.catalogTextVi.toString(),
                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                              }).toList();
                                            },
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
                                      QualityErrorValidate(text: "Vui lòng chọn phương pháp KT", error: _errorTestMethodDetail)
                                    ]),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(height: 5.h),
                            buildSamplingLevelListDropdown(
                              title: "Mức độ lấy mẫu KT",
                              onChanged: isNVLDoneQC()
                                  ? null
                                  : (SamplingLevelList? value) {
                                      _getSelectedLevel(value);
                                    },
                              items: _lsSamplingLevelList.map((SamplingLevelList level) {
                                return DropdownMenuItem<SamplingLevelList>(
                                    value: level,
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(vertical: 5.h),
                                      child: Text(
                                        level.catalogTextVi.toString(),
                                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                      ),
                                    ));
                              }).toList(),
                              selectedItemBuilder: (BuildContext context) {
                                return _lsSamplingLevelList.map<Widget>((SamplingLevelList level) {
                                  return Text(level.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                }).toList();
                              },
                              error: QualityErrorValidate(text: "Vui lòng chọn mức độ lấy mẫu KT", error: _errorLevelDetail),
                            ),
                            SizedBox(height: 5.h),
                            buildLimitDropdown(
                              title: "Mức giới hạn lỗi nghiêm trọng",
                              value: _selectedLimitCritical ?? QualityControlDetailFunction.defaultValueLimitList,
                              onChanged: isNVLDoneQC() ? null : _getSelectedLimitCritical,
                              items: _lsLimitCriticalList.map((DropdownItemList method) {
                                return DropdownMenuItem<DropdownItemList>(
                                  value: method,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                    child: Text(
                                      method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                    ),
                                  ),
                                );
                              }).toList(),
                              errorText: "Vui lòng chọn mức giới hạn lỗi nghiêm trọng",
                              selectedItemBuilder: (BuildContext context) {
                                return _lsLimitCriticalList.map<Widget>((DropdownItemList method) {
                                  return Text(method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                }).toList();
                              },
                            ),
                            SizedBox(height: 5.h),
                            buildLimitDropdown(
                              title: "Mức giới hạn lỗi nặng",
                              value: _selectedLimitHigh ?? QualityControlDetailFunction.defaultValueLimitList,
                              onChanged: isNVLDoneQC() ? null : _getSelectedLimitHigh,
                              items: _lsLimitHighList.map((DropdownItemList method) {
                                return DropdownMenuItem<DropdownItemList>(
                                  value: method,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                    child: Text(
                                      method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                    ),
                                  ),
                                );
                              }).toList(),
                              errorText: "Vui lòng chọn mức giới hạn lỗi nặng",
                              selectedItemBuilder: (BuildContext context) {
                                return _lsLimitHighList.map<Widget>((DropdownItemList method) {
                                  return Text(method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                }).toList();
                              },
                            ),
                            SizedBox(height: 5.h),
                            buildLimitDropdown(
                              title: "Mức giới hạn lỗi nhẹ",
                              value: _selectedLimitLow ?? QualityControlDetailFunction.defaultValueLimitList,
                              onChanged: isNVLDoneQC() ? null : _getSelectedLimitLow,
                              items: _lsLimitLowList.map((DropdownItemList method) {
                                return DropdownMenuItem<DropdownItemList>(
                                  value: method,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                    child: Text(
                                      method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                    ),
                                  ),
                                );
                              }).toList(),
                              errorText: "Vui lòng chọn mức giới hạn lỗi nhẹ",
                              selectedItemBuilder: (BuildContext context) {
                                return _lsLimitLowList.map<Widget>((DropdownItemList method) {
                                  return Text(method.catalogTextVi.toString(),
                                      style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                }).toList();
                              },
                            ),

                            // SizedBox(height: 5.h),
                            // formGroup(
                            //   title: "Mức chấp nhận",
                            //   enabled: checkQualityControl(),
                            //   controller: _controllerMucChapNhan,
                            //   onChanged: (value) {
                            //     _checkErrorAcceptableLevelDetail();
                            //   },
                            //   errorText: "Vui lòng nhập mức chấp nhận",
                            //   error: _errorAcceptableLevelDetail,
                            // ),
                            SizedBox(height: 5.h),

                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: Align(
                                alignment: Alignment.centerRight,
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: determineColor(),
                                  ),
                                  // onPressed: _controllerInspectLotQuantity.text.isEmpty ||
                                  //         (_selectedLevel == null || _selectedLevel!.catalogCode == " ") ||
                                  //         _loadingGetQuanititySample == true
                                  //     ? null
                                  //     : _qualityControl!.qcType == "NVL"
                                  //         ? _qualityControl!.qualityChecker != null
                                  //             ? null
                                  //             : () {
                                  //                 _getGetQuanititySample(context);
                                  //               }
                                  //         : () => _getGetQuanititySample(context),
                                  onPressed: _qualityControl!.qcType != "NVL" || _qualityControl!.qualityChecker == null
                                      ? () => _getGetQuanititySample(context)
                                      : null,
                                  child: Text(_loadingGetQuanititySample == true ? "Loading..." : 'Lấy SL kiểm tra',
                                      style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold)),
                                ),
                              ),
                            ),
                            SizedBox(height: 5.h),
                            formGroup(
                              // InspectQuantity (Detail)
                              title: "SL kiểm tra",
                              enabled: checkQualityControl(),
                              controller: _controllerInspectQuantityDetail,
                              onChanged: (value) {
                                _checkErrorQuantityCheckDetail();
                              },
                              errorText: "Vui lòng nhập SL kiểm tra",
                              error: _errorQuantityCheckDetail,
                            ),
                            SizedBox(height: 5.h),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  const Expanded(flex: 4, child: FieldQuantity(field: "Tổng số lượng SP lỗi")),
                                  SizedBox(width: 10.w),
                                  Expanded(
                                      flex: 6,
                                      child: Container(
                                        // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                        decoration: BoxDecoration(
                                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                          borderRadius: BorderRadius.circular(3.r),
                                        ),
                                        child: TextFormField(
                                          // enabled: false,
                                          readOnly: true,
                                          maxLines: null,
                                          controller: _controllerTongSoSanPhamLoi,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(fontSize: 12.sp),
                                          decoration: InputDecoration(
                                            border: InputBorder.none,
                                            isDense: true,
                                            contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                            errorBorder: InputBorder.none,
                                            disabledBorder: InputBorder.none,
                                            filled: true,
                                            fillColor: Colors.white,
                                            hintStyle: TextStyle(fontSize: 12.sp),
                                          ),
                                        ),
                                      ))
                                ],
                              ),
                            ),
                            // formGroup(
                            //   title: "Tổng số lượng SP lỗi",
                            //   enabled: checkQualityControl(),
                            //   controller: _controllerSLKiemTra,
                            //   onChanged: (value) {
                            //     _checkErrorQuantityCheckDetail();
                            //   },
                            //   errorText: "Vui lòng nhập SL kiểm tra",
                            //   error: _errorQuantityCheckDetail,
                            // ),
                            SizedBox(height: 15.h),
                          ],
                        ),
                      ),
                      SizedBox(height: 15.h),
                    ],
                  ),
                ),
              ),
              SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 200),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 15.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                        ),
                        child: Column(
                          children: <Widget>[
                            const QualityTitle(title: "Thông tin kiểm tra"), // Title
                            SizedBox(height: 10.h),
                            Column(
                                children: List.generate(
                                    _lsQualityControlInformation.length,
                                    (index) => Container(
                                          margin: EdgeInsets.symmetric(vertical: 5.h),
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(horizontal: 1.w),
                                            child: Column(
                                              children: [
                                                itemHeader(),
                                                // ItemThongTinKiemTra(index, context),
                                                ItemThongTinKiemTra2(
                                                    index: index,
                                                    qualityControl: _qualityControl,
                                                    lsGetFileInfor: _lsGetFileInfor,
                                                    lsThongTinKiemTra: _lsQualityControlInformation,
                                                    lsThongTinKiemTraMasterData: _lsQualityControlInformationIdList,
                                                    checkVisiButtonInformation: _checkVisiButtonInformation,
                                                    pickFileImageInformation: _pickFileImageInformation,
                                                    deleteListQualityInformation: _deleteListQualityInformation,
                                                    deleteItemListInformation: _deleteItemListInformation,
                                                    lsControllerThongTinKiemTra: _lsControllerInformation,
                                                    lsControllerSoSanPhamLoi: _lsSoSanPhamLoiController,
                                                    lsControllerGhiChu: _lsGhiChuController,
                                                    checkClearLsSelectedInfo: _checkClearLsSelectedInfo,
                                                    pickerImage: _pickerImage,
                                                    lsErrorInfor: _lsErrorInfor,
                                                    checkErrorSelectedInfo: _checkErrorSelectedInfo,
                                                    updateTongSoSanPhamLoi: _updateTongSoSanPhamLoi)
                                              ],
                                            ),
                                          ),
                                        ))),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: IntrinsicHeight(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: <Widget>[
                                    Expanded(
                                      flex: 6,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                        ),
                                        child: const Text(""),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 4,
                                      child: Container(
                                        padding: EdgeInsets.symmetric(vertical: 5.h),
                                        height: 40.h,
                                        decoration: BoxDecoration(
                                          border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                        ),
                                        child: Container(
                                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                                          child: ElevatedButton.icon(
                                            icon: Icon(
                                              Icons.add,
                                              color: Colors.white,
                                              size: 15.sp,
                                            ),
                                            style: ButtonStyle(
                                              padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w)),
                                              side: MaterialStateProperty.all(
                                                const BorderSide(
                                                  color: Colors.green,
                                                ),
                                              ),
                                              backgroundColor: MaterialStateProperty.all(Colors.green),
                                            ),
                                            onPressed: isNVLDoneQC()
                                                ? null
                                                : () {
                                                    _addNewCard();
                                                  },
                                            label: Text(
                                              'Thêm',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 10.sp,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: 15.h),
                          ],
                        ),
                      ),
                      SizedBox(height: 15.h),
                    ],
                  ),
                ),
              ),
              SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 200),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 15.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                        ),
                        child: Column(
                          children: <Widget>[
                            const QualityTitle(title: 'Thông tin lỗi'),
                            SizedBox(height: 10.h),
                            Column(
                                children: List.generate(
                                    _lsError.length,
                                    (index) => Container(
                                          margin: EdgeInsets.symmetric(vertical: 5.h),
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(horizontal: 15.w),
                                            child: IntrinsicHeight(
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                                children: <Widget>[
                                                  Expanded(
                                                    flex: 1,
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                                                      ),
                                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                                                      child: Center(
                                                        child: Text(
                                                          (index + 1).toString(),
                                                          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 9,
                                                    child: IntrinsicHeight(
                                                      child: Column(
                                                        mainAxisSize: MainAxisSize.min,
                                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                                        children: <Widget>[
                                                          IntrinsicHeight(
                                                            child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Container(
                                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(
                                                                        width: 0.5,
                                                                        color: Colors.grey.shade300,
                                                                      ),
                                                                    ),
                                                                    child: Text(
                                                                      "Danh sách lỗi",
                                                                      style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 4,
                                                                  child: Container(
                                                                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                      decoration: BoxDecoration(
                                                                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                      ),
                                                                      child: Text(
                                                                        "Số lượng lỗi",
                                                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                      )),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          IntrinsicHeight(
                                                            child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Container(
                                                                      decoration: BoxDecoration(
                                                                        border: Border.all(
                                                                          width: 0.5,
                                                                          color: Colors.grey.shade300,
                                                                        ),
                                                                      ),
                                                                      child: Container(
                                                                        child: Padding(
                                                                          padding: REdgeInsets.all(5),
                                                                          child: Center(
                                                                            child: Container(
                                                                              decoration: BoxDecoration(
                                                                                border: Border.all(
                                                                                  width: 0.5.w,
                                                                                  color: Colors.grey.shade400,
                                                                                ),
                                                                              ),
                                                                              child: TypeAheadField(
                                                                                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                                                                  constraints: BoxConstraints(
                                                                                    minWidth: 150.w,
                                                                                  ),
                                                                                ),
                                                                                textFieldConfiguration: TextFieldConfiguration(
                                                                                    enabled: checkQualityControl(),
                                                                                    decoration: InputDecoration(
                                                                                      labelStyle: TextStyle(fontSize: 11.sp),
                                                                                      contentPadding:
                                                                                          EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                                      isDense: true,
                                                                                      border: InputBorder.none,
                                                                                      focusedBorder: InputBorder.none,
                                                                                      enabledBorder: InputBorder.none,
                                                                                    ),
                                                                                    controller: _lsControllerError[index],
                                                                                    // focusNode: focusError[index],
                                                                                    style: TextStyle(fontSize: 12.sp),
                                                                                    onChanged: (value) {
                                                                                      _checkClearLsSelectedError(index);
                                                                                    }),
                                                                                suggestionsCallback: (pattern) {
                                                                                  return QualityControlFunction.filterQualityControlErrorList(
                                                                                      _lsErrorList ?? [], pattern);
                                                                                },
                                                                                itemBuilder: (context, suggestion) {
                                                                                  return ListTile(
                                                                                    title: Text((suggestion as ErrorList).catalogTextVi ?? " ",
                                                                                        style: TextStyle(fontSize: 12.sp)),
                                                                                  );
                                                                                },
                                                                                onSuggestionSelected: (suggestion) {
                                                                                  _setTypeAhead(
                                                                                      TypeAheadErrorQuatity(index: index, errorList: suggestion));
                                                                                },
                                                                                noItemsFoundBuilder: (value) {
                                                                                  return Padding(
                                                                                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                                                      child: Text("Không tìm thấy kết quả",
                                                                                          style: TextStyle(fontSize: 11.sp)));
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      )),
                                                                ),
                                                                Expanded(
                                                                  flex: 4,
                                                                  child: Container(
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                    ),
                                                                    child: Padding(
                                                                      padding: REdgeInsets.all(5),
                                                                      child: Container(
                                                                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                                        decoration: BoxDecoration(
                                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                                        ),
                                                                        child: TextFormField(
                                                                          enabled: checkQualityControl(),
                                                                          textAlign: TextAlign.center,
                                                                          keyboardType: TextInputType.number,
                                                                          inputFormatters: <TextInputFormatter>[
                                                                            FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                                          ],
                                                                          controller: _lsTextEditingControllerError_1[index],
                                                                          style: TextStyle(fontSize: 12.sp),
                                                                          decoration: InputDecoration(
                                                                            border: InputBorder.none,
                                                                            focusedBorder: InputBorder.none,
                                                                            enabledBorder: InputBorder.none,
                                                                            errorBorder: InputBorder.none,
                                                                            disabledBorder: InputBorder.none,
                                                                            filled: true,
                                                                            isDense: true,
                                                                            fillColor: Colors.white,
                                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                                            contentPadding: EdgeInsets.zero,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                          IntrinsicHeight(
                                                            child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Container(
                                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(
                                                                        width: 0.5,
                                                                        color: Colors.grey.shade300,
                                                                      ),
                                                                    ),
                                                                    child: Text(
                                                                      "Mức độ lỗi",
                                                                      style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                    ),
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  flex: 4,
                                                                  child: Container(
                                                                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                      decoration: BoxDecoration(
                                                                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                      ),
                                                                      child: Text(
                                                                        "Ghi chú",
                                                                        style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                      )),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          IntrinsicHeight(
                                                            child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Container(
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                    ),
                                                                    child: Column(
                                                                      children: <Widget>[
                                                                        Padding(
                                                                          padding: REdgeInsets.all(5),
                                                                          child: Container(
                                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                            decoration: BoxDecoration(
                                                                              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                                            ),
                                                                            child: DropdownButtonHideUnderline(
                                                                              child: DropdownButton<DataGetDefectLevel>(
                                                                                isExpanded: true,
                                                                                isDense: true,
                                                                                itemHeight: null,
                                                                                value: _lselectDataGetDefectLevel[index],
                                                                                iconSize: 15.sp,
                                                                                style: const TextStyle(color: Colors.white),
                                                                                onChanged: _qualityControl!.qcType == "NVL" &&
                                                                                        _qualityControl!.qualityChecker != null
                                                                                    ? null
                                                                                    : (DataGetDefectLevel? value) {
                                                                                        _getSelectedDefectLevel(
                                                                                            DropdownDefetchLevel(index: index, value: value));
                                                                                      },
                                                                                items: _lsDataGetDefetchLevel.map((DataGetDefectLevel result) {
                                                                                  return DropdownMenuItem<DataGetDefectLevel>(
                                                                                      value: result,
                                                                                      child: Padding(
                                                                                        padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                                        child: Text(
                                                                                          result.value.toString(),
                                                                                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                        ),
                                                                                      ));
                                                                                }).toList(),
                                                                                selectedItemBuilder: (BuildContext context) {
                                                                                  return _lsDataGetDefetchLevel.map((DataGetDefectLevel result) {
                                                                                    return Text(result.value.toString(),
                                                                                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                                        overflow: TextOverflow.ellipsis);
                                                                                  }).toList();
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ),
                                                                // muc do loi controller
                                                                Expanded(
                                                                  flex: 4,
                                                                  child: Container(
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                    ),
                                                                    child: Column(
                                                                      children: [
                                                                        Padding(
                                                                          padding: REdgeInsets.all(5),
                                                                          child: Container(
                                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                                            decoration: BoxDecoration(
                                                                              border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                                            ),
                                                                            child: TextFormField(
                                                                              enabled: checkQualityControl(),
                                                                              maxLines: null,
                                                                              textAlign: TextAlign.center,
                                                                              controller: _lsTextEditingControllerError_3[index],
                                                                              style: TextStyle(fontSize: 12.sp),
                                                                              decoration: InputDecoration(
                                                                                border: InputBorder.none,
                                                                                focusedBorder: InputBorder.none,
                                                                                enabledBorder: InputBorder.none,
                                                                                errorBorder: InputBorder.none,
                                                                                disabledBorder: InputBorder.none,
                                                                                filled: true,
                                                                                isDense: true,
                                                                                fillColor: Colors.white,
                                                                                hintStyle: TextStyle(fontSize: 12.sp),
                                                                                contentPadding: EdgeInsets.zero,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          IntrinsicHeight(
                                                            child: Container(
                                                              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                              decoration: BoxDecoration(
                                                                border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                              ),
                                                              child: Row(
                                                                children: <Widget>[
                                                                  Expanded(
                                                                    flex: 8,
                                                                    child: _lsGetFileError[index].isEmpty
                                                                        ? Row(
                                                                            crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                            children: <Widget>[
                                                                              GestureDetector(
                                                                                onTap: _qualityControl!.qcType == "NVL" &&
                                                                                        _qualityControl!.qualityChecker != null
                                                                                    ? null
                                                                                    : () async {
                                                                                        final check = await QualityControlFunction.pickImage(context);
                                                                                        debugPrint(check.toString());
                                                                                        if (check != null) {
                                                                                          bool checkPermission =
                                                                                              await ImageFunction.handlePermission(check);
                                                                                          if (checkPermission == true) {
                                                                                            if (check == true) {
                                                                                              List<XFile>? selectedImages =
                                                                                                  await _pickerImage.pickMultiImage(
                                                                                                maxWidth: globalImageConfig.maxWidth,
                                                                                                maxHeight: globalImageConfig.maxHeight,
                                                                                                imageQuality: globalImageConfig.imageQuality,
                                                                                              );
                                                                                              if (selectedImages.isEmpty) return;
                                                                                              for (var i in selectedImages) {
                                                                                                final itemImage =
                                                                                                    await ImageFunction.saveImageMulti(i.path);
                                                                                                _pickFileImageErrorQuality(
                                                                                                    MultiSelectImageErrorQuality(
                                                                                                        index: index, file: itemImage));
                                                                                              }
                                                                                            } else {
                                                                                              final image = await ImagePicker().pickImage(
                                                                                                  maxWidth: globalImageConfig.maxWidth,
                                                                                                  maxHeight: globalImageConfig.maxHeight,
                                                                                                  imageQuality: globalImageConfig.imageQuality,
                                                                                                  source: ImageSource.camera);
                                                                                              if (image == null) return;
                                                                                              final imageProfile =
                                                                                                  await ImageFunction.saveImage(image.path);
                                                                                              _pickFileImageErrorQuality(MultiSelectImageErrorQuality(
                                                                                                  index: index, file: imageProfile));
                                                                                            }
                                                                                          }
                                                                                        }
                                                                                      },
                                                                                child: Container(
                                                                                  padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                                                                  decoration: BoxDecoration(
                                                                                    borderRadius: BorderRadius.circular(10.r),
                                                                                    color: Colors.grey.shade100,
                                                                                  ),
                                                                                  child: Center(
                                                                                    child: Text(
                                                                                      "Chọn tệp",
                                                                                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              SizedBox(width: 10.w),
                                                                              Center(
                                                                                child: Text(
                                                                                  "Chưa chọn tệp nào",
                                                                                  style: TextStyle(fontSize: 11.sp),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          )
                                                                        : Column(
                                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                                            children: <Widget>[
                                                                              GestureDetector(
                                                                                onTap: () async {
                                                                                  final check = await QualityControlFunction.pickImage(context);
                                                                                  debugPrint(check.toString());
                                                                                  if (check != null) {
                                                                                    bool checkPermission =
                                                                                        await ImageFunction.handlePermission(check);
                                                                                    if (checkPermission == true) {
                                                                                      if (check == true) {
                                                                                        List<XFile>? selectedImages =
                                                                                            await _pickerImage.pickMultiImage(
                                                                                          maxWidth: globalImageConfig.maxWidth,
                                                                                          maxHeight: globalImageConfig.maxHeight,
                                                                                          imageQuality: globalImageConfig.imageQuality,
                                                                                        );
                                                                                        if (selectedImages.isEmpty) return;
                                                                                        for (var i in selectedImages) {
                                                                                          final itemImage =
                                                                                              await ImageFunction.saveImageMulti(i.path);
                                                                                          _pickFileImageErrorQuality(MultiSelectImageErrorQuality(
                                                                                              index: index, file: itemImage));
                                                                                        }
                                                                                      } else {
                                                                                        final image = await ImagePicker().pickImage(
                                                                                            maxWidth: globalImageConfig.maxWidth,
                                                                                            maxHeight: globalImageConfig.maxHeight,
                                                                                            imageQuality: globalImageConfig.imageQuality,
                                                                                            source: ImageSource.camera);
                                                                                        if (image == null) return;
                                                                                        final imageProfile =
                                                                                            await ImageFunction.saveImage(image.path);
                                                                                        _pickFileImageErrorQuality(MultiSelectImageErrorQuality(
                                                                                            index: index, file: imageProfile));
                                                                                      }
                                                                                    }
                                                                                  }
                                                                                },
                                                                                child: Container(
                                                                                  padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                                                                  decoration: BoxDecoration(
                                                                                    borderRadius: BorderRadius.circular(10.r),
                                                                                    color: Colors.grey.shade100,
                                                                                  ),
                                                                                  child: Text(
                                                                                    "Chọn tệp",
                                                                                    style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                              SizedBox(height: 10.h),
                                                                              Wrap(
                                                                                spacing: 3.w,
                                                                                runSpacing: 3.h,
                                                                                children: List.generate(
                                                                                  _lsGetFileError[index].length,
                                                                                  (indexImageError) {
                                                                                    // String filenameError = basename(
                                                                                    //     lsGetFileError[index]
                                                                                    //             [indexImageError]
                                                                                    //         .path);
                                                                                    return SizedBox(
                                                                                      width: 50.w,
                                                                                      child: Stack(
                                                                                        children: <Widget>[
                                                                                          GestureDetector(
                                                                                            onTap: () {
                                                                                              Navigator.push(
                                                                                                context,
                                                                                                MaterialPageRoute(
                                                                                                  builder: (context) => ImageQuatity(
                                                                                                      lsImage: _lsGetFileError[index],
                                                                                                      index: indexImageError),
                                                                                                ),
                                                                                              );
                                                                                            },
                                                                                            child: ListImagePicker(
                                                                                                fileImage: _lsGetFileError[index][indexImageError]),
                                                                                          ),
                                                                                          Align(
                                                                                            alignment: Alignment.topRight,
                                                                                            child: IconButton(
                                                                                              highlightColor: Colors.transparent,
                                                                                              hoverColor: Colors.transparent,
                                                                                              constraints: const BoxConstraints(),
                                                                                              iconSize: 17.sp,
                                                                                              color: Colors.red.shade800,
                                                                                              icon: const Icon(Icons.remove_circle),
                                                                                              onPressed: () {
                                                                                                _deleteListFileError(MultiDeleteImageErrorQuality(
                                                                                                    index: index, indexImageError: indexImageError));
                                                                                              },
                                                                                            ),
                                                                                          )
                                                                                        ],
                                                                                      ),
                                                                                    );
                                                                                  },
                                                                                ),
                                                                              ),
                                                                              SizedBox(height: 10.h),
                                                                            ],
                                                                          ),
                                                                  ),
                                                                  Visibility(
                                                                    visible: (_lsError[index].errorFileViewModel ?? []).isNotEmpty ? true : false,
                                                                    child: Expanded(
                                                                      flex: 2,
                                                                      child: GestureDetector(
                                                                        onTap: () {
                                                                          String title = "";
                                                                          String error = _lsSelectedError[index] == null
                                                                              ? ""
                                                                              : _lsSelectedError[index]!.catalogTextVi.toString();
                                                                          int indexErrorName = error.indexOf('|');
                                                                          title = _lsSelectedError[index] == null
                                                                              ? ""
                                                                              : _lsSelectedError[index]!
                                                                                  .catalogTextVi
                                                                                  .toString()
                                                                                  .substring(indexErrorName + 2)
                                                                                  .toUpperCase();
                                                                          // String error = lsSelectedError[index]!.catalogTextVi.toString();
                                                                          showDialog(
                                                                            context: context,
                                                                            builder: (BuildContext context) {
                                                                              return DialogErrorQuality(
                                                                                  title: 'HÌNH ẢNH LỖI ' + title,
                                                                                  listImage: _lsError[index].errorFileViewModel ?? []);
                                                                            },
                                                                          );
                                                                        },
                                                                        child: Container(
                                                                          padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                                                                          decoration: BoxDecoration(
                                                                            color: const Color(0xff0052cc),
                                                                            borderRadius: BorderRadius.all(
                                                                              Radius.circular(2.r),
                                                                            ),
                                                                          ),
                                                                          child: Icon(
                                                                            Icons.image,
                                                                            color: Colors.white,
                                                                            size: 15.sp,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  )
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                          Visibility(
                                                            visible: _checkVisiButtonError[index],
                                                            child: IntrinsicHeight(
                                                              child: Container(
                                                                decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                ),
                                                                child: Align(
                                                                  alignment: Alignment.topRight,
                                                                  child: IconButton(
                                                                    highlightColor: Colors.transparent,
                                                                    hoverColor: Colors.transparent,
                                                                    constraints: const BoxConstraints(),
                                                                    iconSize: 17.sp,
                                                                    color: Colors.red.shade800,
                                                                    icon: const Icon(Icons.delete),
                                                                    onPressed: () {
                                                                      _deleteItemListError(index);
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ))),
                            ButtonAddNewCardError(addNewCardError: _addNewCardError, qualityControl: _qualityControl),
                            SizedBox(height: 15.h),
                          ],
                        ),
                      ),
                      // Start of Selection
                      SizedBox(height: 15.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 4, child: QualityTitleField(title: "Kết quả")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 6,
                              child: Column(
                                children: <Widget>[
                                  DropdownDetailResult(
                                    getSelectedResultDetail: _getSelectedResultDetail,
                                    selectedResultDetail: _selectedResultDetail,
                                    lsResultList: _lsResultList,
                                    qualityControl: _qualityControl,
                                  ),
                                  SizedBox(height: _errorResultCheckDetail ? 10.h : 0),
                                  QualityErrorValidate(
                                    text: "Vui lòng chọn kết quả",
                                    error: _errorResultCheckDetail,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Start of Selection
                      SizedBox(height: 15.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 4, child: QualityTitleField(title: "Số lượng nhập kho")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 6,
                              child: Column(
                                children: <Widget>[
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: TextFormField(
                                      controller: _controllerSoLuongNhapKho,
                                      keyboardType: TextInputType.number,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintText: "Nhập số lượng nhập kho",
                                      ),
                                      onChanged: (value) {
                                        // Handle change if necessary
                                      },
                                    ),
                                  ),
                                  SizedBox(height: _errorSoLuongNhapKho ? 10.h : 0),
                                  QualityErrorValidate(
                                    text: "Vui lòng nhập số lượng nhập kho",
                                    error: _errorSoLuongNhapKho,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 15.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 4, child: QualityTitleField(title: "Số lượng block")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 6,
                              child: Column(
                                children: <Widget>[
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: TextFormField(
                                      controller: _controllerSoLuongBlock,
                                      keyboardType: TextInputType.number,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintText: "Nhập số lượng block",
                                      ),
                                      onChanged: (value) {
                                        // Handle change if necessary
                                      },
                                    ),
                                  ),
                                  SizedBox(height: _errorSoLuongBlock ? 10.h : 0),
                                  QualityErrorValidate(
                                    text: "Vui lòng nhập số lượng block",
                                    error: _errorSoLuongBlock,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 15.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 4, child: QualityTitleField(title: "Số lượng trả về")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 6,
                              child: Column(
                                children: <Widget>[
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(3.r),
                                    ),
                                    child: TextFormField(
                                      controller: _controllerSoLuongTraVe,
                                      keyboardType: TextInputType.number,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintText: "Nhập số lượng trả về",
                                      ),
                                      onChanged: (value) {
                                        // Handle change if necessary
                                      },
                                    ),
                                  ),
                                  SizedBox(height: _errorSoLuongTraVe ? 10.h : 0),
                                  QualityErrorValidate(
                                    text: "Vui lòng nhập số lượng trả về",
                                    error: _errorSoLuongTraVe,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 15.h),
                      GestureDetector(
                        onTap: isNVLDoneQC()
                            ? null
                            : () {
                                submitData();
                              },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 15.h),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5.r),
                            ),
                            color: isNVLDoneQC() ? Colors.grey.shade400 : const Color(0xff0052cc),
                          ),
                          child: Center(
                            child: Text(
                              'Submit',
                              style: TextStyle(color: Colors.white, fontSize: 12.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 15.h),
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }

  Widget itemHeaderText(int flexNum, String text) {
    return Expanded(
      flex: flexNum,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
        ),
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 2.w),
        child: Text(
          text,
          style: TextStyle(fontSize: 9.sp, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  IntrinsicHeight itemHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          itemHeaderText(1, "STT"),
          itemHeaderText(5, "Hạng mục kiểm tra"),
          itemHeaderText(3, "SL sản phẩm lỗi"),
          itemHeaderText(4, "Ghi chú"),
        ],
      ),
    );
  }
}

class _ErrorView extends StatelessWidget {
  const _ErrorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)));
  }
}

class _ListNotFoundView extends StatelessWidget {
  const _ListNotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text("Không tìm thấy thông tin phiếu kiểm tra!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
  }
}
