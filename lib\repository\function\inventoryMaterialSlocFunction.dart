import 'dart:convert';

import '../../model/InventoryMaterialSloc.dart';
import '../api/getInventoryMaterialSlocApi.dart';

class InventoryMaterialSlocFunction{
  static Future<List<DataInventoryMaterialSloc>?> fetchInventoryMaterialSloc(String productCode,String sloc,String token) async{
    final response = await GetInventoryMaterialSlocApi.postInventoryMaterialSloc(productCode,sloc,token);
    if (response.statusCode == 200) {
      final responseInventoryMaterialSloc = jsonDecode(response.body);
        final getInventoryMaterialSloc = GetInventoryMaterialSloc.fromJson(responseInventoryMaterialSloc);
        if (getInventoryMaterialSloc.code == 200) {
          return getInventoryMaterialSloc.data;
        } else {
          return [];
        }
    } else {
      throw(response.reasonPhrase.toString());
    }
  }
}