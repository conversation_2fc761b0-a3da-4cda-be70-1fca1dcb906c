*&---------------------------------------------------------------------*
*& Include          ZPP_IN_BCDMTP_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*&      Form  FILL_DATA
*&---------------------------------------------------------------------*
*       Fill main data from database
*----------------------------------------------------------------------*
form fill_data .

  data: lt_r_maktx type range of matnr.
  data: ls_r_maktx  like line of lt_r_maktx.
  field-symbols:
    <lf_main> type gty_main,
    <lf_stko> type gty_stko.


* Set value for S_MATNR if S_MATNR is initial (to increase Performance)
  if s_matnr is initial.
    select min( matnr )
    into   s_matnr-low
    from   mara.

    select max( matnr )
    into   s_matnr-high
    from   mara.

    s_matnr-sign   = 'I'.
    s_matnr-option = 'BT'.
    append s_matnr.
  endif.

* Set value for S_MATKL if S_MATKL is initial (to increase Performance)
  if s_matkl is initial.
    select min( matkl )
    into   s_matkl-low
    from   mara.

    select max( matkl )
    into   s_matkl-high
    from   mara.

    s_matkl-sign   = 'I'.
    s_matkl-option = 'BT'.
    append s_matkl.
  endif.

* Set value for S_MTART if S_MTART is initial (to increase Performance)
  if s_mtart is initial.
    select min( mtart )
    into   s_mtart-low
    from   mara.

    select max( mtart )
    into   s_mtart-high
    from   mara.

    s_mtart-sign   = 'I'.
    s_mtart-option = 'BT'.
    append s_mtart.
  endif.

* Set value for S_IDNRK if S_IDNRK is initial (to increase Performance)
  if s_idnrk is initial.
    select min( idnrk )
    into   s_idnrk-low
    from   stpo.

    select max( idnrk )
    into   s_idnrk-high
    from   stpo.

    s_idnrk-sign   = 'I'.
    s_idnrk-option = 'BT'.
    append s_idnrk.
  endif.

  if p_xk is not initial.
* Get data from MAST, STKO and STPO
    select mast~matnr
           mast~stlan
           mast~stlal
           mast~stlnr
           mara~meins
           mast~werks
           stko~datuv
           stko~andat
           stko~annam
           stko~aedat
           stko~aenam
           stko~bmein
           stko~bmeng
*           STKO~STLST
           stpo~idnrk
           stpo~posnr
           stpo~meins
           stpo~menge
           stpo~fmeng
           stpo~ausch
           stpo~stkkz
           stpo~potx1
           stpo~potx2
           mara~z_duan
           stpo~aedat as datec
           stpo~vgknt
           stpo~stlkn
           stas~lkenz       "KhanDV update 04.05.2021
           stas~dvdat       "end
*           MAST~STLNR
*        DuyNNM - 18.01.2019 - Delete - Start
*           VBAP~VBELN
*        DuyNNM - 18.01.2019 - Delete - End
    into corresponding fields of   table gt_main
    from   mast
*        DuyNNM - 18.01.2019 - Delete - Start
*    INNER JOIN VBAP ON MAST~MATNR = VBAP~MATNR
*        DuyNNM - 18.01.2019 - Delete - End
    inner join mara on mast~matnr = mara~matnr
    inner  join stko on mast~stlnr = stko~stlnr
                    and mast~stlal = stko~stlal
    inner  join stas on stko~stlty = stas~stlty
                    and stko~stlnr = stas~stlnr
                    and stko~stlal = stas~stlal
    inner  join stpo on stas~stlty = stpo~stlty
                    and stas~stlnr = stpo~stlnr
                    and stas~stlkn = stpo~stlkn
    where  mast~matnr in s_matnr
    and    mast~werks in  s_werks
*        DuyNNM - 18.01.2019 - Delete - Start
*    AND    VBAP~VBELN IN S_VBELN
*        DuyNNM - 18.01.2019 - Delete - End
    and    mara~mtart in s_mtart
    and    mara~matkl in s_matkl
    and    mast~andat in s_andat
    and    stpo~datuv in s_datuv
    and    mast~stlan in s_stlan
    and    mast~stlal in s_stlal
    and    stpo~idnrk in s_idnrk
    and    stpo~sanin = ' '             "THINHPH
    and    mara~z_duan in s_duan ##DB_FEATURE_MODE[TABLE_LEN_MAX1].
  elseif p_ttda is not initial.
    select mast~matnr
         mast~stlan
         mast~stlal
         mast~stlnr
         mast~werks
         mara~meins
         stko~datuv
         stko~andat
         stko~annam
         stko~aedat
         stko~aenam
         stko~bmein
         stko~bmeng
*         STKO~STLST
         stpo~idnrk
         stpo~posnr
         stpo~meins
         stpo~menge
         stpo~fmeng
         stpo~ausch
         stpo~stkkz
         stpo~potx1
         stpo~potx2
         mara~z_duan
         stpo~aedat as datec
         stpo~vgknt
         stpo~stlkn
         stas~lkenz     "KhanDV update 04.05.2021
         stas~dvdat     "end
*         MAST~STLNR
*        DuyNNM - 18.01.2019 - Delete - Start
*         VBAP~VBELN
*        DuyNNM - 18.01.2019 - Delete - End
    into corresponding fields of   table gt_main
    from   mast
*        DuyNNM - 18.01.2019 - Delete - Start
*    LEFT JOIN VBAP ON MAST~MATNR = VBAP~MATNR
*        DuyNNM - 18.01.2019 - Delete - End
    inner join mara on mast~matnr = mara~matnr
    inner  join stko on mast~stlnr = stko~stlnr
                    and mast~stlal = stko~stlal
    inner  join stas on stko~stlty = stas~stlty
                    and stko~stlnr = stas~stlnr
                    and stko~stlal = stas~stlal
    inner  join stpo on stas~stlty = stpo~stlty
                    and stas~stlnr = stpo~stlnr
                    and stas~stlkn = stpo~stlkn
    where  mast~matnr in s_matnr
    and    mast~werks in s_werks
    and    mara~mtart in s_mtart
    and    mara~matkl in s_matkl
    and    mast~stlan in s_stlan
    and    mast~stlal in s_stlal
    and    mast~andat in s_andat
    and    stpo~datuv in s_datuv
    and    stpo~idnrk in s_idnrk
    and    stpo~sanin = ' '             "THINHPH
    and    mara~z_duan in s_duan ##DB_FEATURE_MODE[TABLE_LEN_MAX1].
  endif.
  "KhangDV update component delete start 04.05.2021

  break abapleader.
  select *
  from stko
  into corresponding fields of   table gt_stko2
  where stlal in s_stlal.
*    AND STKTX <> ' '.

  sort gt_stko2 by datuv.

*  DELETE GT_MAIN WHERE AEDAT IS NOT INITIAL AND AENAM IS NOT INITIAL.
  sort gt_main by stlnr stlal stlkn werks.
  loop at gt_main into gs_main.
    loop at gt_main into data(lw_m) where stlnr = gs_main-stlnr
                                      and stlal = gs_main-stlal
                                      and stlkn = gs_main-stlkn
                                      and werks = gs_main-werks
                                      and lkenz = 'X'
                                      and ( dvdat <> '00000000' or dvdat <> '' ).
      delete gt_main where stlnr = gs_main-stlnr and stlkn = gs_main-stlkn and stlan = gs_main-stlan and stlal = gs_main-stlal and werks = gs_main-werks.
    endloop.
  endloop.


  loop at gt_main into gs_main.
    loop at gt_main into data(lw_m2) where stlnr = gs_main-stlnr
                                      and stlal = gs_main-stlal
                                      and stlkn = gs_main-stlkn
                                      and werks = gs_main-werks.
*      DELETE GT_MAIN WHERE STLNR = GS_MAIN-STLNR AND DATUV < GS_MAIN-DATUV AND AENAM <> GS_MAIN-AENAM AND ANDAT = GS_MAIN-ANDAT .
*      DELETE GT_MAIN WHERE STLNR = GS_MAIN-STLNR AND DATUV <> GS_MAIN-ANDAT AND DATUV < GS_MAIN-DATUV  .
      delete gt_main where stlnr = gs_main-stlnr and datuv <> gs_main-andat and datuv < gs_main-datuv and stlal = gs_main-stlal and werks = gs_main-werks.
    endloop.
  endloop.
  "end

  loop at gt_main into gs_main.
    read table gt_main into data(lw_main) with key stlnr = gs_main-stlnr
                                                   vgknt = gs_main-stlkn
                                                   stlal = gs_main-stlal. " TramNB6/24.02.2025/ Update
    if sy-subrc is initial.
      delete gt_main where stlnr = gs_main-stlnr and stlkn = gs_main-stlkn.
    endif.
  endloop.

  if gt_main[] is not initial.
    select stlty
           stlnr
      into table gt_stko
      from stko
      for all entries in gt_main
    where stlnr = gt_main-stlnr.
    if sy-subrc is initial.
      loop at gt_stko assigning <lf_stko>.
        concatenate sy-mandt
                    <lf_stko>-stlty
                    <lf_stko>-stlnr
               into <lf_stko>-objectid.
      endloop.
      sort gt_stko by stlnr.
    endif.

    if gt_stko[] is not initial.
      select *
        from cdhdr
        into table gt_cdhdr
      for all entries in gt_stko
      where objectid = gt_stko-objectid.
      if sy-subrc is initial.
        sort gt_cdhdr by objectid ascending udate descending.
      endif.
    endif.
  endif.

* Set material range table
  ls_r_maktx-sign    = 'I'.
  ls_r_maktx-option  = 'EQ'.

  loop at gt_main assigning <lf_main>.
    ls_r_maktx-low = <lf_main>-matnr.
    append ls_r_maktx to lt_r_maktx.

    ls_r_maktx-low = <lf_main>-idnrk.
    append ls_r_maktx to lt_r_maktx.
  endloop.

  sort lt_r_maktx by low.
  delete adjacent duplicates from lt_r_maktx comparing low.

* Get material text from MAKT
  select *
  into   table gt_makt
  from   makt
  where  matnr in lt_r_maktx
  and    spras =  sy-langu.

* Get Price control indicator from MBEW
  select mbew~bwkey as werks
         mbew~matnr
         mbew~vprsv
         stprs
         verpr
         mara~meins
  into   table gt_mbew
  from   mbew
  inner  join t001w on mbew~bwkey = t001w~bwkey
  inner  join mara  on mbew~matnr = mara~matnr
  where  mbew~matnr in lt_r_maktx
  and    t001w~werks in s_werks.

  sort gt_main by matnr  stlal posnr.
  delete adjacent duplicates from gt_main comparing matnr  stlal posnr.
endform.
*&---------------------------------------------------------------------*
*&      Form  PROCESS_DATA
*&---------------------------------------------------------------------*
*       Process gotten data
*----------------------------------------------------------------------*
form process_data .

  data: gt_return type table of bapiret2   .
  data: ls_alv    type zpp_st_bcdmtp.
  data: lw_menge type ekpo-menge,
        ls_stko  type gty_stko,
        ls_cdhdr type gty_cdhdr.
  field-symbols:
    <lf_s_main> type gty_main,
    <lf_makt>   type makt,
    <lf_mbew>   type gty_mbew.

  data: lw_material type bapi1080_mbm_c-material.


  sort gt_makt by matnr.
  sort gt_mbew by matnr.
  data: lv_max type stko-stlal.

  "Tuanpt17/11.06.2021/update ct 29 start"
  loop at gt_main into gs_main.
*         SELECT
*         MAX( STLAL )
*         FROM STKO
*         INTO @LV_MAX
*         WHERE STLNR  = @GS_MAIN-STLNR .
    loop at gt_stko2 into gs_stko2 .
*        IF   LV_MAX = GS_STKO2-STLAL  AND GS_MAIN-STLNR = GS_STKO2-STLNR AND GS_MAIN-BMEIN = GS_STKO2-BMEIN AND GS_MAIN-BMENG = GS_STKO2-BMENG .
      if  gs_main-stlal = gs_stko2-stlal and gs_main-datuv = gs_stko2-datuv and gs_main-bmein = gs_stko2-bmein and gs_main-bmeng = gs_stko2-bmeng .
        append gs_stko2 to gt_stko4.
      endif.
    endloop.
  endloop.
  break abapleader.

  sort gt_stko4 by stlal.
  "KhangDV rem 15.10.2021 start
*  LOOP AT GT_MAIN INTO GS_MAIN.

*    LOOP AT GT_STKO4 INTO GS_STKO4 .
*      IF GS_MAIN-STLAL = GS_STKO4-STLAL AND GS_MAIN-DATUV = GS_STKO4-DATUV  .
*         GS_MAIN-STKTX = GS_STKO4-STKTX.
*         GS_MAIN-BMENG = GS_STKO4-BMENG.
*         GS_MAIN-STLST = GS_STKO4-STLST.
*         MODIFY GT_MAIN FROM GS_MAIN.
*      ENDIF.
*    ENDLOOP .\
  "end
  "KhangDV add 15.10.2021 start
  loop at gt_main assigning field-symbol(<fs_main>).
    read table gt_stko4 into gs_stko4 with key stlal = <fs_main>-stlal datuv = <fs_main>-datuv stlnr = <fs_main>-stlnr.
    if sy-subrc is initial.
      <fs_main>-stktx = gs_stko4-stktx.
      <fs_main>-bmeng = gs_stko4-bmeng.
      <fs_main>-stlst = gs_stko4-stlst.
    endif.
  endloop.
  "end
*
*  BREAK ABAPLEADER.
*
*  LOOP AT GT_MAIN INTO GS_MAIN.
*    LOOP AT GT_STKO3 INTO GS_STKO3 .
**      IF GS_MAIN-STLNR = GS_STKO3-STLNR AND  GS_MAIN-STLAL = GS_STKO3-STLAL AND GS_STKO3-STLAL <> GS_STKO3-STLST.
*      IF  GS_MAIN-STLAL = GS_STKO3-STLAL .
*        GS_MAIN-STLST = GS_STKO3-STLST.
*      ENDIF.
*
*      IF GS_MAIN-STLST = ''.
**        GS_MAIN-STLST = 1.
*      ENDIF.
*      MODIFY GT_MAIN FROM GS_MAIN.
*    ENDLOOP .
*  ENDLOOP.

  "Tuanpt17/11.06.2021/update ct 29 end "

  loop at gt_main assigning <lf_s_main>.
    clear ls_alv.
    "Move data from GT_MAIN to GT_ALV
    move-corresponding <lf_s_main> to ls_alv.

    split ls_alv-potx1 at '-' into ls_alv-slct data(lv_qcach) ls_alv-taophoi ls_alv-zghichu.
    split lv_qcach at 'x' into ls_alv-day ls_alv-rong ls_alv-dai.
    clear lv_qcach.
    "thinhph
    "ls_alv-werks = <lf_s_main>-werks.

    "Set Material Name
    read table gt_makt assigning <lf_makt> binary search
    with key matnr = <lf_s_main>-matnr.
    if sy-subrc = 0.
      ls_alv-maktx = <lf_makt>-maktx.
    endif.

    "Set component name
    read table gt_makt assigning <lf_makt> binary search
    with key matnr = <lf_s_main>-idnrk.
    if sy-subrc = 0.
      ls_alv-maktx_comp = <lf_makt>-maktx.
    endif.

    "Set Price control indicator
    read table gt_mbew assigning <lf_mbew> binary search
    with key matnr = <lf_s_main>-matnr werks = <lf_s_main>-werks.
    if sy-subrc = 0.
      ls_alv-vprsv = <lf_mbew>-vprsv.
      ls_alv-verpr = <lf_mbew>-stprs."Periodic Unit Price
    endif.

    read table gt_mbew assigning <lf_mbew> binary search
    with key matnr = <lf_s_main>-idnrk werks = <lf_s_main>-werks.
    if sy-subrc = 0.
      if <lf_mbew>-vprsv eq 'S'.
        ls_alv-stprs = <lf_mbew>-stprs."Standard price
      else.
        ls_alv-stprs = <lf_mbew>-verpr."Standard price
      endif.

      "Calculate Price Comp. by BOM Unit
      if  <lf_mbew>-meins <> <lf_s_main>-meins
      and ls_alv-stprs is not initial.
        call function 'MD_CONVERT_MATERIAL_UNIT'
          exporting
            i_matnr              = <lf_s_main>-idnrk
            i_in_me              = <lf_mbew>-meins
            i_out_me             = <lf_s_main>-meins
            i_menge              = 1
          importing
            e_menge              = lw_menge
          exceptions
            error_in_application = 1
            error                = 2
            others               = 3.

        if sy-subrc = 0.
          if lw_menge <> 0.
            ls_alv-stprs = ls_alv-stprs / lw_menge.
          endif.
        endif.
      endif.

      ls_alv-thanh_tien = ls_alv-menge * ls_alv-stprs  ."Standard price
      ls_alv-waers = 'VND'.
    endif.

    refresh gt_return.

    clear: lw_material.
    lw_material = ls_alv-idnrk.
    "thinhph
    call function 'BAPI_MAT_BOM_EXISTENCE_CHECK'
      exporting
        material = lw_material
        plant    = <lf_s_main>-werks
        bomusage = '1'
      tables
        return   = gt_return.

    if gt_return[] is  initial.
      ls_alv-stlkz = 'X'.
    endif.

    "Set Low-Level BOM & False
*    IF CB_MTBOM = 'X'.
*      IF  LS_ALV-VPRSV = 'S'
*      AND LS_ALV-STLKZ <> 'X'.
*        LS_ALV-LL_BOM = 'F'.
*      ENDIF.
*
*      IF LS_ALV-IDNRK IN GT_R_MATNR.
*        LS_ALV-FALSE  = 'X'.
*      ENDIF.
*    ENDIF.

    read table gt_stko into ls_stko
       with key stlnr = <lf_s_main>-stlnr
       binary search.
    if sy-subrc is initial.
      read table gt_cdhdr into ls_cdhdr
          with key objectid = ls_stko-objectid
          binary search.
      if sy-subrc is initial.
        <lf_s_main>-aedat = ls_cdhdr-udate.
        <lf_s_main>-aenam = ls_cdhdr-username.

        ls_alv-aedat = <lf_s_main>-aedat.
        ls_alv-aenam = <lf_s_main>-aenam.
      endif.
    endif.

    ls_alv-bomtext = ls_alv-stktx ."Tuanpt17/11.06.2021/update ct 29 start"
    ls_alv-bmeng = ls_alv-bmeng ."Tuanpt17/1.07.2021/update ct 7 start"
    ls_alv-stlst = ls_alv-stlst ."Tuanpt17/11.06.2021/update ct 6 start"

    if ls_alv-idnrk cp '*THIEU*'.
      ls_alv-color = 'C610'.
    endif.
    append ls_alv to gt_alv.
    clear ls_alv.
  endloop.

  sort gt_alv by matnr  stlal posnr.



endform.
*&---------------------------------------------------------------------*
*&      Form  DISPLAY_DATA
*&---------------------------------------------------------------------*
*       Display data
*----------------------------------------------------------------------*
form display_data .

  data:lw_variant type disvariant.


  perform build_fieldcat.
  perform build_layout.


  call function 'REUSE_ALV_GRID_DISPLAY_LVC'
    exporting
      i_callback_program       = sy-repid
      i_callback_pf_status_set = 'SET_PF_STATUS'
      i_callback_user_command  = 'HANDLE_USER_COMMAND'
      is_layout_lvc            = gs_layout
      it_fieldcat_lvc          = gt_fieldcat
      i_save                   = 'A'
      is_variant               = lw_variant
    tables
      t_outtab                 = gt_alv
    exceptions
      program_error            = 1
      others                   = 2.

  if sy-subrc <> 0.
    message id sy-msgid type sy-msgty number sy-msgno
          with sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  endif.

endform.
*&---------------------------------------------------------------------*
*&      Form  BUILD_FIELDCAT
*&---------------------------------------------------------------------*
*       Build ALV field catalog
*----------------------------------------------------------------------*
form build_fieldcat .

  data: lw_price_author   type c.
  field-symbols: <lf_s_fieldcat> type lvc_s_fcat.


  call function 'LVC_FIELDCATALOG_MERGE'
    exporting
      i_structure_name       = 'ZPP_ST_BCDMTP'
    changing
      ct_fieldcat            = gt_fieldcat[]
    exceptions
      inconsistent_interface = 1
      program_error          = 2
      others                 = 3.

  if sy-subrc <> 0.
    message id sy-msgid type sy-msgty number sy-msgno
          with sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  endif.

* Check authorization
  authority-check object 'ZBOM_PRICE'
           id 'ATAUTH' field '*'
           id 'ACTVT' field '03'.
  if sy-subrc = 0.
    lw_price_author = 'X'.
  endif.


  loop at gt_fieldcat assigning <lf_s_fieldcat>.
    case <lf_s_fieldcat>-fieldname.
      when 'WERKS'.
        <lf_s_fieldcat>-reptext     = text-001.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-001.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'STLAN'.
        <lf_s_fieldcat>-reptext     = text-002.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-002.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
*        DuyNNM - 18.01.2019 - Replace - Start
      when 'Z_DUAN' or 'DAY' or 'RONG' or 'DAI' or 'POTX2' or 'SLCT' or 'TAOPHOI' ."OR 'VBELN'.
*        DuyNNM - 18.01.2019 - Replace - End
      when 'MATNR'.
        <lf_s_fieldcat>-reptext     = text-003.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-003.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'MAKTX'.
        <lf_s_fieldcat>-reptext     = text-004.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-004.
        <lf_s_fieldcat>-just        = 'L'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'STLAL'.
        <lf_s_fieldcat>-reptext     = text-005.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-005.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'STLST'.
        <lf_s_fieldcat>-reptext     = text-006.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-006.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'BMENG'.
        <lf_s_fieldcat>-reptext     = text-007.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-007.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-qfieldname  = 'BMEIN'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'BMEIN'.
        <lf_s_fieldcat>-reptext     = text-008.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-008.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'VERPR'.
        <lf_s_fieldcat>-reptext     = text-021.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-021.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
        <lf_s_fieldcat>-cfieldname  = 'WAERS'.

        if lw_price_author <> 'X'.
          <lf_s_fieldcat>-tech       = 'X'.
        endif.
      when 'DATUV'.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C510'.
      when 'POSNR'.
        <lf_s_fieldcat>-reptext     = text-009.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-009.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'IDNRK'.
        <lf_s_fieldcat>-reptext     = text-010.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-010.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'MAKTX_COMP'.
        <lf_s_fieldcat>-reptext     = text-011.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-011.
        <lf_s_fieldcat>-just        = 'L'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'MENGE'.
        <lf_s_fieldcat>-reptext     = text-012.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-012.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-qfieldname  = 'MEINS'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'MEINS'.
        <lf_s_fieldcat>-reptext     = text-013.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-013.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'STPRS'.
        <lf_s_fieldcat>-reptext     = text-022.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-022.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
        <lf_s_fieldcat>-cfieldname  = 'WAERS'.

        if lw_price_author <> 'X'.
          <lf_s_fieldcat>-tech       = 'X'.
        endif.
      when 'THANH_TIEN'.
        <lf_s_fieldcat>-reptext     = text-023.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-023.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-cfieldname  = 'WAERS'.
        <lf_s_fieldcat>-emphasize   = 'C310'.

        if lw_price_author <> 'X'.
          <lf_s_fieldcat>-tech       = 'X'.
        endif.
      when 'AUSCH'.
        <lf_s_fieldcat>-reptext     = text-014.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-014.
        <lf_s_fieldcat>-just        = 'R'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
*      WHEN 'POTX1'.
*        <LF_S_FIELDCAT>-REPTEXT     = TEXT-015.
*        <LF_S_FIELDCAT>-SCRTEXT_S   = <LF_S_FIELDCAT>-SCRTEXT_M
*        = <LF_S_FIELDCAT>-SCRTEXT_L = TEXT-015.
*        <LF_S_FIELDCAT>-JUST        = 'L'.
*        <LF_S_FIELDCAT>-EMPHASIZE   = 'C310'.
      when 'FMENG'.
        <lf_s_fieldcat>-reptext     = text-016.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-016.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'STLKZ'.
        <lf_s_fieldcat>-reptext     = text-017.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-017.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C310'.

      when 'BOMTEXT'.
        <lf_s_fieldcat>-reptext     = text-024.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-024.
        <lf_s_fieldcat>-just        = 'C'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
*      WHEN 'VPRSV'.
*        <LF_S_FIELDCAT>-REPTEXT     = TEXT-018.
*        <LF_S_FIELDCAT>-SCRTEXT_S   = <LF_S_FIELDCAT>-SCRTEXT_M
*        = <LF_S_FIELDCAT>-SCRTEXT_L = TEXT-018.
*        <LF_S_FIELDCAT>-JUST        = 'C'.
*        <LF_S_FIELDCAT>-EMPHASIZE   = 'C710'.
*      WHEN 'LL_BOM'.
*        <LF_S_FIELDCAT>-REPTEXT     = TEXT-019.
*        <LF_S_FIELDCAT>-SCRTEXT_S   = <LF_S_FIELDCAT>-SCRTEXT_M
*        = <LF_S_FIELDCAT>-SCRTEXT_L = TEXT-019.
*        <LF_S_FIELDCAT>-JUST        = 'C'.
*        <LF_S_FIELDCAT>-EMPHASIZE   = 'C710'.
**      WHEN 'FALSE'.
*        <LF_S_FIELDCAT>-REPTEXT     = TEXT-020.
*        <LF_S_FIELDCAT>-SCRTEXT_S   = <LF_S_FIELDCAT>-SCRTEXT_M
*        = <LF_S_FIELDCAT>-SCRTEXT_L = TEXT-020.
*        <LF_S_FIELDCAT>-JUST        = 'C'.
*        <LF_S_FIELDCAT>-EMPHASIZE   = 'C710'.
      when 'ZGHICHU'.
        <lf_s_fieldcat>-reptext     = text-025.
        <lf_s_fieldcat>-scrtext_s   = <lf_s_fieldcat>-scrtext_m
        = <lf_s_fieldcat>-scrtext_l = text-025.
        <lf_s_fieldcat>-just        = 'L'.
        <lf_s_fieldcat>-emphasize   = 'C310'.
      when 'ANDAT'.
        <lf_s_fieldcat>-just        = 'C'.
      when 'ANNAM'.
        <lf_s_fieldcat>-just        = 'C'.
      when 'AEDAT'.
        <lf_s_fieldcat>-just        = 'C'.
      when 'AENAM'.
        <lf_s_fieldcat>-just        = 'C'.
      when others.
        <lf_s_fieldcat>-tech = 'X'.
    endcase.
  endloop.

endform.
*&---------------------------------------------------------------------*
*&      Form  BUILD_LAYOUT
*&---------------------------------------------------------------------*
*       Build ALV Layout
*----------------------------------------------------------------------*
form build_layout .

  gs_layout-cwidth_opt   = 'X'.
  gs_layout-sel_mode = 'A'.
  gs_layout-info_fname = 'COLOR'.

endform.
*&---------------------------------------------------------------------*
*&      Form  SET_PF_STATUS
*&---------------------------------------------------------------------*
*       Set PF Status
*----------------------------------------------------------------------*
form set_pf_status using lpt_extab type slis_t_extab.

  set pf-status 'ZSTANDARD_FULLSCREEN' excluding lpt_extab.

endform.                    " SET_PF_STATUS
*&---------------------------------------------------------------------*
*&      Form  HANDLE_USER_COMMAND
*&---------------------------------------------------------------------*
form handle_user_command using lpw_ucomm     like sy-ucomm
                               lps_selfield  type slis_selfield.

  field-symbols:
    <lf_alv>    type zpp_st_bcdmtp.


  case lpw_ucomm.
    when '&IC1'.
      unassign <lf_alv>.
      read table gt_alv assigning <lf_alv> index lps_selfield-tabindex.
      if <lf_alv> is assigned.
        set parameter id 'MAT' field <lf_alv>-matnr.
        set parameter id 'WRK' field <lf_alv>-werks.
        set parameter id 'CSV' field <lf_alv>-stlan.
        call transaction 'CS03' and skip first screen.
      endif.
  endcase.

endform.                    "HANDLE_USER_COMMAND
*&---------------------------------------------------------------------*
*& Form PBO
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form pbo .
  data lv_active type c.
*  DuyNNM - 18.01.2019 - Delete - Start
*  CASE 'X'.
*    WHEN P_XK.
*      LV_ACTIVE = '1'.
*    WHEN OTHERS.
  lv_active = '0'.
*  ENDCASE.
*  DuyNNM - 18.01.2019 - Delete - End
  loop at screen.
    check screen-group1 is not initial.
    case screen-group1.
      when 'E01'.
        screen-active = lv_active.
        screen-invisible = lv_active.
      when 'GR1'.
        screen-active = lv_active.
        screen-invisible = lv_active.
    endcase.
    modify screen.
  endloop.
endform.