﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("CollectingAuthorityModel", Schema = "ghMasterData")]
    public partial class CollectingAuthorityModel
    {
        [Key]
        public Guid CollectingAuthorityId { get; set; }
        [StringLength(50)]
        public string CollectingAuthorityCode { get; set; }
        [StringLength(200)]
        public string CollectingAuthorityName { get; set; }
        public bool? Actived { get; set; }
    }
}