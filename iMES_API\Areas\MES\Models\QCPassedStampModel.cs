using Newtonsoft.Json;

namespace iMES_API.Areas.MES.Models
{
    public class QCPassedStampModel
    {
        [JsonProperty("po")]
        public string Po { get; set; }

        [JsonProperty("productCode")]
        public string ProductCode { get; set; }

        [JsonProperty("productName")]
        public string ProductName { get; set; }

        [JsonProperty("lsxSap")]
        public string LsxSap { get; set; }

        [JsonProperty("serial")]
        public string Serial { get; set; }

        [JsonProperty("qualityControlTime")]
        public string QualityControlTime { get; set; }

        [JsonProperty("workshop")]
        public string Workshop { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("dateOfManufacture")]
        public string DateOfManufacture { get; set; }

        [JsonProperty("placeOfManufacture")]
        public string PlaceOfManufacture { get; set; }

        [<PERSON>sonProperty("poKhach")]
        public string <PERSON>Khach { get; set; }
    }
}