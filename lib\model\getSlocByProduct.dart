class GetSlocByProduct {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetSlocByProduct>? data;


  GetSlocByProduct(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetSlocByProduct.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetSlocByProduct>[];
      json['data'].forEach((v) {
        data!.add(DataGetSlocByProduct.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetSlocByProduct {
  String? id;
  String? sloc;

  DataGetSlocByProduct({this.id, this.sloc});

  DataGetSlocByProduct.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    sloc = json['sloc'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['sloc'] = sloc;
    return data;
  }
}