﻿using iMES_API.Extensions;
using iMES_API.Filters;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using Microsoft.AspNetCore.Mvc;

namespace MP_CRM_API
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // services.Configure, services.AddControllers, services.AddDbContext, services.AddSwaggerGen, services.AddAutoMapper, services.AddHttpContextAccessor
            services.AddInfrastructure(Configuration);
            
            services.AddControllers(options =>
            {
                // Add AuditLogFilter as a global filter
                options.Filters.Add<AuditLogFilter>();
            });

            // Register MaiDao repository
            services.AddScoped<ISD.API.Repositories.Interfaces.IMaiDaoRepository, ISD.API.Repositories.Implementations.MaiDaoRepository>();

            // Enable Areas in MVC
            services.AddMvc().AddRazorPagesOptions(options => 
            {
                // Razor Pages options if needed
            }).SetCompatibilityVersion(CompatibilityVersion.Version_3_0);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHttpContextAccessor accessor, IServiceProvider serviceProvider, IConfiguration configuration)
        {
            app.UseInfrastructure(env, accessor, serviceProvider, configuration);

            // And make sure we're configuring endpoints with areas
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "areas",
                    pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}");
                
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
                
                endpoints.MapRazorPages();
            });
        }
    }
}
