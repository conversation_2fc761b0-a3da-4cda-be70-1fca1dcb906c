class PurchaseOrderDetail {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataPurchaseOrderDetail>? data;
  DataPurchaseOrderDetail? additionalData;

  PurchaseOrderDetail({this.code, this.isSuccess, this.message, this.data});

  PurchaseOrderDetail.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataPurchaseOrderDetail>[];
      json['data'].forEach((v) {
        data!.add(DataPurchaseOrderDetail.fromJson(v));
      });
    }
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (additionalData != null) {
      data['additionalData'] = additionalData!.toJson();
    }
    return data;
  }
}

class DataPurchaseOrderDetail {
  String? purchaseOrderDetailId;
  String? purchaseOrderId;
  String? purchaseOrderCode;
  String? poItem;
  int? poItemInt;
  String? plant;
  String? material;
  String? productCode;
  String? deletionInd;
  String? shortText;
  String? storageLocation;
  double? poQuantity;
  String? orderUnit;
  String? delivCompl;
  String? acctAssgmtCat;
  String? itemCategory;
  String? goodsReceipt;
  String? sdDocument;
  String? item;
  String? wbsElement;
  String? cumulativeQuantity;
  double? cumulativeQuantityInt;
  String? basicDataText;
  String? sso;

  DataPurchaseOrderDetail.fromJson(Map<String, dynamic> json) {
    purchaseOrderDetailId = json['purchaseOrderDetailId'];
    purchaseOrderId = json['purchaseOrderId'];
    purchaseOrderCode = json['purchaseOrderCode'];
    poItem = json['poItem'];
    poItemInt = json['poItemInt'];
    plant = json['plant'];
    material = json['material'];
    productCode = json['productCode'];
    deletionInd = json['deletionInd'];
    shortText = json['shortText'];
    storageLocation = json['storageLocation'];
    poQuantity = json['poQuantity'];
    orderUnit = json['orderUnit'];
    delivCompl = json['delivCompl'];
    acctAssgmtCat = json['acctAssgmtCat'];
    itemCategory = json['itemCategory'];
    goodsReceipt = json['goodsReceipt'];
    sdDocument = json['sdDocument'];
    item = json['item'];
    wbsElement = json['wbsElement'];
    cumulativeQuantity = json['cumulativeQuantity'];
    cumulativeQuantityInt = json['cumulativeQuantityInt'];
    basicDataText = json['basicDataText'];
    sso = json['sso'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['purchaseOrderDetailId'] = purchaseOrderDetailId;
    data['purchaseOrderId'] = purchaseOrderId;
    data['purchaseOrderCode'] = purchaseOrderCode;
    data['poItem'] = poItem;
    data['poItemInt'] = poItemInt;
    data['plant'] = plant;
    data['material'] = material;
    data['productCode'] = productCode;
    data['deletionInd'] = deletionInd;
    data['shortText'] = shortText;
    data['storageLocation'] = storageLocation;
    data['poQuantity'] = poQuantity;
    data['orderUnit'] = orderUnit;
    data['delivCompl'] = delivCompl;
    data['acctAssgmtCat'] = acctAssgmtCat;
    data['itemCategory'] = itemCategory;
    data['goodsReceipt'] = goodsReceipt;
    data['sdDocument'] = sdDocument;
    data['item'] = item;
    data['wbsElement'] = wbsElement;
    data['cumulativeQuantity'] = cumulativeQuantity;
    data['cumulativeQuantityInt'] = cumulativeQuantityInt;
    data['basicDataText'] = basicDataText;
    data['sso'] = sso;
    return data;
  }
}
