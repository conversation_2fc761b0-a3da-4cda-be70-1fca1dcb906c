class PostSuccessReceiveIntegrationSAP {
  int? code;
  bool? isSuccess;
  String? message;
  bool? data;


  PostSuccessReceiveIntegrationSAP(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  PostSuccessReceiveIntegrationSAP.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['isSuccess'] = this.isSuccess;
    data['message'] = this.message;
    data['data'] = this.data;
    return data;
  }
}