*&---------------------------------------------------------------------*
*& Include          ZPG_PPR01_SEL
*&---------------------------------------------------------------------*
SELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.

SELECT-OPTIONS: "s_bukrs FOR aufk-bukrs,
                s_werks FOR aufk-werks OBLIGATORY DEFAULT '1000',

*                s_matkl FOR mara-matkl,
                s_gltri FOR afko-gltri,
                s_zzlsx FOR afko-zzlsx,
                s_aufnr FOR afko-aufnr,
                s_auart FOR aufk-auart NO-DISPLAY,
                s_vbeln FOR vbak-vbeln,
                s_matnr FOR mara-matnr,
                s_compo FOR stpo-idnrk.

SELECT-OPTIONS:
                s_datum FOR sy-datum NO-EXTENSION NO INTERVALS DEFAULT sy-datum NO-DISPLAY.

SELECTION-SCREEN BEGIN OF LINE.
PARAMETERS: p_ct TYPE c1 RADIOBUTTON GROUP rbt1 DEFAULT 'X' USER-COMMAND rb_com.
SELECTION-SCREEN COMMENT 5(25) TEXT-002 FOR FIELD p_ct.
PARAMETERS: p_dt TYPE c1 RADIOBUTTON GROUP rbt1.
SELECTION-SCREEN COMMENT 40(30) TEXT-003 FOR FIELD p_dt.

SELECTION-SCREEN END OF LINE.
PARAMETERS: p_ut TYPE xchar DEFAULT ''.
PARAMETER: p_sb TYPE xchar NO-DISPLAY.
SELECTION-SCREEN END OF BLOCK b1.