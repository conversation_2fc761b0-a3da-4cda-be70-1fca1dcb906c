import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetStatusGoodsArriveApi {
  static Future<http.Response> getStatusGoodsArriveApi(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlCommon_2 + "GetStatusGoodsArrive");
    debugPrint(url.toString());
    final response = await http.get(
      url,
      headers: UrlApi.headersToken(token),
    );
    // debugPrint(response.body);
    // debugPrint(dataPut.toString());
    return response;
  }
}
