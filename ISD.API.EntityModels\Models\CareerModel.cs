﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("CareerModel", Schema = "ghMasterData")]
    public partial class CareerModel
    {
        [Key]
        public Guid CareerId { get; set; }
        [StringLength(50)]
        public string CareerCode { get; set; }
        [StringLength(1000)]
        public string CareerName { get; set; }
        public bool? Actived { get; set; }
    }
}