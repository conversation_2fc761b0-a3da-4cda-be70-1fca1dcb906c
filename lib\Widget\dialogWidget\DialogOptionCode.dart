import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../model/dialogGetBarCodeModel.dart';


class DialogOptionCode extends StatefulWidget {
  const DialogOptionCode({Key? key}) : super(key: key);

  @override
  State<DialogOptionCode> createState() => _DialogOptionCodeState();
}

class _DialogOptionCodeState extends State<DialogOptionCode> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocus = FocusNode();
   bool _emptyTest = false;
   void _checkErrorEmpty(String input){
     if(input.isEmpty){
       if(_emptyTest != true) {
         setState(() {
           _emptyTest = true;
         });
       }
     }else{
       if(_emptyTest != false) {
         setState(() {
           _emptyTest = false;
         });
       }
     }
   }
  @override
  void dispose() {
    _searchController.dispose();
    _searchFocus.dispose();
    debugPrint('dispose');
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Nhập tay hoặc quét mã"),
      titleTextStyle: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xff0052cc),
          fontWeight: FontWeight.bold
      ),
      content: SingleChildScrollView(
        child: ListBody(
          children: <Widget>[
            _SearchForm(searchFocus: _searchFocus, searchController: _searchController, checkEmptyInput: _checkErrorEmpty),
            SizedBox(height: _emptyTest == true ?5.h:0),
            _ContainerErrorWidget(error: _emptyTest, message: "Vui lòng nhập id",),
            SizedBox(height: 3.h),
          ],
        ),
      ),
      actionsAlignment: MainAxisAlignment.spaceBetween,
      actions: [
         TextButton(
            style: ButtonStyle(
              overlayColor: MaterialStateProperty.all(
                  const Color(0xff0052cc).withOpacity(0.1)),
            ),
            onPressed: ()  {
              FocusScope.of(context).unfocus();
              Navigator.pop(context,DialogGetBarCodeModel(changePageTo: "scan", param: ''));
            },
            child: Icon(
              Icons.camera_alt,
              size: 15.sp,
              color: const Color(0xff0052cc),
            ),
          ),
    TextButton(
            style: ButtonStyle(
              overlayColor: MaterialStateProperty.all(
                  const Color(0xff0052cc).withOpacity(0.1)),
            ),
            onPressed: () {
              FocusScope.of(context).unfocus();
              _checkErrorEmpty(_searchController.text);
              if(_emptyTest == false) {
                Navigator.pop(context, DialogGetBarCodeModel(changePageTo: "id", param: _searchController.text));
              }
            },
            child: Text(
              "Tìm",
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0xff0052cc),
              ),
            ),
          ),
      ],
    );
  }
}

class _SearchForm extends StatelessWidget {
  final TextEditingController searchController;
  final FocusNode searchFocus;
  final ValueChanged<String> checkEmptyInput;
  const _SearchForm({Key? key, required this.searchController, required this.searchFocus, required this.checkEmptyInput}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              border: Border.all(width: 0.5.w, color: Colors.white),
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: TextFormField(
              controller: searchController,
              focusNode: searchFocus,
              style: TextStyle(fontSize: 12.sp),
              decoration: InputDecoration(
                hintText: "Nhập id",
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
                suffixIconConstraints: const BoxConstraints(),
                suffixIcon: const Icon(Icons.search_outlined,color: Colors.grey),
                hintStyle: TextStyle(fontSize: 12.sp,color: Colors.grey.shade400),
              ),
              onChanged: checkEmptyInput
            ),
    );
  }
}
class _ContainerErrorWidget extends StatelessWidget {
  final bool error;
  final String message;
  const _ContainerErrorWidget({Key? key, required this.error, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return
        Visibility(
            visible: error,
            child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Flexible(
                    flex: 1,
                    child: Icon(Icons.error_outline,
                        size: 13.sp, color: Colors.red[700]),
                  ),
                  SizedBox(width: 5.w),
                  Flexible(
                      flex: 9,
                      child: Text(message,
                          style:
                          TextStyle(fontSize: 11.sp, color: Colors.red[700])))
                ]));
  }
}




