import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/getInventoryBySOWBS.dart';
import '../../urlApi/urlApi.dart';

class GetInventoryByListSOWBSApi {
  static Future<http.Response> getInventoryByListSOWBSApi(GetInventoryBySOWBS getInventoryBySOWBS, String token) async {
    final dataPost = jsonEncode(getInventoryBySOWBS);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "GetInventoryBySOWBS");
    if (kDebugMode) {
      print(url);
    }
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
