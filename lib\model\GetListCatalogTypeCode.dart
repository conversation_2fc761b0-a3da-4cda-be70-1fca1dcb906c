class GetListCatalogTypeCode {
  int? code;
  bool? isSuccess;
  List<DataGetListCatalogTypeCode>? data;


  GetListCatalogTypeCode(
      {this.code,
        this.isSuccess,
        this.data});

  GetListCatalogTypeCode.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    if (json['data'] != null) {
      data = <DataGetListCatalogTypeCode>[];
      json['data'].forEach((v) {
        data!.add(DataGetListCatalogTypeCode.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetListCatalogTypeCode {
  String? key;
  String? value;

  DataGetListCatalogTypeCode({this.key, this.value});

  DataGetListCatalogTypeCode.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}