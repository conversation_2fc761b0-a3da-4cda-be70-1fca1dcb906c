import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';

class ConfirmWorkCenterApi {
  static Future<http.Response> getConfirmWorkCenter(String barcode, String token) async {
    final data = {"Barcode": barcode};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlConfirmWorkCenter}ConfirmWorkCenter?Barcode=${data['Barcode']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postConfirmWorkCenter(String taskId, String confirmWorkCenter, String workCenterConfirmTime, String token) async {
    Map data = {"taskId": taskId, "confirmWorkCenter": confirmWorkCenter, "workCenterConfirmTime": workCenterConfirmTime};

    var body = json.encode(data);
    if (kDebugMode) {
      print(body);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlConfirmWorkCenter + "ConfirmWorkCenter");
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersToken(token), body: body);
    return response;
  }
}
