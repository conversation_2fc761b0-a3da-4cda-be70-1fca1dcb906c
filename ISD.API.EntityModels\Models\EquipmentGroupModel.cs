﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("EquipmentGroupModel", Schema = "MES")]
    public partial class EquipmentGroupModel
    {
        [Key]
        public Guid EquipmentGroupId { get; set; }
        [StringLength(50)]
        public string EQUNR { get; set; }
        [StringLength(50)]
        public string EQART { get; set; }
        [StringLength(500)]
        public string SHTXT { get; set; }
        [StringLength(50)]
        public string SWERK { get; set; }
        [StringLength(50)]
        public string STORT { get; set; }
        [StringLength(100)]
        public string ARBPL { get; set; }
        [StringLength(10)]
        public string STATU { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DATBI { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}