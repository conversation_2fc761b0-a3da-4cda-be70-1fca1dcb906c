# Material Consumption Module

The Material Consumption module in TTF MES Mobile provides tools for tracking, managing, and optimizing the usage of materials in manufacturing processes. This module helps maintain accurate inventory records, analyze consumption patterns, and identify opportunities for material usage optimization.

## Module Structure

The Material Consumption module is primarily located in `lib/page/TyLeTieuHao/` and consists of the following components:

### Main Components

- `TyLeTieuHaoList.dart`: List of material consumption records
- `TyLeTieuHaoDetail.dart`: Detailed view of consumption records
- Related model files in `lib/model/` directory

### Supporting Files

- `StatisticsMaterials.dart`: Material usage statistics
- Integration with QR code scanning for material identification
- Connection to inventory management for stock updates

## Features

### Consumption Recording

The consumption recording functionality allows users to:

- Record material usage for production orders
- Scan material barcodes/QR codes for accurate identification
- Specify consumption quantities
- Document material lot/batch information
- Track consumption by work center or operation

### Consumption Analysis

The consumption analysis feature enables:

- Comparison of actual vs. standard consumption
- Identification of excessive material usage
- Consumption trend analysis
- Material usage efficiency metrics
- Waste and scrap tracking

### Material Efficiency

The material efficiency functionality supports:

- Standard usage calculation
- Efficiency ratio monitoring
- Variance analysis
- Root cause documentation for variances
- Improvement tracking

### Material Substitution

The material substitution system allows:

- Recording of material substitutions
- Approval workflow for substitutions
- Impact analysis of substitutions
- Substitution history tracking
- Engineering change integration

### Waste Management

The waste management functionality provides:

- Scrap and waste recording
- Waste categorization
- Waste reduction initiative tracking
- Cost of waste analysis
- Environmental impact monitoring

## Workflows

### Consumption Recording Process

1. Access the Material Consumption List (`TyLeTieuHaoList.dart`)
2. Create new consumption record
3. Scan production order or select from list
4. Scan material or select from bill of materials
5. Record actual consumption quantity
6. Document any variance from standard
7. Submit consumption record
8. Update inventory levels

### Consumption Analysis Process

1. Navigate to Consumption Details (`TyLeTieuHaoDetail.dart`)
2. Select time period for analysis
3. View consumption metrics by material
4. Analyze variances from standards
5. Identify trends and patterns
6. Generate consumption reports
7. Share findings with relevant teams

### Efficiency Improvement Process

1. Access Material Consumption details
2. Identify materials with high variance
3. Analyze root causes for excessive consumption
4. Document improvement opportunities
5. Implement and track improvement actions
6. Monitor impact on material efficiency
7. Update standard consumption if needed

### Substitution Management Process

1. Navigate to Material Consumption
2. Create new substitution request
3. Specify original material and substitute
4. Document reason for substitution
5. Submit for approval if required
6. Record actual substitution
7. Monitor impact on product quality and cost

## Data Models

The material consumption module uses the following data models:

- **ConsumptionModel**: Material consumption records
- **ConsumptionRateModel**: Consumption efficiency data
- **MaterialSubstitutionModel**: Material substitution records
- **WasteRecordModel**: Waste and scrap documentation
- **ConsumptionAnalysisModel**: Analysis and metrics data
- **StandardConsumptionModel**: Standard consumption rates

## API Integration

Material consumption operations are synchronized with backend systems through the following API endpoints:

- `/api/consumption/records`: Consumption record management
- `/api/consumption/analysis`: Consumption analysis
- `/api/consumption/standards`: Standard consumption rates
- `/api/consumption/substitutions`: Material substitution management
- `/api/consumption/waste`: Waste and scrap tracking

## User Interfaces

### List Views
Material consumption list views display:
- Production order identification
- Material information
- Consumption quantity
- Efficiency ratio
- Variance indicators
- Date/time information

### Detail Views
Consumption detail views include:
- Comprehensive consumption information
- Standard vs. actual comparison
- Variance analysis
- Historical consumption data
- Related quality information
- User comments and documentation

### Analysis Views
Consumption analysis views provide:
- Efficiency metrics visualization
- Trend graphs for consumption
- Pareto analysis of problem materials
- Cost impact analysis
- Improvement opportunity highlights

## Integration with Other Modules

The Material Consumption module integrates with other modules in the following ways:

- **Inventory Management**: Updates inventory based on consumption
- **Production Management**: Links consumption to production orders
- **Quality Control**: Correlates material quality with consumption variances
- **Warehouse Management**: Provides material tracking information
- **Downtime Tracking**: Identifies downtime impacts on material efficiency

## Best Practices

For effective use of the Material Consumption module:

1. Always scan materials for accurate identification
2. Record consumption as close to real-time as possible
3. Document reasons for any significant variances
4. Regularly review consumption efficiency metrics
5. Investigate consistent variances from standards
6. Keep standard consumption rates updated
7. Track and address waste reduction opportunities

## Future Enhancements

Planned improvements for the Material Consumption module include:

1. Predictive consumption modeling
2. Advanced waste reduction tools
3. Machine learning for variance pattern detection
4. Real-time consumption monitoring with IoT
5. Enhanced bill of materials integration
6. Material substitution optimization
7. Sustainability metrics and reporting 