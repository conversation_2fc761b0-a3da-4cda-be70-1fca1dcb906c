﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SettingJob", Schema = "system")]
    public partial class SettingJob
    {
        [Key]
        public long Id { get; set; }
        [StringLength(50)]
        public string JobName { get; set; }
        [StringLength(50)]
        public string Config { get; set; }
        [StringLength(50)]
        public string ConfigDescription { get; set; }
        public bool? IsRun { get; set; }
    }
}