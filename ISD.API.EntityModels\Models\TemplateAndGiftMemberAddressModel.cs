﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TemplateAndGiftMemberAddressModel", Schema = "Marketing")]
    public partial class TemplateAndGiftMemberAddressModel
    {
        [Key]
        public Guid Id { get; set; }
        public Guid? TempalteAndGiftMemberId { get; set; }
        [StringLength(4000)]
        public string Address { get; set; }
        public Guid? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? Quantity { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }

        [ForeignKey("CreateBy")]
        [InverseProperty("TemplateAndGiftMemberAddressModelCreateByNavigation")]
        public virtual AccountModel CreateByNavigation { get; set; }
        [ForeignKey("LastEditBy")]
        [InverseProperty("TemplateAndGiftMemberAddressModelLastEditByNavigation")]
        public virtual AccountModel LastEditByNavigation { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("TemplateAndGiftMemberAddressModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("TempalteAndGiftMemberId")]
        [InverseProperty("TemplateAndGiftMemberAddressModel")]
        public virtual TemplateAndGiftMemberModel TempalteAndGiftMember { get; set; }
    }
}