import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:ttf/screenArguments/screenArgumentInfor.dart';
import 'package:xml/xml.dart';
import '../model/userModel.dart';
import '../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../screenArguments/screenArgumentImportWareHouse.dart';
import '../screenArguments/screenArgumentInforSwitchingStages.dart';
import '../screenArguments/screenArgumentMaterialRetail.dart';

class QrCodePage extends StatefulWidget {
  final String fromPage;
  final String token;
  final Permission permission;
  final String plant;
  final String dateTimeOld;
  const QrCodePage({Key? key, required this.fromPage, required this.token, required this.permission, required this.plant, required this.dateTimeOld})
      : super(key: key);

  @override
  _QrCodePageState createState() => _QrCodePageState();
}

class _QrCodePageState extends State<QrCodePage> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    }
    controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          await Future.delayed(const Duration(milliseconds: 500));
          await controller!.pauseCamera();
          Navigator.pop(context);
          return false;
        },
        child: Scaffold(
            body: Stack(alignment: Alignment.bottomCenter, children: <Widget>[
          _buildQrView(context),
          Positioned(
            child: buildButton(context),
          )
        ])
            // OrientationBuilder(
            //     builder: (context, orientation) {
            //       return orientation == Orientation.portrait ?Stack(
            //     alignment: Alignment.bottomCenter,
            //     children: <Widget>[
            //       _buildQrView(context),
            //       Positioned(
            //         child: buildButton(context),
            //       )
            //     ]
            //      ):Stack(
            //           alignment: Alignment.bottomCenter,
            //           children: <Widget>[
            //             _buildQrViewLandscape(context),
            //             Positioned(
            //               child: buildButton(context),
            //             )
            //           ]
            //       );})
            ));
  }

  Widget buildButton(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 30.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            IconButton(
                onPressed: () async {
                  await controller?.toggleFlash();
                  setState(() {});
                },
                icon: FutureBuilder<bool?>(
                  future: controller?.getFlashStatus(),
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      return Icon(snapshot.data! ? Icons.flash_on : Icons.flash_off, color: Colors.white, size: 30.sp);
                    } else {
                      return Container();
                    }
                  },
                )),
            GestureDetector(
                onTap: () async {
                  await Future.delayed(const Duration(milliseconds: 500));
                  await controller!.pauseCamera();
                  Navigator.pop(context);
                },
                child: Text(
                  'Thoát',
                  style: TextStyle(color: Colors.blueAccent, fontSize: 18.sp),
                ))
          ],
        ));
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: (QRViewController controller) => _onQRViewCreated(controller, context),
      overlay: QrScannerOverlayShape(borderColor: Colors.red, borderRadius: 10.r, borderLength: 30, borderWidth: 10, cutOutSize: 300.w),
    );
  }
  // Widget _buildQrViewLandscape(BuildContext context) {
  //   return QRView(
  //     key: qrKey,
  //     onQRViewCreated: (QRViewController controller) =>
  //         _onQRViewCreated(controller, context),
  //     overlay: QrScannerOverlayShape(
  //         borderColor: Colors.red,
  //         borderRadius: 10.r,
  //         borderLength: 30,
  //         borderWidth: 10,
  //         cutOutSize: 150.w),
  //
  //   );
  // }

  Future<void> _onQRViewCreated(QRViewController controller, BuildContext context) async {
    setState(() {
      this.controller = controller;
    });
    if (Platform.isAndroid) {
      await this.controller!.resumeCamera();
    }

    controller.scannedDataStream.listen((scanData) {
      _changPage(scanData.code, context);
    });
  }

  Future<void> _changPage(String? barCode, BuildContext context) async {
    try {
      if (barCode != null) {
        // String getBarcode = scanData.code.toString();
        if (widget.fromPage == 'infor') {
          if (widget.permission.mobileScreenModel!.firstWhereOrNull((element) =>
                  element.screenName == "Ghi nhận sản lượng" ||
                  element.screenName == "Hoàn tất công đoạn lớn" ||
                  element.screenName == "Chuyển công đoạn") !=
              null) {
            await controller!.pauseCamera();
            Navigator.pushReplacementNamed(context, '/info',
                arguments: ScreenArgumentInfor(
                  barCode,
                  widget.token,
                  widget.dateTimeOld,
                ));
          } else {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.blue[900],
                content: Text(
                  'Tài khoản không được cấp quyền hợp lệ!',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
          }
        }

        if (widget.fromPage == 'qcpassed') {
          if (widget.permission.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "QCPassedInfo") != null) {
            await controller!.pauseCamera();
            // Navigate to the QC passed stamp scanning page
            Navigator.pushReplacementNamed(
              context,
              '/QCPassedStampScan',
              arguments: ScreenArgumentInfor(
                barCode,
                widget.token,
                widget.dateTimeOld,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.blue[900],
              content: Text(
                'Tài khoản không được cấp quyền quét QC passed stamp!',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 1),
            ));
          }
        } else if (widget.fromPage == 'MaterialRetail') {
          await controller!.pauseCamera();
          Navigator.pushReplacementNamed(context, "/MaterialRetail",
              arguments: ScreenArgumentMaterialRetail(widget.token, barCode, widget.plant, widget.dateTimeOld));
        } else if (widget.fromPage == 'importProduct') {
          // For importProduct, just return the barcode directly without XML parsing
          await controller!.pauseCamera();
          Navigator.pop(context, barCode);
        } else {
          // For other cases, use the XML parsing approach
          await controller!.pauseCamera();
          try {
            String convertBarCode = '''<?xml version="1.0"?><data>$barCode</data>''';
            final materialID = getQRCodeMaterial(convertBarCode);
            Navigator.pushReplacementNamed(context, '/ImportWarehouseSAP',
                arguments: ScreenArgumentImportWareHouse(
                  materialID,
                  widget.token,
                  widget.plant,
                  widget.dateTimeOld,
                  "qr",
                ));
          } catch (parseError) {
            // If XML parsing fails, show a more specific error
            debugPrint("XML parsing error: $parseError");
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                backgroundColor: Colors.blue[900],
                content: Text(
                  'QR code không đúng định dạng',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
                duration: const Duration(seconds: 1)));
            controller!.resumeCamera();
          }
        }
      }
    } catch (error) {
      debugPrint(error.toString());
      controller!.resumeCamera();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.blue[900],
          content: Text(
            'Thẻ không tồn tại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  String getQRCodeMaterial(String convertBarCode) {
    XmlDocument document = XmlDocument.parse(convertBarCode);
    return document.findAllElements('T1').first.text;
  }

  @override
  void dispose() {
    controller?.dispose();
    // SystemChrome.setPreferredOrientations([
    //   // DeviceOrientation.landscapeRight,
    //   // DeviceOrientation.landscapeLeft,
    //   DeviceOrientation.portraitUp,
    //   DeviceOrientation.portraitDown,
    // ]);
    super.dispose();
  }
}
