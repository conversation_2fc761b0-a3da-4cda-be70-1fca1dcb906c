﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseModel", Schema = "tSale")]
    public partial class WarehouseModel
    {
        public WarehouseModel()
        {
            WarehouseProductModel = new HashSet<WarehouseProductModel>();
        }

        [Key]
        public Guid WarehouseId { get; set; }
        [Required]
        [StringLength(50)]
        public string WarehouseCode { get; set; }
        [Required]
        [StringLength(200)]
        public string WarehouseName { get; set; }
        [StringLength(100)]
        public string WarehouseShortName { get; set; }
        public Guid StoreId { get; set; }
        public int? OrderIndex { get; set; }
        public bool Actived { get; set; }

        [InverseProperty("Warehouse")]
        public virtual ICollection<WarehouseProductModel> WarehouseProductModel { get; set; }
    }
}