import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import 'package:ttf/model/productionRecordHistoryApi.dart';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:ttf/urlApi/urlApi.dart';

import '../../element/imageGallery.dart';

class DialogImage extends StatefulWidget {
  final String title;
  final List<FileViewModel>? listImage;

  const DialogImage({Key? key, required this.title, required this.listImage}) : super(key: key);

  @override
  State<DialogImage> createState() => _DialogImageState();
}

class _DialogImageState extends State<DialogImage> {
  int? _selectedIndex;

  late String imageUrl;

  @override
  void initState() {
    super.initState();
    _setup();
  }

  Future<void> _setup() async {
    final environment = await SecureStorage.getString("environment", null);
    final url = environment == 'PRD' ? UrlApi.urlImagePrd : await UrlApi.urlImageQas;
    setState(() {
      imageUrl = url;
    });
  }

  Future<void> _downloadImage(String url, BuildContext context, int index) async {
    try {
      setState(() {
        _selectedIndex = index;
      });
      await GallerySaver.saveImage(url, albumName: 'iMES');
      setState(() {
        _selectedIndex = -1;
      });
    } catch (error) {
      setState(() {
        _selectedIndex = -1;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: SingleChildScrollView(
        child: ListBody(
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
              decoration: const BoxDecoration(color: Color(0xff0052cc)),
              child: Text(
                widget.title,
                style: TextStyle(fontSize: 13.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 15.h),
            Table(
              border: TableBorder.all(color: Colors.grey.shade400, width: 0.2),
              columnWidths: const <int, TableColumnWidth>{
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(2),
                2: FlexColumnWidth(3),
                3: FlexColumnWidth(3),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: <TableRow>[
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey.shade100),
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Center(
                        child: Text(
                          "STT",
                          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Text(
                        "",
                        style: TextStyle(fontSize: 10.sp),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Center(
                        child: Text(
                          "Ngày tạo",
                          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Center(
                        child: Text(
                          "Chức năng",
                          style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              width: double.minPositive,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: widget.listImage!.length,
                itemBuilder: (BuildContext context, int index) {
                  final stringCreateTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(widget.listImage![index].createTime.toString());
                  String createTimeToString = DateFormat("MM/dd/yyyy HH:mm:ss").format(stringCreateTime);
                  return Table(
                    border: TableBorder.all(color: Colors.grey.shade400, width: 0.2),
                    columnWidths: const <int, TableColumnWidth>{
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(2),
                      2: FlexColumnWidth(3),
                      3: FlexColumnWidth(3),
                    },
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    children: <TableRow>[
                      TableRow(
                        decoration: BoxDecoration(
                          color: index % 2 != 0 ? Colors.grey.shade100 : Colors.white,
                        ),
                        children: <Widget>[
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                            child: Center(
                              child: Text(
                                (index + 1).toString(),
                                style: TextStyle(
                                  fontSize: 10.sp,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            height: 50,
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => GalleryWidget(imageComment: widget.listImage, index: index),
                                  ),
                                );
                              },
                              child: Image.network(
                                imageUrl + widget.listImage![index].fileUrl.toString(),
                                loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Center(
                                      child: Transform.scale(
                                    scale: 0.7,
                                    child: CircularProgressIndicator(
                                      value: loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                          : null,
                                    ),
                                  ));
                                },
                                errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                                  return Image.asset("assets/errorimage/imageerror.jpg");
                                },
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                            child: Text(
                              createTimeToString,
                              style: TextStyle(fontSize: 10.sp),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              _downloadImage(imageUrl + widget.listImage![index].fileUrl.toString(), context, index);
                              // download(UrlApi.urlImage +
                              //     widget.listImage![index].fileUrl.toString());
                            },
                            child: Icon(
                              Icons.download_outlined,
                              size: 20.sp,
                              color: _selectedIndex == index ? Colors.blue : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
