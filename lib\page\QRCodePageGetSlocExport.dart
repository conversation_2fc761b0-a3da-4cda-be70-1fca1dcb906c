import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:xml/xml.dart';

import '../model/qrCodeChooseAddress.dart';
import '../model/slocAddresse.dart';

class QRCodePageGetSlocExport extends StatefulWidget {
  const QRCodePageGetSlocExport({Key? key}) : super(key: key);
  @override
  _QRCodePageGetSlocExportState createState() => _QRCodePageGetSlocExportState();
}

class _QRCodePageGetSlocExportState extends State<QRCodePageGetSlocExport> {

  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;


  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    }
    controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
      await Future.delayed(const Duration(milliseconds: 500));
      await controller!.pauseCamera();
    Navigator.pop(context);
    return false;
    },
    child:Scaffold(
        body: Stack(alignment: Alignment.bottomCenter, children: <Widget>[
          _buildQrView(context),
          Positioned(
            child: buildButton(context),
          )
        ])));
  }

  Widget buildButton(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 30.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            IconButton(
                onPressed: () async {
                  await controller?.toggleFlash();
                  setState(() {});
                },
                icon: FutureBuilder<bool?>(
                  future: controller?.getFlashStatus(),
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      return Icon(
                          snapshot.data! ? Icons.flash_on : Icons.flash_off,
                          color: Colors.white,
                          size: 30.sp);
                    } else {
                      return Container();
                    }
                  },
                )),
            GestureDetector(
                onTap: () async {
                  await Future.delayed(const Duration(milliseconds: 500));
                  await controller!.pauseCamera();
                  Navigator.pop(context);
                },
                child: Text(
                  'Thoát',
                  style: TextStyle(color: Colors.blueAccent, fontSize: 18.sp),
                ))
          ],
        ));
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: (QRViewController controller) =>
          _onQRViewCreated(controller, context),
      overlay: QrScannerOverlayShape(
          borderColor: Colors.red,
          borderRadius: 10.r,
          borderLength: 30,
          borderWidth: 10,
          cutOutSize: 300.w),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller, BuildContext context) async {
    setState(() {
      this.controller = controller;
    });
    if (Platform.isAndroid) {
      await this.controller!.resumeCamera();
    }
    controller.scannedDataStream.listen((scanData) async {
      _getAddresse(scanData.code,context);
    });
  }
  Future<void> _getAddresse(String? barCode,BuildContext context) async{
    try {
      if (barCode != null) {
        print(barCode);
        await controller!.pauseCamera();
        String converBarCode = '''<?xml version="1.0"?><data>$barCode</data>''';
        final qrCodeChooseAddress = getQRCodeChooseAddress(converBarCode);
        Navigator.pop(context, qrCodeChooseAddress);
      }
    }catch (error) {
      print(barCode);
      print(error);
      controller!.resumeCamera();
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.blue[900],
          content: Text(
            "Barcode không tồn tại",
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }
  DataSlocAddress getQRCodeChooseAddress(String convertBarCode){
    XmlDocument document = XmlDocument.parse(convertBarCode);
    final t1 = document.findAllElements('T1').first.text;
    final t2 = int.parse(document.findAllElements('T2').first.text);
    final t3 = document.findAllElements('T3').first.text;
    final t4 = int.parse(document.findAllElements('T4').first.text);
    final t5 = document.findAllElements('T5').first.text;
    final t6 = document.findAllElements('T6').first.text;
    final qrCodeChooseAddress = QRCodeChooseAddress(storageID: t1,plant: t2,sloc: t3,wareHouseNO: t4,storageType: t5,storageBin: t6) ;
    return DataSlocAddress(slocId: "" ,sloc: qrCodeChooseAddress.sloc,plant:qrCodeChooseAddress.plant.toString(),warehouseNo:qrCodeChooseAddress.wareHouseNO.toString(),defaultStorageBin:qrCodeChooseAddress.storageBin,defaultStorageBinId: qrCodeChooseAddress.storageID);
  }
  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
