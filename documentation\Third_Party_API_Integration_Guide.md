# Third-Party Quality Control API Integration Guide

## Overview

This guide explains how external third parties can integrate with the TTF iMES Quality Control API system using API key authentication. The system provides secure, controlled access to Quality Control data while maintaining proper authorization and rate limiting.

## Authentication Method: API Key

The system uses API key-based authentication for third-party access, which is separate from the internal JWT authentication system. This ensures that third-party integrations don't interfere with internal user sessions.

### API Key Features

- **Secure Generation**: API keys are generated using cryptographically secure random number generation
- **Hashed Storage**: Only hashed versions of API keys are stored in the database
- **Scoped Access**: API keys can be limited to specific operations (e.g., read-only)
- **Company/Organization Isolation**: Each API key is tied to a specific company and optionally a sales organization
- **IP Address Restrictions**: API keys can be restricted to specific IP addresses
- **Rate Limiting**: Configurable rate limits per API key (default: 1000 requests/hour)
- **Expiration Support**: API keys can have expiration dates
- **Usage Tracking**: Last used date tracking for monitoring

## Getting Started

### 1. Request API Key Creation

Contact your system administrator to create an API key. Provide the following information:

- **Key Name**: A descriptive name for the API key
- **Description**: Purpose of the integration
- **Company Code**: Your company identifier in the system
- **Sale Organization Code**: (Optional) Specific sales organization if applicable
- **Required Scopes**: Permissions needed (e.g., `QualityControl.Read`)
- **IP Addresses**: (Optional) Restrict access to specific IP addresses
- **Rate Limit**: (Optional) Custom rate limit if different from default

### 2. API Key Format

API keys follow this format: `ttf_[40-character-string]`

Example: `ttf_AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCd`

⚠️ **Important**: Store your API key securely. It will only be shown once during creation.

## Using the API

### Authentication Headers

Include your API key in requests using one of these methods:

#### Method 1: X-API-Key Header (Recommended)
```http
GET /api/v1/External/QualityControlExternal/QualityControl/{id}
X-API-Key: ttf_your_api_key_here
Content-Type: application/json
```

#### Method 2: Authorization Header
```http
GET /api/v1/External/QualityControlExternal/QualityControl/{id}
Authorization: ApiKey ttf_your_api_key_here
Content-Type: application/json
```

### Base URL

- **Production**: `https://your-production-domain.com`
- **Development**: `https://your-dev-domain.com`

All third-party endpoints are prefixed with `/api/v1/External/`

## Available Endpoints

### 1. Get Quality Control by ID

Retrieve detailed quality control information for a specific QC record.

```http
GET /api/v1/External/QualityControlExternal/QualityControl/{qualityControlId}
```

**Parameters:**
- `qualityControlId` (required): GUID of the quality control record

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "qualityControlId": "e59103f5-e697-4bb8-9010-c5b1a62ffb3d",
    "qualityControlCode": 6902,
    "saleOrgCode": "1000",
    "workShopCode": "20000050",
    "workCenterCode": "LRN",
    "profileCode": "0020010615",
    "productCode": "400011295",
    "productName": "400011295 | Đầu thấp+vai+dạt giường Bodie WO Twin",
    "lsxdt": "DT-341-21-CB2",
    "lsxsap": "600003113",
    "dsx": "DT-341-21-CB2-D1",
    "confirmDate": "2022-05-09T00:00:00",
    "qualityDate": "2022-05-10T00:00:00",
    "qualityChecker": "QC01",
    "status": true,
    "result": "Pass",
    "qualityType": "Final",
    "qty": 200,
    "unit": "Cụm",
    "inspectionLotQuantity": 20,
    "environmental": "Normal"
  }
}
```

### 2. Get Quality Control List (with Pagination)

Retrieve a paginated list of quality control records with filtering options.

```http
GET /api/v1/External/QualityControlExternal/QualityControl
```

**Query Parameters:**
- `saleOrgCode` (optional): Filter by sales organization
- `workShopCode` (optional): Filter by workshop
- `productCode` (optional): Filter by product code
- `status` (optional): Filter by status (true/false)
- `fromDate` (optional): Filter from date (ISO 8601 format)
- `toDate` (optional): Filter to date (ISO 8601 format)
- `pageIndex` (optional): Page number (default: 1)
- `pageSize` (optional): Records per page (default: 20, max: 100)

**Example:**
```http
GET /api/v1/External/QualityControlExternal/QualityControl?status=true&pageIndex=1&pageSize=20&fromDate=2024-01-01
```

**Response:**
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "qualityControlId": "...",
        "qualityControlCode": "...",
        "saleOrgCode": "...",
        "workShopCode": "...",
        "productCode": "...",
        "productName": "...",
        "confirmDate": "...",
        "qualityDate": "...",
        "status": true,
        "result": "Pass",
        "qualityType": "Final",
        "qty": 200
      }
    ],
    "pagination": {
      "pageIndex": 1,
      "pageSize": 20,
      "totalCount": 150,
      "totalPages": 8,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### 3. Get Quality Control by Barcode

Retrieve quality control information using product barcode/serial number.

```http
GET /api/v1/External/QualityControlExternal/QualityControl/ByBarcode/{barcode}
```

**Parameters:**
- `barcode` (required): Product barcode, LSX DT, LSX SAP, or DSX number

**Example:**
```http
GET /api/v1/External/QualityControlExternal/QualityControl/ByBarcode/DT-341-21-CB2
```

## Error Handling

### HTTP Status Codes

- `200 OK`: Successful request
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Invalid or missing API key
- `403 Forbidden`: API key lacks required permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Error Response Format

```json
{
  "isSuccess": false,
  "code": 401,
  "message": "Invalid or missing API key",
  "data": null
}
```

## Security Considerations

### Data Access Restrictions

- API keys are automatically restricted to their assigned company/organization
- Cross-company data access is prevented
- Only approved scopes are accessible per API key

### Rate Limiting

- Default: 1000 requests per hour per API key
- Rate limits are configurable per API key
- Exceeded limits return HTTP 429 status

### IP Address Restrictions

- API keys can be restricted to specific IP addresses
- Use comma-separated list for multiple IPs
- Use "*" to allow all IP addresses (not recommended for production)

## Best Practices

### 1. API Key Management

- Store API keys securely (environment variables, secure vaults)
- Never commit API keys to source control
- Rotate API keys periodically
- Use descriptive names for API keys
- Set appropriate expiration dates

### 2. Request Patterns

- Implement exponential backoff for retries
- Cache responses when appropriate
- Use pagination for large datasets
- Implement proper error handling
- Monitor rate limit usage

### 3. Security

- Use HTTPS for all API calls
- Validate all response data
- Implement request logging for audit purposes
- Monitor for unusual access patterns

## Sample Code Examples

### C# Example

```csharp
using System;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class QualityControlClient
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly string _baseUrl;

    public QualityControlClient(string apiKey, string baseUrl)
    {
        _httpClient = new HttpClient();
        _apiKey = apiKey;
        _baseUrl = baseUrl;
    }

    public async Task<QualityControlResponse> GetQualityControlAsync(Guid qualityControlId)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, 
            $"{_baseUrl}/api/v1/External/QualityControlExternal/QualityControl/{qualityControlId}");
        
        request.Headers.Add("X-API-Key", _apiKey);

        var response = await _httpClient.SendAsync(request);
        var content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            return JsonConvert.DeserializeObject<QualityControlResponse>(content);
        }
        
        throw new Exception($"API call failed: {response.StatusCode} - {content}");
    }
}
```

### Python Example

```python
import requests
import json

class QualityControlClient:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }

    def get_quality_control(self, quality_control_id):
        url = f"{self.base_url}/api/v1/External/QualityControlExternal/QualityControl/{quality_control_id}"
        
        response = requests.get(url, headers=self.headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

    def get_quality_control_list(self, page_index=1, page_size=20, **filters):
        url = f"{self.base_url}/api/v1/External/QualityControlExternal/QualityControl"
        
        params = {
            'pageIndex': page_index,
            'pageSize': page_size,
            **filters
        }
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

# Usage
client = QualityControlClient("ttf_your_api_key_here", "https://your-domain.com")
result = client.get_quality_control("e59103f5-e697-4bb8-9010-c5b1a62ffb3d")
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');

class QualityControlClient {
    constructor(apiKey, baseUrl) {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
        this.axios = axios.create({
            baseURL: baseUrl,
            headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            }
        });
    }

    async getQualityControl(qualityControlId) {
        try {
            const response = await this.axios.get(
                `/api/v1/External/QualityControlExternal/QualityControl/${qualityControlId}`
            );
            return response.data;
        } catch (error) {
            throw new Error(`API call failed: ${error.response.status} - ${error.response.data}`);
        }
    }

    async getQualityControlList(options = {}) {
        const {
            pageIndex = 1,
            pageSize = 20,
            ...filters
        } = options;

        try {
            const response = await this.axios.get('/api/v1/External/QualityControlExternal/QualityControl', {
                params: {
                    pageIndex,
                    pageSize,
                    ...filters
                }
            });
            return response.data;
        } catch (error) {
            throw new Error(`API call failed: ${error.response.status} - ${error.response.data}`);
        }
    }
}

// Usage
const client = new QualityControlClient('ttf_your_api_key_here', 'https://your-domain.com');
client.getQualityControl('e59103f5-e697-4bb8-9010-c5b1a62ffb3d')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

## Support and Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check API key format and validity
   - Ensure API key is active and not expired
   - Verify correct header format

2. **403 Forbidden**
   - Check API key scopes/permissions
   - Verify company/organization access rights
   - Check IP address restrictions

3. **429 Rate Limit Exceeded**
   - Implement request throttling
   - Check current rate limit settings
   - Consider requesting higher limits

4. **404 Not Found**
   - Verify resource IDs
   - Check company/organization access rights
   - Ensure resource exists in the system

### Contact Information

For technical support, API key requests, or integration assistance:

- **Email**: <EMAIL>
- **Technical Documentation**: https://docs.ttf-mes.com
- **System Status**: https://status.ttf-mes.com

## Changelog

### Version 1.0 (Current)
- Initial release of third-party QC API
- API key authentication system
- Basic QC data retrieval endpoints
- Rate limiting and security features
- Company/organization data isolation

---

*This documentation is updated regularly. Please check for the latest version before implementing integrations.*