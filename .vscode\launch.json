{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    
    // {
    //   "name": "Honor 8X",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d", "XSQ4C18C14000304"]
    // },
    // {
    //   "name": "iPhone 8 Plus Similar",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d", "***************:5555"]
    // },
    // {
    //   "name": "Google Pixel 2",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d", "***************:5555"]
    // },
    {
      "name": "Current Device",
      "request": "launch",
      "type": "dart"
    },
    // {
    //   "name": "Pixel 3",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d", "emulator-5556"]
    // },
    // {
    //   "name": "Pixel 6",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d", "emulator-5554"]
    // },
    // {
    //   "name": "TTF_MES_Mobile 2 real device",
    //   "request": "launch",
      // "type": "dart",
    //   "args": ["-d", "XSQ4C18C14000304"]
    // },
    // {
    //   "name": "TTF_MES_Mobile (run all)",
    //   "request": "launch",
    //   "type": "dart",
    //   "args": ["-d all"]
    // },
    // {
    //   "name": "TTF_MES_Mobile (profile mode)",
    //   "request": "launch",
    //   "type": "dart",
    //   "flutterMode": "profile"
    // },
    {
      "name": "Current Device (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    }
  ],
  // "compounds": [
  //   {
  //     "name": "Multiple Devices",
  //     "configurations": [
  //       // "Honor 8X", 
  //       // "iPhone 8 Plus Similar",
  //       // "Google Pixel 2"
  //       // "Pixel 6", 
  //       // "Pixel 3", 
  //     ]
  //   }
  // ]
}