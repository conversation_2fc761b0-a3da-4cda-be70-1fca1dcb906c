﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("GH_NotificationModel", Schema = "ghMasterData")]
    public partial class GH_NotificationModel
    {
        [Key]
        public Guid NotificationId { get; set; }
        [StringLength(200)]
        public string NotificationUrl { get; set; }
        [StringLength(50)]
        public string SaleOrg { get; set; }
        [StringLength(400)]
        public string NotificationContent { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? NotificationDateTime { get; set; }
        public bool? isComplete { get; set; }
    }
}