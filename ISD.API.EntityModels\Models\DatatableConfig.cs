﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DatatableConfig", Schema = "utilities")]
    public partial class DatatableConfig
    {
        [Key]
        public Guid ConfigId { get; set; }
        public Guid? AccountId { get; set; }
        public Guid PageId { get; set; }
        public int ColIndex { get; set; }
        [Required]
        [StringLength(200)]
        public string ColName { get; set; }
        [StringLength(200)]
        public string ColClass { get; set; }
        public bool? IsVisible { get; set; }
    }
}