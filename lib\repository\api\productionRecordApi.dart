import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postProduction.dart';
import '../../model/productionRecord.dart';
import '../../urlApi/urlApi.dart';

class ProductionRecordApi {
  static Future<http.Response> getProduction(String barcode, String token) async {
    final data = {"Barcode": barcode};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlProductionRecord}ProductionRecord2?Barcode=${data['Barcode']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postProduction(
      ProductionOrderViewModel productionOrderViewModel,
      List<ProductionOrderDetailOld> productionOrderDetailOld,
      List<UsageQuantityViewModels> usageQuantityViewModels,
      Data getData,
      String token,
      ListStepCode? selectedStepCode) async {
    Map data = {};
    if (getData.productionRecord!.stepCode == null) {
      if (selectedStepCode != null) {
        data = {"productionOrderViewModel": productionOrderViewModel, "productionOrderDetailOld": productionOrderDetailOld};
      } else {
        data = {
          "productionOrderViewModel": productionOrderViewModel,
        };
      }
    } else {
      if (usageQuantityViewModels.isNotEmpty) {
        data = {
          "productionOrderViewModel": productionOrderViewModel,
          "productionOrderDetailOld": productionOrderDetailOld,
          "usageQuantityViewModels": usageQuantityViewModels
        };
        if (kDebugMode) {
          print(usageQuantityViewModels[usageQuantityViewModels.length - 1].bmschdc);
          print(usageQuantityViewModels.length);
        }
      } else {
        data = {
          "productionOrderViewModel": productionOrderViewModel,
          "productionOrderDetailOld": productionOrderDetailOld,
        };
      }
    }
    var body = json.encode(data);
    if (kDebugMode) {
      print(body);
    }
    // final environment = await SecureStorage.getString("environment", null);
    // final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // var url = Uri.parse(baseUrl + UrlApi.baseUrlProductionRecord + "ProductionRecord");
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlProductionRecord + "ProductionRecord2");
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersToken(token), body: body);

    return response;
  }
}
