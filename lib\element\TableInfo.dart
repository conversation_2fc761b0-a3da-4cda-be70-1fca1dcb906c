import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TableInfo extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const TableInfo({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border.all(
                  color: Colors.black,
                  width: 0.5.w,
                ),
              ),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    top: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableInfoNoTop extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const TableInfoNoTop({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border(
                  left: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL2),
                border: Border(
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableInfoNoTop3T extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final String textCL3;
  final int colorCL1;
  final int colorCL2;
  final int colorCL3;
  const TableInfoNoTop3T({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.textCL3,
    required this.colorCL1,
    required this.colorCL2,
    required this.colorCL3,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border(
                  left: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              alignment: Alignment.centerRight,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL2),
                border: Border(
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL3),
                border: Border(
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL3,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
