﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("LogApiModel", Schema = "system")]
    public partial class LogApiModel
    {
        [Key]
        public Guid Id { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
        public int? StatusCode { get; set; }
        [StringLength(2000)]
        public string Url { get; set; }
        [StringLength(20)]
        public string Method { get; set; }
        public string Request { get; set; }
        public string Response { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
    }
}