class GetListSOWBSByBatchTranferWareHouse {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetListSOWBSByBatchTranferWareHouse>? data;

  GetListSOWBSByBatchTranferWareHouse({this.code, this.isSuccess, this.message, this.data});

  GetListSOWBSByBatchTranferWareHouse.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetListSOWBSByBatchTranferWareHouse>[];
      json['data'].forEach((v) {
        data!.add(DataGetListSOWBSByBatchTranferWareHouse.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetListSOWBSByBatchTranferWareHouse {
  String? productCode;
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;
  String? lsxdt;
  String? batchNumber;
  String? rawMaterialCardId;

  DataGetListSOWBSByBatchTranferWareHouse({
    this.so,
    this.soLine,
    this.wbs,
    this.quantity,
    this.unit,
    this.lsxdt,
    this.batchNumber,
    this.rawMaterialCardId,
  });

  DataGetListSOWBSByBatchTranferWareHouse.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantity = json['quantity'];
    unit = json['unit'];
    lsxdt = json['lsxdt'];
    rawMaterialCardId = json['rawMaterialCardId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['lsxdt'] = lsxdt;
    data['batchNumber'] = batchNumber;
    data['rawMaterialCardId'] = rawMaterialCardId;
    return data;
  }
}
