/*
Change History:
--------------
2024-03-25 | <PERSON> | Initial creation
2024-03-25 | <PERSON> | Added form layout and styling to match BaoCaoQCGiaCongDetail
2024-03-25 | <PERSON> | Added date picker and time picker functionality
2024-03-25 | <PERSON> | Fixed date/time format display and server communication
2024-03-25 | <PERSON> | Adjusted UI components to match design system

Summary of Changes:
------------------
- Implemented form layout using FormLayout widget
- Added date picker with dd-MM-yyyy format
- Added time pickers with hh:mm AM/PM format
- Fixed date/time format conversion between UI and server
- Styled form fields to match design system
- Added validation and error handling
*/

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../model/userModel.dart';
import '../../model/downtimeModel.dart';
import '../../repository/function/downtimeFunction.dart';
import '../../element/QualityFormFields.dart';
import '../LostConnect.dart';
import '../../element/FormLayout.dart';
import 'package:intl/intl.dart';

class DowntimeDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;

  const DowntimeDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _DowntimeDetailState createState() => _DowntimeDetailState();
}

class _DowntimeDetailState extends State<DowntimeDetail> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _teamController = TextEditingController();
  final TextEditingController _workstationController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();
  final TextEditingController _responsibleTeamController = TextEditingController();
  final TextEditingController _responsibleDepartmentController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();

  String? _status;

  List<DowntimeHistory>? _history;
  bool _isHistoryExpanded = false;

  @override
  void initState() {
    super.initState();
    if (widget.id.isNotEmpty) {
      _loadData();
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      final record = await DowntimeFunction.fetchDowntimeDetail(
        widget.user.token!,
        widget.id,
      );

      if (!mounted) return;

      if (record != null) {
        setState(() {
          if (record.date != null) {
            try {
              final date = DateTime.parse(record.date!);
              _dateController.text = DateFormat('dd-MM-yyyy').format(date);
            } catch (e) {
              _dateController.text = record.date ?? '';
            }
          }

          if (record.startTime != null) {
            try {
              final time = DateFormat('HH:mm').parse(record.startTime!);
              _startTimeController.text = DateFormat('hh:mm a').format(time);
            } catch (e) {
              _startTimeController.text = record.startTime ?? '';
            }
          }

          if (record.endTime != null) {
            try {
              final time = DateFormat('HH:mm').parse(record.endTime!);
              _endTimeController.text = DateFormat('hh:mm a').format(time);
            } catch (e) {
              _endTimeController.text = record.endTime ?? '';
            }
          }

          _teamController.text = record.departmentCode ?? '';
          _workstationController.text = record.stepCode ?? '';
          _reasonController.text = record.reason ?? '';
          _responsibleTeamController.text = record.responsibleTeam ?? '';
          _responsibleDepartmentController.text = record.responsibleDepartment ?? '';
          _noteController.text = record.note ?? '';
          _status = record.status;

          _history = record.history;
          debugPrint("History loaded: ${_history?.length ?? 0} items");
        });
      }

      setState(() {
        _isLoading = false;
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isError = true;
        _isLoading = false;
      });
    }
  }

  Future<void> _saveData() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      setState(() => _isSaving = true);

      String serverDate = _dateController.text;
      try {
        final date = DateFormat('dd-MM-yyyy').parse(_dateController.text);
        serverDate = DateFormat('yyyy-MM-dd').format(date);
      } catch (e) {
        debugPrint('Date parsing error: $e');
      }

      String? serverStartTime;
      String? serverEndTime;

      if (_startTimeController.text.isNotEmpty) {
        final startTime = DateFormat('hh:mm a').parse(_startTimeController.text);
        serverStartTime = DateFormat('HH:mm').format(startTime);
      }

      if (_endTimeController.text.isNotEmpty) {
        final endTime = DateFormat('hh:mm a').parse(_endTimeController.text);
        serverEndTime = DateFormat('HH:mm').format(endTime);
      }

      final record = DowntimeRecord(
        id: widget.id,
        date: serverDate,
        departmentCode: _teamController.text,
        stepCode: _workstationController.text,
        startTime: serverStartTime,
        endTime: serverEndTime,
        reason: _reasonController.text,
        responsibleTeam: _responsibleTeamController.text,
        responsibleDepartment: _responsibleDepartmentController.text,
        note: _noteController.text,
        status: _status,
      );

      final success = await DowntimeFunction.saveDowntime(
        widget.user.token!,
        record,
      );

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.id.isEmpty ? 'Tạo thành công' : 'Cập nhật thành công'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lưu thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã xảy ra lỗi'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  Future<void> _selectStartTime(BuildContext context) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );
      setState(() {
        _startTimeController.text = DateFormat('hh:mm a').format(dateTime);
      });
    }
  }

  Future<void> _selectEndTime(BuildContext context) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      final dateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );
      setState(() {
        _endTimeController.text = DateFormat('hh:mm a').format(dateTime);
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = DateFormat('dd-MM-yyyy').format(pickedDate);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.id.isEmpty ? 'Thêm Downtime' : 'Chi tiết Downtime',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          FormLayout(
            title: "Thông tin Downtime",
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Ngày',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: GestureDetector(
                        onTap: () => _selectDate(context),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _dateController.text.isEmpty ? 'Chọn ngày' : _dateController.text,
                                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                ),
                              ),
                              Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Tổ',
                controller: _teamController,
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Công đoạn',
                controller: _workstationController,
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Thời gian bắt đầu',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: GestureDetector(
                        onTap: () => _selectStartTime(context),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _startTimeController.text.isEmpty ? 'Chọn giờ' : _startTimeController.text,
                                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                ),
                              ),
                              Icon(Icons.access_time, size: 18.sp, color: Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Text(
                        'Thời gian kết thúc',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: GestureDetector(
                        onTap: () => _selectEndTime(context),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _endTimeController.text.isEmpty ? 'Chọn giờ' : _endTimeController.text,
                                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                ),
                              ),
                              Icon(Icons.access_time, size: 18.sp, color: Colors.blue),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Nguyên nhân',
                controller: _reasonController,
                maxLines: 2,
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Bộ phận chịu trách nhiệm',
                controller: _responsibleTeamController,
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Tỉ lệ trách nhiệm',
                controller: _responsibleDepartmentController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              SizedBox(height: 10.h),
              FormInputField(
                label: 'Ghi chú',
                controller: _noteController,
                maxLines: 3,
                required: false,
              ),
              if (widget.id.isNotEmpty) ...[
                SizedBox(height: 10.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Trạng thái',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 4.w),
                              isDense: true,
                              isExpanded: true,
                              value: _status,
                              items: ['Pending', 'In Progress', 'Completed']
                                  .map((status) => DropdownMenuItem(
                                        value: status,
                                        child: Text(
                                          status,
                                          style: TextStyle(fontSize: 12.sp),
                                        ),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  _status = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: FormSubmitButton(
              text: widget.id.isEmpty ? 'Tạo mới' : 'Cập nhật',
              onPressed: _saveData,
              isLoading: _isSaving,
            ),
          ),
          if (widget.id.isNotEmpty && _history != null && _history!.isNotEmpty)
            Container(
              margin: EdgeInsets.symmetric(horizontal: 5.w),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 15.w),
                    decoration: BoxDecoration(
                      color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4.r),
                        topRight: Radius.circular(4.r),
                      ),
                    ),
                    child: Text(
                      'Lịch sử thay đổi',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _history!.length,
                    itemBuilder: (context, index) => _buildHistoryItem(_history![index]),
                  ),
                ],
              ),
            ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(DowntimeHistory historyItem) {
    final isApproval = historyItem.action == 'APPROVAL';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 0.5,
            color: Colors.grey.shade200,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: isApproval ? Colors.green.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              isApproval ? Icons.approval : Icons.history,
              size: 14.sp,
              color: isApproval ? Colors.green : Colors.blue,
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: TextStyle(fontSize: 11.sp, color: Colors.black),
                    children: [
                      TextSpan(
                        text: historyItem.changedBy ?? '',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      if (isApproval) ...[
                        const TextSpan(text: ' đã '),
                        TextSpan(
                          text: historyItem.newStatus == 'APPROVED' ? 'phê duyệt' : 'từ chối',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: historyItem.newStatus == 'APPROVED' ? Colors.green : Colors.red,
                          ),
                        ),
                        if (historyItem.verifierRole != null) ...[
                          const TextSpan(text: ' với vai trò '),
                          TextSpan(
                            text: _getApprovalStepText(historyItem.verifierRole!),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ] else ...[
                        const TextSpan(text: ' đã thay đổi trạng thái từ '),
                        TextSpan(
                          text: historyItem.oldStatus ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const TextSpan(text: ' sang '),
                        TextSpan(
                          text: historyItem.newStatus ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ],
                  ),
                ),
                if (historyItem.comment != null && historyItem.comment!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(4.r),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Text(
                      historyItem.comment!,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
                SizedBox(height: 2.h),
                Text(
                  DateFormat('dd/MM/yyyy HH:mm').format(
                    DateTime.parse(historyItem.changedDate!),
                  ),
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getApprovalStepText(String step) {
    switch (step) {
      case 'TEAM_LEADER':
        return 'Tổ trưởng';
      case 'MANAGER':
        return 'Quản lý';
      case 'DIRECTOR':
        return 'Giám đốc';
      default:
        return step;
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _teamController.dispose();
    _workstationController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _reasonController.dispose();
    _responsibleTeamController.dispose();
    _responsibleDepartmentController.dispose();
    _noteController.dispose();
    super.dispose();
  }
}
