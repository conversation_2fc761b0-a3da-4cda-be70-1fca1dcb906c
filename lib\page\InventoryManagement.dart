import 'package:collection/collection.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/screenArguments/screenArgumentListTranferMaterial.dart';
import '../Widget/dialogWidget/DialogOptionCode.dart';
import '../model/dialogGetBarCodeModel.dart';
import '../model/userModel.dart';
import '../element/ButtonInventoryMNG.dart';
import '../repository/function/mainPageFunction.dart';
import '../screenArguments/ScreenArgumentInventMSloc.dart';
import '../screenArguments/screenArgumentImportWareHouse.dart';
import '../screenArguments/screenArgumentMaterialUnused.dart';
import '../screenArguments/screenArgumentQRPage.dart';
import '../screenArguments/screenArgumentStatisticsMaterials.dart';

class InventoryManagement extends StatelessWidget {
  const InventoryManagement(
      {Key? key, required this.permission, required this.token, required this.plant, required this.dateTimeOld, required this.accountId})
      : super(key: key);
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              size: 14.sp,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            'Quản lý NVL',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          ),
        ),
        body: _SingleChildInventoryManagement(
          permission: permission,
          token: token,
          dateTimeOld: dateTimeOld,
          plant: plant,
          accountId: accountId,
        ));
  }
}

class _SingleChildInventoryManagement extends StatelessWidget {
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  const _SingleChildInventoryManagement({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _InventoryManagementView(
            permission: permission,
            token: token,
            dateTimeOld: dateTimeOld,
            plant: plant,
            accountId: accountId,
          )),
    );
  }
}

class _InventoryManagementView extends StatelessWidget {
  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  const _InventoryManagementView({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        SizedBox(height: 10.h),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Nhập kho mua hàng") != null,
          child: ButtonInventoryMNG(
            txt: "Nhập kho NVL",
            icon: Icon(
              Icons.label_important_rounded,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () async {
              if (permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Nhập kho mua hàng") != null) {
                final check = await showDialog<DialogGetBarCodeModel>(
                  context: context,
                  builder: (BuildContext context) {
                    return const DialogOptionCode();
                  },
                );
                debugPrint(check.toString());
                if (check == null) return;
                if (check.changePageTo.isNotEmpty) {
                  if (check.changePageTo == "scan") {
                    Navigator.pushNamed(context, '/QRCodePage',
                        arguments: ScreenArgumentQRPage(
                          'importWareHouse',
                          token.toString(),
                          permission!,
                          plant,
                          dateTimeOld,
                        ));
                  } else {
                    Navigator.pushNamed(context, '/ImportWarehouseSAP',
                        arguments: ScreenArgumentImportWareHouse(
                          check.param,
                          token,
                          plant,
                          dateTimeOld,
                          "id",
                        ));
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    backgroundColor: Colors.black,
                    content: Text(
                      'Bạn không có quyền sử dụng tính năng này!',
                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                    ),
                    duration: const Duration(seconds: 1)));
              }
            },
          ),
        ),
        //     Visibility(
        //       visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Nhập kho mua hàng") != null,
        // child:
        //     Container(
        //       width: double.infinity,
        //       decoration: const BoxDecoration(),
        //       child: ElevatedButton(
        //         style: ButtonStyle(
        //           shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //               RoundedRectangleBorder(
        //                   borderRadius: BorderRadius.circular(5.r),
        //                   side: const BorderSide(color: Colors.white))),
        //           side: MaterialStateProperty.all(
        //             const BorderSide(
        //               color: Color(0xff0052cc),
        //             ),
        //           ),
        //           backgroundColor:
        //           MaterialStateProperty.all(const Color(0xff0052cc)),
        //         ),
        //         onPressed: () {
        //           if (permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Nhập kho mua hàng") != null) {
        //             Navigator.pushNamed(context, '/QRCodePage',
        //                 arguments: ScreenArgumentQRPage(
        //                     'importWareHouse',
        //                     token.toString(),
        //                     permission!,
        //                     plant));
        //           }else{
        //             ScaffoldMessenger.of(context)
        //                 .showSnackBar(SnackBar(
        //                 backgroundColor: Colors.black,
        //                 content: Text(
        //                   'Bạn không có quyền sử dụng tính năng này!',
        //                   style: TextStyle(
        //                       fontSize: 15.sp, color: Colors.white),
        //                 ),
        //                 duration: const Duration(seconds: 1)
        //             ));
        //           }
        //         },
        //         child: Container(
        //           margin: EdgeInsets.symmetric(vertical: 12.h),
        //           child: Text(
        //             'Nhập kho NVL',
        //             style: TextStyle(
        //                 color: Colors.white,
        //                 fontWeight: FontWeight.bold,
        //                 fontSize: 13.sp),
        //           ),
        //         ),
        //       ),
        //     )),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Chuyển kho") != null,
          child: SizedBox(
            height: 10.h,
          ),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Chuyển kho") != null,
          child: ButtonInventoryMNG(
            txt: "Chuyển kho NVL",
            icon: Icon(
              Icons.import_export_rounded,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () {
              Navigator.pushNamed(context, "/ListTranferMaterial",
                  arguments: ScreenArgumentListTranferMaterial(token, plant, dateTimeOld, accountId));
            },
          ),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Chuyển kho") != null,
          child: SizedBox(
            height: 10.h,
          ),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Chuyển kho") != null,
          child: ButtonInventoryMNG(
            txt: "Chuyển kho NVL (nhiều line)",
            icon: Icon(
              Icons.import_export_rounded,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () {
              Navigator.pushNamed(context, "/ListTranferMaterial2",
                  arguments: ScreenArgumentListTranferMaterial(token, plant, dateTimeOld, accountId));
            },
          ),
        ),
        // Visibility(
        //   visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Chuyển kho") != null,
        //     child: Container(
        //   width: double.infinity,
        //   decoration: const BoxDecoration(),
        //   child: ElevatedButton(
        //     style: ButtonStyle(
        //       shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //           RoundedRectangleBorder(
        //               borderRadius: BorderRadius.circular(5.r),
        //               side: const BorderSide(color: Colors.white))),
        //       side: MaterialStateProperty.all(
        //         const BorderSide(
        //           color: Color(0xff0052cc),
        //         ),
        //       ),
        //       backgroundColor:
        //       MaterialStateProperty.all(const Color(0xff0052cc)),
        //     ),
        //     onPressed: ()  {
        //       Navigator.pushNamed(context, "/ListTranferMaterial", arguments: ScreenArgumentListTranferMaterial(token, plant));
        //     },
        //     child: Container(
        //       margin: EdgeInsets.symmetric(vertical: 12.h),
        //       child: Text(
        //         'Chuyển kho NVL',
        //         style: TextStyle(
        //             color: Colors.white,
        //             fontWeight: FontWeight.bold,
        //             fontSize: 13.sp),
        //       ),
        //     ),
        //   ),
        // )),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Thống kê NVL sử dụng trong ca") != null,
          child: SizedBox(height: 10.h),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Thống kê NVL sử dụng trong ca") != null,
          child: ButtonInventoryMNG(
            txt: "Thống kê NVL sử dụng trong ngày",
            icon: Icon(
              Icons.note_alt_outlined,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () {
              Navigator.pushNamed(context, "/StatisticsMaterials", arguments: ScreenArgumentStatisticsMaterial(plant, token, dateTimeOld));
            },
          ),
        ),
        // Visibility(
        //   visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Thống kê NVL sử dụng trong ca") != null,
        //     child: Container(
        //   width: double.infinity,
        //   decoration: const BoxDecoration(),
        //   child: ElevatedButton(
        //     style: ButtonStyle(
        //       shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //           RoundedRectangleBorder(
        //               borderRadius: BorderRadius.circular(5.r),
        //               side: const BorderSide(color: Colors.white))),
        //       side: MaterialStateProperty.all(
        //         const BorderSide(
        //           color: Color(0xff0052cc),
        //         ),
        //       ),
        //       backgroundColor:
        //       MaterialStateProperty.all(const Color(0xff0052cc)),
        //     ),
        //     onPressed: ()  {
        //           Navigator.pushNamed(context, "/StatisticsMaterials",arguments: ScreenArgumentStatisticsMaterial(
        //               plant,
        //               token,
        //             ));
        //     },
        //     child: Container(
        //       margin: EdgeInsets.symmetric(vertical: 12.h),
        //       child: Text(
        //         'Thống kê NVL sử dụng trong ca',
        //         style: TextStyle(
        //             color: Colors.white,
        //             fontWeight: FontWeight.bold,
        //             fontSize: 13.sp),
        //       ),
        //     ),
        //   ),
        // )),

        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Ghi nhận NVL chưa sử dụng hết") != null,
          child: SizedBox(height: 10.h),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Ghi nhận NVL chưa sử dụng hết") != null,
          child: ButtonInventoryMNG(
            txt: "Ghi nhận NVL chưa sử dụng hết",
            icon: Icon(
              Icons.edit_note_rounded,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () {
              Navigator.pushNamed(context, "/MaterialUnused", arguments: ScreenArgumentMaterialUnused(token, dateTimeOld));
              // Navigator.pushNamed(context, '/QRCodePage',
              //     arguments: ScreenArgumentQRPage(
              //         'MaterialUnused',
              //         token.toString(),
              //         permission!,
              //         plant,
              //         dateTimeOld
              //     ));
            },
          ),
        ),
        // Visibility(
        //   visible:permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Ghi nhận NVL chưa sử dụng hết") != null,
        //     child: Container(
        //   width: double.infinity,
        //   decoration: const BoxDecoration(),
        //   child: ElevatedButton(
        //     style: ButtonStyle(
        //       shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //           RoundedRectangleBorder(
        //               borderRadius: BorderRadius.circular(5.r),
        //               side: const BorderSide(color: Colors.white))),
        //       side: MaterialStateProperty.all(
        //         const BorderSide(
        //           color: Color(0xff0052cc),
        //         ),
        //       ),
        //       backgroundColor:
        //       MaterialStateProperty.all(const Color(0xff0052cc)),
        //     ),
        //     onPressed: () {
        //         Navigator.pushNamed(context, '/QRCodePage',
        //             arguments: ScreenArgumentQRPage(
        //                 'MaterialUnused',
        //                 token.toString(),
        //                 permission!,
        //                 plant));
        //     },
        //     child: Container(
        //       margin: EdgeInsets.symmetric(vertical: 12.h),
        //       child: Text(
        //         'Ghi nhận NVL chưa sử dụng hết',
        //         style: TextStyle(
        //             color: Colors.white,
        //             fontWeight: FontWeight.bold,
        //             fontSize: 13.sp),
        //       ),
        //     ),
        //   ),
        // )),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Xác định NVL cho đơn hàng lẻ") != null,
          child: SizedBox(height: 10.h),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Xác định NVL cho đơn hàng lẻ") != null,
          child: ButtonInventoryMNG(
            txt: "Xác định NVL cho đơn hàng lẻ",
            icon: Icon(
              Icons.radio_button_checked,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () {
              Navigator.pushNamed(context, '/QRCodePage',
                  arguments: ScreenArgumentQRPage('MaterialRetail', token.toString(), permission!, plant, dateTimeOld));
            },
          ),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Kiểm tra tồn kho NVL") != null,
          child: SizedBox(height: 10.h),
        ),
        Visibility(
          visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Kiểm tra tồn kho NVL") != null,
          child: ButtonInventoryMNG(
            txt: "Kiểm tra tồn kho NVL",
            icon: Icon(
              Icons.search_rounded,
              size: 20.sp,
              color: Colors.white,
            ),
            route: () async {
              final result = await MainPageFunction.checkConnectNetwork();
              if (result != ConnectivityResult.none) {
                Navigator.pushNamed(context, '/InventoryMaterialSloc',
                    arguments: ScreenArgumentInventMSloc(token: token.toString(), plant: plant.toString(), dateTimeOld: dateTimeOld));
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      backgroundColor: Colors.black,
                      content: Text(
                        'Tính năng cần có kết nối internet để sử dụng',
                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                      ),
                      duration: const Duration(seconds: 1)),
                );
              }
            },
          ),
        ),
        // Visibility(
        //   visible: permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Xác định NVL cho đơn hàng lẻ") != null,
        //   child: Container(
        //   width: double.infinity,
        //   decoration: const BoxDecoration(),
        //   child: ElevatedButton(
        //     style: ButtonStyle(
        //       shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //           RoundedRectangleBorder(
        //               borderRadius: BorderRadius.circular(5.r),
        //               side: const BorderSide(color: Colors.white))),
        //       side: MaterialStateProperty.all(
        //         const BorderSide(
        //           color: Color(0xff0052cc),
        //         ),
        //       ),
        //       backgroundColor:
        //       MaterialStateProperty.all(const Color(0xff0052cc)),
        //     ),
        //     onPressed: () {
        //         Navigator.pushNamed(context, '/QRCodePage',
        //             arguments: ScreenArgumentQRPage(
        //                 'MaterialRetail',
        //                 token.toString(),
        //                 permission!,
        //                 plant));
        //     },
        //     child: Container(
        //       margin: EdgeInsets.symmetric(vertical: 12.h),
        //       child: Text(
        //         'Xác định NVL cho đơn hàng lẻ',
        //         style: TextStyle(
        //             color: Colors.white,
        //             fontWeight: FontWeight.bold,
        //             fontSize: 13.sp),
        //       ),
        //     ),
        //   ),
        // )),
      ],
    );
  }
}
