# 1. TypeAhead TextField Component

## 1.1. Suggestion TextField API call, X button to clear (LSX SAP)

**📁 Location**: `lib\page\KiemTraChatLuong\BaoCaoQCSanPhamDetail.dart` (Lines: 2481-2600)

### ✨ What it does
Auto-suggest text field for LSX SAP codes with real-time API search and clear functionality.

### 🔧 Key Features
- **Minimum input**: 3 characters required
- **Real-time suggestions**: API call on text change
- **Clear functionality**: X button to reset field
- **Auto-complete**: Select suggestion → load details
- **Input validation**: Numbers only (LSX SAP)
- **Conditional enable/disable**: Based on quality control state

### 💻 Implementation Details
- **Component**: `Type<PERSON><PERSON>Field` from `flutter_typeahead`
- **API Function**: `filterLSXSAP(pattern)` → `fetchLSXSAP()`
- **Selection Handler**: `onSuggestionSelected()` → `_loadQualityControlByLSQSAP()`
- **State Management**: `_controllerLSXSAP`, `_isLSXSAPSelected`, `_isTTFCode`
- **Clear Logic**: `_checkCanClearLSX()` conditions

### ⚙️ Key Properties
```dart
minCharsForSuggestions: 3
keyboardType: TextInputType.number
inputFormatters: [FilteringTextInputFormatter.digitsOnly]
suggestionsBoxDecoration: BoxConstraints(minHeight: 200.h, maxHeight: 200.h)
enabled: checkQualityControl() && _isTTFCode != true
```

### 🔄 User Flow
1. **Input** → User types ≥3 characters
2. **API Call** → `filterLSXSAP()` calls server
3. **Display** → Show suggestions dropdown
4. **Select** → User chooses item
5. **Load** → `_loadQualityControlByLSQSAP()` fetches details
6. **Update** → UI state refreshed

### ⚠️ Common Issues
- **No suggestions**: Check if input has ≥3 characters
- **Field disabled**: Verify `checkQualityControl()` and `_isTTFCode` states
- **Clear button hidden**: Ensure `_checkCanClearLSX()` conditions are met
- **API errors**: Check network connection and token validity

### 💡 Tips
- Add debouncing to reduce API calls
- Show loading indicator during API requests
- Cache recent suggestions for better performance


## 1.2. AutoComplete Field with Loading State (Department Selection)

**📁 Location**: `lib\page\Downtime\element\AutoCompleteField.dart` (Lines: 1-273)
**🎯 Usage**: `lib\page\Downtime\DowntimeDetail.dart` (Lines: 806-831)

### ✨ What it does
Reusable auto-complete field component with loading states, clear functionality, and focus management for dropdown suggestions.

### 🔧 Key Features
- **Custom suggestions**: Uses provided list for filtering
- **Loading state**: Shows spinner during data fetch
- **Clear functionality**: X button when field has text and is focused
- **Focus management**: Auto-opens/closes suggestions on focus
- **Multi-line support**: Supports 1-2 lines of text
- **Flexible layout**: Optional label with responsive flex layout
- **Input validation**: Supports custom keyboard type and formatters

### 💻 Implementation Details
- **Component**: Custom wrapper around `TypeAheadField`
- **Props**: `enabled`, `suggestions`, `controller`, `onChanged`, `onSuggestionSelected`
- **State Management**: `_isFocused`, `_focusNode`, `_suggestionsBoxController`
- **Loading Logic**: Shows spinner when `isLoading = true`
- **Clear Logic**: Visible when text exists and field is focused

### ⚙️ Key Properties
```dart
// Required props
enabled: bool
suggestions: List<String>
onSuggestionSelected: Function(String)
controller: TextEditingController
onChanged: ValueChanged<String>
label: String

// Optional props
isLoading: false
keyboardType: TextInputType.text
inputFormatters: null
```

### 🔄 Component Flow
1. **Focus** → User taps field, `_isFocused = true`
2. **Type** → User types, `onChanged` triggers
3. **Filter** → Local filtering of suggestions list
4. **Display** → Show filtered suggestions dropdown
5. **Select** → User chooses item, `onSuggestionSelected` fires
6. **Update** → Controller text updated, dropdown closes

### 📋 Usage Example
```dart
AutoCompleteField(
  label: 'Tổ *',
  enabled: !widget.isViewMode,
  suggestions: _departmentSuggestions,
  controller: _departmentController,
  isLoading: _isDepartmentLoading,
  onChanged: (value) {
    debugPrint('Department value changed: $value');
  },
  onSuggestionSelected: (suggestion) {
    setState(() {
      _departmentController.text = suggestion;
    });
  },
)
```

### ⚠️ Common Issues
- **No suggestions showing**: Check if `suggestions` list is populated
- **Clear button not visible**: Ensure field is focused and has text
- **Loading state stuck**: Verify `isLoading` is properly toggled
- **Dropdown not closing**: Focus management issue, check `_focusNode`

### 💡 Differences from TypeAhead TextField
- **Reusable component** vs inline implementation
- **Local filtering** vs API-based suggestions
- **Loading state management** built-in
- **Better focus handling** with automatic open/close
- **Responsive layout** with optional labels


## 1.3. Generic API Suggestion Field (Reusable Component)

**📁 Location**: `lib\element\ApiSuggestionField.dart` (Lines: 1-270)
**🎯 Usage Example**: `docs\components\ApiSuggestionField_Usage.md`

### ✨ What it does
Generic, reusable API-based suggestion field that can replace all TypeAhead implementations across the app.

### 🔧 Key Features
- **API-based suggestions**: Configurable async callback for fetching data
- **Built-in loading states**: Automatic loading indicators during API calls
- **Smart clear button**: Shows/hides based on custom logic or default behavior
- **Camera scan support**: Optional QR/barcode scanner with custom callback
- **Error handling**: Try-catch wrapper around API calls with fallback
- **Flexible styling**: Customizable colors, borders, fonts, and layouts
- **Input validation**: Supports formatters and keyboard types
- **Responsive design**: Auto-adjusts layout with/without labels

### 💻 Implementation Details
- **Base Component**: Custom wrapper around `TypeAheadField`
- **Required Props**: `label`, `controller`, `suggestionsCallback`, `onSuggestionSelected`
- **State Management**: Internal `_isLoadingSuggestions` state
- **Error Handling**: Built-in try-catch with debug logging
- **Memory Management**: Proper controller listener cleanup

### ⚙️ Key Properties
```dart
// Required
label: String
controller: TextEditingController
suggestionsCallback: Future<List<String>> Function(String pattern)
onSuggestionSelected: Future<void> Function(String selected)

// Optional
enabled: true
minCharsForSuggestions: 3
showClearButton: true
canClear: null (uses default logic)
isLoading: false
keyboardType: TextInputType.text

// Camera scan
showCameraButton: false
onCameraScan: null
canShowCamera: null (uses default logic)
cameraIcon: null (uses default camera icon)
cameraTooltip: 'Quét mã'
```

### 🔄 Usage Flow
1. **Initialize** → Create controller and callback functions
2. **Type** → User types ≥minChars, triggers API call
3. **Loading** → Shows spinner, calls `suggestionsCallback`
4. **Display** → Shows filtered results in dropdown
5. **Select** → User picks item, calls `onSuggestionSelected`
6. **Clear** → X button calls `onClear` callback

### 📋 Migration Example (LSX SAP)
```dart
// Replace 120+ lines of TypeAheadField code with:
ApiSuggestionField(
  label: "LSX SAP:",
  controller: _controllerLSXSAP,
  enabled: checkQualityControl() && _isTTFCode != true,
  hintText: "Vui lòng nhập LSX SAP (ít nhất 3 kí tự)",
  keyboardType: TextInputType.number,
  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
  canClear: () => _checkCanClearLSX(),
  suggestionsCallback: (pattern) => QualityControlDetailFunction.fetchLSXSAP(pattern, token),
  onSuggestionSelected: (suggestion) => _handleLSXSAPSelection(suggestion),
  onClear: () => _clearLSXSAPField(),
)
```

### 📱 Camera Scan Example (Storage Bin)
```dart
// With camera scan functionality:
ApiSuggestionField(
  label: "Storage Bin:",
  controller: _storageBinController,
  showCameraButton: true,
  canShowCamera: () => _selectedSloc != null,
  suggestionsCallback: (pattern) => StorageService.searchStorageBins(pattern),
  onSuggestionSelected: (bin) => _selectStorageBin(bin),
  onCameraScan: () async {
    final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
    if (data != null) {
      var storageBin = data as DataSlocAddress;
      _storageBinController.text = storageBin.defaultStorageBin!;
    }
  },
)
```

### ⚠️ Common Issues
- **No API calls**: Check if `minCharsForSuggestions` threshold is met
- **Loading stuck**: Ensure try-catch properly handles API errors
- **Clear not working**: Verify `canClear` function returns correct boolean
- **Memory leaks**: Component handles controller listener cleanup automatically

### 💡 Benefits vs Manual TypeAhead
- **80% less code** per implementation
- **Consistent UI/UX** across all suggestion fields
- **Better error handling** with built-in try-catch
- **Standardized loading states** and animations
- **Type-safe callbacks** with proper async handling


# 2. QR/Barcode Scanner Components

## 2.1. Generic QR Scanner (Reusable Component)

**📁 Location**: `lib\element\GenericQRScanner.dart` (Lines: 1-390)
**🎯 Usage Example**: `docs\components\GenericQRScanner_Usage.md`

### ✨ What it does
Reusable QR/barcode scanner with built-in validation, error handling, and customizable UI elements.

### 🔧 Key Features
- **Data validation**: Custom validation logic with async callbacks
- **Error handling**: Built-in error display with auto-retry mechanism
- **Loading states**: Shows processing indicator during validation
- **Flash toggle**: Optional flashlight control
- **Custom styling**: Configurable colors, text, and overlay
- **Auto/Manual return**: Choose automatic or manual result handling
- **Vibration feedback**: Optional haptic feedback on scan
- **Flexible UI**: Configurable title, subtitle, and buttons

### 💻 Implementation Details
- **Base Component**: Custom wrapper around `QRView` from `qr_code_scanner`
- **Required Props**: `onDataScanned` (validation callback)
- **Optional Props**: `title`, `subtitle`, styling options, behavior flags
- **State Management**: Internal `_isProcessing`, `_errorMessage` states
- **Error Handling**: Try-catch with custom error message formatting
- **Camera Management**: Proper pause/resume and disposal

### ⚙️ Key Properties
```dart
// Required
onDataScanned: Future<dynamic> Function(String scannedData)

// Optional UI
title: 'Quét mã'
subtitle: null
backgroundColor: Colors.black
overlayBorderColor: Colors.red
cutOutSize: 300

// Behavior
autoReturn: true
showFlashToggle: true
showBackButton: true
vibrationOnScan: true
pauseDurationAfterScan: Duration(seconds: 2)

// Error handling
getErrorMessage: String Function(dynamic error)?
```

### 🔄 Usage Flow
1. **Initialize** → Open scanner with validation callback
2. **Scan** → User scans QR code or barcode
3. **Process** → Shows loading, calls `onDataScanned` callback
4. **Validate** → Custom validation logic (API calls, format checks)
5. **Result** → Auto-return with data or show error message
6. **Retry** → On error, shows message and auto-resumes scanning

### 📋 Common Use Cases

#### Storage Bin Scanner
```dart
Navigator.push(context, MaterialPageRoute(
  builder: (context) => GenericQRScanner(
    title: 'Quét Storage Bin',
    onDataScanned: (scannedData) async {
      // Parse XML QR code data
      String convertedBarCode = '''<?xml version="1.0"?><data>$scannedData</data>''';
      final document = XmlDocument.parse(convertedBarCode);
      return _parseStorageBinData(document);
    },
    getErrorMessage: (error) => 'Mã QR Storage Bin không hợp lệ',
  ),
)).then((data) => _handleStorageBinData(data));
```

#### Product Code with API Validation
```dart
GenericQRScanner(
  title: 'Quét mã sản phẩm',
  subtitle: 'Quét mã vạch hoặc QR code sản phẩm',
  onDataScanned: (scannedData) async {
    final product = await ProductService.validateAndGetProduct(scannedData);
    if (product == null) throw Exception('Sản phẩm không tồn tại');
    return product;
  },
)
```

### ⚠️ Common Issues
- **Camera permission**: Ensure camera permissions are granted
- **Overlay typing**: Use proper type casting for `customOverlay`
- **Memory leaks**: Component handles camera disposal automatically
- **Error loops**: Built-in auto-retry with configurable delay

### 💡 Integration with ApiSuggestionField
```dart
ApiSuggestionField(
  showCameraButton: true,
  onCameraScan: () async {
    final data = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GenericQRScanner(
          title: 'Quét LSX SAP',
          onDataScanned: (scannedData) async {
            // Validate and return LSX data
            return await _validateLSXSAP(scannedData);
          },
        ),
      ),
    );
    if (data != null) _handleLSXSAPData(data);
  },
)
```

### 🔄 Migration from QRCodePageChooseAnAddress
```dart
// Before (Route-based)
final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');

// After (Component-based)
final data = await Navigator.push(context, MaterialPageRoute(
  builder: (context) => GenericQRScanner(
    title: 'Quét Storage Bin',
    onDataScanned: (scannedData) => _parseStorageBinQR(scannedData),
  ),
));
```

### 💡 Benefits vs Specific QR Screens
- **90% less code** per scanner implementation
- **Consistent error handling** across all scan types
- **Standardized UI/UX** with customizable styling
- **Better validation flow** with async callback pattern
- **Centralized camera management** and memory handling

# 3. Plant/CompanyCode dropdown

## 3.1. Plant/CompanyCode dropdown with loading state and cascading department load

**📁 Location**: `lib\page\KiemTraChatLuong\BaoCaoQCMauDetail.dart` (Lines: 4367-4392)

### ✨ What it does
This pattern describes loading a list of "Nhà máy" (Plants/Company Codes) into a selection mechanism (e.g., a dropdown), automatically selecting a default plant based on user profile or a provided code, and then triggering a subsequent load of related data like "Phân xưởng" (Workshops/Departments) based on the selected plant.

### 🔧 Key Features
- **Asynchronous Loading**: Fetches plant/company code data from a storage source (e.g., `SharedPreferences`).
- **Default Selection**: Automatically selects a plant based on an incoming `companyCode` parameter or the user's default `companyCode`. Falls back to the first item if the preferred one isn't found.
- **State Update**: Updates the UI state with the list of plant options and the selected plant.
- **Cascading Data Load**: Triggers a secondary data loading process (e.g., for workshops/departments via `_loadPhanXuongData()`) after the initial plant data is processed.
- **Error Handling**: Includes basic error logging if the data loading process fails.

### 💻 Implementation Details
- **Primary Function**: `_loadNhaMayAndPhanXuongData(String? companyCode)`
- **Data Source**: `LoginFunction.getCompanyListFromStorage(widget.user.userName!)` retrieves the list of available plants/companies.
- **Data Model**: Uses `SalesOrgCodes` (with `saleOrgCode` and `storeName`) to represent plant options.
- **State Variables**:
    - `_lsNhaMayOptions`: Holds the list of `SalesOrgCodes` for the dropdown.
    - `_selectedNhaMay`: Stores the currently selected `SalesOrgCodes` object.
- **Dependent Load**: Calls `_loadPhanXuongData()` to fetch department/workshop information related to the selected plant.

### ⚙️ Key Parameters/Variables
```dart
// Input parameter for the main function
String? companyCode // Optional: to pre-select a specific plant

// User-specific data
widget.user.companyCode // User's default company code, used for initial selection
widget.user.userName // Used to fetch the company list relevant to the user

// State
_lsNhaMayOptions // List<SalesOrgCodes>
_selectedNhaMay // SalesOrgCodes?
```

### 🔄 User Flow / Logic
1.  The `_loadNhaMayAndPhanXuongData` function is called, optionally with a `companyCode`.
2.  It attempts to fetch the list of company codes (plants) using `LoginFunction.getCompanyListFromStorage`.
3.  If successful and the component is still mounted:
    a.  The `_lsNhaMayOptions` list is populated with `SalesOrgCodes` objects.
    b.  `_selectedNhaMay` is set:
        i.  It tries to find a plant matching the provided `companyCode`.
        ii. If not found or `companyCode` is null, it tries to find a plant matching `widget.user.companyCode`.
        iii. If still not found, it defaults to the first plant in `_lsNhaMayOptions`.
    c.  The UI state is updated.
4.  The `_loadPhanXuongData` function is called to load associated workshops/departments.
5.  If any error occurs during this process, it's caught and printed to the debug console.

### ⚠️ Common Issues
- **Plant list empty**: Ensure `LoginFunction.getCompanyListFromStorage` returns valid data and the storage contains the expected company list for the user.
- **Incorrect default selection**: Verify the passed `companyCode`, the `widget.user.companyCode`, and the contents of `_lsNhaMayOptions`.
- **Workshop/Department data not loading**: Check the implementation and dependencies of `_loadPhanXuongData()`.
- **Data inconsistency**: Ensure that plant codes (e.g., `saleOrgCode`) are consistent across different data sources and user profiles.

### 💡 Tips
- Implement loading indicators (e.g., `isLoading` state) to provide user feedback while `_loadNhaMayAndPhanXuongData` and `_loadPhanXuongData` are executing.
- Handle the case where `_lsNhaMayOptions` might be empty after loading (e.g., show a message to the user or disable dependent fields).
- Consider more robust error handling, such as displaying user-friendly messages instead of just `debugPrint`.
- If "Phân xưởng" depends on "Nhà máy", ensure `_loadPhanXuongData` is always called with the correct context or after `_selectedNhaMay` is definitively set.

