using SapNwRfc;
using SapNwRfc.Pooling;
using iMES_API.ViewModel.SAP;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace ISD.API.Repositories.Utility
{
    public interface ISapTableService
    {
        DataTable FetchFromSAPTable(string tableName, string condition, List<string> fieldNames, int rowCount = 10, int offset = 0);
    }

    public class SapTableService : ISapTableService
    {
        private readonly ISapPooledConnection _sapPooledConnection;
        private readonly ILogger<SapTableService> _logger;

        public SapTableService(
            ISapPooledConnection sapPooledConnection,
            ILogger<SapTableService> logger)
        {
            _sapPooledConnection = sapPooledConnection ?? throw new ArgumentNullException(nameof(sapPooledConnection));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public DataTable FetchFromSAPTable(string tableName, string condition, List<string> fieldNames, int rowCount = 10, int offset = 0)
        {
            DataTable adoTable = new DataTable(tableName);

            try
            {
                // Check if condition contains ORDER BY
                string whereCondition = condition;
                string orderBy = "";

                if (condition.Contains("ORDER BY"))
                {
                    var parts = condition.Split(new[] { "ORDER BY" }, StringSplitOptions.None);
                    whereCondition = parts[0].Trim();
                    orderBy = parts[1].Trim();
                }

                // Split conditions and get operators for WHERE clause only
                string[] operators = { " AND ", " OR " };
                string[] conditions = whereCondition.Split(operators, StringSplitOptions.None);

                // Extract operators in order of appearance
                List<string> conditionOperators = new List<string>();
                foreach (string op in operators)
                {
                    int start = 0;
                    while (start < whereCondition.Length && whereCondition.IndexOf(op, start) >= 0)
                    {
                        conditionOperators.Add(op.Trim());
                        start = whereCondition.IndexOf(op, start) + op.Length;
                    }
                }

                // Build OPTIONS array with operators
                var optionsArray = BuildOptionsArray(conditions, conditionOperators);

                // Add ORDER BY as the last option if present
                if (!string.IsNullOrEmpty(orderBy))
                {
                    optionsArray.Add(new RFC_DB_OPT { TEXT = $"ORDER BY {orderBy}" });
                }



                var SapInputParam = new SapInputParam
                {
                    QUERY_TABLE = tableName,
                    //FIELDS = fieldsArray,
                    DELIMITER = "|",
                    ROWCOUNT = rowCount.ToString(),
                    ROWSKIPS = offset.ToString(),
                    OPTIONS = optionsArray.ToArray()
                };

                if (fieldNames != null)
                {
                    // Use fieldNames parameter to specify fields, or null for all fields (useful for counting)
                    var fieldsArray = fieldNames?.Select(fieldName => new RFC_DB_FLD { FIELDNAME = fieldName }).ToArray();

                    SapInputParam.FIELDS = fieldsArray;
                }

                var result = _sapPooledConnection.InvokeFunction<SapOutput>("RFC_READ_TABLE", SapInputParam);

                // Create columns based on the fields
                foreach (var field in result.FIELDS)
                {
                    adoTable.Columns.Add(field.FIELDNAME, typeof(string));
                }

                // Add data rows
                if (result.DATA != null)
                {
                    foreach (var row in result.DATA)
                    {
                        var values = row.WA.Split('|');
                        adoTable.Rows.Add(values);
                    }
                }

                return adoTable;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error fetching data from SAP table {tableName}: {ex.Message}");
                throw;
            }
        }

        private List<RFC_DB_OPT> BuildOptionsArray(string[] conditions, List<string> conditionOperators)
        {
            var optionsArray = new List<RFC_DB_OPT>();

            for (int i = 0; i < conditions.Length; i++)
            {
                string cond = conditions[i];

                // Handle IN clause splitting
                if (cond.Contains("IN ("))
                {
                    var parts = cond.Split(new[] { "IN (" }, StringSplitOptions.None);
                    var fieldName = parts[0].Trim();
                    var values = parts[1].TrimEnd(')').Split(',');

                    // Split values into chunks that will fit within 40 chars
                    for (int j = 0; j < values.Length; j += 2) // Reduced to 2 values per chunk
                    {
                        var chunk = values.Skip(j).Take(2);
                        string condition = j == 0
                            ? $"{fieldName} IN ({string.Join(",", chunk)})"
                            : $"{fieldName} IN ({string.Join(",", chunk)})";

                        optionsArray.Add(new RFC_DB_OPT { TEXT = condition });

                        // Add OR operator between chunks if there are more values
                        if (j + 2 < values.Length)
                        {
                            optionsArray.Add(new RFC_DB_OPT { TEXT = "OR" });
                        }
                    }

                    // Add the AND operator after the entire IN clause if this isn't the last condition
                    if (i < conditions.Length - 1)
                    {
                        optionsArray.Add(new RFC_DB_OPT { TEXT = conditionOperators[i] });
                    }
                }
                else
                {
                    // Handle regular conditions
                    optionsArray.Add(new RFC_DB_OPT { TEXT = cond });

                    // Add operator if this isn't the last condition
                    if (i < conditions.Length - 1)
                    {
                        optionsArray.Add(new RFC_DB_OPT { TEXT = conditionOperators[i] });
                    }
                }
            }

            return optionsArray;
        }
    }
}