﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RequestEccEmailConfigModel", Schema = "tMasterData")]
    public partial class RequestEccEmailConfigModel
    {
        [Key]
        public Guid Id { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string ToEmail { get; set; }
        [StringLength(255)]
        public string Subject { get; set; }
        public string EmailContent { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string FromEmail { get; set; }
        [StringLength(20)]
        [Unicode(false)]
        public string FromEmailPassword { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string Host { get; set; }
        public int? Port { get; set; }
    }
}