﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>-->
		<DisableImplicitNamespaceImports>true</DisableImplicitNamespaceImports>
		<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
	</PropertyGroup>
	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<UserSecretsId>3f9e8e53-1899-40d2-887e-9287c3aa7307</UserSecretsId>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	  <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
	</PropertyGroup>
	<PropertyGroup>
		<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="Areas\MES\Data\**" />
	  <Compile Remove="Areas\MES\Views\**" />
	  <Compile Remove="Areas\Permission\Data\**" />
	  <Compile Remove="Areas\Permission\Models\**" />
	  <Compile Remove="Areas\Permission\Views\**" />
	  <Compile Remove="Areas\Utilities\Data\**" />
	  <Compile Remove="Areas\Utilities\Models\**" />
	  <Compile Remove="Areas\Utilities\Views\**" />
	  <Compile Remove="Controllers\**" />
	  <Compile Remove="Upload\**" />
	  <Content Remove="Areas\MES\Data\**" />
	  <Content Remove="Areas\MES\Views\**" />
	  <Content Remove="Areas\Permission\Data\**" />
	  <Content Remove="Areas\Permission\Models\**" />
	  <Content Remove="Areas\Permission\Views\**" />
	  <Content Remove="Areas\Utilities\Data\**" />
	  <Content Remove="Areas\Utilities\Models\**" />
	  <Content Remove="Areas\Utilities\Views\**" />
	  <Content Remove="Controllers\**" />
	  <Content Remove="Upload\**" />
	  <EmbeddedResource Remove="Areas\MES\Data\**" />
	  <EmbeddedResource Remove="Areas\MES\Views\**" />
	  <EmbeddedResource Remove="Areas\Permission\Data\**" />
	  <EmbeddedResource Remove="Areas\Permission\Models\**" />
	  <EmbeddedResource Remove="Areas\Permission\Views\**" />
	  <EmbeddedResource Remove="Areas\Utilities\Data\**" />
	  <EmbeddedResource Remove="Areas\Utilities\Models\**" />
	  <EmbeddedResource Remove="Areas\Utilities\Views\**" />
	  <EmbeddedResource Remove="Controllers\**" />
	  <EmbeddedResource Remove="Upload\**" />
	  <None Remove="Areas\MES\Data\**" />
	  <None Remove="Areas\MES\Views\**" />
	  <None Remove="Areas\Permission\Data\**" />
	  <None Remove="Areas\Permission\Models\**" />
	  <None Remove="Areas\Permission\Views\**" />
	  <None Remove="Areas\Utilities\Data\**" />
	  <None Remove="Areas\Utilities\Models\**" />
	  <None Remove="Areas\Utilities\Views\**" />
	  <None Remove="Controllers\**" />
	  <None Remove="Upload\**" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Areas\MES\Controllers\ImportProductController - Copy.cs" />
	</ItemGroup>
	<ItemGroup>
	  <Content Remove="appsettings.Development.json" />
	  <Content Remove="appsettings.json" />
	</ItemGroup>
	<ItemGroup>
	  <_ContentIncludedByDefault Remove="efpt.config.json" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="11.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="EPPlus" Version="4.5.3.2" />
		<PackageReference Include="Hangfire.AspNetCore" Version="1.7.9" />
		<PackageReference Include="Hangfire.Core" Version="1.7.9" />
		<PackageReference Include="Hangfire.SqlServer" Version="1.7.9" />
		<PackageReference Include="HtmlAgilityPack" Version="1.11.63" />
		<PackageReference Include="Microsoft.AspNet.Identity.Core" Version="2.2.3" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.5" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Graph" Version="4.25.0" />
		<PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="6.35.0" />
		<PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.2.0" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.6" />
		<PackageReference Include="MongoDB.Driver" Version="2.24.0" />
		<PackageReference Include="RestSharp" Version="110.2.0" />
		<PackageReference Include="SapNwRfc" Version="1.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
		<PackageReference Include="System.ServiceModel.Federation" Version="4.8.*" />
		<PackageReference Include="System.ServiceModel.Http" Version="4.8.*" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
		<PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Connected Services\" />
		<Folder Include="Controllers\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="appsettings.Development.json" />
	  <None Include="appsettings.json">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\ISD.API.Constant\ISD.API.Constant.csproj" />
	  <ProjectReference Include="..\ISD.API.Core\ISD.API.Core.csproj" />
	  <ProjectReference Include="..\ISD.API.EntityModels\ISD.API.EntityModels.csproj" />
	  <ProjectReference Include="..\ISD.API.Repositories\ISD.API.Repositories.csproj" />
	  <ProjectReference Include="..\ISD.API.Resources\ISD.API.Resources.csproj" />
	  <ProjectReference Include="..\ISD.API.ViewModels\ISD.API.ViewModels.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="GoogleMaps.LocationServices">
	    <HintPath>..\ISD.API.Repositories\GoogleMaps.LocationServices.dll</HintPath>
	  </Reference>
	  <Reference Include="sapnwrfcsdk">
	    <HintPath>D:\DOWNLOADS\nwrfc750P_13-70002755\nwrfcsdk\lib\sapnwrfc.dll</HintPath>
	  </Reference>
	</ItemGroup>

</Project>
