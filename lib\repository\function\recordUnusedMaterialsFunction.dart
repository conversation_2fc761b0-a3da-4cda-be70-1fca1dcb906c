import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/Widget/dialogWidget/DialogErrorValidate.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../model/GetStepByProductBach.dart';
import '../../model/getInforMaterialUsedShift.dart';
import '../../model/getQuantityMaterialUsedShift.dart';
import '../../model/materialUnused.dart';
import '../api/getInforMaterialUsedShiftApi.dart';
import '../api/getStepByProductBatchApi.dart';
import '../api/recordUnusedMaterialsApi.dart';

class RecordUnusedMaterialsFunction {
  static bool checkIsSend = false;
  static Future<List<Steps>?> fetchStepByProductBach(String productCode, String batchNumber, String token) async {
    final response = await GetStepByProductBatchApi.getStepByProductBatchApi(productCode, batchNumber, token);
    if (response.statusCode == 200) {
      final getStepByProductBatchApi = GetStepByProductBach.fromJson(jsonDecode(response.body));
      if (getStepByProductBatchApi.code == 200 && getStepByProductBatchApi.isSuccess == true) {
        if (getStepByProductBatchApi.data != null && getStepByProductBatchApi.data!.steps != null) {
          return getStepByProductBatchApi.data!.steps;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<DataInforMaterialUsedShift?> getInforMaterialUsedShiftApi(String rawMaterialCardId, String token) async {
    final response = await GetInforMaterialUsedShiftApi.getInforMaterialUsedShiftApi(rawMaterialCardId, token);
    if (response.statusCode == 200) {
      final getInforMaterialUsedShift = GetInforMaterialUsedShift.fromJson(jsonDecode(response.body));
      if (getInforMaterialUsedShift.code == 200 && getInforMaterialUsedShift.isSuccess == true) {
        return getInforMaterialUsedShift.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataQuantityMaterialUsedShift>?> fetchQuantityMaterialUsedShift(
      String productCode, String stepCode, String batchNumber, String token) async {
    final response = await RecordUnusedMaterialsApi.getQuantityMaterialUsedShift(productCode, stepCode, batchNumber, token);
    if (response.statusCode == 200) {
      if (jsonDecode(response.body)['data'] != false) {
        final getInforMaterialUsedShift = GetQuantityMaterialUsedShift.fromJson(jsonDecode(response.body));
        if (getInforMaterialUsedShift.code == 200 && getInforMaterialUsedShift.isSuccess == true) {
          return getInforMaterialUsedShift.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<void> getMessasgeMaterialUnusedShift(MaterialUnusedModel materialUnused, String token, BuildContext context) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await GetInforMaterialUsedShiftApi.postMaterialUnused(materialUnused, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final getMessage = MaterialUnusedMessage.fromJson(jsonDecode(response.body));
        if (getMessage.code == 200 && getMessage.isSuccess == true) {
          if (kDebugMode) {
            print(getMessage.isSuccess);
          }
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                getMessage.message.toString(),
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 2)));
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context);
          });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: getMessage.message.toString()));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //     backgroundColor: Colors.black,
        //     content: Text(
        //       "Hệ thống xảy ra lỗi! vui lòng thử lại sau",
        //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
        //     ),
        //     duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }
}
