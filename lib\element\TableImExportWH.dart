import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/slocAddresse.dart';

class TableImExportWHDD extends StatelessWidget {
  final String textCL1;
  final DataSlocAddress? selectedSloc;
  final ValueChanged<DataSlocAddress?> onChange;
  final List<DataSlocAddress?>? lsDataSlocAddress;
  final String textCL3;
  final DataSlocAddress? selectedSlocInput;
  final ValueChanged<DataSlocAddress?> onChangeInput;
  final int colorCL1;
  final int colorCL2;
  final int colorCL3;
  final int colorCL4;
  final bool disableDropdown;
  final bool disableDropdownInput;
  const TableImExportWHDD({
    Key? key,
    required this.textCL1,
    required this.selectedSloc,
    required this.onChange,
    required this.lsDataSlocAddress,
    required this.textCL3,
    required this.selectedSlocInput,
    required this.onChangeInput,
    required this.colorCL1,
    required this.colorCL2,
    required this.colorCL3,
    required this.colorCL4,
    required this.disableDropdown,
    required this.disableDropdownInput,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL1),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: DropdownButtonHideUnderline(
                child: ButtonTheme(
                  child: DropdownButton<DataSlocAddress>(
                    isExpanded: true,
                    itemHeight: null,
                    isDense: true,
                    value: selectedSloc,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: disableDropdown != true ? onChange : null,
                    items: lsDataSlocAddress!.map((DataSlocAddress? wareHouse) {
                      return DropdownMenuItem<DataSlocAddress>(
                        value: wareHouse,
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 0.w),
                          child: Text(
                            wareHouse!.slocDisplay.toString(),
                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                          ),
                        ),
                      );
                    }).toList(),
                    selectedItemBuilder: (BuildContext context) {
                      return lsDataSlocAddress!.map<Widget>((DataSlocAddress? wareHouse) {
                        return Text(wareHouse!.slocDisplay.toString(),
                            style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL3),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL3,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL4),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<DataSlocAddress>(
                  isExpanded: true,
                  itemHeight: null,
                  isDense: true,
                  value: selectedSlocInput,
                  iconSize: 15.sp,
                  style: const TextStyle(color: Colors.white),
                  onChanged: disableDropdownInput != true ? onChangeInput : null,
                  items: lsDataSlocAddress!.map((DataSlocAddress? wareHouse) {
                    return DropdownMenuItem<DataSlocAddress>(
                      value: wareHouse,
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 5.h),
                        child: Text(wareHouse!.slocDisplay.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp)),
                      ),
                    );
                  }).toList(),
                  selectedItemBuilder: (BuildContext context) {
                    return lsDataSlocAddress!.map<Widget>((DataSlocAddress? wareHouse) {
                      return Text(wareHouse!.slocDisplay.toString(),
                          style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                    }).toList();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableImExportWHText extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final String textCL3;
  final String textCL4;
  final int colorCL1;
  final int colorCL2;
  final int colorCL3;
  final int colorCL4;
  const TableImExportWHText({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.textCL3,
    required this.textCL4,
    required this.colorCL1,
    required this.colorCL2,
    required this.colorCL3,
    required this.colorCL4,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL1),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL3),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL3,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL4),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL4,
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HeaderTableImExportWH extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const HeaderTableImExportWH({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border.all(
                  color: Colors.black,
                  width: 0.5.w,
                ),
              ),
              child: Center(
                child: Text(
                  textCL1,
                  style: TextStyle(fontSize: 12.sp, color: Colors.white),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    top: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Center(
                child: Text(
                  textCL2,
                  style: TextStyle(fontSize: 12.sp, color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableInfoExport extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const TableInfoExport({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border.all(
                  color: Colors.black,
                  width: 0.5.w,
                ),
              ),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 7,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                  color: Color(colorCL2),
                  border: Border(
                    top: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  )),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableInfoNoTopExport extends StatelessWidget {
  final String textCL1;
  final String textCL2;
  final int colorCL1;
  final int colorCL2;
  const TableInfoNoTopExport({
    Key? key,
    required this.textCL1,
    required this.textCL2,
    required this.colorCL1,
    required this.colorCL2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL1),
                border: Border(
                  left: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL1,
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 7,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Color(colorCL2),
                border: Border(
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                textCL2,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
