import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class QRcodePageGetWorkShopDepartment extends StatefulWidget {
  const QRcodePageGetWorkShopDepartment({Key? key}) : super(key: key);

  @override
  _QRcodePageGetWorkShopDepartmentState createState() =>
      _QRcodePageGetWorkShopDepartmentState();
}

class _QRcodePageGetWorkShopDepartmentState
    extends State<QRcodePageGetWorkShopDepartment> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;
  String? getBarcode;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    }
    controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
      await Future.delayed(const Duration(milliseconds: 500));
      await controller!.pauseCamera();
    Navigator.pop(context);
    return false;
    },
    child:Scaffold(
        body: Stack(alignment: Alignment.bottomCenter, children: <Widget>[
      _buildQrView(context),
      Positioned(
        child: buildButton(context),
      )
    ])));
  }

  Widget buildButton(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 30.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            IconButton(
                onPressed: () async {
                  await controller?.toggleFlash();
                  setState(() {});
                },
                icon: FutureBuilder<bool?>(
                  future: controller?.getFlashStatus(),
                  builder: (context, snapshot) {
                    if (snapshot.data != null) {
                      return Icon(
                          snapshot.data! ? Icons.flash_on : Icons.flash_off,
                          color: Colors.white,
                          size: 30.sp);
                    } else {
                      return Container();
                    }
                  },
                )),
            GestureDetector(
                onTap: () async {
                  await Future.delayed(const Duration(milliseconds: 500));
                  await controller!.pauseCamera();
                  Navigator.pop(context);
                },
                child: Text(
                  'Thoát',
                  style: TextStyle(color: Colors.blueAccent, fontSize: 18.sp),
                ))
          ],
        ));
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: (QRViewController controller) =>
          _onQRViewCreated(controller, context),
      overlay: QrScannerOverlayShape(
          borderColor: Colors.red,
          borderRadius: 10.r,
          borderLength: 30,
          borderWidth: 10,
          cutOutSize: 300.w),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller, BuildContext context) async {
    setState(() {
      this.controller = controller;
    });
    if (Platform.isAndroid) {
      await this.controller!.resumeCamera();
    }
    controller.scannedDataStream.listen((scanData) async {
      if (scanData.code != null) {
        String getBarcode = scanData.code.toString();
        await this.controller!.pauseCamera();
        Navigator.pop(context, getBarcode);
      }
    });
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
