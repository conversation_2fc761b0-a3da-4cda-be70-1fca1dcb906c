import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/model/getDepartentByApi.dart';

class DialogWorkShopDepartment extends StatelessWidget {
  final GetDepartentByApi? getDepartentByApi;
  const DialogWorkShopDepartment({Key? key, required this.getDepartentByApi})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: SingleChildScrollView(
        child: ListBody(
          children: <Widget>[
            Center(
              child: Text(
                getDepartentByApi!.data!.department.toString(),
                style: TextStyle(fontSize: 10.sp),
              ),
            ),
            Center(
              child: Text(
                getDepartentByApi!.data!.workshop.toString(),
                style: TextStyle(fontSize: 10.sp),
              ),
            )
          ],
        ),
      ),
    );
  }
}
