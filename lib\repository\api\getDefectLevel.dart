import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetDefectLevelApi {
  static Future<http.Response> getDefectLevelApi(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    String url = baseUrl + UrlApi.baseUrlCommon_2 + 'get-defect-level';
    debugPrint(url.toString());
    final response = await http.get(Uri.parse(url), headers: UrlApi.headersToken(token));
    return response;
  }
}
