﻿using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDAuthorizationAttribute]
    public class SwitchingStageController : ControllerBaseAPI
    {
        #region "Chuyển công đoạn" - lấy thông tin
        /// <summary>API "Chuyển công đoạn" - L<PERSON>y thông tin </summary>
        /// <param name="Barcode"></param>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/SwitchingStage/SwitchingStages?Barcode={Barcode}
        ///     Params: 
        ///             + version : 1
        ///             + Barcode  : B0C17D8A-E82A-490A-A6F6-CA5A2F582422e
        ///             
        /// OUT PUT
        ///  
        ///     additionalData : Danh sách công đoạn
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": {
        ///             "taskId": "bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///             "parentTaskId": "56fb2574-5ad5-421a-82fd-22c593fdb854",
        ///             "unit": "CAI",
        ///             "fromStepCode": "LON",
        ///             "fromStepName": "Lọng (m/ph)",
        ///             "productAttributes": "0101.b.9",
        ///             "productAttributesQty": 120.000,
        ///             "workDate": "2022-05-06T00:00:00",
        ///             "qty": 60,
        ///             "productCode": "530004331",
        ///             "productName": "Ghế dài Aspen-CH025ARHP Matte Black",
        ///             "productId": "95d35eb5-2d5f-440a-8714-453d17d3f7b7",
        ///             "productionOrder_SAP": "500016230",
        ///             "createByFullName": "Thongke002",
        ///             "createTime": "2022-05-09T14:08:59.357",
        ///             "lastEditByFullName": null,
        ///             "lastEditTime": null,
        ///             "actived": true,
        ///             "toStepCode": null,
        ///             "toStepName": null,
        ///             "productionOrder": "DT-346-21-FOH",
        ///             "summary": "DT-346-21-FOH-D1",
        ///             "productAttributesName": "Nối vai tựa dưới",
        ///             "poT12": "25x30x163",
        ///             "productAttributesUnit": "Cái",
        ///             "productAttributesQtyD": 120.00,
        ///             "listDetail": [
        ///                 {
        ///                     "ktext": "Nối vai tựa dưới",
        ///                     "quantity": 120.00
        ///                 }
        ///             ]
        ///         },
        ///         "additionalData": [
        ///             {
        ///                 "arbpL_SUB": "BA2",
        ///                 "display": "BA2 | Bào 2 Mặt"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "CAX",
        ///                 "display": "CAX | Cào Cước"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "CAT",
        ///                 "display": "CAT | Cắt Định Hình"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "GH2",
        ///                 "display": "GH2 | Ghép Ngang"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "KHO",
        ///                 "display": "KHO | Khoan"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "NHC",
        ///                 "display": "NHC | Nhám Cạnh"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "NHM",
        ///                 "display": "NHM | Nhám Mặt"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "PHA",
        ///                 "display": "PHA | Phay"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "RO1",
        ///                 "display": "RO1 | Rong Gỗ"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "TKP",
        ///                 "display": "TKP | Thiết Kế Phôi"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "DGO",
        ///                 "display": "DGO | Đóng Gói"
        ///             }
        ///         ]
        ///     }
        ///</remarks>
        [HttpGet("SwitchingStages")]
        public ActionResult GET(Guid? Barcode)
        {
            try
            {
                //Tìm LSX SAP theo barcode
                var TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

                //Nếu có lệnh thực thi thì hiển thị popup ghi nhận sản lượng
                //Nếu chưa có thì thêm thực thi lệnh sản xuất=> get data để hiển thị popup ghi nhận sản lượng
                if (TaskId == null || TaskId == Guid.Empty)
                {
                    return BadRequest(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Thẻ treo không hợp lệ, Do chưa được ghi nhận thẻ treo này lần nào, Anh / Chị vui lòng sử dụng chức năng \"PDA - Ghi nhận sản lượng\"!",
                    });
                    //Báo lỗi không tìm thấy barcode
                }

                #region Master
                #region Nếu có task theo barcode => show popup theo dữ liệu
                var dataMaster = new SwitchingStagesViewModel();
                dataMaster = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageByTaskId(TaskId);
                #endregion

                var CompanyCode = CurrentUser.CompanyCode;

                #region Create ViewBag "Công đoạn"
                var listStep = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, dataMaster.ProductAttributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();

                //khi chuyển công đoạn: cho phép lấy công đoạn của cụm 
                if (dataMaster.ProductAttributes.Split('.').Length > 1)
                {
                    string ProductParentAtrributes = dataMaster.ProductAttributes.Split('.')[0];
                    var parentStepCodeLst = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, ProductParentAtrributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();
                    listStep.AddRange(parentStepCodeLst);
                }

                listStep = listStep.GroupBy(p => new { p.ARBPL_SUB, p.display }).Select(p => new { p.Key.ARBPL_SUB, p.Key.display }).ToList();
                #endregion
                #endregion

                #region Detail
                var dataDetail = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageHistoryByTTLSX(TaskId);
                #endregion

                dataMaster.ListDetail = dataDetail;

                return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = null, Data = dataMaster, AdditionalData = listStep });
            }
            catch (Exception ex)
            {

                return BadRequest(new { Code = HttpStatusCode.BadRequest, Success = false,  Message = $"Lỗi hệ thống. {ex.Message}"});
            }

            
        }

        [HttpGet("SwitchingStages2")]
        public ActionResult GET(Guid? Barcode, string ver2 = null)
        {
            try
            {
                //Tìm LSX SAP theo barcode
                var TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

                //Nếu có lệnh thực thi thì hiển thị popup ghi nhận sản lượng
                //Nếu chưa có thì thêm thực thi lệnh sản xuất=> get data để hiển thị popup ghi nhận sản lượng
                if (TaskId == null || TaskId == Guid.Empty)
                {
                    return BadRequest(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Thẻ treo không hợp lệ, Do chưa được ghi nhận thẻ treo này lần nào, Anh / Chị vui lòng sử dụng chức năng \"PDA - Ghi nhận sản lượng\"!",
                    });
                    //Báo lỗi không tìm thấy barcode
                }

                #region Master
                #region Nếu có task theo barcode => show popup theo dữ liệu
                var dataMaster = new SwitchingStagesViewModel2();
                dataMaster = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageByTaskId2(TaskId);
                #endregion

                var CompanyCode = CurrentUser.CompanyCode;

                #region Create ViewBag "Công đoạn"
                var listStep = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, dataMaster.ProductAttributes, CompanyCode)
                                                                        .Where(x => x.ARBPL_SUB != dataMaster.FromStepCode)
                                                                        .Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 })
                                                                        .ToList();

                //khi chuyển công đoạn: cho phép lấy công đoạn của cụm 
                if (dataMaster.ProductAttributes.Split('.').Length > 1)
                {
                    string ProductParentAtrributes = dataMaster.ProductAttributes.Split('.')[0];
                    var parentStepCodeLst = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, ProductParentAtrributes, CompanyCode)
                                                                            .Where(x => x.ARBPL_SUB != dataMaster.FromStepCode)
                                                                            .Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 })
                                                                            .ToList();
                    listStep.AddRange(parentStepCodeLst);
                }

                listStep = listStep.GroupBy(p => new { p.ARBPL_SUB, p.display }).Select(p => new { p.Key.ARBPL_SUB, p.Key.display }).ToList();
                #endregion
                #endregion

                #region Detail
                var dataDetail = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageHistoryByTTLSX2(TaskId);
                #endregion

                dataMaster.ListDetail = dataDetail;

                return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = null, Data = dataMaster, AdditionalData = listStep });
            }
            catch (Exception ex)
            {

                return BadRequest(new { Code = HttpStatusCode.BadRequest, Success = false, Message = $"Lỗi hệ thống. {ex.Message}" });
            }


        }
        #endregion

        #region "Chuyển công đoạn" - Lưu thông tin
        /// <summary>API "Chuyển công đoạn" - Lưu thông tin</summary>
        /// 
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/SwitchingStage/SwitchingStages
        ///     Params: 
        ///             + version : 1
        ///  
        /// BODY
        /// 
        ///     {
        ///         "taskId": "bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///         "toStepCode": "BA2"
        ///     }
        /// 
        /// 
        /// OUT PUT
        /// 
        ///     {
        ///         code = 201,
        ///         success = true,
        ///         data = "Chuyển công đoạn thành công!"
        ///     }
        /// 
        /// 
        /// </remarks>
        [HttpPost("SwitchingStages")]
        public IActionResult POST([FromBody] SwitchingStagesPostVm switchingStagesViewModel)
        {
            var userAccountId = CurrentUser.AccountId;

            if (switchingStagesViewModel == null)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Lỗi! Vui lòng thử lại hoặc liên hệ bộ phận kỹ thuật",
                });
            }

            //Lấy ngày hiện tại
            var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            //Lấy thông tin Barcode
            var Barcode = switchingStagesViewModel.ToBarcode;

            #region Kiểm tra nhập
            //Báo lỗi nếu chưa nhập tổ
            //if (string.IsNullOrEmpty(Barcode) || Guid.Parse(Barcode) == Guid.Empty)
            //{
            //    return Json(new
            //    {
            //        Code = HttpStatusCode.NotModified,
            //        Success = false,
            //        Data = "Vui lòng quét mã QR!",
            //    });
            //}

            //Báo lỗi nếu chưa nhập Stepcode
            //if (string.IsNullOrEmpty(switchingStagesViewModel.ToStepCode))
            //{
            //    return Json(new
            //    {
            //        Code = HttpStatusCode.NotModified,
            //        Success = false,
            //        Data = "Vui lòng chọn công đoạn cần chuyển!",
            //    });
            //}

            if (switchingStagesViewModel.TaskId == null || switchingStagesViewModel.TaskId == Guid.Empty)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Lỗi! Vui lòng thử lại hoặc liên hệ bộ phận kỹ thuật",
                });
            }

            //Lấy chi tiết thông tin stock của lệnh thực thi cũ
            List<SwitchingStagesDetailVm> detailTTLSX = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageHistoryByTTLSX(switchingStagesViewModel.TaskId);

            if (detailTTLSX == null || detailTTLSX.Count() == 0)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Không có dữ liệu để chuyển!",
                });
            }
            #endregion
            #region Công đoạn
            //Lấy thông tin stock mới
            var stockIdNew = _unitOfWork.StockRepository.GetStockIdByStockCode(switchingStagesViewModel.ToStepCode);
            #endregion

            //Kiểm tra Barcode đã tồn tại chưa
            //var CheckBarcode = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

            #region Lệnh thực thi sản xuất mới
            //if (CheckBarcode == null || CheckBarcode == Guid.Empty)
            //{
            //    //Tạo TTLSX mới theo Barcode
            //    Guid HangTagId = Guid.Parse(Barcode);
            //    var handTag = _context.HangTagModel.Where(p => p.HangTagId == HangTagId).FirstOrDefault();
            //    if (handTag != null && handTag.CustomerReference.HasValue)
            //    {
            //        //Thêm mới lệnh thực thi và lấy dữ liệu
            //        switchingStagesViewModel.CustomerReference = _unitOfWork.ProductionManagementRepository.CreateNewExecutionTask(handTag.CustomerReference.Value, Barcode, CurrentUserId: CurrentUser.AccountId);
            //        var ttlxsNew = _context.ThucThiLenhSanXuatModel.FirstOrDefault(x => x.TaskId == switchingStagesViewModel.CustomerReference);
            //        if (ttlxsNew != null)
            //        {
            //            #region //Cập nhật công đoạn sản xuất
            //            ttlxsNew.Property6 = switchingStagesViewModel.ToStepCode;
            //            ttlxsNew.StockId = stockIdNew;
            //            ttlxsNew.Actived = true;
            //            #endregion
            //        }
            //        _context.Entry(ttlxsNew).State = EntityState.Modified;
            //    }
            //    else
            //    {
            //        return Json(new
            //        {
            //            Code = HttpStatusCode.NotModified,
            //            Success = false,
            //            Data = "Vui lòng chọn thẻ treo khác!",
            //        });
            //    }
            //}
            //else
            //{
            //    var ttlxsNew = _context.ThucThiLenhSanXuatModel.FirstOrDefault(x => x.TaskId == CheckBarcode);
            //    if (ttlxsNew.StockId == null || ttlxsNew.StockId == Guid.Empty)
            //    {
            //        ttlxsNew.Property6 = switchingStagesViewModel.ToStepCode;
            //        ttlxsNew.StockId = stockIdNew;
            //        ttlxsNew.Actived = true;
            //        _context.Entry(ttlxsNew).State = EntityState.Modified;
            //    }
            //    else
            //    {
            //        if (ttlxsNew.StockId != stockIdNew || ttlxsNew.ToStockCode != null)
            //        {
            //            return Json(new
            //            {
            //                Code = HttpStatusCode.NotModified,
            //                Success = false,
            //                Data = "Công đoạn chuyển đến không khớp. Vui lòng chọn thẻ treo khác!",
            //            });
            //        }
            //    }
            //    switchingStagesViewModel.CustomerReference = CheckBarcode;
            //}
            #endregion

            #region Lệnh thực thi sản xuất cũ
            //Lấy thông tin lênh thực thi cũ
            var ttlxsOld = _context.ThucThiLenhSanXuatModel.FirstOrDefault(x => x.TaskId == switchingStagesViewModel.TaskId);

            //Lấy thông tin stock cũ
            var stockIdOld = ttlxsOld.StockId;
            if (stockIdOld == null || stockIdOld == Guid.Empty)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng ghi nhận sản lượng trước khi chuyển công đoạn!",
                });
            }

            if (stockIdOld == stockIdNew)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Công đoạn \"kế tiếp\" không được trùng công đoạn \"hoàn thành\"!",
                });
            }

            if (ttlxsOld != null)
            {
                #region //Cập nhật công đoạn sản xuất
                //ttlxsOld.Actived = false;
                //ttlxsOld.ToStockId = stockIdNew;
                //ttlxsOld.ToStockCode = switchingStagesViewModel.ToStepCode;
                ttlxsOld.Property6 = switchingStagesViewModel.ToStepCode;
                ttlxsOld.StockId = stockIdNew;
                ttlxsOld.TransferTime = currentDate;
                ttlxsOld.ToTaskId = switchingStagesViewModel.CustomerReference;

                #endregion
            }
            _context.Entry(ttlxsOld).State = EntityState.Modified;
            #endregion



            //Lưu thông tin vào Transfer Detai
            foreach (var item in detailTTLSX)
            {
                //Get datekey from documentDate

                var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(currentDate);
                #region Lưu thông tin StockReceivingDetail
                StockReceivingDetailViewModel stockReceivingDetailViewModel = new StockReceivingDetailViewModel
                {
                    StockReceivingDetailId = Guid.NewGuid(),
                    DateKey = dateKey,
                    FromTime = currentDate,
                    ToTime = currentDate,
                    ProductId = item.ProductId,
                    ProductAttributes = item.ITMNO,
                    Quantity = -item.Quantity,
                    StockRecevingType = "D",
                    CustomerReference = item.CustomerReference,
                    StockId = stockIdOld,
                    CreateTime = currentDate,
                    CreateBy = userAccountId,
                    Phase = item.Phase,
                    MovementType = "TRANSFER"
                };
                _unitOfWork.StockRecevingDetailRepository.Create(stockReceivingDetailViewModel);
                #endregion

                #region Lưu thông tin transferDetail
                TransferDetailViewModel transferDetail = new TransferDetailViewModel
                {
                    TransferDetailId = Guid.NewGuid(),
                    DateKey = dateKey,
                    FromTime = currentDate,
                    ToTime = currentDate,
                    ProductId = item.ProductId,
                    ProductAttributes = item.ITMNO,
                    Quantity = item.Quantity,
                    StockRecevingType = "D",
                    FromCustomerReference = item.CustomerReference,
                    //CustomerReference = switchingStagesViewModel.CustomerReference,
                    FromStockId = stockIdOld,
                    ToStockId = stockIdNew,
                    CreateTime = currentDate,
                    CreateBy = userAccountId,
                    Phase = item.Phase,
                    MovementType = "MOVEIN"
                };
                _unitOfWork.TransferDetailRepository.Create(transferDetail);
                #endregion
            }

            _context.SaveChanges();


            return BadRequest(new
            {
                Code = HttpStatusCode.Created,
                Success = true,
                Data = "Chuyển công đoạn thành công!",
            });
        }
        #endregion
    }
}