import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import '../../../model/userModel.dart';
import '../../../model/maiDaoModel.dart';
import '../../../model/commonDateModel.dart';
import '../../../repository/function/listQcFunction.dart';
import '../../../utils/ui_helpers.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';

class FilterListMaiDao extends StatefulWidget {
  final String token;
  final MaiDaoSearchModel? searchModel;
  final DataUser user;
  final Function(MaiDaoSearchModel) onFilterSelected;

  const FilterListMaiDao({
    Key? key,
    required this.token,
    required this.searchModel,
    required this.user,
    required this.onFilterSelected,
  }) : super(key: key);

  @override
  _FilterListMaiDaoState createState() => _FilterListMaiDaoState();
}

class _FilterListMaiDaoState extends State<FilterListMaiDao> {
  bool _isLoading = false;
  List<CommonDates>? _commonDates;
  CommonDateModel? _commonDateModel;
  String? _selectedDateFilter;

  final TextEditingController _equipmentController = TextEditingController();
  final TextEditingController _materialController = TextEditingController();

  String? _selectedOperationType;
  DateTime? _fromDate;
  DateTime? _toDate;

  final List<String> _operationTypes = ["Mài dao", "Đắp dao"];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.token);

      if (dataDropdown != null) {
        setState(() {
          _commonDates = dataDropdown.data?.commonDates;
        });
      }

      // Set initial values from search model if available
      if (widget.searchModel != null) {
        _equipmentController.text = widget.searchModel!.equipmentCode ?? '';
        _materialController.text = widget.searchModel!.materialCode ?? '';
        _selectedOperationType = widget.searchModel!.operationType;
        _fromDate = widget.searchModel!.fromDate;
        _toDate = widget.searchModel!.toDate;

        // Determine which date filter is active
        if (_fromDate != null && _toDate != null && _commonDates != null) {
          for (var date in _commonDates!) {
            if (date.catalogCode == null) continue;

            final commonDateModel = await ListQCFunction.getCommonDateModel(
              date.catalogCode!,
              widget.token,
            );

            if (commonDateModel != null) {
              final fromDate = DateTime.parse(commonDateModel.fromDate!);
              final toDate = DateTime.parse(commonDateModel.toDate!);

              if (_fromDate!.difference(fromDate).inDays.abs() <= 1 && _toDate!.difference(toDate).inDays.abs() <= 1) {
                _selectedDateFilter = date.catalogCode;
                break;
              }
            }
          }
        }
      } else {
        // Default to "ThisWeek" if no search model
        _selectedDateFilter = "ThisWeek";
        final defaultCommonDate = _commonDates?.firstWhere(
          (element) => element.catalogCode == "ThisWeek",
          orElse: () => _commonDates!.first,
        );

        if (defaultCommonDate != null && defaultCommonDate.catalogCode != null) {
          final commonDateModel = await ListQCFunction.getCommonDateModel(
            defaultCommonDate.catalogCode!,
            widget.token,
          );

          if (commonDateModel != null) {
            _fromDate = DateTime.parse(commonDateModel.fromDate!);
            _toDate = DateTime.parse(commonDateModel.toDate!);
          }
        }
      }
    } catch (e) {
      debugPrint("Error in _initializeData: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? (_fromDate ?? DateTime.now()) : (_toDate ?? DateTime.now()),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
          _selectedDateFilter = null; // Reset predefined filter when custom date is selected
        } else {
          _toDate = picked;
          _selectedDateFilter = null; // Reset predefined filter when custom date is selected
        }
      });
    }
  }

  Future<void> _applyPredefinedDateFilter(String catalogCode) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final commonDateModel = await ListQCFunction.getCommonDateModel(
        catalogCode,
        widget.token,
      );

      if (commonDateModel != null) {
        setState(() {
          _fromDate = DateTime.parse(commonDateModel.fromDate!);
          _toDate = DateTime.parse(commonDateModel.toDate!);
          _selectedDateFilter = catalogCode;
        });
      }
    } catch (e) {
      debugPrint("Error in _applyPredefinedDateFilter: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilter() {
    if (_fromDate != null && _toDate != null && _fromDate!.isAfter(_toDate!)) {
      showToast(
        context: context,
        message: 'Từ ngày không thể sau đến ngày',
      );
      return;
    }

    final searchModel = MaiDaoSearchModel(
      equipmentCode: _equipmentController.text.isNotEmpty ? _equipmentController.text : null,
      materialCode: _materialController.text.isNotEmpty ? _materialController.text : null,
      operationType: _selectedOperationType,
      fromDate: _fromDate,
      toDate: _toDate,
      companyCode: widget.user.companyCode ?? '',
    );

    Navigator.pop(context);
    widget.onFilterSelected(searchModel);
  }

  void _resetFilter() {
    setState(() {
      _equipmentController.clear();
      _materialController.clear();
      _selectedOperationType = null;
      _selectedDateFilter = "ThisWeek";
      if (_selectedDateFilter != null) {
        _applyPredefinedDateFilter(_selectedDateFilter!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.85,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: Colors.blue,
          title: Text(
            'Bộ lọc',
            style: TextStyle(color: Colors.white, fontSize: 14.sp),
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: 19.sp),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Equipment Filter
                      Text(
                        'Thiết bị',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      AutoCompleteField(
                        label: '',
                        controller: _equipmentController,
                        suggestions: [],
                        onChanged: (value) {},
                        onSuggestionSelected: (value) {},
                        enabled: true,
                      ),
                      SizedBox(height: 16.h),

                      // Material Filter
                      Text(
                        'Vật tư',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      AutoCompleteField(
                        label: '',
                        controller: _materialController,
                        suggestions: [],
                        onChanged: (value) {},
                        onSuggestionSelected: (value) {},
                        enabled: true,
                      ),
                      SizedBox(height: 16.h),

                      // Operation Type Filter
                      Text(
                        'Nghiệp vụ',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedOperationType,
                            isExpanded: true,
                            hint: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.w),
                              child: Text(
                                'Chọn nghiệp vụ',
                                style: TextStyle(fontSize: 13.sp),
                              ),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 8.w),
                            items: _operationTypes.map((type) {
                              return DropdownMenuItem<String>(
                                value: type,
                                child: Text(type, style: TextStyle(fontSize: 13.sp)),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedOperationType = value;
                              });
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Date Range Filter
                      Text(
                        'Khoảng thời gian',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.h),

                      // Predefined date filters
                      Wrap(
                        spacing: 8.w,
                        children: (_commonDates ?? []).map((date) {
                          return FilterChip(
                            selected: _selectedDateFilter == date.catalogCode,
                            label: Text(
                              date.catalogTextVi ?? '',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: _selectedDateFilter == date.catalogCode ? Colors.white : Colors.black,
                              ),
                            ),
                            selectedColor: Colors.blue,
                            checkmarkColor: Colors.white,
                            backgroundColor: Colors.grey[200],
                            onSelected: (bool selected) {
                              if (selected && date.catalogCode != null) {
                                _applyPredefinedDateFilter(date.catalogCode!);
                              }
                            },
                          );
                        }).toList(),
                      ),

                      SizedBox(height: 16.h),

                      // Custom date range
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Từ ngày',
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                InkWell(
                                  onTap: () => _selectDate(context, true),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          _fromDate == null ? 'Chọn ngày' : DateFormat('dd/MM/yyyy').format(_fromDate!),
                                          style: TextStyle(fontSize: 13.sp),
                                        ),
                                        Icon(Icons.calendar_today, size: 16.sp),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Đến ngày',
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                InkWell(
                                  onTap: () => _selectDate(context, false),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          _toDate == null ? 'Chọn ngày' : DateFormat('dd/MM/yyyy').format(_toDate!),
                                          style: TextStyle(fontSize: 13.sp),
                                        ),
                                        Icon(Icons.calendar_today, size: 16.sp),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 24.h),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _resetFilter,
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: Colors.blue),
                                padding: EdgeInsets.symmetric(vertical: 12.h),
                              ),
                              child: Text(
                                'Đặt lại',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.blue,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _applyFilter,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                padding: EdgeInsets.symmetric(vertical: 12.h),
                              ),
                              child: Text(
                                'Áp dụng',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  @override
  void dispose() {
    _equipmentController.dispose();
    _materialController.dispose();
    super.dispose();
  }
}
