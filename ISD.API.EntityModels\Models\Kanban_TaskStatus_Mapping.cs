﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Kanban_TaskStatus_Mapping", Schema = "tMasterData")]
    public partial class Kanban_TaskStatus_Mapping
    {
        [Key]
        public Guid KanbanDetailId { get; set; }
        [Key]
        public Guid TaskStatusId { get; set; }
        [StringLength(200)]
        public string Note { get; set; }

        [ForeignKey("KanbanDetailId")]
        [InverseProperty("Kanban_TaskStatus_Mapping")]
        public virtual KanbanDetailModel KanbanDetail { get; set; }
        [ForeignKey("TaskStatusId")]
        [InverseProperty("Kanban_TaskStatus_Mapping")]
        public virtual TaskStatusModel TaskStatus { get; set; }
    }
}