using AutoMapper;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels.MES;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using System.Net;
using ISD.API.Repositories;
using System.Collections.Generic;
using ISD.API.ViewModels;
using ISD.API.Extensions;
using ISD.API.EntityModels.Data;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class DowntimeController : ControllerBaseAPI
    {
        private readonly IMapper _mapper;
        private readonly IDowntimeRepository _downtimeRepository;
        private readonly ILogger<DowntimeController> _logger;

        public DowntimeController(IMapper mapper, IDowntimeRepository downtimeRepository, ILogger<DowntimeController> logger)
        {
            _mapper = mapper;
            _downtimeRepository = downtimeRepository;
            _logger = logger;
        }

        public static List<DowntimeModel> GetDummyData()
        {
            return new List<DowntimeModel>
            {
                new DowntimeModel
                {
                    DowntimeId = Guid.NewGuid(),
                    StepCode = "STP001",
                },
                new DowntimeModel
                {
                    DowntimeId = Guid.NewGuid(),
                    StepCode = "STP002",
                }
            };
        }

        /// <summary>
        /// Get list of downtime records with optional filters
        /// </summary>
        /// <param name="searchModel">Search parameters</param>
        /// <returns>List of downtime records</returns>
        [HttpPost("DowntimeList")]
        public async Task<IActionResult> DowntimeList([FromBody] DowntimeSearchModel searchModel)
        {
            try
            {
                var records = await _downtimeRepository.GetDowntimeListAsync(searchModel);

                if (records == null || records.Count == 0)
                {
                    _logger.LogWarning($"GetDowntimeList returned no records for CompanyCode: {searchModel.CompanyCode ?? "All"}");
                    return Ok(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.OK,
                        IsSuccess = true,
                        Message = "Không tìm thấy dữ liệu",
                        Data = new List<DowntimeModel>()
                    });
                }

                _logger.LogDebug($"GetDowntimeList returned {records.Count} records");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = records
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetDowntimeList: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get downtime record by ID
        /// </summary>
        /// <param name="id">Downtime record ID</param>
        /// <returns>Downtime record details</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetDowntimeById(string id, [FromQuery] string companyCode = null)
        {
            _logger.LogDebug($"GetDowntimeById API called with id: {id}, companyCode: {companyCode ?? "All"}");
            try
            {
                var record = await _downtimeRepository.GetDowntimeByIdAsync(id, companyCode);

                if (record == null)
                {
                    _logger.LogWarning($"GetDowntimeById returned no record for id: {id}");
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = "Không tìm thấy bản ghi"
                    });
                }

                _logger.LogDebug($"GetDowntimeById returned a record for id: {id}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetDowntimeById: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Create new downtime record
        /// </summary>
        /// <param name="model">Downtime record data</param>
        /// <returns>Created downtime record</returns>
        [HttpPost("CreateDowntime")]
        public async Task<IActionResult> CreateDowntime([FromBody] DowntimePostViewModel model)
        {
            _logger.LogDebug($"CreateDowntime API called for CompanyCode: {model.CompanyCode}");
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("CreateDowntime called with invalid model state.");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid model state"
                    });
                }

                if (string.IsNullOrEmpty(model.CompanyCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "CompanyCode is required"
                    });
                }

                var record = await _downtimeRepository.CreateDowntimeAsync(model, CurrentUser.AccountId ?? Guid.Empty);

                _logger.LogDebug($"CreateDowntime succeeded for CompanyCode: {model.CompanyCode}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Tạo mới thành công",
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in CreateDowntime: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Update existing downtime record
        /// </summary>
        /// <param name="id">Record ID</param>
        /// <param name="model">Updated data</param>
        /// <returns>Updated downtime record</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateDowntime(string id, [FromBody] DowntimePostViewModel model)
        {
            _logger.LogDebug($"UpdateDowntime API called with id: {id}, CompanyCode: {model.CompanyCode}");
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("UpdateDowntime called with invalid model state.");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid model state"
                    });
                }

                if (string.IsNullOrEmpty(model.CompanyCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "CompanyCode is required"
                    });
                }

                var record = await _downtimeRepository.UpdateDowntimeAsync(id, model, CurrentUser.AccountId ?? Guid.Empty);
                var updateModel = _mapper.Map<DowntimeUpdateModel>(model);
                if (record == null)
                {
                    _logger.LogWarning($"UpdateDowntime returned no record for id: {id}");
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = "Không tìm thấy bản ghi"
                    });
                }

                _logger.LogDebug($"UpdateDowntime succeeded for id: {id}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Cập nhật thành công",
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in UpdateDowntime: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get master data for downtime form
        /// </summary>
        /// <returns>Master data for dropdowns</returns>
        [HttpGet("master-data")]
        public async Task<IActionResult> GetMasterData()
        {
            _logger.LogDebug("GetMasterData API called.");
            try
            {
                var masterData = await _downtimeRepository.GetMasterDataAsync();

                _logger.LogDebug("GetMasterData succeeded.");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = masterData
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMasterData: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("GetListDepartment")]
        public async Task<IActionResult> GetListDepartment([FromQuery] string companyCode)
        {
            _logger.LogDebug($"GetListDepartment API called with companyCode: {companyCode}");
            try
            {
                var departmentsList = await _context.DepartmentModel
                    .Include(x => x.Store)
                    .Where(x => x.Actived == true && 
                               x.Store.SaleOrgCode == companyCode)
                    .OrderBy(x => x.DepartmentCode)
                    .Select(x => new DepartmentDropdownItem
                    {
                        DepartmentCode = x.DepartmentCode,
                        DepartmentName = x.DepartmentName,
                        ToTruongCode = x.ToTruongCode
                    })
                    .ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = departmentsList
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetListDepartment: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("GetListStepCode")]
        public async Task<IActionResult> GetListStepCode([FromQuery] string companyCode)
        {
            _logger.LogDebug($"GetListStepCode API called with companyCode: {companyCode}");
            try
            {
                _logger.LogInformation($"GetListStepCode called with companyCode: {companyCode}");

                if (string.IsNullOrEmpty(companyCode))
                {
                    _logger.LogWarning("Company code is null or empty");
                    return Ok(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.OK,
                        IsSuccess = true,
                        Data = new List<RoutingDropdownItem>()
                    });
                }

                var steps = await _context.RoutingModel
                    .Where(x => x.Actived == true && x.Plant == companyCode)
                    .OrderBy(x => x.StepCode)
                    .Select(x => new RoutingDropdownItem
                    {
                        StepCode = x.StepCode,
                        StepName = x.StepName,
                        WorkCenter = x.WorkCenter,
                        KCSCode = x.KCSCode
                    })
                    .ToListAsync();

                _logger.LogDebug($"GetListStepCode returned {steps.Count} records.");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = steps
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetListStepCode: {ex}");
                throw;
            }
        }

        [HttpGet("GetEmployees")]
        public async Task<IActionResult> GetEmployees([FromQuery] string companyCode)
        {
            _logger.LogDebug($"GetEmployees API called with companyCode: {companyCode}");
            try
            {
                var employees = await _context.SalesEmployeeModel
                    .Where(x => x.Actived == true)
                    .Select(x => new EmployeeDropdownItem
                    {
                        EmployeeCode = x.SalesEmployeeCode,
                        EmployeeName = x.SalesEmployeeName
                    })
                    .ToListAsync();

                _logger.LogDebug($"GetEmployees returned {employees.Count} records.");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = employees
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetEmployees: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("test")]
        [AllowAnonymous]
        public IActionResult Test()
        {
            _logger.LogDebug("Test API called.");
            return Ok("DowntimeController is reachable");
        }

        [HttpGet("test2")]
        public IActionResult Test2()
        {
            _logger.LogDebug("Test2 API called.");
            return Ok("DowntimeController is reachable");
        }

        [HttpGet("{id}/history")]
        public async Task<IActionResult> GetDowntimeHistory(string id, [FromQuery] string companyCode = null)
        {
            _logger.LogDebug($"GetDowntimeHistory API called with id: {id}, companyCode: {companyCode ?? "All"}");
            try
            {
                var history = await _downtimeRepository.GetDowntimeHistoryAsync(id, companyCode);

                if (history == null || !history.Any())
                {
                    _logger.LogWarning($"GetDowntimeHistory returned no records for id: {id}");
                    return Ok(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.OK,
                        IsSuccess = true,
                        Message = "Không có lịch sử thay đổi",
                        Data = new List<DowntimeHistoryViewModel>()
                    });
                }

                _logger.LogDebug($"GetDowntimeHistory returned {history.Count} records for id: {id}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetDowntimeHistory: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }
    }
}