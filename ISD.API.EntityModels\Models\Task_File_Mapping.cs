﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Task_File_Mapping", Schema = "Task")]
    public partial class Task_File_Mapping
    {
        [Key]
        public Guid TaskId { get; set; }
        [Key]
        public Guid FileAttachmentId { get; set; }
        [StringLength(200)]
        public string Note { get; set; }

        [ForeignKey("FileAttachmentId")]
        [InverseProperty("Task_File_Mapping")]
        public virtual FileAttachmentModel FileAttachment { get; set; }
        [ForeignKey("TaskId")]
        [InverseProperty("Task_File_Mapping")]
        public virtual TaskModel Task { get; set; }
    }
}