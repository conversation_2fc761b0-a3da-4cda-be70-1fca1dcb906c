﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SearchResultTemplateModel", Schema = "utilities")]
    public partial class SearchResultTemplateModel
    {
        public SearchResultTemplateModel()
        {
            SearchResultDetailTemplateModel = new HashSet<SearchResultDetailTemplateModel>();
        }

        [Key]
        public Guid SearchResultTemplateId { get; set; }
        public bool? isSystem { get; set; }
        public Guid? AccountId { get; set; }
        public Guid? PageId { get; set; }
        [StringLength(100)]
        public string TemplateName { get; set; }
        public bool? IsDefaultTemplate { get; set; }

        [InverseProperty("SearchResultTemplate")]
        public virtual ICollection<SearchResultDetailTemplateModel> SearchResultDetailTemplateModel { get; set; }
    }
}