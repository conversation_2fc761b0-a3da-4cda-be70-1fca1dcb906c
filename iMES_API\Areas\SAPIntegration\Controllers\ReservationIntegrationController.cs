﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Resources;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Graph;
using System;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class ReservationIntegrationController : ControllerBaseAPI
    {
        #region Reservation Integration
        /// <summary>API "Tích hợp thông tin Reservation" - Thêm / Cập nhật</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/ReservationIntegration/Reservation
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        /// 
        ///     {
        ///         "header": {
        ///           "reservationNumber": "string",
        ///           "requestDate": "2022-08-12T09:42:04.804Z",
        ///           "movementType": "string",
        ///           "goodsRecipient": "string",
        ///           "costCenter": "string",
        ///           "assetNumber": "string",
        ///           "accountNumberCustomer": "string",
        ///           "orderNumber": "string",
        ///           "salesOrderNumber": "string",
        ///           "salesOrderNumberItem": "string",
        ///           "riPlant": "string",
        ///           "riStorageLocation": "string",
        ///           "wbsElement": "string"
        ///         },
        ///         "listDetail": [
        ///             {
        ///                 "reservationNumber": "string",
        ///                 "reservationItemNumber": "stri",
        ///                 "itemDeleted": "stri",
        ///                 "reservationGoodsMove": "s",
        ///                 "finalIssue": "s",
        ///                 "materialNumber": "string",
        ///                 "plant": "stri",
        ///                 "storageLocation": "string",
        ///                 "batchNumber": "string",
        ///                 "specialStockIndicator": "s",
        ///                 "reqComponentDate": "2022-08-12T09:42:04.804Z",
        ///                 "reqQuantity": 0,
        ///                 "measureUnit": "str",
        ///                 "dcIndicator": "s",
        ///                 "unitEntry": "str",
        ///                 "unitEntryQuantity": 0,
        ///                 "orderNumber": "string",
        ///                 "salesOrderNumber": "string",
        ///                 "salesOrderNumberItem": "string",
        ///                 "riPlant": "stri",
        ///                 "riStorageLocation": "stri",
        ///                 "inputQuantity": 0,
        ///                 "measureUnitNumerator": 0,
        ///                 "measureUnitDenominator": 0,
        ///                 "wbsElement": "string"
        ///             }
        ///         ]
        ///     }
        /// OUT PUT
        /// 
        ///     {
        ///         code": 200,
        ///         isSuccess": true,
        ///         message": "Tích hợp thành công Reservation",
        ///         data": {
        ///            reservationId": "cb208837-7d26-4507-b743-079298241d07",
        ///            reservationNumber": "123",
        ///            reservationNumberInt": 123,
        ///            reservationItemNumber": "123",
        ///            reservationItemNumberInt": 123,
        ///            itemDeleted": "x",
        ///            reservationGoodsMove": "x",
        ///            finalIssue": "x",
        ///            materialNumber": "123",
        ///            plant": "123",
        ///            storageLocation": "123",
        ///            batchNumber": "123",
        ///            specialStockIndicator": "x",
        ///            reqComponentDate": "20220719",
        ///            reqComponentDateYear": 2022,
        ///            reqComponentDateMonth": 7,
        ///            reqComponentDateDate": 19,
        ///            reqQuantity": 0,
        ///            measureUnit": "123",
        ///            dcIndicator": "x",
        ///            unitEntry": "123",
        ///            unitEntryQuantity": 0,
        ///            orderNumber": "123",
        ///            salesOrderNumber": "123",
        ///            salesOrderNumberItem": "123",
        ///            salesOrderNumberItemInt": 123,
        ///            riPlant": "123",
        ///            riStorageLocation": "123",
        ///            inputQuantity": 0,
        ///            measureUnitNumerator": 0,
        ///            measureUnitDenominator": 0,
        ///            wbsElement": "123",
        ///            wbsElementInt": 123,
        ///            createTime": "2022-07-20T10:11:41.2279443+07:00",
        ///            lastEditTime": null,
        ///            actived": true
        ///      },
        ///         additionalData": null
        ///     }
        ///</remarks>
        [HttpPost("Reservation")]
        public async Task<IActionResult> ReservationIntegrationAsync([FromBody] ReservationIntegrationViewModel vm)
        {
            try
            {
                //Check exist
                var reservation = await _context.ReservationHeaderModel.Include(x => x.MaterialReservationModel)
                                                                        .Where(x => x.ReservationNumber == vm.Header.ReservationNumber)
                                                                        .FirstOrDefaultAsync();

                if (vm.ListDetail == null)
                    return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_RequierdFile, "Danh sách item") });            

                if (reservation == null)
                {
                    //Danh sách material reservation của header reservation
                    var listDetailReservation = vm.ListDetail.Select(x => new MaterialReservationModel
                    {
                        ReservationId = Guid.NewGuid(),
                        //Số reservation
                        ReservationNumber = x.ReservationNumber,
                        //Số dòng trong reservation
                        ReservationItemNumber = x.ReservationItemNumber,
                        //Item đã bị deleted
                        ItemDeleted = x.ItemDeleted,
                        //Item đã hoàn thành
                        ReservationGoodsMove = x.ReservationGoodsMove,
                        //Mã material
                        FinalIssue = x.FinalIssue,
                        //Mã material
                        MaterialNumber = x.MaterialNumber.Replace("000000000", ""),
                        //Kho
                        StorageLocation = x.StorageLocation,
                        //Nhà máy
                        Plant = x.Plant,
                        //Lô
                        BatchNumber = x.BatchNumber,
                        //Tồn kho đặc biệt
                        SpecialStockIndicator = x.SpecialStockIndicator,
                        //Date cần thực hiện nghiệp vụ
                        ReqComponentDate = x.ReqComponentDate,
                        //Day cần thực hiện nghiệp vụ
                        ReqComponentDay = x.ReqComponentDate.HasValue ? x.ReqComponentDate.Value.Day : null,
                        //Month cần thực hiện nghiệp vụ
                        ReqComponentMonth = x.ReqComponentDate.HasValue ? x.ReqComponentDate.Value.Month : null,
                        //Year cần thực hiện nghiệp vụ
                        ReqComponentYear = x.ReqComponentDate.HasValue ? x.ReqComponentDate.Value.Year : null,
                        //Số lượng yêu cầu
                        ReqQuantity = x.ReqQuantity,
                        //Đơn vị tính
                        MeasureUnit = x.MeasureUnit,
                        //Nhập/Xuất
                        DCIndicator = x.DCIndicator,
                        //ĐVT yêu cầu
                        UnitEntry = x.UnitEntry,
                        //Số lương theo ĐVT yêu cầu
                        UnitEntryQuantity = x.UnitEntryQuantity,
                        //Số LSX
                        OrderNumber = x.OrderNumber,
                        //Số SO
                        SalesOrderNumber = x.SalesOrderNumber,
                        //Số Item trên SO
                        SalesOrderNumberItem = x.SalesOrderNumberItem,
                        //Nhà máy nhận
                        RIPlant = x.RIPlant,
                        //Kho nhận
                        RIStorageLocation = x.RIStorageLocation,
                        //Số lượng input (chưa sd tới)
                        InputQuantity = x.InputQuantity,
                        //Hệ số quy đổi từ Unit of entry sang based unit
                        MeasureUnitDenominator = x.MeasureUnitDenominator,
                        //Hệ số quy đổi từ Unit of entry sang based unit
                        MeasureUnitNumerator = x.MeasureUnitNumerator,
                        //Mã WBS
                        WBSElement = x.WBSElement,
                        Note = x.Note,
                        CreateTime = DateTime.Now,
                        Actived = true
                    }).ToList();

                    //_context.MaterialReservationModel.AddRange(listDetailReservation);

                    //Thêm mới header reservataion
                    var reservationHeader = new ReservationHeaderModel
                    {
                        ReservationHeaderId = Guid.NewGuid(),
                        //Số Reservation
                        ReservationNumber = vm.Header.ReservationNumber,
                        //Ngày yêu cầu
                        RequestDate = vm.Header.RequestDate,
                        //Loại giao dịch kho
                        MovementType = vm.Header.MovementType,
                        //Người nhận
                        GoodsRecipient = vm.Header.GoodsRecipient,
                        //Mã customer
                        AccountNumberCustomer = vm.Header.AccountNumberCustomer,
                        //Mã tài sản
                        AssetNumber = vm.Header.AssetNumber,
                        //Mã chi phí (cost center)
                        CostCenter = vm.Header.CostCenter,
                        //Order number
                        OrderNumber = vm.Header.OrderNumber,
                        //Số SO
                        SalesOrderNumber = vm.Header.SalesOrderNumber,
                        //Item trên SO
                        SalesOrderNumberItem = vm.Header.SalesOrderNumberItem,
                        //Nhà máy nhận
                        RIPlant = vm.Header.RIPlant,
                        //Kho nhận
                        RIStorageLocation = vm.Header.RIStorageLocation,
                        //Mã WBS
                        WBSElement = vm.Header.WBSElement,
                        //List detail reservation
                        MaterialReservationModel = listDetailReservation,
                        //Ngày và người tạo
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId,
                    };

                    _context.ReservationHeaderModel.Add(reservationHeader);
                } 
                //Cập nhật
                else
                {
                    reservation.ReservationNumber = vm.Header.ReservationNumber;
                    reservation.RequestDate = vm.Header.RequestDate;
                    reservation.MovementType = vm.Header.MovementType;
                    reservation.GoodsRecipient = vm.Header.GoodsRecipient;
                    reservation.AccountNumberCustomer = vm.Header.AccountNumberCustomer;
                    reservation.AssetNumber = vm.Header.AssetNumber;
                    reservation.CostCenter = vm.Header.CostCenter;
                    reservation.OrderNumber = vm.Header.OrderNumber;
                    reservation.SalesOrderNumberItem = vm.Header.SalesOrderNumberItem;
                    reservation.RIPlant = vm.Header.RIPlant;
                    reservation.RIStorageLocation = vm.Header.RIStorageLocation;
                    reservation.WBSElement = vm.Header.WBSElement;
                    reservation.LastEditTime = DateTime.Now;

                    if (reservation.MaterialReservationModel.Any())
                    {
                        foreach (var item in vm.ListDetail)
                        {
                            var reservationItem = reservation.MaterialReservationModel.FirstOrDefault(x => x.ReservationItemNumber == item.ReservationItemNumber && x.ReservationNumber == item.ReservationNumber);

                            if (reservationItem != null)
                            {
                                reservationItem.ReservationNumber = item.ReservationNumber;
                                reservationItem.ReservationItemNumber = item.ReservationItemNumber;
                                reservationItem.ItemDeleted = item.ItemDeleted;
                                reservationItem.FinalIssue = item.FinalIssue;
                                reservationItem.MaterialNumber = item.MaterialNumber.Replace("000000000", "");
                                reservationItem.StorageLocation = item.StorageLocation;
                                reservationItem.Plant = item.Plant;
                                reservationItem.BatchNumber = item.BatchNumber;
                                reservationItem.ReqComponentDate = item.ReqComponentDate;
                                reservationItem.ReqComponentDay = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Day : null;
                                reservationItem.ReqComponentMonth = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Month : null;
                                reservationItem.ReqComponentYear = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Year : null;
                                reservationItem.ReqQuantity = item.ReqQuantity;
                                reservationItem.ReservationGoodsMove = item.ReservationGoodsMove;
                                reservationItem.MeasureUnit = item.MeasureUnit;
                                reservationItem.DCIndicator = item.DCIndicator;
                                reservationItem.UnitEntry = item.UnitEntry;
                                reservationItem.UnitEntryQuantity = item.UnitEntryQuantity;
                                reservationItem.SpecialStockIndicator = item.SpecialStockIndicator;
                                reservationItem.OrderNumber = item.OrderNumber;
                                reservationItem.RIPlant = item.RIPlant;
                                reservationItem.RIStorageLocation = item.RIStorageLocation;
                                reservationItem.InputQuantity = item.InputQuantity;
                                reservationItem.MeasureUnitDenominator = item.MeasureUnitDenominator;
                                reservationItem.MeasureUnitNumerator = item.MeasureUnitNumerator;
                                reservationItem.WBSElement = item.WBSElement;
                                reservationItem.Note = item.Note;
                                reservationItem.LastEditTime = DateTime.Now;
                            }
                            else
                            {
                                _context.MaterialReservationModel.Add(new MaterialReservationModel
                                {
                                    ReservationId = Guid.NewGuid(),
                                    ReservationHeaderId = reservation.ReservationHeaderId,
                                    ReservationNumber = item.ReservationNumber,
                                    ReservationItemNumber = item.ReservationItemNumber,
                                    ItemDeleted = item.ItemDeleted,
                                    FinalIssue = item.FinalIssue,
                                    MaterialNumber = item.MaterialNumber.Replace("000000000", ""),
                                    StorageLocation = item.StorageLocation,
                                    Plant = item.Plant,
                                    BatchNumber = item.BatchNumber,
                                    ReservationGoodsMove = item.ReservationGoodsMove,
                                    ReqComponentDate = item.ReqComponentDate,
                                    ReqComponentDay = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Day : null,
                                    ReqComponentMonth = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Month : null,
                                    ReqComponentYear = item.ReqComponentDate.HasValue ? item.ReqComponentDate.Value.Year : null,
                                    ReqQuantity = item.ReqQuantity,
                                    MeasureUnit = item.MeasureUnit,
                                    DCIndicator = item.DCIndicator,
                                    UnitEntry = item.UnitEntry,
                                    UnitEntryQuantity = item.UnitEntryQuantity,
                                    OrderNumber = item.OrderNumber,
                                    RIPlant = item.RIPlant,
                                    RIStorageLocation = item.RIStorageLocation,
                                    InputQuantity = item.InputQuantity,
                                    MeasureUnitDenominator = item.MeasureUnitDenominator,
                                    MeasureUnitNumerator = item.MeasureUnitNumerator,
                                    WBSElement = item.WBSElement,
                                    Note = item.Note,
                                    SpecialStockIndicator = item.SpecialStockIndicator,
                                    CreateTime = DateTime.Now,
                                    Actived = true
                                });
                            }
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Tích hợp thành công Reservation", Data = true });
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex.InnerException);
            }

        }
        #endregion
    }
}
