﻿using ISD.API.Core;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class VendorController : ControllerBaseAPI
    {
        // search
        #region Tìm kiếm danh sách Vendor
        /// <summary>API Search "Tìm kiếm danh sách Vendor"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/Vendor/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "supplier": "test",
        ///                 "shortName": "test",
        ///                 "longName": "123",
        ///                 "address": "",
        ///                 "telephone": "",
        ///                 "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": [
        ///              {
        ///                "stt": 1,
        ///                "vendorId": "74c32f98-c523-4373-8f59-f8682a58001a",
        ///                "supplier": "test",
        ///                "shortName": "test",
        ///                "longName": "123",
        ///                "address": null,
        ///                "telephone": null,
        ///                "createTime": "2022-07-15T12:32:43.55",
        ///                "lastEditTime": "2022-07-15T13:06:13.387",
        ///                "actived": true
        ///              }
        ///            ],
        ///            "additionalData": {
        ///              "draw": 1,
        ///              "recordsTotal": 1,
        ///              "recordsFiltered": 1
        ///            }
        ///          }
        /// </remarks>
        [HttpPost("Search")]
        public ActionResult GetVendor([FromBody] VendorSearchViewModel searchVM)
        {

            var querySearch = _context.VendorModel.Where(x => x.Actived == true &&
                                                       (!string.IsNullOrEmpty(searchVM.SupplierNumber) ? x.SupplierNumber.Contains(searchVM.SupplierNumber) : true) &&
                                                       (!string.IsNullOrEmpty(searchVM.LongName) ? x.LongName.Contains(searchVM.LongName) : true) &&
                                                       (!string.IsNullOrEmpty(searchVM.Address) ? x.Address.Contains(searchVM.Address) : true) &&
                                                       (!string.IsNullOrEmpty(searchVM.Telephone) ? x.Telephone.Contains(searchVM.Telephone) : true))

                                                  .OrderBy(x => x.SupplierNumber)
                                                  .Select(p => new VendorResultViewModel
                                                  {
                                                      VendorId = p.VendorId,
                                                      SupplierNumber = p.SupplierNumber,
                                                      ShortName = p.ShortName,
                                                      LongName = p.LongName,
                                                      Adderss = p.Address,
                                                      Telephone = p.Telephone,
                                                      Actived = p.Actived,
                                                  });           

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = NewCustomSearchRepository.CustomSearchFunc<VendorResultViewModel>(searchVM.Paging, out filteredResultsCount, out totalResultsCount, querySearch, "stt");

            if (res != null && res.Count() > 0)
            {
                int i = searchVM.Paging.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<VendorResultViewModel>>
            {
                Data = res,
                Draw = searchVM.Paging.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        //Detail vendor
        #region Detail
        /// <summary>API Search "Get detail vendor"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/Vendor/GetDetail?VendorId={vendorId}
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "id" : 8CD2BB74-4899-41D1-86F9-7DD2C117D280
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///          {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": [
        ///              {
        ///               "vendorId": "8cd2bb74-4899-41d1-86f9-7dd2c117d280",
        ///                "supplierNumber": "test",
        ///                "shortName": "Le",
        ///                "longName": "LeAnBinh",
        ///                "address": "HCM",
        ///                "telephone": "123456789",
        ///                "createTime": "2022-07-18T09:56:33.227",
        ///                "lastEditTime": null,
        ///                "actived": true
        ///              }
        ///            ],
        ///            "additionalData": null
        ///          }
        ///          
        /// </remarks>
        [HttpGet("GetDetail")]
        public async Task<IActionResult> GetDetailVendor(Guid VendorId)
        {
            var details = await _context.VendorModel.FirstOrDefaultAsync(p => p.VendorId == VendorId);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = details
            });
        }
        #endregion


    }
}