import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import '../../Storage/storageSharedPreferences.dart';
import '../../model/drawerFilterTranferMaterial.dart';
import '../../model/getStatusReservation.dart';
import '../../model/getStatusWarehouseTranfer.dart';
import '../../model/listWarehouseTranfer.dart';
import '../../model/postFilterWareHouseTranfer.dart';

import '../../model/saveEndDrawerListWareHouse.dart';
import 'listWarehouseTranferFunction.dart';

class EndDrawerListWareHouseTranferFunction{

  static Future <List<DataListWarehouseTranfer>> postFilterListWareHouseTranfer(BuildContext context,PostFilterWareHouseTranfer postFilterWareHouseTranfer, String token,int start,int draw,int length) async {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) =>
          WillPopScope(
            onWillPop: () async => false,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
    );
    final data = await ListWarehouseTranferFunction.fetchListWareHouseTranfer(token, postFilterWareHouseTranfer,start,draw,length);
    Navigator.pop(context);
    if (data != null) {
      return data;
    } else {
      return [];
    }
  }
  static Future<DrawerFilterTranferMaterial> sendBackData(
      BuildContext context,
      String controllerPlantText,
      String? slocExport,
      String? slocImport,
      String controllerReservationText,
      DataGetStatusReservation? selectReservation,
      String? keyNVL,
      DataGetStatusWarehouseTranfer? selectStatus,
      String token,
      int start,
      int draw,
      int length,
      DateTime fromDateRequest,
      DateTime toDateRequest,
      DateTime? fromDateTrans,
      DateTime? toDateTrans,
      String? accountId,
      String valueNVl
      ) async {
      final postFilterWareHouseTranfer = PostFilterWareHouseTranfer(
          controllerPlantText.isEmpty ? null : controllerPlantText,
          slocExport,
          slocImport,
          controllerReservationText.isEmpty ? null : controllerReservationText,
          selectReservation?.key,
          keyNVL,
          selectStatus?.key,
          fromDateRequest,
          toDateRequest,
          fromDateTrans,
          toDateTrans
      );
      SaveEndDrawerListWareHouse saveEndDrawerListWareHouse = SaveEndDrawerListWareHouse(value: valueNVl,postFilterWareHouseTranfer:postFilterWareHouseTranfer);
      await StorageSharedPreferences.setString("listTranferSearch$accountId", jsonEncode(saveEndDrawerListWareHouse));

      final lsDataWarehouseTranfe = await postFilterListWareHouseTranfer(context, postFilterWareHouseTranfer,token, start,draw,length);

      final drawerFilterTranferMaterial = DrawerFilterTranferMaterial(
          lsDataWarehouseTranfe: lsDataWarehouseTranfe,
          postFilterWareHouseTranfer:postFilterWareHouseTranfer,
          start:start +5,
          draw: draw + 1,
          length: length,
          resetList: false
      );
      return drawerFilterTranferMaterial;

  }
}