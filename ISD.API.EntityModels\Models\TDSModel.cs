﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    public partial class TDSModel
    {
        [Key]
        public int TdsCode { get; set; }
        [StringLength(255)]
        public string Title { get; set; }
        [StringLength(255)]
        public string RequestType { get; set; }
        [StringLength(255)]
        public string PIC { get; set; }
        [StringLength(50)]
        public string TrangThai { get; set; }
        [StringLength(100)]
        public string ViTri { get; set; }
        [StringLength(100)]
        public string CreatedOnText { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedOn { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public string HtmlData { get; set; }
    }
}