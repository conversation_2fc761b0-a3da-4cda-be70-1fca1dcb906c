﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MobileScreenModel", Schema = "ghMasterData")]
    public partial class MobileScreenModel
    {
        public MobileScreenModel()
        {
            MobileScreenPermissionModel = new HashSet<MobileScreenPermissionModel>();
            Function = new HashSet<FunctionModel>();
        }

        [Key]
        public Guid MobileScreenId { get; set; }
        [StringLength(100)]
        public string ScreenName { get; set; }
        [StringLength(300)]
        public string ScreenCode { get; set; }
        public Guid? MenuId { get; set; }
        [StringLength(50)]
        public string IconType { get; set; }
        [StringLength(50)]
        public string IconName { get; set; }
        public int? OrderIndex { get; set; }
        [StringLength(100)]
        public string Icon { get; set; }
        public bool? Visible { get; set; }
        public bool? isSystem { get; set; }
        public bool? Actived { get; set; }
        public bool? isCreated { get; set; }
        public bool? isReporter { get; set; }
        public bool? isAssignee { get; set; }

        [ForeignKey("MenuId")]
        [InverseProperty("MobileScreenModel")]
        public virtual MenuModel Menu { get; set; }
        [InverseProperty("MobileScreen")]
        public virtual ICollection<MobileScreenPermissionModel> MobileScreenPermissionModel { get; set; }

        [ForeignKey("MobileScreenId")]
        [InverseProperty("MobileScreen")]
        public virtual ICollection<FunctionModel> Function { get; set; }
    }
}