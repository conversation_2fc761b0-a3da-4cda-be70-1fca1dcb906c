﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseTransactionModel", Schema = "MESP2")]
    public partial class WarehouseTransactionModel
    {
        [Key]
        public Guid WarhouseTransactionId { get; set; }
        [StringLength(50)]
        public string GoodsReceivedNote { get; set; }
        [StringLength(50)]
        public string YearReceive { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(50)]
        public string PO { get; set; }
        [StringLength(50)]
        public string POLine { get; set; }
        [StringLength(50)]
        public string SO { get; set; }
        [StringLength(50)]
        public string SOLine { get; set; }
        [StringLength(50)]
        public string WBS { get; set; }
        public Guid? SlocId { get; set; }
        public Guid? StorageBinId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [StringLength(50)]
        public string Batch { get; set; }
        [StringLength(50)]
        public string MovementType { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocumentDate { get; set; }
        public int? DateKey { get; set; }
        public Guid ReferenceDocumentId { get; set; }
        [StringLength(50)]
        public string ReferenceDocumentText { get; set; }
        public bool? isSendToSAP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SendToSAPTime { get; set; }
        [StringLength(500)]
        public string SendToSAPError { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool? Actived { get; set; }
        [StringLength(10)]
        public string TdsNumber { get; set; }
        [StringLength(50)]
        public string SoToKhai { get; set; }
    }
}