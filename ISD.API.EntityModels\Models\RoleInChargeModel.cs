﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RoleInChargeModel", Schema = "Customer")]
    public partial class RoleInChargeModel
    {
        [Key]
        public Guid RoleInChargeId { get; set; }
        public Guid? ProfileId { get; set; }
        public Guid? RolesId { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }

        [ForeignKey("ProfileId")]
        [InverseProperty("RoleInChargeModel")]
        public virtual ProfileModel Profile { get; set; }
        [ForeignKey("RolesId")]
        [InverseProperty("RoleInChargeModel")]
        public virtual RolesModel Roles { get; set; }
    }
}