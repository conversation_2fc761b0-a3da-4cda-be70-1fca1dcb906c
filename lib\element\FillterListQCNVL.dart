import 'dart:async';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/getLstQCNVLByFilter.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../model/CodeNameProduct.dart';
import '../model/drawerFilterListQCNVL.dart';
import '../model/getStatusGoodsArrive.dart';
import '../model/listQCNVL.dart';
import '../repository/function/endDrawerListQCNVLFunction.dart';
import '../repository/function/listWarehouseTranferFunction.dart';
import '../repository/showDateTime.dart';
import 'ColumnTextFieldLsWHTranfer.dart';
import 'DropdownLsQC.dart';
import 'RowTextFieldLsQC.dart';

class FillterListQCNVL extends StatefulWidget {
  final String token;
  final void Function(DrawerFilterListQCNVL) onFilterSelected;

  final List<DataGetStatusGoodsArrive> lsDataGetStatusGoodArrive;
  const FillterListQCNVL({Key? key, required this.token, required this.onFilterSelected, required this.lsDataGetStatusGoodArrive}) : super(key: key);

  @override
  _FillterListQCNVLState createState() => _FillterListQCNVLState();
}

class _FillterListQCNVLState extends State<FillterListQCNVL> {
  final _controllerNVL = TextEditingController();
  final _controllerPoNumber = TextEditingController();
  final _controllerVendor = TextEditingController();
  final _controllerSO = TextEditingController();
  final _controllerWBS = TextEditingController();

  final _focusControllerWBS = FocusNode();
  final _focusVendor = FocusNode();
  final _focusControllerNVL = FocusNode();
  final _focusControllerSO = FocusNode();
  final _focusControllerPoNumber = FocusNode();
  int pageNumber = 1;

  String? _keyNVL;
  DeliveryStatus? _selectedDeliveryStatus;
  QCStatus? _selectedStatusQC;
  ResultListQCNVL? _selectedResult;
  DataGetStatusGoodsArrive? _selectedFactoryReturnDate;
  bool _showNotiError = false;
  String _messageNoti = "";
  DateTime? _fromDateCreateBarcode;
  DateTime? _toDateCreateBarcode;
  DateTime? _deliveryFromDate;
  DateTime? _deliveryToDate;
  DateTime? _timeQCFromDate;
  DateTime? _timeQCToDate;
  DateTime? _factoryReturnFromDate;
  DateTime? _factoryReturnToDate;
  bool _isLoading = false;
  List<DataCodenameProduct> _lsCodeNameProduct = [];
  bool _errorInputAutoComplete = false;

  late List<ResultListQCNVL> _lsResultListQCNVL;
  late List<DeliveryStatus> _lsDeliveryStatus;
  late List<QCStatus> _lsQCStatus;
  // late final KeyboardVisibilityController _keyboardVisibilityController;
  // late StreamSubscription<bool> _keyboardSubscription;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _lsResultListQCNVL = [ResultListQCNVL(key: false, value: "Fail"), ResultListQCNVL(key: true, value: "Pass")];
    _lsDeliveryStatus = [DeliveryStatus(key: false, value: "Đang giao"), DeliveryStatus(key: true, value: "Đã giao")];
    _lsQCStatus = [QCStatus(key: false, value: "Chưa kiểm tra"), QCStatus(key: true, value: "Đã kiểm tra")];
    _selectedStatusQC = _lsQCStatus.firstWhereOrNull((element) => element.value == "Chưa kiểm tra");
    // _fromDateCreateBarcode = DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, DateTime.now().hour, DateTime.now().minute)));
    // _toDateCreateBarcode= DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, DateTime.now().hour, DateTime.now().minute)));
    // _keyboardVisibilityController = KeyboardVisibilityController();
    // _keyboardSubscription = _keyboardVisibilityController.onChange.listen((isVisible) {
    //   if (!isVisible && mounted){
    //     if(_focusControllerWBS.hasFocus){
    //       _focusControllerWBS.unfocus();
    //     }
    //     if(_focusVendor.hasFocus){
    //       _focusVendor.unfocus();
    //     }
    //     if(_focusControllerNVL.hasFocus){
    //       _focusControllerNVL.unfocus();
    //     }
    //     if(_focusControllerSO.hasFocus){
    //       _focusControllerSO.unfocus();
    //     }
    //     if(_focusControllerPoNumber.hasFocus){
    //       _focusControllerPoNumber.unfocus();
    //     }
    //   }
    // });
  }

  void _setSelectedDeliveryStatus(DeliveryStatus? value) {
    setState(() {
      _selectedDeliveryStatus = value!;
    });
  }

  void _setSelectedStatusQC(QCStatus? value) {
    setState(() {
      _selectedStatusQC = value!;
    });
  }

  void _setSelectedResult(ResultListQCNVL? value) {
    setState(() {
      _selectedResult = value!;
    });
  }

  void _setSelectedFactoryReturnDate(DataGetStatusGoodsArrive? value) {
    setState(() {
      _selectedFactoryReturnDate = value!;
    });
  }

  void _checkError(String input) {
    if (input.trim().length < 3) {
      if (_errorInputAutoComplete != true) {
        setState(() {
          _errorInputAutoComplete = true;
        });
      }
    } else {
      if (_errorInputAutoComplete != false) {
        setState(() {
          _errorInputAutoComplete = false;
        });
      }
    }
  }

  Future<void> _sendBackData(BuildContext context) async {
    try {
      GetLstQCNVLByFilter _getLstQCNVLByFilter = GetLstQCNVLByFilter(
          pageNumber: pageNumber,
          pageSize: 5,
          productCode: _controllerNVL.text.isEmpty ? null : _keyNVL,
          poNumber: _controllerPoNumber.text.isEmpty ? null : _controllerPoNumber.text,
          so: _controllerSO.text.isEmpty ? null : _controllerSO.text,
          vendorNumber: _controllerVendor.text.isEmpty ? null : _controllerVendor.text,
          wbs: _controllerWBS.text.isEmpty ? null : _controllerWBS.text,
          createBarcodeFromDate: _fromDateCreateBarcode == null ? null : _fromDateCreateBarcode!.toIso8601String(),
          createBarcodeToDate: _toDateCreateBarcode == null ? null : _toDateCreateBarcode!.toIso8601String(),
          deliveryStatus: _selectedDeliveryStatus == null ? null : _selectedDeliveryStatus!.key,
          deliveryFromDate: _deliveryFromDate == null ? null : _deliveryFromDate!.toIso8601String(),
          deliveryToDate: _deliveryToDate == null ? null : _deliveryToDate!.toIso8601String(),
          qcStatus: _selectedStatusQC == null ? null : _selectedStatusQC!.key,
          qcResult: _selectedResult == null ? null : _selectedResult!.key,
          qcFromDate: _timeQCFromDate == null ? null : _timeQCFromDate!.toIso8601String(),
          qcToDate: _timeQCToDate == null ? null : _timeQCToDate!.toIso8601String(),
          goodsArriveToDate: _factoryReturnToDate,
          goodsArriveFromDate: _factoryReturnFromDate,
          goodsArrive: _selectedFactoryReturnDate == null ? null : _selectedFactoryReturnDate!.key);
      final data = await EndDrawerListQCNVLFunction.sendBackData(context, widget.token, _getLstQCNVLByFilter, pageNumber);
      widget.onFilterSelected(data);
      Navigator.pop(context);
    } on SocketException catch (_) {
      Navigator.pop(context);
      if (!mounted) return;
      setState(() {
        _showNotiError = true;
        _messageNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      debugPrint(error.toString());
      Navigator.pop(context);
      if (!mounted) return;
      setState(() {
        _showNotiError = true;
        _messageNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  Future<void> _onChangeNVL(String value) async {
    _checkError(value);
    if (!mounted) return;
    _timer?.cancel();
    if (_errorInputAutoComplete == false) {
      _getAutocompleteProduct(value);
    }
  }

  Future<void> _getAutocompleteProduct(String productCode) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoading = true;
            });
            final data = await ListWarehouseTranferFunction.fechCodeNameWarehouseTranfer(productCode, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoading = false;
              if (data != null) {
                _lsCodeNameProduct = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickFromDateCreateBarcode(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    TimeOfDay initialTime = TimeOfDay(hour: now.hour, minute: now.minute);
    TimeOfDay? newTime = await ShowDateTime.showTime(
        context, initialTime, TimeOfDay(hour: (_fromDateCreateBarcode ?? now).hour, minute: (_fromDateCreateBarcode ?? now).minute));
    if (newTime == null) return;
    setState(() {
      _fromDateCreateBarcode = DateFormat("yyyy-MM-ddTHH:mm:ss")
          .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newTime.hour, newTime.minute)));
    });
  }

  Future<void> _pickToDateCreateBarcode(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    TimeOfDay initialTime = TimeOfDay(hour: now.hour, minute: now.minute);
    TimeOfDay? newTime = await ShowDateTime.showTime(
        context, initialTime, TimeOfDay(hour: (_toDateCreateBarcode ?? now).hour, minute: (_toDateCreateBarcode ?? now).minute));
    if (newTime == null) return;
    setState(() {
      _toDateCreateBarcode = DateFormat("yyyy-MM-ddTHH:mm:ss")
          .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newTime.hour, newTime.minute)));
    });
    // setState(() {
    //   _toDateCreateBarcode = newDate;
    // });
  }

  Future<void> _pickDeliveryFromDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _deliveryFromDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
    // setState(() {
    //   _deliveryFromDate = newDate;
    // });
  }

  Future<void> _pickDeliveryToDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _deliveryToDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
  }

  Future<void> _pickTimeQCFromDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _timeQCFromDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
    // setState(() {
    //   _timeQCFromDate = newDate;
    // });
  }

  Future<void> _pickTimeQCToDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _timeQCToDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
  }

  Future<void> _pickFactoryReturnFromDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _factoryReturnFromDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
  }

  Future<void> _pickFactoryReturnToDate(BuildContext context) async {
    DateTime now = DateTime.now();
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime(now.year - 50),
      lastDate: DateTime(now.year + 5),
    );
    if (newDate == null) return;
    setState(() {
      _factoryReturnToDate =
          DateFormat("yyyy-MM-ddTHH:mm:ss").parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day)));
    });
  }

  Future<void> _pickFromDateCreateBarcodeIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToCompleteDateTime(context);
      if (newDate == null) return;
      setState(() {
        _fromDateCreateBarcode = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickToDateCreateBarcodeIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToCompleteDateTime(context);
      if (newDate == null) return;
      setState(() {
        _toDateCreateBarcode = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickDeliveryFromDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _deliveryFromDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickDeliveryToDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _deliveryToDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickTimeQCFromDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _timeQCFromDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickTimeQCToDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _timeQCToDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickFactoryReturnFromDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _factoryReturnFromDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickFactoryReturnToDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _factoryReturnToDate = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  void dispose() {
    _controllerVendor.dispose();
    _focusVendor.dispose();
    _controllerNVL.dispose();
    _focusControllerNVL.dispose();
    _controllerPoNumber.dispose();
    _focusControllerPoNumber.dispose();
    _controllerSO.dispose();
    _focusControllerSO.dispose();
    _controllerWBS.dispose();
    _focusControllerWBS.dispose();
    if (_timer != null) {
      _timer!.cancel();
    }
    // _keyboardSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Container(
          margin: EdgeInsets.only(top: 40.h),
          child: Drawer(
            backgroundColor: Colors.white,
            child: Stack(children: [
              SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                      color: const Color(0xff0052cc),
                      child: Text(
                        "Tìm kiếm phiếu QC NVL",
                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                      ),
                    ),
                    Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            SizedBox(height: 10.h),
                            ColumnTextFieldLsWHTranferOnchange(
                              isLoading: _isLoading,
                              focusNode: _focusControllerNVL,
                              title: 'Mã NVL | Tên NVL',
                              controller: _controllerNVL,
                              onChange: (value) {
                                _onChangeNVL(value);
                              },
                            ),
                            Visibility(visible: _errorInputAutoComplete, child: SizedBox(height: 10.h)),
                            ContainerError.widgetError(_errorInputAutoComplete, 'Nhập tối thiểu 3 kí tự'),
                            SizedBox(height: 10.h),
                            Visibility(
                              visible: _lsCodeNameProduct.isNotEmpty || _isLoading == true,
                              child: SizedBox(
                                height: 250.h,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.5),
                                        spreadRadius: 5,
                                        blurRadius: 7,
                                        offset: const Offset(0, 3), // changes position of shadow
                                      ),
                                    ],
                                  ),
                                  child: _isLoading == true
                                      ? const Center(child: CircularProgressIndicator())
                                      : ListView.separated(
                                          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                          itemCount: _lsCodeNameProduct.length,
                                          itemBuilder: (BuildContext context, int index) {
                                            return InkWell(
                                              onTap: () {
                                                setState(() {
                                                  _controllerNVL.text = _lsCodeNameProduct[index].value ?? "";
                                                  _keyNVL = _lsCodeNameProduct[index].key;
                                                  _lsCodeNameProduct = [];
                                                });
                                              },
                                              child: Text(_lsCodeNameProduct[index].value ?? ""),
                                            );
                                          },
                                          separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                                        ),
                                ),
                              ),
                            ),
                            _lsCodeNameProduct.isNotEmpty || _isLoading == true ? SizedBox(height: 10.h) : const SizedBox(height: 0),
                            RowTextFieldLsQC(controller: _controllerPoNumber, title: "Số PO", focusNode: _focusControllerPoNumber),
                            SizedBox(height: 10.h),
                            RowTextFieldLsQC(controller: _controllerVendor, title: "Nhà cung cấp", focusNode: _focusVendor),
                            SizedBox(height: 10.h),
                            RowTextFieldLsQC(controller: _controllerSO, title: "SO", focusNode: _focusControllerSO),
                            SizedBox(height: 10.h),
                            RowTextFieldLsQC(controller: _controllerWBS, title: "WBS", focusNode: _focusControllerWBS),
                            SizedBox(height: 10.h),
                            const _TitleWidget(text: "Thời gian tạo barcode:"),
                            SizedBox(height: 5.h),
                            Container(
                              padding: REdgeInsets.all(7),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  // DropdownTimeCreateBarcode(
                                  //   onTap: (value) => _setSelectedTimeCreateBarcode(value),
                                  //   lsTimeCreateBarcode: lsTimeCreateBarcode,
                                  //   title: "Thời gian tạo barcode",
                                  //   selectedTimeCreateBarcode: _selectedTimeCreateBarcode,
                                  // ),
                                  SizedBox(height: 5.h),
                                  const _TitleWidget(text: "Từ ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickFromDateCreateBarcode(context) : _pickFromDateCreateBarcodeIOS(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _fromDateCreateBarcode != null ? DateFormat('dd-MM-yyyy HH:mm').format(_fromDateCreateBarcode!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  const _TitleWidget(text: "Đến ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickToDateCreateBarcode(context) : _pickToDateCreateBarcodeIOS(context);
                                      // Platform.isAndroid
                                      //     ?_pickToDateRequestIOS(context)
                                      //     :_pickToDateRequest(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _toDateCreateBarcode != null ? DateFormat('dd-MM-yyyy HH:mm').format(_toDateCreateBarcode!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.h),
                            const _TitleWidget(text: "Ngày giao hàng:"),
                            SizedBox(height: 5.h),
                            Container(
                              padding: REdgeInsets.all(7),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  SizedBox(height: 5.h),
                                  const _TitleWidget(text: "Từ ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickDeliveryFromDate(context) : _pickDeliveryFromDateIOS(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _deliveryFromDate != null ? DateFormat('dd-MM-yyyy').format(_deliveryFromDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  const _TitleWidget(text: "Đến ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickDeliveryToDate(context) : _pickDeliveryToDateIOS(context);
                                      // Platform.isAndroid
                                      //     ?_pickToDateRequestIOS(context)
                                      //     :_pickToDateRequest(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _deliveryToDate != null ? DateFormat('dd-MM-yyyy').format(_deliveryToDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.h),
                            DropdownDeliveryStatus(
                              onTap: (value) => _setSelectedDeliveryStatus(value),
                              lsDeliveryStatus: _lsDeliveryStatus,
                              title: "Tình trạng giao hàng",
                              selectedDeliveryStatus: _selectedDeliveryStatus,
                            ),
                            SizedBox(height: 10.h),
                            DropdownStatusQC(
                              onTap: (value) => _setSelectedStatusQC(value),
                              lsStatusQC: _lsQCStatus,
                              title: "Trạng thái QC",
                              selectedStatusQC: _selectedStatusQC,
                            ),
                            SizedBox(height: 10.h),
                            DropdownResult(
                              onTap: (value) => _setSelectedResult(value),
                              lsResult: _lsResultListQCNVL,
                              title: "Kết quả QC",
                              selectedResult: _selectedResult,
                            ),
                            SizedBox(height: 10.h),
                            const _TitleWidget(text: "Thời gian QC:"),
                            SizedBox(height: 5.h),
                            Container(
                              padding: REdgeInsets.all(7),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  SizedBox(height: 5.h),
                                  const _TitleWidget(text: "Từ ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickTimeQCFromDate(context) : _pickTimeQCFromDateIOS(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _timeQCFromDate != null ? DateFormat('dd-MM-yyyy').format(_timeQCFromDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  const _TitleWidget(text: "Đến ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickTimeQCToDate(context) : _pickTimeQCToDateIOS(context);
                                      // Platform.isAndroid
                                      //     ?_pickToDateRequestIOS(context)
                                      //     :_pickToDateRequest(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _timeQCToDate != null ? DateFormat('dd-MM-yyyy').format(_timeQCToDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.h),
                            const _TitleWidget(text: "Ngày về nhà máy:"),
                            SizedBox(height: 5.h),
                            Container(
                              padding: REdgeInsets.all(7),
                              decoration: BoxDecoration(
                                border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(3.r),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  SizedBox(height: 5.h),
                                  const _TitleWidget(text: "Từ ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickFactoryReturnFromDate(context) : _pickFactoryReturnFromDateIOS(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _factoryReturnFromDate != null ? DateFormat('dd-MM-yyyy').format(_factoryReturnFromDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  const _TitleWidget(text: "Đến ngày:"),
                                  SizedBox(height: 5.h),
                                  GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      Platform.isAndroid ? _pickFactoryReturnToDate(context) : _pickFactoryReturnToDateIOS(context);
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: Text(
                                              _factoryReturnToDate != null ? DateFormat('dd-MM-yyyy').format(_factoryReturnToDate!) : "",
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.h),
                            DropdownFactoryReturnDate(
                              onTap: _setSelectedFactoryReturnDate,
                              lsDataGetStatusGoodArrive: widget.lsDataGetStatusGoodArrive,
                              title: "Trạng thái giao hàng",
                              selectedFactoryReturnDate: _selectedFactoryReturnDate,
                            ),
                            SizedBox(height: 10.h),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                style: ButtonStyle(
                                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                      RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                  side: MaterialStateProperty.all(
                                    const BorderSide(
                                      color: Color(0xff0052cc),
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                ),
                                onPressed: () async {
                                  await _sendBackData(context);
                                },
                                icon: Icon(
                                  Icons.search,
                                  size: 23.sp,
                                  color: Colors.white,
                                ),
                                label: Text(
                                  "Tìm kiếm",
                                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            SizedBox(height: 100.h),
                          ],
                        )),
                  ],
                ),
              ),
              Visibility(
                visible: _showNotiError,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                  width: double.infinity,
                  decoration: BoxDecoration(color: Colors.red.shade900),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 9,
                        child: Text(
                          _messageNoti,
                          style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: IconButton(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () {
                              setState(() {
                                _showNotiError = false;
                              });
                            },
                            icon: const Icon(Icons.cancel),
                            iconSize: 15.sp,
                            color: Colors.white),
                      ),
                    ],
                  ),
                ),
              )
            ]),
          ),
        ));
  }
}

class _TitleWidget extends StatelessWidget {
  final String text;
  const _TitleWidget({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
