import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/element/RowDetail.dart';
import 'package:ttf/model/GetQuantitySampleModel.dart';
import 'package:ttf/model/HangMucKiemTraVm.dart';
import 'package:ttf/page/KiemTraChatLuong/element/CaNhanGayLoiParentTypeAhead.dart';
import 'package:ttf/page/KiemTraChatLuong/element/CaNhanGayLoiTypeAhead.dart';
import 'package:ttf/page/KiemTraChatLuong/model/CongDoanInfoVm.dart';
import 'package:ttf/page/KiemTraChatLuong/model/TypeAheadCaNhanGayLoi.dart';
import '../../Widget/dialogWidget/DialogErrorQuality.dart';
import '../../Widget/dialogWidget/DialogImage.dart';
import '../../Widget/dialogWidget/DialogQualityInformation.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../../element/ButtonAddNewCardErrorQuatity.dart';
import '../../element/DropdownQualityView.dart';
import '../../element/ImageQuatity.dart';
import '../../element/ListChoseFileQuality.dart';
import '../../element/QualityErrorValidate.dart';
import '../../element/QualityTitle.dart';
import '../../element/TitleQuality.dart';
import '../../element/listImagePicker.dart';
import '../../element/timeOut.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/QuantityInformationSelectedInfor.dart';
import '../../model/dropdownDefectLevel.dart';
import '../../model/mulitListImageFile.dart';
import '../../model/multiSelectedErrorQuality.dart';
import '../../model/qualityControlApi.dart';
import '../../model/rawMaterialCard.dart';
import '../../model/typeAheadErrorQuatity.dart';
import '../../model/userModel.dart';
import '../../repository/function/imageFunction.dart';
import '../../repository/function/importWareHouseFunction.dart';
import '../../repository/function/qualityControlDetailFunction.dart';
import '../../repository/function/qualityControlErrorFunction.dart';
import '../../repository/function/qualityControlFunction.dart';
import '../../repository/function/qualityControlInformationFunciton.dart';
import '../../utils/ui_helpers.dart';
import '../BottomNavigatorBarComponent.dart';
import '../LostConnect.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/repository/function/loginFunction.dart';
import 'package:ttf/repository/function/downtimeFunction.dart';
import 'package:ttf/element/DropdownLsQC.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';

// Kiểm tra chất lượng
class BaoCaoQCTestLabDetail extends StatefulWidget {
  final String qualityControlId;
  final String dateTimeOld;
  final String qualityType;
  final String fromPage;
  final DataUser user;
  const BaoCaoQCTestLabDetail(
      {Key? key, required this.qualityControlId, required this.dateTimeOld, required this.qualityType, required this.fromPage, required this.user})
      : super(key: key);

  @override
  State<BaoCaoQCTestLabDetail> createState() => _BaoCaoQCTestLabDetailState();
}

class _BaoCaoQCTestLabDetailState extends State<BaoCaoQCTestLabDetail> with WidgetsBindingObserver {
  String qualityControlId = "";

  // Optional variables
  QualityControlDetail? _qualityControlDetail;
  QualityControlModel? _qualityControlModel;
  CongDoanInfoVm? _congDoanInfoVm;
  QualityControl? _qualityControl;
  DataRawMeterial? _dataRawMaterial;
  TestMethodList? _selectedTestMethod;

  // DropdownItemList? _selectedCongDoanNho;

  DropdownItemList? _selectedLimitCritical;
  DropdownItemList? _selectedLimitHigh;
  DropdownItemList? _selectedLimitLow;
  DropdownItemList? _selectedCaNhanGayLoi;

  QualityTypeList? _selectedType;
  QualityTypeList? _selectedLoaiNghiemThu; // new
  List<QualityTypeList> _lsNghiemThuTypeList = []; // new

  // Loại mẫu dropdown
  String? _selectedLoaiMau;
  List<Map<String, String>> _lsLoaiMauOptions = [
    {'value': '', 'label': '-- Chọn loại mẫu --'},
    {'value': 'Inline', 'label': 'Mẫu Inline/Hàng trắng'},
    {'value': 'Midline', 'label': 'Mẫu Midline/Hàng màu'},
    {'value': 'Final', 'label': 'Mẫu Final/Đóng gói'},
  ];

  SamplingLevelList? _selectedLevel; // Mức độ lấy mẫu KT
  // ResultList? _selectedResult;
  ResultList? _selectedResultDetail;
  List<ThongTinKiemTra>? _lsThongTinKiemTraSelectMasterData;
  List<ErrorList>? _lsErrorList;

  // Lists with known types
  List<QualityTypeList> _qualityTypeList = [];
  List<TestMethodList> _lsTestMethodList = [];

  List<DropdownItemList> _lsCongDoanNhoMasterData = [];
  List<DropdownItemList> _lsPhuongAnXuLyMasterData = [];

  List<DropdownItemList> _lsLimitCriticalList = [];
  List<DropdownItemList> _lsLimitHighList = [];
  List<DropdownItemList> _lsLimitLowList = [];
  List<DropdownItemList> _lsCaNhanGayLoiMasterData = [];
  List<DropdownItemList> _lsCaNhanGayLoiAllMasterData = [];

  List<HangMucKiemTraVm> _lsHangMucKiemTraMasterData = []; // new

  List<ResultList> _lsResultList = [];
  List<SamplingLevelList> _lsSamplingLevelList = [];

  List<Error> _lsError = [];

  List<QualityControlInformation> _lsThongTinKiemTra = [];
  List<DataGetDefectLevel> _lsDataGetDefetchLevel = []; // NANG, NHE, NGHIEMTRONG

  // Lists with controller type
  TextEditingController _controllerMauHoanThien = TextEditingController();
  TextEditingController _controllerPONumber = TextEditingController();
  TextEditingController _controllerSKU = TextEditingController();
  TextEditingController _controllerHideDetailLever = TextEditingController();
  TextEditingController _controllerMucChapNhan = TextEditingController();
  TextEditingController _controllerLanKiemTra = TextEditingController();

  TextEditingController _controllerInspectLotQuantity = TextEditingController();
  TextEditingController _controllerInspectQuantityDetail = TextEditingController();

  TextEditingController _controllerTongSoSanPhamLoi = TextEditingController();
  TextEditingController _controllerTongSoLuongLoi = TextEditingController();

  TextEditingController _controllerSLLoiNangChapNhan = TextEditingController();
  TextEditingController _controllerSLLoiNheChapNhan = TextEditingController();

  List<TextEditingController> _lsControllerThongTinKiemTra = [];
  List<TextEditingController> _lsControllerSoSanPhamLoi = [];
  List<TextEditingController> _lsControllerGhiChu = [];

// Thông tin lỗi

  List<TextEditingController> _lsControllerError = [];
  List<ErrorList?> _lsSelectedError = [];
  List<TextEditingController> _lsControllerSoLuongLoi = [];
  List<DataGetDefectLevel?> _lsSelectedMucDoLoi = [];
  List<TextEditingController> _lsControllerGhiChuLoi = [];
  List<TextEditingController> _lsControllerCaNhanLoiParent = [];

// New
  List<TextEditingController> _lsControllerCaNhanLoi1QuanDoc = [];
  List<TextEditingController> _lsControllerCaNhanLoi2ToTruong = [];
  List<TextEditingController> _lsControllerCaNhanLoi3QAQC = [];
  List<TextEditingController> _lsControllerCaNhanLoi4KCS = [];

  List<List<File>> _lsFileHinhAnhLoi = [];

  List<DropdownItemList?> _lsSelectedCongDoanLoi = [];

  List<DropdownItemList?> _lsSelectedPhuongAnXuLy = [];

  List<TextEditingController> _lsControllerNhaMayLoi = [];
  List<TextEditingController> _lsControllerPhanXuongLoi = [];
  List<TextEditingController> _lsControllerToChuyenLoi = [];

  //  Cá nhân gây lỗi
  List<TextEditingController> _lsControllerCaNhanGayLoi = [];
  List<DropdownItemList?> _lsSelectedCaNhanGayLoi = [];

  //  Cá nhân gây lỗi new many
  List<List<TextEditingController>> _lsControllerCaNhanGayLoiMany = [];
  List<List<DropdownItemList?>> _lsSelectedCaNhanGayLoiMany = []; // New: 10 cá nhân gây lỗi

// End Thông tin lỗi

  List<ThongTinKiemTra?> _lsSelectedThongTinKiemTra = [];
  List<File> _lsFileHeader = [];
  List<List<File>> _lsFileThongTinKiemTra = [];

// List with boolean types
  List<bool> _lsErrorInfor = [];
  List<bool> _checkVisiButtonThongTinKiemTra = [];
  List<bool> _checkVisiButtonError = [];

// List with int types
  List<int> _lsGetIndexInfo = [];
  List<int> _lsGetIndexError = [];
  List<int> _lsGetIndexCaNhanGayLoi = [];

// Individual variables
  ImagePicker _pickerImage = ImagePicker();
  late int _indexError;
  late int _indexCaNhanGayLoi;
  late int _indexInfoTemp;
  late bool _timeOut;

  late String _hangTagId;

// Boolean variables
  bool _disableButtonTimeOut = false;
  bool _isNotWifi = false;
  bool _isLoading = false;
  bool _isError = false;
  bool _hideDetailLever = false;
  bool _errorSelectType = false;
  bool _errorPO = false;
  bool _errorSKU = false;

  bool _errorQuantityCheck = false;
  bool _errorSelectedResultQualityView = false;
  bool _errorTestMethodDetail = false;

  bool _errorCongDoanNho = false;
  bool _errorLoaiNghiemThu = false; // new

  bool _errorLimitCriticalDetail = false;
  bool _errorLimitHighDetail = false;
  bool _errorLimitLowDetail = false;

  bool _errorLevelDetail = false;
  bool _errorAcceptableLevelDetail = false;
  bool _errorQuantityCheckDetail = false;
  bool _errorResultCheckDetail = false;
  bool _loadingGetQuanititySample = false;

  double _bottomPadding = 0;

  DataGetQuantitySample? _quantitySampleAql;

  bool checkQualityControl() {
    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      return _qualityControl!.qualityChecker == null;
    }
    return true;
  }

  DateTime? _qualityDate;
  String _qualityDateString = " ";
  QualityCheckerInfo? _qualityChckerInfo;

  bool canSubmit() {
    return _qualityControl?.qualityControlId == null;
  }

  String title = "Báo cáo Test lab";

  // New variables for Nhà máy and Phân xưởng
  SalesOrgCodes? _selectedNhaMay;
  List<SalesOrgCodes>? _lsNhaMayOptions;

  WorkShops? _selectedPhanXuong;
  List<WorkShops>? _lsPhanXuongOptions;

  final _focusPhanXuong = TextEditingController();
  List<String> _phanXuongSuggestions = [];

  @override
  void initState() {
    super.initState();
    qualityControlId = widget.qualityControlId;

    debugPrint("qualityControlId: " + qualityControlId);

    WidgetsBinding.instance.addObserver(this);

    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    }

    setState(() {
      _timeOut = false;
    });

    _init();

    _loadDataAndSetDefault();

    // if (qualityControlId != "") {
    //   setupEditView();
    // } else {
    //   setupNewView();
    // }
  }

  Future<void> _init() async {
    setState(() {
      _controllerLanKiemTra.text = "1"; // Default is 1
      _controllerTongSoSanPhamLoi.text = "";
      _controllerTongSoLuongLoi.text = "0";
    });
  }

  void setDefaultOnInit() {
    _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, QualityControlDetailFunction.defaultValueLimitCriticalList.catalogCode);
  }

  // void _resetState(){
  //   setState(() {});
  // }
  Future<void> _getGetQuanititySample(BuildContext context) async {
    setState(() {
      _loadingGetQuanititySample = true;
    });

    if (_selectedLevel != null) {
      final data = await QualityControlFunction.fetchGetQuantitySample2(
        _selectedLevel!.catalogCode.toString(),
        // _controllerInspectLotQuantity.text,
        _qualityControl!.qty!.round().toString(), // _dataRawMaterial!.quantity!.round().toString(),
        widget.user.token.toString(),
        context,
        mounted,
      );

      if (!mounted) return;

      _controllerSLLoiNangChapNhan.text = '';
      _controllerSLLoiNheChapNhan.text = '';

      if (data != null) {
        // String selectedHighProperty = 'aql' + (_selectedLimitHigh * 100).round().toString();
        // String selectedLowProperty = 'aql' + (_selectedLimitLow * 100).round().toString();

        // int? aqlHigh = data.getProperty(selectedHighProperty);
        // _controllerSLLoiNangChapNhan.text = aqlHigh.toString();

        // int? aqlLow = data.getProperty(selectedLowProperty);
        // _controllerSLLoiNheChapNhan.text = aqlLow.toString();
        String? selectedHighProperty;
        String? selectedLowProperty;

        if (_selectedLimitHigh != null) {
          String? highValue = _selectedLimitHigh!.catalogCode;
          if (highValue!.isNotEmpty) {
            selectedHighProperty = 'aql' + highValue.toString().replaceFirst('.', '');
            int? aqlHigh = data.getProperty(selectedHighProperty);
            _controllerSLLoiNangChapNhan.text = aqlHigh?.toString() ?? '';
          }
        }

        if (_selectedLimitLow != null) {
          String? lowValue = _selectedLimitLow!.catalogCode;
          if (lowValue!.isNotEmpty) {
            selectedLowProperty = 'aql' + lowValue.toString().replaceFirst('.', '');
            int? aqlLow = data.getProperty(selectedLowProperty);
            _controllerSLLoiNheChapNhan.text = aqlLow?.toString() ?? '';
          }
        }
      }

      setState(() {
        _quantitySampleAql = data;
        _loadingGetQuanititySample = false;
        _controllerInspectQuantityDetail.text = data == null ? "" : data.sampleQuantity.toString();
        _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isNotEmpty ? false : true;
      });
      FocusManager.instance.primaryFocus?.unfocus();
    } else {
      // Handle the case where _selectedLevel is null
      setState(() {
        _loadingGetQuanititySample = false;
      });
    }

    _recheckResultDetail();
  }

  void _checkValidate() {
    setState(() {
      // _errorSelectType = _selectedType == null || _selectedType!.catalogCode == " ";
      _errorPO = _controllerPONumber.text.isEmpty;
      // _errorSKU = _qualityControl!.qcType != "NVL" && _controllerSKU.text.isEmpty;
      // _errorQuantityCheck = _controllerInspectLotQuantity.text.isEmpty;
      // _errorSelectedResultQualityView = _selectedResult == null || _selectedResult!.catalogCode == " ";
      _errorTestMethodDetail = _selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ";
      _errorLevelDetail = _selectedLevel == null || _selectedLevel!.catalogCode == " ";
      // _errorAcceptableLevelDetail = _controllerMucChapNhan.text.isEmpty;
      _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isEmpty;
      _errorResultCheckDetail = _selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ";

      debugPrint(_errorAcceptableLevelDetail.toString());

      for (int i = 0; i < _lsSelectedThongTinKiemTra.length; i++) {
        _lsErrorInfor[i] = _lsSelectedThongTinKiemTra[i] == null;
      }
    });
  }

  Future<void> _loadDataAndSetDefault() async {
    try {
      if (!isTokenLive(widget.dateTimeOld)) {
        setState(() {
          _timeOut = true;
        });
        return;
      }

      if (!mounted) return;
      await _getQualitityControl();

      if (!mounted) return;
      if (_qualityControl != null) {
        // _qualityControl!.qcType == "NVL" ? _getMaterial() : updateValues();
        initValue();
      } else {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _isError = true;
        _timeOut = false;
      });
    }
  }

  void initValue() {
    debugPrint("0. TIEN updateValues");
    debugPrint((_qualityControl != null && _qualityControl!.qualityControlDetail != null).toString());

    if (_qualityControl != null && _qualityControl!.qualityControlDetail != null) {
      _loadNhaMayAndPhanXuongData(_qualityControl!.saleOrgCode);
    } else {
      _loadNhaMayAndPhanXuongData(widget.user.companyCode);
    }

    setState(() {
      _isLoading = false;
      _qualityDateString = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
      _qualityChckerInfo = createQualityCheckerInfo();
      _lsHangMucKiemTraMasterData = _qualityControlModel!.hangMucKiemTra ?? [];
      _lsCaNhanGayLoiMasterData = _qualityControlModel!.caNhanGayLoiList ?? [];

      updateControllerPONumber();
      updateControllerSKU();
      updateSelectedLoaiMau();
      // updateSelectedType();
      // updateController2Text();
      // updateSelectedResult();
      // updateQualityControlDetail();
      // setupQualityControlInformation();

      updateQualityControlDetail();

      setupThongTinKiemtra();
      setupError();
    });
  }

  QualityCheckerInfo createQualityCheckerInfo() {
    // this is the format qcSaleEmployee: "employeeCode | employeeFullname"
    // get employeeCode only and get employeeFullname only

    var employeeCode = "";
    var employeeFullname = "";

    if (_qualityControl!.qualityChecker != null) {
      employeeCode = _qualityControl!.qcSaleEmployee!.split(" | ")[0];
      employeeFullname = _qualityControl!.qcSaleEmployee!.split(" | ")[1];
    }

    var qualityCheckerList = QualityCheckerInfo(
      accountId: _qualityControl!.qualityChecker ?? widget.user.accountId.toString(),
      salesEmployeeCode: _qualityControl!.qualityChecker != null ? employeeCode : widget.user.employeeCode,
      salesEmployeeName: _qualityControl!.qualityChecker != null ? employeeFullname : widget.user.fullName,
    );
    return qualityCheckerList;
  }

  void updateSelectedType() {
    if (_qualityControl!.qualityType != null) {
      int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityType);
      _selectedType = _qualityTypeList[indexType];
    } else {
      int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == "DAUVAO");
      _selectedType = _qualityTypeList[indexType];
    }
  }

  void updateControllerPONumber() {
    if (_qualityControl!.po != null) {
      _controllerPONumber.text = _qualityControl!.po.toString();
    }
  }

  void updateControllerSKU() {
    if (_qualityControl!.sku != null) {
      _controllerSKU.text = _qualityControl!.sku.toString();
    }
  }

  void updateSelectedLoaiMau() {
    String? loaiMauValue = _qualityControl?.getPropertyValue("loaiMau");
    if (loaiMauValue != null && loaiMauValue.isNotEmpty) {
      _selectedLoaiMau = loaiMauValue.toString();
    } else {
      _selectedLoaiMau = ''; // Set to empty option by default
    }
  }

  // void updateSelectedResult() {
  //   if (_qualityControl!.result != null) {
  //     int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.result);
  //     _selectedResult = _lsResultList[indexResult];
  //   }
  // }

  void updateQualityControlDetail() {
    if (_qualityControl!.qualityControlDetail != null) {
      updateSelectedMethod();
      updateLimitMethod();
      updateSelectedLevel();
      // updateControllerMucChapNhanText();
      updateControllerSoLuongKiemTraText();
      updateTongSoSanPhamLoi();
      updateSelectedResultDetail();
      updateSoLuongLoiChapNhan();
      updateLanKiemTra();
    } else {
      // Set default is KHOGNCHAPNHAN if not have qualityControlDetail
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, QualityControlDetailFunction.defaultValueLimitCriticalList.catalogCode);

      // Auto select 100% method for new entries
      int default100Method = _lsTestMethodList.indexWhere((element) => element.catalogCode == "100");
      if (default100Method != -1) {
        _selectedTestMethod = _lsTestMethodList[default100Method];
      }
    }
  }

  void updateSelectedMethod() {
    if (_qualityControl!.qualityControlDetail!.testMethod != null) {
      int indexMethod = _lsTestMethodList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.testMethod);
      _selectedTestMethod = _lsTestMethodList[indexMethod];
    } else {
      // Auto select 100% method if no method is selected
      int default100Method = _lsTestMethodList.indexWhere((element) => element.catalogCode == "100");
      if (default100Method != -1) {
        _selectedTestMethod = _lsTestMethodList[default100Method];
      } else {
        _selectedTestMethod = null;
      }
    }
  }

  void updateLimitMethod() {
    debugPrint("1. updateLimitMethod");
    if (_qualityControl != null && _qualityControl!.qualityControlDetail != null) {
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, _qualityControl!.qualityControlDetail!.limitCritical);
      _selectedLimitHigh = getSelectedLimit(_lsLimitHighList, _qualityControl!.qualityControlDetail!.limitHigh);
      _selectedLimitLow = getSelectedLimit(_lsLimitLowList, _qualityControl!.qualityControlDetail!.limitLow);
    } else {
      debugPrint("2. _selectedLimitCritical");
      _selectedLimitCritical = getSelectedLimit(_lsLimitCriticalList, QualityControlDetailFunction.defaultValueLimitCriticalList.catalogCode);
    }
  }

  DropdownItemList? getSelectedLimit(List<DropdownItemList> list, String? limit) {
    if (limit != null) {
      int index = list.indexWhere((element) => element.catalogCode == limit);
      return (index != -1) ? list[index] : null;
    } else {
      return null;
    }
  }

  void updateSelectedLevel() {
    if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel != "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
    } else if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel == "OTHER") {
      int indexLevel = _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
      _selectedLevel = _lsSamplingLevelList[indexLevel];
      _controllerHideDetailLever.text = _qualityControl!.qualityControlDetail!.samplingLevelName ?? "";
      _hideDetailLever = true;
    } else {
      _selectedLevel = null;
    }
  }

  void updateControllerMucChapNhanText() {
    if (_qualityControl!.qualityControlDetail!.acceptableLevel != null) {
      _controllerMucChapNhan.text = _qualityControl!.qualityControlDetail!.acceptableLevel.toString();
    } else {
      _controllerMucChapNhan.text = _controllerMucChapNhan.text;
    }
  }

  void updateControllerSoLuongKiemTraText() {
    if (_qualityControl!.qualityControlDetail!.inspectionQuantity != null) {
      _controllerInspectQuantityDetail.text = (_qualityControl!.qualityControlDetail!.inspectionQuantity!.round()).toString();
    } else {
      _controllerInspectQuantityDetail.text = _controllerInspectQuantityDetail.text;
    }
  }

  void updateTongSoSanPhamLoi() {
    if (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi != null) {
      _controllerTongSoSanPhamLoi.text = (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi!.round()).toString();
    } else {
      _controllerTongSoSanPhamLoi.text = _controllerTongSoSanPhamLoi.text;
    }
  }

  void updateSoLuongLoiChapNhan() {
    if (_qualityControl!.qualityControlDetail!.loiNangChapNhan != null) {
      _controllerSLLoiNangChapNhan.text = _qualityControl!.qualityControlDetail!.loiNangChapNhan!.toString();
    }

    if (_qualityControl!.qualityControlDetail!.loiNheChapNhan != null) {
      _controllerSLLoiNheChapNhan.text = _qualityControl!.qualityControlDetail!.loiNheChapNhan!.toString();
    }
  }

  void updateSelectedResultDetail() {
    if (_qualityControl!.qualityControlDetail!.result != null) {
      int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.result);
      _selectedResultDetail = _lsResultList[indexResult];
    } else {
      _selectedResultDetail = null;
    }
  }

  void updateLanKiemTra() {
    if (_qualityControl!.qualityControlDetail!.checkingTimes != null) {
      _controllerLanKiemTra.text = (_qualityControl!.qualityControlDetail!.checkingTimes!.round()).toString();
    } else {
      _controllerLanKiemTra.text = "1";
    }
  }

  void setupThongTinKiemtra() {
    if (_lsThongTinKiemTra.isNotEmpty) {
      for (int i = 0; i < _lsThongTinKiemTra.length; i++) {
        addThongTinKiemTraSaved(i);
      }
    } else {
      addDefaultThongTinKiemTra();
    }
  }

  void addThongTinKiemTraSaved(int i) {
    _lsControllerSoSanPhamLoi.add(TextEditingController());
    _lsControllerGhiChu.add(TextEditingController());
    _lsControllerThongTinKiemTra.add(TextEditingController());

    _lsControllerSoSanPhamLoi[i].text =
        (_lsThongTinKiemTra[i].soSanPhamLoi == null ? _lsControllerSoSanPhamLoi[i].text : _lsThongTinKiemTra[i].soSanPhamLoi?.round().toString())!;
    _lsControllerGhiChu[i].text = _lsThongTinKiemTra[i].notes ?? _lsControllerGhiChu[i].text;

    var isOldThongTin = _lsThongTinKiemTra[i].qualityControlInformationCode! < 2000;

    if (isOldThongTin) {
      _indexInfoTemp =
          _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId) != -1
              ? _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId)
              : _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == " ");

      _lsGetIndexInfo.add(_indexInfoTemp);
      _lsSelectedThongTinKiemTra.add(_lsThongTinKiemTraSelectMasterData![_lsGetIndexInfo[i]]);
      _lsControllerThongTinKiemTra[i].text = _lsSelectedThongTinKiemTra[i] == null ? "" : _lsSelectedThongTinKiemTra[i]!.name ?? "";
    } else {
      _indexInfoTemp = _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId) != -1
          ? _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == _lsThongTinKiemTra[i].qualityControlInformationId)
          : _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == " ");

      _lsGetIndexInfo.add(_indexInfoTemp);
      // _lsSelectedThongTinKiemTra.add(_lsHangMucKiemTraMasterData![_lsGetIndexInfo[i]]);
      var item = _lsHangMucKiemTraMasterData![_lsGetIndexInfo[i]];
      _lsSelectedThongTinKiemTra.add(ThongTinKiemTra(id: item.id, name: item.name));

      _lsControllerThongTinKiemTra[i].text = _lsSelectedThongTinKiemTra[i] == null ? "" : _lsSelectedThongTinKiemTra[i]!.name ?? "";
    }

    _lsFileThongTinKiemTra.add([]);
    _lsErrorInfor.add(false);
    _checkVisiButtonThongTinKiemTra.add(false);
  }

  void addEmptyThongTinKiemTra([bool canRemove = false]) {
    _checkVisiButtonThongTinKiemTra.add(canRemove);

    _lsThongTinKiemTra.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
    _lsControllerSoSanPhamLoi.add(TextEditingController());
    _lsControllerGhiChu.add(TextEditingController());
    _lsControllerThongTinKiemTra.add(TextEditingController());
    _indexInfoTemp = _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == " ");
    _lsGetIndexInfo.add(_indexInfoTemp);
    _lsFileThongTinKiemTra.add([]);
    _lsSelectedThongTinKiemTra.add(null);
    _lsErrorInfor.add(false);
  }

  void addDefaultThongTinKiemTra([bool canRemove = false]) {
    if (_lsHangMucKiemTraMasterData.isNotEmpty) {
      for (var i = 0; i < _lsHangMucKiemTraMasterData.length; i++) {
        var item = _lsHangMucKiemTraMasterData[i];

        _checkVisiButtonThongTinKiemTra.add(false);

        _lsThongTinKiemTra.add(QualityControlInformation(
            checkedFileViewModel: [],
            file: [],
            workCenterName: " ",
            qualityControlInformationCode: item.code,
            qualityControlInformationName: item.name,
            qualityControlQCInformationId: " ",
            qualityControlId: " ",
            qualityControlInformationId: item.id,
            workCenterCode: " ",
            notes: " "));

        _lsControllerThongTinKiemTra.add(TextEditingController(text: item.name));
        _lsControllerSoSanPhamLoi.add(TextEditingController());
        _lsControllerGhiChu.add(TextEditingController());
        // _indexInfoTemp = _lsThongTinKiemTraSelectMasterData!.indexWhere((element) => element.id == item.id);
        _indexInfoTemp = _lsHangMucKiemTraMasterData!.indexWhere((element) => element.id == item.id);
        _lsGetIndexInfo.add(_indexInfoTemp);
        _lsFileThongTinKiemTra.add([]);
        _lsSelectedThongTinKiemTra.add(ThongTinKiemTra(id: item.id, name: item.name));
        _lsErrorInfor.add(false);
      }

      showToast(
        context: context,
        message: "Đã load ${_lsHangMucKiemTraMasterData.length} checklist cần kiểm tra",
        duration: 2,
      );
    }
  }

  void setupError() {
    if (_lsError.isNotEmpty) {
      for (int i = 0; i < _lsError.length; i++) {
        addErrorFromSaved(i);
      }
    } else {
      // Update: không hiển thị thông tin lỗi mặc định, nếu muốn thêm thì phải thêm
      // addDefaultError();
    }
  }

  void addErrorFromSaved(int i) {
    _checkVisiButtonError.add(false);
    _lsSelectedMucDoLoi.add(null);
    _lsSelectedCongDoanLoi.add(null); // Nếu là select thì thêm null vô
    _lsSelectedPhuongAnXuLy.add(null); // Nếu là select thì thêm null vô
    _lsControllerSoLuongLoi.add(TextEditingController());
    _lsControllerGhiChuLoi.add(TextEditingController());
    _lsControllerError.add(TextEditingController());

    _lsControllerNhaMayLoi.add(TextEditingController());
    _lsControllerPhanXuongLoi.add(TextEditingController());
    _lsControllerToChuyenLoi.add(TextEditingController());
    // _lsControllerCaNhanGayLoi.add(TextEditingController());

    _lsControllerSoLuongLoi[i].text = _lsError[i].quantityError == null ? (0.toString()) : ((_lsError[i].quantityError ?? 0.0).round()).toString();
    _lsSelectedMucDoLoi[i] = _lsError[i].levelError == null
        ? _lsSelectedMucDoLoi[i]
        : _lsDataGetDefetchLevel.firstWhereOrNull((element) => element.key == _lsError[i].levelError.toString());

    // Update 1 Lấy công đoạn nhỏ đang chọn
    _lsSelectedCongDoanLoi[i] = _lsError[i].congDoanLoi == null
        ? _lsSelectedCongDoanLoi[i]
        : _lsCongDoanNhoMasterData.firstWhereOrNull((element) => element.catalogCode == _lsError[i].congDoanLoi.toString());
    _lsSelectedPhuongAnXuLy[i] = _lsError[i].phuongAnXuLy == null
        ? _lsSelectedPhuongAnXuLy[i]
        : _lsPhuongAnXuLyMasterData.firstWhereOrNull((element) => element.catalogCode == _lsError[i].phuongAnXuLy.toString());

    // Update 1 lấy thông tin từ master
    // _lsControllerNhaMayLoi[i].text = _lsError[i].nhaMayLoi == null ? _qualityControl!.storeName! : _lsError[i].nhaMayLoi.toString();
    // _lsControllerPhanXuongLoi[i].text = _lsError[i].phanXuongLoi == null ? "" : _lsError[i].phanXuongLoi.toString();
    // _lsControllerToChuyenLoi[i].text = _lsError[i].toChuyenLoi == null ? "" : _lsError[i].toChuyenLoi.toString();

    _lsControllerGhiChuLoi[i].text = _lsError[i].notes == null ? _lsControllerGhiChuLoi[i].text : _lsError[i].notes.toString();

    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
    _lsGetIndexError.add(_indexError);
    _lsFileHinhAnhLoi.add([]);
    _lsSelectedError.add(_lsGetIndexError[i] == -1 ? null : _lsErrorList![_lsGetIndexError[i]]);
    _lsControllerError[i].text = _lsSelectedError[i] == null ? "" : _lsSelectedError[i]!.catalogTextVi ?? "";

    _indexCaNhanGayLoi = _lsCaNhanGayLoiMasterData!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
    _lsGetIndexCaNhanGayLoi.add(_indexCaNhanGayLoi);

    _lsFileHinhAnhLoi.add([]);

    // Add default 1 cá nhân gây lỗi
    _lsControllerCaNhanGayLoi.add(TextEditingController());
    var foundCaNhanGayLoi =
        _lsCaNhanGayLoiMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].caNhanGayLoi, orElse: () => DropdownItemList());
    // if exists in data loaded then assign it, else empty
    _lsControllerCaNhanGayLoi[i].text = foundCaNhanGayLoi.catalogTextVi ?? "";

    List<String> caNhanGayLoiMany = _lsError[i].caNhanGayLoiMany == null ? [] : _lsError[i].caNhanGayLoiMany!.split(",");

    if (caNhanGayLoiMany.isEmpty) {
      // NEW
      // Cá nhân gây lỗi (multiple)
      _lsControllerCaNhanGayLoiMany.add([TextEditingController()]);

      // Add the default "Cá nhân gây lỗi" input
      _lsControllerCaNhanGayLoiMany[i][0].text = foundCaNhanGayLoi.catalogTextVi ?? "";
    } else {
      _lsControllerCaNhanGayLoiMany.add([]);
      caNhanGayLoiMany.forEach((element) {
        _lsControllerCaNhanGayLoiMany[i].add(TextEditingController());
        var foundCaNhanGayLoiMany = _lsCaNhanGayLoiMasterData!.firstWhere((item) => item.catalogCode == element, orElse: () => DropdownItemList());
        _lsControllerCaNhanGayLoiMany[i].last.text = foundCaNhanGayLoiMany.catalogTextVi ?? "";
      });
    }

    _lsSelectedCaNhanGayLoi.add(null);
    _lsControllerCaNhanGayLoi.add(TextEditingController());

    _indexCaNhanGayLoi = _lsCaNhanGayLoiMasterData!.indexWhere((element) => element.catalogCode == _lsError[i].caNhanGayLoi);

    if (_indexCaNhanGayLoi != -1) {
      // _lsControllerCaNhanGayLoi.add(TextEditingController());
      _lsControllerCaNhanGayLoi[i].text = _lsError[i] == null ? "" : _lsCaNhanGayLoiMasterData[_indexCaNhanGayLoi].catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 0 | 1. Quản đốc
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 1 | 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 2 | 3. QA-QC
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 3 | 4. KCS

    _lsControllerCaNhanLoi1QuanDoc.add(TextEditingController(text: _lsError[i].quanDoc));

    var foundQuanDoc =
        _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].quanDoc, orElse: () => DropdownItemList());

    if (foundQuanDoc.catalogCode != null) {
      _lsControllerCaNhanLoi1QuanDoc[i].text = foundQuanDoc.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi2ToTruong.add(TextEditingController(text: _lsError[i].toTruong));

    var foundToTruong =
        _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].toTruong, orElse: () => DropdownItemList());

    if (foundToTruong.catalogCode != null) {
      _lsControllerCaNhanLoi2ToTruong[i].text = foundToTruong.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi3QAQC.add(TextEditingController(text: _lsError[i].qaqc));

    var foundQAQC = _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].qaqc, orElse: () => DropdownItemList());

    if (foundQAQC.catalogCode != null) {
      _lsControllerCaNhanLoi3QAQC[i].text = foundQAQC.catalogTextVi ?? "";
    }

    _lsControllerCaNhanLoi4KCS.add(TextEditingController(text: _lsError[i].kcs));

    var foundKCS = _lsCaNhanGayLoiAllMasterData!.firstWhere((element) => element.catalogCode == _lsError[i].kcs, orElse: () => DropdownItemList());

    if (foundKCS.catalogCode != null) {
      _lsControllerCaNhanLoi4KCS[i].text = foundKCS.catalogTextVi ?? "";
    }
  }

  void addDefaultError([bool canRemove = false]) {
    _checkVisiButtonError.add(canRemove);
    _lsError.add(QualityControlErrorFunction.defaultListError);
    _lsControllerSoLuongLoi.add(TextEditingController());
    _lsSelectedMucDoLoi.add(null);
    _lsControllerGhiChuLoi.add(TextEditingController());
    // TODO: add master data
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 0 | 1. Quản đốc
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 1 | 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 2 | 3. QA-QC
    _lsControllerCaNhanLoiParent.add(TextEditingController()); // index: 3 | 4. KCS

    _lsControllerCaNhanLoi1QuanDoc.add(TextEditingController()); // 1. Quản đốc
    _lsControllerCaNhanLoi2ToTruong.add(TextEditingController()); // 2. Tổ trưởng/kỹ thuật
    _lsControllerCaNhanLoi3QAQC.add(TextEditingController()); // 3. QA-QC
    _lsControllerCaNhanLoi4KCS.add(TextEditingController()); // 4. KCS

    _lsControllerError.add(TextEditingController());

    _lsControllerCaNhanGayLoi.add(TextEditingController());

    _lsControllerCaNhanGayLoiMany.add([
      TextEditingController(),
    ]);

    _lsSelectedCaNhanGayLoi.add(null);
    _lsSelectedCaNhanGayLoiMany.add([null]);

    _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
    _lsGetIndexError.add(_indexError);
    _lsFileHinhAnhLoi.add([]);
    _lsSelectedError.add(null);
    _lsSelectedCaNhanGayLoi.add(null);
    _lsSelectedPhuongAnXuLy.add(null); // NEW

    // Công đoạn nhỏ bên app KCS, còn bên QAQC nghiệm thu không có
    // _lsSelectedCongDoanLoi.add(null);

    _lsSelectedCongDoanLoi.add(null);

    // New: nhà máy, phân xưởng, tổ chuyền lỗi
    _lsControllerNhaMayLoi.add(TextEditingController());
    _lsControllerPhanXuongLoi.add(TextEditingController());
    _lsControllerToChuyenLoi.add(TextEditingController());
  }

  void _setDate(DateTime? newDate) {
    if (!mounted) return;
    if (newDate != null) {
      setState(() {
        _qualityDate = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
        _qualityDateString = QualityControlFunction.getDateString2(_qualityControl, _qualityDate);
        debugPrint(_qualityDate!.toIso8601String());
      });
    } else {
      return;
    }
  }

  Future<void> _getQualitityControl() async {
    setState(() {
      _timeOut = false;
      _isLoading = true;
      _isNotWifi = false;
    });

    final responses = await Future.wait([
      QualityControlFunction.fetchQualityControlQCMau(
        widget.qualityControlId,
        widget.user.token.toString(),
        widget.qualityType,
      ),
    ]);

    if (!mounted) return;

    setState(() {
      if (responses.isNotEmpty) {
        setDefectLevelList(responses[0]);
        qualityControlDataLoaded(responses[0]);
      }
    });
  }

  void setDefectLevelList(dynamic data) {
    if (data != null) {
      _lsDataGetDefetchLevel = data.defectLevel as List<DataGetDefectLevel>;
    }
  }

  void qualityControlDataLoaded(dynamic data) {
    if (data != null) {
      _qualityControlModel = data as QualityControlModel?;

      if (_qualityControlModel != null) {
        _qualityControl = _qualityControlModel!.qualityControl;
        _lsThongTinKiemTra = _qualityControl!.qualityControlInformation?.isNotEmpty == true ? _qualityControl!.qualityControlInformation! : [];
        _lsError = _qualityControl!.error?.isNotEmpty == true ? _qualityControl!.error! : [];

        setupMasterDataList();

        setDate();
        setErrorsList();

        // setupThongTinKiemtra();
        // setupError();
      }
    } else {
      _isLoading = false;
    }
  }

  void setupMasterDataList() {
    _qualityTypeList = prependDefault(_qualityControlModel!.qualityTypeList, QualityControlFunction.defaultValueQualityTypeList);

    _lsTestMethodList = prependDefault(_qualityControlModel!.testMethodList, QualityControlDetailFunction.defaultValueTestMethodList);

    _lsCongDoanNhoMasterData = prependDefault(_qualityControlModel!.congDoanNhoList, QualityControlDetailFunction.defaultValueCongDoanNho);
    _lsPhuongAnXuLyMasterData = prependDefault(_qualityControlModel!.phuongAnXuLyList, QualityControlDetailFunction.defaultValuePhuongAnXuLy);

    _lsLimitCriticalList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitHighList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);
    _lsLimitLowList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);

    _lsLimitLowList = prependDefault(_qualityControlModel!.limitList, QualityControlDetailFunction.defaultValueLimitList);

    // new
    _lsNghiemThuTypeList = prependDefault(_qualityControlModel!.nghiemThuTypeList, QualityControlDetailFunction.defaultValueLoaiNghiemThu);

    _lsResultList = prependDefault(_qualityControlModel!.resultList, QualityControlDetailFunction.defaultResultList);
    _lsSamplingLevelList = prependDefault(_qualityControlModel!.samplingLevelList, QualityControlDetailFunction.defaultValueSamplingLevelList);
    _lsThongTinKiemTraSelectMasterData = _qualityControlModel!.qualityControlInformationIdList;

    // New
    _lsCaNhanGayLoiAllMasterData = _qualityControlModel!.caNhanGayLoiList!;
  }

  void updateQualityControlInformation() {
    _lsThongTinKiemTra = _qualityControl?.qualityControlInformation?.isNotEmpty == true ? _qualityControl!.qualityControlInformation! : [];
  }

  void updateErrors() {
    _lsError = _qualityControl?.error?.isNotEmpty == true ? _qualityControl!.error! : [];
  }

  void setDate() {
    _qualityDate = _qualityControl?.qualityDate != null
        ? DateFormat("yyyy-MM-ddThh:mm:ss").parse(_qualityControl!.qualityDate!)
        : DateFormat("yyyy-MM-dd hh:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()));
  }

  void setErrorsList() {
    _lsErrorList = _qualityControlModel!.errorList;
  }

  List<T> prependDefault<T>(List<T>? list, T defaultValue) {
    if (list == null) {
      return [defaultValue];
    } else {
      return [defaultValue, ...list];
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButtonTimeOut = true;
    });
  }

  void _deleteListImageTabCheck(int index) {
    if (!mounted) return;
    setState(() {
      _lsFileHeader.removeAt(index);
    });
  }

  void _pickFileImage(File file) {
    if (!mounted) return;
    setState(() {
      _lsFileHeader.add(file);
    });
  }

  void _pickFileImageInformation(MultiListImageFile multiListImageFile) {
    setState(() {
      _lsFileThongTinKiemTra[multiListImageFile.index].add(multiListImageFile.file);
    });
  }

  // void _getSelectedResult(ResultList? value) {
  //   setState(() {
  //     _selectedResult = value;
  //     if (_selectedResult == null || _selectedResult!.catalogCode == " ") {
  //       _errorSelectedResultQualityView = true;
  //     } else {
  //       _errorSelectedResultQualityView = false;
  //     }
  //   });
  // }

  void _getSelectedType(QualityTypeList? value) {
    setState(() {
      _selectedType = value;
      if (_selectedType == null || _selectedType!.catalogCode == " ") {
        _errorSelectType = true;
      } else {
        _errorSelectType = false;
      }
    });
  }

  void _getSelectedResultDetail(ResultList? value) {
    setState(() {
      _selectedResultDetail = value;
      if (_selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ") {
        _errorResultCheckDetail = true;
      } else {
        _errorResultCheckDetail = false;
      }
    });
    // print(_selectedResultDetail!.catalogTextVi.toString());
  }

  void _getSelectedLevel(SamplingLevelList? value) {
    if (value!.catalogCode != "OTHER") {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = false;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    } else {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = true;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    }
  }

  void _getSelectedMethod(TestMethodList? value) {
    setState(() {
      _selectedTestMethod = value;
      if (_selectedTestMethod == null || _selectedTestMethod!.catalogCode == " ") {
        _errorTestMethodDetail = true;
      } else {
        _errorTestMethodDetail = false;
      }
    });
  }

  Future<void> _onPhuongAnXuLyChanged(DropdownItemList? value, int index) async {
    if (value!.catalogCode! == " ") {
      _lsSelectedPhuongAnXuLy[index] = null;
    } else {
      _lsSelectedPhuongAnXuLy[index] = value;
    }
    setState(() {});
  }

  void clearForNew() {
    setState(() {
      clearQualityControl();
      clearThongTinKiemTra();
      clearErrors();

      setupThongTinKiemtra();
      setupError();
      // showToast(context: context, message: 'Thêm phiếu');
    });
  }

  void clearErrors() {
    _checkVisiButtonError.clear();
    _lsError.clear();
    _lsControllerSoLuongLoi.clear();
    _lsSelectedMucDoLoi.clear();
    _lsControllerGhiChuLoi.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();
    _lsControllerCaNhanLoiParent.clear();

    _lsControllerError.clear();

    _lsControllerCaNhanGayLoi.clear();
    _lsControllerCaNhanGayLoiMany.clear();

    _lsControllerCaNhanGayLoi.clear();

    _lsGetIndexError.clear();
    _lsFileHinhAnhLoi.clear();
    _lsSelectedError.clear();
    _lsSelectedCaNhanGayLoi.clear();
  }

  void clearQualityControl() {
    _controllerMauHoanThien.clear();
  }

  void clearThongTinKiemTra() {
    _selectedTestMethod = null;
    // _lsSamplingLevelList.clear();
    _selectedLevel = QualityControlDetailFunction.defaultValueSamplingLevelList;
    _controllerInspectQuantityDetail.clear();

    _controllerTongSoSanPhamLoi.clear();
    _controllerTongSoLuongLoi.clear();

    _selectedResultDetail = null;

    _selectedLimitCritical = null;
    _selectedLimitHigh = null;
    _selectedLimitLow = null;

    _checkVisiButtonThongTinKiemTra.clear();

    _lsControllerThongTinKiemTra.clear();
    _lsControllerSoSanPhamLoi.clear();
    _lsControllerGhiChu.clear();

    _lsThongTinKiemTra.clear();

    _lsErrorInfor.clear();
    _lsSelectedThongTinKiemTra.clear();
    _lsGetIndexInfo.clear();

    _controllerSLLoiNangChapNhan.clear();
    _controllerSLLoiNheChapNhan.clear();
  }

  void _getSelectedLimitCritical(DropdownItemList? value) {
    setState(() {
      _selectedLimitCritical = value;
      if (_selectedLimitCritical == null || _selectedLimitCritical!.catalogCode == " ") {
        _errorLimitCriticalDetail = true;
      } else {
        _errorLimitCriticalDetail = false;
      }
    });
  }

  void _getSelectedLimitHigh(DropdownItemList? value) {
    setState(() {
      _selectedLimitHigh = value;
      if (_selectedLimitHigh == null || _selectedLimitHigh!.catalogCode == " ") {
        _errorLimitHighDetail = true;
      } else {
        _errorLimitHighDetail = false;
      }
    });
  }

  void _getSelectedLimitLow(DropdownItemList? value) {
    setState(() {
      _selectedLimitLow = value;
      if (_selectedLimitLow == null || _selectedLimitLow!.catalogCode == " ") {
        _errorLimitLowDetail = true;
      } else {
        _errorLimitLowDetail = false;
      }
    });
  }

  void _getSelectedLoaiMau(String? value) async {
    if (kDebugMode) {
      print("Loại mẫu changed: $value");
    }
    setState(() {
      _selectedLoaiMau = value;
      if (_qualityControl != null) {
        if (value != null && value.isNotEmpty) {
          _qualityControl!.setPropertyValue("loaiMau", value);

          // Set loading state
          _isLoading = true;
        }
      }
    });

    // Fetch checklist data for the selected sample type
    if (value != null && value.isNotEmpty) {
      try {
        final items = await QualityControlFunction.fetchChecklistMau(
          value,
          widget.user.token.toString(),
        );

        if (!mounted) return;

        setState(() {
          _lsHangMucKiemTraMasterData = items!;

          // Clear existing items
          _lsControllerThongTinKiemTra.clear();
          _lsControllerSoSanPhamLoi.clear();
          _lsControllerGhiChu.clear();
          _lsThongTinKiemTra.clear();

          setupThongTinKiemtra();

          _isLoading = false;
        });
      } catch (e) {
        if (kDebugMode) {
          print("Error fetching checklist for sample type: $e");
        }
        setState(() {
          _isLoading = false;
          // Add an empty item in case of error
          addEmptyThongTinKiemTra(true);
        });
      }
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _addNewThongTinKiemTra() {
    setState(() {
      addEmptyThongTinKiemTra(true);
    });
  }

  void _deleteListQualityInformation(MultiListImageDeleteFile multiListImageDeleteFile) {
    setState(() {
      _lsFileThongTinKiemTra[multiListImageDeleteFile.index].removeAt(multiListImageDeleteFile.indexImage);
    });
  }

  void _addNewCardError() {
    setState(() {
      addDefaultError(true);
    });
  }

  void _deleteListFileError(MultiDeleteImageErrorQuality multiDeleteImageErrorQuality) {
    setState(() {
      _lsFileHinhAnhLoi[multiDeleteImageErrorQuality.index].removeAt(multiDeleteImageErrorQuality.indexImageError);
    });
  }

  void _pickFileImageErrorQuality(MultiSelectImageErrorQuality multiSelectImageErrorQuality) {
    setState(() {
      _lsFileHinhAnhLoi[multiSelectImageErrorQuality.index].add(multiSelectImageErrorQuality.file);
    });
  }

  void _checkErrorQuantityView() {
    // setState(() {
    if (_controllerPONumber.text.isNotEmpty) {
      if (_errorPO != false) {
        setState(() {
          _errorPO = false;
        });
      }
    } else {
      if (_errorPO != true) {
        setState(() {
          _errorPO = true;
        });
      }
    }
    // });
    // });
  }

  void _checkClearLsSelectedInfo(int index) {
    if (_lsSelectedThongTinKiemTra[index] != null) {
      setState(() {
        _lsSelectedThongTinKiemTra[index] = null;
      });
    }
  }

  void _setTypeAhead(TypeAheadErrorQuatity typeAheadErrorQuatity) {
    setState(() {
      _lsControllerError[typeAheadErrorQuatity.index ?? 0].text =
          typeAheadErrorQuatity.errorList == null ? "" : typeAheadErrorQuatity.errorList!.catalogTextVi ?? "";
      _lsSelectedError[typeAheadErrorQuatity.index ?? 0] = typeAheadErrorQuatity.errorList;
    });
  }

  void _setTypeAheadCaNhanGayLoi(TypeAheadCaNhanGayLoi typeAheadCaNhanGayLoi) {
    setState(() {
      _lsControllerCaNhanGayLoi[typeAheadCaNhanGayLoi.index ?? 0].text =
          typeAheadCaNhanGayLoi.selectedItem == null ? "" : typeAheadCaNhanGayLoi.selectedItem!.catalogTextVi ?? "";
      _lsSelectedCaNhanGayLoi[typeAheadCaNhanGayLoi.index ?? 0] = typeAheadCaNhanGayLoi.selectedItem;
    });
  }

  void _setTypeAheadCaNhanGayLoiMany(TypeAheadCaNhanGayLoi typeAheadCaNhanGayLoi) {
    var errorIndex = typeAheadCaNhanGayLoi.index ?? -1;
    var caNhanLoiIndex = typeAheadCaNhanGayLoi.caNhanLoiIndex ?? -1;

    debugPrint("errorIndex: $errorIndex, caNhanLoiIndex: $caNhanLoiIndex");

    if (errorIndex == -1 || caNhanLoiIndex == -1) {
      return;
    }

    // check on the list of the selected items already exists typeAheadCaNhanGayLoi.selectedItem!.catalogCode

    if (_lsSelectedCaNhanGayLoiMany[errorIndex].contains(typeAheadCaNhanGayLoi.selectedItem)) {
      showToast(context: context, message: "Cá nhân này đã được chọn");
      return;
    }

    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex][caNhanLoiIndex].text =
          typeAheadCaNhanGayLoi.selectedItem == null ? "" : typeAheadCaNhanGayLoi.selectedItem!.catalogTextVi ?? "";
      _lsSelectedCaNhanGayLoiMany[errorIndex][caNhanLoiIndex] = typeAheadCaNhanGayLoi.selectedItem;
    });
  }

  void _checkErrorSelectedInfo(QuantityInformationSelected quantityInformationSelected) {
    setState(() {
      _lsControllerThongTinKiemTra[quantityInformationSelected.index ?? 0].text =
          quantityInformationSelected.qualityControlInformationIdList!.name ?? "";
      _lsSelectedThongTinKiemTra[quantityInformationSelected.index ?? 0] = quantityInformationSelected.qualityControlInformationIdList;
      if (_lsSelectedThongTinKiemTra[quantityInformationSelected.index ?? 0] != null) {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != false) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = false;
        }
      } else {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != true) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = true;
        }
      }
    });
  }

  void _updateTongSoSanPhamLoi() {
    int total = 0;
    for (var controller in _lsControllerSoSanPhamLoi) {
      String text = controller.text;
      // In case the text is empty or is not a valid integer, skip the iteration.
      if (text.isEmpty || int.tryParse(text) == null) {
        continue;
      }
      total += int.parse(text);
    }

    // After the loop, you can check if the total is valid and set to _controllerTongSoSanPhamLoi
    if (total != null && total >= 0) {
      _controllerTongSoSanPhamLoi.text = total.toString();
    }
  }

  void _updateTongSoLuongLoi() {
    int total = 0;
    for (var controller in _lsControllerSoLuongLoi) {
      String text = controller.text;
      // In case the text is empty or is not a valid integer, skip the iteration.
      if (text.isEmpty || int.tryParse(text) == null) {
        continue;
      }
      total += int.parse(text);
    }

    // After the loop, you can check if the total is valid and set to _controllerTongSoLuongLoi
    if (total != null && total >= 0) {
      _controllerTongSoLuongLoi.text = total.toString();
    }
  }

  void _onRadioChanged(RadioValue? value, int index) {
    if (value == null) {
      return;
    }
    setState(() {
      _lsThongTinKiemTra[index].outcomeStatus = getOutcomeStatus(value);
    });
  }

  // Khi thay đổi text thì clear giá trị đã chọn vì xài typeahead
  // Chỉ set value khi chọn item trong typeahead
  void _onDanhSachLoiChanged(int index) {
    if (_lsSelectedError[index] != null) {
      setState(() {
        _lsSelectedError[index] = null;
      });
    }
  }

  // Khi thay đổi text thì clear giá trị đã chọn vì xài typeahead
  // Chỉ set value khi chọn item trong typeahead
  void _clearCaNhanGayLoiIndex(int index) {
    if (_lsSelectedCaNhanGayLoi[index] != null) {
      setState(() {
        _lsSelectedCaNhanGayLoi[index] = null;
      });
    }
  }

  void _clearCaNhanGayLoiIndexMany(int caNhanLoiIndex, int index) {
    if (_lsSelectedCaNhanGayLoiMany[index][caNhanLoiIndex] != null) {
      setState(() {
        _lsSelectedCaNhanGayLoiMany[index][caNhanLoiIndex] = null;
      });
    }
  }

  // Khi thay đổi text thì clear giá trị đã chọn vì xài typeahead
  // Chỉ set value khi chọn item trong typeahead
  void _onCaNhanGayLoiChanged(int index) {
    if (_lsSelectedCaNhanGayLoi[index] != null) {
      setState(() {
        _lsSelectedCaNhanGayLoi[index] = null;
      });
    }
  }

  // void _checkErrorSelectedInfo(int index) {
  //   if (_lsSelectedInfo[index] != null) {
  //     if (_lsErrorInfor[index] != false) {
  //       setState(() {
  //         _lsErrorInfor[index] = false;
  //       });
  //     }
  //   } else {
  //     if (_lsErrorInfor[index] != true) {
  //       setState(() {
  //         _lsErrorInfor[index] = true;
  //       });
  //     }
  //   }
  // }
  void _deleteItemListInformation(int index) {
    setState(() {
      _lsThongTinKiemTra.removeAt(index);
      _lsControllerSoSanPhamLoi.removeAt(index);
      _lsControllerGhiChu.removeAt(index);
      _lsControllerThongTinKiemTra.removeAt(index);
      // _focusInformation.removeAt(index);
      _lsGetIndexInfo.removeAt(index);
      _lsFileThongTinKiemTra.removeAt(index);
      _lsSelectedThongTinKiemTra.removeAt(index);
      _lsErrorInfor.removeAt(index);
      _checkVisiButtonThongTinKiemTra.removeAt(index);
    });
  }

  void _deleteItemListError(int index) {
    setState(() {
      _lsError.removeAt(index);
      _lsControllerSoLuongLoi.removeAt(index);
      _lsSelectedMucDoLoi.removeAt(index);
      _lsControllerGhiChuLoi.removeAt(index);
      _lsControllerError.removeAt(index);

      _lsControllerCaNhanGayLoi.removeAt(index);
      _lsSelectedCaNhanGayLoi.removeAt(index);

      _lsControllerCaNhanGayLoiMany.removeAt(index);
      _lsSelectedCaNhanGayLoiMany.removeAt(index);

      // _focusError.removeAt(index);
      _lsGetIndexError.removeAt(index);
      _lsFileHinhAnhLoi.removeAt(index);
      _lsSelectedError.removeAt(index);
      _checkVisiButtonError.removeAt(index);
      _lsErrorList = _qualityControlModel!.errorList;
    });

    _updateTongSoLuongLoi();
    _recheckResultDetail();
  }

  void _recheckResultDetail() {
    // appear any NGHIEMTRONG => _lsResultList find failed
    // _lsErrorInformationAql

    var isNghiemTrong = false;
    var isLoiNangPassed = false;
    var isLoiNhePassed = false;

    var sumLoiNang = 0;
    var sumLoiNhe = 0;

    var listIndexLoiNang;
    var listIndexLoiNhe;

    // if contain NGHIEMTRONG then _lsResultList find
    if (kDebugMode) {
      print(_lsSelectedMucDoLoi);
      print(_lsControllerSoLuongLoi); //
      print(_lsResultList); // find failed, find passed
    }
    // "QualityControl_Result_Fail"
    // "QualityControl_Result_Pass"

    // _controllerSLLoiNangChapNhan
    // _controllerSLLoiNheChapNhan

    var resultPassed = _lsResultList.where((element) => element.catalogCode == "QualityControl_Result_Pass").firstOrNull;
    var resultFailed = _lsResultList.where((element) => element.catalogCode == "QualityControl_Result_Fail").firstOrNull;

    isNghiemTrong = _lsSelectedMucDoLoi.any((item) => item?.key == "NGHIEMTRONG");

    if (isNghiemTrong) {
      _getSelectedResultDetail(resultFailed);
      return;
    }

    listIndexLoiNang = _lsSelectedMucDoLoi.asMap().entries.where((entry) => entry.value?.key == "NANG").map((entry) => entry.key).toList();

    listIndexLoiNhe = _lsSelectedMucDoLoi.asMap().entries.where((entry) => entry.value?.key == "NHE").map((entry) => entry.key).toList();

    if (_lsControllerSoLuongLoi.isNotEmpty) {
      sumLoiNang = listIndexLoiNang.fold(0, (prev, index) => prev + (int.tryParse(_lsControllerSoLuongLoi[index].text) ?? 0));
      sumLoiNhe = listIndexLoiNhe.fold(0, (prev, index) => prev + (int.tryParse(_lsControllerSoLuongLoi[index].text) ?? 0));

      int valueNangChapNhan = int.tryParse(_controllerSLLoiNangChapNhan.text) ?? 0;
      int valueNheChapNhan = int.tryParse(_controllerSLLoiNheChapNhan.text) ?? 0;

      isLoiNangPassed = sumLoiNang <= valueNangChapNhan;
      isLoiNhePassed = sumLoiNhe <= valueNheChapNhan;
    }

    var result;

    if (isLoiNangPassed && isLoiNhePassed) {
      result = resultPassed;
    } else {
      result = resultFailed;
    }

    if (result != null) {
      _getSelectedResultDetail(result);
    }
  }

  void _onSoLuongLoiChanged(int index, String value) {
    // print('-- TIEN số lượng lỗi changed');
    // print(index);
    // print(value);

    if (value.isEmpty) {
      return;
    }

    _updateTongSoLuongLoi();
    _recheckResultDetail();
  }

  void _getSelectedDefectLevel(DropdownDefetchLevel item) {
    // print('-- TIEN mức độ lỗi changed');
    // print(item);
    // print(item.value);
    // print(item.value!.key! + " " + item.value!.value!);
    setState(() {
      _lsSelectedMucDoLoi[item.index] = item.value;
    });

    _recheckResultDetail();
  }

  bool canSendQualityControl() {
    // var ret =
    //     // _errorSelectType == false &&
    //     // _errorPO == false &&
    //     _errorQuantityCheck == false &&
    //     _errorSelectedResultQualityView == false &&
    //     _errorTestMethodDetail == false &&
    //     _errorLevelDetail == false &&
    //     _errorAcceptableLevelDetail == false &&
    //     _errorQuantityCheckDetail == false &&
    //     _errorResultCheckDetail == false &&
    //     _lsErrorInfor.where((element) => element == true).isEmpty;

    bool ret = true;

    if (_errorQuantityCheck) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorSelectedResultQualityView) {
      ret = false;
    }

    if (_errorTestMethodDetail) {
      showToast(context: context, message: "Vui lòng chọn phương pháp lấy mẫu");
      ret = false;
    }

    // if (_errorLevelDetail) {
    //   showToast(context: context, message: "Vui lòng chọn mức độ lấy mẫu KT");
    //   ret = false;
    // }

    // if (_errorAcceptableLevelDetail) {
    //   showToast(context: context, message: "Error: Acceptable Level Detail failed!");
    //   ret = false;
    // }

    if (_errorQuantityCheckDetail) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorResultCheckDetail) {
      ret = false;
    }

    if (_lsErrorInfor.where((element) => element == true).isNotEmpty) {
      showToast(context: context, message: "Vui lòng chọn hạng mục kiểm tra");
      ret = false;
    }

    return ret;
  }

  void submitData() {
    // if (kDebugMode) {
    //   // print("--- TIEN _qualityControl");
    //   // print(json.encode(_qualityControl));

    //   // print("--- TIEN _selectedTestMethod");
    //   // print(json.encode(_selectedTestMethod));

    //   // print("--- TIEN _lsThongTinKiemTra");
    //   // print(json.encode(_lsThongTinKiemTra));

    //   // print("--- TIEN _lsFileThongTinKiemTra");
    //   // print(_lsFileThongTinKiemTra);

    //   print("--- TIEN _lsControllerSoLuongLoi");
    //   print(_lsControllerSoLuongLoi);

    //   // print("--- TIEN _lsFileHinhAnhLoi");
    //   // print(_lsFileHinhAnhLoi);

    //   return;
    // }

    if (!isTokenLive(widget.dateTimeOld)) {
      Platform.isAndroid
          ? showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
          : showCupertinoDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
      return;
    }

    _checkValidate();
    FocusManager.instance.primaryFocus?.unfocus();
    debugPrint(_errorAcceptableLevelDetail.toString());
    // debugPrint(_errorAcceptableLevelDetail.toString());
    // debugPrint(_lsTextEditingControllerError_1[0].text);
    var canSend = canSendQualityControl();

    if (canSend) {
      // Update the quality control model with the selected Nhà máy and Phân xưởng values
      if (_selectedNhaMay != null && _selectedNhaMay!.saleOrgCode != null) {
        _qualityControl!.saleOrgCode = _selectedNhaMay!.saleOrgCode;
      }

      if (_selectedPhanXuong != null && _selectedPhanXuong!.workShopCode != null) {
        _qualityControl!.workShopCode = _selectedPhanXuong!.workShopCode;
      }

      QualityControlFunction.postQualityControlQCMau(
        context,
        _lsFileHeader,
        _controllerInspectQuantityDetail.text,
        _qualityControl,
        _qualityChckerInfo,
        _qualityDate!.toIso8601String(),
        _selectedLoaiNghiemThu, //_selectedType,
        _selectedResultDetail,
        _qualityControl!.po ?? "",
        _controllerSKU.text,
        widget.qualityType,
        QualityControlDetailFunction.getSendQualityControlDetail2(
          _qualityControl,
          _selectedTestMethod,
          // Mức giới hạn
          _selectedLimitCritical,
          _selectedLimitHigh,
          _selectedLimitLow,

          _selectedLevel,
          _controllerMucChapNhan.text,
          _controllerInspectQuantityDetail.text,
          _controllerTongSoSanPhamLoi.text,

          _selectedResultDetail,
          _hideDetailLever,
          _controllerHideDetailLever.text,
          null,
          _qualityControlModel!.qualityControl!.mauHoanThien ?? "", // _controllerMauHoanThien.text, // new
          _controllerSLLoiNangChapNhan.text,
          _controllerSLLoiNheChapNhan.text,
          _controllerLanKiemTra.text,
        ),
        QualityControlInfoFunction.getLsSendQualityControlInformation2(
          _lsThongTinKiemTra,
          _qualityControl,
          _lsSelectedThongTinKiemTra,
          _lsControllerSoSanPhamLoi,
          _lsControllerGhiChu,
          _lsFileThongTinKiemTra,
        ),
        QualityControlErrorFunction.getLsError(
          _lsError,
          _qualityControl,
          _lsSelectedError,
          _lsSelectedMucDoLoi,
          _lsControllerSoLuongLoi,
          _lsControllerGhiChuLoi,
          _lsFileHinhAnhLoi,
          _lsSelectedCaNhanGayLoi,
          _lsSelectedCongDoanLoi,
          _lsSelectedPhuongAnXuLy, // new
          _lsControllerNhaMayLoi,
          _lsControllerPhanXuongLoi,
          _lsControllerToChuyenLoi,
          _lsSelectedCaNhanGayLoiMany,

          // New
          _lsControllerCaNhanLoi1QuanDoc,
          _lsControllerCaNhanLoi2ToTruong,
          _lsControllerCaNhanLoi3QAQC,
          _lsControllerCaNhanLoi4KCS,
        ),
        widget.user.token.toString(),
        _selectedLoaiMau ?? "",
        _selectedNhaMay?.saleOrgCode ?? "", // Add this line
        _selectedPhanXuong?.workShopCode ?? "", // Add this line
      );
    }
  }

  @override
  void dispose() {
    _controllerPONumber.dispose();
    _controllerSKU.dispose();
    _controllerInspectLotQuantity.dispose();
    _controllerMucChapNhan.dispose();
    _controllerInspectQuantityDetail.dispose();
    _controllerTongSoSanPhamLoi.dispose();
    _controllerTongSoLuongLoi.dispose();
    _controllerHideDetailLever.dispose();
    _controllerLanKiemTra.dispose();
    _focusPhanXuong.dispose();
    for (var i in _lsControllerThongTinKiemTra) {
      i.dispose();
    }
    for (var i in _lsControllerSoSanPhamLoi) {
      i.dispose();
    }
    for (var i in _lsControllerGhiChu) {
      i.dispose();
    }
    // for(var i in _focusError){
    //   i.dispose();
    // }
    for (var i in _lsControllerError) {
      i.dispose();
    }
    for (var i in _lsControllerCaNhanGayLoi) {
      i.dispose();
    }
    for (var i in _lsControllerCaNhanGayLoiMany) {
      for (var j in i) {
        j.dispose();
      }
    }
    // for (var i in _focusInformation) {
    //   i.dispose();
    // }
    for (var i in _lsControllerSoLuongLoi) {
      i.dispose();
    }
    // for (var i in _lsTextEditingControllerError_2) {
    //   i.dispose();
    // }
    for (var i in _lsControllerGhiChuLoi) {
      i.dispose();
    }
    debugPrint('dispose');

    WidgetsBinding.instance!.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      setState(() {
        _bottomPadding = keyboardHeight;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true ? _buildTimeOutView() : _buildTabControllerView(context);
  }

  Widget _buildTimeOutView() {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimeOut)));
  }

  Widget _buildTabControllerView(BuildContext context) {
    return DefaultTabController(
        initialIndex: 0,
        length: 4,
        child: WillPopScope(
            onWillPop: () async {
              Navigator.pop(context, false);
              return false;
            },
            child: _buildChildBasedOnCondition()));
  }

  Widget _buildChildBasedOnCondition() {
    if (_isLoading) {
      return _buildLoading();
    }

    if (_isError) {
      return _buildError();
    }

    if (_isNotWifi) {
      return _buildWifi();
    }

    if (_qualityControlModel == null || _qualityControl == null) {
      return _buildEmptyQualityModel();
    }

    return _buildTabContent();
  }

  Widget _buildLoading() {
    return Scaffold(
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildError() {
    return Scaffold(
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ErrorView(),
    );
  }

  Widget _buildWifi() {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: Text(
              title,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
            )),
        body: LostConnect(checkConnect: () => _loadDataAndSetDefault()));
  }

  Widget _buildEmptyQualityModel() {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context, false);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          )),
      body: const _ListNotFoundView(),
    );
  }

  Widget formGroup({
    required String title,
    required bool enabled,
    required TextEditingController controller,
    Function(String)? onChanged,
    String errorText = "",
    bool error = false,
    String hintText = "",
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                decoration: BoxDecoration(border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                child: TextFormField(
                  enabled: enabled,
                  maxLines: null,
                  textAlign: TextAlign.center,
                  controller: controller,
                  style: TextStyle(fontSize: 12.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    filled: true,
                    fillColor: Colors.white,
                    hintText: hintText,
                    hintStyle: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                  // onChanged: onChanged,
                  onChanged: onChanged ?? (value) {},
                ),
              ),
              Visibility(
                visible: error,
                child: SizedBox(height: 10.h),
              ),
              QualityErrorValidate(text: errorText, error: error)
            ]),
          ),
        ],
      ),
    );
  }

  Widget buildSamplingLevelListDropdown({
    required String title,
    required void Function(SamplingLevelList?)? onChanged,
    required List<DropdownMenuItem<SamplingLevelList>> items,
    required DropdownButtonBuilder selectedItemBuilder,
    required Widget error,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<SamplingLevelList>(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: _selectedLevel ?? QualityControlDetailFunction.defaultValueSamplingLevelList,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: onChanged, // dropdown will disabled if this is null
                    items: items,
                    selectedItemBuilder: selectedItemBuilder,
                  ),
                ),
              ),
              Visibility(
                visible: _errorLevelDetail,
                child: SizedBox(height: 10.h),
              ),
              error,
            ]),
          )
        ],
      ),
    );
  }

  Widget buildLimitDropdown({
    required String title,
    required dynamic value,
    required void Function(DropdownItemList?)? onChanged,
    required List<DropdownMenuItem<DropdownItemList>> items,
    required String errorText,
    required DropdownButtonBuilder selectedItemBuilder,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(flex: 4, child: QualityTitleField(title: title)),
          SizedBox(width: 10.w),
          Expanded(
            flex: 6,
            child: Column(children: <Widget>[
              Container(
                // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<DropdownItemList>(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                    isExpanded: true,
                    isDense: true,
                    itemHeight: null,
                    value: value ?? QualityControlDetailFunction.defaultValueLimitList,
                    iconSize: 15.sp,
                    style: const TextStyle(color: Colors.white),
                    onChanged: onChanged, // dropdown will disabled if this is null
                    items: items,
                    selectedItemBuilder: selectedItemBuilder,
                  ),
                ),
              ),
              SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
              QualityErrorValidate(text: errorText, error: _errorTestMethodDetail)
            ]),
          )
        ],
      ),
    );
  }

  Color determineColor() {
    if (_qualityControl != null && _qualityControl!.qcType == "NVL") {
      if (_qualityControl!.qualityChecker != null) {
        return Colors.grey.shade400;
      } else {
        return const Color(0xff0052cc);
      }
    } else {
      return const Color(0xff0052cc);
    }
  }

  Widget _buildTabContent() {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.white,
        appBar: AppBar(
            titleSpacing: 0,
            automaticallyImplyLeading: false,
            backgroundColor: const Color(0xff0052cc),
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            title: TitleQuality(title: title)),
        bottomNavigationBar: SafeArea(
          child: TabBar(
            onTap: (_) => FocusManager.instance.primaryFocus?.unfocus(),
            unselectedLabelColor: Colors.black,
            labelColor: const Color(0xff0052cc),
            labelStyle: TextStyle(fontSize: 13.sp),
            indicatorColor: const Color(0xff0052cc),
            tabs: renderTabs,
          ),
        ),
        body: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: TabBarView(
            physics: const NeverScrollableScrollPhysics(),
            children: <Widget>[
              // 1. Phiếu KT
              SingleChildScrollView(
                padding: EdgeInsets.only(bottom: _bottomPadding),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 10.h),

                      // Add loại mẫu
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Loại mẫu',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    isExpanded: true,
                                    isDense: true,
                                    itemHeight: null,
                                    value: _selectedLoaiMau,
                                    iconSize: 15.sp,
                                    style: const TextStyle(color: Colors.white),
                                    onChanged: canSubmit() ? _getSelectedLoaiMau : null,
                                    items: _lsLoaiMauOptions.map((Map<String, String> option) {
                                      return DropdownMenuItem<String>(
                                        value: option['value'],
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(vertical: 5.h),
                                          child: Text(
                                            option['label']!,
                                            style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    selectedItemBuilder: (BuildContext context) {
                                      return _lsLoaiMauOptions.map<Widget>((Map<String, String> option) {
                                        return Text(
                                          option['label']!,
                                          style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                          overflow: TextOverflow.ellipsis,
                                        );
                                      }).toList();
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Nhà máy field
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Nhà máy',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<SalesOrgCodes>(
                                    isExpanded: true,
                                    isDense: true,
                                    itemHeight: null,
                                    value: _selectedNhaMay,
                                    iconSize: 15.sp,
                                    style: const TextStyle(color: Colors.white),
                                    onChanged: canSubmit() ? _onNhaMayChanged : null,
                                    items: _lsNhaMayOptions?.map((SalesOrgCodes company) {
                                      return DropdownMenuItem<SalesOrgCodes>(
                                        value: company,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(vertical: 5.h),
                                          child: Text(
                                            company.storeName!,
                                            style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    selectedItemBuilder: (BuildContext context) {
                                      return _lsNhaMayOptions?.map<Widget>((SalesOrgCodes company) {
                                            return Text(
                                              company.storeName!,
                                              style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                              overflow: TextOverflow.ellipsis,
                                            );
                                          }).toList() ??
                                          [];
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Phân xưởng field
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Phân xưởng',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 7,
                              child: AutoCompleteField(
                                label: '',
                                enabled: canSubmit(),
                                suggestions: _phanXuongSuggestions,
                                controller: _focusPhanXuong,
                                onChanged: (value) {
                                  debugPrint('Phân xưởng value changed: $value');
                                  // Check if the entered value matches any suggestion
                                  if (value.isEmpty) {
                                    setState(() {
                                      _selectedPhanXuong = null;
                                      // Clear quality control workshop data
                                      if (_qualityControl != null) {
                                        _qualityControl!.workShopCode = null;
                                        _qualityControl!.workShopName = null;
                                      }
                                    });
                                  } else if (_phanXuongSuggestions.contains(value)) {
                                    // If it matches a suggestion, update the selected workshop
                                    WorkShops? selectedWorkshop;
                                    if (_lsPhanXuongOptions != null) {
                                      try {
                                        selectedWorkshop = _lsPhanXuongOptions!.firstWhere(
                                          (workshop) => "${workshop.workShopCode} | ${workshop.workShopName}" == value,
                                        );
                                      } catch (e) {
                                        selectedWorkshop = null;
                                      }
                                    }
                                    setState(() {
                                      _selectedPhanXuong = selectedWorkshop;
                                      // Update quality control object with selected workshop
                                      if (_qualityControl != null && _selectedPhanXuong != null) {
                                        _qualityControl!.workShopCode = _selectedPhanXuong!.workShopCode;
                                        _qualityControl!.workShopName = _selectedPhanXuong!.workShopName;
                                      }
                                    });
                                  }
                                },
                                onSuggestionSelected: (suggestion) {
                                  debugPrint('Phân xưởng suggestion selected: $suggestion');
                                  setState(() {
                                    _focusPhanXuong.text = suggestion;
                                    // Find the matching workshop from the suggestions
                                    WorkShops? selectedWorkshop;
                                    if (_lsPhanXuongOptions != null) {
                                      try {
                                        selectedWorkshop = _lsPhanXuongOptions!.firstWhere(
                                          (workshop) => "${workshop.workShopCode} | ${workshop.workShopName}" == suggestion,
                                        );
                                      } catch (e) {
                                        selectedWorkshop = null;
                                      }
                                    }
                                    _selectedPhanXuong = selectedWorkshop;
                                    // Update quality control object with selected workshop
                                    if (_qualityControl != null && _selectedPhanXuong != null) {
                                      _qualityControl!.workShopCode = _selectedPhanXuong!.workShopCode;
                                      _qualityControl!.workShopName = _selectedPhanXuong!.workShopName;
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Nhà gia công
                      // Thời gian Confirm
                      // Khách hàng
                      // Nhà cung cấp
                      // PO
                      // LSX SAP
                      // Sản phẩm
                      // Số lượng lô hàng
                      // Tình trạng MT
                      // Loại NVL
                      // Màu hoàn thiện
                      InputQCMauHeader(
                        qualityControl: _qualityControl,
                        onQualityControlChanged: (updatedQualityControl) {
                          _qualityControl = updatedQualityControl;
                        },
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "Ngày kiểm tra:")),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: GestureDetector(
                                onTap: !canSubmit()
                                    ? null
                                    : () async {
                                        // _pickDateIOS(context);
                                        if (Platform.isAndroid) {
                                          final getDate = await QualityControlFunction.pickDate(context, _qualityDate);
                                          if (!mounted) return;
                                          _setDate(getDate);
                                        } else {
                                          final getDateIOS = await QualityControlFunction.pickDateIOS(context);
                                          if (!mounted) return;
                                          _setDate(getDateIOS);
                                        }
                                      },
                                child: TitleDateTimeQuality(date: _qualityDateString)),
                          )
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "NV kiểm tra:")),
                          SizedBox(width: 10.w),
                          Expanded(flex: 7, child: TitleStaff(selectedStaff: _qualityChckerInfo))
                        ],
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(flex: 3, child: FieldQuantity(field: "MSNV kiểm tra:")),
                          SizedBox(width: 10.w),
                          Expanded(flex: 7, child: TextBox(text: _qualityChckerInfo?.salesEmployeeCode ?? ''))
                        ],
                      ),
                      SizedBox(height: 10.h),
                      SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   crossAxisAlignment: CrossAxisAlignment.center,
                      //   children: <Widget>[
                      //     const Expanded(
                      //       flex: 3,
                      //       child: Align(
                      //         alignment: Alignment.centerLeft,
                      //         child: QualityTitleField(title: "SL kiểm tra"),
                      //       ),
                      //     ),
                      //     SizedBox(width: 10.w),
                      //     Expanded(
                      //       flex: 7,
                      //       child: Align(
                      //         alignment: Alignment.centerLeft,
                      //         child: Column(children: <Widget>[
                      //           Container(
                      //             padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                      //             decoration: BoxDecoration(
                      //                 border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                      //             child: TextFormField(
                      //               enabled: checkQualityControl(),
                      //               maxLines: null,
                      //               textAlign: TextAlign.center,
                      //               // ÍnpectionLotQuantiy (Master)
                      //               controller: _controller_2,
                      //               style: TextStyle(fontSize: 12.sp),
                      //               keyboardType: TextInputType.number,
                      //               inputFormatters: <TextInputFormatter>[
                      //                 FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                      //               ],
                      //               decoration: InputDecoration(
                      //                 border: InputBorder.none,
                      //                 isDense: true,
                      //                 contentPadding: EdgeInsets.zero,
                      //                 errorBorder: InputBorder.none,
                      //                 disabledBorder: InputBorder.none,
                      //                 filled: true,
                      //                 fillColor: Colors.white,
                      //                 hintStyle: TextStyle(fontSize: 12.sp),
                      //               ),
                      //               onChanged: (value) {
                      //                 _checkErrorQuantityCheckerQuantityView();
                      //               },
                      //             ),
                      //           ),
                      //           Visibility(
                      //             visible: _errorQuantityCheck,
                      //             child: SizedBox(height: 10.h),
                      //           ),
                      //           QualityErrorValidate(error: _errorQuantityCheck, text: "Vui lòng nhập SL kiểm tra"),
                      //         ]),
                      //       ),
                      //     )
                      //   ],
                      // ),
                      // SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const Expanded(
                            flex: 3,
                            child: Align(alignment: Alignment.centerLeft, child: QualityTitleField(title: "Hình ảnh")),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                              flex: 7,
                              child: _lsFileHeader.isEmpty
                                  ? Row(
                                      children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.r),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: InkWell(
                                            onTap: !canSubmit()
                                                ? null
                                                : () async {
                                                    final check = await QualityControlFunction.pickImage(context);
                                                    debugPrint(check.toString());
                                                    if (check != null) {
                                                      bool checkPermission = await ImageFunction.handlePermission(check);
                                                      if (checkPermission == true) {
                                                        if (check == true) {
                                                          List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                            maxWidth: globalImageConfig.maxWidth,
                                                            maxHeight: globalImageConfig.maxHeight,
                                                            imageQuality: globalImageConfig.imageQuality,
                                                          );
                                                          if (selectedImages.isEmpty) return;
                                                          for (var i in selectedImages) {
                                                            final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                            if (!mounted) return;
                                                            _pickFileImage(itemImage);
                                                          }
                                                        } else {
                                                          final image = await ImagePicker().pickImage(
                                                              maxWidth: globalImageConfig.maxWidth,
                                                              maxHeight: globalImageConfig.maxHeight,
                                                              imageQuality: globalImageConfig.imageQuality,
                                                              source: ImageSource.camera);
                                                          if (image == null) return;
                                                          final imageProfile = await ImageFunction.saveImage(image.path);
                                                          if (!mounted) return;
                                                          _pickFileImage(imageProfile);
                                                        }
                                                      }
                                                    }
                                                  },
                                            child: Text(
                                              "Chọn tệp",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10.w),
                                        Center(
                                          child: Text(
                                            "Chưa chọn tệp nào",
                                            style: TextStyle(fontSize: 11.sp),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                                      Container(
                                        padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10.r),
                                          color: Colors.grey.shade100,
                                        ),
                                        child: InkWell(
                                          onTap: () async {
                                            // debugPrint('yes');
                                            final check = await QualityControlFunction.pickImage(context);
                                            debugPrint(check.toString());
                                            if (check != null) {
                                              bool checkPermission = await ImageFunction.handlePermission(check);
                                              if (checkPermission == true) {
                                                if (check == true) {
                                                  List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                    maxWidth: globalImageConfig.maxWidth,
                                                    maxHeight: globalImageConfig.maxHeight,
                                                    imageQuality: globalImageConfig.imageQuality,
                                                  );
                                                  if (selectedImages.isEmpty) return;
                                                  for (var i in selectedImages) {
                                                    final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                    _pickFileImage(itemImage);
                                                  }
                                                } else {
                                                  final image = await ImagePicker().pickImage(
                                                      maxWidth: globalImageConfig.maxWidth,
                                                      maxHeight: globalImageConfig.maxHeight,
                                                      imageQuality: globalImageConfig.imageQuality,
                                                      source: ImageSource.camera);
                                                  if (image == null) return;
                                                  final imageProfile = await ImageFunction.saveImage(image.path);
                                                  _pickFileImage(imageProfile);
                                                }
                                              }
                                            }
                                          },
                                          child: Text(
                                            "Chọn tệp",
                                            style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 10.h),
                                      ListChooseImage(lsFileTabCheck: _lsFileHeader, deleteListImageTabCheck: _deleteListImageTabCheck),
                                      SizedBox(height: 10.h),
                                    ])),
                        ],
                      ),
                      Visibility(
                        visible: _qualityControl!.fileViewModel == null || _qualityControl!.fileViewModel!.isEmpty ? false : true,
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: ElevatedButton.icon(
                            icon: Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 15.sp,
                            ),
                            style: ButtonStyle(
                              side: MaterialStateProperty.all(
                                const BorderSide(
                                  color: Color(0xff0052cc),
                                ),
                              ),
                              backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                            ),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return DialogImage(title: 'HÌNH ẢNH KIỂM TRA', listImage: _qualityControl!.fileViewModel);
                                },
                              );
                            },
                            label: Text(
                              'Hình ảnh',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 11.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 100.h),
                    ],
                  ),
                ),
              ),
              // 2. Mẫu CT
              SingleChildScrollView(
                padding: EdgeInsets.only(bottom: _bottomPadding),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 10.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.h),
                              decoration: const BoxDecoration(
                                color: Color(0xff0052cc),
                              ),
                              child: Text(
                                "Mẫu thử chi tiết",
                                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
                              ),
                            ),
                            Column(
                              children: [
                                SizedBox(height: 10.h),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      const Expanded(flex: 4, child: QualityTitleField(title: "Lần kiểm tra")),
                                      SizedBox(width: 10.w),
                                      Expanded(
                                        flex: 6,
                                        child: Column(children: <Widget>[
                                          Container(
                                            decoration: BoxDecoration(
                                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                borderRadius: BorderRadius.circular(3.r)),
                                            child: TextFormField(
                                              enabled: canSubmit(),
                                              maxLines: null,
                                              textAlign: TextAlign.center,
                                              controller: _controllerLanKiemTra,
                                              style: TextStyle(fontSize: 12.sp),
                                              keyboardType: TextInputType.number,
                                              inputFormatters: <TextInputFormatter>[
                                                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                              ],
                                              decoration: InputDecoration(
                                                border: InputBorder.none,
                                                isDense: true,
                                                contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                                errorBorder: InputBorder.none,
                                                disabledBorder: InputBorder.none,
                                                filled: true,
                                                fillColor: Colors.white,
                                                // hintStyle: TextStyle(fontSize: 12.sp),
                                                // hintText: "Nhập lần kiểm tra",
                                              ),
                                            ),
                                          ),
                                        ]),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      const Expanded(flex: 4, child: QualityTitleField(title: "Phương pháp lấy mẫu")),
                                      SizedBox(width: 10.w),
                                      Expanded(
                                        flex: 6,
                                        child: Column(children: <Widget>[
                                          Container(
                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                            decoration: BoxDecoration(
                                              border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                              borderRadius: BorderRadius.circular(3.r),
                                            ),
                                            child: DropdownButtonHideUnderline(
                                              child: DropdownButton<TestMethodList>(
                                                isExpanded: true,
                                                isDense: true,
                                                itemHeight: null,
                                                value: _selectedTestMethod ?? QualityControlDetailFunction.defaultValueTestMethodList,
                                                iconSize: 15.sp,
                                                style: const TextStyle(color: Colors.white),
                                                onChanged: !canSubmit() ? null : _getSelectedMethod,
                                                items: _lsTestMethodList.map((TestMethodList method) {
                                                  return DropdownMenuItem<TestMethodList>(
                                                      value: method,
                                                      child: Padding(
                                                        padding: EdgeInsets.symmetric(vertical: 5.h),
                                                        child: Text(
                                                          method.catalogTextVi.toString(),
                                                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                        ),
                                                      ));
                                                }).toList(),
                                                selectedItemBuilder: (BuildContext context) {
                                                  return _lsTestMethodList.map<Widget>((TestMethodList method) {
                                                    return Text(method.catalogTextVi.toString(),
                                                        style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                                  }).toList();
                                                },
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
                                          QualityErrorValidate(text: "Vui lòng chọn phương pháp lấy mẫu", error: _errorTestMethodDetail)
                                        ]),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                // buildSamplingLevelListDropdown(
                                //   title: "Mức độ lấy mẫu KT",
                                //   onChanged: !canSubmit()
                                //       ? null
                                //       : (SamplingLevelList? value) {
                                //           _getSelectedLevel(value);
                                //         },
                                //   items: _lsSamplingLevelList.map((SamplingLevelList level) {
                                //     return DropdownMenuItem<SamplingLevelList>(
                                //         value: level,
                                //         child: Padding(
                                //           padding: EdgeInsets.symmetric(vertical: 5.h),
                                //           child: Text(
                                //             level.catalogTextVi.toString(),
                                //             style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                //           ),
                                //         ));
                                //   }).toList(),
                                //   selectedItemBuilder: (BuildContext context) {
                                //     return _lsSamplingLevelList.map<Widget>((SamplingLevelList level) {
                                //       return Text(level.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                //     }).toList();
                                //   },
                                //   error: QualityErrorValidate(text: "Vui lòng chọn mức độ lấy mẫu KT", error: _errorLevelDetail),
                                // ),
                                // SizedBox(height: 5.h),
                                // buildLimitDropdown(
                                //   title: "Mức giới hạn lỗi nghiêm trọng",
                                //   value: _selectedLimitCritical ?? QualityControlDetailFunction.defaultValueLimitList,
                                //   onChanged: !canSubmit() ? null : _getSelectedLimitCritical,
                                //   items: _lsLimitCriticalList.map((DropdownItemList method) {
                                //     return DropdownMenuItem<DropdownItemList>(
                                //       value: method,
                                //       child: Padding(
                                //         padding: EdgeInsets.symmetric(vertical: 5.h),
                                //         child: Text(
                                //           method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                //         ),
                                //       ),
                                //     );
                                //   }).toList(),
                                //   errorText: "Vui lòng chọn mức giới hạn lỗi nghiêm trọng",
                                //   selectedItemBuilder: (BuildContext context) {
                                //     return _lsLimitCriticalList.map<Widget>((DropdownItemList method) {
                                //       return Text(method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                //     }).toList();
                                //   },
                                // ),
                                // SizedBox(height: 5.h),
                                // buildLimitDropdown(
                                //   title: "Mức giới hạn lỗi nặng",
                                //   value: _selectedLimitHigh ?? QualityControlDetailFunction.defaultValueLimitList,
                                //   onChanged: !canSubmit() ? null : _getSelectedLimitHigh,
                                //   items: _lsLimitHighList.map((DropdownItemList method) {
                                //     return DropdownMenuItem<DropdownItemList>(
                                //       value: method,
                                //       child: Padding(
                                //         padding: EdgeInsets.symmetric(vertical: 5.h),
                                //         child: Text(
                                //           method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                //         ),
                                //       ),
                                //     );
                                //   }).toList(),
                                //   errorText: "Vui lòng chọn mức giới hạn lỗi nặng",
                                //   selectedItemBuilder: (BuildContext context) {
                                //     return _lsLimitHighList.map<Widget>((DropdownItemList method) {
                                //       return Text(method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                //     }).toList();
                                //   },
                                // ),
                                // SizedBox(height: 5.h),
                                // buildLimitDropdown(
                                //   title: "Mức giới hạn lỗi nhẹ",
                                //   value: _selectedLimitLow ?? QualityControlDetailFunction.defaultValueLimitList,
                                //   onChanged: !canSubmit() ? null : _getSelectedLimitLow,
                                //   items: _lsLimitLowList.map((DropdownItemList method) {
                                //     return DropdownMenuItem<DropdownItemList>(
                                //       value: method,
                                //       child: Padding(
                                //         padding: EdgeInsets.symmetric(vertical: 5.h),
                                //         child: Text(
                                //           method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                //         ),
                                //       ),
                                //     );
                                //   }).toList(),
                                //   errorText: "Vui lòng chọn mức giới hạn lỗi nhẹ",
                                //   selectedItemBuilder: (BuildContext context) {
                                //     return _lsLimitLowList.map<Widget>((DropdownItemList method) {
                                //       return Text(method.catalogTextVi.toString(),
                                //           style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                //     }).toList();
                                //   },
                                // ),

                                // SizedBox(height: 5.h),
                                // formGroup(
                                //   title: "Mức chấp nhận",
                                //   enabled: checkQualityControl(),
                                //   controller: _controllerMucChapNhan,
                                //   onChanged: (value) {
                                //     _checkErrorAcceptableLevelDetail();
                                //   },
                                //   errorText: "Vui lòng nhập mức chấp nhận",
                                //   error: _errorAcceptableLevelDetail,
                                // ),
                                // SizedBox(height: 5.h),

                                // Padding(
                                //   padding: EdgeInsets.symmetric(horizontal: 15.w),
                                //   child: Align(
                                //     alignment: Alignment.centerRight,
                                //     child: ElevatedButton(
                                //       style: ElevatedButton.styleFrom(
                                //         backgroundColor: determineColor(),
                                //       ),
                                //       // onPressed: _controllerInspectLotQuantity.text.isEmpty ||
                                //       //         (_selectedLevel == null || _selectedLevel!.catalogCode == " ") ||
                                //       //         _loadingGetQuanititySample == true
                                //       //     ? null
                                //       //     : _qualityControl!.qcType == "NVL"
                                //       //         ? _qualityControl!.qualityChecker != null
                                //       //             ? null
                                //       //             : () {
                                //       //                 _getGetQuanititySample(context);
                                //       //               }
                                //       //         : () => _getGetQuanititySample(context),
                                //       onPressed: (_qualityControl!.qcType != "NVL" || _qualityControl!.qualityChecker == null) && canSubmit()
                                //           ? () => _getGetQuanititySample(context)
                                //           : null,
                                //       child: Text(
                                //         _loadingGetQuanititySample == true ? "Loading..." : 'Lấy SL kiểm tra',
                                //         style: TextStyle(
                                //           fontSize: 12.sp,
                                //           color: Colors.white,
                                //           fontWeight: FontWeight.bold,
                                //         ),
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                // SizedBox(height: 5.h),
                                formGroup(
                                  // InspectQuantity (Detail)
                                  title: "SL kiểm tra",
                                  enabled: checkQualityControl(),
                                  controller: _controllerInspectQuantityDetail,
                                  onChanged: (value) {
                                    _checkErrorQuantityCheckDetail();
                                  },
                                  errorText: "Vui lòng nhập SL kiểm tra",
                                  error: _errorQuantityCheckDetail,
                                  hintText: "Nhập SL kiểm tra",
                                ),

                                SizedBox(height: 5.h),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      const Expanded(flex: 4, child: FieldQuantity(field: "Tổng số lượng SP lỗi")),
                                      SizedBox(width: 10.w),
                                      Expanded(
                                          flex: 6,
                                          child: Container(
                                            // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                            decoration: BoxDecoration(
                                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                borderRadius: BorderRadius.circular(3.r)),
                                            child: TextFormField(
                                              keyboardType: TextInputType.number,
                                              maxLines: 1,
                                              controller: _controllerTongSoSanPhamLoi,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(fontSize: 12.sp),
                                              decoration: InputDecoration(
                                                border: InputBorder.none,
                                                isDense: true,
                                                contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                                errorBorder: OutlineInputBorder(
                                                  borderSide: BorderSide(color: Colors.red, width: 1.0),
                                                ),
                                                filled: true,
                                                fillColor: Colors.white,
                                                hintText: 'Nhập số lượng SP lỗi',
                                                hintStyle: TextStyle(fontSize: 12.sp, color: Colors.grey),
                                              ),
                                            ),
                                          ))
                                    ],
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: <Widget>[
                                      const Expanded(flex: 4, child: FieldQuantity(field: "Tổng số lượng Lỗi")),
                                      SizedBox(width: 10.w),
                                      Expanded(
                                          flex: 6,
                                          child: Container(
                                            // padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                            decoration: BoxDecoration(
                                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                borderRadius: BorderRadius.circular(3.r)),
                                            child: TextFormField(
                                              enabled: false,
                                              // readOnly: true,
                                              maxLines: null,
                                              controller: _controllerTongSoLuongLoi,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(fontSize: 12.sp),
                                              decoration: InputDecoration(
                                                border: InputBorder.none,
                                                isDense: true,
                                                contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                                                errorBorder: InputBorder.none,
                                                disabledBorder: InputBorder.none,
                                                filled: true,
                                                fillColor: Colors.white,
                                                hintStyle: TextStyle(fontSize: 12.sp),
                                              ),
                                            ),
                                          ))
                                    ],
                                  ),
                                ),
                                // SizedBox(height: 5.h),
                                // formGroup(
                                //   title: "SL lỗi nặng chấp nhận",
                                //   enabled: false,
                                //   controller: _controllerSLLoiNangChapNhan,
                                //   onChanged: (value) {
                                //     _checkErrorQuantityCheckDetail();
                                //   },
                                //   errorText: "",
                                //   error: false,
                                // ),
                                // SizedBox(height: 5.h),
                                // formGroup(
                                //   title: "SL lỗi nhẹ chấp nhận",
                                //   enabled: false,
                                //   controller: _controllerSLLoiNheChapNhan,
                                //   onChanged: null,
                                //   errorText: "",
                                //   error: false,
                                // ),
                              ],
                            ),
                            SizedBox(height: 10.h),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                    ],
                  ),
                ),
              ),
              // 3. T.Tin KT
              SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 200),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 10.h),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                          color: Colors.grey.shade300,
                        ),
                        child: Column(
                          children: [
                            Column(
                              children: <Widget>[
                                const QualityTitle(title: "Thông tin kiểm tra"), // Title
                                Column(
                                  children: List.generate(
                                    _lsThongTinKiemTra.length,
                                    (index) => Container(
                                      color: Colors.white,
                                      margin: EdgeInsets.symmetric(vertical: 4.h),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 1.w),
                                        child: Column(
                                          children: [
                                            // itemHeader(),
                                            // ItemThongTinKiemTra(index, context),
                                            itemHeaderThongTinKiemTra(),
                                            ItemThongTinKiemTraMau(
                                              index: index,
                                              qualityControl: _qualityControl,
                                              lsGetFileInfor: _lsFileThongTinKiemTra,
                                              lsThongTinKiemTra: _lsThongTinKiemTra,
                                              lsThongTinKiemTraSelectMasterData: _lsThongTinKiemTraSelectMasterData,
                                              checkVisiButtonInformation: _checkVisiButtonThongTinKiemTra,
                                              pickFileImageInformation: _pickFileImageInformation,
                                              deleteListQualityInformation: _deleteListQualityInformation,
                                              deleteItemListInformation: _deleteItemListInformation,
                                              lsControllerThongTinKiemTra: _lsControllerThongTinKiemTra,
                                              lsControllerSoSanPhamLoi: _lsControllerSoSanPhamLoi,
                                              lsControllerGhiChu: _lsControllerGhiChu,
                                              checkClearLsSelectedInfo: _checkClearLsSelectedInfo,
                                              pickerImage: _pickerImage,
                                              lsErrorInfor: _lsErrorInfor,
                                              checkErrorSelectedInfo: _checkErrorSelectedInfo,
                                              updateTongSoSanPhamLoi: _updateTongSoSanPhamLoi,
                                              onRadioChanged: _onRadioChanged,
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: IntrinsicHeight(
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                      children: <Widget>[
                                        Expanded(
                                          flex: 6,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                            ),
                                            child: const Text(""),
                                          ),
                                        ),
                                        canSubmit()
                                            ? Expanded(
                                                flex: 4,
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(vertical: 5.h),
                                                  height: 40.h,
                                                  decoration: BoxDecoration(
                                                    border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                  ),
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                                                    child: ElevatedButton.icon(
                                                      icon: Icon(
                                                        Icons.add,
                                                        color: Colors.white,
                                                        size: 15.sp,
                                                      ),
                                                      style: ButtonStyle(
                                                        padding: MaterialStateProperty.all<EdgeInsets>(
                                                            EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w)),
                                                        side: MaterialStateProperty.all(
                                                          const BorderSide(
                                                            color: Colors.green,
                                                          ),
                                                        ),
                                                        backgroundColor: MaterialStateProperty.all(Colors.green),
                                                      ),
                                                      onPressed: !canSubmit()
                                                          ? null
                                                          : () {
                                                              _addNewThongTinKiemTra();
                                                            },
                                                      label: Text(
                                                        'Thêm',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontWeight: FontWeight.bold,
                                                          fontSize: 10.sp,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            : Container()
                                      ],
                                    ),
                                  ),
                                ),
                                // SizedBox(height: 10.h),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                    ],
                  ),
                ),
              ),
              // 4.T.Tin Lỗi
              SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 200),
                child: SafeArea(
                  minimum: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 10.h),
                      Container(
                        color: const Color.fromRGBO(0, 0, 0, 0.1),
                        // decoration: BoxDecoration(
                        //   border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                        // ),
                        constraints: BoxConstraints(minHeight: 100.h),
                        child: Column(
                          children: <Widget>[
                            const QualityTitle(title: 'Thông tin lỗi'),
                            SizedBox(height: 10.h),
                            Column(
                                children: List.generate(
                              _lsError.length,
                              (index) {
                                return Container(
                                  color: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 0.h),
                                  margin: EdgeInsets.symmetric(vertical: 5.h),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(horizontal: 0.w),
                                    child: IntrinsicHeight(
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                        children: <Widget>[
                                          Expanded(
                                            flex: 1,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                                              ),
                                              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                                              child: Center(
                                                child: Text(
                                                  (index + 1).toString(),
                                                  style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 12,
                                            child: IntrinsicHeight(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                                children: <Widget>[
                                                  buildDanhSachLoiHeader(),
                                                  buildDanhSachLoi(index),
                                                  buildMucDoLoiHeader(),
                                                  buildMucDoLoi(index),
                                                  buildImagePickerList(index),
                                                  // Phương án xử lý
                                                  // buidPhuongAnXuLy(index),
                                                  // Vị trí gây lỗi
                                                  // buildViTriGayLoiHeader(),
                                                  // Column(
                                                  //   children: buildViTriGayLoiList(index),
                                                  // ),
                                                  // Cá nhân gây lỗi
                                                  buildCaNhanGayLoiHeader(),
                                                  Column(
                                                    children: buildCaNhanGayLoiList(index),
                                                  ),

                                                  // Delete button
                                                  buildDeleteButton(index),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )),
                            canSubmit() ? ButtonAddNewCardError(addNewCardError: _addNewCardError, qualityControl: _qualityControl) : Container(),
                            SizedBox(height: 10.h),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            const Expanded(flex: 4, child: QualityTitleField(title: "Kết quả")),
                            SizedBox(width: 10.w),
                            Expanded(
                              flex: 6,
                              child: Column(children: <Widget>[
                                DropdownDetailResult(
                                  getSelectedResultDetail: canSubmit() ? _getSelectedResultDetail : (_) {},
                                  selectedResultDetail: _selectedResultDetail,
                                  lsResultList: _lsResultList,
                                  qualityControl: _qualityControl,
                                ),
                                SizedBox(height: _errorResultCheckDetail == true ? 10.h : 0),
                                QualityErrorValidate(text: "Vui lòng chọn kết quả", error: _errorResultCheckDetail)
                              ]),
                            )
                          ],
                        ),
                      ),
                      SizedBox(height: 10.h),
                      GestureDetector(
                        // onTap: () {
                        //   debugPrint(_qualityControl?.toJson().toString());
                        // },

                        onTap: !canSubmit()
                            ? null
                            : () {
                                submitData();
                              },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 15.h),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5.r),
                            ),
                            color: !canSubmit() ? Colors.grey.shade400 : const Color(0xff0052cc),
                          ),
                          child: Center(
                            child: Text(
                              'Submit',
                              style: TextStyle(color: Colors.white, fontSize: 12.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10.h),
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }

  List<Widget> get renderTabs {
    return const <Widget>[
      Tab(
        icon: Icon(Icons.edit_note_rounded),
        text: 'Phiếu KT',
      ),
      Tab(
        icon: Icon(Icons.details_rounded),
        text: 'Mẫu CT',
      ),
      Tab(
        icon: Icon(Icons.list),
        text: "T.Tin KT",
      ),
      Tab(
        icon: Icon(Icons.error_outline_outlined),
        text: "T.Tin Lỗi",
      ),
    ];
  }

  IntrinsicHeight buildDanhSachLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text(
                "Danh sách lỗi",
                style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                ),
                child: Text(
                  "Số lượng lỗi",
                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                )),
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildDanhSachLoi(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // Danh sách lỗi
          Expanded(
            flex: 5,
            child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 0.5,
                    color: Colors.grey.shade300,
                  ),
                ),
                child: Container(
                  child: Padding(
                    padding: REdgeInsets.all(5),
                    child: Center(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 0.5.w,
                            color: Colors.grey.shade400,
                          ),
                        ),
                        child: TypeAheadField(
                          suggestionsBoxDecoration: SuggestionsBoxDecoration(
                            constraints: BoxConstraints(
                              minWidth: 150.w,
                            ),
                          ),
                          textFieldConfiguration: TextFieldConfiguration(
                              enabled: checkQualityControl(),
                              decoration: InputDecoration(
                                labelStyle: TextStyle(fontSize: 11.sp),
                                contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                isDense: true,
                                border: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                enabledBorder: InputBorder.none,
                              ),
                              controller: _lsControllerError[index],
                              // focusNode: focusError[index],
                              style: TextStyle(fontSize: 12.sp),
                              onChanged: (value) {
                                _onDanhSachLoiChanged(index);
                              }),
                          suggestionsCallback: (pattern) {
                            return QualityControlFunction.filterQualityControlErrorList(_lsErrorList ?? [], pattern);
                          },
                          itemBuilder: (context, suggestion) {
                            return ListTile(
                              title: Text((suggestion as ErrorList).catalogTextVi ?? " ", style: TextStyle(fontSize: 12.sp)),
                            );
                          },
                          onSuggestionSelected: (suggestion) {
                            _setTypeAhead(TypeAheadErrorQuatity(index: index, errorList: suggestion));
                          },
                          noItemsFoundBuilder: (value) {
                            return Padding(
                                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                          },
                        ),
                      ),
                    ),
                  ),
                )),
          ),
          // Số lượng lỗi
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Padding(
                padding: REdgeInsets.all(5),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                  ),
                  child: TextFormField(
                    enabled: checkQualityControl(),
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    ],
                    controller: _lsControllerSoLuongLoi[index],
                    style: TextStyle(fontSize: 12.sp),
                    onChanged: (value) {
                      _onSoLuongLoiChanged(index, value);
                    },
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      filled: true,
                      isDense: true,
                      fillColor: Colors.white,
                      hintStyle: TextStyle(fontSize: 12.sp),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  IntrinsicHeight buildMucDoLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text(
                "Mức độ lỗi",
                style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                ),
                child: Text(
                  "Ghi chú",
                  style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
                )),
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildMucDoLoi(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          // Mức độ lỗi
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Column(
                children: <Widget>[
                  Padding(
                    padding: REdgeInsets.all(5),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DataGetDefectLevel>(
                          isExpanded: true,
                          isDense: true,
                          itemHeight: null,
                          value: _lsSelectedMucDoLoi[index],
                          iconSize: 15.sp,
                          style: const TextStyle(color: Colors.white),
                          onChanged: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                              ? null
                              : (DataGetDefectLevel? value) {
                                  _getSelectedDefectLevel(DropdownDefetchLevel(index: index, value: value));
                                },
                          items: _lsDataGetDefetchLevel.map((DataGetDefectLevel result) {
                            return DropdownMenuItem<DataGetDefectLevel>(
                              value: result,
                              child: Text(
                                result.value.toString(),
                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                              ),
                            );
                          }).toList(),
                          selectedItemBuilder: (BuildContext context) {
                            return _lsDataGetDefetchLevel.map(
                              (DataGetDefectLevel result) {
                                return Text(
                                  result.value.toString(),
                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                  overflow: TextOverflow.ellipsis,
                                );
                              },
                            ).toList();
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Ghi chú
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: REdgeInsets.all(5),
                    child: Container(
                      // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                      ),
                      child: TextFormField(
                        enabled: checkQualityControl(),
                        maxLines: null,
                        textAlign: TextAlign.center,
                        controller: _lsControllerGhiChuLoi[index],
                        style: TextStyle(fontSize: 12.sp),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          filled: true,
                          isDense: true,
                          fillColor: Colors.white,
                          hintStyle: TextStyle(fontSize: 12.sp),
                          // contentPadding: EdgeInsets.zero,
                          contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> caNhanGayLoiTitleList = [
    "1. Quản đốc",
    "2. Tổ trưởng/Kỹ thuật",
    "3. QA-QC",
    "4. KCS (Gây lỗi)",
    "5. Cá nhân gây lỗi",
  ];

  List<String> viTriGayLoiTitleList = [
    "Công đoạn lỗi",
    "Nhà máy",
    "Phân xưởng",
    "Tổ chuyền",
  ];

  Widget customExpandedWidget(int flexValue, Widget childWidget, {bool showBorder = true}) {
    return Expanded(
      flex: flexValue,
      child: Container(
        constraints: const BoxConstraints(minHeight: inputHeight),
        decoration: const BoxDecoration(
            // border: Border.all(width: 0.5, color: Colors.grey.shade300),
            ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 5.w),
          child: Container(
            decoration: showBorder
                ? BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                  )
                : null,
            child: Align(
              alignment: Alignment.centerLeft,
              child: childWidget,
            ),
          ),
        ),
      ),
    );
  }

  IntrinsicHeight buildCaNhanGayLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text("Cá nhân gây lỗi", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold)),
            ),
          ),
          // Expanded(
          //   flex: 6,
          //   child: Container(
          //       padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
          //       decoration: BoxDecoration(
          //         border: Border.all(width: 0.5, color: Colors.grey.shade300),
          //       ),
          //       child: Text("Ghi chú", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold))),
          // ),
        ],
      ),
    );
  }

  IntrinsicHeight buildViTriGayLoiHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(
                  width: 0.5,
                  color: Colors.grey.shade300,
                ),
              ),
              child: Text("Vị trí gây lỗi", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold)),
            ),
          ),
          // Expanded(
          //   flex: 6,
          //   child: Container(
          //       padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
          //       decoration: BoxDecoration(
          //         border: Border.all(width: 0.5, color: Colors.grey.shade300),
          //       ),
          //       child: Text("Ghi chú", style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold))),
          // ),
        ],
      ),
    );
  }

  TextEditingController _getControllerForCaNhanIndex(int caNhanIndex, int errorIndex) {
    switch (caNhanIndex) {
      case 0:
        return _lsControllerCaNhanLoi1QuanDoc[errorIndex];
      case 1:
        return _lsControllerCaNhanLoi2ToTruong[errorIndex];
      case 2:
        return _lsControllerCaNhanLoi3QAQC[errorIndex];
      case 3:
        return _lsControllerCaNhanLoi4KCS[errorIndex];
      default:
        throw Exception('Invalid caNhanIndex: $caNhanIndex');
    }
  }

  IntrinsicHeight buildCaNhanGayLoiItem(int caNhanIndex, int errorIndex) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // Ca nhan gay loi - Quan doc
          customExpandedWidget(3, Text(caNhanGayLoiTitleList[caNhanIndex], style: TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
            6,
            CaNhanGayLoiParentTypeAhead(
              enabled: checkQualityControl(),
              showRemoveButton: false,
              masterDataList: _lsCaNhanGayLoiAllMasterData ?? [],
              onSuggestionSelected: (selectedItem) {
                // _setTypeAheadCaNhanGayLoiMany(
                //   TypeAheadCaNhanGayLoi(
                //     index: errorIndex,
                //     selectedItem: selectedItem,
                //     caNhanLoiIndex: caNhanLoiIndex,
                //   ),
                // );
                debugPrint(caNhanIndex.toString() + " " + errorIndex.toString() + " " + selectedItem.catalogTextVi.toString());
                _getControllerForCaNhanIndex(caNhanIndex, errorIndex).text = selectedItem.catalogTextVi!;
              },
              // _getControllerForCaNhanIndex(caNhanIndex, errorIndex)
              controller: _getControllerForCaNhanIndex(caNhanIndex, errorIndex), // Assuming you have a matching controller for each widget
              onChanged: (value) {
                // _clearCaNhanGayLoiIndexMany(caNhanLoiIndex, errorIndex)
                debugPrint(caNhanIndex.toString() + " " + errorIndex.toString() + " " + value.toString());
              },
            ),
            showBorder: false,
          ),
        ],
      ),
    );
  }

  IntrinsicHeight buildCaNhanGayLoiItemMany(int i, int errorIndex) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // Ca nhan gay loi - Quan doc
          customExpandedWidget(3, Text(caNhanGayLoiTitleList[i], style: const TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
            6,
            Column(
              children: <Widget>[
                ...List.generate(
                  _lsControllerCaNhanGayLoiMany[errorIndex].length,
                  (caNhanLoiIndex) => CaNhanGayLoiTypeAhead(
                    enabled: checkQualityControl(),
                    showRemoveButton: canSubmit(),
                    masterDataList: _getCaNhanGayLoiMaster(),
                    onSuggestionSelected: (selectedItem) {
                      debugPrint("Selected item: $selectedItem");
                      debugPrint("Error index: $errorIndex");
                      debugPrint("Ca nhan loi index: $caNhanLoiIndex");
                      _setTypeAheadCaNhanGayLoiMany(
                        TypeAheadCaNhanGayLoi(
                          index: errorIndex,
                          selectedItem: selectedItem,
                          caNhanLoiIndex: caNhanLoiIndex,
                        ),
                      );
                    },
                    controller: _lsControllerCaNhanGayLoiMany[errorIndex][caNhanLoiIndex], // Assuming you have a matching controller for each widget
                    onChanged: (value) => _clearCaNhanGayLoiIndexMany(caNhanLoiIndex, errorIndex),
                    caNhanLoiIndex: caNhanLoiIndex,
                    errorIndex: errorIndex, // errorIndex
                    onAddCaNhanLoi: () => _addCaNhanGayLoiMany(errorIndex),
                    onRemoveCaNhanLoi: () => _removeCaNhanGayLoiMany(errorIndex, caNhanLoiIndex),
                  ),
                )
              ],
            ),
            showBorder: false,
          ),
          // Expanded(
          //   // Existing or use Flexible for the red container
          //   flex: 1,
          //   child: Container(
          //     constraints: const BoxConstraints(minHeight: inputHeight),
          //     // color: Colors.red,
          //     child: addCaNhanGayLoiMany(errorIndex),
          //   ),
          // )
        ],
      ),
    );
  }

  _addCaNhanGayLoiMany(int errorIndex) {
    if (_lsControllerCaNhanGayLoiMany[errorIndex].length >= 10) {
      return;
    }
    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex].add(TextEditingController());
      _lsSelectedCaNhanGayLoiMany[errorIndex].add(null);
    });
  }

  _removeCaNhanGayLoiMany(int errorIndex, int caNhanLoiIndex) {
    setState(() {
      _lsControllerCaNhanGayLoiMany[errorIndex].removeAt(caNhanLoiIndex);
      _lsSelectedCaNhanGayLoiMany[errorIndex].removeAt(caNhanLoiIndex);
    });
  }

  List<Widget> buildCaNhanGayLoiList(index) {
    var isMany = true;

    List<Widget> listUICaNhanGayLoi; // = List<Widget>.generate(caNhanGayLoiTitleList.length, (i) => buildCaNhanGayLoiItem(i, index));

    if (isMany) {
      listUICaNhanGayLoi = [
        buildCaNhanGayLoiItem(0, index), // Quản đốc
        buildCaNhanGayLoiItem(1, index), // Tổ trưởng/KT
        buildCaNhanGayLoiItem(2, index), // QAQC
        buildCaNhanGayLoiItem(3, index), // KCS
        buildCaNhanGayLoiItemMany(4, index), // Cá nhân gây lỗi lít 10
      ];
    }

    return listUICaNhanGayLoi;
  }

  IntrinsicHeight buildImagePickerList(int index) {
    return IntrinsicHeight(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: Colors.grey.shade300),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              flex: 8,
              child: _lsFileHinhAnhLoi[index].isEmpty
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        GestureDetector(
                          onTap: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                              ? null
                              : () async {
                                  final check = await QualityControlFunction.pickImage(context);
                                  debugPrint(check.toString());
                                  if (check != null) {
                                    bool checkPermission = await ImageFunction.handlePermission(check);
                                    if (checkPermission == true) {
                                      if (check == true) {
                                        List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                          maxWidth: globalImageConfig.maxWidth,
                                          maxHeight: globalImageConfig.maxHeight,
                                          imageQuality: globalImageConfig.imageQuality,
                                        );
                                        if (selectedImages.isEmpty) return;
                                        for (var i in selectedImages) {
                                          final itemImage = await ImageFunction.saveImageMulti(i.path);
                                          _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: itemImage));
                                        }
                                      } else {
                                        final image = await ImagePicker().pickImage(
                                            maxWidth: globalImageConfig.maxWidth,
                                            maxHeight: globalImageConfig.maxHeight,
                                            imageQuality: globalImageConfig.imageQuality,
                                            source: ImageSource.camera);
                                        if (image == null) return;
                                        final imageProfile = await ImageFunction.saveImage(image.path);
                                        _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: imageProfile));
                                      }
                                    }
                                  }
                                },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: Colors.grey.shade100,
                            ),
                            child: Center(
                              child: Text(
                                "Chọn tệp",
                                style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Center(
                          child: Text(
                            "Chưa chọn tệp nào",
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        ),
                      ],
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            final check = await QualityControlFunction.pickImage(context);
                            debugPrint(check.toString());
                            if (check != null) {
                              bool checkPermission = await ImageFunction.handlePermission(check);
                              if (checkPermission == true) {
                                if (check == true) {
                                  List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                    maxWidth: globalImageConfig.maxWidth,
                                    maxHeight: globalImageConfig.maxHeight,
                                    imageQuality: globalImageConfig.imageQuality,
                                  );
                                  if (selectedImages.isEmpty) return;
                                  for (var i in selectedImages) {
                                    final itemImage = await ImageFunction.saveImageMulti(i.path);
                                    _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: itemImage));
                                  }
                                } else {
                                  final image = await ImagePicker().pickImage(
                                      maxWidth: globalImageConfig.maxWidth,
                                      maxHeight: globalImageConfig.maxHeight,
                                      imageQuality: globalImageConfig.imageQuality,
                                      source: ImageSource.camera);
                                  if (image == null) return;
                                  final imageProfile = await ImageFunction.saveImage(image.path);
                                  _pickFileImageErrorQuality(MultiSelectImageErrorQuality(index: index, file: imageProfile));
                                }
                              }
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: Colors.grey.shade100,
                            ),
                            child: Text(
                              "Chọn tệp",
                              style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Wrap(
                          spacing: 3.w,
                          runSpacing: 3.h,
                          children: List.generate(
                            _lsFileHinhAnhLoi[index].length,
                            (indexImageError) {
                              // String filenameError = basename(
                              //     lsGetFileError[index]
                              //             [indexImageError]
                              //         .path);
                              return SizedBox(
                                width: 50.w,
                                child: Stack(
                                  children: <Widget>[
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => ImageQuatity(lsImage: _lsFileHinhAnhLoi[index], index: indexImageError),
                                          ),
                                        );
                                      },
                                      child: ListImagePicker(fileImage: _lsFileHinhAnhLoi[index][indexImageError]),
                                    ),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: IconButton(
                                        highlightColor: Colors.transparent,
                                        hoverColor: Colors.transparent,
                                        constraints: const BoxConstraints(),
                                        iconSize: 17.sp,
                                        color: Colors.red.shade800,
                                        icon: const Icon(Icons.remove_circle),
                                        onPressed: () {
                                          _deleteListFileError(MultiDeleteImageErrorQuality(index: index, indexImageError: indexImageError));
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
            ),
            Visibility(
              visible: (_lsError[index].errorFileViewModel ?? []).isNotEmpty ? true : false,
              child: Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: () {
                    String title = "";
                    String error = _lsSelectedError[index] == null ? "" : _lsSelectedError[index]!.catalogTextVi.toString();
                    int indexErrorName = error.indexOf('|');
                    title = _lsSelectedError[index] == null
                        ? ""
                        : _lsSelectedError[index]!.catalogTextVi.toString().substring(indexErrorName + 2).toUpperCase();
                    // String error = lsSelectedError[index]!.catalogTextVi.toString();
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return DialogErrorQuality(title: 'HÌNH ẢNH LỖI ' + title, listImage: _lsError[index].errorFileViewModel ?? []);
                      },
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 5.h),
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    decoration: BoxDecoration(
                      color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.all(
                        Radius.circular(2.r),
                      ),
                    ),
                    child: Icon(
                      Icons.image,
                      color: Colors.white,
                      size: 15.sp,
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  IntrinsicHeight buidPhuongAnXuLy(int index) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          customExpandedWidget(4, Text("Phương án xử lý", style: TextStyle(fontSize: 11)), showBorder: false),
          customExpandedWidget(
              6,
              DropdownButtonHideUnderline(
                child: DropdownButton<DropdownItemList>(
                  isExpanded: true,
                  isDense: true,
                  itemHeight: null,
                  value: _lsSelectedPhuongAnXuLy[index] ?? QualityControlDetailFunction.defaultValuePhuongAnXuLy,
                  iconSize: 15.sp,
                  padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                  onChanged: (value) {
                    _onPhuongAnXuLyChanged(value, index);
                  },
                  items: _lsPhuongAnXuLyMasterData.map((DropdownItemList method) {
                    return DropdownMenuItem<DropdownItemList>(
                        value: method,
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 5.h),
                          child: Text(
                            method.catalogTextVi.toString(),
                            style: TextStyle(color: Colors.black, fontSize: 10.sp),
                          ),
                        ));
                  }).toList(),
                  selectedItemBuilder: (BuildContext context) {
                    return _lsPhuongAnXuLyMasterData.map<Widget>((DropdownItemList method) {
                      return Text(
                        method.catalogTextVi.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 10.sp),
                        overflow: TextOverflow.ellipsis,
                      );
                    }).toList();
                  },
                ),
              )),
        ],
      ),
    );
  }

  Visibility buildDeleteButton(int index) {
    return Visibility(
      visible: _checkVisiButtonError[index],
      child: IntrinsicHeight(
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: Colors.grey.shade300),
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              constraints: const BoxConstraints(),
              iconSize: 17.sp,
              color: Colors.red.shade800,
              icon: const Icon(Icons.delete),
              onPressed: () {
                _deleteItemListError(index);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget itemHeaderText(int flexNum, String text) {
    return Expanded(
      flex: flexNum,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
        ),
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 2.w),
        child: Text(
          text,
          style: TextStyle(fontSize: 9.sp, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Container itemHeaderThongTinKiemTra() {
    return Container(
      color: Colors.white,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            itemHeaderText(1, "STT"),
            itemHeaderText(6, "Hạng mục kiểm tra"),
            itemHeaderText(4, "Ghi chú"),
          ],
        ),
      ),
    );
  }

  List<DropdownItemList> _getCaNhanGayLoiMaster() {
    return _lsCaNhanGayLoiMasterData;
  }

  Future<void> _loadNhaMayAndPhanXuongData(String? companyCode) async {
    try {
      // Load Nhà máy (similar to company loading in FilterListDowntime)
      final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);

      if (!mounted) return;

      if (companyList != null) {
        setState(() {
          _lsNhaMayOptions = companyList.map((company) => SalesOrgCodes(saleOrgCode: company.companyCode, storeName: company.companyName)).toList();

          if (_lsNhaMayOptions!.isNotEmpty) {
            _selectedNhaMay = _lsNhaMayOptions!.firstWhere(
              (element) => element.saleOrgCode == (companyCode ?? widget.user.companyCode),
              orElse: () => _lsNhaMayOptions!.first,
            );
          }
        });
      }

      // Load Phân xưởng (similar to departments loading in FilterListDowntime)
      await _loadPhanXuongData();
    } catch (error) {
      debugPrint('Error loading Nhà máy and Phân xưởng data: $error');
    }
  }

  Future<void> _loadPhanXuongData() async {
    if (_selectedNhaMay?.saleOrgCode == null) return;

    try {
      // Use the new fetchWorkshop function for fetching workshops
      final workshopsResponse = await DowntimeFunction.fetchWorkshop(
        widget.user.token.toString(),
        _selectedNhaMay!.saleOrgCode!,
      );

      if (!mounted) return;

      if (workshopsResponse?.workshops != null) {
        setState(() {
          _lsPhanXuongOptions = workshopsResponse!.workshops!
              .where((workshop) => workshop.workShopCode != null && workshop.workShopName != null)
              .map((workshop) => WorkShops(
                    workShopCode: workshop.workShopCode,
                    workShopName: workshop.workShopName,
                  ))
              .toList();

          // Clear previous selection
          _selectedPhanXuong = null;
          _focusPhanXuong.clear();
          _phanXuongSuggestions.clear();
          _phanXuongSuggestions.addAll(_lsPhanXuongOptions!.map((workshop) => "${workshop.workShopCode} | ${workshop.workShopName}").toList());

          // Set existing workshop data if available
          if (_qualityControl?.workShopCode != null) {
            try {
              _selectedPhanXuong = _lsPhanXuongOptions!.firstWhere(
                (workshop) => workshop.workShopCode == _qualityControl!.workShopCode,
              );
              if (_selectedPhanXuong != null) {
                _focusPhanXuong.text = "${_selectedPhanXuong!.workShopCode} | ${_selectedPhanXuong!.workShopName}";
              }
            } catch (e) {
              debugPrint('Workshop not found in loaded options: ${_qualityControl!.workShopCode}');
            }
          }
        });
      }
    } catch (error) {
      debugPrint('Error loading Phân xưởng data: $error');
    }
  }

  void _onNhaMayChanged(SalesOrgCodes? selectedNhaMay) {
    setState(() {
      _selectedNhaMay = selectedNhaMay;

      // Clear existing Phân xưởng selection
      _selectedPhanXuong = null;
      _lsPhanXuongOptions = null;
      _focusPhanXuong.clear();
      _phanXuongSuggestions.clear();
    });

    // Load new Phân xưởng data for the selected Nhà máy
    _loadPhanXuongData();
  }

  void _onPhanXuongChanged(WorkShops? selectedPhanXuong) {
    setState(() {
      _selectedPhanXuong = selectedPhanXuong;
      if (_selectedPhanXuong != null) {
        _focusPhanXuong.text = "${_selectedPhanXuong!.workShopCode} | ${_selectedPhanXuong!.workShopName}";
      } else {
        _focusPhanXuong.clear();
      }
    });
  }

  void _checkErrorQuantityCheckDetail() {
    if (_controllerInspectQuantityDetail.text.isNotEmpty) {
      if (_errorQuantityCheckDetail != false) {
        setState(() {
          _errorQuantityCheckDetail = false;
        });
      }
    } else {
      if (_errorQuantityCheckDetail != true) {
        setState(() {
          _errorQuantityCheckDetail = true;
        });
      }
    }
  }

  bool _checkQualityControlValidation() {
    bool ret = true;

    if (_errorQuantityCheck) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorSelectedResultQualityView) {
      ret = false;
    }

    if (_errorTestMethodDetail) {
      showToast(context: context, message: "Vui lòng chọn phương pháp lấy mẫu");
      ret = false;
    }

    if (_errorQuantityCheckDetail) {
      showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
      ret = false;
    }

    if (_errorResultCheckDetail) {
      ret = false;
    }

    if (_lsErrorInfor.where((element) => element == true).isNotEmpty) {
      showToast(context: context, message: "Vui lòng chọn hạng mục kiểm tra");
      ret = false;
    }

    return ret;
  }
}

class _ErrorView extends StatelessWidget {
  const _ErrorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)));
  }
}

class _ListNotFoundView extends StatelessWidget {
  const _ListNotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text("Không tìm thấy thông tin phiếu kiểm tra!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
  }
}
