FUNCTION zmes_fm_trans_nkmh_qc_bg.
*"----------------------------------------------------------------------
*"*"Local Interface:
*"  IMPORTING
*"     VALUE(I_MBLNR) TYPE  MBLNR
*"     VALUE(I_MJAHR) TYPE  MJAHR
*"     VALUE(I_WERKS) TYPE  WERKS_D
*"     VALUE(I_SAMPLE) TYPE  ZST_MES_INF_QC_SAMPLE OPTIONAL
*"     VALUE(I_QSTICHVERF) TYPE  QSTICHVERF
*"     VALUE(I_MATNR) TYPE  MATNR OPTIONAL
*"     VALUE(I_RESULT) TYPE  CHAR10
*"     VALUE(I_QTY_POSTING) TYPE  RQEVA
*"     VALUE(I_VORNR) TYPE  VORNR DEFAULT 0010
*"     VALUE(I_BLDAT) TYPE  BLDAT
*"     VALUE(I_BUDAT) TYPE  BUDAT
*"     VALUE(I_BREAK) TYPE  CHAR1 OPTIONAL
*"     VALUE(I_JSON) TYPE  STRING OPTIONAL
*"  EXPORTING
*"     VALUE(E_RETURN) TYPE  BAPIRETURN
*"  TABLES
*"      T_CHAR STRUCTURE  ZST_MES_INF_QC_CHARAC OPTIONAL
*"      T_DEF STRUCTURE  ZST_MES_INF_QC_DEFECT OPTIONAL
*"      T_RETURN STRUCTURE  BAPIRET2
*"----------------------------------------------------------------------
  DATA: lv_destination TYPE rfcdest.
  DATA: gv_guid TYPE guid.
  DATA: lv_len    TYPE i,
        lv_json   TYPE string,
        lv_offset TYPE i VALUE 0.
  DATA: ls_json_in TYPE ztb_mes_qc_in.

* save json data
  IF i_json IS NOT INITIAL.
    lv_json = i_json.
    lv_len = strlen( lv_json ).
    IF lv_len > 3672.
      lv_len = 3672.
    ENDIF.
    ls_json_in-mblnr = i_mblnr.

    WHILE lv_offset < lv_len.
      IF lv_len > 0.
        ls_json_in-part1 = lv_json+0(1024).
      ENDIF.
      IF lv_len > 1024.
        ls_json_in-part2 = lv_json+1024(1024).
      ENDIF.
      IF lv_len > 2048.
        ls_json_in-part3 = lv_json+2048(1024).
      ENDIF.
      IF lv_len > 3072.
        ls_json_in-part4 = lv_json+3072(600).
      ENDIF.
    ENDWHILE.
    IF ls_json_in IS NOT INITIAL.
      MODIFY ztb_mes_qc_in FROM ls_json_in.
    ENDIF.
  ENDIF.

* create guid value
  gv_guid = cl_system_uuid=>create_uuid_x16_static( ).
  lv_destination = |MES_{ gv_guid }|.

  SELECT SINGLE *
    FROM zxx_config
    WHERE prog = 'ZMES_QC_NKMH' AND
    rout = 'GET_CONFIG' AND
    fiel = 'FUNCTION_N'
    INTO @DATA(ls_config).
  IF sy-subrc NE 0.
* call function module in background task.
    CALL FUNCTION 'ZMES_FM_TRANS_NKMH_QC'
      IN BACKGROUND TASK
      DESTINATION lv_destination
      EXPORTING
        i_mblnr       = i_mblnr
        i_mjahr       = i_mjahr
        i_werks       = i_werks
        i_sample      = i_sample
        i_qstichverf  = i_qstichverf
        i_matnr       = i_matnr
        i_result      = i_result
        i_qty_posting = i_qty_posting
        i_vornr       = i_vornr
        i_bldat       = i_bldat
        i_budat       = i_budat
        i_guid        = gv_guid
*       I_BREAK       =
      IMPORTING
        e_return      = e_return
      TABLES
        t_char        = t_char
        t_def         = t_def
        t_return      = t_return.
    COMMIT WORK.
  ELSE.
    CALL FUNCTION 'ZMES_FM_TRANS_NKMH_QC_V2'
      IN BACKGROUND TASK
      DESTINATION lv_destination
      EXPORTING
        i_mblnr       = i_mblnr
        i_mjahr       = i_mjahr
        i_werks       = i_werks
        i_sample      = i_sample
        i_qstichverf  = i_qstichverf
        i_matnr       = i_matnr
        i_result      = i_result
        i_qty_posting = i_qty_posting
        i_vornr       = i_vornr
        i_bldat       = i_bldat
        i_budat       = i_budat
        i_guid        = gv_guid
*       I_BREAK       =
      IMPORTING
        e_return      = e_return
      TABLES
        t_char        = t_char
        t_def         = t_def
        t_return      = t_return.
    COMMIT WORK.
  ENDIF.

  e_return-type = 'S'.
  e_return-message_v1 = gv_guid.
  e_return-message_v2 = lv_destination.

ENDFUNCTION.