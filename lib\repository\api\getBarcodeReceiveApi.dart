import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetBarcodeReceiveApi {
  static Future<http.Response> getBarcodeReceive(String rawMaterialCardId, String token) async {
    Map<String, dynamic> data = {"RawMaterialCardId": rawMaterialCardId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/${UrlApi.baseUrlWarehousTransaction_2}GetBarcodeReceive?RawMaterialCardId=${data['RawMaterialCardId']}';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
