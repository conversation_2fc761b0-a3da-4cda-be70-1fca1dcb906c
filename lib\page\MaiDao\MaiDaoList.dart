import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/page/MaiDao/element/FilterListMaiDao.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import 'package:ttf/element/GenericQRScanner.dart';
import 'package:ttf/utils/ui_helpers.dart';
import 'package:ttf/utils/user_helper.dart';
import 'package:ttf/Storage/storageSharedPreferences.dart';
import '../../model/userModel.dart';
import '../../model/maiDaoModel.dart';
import '../../repository/function/maiDaoFunction.dart';
import '../LostConnect.dart';

class MaiDaoList extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;
  final bool useDummyData;

  const MaiDaoList({
    Key? key,
    required this.dateTimeOld,
    required this.user,
    this.useDummyData = false,
    // this.useDummyData = true,
  }) : super(key: key);

  @override
  _MaiDaoListState createState() => _MaiDaoListState();

  // Helper method to create a demo version of this widget
  static MaiDaoList demo(DataUser user) {
    return MaiDaoList(
      dateTimeOld: DateFormat('yyyy-MM-dd').format(DateTime.now()),
      user: user,
      useDummyData: true,
    );
  }
}

class _MaiDaoListState extends State<MaiDaoList> {
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  List<MaiDaoRecord>? _records;
  ConnectivityResult _result = ConnectivityResult.none;
  MaiDaoSearchModel? _searchModel;
  CommonDateModel? _commonDateModel;
  List<CommonDates>? _commonDates;

  // Dummy data
  List<MaiDaoRecord> _dummyRecords = [
    MaiDaoRecord(
      id: "dummy1",
      date: "25/04/2025",
      equipmentCode: "MH001",
      equipmentName: "Máy mài dao CNC",
      materialCode: "VT001",
      materialName: "Dao phay kim loại",
      operationType: "Mài dao",
      employeeCodes: "NV001,NV002",
      employeeNames: "Nguyễn Văn A, Trần Thị B",
      requestingEmployeeCode: "NV010",
      requestingEmployeeName: "Phạm Văn Quản",
      note: "Mài dao định kỳ theo quy trình",
      status: "Created",
      companyCode: "1000",
      createdDate: "2025-05-25T08:30:00",
    ),
    MaiDaoRecord(
      id: "dummy2",
      date: "26/04/2025",
      equipmentCode: "MH002",
      equipmentName: "Máy đắp mũi khoan",
      materialCode: "VT002",
      materialName: "Mũi khoan hợp kim",
      operationType: "Đắp dao",
      employeeCodes: "NV003",
      employeeNames: "Lê Văn C",
      requestingEmployeeCode: "NV011",
      requestingEmployeeName: "Đỗ Thị Lan",
      note: "Đắp dao sau sửa chữa",
      status: "Confirmed",
      companyCode: "1200",
      createdDate: "2025-05-26T10:15:00",
    ),
    MaiDaoRecord(
      id: "dummy3",
      date: "27/04/2025",
      equipmentCode: "MH003",
      equipmentName: "Máy mài lưỡi cắt",
      materialCode: "VT003",
      materialName: "Lưỡi cắt nhôm",
      operationType: "Mài dao",
      employeeCodes: "NV001,NV004",
      employeeNames: "Nguyễn Văn A, Phạm Thị D",
      requestingEmployeeCode: "NV012",
      requestingEmployeeName: "Trần Minh Tú",
      note: "Mài lưỡi cắt theo yêu cầu sản xuất",
      status: "Completed",
      companyCode: "1000",
      createdDate: "2025-05-27T14:45:00",
    ),
    MaiDaoRecord(
      id: "dummy4",
      date: "28/04/2025",
      equipmentCode: "MH004",
      equipmentName: "Máy mài dao tiện",
      materialCode: "VT004",
      materialName: "Dao tiện thép gió",
      operationType: "Đắp dao",
      employeeCodes: "NV005,NV006",
      employeeNames: "Hoàng Văn E, Vũ Thị F",
      requestingEmployeeCode: "NV013",
      requestingEmployeeName: "Lưu Hoàng Nam",
      note: "Đắp dao tiện bị mòn",
      status: "Cancelled",
      companyCode: "1100",
      createdDate: "2025-05-28T09:20:00",
    ),
    MaiDaoRecord(
      id: "dummy5",
      date: "29/04/2025",
      equipmentCode: "MH005",
      equipmentName: "Máy mài dao phay",
      materialCode: "VT005",
      materialName: "Dao phay ngón carbide",
      operationType: "Mài dao",
      employeeCodes: "NV007",
      employeeNames: "Đặng Văn G",
      requestingEmployeeCode: "NV014",
      requestingEmployeeName: "Võ Thị Hoa",
      note: "Mài dao phay ngón theo kế hoạch bảo dưỡng",
      status: "Created",
      companyCode: "1000",
      createdDate: "2025-05-29T11:10:00",
    ),
    MaiDaoRecord(
      id: "dummy6",
      date: "30/04/2025",
      equipmentCode: "MH006",
      equipmentName: "Máy đắp dao khoan",
      materialCode: "VT006",
      materialName: "Mũi khoan xoắn HSS",
      operationType: "Đắp dao",
      employeeCodes: "NV008,NV009",
      employeeNames: "Bùi Thị H, Lý Văn I",
      requestingEmployeeCode: "NV015",
      requestingEmployeeName: "Ngô Văn Thành",
      note: "Đắp dao khoan bị gãy mũi",
      status: "Confirmed",
      companyCode: "1000",
      createdDate: "2025-05-30T15:30:00",
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// Get user information from storage and verify permissions
  Future<void> _getUserInfo() async {
    try {
      // Method 1: Using UserHelper (Recommended)
      UserModel? userModel = await UserHelper().getUserModel();
      DataUser? userData = await UserHelper().getUserData();

      if (userModel != null && userData != null) {
        debugPrint('User loaded from storage:');
        debugPrint('Username: ${userData.userName}');
        debugPrint('Company Code: ${userData.companyCode}');
        debugPrint('Full Name: ${userData.fullName}');
        debugPrint('Account ID: ${userData.accountId}');

        // Check if user has permission for MaiDao screen
        final hasMaiDaoPermission = userData.permission?.mobileScreenModel?.any((screen) => screen.screenCode == "MaiDao") ?? false;

        debugPrint('Has MaiDao Permission: $hasMaiDaoPermission');

        // You can also get specific user information directly
        String? companyCode = await UserHelper().getCompanyCode();
        String? fullName = await UserHelper().getUserFullName();
        String? userId = await UserHelper().getUserId();
        String? username = await UserHelper().getUsername();

        debugPrint('Company Code from UserHelper: $companyCode');
        debugPrint('Full Name from UserHelper: $fullName');

        // Check if routing is enabled for this company
        bool isRoutingEnabled = await UserHelper().isRoutingEnabledForCurrentCompany();
        debugPrint('Routing enabled for current company: $isRoutingEnabled');
      }

      // Method 2: Get session information from SharedPreferences
      String? sessionDateTime = StorageSharedPreferences.getString('datetimeNow');
      debugPrint('Session DateTime: $sessionDateTime');

      // Method 3: Verify the passed user data from widget
      debugPrint('Widget User Info:');
      debugPrint('Widget Username: ${widget.user.userName}');
      debugPrint('Widget Company: ${widget.user.companyCode}');
      debugPrint('Widget Token exists: ${widget.user.token != null}');
    } catch (e) {
      debugPrint('Error getting user info: $e');
    }
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      // Get user permission on the user info
      await _getUserInfo();

      // If using dummy data, set it and return
      if (widget.useDummyData) {
        await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
        setState(() {
          _records = _dummyRecords;
          _isLoading = false;
        });
        return;
      }

      // Initialize default search model if not exists
      if (_searchModel == null) {
        final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.user.token!);
        if (dataDropdown != null && dataDropdown.data?.commonDates != null) {
          _commonDates = dataDropdown.data?.commonDates;

          // Get default date range (ThisWeek)
          final defaultCommonDate = _commonDates?.firstWhere(
            (element) => element.catalogCode == "ThisWeek",
            orElse: () => _commonDates!.first,
          );

          if (defaultCommonDate != null && defaultCommonDate.catalogCode != null) {
            final commonDateModel = await ListQCFunction.getCommonDateModel(
              defaultCommonDate.catalogCode!,
              widget.user.token!,
            );

            if (commonDateModel != null) {
              _searchModel = MaiDaoSearchModel(
                companyCode: widget.user.companyCode ?? '',
                pageNumber: 1,
                pageSize: 20,
                fromDate: commonDateModel.fromDate != null ? DateTime.parse(commonDateModel.fromDate!) : null,
                toDate: commonDateModel.toDate != null ? DateTime.parse(commonDateModel.toDate!) : null,
              );
            }
          }
        }
      }

      // Fetch the actual data
      if (_searchModel != null) {
        try {
          final data = await MaiDaoFunction.fetchMaiDaoList(
            widget.user.token!,
            _searchModel!,
          );

          if (!mounted) return;

          setState(() {
            _records = data?.data;
          });
        } catch (e) {
          // If API call fails, use dummy data
          debugPrint("API call failed, using dummy data: $e");
          setState(() {
            _records = _dummyRecords;
          });
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      debugPrint("SocketException caught, using dummy data");
      setState(() {
        _records = _dummyRecords;
        _isNotWifi = false; // Don't show lost connection screen, just use dummy data
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _records = _dummyRecords; // Use dummy data in case of errors
        _isError = false; // Don't show error screen
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadData() async {
    if (_isLoading) return; // Prevent multiple simultaneous refreshes

    // If using dummy data, just refresh the UI with dummy data
    if (widget.useDummyData) {
      setState(() {
        _isLoading = true;
      });
      await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
      setState(() {
        _records = _dummyRecords;
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      // Only fetch data if we have a valid search model
      if (_searchModel != null) {
        try {
          final data = await MaiDaoFunction.fetchMaiDaoList(
            widget.user.token!,
            _searchModel!,
          );

          if (!mounted) return;

          setState(() {
            _records = data?.data;
          });
        } catch (e) {
          debugPrint("API call failed, using dummy data: $e");
          setState(() {
            _records = _dummyRecords;
          });
        }
      }
    } on SocketException catch (_) {
      debugPrint("SocketException caught, using dummy data");
      if (!mounted) return;
      setState(() {
        _records = _dummyRecords;
        _isNotWifi = false; // Use dummy data instead of showing error
      });
    } catch (error) {
      debugPrint("Error in _loadData: $error");
      if (!mounted) return;
      setState(() {
        _records = _dummyRecords;
        _isError = false;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDateTime(String? dateTime) {
    if (dateTime == null) return '';
    try {
      final dt = DateTime.parse(dateTime);
      return DateFormat('dd/MM/yyyy HH:mm').format(dt);
    } catch (e) {
      return dateTime;
    }
  }

  String _formatEmployeeDisplay(String? employeeCodes, String? employeeNames) {
    if (employeeCodes == null || employeeNames == null) return '';

    final codes = employeeCodes.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
    final names = employeeNames.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();

    // Ensure we have matching pairs
    final minLength = codes.length < names.length ? codes.length : names.length;

    List<String> formattedEmployees = [];
    for (int i = 0; i < minLength; i++) {
      formattedEmployees.add('${codes[i]} | ${names[i]}');
    }

    return formattedEmployees.join(', ');
  }

  // DEBUG ONLY
  List<Map<String, String>> dummyEQCode = [
    {
      'label': 'EQ 10000803 Máy khoan',
      'qrcode':
          '<T1>66fd7a8a-727d-4367-a23a-023268616c7a</T1><T2>10000803</T2><T7>Máy khoan lỗ bản lề MZ4214B</T7><T8>PMTTFTCD</T8><T9>000211200692</T9><T10>1000-PX04-33</T10><T11>1022</T11><T12>1000C06</T12><T13>1000</T13>'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            hoverColor: Colors.transparent,
            icon: Icon(
              Icons.camera_alt,
              size: 19.sp,
              color: Colors.white,
            ),
            onPressed: _isLoading == true
                ? null
                : () async {
                    await _checkConnectNetwork();
                    if (!mounted) return;
                    if (_result != ConnectivityResult.none || widget.useDummyData) {
                      await _scanEquipmentQR();
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Tính năng cần có kết nối internet để sử dụng',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                      ));
                    }
                  },
          ),
          Builder(
            builder: (BuildContext context) {
              return IconButton(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                hoverColor: Colors.transparent,
                icon: Icon(
                  Icons.search_outlined,
                  size: 19.sp,
                  color: Colors.white,
                ),
                onPressed: _isLoading == true
                    ? null
                    : () async {
                        await _checkConnectNetwork();
                        if (!mounted) return;
                        if (_result != ConnectivityResult.none) {
                          Scaffold.of(context).openEndDrawer();
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            backgroundColor: Colors.black,
                            content: Text(
                              'Tính năng cần có kết nối internet để sử dụng',
                              style: TextStyle(fontSize: 15.sp, color: Colors.white),
                            ),
                          ));
                        }
                      },
              );
            },
          ),
        ],
        title: Text(
          'Mài dao cụ mũi lưỡi',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      endDrawer: _isLoading == true
          ? null
          : FilterListMaiDao(
              searchModel: _searchModel,
              token: widget.user.token!,
              user: widget.user,
              onFilterSelected: (MaiDaoSearchModel filter) {
                setState(() {
                  _searchModel = filter;
                });
                _loadData(); // Reload data with new filters
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.pushNamed(context, '/MaiDaoDetail', arguments: {
            'id': '',
            'dateTimeOld': widget.dateTimeOld,
            'user': widget.user,
          });
          if (result == true) {
            // reload data
            _loadData();
          }
        },
        backgroundColor: const Color(0xff4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: _buildBody(),
    );
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  Future<void> _scanEquipmentQR() async {
    // Skip for dummy data or when loading
    if (widget.useDummyData || _isLoading) {
      showToast(
        context: context,
        message: 'Tính năng quét QR không khả dụng trong chế độ demo',
      );
      return;
    }

    try {
      FocusScope.of(context).unfocus();
      // await Future<void>.delayed(const Duration(milliseconds: 500));

      if (!mounted) return;

      // Navigate to QR scan page and wait for result
      final scannedData = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GenericQRScanner(
            title: 'Quét mã thiết bị',
            subtitle: 'Hướng camera vào mã QR của thiết bị',
            onDataScanned: (scannedData) async {
              // Just return the scanned data, validation will be done later
              return scannedData;
            },
          ),
        ),
      );

      if (scannedData == null) return;
      if (!mounted) return;

      await _handleScannedEquipment(scannedData as String);
    } catch (e) {
      debugPrint('Error scanning barcode: $e');
      showToast(
        context: context,
        message: 'Có lỗi xảy ra khi quét mã vạch',
      );
    }
  }

  Future<void> _handleScannedEquipment(String scannedData) async {
    // Validate QR code format first
    if (!scannedData.contains('<T2>') || !scannedData.contains('<T7>')) {
      showToast(
        context: context,
        message: 'QR code thiết bị không hợp lệ',
      );
      return;
    }

    // Parse XML-like QR code structure
    String? equipmentCode;
    String? equipmentName;

    // Extract equipment code from T2 tag
    final codeRegex = RegExp(r'<T2>(.*?)</T2>');
    final codeMatch = codeRegex.firstMatch(scannedData);
    if (codeMatch != null) {
      equipmentCode = codeMatch.group(1);
    }

    // Extract equipment name from T7 tag
    final nameRegex = RegExp(r'<T7>(.*?)</T7>');
    final nameMatch = nameRegex.firstMatch(scannedData);
    if (nameMatch != null) {
      equipmentName = nameMatch.group(1);
    }

    // Validate that we found both code and name
    if (equipmentCode == null || equipmentCode.isEmpty) {
      showToast(
        context: context,
        message: 'Không tìm thấy mã thiết bị trong QR code',
      );
      return;
    }

    if (equipmentName == null || equipmentName.isEmpty) {
      showToast(
        context: context,
        message: 'Không tìm thấy tên thiết bị trong QR code',
      );
      return;
    }

    // Check for existing incomplete record first
    await _checkExistingRecordAndNavigate(equipmentCode, equipmentName);
  }

  Future<void> _checkExistingRecordAndNavigate(String equipmentCode, String equipmentName) async {
    try {
      // Show loading indicator while checking for existing records
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog(
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16.w),
                Text('Đang kiểm tra bản ghi...'),
              ],
            ),
          ),
        ),
      );

      // Call API to check for existing MaiDao record for this equipment
      final latestRecord = await MaiDaoFunction.checkExistingRecord(
        widget.user.token!,
        equipmentCode,
        widget.user.companyCode ?? '',
      );

      // Close loading dialog
      if (mounted) {
        Navigator.pop(context);
      }

      if (latestRecord != null && mounted) {
        // Go to detail page instead of asking what to do
        await _navigateToExistingRecord(latestRecord);
        return;

        // Found existing record - show dialog asking what to do
        final action = await showDialog<String>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Tìm thấy bản ghi'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Thiết bị này có bản ghi gần nhất:'),
                SizedBox(height: 8.h),
                Text('• Trạng thái: ${MaiDaoFunction.getStatusText(latestRecord.status ?? 'Created')}'),
                Text('• Ngày: ${latestRecord.date ?? ''}'),
                if (latestRecord.operationType != null) Text('• Nghiệp vụ: ${latestRecord.operationType}'),
                SizedBox(height: 12.h),
                Text('Bạn muốn làm gì?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, 'create_new'),
                child: Text('Tạo mới'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, 'view_existing'),
                child: Text('Xem bản ghi'),
              ),
            ],
          ),
        );

        if (action == 'view_existing') {
          // Navigate to existing record
          await _navigateToExistingRecord(latestRecord);
          return;
        } else if (action == 'create_new') {
          // Create new record with equipment pre-filled
          await _navigateToNewRecord(equipmentCode, equipmentName);
          return;
        }
        // If dialog was dismissed, do nothing
        return;
      }

      // No existing record found - navigate to new record with equipment pre-filled
      await _navigateToNewRecord(equipmentCode, equipmentName);
    } catch (e) {
      // Close loading dialog if it's still open
      if (mounted) {
        Navigator.pop(context);
      }

      debugPrint('Error checking existing record: $e');

      if (mounted) {
        showToast(
          context: context,
          message: 'Không thể kiểm tra bản ghi. Tạo mới với thiết bị đã quét.',
        );

        // Continue with new record creation if API check fails
        await _navigateToNewRecord(equipmentCode, equipmentName);
      }
    }
  }

  Future<void> _navigateToExistingRecord(MaiDaoRecord record) async {
    final result = await Navigator.pushNamed(
      context,
      '/MaiDaoDetail',
      arguments: {
        'id': record.id ?? '',
        'dateTimeOld': widget.dateTimeOld,
        'user': widget.user,
        'maiDaoRecord': record,
      },
    );

    if (result == true) {
      _loadData(); // Reload the list
    }

    if (mounted) {
      // showToast(
      //   context: context,
      //   message: 'Đã mở bản ghi hiện có của thiết bị',
      // );
    }
  }

  Future<void> _navigateToNewRecord(String equipmentCode, String equipmentName) async {
    // Create a partial record with the scanned equipment info
    final scannedRecord = MaiDaoRecord(
      id: '', // Empty ID indicates new record
      equipmentCode: equipmentCode,
      equipmentName: equipmentName,
      companyCode: widget.user.companyCode,
      date: DateFormat('dd/MM/yyyy').format(DateTime.now()),
      status: 'Created',
    );

    final result = await Navigator.pushNamed(
      context,
      '/MaiDaoDetail',
      arguments: {
        'id': '',
        'dateTimeOld': widget.dateTimeOld,
        'user': widget.user,
        'maiDaoRecord': scannedRecord,
      },
    );

    if (result == true) {
      _loadData(); // Reload the list
    }

    showToast(
      context: context,
      message: 'Đã quét thiết bị: $equipmentName',
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    if (_records == null || _records!.isEmpty) {
      return RefreshIndicator(
        onRefresh: _loadData,
        child: Column(
          children: [
            kDebugMode
                ? RenderDebugButtons(dummyEQCode, (item) {
                    _handleScannedEquipment(item['qrcode']!);
                  }, 'label')
                : Container(),
            Expanded(
              child: ListView(
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height / 3),
                  Center(
                    child: Text(
                      'Không có dữ liệu',
                      style: TextStyle(fontSize: 15.sp),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: Column(
        children: [
          kDebugMode
              ? RenderDebugButtons(dummyEQCode, (item) {
                  _handleScannedEquipment(item['qrcode']!);
                }, 'label')
              : Container(),
          Expanded(
            child: ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(), // Enable scrolling even when content is small
              itemCount: _records!.length,
              itemBuilder: (context, index) {
                final record = _records![index];
                return Card(
                  elevation: 2,
                  margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(12.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Colors.black,
                                        ),
                                        children: [
                                          const TextSpan(text: 'Thiết bị: '),
                                          TextSpan(
                                            text: '${record.equipmentCode} | ${record.equipmentName}',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Colors.black,
                                        ),
                                        children: [
                                          const TextSpan(text: 'Vật tư: '),
                                          TextSpan(
                                            text: record.materialCode != null && record.materialName != null
                                                ? '${record.materialCode} | ${record.materialName}'
                                                : record.materialCode ?? record.materialName ?? '',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Colors.black,
                                        ),
                                        children: [
                                          const TextSpan(text: 'Nghiệp vụ: '),
                                          TextSpan(
                                            text: record.operationType ?? '',
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  color: MaiDaoFunction.getStatusColor(record.status ?? 'Created'),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  MaiDaoFunction.getStatusText(record.status ?? 'Created'),
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Divider(height: 16.h),
                          Row(
                            children: [
                              Icon(Icons.date_range, size: 16.sp, color: Colors.grey[600]),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Text(
                                  'Ngày: ${record.date ?? ''}',
                                  style: TextStyle(fontSize: 12.sp),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4.h),
                          if (record.employeeNames?.isNotEmpty == true) ...[
                            Row(
                              children: [
                                Icon(Icons.person, size: 16.sp, color: Colors.grey[600]),
                                SizedBox(width: 4.w),
                                Expanded(
                                  child: Text(
                                    // Code | Name, Code | Name
                                    'NV mài: ${_formatEmployeeDisplay(record.employeeCodes, record.employeeNames)}',
                                    style: TextStyle(fontSize: 12.sp),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 4.h),
                          ],
                          if (record.requestingEmployeeName?.isNotEmpty == true) ...[
                            Row(
                              children: [
                                Icon(Icons.person_outline, size: 16.sp, color: Colors.grey[600]),
                                SizedBox(width: 4.w),
                                Expanded(
                                  child: Text(
                                    'NV yêu cầu: ${record.requestingEmployeeCode} | ${record.requestingEmployeeName}',
                                    style: TextStyle(fontSize: 12.sp),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 4.h),
                          ],
                          if (record.note?.isNotEmpty == true) ...[
                            Row(
                              children: [
                                Icon(Icons.note, size: 16.sp, color: Colors.grey[600]),
                                SizedBox(width: 4.w),
                                Expanded(
                                  child: Text(
                                    'Ghi chú: ${record.note}',
                                    style: TextStyle(fontSize: 12.sp),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 4.h),
                          ],
                          Row(
                            children: [
                              Icon(Icons.factory, size: 16.sp, color: Colors.grey[600]),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Text(
                                  'Nhà máy: ${record.companyCode ?? ''}',
                                  style: TextStyle(fontSize: 12.sp),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Tạo lúc: ${_formatDateTime(record.createdDate)}',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Colors.grey[500],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ).onTap(() async {
                  // Check if it's a dummy record by ID prefix
                  final isDummyRecord = record.id?.startsWith('dummy') ?? false;
                  debugPrint("Tapped record ID: ${record.id}, isDummyRecord: $isDummyRecord");

                  if (isDummyRecord) {
                    debugPrint("Navigating to dummy record: ${record.id}");

                    // Pass the dummy record to the detail screen
                    final result = await Navigator.pushNamed(
                      context,
                      '/MaiDaoDetail',
                      arguments: {
                        'id': record.id ?? '',
                        'dateTimeOld': widget.dateTimeOld,
                        'user': widget.user,
                        'maiDaoRecord': record,
                      },
                    );

                    if (result == true) {
                      _loadData();
                    }
                  } else {
                    debugPrint("Navigating to real record: ${record.id}");

                    // Real data navigation
                    final result = await Navigator.pushNamed(
                      context,
                      '/MaiDaoDetail',
                      arguments: {
                        'id': record.id ?? '',
                        'dateTimeOld': widget.dateTimeOld,
                        'user': widget.user,
                      },
                    );

                    if (result == true) {
                      _loadData();
                    }
                  }
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}

extension TapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }
}
