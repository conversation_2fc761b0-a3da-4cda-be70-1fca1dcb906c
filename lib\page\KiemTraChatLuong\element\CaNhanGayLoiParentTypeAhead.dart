import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:ttf/page/KiemTraChatLuong/model/TypeAheadCaNhanGayLoi.dart';
import 'package:ttf/repository/function/qualityControlFunction.dart';

class CaNhanGayLoiParentTypeAhead extends StatelessWidget {
  final bool enabled;
  final bool showRemoveButton;
  final List<DropdownItemList>? masterDataList;
  final Function(DropdownItemList) onSuggestionSelected;
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  // final int caNhanLoiIndex;
  // final int errorIndex;
  final Function? onAddCaNhanLoi;
  final Function? onRemoveCaNhanLoi;

  const Ca<PERSON>hanGayLoiParentTypeAhead({
    Key? key,
    required this.enabled,
    required this.showRemoveButton,
    required this.masterDataList,
    required this.onSuggestionSelected,
    required this.controller,
    required this.onChanged,
    // required this.caNhanLoiIndex,
    // required this.errorIndex,
    this.onAddCaNhanLoi,
    this.onRemoveCaNhanLoi,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(minHeight: inputHeight),
      margin: EdgeInsets.only(bottom: 0.h),
      child: Row(
        children: [
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade400),
              ),
              child: TypeAheadField(
                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                  constraints: BoxConstraints(
                    minWidth: 200.w,
                  ),
                ),
                textFieldConfiguration: TextFieldConfiguration(
                    enabled: enabled,
                    decoration: InputDecoration(
                      labelStyle: TextStyle(fontSize: 11.sp),
                      contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                      isDense: true,
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                    ),
                    controller: controller,
                    // focusNode: focusError[index],
                    style: TextStyle(fontSize: 10.sp),
                    onChanged: (value) {
                      onChanged(value);
                    }),
                suggestionsCallback: (pattern) {
                  return QualityControlFunction.filterCaNhanGayLoiList(masterDataList ?? [], pattern);
                },
                itemBuilder: (context, suggestion) {
                  return Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                      child: Text((suggestion).catalogTextVi ?? " ", style: TextStyle(fontSize: 10.sp)));
                },
                onSuggestionSelected: (selectedItem) {
                  // onSuggestionSelected(suggestion as TypeAheadCaNhanGayLoi);
                  // onSuggestionSelected(TypeAheadCaNhanGayLoi(index: errorIndex, selectedItem: suggestion, caNhanLoiIndex: caNhanLoiIndex));
                  onSuggestionSelected(selectedItem);
                },
                noItemsFoundBuilder: (value) {
                  return Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                      child: Text("Không tìm thấy kết quả", style: TextStyle(fontSize: 11.sp)));
                },
              ),
            ),
          ),
          showRemoveButton
              ? Expanded(
                  flex: 1,
                  child: Container(
                      // child: IconButton(
                      //   padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 0.w),
                      //   highlightColor: Colors.transparent,
                      //   hoverColor: Colors.transparent,
                      //   constraints: BoxConstraints(maxHeight: 20.h),
                      //   iconSize: 17.sp,
                      //   color: caNhanLoiIndex == 0 ? Colors.blue : Colors.red, // Color change
                      //   icon: caNhanLoiIndex == 0 ? const Icon(Icons.add) : const Icon(Icons.remove), // Icon change
                      //   onPressed: () {
                      //     if (caNhanLoiIndex == 0) {
                      //       onAddCaNhanLoi!();
                      //     } else {
                      //       onRemoveCaNhanLoi!();
                      //     }
                      //     // Add or remove logic based on errorIndex
                      //   },
                      // ),
                      ),
                )
              : Container()
        ],
      ),
    );
  }
}
