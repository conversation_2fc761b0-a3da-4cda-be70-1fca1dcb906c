class WarehouseTranfer {
  String? reservationId;
  String? slocExportId;
  String? storageBinExportId;
  String? slocImportId;
  String? storageBinImportId;
  String? rawMaterialCardId;
  List<WarehouseTranferDetails>? warehouseTranferDetails;

  WarehouseTranfer(
      {this.reservationId,
      this.slocExportId,
      this.storageBinExportId,
      this.slocImportId,
      this.storageBinImportId,
      this.rawMaterialCardId,
      this.warehouseTranferDetails});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reservationId'] = reservationId;
    data['slocExportId'] = slocExportId;
    data['storageBinExportId'] = storageBinExportId;
    data['slocImportId'] = slocImportId;
    data['storageBinImportId'] = storageBinImportId;
    data['rawMaterialCardId'] = rawMaterialCardId;
    if (warehouseTranferDetails != null) {
      data['warehouseTranferDetails'] = warehouseTranferDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // fromJson
  WarehouseTranfer.fromJson(Map<String, dynamic> json) {
    reservationId = json['reservationId'];
    slocExportId = json['slocExportId'];
    storageBinExportId = json['storageBinExportId'];
    slocImportId = json['slocImportId'];
    storageBinImportId = json['storageBinImportId'];
    rawMaterialCardId = json['rawMaterialCardId'];
    if (json['warehouseTranferDetails'] != null) {
      warehouseTranferDetails = <WarehouseTranferDetails>[];
      json['warehouseTranferDetails'].forEach((v) {
        warehouseTranferDetails!.add(WarehouseTranferDetails.fromJson(v));
      });
    }
  }
}

class WarehouseTranferDetails {
  String? productCode;
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;
  String? batchNumber;
  String? rawMaterialCardId;
  String? sttLine;

  WarehouseTranferDetails({
    this.productCode,
    this.so,
    this.soLine,
    this.wbs,
    this.quantity,
    this.unit,
    required this.batchNumber,
    this.rawMaterialCardId,
    this.sttLine,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['batchNumber'] = batchNumber;
    data['rawMaterialCardId'] = rawMaterialCardId;
    data['sttLine'] = sttLine;
    return data;
  }

  // fromJson
  WarehouseTranferDetails.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantity = json['quantity'];
    unit = json['unit'];
    batchNumber = json['batchNumber'];
    rawMaterialCardId = json['rawMaterialCardId'];
    sttLine = json['sttLine'];
  }
}

class MessageWarehouseTranfer {
  int? code;
  bool? isSuccess;
  String? message;
  bool? data;

  MessageWarehouseTranfer({this.code, this.isSuccess, this.message, this.data});

  MessageWarehouseTranfer.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = this.data;
    return data;
  }
}
