﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RequestReturnVendorDetailModel", Schema = "MES")]
    public partial class RequestReturnVendorDetailModel
    {
        [Key]
        public int Id { get; set; }
        public int? Stt { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string PoNumber { get; set; }
        public string MaNVL { get; set; }
        public string TenNVL { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? SoLuongTra { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? SoLuongPO { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? SoLuongThucTe { get; set; }
        public string Dvt { get; set; }
        public Guid RequestReturnVendorModelId { get; set; }

        [ForeignKey("RequestReturnVendorModelId")]
        [InverseProperty("RequestReturnVendorDetailModel")]
        public virtual RequestReturnVendorModel RequestReturnVendorModel { get; set; }
    }
}