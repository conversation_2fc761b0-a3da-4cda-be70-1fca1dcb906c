import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postGetListSOWBSByBatch.dart';
import '../../urlApi/urlApi.dart';
import 'package:http/http.dart' as http;

class GetListSOWBSByBatchApi {
  static Future<http.Response> getListSOWBSBYBatch(PostGetListSOWBSByBatch postGetListSOWBSByBatch, String token) async {
    final dataPost = jsonEncode(postGetListSOWBSByBatch);
    if (kDebugMode) {
      print(dataPost.toString());
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "GetListSOWBSByBatch");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> getListSOWBSBYBatchTranfer(PostGetListSOWBSByBatch postGetListSOWBSByBatch, String token) async {
    final dataPost = jsonEncode(postGetListSOWBSByBatch);
    if (kDebugMode) {
      print(dataPost.toString());
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "GetListSOWBSByBatch");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
