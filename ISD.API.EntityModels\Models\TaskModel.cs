﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskModel", Schema = "Task")]
    public partial class TaskModel
    {
        public TaskModel()
        {
            TaskProductModel = new HashSet<TaskProductModel>();
            Task_File_Mapping = new HashSet<Task_File_Mapping>();
            ThucThiLenhSanXuatModel = new HashSet<ThucThiLenhSanXuatModel>();
        }

        [Key]
        public Guid TaskId { get; set; }
        public int TaskCode { get; set; }
        [Required]
        [StringLength(4000)]
        public string Summary { get; set; }
        public Guid? ProfileId { get; set; }
        [StringLength(4000)]
        public string Description { get; set; }
        [StringLength(10)]
        public string PriorityCode { get; set; }
        public Guid WorkFlowId { get; set; }
        public Guid TaskStatusId { get; set; }
        public Guid? ProductWarrantyId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ReceiveDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EstimateEndDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        public Guid CompanyId { get; set; }
        public Guid StoreId { get; set; }
        [StringLength(20)]
        public string Reporter { get; set; }
        [StringLength(50)]
        public string ServiceTechnicalTeamCode { get; set; }
        public string CustomerReviews { get; set; }
        /// <summary>
        /// đơn vị thi công
        /// </summary>
        public Guid? ConstructionUnit { get; set; }
        /// <summary>
        /// liên hệ của đơn vị thi công
        /// </summary>
        public Guid? ConstructionUnitContact { get; set; }
        [StringLength(200)]
        public string FileUrl { get; set; }
        [StringLength(10)]
        public string CommonMistakeCode { get; set; }
        public int? DateKey { get; set; }
        [StringLength(50)]
        public string ErrorTypeCode { get; set; }
        [StringLength(50)]
        public string ErrorCode { get; set; }
        public bool? isRequiredCheckin { get; set; }
        [StringLength(4000)]
        public string VisitAddress { get; set; }
        [StringLength(200)]
        public string lat { get; set; }
        [StringLength(200)]
        public string lng { get; set; }
        [StringLength(50)]
        public string VisitSaleOfficeCode { get; set; }
        [StringLength(50)]
        public string VisitTypeCode { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        public string CancelReason { get; set; }
        public Guid? DeleteBy { get; set; }
        public bool? isDeleted { get; set; }
        public bool? isPrivate { get; set; }
        public bool? isRemind { get; set; }
        public int? RemindTime { get; set; }
        [StringLength(50)]
        public string RemindCycle { get; set; }
        public bool? isRemindForReporter { get; set; }
        public bool? isRemindForAssignee { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? RemindStartDate { get; set; }
        public string Property1 { get; set; }
        public string Property2 { get; set; }
        public string Property3 { get; set; }
        public string Property4 { get; set; }
        public string Property5 { get; set; }
        /// <summary>
        /// Giá trị ĐTB
        /// </summary>
        public string Property6 { get; set; }
        public string Property7 { get; set; }
        public string Property8 { get; set; }
        public string Property9 { get; set; }
        public string Property10 { get; set; }
        public Guid? ParentTaskId { get; set; }
        [StringLength(50)]
        public string SalesSupervisorCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date1 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date2 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date3 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date4 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date5 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date6 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date7 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date8 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date9 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date10 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number1 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number2 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number3 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number4 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number5 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number6 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number7 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number8 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number9 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Number10 { get; set; }
        [StringLength(500)]
        public string ProfileAddress { get; set; }
        public bool? IsAssignGroup { get; set; }
        public bool? IsSync { get; set; }
        public int? TaskTempCode { get; set; }
        [StringLength(50)]
        public string SubtaskCode { get; set; }
        /// <summary>
        /// Ghi chú ngắn dùng để edit trực tiếp trên lịch
        /// </summary>
        public string ShortNote { get; set; }
        [StringLength(500)]
        public string VisitPlace { get; set; }
        public Guid? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Qty { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [StringLength(500)]
        public string Barcode { get; set; }
        public bool? isKhongDongBo { get; set; }

        [InverseProperty("Task")]
        public virtual ICollection<TaskProductModel> TaskProductModel { get; set; }
        [InverseProperty("Task")]
        public virtual ICollection<Task_File_Mapping> Task_File_Mapping { get; set; }
        [InverseProperty("ParentTask")]
        public virtual ICollection<ThucThiLenhSanXuatModel> ThucThiLenhSanXuatModel { get; set; }
    }
}