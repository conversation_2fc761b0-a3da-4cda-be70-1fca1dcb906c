Based on the ZPG_ISD_PO program analysis, I'll help you create SQL queries for both header and detail data with <PERSON><PERSON> number as the key. Here are the SQL queries for SAP:

## **Header Data Query**

```sql
SELECT DISTINCT
    EKKO.EBELN,                    -- Purchase Order Number
    EKKO.BUKRS,                    -- Company Code
    EKKO.BSART,                    -- Document Type
    EKKO.LOEKZ,                    -- Deletion Indicator
    EKKO.LIFNR,                    -- Vendor Number
    EKKO.EKORG,                    -- Purchasing Organization
    EKKO.EKGRP,                    -- Purchasing Group
    EKKO.BEDAT,                    -- Document Date
    EKKO.FRGKE,                    -- Release Indicator
    EKKO.WAERS,                    -- Currency
    EKKO.WKURS,                    -- Exchange Rate
    EKKO.KUFIX,                    -- Exchange Rate Fixed
    EKKO.BEDAT,                    -- Purchase Order Date
    EKKO.KDATB,                    -- Start of Validity Period
    EKKO.KDATE,                    -- End of Validity Period
    EKKO.BWBDT,                    -- Default Value for Goods Receipt Processing Time
    EKKO.ANGDT,                    -- Quotation Submission Date
    EKKO.BNDDT,                    -- Binding Period for Quotation
    EKKO.GWLDT,                    -- Warranty Date
    EKKO.AUSNR,                    -- External Auction Number
    EKKO.ANGNR,                    -- Quotation Number
    EKKO.IHRAN,                    -- Your Reference
    EKKO.IHREZ,                    -- Our Reference
    EKKO.VERKF,                    -- Responsible Sales Person
    EKKO.TELF1,                    -- Telephone
    EKKO.LLIEF,                    -- Subcontractor
    EKKO.KONNR,                    -- Contract Number
    EKKO.ABGRU,                    -- Rejection Reason
    EKKO.PROCSTAT,                 -- Processing Status
    EKKO.RLWRT,                    -- Total Value at Time of Release
    -- Change document information
    CDHDR.OBJECTCLAS,              -- Object Class
    CDHDR.OBJECTID,                -- Object Value
    CDHDR.CHANGENR,                -- Document Change Number
    CDHDR.USERNAME,                -- Name of Person Who Changed Object
    CDHDR.UDATE,                   -- Date of Change
    CDHDR.UTIME,                   -- Time of Change
    CDHDR.TCODE,                   -- Transaction Code
    -- MES Integration Status
    CASE 
        WHEN MES_PO.ISSUCCESS IS NOT NULL THEN MES_PO.ISSUCCESS 
        ELSE MES_CDHDR.ISSUCCESS 
    END AS ISSUCCESS,
    CASE 
        WHEN MES_PO.MESSAGE IS NOT NULL THEN MES_PO.MESSAGE 
        ELSE MES_CDHDR.MESSAGE 
    END AS MESSAGE,
    CASE 
        WHEN MES_PO.PURCHASEORDERID IS NOT NULL THEN MES_PO.PURCHASEORDERID 
        ELSE MES_CDHDR.PURCHASEORDERID 
    END AS PURCHASEORDERID,
    CASE 
        WHEN MES_PO.I_USERNAME IS NOT NULL THEN MES_PO.I_USERNAME 
        ELSE MES_CDHDR.I_USERNAME 
    END AS I_USERNAME,
    CASE 
        WHEN MES_PO.I_UDATE IS NOT NULL THEN MES_PO.I_UDATE 
        ELSE MES_CDHDR.I_UDATE 
    END AS I_UDATE,
    CASE 
        WHEN MES_PO.I_UTIME IS NOT NULL THEN MES_PO.I_UTIME 
        ELSE MES_CDHDR.I_UTIME 
    END AS I_UTIME,
    -- Vendor Information (joined separately for better performance)
    LFA1.NAME1 AS VENDOR_NAME,     -- Vendor Name
    LFA1.LAND1 AS VENDOR_COUNTRY,  -- Vendor Country
    LFA1.ORT01 AS VENDOR_CITY       -- Vendor City

FROM EKKO                          -- Purchase Order Header
    INNER JOIN EKPO ON EKKO.EBELN = EKPO.EBELN  -- Purchase Order Items
    LEFT JOIN EKBE ON EKBE.EBELN = EKPO.EBELN 
                  AND EKBE.EBELP = EKPO.EBELP   -- Purchase Order History
    LEFT JOIN CDHDR ON CDHDR.OBJECTID = EKKO.EBELN
                   AND CDHDR.OBJECTCLAS = 'EINKBELEG'  -- Change Documents
    LEFT JOIN ZTB_MES_INF_PO AS MES_PO 
                   ON EKKO.EBELN = MES_PO.EBELN 
                  AND MES_PO.OBJECTCLAS = ''    -- MES Integration Direct PO Link
    LEFT JOIN ZTB_MES_INF_PO AS MES_CDHDR 
                   ON CDHDR.OBJECTCLAS = MES_CDHDR.OBJECTCLAS
                  AND CDHDR.OBJECTID = MES_CDHDR.OBJECTID
                  AND CDHDR.CHANGENR = MES_CDHDR.CHANGENR  -- MES Integration via Change Docs
    LEFT JOIN LFA1 ON EKKO.LIFNR = LFA1.LIFNR   -- Vendor Master

WHERE EKKO.EBELN = '**********'     -- Replace with your PO Number parameter
    AND EKKO.BUKRS IN ('1000')      -- Company Code filter
    AND EKKO.BSART IN ('ZPO1', 'ZPO2', 'ZPO7')  -- Document Types
    AND (EKBE.BWART IN ('101', '102') OR EKBE.BWART IS NULL)  -- Movement Types
    AND EKKO.LOEKZ = ''             -- Not deleted

ORDER BY EKKO.EBELN ASC, 
         CDHDR.UDATE DESC, 
         CDHDR.UTIME DESC;
```

## **Detail Data Query**

```sql
SELECT 
    EKPO.EBELN,                    -- Purchase Order Number
    EKPO.EBELP,                    -- Purchase Order Item Number
    EKPO.UNIQUEID,                 -- Unique ID for PO Item
    EKPO.LOEKZ,                    -- Deletion Indicator
    EKPO.STATU,                    -- Status of Purchase Order Item
    EKPO.AEDAT,                    -- Date on Which Record Was Created
    EKPO.TXZ01,                    -- Short Text
    EKPO.MATNR,                    -- Material Number
    EKPO.EMATN,                    -- Material Number of Trading Partner
    EKPO.BUKRS,                    -- Company Code
    EKPO.WERKS,                    -- Plant
    EKPO.LGORT,                    -- Storage Location
    EKPO.BEDNR,                    -- Requirement Tracking Number
    EKPO.MATKL,                    -- Material Group
    EKPO.INFNR,                    -- Number of Purchasing Info Record
    EKPO.IDNLF,                    -- Material Number Used by Vendor
    EKPO.KTMNG,                    -- Target Quantity
    EKPO.MENGE,                    -- Purchase Order Quantity
    EKPO.MEINS,                    -- Purchase Order Unit of Measure
    EKPO.BPRME,                    -- Order Price Unit (Purchasing)
    EKPO.BPUMZ,                    -- Numerator for Conversion of Order Price Unit
    EKPO.BPUMN,                    -- Denominator for Conv. of Order Price Unit
    EKPO.UMREZ,                    -- Numerator for Conversion of Order Unit to Base Unit
    EKPO.UMREN,                    -- Denominator for Conversion of Order Unit to Base Unit
    EKPO.NETPR,                    -- Net Price in Purchasing Document (in Document Currency)
    EKPO.PEINH,                    -- Price Unit
    EKPO.NETWR,                    -- Net Order Value in PO Currency
    EKPO.BRTWR,                    -- Gross Order Value in PO Currency
    EKPO.AGDAT,                    -- Deadline for Submission of Bid/Quotation
    EKPO.WEBAZ,                    -- Goods Receipt Processing Time in Days
    EKPO.MWSKZ,                    -- Tax on Sales/Purchases Code
    EKPO.BONUS,                    -- Settlement: Qualifying Bonus
    EKPO.INSMK,                    -- Stock Type of Goods Receipt
    EKPO.SPINF,                    -- Update Info Record
    EKPO.PRSDR,                    -- Print Relevant
    EKPO.SCHPR,                    -- Estimated Price
    EKPO.MAHNZ,                    -- Number of Reminders/Expediter Letters
    EKPO.MAHN1,                    -- Date of First Reminder
    EKPO.MAHN2,                    -- Date of Second Reminder
    EKPO.MAHN3,                    -- Date of Third Reminder
    EKPO.UEBTO,                    -- Overdelivery Tolerance
    EKPO.UEBTK,                    -- Indicator: Unlimited Overdelivery Allowed
    EKPO.UNTTO,                    -- Underdelivery Tolerance
    EKPO.ZWERT,                    -- Target Value for Header Area per Distribution
    EKPO.ABDAT,                    -- Unloading Point Date/Time
    EKPO.ABFTZ,                    -- Unloading Point Time
    EKPO.ETFZ1,                    -- Earliest Delivery Date/Time
    EKPO.ETFZ2,                    -- Latest Delivery Date/Time
    EKPO.KZABS,                    -- Indicator: Order Acknowledgment Required
    EKPO.LABNR,                    -- Order Acknowledgment Number
    EKPO.KONNR,                    -- Number of Principal Purchase Agreement
    EKPO.KTPNR,                    -- Item Number of Principal Purchase Agreement
    EKPO.ABRUS,                    -- Reason for Cancellation
    EKPO.PACKNO,                   -- Package Number
    EKPO.INTROW,                   -- Internal Row Number
    EKPO.ELIKZ,                    -- Delivery Completed Indicator
    EKPO.EREKZ,                    -- Final Invoice Indicator
    EKPO.PSTYP,                    -- Item Category in Purchasing Document
    EKPO.KNTTP,                    -- Account Assignment Category
    EKPO.KZVBR,                    -- Indicator: Consumption Posting
    EKPO.VRTKZ,                    -- Distribution Indicator
    EKPO.TWRKZ,                    -- Partial Invoice Indicator
    EKPO.WEPOS,                    -- Goods Receipt Indicator
    EKPO.WEUNB,                    -- Indicator: GR-Based Inv. Verif.
    EKPO.REPOS,                    -- Invoice Receipt Indicator
    EKPO.WEBRE,                    -- Indicator: GR-Based Invoice Verification
    EKPO.KZABS,                    -- Indicator: Order Acknowledgment Required
    EKPO.REVLV,                    -- Revision Level
    EKPO.GEBER,                    -- Fund
    EKPO.FISTL,                    -- Funds Center
    EKPO.FIPOS,                    -- Commitment Item
    EKPO.KO_GSBER,                 -- Business Area
    EKPO.KO_PARGB,                 -- Trading Partner's Business Area
    EKPO.KO_PRCTR,                 -- Profit Center
    EKPO.KO_PPRCTR,                -- Partner Profit Center
    EKPO.MEPRF,                    -- Purchasing Document Category
    EKPO.BSTYP,                    -- Purchasing Document Category
    EKPO.BSTAE,                    -- Purchasing Document Status
    EKPO.RETPO,                    -- Returns Item
    EKPO.DPTYP,                    -- Document Category
    EKPO.DPPCT,                    -- Down Payment Percentage
    EKPO.DPAMT,                    -- Down Payment Amount
    EKPO.DPDAT,                    -- Expected Down Payment Date
    EKPO.FLS_RSTO,                 -- Restoration Event
    EKPO.EXT_RFX_NUMBER,           -- External RFx Number
    EKPO.EXT_RFX_ITEM,             -- External RFx Item Number
    EKPO.EXT_RFX_SYSTEM,           -- External RFx System
    EKPO.SRM_CONTRACT_ID,          -- Contract in SRM
    EKPO.SRM_CONTRACT_ITM,         -- Contract Item Number in SRM
    EKPO.BLK_REASON_ID,            -- Block Reason ID
    EKPO.BLK_REASON_TXT,           -- Block Reason Text
    EKPO.ITCONS,                   -- Indicator: Item Consigned
    EKPO.FIXPO,                    -- Indicator: Item with Fixed Vendor
    EKPO.EXCLUS,                   -- Indicator: Exclusive
    EKPO.ULDUL,                    -- Indicator: Unlimited/Limited
    EKPO.TCOMP,                    -- Indicator: Termination of Contract
    EKPO.TCONT_TXT,                -- Contract Termination: Text
    EKPO.BLIND,                    -- Indicator: Blind
    EKPO.MAXLZ,                    -- Maximum Lead Time
    EKPO.INCO1,                    -- Incoterms (Part 1)
    EKPO.INCO2,                    -- Incoterms (Part 2)
    EKPO.VORAB,                    -- Advance Shipment Notice Required
    EKPO.KOLIF,                    -- Subsequent Delivery
    EKPO.LTSNR,                    -- Delivery Schedule Number
    EKPO.PACKNO,                   -- Package Number
    EKPO.FPLNR,                    -- Invoice Plan Number
    EKPO.GNETWR,                   -- Net Order Value in PO Currency
    EKPO.STAFO,                    -- Update Group for Statistics Update
    EKPO.PLIFZ,                    -- Planned Delivery Time in Days
    EKPO.NTGEW,                    -- Net Weight
    EKPO.GEWEI,                    -- Weight Unit
    EKPO.VOLUM,                    -- Volume
    EKPO.VOLEH,                    -- Volume Unit
    EKPO.INCO2_L,                  -- Incoterms Location 1
    EKPO.INCO3_L,                  -- Incoterms Location 2
    EKPO.LODED,                    -- Unloading Point
    EKPO.AFLDT,                    -- Release Date
    -- SD Related Fields
    EKPO.VBELN,                    -- Sales and Distribution Document Number
    EKPO.VBELP,                    -- Sales and Distribution Document Item Number
    EKPO.VRKME,                    -- Sales Unit
    -- Project System Fields  
    EKPO.PS_PSP_PNR,               -- Work Breakdown Structure Element (WBS Element)
    EKPO.AUFNR,                    -- Order Number
    EKPO.SAKTO,                    -- G/L Account Number
    EKPO.KOSTL,                    -- Cost Center
    -- Additional Material Info
    MAKT.MAKTX AS MATERIAL_DESC,   -- Material Description
    MARA.MTART AS MATERIAL_TYPE,   -- Material Type
    MARA.MEINS AS BASE_UOM,        -- Base Unit of Measure
    -- Storage Location Info
    T001L.LGOBE AS STORAGE_LOC_DESC, -- Storage Location Description
    -- Plant Info  
    T001W.NAME1 AS PLANT_NAME      -- Plant Name

FROM EKPO                          -- Purchase Order Items
    LEFT JOIN MAKT ON EKPO.MATNR = MAKT.MATNR 
                  AND MAKT.SPRAS = 'E'         -- Material Descriptions (English)
    LEFT JOIN MARA ON EKPO.MATNR = MARA.MATNR  -- Material Master
    LEFT JOIN T001L ON EKPO.WERKS = T001L.WERKS 
                   AND EKPO.LGORT = T001L.LGORT -- Storage Location
    LEFT JOIN T001W ON EKPO.WERKS = T001W.WERKS -- Plant

WHERE EKPO.EBELN = '**********'    -- Replace with your PO Number parameter
    AND EKPO.LOEKZ = ''            -- Not deleted items only

ORDER BY EKPO.EBELN ASC, 
         EKPO.EBELP ASC;           -- Sort by PO Number and Item Number
```

## **Simplified Queries (Core Fields Only)**

If you need simpler versions with just the essential fields:

### **Header - Essential Fields**
```sql
SELECT 
    EKKO.EBELN,           -- PO Number
    EKKO.BUKRS,           -- Company Code  
    EKKO.BSART,           -- Document Type
    EKKO.LIFNR,           -- Vendor
    EKKO.BEDAT,           -- PO Date
    EKKO.WAERS,           -- Currency
    LFA1.NAME1 AS VENDOR_NAME
FROM EKKO
    LEFT JOIN LFA1 ON EKKO.LIFNR = LFA1.LIFNR
WHERE EKKO.EBELN = '**********'
    AND EKKO.LOEKZ = '';
```

### **Detail - Essential Fields**
```sql
SELECT 
    EKPO.EBELN,           -- PO Number
    EKPO.EBELP,           -- PO Item
    EKPO.MATNR,           -- Material
    EKPO.TXZ01,           -- Description
    EKPO.MENGE,           -- Quantity
    EKPO.MEINS,           -- Unit
    EKPO.NETPR,           -- Price
    EKPO.NETWR,           -- Net Value
    EKPO.WERKS,           -- Plant
    EKPO.LGORT,           -- Storage Location
    MAKT.MAKTX AS MATERIAL_DESC
FROM EKPO
    LEFT JOIN MAKT ON EKPO.MATNR = MAKT.MATNR AND MAKT.SPRAS = 'E'
WHERE EKPO.EBELN = '**********'
    AND EKPO.LOEKZ = ''
ORDER BY EKPO.EBELP;
```

## **Usage Notes:**

1. **Replace '**********'** with your actual PO number or use a parameter
2. **Adjust Company Code** ('1000') as needed
3. **Language Code** - Change 'E' to your required language in MAKT joins
4. **Custom Tables** - ZTB_MES_INF_PO may not exist in your system,