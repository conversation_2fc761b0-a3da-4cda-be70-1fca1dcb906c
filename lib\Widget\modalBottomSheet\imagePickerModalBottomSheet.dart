import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class ImagePickerActionSheet extends StatelessWidget {
  const ImagePickerActionSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Platform.isAndroid ? Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        ListTile(
          leading: Icon(Icons.camera_alt,size: 25.sp,),
          title:  Text(
            'Camera',
            style: TextStyle(fontSize: 15.sp),
          ),
          onTap: () => Navigator.of(context).pop(false)


        ),
        ListTile(
            leading: Icon(Icons.image,size: 25.sp,),
            title:  Text(
              'Hình Ảnh',
              style: TextStyle(fontSize: 15.sp),
            ),
            onTap: () =>Navigator.of(context).pop(true)

        )

      ],
    ):CupertinoActionSheet(
      actions: <Widget>[
        CupertinoActionSheetAction(
          child: Text('Camera',style: TextStyle(fontSize: 20.sp),),

          onPressed: () => Navigator.of(context).pop(false),
        ),
        CupertinoActionSheetAction(
          child: Text('Hình Ảnh',style: TextStyle(fontSize: 20.sp),),
          onPressed: () => Navigator.of(context).pop(true),
        ),
      ],

    );


  }
}