class GetQuantityImported {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetQuantityImported? data;


  GetQuantityImported(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetQuantityImported.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetQuantityImported.fromJson(json['data']) : null;

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetQuantityImported {
  String? batchNumber;
  String? plant;
  String? sloc;
  String? warehouseNo;
  String? storageBin;
  String? productCode;
  String? productName;
  double? sumQuantityImported;
  List<QuantityImporteds>? quantityImporteds;

  DataGetQuantityImported(
      {this.batchNumber,
        this.plant,
        this.sloc,
        this.warehouseNo,
        this.storageBin,
        this.productCode,
        this.productName,
        this.sumQuantityImported,
        this.quantityImporteds});

  DataGetQuantityImported.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
    plant = json['plant'];
    sloc = json['sloc'];
    warehouseNo = json['warehouseNo'];
    storageBin = json['storageBin'];
    productCode = json['productCode'];
    productName = json['productName'];
    sumQuantityImported = json['sumQuantityImported'];
    if (json['quantityImporteds'] != null) {
      quantityImporteds = <QuantityImporteds>[];
      json['quantityImporteds'].forEach((v) {
        quantityImporteds!.add(QuantityImporteds.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['warehouseNo'] = warehouseNo;
    data['storageBin'] = storageBin;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['sumQuantityImported'] = sumQuantityImported;
    if (quantityImporteds != null) {
      data['quantityImporteds'] =
          quantityImporteds!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class QuantityImporteds {
  String? po;
  String? poLine;
  String? so;
  String? soLine;
  String? wbs;
  double? quantityImported;
  double? quantityStock;
  String? unit;
  QuantityImporteds(
      {this.po,
        this.poLine,
        this.so,
        this.soLine,
        this.wbs,
        this.quantityImported,
        this.quantityStock,
        this.unit});

  QuantityImporteds.fromJson(Map<String, dynamic> json) {
    po = json['po'];
    poLine = json['poLine'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantityImported = json['quantityImported'];
    quantityStock = json['quantityStock'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['po'] = po;
    data['poLine'] = poLine;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantityImported'] = quantityImported;
    data['quantityStock'] = quantityStock;
    data['unit'] = unit;
    return data;
  }
}