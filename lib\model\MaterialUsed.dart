class ListMaterialUsedmd {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataMaterialUsed>? data;

  ListMaterialUsedmd(
      {this.code,
        this.isSuccess,
        this.message,
        this.data,
      });

  ListMaterialUsedmd.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataMaterialUsed>[];
      json['data'].forEach((v) {
        data!.add(DataMaterialUsed.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataMaterialUsed {
  String? productCode;
  String? productName;
  String? productDisplay;
  List<String>? batchsNumber;

  DataMaterialUsed(
      {this.productCode,
        this.productName,
        this.productDisplay,
        this.batchsNumber});

  DataMaterialUsed.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    productName = json['productName'];
    productDisplay = json['productDisplay'];
    batchsNumber = json['batchsNumber'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['productDisplay'] = productDisplay;
    data['batchsNumber'] = batchsNumber;
    return data;
  }
}
