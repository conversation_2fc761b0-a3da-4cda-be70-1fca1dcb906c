﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PurchaseOrderMasterModel", Schema = "MESP2")]
    public partial class PurchaseOrderMasterModel
    {
        public PurchaseOrderMasterModel()
        {
            PurchaseOrderDetailModel = new HashSet<PurchaseOrderDetailModel>();
        }

        [Key]
        public Guid PurchaseOrderId { get; set; }
        [StringLength(50)]
        public string PurchaseOrderCode { get; set; }
        [StringLength(50)]
        public string CompanyCode { get; set; }
        [StringLength(50)]
        public string DocumentType { get; set; }
        [StringLength(50)]
        public string DeletionInd { get; set; }
        [StringLength(50)]
        public string VendorNumber { get; set; }
        [StringLength(50)]
        public string PurchasingOrg { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocumentDate { get; set; }
        [StringLength(50)]
        public string ReleaseIndicator { get; set; }
        public bool? IsVendorView { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("PurchaseOrder")]
        public virtual ICollection<PurchaseOrderDetailModel> PurchaseOrderDetailModel { get; set; }
    }
}