*&---------------------------------------------------------------------*
*& Include          ZIN_FI_EINV_PROPOSAL_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form INIT_PROC
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM INIT_PROC .

  CLEAR S_BSART[].
  S_BSART-OPTION = 'EQ'.
  S_BSART-SIGN = 'I'.
  S_BSART-LOW = 'ZPO1'.
  APPEND S_BSART.
  S_BSART-LOW = 'ZPO2'.
  APPEND S_BSART.
  S_BSART-LOW = 'ZPO7'.
  APPEND S_BSART.

  CLEAR S_BWART[].
  S_BWART-OPTION = 'EQ'.
  S_BWART-SIGN = 'I'.
  S_BWART-LOW = '101'.
  APPEND S_BWART.
  S_BWART-LOW = '102'.
  APPEND S_BWART.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form CHECK_PROC
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM CHECK_PROC .
*  AUTHORITY-CHECK OBJECT 'F_BKPF_BUK'
*   ID 'BUKRS' FIELD S_BUKRS-LOW
*   ID 'ACTVT' FIELD '03'.
*  IF SY-SUBRC <> 0.
*    MESSAGE ID SY-MSGID TYPE SY-MSGTY
*    NUMBER SY-MSGNO
*    WITH SY-MSGV1 SY-MSGV2 SY-MSGV3 SY-MSGV4.
**   Implement a suitable exception handling here
*  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form MAIN_PROC
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM MAIN_PROC .
  PERFORM GET_STYLE_DISABLE.
  PERFORM GET_DATA.
  PERFORM PROCESS_DATA.
  PERFORM DISPLAY_DATA.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM GET_DATA .
  PERFORM GET_INPUT.
  PERFORM GET_MAIN_DATA.
  PERFORM GET_TABLE_DATA.
  PERFORM GET_DESC_DATA.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_INPUT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
FORM GET_INPUT.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_MAIN_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM GET_MAIN_DATA .
  DATA:
        LS_INPUT LIKE LINE OF GT_DATA_INPUT.

  SELECT DISTINCT
      EKKO~EBELN,
      EKKO~BUKRS,
      EKKO~BSART,
      EKKO~LOEKZ,
      EKKO~LIFNR,
      EKKO~EKORG,
      EKKO~BEDAT,
      EKKO~FRGKE,
      CDHDR~OBJECTCLAS,
      CDHDR~OBJECTID,
      CDHDR~CHANGENR,
      CDHDR~USERNAME,
      CDHDR~UDATE,
      CDHDR~UTIME,
      CDHDR~TCODE,
      CDHDR~PLANCHNGNR,
      CDHDR~ACT_CHNGNO,
      CDHDR~WAS_PLANND,
      CDHDR~CHANGE_IND,
      CDHDR~LANGU,
      CDHDR~VERSION,
      CDHDR~_DATAAGING,
       (   CASE WHEN MES_PO~ISSUCCESS IS NOT NULL THEN MES_PO~ISSUCCESS ELSE MES_CDHDR~ISSUCCESS END ) AS ISSUCCESS,
       (   CASE WHEN MES_PO~MESSAGE IS NOT NULL THEN MES_PO~MESSAGE ELSE MES_CDHDR~MESSAGE END ) AS MESSAGE,
       (   CASE WHEN MES_PO~PURCHASEORDERID IS NOT NULL THEN MES_PO~PURCHASEORDERID ELSE MES_CDHDR~PURCHASEORDERID END ) AS PURCHASEORDERID,
       (   CASE WHEN MES_PO~I_USERNAME IS NOT NULL THEN MES_PO~I_USERNAME ELSE MES_CDHDR~I_USERNAME END ) AS I_USERNAME,
       (   CASE WHEN MES_PO~I_UDATE IS NOT NULL THEN MES_PO~I_UDATE ELSE MES_CDHDR~I_UDATE END ) AS I_UDATE,
       (   CASE WHEN MES_PO~I_UTIME IS NOT NULL THEN MES_PO~I_UTIME ELSE MES_CDHDR~I_UTIME END ) AS I_UTIME
    FROM EKKO INNER JOIN EKPO ON EKKO~EBELN = EKPO~EBELN
      LEFT JOIN EKBE ON EKBE~EBELN = EKPO~EBELN AND EKBE~EBELP = EKPO~EBELP
      LEFT JOIN CDHDR ON CDHDR~OBJECTID = EKKO~EBELN
      LEFT JOIN ZTB_MES_INF_PO AS MES_PO ON EKKO~EBELN = MES_PO~EBELN AND MES_PO~OBJECTCLAS = ''
      LEFT JOIN ZTB_MES_INF_PO AS MES_CDHDR ON CDHDR~OBJECTCLAS = MES_CDHDR~OBJECTCLAS
          AND CDHDR~OBJECTID = MES_CDHDR~OBJECTID
          AND CDHDR~CHANGENR = MES_CDHDR~CHANGENR
    INTO CORRESPONDING FIELDS OF TABLE @GT_DATA_HEADER
    WHERE EKKO~EBELN IN @S_EBELN
      AND EKKO~BUKRS IN @S_BUKRS
      AND EKKO~BSART IN @S_BSART
      AND EKBE~BWART IN @S_BWART
      AND CDHDR~UDATE IN @S_DATUM.

  SORT GT_DATA_HEADER BY EBELN ASCENDING UDATE DESCENDING
                                         UTIME DESCENDING.
  DELETE ADJACENT DUPLICATES FROM GT_DATA_HEADER COMPARING EBELN.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  GET_TABLE_DATA
*&---------------------------------------------------------------------*
*&      text
*&---------------------------------------------------------------------*
FORM GET_TABLE_DATA.


ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  GET_DESC_DATA
*&---------------------------------------------------------------------*
*&      text
*&---------------------------------------------------------------------*
FORM GET_DESC_DATA.
*
*  SELECT * FROM T006A
*  INTO TABLE GT_T006A
*  WHERE SPRAS EQ SY-LANGU.
*
*  SELECT * FROM T007V
*  INTO TABLE GT_T007V
*  WHERE ALAND EQ 'VN'.
*
*  SELECT *
*    INTO TABLE GT_T042ZT
*    FROM T042ZT
*    WHERE SPRAS EQ SY-LANGU.
*
*  DATA: LT_ACDOCA LIKE GT_ACDOCA.
*
*  CHECK GT_ACDOCA IS NOT INITIAL.
*  LT_ACDOCA[] = GT_ACDOCA[].
*  DELETE LT_ACDOCA WHERE MATNR IS INITIAL.
*  SORT LT_ACDOCA BY MATNR.
*  CHECK LT_ACDOCA IS NOT INITIAL.
*
*  SELECT * FROM MAKT
*  INTO TABLE GT_MAKT
*  FOR ALL ENTRIES IN LT_ACDOCA
*  WHERE MATNR EQ LT_ACDOCA-MATNR AND
*        SPRAS EQ SY-LANGU.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form DISPLAY_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM DISPLAY_DATA .

  PERFORM CREATE_OBJECT.
  PERFORM CALL_SCREEN.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  CALL_SCREEN
*&---------------------------------------------------------------------*
*       Call screen ALV
*----------------------------------------------------------------------*
FORM CALL_SCREEN .

  CALL SCREEN 0100.

ENDFORM.                    " CALL_SCREEN
*&---------------------------------------------------------------------*
*&      Form  CREATE_OBJECT
*&---------------------------------------------------------------------*
*       Create objects
*----------------------------------------------------------------------*
FORM CREATE_OBJECT .

  DATA: LS_VARIANT_H TYPE DISVARIANT,
        LS_VARIANT_D TYPE DISVARIANT,
        LT_EXCLUDE_H TYPE UI_FUNCTIONS,
        LT_EXCLUDE_D TYPE UI_FUNCTIONS.

  FIELD-SYMBOLS: <LF_FIELDCAT> TYPE LVC_S_FCAT.

  IF GO_CONTAINER IS INITIAL.
    LS_VARIANT_H-REPORT = SY-REPID && '_H'.
    LS_VARIANT_D-REPORT = SY-REPID && '_D'.
*   Create main contianer
    CREATE OBJECT GO_CONTAINER
      EXPORTING
        CONTAINER_NAME = 'CC_CONTAINER'.
*   Create Spliter
    CREATE OBJECT GO_SPLITTER
      EXPORTING
        PARENT  = GO_CONTAINER
        ROWS    = 2
        COLUMNS = 1
        ALIGN   = 15.
*   Get 1st container of spliter
    CALL METHOD GO_SPLITTER->GET_CONTAINER
      EXPORTING
        ROW       = 1
        COLUMN    = 1
      RECEIVING
        CONTAINER = GO_PARENT1.
*   Get 2nd container of spliter
    CALL METHOD GO_SPLITTER->GET_CONTAINER
      EXPORTING
        ROW       = 2
        COLUMN    = 1
      RECEIVING
        CONTAINER = GO_PARENT2.
*   Create ALV header from 1st container
    CREATE OBJECT GO_ALV_H
      EXPORTING
        I_PARENT = GO_PARENT1.
*   Set layout
    GS_LAYOUT01-ZEBRA = 'X'.
    GS_LAYOUT01-CWIDTH_OPT = 'X'.
    GS_LAYOUT01-STYLEFNAME = 'STYLE'.
*   Create field catalog
    PERFORM BUILD_FIELDCAT.

*   Create excluding tollbar for header
    PERFORM CREATE_EX_TOOLBAR
      CHANGING LT_EXCLUDE_H.
*   Display ALV
    CALL METHOD GO_ALV_H->SET_TABLE_FOR_FIRST_DISPLAY
      EXPORTING
        IS_VARIANT           = LS_VARIANT_H
        I_SAVE               = 'A'
        IS_LAYOUT            = GS_LAYOUT01
        IT_TOOLBAR_EXCLUDING = LT_EXCLUDE_H
      CHANGING
        IT_OUTTAB            = GT_DATA_HEADER
        IT_FIELDCATALOG      = GT_FLDCAT01.
*   Register events
    CALL METHOD GO_ALV_H->REGISTER_EDIT_EVENT
      EXPORTING
        I_EVENT_ID = CL_GUI_ALV_GRID=>MC_EVT_ENTER.
*   Create events and set event handler
    CREATE OBJECT GO_EVT_HEAD.
    SET HANDLER GO_EVT_HEAD->HANDLE_DATA_CHANGED FOR GO_ALV_H.
    SET HANDLER GO_EVT_HEAD->HANDLE_DOUBLE_CLICK FOR GO_ALV_H.
*    SET HANDLER GO_EVT_HEAD->HANDLE_TOOLBAR FOR GO_ALV_H.
*    SET HANDLER GO_EVT_HEAD->HANDLE_USER_COMMAND FOR GO_ALV_H.
*   Raise event toolbar
    CALL METHOD GO_ALV_H->SET_TOOLBAR_INTERACTIVE.
*   Create ALV detail from 2nd container
    CREATE OBJECT GO_ALV_D
      EXPORTING
        I_PARENT = GO_PARENT2.
*   Set layout
    GS_LAYOUT02-ZEBRA = 'X'.
    GS_LAYOUT02-CWIDTH_OPT = 'X'.

*   Create excluding tollbar for detail
    PERFORM CREATE_EX_TOOLBAR
      CHANGING LT_EXCLUDE_D.
*   Display ALV
    CALL METHOD GO_ALV_D->SET_TABLE_FOR_FIRST_DISPLAY
      EXPORTING
        IS_VARIANT           = LS_VARIANT_D
        I_SAVE               = 'A'
        IS_LAYOUT            = GS_LAYOUT02
        IT_TOOLBAR_EXCLUDING = LT_EXCLUDE_D
      CHANGING
        IT_OUTTAB            = GT_DATA_DETAIL
        IT_FIELDCATALOG      = GT_FLDCAT02.
*   Register events
    CALL METHOD GO_ALV_D->REGISTER_EDIT_EVENT
      EXPORTING
        I_EVENT_ID = CL_GUI_ALV_GRID=>MC_EVT_ENTER.
*   Create events and set event handler
    CREATE OBJECT GO_EVT_DET.
    SET HANDLER GO_EVT_DET->HANDLE_DATA_CHANGED FOR GO_ALV_D.
  ELSE.
    PERFORM REFRESH_ALV.
  ENDIF.

ENDFORM.                    " CREATE_OBJECT
*&---------------------------------------------------------------------*
*&      Form  BUILD_FIELDCAT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
FORM BUILD_FIELDCAT .
  FIELD-SYMBOLS: <LF_FLDCAT> TYPE LVC_S_FCAT.

  CALL FUNCTION 'LVC_FIELDCATALOG_MERGE'
    EXPORTING
      I_STRUCTURE_NAME       = 'ZISD_EKKO'
    CHANGING
      CT_FIELDCAT            = GT_FLDCAT01[]
    EXCEPTIONS
      INCONSISTENT_INTERFACE = 1
      PROGRAM_ERROR          = 2
      OTHERS                 = 3.

  LOOP AT GT_FLDCAT01 ASSIGNING <LF_FLDCAT>.

    <LF_FLDCAT>-NO_OUT    = ''.
    <LF_FLDCAT>-TECH      = ''.

    CASE <LF_FLDCAT>-FIELDNAME.
      WHEN 'SELECT'.
        <LF_FLDCAT>-EDIT = 'X'.
        <LF_FLDCAT>-CHECKBOX = 'X'.
        <LF_FLDCAT>-COL_POS = '01'.
    ENDCASE.

  ENDLOOP.
  UNASSIGN <LF_FLDCAT>.
  "=====================================================

  CALL FUNCTION 'LVC_FIELDCATALOG_MERGE'
    EXPORTING
      I_STRUCTURE_NAME       = 'ZISD_EKPO'
    CHANGING
      CT_FIELDCAT            = GT_FLDCAT02[]
    EXCEPTIONS
      INCONSISTENT_INTERFACE = 1
      PROGRAM_ERROR          = 2
      OTHERS                 = 3.

  UNASSIGN <LF_FLDCAT>.
ENDFORM.                    " BUILD_FIELDCAT

*&---------------------------------------------------------------------*
*&      Form  CREATE_EX_TOOLBAR
*&---------------------------------------------------------------------*
*       Creat excluding toolbar
*----------------------------------------------------------------------*
FORM CREATE_EX_TOOLBAR
  CHANGING LPT_EXCLUDE  TYPE UI_FUNCTIONS.

  DATA:
    LS_EXCLUDE TYPE UI_FUNC.

  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_COPY_ROW.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_INFO.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_APPEND_ROW.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_INSERT_ROW.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_CUT.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_PASTE.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_PASTE_NEW_ROW.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_DELETE_ROW.
  APPEND LS_EXCLUDE TO LPT_EXCLUDE.
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_REFRESH.
*  APPEND LS_EXCLUDE TO LPT_EXCLUDE.

ENDFORM.                    " CREATE_EX_TOOLBAR
*&---------------------------------------------------------------------*
*&      Form  EDIT_TOOLBAR
*&---------------------------------------------------------------------*
*       Edit toolbar
*----------------------------------------------------------------------*
FORM EDIT_TOOLBAR
  CHANGING LPO_TOOLBAR TYPE REF TO CL_ALV_EVENT_TOOLBAR_SET.

*  DATA:
*    LS_TOOLBAR TYPE STB_BUTTON.
** Separator
*  CLEAR: LS_TOOLBAR.
*  LS_TOOLBAR-BUTN_TYPE  = 3.
*  INSERT LS_TOOLBAR INTO LPO_TOOLBAR->MT_TOOLBAR INDEX 1.
** Deselect all
*  CLEAR: LS_TOOLBAR.
*  LS_TOOLBAR-FUNCTION   = 'DE_SEL'.
*  LS_TOOLBAR-ICON       = ICON_DESELECT_ALL.
*  LS_TOOLBAR-QUICKINFO  = 'Deselect all'.
*  INSERT LS_TOOLBAR INTO LPO_TOOLBAR->MT_TOOLBAR INDEX 1.
** Select all
*  CLEAR: LS_TOOLBAR.
*  LS_TOOLBAR-FUNCTION   = 'SEL_ALL'.
*  LS_TOOLBAR-ICON       = ICON_SELECT_ALL.
*  LS_TOOLBAR-QUICKINFO  = 'Select all'.
*  INSERT LS_TOOLBAR INTO LPO_TOOLBAR->MT_TOOLBAR INDEX 1.

ENDFORM.                    " EDIT_TOOLBAR
*&---------------------------------------------------------------------*
*&      Form  USER_COMMAND
*&---------------------------------------------------------------------*
*       Process user command on alv
*----------------------------------------------------------------------*
FORM USER_COMMAND
  USING VALUE(LPW_UCOMM) TYPE SY-UCOMM.

*  CASE LPW_UCOMM.
*    WHEN 'SEL_ALL'.
*      PERFORM SET_SELECT USING 'X'.
*    WHEN 'DE_SEL'.
*      PERFORM SET_SELECT USING ''.
*    WHEN OTHERS.
*  ENDCASE.
*
*  PERFORM REFRESH_ALV
*    USING GO_ALV_H
*          GS_LAYOUT_H.

ENDFORM.                    " USER_COMMAND
*&---------------------------------------------------------------------*
*& Form PROCESS_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM PROCESS_DATA .


ENDFORM.
*&---------------------------------------------------------------------*
*& Form REFRESH_ALV
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM REFRESH_ALV .

  DATA:
    LS_LAYOUT TYPE LVC_S_LAYO,
    LS_STABLE TYPE LVC_S_STBL.

  LS_STABLE-COL = 'X'.
  LS_STABLE-ROW = 'X'.

  PERFORM SET_TEXT_HEADER.

  GO_ALV_H->REFRESH_TABLE_DISPLAY(
  EXPORTING
    IS_STABLE = LS_STABLE ).
  GO_ALV_D->REFRESH_TABLE_DISPLAY(
  EXPORTING
    IS_STABLE = LS_STABLE ).

  CALL METHOD GO_ALV_H->GET_FRONTEND_LAYOUT
    IMPORTING
      ES_LAYOUT = LS_LAYOUT.

  IF SY-SUBRC EQ 0.

    LS_LAYOUT-CWIDTH_OPT = 'X'.

    CALL METHOD GO_ALV_H->SET_FRONTEND_LAYOUT
      EXPORTING
        IS_LAYOUT = LS_LAYOUT.

  ENDIF.


  CALL METHOD GO_ALV_D->GET_FRONTEND_LAYOUT
    IMPORTING
      ES_LAYOUT = LS_LAYOUT.

  IF SY-SUBRC EQ 0.

    LS_LAYOUT-CWIDTH_OPT = 'X'.

    CALL METHOD GO_ALV_D->SET_FRONTEND_LAYOUT
      EXPORTING
        IS_LAYOUT = LS_LAYOUT.

  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*&      Form  CALL_POPUP
*&---------------------------------------------------------------------*
*       Call popup to confirm
*----------------------------------------------------------------------*
FORM CALL_POPUP CHANGING LW_ANSWER TYPE C.

*  DATA:
*    LW_TITLE    TYPE CHAR50,
*    LW_QUESTION TYPE CHAR255.
*
*  CASE GW_OK_CODE.
*    WHEN '&ISSUE'.
*      LW_TITLE    = TEXT-T03.
*      LW_QUESTION = TEXT-T04.
*    WHEN '&REJECT'.
*      LW_TITLE    = TEXT-T07.
*      LW_QUESTION = TEXT-T04.
*    WHEN '&I_CANCEL'.
*      LW_TITLE    = TEXT-T09.
*      LW_QUESTION = TEXT-T04.
*    WHEN 'R_CANCEL'.
*      LW_TITLE    = TEXT-T11.
*      LW_QUESTION = TEXT-T04.
*  ENDCASE.
*
*  CALL FUNCTION 'POPUP_TO_CONFIRM'
*    EXPORTING
*      TITLEBAR              = LW_TITLE
*      TEXT_QUESTION         = LW_QUESTION
*      TEXT_BUTTON_1         = 'Yes'
*      TEXT_BUTTON_2         = 'No'
*      DEFAULT_BUTTON        = '2'
*      DISPLAY_CANCEL_BUTTON = SPACE
*    IMPORTING
*      ANSWER                = LW_ANSWER
*    EXCEPTIONS
*      TEXT_NOT_FOUND        = 1
*      OTHERS                = 2.
*  IF SY-SUBRC <> 0.
*    MESSAGE S022(ZMC_CTK) WITH 'POPUP_TO_CONFIRM' DISPLAY LIKE 'E'.
*    LEAVE TO SCREEN 0100.
*  ENDIF.

  LW_ANSWER = '1'.

ENDFORM.                    " CALL_POPUP
*&---------------------------------------------------------------------*
*& Form DISPLAY_DETAIL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> E_ROW
*&---------------------------------------------------------------------*
FORM DISPLAY_DETAIL
  USING
        P_ROW TYPE LVC_S_ROW
        P_COL TYPE LVC_S_COL
  .

  READ TABLE GT_DATA_HEADER INTO DATA(LS_HEADER)
    INDEX P_ROW-INDEX.
  IF SY-SUBRC EQ 0.
    PERFORM GET_DATA_DETAIL USING LS_HEADER CHANGING GT_DATA_DETAIL.

    PERFORM REFRESH_ALV.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form CHECK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM CHECK_DATA .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form APPROVAL_FOR_INVOICE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM APPROVAL_FOR_INVOICE .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_DOMAIN_VALUE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM GET_DOMAIN_VALUE .
*
*  DATA:
*    LV_DOMNAME TYPE DOMNAME.
*
*  LV_DOMNAME = 'ZDO_EINV_STATUS'.
*  CALL FUNCTION 'GET_DOMAIN_VALUES'
*    EXPORTING
*      DOMNAME         = LV_DOMNAME
*    TABLES
*      VALUES_TAB      = GT_STATUS
*    EXCEPTIONS
*      NO_VALUES_FOUND = 1
*      OTHERS          = 2.
*
*  LV_DOMNAME = 'ZDO_EINV_CANCEL_STATUS'.
*  CALL FUNCTION 'GET_DOMAIN_VALUES'
*    EXPORTING
*      DOMNAME         = LV_DOMNAME
*    TABLES
*      VALUES_TAB      = GT_CANCEL_STATUS
*    EXCEPTIONS
*      NO_VALUES_FOUND = 1
*      OTHERS          = 2.
*
*  LV_DOMNAME = 'ZDO_EINV_TYPE'.
*  CALL FUNCTION 'GET_DOMAIN_VALUES'
*    EXPORTING
*      DOMNAME         = LV_DOMNAME
*    TABLES
*      VALUES_TAB      = GT_INV_TYPE
*    EXCEPTIONS
*      NO_VALUES_FOUND = 1
*      OTHERS          = 2.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form SET_TEXT_HEADER
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM SET_TEXT_HEADER .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_STYLE_DISABLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM GET_STYLE_DISABLE .
  DATA:
        LS_STYLE LIKE LINE OF GT_STYLE_DISABLE.

  LS_STYLE-FIELDNAME = 'SELECT'.
  LS_STYLE-STYLE2     = SPACE.
  LS_STYLE-STYLE3     = SPACE.
  LS_STYLE-STYLE4     = SPACE.
  LS_STYLE-STYLE      = CL_GUI_ALV_GRID=>MC_STYLE_DISABLED.
  APPEND LS_STYLE TO GT_STYLE_DISABLE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form UNLOCK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM UNLOCK_DATA .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_DATA_FROM_DETAIL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_PROPOSAL
*&---------------------------------------------------------------------*
FORM GET_DATA_FROM_DETAIL CHANGING LS_PROPOSAL LIKE LINE OF GT_DATA_HEADER.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form LOG_WS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> P_
*&      --> LV_RESULT
*&---------------------------------------------------------------------*
FORM LOG_WS  USING    P_SM59
                      P_FUNCTION
                      P_RESULT
                      P_LONG_TEXT
  .
*  DATA:
*    LV_RESULT    TYPE STRING,
*    LV_LONG_TEXT TYPE STRING.
*
*  DATA:
*        LS_LOG TYPE ZTB_LOG_WS.
*
*  CASE P_FUNCTION.
*    WHEN GC_UPDATECUS.
*      MOVE-CORRESPONDING LS_CUS_HD TO LS_LOG.
*    WHEN GC_IMPORT_AND_PUBLISH_INV.
*      MOVE-CORRESPONDING GT_HEADER TO LS_LOG.
*    WHEN GC_TUOTRE_VN.
*    WHEN OTHERS.
*  ENDCASE.
*
*  LS_LOG-SM59_NAME = P_SM59.
*  LS_LOG-FUNCTION  = P_FUNCTION.
*  LS_LOG-RESULT_WS = P_RESULT.
*  LS_LOG-LONG_TEXT = P_LONG_TEXT.
*
*  LS_LOG-USNAM = SY-UNAME.
*  LS_LOG-CPUDT = SY-DATUM.
*  LS_LOG-CPUTM = SY-TIMLO.
*
*  IF P_SM59 NE GC_TUOTRE_VN.
*
*    DATA:
*      CO_TIMEOUT_DEFAULT TYPE I,
*      LV_SUBRC           LIKE SY-SUBRC,
*      TUOTRE_VN_CLIENT   TYPE REF TO IF_HTTP_CLIENT.
*
*    CL_HTTP_CLIENT=>CREATE_BY_DESTINATION(
*    EXPORTING
*      DESTINATION = 'TUOITRE_VN'
*      IMPORTING
*        CLIENT = TUOTRE_VN_CLIENT
*         ).
*
*    CALL METHOD TUOTRE_VN_CLIENT->SEND
*      EXPORTING
*        TIMEOUT                    = CO_TIMEOUT_DEFAULT
*      EXCEPTIONS
*        HTTP_COMMUNICATION_FAILURE = 1
*        HTTP_INVALID_STATE         = 2
*        HTTP_PROCESSING_FAILED     = 3
*        HTTP_INVALID_TIMEOUT       = 4
*        OTHERS                     = 5.
*
*    IF SY-SUBRC <> 0.
*      CALL METHOD CL_HTTP_CLIENT=>GET_LAST_ERROR(
*        IMPORTING
*          CODE    = LV_SUBRC
*          MESSAGE = LV_LONG_TEXT ).
*
*      CASE SY-SUBRC.
*        WHEN 1.
*          LV_RESULT = `Error code: ` && LV_SUBRC && SY-SUBRC && 'HTTP_COMMUNICATION_FAILURE'.
*        WHEN 2.
*          LV_RESULT = `Error code: ` && LV_SUBRC && SY-SUBRC && 'HTTP_INVALID_STATE'.
*        WHEN 3.
*          LV_RESULT = `Error code: ` && LV_SUBRC && SY-SUBRC && 'HTTP_PROCESSING_FAILED'.
*        WHEN 4.
*          LV_RESULT = `Error code: ` && LV_SUBRC && SY-SUBRC && 'OTHERS'.
*        WHEN OTHERS.
*      ENDCASE.
*    ELSE.
*      LV_RESULT = `Susscess with time out: ` && CO_TIMEOUT_DEFAULT.
*    ENDIF.
*
*    PERFORM LOG_WS USING
*          GC_TUOTRE_VN
*          P_FUNCTION
*          LV_RESULT
*          LV_LONG_TEXT
*          .
*  ENDIF.
*
*  MODIFY ZTB_LOG_WS FROM LS_LOG.
*
*  COMMIT WORK AND WAIT.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form MESSAGE_CALL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> LV_NUM
*&      --> P_
*&      --> P_
*&      --> P_
*&      --> P_
*&---------------------------------------------------------------------*
FORM MESSAGE_CALL  USING    P_ID
                            P_TYPE
                            P_LV_NUM
                            P_V1
                            P_V2
                            P_V3
                            P_V4
                            P_XML
                            P_RESULT.
*                            P_SERVER TYPE ZTB_EINV_PATTERN.
*
*  DATA:
*    LS_RETURN TYPE BAPIRET2,
*    LT_RETURN TYPE TABLE OF BAPIRET2.
*
*  CLEAR LS_RETURN.
*
** process field return-type.
*  LS_RETURN-TYPE = P_TYPE.
*
** process field id, number
*  LS_RETURN-ID = P_ID.
*  LS_RETURN-NUMBER = P_LV_NUM.
*
** process message variables
*  LS_RETURN-MESSAGE_V1 = P_V1.
*  LS_RETURN-MESSAGE_V2 = P_V2.
*  LS_RETURN-MESSAGE_V3 = P_V3.
*  LS_RETURN-MESSAGE_V4 = P_V4.
*
**  IF P_AUTO EQ '' AND LS_RETURN-TYPE EQ 'E'.
**    LS_RETURN-TYPE = 'W'.
**  ENDIF.
*
*  APPEND LS_RETURN TO GT_RETURN.
*
*  PERFORM SAVE_ZTABLE USING P_V1      "FUNC
*                            P_V2      "KEY_WORD
*                            SY-DATUM  "DATUM
*                            SY-TIMLO  "TIMLO
*                            P_XML     "XML
*                            P_RESULT
*                            P_SERVER.  "RESULTS.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form SHOW_XML
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LV_XML
*&---------------------------------------------------------------------*
FORM SHOW_XML  USING    P_LV_XML TYPE STRING.
*&---------------------------------------------------------------------*
*&      Data Declaration
*&---------------------------------------------------------------------*

  DATA:
        LW_XML TYPE CHAR01.

*  GET PARAMETER ID '/ACCGO/EXM_IND' FIELD LW_XML.
*  CHECK LW_XML = 'X'.


  DATA: GCL_XML TYPE REF TO CL_XML_DOCUMENT.

*Create XML string
  CONCATENATE 'Value' P_LV_XML INTO P_LV_XML.

  CREATE OBJECT GCL_XML.

*Parses XML String to DOM
  CALL METHOD GCL_XML->PARSE_STRING
    EXPORTING
      STREAM = P_LV_XML.

*Display XML
  CALL METHOD GCL_XML->DISPLAY.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form CONVERT_DISPLAY_AMOUNT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_DETAIL_ZZNETPRICE
*&      <-- LS_DETAIL_ZZNETPRICE_C
*&---------------------------------------------------------------------*
FORM CONVERT_DISPLAY_AMOUNT  USING    P_AMOUNT
                                      P_CURRENCY
                             CHANGING P_AMOUNT_C.

  DATA:
    INPUT  TYPE ZDE_DMBTR,
    OUTPUT TYPE ZDE_DMBTR.

  INPUT = P_AMOUNT .

  CALL FUNCTION 'ZFM_CURRENCY_AMOUNT_SAP_TO_DIS'
    EXPORTING
      CURRENCY        = P_CURRENCY
      AMOUNT_INTERNAL = INPUT
    IMPORTING
      AMOUNT_DISPLAY  = OUTPUT
    EXCEPTIONS
      INTERNAL_ERROR  = 1
      OTHERS          = 2.

  P_AMOUNT_C = OUTPUT.
  CONDENSE P_AMOUNT_C.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form SEL_ALL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM SEL_ALL .

  DATA:
    LT_FILTER TYPE LVC_T_FIDX,
    LS_FILTER TYPE LINE OF LVC_T_FIDX.

  FIELD-SYMBOLS:
                 <FS_DATA> LIKE LINE OF GT_DATA_HEADER.

*  CALL METHOD GO_ALV_H->GET_FILTERED_ENTRIES
*    IMPORTING
*      ET_FILTERED_ENTRIES = LT_FILTER.
*  IF LT_FILTER IS NOT INITIAL.
*    LOOP AT GT_DATA_HEADER ASSIGNING <FS_DATA>.
*      READ TABLE LT_FILTER INTO LS_FILTER WITH KEY TABLE_LINE = SY-TABIX.
*      IF SY-SUBRC EQ 0.
*        <FS_DATA>-SELECT = ''.
*      ELSE.
*        <FS_DATA>-SELECT = 'X'.
*      ENDIF.
*    ENDLOOP.
*  ELSE.
  LOOP AT GT_DATA_HEADER ASSIGNING <FS_DATA>.
    <FS_DATA>-SELECT = 'X'.
  ENDLOOP.
*  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form GET_PASSWORD
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_PASS
*&---------------------------------------------------------------------*
FORM GET_PASSWORD.
*  USING PS_HEADER LIKE LINE OF GT_DATA_HEADER
*  CHANGING
*        LS_PASS TYPE ZTB_KB_USER_PASS.
*
*  SELECT SINGLE *
*    INTO LS_PASS
*    FROM ZTB_KB_USER_PASS
*    WHERE GSBER  = PS_HEADER-GSBER
*      AND BUKRS  = PS_HEADER-BUKRS
*      AND VSTEL  = PS_HEADER-VSTEL
*      AND TYPES  = '01'."HÐÐT
*  IF SY-SUBRC NE 0.
*    MESSAGE E000(ZMC_EINV)
*      WITH 'User login VNPT do not define in'
*            PS_HEADER-BUKRS
*            PS_HEADER-GSBER
*            PS_HEADER-VSTEL.
*  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form USER_COMMAND_0100
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM USER_COMMAND_0100 .

  FIELD-SYMBOLS:
                 <FS_DATA> LIKE LINE OF GT_DATA_HEADER.

  IF OK_CODE IS NOT INITIAL.
    GW_OK_CODE = OK_CODE.
    CLEAR OK_CODE.
  ENDIF.

*  CLEAR GT_RETURN.
  CLEAR: GT_CUSTOMERS, GT_MESSAGES.
* User command processing
  CASE GW_OK_CODE.
    WHEN '&PUT'.
      PERFORM APPROVE USING 'X' CHANGING GT_DATA_HEADER.
    WHEN 'SALL'.
      PERFORM SEL_ALL.
    WHEN 'UALL'.
      LOOP AT GT_DATA_HEADER ASSIGNING <FS_DATA>.
        <FS_DATA>-SELECT = ''.
      ENDLOOP.
    WHEN 'BACK'
      OR 'SAVE'
      OR 'EXIT'
      OR 'CANC'.

*      PERFORM SAVE_DATA.

      IF GW_OK_CODE EQ 'BACK'.
        LEAVE TO SCREEN 0.
      ENDIF.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form AUTO_APPROVE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM AUTO_APPROVE .

  OK_CODE = 'SALL'.
  PERFORM USER_COMMAND_0100.

  OK_CODE = '&ISSUE'.
  PERFORM USER_COMMAND_0100.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form SAVE_ZTABLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> FUNC
*&      --> KEY_WORD
*&      --> DATUM
*&      --> TIMLO
*&      --> XML
*&      --> RESULTS
*&---------------------------------------------------------------------*
FORM SAVE_ZTABLE  USING    P_FUNC
                           P_KEY_WORD
                           P_DATUM
                           P_TIMLO
                           P_XML
                           P_RESULTS.
*                           P_SERVER TYPE ZTB_EINV_PATTERN.
*
*  DATA:
*    LV_START  TYPE INT4,
*    LS_ZTABLE TYPE ZTB_LOG_VNPT.
*
*  MOVE-CORRESPONDING P_SERVER TO LS_ZTABLE.
*
*  LS_ZTABLE-FUNC          = P_FUNC.
*  LS_ZTABLE-KEY_WORD      = P_KEY_WORD.
*  LS_ZTABLE-DATUM         = P_DATUM.
*  LS_ZTABLE-TIMLO         = P_TIMLO.
*  LS_ZTABLE-UNAME         = SY-UNAME.
*  LS_ZTABLE-XMLCUS_DATA1  = P_XML.
*  LV_START = LV_START + 1333.
*  IF LV_START <= STRLEN( P_XML ).
*    LS_ZTABLE-XMLCUS_DATA2  = P_XML+LV_START.
*  ENDIF.
*  LV_START = LV_START + 1333.
*  IF LV_START <= STRLEN( P_XML ).
*    LS_ZTABLE-XMLCUS_DATA3  = P_XML+LV_START.
*  ENDIF.
*  LV_START = LV_START + 1333.
*  IF LV_START <= STRLEN( P_XML ).
*    LS_ZTABLE-XMLCUS_DATA4  = P_XML+LV_START.
*  ENDIF.
*  LV_START = LV_START + 1333.
*  IF LV_START <= STRLEN( P_XML ).
*    LS_ZTABLE-XMLCUS_DATA5  = P_XML+LV_START.
*  ENDIF.
*  LV_START = LV_START + 1333.
*  IF LV_START <= STRLEN( P_XML ).
*    LS_ZTABLE-XMLCUS_DATA6  = P_XML+LV_START.
*  ENDIF.
*  LS_ZTABLE-RESULTS       = P_RESULTS .
*
*  MODIFY ZTB_LOG_VNPT FROM LS_ZTABLE.
*  COMMIT WORK AND WAIT.
ENDFORM.
**&---------------------------------------------------------------------*
**& Form EXPORT_SMARTFORMS
**&---------------------------------------------------------------------*
**& text
**&---------------------------------------------------------------------*
**&      --> PS_HEADER
**&      --> LT_DETAIL
**&---------------------------------------------------------------------*
*FORM EXPORT_SMARTFORMS
*  USING PS_HEADER LIKE LINE OF GT_DATA_HEADER
*        LT_DETAIL LIKE GT_DATA_HEADER_ITEM.
*
*  CALL FUNCTION FM_NAME
*    EXPORTING
*      CONTROL_PARAMETERS = WA_CPARAM
*      OUTPUT_OPTIONS     = WA_OUTPUT
*      USER_SETTINGS      = SPACE
*      GS_HEADER          = PS_HEADER
*    TABLES
*      GT_ITEM            = LT_DETAIL
*    EXCEPTIONS
*      FORMATTING_ERROR   = 1
*      INTERNAL_ERROR     = 2
*      SEND_ERROR         = 3
*      USER_CANCELED      = 4
*      OTHERS             = 5.
*
*
*  IF SY-SUBRC <> 0.
*    MESSAGE ID SY-MSGID TYPE SY-MSGTY NUMBER SY-MSGNO
*            WITH SY-MSGV1 SY-MSGV2 SY-MSGV3 SY-MSGV4.
*  ENDIF.
*
*
*ENDFORM.
*&---------------------------------------------------------------------*
*& Form REFRESH_DATA
*&---------------------------------------------------------------------*
*& Refresh data
*&---------------------------------------------------------------------*
FORM REFRESH_DATA .

  PERFORM INIT_PROC.
  PERFORM GET_DATA.
  PERFORM PROCESS_DATA.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form GET_ID
*&---------------------------------------------------------------------*
*& Get number range for ID
*&---------------------------------------------------------------------*
FORM GET_ID  USING LPW_NR LPW_OBJECT
                      CHANGING LPW_NUMBER.

  CALL FUNCTION 'NUMBER_GET_NEXT'
    EXPORTING
      NR_RANGE_NR             = LPW_NR
      OBJECT                  = LPW_OBJECT
    IMPORTING
      NUMBER                  = LPW_NUMBER
    EXCEPTIONS
      INTERVAL_NOT_FOUND      = 1
      NUMBER_RANGE_NOT_INTERN = 2
      OBJECT_NOT_FOUND        = 3
      QUANTITY_IS_0           = 4
      QUANTITY_IS_NOT_1       = 5
      INTERVAL_OVERFLOW       = 6
      BUFFER_OVERFLOW         = 7
      OTHERS                  = 8.
  IF SY-SUBRC <> 0.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  APPROVE
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
*  -->  p1        text
*  <--  p2        text
*----------------------------------------------------------------------*
FORM APPROVE USING TESTRUN TYPE TESTRUN
      CHANGING PT_HEADER LIKE GT_DATA_HEADER.
  DATA: LS_OUTPUT TYPE ZISD_PO_JSON.
  DATA: LT_DATA_DETAIL TYPE TABLE OF ZISD_EKPO.
  DATA: LS_JSON_DETAIL LIKE LINE OF LS_OUTPUT-LIST_DETAIL.

  LOOP AT PT_HEADER ASSIGNING FIELD-SYMBOL(<LS_DATA_HEADER>) WHERE SELECT = 'X'.

    CLEAR LS_OUTPUT.
    LS_OUTPUT-HEADER-PURCHASE_ORDER_CODE = <LS_DATA_HEADER>-EBELN.
    LS_OUTPUT-HEADER-COMPANY_CODE = <LS_DATA_HEADER>-BUKRS.
    LS_OUTPUT-HEADER-DOCUMENT_TYPE = <LS_DATA_HEADER>-BSART.
    LS_OUTPUT-HEADER-DELETION_IND = <LS_DATA_HEADER>-LOEKZ.
    LS_OUTPUT-HEADER-VENDOR_NUMBER = <LS_DATA_HEADER>-LIFNR.
    LS_OUTPUT-HEADER-PURCHASING_ORG = <LS_DATA_HEADER>-EKORG.
    LS_OUTPUT-HEADER-DOCUMENT_DATE = <LS_DATA_HEADER>-BEDAT.
    LS_OUTPUT-HEADER-RELEASE_INDICATOR = <LS_DATA_HEADER>-FRGKE.

    PERFORM GET_DATA_DETAIL USING <LS_DATA_HEADER> CHANGING LT_DATA_DETAIL.

    CLEAR LS_OUTPUT-LIST_DETAIL.
    LOOP AT LT_DATA_DETAIL INTO DATA(LS_DATA_DETAIL).
      CLEAR LS_JSON_DETAIL.
      LS_JSON_DETAIL-PURCHASE_ORDER_CODE = LS_DATA_DETAIL-EBELN.
      LS_JSON_DETAIL-PO_ITEM = LS_DATA_DETAIL-EBELP.
      LS_JSON_DETAIL-PLANT = LS_DATA_DETAIL-WERKS.
      LS_JSON_DETAIL-MATERIAL = LS_DATA_DETAIL-MATNR.
      LS_JSON_DETAIL-DELETION_IND = LS_DATA_DETAIL-LOEKZ.
      LS_JSON_DETAIL-SHORT_TEXT = LS_DATA_DETAIL-TXZ01.
      LS_JSON_DETAIL-STORAGE_LOCATION = LS_DATA_DETAIL-LGORT.
      LS_JSON_DETAIL-PO_QUANTITY = LS_DATA_DETAIL-BSTMG.
*      LS_JSON_DETAIL-ORDER_UNIT = LS_DATA_DETAIL-BSTME.

      CALL FUNCTION 'CONVERSION_EXIT_CUNIT_OUTPUT'
        EXPORTING
          INPUT  = LS_DATA_DETAIL-BSTME
        IMPORTING
          OUTPUT = LS_JSON_DETAIL-ORDER_UNIT.

      LS_JSON_DETAIL-DELIV_COMPL = LS_DATA_DETAIL-ELIKZ.
      LS_JSON_DETAIL-ACCT_ASSGMT_CAT = LS_DATA_DETAIL-KNTTP.
      LS_JSON_DETAIL-ITEM_CATEGORY = LS_DATA_DETAIL-PSTYP.
      LS_JSON_DETAIL-GOODS_RECEIPT = LS_DATA_DETAIL-WEPOS.
      LS_JSON_DETAIL-SD_DOCUMENT = LS_DATA_DETAIL-VBELN.
      LS_JSON_DETAIL-ITEM = LS_DATA_DETAIL-POSNR.
      LS_JSON_DETAIL-BASIC_DATA_TEXT = LS_DATA_DETAIL-BASICDATATEXT.
      LS_JSON_DETAIL-SO_SO = LS_DATA_DETAIL-SOSO.

      CALL FUNCTION 'CONVERSION_EXIT_ABPSP_OUTPUT'
        EXPORTING
          INPUT  = LS_DATA_DETAIL-PS_PSP_PNR
        IMPORTING
          OUTPUT = LS_JSON_DETAIL-WBS_ELEMENT.

      LS_JSON_DETAIL-CUMULATIVE_QUANTITY = LS_DATA_DETAIL-MENGE.
      APPEND LS_JSON_DETAIL TO LS_OUTPUT-LIST_DETAIL.
    ENDLOOP.

    PERFORM SEND_REQUEST_API USING LS_OUTPUT <LS_DATA_HEADER>.

  ENDLOOP.

**… CREATE INSTANCE
*  DATA:
*        LS_MESSAGE LIKE LINE OF GT_MESSAGES.
*
*  LOOP AT GT_CUSTOMERS INTO DATA(LS_CUSTOMER).
*    LS_MESSAGE-ID = 'ZCRM'.
*    LS_MESSAGE-TYPE = 'S'.
*    LS_MESSAGE-NUMBER = '005'.
*    LS_MESSAGE-MESSAGE_V1 = LS_CUSTOMER-HEADER-OBJECT_INSTANCE-KUNNR.
*    PERFORM BUILD_RETURN
*      USING LS_MESSAGE-ID LS_MESSAGE-TYPE LS_MESSAGE-NUMBER
*             LS_CUSTOMER-HEADER-OBJECT_INSTANCE-KUNNR
*             LS_MESSAGE-MESSAGE_V1
*             SPACE
*             SPACE
*             CHANGING
*               LS_MESSAGE-MESSAGE.
*    APPEND LS_MESSAGE TO GT_MESSAGES.
*  ENDLOOP.
*
*  DELETE GT_MESSAGES WHERE TYPE = 'W'.
*
*  DATA : GR_TABLE TYPE REF TO CL_SALV_TABLE.
*
*  CALL METHOD CL_SALV_TABLE=>FACTORY
*    IMPORTING
*      R_SALV_TABLE = GR_TABLE
*    CHANGING
*      T_TABLE      = GT_MESSAGES.

*… DISPLAY TABLE

*  GR_TABLE->DISPLAY( ).
ENDFORM.                    " APPROVE

*&---------------------------------------------------------------------*
*&      Form  SEND_REQUEST_API
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
*      -->P_LT_OUTPUT  text
*----------------------------------------------------------------------*
FORM SEND_REQUEST_API  USING
      PS_OUTPUT TYPE ZISD_PO_JSON
      PS_HEADER TYPE ZISD_EKKO.

  "ISDSwagger : isdcorp686868

*HTTP Client Abstraction
  DATA  LO_CLIENT TYPE REF TO IF_HTTP_CLIENT.

*Data variables for storing response in xstring and string
  DATA  : LV_XSTRING   TYPE XSTRING,
          LV_STRING    TYPE STRING,
          LV_NODE_NAME TYPE STRING.

  CLEAR : LV_XSTRING, LV_STRING, LV_NODE_NAME.

*Pass the URL to get Data
  DATA:
    LV_USERNAME TYPE STRING,
    LV_PASSWORD TYPE STRING.

  CALL FUNCTION 'ZFM_MES_GET_API'
    EXPORTING
      API_TYPE    = 'PURCHASE_ORDER'
    IMPORTING
      LV_LINKAPI  = LV_STRING
      LV_USERNAME = LV_USERNAME
      LV_PASSWORD = LV_PASSWORD.

*Creation of New IF_HTTP_Client Object
  CL_HTTP_CLIENT=>CREATE_BY_URL(
  EXPORTING
    URL                = LV_STRING
    SSL_ID = 'ANONYM'
    SAP_CLIENT = SY-MANDT
    SAP_USERNAME = SY-UNAME
  IMPORTING
    CLIENT             = LO_CLIENT
  EXCEPTIONS
    ARGUMENT_NOT_FOUND = 1
    PLUGIN_NOT_ACTIVE  = 2
    INTERNAL_ERROR     = 3
    ).

  CALL METHOD LO_CLIENT->AUTHENTICATE
    EXPORTING
      USERNAME = LV_USERNAME
      PASSWORD = LV_PASSWORD.

  IF SY-SUBRC IS NOT INITIAL.
* Handle errors
  ENDIF.

  LO_CLIENT->PROPERTYTYPE_LOGON_POPUP = LO_CLIENT->CO_DISABLED.
  LO_CLIENT->REQUEST->SET_METHOD( 'POST' ).

  DATA LV_JSON TYPE STRING.
  DATA LV_LEN TYPE INT4.

  /UI2/CL_JSON=>SERIALIZE(
    EXPORTING
      DATA        = PS_OUTPUT " Data to serialize
*      COMPRESS    = ABAP_FALSE    " Skip empty elements
*      NAME        =     " Object name
      PRETTY_NAME = ABAP_TRUE    " Pretty Print property names
*      TYPE_DESCR  =     " Data descriptor
    RECEIVING
      R_JSON      = LV_JSON   " JSON string
  ).

  REPLACE ALL OCCURRENCES OF '"TRUE"' IN LV_JSON WITH 'true'.
  REPLACE ALL OCCURRENCES OF '"FALSE"' IN LV_JSON WITH 'false'.

  LV_LEN = STRLEN( LV_JSON ).

  CALL METHOD LO_CLIENT->REQUEST->SET_CDATA " Removed APPEND_CDATA
    EXPORTING
      DATA   = LV_JSON
      OFFSET = 0
      LENGTH = LV_LEN.


  CALL METHOD LO_CLIENT->REQUEST->SET_HEADER_FIELD
    EXPORTING
      NAME  = 'Content-Type'
      VALUE = 'application/json'.

*Structure of HTTP Connection and Dispatch of Data
  LO_CLIENT->SEND( ).
  IF SY-SUBRC IS NOT INITIAL.
* Handle errors
  ENDIF.

*Receipt of HTTP Response
  LO_CLIENT->RECEIVE( ).
  IF SY-SUBRC IS NOT INITIAL.
* Handle errors
  ENDIF.


*Return the HTTP body of this entity as binary data
  LV_XSTRING = LO_CLIENT->RESPONSE->GET_DATA( ).

  CL_BCS_CONVERT=>XSTRING_TO_STRING(
      EXPORTING
        IV_XSTR   = LV_XSTRING
        IV_CP     =  4110                " SAP character set identification
      RECEIVING
        RV_STRING = LV_STRING
    ).

  DATA:  LO_PARSE     TYPE REF TO /UI5/CL_JSON_PARSER.
*   Deserialize the data
  CREATE OBJECT LO_PARSE.
*   Parse the result
  LO_PARSE->PARSE( JSON = LV_STRING ).
*   Save return values into local table
  DATA(LT_RET_DATA) = LO_PARSE->M_ENTRIES.

  DATA: LS_MES_INF_PO TYPE ZTB_MES_INF_PO.

  READ TABLE LT_RET_DATA INTO DATA(LS_RET_ISSUCCESS)
  WITH KEY NAME = 'isSuccess'.
  IF SY-SUBRC = 0.
    LS_MES_INF_PO-ISSUCCESS = LS_RET_ISSUCCESS-VALUE.
    READ TABLE LT_RET_DATA INTO DATA(LS_RET_MESSAGE)
    WITH KEY NAME = 'message'.
    IF SY-SUBRC = 0.
      LS_MES_INF_PO-MESSAGE = LS_RET_MESSAGE-VALUE.
      READ TABLE LT_RET_DATA INTO DATA(LS_RET_PURCHASEORDERID)
      WITH KEY NAME = 'purchaseOrderId'.
      IF SY-SUBRC = 0.
        LS_MES_INF_PO-PURCHASEORDERID = LS_RET_PURCHASEORDERID-VALUE.
      ENDIF.

      LS_MES_INF_PO-OBJECTCLAS = PS_HEADER-OBJECTCLAS.
      LS_MES_INF_PO-OBJECTID = PS_HEADER-OBJECTID.
      LS_MES_INF_PO-CHANGENR  = PS_HEADER-CHANGENR .
      LS_MES_INF_PO-EBELN  = PS_HEADER-EBELN .
      LS_MES_INF_PO-I_USERNAME  = SY-UNAME .
      LS_MES_INF_PO-I_UDATE     = SY-DATUM .
      LS_MES_INF_PO-I_UTIME     = SY-TIMLO .

      PS_HEADER-ISSUCCESS = LS_MES_INF_PO-ISSUCCESS.
      PS_HEADER-MESSAGE = LS_MES_INF_PO-MESSAGE.
      PS_HEADER-PURCHASEORDERID = LS_MES_INF_PO-PURCHASEORDERID.
      PS_HEADER-I_USERNAME = LS_MES_INF_PO-I_USERNAME.
      PS_HEADER-I_UDATE    = LS_MES_INF_PO-I_UDATE   .
      PS_HEADER-I_UTIME    = LS_MES_INF_PO-I_UTIME   .
      MODIFY ZTB_MES_INF_PO FROM LS_MES_INF_PO.

      UPDATE ZTB_MES_EKKO SET ZUPDATE = '' WHERE EBELN = PS_HEADER-EBELN.
      COMMIT WORK AND WAIT.

    ENDIF.
  ELSE.

    LS_MES_INF_PO-OBJECTCLAS = PS_HEADER-OBJECTCLAS.
    LS_MES_INF_PO-OBJECTID = PS_HEADER-OBJECTID.
    LS_MES_INF_PO-CHANGENR  = PS_HEADER-CHANGENR .
    LS_MES_INF_PO-EBELN  = PS_HEADER-EBELN .
    LS_MES_INF_PO-I_USERNAME  = SY-UNAME .
    LS_MES_INF_PO-I_UDATE     = SY-DATUM .
    LS_MES_INF_PO-I_UTIME     = SY-TIMLO .

    PS_HEADER-ISSUCCESS = LS_MES_INF_PO-ISSUCCESS = 'false'.
    PS_HEADER-MESSAGE = LS_MES_INF_PO-MESSAGE = 'Response is null'.
    PS_HEADER-PURCHASEORDERID = LS_MES_INF_PO-PURCHASEORDERID.
    PS_HEADER-I_USERNAME = LS_MES_INF_PO-I_USERNAME.
    PS_HEADER-I_UDATE    = LS_MES_INF_PO-I_UDATE   .
    PS_HEADER-I_UTIME    = LS_MES_INF_PO-I_UTIME   .
    MODIFY ZTB_MES_INF_PO FROM LS_MES_INF_PO.
    COMMIT WORK AND WAIT.
  ENDIF.

ENDFORM.                    " SEND_REQUEST_API

*&---------------------------------------------------------------------*
*&      Form  BUILD_RETURN
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
*      -->P_0020   text
*      -->P_0021   text
*      -->P_0022   text
*      <--P_LV_LINE  text
*----------------------------------------------------------------------*
FORM BUILD_RETURN  USING    VALUE(P_MSG)
                            VALUE(P_TYP)
                            VALUE(P_NUM)
                            VALUE(P_MS1)
                            VALUE(P_MS2)
                            VALUE(P_MS3)
                            VALUE(P_MS4)
                   CHANGING MESSAGE.

  CALL FUNCTION 'MESSAGE_TEXT_BUILD'
    EXPORTING
      MSGID               = P_MSG          "Messg class
      MSGNR               = P_NUM          "Messg No.
      MSGV1               = P_MS1
      MSGV2               = P_MS2
      MSGV3               = P_MS3
      MSGV4               = P_MS4
    IMPORTING
      MESSAGE_TEXT_OUTPUT = MESSAGE.

ENDFORM.                    " BUILD_RETURN
*&---------------------------------------------------------------------*
*&      Form  APPROVE
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
*  -->  p1        text
*  <--  p2        text
*----------------------------------------------------------------------*
FORM REJECT.
  LOOP AT GT_DATA_HEADER ASSIGNING FIELD-SYMBOL(<LS_DATA_HEADER>) WHERE SELECT = 'X'.

  ENDLOOP.

  MESSAGE 'Rejected' TYPE 'S'.
ENDFORM.                    " APPROVE
*&---------------------------------------------------------------------*
*& Form GET_DATA_DETAIL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_HEADER
*&      <-- GT_DATA_DETAIL
*&---------------------------------------------------------------------*
FORM GET_DATA_DETAIL  USING    LS_HEADER LIKE LINE OF GT_DATA_HEADER
                      CHANGING PT_DETAIL LIKE GT_DATA_DETAIL.
  DATA: LT_DETAIL LIKE PT_DETAIL.

  CLEAR PT_DETAIL.

  SELECT
    EKPO~EBELN,
    EKPO~EBELP,
    EKPO~WERKS,
    EKPO~MATNR,
    EKPO~LOEKZ,
    EKPO~TXZ01,
    EKPO~LGORT,
    EKPO~MENGE AS BSTMG,
    EKPO~MEINS AS BSTME,
    EKPO~ELIKZ,
    EKPO~KNTTP,
    EKPO~PSTYP,
    EKPO~WEPOS,
    EKPO~BANFN,
    EKKN~VBELN,
    EKKN~VBELP AS POSNR,
    EKKN~PS_PSP_PNR,
    EKBE~MENGE,
    EKBE~VGABE,
    EKBE~GJAHR,
    EKBE~BELNR,
    EKBE~BUZEI,
    EKBE~BWART
    FROM EKPO
    LEFT JOIN EKBE ON EKBE~EBELN = EKPO~EBELN AND EKBE~EBELP = EKPO~EBELP
                  AND EKBE~BWART IN ('101', '102')
    LEFT JOIN EKKN ON EKKN~EBELN = EKPO~EBELN AND EKKN~EBELP = EKPO~EBELP" AND EKKN~ZEKKN = EKBE~ZEKKN
    INTO CORRESPONDING FIELDS OF TABLE @LT_DETAIL
    WHERE EKPO~EBELN = @LS_HEADER-EBELN.
*      AND EKBE~BWART IN @S_BWART[].

*  IF S_BWART[] IS NOT INITIAL.
*    DELETE LT_DETAIL WHERE BWART NOT IN S_BWART[].
*  ENDIF.

  SORT LT_DETAIL BY EBELN
                    EBELP
                    WERKS
                    MATNR
                    LOEKZ
                    TXZ01
                    LGORT
                    BSTMG
                    BSTME
                    ELIKZ
                    KNTTP
                    PSTYP
                    WEPOS
                    .

  DATA: LS_OUTPUT LIKE LINE OF LT_DETAIL.
  LOOP AT LT_DETAIL INTO DATA(LS_DETAIL).

    LS_OUTPUT = LS_DETAIL.
    AT END OF WEPOS.
      SUM.
      LS_OUTPUT-MENGE = LS_DETAIL-MENGE.

      DATA I_TDNAME   TYPE TDOBNAME.
      I_TDNAME = LS_OUTPUT-MATNR.

      CALL FUNCTION 'ZCORE_FM_GET_LONG_TEXT'
        EXPORTING
          I_TDOBJECT  = 'MATERIAL'
          I_TDNAME    = I_TDNAME
          I_TDID      = 'GRUN'
*         I_TDSPRAS   = 'E'
        IMPORTING
          E_LONG_TEXT = LS_OUTPUT-BASICDATATEXT.

      I_TDNAME = LS_OUTPUT-BANFN.

      CALL FUNCTION 'ZCORE_FM_GET_LONG_TEXT'
        EXPORTING
          I_TDOBJECT  = 'EBANH'
          I_TDNAME    = I_TDNAME
          I_TDID      = 'B07'
*         I_TDSPRAS   = 'E'
        IMPORTING
          E_LONG_TEXT = LS_OUTPUT-SOSO.

      APPEND LS_OUTPUT TO PT_DETAIL.
    ENDAT.
  ENDLOOP.
ENDFORM.