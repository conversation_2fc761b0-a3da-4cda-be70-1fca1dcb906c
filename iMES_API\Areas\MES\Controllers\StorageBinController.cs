﻿using ISD.API.Core;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class StorageBinController : ControllerBaseAPI
    {
        // search
        #region Tìm kiếm danh sách StorageBin
        /// <summary>API Search "Tìm kiếm danh sách StorageBin"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/StorageBin/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "plant": "test",
        ///                 "sloc": "test",
        ///                 "warehouseNo": "123",
        ///                 "storageType": "",
        ///                 "storageBin": "",
        ///                 "actived": true
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///            "code": 200,
        ///            "isSuccess": true,
        ///            "message": null,
        ///            "data": [
        ///              {
        ///                "stt": 1,
        ///                "storageBinId": "74c32f98-c523-4373-8f59-f8682a58001a",
        ///                "plant": "test",
        ///                "sloc": "test",
        ///                "warehouseNo": "123",
        ///                "storageType": null,
        ///                "storageBin": null,
        ///                "createTime": "2022-07-15T12:32:43.55",
        ///                "lastEditTime": "2022-07-15T13:06:13.387",
        ///                "actived": true
        ///              }
        ///            ],
        ///            "additionalData": {
        ///              "draw": 1,
        ///              "recordsTotal": 1,
        ///              "recordsFiltered": 1
        ///            }
        ///          }
        /// </remarks>
        [HttpPost("Search")]
        public ActionResult GetStorageBin([FromBody] StorageBinSearchViewModel searchVM)
        {
            var querySearch = _unitOfWork.StorageBinRepository.GetStorageBin(searchVM.WarehouseNo, searchVM.StorageType, searchVM.StorageBin);

            //var datatblModel = new DatatableViewModel()
            //{
            //    draw = searchVM.Paging.draw,
            //    start = searchVM.Paging.start,
            //    length = searchVM.Paging.length,
            //};

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = NewCustomSearchRepository.CustomSearchFunc<StorageBinResultViewMode>(searchVM.Paging, 
                                                                                            out filteredResultsCount, 
                                                                                            out totalResultsCount, 
                                                                                            querySearch, 
                                                                                            "stt");

            if (res != null && res.Count() > 0)
            {
                int i = searchVM.Paging.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }

            return Ok(new ApiSuccessResponse<List<StorageBinResultViewMode>>
            {
                Data = res,
                Draw = searchVM.Paging.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }

        #endregion

        //Get barcode
        #region Get Barcode
        /// <summary>API Get Barcode "Lấy barcode"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/StorageBin/GetBarcode?StorageBinId={storageBinId}
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "id" : 484FAC30-C966-4304-A58B-DAEAB9C9B533
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///          {
        ///              "code": 200,
        ///              "isSuccess": true,
        ///              "message": null,
        ///              "data": "Upload/StorageBinQRcode/202207/2022-07-21T09-01-21484fac30-c966-4304-a58b-daeab9c9b533.png",
        ///              "additionalData": 
        ///              {
        ///                 "plant": "test",
        ///                 "sloc": "test",
        ///                 "warehouseNo": "123",
        ///                 "storageType": "123",
        ///                  "storageBin": "123"
        ///              }
        ///          }
        /// </remarks>
        /// 
        // RESULT:  https://localhost:49101/api/v2/MES/StorageBin/GetBarcode?StorageBinId=6ca63f2d-d0c9-4ea0-a98c-ba6058290e86&Plant=1000&Sloc=HC01
        [HttpGet("GetBarcode")]
        public async Task<ActionResult> GetBarcode(Guid StorageBinId, string Plant, string Sloc)
        {
            var queryBC = await _context.StorageBinModel.FirstOrDefaultAsync(p => p.StorageBinId == StorageBinId);
            var barCode = "";

            if (queryBC != null)
            {
                barCode = String.Format("<T1>{0}</T1><T2>{1}</T2><T3>{2}</T3><T4>{3}</T4><T5>{4}</T5><T6>{5}</T6>",
                                        queryBC.StorageBinId,
                                        Plant,
                                        Sloc,
                                        queryBC.WarehouseNo,
                                        queryBC.StorageType,
                                        queryBC.StorageBin);
                var imageBC = _unitOfWork.UtilitiesRepository.GenerateQRCode("StorageBinQRcode", StorageBinId.ToString(), barCode);

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = new List<object>() { new {
                        Plant = Plant,
                        Sloc = Sloc,
                        WarehouseNo = queryBC.WarehouseNo,
                        StorageType = queryBC.StorageType,
                        StorageBin = queryBC.StorageBin,
                        QRCode = imageBC,
                    }}
                });
            }
            return Ok(new ApiResponse
            {
                Code = 500,
                IsSuccess = false,
                Message = LanguageResource.ErrorBarCode
            });

        }
        #endregion
    }
}