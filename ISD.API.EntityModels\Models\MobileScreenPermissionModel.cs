﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MobileScreenPermissionModel", Schema = "pms")]
    public partial class MobileScreenPermissionModel
    {
        [Key]
        public Guid RolesId { get; set; }
        [Key]
        public Guid MobileScreenId { get; set; }
        [Key]
        [StringLength(50)]
        public string FunctionId { get; set; }

        [ForeignKey("FunctionId")]
        [InverseProperty("MobileScreenPermissionModel")]
        public virtual FunctionModel Function { get; set; }
        [ForeignKey("MobileScreenId")]
        [InverseProperty("MobileScreenPermissionModel")]
        public virtual MobileScreenModel MobileScreen { get; set; }
        [ForeignKey("RolesId")]
        [InverseProperty("MobileScreenPermissionModel")]
        public virtual RolesModel Roles { get; set; }
    }
}