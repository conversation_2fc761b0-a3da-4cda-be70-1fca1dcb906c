﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseProductModel", Schema = "tSale")]
    public partial class WarehouseProductModel
    {
        [Key]
        public Guid WarehouseProductId { get; set; }
        public Guid ProductId { get; set; }
        public Guid? WarehouseId { get; set; }
        public Guid? MainColorId { get; set; }
        public Guid? StyleId { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PostDate { get; set; }
        public TimeSpan? PostTime { get; set; }
        [StringLength(50)]
        public string UserPost { get; set; }

        [ForeignKey("ProductId")]
        [InverseProperty("WarehouseProductModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("StyleId")]
        [InverseProperty("WarehouseProductModel")]
        public virtual StyleModel Style { get; set; }
        [ForeignKey("WarehouseId")]
        [InverseProperty("WarehouseProductModel")]
        public virtual WarehouseModel Warehouse { get; set; }
    }
}