﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ContConfigModel", Schema = "MES")]
    public partial class ContConfigModel
    {
        [Key]
        public Guid ContConfigId { get; set; }
        [StringLength(50)]
        public string MaterialType { get; set; }
        [StringLength(500)]
        public string Plant { get; set; }
        [StringLength(1000)]
        public string Formula { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}