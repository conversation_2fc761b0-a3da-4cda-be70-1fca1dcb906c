import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../Storage/storageSharedPreferences.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/TableInfo.dart';
import '../element/timeOut.dart';
import '../model/GetBarcodeReceive.dart';
import '../model/GetStepByProductBach.dart';
import '../model/MaterialUsed.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getBatchDropdown.dart';
import '../model/getQuantityMaterialUsedShift.dart';
import '../model/materialUnused.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/GetListMaterialUsed.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/recordUnusedMaterialsFunction.dart';
import 'LostConnect.dart';

class MaterialUnused extends StatefulWidget {
  final String token;
  final String dateTimeOld;
  const MaterialUnused({Key? key, required this.token, required this.dateTimeOld}) : super(key: key);

  @override
  _MaterialUnusedState createState() => _MaterialUnusedState();
}

class _MaterialUnusedState extends State<MaterialUnused> {
  final _controllerQuantity = TextEditingController();
  final _focusQuantity = FocusNode();
  final _stageController = TextEditingController();
  final _focusStageController = FocusNode();
  bool _isLoading = false;
  bool _notWifi = false;
  bool _error = false;
  bool _checkQuantity = false;
  bool _checkSloc = false;
  bool _disableButton = false;
  bool _isLoadingQuantityLSX = false;
  bool _isLoadingSteopCode = false;
  late bool _timeOut;
  bool _isLoadingMaterial = false;
  String? _rawMaterialID;
  DataGetBarcodeReceive? _dataRawMaterial;
  List<DataMaterialUsed> _getDataMaterialUsed = [];
  List<GetBatchDropdown> _lsBatch = [];
  GetBatchDropdown? _selectedBatch;
  String? _getBatchNumberQR;
  DataMaterialUsed? _selectedMaterialUsed;
  List<DataQuantityMaterialUsedShift> _dataQuantityMaterialUsedShift = [];
  Steps? _selectedSteps;
  DataQuantityMaterialUsedShift? _selectedDataQuantityMaterialUsedShift;
  // String _unit =  "";
  // double _quantity = 0.0;
  List<Steps> _lsSteps = [];

  @override
  void initState() {
    super.initState();
    _getListMaterialUsed();
  }

  Future<void> _getListMaterialUsed() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _notWifi = false;
          _error = false;
        });
        final data = await GetListMaterialUsedFunction.getListMaterialUsed(widget.token);
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          if (data != null) {
            _getDataMaterialUsed = data;
          }
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _notWifi = true;
        _isLoading = false;
        _error = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _notWifi = false;
        _isLoading = false;
        _error = true;
        _timeOut = false;
      });
    }
  }

  void _checkError() {
    setState(() {
      if (_controllerQuantity.text.isEmpty) {
        if (_checkQuantity != true) {
          _checkQuantity = true;
        }
      } else {
        if (_checkQuantity != false) {
          _checkQuantity = false;
        }
      }
      if (_selectedDataQuantityMaterialUsedShift == null) {
        _checkSloc = true;
      } else {
        _checkSloc = false;
      }
    });
  }

  Future<void> _setStepCode(Steps? value, BuildContext context) async {
    try {
      setState(() {
        _dataQuantityMaterialUsedShift = [];
        _selectedDataQuantityMaterialUsedShift = null;
        _isLoadingQuantityLSX = true;

        _selectedSteps = value!;
      });
      final data = await RecordUnusedMaterialsFunction.fetchQuantityMaterialUsedShift(
          _dataRawMaterial != null ? _dataRawMaterial!.productCode.toString() : _selectedMaterialUsed!.productCode.toString(),
          _selectedSteps!.stepCode.toString(),
          _dataRawMaterial != null ? _dataRawMaterial!.batchNumber.toString() : _selectedBatch!.batch.toString(),
          widget.token);
      if (!mounted) return;
      setState(() {
        _isLoadingQuantityLSX = false;
      });
      if (!mounted) return;
      if (data != null) {
        setState(() {
          _dataQuantityMaterialUsedShift = data;
        });
      } else {
        setState(() {
          // _unit = "";
          // _quantity = 0.0;
          _dataQuantityMaterialUsedShift = [];
          _selectedDataQuantityMaterialUsedShift = null;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Lấy thông tin số lượng đã xuất LSX trong ngày thất bại!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 2)));
    }
  }

  void _setSloc(DataQuantityMaterialUsedShift? value) {
    setState(() {
      _selectedDataQuantityMaterialUsedShift = value;
      if (_selectedDataQuantityMaterialUsedShift != null) {
        if (_checkSloc != false) {
          _checkSloc = false;
        }
      } else {
        if (_checkSloc != true) {
          _checkSloc = true;
        }
      }
    });
  }

  void _setNVL(DataMaterialUsed? value, BuildContext context) {
    try {
      setState(() {
        _getBatchNumberQR = null;
        _dataRawMaterial = null;
        _dataQuantityMaterialUsedShift = [];
        _selectedDataQuantityMaterialUsedShift = null;
        _selectedMaterialUsed = value!;
        _lsSteps = [];
        _selectedSteps = null;
        if (_lsBatch.isNotEmpty) {
          _lsBatch = [];
        }
        if (_selectedBatch != null) {
          _selectedBatch = null;
        }
        if (_lsBatch.isNotEmpty) {
          _lsBatch = [];
        }
        List<String> getListBatch = List<String>.from(
            _getDataMaterialUsed.firstWhereOrNull((element) => element.productCode == _selectedMaterialUsed!.productCode) == null
                ? []
                : _getDataMaterialUsed.firstWhereOrNull((element) => element.productCode == _selectedMaterialUsed!.productCode)!.batchsNumber ?? []);
        if (_getDataMaterialUsed.firstWhereOrNull((element) => element.productCode == _selectedMaterialUsed!.productCode) != null) {
          for (int i = 0; i < getListBatch.length; i++) {
            GetBatchDropdown getBatchDropdown = GetBatchDropdown(batch: getListBatch[i], index: i);
            _lsBatch.add(getBatchDropdown);
          }
        }
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 2)));
    }
  }

  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }
  Future<void> _getDataRawMaterial(String materialID, BuildContext context) async {
    try {
      setState(() {
        _dataQuantityMaterialUsedShift = [];
        _selectedDataQuantityMaterialUsedShift = null;
        _selectedSteps = null;
        _lsSteps = [];
        _isLoadingMaterial = true;
      });
      final getRawMaterial = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);
      if (!mounted) return;
      if (getRawMaterial != null) {
        final dataStepCode = await RecordUnusedMaterialsFunction.fetchStepByProductBach(
            getRawMaterial.productCode.toString(), getRawMaterial.batchNumber.toString(), widget.token);
        if (dataStepCode != null) {
          if (!mounted) return;
          setState(() {
            _selectedBatch = null;
            _lsBatch = [];
            _selectedMaterialUsed = null;
            _rawMaterialID = materialID;
            _isLoadingMaterial = false;
            _getBatchNumberQR = getRawMaterial.batchNumber;
            _dataRawMaterial = getRawMaterial;
            _lsSteps = dataStepCode;
          });
        } else {
          if (!mounted) return;
          setState(() {
            _selectedBatch = null;
            _lsBatch = [];
            _selectedMaterialUsed = null;
            _rawMaterialID = null;
            _dataRawMaterial = null;
            _getBatchNumberQR = null;
            _isLoadingMaterial = false;
            _dataQuantityMaterialUsedShift = [];
            _selectedDataQuantityMaterialUsedShift = null;
            _lsSteps = [];
            _selectedSteps = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                'Không tìm thấy thông tin StepCode',
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 2)));
        }
      } else {
        if (!mounted) return;
        setState(() {
          _selectedBatch = null;
          _lsBatch = [];
          _selectedMaterialUsed = null;
          _rawMaterialID = null;
          _dataRawMaterial = null;
          _getBatchNumberQR = null;
          _isLoadingMaterial = false;
          _dataQuantityMaterialUsedShift = [];
          _selectedDataQuantityMaterialUsedShift = null;
          _lsSteps = [];
          _selectedSteps = null;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin pallet NVL!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi vui lòng thử lại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  Future<void> _setBatch(GetBatchDropdown? value, BuildContext context) async {
    try {
      setState(() {
        _lsSteps = [];
        _selectedSteps = null;
        _dataQuantityMaterialUsedShift = [];
        _selectedDataQuantityMaterialUsedShift = null;
        _selectedBatch = value!;
        _isLoadingSteopCode = true;
        // _isLoadingQuantityLSX = true;
      });
      final dataStepCode = await RecordUnusedMaterialsFunction.fetchStepByProductBach(
          _selectedMaterialUsed!.productCode.toString(), _selectedBatch!.batch.toString(), widget.token);

      if (dataStepCode != null) {
        if (!mounted) return;
        setState(() {
          _isLoadingSteopCode = false;
          // _isLoadingQuantityLSX = false;
          _lsSteps = dataStepCode;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _isLoadingQuantityLSX = false;
          _selectedSteps = null;
          _lsSteps = [];
          _dataQuantityMaterialUsedShift = [];
          _selectedDataQuantityMaterialUsedShift = null;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Không tìm thấy thông tin StepCode!',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingQuantityLSX = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }

  @override
  void dispose() {
    _controllerQuantity.dispose();
    _focusQuantity.dispose();
    _stageController.dispose();
    _focusStageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton)))
        : Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                'Ghi nhận NVL chưa sử dụng hết',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: _notWifi == true
                ? LostConnect(checkConnect: () => _getListMaterialUsed())
                : _isLoading == true
                    ? const Center(child: CircularProgressIndicator())
                    : _error == true
                        ? Center(child: Text("Ứng dụng xảy ra lỗi! vui lòng thử lại sau!", style: TextStyle(fontSize: 15.sp)))
                        : _getDataMaterialUsed.isEmpty
                            ? Center(
                                child: Center(
                                    child: Text(
                                'Không tìm thấy thông tin danh sách NVL chưa sử dụng hết',
                                style: TextStyle(fontSize: 15.sp),
                                textAlign: TextAlign.center,
                              )))
                            : SingleChildScrollView(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: REdgeInsets.all(10),
                                      decoration: const BoxDecoration(color: Colors.white),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: Container(
                                              decoration: const BoxDecoration(),
                                              child: ElevatedButton.icon(
                                                style: ButtonStyle(
                                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                  side: MaterialStateProperty.all(
                                                    const BorderSide(
                                                      color: Color(0xff303F9F),
                                                    ),
                                                  ),
                                                  backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                ),
                                                onPressed: () async {
                                                  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                  if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                    Platform.isAndroid
                                                        ? showDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                        : showCupertinoDialog(
                                                            context: context,
                                                            barrierDismissible: false,
                                                            builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                  } else {
                                                    if (_isLoadingMaterial == true) {
                                                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                          backgroundColor: Colors.black,
                                                          content: Text(
                                                            'Vui lòng chờ quá trình lấy thông tin NVL hoàn thành',
                                                            style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                          ),
                                                          duration: const Duration(seconds: 2)));
                                                    } else {
                                                      final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                                      if (!mounted) return;
                                                      if (data == null) return;
                                                      if ((data as GetBackDataQRCodePage).isScan == true) {
                                                        if (!mounted) return;
                                                        await _getDataRawMaterial(data.materialID.toString(), context);
                                                      }
                                                    }
                                                  }
                                                },
                                                icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                label: Text(
                                                  'Quét mã',
                                                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.h),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xff303F9F),
                                                      border: Border.all(
                                                        color: Colors.black,
                                                        width: 0.5.w,
                                                      ),
                                                    ),
                                                    child: Text(
                                                      "Chọn NVL:",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 6,
                                                    child: Container(
                                                      height: double.infinity,
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                      decoration: BoxDecoration(
                                                          color: const Color(0xFFFFFFFF),
                                                          border: Border(
                                                            top: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                            right: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                            bottom: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                          )),
                                                      child: Container(
                                                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                        decoration: BoxDecoration(
                                                          border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                          borderRadius: BorderRadius.circular(3.r),
                                                        ),
                                                        child: DropdownButtonHideUnderline(
                                                          child: DropdownButton<DataMaterialUsed>(
                                                            isExpanded: true,
                                                            isDense: true,
                                                            itemHeight: null,
                                                            value: _selectedMaterialUsed,
                                                            iconSize: 15.sp,
                                                            style: const TextStyle(color: Colors.white),
                                                            onChanged: _isLoadingQuantityLSX == true
                                                                ? null
                                                                : (DataMaterialUsed? value) {
                                                                    _setNVL(value, context);
                                                                  },
                                                            items: _getDataMaterialUsed.map((DataMaterialUsed materialUsed) {
                                                              return DropdownMenuItem<DataMaterialUsed>(
                                                                  value: materialUsed,
                                                                  child: Padding(
                                                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                    child: Text(
                                                                      materialUsed.productDisplay ?? " ",
                                                                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                    ),
                                                                  ));
                                                            }).toList(),
                                                            selectedItemBuilder: (BuildContext context) {
                                                              return _getDataMaterialUsed.map<Widget>((DataMaterialUsed materialUsed) {
                                                                return Text(materialUsed.productDisplay ?? " ",
                                                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                    overflow: TextOverflow.ellipsis);
                                                              }).toList();
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                    ))
                                              ],
                                            ),
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Mã NVL:",
                                            textCL2: _isLoadingMaterial == true
                                                ? "...Loading"
                                                : _dataRawMaterial != null
                                                    ? _dataRawMaterial!.productCode ?? ""
                                                    : _selectedMaterialUsed != null
                                                        ? _selectedMaterialUsed!.productCode ?? ""
                                                        : "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xFFFFFFFF,
                                          ),
                                          TableInfoNoTop(
                                            textCL1: "Tên NVL:",
                                            textCL2: _isLoadingMaterial == true
                                                ? "...Loading"
                                                : _dataRawMaterial != null
                                                    ? _dataRawMaterial!.productName ?? ""
                                                    : _selectedMaterialUsed != null
                                                        ? _selectedMaterialUsed!.productName ?? ""
                                                        : "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xFFFFFFFF,
                                          ),
                                          // TableInfoNoTop(
                                          //   textCL1:"Số lô:",
                                          //   textCL2:_getBatch == null ? "":_getBatch!.batchNumber ?? " ",
                                          //   colorCL1:0xff303F9F,
                                          //   colorCL2:0xFFFFFFFF,
                                          // ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xff303F9F),
                                                      border: Border(
                                                        left: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      "Số lô:",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 6,
                                                    child: Container(
                                                      height: double.infinity,
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                      decoration: BoxDecoration(
                                                        color: const Color(0xFFFFFFFF),
                                                        border: Border(
                                                          right: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                          bottom: BorderSide(
                                                            color: Colors.black,
                                                            width: 0.5.w,
                                                          ),
                                                        ),
                                                      ),
                                                      child: _isLoadingMaterial == true
                                                          ? Text(
                                                              "...Loading",
                                                              style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                            )
                                                          : _selectedBatch != null || _getBatchNumberQR == null
                                                              ? Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                  decoration: BoxDecoration(
                                                                    border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                                    borderRadius: BorderRadius.circular(3.r),
                                                                  ),
                                                                  child: DropdownButtonHideUnderline(
                                                                    child: DropdownButton<GetBatchDropdown>(
                                                                      isExpanded: true,
                                                                      isDense: true,
                                                                      itemHeight: null,
                                                                      value: _selectedBatch,
                                                                      iconSize: 15.sp,
                                                                      style: const TextStyle(color: Colors.white),
                                                                      onChanged: _isLoadingQuantityLSX == true
                                                                          ? null
                                                                          : (GetBatchDropdown? value) {
                                                                              _setBatch(value, context);
                                                                            },
                                                                      items: _lsBatch.map((GetBatchDropdown batch) {
                                                                        return DropdownMenuItem<GetBatchDropdown>(
                                                                            value: batch,
                                                                            child: Padding(
                                                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                              child: Text(
                                                                                batch.batch ?? "",
                                                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              ),
                                                                            ));
                                                                      }).toList(),
                                                                      selectedItemBuilder: (BuildContext context) {
                                                                        return _lsBatch.map<Widget>((GetBatchDropdown? batch) {
                                                                          return Text(batch == null ? "" : batch.batch ?? " ",
                                                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              overflow: TextOverflow.ellipsis);
                                                                        }).toList();
                                                                      },
                                                                    ),
                                                                  ),
                                                                )
                                                              : Text(
                                                                  _getBatchNumberQR ?? "",
                                                                  style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                                ),
                                                    ))
                                              ],
                                            ),
                                          ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xff303F9F),
                                                      border: Border(
                                                        left: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      "Công đoạn xuất trả:",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 6,
                                                    child: Container(
                                                      height: double.infinity,
                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                      decoration: BoxDecoration(
                                                          color: const Color(0xFFFFFFFF),
                                                          border: Border(
                                                            right: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                            bottom: BorderSide(
                                                              color: Colors.black,
                                                              width: 0.5.w,
                                                            ),
                                                          )),
                                                      child: _isLoadingMaterial == true
                                                          ? Text('...Loading', style: TextStyle(fontSize: 11.sp, color: Colors.black))
                                                          : _isLoadingSteopCode == true
                                                              ? Text('...Loading', style: TextStyle(fontSize: 11.sp, color: Colors.black))
                                                              : Container(
                                                                  padding: REdgeInsets.all(3),
                                                                  decoration: BoxDecoration(
                                                                    border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                                    borderRadius: BorderRadius.circular(3.r),
                                                                  ),
                                                                  child: DropdownButtonHideUnderline(
                                                                    child: DropdownButton<Steps>(
                                                                      isExpanded: true,
                                                                      isDense: true,
                                                                      itemHeight: null,
                                                                      value: _selectedSteps,
                                                                      iconSize: 15.sp,
                                                                      style: const TextStyle(color: Colors.white),
                                                                      onChanged: _isLoadingQuantityLSX == true
                                                                          ? null
                                                                          : (Steps? value) {
                                                                              _setStepCode(value, context);
                                                                            },
                                                                      items: _lsSteps.map((Steps steps) {
                                                                        return DropdownMenuItem<Steps>(
                                                                            value: steps,
                                                                            child: Padding(
                                                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                              child: Text(
                                                                                steps.stepCodeDisplay ?? " ",
                                                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              ),
                                                                            ));
                                                                      }).toList(),
                                                                      selectedItemBuilder: (BuildContext context) {
                                                                        return _lsSteps.map<Widget>((Steps steps) {
                                                                          return Text(steps.stepCodeDisplay ?? " ",
                                                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              overflow: TextOverflow.ellipsis);
                                                                        }).toList();
                                                                      },
                                                                    ),
                                                                  ),
                                                                ),
                                                    ))
                                              ],
                                            ),
                                          ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xff303F9F),
                                                      border: Border(
                                                        left: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      "Sloc nhận:",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                    flex: 6,
                                                    child: Container(
                                                        height: double.infinity,
                                                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                        decoration: BoxDecoration(
                                                            color: const Color(0xFFFFFFFF),
                                                            border: Border(
                                                              right: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                              bottom: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                            )),
                                                        child: _isLoadingMaterial == true
                                                            ? Text('...Loading', style: TextStyle(fontSize: 11.sp, color: Colors.black))
                                                            : Column(children: <Widget>[
                                                                Container(
                                                                  padding: REdgeInsets.all(3),
                                                                  decoration: BoxDecoration(
                                                                    border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                                    borderRadius: BorderRadius.circular(3.r),
                                                                  ),
                                                                  child: DropdownButtonHideUnderline(
                                                                    child: DropdownButton<DataQuantityMaterialUsedShift>(
                                                                      isExpanded: true,
                                                                      isDense: true,
                                                                      itemHeight: null,
                                                                      value: _selectedDataQuantityMaterialUsedShift,
                                                                      iconSize: 15.sp,
                                                                      style: const TextStyle(color: Colors.white),
                                                                      onChanged: _isLoadingMaterial == true
                                                                          ? null
                                                                          : (DataQuantityMaterialUsedShift? value) {
                                                                              // _setStepCode(value,context);
                                                                              _setSloc(value);
                                                                            },
                                                                      items: _dataQuantityMaterialUsedShift.map((DataQuantityMaterialUsedShift data) {
                                                                        return DropdownMenuItem<DataQuantityMaterialUsedShift>(
                                                                            value: data,
                                                                            child: Padding(
                                                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                              child: Text(
                                                                                data.slocDisplay ?? " ",
                                                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              ),
                                                                            ));
                                                                      }).toList(),
                                                                      selectedItemBuilder: (BuildContext context) {
                                                                        return _dataQuantityMaterialUsedShift
                                                                            .map<Widget>((DataQuantityMaterialUsedShift data) {
                                                                          return Text(data.slocDisplay ?? " ",
                                                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                              overflow: TextOverflow.ellipsis);
                                                                        }).toList();
                                                                      },
                                                                    ),
                                                                  ),
                                                                ),
                                                                SizedBox(height: _checkSloc == true ? 5.h : 0),
                                                                ContainerError.widgetError(_checkSloc, 'Vui lòng chọn sloc'),
                                                              ])))
                                              ],
                                            ),
                                          ),

                                          TableInfoNoTop3T(
                                            textCL1: "Số lượng đã xuất vào LSX trong ngày:",
                                            textCL2: _selectedDataQuantityMaterialUsedShift == null
                                                ? ""
                                                : _selectedDataQuantityMaterialUsedShift!.quantity == null
                                                    ? ""
                                                    : _selectedDataQuantityMaterialUsedShift!.quantity.toString(),
                                            textCL3: _selectedDataQuantityMaterialUsedShift == null
                                                ? ""
                                                : _selectedDataQuantityMaterialUsedShift!.unit ?? "",
                                            colorCL1: 0xff303F9F,
                                            colorCL2: 0xFFFFFFFF,
                                            colorCL3: 0xFFFFFFFF,
                                          ),
                                          IntrinsicHeight(
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xff303F9F),
                                                      border: Border(
                                                        left: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      "Số lượng trả:",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 4,
                                                  child: Container(
                                                    height: double.infinity,
                                                    alignment: Alignment.centerRight,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xFFFFFFFF),
                                                      border: Border(
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Container(
                                                      padding: REdgeInsets.all(3),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        focusNode: _focusQuantity,
                                                        textAlign: TextAlign.center,
                                                        controller: _controllerQuantity,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                        inputFormatters: <TextInputFormatter>[
                                                          FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d*')),
                                                          CommaTextInputFormatter()
                                                        ],
                                                        decoration: InputDecoration(
                                                          border: InputBorder.none,
                                                          isDense: true,
                                                          contentPadding: EdgeInsets.zero,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          filled: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                        ),
                                                        onChanged: (value) {
                                                          if (value.isEmpty) {
                                                            if (_checkQuantity != true) {
                                                              setState(() {
                                                                _checkQuantity = true;
                                                              });
                                                            }
                                                          } else {
                                                            if (_checkQuantity != false) {
                                                              setState(() {
                                                                _checkQuantity = false;
                                                              });
                                                            }
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  // Container(
                                                  //   height: double.infinity,
                                                  //   alignment: Alignment.centerRight,
                                                  //   padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                  //   decoration: BoxDecoration(
                                                  //     color: const Color(0xFFFFFFFF),
                                                  //     border: Border(
                                                  //       right: BorderSide(
                                                  //         color: Colors.black,
                                                  //         width: 0.5.w,
                                                  //       ),
                                                  //       bottom: BorderSide(
                                                  //         color: Colors.black,
                                                  //         width: 0.5.w,
                                                  //       ),
                                                  //     ),
                                                  //   ),
                                                  //   child: TextFormField(
                                                  //     focusNode: _focusQuantity,
                                                  //     textAlign: TextAlign.right,
                                                  //     controller: _controllerQuantity,
                                                  //     style: TextStyle(fontSize: 12.sp),
                                                  //     decoration: InputDecoration(
                                                  //       border: InputBorder.none,
                                                  //       focusedBorder: OutlineInputBorder(
                                                  //         borderRadius:
                                                  //         BorderRadius.circular(3.r),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: Colors.grey.shade400),
                                                  //       ),
                                                  //       enabledBorder: OutlineInputBorder(
                                                  //         borderRadius:
                                                  //         BorderRadius.circular(3.r),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: Colors.grey.shade400),
                                                  //       ),
                                                  //       errorBorder: InputBorder.none,
                                                  //       disabledBorder: InputBorder.none,
                                                  //       filled: true,
                                                  //       isDense: true,
                                                  //       fillColor: Colors.white,
                                                  //       hintStyle:
                                                  //       TextStyle(fontSize: 12.sp),
                                                  //       contentPadding:
                                                  //       EdgeInsets.symmetric(
                                                  //           horizontal: 20.w),
                                                  //     ),
                                                  //   ),
                                                  // ),
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: Container(
                                                    height: double.infinity,
                                                    alignment: Alignment.centerLeft,
                                                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xFFFFFFFF),
                                                      border: Border(
                                                        right: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                        bottom: BorderSide(
                                                          color: Colors.black,
                                                          width: 0.5.w,
                                                        ),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      _selectedDataQuantityMaterialUsedShift == null
                                                          ? ""
                                                          : _selectedDataQuantityMaterialUsedShift!.unit ?? "",
                                                      style: TextStyle(fontSize: 11.sp, color: Colors.black),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(height: _checkQuantity ? 5.h : 0),
                                          ContainerError.widgetError(_checkQuantity, 'Vui lòng nhập số lượng trả'),
                                          SizedBox(height: 10.h),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Padding(
                                      padding: REdgeInsets.symmetric(horizontal: 10.w),
                                      child: Container(
                                        width: double.infinity,
                                        decoration: const BoxDecoration(),
                                        child: ElevatedButton(
                                          style: ButtonStyle(
                                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                            side: MaterialStateProperty.all(
                                              BorderSide(
                                                color: (_dataRawMaterial != null || (_selectedMaterialUsed != null && _selectedBatch != null)) &&
                                                        _selectedSteps != null &&
                                                        _selectedDataQuantityMaterialUsedShift != null
                                                    ? _isLoadingQuantityLSX == false && _isLoadingMaterial == false
                                                        ? const Color(0xff303F9F)
                                                        : Colors.grey.shade300
                                                    : Colors.grey.shade300,
                                              ),
                                            ),
                                            backgroundColor: MaterialStateProperty.all(
                                                (_dataRawMaterial != null || (_selectedMaterialUsed != null && _selectedBatch != null)) &&
                                                        _selectedSteps != null &&
                                                        _selectedDataQuantityMaterialUsedShift != null
                                                    ? _isLoadingQuantityLSX == false && _isLoadingMaterial == false
                                                        ? const Color(0xff303F9F)
                                                        : Colors.grey.shade300
                                                    : Colors.grey.shade300),
                                          ),
                                          onPressed: (_dataRawMaterial != null || (_selectedMaterialUsed != null && _selectedBatch != null)) &&
                                                  _selectedSteps != null &&
                                                  _selectedDataQuantityMaterialUsedShift != null
                                              ? _isLoadingQuantityLSX == false && _isLoadingMaterial == false
                                                  ? () {
                                                      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                        Platform.isAndroid
                                                            ? showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                            : showCupertinoDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                      } else {
                                                        _checkError();
                                                        if (_checkQuantity == false && _checkSloc == false) {
                                                          FocusScope.of(context).unfocus();
                                                          MaterialUnusedModel materialUnused = MaterialUnusedModel(
                                                              rawMaterialCardId: _dataRawMaterial != null ? _rawMaterialID : null,
                                                              slocId: _selectedDataQuantityMaterialUsedShift!.slocId,
                                                              productCode: _dataRawMaterial != null
                                                                  ? _dataRawMaterial!.productCode
                                                                  : _selectedMaterialUsed!.productCode,
                                                              batchNumber:
                                                                  _dataRawMaterial != null ? _dataRawMaterial!.batchNumber : _selectedBatch!.batch,
                                                              quantity: double.parse(_controllerQuantity.text),
                                                              unit: _selectedDataQuantityMaterialUsedShift!.unit.toString(),
                                                              stepCode: _selectedSteps!.stepCode.toString());
                                                          // debugPrint(jsonEncode(materialUnused));
                                                          RecordUnusedMaterialsFunction.getMessasgeMaterialUnusedShift(
                                                              materialUnused, widget.token, context);
                                                        }
                                                      }
                                                    }
                                                  : null
                                              : null,
                                          child: Container(
                                            margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                            child: Text(
                                              "Lưu",
                                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
          );
  }
}
