import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import '../model/userModel.dart';
import '../model/poInfo.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../utils/ui_helpers.dart';
import 'ImportProduct.dart'; // Import ScannedProduct from ImportProduct.dart

class ContinuousQRScannerPage extends StatefulWidget {
  final String token;
  final Permission permission;
  final String plant;
  final String dateTimeOld;
  final String batch;
  final String location;
  final List<ScannedProduct> scannedProducts;
  final Function(ScannedProduct) onProductScanned;
  final Map<String, POInfo>? poInfoCache;

  const ContinuousQRScannerPage({
    Key? key,
    required this.token,
    required this.permission,
    required this.plant,
    required this.dateTimeOld,
    required this.batch,
    required this.location,
    required this.scannedProducts,
    required this.onProductScanned,
    this.poInfoCache,
  }) : super(key: key);

  @override
  _ContinuousQRScannerPageState createState() => _ContinuousQRScannerPageState();
}

class _ContinuousQRScannerPageState extends State<ContinuousQRScannerPage> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool _isFlashOn = false;
  bool _isProcessing = false;
  String _lastScannedCode = '';
  String? _errorMessage;
  bool _isCameraInitialized = false;

  // Counter for items scanned in this session
  int _sessionScanCount = 0;

  // Cooldown timer to prevent too frequent scans
  DateTime _lastScanTime = DateTime.now();
  static const _scanCooldown = Duration(milliseconds: 1500);

  // Cache for PO information to avoid redundant API calls
  late Map<String, POInfo> _poInfoCache;

  // Map to track PO quantities
  Map<String, int> _poQuantities = {};

  @override
  void initState() {
    super.initState();
    // Initialize the cache from the widget or create a new one
    _poInfoCache = widget.poInfoCache?.cast<String, POInfo>() ?? {};
    debugPrint('ContinuousQRScannerPage initialized with cache size: ${_poInfoCache.length}');

    // Initialize the PO quantities from the cache
    for (var entry in _poInfoCache.entries) {
      _poQuantities[entry.value.po] = entry.value.quantity;
    }

    // Request vibration permission on app start
    _requestVibrationPermission();
  }

  // Request vibration permission
  Future<void> _requestVibrationPermission() async {
    try {
      // This is a dummy call to trigger the permission request on some devices
      await HapticFeedback.vibrate();
      debugPrint('Vibration permission requested');
    } catch (e) {
      debugPrint('Error requesting vibration permission: $e');
    }
  }

  // Provide success feedback with sound and vibration
  Future<void> _provideSuccessFeedback() async {
    try {
      debugPrint('Playing success feedback');

      // Multiple vibration patterns for stronger feedback
      HapticFeedback.mediumImpact();

      // Add a second vibration with a delay
      await Future.delayed(const Duration(milliseconds: 50));
      HapticFeedback.vibrate();

      // Play system sound
      SystemSound.play(SystemSoundType.click);

      // Use native method channel for Android
      if (Platform.isAndroid) {
        const platform = MethodChannel('com.ttf.mes/sound');
        try {
          await platform.invokeMethod('playClickSound');
          await platform.invokeMethod('vibrate');
        } catch (e) {
          debugPrint('Failed to play sound via method channel: $e');
        }
      }

      debugPrint('Success feedback completed');
    } catch (e) {
      debugPrint('Error in success feedback: $e');
    }
  }

  // Provide error feedback with sound and vibration
  Future<void> _provideErrorFeedback() async {
    try {
      debugPrint('Playing error feedback');

      // Multiple vibration patterns for stronger feedback
      HapticFeedback.heavyImpact();

      // Add a second vibration with a delay
      await Future.delayed(const Duration(milliseconds: 100));
      HapticFeedback.vibrate();

      // Add a third vibration for emphasis
      await Future.delayed(const Duration(milliseconds: 200));
      HapticFeedback.vibrate();

      // Play system sound
      SystemSound.play(SystemSoundType.alert);

      // Use native method channel for Android
      if (Platform.isAndroid) {
        const platform = MethodChannel('com.ttf.mes/sound');
        try {
          await platform.invokeMethod('playAlertSound');
          await platform.invokeMethod('vibrate');
        } catch (e) {
          debugPrint('Failed to play sound via method channel: $e');
        }
      }

      debugPrint('Error feedback completed');
    } catch (e) {
      debugPrint('Error in error feedback: $e');
    }
  }

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    }
    controller!.resumeCamera();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        await controller?.pauseCamera();
        // Return the updated cache and PO quantities when navigating back
        debugPrint('Returning cache with ${_poInfoCache.length} entries to ImportProduct page');
        Navigator.pop(context, {
          'poInfoCache': _poInfoCache,
          'poQuantities': _poQuantities,
        });
        return false;
      },
      child: Scaffold(
        body: Column(
          children: [
            // Top half: Camera view
            Expanded(
              flex: 5,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  _buildQrView(context),
                  Positioned(
                    top: 40.h,
                    left: 16.w,
                    child: IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.white, size: 24.sp),
                      onPressed: () async {
                        await controller?.pauseCamera();
                        // Return the updated cache and PO quantities when navigating back
                        debugPrint('Returning cache with ${_poInfoCache.length} entries to ImportProduct page');
                        Navigator.pop(context, {
                          'poInfoCache': _poInfoCache,
                          'poQuantities': _poQuantities,
                        });
                      },
                    ),
                  ),
                  Positioned(
                    top: 40.h,
                    right: 16.w,
                    child: IconButton(
                      icon: Icon(
                        _isFlashOn ? Icons.flash_on : Icons.flash_off,
                        color: Colors.white,
                        size: 24.sp,
                      ),
                      onPressed: () async {
                        await controller?.toggleFlash();
                        setState(() {
                          _isFlashOn = !_isFlashOn;
                        });
                      },
                    ),
                  ),
                  // Test sound button
                  Positioned(
                    top: 40.h,
                    right: 60.w,
                    child: IconButton(
                      icon: Icon(Icons.volume_up, color: Colors.white, size: 24.sp),
                      onPressed: () async {
                        // Test sound and vibration
                        _provideSuccessFeedback();
                        showToast(
                          context: context,
                          message: "Đang kiểm tra âm thanh và rung",
                          duration: 1,
                        );
                      },
                    ),
                  ),
                  // Session scan counter badge
                  if (_sessionScanCount > 0)
                    Positioned(
                      top: 90.h,
                      right: 16.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: Colors.green.shade700,
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle, color: Colors.white, size: 16.sp),
                            SizedBox(width: 4.w),
                            Text(
                              '$_sessionScanCount',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (_isProcessing)
                    Container(
                      width: 60.w,
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                  if (_errorMessage != null)
                    Positioned(
                      bottom: 20.h,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.white, fontSize: 14.sp),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Bottom half: Scanned items list
            Expanded(
              flex: 5,
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                      color: const Color(0xff0052cc),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'LSX đã thêm (${widget.scannedProducts.length})',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: const Color(0xff0052cc),
                              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                            ),
                            onPressed: () async {
                              await controller?.pauseCamera();
                              // Return the updated cache and PO quantities when navigating back
                              debugPrint('Returning cache with ${_poInfoCache.length} entries to ImportProduct page');
                              Navigator.pop(context, {
                                'poInfoCache': _poInfoCache,
                                'poQuantities': _poQuantities,
                              });
                            },
                            child: Text(
                              'Hoàn tất',
                              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: widget.scannedProducts.isEmpty
                          ? Center(
                              child: Text(
                                'Chưa có LSX nào được thêm',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                            )
                          : ListView.builder(
                              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
                              itemCount: widget.scannedProducts.length,
                              itemBuilder: (context, index) {
                                // Display most recent scans at the top
                                final product = widget.scannedProducts[widget.scannedProducts.length - 1 - index];
                                return Container(
                                  margin: EdgeInsets.symmetric(horizontal: 14.w, vertical: 2.h),
                                  padding: EdgeInsets.all(3.w),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(color: Colors.grey[300]!),
                                  ),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(6.w),
                                        decoration: BoxDecoration(
                                          color: const Color(0xff0052cc).withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(8.r),
                                        ),
                                        child: Icon(
                                          Icons.qr_code,
                                          size: 24.sp,
                                          color: const Color(0xff0052cc),
                                        ),
                                      ),
                                      SizedBox(width: 12.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              product.productName,
                                              style: TextStyle(
                                                fontSize: 14.sp,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            SizedBox(height: 2.h),
                                            Text(
                                              'Mã: ${product.productCode}',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: Colors.grey[700],
                                              ),
                                            ),
                                            SizedBox(height: 1.h),
                                            Text(
                                              'LSX: ${product.sapOrder}',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: Colors.grey[700],
                                              ),
                                            ),
                                            SizedBox(height: 1.h),
                                            Text(
                                              'Số lượng: ${product.importQuantity}/${product.totalQuantity}',
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: Colors.grey[700],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderColor: Colors.red,
        borderRadius: 10.r,
        borderLength: 30,
        borderWidth: 10,
        cutOutSize: 250.w,
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      this.controller = controller;
      _isCameraInitialized = true;
    });

    if (Platform.isAndroid) {
      controller.resumeCamera();
    }

    controller.scannedDataStream.listen((scanData) async {
      // Implement cooldown to prevent too frequent scans
      final now = DateTime.now();
      if (now.difference(_lastScanTime) < _scanCooldown) {
        return;
      }
      _lastScanTime = now;

      // Skip if already processing a code or if it's the same as the last one
      if (_isProcessing || scanData.code == _lastScannedCode) {
        return;
      }

      final code = scanData.code;
      if (code == null || code.isEmpty) {
        return;
      }

      // Extract PO from serial to check for duplicates
      String productionOrder = code.length > 5 ? code.substring(0, code.length - 5) : code;

      // Check if this PO has already been scanned
      bool isDuplicate = widget.scannedProducts.any((product) => product.sapOrder == productionOrder);
      if (isDuplicate) {
        setState(() {
          _errorMessage = "LSX này đã được thêm vào danh sách";
        });

        // Provide error feedback with sound and vibration
        _provideErrorFeedback();

        // Clear error message after a delay
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _errorMessage = null;
            });
          }
        });

        return;
      }

      setState(() {
        _isProcessing = true;
        _lastScannedCode = code;
        _errorMessage = null;
      });

      try {
        debugPrint('Extracting Production Order from $code: $productionOrder');

        // Check if we already have this production order in cache
        POInfo? poInfo;
        if (_poInfoCache.containsKey(productionOrder)) {
          debugPrint('Cache HIT for Production Order: $productionOrder');
          poInfo = _poInfoCache[productionOrder];

          // Make sure we update the PO quantities map
          if (poInfo != null) {
            _poQuantities[poInfo.po] = poInfo.quantity;
            debugPrint('Using cached quantity for ${poInfo.po}: ${poInfo.quantity}');
          }
        } else {
          // Fetch product information if not in cache
          debugPrint('Cache MISS for Production Order: $productionOrder - Fetching from API');
          poInfo = await ImportWareHouseFunction.fetchPOInfoBySerial(code, widget.token);

          // Cache the result for future use
          if (poInfo != null) {
            _poInfoCache[productionOrder] = poInfo;
            _poQuantities[poInfo.po] = poInfo.quantity;
            debugPrint('Added to cache: $productionOrder -> ${poInfo.po} (${poInfo.productName})');
            debugPrint('Storing quantity for ${poInfo.po}: ${poInfo.quantity}');
          }
        }

        if (poInfo != null) {
          // Double-check for duplicates using the actual PO from API
          bool isDuplicateActualPO = widget.scannedProducts.any((product) => product.sapOrder == poInfo!.po);
          if (isDuplicateActualPO) {
            setState(() {
              _errorMessage = "LSX ${poInfo!.po} đã được thêm vào danh sách";
            });

            // Provide error feedback with sound and vibration
            _provideErrorFeedback();

            // Clear error message after a delay
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                setState(() {
                  _errorMessage = null;
                });
              }
            });

            return;
          }
          final newProduct = ScannedProduct(
            sapOrder: poInfo.po,
            productCode: poInfo.productCode,
            productName: poInfo.productName,
            batch: widget.batch,
            location: widget.location,
            so: poInfo.so ?? "",
            soItem: poInfo.soItem ?? "10",
            wbs: poInfo.wbs ?? "",
            unit: "CAI",
            importQuantity: 1, // Default quantity when scanned
            totalQuantity: poInfo.totalQuantity,
            importedQuantity: poInfo.importedQuantity,
          );

          // Call the callback to add the product to the parent's state
          widget.onProductScanned(newProduct);

          // Increment session scan count
          setState(() {
            _sessionScanCount++;
          });

          // Provide success feedback with sound and vibration
          _provideSuccessFeedback();

          // Show a toast message for additional feedback
          showToast(
            context: context,
            message: "Đã thêm LSX: ${poInfo.po}",
            duration: 1,
          );
        } else {
          setState(() {
            _errorMessage = "Không tìm thấy thông tin sản phẩm";
          });

          // Provide error feedback with sound and vibration
          _provideErrorFeedback();
        }
      } catch (error) {
        setState(() {
          _errorMessage = "Lỗi: ${error.toString()}";
        });

        // Provide error feedback with sound and vibration
        _provideErrorFeedback();
      } finally {
        setState(() {
          _isProcessing = false;
        });

        // Clear error message after a delay
        if (_errorMessage != null) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              setState(() {
                _errorMessage = null;
              });
            }
          });
        }
      }
    });
  }
}
