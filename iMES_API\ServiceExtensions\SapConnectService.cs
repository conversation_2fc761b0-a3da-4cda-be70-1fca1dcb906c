﻿using Microsoft.Extensions.Configuration;
using Microsoft.Graph.ExternalConnectors;
using SapNwRfc;
using System.Configuration;
using System.Collections.Generic;
using iMES_API.ViewModel.SAP;

namespace iMES_API.ServiceExtensions
{
    public class SapConnectService
    {
        private IConfiguration _configuration;

        public SapConnectService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public SapConnection GetSapConnection()
        {
            var sapConfig = _configuration.GetSection("SAP:PRD").Get<SAPConfiguration>();

            string connectionString =
            $"AppServerHost={sapConfig.SAPAppServerHost};" +
            $"SystemNumber={sapConfig.SAPSystemNumber};" +
            $"User={sapConfig.SAPUsername};" +
            $"Password={sapConfig.SAPPassword};" +
            $"Client={sapConfig.SAPClient};" +
            $"Language={sapConfig.SAPLanguage};" +
            $"PoolSize={sapConfig.SAPMaxPoolSize};";
            var connection = new SapConnection(connectionString);
            connection.Connect();

            return connection;
        }
    }
}
