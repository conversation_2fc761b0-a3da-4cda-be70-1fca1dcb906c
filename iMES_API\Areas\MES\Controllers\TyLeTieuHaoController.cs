using AutoMapper;
using ISD.API.Core;
using ISD.API.ViewModels.MES;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using System.Net;
using ISD.API.Repositories;
using Microsoft.Extensions.Logging;
using ISD.API.Extensions;
using ISD.API.ViewModels;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class TyLeTieuHaoController : ControllerBaseAPI
    {
        private readonly IMapper _mapper;
        private readonly ITyLeTieuHaoRepository _tyLeTieuHaoRepository;
        private readonly ILogger<TyLeTieuHaoController> _logger;

        public TyLeTieuHaoController(
            IMapper mapper,
            ITyLeTieuHaoRepository tyLeTieuHaoRepository,
            ILogger<TyLeTieuHaoController> logger)
        {
            _mapper = mapper;
            _tyLeTieuHaoRepository = tyLeTieuHaoRepository;
            _logger = logger;
        }

        [HttpPost("List")]
        public async Task<IActionResult> GetList([FromBody] TyLeTieuHaoSearchModel searchModel)
        {
            try
            {
                var records = await _tyLeTieuHaoRepository.GetTyLeTieuHaoListAsync(searchModel);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = records
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetList");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id, [FromQuery] string companyCode = null)
        {
            try
            {
                var record = await _tyLeTieuHaoRepository.GetTyLeTieuHaoByIdAsync(id, companyCode);

                if (record == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = "Record not found"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetById: {id}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] TyLeTieuHaoPostViewModel model)
        {
            _logger.LogDebug($"Create API called for CompanyCode: {model.CompanyCode}");
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Create called with invalid model state.");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid model state"
                    });
                }

                if (string.IsNullOrEmpty(model.CompanyCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "CompanyCode is required"
                    });
                }

                var record = await _tyLeTieuHaoRepository.CreateTyLeTieuHaoAsync(model, CurrentUser.AccountId ?? Guid.Empty);

                _logger.LogDebug($"Create succeeded for CompanyCode: {model.CompanyCode}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Tạo mới thành công",
                    Data = record
                });
            }
            catch (AutoMapper.AutoMapperMappingException mapEx)
            {
                _logger.LogError($"Mapping error in Create: {mapEx}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = "Error mapping data. Please check model configuration."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in Create: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] TyLeTieuHaoPostViewModel model)
        {
            try
            {
                var record = await _tyLeTieuHaoRepository.UpdateTyLeTieuHaoAsync(id, model, CurrentUser.AccountId ?? Guid.Empty);

                if (record == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = "Record not found"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Updated successfully",
                    Data = record
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in Update: {id}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }
        }

        [HttpGet("LSXSAPSuggestions")]
        public async Task<IActionResult> GetLSXSAPSuggestions([FromQuery] string query, [FromQuery] string companyCode)
        {
            try
            {
                var suggestions = await _tyLeTieuHaoRepository.GetLSXSAPSuggestionsAsync(query, companyCode);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = suggestions
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetLSXSAPSuggestions");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }
        }

        [HttpGet("MaterialSuggestions")]
        public async Task<IActionResult> GetMaterialSuggestions([FromQuery] string productCode, [FromQuery] string companyCode, [FromQuery] string query = "")
        {
            try
            {
                var suggestions = await _tyLeTieuHaoRepository.GetMaterialSuggestionsAsync(productCode, companyCode, query);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = suggestions
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMaterialSuggestions");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = ex.Message
                });
            }
        }
    }
}
