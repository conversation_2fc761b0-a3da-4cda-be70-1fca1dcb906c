class GetInventoryMaterialSloc {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataInventoryMaterialSloc>? data;

  GetInventoryMaterialSloc(
      {this.code, this.isSuccess, this.message, this.data});

  GetInventoryMaterialSloc.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataInventoryMaterialSloc>[];
      json['data'].forEach((v) {
        data!.add(DataInventoryMaterialSloc.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataInventoryMaterialSloc {
  num? quantity;
  String? sowbs;
  String? batch;

  DataInventoryMaterialSloc({this.quantity, this.sowbs, this.batch});

  DataInventoryMaterialSloc.fromJson(Map<String, dynamic> json) {
    quantity = json['quantity'];
    sowbs = json['sowbs'];
    batch = json['batch'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantity'] = quantity;
    data['sowbs'] = sowbs;
    data['batch'] = batch;
    return data;
  }
}