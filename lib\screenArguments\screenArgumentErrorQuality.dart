import 'dart:io';

import '../model/lsSendQualityControlInformation.dart';
import '../model/qualityControlApi.dart';
import '../model/sendQualityControlDetail.dart';

class ScreenArgumentErrorQuality {
  final String token;
  final QualityControl? qualityControl;
  final List<Error>? lsError;
  final List<ErrorList>? lsErrorList;
  final SendQualityControlDetail? getSendQualityControlDetail;
  final QualityCheckerInfo? selectedStaff;
  final QualityTypeList? selectedType;
  final String pO;
  final String quantityCheck;
  final ResultList? selectedResult;
  final List<LsSendQualityControlInformation> getLsSendQualityControlInformation;
  final List<File> lsFileTabCheck;
  final String formatDatePost;
  final String dateTimeOld;
  ScreenArgumentErrorQuality(
      this.token,
      this.qualityControl,
      this.lsError,
      this.lsErrorList,
      this.getSendQualityControlDetail,
      this.selectedStaff,
      this.selectedType,
      this.pO,
      this.quantityCheck,
      this.selectedResult,
      this.getLsSendQualityControlInformation,
      this.lsFileTabCheck,
      this.formatDatePost,
      this.dateTimeOld);
}
