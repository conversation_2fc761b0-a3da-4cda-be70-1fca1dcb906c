class ProductionRecordTTF {
  int? code;
  bool? isSuccess;
  String? message;
  Data? data;
  String? additionalData;

  ProductionRecordTTF({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  ProductionRecordTTF.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class Data {
  List<UsageQuantity>? usageQuantity;
  ProductionRecordRouting? productionRecordRouting;
  ProductionRecord? productionRecord;
  List<ListStepCode>? listStepCode;

  Data({this.usageQuantity, this.productionRecordRouting, this.productionRecord, this.listStepCode});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['usageQuantity'] != null) {
      usageQuantity = <UsageQuantity>[];
      json['usageQuantity'].forEach((v) {
        usageQuantity!.add(UsageQuantity.fromJson(v));
      });
    }
    productionRecordRouting = json['productionRecordRouting'] != null ? ProductionRecordRouting.fromJson(json['productionRecordRouting']) : null;
    productionRecord = json['productionRecord'] != null ? ProductionRecord.fromJson(json['productionRecord']) : null;
    if (json['listStepCode'] != null) {
      listStepCode = <ListStepCode>[];
      json['listStepCode'].forEach((v) {
        listStepCode!.add(ListStepCode.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (usageQuantity != null) {
      data['usageQuantity'] = usageQuantity!.map((v) => v.toJson()).toList();
    }
    if (productionRecordRouting != null) {
      data['productionRecordRouting'] = productionRecordRouting!.toJson();
    }
    if (productionRecord != null) {
      data['productionRecord'] = productionRecord!.toJson();
    }
    if (listStepCode != null) {
      data['listStepCode'] = listStepCode!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class UsageQuantity {
  String? productAttributes;
  String? productName;
  String? itmno;
  String? ktext;
  String? poT12;
  String? bmschdc;
  double? bmsch;
  double? quantity;
  String? listITMNO;

  UsageQuantity(
      {this.productAttributes, this.productName, this.itmno, this.ktext, this.poT12, this.bmschdc, this.bmsch, this.quantity, this.listITMNO});

  UsageQuantity.fromJson(Map<String, dynamic> json) {
    productAttributes = json['productAttributes'];
    productName = json['productName'];
    itmno = json['itmno'];
    ktext = json['ktext'];
    poT12 = json['poT12'];
    bmschdc = json['bmschdc'];
    bmsch = json['bmsch'];
    quantity = json['quantity'];
    listITMNO = json['listITMNO'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productAttributes'] = productAttributes;
    data['productName'] = productName;
    data['itmno'] = itmno;
    data['ktext'] = ktext;
    data['poT12'] = poT12;
    data['bmschdc'] = bmschdc;
    data['bmsch'] = bmsch;
    data['quantity'] = quantity;
    data['listITMNO'] = listITMNO;
    return data;
  }
}

class ProductionRecordRouting {
  double? quantityDLD;
  double? quantityDLKD;
  String? productAttributes;

  ProductionRecordRouting({this.quantityDLD, this.quantityDLKD, this.productAttributes});

  ProductionRecordRouting.fromJson(Map<String, dynamic> json) {
    quantityDLD = json['quantity_DLD'];
    quantityDLKD = json['quantity_DLKD'];
    productAttributes = json['productAttributes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantity_DLD'] = quantityDLD;
    data['quantity_DLKD'] = quantityDLKD;
    data['productAttributes'] = productAttributes;
    return data;
  }
}

class ProductionRecord {
  bool? isHasRouting;
  String? taskId;
  String? parentTaskId;
  String? unit;
  String? barcode;
  double? qty;
  double? productAttributesQty;
  String? productId;
  String? createTime;
  String? lastEditTime;
  String? stepCode;
  String? stepName;
  String? stepId;
  String? productionOrderEstimateEndDate;
  String? productionOrderStartDate;
  String? workDate;
  String? productCode;
  String? productName;
  String? productAttributes;
  String? productionOrderSAP;
  String? createByFullName;
  String? lastEditByFullName;
  bool? actived;
  String? toStockCode;
  String? toStockName;
  String? isWorkCenterCompleted;
  String? workCenterConfirmTime;
  String? confirmWorkCenter;
  String? property1;
  String? property2;
  String? productionOrder;
  String? summary;
  String? productAttributesName;
  String? productAttributesUnit;
  String? poT12;
  double? productAttributesQtyD;
  int? phase;
  double? quantityDLD;
  double? quantityDLKD;
  String? fromDate;
  String? toDate;
  String? departmentId;
  List<ListDetail>? listDetail;

  ProductionRecord(
      {this.isHasRouting,
      this.taskId,
      this.parentTaskId,
      this.unit,
      this.barcode,
      this.qty,
      this.productAttributesQty,
      this.productId,
      this.createTime,
      this.lastEditTime,
      this.stepCode,
      this.stepName,
      this.stepId,
      this.productionOrderEstimateEndDate,
      this.productionOrderStartDate,
      this.workDate,
      this.productCode,
      this.productName,
      this.productAttributes,
      this.productionOrderSAP,
      this.createByFullName,
      this.lastEditByFullName,
      this.actived,
      this.toStockCode,
      this.toStockName,
      this.isWorkCenterCompleted,
      this.workCenterConfirmTime,
      this.confirmWorkCenter,
      this.property1,
      this.property2,
      this.productionOrder,
      this.summary,
      this.productAttributesName,
      this.productAttributesUnit,
      this.poT12,
      this.productAttributesQtyD,
      this.phase,
      this.quantityDLD,
      this.quantityDLKD,
      this.fromDate,
      this.toDate,
      this.departmentId,
      this.listDetail});

  ProductionRecord.fromJson(Map<String, dynamic> json) {
    isHasRouting = json['isHasRouting'];
    taskId = json['taskId'];
    parentTaskId = json['parentTaskId'];
    unit = json['unit'];
    barcode = json['barcode'];
    qty = json['qty'];
    productAttributesQty = json['productAttributesQty'];
    productId = json['productId'];
    createTime = json['createTime'];
    lastEditTime = json['lastEditTime'];
    stepCode = json['stepCode'];
    stepName = json['stepName'];
    stepId = json['stepId'];
    productionOrderEstimateEndDate = json['productionOrder_EstimateEndDate'];
    productionOrderStartDate = json['productionOrder_StartDate'];
    workDate = json['workDate'];
    productCode = json['productCode'];
    productName = json['productName'];
    productAttributes = json['productAttributes'];
    productionOrderSAP = json['productionOrder_SAP'];
    createByFullName = json['createByFullName'];
    lastEditByFullName = json['lastEditByFullName'];
    actived = json['actived'];
    toStockCode = json['toStockCode'];
    toStockName = json['toStockName'];
    isWorkCenterCompleted = json['isWorkCenterCompleted'];
    workCenterConfirmTime = json['workCenterConfirmTime'];
    confirmWorkCenter = json['confirmWorkCenter'];
    property1 = json['property1'];
    property2 = json['property2'];
    productionOrder = json['productionOrder'];
    summary = json['summary'];
    productAttributesName = json['productAttributesName'];
    productAttributesUnit = json['productAttributesUnit'];
    poT12 = json['poT12'];
    productAttributesQtyD = json['productAttributesQtyD'];
    phase = json['phase'];
    quantityDLD = json['quantity_DLD'];
    quantityDLKD = json['quantity_DLKD'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
    departmentId = json['departmentId'];
    if (json['listDetail'] != null) {
      listDetail = <ListDetail>[];
      json['listDetail'].forEach((v) {
        listDetail!.add(ListDetail.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isHasRouting'] = isHasRouting;
    data['taskId'] = taskId;
    data['parentTaskId'] = parentTaskId;
    data['unit'] = unit;
    data['barcode'] = barcode;
    data['qty'] = qty;
    data['productAttributesQty'] = productAttributesQty;
    data['productId'] = productId;
    data['createTime'] = createTime;
    data['lastEditTime'] = lastEditTime;
    data['stepCode'] = stepCode;
    data['stepName'] = stepName;
    data['stepId'] = stepId;
    data['productionOrder_EstimateEndDate'] = productionOrderEstimateEndDate;
    data['productionOrder_StartDate'] = productionOrderStartDate;
    data['workDate'] = workDate;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['productAttributes'] = productAttributes;
    data['productionOrder_SAP'] = productionOrderSAP;
    data['createByFullName'] = createByFullName;
    data['lastEditByFullName'] = lastEditByFullName;
    data['actived'] = actived;
    data['toStockCode'] = toStockCode;
    data['toStockName'] = toStockName;
    data['isWorkCenterCompleted'] = isWorkCenterCompleted;
    data['workCenterConfirmTime'] = workCenterConfirmTime;
    data['confirmWorkCenter'] = confirmWorkCenter;
    data['property1'] = property1;
    data['property2'] = property2;
    data['productionOrder'] = productionOrder;
    data['summary'] = summary;
    data['productAttributesName'] = productAttributesName;
    data['productAttributesUnit'] = productAttributesUnit;
    data['poT12'] = poT12;
    data['productAttributesQtyD'] = productAttributesQtyD;
    data['phase'] = phase;
    data['quantity_DLD'] = quantityDLD;
    data['quantity_DLKD'] = quantityDLKD;
    data['fromDate'] = fromDate;
    data['toDate'] = toDate;
    data['departmentId'] = departmentId;
    if (listDetail != null) {
      data['listDetail'] = listDetail!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListDetail {
  String? stockRecevingType;
  String? createTime;
  String? createByName;
  double? quantity;
  String? itmno;
  String? fromTime;
  String? toTime;
  String? customerReference;
  double? quantityD;
  double? quantityKD;
  String? ktext;
  String? stepCode;
  int? phase;
  String? fromDate;
  String? toDate;

  ListDetail({
    this.stockRecevingType,
    this.createTime,
    this.createByName,
    this.quantity,
    this.itmno,
    this.fromTime,
    this.toTime,
    this.customerReference,
    this.quantityD,
    this.quantityKD,
    this.ktext,
    this.stepCode,
    this.phase,
    this.fromDate,
    this.toDate,
  });

  ListDetail.fromJson(Map<String, dynamic> json) {
    stockRecevingType = json['stockRecevingType'];
    createTime = json['createTime'];
    createByName = json['createByName'];
    quantity = json['quantity'];
    itmno = json['itmno'];
    fromTime = json['fromTime'];
    toTime = json['toTime'];
    customerReference = json['customerReference'];
    quantityD = json['quantity_D'];
    quantityKD = json['quantity_KD'];
    ktext = json['ktext'];
    stepCode = json['stepCode'];
    phase = json['phase'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stockRecevingType'] = stockRecevingType;
    data['createTime'] = createTime;
    data['createByName'] = createByName;
    data['quantity'] = quantity;
    data['itmno'] = itmno;
    data['fromTime'] = fromTime;
    data['toTime'] = toTime;
    data['customerReference'] = customerReference;
    data['quantity_D'] = quantityD;
    data['quantity_KD'] = quantityKD;
    data['ktext'] = ktext;
    data['stepCode'] = stepCode;
    data['phase'] = phase;
    data['fromDate'] = fromDate;
    data['toDate'] = toDate;
    return data;
  }
}

class ListStepCode {
  String? value;
  String? text;
  String? orderIndex;

  ListStepCode({this.value, this.text, this.orderIndex});

  ListStepCode.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    text = json['text'];
    orderIndex = json['orderIndex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['value'] = value;
    data['text'] = text;
    data['orderIndex'] = orderIndex;
    return data;
  }
}
