class GetListCatalog {
  int? code;
  bool? isSuccess;
  List<DataGetListCatalog>? data;


  GetListCatalog(
      {this.code,
        this.isSuccess,
        this.data});

  GetListCatalog.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    if (json['data'] != null) {
      data = <DataGetListCatalog>[];
      json['data'].forEach((v) {
        data!.add(DataGetListCatalog.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetListCatalog {
  String? key;
  String? value;

  DataGetListCatalog({this.key, this.value});

  DataGetListCatalog.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}