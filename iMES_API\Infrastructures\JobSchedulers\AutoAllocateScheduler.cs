﻿using Hangfire;
using Hangfire.Storage;
using ISD.API.Constant.MESP2;
using ISD.API.EntityModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers
{
    public static class AutoAllocateScheduler
    {
        public static async Task Invoke(IServiceProvider serviceProvider)
        {
            //Create scope context
            using var scope = serviceProvider.CreateScope();
            using var dbContext = scope.ServiceProvider.GetRequiredService<EntityDataContext>();

            //Check trạng thái run job
            var settingJob = await dbContext.SettingJob.SingleOrDefaultAsync(x => x.JobName == "AutoAllocateScheduler");

            if (settingJob?.IsRun == false) return;

            var service = new AutoAllocateService(serviceProvider);

            try
            {
                //Xóa job nếu tồn tại trước khi thêm mới
                RecurringJob.RemoveIfExists("AutoAllocateService");
                RecurringJob.RemoveIfExists("AutoAllocateScheduler");

                //Thêm mới job và run
                //RecurringJob.AddOrUpdate("AutoAllocateScheduler", () => service.Run(), settingJob?.Config);

                TimeZoneInfo gmt7 = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");
                // Schedule the job to run daily at 0:33 AM GMT+7
                RecurringJob.AddOrUpdate(
                    () => service.Run(), // Replace with your actual job method 
                    Cron.Daily(0, 35),     // 0:33 AM
                    gmt7                   // GMT+7 Timezone
                );


            }
            catch (Exception)
            {
                return;
            }
        }
    }
}
