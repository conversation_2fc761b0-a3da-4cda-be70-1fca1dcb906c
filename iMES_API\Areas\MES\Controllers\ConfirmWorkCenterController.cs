﻿using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Linq;
using System.Net;
using System.Threading;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDAuthorizationAttribute]
    public class ConfirmWorkCenterController : ControllerBaseAPI
    {
        #region "Hoàn thành công đoạn lớn" - l<PERSON>y thông tin
        /// <summary>API "Hoàn thành công đoạn lớn" - L<PERSON>y thông tin </summary>
        /// <param name="TaskId"></param>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/ConfirmWorkCenter/ConfirmWorkCenter?Barcode={Barcode}
        ///     Params: 
        ///             + version : 1
        ///             + Barcode  : B0C17D8A-E82A-490A-A6F6-CA5A2F582422e
        ///             
        /// OUT PUT
        ///  
        ///     additionalData : Danh sách công đoạn
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": {
        ///             "taskId": "bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///             "parentTaskId": "56fb2574-5ad5-421a-82fd-22c593fdb854",
        ///             "unit": "CAI",
        ///             "fromStepCode": "LON",
        ///             "fromStepName": "Lọng (m/ph)",
        ///             "productAttributes": "0101.b.9",
        ///             "productAttributesQty": 120.000,
        ///             "workDate": "2022-05-06T00:00:00",
        ///             "qty": 60,
        ///             "productCode": "530004331",
        ///             "productName": "Ghế dài Aspen-CH025ARHP Matte Black",
        ///             "productId": "95d35eb5-2d5f-440a-8714-453d17d3f7b7",
        ///             "productionOrder_SAP": "500016230",
        ///             "createByFullName": "Thongke002",
        ///             "createTime": "2022-05-09T14:08:59.357",
        ///             "lastEditByFullName": null,
        ///             "lastEditTime": null,
        ///             "actived": true,
        ///             "toStepCode": null,
        ///             "toStepName": null,
        ///             "productionOrder": "DT-346-21-FOH",
        ///             "summary": "DT-346-21-FOH-D1",
        ///             "productAttributesName": "Nối vai tựa dưới",
        ///             "poT12": "25x30x163",
        ///             "productAttributesUnit": "Cái",
        ///             "productAttributesQtyD": 120.00,
        ///             "listDetail": [
        ///                 {
        ///                     "ktext": "Nối vai tựa dưới",
        ///                     "quantity": 120.00
        ///                 }
        ///             ]
        ///         },
        ///         "additionalData": [
        ///             {
        ///                 "arbpL_SUB": "BA2",
        ///                 "display": "BA2 | Bào 2 Mặt"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "CAX",
        ///                 "display": "CAX | Cào Cước"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "CAT",
        ///                 "display": "CAT | Cắt Định Hình"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "GH2",
        ///                 "display": "GH2 | Ghép Ngang"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "KHO",
        ///                 "display": "KHO | Khoan"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "NHC",
        ///                 "display": "NHC | Nhám Cạnh"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "NHM",
        ///                 "display": "NHM | Nhám Mặt"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "PHA",
        ///                 "display": "PHA | Phay"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "RO1",
        ///                 "display": "RO1 | Rong Gỗ"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "TKP",
        ///                 "display": "TKP | Thiết Kế Phôi"
        ///             },
        ///             {
        ///                 "arbpL_SUB": "DGO",
        ///                 "display": "DGO | Đóng Gói"
        ///             }
        ///         ]
        ///     }
        ///</remarks>
        [HttpGet("ConfirmWorkCenter")]
        public ActionResult GET(Guid? Barcode)
        {
            //Tìm LSX SAP theo barcode
            var TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

            //Nếu không có lệnh thực thi thì báo lỗi
            if (TaskId == null || TaskId == Guid.Empty)
            {
                //Báo lỗi không tìm thấy barcode
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Thẻ treo không hợp lệ!",
                });
            }

            #region Master
            #region Nếu có task theo barcode => show popup theo dữ liệu
            var dataMaster = new SwitchingStagesViewModel();
            dataMaster = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageByTaskId(TaskId);
            #endregion

            var CompanyCode = CurrentUser.CompanyCode;

            #region Create ViewBag "Công đoạn"
            var listStep = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, dataMaster.ProductAttributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();

            //khi chuyển công đoạn: cho phép lấy công đoạn của cụm 
            if (dataMaster.ProductAttributes.Split('.').Length > 1)
            {
                string ProductParentAtrributes = dataMaster.ProductAttributes.Split('.')[0];
                var parentStepCodeLst = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, ProductParentAtrributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();
                listStep.AddRange(parentStepCodeLst);
            }

            listStep = listStep.GroupBy(p => new { p.ARBPL_SUB, p.display }).Select(p => new { p.Key.ARBPL_SUB, p.Key.display }).ToList();
            #endregion
            #endregion

            #region Detail
            var dataDetail = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageHistoryByTTLSX(TaskId);
            #endregion

            dataMaster.ListDetail = dataDetail;


            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = null, Data = dataMaster, AdditionalData = listStep });
        }

        //[HttpGet("ConfirmWorkCenter2")]
        //public ActionResult GET2(Guid? Barcode)
        //{
        //    //Tìm LSX SAP theo barcode
        //    var TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode2(Barcode);

        //    //Nếu không có lệnh thực thi thì báo lỗi
        //    if (TaskId == null || TaskId == Guid.Empty)
        //    {
        //        //Báo lỗi không tìm thấy barcode
        //        return BadRequest(new
        //        {
        //            Code = HttpStatusCode.NotFound,
        //            Success = false,
        //            Data = "Thẻ treo không hợp lệ!",
        //        });
        //    }

        //    #region Master
        //    #region Nếu có task theo barcode => show popup theo dữ liệu
        //    var dataMaster = new SwitchingStagesViewModel();
        //    dataMaster = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageByTaskId2(TaskId);
        //    #endregion

        //    var CompanyCode = CurrentUser.CompanyCode;

        //    #region Create ViewBag "Công đoạn"
        //    var listStep = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, dataMaster.ProductAttributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();

        //    //khi chuyển công đoạn: cho phép lấy công đoạn của cụm 
        //    if (dataMaster.ProductAttributes.Split('.').Length > 1)
        //    {
        //        string ProductParentAtrributes = dataMaster.ProductAttributes.Split('.')[0];
        //        var parentStepCodeLst = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataMaster.ProductCode, ProductParentAtrributes, CompanyCode).Where(x => x.ARBPL_SUB != dataMaster.FromStepCode).Select(x => new { x.ARBPL_SUB, display = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();
        //        listStep.AddRange(parentStepCodeLst);
        //    }

        //    listStep = listStep.GroupBy(p => new { p.ARBPL_SUB, p.display }).Select(p => new { p.Key.ARBPL_SUB, p.Key.display }).ToList();
        //    #endregion
        //    #endregion

        //    #region Detail
        //    var dataDetail = _unitOfWork.ProductionManagementRepository.GetTTLSXForSwitchingStageHistoryByTTLSX(TaskId);
        //    #endregion

        //    dataMaster.ListDetail = dataDetail;


        //    return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = null, Data = dataMaster, AdditionalData = listStep });
        //}
        #endregion

        #region "Hoàn thành công đoạn lớn" - Lưu thông tin
        /// <summary>API "Hoàn thành công đoạn lớn" - Lưu thông tin</summary>
        /// <param name="confirmWorkCenterViewModel"></param>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/ConfirmWorkCenter/ConfirmWorkCenter
        ///     Params: 
        ///             + version : 1
        ///   
        /// BODY
        /// 
        ///     {
        ///         "taskId": "bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///         "confirmWorkCenter": "LRN",
        ///         "workCenterConfirmTime": "2022-05-13"
        ///     }
        /// 
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 201,
        ///         "success": true,
        ///         "data": "Cập nhật công đoạn lớn thành công!"
        ///     }
        ///</remarks>
        [HttpPost("ConfirmWorkCenter")]
        public IActionResult POST(ConfirmWorkCenterViewModel confirmWorkCenterViewModel)
        {
            var CurrentUserAccountId = CurrentUser.AccountId;

            if (confirmWorkCenterViewModel == null || confirmWorkCenterViewModel.TaskId == null || confirmWorkCenterViewModel.TaskId == Guid.Empty)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = true,
                    Data = "Lỗi, vui lòng tải lại trang và thử lại!",
                });
            }

            var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            //Hoàn tất công đoạn lớn - không cho lưu trước ngày khóa sổ
            var dateClosed = _context.DateClosedModel.FirstOrDefault();
            if (dateClosed != null)
            {
                if (confirmWorkCenterViewModel.WorkCenterConfirmTime.HasValue && confirmWorkCenterViewModel.WorkCenterConfirmTime.Value.Date < dateClosed.DateClosed.Date)
                {
                    return BadRequest(new
                    {
                        Code = HttpStatusCode.NotModified,
                        Success = false,
                        Data = string.Format("Không thể hoàn tất công đoạn lớn trước ngày khóa sổ! ({0:dd/MM/yyyy})", dateClosed.DateClosed.Date),
                    });
                }
            }
            var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(confirmWorkCenterViewModel.WorkCenterConfirmTime);
            //Lấy thông tin lênh thực thi
            var ttlxs = _unitOfWork.ProductionManagementRepository.GetExecutionTaskByTaskId(confirmWorkCenterViewModel.TaskId);
            #region nhân viên confirm
            confirmWorkCenterViewModel.ConfirmBy = CurrentUserAccountId;
            #endregion

            var StockReceivingDetail = _context.StockReceivingDetailModel.Where(x => x.CustomerReference == ttlxs.TaskId && x.ProductId == ttlxs.ProductId && x.ProductAttributes == ttlxs.ProductAttributes && x.Phase == ttlxs.Phase && x.StockId == ttlxs.StepId);

            if (StockReceivingDetail != null)
            {

                foreach (var item in StockReceivingDetail)
                {
                    #region hoàn tất công đoạn lớn
                    item.IsWorkCenterCompleted = true;
                    item.ConfirmWorkCenter = confirmWorkCenterViewModel.ConfirmWorkCenter;
                    item.ConfirmDatekey = dateKey;
                    item.WorkCenterConfirmTime = confirmWorkCenterViewModel.WorkCenterConfirmTime;
                    item.ConfirmBy = confirmWorkCenterViewModel.ConfirmBy;
                    _context.Entry(item).State = EntityState.Modified;
                    #endregion
                }

                _context.SaveChanges();
            }

            //Lấy thông tin StockReceivingDetail
            //Sau khi confirm thì đẩy lại thông tin lên SAP
            var StockReceiving = _context.StockReceivingDetailModel.Where(x => x.CustomerReference == ttlxs.TaskId);
            if (StockReceiving != null && StockReceiving.Count() > 0)
            {
                foreach (var item in StockReceiving)
                {
                    item.isSendToSAP = null;
                    _context.Entry(item).State = EntityState.Modified;
                }
            }

            try
            {
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = ex.Message,
                });
            }

            #region Tạo phiếu QC sau khi confirm (Loại "Cụm", "CUM", "cụm")
            var routing = _context.RoutingInventorModel.Where(x => x.ITMNO == ttlxs.ProductAttributes && x.MATNR == ttlxs.ProductCode).FirstOrDefault();
            if (routing != null && (routing.BMEIN.ToLower() == "cum" || routing.BMEIN.ToLower() == "cụm"))
            {
                _unitOfWork.QualityControlRepository.CreateQualityControl(confirmWorkCenterViewModel);
            }

            #endregion

            #region Cập nhật dữ liệu cho BC01
            var DSXId = _context.TaskModel.Where(p => p.TaskId == ttlxs.ParentTaskId).Select(p => p.ParentTaskId).FirstOrDefault();
            new Thread(delegate ()
            {
                UpdateBC01(DSXId, ttlxs.ParentTaskId);
            }).Start();
            #endregion

            return Ok(new
            {
                Code = HttpStatusCode.Created,
                Success = true,
                Data = "Cập nhật công đoạn lớn thành công!",
            });
        }
        private void UpdateBC01(Guid? DSXId, Guid? LSXSAPId)
        {
            var _bcRepository = new BC00ReportRepository(_context);
            _bcRepository.UpdateBC01FromDSX(DSXId, LSXSAPId);
        }
        #endregion
    }
}