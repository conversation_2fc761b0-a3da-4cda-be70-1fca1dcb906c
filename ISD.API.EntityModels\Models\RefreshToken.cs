﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RefreshToken", Schema = "pms")]
    public partial class RefreshToken
    {
        [Key]
        public int Id { get; set; }
        public string Token { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Expires { get; set; }
        public bool? IsExpired { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Created { get; set; }
        [StringLength(500)]
        public string CreatedByUserName { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Revoked { get; set; }
        [StringLength(500)]
        public string RevokedByUserName { get; set; }
        public string ReplacedByToken { get; set; }
        public bool? IsActive { get; set; }
    }
}