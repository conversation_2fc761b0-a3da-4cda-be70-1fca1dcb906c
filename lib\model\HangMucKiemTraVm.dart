class HangMucKiemTraVm {
  String id;
  int code;
  String? saleOrgCode;
  String name;
  int? orderIndex;
  bool? actived;
  DateTime? createTime;
  String? createBy;
  DateTime? lastEditTime;
  String? lastEditBy;
  String? qualityType;
  String? itemType;
  String? routingType;
  int? numberMin;
  int? numberMax;
  String? materialType;
  String? materialDescription;

  HangMucKiemTraVm({
    required this.id,
    required this.code,
    this.saleOrgCode,
    required this.name,
    this.orderIndex,
    this.actived,
    this.createTime,
    this.createBy,
    this.lastEditTime,
    this.lastEditBy,
    this.qualityType,
    this.itemType,
    this.routingType,
    this.numberMin,
    this.numberMax,
    this.materialType,
    this.materialDescription,
  });

  factory HangMucKiemTraVm.fromJson(Map<String, dynamic> json) {
    return HangMucKiemTraVm(
      id: json['id'],
      code: json['code'],
      saleOrgCode: json['saleOrgCode'],
      name: json['name'],
      orderIndex: json['orderIndex'],
      actived: json['actived'],
      createTime: json['createTime'] != null ? DateTime.parse(json['createTime']) : null,
      createBy: json['createBy'],
      lastEditTime: json['lastEditTime'] != null ? DateTime.parse(json['lastEditTime']) : null,
      lastEditBy: json['lastEditBy'],
      qualityType: json['qualityType'],
      itemType: json['itemType'],
      routingType: json['routingType'],
      numberMin: json['numberMin'],
      numberMax: json['numberMax'],
      materialType: json['materialType'],
      materialDescription: json['materialDescription'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['code'] = code;
    data['saleOrgCode'] = saleOrgCode;
    data['name'] = name;
    data['orderIndex'] = orderIndex;
    data['actived'] = actived;
    data['createTime'] = createTime?.toIso8601String();
    data['createBy'] = createBy;
    data['lastEditTime'] = lastEditTime?.toIso8601String();
    data['lastEditBy'] = lastEditBy;
    data['qualityType'] = qualityType;
    data['itemType'] = itemType;
    data['routingType'] = routingType;
    data['numberMin'] = numberMin;
    data['numberMax'] = numberMax;
    data['materialType'] = materialType;
    data['materialDescription'] = materialDescription;
    return data;
  }
}
