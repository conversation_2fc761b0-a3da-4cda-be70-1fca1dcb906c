# Power BI Integration Guide - TTF iMES Quality Control API

## Overview

This guide explains how to integrate Microsoft Power BI with the TTF iMES Quality Control API using API key authentication. Power BI can connect directly to the API endpoints to retrieve real-time quality control data for reporting and analytics.

## Prerequisites

1. **API Key**: Contact your system administrator to create an API key with `QualityControl.Read` scope
2. **Power BI Desktop** or **Power BI Service** access
3. **Base URL**: Your API server URL (e.g., `https://your-api-domain.com`)

## Quick Setup Steps

### 1. Create API Key (Admin Task)

Your system administrator should create an API key using:

```http
POST /api/v1/Permission/ApiKeyManagement/Create
Authorization: Bearer {admin-jwt-token}

{
  "keyName": "Power BI Integration",
  "description": "Quality Control data access for Power BI reports",
  "companyCode": "1000",
  "saleOrgCode": "1000",
  "allowedScopes": "QualityControl.Read",
  "rateLimitPerHour": 5000
}
```

### 2. Configure Data Source in Power BI

#### Method 1: Power BI Desktop

1. **Open Power BI Desktop**
2. **Get Data** → **Web**
3. **URL**: `https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl`
4. **Advanced Options**:
   - **HTTP request header parameters**:
     - Name: `X-API-Key`
     - Value: `ttf_your_api_key_here`

#### Method 2: Power Query Advanced Editor

```m
let
    ApiKey = "ttf_your_api_key_here",
    BaseUrl = "https://your-api-domain.com",
    Headers = [
        #"X-API-Key" = ApiKey,
        #"Content-Type" = "application/json"
    ],
    
    // Function to get paginated data
    GetPage = (pageIndex as number) =>
        let
            Url = BaseUrl & "/api/v1/External/QualityControlExternal/QualityControl?pageIndex=" & Number.ToText(pageIndex) & "&pageSize=100",
            Response = Json.Document(Web.Contents(Url, [Headers=Headers])),
            Data = Response[data],
            Items = Data[items],
            HasNextPage = Data[pagination][hasNextPage]
        in
            [Items = Items, HasNextPage = HasNextPage],
    
    // Get all pages
    GetAllPages = () =>
        let
            FirstPage = GetPage(1),
            GetPagesRecursive = (pageIndex as number, accumulator as list) =>
                let
                    CurrentPage = GetPage(pageIndex),
                    NewAccumulator = List.Combine({accumulator, CurrentPage[Items]})
                in
                    if CurrentPage[HasNextPage] then
                        @GetPagesRecursive(pageIndex + 1, NewAccumulator)
                    else
                        NewAccumulator
        in
            GetPagesRecursive(1, {}),
    
    AllData = GetAllPages(),
    Table = Table.FromList(AllData, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    ExpandedTable = Table.ExpandRecordColumn(Table, "Column1", 
        {"qualityControlId", "qualityControlCode", "saleOrgCode", "workShopCode", 
         "productCode", "productName", "confirmDate", "qualityDate", "status", 
         "result", "qualityType", "qty"})
in
    ExpandedTable
```

## Power BI Optimized Endpoints

### 1. Quality Control Summary (Recommended for Dashboards)

**Endpoint**: `/api/v1/External/QualityControlExternal/QualityControl`

**Best Query Parameters for Power BI**:
```
?pageSize=100&fromDate=2024-01-01&status=true
```

**Power Query M Code**:
```m
let
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl?pageSize=100&fromDate=2024-01-01",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    )),
    data = Source[data],
    items = data[items],
    #"Converted to Table" = Table.FromList(items, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Column1" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"qualityControlId", "qualityControlCode", "saleOrgCode", "workShopCode", 
         "productCode", "productName", "confirmDate", "qualityDate", "status", 
         "result", "qualityType", "qty"}, 
        {"QC ID", "QC Code", "Sale Org", "Workshop", "Product Code", "Product Name", 
         "Confirm Date", "Quality Date", "Status", "Result", "Quality Type", "Quantity"}),
    #"Changed Type" = Table.TransformColumnTypes(#"Expanded Column1",{
        {"Confirm Date", type datetime}, 
        {"Quality Date", type datetime}, 
        {"Status", type logical}, 
        {"Quantity", Int64.Type}
    })
in
    #"Changed Type"
```

### 2. Quality Control by Date Range (For Time-based Analysis)

**Power Query M Code with Dynamic Date Filter**:
```m
let
    // Dynamic date range (last 30 days)
    EndDate = DateTime.Date(DateTime.LocalNow()),
    StartDate = Date.AddDays(EndDate, -30),
    
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl" &
        "?fromDate=" & Date.ToText(StartDate, "yyyy-MM-dd") &
        "&toDate=" & Date.ToText(EndDate, "yyyy-MM-dd") &
        "&pageSize=100",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    )),
    
    // Process the response
    data = Source[data],
    items = data[items],
    #"Converted to Table" = Table.FromList(items, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Data" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"qualityControlId", "qualityControlCode", "saleOrgCode", "workShopCode", 
         "productCode", "productName", "confirmDate", "qualityDate", "status", 
         "result", "qualityType", "qty"})
in
    #"Expanded Data"
```

### 3. Workshop Performance Query

**Endpoint**: `/api/v1/External/QualityControlExternal/QualityControl`

**Power Query for Workshop Analysis**:
```m
let
    // Get data grouped by workshop
    Workshops = {"20000050", "20000051", "20000052"}, // Your workshop codes
    
    GetWorkshopData = (workshopCode as text) =>
        let
            Source = Json.Document(Web.Contents(
                "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl" &
                "?workShopCode=" & workshopCode & "&pageSize=100",
                [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
            )),
            data = Source[data],
            items = data[items]
        in
            items,
    
    AllWorkshopData = List.Combine(List.Transform(Workshops, GetWorkshopData)),
    #"Converted to Table" = Table.FromList(AllWorkshopData, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Data" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"qualityControlId", "qualityControlCode", "saleOrgCode", "workShopCode", 
         "productCode", "productName", "confirmDate", "qualityDate", "status", 
         "result", "qualityType", "qty"})
in
    #"Expanded Data"
```

## Advanced Power BI Features

### 1. Parameters for Dynamic Filtering

Create Power BI parameters for dynamic filtering:

1. **Home** → **Manage Parameters** → **New Parameter**
2. Create parameters:
   - `WorkShopCode` (Text)
   - `DateRange` (Number, for days back)
   - `ProductCode` (Text)

**Updated Power Query with Parameters**:
```m
let
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl" &
        "?workShopCode=" & WorkShopCode &
        "&productCode=" & ProductCode &
        "&fromDate=" & Date.ToText(Date.AddDays(DateTime.Date(DateTime.LocalNow()), -DateRange), "yyyy-MM-dd") &
        "&pageSize=100",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    ))
    // ... rest of the query
in
    Source
```

### 2. Incremental Refresh Setup

For large datasets, configure incremental refresh:

1. **Power BI Desktop** → **Manage Parameters**
2. Create `RangeStart` and `RangeEnd` parameters (DateTime)
3. Use these in your query:

```m
let
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl" &
        "?fromDate=" & Date.ToText(DateTime.Date(RangeStart), "yyyy-MM-dd") &
        "&toDate=" & Date.ToText(DateTime.Date(RangeEnd), "yyyy-MM-dd") &
        "&pageSize=100",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    ))
    // ... rest of the query
in
    Source
```

### 3. Error Handling

Add robust error handling to your Power Query:

```m
let
    TryGetData = () =>
        try
            Json.Document(Web.Contents(
                "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl?pageSize=100",
                [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
            ))
        otherwise
            [data = [items = {}], message = "API call failed"],
    
    Source = TryGetData(),
    
    // Check if API call was successful
    IsSuccess = Record.HasFields(Source, "data"),
    
    Result = if IsSuccess then
        let
            data = Source[data],
            items = data[items],
            Table = Table.FromList(items, Splitter.SplitByNothing(), null, null, ExtraValues.Error)
        in
            Table
    else
        #table({"Error"}, {{"Failed to connect to API"}})
in
    Result
```

## Data Refresh Configuration

### Automatic Refresh (Power BI Service)

1. **Publish** your report to Power BI Service
2. Go to **Settings** → **Dataset** → **Scheduled Refresh**
3. Configure refresh frequency:
   - **Recommended**: Every 1-4 hours for QC data
   - **Maximum**: 8 times per day (free tier)

### Gateway Configuration (On-premises)

If your API is on-premises, configure Power BI Gateway:

1. Install **Power BI Gateway** on a server with API access
2. Configure data source with API key authentication
3. Set up scheduled refresh through the gateway

## Sample Power BI Measures

### Quality Performance Metrics

```dax
// Pass Rate
Pass Rate = 
DIVIDE(
    CALCULATE(COUNT('Quality Control'[QC ID]), 'Quality Control'[Result] = "Pass"),
    COUNT('Quality Control'[QC ID])
) * 100

// Daily Quality Volume
Daily QC Volume = 
CALCULATE(
    COUNT('Quality Control'[QC ID]),
    'Quality Control'[Quality Date] = TODAY()
)

// Workshop Performance
Workshop Pass Rate = 
CALCULATE(
    [Pass Rate],
    ALLEXCEPT('Quality Control', 'Quality Control'[Workshop])
)

// Quality Trend (30-day moving average)
Quality Trend = 
CALCULATE(
    [Pass Rate],
    DATESINPERIOD(
        'Quality Control'[Quality Date],
        LASTDATE('Quality Control'[Quality Date]),
        -30,
        DAY
    )
)
```

### Time Intelligence Measures

```dax
// Previous Period Comparison
Pass Rate Previous Period = 
CALCULATE(
    [Pass Rate],
    DATEADD('Quality Control'[Quality Date], -1, MONTH)
)

// Year-to-Date Quality
YTD Pass Rate = 
CALCULATE(
    [Pass Rate],
    DATESYTD('Quality Control'[Quality Date])
)

// Quality by Month
Monthly Quality Volume = 
CALCULATE(
    COUNT('Quality Control'[QC ID]),
    DATESMTD('Quality Control'[Quality Date])
)
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```
   Error: "Invalid or missing API key"
   Solution: Verify API key format and expiration
   ```

2. **Rate Limit Exceeded**
   ```
   Error: "Too Many Requests"
   Solution: Reduce refresh frequency or request higher limits
   ```

3. **Data Not Loading**
   ```
   Error: "Could not connect to data source"
   Solution: Check API URL and network connectivity
   ```

### Testing Your Connection

Use this simple test query first:

```m
let
    Source = Json.Document(Web.Contents(
        "https://your-api-domain.com/api/v1/External/QualityControlExternal/QualityControl?pageSize=1",
        [Headers=[#"X-API-Key"="ttf_your_api_key_here"]]
    ))
in
    Source
```

### Power BI Specific Optimizations

1. **Reduce Column Count**: Only expand needed columns
2. **Use Date Filters**: Always filter by date range to limit data
3. **Enable Query Folding**: Use simple filters when possible
4. **Cache Results**: Enable query caching in Power BI Service

## Security Best Practices

1. **Store API Keys Securely**: Use Power BI Service's credential store
2. **Limit Data Access**: Use specific date ranges and filters
3. **Monitor Usage**: Track API key usage and refresh patterns
4. **Regular Key Rotation**: Periodically update API keys

## Sample Reports

### Quality Control Dashboard

Recommended visuals:
- **Card**: Total QC Records, Pass Rate, Fail Rate
- **Line Chart**: Quality trend over time
- **Bar Chart**: Quality by workshop/product
- **Table**: Recent failed quality checks
- **Gauge**: Current day pass rate vs. target

### Workshop Performance Report

Recommended visuals:
- **Matrix**: Workshop vs. Product quality metrics
- **Funnel**: Quality process flow
- **Scatter Chart**: Quality vs. Volume correlation
- **Slicer**: Date range, workshop, product filters

## Contact Support

For Power BI integration issues:
- **API Issues**: Contact system administrator
- **Power BI Issues**: Check Microsoft Power BI documentation
- **Data Questions**: Review the main API integration guide

---

*This guide covers Power BI-specific integration. For general API documentation, refer to the main Third-Party API Integration Guide.*