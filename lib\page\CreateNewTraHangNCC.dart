import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/data/dummy_data.dart';

import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/QualityTitle.dart';
import '../model/CodeNameProduct.dart';
import '../model/DataTraHangNCC.dart';
import '../model/PurchaseOrderDetail.dart';
import '../model/RequestTraHangNCC.dart';
import '../model/getBackDataQRCodePage.dart';
import '../model/getListSOWBSByBatchTranferWarehouse.dart';
import '../model/qualityControlApi.dart';
import '../repository/function/exportWarehouseFunction.dart';
import '../repository/function/listWarehouseTranferFunction.dart';
import '../repository/function/traHangNCCFunction.dart';
import '../repository/function/traHangNCCInfoFunction.dart';
import '../utils/ui_helpers.dart';
import '../utils/ui_utils.dart';

const isDebug = false;
// const isDebug = true;

class CreateNewTraHangNCC extends StatefulWidget {
  const CreateNewTraHangNCC({
    Key? key,
    required this.token,
    required this.dateTimeOld,
    this.id,
  }) : super(key: key);

  final String token;
  final String dateTimeOld;
  final String? id;

  @override
  _CreateNewTraHangNCCState createState() => _CreateNewTraHangNCCState();
}

class _CreateNewTraHangNCCState extends State<CreateNewTraHangNCC> {
  String? radioButtonValue;
  int _returnType = 1; // initialize with the default value

  // You now have a list of RequestTraHangNCC instead of DataTraHangNCC
  List<RequestTraHangNCC> _listRequestTraHangNCC = [];

  List<List<TextEditingController>> _lsControllerMaNVL = [];
  List<List<TextEditingController>> _lsControllerSoLuong = [];

  List<TextEditingController> _lsControllerPO = [];

  bool _errorInputAutoComplete = false;
  bool _isLoading = false;
  List<DataCodenameProduct> _lsCodeNameProductSearch = [];
  List<DataCodenameProduct> _lsCodeNamePOSearch = [];
  Timer? _timer;
  int selectedIndex = -1;
  int poIndex = -1;

  bool isEdit = false;

  Map<String, List<DataPurchaseOrderDetail>> _listPODetail = {};

  late bool _timeOut;
  bool _isNotWifi = false;
  String _error = "";
  String? selectedVendorName = "";

  DataRequestReturnVendorModel? _dataServer = null;

  @override
  void initState() {
    super.initState();

    if (isDebug) {
      createDummyData();
    }

    if (widget.id.toString() != "null") {
      _getData();
      setState(() {
        isEdit = true;
      });
    } else {
      _loadDataAndSetDefault();
      setState(() {
        isEdit = false;
      });
    }
  }

  @override
  void dispose() {
    // Clear all controllers and data lists
    for (var controllerList in _lsControllerMaNVL) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    for (var controllerList in _lsControllerSoLuong) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    for (var controller in _lsControllerPO) {
      controller.dispose();
    }
    for (var request in _listRequestTraHangNCC) {
      request.dataList.clear();
    }
    _listRequestTraHangNCC.clear();

    super.dispose();
  }

  Future<void> _getData() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          // _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false);
        });
        final dataResponse = await Future.wait([
          TraHangNCCFunction.getData(widget.token.toString(), widget.id.toString()),
        ]);
        if (!mounted) return;
        setState(() {
          _isLoading = false;
        });
        if (dataResponse.isNotEmpty) {
          if ((dataResponse[0] as DataRequestReturnVendorModel?) != null) {
            setState(() {
              _dataServer = dataResponse[0] as DataRequestReturnVendorModel;

              Set<String> uniquePoNumbers = {};
              for (DataTraHangNCC data in _dataServer!.dataList ?? []) {
                if (data.poNumber != null) {
                  uniquePoNumbers.add(data.poNumber!);
                }
              }

              for (String poNumber in uniquePoNumbers) {
                var poDataList = _dataServer!.dataList!.where((d) => d.poNumber == poNumber).toList();
                _listRequestTraHangNCC.add(RequestTraHangNCC(poNumber: poNumber, dataList: poDataList, requestReturnVendorId: _dataServer?.id));
              }

              for (var i = 0; i < _listRequestTraHangNCC.length; i++) {
                List<TextEditingController> controllerListMaNVL = [];
                List<TextEditingController> controllerListSoLuong = [];

                for (var j = 0; j < _listRequestTraHangNCC[i].dataList.length; j++) {
                  var maNVL = _listRequestTraHangNCC[i].dataList[j].maNVL;

                  var soLuongTra = _listRequestTraHangNCC[i].dataList[j].soLuongTra;
                  var soLuongTraThucTe = _listRequestTraHangNCC[i].dataList[j].soLuongThucTe;

                  if (double.parse(soLuongTra!) >= 1) {
                    soLuongTra = double.parse(soLuongTra).round().toString();
                  }

                  if (soLuongTraThucTe != "null" && double.parse(soLuongTraThucTe!) >= 1) {
                    soLuongTraThucTe = double.parse(soLuongTraThucTe).round().toString();
                  }

                  controllerListMaNVL.add(TextEditingController(text: maNVL));
                  controllerListSoLuong.add(TextEditingController(text: soLuongTraThucTe != "null" ? soLuongTraThucTe : soLuongTra));
                }

                var soPo = _listRequestTraHangNCC[i].poNumber;

                _lsControllerMaNVL.add(controllerListMaNVL);
                _lsControllerSoLuong.add(controllerListSoLuong);
                _lsControllerPO.add(TextEditingController(text: soPo));
              }
            });
          }
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _timeOut = false;
        _error = error.toString();
      });
    }

    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);

    // _listRequestTraHangNCC.add(RequestTraHangNCC(poNumber: "", dataList: [TraHangNCCInfoFunction.defaultTraHangNCCInfo()]));

    // for (var i = 0; i < _listRequestTraHangNCC.length; i++) {
    //   List<TextEditingController> controllerListMaNVL = [];
    //   List<TextEditingController> controllerListSoLuong = [];
    //   for (var j = 0; j < _listRequestTraHangNCC[i].dataList.length; j++) {
    //     controllerListMaNVL.add(TextEditingController());
    //     controllerListSoLuong.add(TextEditingController());
    //   }
    //   _lsControllerMaNVL.add(controllerListMaNVL);
    //   _lsControllerSoLuong.add(controllerListSoLuong);
    //   _lsControllerPO.add(TextEditingController());
    // }
  }

  Future<void> _loadDataAndSetDefault() async {
    _listRequestTraHangNCC.add(RequestTraHangNCC(poNumber: "", dataList: [TraHangNCCInfoFunction.defaultTraHangNCCInfo()]));

    for (var i = 0; i < _listRequestTraHangNCC.length; i++) {
      List<TextEditingController> controllerListMaNVL = [];
      List<TextEditingController> controllerListSoLuong = [];
      for (var j = 0; j < _listRequestTraHangNCC[i].dataList.length; j++) {
        controllerListMaNVL.add(TextEditingController());
        controllerListSoLuong.add(TextEditingController());
      }
      _lsControllerMaNVL.add(controllerListMaNVL);
      _lsControllerSoLuong.add(controllerListSoLuong);
      _lsControllerPO.add(TextEditingController());
    }
  }

  void createDummyData() {
    // First request with two child items
    List<DataTraHangNCC> request1Data = [
      DataTraHangNCC(
        stt: 1,
        maNVL: '291012293',
        tenNVL: 'Nút giảm ồn nhựa trắng trong Ø8x1,5',
        soLuongTra: '150',
        soLuongPO: "300.0",
        dvt: 'CAI',
      ),
      DataTraHangNCC(
        stt: 2,
        maNVL: '291001102',
        tenNVL: 'Tán sò không vành ren mm 6x12',
        soLuongTra: '50',
        soLuongPO: "100.0",
        dvt: 'CAI',
      ),
    ];
    RequestTraHangNCC request1 = RequestTraHangNCC(
      poNumber: '4100048789',
      dataList: request1Data,
    );
    _listRequestTraHangNCC.add(request1);

    // Second request with one child item
    List<DataTraHangNCC> request2Data = [
      DataTraHangNCC(
        stt: 1,
        maNVL: '291000522',
        tenNVL: 'Tán sò không vành mm Sxi7M 6x20',
        soLuongTra: '200',
        soLuongPO: "400.0",
        dvt: 'CON',
      ),
    ];
    RequestTraHangNCC request2 = RequestTraHangNCC(
      poNumber: '4100047333',
      dataList: request2Data,
    );
    _listRequestTraHangNCC.add(request2);
  }

  void addNewPO() {
    setState(() {
      // Create a new RequestTraHangNCC
      RequestTraHangNCC newRequest = RequestTraHangNCC(poNumber: "", dataList: [TraHangNCCInfoFunction.defaultTraHangNCCInfo()]);

      // Add the new RequestTraHangNCC to _listRequestTraHangNCC
      _listRequestTraHangNCC.add(newRequest);

      // Now, create Controllers for this new RequestTraHangNCC
      List<TextEditingController> controllerListMaNVL = [];
      List<TextEditingController> controllerListSoLuong = [];

      for (var j = 0; j < newRequest.dataList.length; j++) {
        controllerListMaNVL.add(TextEditingController());
        controllerListSoLuong.add(TextEditingController());
      }

      // Add to existing list of controllers
      _lsControllerMaNVL.add(controllerListMaNVL);
      _lsControllerSoLuong.add(controllerListSoLuong);
      _lsControllerPO.add(TextEditingController());
    });
  }

  Future<void> submitData() async {
    // Post data
    FocusManager.instance.primaryFocus?.unfocus();
    TraHangNCCFunction.sendRequestReturnVendor(context, _listRequestTraHangNCC, _returnType, widget.token.toString());
  }

  Future<void> onChangeSoLuong(int parentIndex, String value, int index) async {
    if (!mounted) return;

    setState(() {
      selectedIndex = index;
      poIndex = parentIndex;
      _listRequestTraHangNCC[parentIndex].dataList[selectedIndex].soLuongTra = value;
    });
  }

  Future<void> onChangeNVL(int parentIndex, String value, int index) async {
    _checkError(value);
    if (!mounted) return;

    setState(() {
      selectedIndex = index;
      poIndex = parentIndex;
    });

    _timer?.cancel();
    if (_errorInputAutoComplete == false) {
      _getAutocompleteProduct(value);
    }
  }

  Future<void> onChangePO(int parentIndex, value) async {
    _checkError(value);
    if (!mounted) return;

    setState(() {
      poIndex = parentIndex;
    });

    if (_returnType == 2) {
      setState(() {
        _listRequestTraHangNCC[parentIndex].poNumber = value.toString();
        // _lsControllerPO[parentIndex].text = value.toString();
      });
      return;
    }

    _timer?.cancel();
    if (_errorInputAutoComplete == false) {
      _getAutocompletePO(value);
    }
  }

  Future<void> _getAutocompletePO(String po) async {
    print(po);
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoading = true;
            });
            final data = await ListWarehouseTranferFunction.fetchPO(po, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoading = false;
              if (data != null) {
                _lsCodeNamePOSearch = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> onAutocompletePOPress(int requestIndex, int index) async {
    var po = _lsCodeNamePOSearch[index].key;

    var vendorName = _lsCodeNamePOSearch[index].value?.split(" | ")[1];

    _listRequestTraHangNCC[requestIndex].poNumber = po.toString();
    _lsControllerPO[requestIndex].text = po.toString();

    // I'm assuming fetchPODetails is some instance method you have
    await fetchPODetails(po.toString());

    // setState here to update the UI
    if (mounted) {
      setState(() {
        selectedVendorName = vendorName;
        _lsCodeNamePOSearch = [];
        selectedIndex = -1;
      });
    }
  }

  Future<void> onAutocompleteNVLPress(int requestIndex, int index) async {
    // debugPrint("_lsCodeNameProductSearch.length");
    // debugPrint(_lsCodeNameProductSearch.length.toString());
    setState(() {
      // _controllerNVL.text = _lsCodeNameProductSearch[index].value ?? "";
      // _keyNVL = _lsCodeNameProductSearch[index].key;
      var maNVLTenNVL = _lsCodeNameProductSearch[index].value;
      var maNVL = _lsCodeNameProductSearch[index].key;
      var tenNVL = maNVLTenNVL?.replaceFirst(maNVL! + ' | ', '');
      var dvt = _lsCodeNameProductSearch[index].data;

      _lsControllerMaNVL[requestIndex][selectedIndex].text = _lsCodeNameProductSearch[index].key.toString();

      _listRequestTraHangNCC[requestIndex].dataList[selectedIndex].maNVL = maNVL;
      _listRequestTraHangNCC[requestIndex].dataList[selectedIndex].tenNVL = tenNVL;
      _listRequestTraHangNCC[requestIndex].dataList[selectedIndex].dvt = dvt;

      // Find on
      DataPurchaseOrderDetail? foundItem = findItem(_listRequestTraHangNCC[requestIndex].poNumber, maNVL!);

      if (foundItem != null) {
        _listRequestTraHangNCC[requestIndex].dataList[selectedIndex].soLuongPO = foundItem.poQuantity.toString();
        // _lsControllerSoLuong[requestIndex][selectedIndex].text = foundItem.quantity.toString();
      }

      // Clear data
      _lsCodeNameProductSearch = [];
      selectedIndex = -1;
    });
  }

  Future<void> fetchPODetails(String po) async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoading = true;
      });
      final tempData = await ListWarehouseTranferFunction.fetchPODetails(po, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _listPODetail.removeWhere((key, value) => key == po);
        _listPODetail[po] = tempData!;
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _getAutocompleteProduct(String productCode) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoading = true;
            });
            final data = await ListWarehouseTranferFunction.fechCodeNameDvt(productCode, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoading = false;
              if (data != null) {
                _lsCodeNameProductSearch = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  void _checkError(String input) {
    if (input.trim().length < 3) {
      if (_errorInputAutoComplete != true) {
        setState(() {
          _errorInputAutoComplete = true;
        });
      }
    } else {
      if (_errorInputAutoComplete != false) {
        setState(() {
          _errorInputAutoComplete = false;
        });
      }
    }
  }

  void addItem(int requestIndex) {
    setState(() {
      // Add a new data item to the specific RequestTraHangNCC's dataList
      _listRequestTraHangNCC[requestIndex].dataList.add(TraHangNCCInfoFunction.defaultTraHangNCCInfo());

      // Add new controllers to the specific lists of controllers
      _lsControllerMaNVL[requestIndex].add(TextEditingController());
      _lsControllerSoLuong[requestIndex].add(TextEditingController());
    });
  }

  void removeItem(int requestIndex, int dataIndex) {
    setState(() {
      // Remove data item and corresponding controllers by their indices
      _listRequestTraHangNCC[requestIndex].dataList.removeAt(dataIndex);
      _lsControllerMaNVL[requestIndex].removeAt(dataIndex);
      _lsControllerSoLuong[requestIndex].removeAt(dataIndex);
    });
  }

  void removePO(int requestIndex) {
    setState(() {
      _listRequestTraHangNCC.removeAt(requestIndex);
      _lsControllerMaNVL.removeAt(requestIndex);
      _lsControllerSoLuong.removeAt(requestIndex);
      _lsControllerPO.removeAt(requestIndex);

      if (_listRequestTraHangNCC.length == 0) {
        selectedVendorName = null;
      }
    });
  }

  DataPurchaseOrderDetail? findItem(String po, String materialCode) {
    List<DataPurchaseOrderDetail>? details = _listPODetail[po];
    if (details == null) {
      return null;
    }
    try {
      return details.firstWhere((detail) => detail.material == materialCode);
    } catch (e) {
      return null;
    }
  }

  Future<void> _getDataRawMaterial(String materialID, BuildContext context) async {
    final dataBarcodeReceive = await ExportWareHouseFunction.fetchBarcodeReceive(materialID, widget.token);

    var maNVL = dataBarcodeReceive?.productCode;
    var tenNVL = dataBarcodeReceive?.productName;
    double? soLuongPO = 0;

    if (maNVL != null) {
      DataPurchaseOrderDetail? foundItem = findItem(_listRequestTraHangNCC[poIndex].poNumber, maNVL!);

      if (foundItem != null) {
        soLuongPO = foundItem.poQuantity;
      }

      final data = await ListWarehouseTranferFunction.fechCodeNameDvt(maNVL, widget.token);

      if (data!.isNotEmpty) {
        final material = data[0];
        setState(() {
          _listRequestTraHangNCC[poIndex].dataList.add(DataTraHangNCC(
                maNVL: maNVL,
                tenNVL: tenNVL,
                dvt: material.data,
                soLuongPO: soLuongPO.toString(),
              ));

          _lsControllerMaNVL[poIndex].add(TextEditingController(text: maNVL));
          _lsControllerSoLuong[poIndex].add(TextEditingController());
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<QualityControlInformation> _lsQualityControlInformation = [];

    List<FocusNode> _lsFocusNode = [];

    Map<int, TableColumnWidth> columnWidthConfig() {
      return {
        0: FixedColumnWidth(30.w),
        1: FixedColumnWidth(80.w),
        2: FixedColumnWidth(150.w),
        3: FixedColumnWidth(70.w),
        4: FixedColumnWidth(70.w),
        5: FixedColumnWidth(70.w),
        6: FixedColumnWidth(70.w),
      };
    }

    const headerTitles = <Widget>[
      _TableHeader(text: "STT"),
      _TableHeader(text: "Mã NVL"),
      _TableHeader(text: "Tên NVL"),
      _TableHeader(text: "Số lượng trả"),
      _TableHeader(text: "Số lượng PO"),
      _TableHeader(text: "ĐVT"),
      _TableHeader(text: "Hành động"),
    ];

    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          isEdit ? "Phiếu yêu cầu trả hàng" : 'Tạo yêu cầu trả hàng NCC',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
        // actions: [
        //   IconButton(
        //     icon: Icon(Icons.qr_code_scanner),
        //     onPressed: () {
        //       // handle QR code scanning
        //     },
        //   ),
        // ],
      ),
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: _isLoading == true
            ? const Center(child: CircularProgressIndicator())
            : Container(
                // color: Colors.red,
                height: MediaQuery.of(context).size.height,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(10.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        !isEdit
                            ? Container()
                            : Visibility(
                                visible: isEdit,
                                child: Column(
                                  children: [
                                    TextInfo("Ngày tạo:", DateFormat("HH:mm dd/MM/yyyy").format(_dataServer!.createTime)),
                                    TextInfo("Số phiếu:", _dataServer!.requestReturnVendorCode),
                                    TextInfo("Loại:", _dataServer!.returnType == 1 ? "Trả hàng theo PO mua hàng" : "Trả hàng theo PO trả hàng"),
                                    TextInfo("NV tạo:", _dataServer!.creatorName),
                                    TextInfo(
                                        "NV xác nhận:", _dataServer!.warehouseStaffName == null ? "" : _dataServer!.warehouseStaffName.toString()),
                                    _dataServer!.returnType == 1 ? TextInfo("NCC:", _dataServer!.vendorName) : Container(),
                                    TextInfo("Trạng thái:", _dataServer!.warehouseStaffId.toString() == "null" ? "Mới tạo" : "NV kho đã xác nhận"),
                                    SizedBox(height: 10),
                                  ],
                                )),
                        Visibility(
                          visible: !isEdit,
                          child: Column(children: [
                            Row(
                              children: <Widget>[
                                Expanded(
                                  flex: 3,
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "Loại trả hàng",
                                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: <Widget>[
                                      Radio<int>(
                                        value: 1,
                                        groupValue: _returnType,
                                        onChanged: (int? value) {
                                          setState(() {
                                            _returnType = value!;
                                          });
                                        },
                                        activeColor: const Color(0xff0052cc),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _returnType = 1;
                                          });
                                        },
                                        child: const Text('trả hàng theo PO mua hàng'),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: <Widget>[
                                      Radio<int>(
                                        value: 2,
                                        groupValue: _returnType,
                                        onChanged: (int? value) {
                                          setState(() {
                                            _returnType = value!;
                                          });
                                        },
                                        activeColor: const Color(0xff0052cc),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _returnType = 2;
                                          });
                                        },
                                        child: const Text('trả hàng theo PO trả hàng'),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ]),
                        ),
                        Visibility(
                          visible: selectedVendorName != null && !isEdit,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2.0),
                            child: selectedVendorName != null ? TextInfo("NCC:", selectedVendorName!) : Container(),
                          ),
                        ),
                        ListView.builder(
                          shrinkWrap: true, // This needs to be true since you're in a Column widget
                          physics: NeverScrollableScrollPhysics(), // To disable nested scrolling in column
                          itemCount: _listRequestTraHangNCC.length,
                          itemBuilder: (context, requestIndex) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: <Widget>[
                                    Padding(
                                      padding: const EdgeInsets.only(right: 8.0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          (requestIndex + 1).toString() + ".Số PO",
                                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Align(
                                        child: Column(children: <Widget>[
                                          Container(
                                            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
                                            decoration: BoxDecoration(
                                                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                borderRadius: BorderRadius.circular(3.r)),
                                            child: TextFormField(
                                              maxLines: null,
                                              readOnly: isEdit,
                                              // textAlign: TextAlign.center,
                                              controller: _lsControllerPO[requestIndex],
                                              style: TextStyle(fontSize: 12.sp),
                                              keyboardType: TextInputType.number,
                                              // inputFormatters: <TextInputFormatter>[
                                              //   FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                              // ],
                                              decoration: InputDecoration(
                                                border: InputBorder.none,
                                                isDense: true,
                                                // contentPadding: EdgeInsets.zero,
                                                contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 4.w),
                                                // contentPadding: EdgeInsets.all(4),

                                                errorBorder: InputBorder.none,
                                                disabledBorder: InputBorder.none,
                                                filled: true,
                                                fillColor: Colors.white,
                                                hintStyle: TextStyle(fontSize: 12.sp),
                                              ),
                                              onChanged: (value) {
                                                onChangePO(requestIndex, value);
                                              },
                                            ),
                                          ),
                                          // SizedBox(height: errorQuantityCheck == true ? 10.h : 0),
                                          // _ErrorValidateView(error: errorQuantityCheck, text: "Vui lòng nhập SL kiểm tra"),
                                        ]),
                                      ),
                                    ),
                                    Visibility(
                                      visible: !isEdit,
                                      child: IconButton(
                                        highlightColor: Colors.transparent,
                                        hoverColor: Colors.transparent,
                                        constraints: const BoxConstraints(),
                                        iconSize: 17.sp,
                                        color: Colors.red.shade800,
                                        icon: const Icon(Icons.delete),
                                        onPressed: () {
                                          // _deleteItemListError(index);
                                          showAlert(
                                            context: context,
                                            title: 'Xác nhận',
                                            content: 'Bạn có chắc xóa PO này và tất cả line đã thêm',
                                            buttonsWithOnPressed: {
                                              'OK': () {
                                                removePO(requestIndex);
                                                Navigator.of(context).pop();
                                              },
                                              'Cancel': () => Navigator.of(context).pop(),
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Visibility(
                                  // Autocompleted
                                  visible: poIndex == requestIndex && (_lsCodeNamePOSearch.isNotEmpty || _isLoading == true),
                                  child: SizedBox(
                                    height: 250.h,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.5),
                                            spreadRadius: 5,
                                            blurRadius: 7,
                                            offset: const Offset(0, 3), // changes position of shadow
                                          ),
                                        ],
                                      ),
                                      child: _isLoading == true
                                          ? const Center(child: CircularProgressIndicator())
                                          : ListView.separated(
                                              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                              itemCount: _lsCodeNamePOSearch.length,
                                              itemBuilder: (BuildContext context, int index) {
                                                return InkWell(
                                                  onTap: () {
                                                    onAutocompletePOPress(requestIndex, index);
                                                  },
                                                  child: Text(_lsCodeNamePOSearch[index].value ?? ""),
                                                );
                                              },
                                              separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                                            ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Table(
                                            border: TableBorder.all(width: 0.5.w),
                                            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                            columnWidths: columnWidthConfig(),
                                            children: const <TableRow>[
                                              TableRow(
                                                decoration: BoxDecoration(
                                                  color: Color(0xff0052cc),
                                                ),
                                                children: headerTitles,
                                              ),
                                            ]),
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: _listRequestTraHangNCC.isNotEmpty
                                              ? List.generate(
                                                  (_listRequestTraHangNCC[requestIndex].dataList).length,
                                                  (index) => Table(
                                                    border: UIUtils.createTableBorder(),
                                                    columnWidths: columnWidthConfig(),
                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                    children: <TableRow>[
                                                      TableRow(
                                                        children: <Widget>[
                                                          // Columns Data
                                                          // _ColumnTableIV(text: _listRequestTraHangNCC[requestIndex].dataList[index].stt.toString()), // Col1
                                                          _ColumnTableIV(text: (index + 1).toString()), // STT
                                                          // _ColumnTableIV(text: _listRequestTraHangNCC[requestIndex].dataList[index].maNVL ?? ""), // Mã NVL
                                                          _ColumnTableInputField(
                                                            // enabled: _isLoading
                                                            //     ? false
                                                            //     : true, //_listRequestTraHangNCC[requestIndex].dataList[index].soLuongTra != 0.0,
                                                            enabled: true,
                                                            readOnly: isEdit,
                                                            controller: _lsControllerMaNVL[requestIndex][index],
                                                            onChanged: (value) {
                                                              onChangeNVL(requestIndex, value, index);
                                                            },
                                                          ),
                                                          _ColumnTableIV(
                                                              text: _listRequestTraHangNCC[requestIndex].dataList[index].tenNVL ?? ""), // Tên NVL
                                                          // Col4
                                                          _ColumnTableInputField(
                                                            enabled: true, //_listRequestTraHangNCC[requestIndex].dataList[index].soLuongTra != 0.0,
                                                            readOnly: isEdit && _dataServer!.warehouseStaffId != null,
                                                            controller: _lsControllerSoLuong[requestIndex][index],
                                                            onChanged: (value) {
                                                              onChangeSoLuong(requestIndex, value, index);
                                                            },
                                                          ),
                                                          // Col5
                                                          !isEdit || (isEdit && _dataServer!.returnType == 1)
                                                              ? _listRequestTraHangNCC[requestIndex].dataList[index].soLuongPO != null
                                                                  ? _ColumnTableIV(
                                                                      text: double.parse(
                                                                                  _listRequestTraHangNCC[requestIndex].dataList[index].soLuongPO!) >=
                                                                              1
                                                                          ? double.parse(
                                                                                  _listRequestTraHangNCC[requestIndex].dataList[index].soLuongPO!)
                                                                              .round()
                                                                              .toString()
                                                                          : _listRequestTraHangNCC[requestIndex].dataList[index].soLuongPO.toString())
                                                                  : Container()
                                                              : Container(),

                                                          _ColumnTableIV(text: _listRequestTraHangNCC[requestIndex].dataList[index].dvt ?? ""),
                                                          Visibility(
                                                            visible: !isEdit,
                                                            child: IconButton(
                                                              highlightColor: Colors.transparent,
                                                              hoverColor: Colors.transparent,
                                                              constraints: const BoxConstraints(),
                                                              iconSize: 17.sp,
                                                              color: Colors.red.shade800,
                                                              icon: const Icon(Icons.delete),
                                                              onPressed: () {
                                                                // _deleteItemListError(index);
                                                                removeItem(requestIndex, index);
                                                              },
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              : [],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Visibility(
                                  // Autocompleted
                                  visible: poIndex == requestIndex && (_lsCodeNameProductSearch.isNotEmpty || _isLoading == true),
                                  child: SizedBox(
                                    height: 250.h,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.5),
                                            spreadRadius: 5,
                                            blurRadius: 7,
                                            offset: const Offset(0, 3), // changes position of shadow
                                          ),
                                        ],
                                      ),
                                      child: _isLoading == true
                                          ? const Center(child: CircularProgressIndicator())
                                          : ListView.separated(
                                              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                              itemCount: _lsCodeNameProductSearch.length,
                                              itemBuilder: (BuildContext context, int index) {
                                                return InkWell(
                                                  onTap: () {
                                                    onAutocompleteNVLPress(requestIndex, index);
                                                  },
                                                  child: Text(_lsCodeNameProductSearch[index].value ?? ""),
                                                );
                                              },
                                              separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                                            ),
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: !isEdit,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Container(
                                        decoration: const BoxDecoration(),
                                        child: ElevatedButton.icon(
                                          style: ButtonStyle(
                                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                            side: MaterialStateProperty.all(BorderSide(color: const Color(0xff303F9F))),
                                            backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                          ),
                                          onPressed: () async {
                                            String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                            DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                            DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                            if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                              Platform.isAndroid
                                                  ? showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                  : showCupertinoDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                            } else {
                                              FocusScope.of(context).unfocus();
                                              final data = await Navigator.pushNamed(context, '/QRcodePageTranferMaterial');
                                              if (!mounted) return;
                                              setState(() {
                                                poIndex = requestIndex;
                                              });
                                              if (data == null) return;
                                              if ((data as GetBackDataQRCodePage).isScan == true) {
                                                if (!mounted) return;
                                                await _getDataRawMaterial(data.materialID.toString(), context);
                                              }
                                            }
                                          },
                                          icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                          label: Text(
                                            "Quét mã",
                                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(left: 8),
                                        child: AddButton(
                                          onPressed: () {
                                            addItem(requestIndex);
                                          },
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(height: 20),
                              ],
                            );
                          },
                        ),
                        SizedBox(height: 10),
                        Visibility(
                          visible: !isEdit,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ElevatedButton.icon(
                                style: ButtonStyle(
                                  padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 4.h, horizontal: 10.w)),
                                  backgroundColor: MaterialStateProperty.all(Colors.blue),
                                ),
                                icon: const Icon(
                                  Icons.add,
                                  color: Colors.white,
                                ),
                                label: const Text('Thêm PO', style: TextStyle(color: Colors.white)),
                                onPressed: () {
                                  addNewPO();
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 50),
                        Visibility(
                          visible: !isEdit || (isEdit && _dataServer!.warehouseStaffId == null),
                          // The `isCreatingNew` variable should be true when creating a new record
                          // The second clause checks if we are editing and the warehouseStaffId is null
                          // The `isEdit` variable should be true when in edit mode

                          child: Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () {
                                    submitData();
                                  },
                                  child: Text(isEdit ? 'Xác nhận' : 'Submit', style: TextStyle(color: Colors.white)),
                                  style: ButtonStyle(
                                    backgroundColor: MaterialStateProperty.all<Color>(Color(0xff0052cc)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // _data.isEmpty
                        //     ? Text('No data available')
                        //     : ListView.builder(
                        //         shrinkWrap: true, // This needs to be true since you're in a Column widget
                        //         physics: NeverScrollableScrollPhysics(), // To disable nested scrolling in column
                        //         itemCount: _data.length,
                        //         itemBuilder: (context, index) {
                        //           String key = _data.keys.elementAt(index);
                        //           List<DataPurchaseOrderDetail>? values = _data[key];
                        //           return Row(
                        //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //             children: [
                        //               Text("$key:"),
                        //               Text("${values?.length} items"),
                        //             ],
                        //           );
                        //         },
                        //       )
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}

class TextInfo extends StatelessWidget {
  final String label;
  final String info;

  const TextInfo(
    this.label,
    this.info,
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              label.toString(),
              style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(width: 5),
            Text(
              info.toString(),
              style: TextStyle(fontSize: 12.sp),
            ),
          ],
        ),
        SizedBox(height: 10),
      ],
    );
  }
}

class _ColumnTableInputField extends StatelessWidget {
  final bool enabled;
  final bool readOnly;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final String hintText;

  const _ColumnTableInputField({
    Key? key,
    required this.enabled,
    this.readOnly = false,
    required this.controller,
    this.onChanged,
    this.hintText = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: DecimalTextInputField(
          enabled: enabled,
          readOnly: readOnly,
          maxLines: null,
          controller: controller,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          hintText: hintText,
          onChanged: onChanged,
        ),
      ),
    );
  }
}

// Scraffold, rèactor

TableBorder createTableBorder() {
  return TableBorder(
    left: BorderSide(color: Colors.black, width: 0.5.w),
    right: BorderSide(color: Colors.black, width: 0.5.w),
    bottom: BorderSide(color: Colors.black, width: 0.5.w),
    top: BorderSide(color: Colors.black, width: 0.5.w),
    horizontalInside: BorderSide(color: Colors.black, width: 0.5.w),
    verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
  );
}

class _ColumnTableIV extends StatelessWidget {
  final String text;
  const _ColumnTableIV({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        child: Text(text, style: TextStyle(fontSize: 12.sp), textAlign: TextAlign.center));
  }
}

class _TableHeader extends StatelessWidget {
  final String text;
  const _TableHeader({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 4.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class AddButton extends StatelessWidget {
  final VoidCallback onPressed;

  const AddButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: Icon(
        Icons.add,
        color: Colors.white,
        size: 15.sp,
      ),
      style: ButtonStyle(
        side: MaterialStateProperty.all(
          const BorderSide(
            color: Colors.green,
          ),
        ),
        backgroundColor: MaterialStateProperty.all(Colors.green),
      ),
      onPressed: onPressed,
      label: Text(
        'Thêm',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 11.sp,
        ),
      ),
    );
  }
}

IntrinsicHeight itemHeader() {
  return IntrinsicHeight(
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
            ),
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
            child: Text(
              "Stt",
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        Expanded(
          flex: 5,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
            ),
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
            child: Text(
              "Thông tin kiểm tra",
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
            ),
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
            child: Text(
              "Ghi chú",
              style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    ),
  );
}
