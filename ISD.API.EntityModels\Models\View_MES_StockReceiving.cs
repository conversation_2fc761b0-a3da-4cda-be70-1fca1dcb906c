﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Keyless]
    public partial class View_MES_StockReceiving
    {
        [Required]
        [StringLength(4000)]
        public string Summary { get; set; }
        public Guid? StockId { get; set; }
        public Guid? ProductId { get; set; }
        [StringLength(500)]
        public string ProductAttributes { get; set; }
        [StringLength(10)]
        public string StockRecevingType { get; set; }
        [Column(TypeName = "decimal(38, 2)")]
        public decimal? Qty { get; set; }
    }
}