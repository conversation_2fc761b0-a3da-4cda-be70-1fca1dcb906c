class ProductionOrderViewModel {
  String? taskId;
  String? productId;
  String? productAttributes;
  int? phase;
  String? stepCode;
  String? departmentId;
  String? fromDate;
  String? toDate;
  String? workDate;

  ProductionOrderViewModel(
      {this.taskId, this.productId, this.productAttributes, this.phase, this.stepCode, this.departmentId, this.fromDate, this.toDate, this.workDate});

  ProductionOrderViewModel.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'];
    productId = json['productId'];
    productAttributes = json['productAttributes'];
    phase = json['phase'];
    stepCode = json['stepCode'];
    departmentId = json['departmentId'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
    workDate = json['workDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['taskId'] = taskId;
    data['productId'] = productId;
    data['productAttributes'] = productAttributes;
    data['phase'] = phase;
    data['stepCode'] = stepCode;
    data['departmentId'] = departmentId;
    data['fromDate'] = fromDate;
    data['toDate'] = toDate;
    data['workDate'] = workDate;
    return data;
  }
}

class ProductionOrderDetailOld {
  String? fromTime;
  String? toTime;
  double? quantityD;
  double? quantityKD;

  ProductionOrderDetailOld({this.fromTime, this.toTime, this.quantityD, this.quantityKD});

  ProductionOrderDetailOld.fromJson(Map<String, dynamic> json) {
    fromTime = json['fromTime'];
    toTime = json['toTime'];
    quantityD = json['quantity_D'];
    quantityKD = json['quantity_KD'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fromTime'] = fromTime;
    data['toTime'] = toTime;
    data['quantity_D'] = quantityD;
    data['quantity_KD'] = quantityKD;
    return data;
  }
}

class UsageQuantityViewModels {
  String? productAttributes;
  String? itmno;
  int? bmschdc;

  UsageQuantityViewModels({this.productAttributes, this.itmno, this.bmschdc});

  UsageQuantityViewModels.fromJson(Map<String, dynamic> json) {
    productAttributes = json['productAttributes'];
    itmno = json['itmno'];
    bmschdc = json['bmschdc'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productAttributes'] = productAttributes;
    data['itmno'] = itmno;
    data['bmschdc'] = bmschdc;
    return data;
  }
}
