using AutoMapper;
using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.Repositories.Interfaces;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MaiDao;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class MaiDaoController : ControllerBaseAPI
    {
        private readonly IMapper _mapper;
        private readonly IMaiDaoRepository _repository;
        private readonly ILogger<MaiDaoController> _logger;

        public MaiDaoController(
            IMapper mapper,
            IMaiDaoRepository repository,
            ILogger<MaiDaoController> logger)
        {
            _mapper = mapper;
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Get list of MaiDao records with optional filters
        /// </summary>
        /// <param name="model">Search parameters</param>
        /// <returns>List of MaiDao records</returns>
        [HttpPost("MaiDaoList")]
        public async Task<IActionResult> GetMaiDaoList([FromBody] MaiDaoSearchModel model)
        {
            try
            {
                _logger.LogInformation($"GetMaiDaoList called with filters: CompanyCode={model.CompanyCode}, EquipmentCode={model.EquipmentCode}, OperationType={model.OperationType}, Status={model.Status}, FromDate={model.FromDate}, ToDate={model.ToDate}");
                
                var result = await _repository.GetMaiDaoListAsync(model);

                _logger.LogInformation($"GetMaiDaoList returned {result.Data?.Count ?? 0} records out of {result.TotalCount} total");

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = result.Status,
                    Message = result.Message,
                    Data = result.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaiDaoList: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get MaiDao record by ID
        /// </summary>
        /// <param name="id">MaiDao ID</param>
        /// <returns>MaiDao record details</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetMaiDaoById(string id)
        {
            try
            {
                if (!Guid.TryParse(id, out var maiDaoId))
                {
                    _logger.LogWarning($"Invalid MaiDao ID format: {id}");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid MaiDao ID format"
                    });
                }

                var result = await _repository.GetMaiDaoByIdAsync(maiDaoId);

                if (!result.Status)
                {
                    _logger.LogWarning($"GetMaiDaoById returned no record for id: {id}");
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = result.Message ?? "Không tìm thấy bản ghi"
                    });
                }

                _logger.LogDebug($"GetMaiDaoById returned record for id: {id}");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = result.Message,
                    Data = result.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaiDaoById: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Create a new MaiDao record
        /// </summary>
        /// <param name="model">MaiDao record data</param>
        /// <returns>Created MaiDao record</returns>
        [HttpPost("CreateMaiDao")]
        public async Task<IActionResult> CreateMaiDao([FromBody] MaiDaoRecordVM model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid model data"
                    });
                }

                // Get user ID from claims
                var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    _logger.LogWarning("User not authorized or invalid user ID");
                    return Unauthorized(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.Unauthorized,
                        IsSuccess = false,
                        Message = "User not authorized or invalid user ID"
                    });
                }

                var result = await _repository.CreateMaiDaoAsync(model, userId);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = result.Status,
                    Message = result.Message,
                    Data = result.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in CreateMaiDao: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Update an existing MaiDao record
        /// </summary>
        /// <param name="id">MaiDao ID</param>
        /// <param name="model">Updated MaiDao record data</param>
        /// <returns>Updated MaiDao record</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMaiDao(string id, [FromBody] MaiDaoRecordVM model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid model data"
                    });
                }

                if (!Guid.TryParse(id, out var maiDaoId))
                {
                    _logger.LogWarning($"Invalid MaiDao ID format: {id}");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid MaiDao ID format"
                    });
                }

                // Get user ID from claims
                var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    _logger.LogWarning("User not authorized or invalid user ID");
                    return Unauthorized(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.Unauthorized,
                        IsSuccess = false,
                        Message = "User not authorized or invalid user ID"
                    });
                }

                var result = await _repository.UpdateMaiDaoAsync(maiDaoId, model, userId);

                if (!result.Status)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.NotFound,
                        IsSuccess = false,
                        Message = result.Message ?? "Không tìm thấy bản ghi"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = result.Message,
                    Data = result.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in UpdateMaiDao: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Check for existing incomplete MaiDao record for equipment
        /// </summary>
        /// <param name="equipmentCode">Equipment Code</param>
        /// <param name="companyCode">Company Code</param>
        /// <returns>Existing incomplete record if found</returns>
        [HttpGet("CheckExistingRecord")]
        public async Task<IActionResult> CheckExistingRecord([FromQuery] string equipmentCode, [FromQuery] string companyCode)
        {
            try
            {
                if (string.IsNullOrEmpty(equipmentCode) || string.IsNullOrEmpty(companyCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Equipment code and company code are required"
                    });
                }

                var result = await _repository.CheckExistingRecordAsync(equipmentCode, companyCode);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = result != null ? "Existing record found" : "No existing record found",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in CheckExistingRecord: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get equipment suggestions for autocomplete
        /// </summary>
        /// <param name="companyCode">Company Code</param>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of equipment suggestions</returns>
        [HttpGet("GetEquipment")]
        public async Task<IActionResult> GetEquipment([FromQuery] string companyCode, [FromQuery] string searchTerm = "")
        {
            try
            {
                var result = await _repository.GetEquipmentAsync(companyCode, searchTerm);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Success",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetEquipment: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get materials suggestions for autocomplete
        /// </summary>
        /// <param name="companyCode">Company Code</param>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of materials suggestions</returns>
        [HttpGet("GetMaterials")]
        public async Task<IActionResult> GetMaterials([FromQuery] string companyCode, [FromQuery] string searchTerm = "")
        {
            try
            {
                var result = await _repository.GetMaterialsAsync(companyCode, searchTerm);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Success",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaterials: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get employees for dropdown/autocomplete
        /// </summary>
        /// <param name="companyCode">Company Code</param>
        /// <returns>List of employees</returns>
        [HttpGet("GetEmployees")]
        public async Task<IActionResult> GetEmployees([FromQuery] string companyCode)
        {
            try
            {
                if (string.IsNullOrEmpty(companyCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Company code is required"
                    });
                }

                var result = await _repository.GetEmployeesAsync(companyCode);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Success",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetEmployees: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("test")]
        [AllowAnonymous]
        public async Task<IActionResult> Test()
        {
            try
            {
                // Create a basic search model to test the repository
                var testSearchModel = new MaiDaoSearchModel
                {
                    CompanyCode = "1000", // Default company code
                    PageNumber = 1,
                    PageSize = 20
                };

                var result = await _repository.GetMaiDaoListAsync(testSearchModel);

                return Ok(new
                {
                    message = "MaiDaoController is reachable",
                    testResult = new
                    {
                        status = result.Status,
                        message = result.Message,
                        totalCount = result.TotalCount,
                        dataCount = result.Data?.Count ?? 0
                    }
                });
            }
            catch (Exception ex)
            {
                return Ok(new
                {
                    message = "MaiDaoController is reachable but test failed",
                    error = ex.Message
                });
            }
        }

        [HttpPost("CreateTestData")]
        [AllowAnonymous]
        public async Task<IActionResult> CreateTestData()
        {
            try
            {
                // Create test data if none exists
                var testRecord = new MaiDaoRecordVM
                {
                    Date = DateTime.Now.ToString("dd/MM/yyyy"),
                    EquipmentCode = "EQ001",
                    EquipmentName = "Test Equipment",
                    MaterialCode = "MAT001",
                    MaterialName = "Test Material",
                    OperationType = "Mài dao",
                    EmployeeCodes = "NV001",
                    EmployeeNames = "Test Employee",
                    RequestingEmployeeCode = "NV002",
                    RequestingEmployeeName = "Test Requestor",
                    Note = "Test note",
                    Status = "Created",
                    CompanyCode = "1000"
                };

                var result = await _repository.CreateMaiDaoAsync(testRecord, Guid.NewGuid());

                return Ok(new
                {
                    message = "Test data created",
                    result = new
                    {
                        status = result.Status,
                        message = result.Message,
                        id = result.Data?.MaiDaoId
                    }
                });
            }
            catch (Exception ex)
            {
                return Ok(new
                {
                    message = "Failed to create test data",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get MaiDao history by ID (for debugging and testing purposes)
        /// </summary>
        /// <param name="id">MaiDao ID</param>
        /// <returns>List of history records</returns>
        [HttpGet("{id}/history")]
        public async Task<IActionResult> GetMaiDaoHistory(string id)
        {
            try
            {
                if (!Guid.TryParse(id, out var maiDaoId))
                {
                    _logger.LogWarning($"Invalid MaiDao ID format: {id}");
                    return BadRequest(new ApiResponse
                    {
                        Code = (int)HttpStatusCode.BadRequest,
                        IsSuccess = false,
                        Message = "Invalid MaiDao ID format"
                    });
                }

                var history = await _repository.GetMaiDaoHistoryAsync(maiDaoId);

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Message = "Success",
                    Data = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetMaiDaoHistory: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }
    }
} 