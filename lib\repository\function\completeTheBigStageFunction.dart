import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/confirmWorkCenterApi.dart';
import '../api/confirmWorkCenterApi.dart';

class CompleteTheBigStageFunction {
  static bool checkIsSend = false;
  static String getDate(DateTime? date) {
    if (date == null) {
      return DateFormat('dd/MM/yyyy').format(DateTime.now());
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
      // return '${date.month}/${date.day}/${date.year}';
    }
  }

  static String formatDatePost(DateTime? date) {
    DateTime getFormatDateTime = DateFormat("dd/MM/yyyy").parse(getDate(date));
    String getFormatDateString = DateFormat("yyyy-MM-dd").format(getFormatDateTime);
    return getFormatDateString;
  }

  static Future<void> postCompleteTheBigStage(
      BuildContext context, DataConfirmWorkCenter getData, String confirmWorkCenter, String token, DateTime? date) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      final response = await ConfirmWorkCenterApi.postConfirmWorkCenter(
          getData.taskId.toString(), confirmWorkCenter, CompleteTheBigStageFunction.formatDatePost(date), token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final check = jsonDecode(response.body);
        if (check['code'] == 201 && check['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              check['data'],
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
          ));
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context, true);
          });
        } else {
          showDialog(
              context: context, barrierDismissible: false, builder: (BuildContext context) => DiaLogErrorValidate(message: check['data'].toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   backgroundColor: Colors.black,
          //   content: Text(
          //     'Cập nhật công đoạn lớn Thất bại',
          //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //   ),
          // ));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //   backgroundColor: Colors.black,
        //   content: Text(
        //     'Xảy ra lỗi! vui lòng thử lại sau',
        //     style: TextStyle(fontSize: 15.sp, color: Colors.white),
        //   ),
        // ));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra Lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    }
  }

  static String getCreateTime(DataConfirmWorkCenter getData) {
    DateTime stringToDateTime = DateFormat("yyyy-MM-ddThh:mm").parse(getData.createTime.toString());
    String dateToString = DateFormat("dd/MM/yyyy hh:mm").format(stringToDateTime);
    return dateToString;
  }
}
