import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Storage/storageSharedPreferences.dart';
import 'package:ttf/model/getStatusReservation.dart';
import 'package:ttf/model/getStatusWarehouseTranfer.dart';
import 'package:ttf/model/saveEndDrawerListWareHouse.dart';
import 'package:ttf/model/slocAddresse.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../model/CodeNameProduct.dart';
import '../model/drawerFilterTranferMaterial.dart';
import '../repository/function/endDrawerListWareHouseTranfer.dart';
import '../repository/function/listWarehouseTranferFunction.dart';
import '../repository/showDateTime.dart';
import 'ColumnTextFieldLsWHTranfer.dart';

class EndDrawerListWarehouseTranfer extends StatefulWidget {
  final List<DataGetStatusReservation> lsDataGetStatusReservation;
  final List<DataGetStatusWarehouseTranfer> lsDataGetStatusWarehouseTranfer;
  final String plant;
  final void Function(DrawerFilterTranferMaterial) onFilterSelected;
  final String token;
  final List<DataSlocAddress> lsDataSlocAddress;
  final String accountId;

  const EndDrawerListWarehouseTranfer(
      {Key? key,
      required this.lsDataGetStatusReservation,
      required this.lsDataGetStatusWarehouseTranfer,
      required this.plant,
      required this.onFilterSelected,
      required this.token,
      required this.lsDataSlocAddress,
      required this.accountId})
      : super(key: key);

  @override
  _EndDrawerListWarehouseTranferState createState() => _EndDrawerListWarehouseTranferState();
}

class _EndDrawerListWarehouseTranferState extends State<EndDrawerListWarehouseTranfer> {
  final _controllerReservation = TextEditingController();
  final _controllerNVL = TextEditingController();
  final _focusReserVation = FocusNode();
  final _focusControllerNVL = FocusNode();
  late DateTime _fromDateRequest;
  late DateTime _toDateRequest;
  DateTime? _fromDateTrans;
  DateTime? _toDateTrans;
  late DataGetStatusReservation? _selectReservation;
  DataGetStatusWarehouseTranfer? _selectStatus;
  DataSlocAddress? _selectSlocAddressImport;
  DataSlocAddress? _selectSlocAddressExport;
  String? _keyNVL;
  List<DataCodenameProduct> _lsCodeNameProduct = [];
  bool _isLoading = false;
  Timer? _timer;
  bool _errorInputAutoComplete = false;
  bool _showNotiError = false;
  String _messageNoti = "";

  @override
  void initState() {
    super.initState();
    _init();
  }

  void _init() {
    String? getData = StorageSharedPreferences.getString("listTranferSearch${widget.accountId}");
    if (getData != null) {
      SaveEndDrawerListWareHouse saveEndDrawerListWareHouse = SaveEndDrawerListWareHouse.fromJson(jsonDecode(getData));
      if (saveEndDrawerListWareHouse.postFilterWareHouseTranfer != null) {
        _selectSlocAddressExport = widget.lsDataSlocAddress
            .firstWhereOrNull((element) => element.sloc != null && element.sloc == saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.slocExport);
        _selectSlocAddressImport = widget.lsDataSlocAddress
            .firstWhereOrNull((element) => element.sloc != null && element.sloc == saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.slocImport);
        _controllerReservation.text = saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.reservationCode ?? "";
        _selectReservation = widget.lsDataGetStatusReservation
                .firstWhereOrNull((element) => element.key == saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.statusReservation) ??
            widget.lsDataGetStatusReservation.firstWhereOrNull((element) => element.value == "Đã duyệt");
        _selectStatus = widget.lsDataGetStatusWarehouseTranfer
            .firstWhereOrNull((element) => element.key == saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.statusWarehouse);
        _controllerNVL.text = saveEndDrawerListWareHouse.value ?? "";
        _keyNVL = saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.productCode;
        _fromDateRequest =
            saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.requestDateFrom ?? DateTime(DateTime.now().year, DateTime.now().month, 1);
        _toDateRequest =
            saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.requestDateTo ?? DateTime(DateTime.now().year, DateTime.now().month + 1, 0);
        _fromDateTrans = saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.documentDateFrom;
        _toDateTrans = saveEndDrawerListWareHouse.postFilterWareHouseTranfer!.documentDateTo;
      }
    } else {
      _selectReservation = widget.lsDataGetStatusReservation.firstWhereOrNull((element) => element.value == "Đã duyệt");
      _fromDateRequest = DateTime(DateTime.now().year, DateTime.now().month, 1);
      _toDateRequest = DateTime(DateTime.now().year, DateTime.now().month + 1, 0);
    }
  }

  Future<void> _getAutocompleteProduct(String productCode) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() {
        _timer = Timer(duration, () async {
          if (!mounted) return;

          setState(() {
            _isLoading = true;
          });
          final data = await ListWarehouseTranferFunction.fechCodeNameWarehouseTranfer(productCode, widget.token);
          if (!mounted) return;
          setState(() {
            _isLoading = false;
            if (data != null) {
              _lsCodeNameProduct = data;
            }
          });
        });
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _sendBackData(BuildContext context) async {
    try {
      // print(_selectSlocAddressImport.toString());
      final data = await EndDrawerListWareHouseTranferFunction.sendBackData(
          context,
          widget.plant,
          _selectSlocAddressExport != null ? _selectSlocAddressExport!.sloc : null,
          _selectSlocAddressImport != null ? _selectSlocAddressImport!.sloc : null,
          _controllerReservation.text,
          _selectReservation,
          _keyNVL,
          _selectStatus,
          widget.token,
          0,
          0,
          5,
          _fromDateRequest,
          _toDateRequest,
          _fromDateTrans,
          _toDateTrans,
          widget.accountId,
          _controllerNVL.text);
      if (!mounted) return;
      widget.onFilterSelected(data);
      Navigator.pop(context);
    } on SocketException catch (_) {
      Navigator.pop(context);
      if (!mounted) return;
      setState(() {
        _showNotiError = true;
        _messageNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      debugPrint(error.toString());
      Navigator.pop(context);
      if (!mounted) return;
      setState(() {
        _showNotiError = true;
        _messageNoti = "Ứng dụng xảy ra lỗi! vui lòng thử lại sau";
      });
    }
  }

  void _checkError(String input) {
    if (input.trim().length < 3) {
      if (_errorInputAutoComplete != true || _keyNVL != null) {
        setState(() {
          _errorInputAutoComplete = true;
          _keyNVL = null;
        });
      }
    } else {
      if (_errorInputAutoComplete != false || _keyNVL != null) {
        setState(() {
          _errorInputAutoComplete = false;
          _keyNVL = null;
        });
      }
    }
  }

  Future<void> _onChangeNVL(String value) async {
    _checkError(value);
    if (!mounted) return;
    // await Future<void>.delayed(const Duration(seconds: 1));
    _timer?.cancel();
    if (_errorInputAutoComplete == false) {
      // final checkTimer = _timer == null ? false : true;
      _getAutocompleteProduct(value);
    }
  }

  Future<void> _pickFromDateRequest(BuildContext context) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: _fromDateRequest,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _fromDateRequest = newDate;
    });
  }

  Future<void> _pickToDateRequest(BuildContext context) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: _toDateRequest,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _toDateRequest = newDate;
    });
  }

  Future<void> _pickFromDateTrans(BuildContext context) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: _fromDateTrans == null ? DateTime.now() : _fromDateTrans!,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _fromDateTrans = newDate;
    });
  }

  Future<void> _pickToDateTrans(BuildContext context) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: _toDateTrans == null ? DateTime.now() : _toDateTrans!,
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _toDateTrans = newDate;
    });
  }

  Future<void> _pickFromDateRequestIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerLsWareHouseIOS(context, _fromDateRequest);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _fromDateRequest = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickToDateRequestIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerLsWareHouseIOS(context, _toDateRequest);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _toDateRequest = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickFromDateTransIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _fromDateTrans = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  Future<void> _pickToDateTransIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _toDateTrans = DateTime(newDate.year, newDate.month, newDate.day);
      });
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  void dispose() {
    _controllerReservation.dispose();
    _controllerNVL.dispose();
    _focusReserVation.dispose();
    _focusControllerNVL.dispose();
    if (_timer != null) {
      _timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: GestureDetector(
          onTap: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: Drawer(
                    backgroundColor: Colors.white,
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                                color: const Color(0xff0052cc),
                                child: Text(
                                  "Tìm kiếm",
                                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 15.w),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    const _Title(text: "Nhà máy:"),
                                    SizedBox(height: 5.h),
                                    Text(
                                      widget.plant,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    const _Title(text: "Kho xuất:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<DataSlocAddress>(
                                          isExpanded: true,
                                          itemHeight: null,
                                          isDense: true,
                                          value: _selectSlocAddressExport,
                                          iconSize: 15.sp,
                                          style: const TextStyle(color: Colors.white),
                                          onChanged: (DataSlocAddress? value) {
                                            if (_selectSlocAddressExport != value) {
                                              setState(() {
                                                if (value!.slocDisplay == "--Bỏ chọn--") {
                                                  _selectSlocAddressExport = null;
                                                } else {
                                                  _selectSlocAddressExport = value;
                                                }
                                              });
                                            }
                                          },
                                          items: widget.lsDataSlocAddress.map((DataSlocAddress sloc) {
                                            return DropdownMenuItem<DataSlocAddress>(
                                              value: sloc,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Text(
                                                  sloc.slocDisplay ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                          selectedItemBuilder: (BuildContext context) {
                                            return widget.lsDataSlocAddress.map<Widget>((DataSlocAddress sloc) {
                                              return Text(sloc.slocDisplay ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 12.sp), overflow: TextOverflow.ellipsis);
                                            }).toList();
                                          },
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    const _Title(text: "Kho nhập:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<DataSlocAddress>(
                                          isExpanded: true,
                                          itemHeight: null,
                                          isDense: true,
                                          value: _selectSlocAddressImport,
                                          iconSize: 15.sp,
                                          style: const TextStyle(color: Colors.white),
                                          onChanged: (DataSlocAddress? value) {
                                            if (_selectSlocAddressImport != value) {
                                              setState(() {
                                                if (value!.slocDisplay == "--Bỏ chọn--") {
                                                  _selectSlocAddressImport = null;
                                                } else {
                                                  _selectSlocAddressImport = value;
                                                }
                                              });
                                            }
                                          },
                                          items: widget.lsDataSlocAddress.map((DataSlocAddress sloc) {
                                            return DropdownMenuItem<DataSlocAddress>(
                                              value: sloc,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Text(
                                                  sloc.slocDisplay ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                          selectedItemBuilder: (BuildContext context) {
                                            return widget.lsDataSlocAddress.map<Widget>((DataSlocAddress sloc) {
                                              return Text(sloc.slocDisplay ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                            }).toList();
                                          },
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Stack(
                                      children: [
                                        // This will be positioned normally, as the base layer
                                        ColumnTextFieldLsWHTranfer(
                                          title: 'Số reservation',
                                          controller: _controllerReservation,
                                          focusNode: _focusReserVation,
                                        ),

                                        Visibility(
                                          visible: _controllerReservation.text.isNotEmpty,
                                          child: Positioned(
                                            bottom: 6,
                                            right: 10,
                                            child: GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  _controllerReservation.text = "";
                                                });
                                              },
                                              child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10.h),
                                    const _Title(text: "Trạng thái reservation:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<DataGetStatusReservation>(
                                          isExpanded: true,
                                          itemHeight: null,
                                          isDense: true,
                                          value: _selectReservation,
                                          iconSize: 15.sp,
                                          style: const TextStyle(color: Colors.white),
                                          onChanged: (DataGetStatusReservation? value) {
                                            if (_selectReservation != value) {
                                              setState(() {
                                                _selectReservation = value!;
                                              });
                                            }
                                          },
                                          items: widget.lsDataGetStatusReservation.map((DataGetStatusReservation reservation) {
                                            return DropdownMenuItem<DataGetStatusReservation>(
                                              value: reservation,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Text(
                                                  reservation.value ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                          selectedItemBuilder: (BuildContext context) {
                                            return widget.lsDataGetStatusReservation.map<Widget>((DataGetStatusReservation reservation) {
                                              return Text(reservation.value ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                            }).toList();
                                          },
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    const _Title(text: "Trạng thái:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<DataGetStatusWarehouseTranfer>(
                                          isExpanded: true,
                                          itemHeight: null,
                                          isDense: true,
                                          value: _selectStatus,
                                          iconSize: 15.sp,
                                          style: const TextStyle(color: Colors.white),
                                          onChanged: (DataGetStatusWarehouseTranfer? value) {
                                            if (_selectStatus != value) {
                                              setState(() {
                                                _selectStatus = value!;
                                              });
                                            }
                                          },
                                          items: widget.lsDataGetStatusWarehouseTranfer.map((DataGetStatusWarehouseTranfer status) {
                                            return DropdownMenuItem<DataGetStatusWarehouseTranfer>(
                                              value: status,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                child: Text(
                                                  status.value ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                          selectedItemBuilder: (BuildContext context) {
                                            return widget.lsDataGetStatusWarehouseTranfer.map<Widget>((DataGetStatusWarehouseTranfer status) {
                                              return Text(status.value ?? " ",
                                                  style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                            }).toList();
                                          },
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    ColumnTextFieldLsWHTranferOnchange(
                                      isLoading: _isLoading,
                                      focusNode: _focusControllerNVL,
                                      title: 'Mã NVL | Tên NVL',
                                      controller: _controllerNVL,
                                      onChange: (value) {
                                        _onChangeNVL(value);
                                      },
                                    ),
                                    SizedBox(height: _errorInputAutoComplete == true ? 10.h : 0),
                                    ContainerError.widgetError(_errorInputAutoComplete, 'Nhập tối thiểu 3 kí tự'),
                                    SizedBox(height: 10.h),
                                    Visibility(
                                      visible: _lsCodeNameProduct.isNotEmpty || _isLoading == true,
                                      child: SizedBox(
                                        height: 280.h,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withOpacity(0.5),
                                                spreadRadius: 5,
                                                blurRadius: 7,
                                                offset: const Offset(0, 3), // changes position of shadow
                                              ),
                                            ],
                                          ),
                                          child: _isLoading == true
                                              ? const Center(child: CircularProgressIndicator())
                                              : ListView.separated(
                                                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.h),
                                                  itemCount: _lsCodeNameProduct.length,
                                                  itemBuilder: (BuildContext context, int index) {
                                                    return InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            _controllerNVL.text = _lsCodeNameProduct[index].value ?? "";
                                                            _keyNVL = _lsCodeNameProduct[index].key;
                                                            _lsCodeNameProduct = [];
                                                          });
                                                        },
                                                        child: Text(_lsCodeNameProduct[index].value ?? "", style: TextStyle(fontSize: 11.sp)));
                                                  },
                                                  separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                                                ),
                                        ),
                                      ),
                                    ),
                                    _lsCodeNameProduct.isNotEmpty || _isLoading == true ? SizedBox(height: 10.h) : const SizedBox(height: 0),
                                    const _Title(text: "Ngày yêu cầu:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: REdgeInsets.all(7),
                                      decoration: BoxDecoration(
                                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 5.h),
                                          const _Title(text: "Từ ngày:"),
                                          SizedBox(height: 5.h),
                                          GestureDetector(
                                            onTap: () {
                                              Platform.isAndroid ? _pickFromDateRequest(context) : _pickFromDateRequestIOS(context);
                                            },
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade200,
                                                borderRadius: BorderRadius.circular(3.r),
                                              ),
                                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 9,
                                                    child: Text(
                                                      DateFormat('dd-MM-yyyy').format(_fromDateRequest),
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.h),
                                          const _Title(text: "Đến ngày:"),
                                          SizedBox(height: 5.h),
                                          GestureDetector(
                                            onTap: () {
                                              Platform.isAndroid ? _pickToDateRequest(context) : _pickToDateRequestIOS(context);
                                              // Platform.isAndroid
                                              //     ?_pickToDateRequestIOS(context)
                                              //     :_pickToDateRequest(context);
                                            },
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade200,
                                                borderRadius: BorderRadius.circular(3.r),
                                              ),
                                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 9,
                                                    child: Text(
                                                      DateFormat('dd-MM-yyyy').format(_toDateRequest),
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    const _Title(text: "Ngày giao dịch:"),
                                    SizedBox(height: 5.h),
                                    Container(
                                      padding: REdgeInsets.all(7),
                                      decoration: BoxDecoration(
                                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(3.r),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          SizedBox(height: 5.h),
                                          const _Title(text: "Từ ngày:"),
                                          SizedBox(height: 5.h),
                                          GestureDetector(
                                            onTap: () {
                                              Platform.isAndroid ? _pickFromDateTrans(context) : _pickFromDateTransIOS(context);
                                              // Platform.isAndroid
                                              //     ?_pickFromDateTransIOS(context)
                                              //     :_pickFromDateTrans(context);
                                            },
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade200,
                                                borderRadius: BorderRadius.circular(3.r),
                                              ),
                                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 9,
                                                    child: Text(
                                                      _fromDateTrans != null ? DateFormat('dd-MM-yyyy').format(_fromDateTrans!) : "",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 10.h),
                                          const _Title(text: "Đến ngày:"),
                                          SizedBox(height: 5.h),
                                          GestureDetector(
                                            onTap: () {
                                              Platform.isAndroid ? _pickToDateTrans(context) : _pickToDateTransIOS(context);
                                              // Platform.isAndroid
                                              //     ?_pickToDateTransIOS(context)
                                              //     :_pickToDateTrans(context);
                                            },
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade200,
                                                borderRadius: BorderRadius.circular(3.r),
                                              ),
                                              padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 9,
                                                    child: Text(
                                                      _toDateTrans != null ? DateFormat('dd-MM-yyyy').format(_toDateTrans!) : "",
                                                      style: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Container(
                                      width: double.infinity,
                                      decoration: const BoxDecoration(),
                                      child: ElevatedButton(
                                        style: ButtonStyle(
                                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                          side: MaterialStateProperty.all(
                                            const BorderSide(
                                              color: Color(0xff0052cc),
                                            ),
                                          ),
                                          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                        ),
                                        onPressed: () async {
                                          // if (_focusReserVation.hasFocus || _focusControllerNVL.hasFocus) {
                                          //   FocusScope.of(context).unfocus();
                                          // }
                                          FocusScope.of(context).unfocus();
                                          await _sendBackData(context);
                                        },
                                        child: Container(
                                          margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                          child: Text(
                                            "Tìm",
                                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 100.h),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: _showNotiError,
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                            width: double.infinity,
                            decoration: BoxDecoration(color: Colors.red.shade900),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 9,
                                  child: Text(
                                    _messageNoti,
                                    style: TextStyle(fontSize: 12.sp, color: Colors.white),
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: IconButton(
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      onPressed: () {
                                        setState(() {
                                          _showNotiError = false;
                                        });
                                      },
                                      icon: const Icon(Icons.cancel),
                                      iconSize: 15.sp,
                                      color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          ),
        ));
  }
}

class _Title extends StatelessWidget {
  final String text;
  const _Title({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
