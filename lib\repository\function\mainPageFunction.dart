import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

import '../../Storage/storageSecureStorage.dart';
import '../../Storage/storageSharedPreferences.dart';

class MainPageFunction {
  static Future<ConnectivityResult> checkConnectNetwork() async {
    final result = await Connectivity().checkConnectivity();
    return result;
  }

  static Future<bool> removeCurrentUser(String keyUserID, String keyDateTimeNow, String keyUser) async {
    try {
      await Future.wait([
        StorageSharedPreferences.removeShared(keyUserID),
        StorageSharedPreferences.removeShared(keyDateTimeNow),
        SecureStorage.removeSecure(keyUser, null)
      ]);

      OneSignal.logout();
      OneSignal.User.removeAlias("employee_code");
      OneSignal.Notifications.removeClickListener((event) {
        print('NOTIFICATION CLICK LISTENER CALLED WITH EVENT: $event');
      });

      return true;
    } catch (error) {
      if (kDebugMode) {
        print(error);
      }
      return false;
    }
  }
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }
}
