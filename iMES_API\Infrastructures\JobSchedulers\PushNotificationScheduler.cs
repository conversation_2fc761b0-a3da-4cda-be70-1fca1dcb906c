﻿using Hangfire;
using Hangfire.Storage;
using ISD.API.Constant.MESP2;
using ISD.API.EntityModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers
{
    public static class PushNotificationScheduler
    {
        public static async Task Invoke(IServiceProvider serviceProvider)
        {
            //Create scope context
            using var scope = serviceProvider.CreateScope();
            using var dbContext = scope.ServiceProvider.GetRequiredService<EntityDataContext>();

            //Check trạng thái run job
            var settingJob = await dbContext.SettingJob.SingleOrDefaultAsync(x => x.JobName == "PushNotificationScheduler");

            if (settingJob?.IsRun == false) return;

            var service = new PushNotificationService(serviceProvider);

            try
            {
                //Xóa job nếu tồn tại trước khi thêm mới
                RecurringJob.RemoveIfExists("PushNotificationService");
                RecurringJob.RemoveIfExists("PushNotificationScheduler");
                RecurringJob.RemoveIfExists(SettingSyncSAPConstant.PushNotificationScheduler);
                
                //Thêm mới job và run
                string cronExpression = !string.IsNullOrEmpty(settingJob?.Config) ? settingJob.Config : "*/10 * * * *"; // Default to every 10 minutes if no config
                RecurringJob.AddOrUpdate("PushNotificationScheduler", () => service.Run(), cronExpression);

                //BackgroundJob.Enqueue(() => service.Run());

                //using (var connection = JobStorage.Current.GetConnection())
                //{
                //    foreach (var jobId in connection.GetRecurringJobs())
                //    {
                //        var jobDetails = connection.GetJobData(jobId.Id);
                //        Console.WriteLine(jobDetails.Job.Type.FullName);
                //    }
                //}

            }
            catch (Exception)
            {
                return;
            }
        }
    }
}
