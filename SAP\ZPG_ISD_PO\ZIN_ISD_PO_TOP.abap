*&---------------------------------------------------------------------*
*& Include          ZIN_FI_EINV_PROPOSAL_TOP
*&---------------------------------------------------------------------*

REPORT ZPG_ISD_PO.

*-----------------------------------------------------------------------
* Class Definition
*-----------------------------------------------------------------------
CLASS CL_EVT_HEAD DEFINITION DEFERRED.
CLASS CL_EVT_DET  DEFINITION DEFERRED.

*-----------------------------------------------------------------------
* Table definition
*-----------------------------------------------------------------------
TABLES: EKKO, T001, TVKO, T161, EKBE
  .
*-----------------------------------------------------------------------
* Type definition
*-----------------------------------------------------------------------
*TYPES:
*  BEGIN OF
*  END OF ,

*-----------------------------------------------------------------------
* Internal Table definition
*-----------------------------------------------------------------------
DATA:
  GT_CUSTOMERS   TYPE CMDS_EI_EXTERN_T,
  GT_MESSAGES    TYPE BAPIRET2_T,
  GT_DATA_INPUT  TYPE TABLE OF ZISD_EKPO,
  GT_DATA_HEADER TYPE TABLE OF ZISD_EKKO,
  GT_DATA_DETAIL TYPE TABLE OF ZISD_EKPO
  .
*-----------------------------------------------------------------------
* Work Area Definition
*-----------------------------------------------------------------------
*DATA:
*      .
*-----------------------------------------------------------------------
* Variable definition
*-----------------------------------------------------------------------

*-----------------------------------------------------------------------
* Selection Screen definition
*-----------------------------------------------------------------------
SELECTION-SCREEN BEGIN OF BLOCK BL1 WITH FRAME.
SELECT-OPTIONS:
  S_BUKRS FOR T001-BUKRS NO-EXTENSION NO INTERVALS OBLIGATORY,
  S_WERKS FOR TVKO-WERKS NO-EXTENSION NO INTERVALS,
  S_EBELN FOR EKKO-EBELN,
  S_DATUM FOR SY-DATUM DEFAULT SY-DATUM,
  S_BSART FOR T161-BSART no INTERVALS,
  S_BWART FOR EKBE-BWART no INTERVALS
  .

*PARAMETERS:
*P_AUTO TYPE FLAG NO-DISPLAY.
*
*PARAMETERS:
*  RB_TYPE1 TYPE FLAG NO-DISPLAY DEFAULT 'X',
*  RB_TYPE2 TYPE FLAG NO-DISPLAY.
*
SELECTION-SCREEN END OF BLOCK BL1.
*
*SELECTION-SCREEN BEGIN OF BLOCK BL2 WITH FRAME TITLE TEXT-T01.
*PARAMETERS:
*  RB_COND TYPE FLAG RADIOBUTTON GROUP GR1,
*  RB_NONC TYPE FLAG RADIOBUTTON GROUP GR1 DEFAULT 'X'.
*
*PARAMETERS:
*C_COMB AS CHECKBOX.

*SELECTION-SCREEN END OF BLOCK BL2.


*-----------------------------------------------------------------------
* Object Definition
*-----------------------------------------------------------------------
DATA:
  GO_ALV_H     TYPE REF TO CL_GUI_ALV_GRID,          "ALV header
  GO_ALV_D     TYPE REF TO CL_GUI_ALV_GRID,          "ALV detail
  GO_CONTAINER TYPE REF TO CL_GUI_CUSTOM_CONTAINER,  "Container
  GO_SPLITTER  TYPE REF TO CL_GUI_SPLITTER_CONTAINER, "Spliter
  GO_PARENT1   TYPE REF TO CL_GUI_CONTAINER,         "Header Container
  GO_PARENT2   TYPE REF TO CL_GUI_CONTAINER,         "Detail Container
  GO_EVT_HEAD  TYPE REF TO CL_EVT_HEAD,              "Event for header
  GO_EVT_DET   TYPE REF TO CL_EVT_DET.               "Event for detail
*-----------------------------------------------------------------------

*-----------------------------------------------------------------------
* Variable Definition
*-----------------------------------------------------------------------
DATA:

  GT_STYLE_DISABLE        TYPE LVC_T_STYL,
  GT_STYLE_DISABLE_CANCEL TYPE LVC_T_STYL,
  GT_STATUS               TYPE DD07V_TAB,
  GT_CANCEL_STATUS        TYPE DD07V_TAB,
  GT_INV_TYPE             TYPE DD07V_TAB.

DATA:
  OK_CODE        TYPE SY-UCOMM,
  GW_OK_CODE     TYPE SY-UCOMM,
  GW_CHANGED_FLG TYPE C,

  GT_FLDCAT      TYPE          LVC_T_FCAT,
  GS_LAYOUT      TYPE          LVC_S_LAYO,

  GT_FLDCAT01    TYPE          LVC_T_FCAT,
  GS_LAYOUT01    TYPE          LVC_S_LAYO,

  GT_FLDCAT02    TYPE          LVC_T_FCAT,
  GS_LAYOUT02    TYPE          LVC_S_LAYO.

DATA:GW_SQL01         TYPE DSTRING.

*-----------------------------------------------------------------------
*----------------------------------------------------------------------*
* EVT_HEAD DEFINITION
*----------------------------------------------------------------------*
CLASS CL_EVT_HEAD DEFINITION.

  PUBLIC SECTION.
    METHODS:
    HANDLE_DATA_CHANGED FOR EVENT DATA_CHANGED
      OF CL_GUI_ALV_GRID,
    HANDLE_DOUBLE_CLICK FOR EVENT DOUBLE_CLICK
      OF CL_GUI_ALV_GRID IMPORTING E_ROW E_COLUMN,
    HANDLE_TOOLBAR FOR EVENT TOOLBAR
      OF CL_GUI_ALV_GRID IMPORTING E_OBJECT,
    HANDLE_USER_COMMAND FOR EVENT USER_COMMAND
      OF CL_GUI_ALV_GRID IMPORTING E_UCOMM.

ENDCLASS. "EVT_HEAD DEFINITION
*----------------------------------------------------------------------*
* EVT_HEAD IMPLEMENTATION
*----------------------------------------------------------------------*
CLASS CL_EVT_HEAD IMPLEMENTATION.

  METHOD HANDLE_DATA_CHANGED.
    IF GW_CHANGED_FLG IS INITIAL.
      GW_CHANGED_FLG = 'X'.
    ENDIF.
    CALL METHOD CL_GUI_CFW=>SET_NEW_OK_CODE
      EXPORTING
        NEW_CODE = 'CHANGED'.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED
  METHOD HANDLE_DOUBLE_CLICK.
    PERFORM DISPLAY_DETAIL USING E_ROW E_COLUMN.
  ENDMETHOD.                    "HANDLE_DOUBLE_CLICK
  METHOD HANDLE_TOOLBAR.
*    PERFORM EDIT_TOOLBAR CHANGING E_OBJECT.
  ENDMETHOD.                    "HANDLE_TOOLBAR
  METHOD HANDLE_USER_COMMAND.
    PERFORM USER_COMMAND USING E_UCOMM.
  ENDMETHOD.                    "HANDLE_USER_COMMAND

ENDCLASS. "EVT_HEAD IMPLEMENTATION
*----------------------------------------------------------------------*
*       CLASS CL_EVT_DET DEFINITION
*----------------------------------------------------------------------*
*
*----------------------------------------------------------------------*
CLASS CL_EVT_DET DEFINITION.

  PUBLIC SECTION.
    METHODS:
      HANDLE_DATA_CHANGED FOR EVENT DATA_CHANGED
        OF CL_GUI_ALV_GRID.

ENDCLASS. "EVT_DET DEFINITION
*----------------------------------------------------------------------*
* EVT_DET IMPLEMENTATION
*----------------------------------------------------------------------*
CLASS CL_EVT_DET IMPLEMENTATION.

  METHOD HANDLE_DATA_CHANGED.
    IF GW_CHANGED_FLG IS INITIAL.
      GW_CHANGED_FLG = 'X'.
    ENDIF.
    CALL METHOD CL_GUI_CFW=>SET_NEW_OK_CODE
      EXPORTING
        NEW_CODE = 'CHANGED'.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED

ENDCLASS. "EVT_HEAD IMPLEMENTATION



*  BREAK-POINT.
DATA: P_FORM             TYPE TDSFNAME,
      FM_NAME            TYPE RS38L_FNAM,
      WA_JOB_OUTPUT_INFO TYPE SSFCRESCL,
      WA_CPARAM          TYPE SSFCTRLOP,
      WA_OUTPUT          TYPE SSFCOMPOP.