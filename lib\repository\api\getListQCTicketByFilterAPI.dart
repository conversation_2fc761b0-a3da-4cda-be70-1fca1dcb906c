import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postFilterQC.dart';
import '../../urlApi/urlApi.dart';

class GetListQCTicketByFilterApi {
  static Future<http.Response> getListQCTicketByFilter(String token, FilterQCVm postFilterQC) async {
    Map<String, dynamic> data = {};
    data = {
      "SaleOrgCode": postFilterQC.saleOrgCode,
      "WorkShopCode": postFilterQC.workShopCode,
      "WorkCenterCode": postFilterQC.workCenterCode,
      "ProfileCode": postFilterQC.profileCode,
      "ProfileName": postFilterQC.profileName,
      "LSXSAP": postFilterQC.lsxSap,
      "ProductCode": postFilterQC.productCode,
      "ProductName": postFilterQC.productName,
      "Status": postFilterQC.status == null ? null : (postFilterQC.status.toString()),
      "Result": postFilterQC.result,
      "ConfirmCommonDate": postFilterQC.confirmCommonDate,
      "ConfirmFromDate": postFilterQC.confirmFormDate,
      "ConfirmToDate": postFilterQC.confirmToDate,
      "QualityCommonDate": postFilterQC.qualityCommonDate,
      "QualityFromDate": postFilterQC.qualityFromDate,
      "QualityToDate": postFilterQC.qualityToDate,
      "QualityType": postFilterQC.qualityType
    };

    data.removeWhere((key, value) => value == null);
    if (kDebugMode) {
      print(data);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + 'GetListQCTicketByFilter');
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersTokenUrlencoded(token), body: data);
    return response;
  }

  static Future<http.Response> getQCKCSFiltered(String token, FilterQCVm filterQCVm) async {
    Map<String, dynamic> data = {};
    data = {
      "SaleOrgCode": filterQCVm.saleOrgCode,
      "WorkShopCode": filterQCVm.workShopCode,
      "WorkCenterCode": filterQCVm.workCenterCode,
      "ProfileCode": filterQCVm.profileCode,
      "ProfileName": filterQCVm.profileName,
      "LSXSAP": filterQCVm.lsxSap,
      "ProductCode": filterQCVm.productCode,
      "ProductName": filterQCVm.productName,
      "Status": filterQCVm.status == null ? null : (filterQCVm.status.toString()),
      "Result": filterQCVm.result,
      "ConfirmCommonDate": filterQCVm.confirmCommonDate,
      "ConfirmFromDate": filterQCVm.confirmFormDate,
      "ConfirmToDate": filterQCVm.confirmToDate,
      "QualityCommonDate": filterQCVm.qualityCommonDate,
      "QualityFromDate": filterQCVm.qualityFromDate,
      "QualityToDate": filterQCVm.qualityToDate
    };

    data.removeWhere((key, value) => value == null);
    if (kDebugMode) {
      print(data);

      print(jsonEncode(filterQCVm));
    }

    final environment = await SecureStorage.getString("environment", null);

    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + 'QCKCSFiltered');
    debugPrint(url.toString());
    var response = await http.post(url, headers: UrlApi.headersTokenUrlencoded(token), body: data);
    return response;
  }
}
