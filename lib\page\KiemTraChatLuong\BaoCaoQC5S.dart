import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/page/KiemTraChatLuong/element/FilterListQCMau.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../../element/errorViewPost.dart';
import '../../element/timeOut.dart';
import '../../model/drawerFilterQC.dart';
import '../../model/getListQCByFilter.dart';
import '../../model/postFilterQC.dart';
import '../../element/FilterLsQC.dart';
import '../../model/qrCodePageListQCModel.dart';
import '../../repository/function/listQcFunction.dart';
import '../../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../LostConnect.dart';

class BaoCaoQC5S extends StatefulWidget {
  // final String token;
  // final String accountID;
  final String dateTimeOld;
  final DataUser user;
  const BaoCaoQC5S(
      {Key? key,
      // required this.token, required this.accountID,
      required this.dateTimeOld,
      required this.user})
      : super(key: key);

  @override
  _BaoCaoQC5SState createState() => _BaoCaoQC5SState();
}

class _BaoCaoQC5SState extends State<BaoCaoQC5S> {
  bool? _isLoading;
  ConnectivityResult _result = ConnectivityResult.none;
  DateTime? _stringToDateTimeConfirm;
  String? _dateFormatStringConfirm;
  DateTime? _stringToDateTimeQualityDate;
  String? _dateFormatStringQualityDate;
  bool _isNotWifi = false;
  FilterQCModel? _filterLSQC;
  CommonDateModel? _commonDateModel;
  List<DataListQC>? _defaultDataFilter = [];
  List<SalesOrgCodes>? _salesOrgCodes;
  List<WorkCenters>? _workCenters;
  List<WorkShops>? _workShops;
  List<CommonDates>? _commonDates;
  List<ResultsDataQC>? _results;
  String _notFoundFilter = " ";
  FilterQCVm? _postFilterQC;
  String _error = "";
  late bool _timeOut;
  bool? _disableButton;

  String title = 'Danh sách báo cáo 5S';

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _getDefaultDropDownForListFiltered() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _timeOut = false;
      });
      final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.user.token.toString());

      debugPrint("--- TIEN getDefaultQCMauFilter | _filterLSQC");
      debugPrint(json.encode(dataDropdown));

      if (dataDropdown == null) {
        if (!mounted) return;
        setState(() {
          _isLoading = true;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _filterLSQC = dataDropdown;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
        _timeOut = false;
      });
    }
  }

  Future<void> _getCommonDateForFilter() async {
    try {
      final dataCommonDateModel = await ListQCFunction.getCommonDateModel(
        _filterLSQC!.additionalData!.selectedQCCommonDate.toString(),
        widget.user.token.toString(),
      );
      if (!mounted) return;

      debugPrint("--- TIEN dataCommonDateModel");
      debugPrint(json.encode(dataCommonDateModel));

      debugPrint("--- TIEN _filterLSQC!.additionalData!.selectedConfirmCommonDate");
      debugPrint(_filterLSQC!.additionalData!.selectedConfirmCommonDate);

      debugPrint("--- TIEN _filterLSQC!.additionalData!.selectedQCCommonDate");
      debugPrint(_filterLSQC!.additionalData!.selectedQCCommonDate);

      debugPrint("--- TIEN dataCommonDateModel");
      debugPrint(dataCommonDateModel!.fromDate);
      debugPrint(dataCommonDateModel!.toDate);
      debugPrint("--- TIEN end dataCommonDateModel");

      setState(() {
        _commonDateModel = dataCommonDateModel;
        _salesOrgCodes = _filterLSQC!.data!.salesOrgCodes;
        _salesOrgCodes!.insert(0, (ListQCFunction.defaultSalesOrgCodes));
        _workCenters = _filterLSQC!.data!.workCenters;
        _workCenters!.insert(0, (ListQCFunction.defaultWorkCenters));
        _results = _filterLSQC!.data!.results;
        _results!.insert(0, (ListQCFunction.defaultResultsDataQC));
        _workShops = _filterLSQC!.data!.workShops;
        _workShops!.insert(0, (ListQCFunction.defaultWorkShops));
        _commonDates = _filterLSQC!.data!.commonDates;
        _postFilterQC = FilterQCVm(
          _filterLSQC!.additionalData!.selectedSalesOrgCode,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          // _filterLSQC!.additionalData!.selectedConfirmCommonDate,
          // _commonDateModel!.fromDate,
          // _commonDateModel!.toDate,
          null,
          null,
          null,
          _filterLSQC!.additionalData!.selectedQCCommonDate,
          _commonDateModel!.fromDate,
          _commonDateModel!.toDate,
          null,
        );
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _fetchListSaved() async {
    // setState(() {
    //   _isLoading = false;
    // });
    // return;
    try {
      _postFilterQC?.qualityType = "QCMAU";
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        debugPrint(_error);
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _rePostListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
      });
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _init() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
      return;
    }

    await _getDefaultDropDownForListFiltered();
    if (_filterLSQC == null) {
      return;
    }

    await _getCommonDateForFilter();
    if (_commonDateModel == null) {
      return;
    }

    _fetchListSaved();
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(
              backgroundColor: Colors.grey.shade200,
              body: TimeOutView(
                setButton: _setButton,
                disableButton: _disableButton ?? false,
              ),
            ),
          )
        : _isNotWifi == true
            ? Scaffold(
                backgroundColor: Colors.grey.shade200,
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    'Danh Sách Kiểm Tra BTP',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                body: LostConnect(
                  checkConnect: () => _init(),
                ),
              )
            : Scaffold(
                endDrawerEnableOpenDragGesture: false,
                backgroundColor: _defaultDataFilter != null && _defaultDataFilter!.isNotEmpty ? Colors.grey.shade200 : Colors.white,
                // key: _key,
                endDrawer: _isLoading == true
                    ? null
                    : FilterListQCMau(
                        filterLSQC: _filterLSQC!,
                        lsStatus: ListQCFunction.lsStatus,
                        results: _results,
                        commonDates: _commonDates,
                        workCenters: _workCenters,
                        commonDateModel: _commonDateModel ?? CommonDateModel(),
                        salesOrgCodes: _salesOrgCodes,
                        workShops: _workShops,
                        token: widget.user.token.toString(),
                        onFilterSelected: (List<DrawerFilterQC> filter) {
                          setState(() {
                            _defaultDataFilter = filter[0].getDataFilter;
                            _notFoundFilter = filter[0].notFoundFilter ?? " "; // 'Không tìm thấy phiếu kiểm tra nào!'
                            _postFilterQC = filter[0].postFilterQC;
                          });
                        },
                      ),
                appBar: AppBar(
                  titleSpacing: 0,
                  automaticallyImplyLeading: false,
                  backgroundColor: const Color(0xff0052cc),
                  elevation: 0,
                  centerTitle: true,
                  actions: [
                    // IconButton(
                    //   highlightColor: Colors.transparent,
                    //   splashColor: Colors.transparent,
                    //   hoverColor: Colors.transparent,
                    //   icon: Icon(
                    //     Icons.camera_alt,
                    //     size: 19.sp,
                    //     color: Colors.white,
                    //   ),
                    //   onPressed: _isLoading == true
                    //       ? null
                    //       : () async {
                    //           final check = await Navigator.pushNamed(context, "/QRCodePageListQC2");
                    //           if (!mounted) return;
                    //           if (check == null) return;
                    //           if ((check as QRCodePageListQCModel).isScan == true) {
                    //             final checkSave = await Navigator.pushNamed(
                    //               context,
                    //               '/BaoCaoQC5SDetail',
                    //               arguments: ScreenArgumentNavigatorBar(
                    //                 "",
                    //                 widget.dateTimeOld,
                    //                 "",
                    //                 "",
                    //                 widget.user,
                    //               ),
                    //             );
                    //             if (checkSave == null) return;
                    //             if (checkSave == true) {
                    //               _rePostListQCApi();
                    //             }
                    //           }
                    //         },
                    // ),
                    Builder(
                      builder: (BuildContext context) {
                        return IconButton(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          icon: Icon(
                            Icons.search_outlined,
                            size: 19.sp,
                            color: Colors.white,
                          ),
                          onPressed: _isLoading == true
                              ? null
                              : () async {
                                  await _checkConnectNetwork();
                                  if (!mounted) return;
                                  if (_result != ConnectivityResult.none) {
                                    Scaffold.of(context).openEndDrawer();
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                      backgroundColor: Colors.black,
                                      content: Text(
                                        'Tính năng cần có kết nối internet để sử dụng',
                                        style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                      ),
                                    ));
                                  }
                                },
                        );
                      },
                    ),
                  ],
                  leading: IconButton(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                  ),
                ),
                // floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
                floatingActionButton: _isNotWifi == false && _isLoading == true && _error.isEmpty
                    ? null
                    : FloatingActionButton(
                        onPressed: () async {
                          final checkSave = await Navigator.pushNamed(
                            context,
                            '/BaoCaoQC5SDetail',
                            arguments: ScreenArgumentNavigatorBar(
                              "",
                              widget.dateTimeOld,
                              "",
                              "",
                              widget.user,
                            ),
                          );
                          if (checkSave == null) return;
                          if (checkSave == true) {
                            _rePostListQCApi();
                          }
                        },
                        backgroundColor: const Color(0xff4CAF50),
                        child: const Icon(Icons.add, color: Colors.white),
                      ),
                body: _error != ""
                    ? ErrorViewPost(error: _error)
                    : _isLoading == true
                        ? _buildLoading()
                        : _buildDataFilteredWithHeader());
  }

  Widget _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildDataFilteredWithHeader() {
    List<Map<String, String>> dummyTheTreo = [
      {'LSX': 'dummy', 'qualityControlId': ''},
      // {'LSX': '150002278 | 0101 | 148', 'hangTagId': 'e8e0a2b0-70b6-44eb-a175-8db7541e8f3c'},
      // {'LSX': '520011035 CAT RO1', 'hangTagId': 'cea0bd31-33d9-4a1a-8535-30001460a16e'},
      // {'LSX': '3500008065', 'hangTagId': 'ee338608-9bfb-4e47-b14f-d8b005964302'},
      // {'LSX': 'test SOT', 'hangTagId': 'e1a8fb60-77de-479c-b8cc-2a11b4a82def'},
      // {'LSX': 'test sai Workshop', 'hangTagId': 'e4b0ddd6-8154-4c9e-8a0e-e5c7c4459267'},
      // {'LSX': 'Test 2', 'hangTagId': '3d6156b6-6c01-4a89-a7a7-f09169e16b53'},
      // {'LSX': 'Fail', 'hangTagId': 'a0230d8d-7280-47b4-848b-7aeff7fde4fe'},
      // {'LSX': 'Passed', 'hangTagId': '6f278cc3-ab2a-4aee-b72f-ab72d28fff52'},
      // {'LSX': 'Mid Test ', 'hangTagId': 'd41f9ee9-5ccd-4a0b-9d23-7fb20bd10614'},
      // {'LSX': 'Inline 7747 2 info', 'hangTagId': '26ea7ecd-e22e-436e-943d-49192639d9e1'},
      // {'LSX': 'Inline 9 info 1 error', 'hangTagId': 'd10ef197-bb0f-48e9-8f45-e6bd738563d8'},
    ];

    return Column(
      children: <Widget>[
        kDebugMode
            ? RenderDebugButtons(dummyTheTreo, (item) {
                Navigator.pushNamed(
                  context,
                  '/BaoCaoQC5SDetail',
                  arguments: ScreenArgumentNavigatorBar(
                    "",
                    widget.dateTimeOld,
                    item['hangTagId']!, // thẻ treo
                    "qr",
                    widget.user,
                  ),
                );
              }, 'LSX')
            : Container(),

        // Your ListView.builder:
        Expanded(child: _buildDataFiltered())
      ],
    );
  }

  _buildDataFiltered() {
    if (_defaultDataFilter != null && _defaultDataFilter!.isNotEmpty) {
      return ListView.builder(
          key: ObjectKey(_defaultDataFilter![0]),
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemCount: _defaultDataFilter!.length,
          itemBuilder: (context, index) {
            final item = _defaultDataFilter![index];
            if (item.confirmDate != null) {
              _stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(item.confirmDate.toString());
              _dateFormatStringConfirm = DateFormat('dd/MM/yyyy ').format(_stringToDateTimeConfirm!);
            }
            if (item.qualityDate != null) {
              _stringToDateTimeQualityDate = DateFormat("yyyy-MM-dd").parse(item.qualityDate.toString());
              _dateFormatStringQualityDate = DateFormat('dd-MM-yyyy').format(_stringToDateTimeQualityDate!);
            } else {
              _dateFormatStringQualityDate = item.qualityDate;
            }
            return GestureDetector(
                onTap: () async {
                  debugPrint(item.qualityControlId);
                  Navigator.pushNamed(
                    context,
                    '/BaoCaoQC5SDetail',
                    arguments: ScreenArgumentNavigatorBar(
                      item.qualityControlId.toString(),
                      widget.dateTimeOld,
                      "",
                      "normal",
                      widget.user,
                    ),
                  ).then((value) {
                    // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                    // final check = arguments.refresh;
                    // if(GlobalValue.refreshListQC == true){
                    if (value == true) {
                      _rePostListQCApi();
                    }
                  }).onError((error, stackTrace) {
                    debugPrint('Lỗi:$error');
                  });
                  // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                  // if (arguments.refresh == true) {
                  // print(GlobalValue.refresh);
                  // if (GlobalValue.refresh == true) {
                  //   _rePostListQCApi();
                  // } else {
                  //   return;
                  // }

                  // }else{
                  //   return;
                  // }
                  // if (data == null) return;
                  // if (data == true) {
                  //   getListQCApi();
                  // }
                },
                child: _ListQCView(
                  item: item,
                  dateFormatStringConfirm: _dateFormatStringConfirm,
                  dateFormatStringQualityDate: _dateFormatStringQualityDate,
                ));
          });
    }

    return _NotFoundFilterView(
      notFoundFilter: _notFoundFilter,
    );
  }
}

class _NotFoundFilterView extends StatelessWidget {
  final String notFoundFilter;
  const _NotFoundFilterView({Key? key, required this.notFoundFilter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            notFoundFilter,
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}

class _ListQCView extends StatelessWidget {
  final DataListQC item;
  final String? dateFormatStringConfirm;
  final String? dateFormatStringQualityDate;
  const _ListQCView({Key? key, required this.item, required this.dateFormatStringConfirm, required this.dateFormatStringQualityDate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Nhà gia công: ${item.nhaGiaCong?.toString() ?? ''}",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "Khách hàng: ${item.khachHangId?.toString() ?? ''}",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                    decoration: BoxDecoration(
                      color: item.status == false ? Colors.deepOrange : const Color(0xff0052cc),
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Nhà cung cấp: ${item.nhaCungCap?.toString() ?? ''}",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      "Sản phẩm: ${item.productName?.toString() ?? ''} ",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                    decoration: BoxDecoration(
                      color: item.result != null
                          ? item.result == 'Fail'
                              ? Colors.red.shade800
                              : Colors.green
                          : Colors.yellow.shade800,
                      // color: Colors.orangeAccent,
                      // color: const Color(0xff0052cc),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Center(
                      child: Text(
                        item.result != null ? item.result.toString() : "Chưa có kết quả",
                        style: TextStyle(fontSize: 12.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ],
          ),
          Text(
            "Số lượng lô hàng: ${item.qty?.round().toString() ?? ''}",
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
          const Divider(),
          // SizedBox(height: 10.h),
          Row(
            children: <Widget>[
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày Confirm",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      // dateFormatStringConfirm,
                      dateFormatStringConfirm?.toString() ?? '',
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Text("|"),
              Expanded(
                flex: 5,
                child: Column(
                  children: <Widget>[
                    Text(
                      "Ngày kiểm tra",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      dateFormatStringQualityDate ?? " ",
                      style: TextStyle(fontSize: 12.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
