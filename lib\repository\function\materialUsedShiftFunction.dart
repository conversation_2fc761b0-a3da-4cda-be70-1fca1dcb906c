import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/getInventoryByListSOWBS.dart';
import '../../model/getInventoryBySOWBS.dart';
import '../../model/getListSOWBSByBatch.dart';
import '../../model/getStep.dart';
import '../../model/materialUsedShift.dart';
import '../../model/getSOWBSByBatch.dart';
import '../../model/postGetListSOWBSByBatch.dart';
import '../api/GetInventoryByListSOWBSApi.dart';
import '../api/getListSOWBSByBatchApi.dart';
import '../api/getSOWBSByBatchApi.dart';
import '../api/getStepApi.dart';
import '../api/materialUsedShiftApi.dart';

class MaterialUsedShiftFunction{
  static bool checkIsSend = false;
  static List<DataGetStep?> filterDataStepCode(List<DataGetStep>? lsStepCode, String query){

    List<DataGetStep?> getFilterStepCode = lsStepCode!.where((element) => element.value != null && element.value!.toLowerCase().contains(query.toLowerCase()) ).toList();
    return getFilterStepCode;
  }
  static Future<List<DataGetStep>?> fetchStepCode(String token) async {
    final response = await GetStepApi.getStepCode(token);
    if (response.statusCode == 200) {
      final getStepCode = GetStep.fromJson(jsonDecode(response.body));
      if (getStepCode.code == 200 && getStepCode.isSuccess == true) {
        return getStepCode.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  static Future<List<DataSOWBSByBatch>?> getSOWBSByBatchApi(String batchNumber, String token) async {
    final response = await GetSOWBSByBatchApi.getSOWBSByBatchApi(batchNumber, token);
    if (response.statusCode == 200) {
      final getSOWBSByBatch = GetSOWBSByBatch.fromJson(jsonDecode(response.body));
      if (getSOWBSByBatch.code == 200 && getSOWBSByBatch.isSuccess == true) {
        return getSOWBSByBatch.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  static Future<List<DataGetListSOWBSByBatch>?> getDataGetListSOWBSByBatch(PostGetListSOWBSByBatch postGetListSOWBSByBatch, String token)async {
    final response = await GetListSOWBSByBatchApi.getListSOWBSBYBatch(postGetListSOWBSByBatch, token);
    if (response.statusCode == 200) {
      final getSOWBSByBatch = GetListSOWBSByBatch.fromJson(jsonDecode(response.body));
      if (getSOWBSByBatch.code == 200 && getSOWBSByBatch.isSuccess == true) {
        return getSOWBSByBatch.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  static Future<DataGetInventoryByListSOWBS?> getInventoryByListSOWBS(GetInventoryBySOWBS getInventoryBySOWBS, String token) async {
    final response = await GetInventoryByListSOWBSApi.getInventoryByListSOWBSApi(getInventoryBySOWBS, token);
    debugPrint(response.body.toString());
    if (response.statusCode == 200) {
      final getInventoryByListSOWBS = GetInventoryByListSOWBS.fromJson(jsonDecode(response.body));
      if (getInventoryByListSOWBS.code == 200 && getInventoryByListSOWBS.isSuccess == true) {
        return getInventoryByListSOWBS.data;
      } else {
        throw(getInventoryByListSOWBS.message.toString());
      }
    } else {
      throw(response.reasonPhrase.toString());
    }
  }

  static Future<void> getMessasgeMaterialUsedShift(MaterialUsedShift materialUsedShift, String token, BuildContext context) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) =>
            WillPopScope(
              onWillPop: () async => false,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
      );
      final response = await MaterialUsedShiftApi.postMaterialUsedShift(materialUsedShift, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (response.statusCode == 200) {
        final getMessage = MessageMaterialUsedShift.fromJson(jsonDecode(response.body));
        if (getMessage.code == 200 && getMessage.isSuccess == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                "Thống kê NVL sử dụng trong ca thành công.",
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 2)));
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context);
          });
        }
        else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) =>
                  DiaLogErrorValidate(message: getMessage.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //       "Thống kê NVL sử dụng trong ca thất bại.",
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
        }
      } else {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) =>
                DialogError(message: response.body.toString()));
        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //     backgroundColor: Colors.black,
        //     content: Text(
        //       "Xảy ra lỗi! vui lòng thử lại sau",
        //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
        //     ),
        //     duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      if(checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if(checkIsSend == false) {
        Navigator.pop(context);
      }
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }
}