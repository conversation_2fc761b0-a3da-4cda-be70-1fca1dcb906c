class ReceiveIntegrationSAP {
  String? rawMaterialCardId;
  List<QuantityImports>? quantityImports;
  String? slocId;
  String? storageBinId;
  String? batch;
  AutoBatch? autoBatch;
  String? tds;
  String? soToKhai;

  ReceiveIntegrationSAP({
    this.rawMaterialCardId,
    this.quantityImports,
    this.slocId,
    this.storageBinId,
    this.batch,
    this.autoBatch,
    this.tds,
    this.soToKhai,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['rawMaterialCardId'] = rawMaterialCardId;
    if (quantityImports != null) {
      data['quantityImports'] = quantityImports!.map((v) => v.toJson()).toList();
    }
    data['slocId'] = slocId;
    data['storageBinId'] = storageBinId;
    data['batch'] = batch;
    if (autoBatch != null) {
      data['autoBatch'] = autoBatch!.toJson();
    }
    data['tds'] = tds;
    data['soToKhai'] = soToKhai;
    return data;
  }
}

class AutoBatch {
  String? materialType;
  String? importExportType;
  String? materialGroup;
  String? materialStatus;
  String? companyCode;
  int? nsxhsd;
  String? skinType;
  String? skinColor;
  String? caseCode;

  AutoBatch(
      {this.materialType,
      this.importExportType,
      this.materialGroup,
      this.materialStatus,
      this.companyCode,
      this.nsxhsd,
      this.skinType,
      this.skinColor,
      this.caseCode});

  AutoBatch.fromJson(Map<String, dynamic> json) {
    materialType = json['materialType'];
    importExportType = json['importExportType'];
    materialGroup = json['materialGroup'];
    materialStatus = json['materialStatus'];
    companyCode = json['companyCode'];
    nsxhsd = json['nsxhsd'];
    skinType = json['skinType'];
    skinColor = json['skinColor'];
    caseCode = json['caseCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['materialType'] = materialType;
    data['importExportType'] = importExportType;
    data['materialGroup'] = materialGroup;
    data['materialStatus'] = materialStatus;
    data['companyCode'] = companyCode;
    data['nsxhsd'] = nsxhsd;
    data['skinType'] = skinType;
    data['skinColor'] = skinColor;
    data['caseCode'] = caseCode;
    return data;
  }
}

class QuantityImports {
  String? po;
  String? poLine;
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;

  QuantityImports({this.po, this.poLine, this.so, this.soLine, this.wbs, this.quantity, this.unit});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['po'] = po;
    data['poLine'] = poLine;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}
