import 'dart:async';
import 'package:collection/collection.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:ttf/element/ButtonMainPage.dart';
import 'package:ttf/screenArguments/screenArgumentInfor.dart';
import 'package:ttf/screenArguments/screenArgumentQRPage.dart';
import 'package:ttf/utils/appConfig.dart';
import 'package:ttf/utils/ui_helpers.dart';
import 'package:ttf/utils/user_helper.dart';
import '../Storage/storageSecureStorage.dart';
import '../element/timeOut.dart';
import '../model/userModel.dart';
import '../Storage/storageSharedPreferences.dart';
import '../repository/function/mainPageFunction.dart';
import '../screenArguments/ScreenArgumentInventMSloc.dart';
import '../screenArguments/ScreenArgumentsTraHangNCC.dart';
import '../screenArguments/CommonScreenPermissionArgument.dart';
import '../screenArguments/screenArgumentInventoryManagement.dart';
import '../screenArguments/screenArgumentListQC.dart';
import '../screenArguments/CommonScreenUserArgument.dart';

class MainPage extends StatefulWidget {
  final DataUser getSaveUser;
  const MainPage({Key? key, required this.getSaveUser}) : super(key: key);

  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  // final String _keyUserId = 'id';
  // final String _keyUser = 'user';
  final String _keyDateTimeNow = 'datetimeNow';
  bool? _disableButton;
  // ConnectivityResult _result = ConnectivityResult.none;
  late String _dateTimeOld;
  late bool _checkViewTraHangNCCPermission;
  late bool _checkViewKTCLPermission;
  late bool _checkViewNVLPermission;
  late bool _checkViewTPPermission;
  late bool _checkScanQRPermission;
  late bool _checkCustomerPermission;
  late bool _checkViewDowntimePermission;
  late bool _checkViewMaintenanceOrderPermission;
  late bool _checkViewTyLeTieuHaoPermission;
  late bool _checkViewMaiDaoPermission;
  late bool _timeOut;
  late bool _error;

  @override
  void initState() {
    super.initState();

    _timeOutSession();
    _checkSecureStorage();

    _initPushNotification();
  }

  _initPushNotification() {
    OneSignal.Notifications.removeClickListener((event) {});
    OneSignal.Notifications.addClickListener((event) {
      print('NOTIFICATION CLICK LISTENER CALLED WITH EVENT: $event');
      // this.setState(() {
      //   _debugLabelString = "Clicked notification: \n${event.notification.jsonRepresentation().replaceAll("\\n", "\n")}";
      // });
      _dateTimeOld = StorageSharedPreferences.getString(_keyDateTimeNow) ?? widget.getSaveUser.expiredTime!;

      if (!Navigator.canPop(context)) {
        Navigator.pushNamed(context, "/Notification",
            arguments: CommonScreenPermissionArgument(
              widget.getSaveUser.permission!,
              widget.getSaveUser.token!,
              widget.getSaveUser.saleOrg!,
              _dateTimeOld,
              widget.getSaveUser.accountId.toString(),
              widget.getSaveUser,
            ));
      }
    });
  }

  Future<void> _checkSecureStorage() async {
    final environment = await SecureStorage.getString("environment", "");
  }

  void _timeOutSession() {
    try {
      _error = false;

      _checkViewTraHangNCCPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenName == "Trả hàng NCC") != null;

      _checkViewKTCLPermission = widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) =>
              element.screenName == "Kiểm tra chất lượng" ||
              element.screenCode == "QCMAU" ||
              element.screenCode == "QCMauDauChuyen" ||
              element.screenCode == "QCGIACONG" ||
              element.screenCode == "QCSANPHAM" ||
              element.screenCode == "HIENTRUONG") !=
          null;

      _checkCustomerPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "QCPassedInfo") != null;

      // Downtime permission (element.screenCode == Downtime)
      _checkViewDowntimePermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "Downtime") != null;

      // Maintenance Order permission (element.screenCode == M2)
      _checkViewMaintenanceOrderPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "M2") != null;

      // Ty Le Tieu Hao permission (element.screenCode == TyLeTieuHao)
      _checkViewTyLeTieuHaoPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "TyLeTieuHao") != null;

      _checkViewMaiDaoPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "MaiDao") != null;

      _checkViewNVLPermission = widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) =>
              element.screenName == "Nhập kho mua hàng" ||
              element.screenName == "Chuyển kho" ||
              element.screenName == "Thống kê NVL sử dụng trong ca" ||
              element.screenName == "Ghi nhận NVL chưa sử dụng hết" ||
              element.screenName == "Xác định NVL cho đơn hàng lẻ") !=
          null;

      _checkViewTPPermission =
          widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) => element.screenCode == "ImportProduct") != null;

      _checkScanQRPermission = widget.getSaveUser.permission!.mobileScreenModel!.firstWhereOrNull((element) =>
              element.screenName == "Ghi nhận sản lượng" ||
              element.screenName == "Chuyển công đoạn" ||
              element.screenName == "Hoàn tất công đoạn lớn") !=
          null;
      _dateTimeOld = StorageSharedPreferences.getString(_keyDateTimeNow) ?? widget.getSaveUser.expiredTime!;
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(_dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        _timeOut = false;
      }
    } catch (error) {
      _timeOut = false;
      _error = true;
      _checkViewTraHangNCCPermission = false;
      _checkViewKTCLPermission = false;
      _checkViewNVLPermission = false;
      _checkScanQRPermission = false;
      debugPrint(error.toString());
    }
  }

  void _setButton() {
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared(_keyUserId),
  //     StorageSharedPreferences.removeShared(_keyDateTimeNow),
  //     SecureStorage.removeSecure(_keyUser, null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Scaffold(
            backgroundColor: _timeOut == true ? Colors.white : Colors.grey.shade50,
            body: _error == true
                ? _ErrorView(
                    disableButton: _disableButton ?? false,
                    setButton: _setButton,
                  )
                : _timeOut == true
                    ? TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)
                    : _MainPageView(
                        getSaveUser: widget.getSaveUser,
                        disableButton: _disableButton ?? false,
                        checkScanQRPermission: _checkScanQRPermission,
                        checkCustomerPermission: _checkCustomerPermission,
                        checkViewNVLPermission: _checkViewNVLPermission,
                        checkViewTPPermission: _checkViewTPPermission,
                        checkViewTraHangNCCPermission: _checkViewTraHangNCCPermission,
                        checkViewKTCLPermission: _checkViewKTCLPermission,
                        checkViewDowntimePermission: _checkViewDowntimePermission,
                        checkViewMaintenanceOrderPermission: _checkViewMaintenanceOrderPermission,
                        checkViewTyLeTieuHaoPermission: _checkViewTyLeTieuHaoPermission,
                        checkViewMaiDaoPermission: _checkViewMaiDaoPermission,
                        dateTimeOld: _dateTimeOld,
                        setButton: _setButton,
                      )));
  }
}

class _MainPageView extends StatelessWidget {
  final DataUser getSaveUser;
  final bool disableButton;
  final bool checkScanQRPermission;
  final bool checkCustomerPermission;
  final bool checkViewNVLPermission;
  final bool checkViewTPPermission;
  final bool checkViewTraHangNCCPermission;
  final bool checkViewKTCLPermission;
  final bool checkViewDowntimePermission;
  final bool checkViewMaintenanceOrderPermission;
  final bool checkViewTyLeTieuHaoPermission;
  final bool checkViewMaiDaoPermission;
  final String dateTimeOld;
  final VoidCallback setButton;

  const _MainPageView(
      {Key? key,
      required this.getSaveUser,
      required this.disableButton,
      required this.checkScanQRPermission,
      required this.checkCustomerPermission,
      required this.checkViewNVLPermission,
      required this.checkViewTPPermission,
      required this.checkViewTraHangNCCPermission,
      required this.checkViewKTCLPermission,
      required this.checkViewDowntimePermission,
      required this.checkViewMaintenanceOrderPermission,
      required this.checkViewTyLeTieuHaoPermission,
      required this.checkViewMaiDaoPermission,
      required this.dateTimeOld,
      required this.setButton})
      : super(key: key);

  List<Map<String, dynamic>> get buttonList => [
        if (checkScanQRPermission)
          {
            'txt': 'Quét barcode TP/BTP',
            'route': '/QRCodePage',
            'icon': Icons.camera_alt_outlined,
            'arguments': ScreenArgumentQRPage(
              'infor',
              getSaveUser.token.toString(),
              getSaveUser.permission!,
              getSaveUser.saleOrg!,
              dateTimeOld,
            ),
          },
        if (checkViewNVLPermission)
          {
            'txt': 'Quản lý NVL',
            'route': '/InventoryManagement',
            'icon': Icons.inventory_2_outlined,
            'arguments': ScreenArgumentInventoryManagement(
              getSaveUser.permission!,
              getSaveUser.token!,
              getSaveUser.saleOrg!,
              dateTimeOld,
              getSaveUser.accountId.toString(),
            ),
          },
        // Quản lý TP
        if (checkViewTPPermission)
          {
            'txt': 'Quản lý TP',
            'route': '/ProductManagement',
            'icon': Icons.view_in_ar,
            'arguments': CommonScreenPermissionArgument(
              getSaveUser.permission!,
              getSaveUser.token!,
              getSaveUser.saleOrg!,
              dateTimeOld,
              getSaveUser.accountId.toString(),
              getSaveUser,
            ),
          },
        if (checkViewKTCLPermission)
          {
            'txt': 'Kiểm tra chất lượng',
            'route': '/KiemTraChatLuong',
            'icon': Icons.inventory_2_outlined,
            'arguments': CommonScreenPermissionArgument(
              getSaveUser.permission!,
              getSaveUser.token!,
              getSaveUser.saleOrg!,
              dateTimeOld,
              getSaveUser.accountId.toString(),
              getSaveUser,
            ),
          },
        if (checkCustomerPermission)
          {
            'txt': 'Quét QC passed stamp',
            'route': '/QRCodePage',
            'icon': Icons.qr_code_scanner_outlined,
            'arguments': ScreenArgumentQRPage(
              'qcpassed',
              getSaveUser.token.toString(),
              getSaveUser.permission!,
              getSaveUser.saleOrg!,
              dateTimeOld,
            ),
          },

        if (checkViewDowntimePermission)
          {
            'txt': 'Downtime',
            'route': '/Downtime',
            'icon': Icons.timer_outlined,
            'arguments': CommonScreenUserArgument(
              null, // id
              dateTimeOld, // dateTimeOld
              null, // qrCode
              null, // fromPage
              getSaveUser, // user
            ),
          },
        if (checkViewMaintenanceOrderPermission)
          {
            'txt': 'Maintenance Order (M2)',
            'route': '/MaintenanceOrder',
            'icon': Icons.build_outlined,
            'arguments': CommonScreenUserArgument(
              null, // id
              dateTimeOld, // dateTimeOld
              null, // qrCode
              null, // fromPage
              getSaveUser, // user
            ),
          },
        if (checkViewTyLeTieuHaoPermission)
          {
            'txt': 'Tỷ lệ tiêu hao',
            'route': '/TyLeTieuHao',
            'icon': Icons.analytics_outlined,
            'arguments': CommonScreenUserArgument(
              null, // id
              dateTimeOld, // dateTimeOld
              null, // qrCode
              null, // fromPage
              getSaveUser, // user
            ),
          },
        if (checkViewMaiDaoPermission) // TODO remove true
          {
            'txt': 'Mài dao cụ mũi lưỡi',
            'route': '/MaiDaoList',
            'icon': Icons.airlines,
            'arguments': CommonScreenUserArgument(
              null, // id
              dateTimeOld, // dateTimeOld
              null, // qrCode
              null, // fromPage
              getSaveUser, // user
            ),
          },
        if (checkViewTraHangNCCPermission)
          {
            'txt': 'Trả hàng NCC',
            'route': '/TraHangNCC',
            'icon': Icons.inventory_2_outlined,
            'arguments': ScreenArgumentsTraHangNCC(
              getSaveUser.permission!,
              getSaveUser.token!,
              getSaveUser.saleOrg!,
              dateTimeOld,
              getSaveUser.accountId.toString(),
            ),
          },

        // Downtime menu item moved to bottom
      ];

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> dummyTheTreo = [
      // {'LSX': '********* | 0101.b.4 | 140', 'hangTagId': '********-fabe-414c-a270-1d7ea64c8f17'},
      // {'LSX': '********* | 0101 | 148', 'hangTagId': 'e8e0a2b0-70b6-44eb-a175-8db7541e8f3c'},
      // {'LSX': 'test', 'hangTagId': '630A8B5B-E26A-4F28-BCA9-D24222ED1DDE'},
      // {'LSX': 'test 2', 'hangTagId': 'e4b0ddd6-8154-4c9e-8a0e-e5c7c4459267'},

      {'LSX': '********* 1', 'hangTagId': 'e0792471-e0b6-435c-a98a-dd2762fce937'},
      {'LSX': '********* 2', 'hangTagId': 'dad8504f-164d-4fb4-a17e-58938d6cb672'},

      {'LSX': '********* 3', 'hangTagId': '0318f049-db55-4069-8985-4a23a603c666'},
      {'LSX': '********* 4', 'hangTagId': '1fc6fd44-552e-40fe-ad91-9bf2d5ec77d2'},

      {'LSX': 'test 1300', 'hangTagId': 'A41283F7-A80F-44A7-81E6-73CB708CA409'},
    ];

    List<Map<String, String>> dummyQCPassed = [
      // {'LSX': '1004705377435', 'barcode': '1004705377435'},
      // {'LSX': '1751185207348', 'barcode': '1751185207348'},
      {'LSX': '1750994001819 NTZ', 'barcode': '1750994001819'},
      {'LSX': '1752687884587', 'barcode': '1752687884587'},
      {'LSX': 'no data', 'barcode': '1'},
    ];

    return SingleChildScrollView(
      child: SafeArea(
        minimum: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            SizedBox(height: 10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      "Hello!",
                      style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold, color: Colors.black),
                    ),
                    Text(
                      getSaveUser.userName ?? "Loading...",
                      style: TextStyle(fontSize: 12.sp, color: Colors.grey.shade600),
                    ),
                  ],
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.notifications), // Replace with your notification icon
                      onPressed: () {
                        // Put the function you want to trigger on notification button press
                        Navigator.pushNamed(context, "/Notification",
                            arguments: CommonScreenPermissionArgument(
                              getSaveUser.permission!,
                              getSaveUser.token!,
                              getSaveUser.saleOrg!,
                              dateTimeOld,
                              getSaveUser.accountId.toString(),
                              getSaveUser,
                            ));
                      },
                      color: const Color(0xff0052cc),
                    ),
                    TextButton(
                      onPressed: disableButton == true
                          ? null
                          : () async {
                              showDialog<String>(
                                barrierDismissible: false,
                                context: context,
                                builder: (BuildContext context) => WillPopScope(
                                  onWillPop: () async => false,
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                              );
                              // Clear UserHelper cache
                              UserHelper().clearCache();
                              bool check = await MainPageFunction.removeCurrentUser("id", "datetimeNow", "user");
                              Navigator.pop(context);
                              if (check == true) {
                                setButton();
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    backgroundColor: Colors.black,
                                    content: Text(
                                      'Đăng xuất thành công',
                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                    ),
                                    duration: const Duration(seconds: 1)));
                                Future.delayed(const Duration(seconds: 0), () {
                                  Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
                                });
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                    backgroundColor: Colors.black,
                                    content: Text(
                                      'Đăng xuất thất bại! Ứng dụng xảy ra lỗi!',
                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                    ),
                                    duration: const Duration(seconds: 2)));
                              }
                            },
                      child: Text(
                        'Đăng xuất',
                        style: TextStyle(fontSize: 14.sp, color: const Color(0xff0052cc)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 15.h),
            Container(
              width: double.infinity,
              height: 150.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
                image: const DecorationImage(
                  image: AssetImage('assets/banner/banner.jpg'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 4.h),
              child: Align(
                alignment: Alignment.centerRight,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      AppConfig.appVersion,
                      style: TextStyle(
                        fontSize: 9.sp,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    FutureBuilder<String>(
                      future: AppConfig.getEnvironment(), // The Future that the FutureBuilder will work with
                      builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return const CircularProgressIndicator(); // You can return a loading widget while waiting for the Future to resolve
                        } else {
                          if (!snapshot.hasError && snapshot.data != "PRD") {
                            return Container(
                              padding: const EdgeInsets.all(4.0), // Add some padding to make the button bigger
                              margin: const EdgeInsets.only(left: 5.0),
                              decoration: BoxDecoration(
                                color: Colors.orange, // Background color
                                borderRadius: BorderRadius.circular(4.0), // Border radius
                              ),
                              child: Text('${snapshot.data}',
                                  style: TextStyle(fontSize: 9.sp, fontStyle: FontStyle.italic, color: Colors.white, fontWeight: FontWeight.bold)),
                            );
                          } else {
                            return Container();
                          }
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            kDebugMode
                ? Column(
                    children: [
                      RenderDebugButtons(dummyTheTreo, (item) {
                        Navigator.pushNamed(context, '/info', arguments: ScreenArgumentInfor(item['hangTagId']!, getSaveUser.token!, dateTimeOld));
                      }, 'LSX'),
                      // Wrap(
                      //   alignment: WrapAlignment.start,
                      //   runAlignment: WrapAlignment.start,
                      //   spacing: 2.w,
                      //   children: dummyTheTreo.map((item) {
                      //     return DebugButton(
                      //       onPressed: () {
                      //         Navigator.pushNamed(context, '/info',
                      //             arguments: ScreenArgumentInfor(item['hangTagId']!, getSaveUser.token!, dateTimeOld));
                      //       },
                      //       buttonText: item['LSX'].toString(),
                      //     );
                      //   }).toList(),
                      // ),
                      // ElevatedButton(
                      //   style: ElevatedButton.styleFrom(
                      //     padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h), // Adjust padding here
                      //     minimumSize: const Size(0, 0), // Add this
                      //   ),
                      //   onPressed: () {
                      //     showToast(context: context, message: "Đang phát triển");
                      //   },
                      //   child: Text("Test chức năng"),
                      // )
                      Text(
                        "test QC passed stamp",
                        style: TextStyle(fontSize: 10.sp, fontStyle: FontStyle.italic),
                      ),
                      RenderDebugButtons(dummyQCPassed, (item) {
                        Navigator.pushNamed(context, '/QCPassedStampScan',
                            arguments: ScreenArgumentInfor(item['barcode']!, getSaveUser.token!, dateTimeOld));
                      }, 'LSX'),
                    ],
                  )
                : Container(),
            ...buttonList
                .map((button) => Column(
                      children: <Widget>[
                        SizedBox(height: 15.h),
                        ButtonMainPage(
                          txt: button['txt'],
                          route: () async {
                            final result = await MainPageFunction.checkConnectNetwork();
                            if (result != ConnectivityResult.none) {
                              Navigator.pushNamed(context, button['route'], arguments: button['arguments']);
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                    backgroundColor: Colors.black,
                                    content: Text(
                                      'Tính năng cần có kết nối internet để sử dụng',
                                      style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                    ),
                                    duration: const Duration(seconds: 1)),
                              );
                            }
                          },
                          icon: Icon(button['icon'], size: 19.sp, color: Colors.white),
                        ),
                      ],
                    ))
                .toList(),
          ],
        ),
      ),
    );
  }
}

class _ErrorView extends StatelessWidget {
  final bool disableButton;
  final VoidCallback setButton;
  const _ErrorView({Key? key, required this.disableButton, required this.setButton}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          "Ứng dụng xảy ra lỗi!",
          style: TextStyle(
            fontSize: 13.sp,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(
          height: 20.h,
        ),
        ElevatedButton.icon(
          style: ButtonStyle(
            padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 5.h, horizontal: 30.w)),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r), side: const BorderSide(color: Colors.white))),
            side: MaterialStateProperty.all(
              const BorderSide(
                color: Color(0xff000000),
              ),
            ),
            backgroundColor: MaterialStateProperty.all(const Color(0xff000000)),
          ),
          onPressed: disableButton == true
              ? null
              : () async {
                  showDialog<String>(
                    barrierDismissible: false,
                    context: context,
                    builder: (BuildContext context) => WillPopScope(
                      onWillPop: () async => false,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                  // Clear UserHelper cache
                  UserHelper().clearCache();
                  bool check = await MainPageFunction.removeCurrentUser("id", "datetimeNow", "user");
                  Navigator.pop(context);
                  if (check == true) {
                    setButton();
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thành công',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 2)));
                    Future.delayed(const Duration(seconds: 0), () {
                      Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
                    });
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        backgroundColor: Colors.black,
                        content: Text(
                          'Đăng xuất thất bại! Ứng dụng xảy ra lỗi!',
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                        duration: const Duration(seconds: 2)));
                  }
                },
          icon: Icon(Icons.refresh_rounded, color: Colors.white, size: 17.sp),
          label: Text(
            "Đăng xuất",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 15.sp),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    ));
  }
}
