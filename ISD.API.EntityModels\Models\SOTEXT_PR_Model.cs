﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SOTEXT_PR_Model", Schema = "MES")]
    public partial class SOTEXT_PR_Model
    {
        [Key]
        [StringLength(10)]
        public string WERKS { get; set; }
        [Key]
        [StringLength(50)]
        public string BANFN { get; set; }
        [Key]
        [StringLength(50)]
        public string BNFPO { get; set; }
        [Key]
        [StringLength(50)]
        public string ZMES_VBELN { get; set; }
        [Key]
        [StringLength(50)]
        public string MATNR { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BADAT { get; set; }
        [StringLength(10)]
        public string ZCHECK { get; set; }
        public bool? SOInvalid { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(500)]
        public string ZMES_LSXLON { get; set; }
    }
}