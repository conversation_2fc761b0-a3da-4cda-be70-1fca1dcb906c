import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/qualityControlApi.dart';

class ButtonAddNewCardError extends StatelessWidget {
  final VoidCallback addNewCardError;
  final QualityControl? qualityControl;
  const ButtonAddNewCardError({Key? key, required this.addNewCardError, required this.qualityControl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: ElevatedButton.icon(
          icon: Icon(
            Icons.add,
            color: Colors.white,
            size: 15.sp,
          ),
          style: ButtonStyle(
            padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w)),
            side: MaterialStateProperty.all(
              const BorderSide(
                color: Colors.green,
              ),
            ),
            backgroundColor: MaterialStateProperty.all(Colors.green),
          ),
          onPressed: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
              ? null
              : () {
                  addNewCardError();
                },
          label: Text(
            'Thêm',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 11.sp,
            ),
          ),
        ),
      ),
    );
  }
}
