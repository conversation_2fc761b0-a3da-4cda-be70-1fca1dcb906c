class UserModel {
  int? code;
  bool? isSuccess;
  String? message;
  DataUser? data;

  UserModel({this.code, this.isSuccess, this.message, this.data});

  UserModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataUser.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataUser {
  String? accountId;
  String? token;
  String? userName;
  String? employeeCode;
  String? fullName;
  String? validaty;
  String? refreshToken;
  String? saleOrg;
  String? saleOrgName;
  String? companyId;
  String? companyCode;
  String? companyName;
  String? emailId;
  String? role;
  String? vendorCode;
  String? vendorName;
  String? roles;
  String? expiredTime;
  Permission? permission;
  List<String>? routingEnabledCompanyCodes;

  DataUser({
    this.accountId,
    this.token,
    this.userName,
    this.employeeCode,
    this.fullName,
    this.validaty,
    this.refreshToken,
    this.saleOrg,
    this.saleOrgName,
    this.companyId,
    this.companyCode,
    this.companyName,
    this.emailId,
    this.role,
    this.vendorCode,
    this.vendorName,
    this.roles,
    this.expiredTime,
    this.permission,
    this.routingEnabledCompanyCodes,
  });

  DataUser.fromJson(Map<String, dynamic> json) {
    accountId = json['accountId'];
    token = json['token'];
    userName = json['userName'];
    employeeCode = json['employeeCode'];
    fullName = json['fullName'];
    validaty = json['validaty'];
    refreshToken = json['refreshToken'];
    saleOrg = json['saleOrg'];
    saleOrgName = json['saleOrgName'];
    companyId = json['companyId'];
    companyCode = json['companyCode'];
    companyName = json['companyName'];
    emailId = json['emailId'];
    role = json['role'];
    vendorCode = json['vendorCode'];
    vendorName = json['vendorName'];
    roles = json['roles'];
    expiredTime = json['expiredTime'];
    permission = json['permission'] != null ? Permission.fromJson(json['permission']) : null;
    routingEnabledCompanyCodes = json['routingEnabledCompanyCodes'] != null ? List<String>.from(json['routingEnabledCompanyCodes']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['accountId'] = accountId;
    data['token'] = token;
    data['userName'] = userName;
    data['employeeCode'] = employeeCode;
    data['fullName'] = fullName;
    data['validaty'] = validaty;
    data['refreshToken'] = refreshToken;
    data['saleOrg'] = saleOrg;
    data['saleOrgName'] = saleOrgName;
    data['companyId'] = companyId;
    data['companyCode'] = companyCode;
    data['companyName'] = companyName;
    data['emailId'] = emailId;
    data['role'] = role;
    data['vendorCode'] = vendorCode;
    data['vendorName'] = vendorName;
    data['roles'] = roles;
    data['expiredTime'] = expiredTime;
    if (permission != null) {
      data['permission'] = permission!.toJson();
    }
    data['routingEnabledCompanyCodes'] = routingEnabledCompanyCodes;
    return data;
  }
}

class Permission {
  List<MobileScreenModel>? mobileScreenModel;
  List<MenuModel>? menuModel;
  List<MobileScreenPermissionModel>? mobileScreenPermissionModel;

  Permission({this.mobileScreenModel, this.menuModel, this.mobileScreenPermissionModel});

  Permission.fromJson(Map<String, dynamic> json) {
    if (json['mobileScreenModel'] != null) {
      mobileScreenModel = <MobileScreenModel>[];
      json['mobileScreenModel'].forEach((v) {
        mobileScreenModel!.add(MobileScreenModel.fromJson(v));
      });
    }
    if (json['menuModel'] != null) {
      menuModel = <MenuModel>[];
      json['menuModel'].forEach((v) {
        menuModel!.add(MenuModel.fromJson(v));
      });
    }
    if (json['mobileScreenPermissionModel'] != null) {
      mobileScreenPermissionModel = <MobileScreenPermissionModel>[];
      json['mobileScreenPermissionModel'].forEach((v) {
        mobileScreenPermissionModel!.add(MobileScreenPermissionModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (mobileScreenModel != null) {
      data['mobileScreenModel'] = mobileScreenModel!.map((v) => v.toJson()).toList();
    }
    if (menuModel != null) {
      data['menuModel'] = menuModel!.map((v) => v.toJson()).toList();
    }
    if (mobileScreenPermissionModel != null) {
      data['mobileScreenPermissionModel'] = mobileScreenPermissionModel!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MobileScreenModel {
  String? mobileScreenId;
  String? screenName;
  String? screenCode;
  String? menuId;
  int? orderIndex;

  MobileScreenModel({this.mobileScreenId, this.screenName, this.screenCode, this.menuId, this.orderIndex});

  MobileScreenModel.fromJson(Map<String, dynamic> json) {
    mobileScreenId = json['mobileScreenId'];
    screenName = json['screenName'];
    screenCode = json['screenCode'];
    menuId = json['menuId'];
    orderIndex = json['orderIndex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mobileScreenId'] = mobileScreenId;
    data['screenName'] = screenName;
    data['screenCode'] = screenCode;
    data['menuId'] = menuId;
    data['orderIndex'] = orderIndex;
    return data;
  }
}

class MenuModel {
  String? menuId;
  String? menuName;
  String? icon;
  int? orderIndex;
  int? rowIndex;
  bool? isNullValueId;

  MenuModel({this.menuId, this.menuName, this.icon, this.orderIndex, this.rowIndex, this.isNullValueId});

  MenuModel.fromJson(Map<String, dynamic> json) {
    menuId = json['menuId'];
    menuName = json['menuName'];
    icon = json['icon'];
    orderIndex = json['orderIndex'];
    rowIndex = json['rowIndex'];
    isNullValueId = json['isNullValueId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['menuId'] = menuId;
    data['menuName'] = menuName;
    data['icon'] = icon;
    data['orderIndex'] = orderIndex;
    data['rowIndex'] = rowIndex;
    data['isNullValueId'] = isNullValueId;
    return data;
  }
}

class MobileScreenPermissionModel {
  String? rolesId;
  String? mobileScreenId;
  String? mobileScreenCode;
  String? functionId;

  MobileScreenPermissionModel({this.rolesId, this.mobileScreenId, this.functionId, this.mobileScreenCode});

  MobileScreenPermissionModel.fromJson(Map<String, dynamic> json) {
    rolesId = json['rolesId'];
    mobileScreenId = json['mobileScreenId'];
    mobileScreenCode = json['mobileScreenCode'];
    functionId = json['functionId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['rolesId'] = rolesId;
    data['mobileScreenId'] = mobileScreenId;
    data['mobileScreenCode'] = mobileScreenCode;
    data['functionId'] = functionId;
    return data;
  }
}

class GetListCompanyByUserName {
  String? companyCode;
  String? companyName;

  GetListCompanyByUserName({this.companyCode, this.companyName});

  GetListCompanyByUserName.fromJson(Map<String, dynamic> json) {
    companyCode = json['companyCode'];
    companyName = json['companyName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['companyCode'] = companyCode;
    data['companyName'] = companyName;
    return data;
  }

  static GetListCompanyByUserName defaultValue = GetListCompanyByUserName(companyCode: ' ', companyName: '-- Công ty --');
}

class GetSaleOrgByCompanyId {
  String? saleOrgCode;
  String? storeName;

  GetSaleOrgByCompanyId({this.saleOrgCode, this.storeName});

  GetSaleOrgByCompanyId.fromJson(Map<String, dynamic> json) {
    saleOrgCode = json['saleOrgCode'];
    storeName = json['storeName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['saleOrgCode'] = saleOrgCode;
    data['storeName'] = storeName;
    return data;
  }
}
