using ISD.API.EntityModels.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;

namespace iMES_API.Helpers
{
    public class DbContextFactory
    {
        private readonly IConfiguration _configuration;

        public DbContextFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public EntityDataContext CreateDbContext(string connectionName = "DefaultConnection")
        {
            var connectionString = _configuration.GetConnectionString(connectionName);

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionName}' was not found in configuration.");
            }

            var optionsBuilder = new DbContextOptionsBuilder<EntityDataContext>();
            optionsBuilder.UseSqlServer(connectionString);

            return new EntityDataContext(optionsBuilder.Options);
        }
    }
} 