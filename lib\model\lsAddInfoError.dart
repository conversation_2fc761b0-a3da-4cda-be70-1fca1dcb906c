// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:ttf/model/qualityControlApi.dart';
//
// class LsAddInfoError{
//   ErrorList? selectedError;
//   Error? selectedLsError;
//
//
//   final TextEditingController textEditingController1;
//   final TextEditingController textEditingController2;
//   final TextEditingController textEditingController3;
//   final List<File> lsImageFileError;
//   LsAddInfoError(this.selectedError,this.selectedLsError,this.textEditingController1,this.textEditingController2, this.textEditingController3,this.lsImageFileError);
// }