class TyLeTieuHaoSearchModel {
  final String? companyCode;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? pageNumber;
  final int? pageSize;
  final String? lsxSAP;

  TyLeTieuHaoSearchModel({
    this.companyCode,
    this.fromDate,
    this.toDate,
    this.pageNumber,
    this.pageSize,
    this.lsxSAP,
  });

  Map<String, dynamic> toJson() {
    return {
      'companyCode': companyCode,
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'pageNumber': pageNumber,
      'pageSize': pageSize,
      'lsxSAP': lsxSAP,
    };
  }

  factory TyLeTieuHaoSearchModel.fromJson(Map<String, dynamic> json) {
    return TyLeTieuHaoSearchModel(
      companyCode: json['companyCode'] as String?,
      fromDate: json['fromDate'] != null ? DateTime.parse(json['fromDate'] as String) : null,
      toDate: json['toDate'] != null ? DateTime.parse(json['toDate'] as String) : null,
      pageNumber: json['pageNumber'] as int?,
      pageSize: json['pageSize'] as int?,
      lsxSAP: json['lsxSAP'] as String?,
    );
  }
}
