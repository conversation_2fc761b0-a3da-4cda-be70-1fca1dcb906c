CREATE PROCEDURE Report.usp_BC01Report_New
	-- Add the parameters for the stored procedure here
	@LSX NVARCHAR(255) = NULL,
	@LSXSAP NVARCHAR(50) = NULL,
	@VBELN NVARCHAR(50) = NULL,
	@POSNR NVARCHAR(50) = NULL,
	@CompletedFromDate DATETIME = NULL,
	@CompletedToDate DATETIME = NULL,
	@TopRow INT = 10,
	@Plant NVARCHAR(50) = NULL,
	@FinishFromDate DATETIME = NULL,
	@FinishToDate DATETIME = NULL,
	--Trạng thái PPOrder: OPEN | CLOSED
	@isOpen BIT = 1,
  @OrderGroup NVARCHAR(50) = NULL

AS
BEGIN
	SET NOCOUNT ON;

	--<PERSON><PERSON> báo các công đoạn thuộc 4 công đoạn lớn
	DECLARE @Step1Code NVARCHAR(50) = '1015'
			,@Step2Code NVARCHAR(50) = '1016'
			,@Step3Code NVARCHAR(50) = '1018'
			,@Step4Code NVARCHAR(50) = '1024'
			,@Step11_1Code NVARCHAR(50) = '1017'
	
	-- Calculate effective top value once
	DECLARE @EffectiveTopRow BIGINT = CASE WHEN @TopRow = 0 THEN 9223372036854775807 ELSE @TopRow END;

	-- Create optimized temp table with only the necessary columns
	CREATE TABLE #FilteredDAUAT (DAUAT NVARCHAR(50));
  
	-- Insert into temp table based on condition (optimized with index hint)
	IF @OrderGroup IS NULL OR @OrderGroup = 'DT'
	BEGIN
		INSERT INTO #FilteredDAUAT (DAUAT)
		SELECT DISTINCT po.DAUAT
		FROM MES.ProductionOrderModel po WITH (NOLOCK)
		LEFT JOIN MES.ProductionOrderConfigModel conf WITH (NOLOCK) 
			ON po.DAUAT = conf.ConfigCode
		WHERE conf.ConfigCode IS NULL;
	END
	ELSE
	BEGIN
		INSERT INTO #FilteredDAUAT (DAUAT)
		SELECT DISTINCT ConfigCode
		FROM MES.ProductionOrderConfigModel WITH (NOLOCK);
	END

	-- Create index on temp table to improve join performance
	CREATE NONCLUSTERED INDEX IX_FilteredDAUAT ON #FilteredDAUAT (DAUAT);

	-- Create optimized temp table with only needed columns
	CREATE TABLE #bc01_optimized(
		UTCT BIGINT,
		STT BIGINT,
		LSX NVARCHAR(1000),
		ReceiveDate DATETIME,
		VBELN NVARCHAR(50),
		AssignResponsibility NVARCHAR(50),
		PYCSXDT NVARCHAR(1000),
		SOCreateOn DATETIME,
		SchedulelineDeliveryDate DATETIME,
		SchedulelineDeliveryDateUpdate DATETIME,
		ImportTimes INT
	);

	-- Apply filters early in the query and only select needed columns
	INSERT INTO #bc01_optimized
	(
		LSX,
		UTCT,
		ReceiveDate,
		VBELN,
		AssignResponsibility,
		PYCSXDT,
		SOCreateOn,
		SchedulelineDeliveryDate,
		SchedulelineDeliveryDateUpdate,
		ImportTimes
	)
	SELECT 
		bc.ZZLSX,
		bc.STT AS UTCT,
		MAX(lsxsap.ReceiveDate),
		po.KDAUF AS VBELN,
		bc.AssignResponsibility AS AssignResponsibility,
		MAX(soitem.ZPYCSXDT) AS PYCSXDT,
		so.ERDAT AS SOCreateOn,
		MIN(schedule.REQ_DLVDATE) AS SchedulelineDeliveryDate,
		bc.SchedulelineDeliveryDateUpdate,
		bc.ImportTimes
	FROM MES.BCUuTienModel bc WITH (NOLOCK)
	INNER JOIN MES.ProductionOrderModel po WITH (NOLOCK) ON bc.ZZLSX = po.ZZLSX
	INNER JOIN Task.TaskModel lsxsap WITH (NOLOCK) ON po.ProductionOrderId = lsxsap.TaskId
	INNER JOIN tMasterData.CompanyModel cm WITH (NOLOCK) ON lsxsap.CompanyId = cm.CompanyId
	INNER JOIN #FilteredDAUAT f ON po.DAUAT = f.DAUAT
	LEFT JOIN MES.SaleOrderHeader100Model so WITH (NOLOCK) ON lsxsap.Property1 = so.VBELN
	LEFT JOIN MES.SaleOrderItem100Model soitem WITH (NOLOCK) 
		ON lsxsap.Property1 = soitem.VBELN 
		AND SUBSTRING(lsxsap.Property2, PATINDEX('%[^0]%', lsxsap.Property2+'.'), LEN(lsxsap.Property2)) = 
			SUBSTRING(soitem.POSNR, PATINDEX('%[^0]%', soitem.POSNR+'.'), LEN(soitem.POSNR))
	LEFT JOIN MES.SO100ScheduleLineModel schedule WITH (NOLOCK) 
		ON lsxsap.Property1 = schedule.VBELN 
		AND SUBSTRING(lsxsap.Property2, PATINDEX('%[^0]%', lsxsap.Property2+'.'), LEN(lsxsap.Property2)) = 
			SUBSTRING(schedule.POSNR, PATINDEX('%[^0]%', schedule.POSNR+'.'), LEN(schedule.POSNR))
	WHERE 1=1
		AND bc.STT <> 500
		AND po.ZCLOSE <> 'X'
		AND (@LSX IS NULL OR bc.ZZLSX LIKE N'%' + @LSX + '%')
		AND (@VBELN IS NULL OR po.KDAUF LIKE '%' + @VBELN + '%')
		AND (@isOpen IS NULL OR (@isOpen = 0 AND lsxsap.isDeleted = 1) OR (@isOpen = 1 AND (lsxsap.isDeleted IS NULL OR lsxsap.isDeleted = 0)))
		AND (@Plant IS NULL OR bc.Plant = @Plant)
		AND (@CompletedFromDate IS NULL OR CAST(@CompletedFromDate AS DATE) <= CAST(bc.SchedulelineDeliveryDateUpdate AS DATE))
		AND (@CompletedToDate IS NULL OR CAST(bc.SchedulelineDeliveryDateUpdate AS DATE) <= CAST(@CompletedToDate AS DATE))
		AND bc.STT <= @EffectiveTopRow
	GROUP BY 
		cm.CompanyCode,
		bc.ZZLSX,
		bc.STT,
		po.KDAUF,
		bc.AssignResponsibility,
		so.ERDAT,
		bc.SchedulelineDeliveryDateUpdate,
		bc.ImportTimes;

	-- Create index on temp table to improve final select performance
	CREATE NONCLUSTERED INDEX IX_bc01_optimized ON #bc01_optimized (UTCT, ReceiveDate);

	-- Return the final result set using the optimized temp table
	SELECT 
		DENSE_RANK() OVER (ORDER BY UTCT, b.ReceiveDate) AS STT,
		*
	FROM #bc01_optimized b
	ORDER BY b.UTCT;

	-- Clean up
	DROP TABLE #FilteredDAUAT;
	DROP TABLE #bc01_optimized;
END
GO