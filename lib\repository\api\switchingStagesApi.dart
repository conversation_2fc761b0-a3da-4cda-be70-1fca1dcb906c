import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';

class SwitchingStagesApi {
  static Future<http.Response> getSwitchingStages(String barcode, String token) async {
    final data = {"Barcode": barcode};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlSwitchingStage}SwitchingStages2?Barcode=${data['Barcode']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postSwitchingStages(String taskId, String toBarcode, String toStepCode, String token) async {
    Map data = {"taskId": taskId, "toBarcode": toBarcode, "toStepCode": toStepCode};

    var body = json.encode(data);
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlSwitchingStage + "SwitchingStages");
    debugPrint(url.toString());
    debugPrint(body.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersToken(token), body: body);
    debugPrint(response.statusCode.toString());
    debugPrint(response.body.toString());
    return response;
  }
}
