import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class CommonMenuButton extends StatelessWidget {
  final String text;
  final String? subText;

  final GestureTapCallback route;
  final Icon icon;
  final bool? isCenter;

  const CommonMenuButton({Key? key, required this.text, required this.route, required this.icon, this.isCenter = false, this.subText = ""})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: route,
      child: Container(
        padding: EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(width: 0.5.w, color: const Color(0xff0052cc)),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              flex: 1,
              child: CircleAvatar(
                backgroundColor: const Color(0xff0052cc),
                child: icon,
              ),
            ),
            SizedBox(width: 5.w),
            Expanded(
                flex: 8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      text,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13.sp,
                        // color: const Color(0xff0052cc),
                      ),
                      textAlign: isCenter == true ? TextAlign.center : TextAlign.start,
                    ),
                    if (subText != null)
                      Text(
                        subText.toString(),
                        style: TextStyle(fontWeight: FontWeight.normal, fontSize: 11.sp, color: Colors.grey),
                      ),
                  ],
                )),
            Expanded(
              flex: 1,
              child: Icon(Icons.arrow_forward_ios_rounded, size: 15.sp, color: const Color(0xff0052cc)),
            ),
          ],
        ),
      ),
    );
  }
}
