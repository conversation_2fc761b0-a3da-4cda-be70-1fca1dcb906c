import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DatePickerIOS extends StatefulWidget {
  const DatePickerIOS({Key? key}) : super(key: key);

  @override
  State<DatePickerIOS> createState() => _DatePickerIOSState();
}

class _DatePickerIOSState extends State<DatePickerIOS> {
  DateTime? _todateComplete;

  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: [
        SizedBox(
            height: 250.h,
            child: CupertinoDatePicker(
                initialDateTime: DateTime.now(),
                mode: CupertinoDatePickerMode.date,
                onDateTimeChanged: (val) {
                  setState(() {
                    _todateComplete = val;
                  });
                })),
      ],
      cancelButton: CupertinoButton(
        child: const Text('OK'),
        onPressed: () =>
            Navigator.pop(context, _todateComplete ?? DateTime.now()),
      ),
    );
  }
}

class DatePickerLsWareHouseHIOS extends StatefulWidget {
  final DateTime dateRequest;
  const DatePickerLsWareHouseHIOS({Key? key, required this.dateRequest}) : super(key: key);

  @override
  State<DatePickerLsWareHouseHIOS> createState() => _DatePickerLsWareHouseHIOSState();
}

class _DatePickerLsWareHouseHIOSState extends State<DatePickerLsWareHouseHIOS> {
  DateTime? _newDateRequest;

  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: [
        SizedBox(
            height: 250.h,
            child: CupertinoDatePicker(
                initialDateTime: widget.dateRequest,
                mode: CupertinoDatePickerMode.date,
                onDateTimeChanged: (val) {
                  setState(() {
                    _newDateRequest = val;
                  });
                })),
      ],
      cancelButton: CupertinoButton(
        child: const Text('OK'),
        onPressed: () =>
            Navigator.pop(context, _newDateRequest ?? DateTime.now()),
      ),
    );
  }
}