import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';

class DetailReservationImportedApi {
  static Future<http.Response> getDetailReservationImportedApi(String reservationId, String token) async {
    Map<String, dynamic> data = {"ReservationId": reservationId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
    // final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    // final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}DetailReservationImported", data);

    final fullUrl = "$baseUrl/${UrlApi.baseUrlWarehousTransaction_2}DetailReservationImported?ReservationId=${reservationId}";
    final url = Uri.parse(fullUrl);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
