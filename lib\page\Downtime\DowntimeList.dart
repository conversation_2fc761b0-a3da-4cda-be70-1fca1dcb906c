import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/page/Downtime/element/FilterListDowntime.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import '../../model/userModel.dart';
import '../../model/downtimeModel.dart';
import '../../repository/function/downtimeFunction.dart';
import '../LostConnect.dart';
import 'dart:convert';

class DowntimeList extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;

  const DowntimeList({
    Key? key,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  _DowntimeListState createState() => _DowntimeListState();
}

class _DowntimeListState extends State<DowntimeList> {
  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  List<DowntimeRecord>? _records;
  ConnectivityResult _result = ConnectivityResult.none;
  DowntimeSearchModel? _searchModel;
  CommonDateModel? _commonDateModel;
  List<CommonDates>? _commonDates;
  FilterQCModel? _filterLSQC;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      // Initialize default search model if not exists
      if (_searchModel == null) {
        final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.user.token!);
        if (dataDropdown != null) {
          _filterLSQC = dataDropdown;
          _commonDates = dataDropdown.data?.commonDates;

          // Get default date range (ThisWeek)
          final defaultCommonDate = _commonDates?.firstWhere(
            (element) => element.catalogCode == "ThisWeek",
            orElse: () => _commonDates!.first,
          );

          if (defaultCommonDate != null) {
            final commonDateModel = await ListQCFunction.getCommonDateModel(
              defaultCommonDate.catalogCode!,
              widget.user.token!,
            );

            _searchModel = DowntimeSearchModel(
              companyCode: widget.user.companyCode ?? '',
              pageNumber: 1,
              pageSize: 20,
              fromDate: commonDateModel?.fromDate != null ? DateTime.parse(commonDateModel!.fromDate!) : null,
              toDate: commonDateModel?.toDate != null ? DateTime.parse(commonDateModel!.toDate!) : null,
            );
          }
        }
      }

      // Fetch the actual data
      if (_searchModel != null) {
        final data = await DowntimeFunction.fetchDowntimeList(
          widget.user.token!,
          _searchModel!,
        );

        if (!mounted) return;

        setState(() {
          _records = data?.data;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadData() async {
    if (_isLoading) return; // Prevent multiple simultaneous refreshes

    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _isError = false;
      });

      // Only fetch data if we have a valid search model
      if (_searchModel != null) {
        final data = await DowntimeFunction.fetchDowntimeList(
          widget.user.token!,
          _searchModel!,
        );

        if (!mounted) return;

        setState(() {
          _records = data?.data;
        });
      }
    } on SocketException catch (_) {
      debugPrint("SocketException caught while fetching downtime list.");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _loadData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDateTime(String? dateTime) {
    if (dateTime == null) return '';
    try {
      final dt = DateTime.parse(dateTime);
      return DateFormat('dd/MM/yyyy HH:mm').format(dt);
    } catch (e) {
      return dateTime;
    }
  }

  String _calculateTimeSpan(String? startTime, String? endTime) {
    try {
      if (startTime == null || endTime == null) {
        return '';
      }

      final start = DateFormat('HH:mm').parse(startTime);
      final end = DateFormat('HH:mm').parse(endTime);

      if (end.isBefore(start)) {
        return '';
      }

      final difference = end.difference(start);
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours == 0) {
        return '($minutes phút)';
      } else if (minutes == 0) {
        return '($hours tiếng)';
      } else {
        return '($hours tiếng $minutes phút)';
      }
    } catch (e) {
      debugPrint('Error calculating time span: $e');
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          Builder(
            builder: (BuildContext context) {
              return IconButton(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                hoverColor: Colors.transparent,
                icon: Icon(
                  Icons.search_outlined,
                  size: 19.sp,
                  color: Colors.white,
                ),
                onPressed: _isLoading == true
                    ? null
                    : () async {
                        await _checkConnectNetwork();
                        if (!mounted) return;
                        if (_result != ConnectivityResult.none) {
                          Scaffold.of(context).openEndDrawer();
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            backgroundColor: Colors.black,
                            content: Text(
                              'Tính năng cần có kết nối internet để sử dụng',
                              style: TextStyle(fontSize: 15.sp, color: Colors.white),
                            ),
                          ));
                        }
                      },
              );
            },
          ),
        ],
        title: Text(
          'Downtime',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      endDrawer: _isLoading == true
          ? null
          : FilterListDowntime(
              searchModel: _searchModel,
              token: widget.user.token!,
              user: widget.user,
              onFilterSelected: (DowntimeSearchModel filter) {
                setState(() {
                  _searchModel = filter;
                });
                _loadData(); // Reload data with new filters
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.pushNamed(context, '/DowntimeDetail', arguments: {
            'id': '',
            'dateTimeOld': widget.dateTimeOld,
            'user': widget.user,
          });
          if (result == true) {
            // reload data
            _loadData();
          }
        },
        backgroundColor: const Color(0xff4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: _buildBody(),
    );
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _loadData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    if (_records == null || _records!.isEmpty) {
      return RefreshIndicator(
        onRefresh: _loadData,
        child: ListView(
          children: [
            SizedBox(height: MediaQuery.of(context).size.height / 3),
            Center(
              child: Text(
                'Không có dữ liệu',
                style: TextStyle(fontSize: 15.sp),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(), // Enable scrolling even when content is small
        itemCount: _records!.length,
        itemBuilder: (context, index) {
          final record = _records![index];
          return Card(
            elevation: 2,
            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Tổ: ${record.departmentName ?? record.departmentCode ?? ''}',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              RichText(
                                text: TextSpan(
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.black,
                                  ),
                                  children: [
                                    const TextSpan(text: 'Công đoạn: '),
                                    TextSpan(
                                      text: record.stepCode != null && record.stepName != null
                                          ? '${record.stepCode} | ${record.stepName}'
                                          : record.stepCode ?? record.stepName ?? '',
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (record.status != null)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color: _getVerificationStatusColor(record.status!),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              _getVerificationStatusText(record.status!),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.sp,
                              ),
                            ),
                          ),
                        // if (record.verificationStatus != null)
                        //   Container(
                        //     padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        //     decoration: BoxDecoration(
                        //       color: _getVerificationStatusColor(record.verificationStatus!),
                        //       borderRadius: BorderRadius.circular(12.r),
                        //     ),
                        //     child: Text(
                        //       _getVerificationStatusText(record.verificationStatus!),
                        //       style: TextStyle(
                        //         color: Colors.white,
                        //         fontSize: 12.sp,
                        //       ),
                        //     ),
                        //   ),
                      ],
                    ),
                    Divider(height: 16.h),
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text.rich(
                            TextSpan(
                              style: TextStyle(fontSize: 12.sp),
                              children: [
                                TextSpan(text: '${record.startTime ?? ''} - ${record.endTime ?? ''}'),
                                TextSpan(
                                  text: ' ${_calculateTimeSpan(record.startTime, record.endTime)}',
                                  style: TextStyle(
                                    color: Colors.blue[700],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.error_outline, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            'Nguyên nhân: ${record.reason ?? ''}',
                            style: TextStyle(fontSize: 12.sp),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.group_work, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            'Bộ phận chịu trách nhiệm: ${record.responsibleTeam ?? ''}',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.factory, size: 16.sp, color: Colors.grey[600]),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            'Nhà máy: ${record.companyCode ?? ''}',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ),
                      ],
                    ),
                    if (record.responsibleDepartment?.isNotEmpty == true) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(Icons.percent, size: 16.sp, color: Colors.grey[600]),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: Text(
                              'Tỉ lệ trách nhiệm: ${record.responsibleDepartment}%',
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (record.note?.isNotEmpty == true) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(Icons.note, size: 16.sp, color: Colors.grey[600]),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: Text(
                              'Ghi chú: ${record.note}',
                              style: TextStyle(fontSize: 12.sp),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                    SizedBox(height: 8.h),
                    Text(
                      'Tạo lúc: ${_formatDateTime(record.createdDate)}',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.grey[500],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ).onTap(() async {
            final result = await Navigator.pushNamed(
              context,
              '/DowntimeDetail',
              arguments: {
                'id': record.id,
                'dateTimeOld': widget.dateTimeOld,
                'user': widget.user,
              },
            );
            if (result == true) {
              _loadData();
            }
          });
        },
      ),
    );
  }

  Color _getVerificationStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'APPROVED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      case 'CREATED':
        return Colors.blue;
      case 'PENDING':
      default:
        return Colors.orange;
    }
  }

  String _getVerificationStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'APPROVED':
        return 'Đã duyệt';
      case 'REJECTED':
        return 'Từ chối';
      case 'CREATED':
        return 'Mới tạo';
      case 'PENDING':
      default:
        return 'Chờ duyệt';
    }
  }
}

extension TapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }
}
