﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("Comment_File_Mapping", Schema = "Task")]
    public partial class Comment_File_Mapping
    {
        [Key]
        public Guid TaskCommentId { get; set; }
        [Key]
        public Guid FileAttachmentId { get; set; }
        [StringLength(200)]
        public string Note { get; set; }

        [ForeignKey("FileAttachmentId")]
        [InverseProperty("Comment_File_Mapping")]
        public virtual FileAttachmentModel FileAttachment { get; set; }
        [ForeignKey("TaskCommentId")]
        [InverseProperty("Comment_File_Mapping")]
        public virtual TaskCommentModel TaskComment { get; set; }
    }
}