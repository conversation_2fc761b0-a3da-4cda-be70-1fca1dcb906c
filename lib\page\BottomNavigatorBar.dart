import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import '../Widget/dialogWidget/DialogErrorQuality.dart';
import '../Widget/dialogWidget/DialogImage.dart';
import '../Widget/dialogWidget/DialogQualityInformation.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/ButtonAddNewCardErrorQuatity.dart';
import '../element/DropdownQualityView.dart';
import '../element/ImageQuatity.dart';
import '../element/ListChoseFileQuality.dart';
import '../element/QualityErrorValidate.dart';
import '../element/QualityTitle.dart';
import '../element/TitleQuality.dart';
import '../element/listImagePicker.dart';
import '../element/timeOut.dart';
import '../model/GetDefectLevel.dart';
import '../model/QuantityInformationSelectedInfor.dart';
import '../model/dropdownDefectLevel.dart';
import '../model/mulitListImageFile.dart';
import '../model/multiSelectedErrorQuality.dart';
import '../model/qualityControlApi.dart';
import '../model/rawMaterialCard.dart';
import '../model/typeAheadErrorQuatity.dart';
import '../model/userModel.dart';
import '../repository/function/imageFunction.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../repository/function/qualityControlDetailFunction.dart';
import '../repository/function/qualityControlErrorFunction.dart';
import '../repository/function/qualityControlFunction.dart';
import '../repository/function/qualityControlInformationFunciton.dart';
import 'BottomNavigatorBarComponent.dart';
import 'LostConnect.dart';

// Kiểm tra chất lượng
class BottomNavigatorBar extends StatefulWidget {
  final String qualityControlId;
  final String dateTimeOld;
  final String qrCode;
  final String fromPage;
  final DataUser user;
  const BottomNavigatorBar(
      {Key? key, required this.qualityControlId, required this.dateTimeOld, required this.qrCode, required this.fromPage, required this.user})
      : super(key: key);

  @override
  State<BottomNavigatorBar> createState() => _BottomNavigatorBarState();
}

class _BottomNavigatorBarState extends State<BottomNavigatorBar> {
  // QualityControlDetail? _qualityControlDetail;
  QualityControlModel? _qualityControlModel;
  QualityControl? _qualityControl;
  // List<QualityCheckerList> _qualityCheckerList = [];
  List<QualityTypeList> _qualityTypeList = [];
  List<TestMethodList> _lsTestMethodList = [];
  List<ResultList> _lsResultList = [];
  List<SamplingLevelList> _lsSamplingLevelList = [];
  List<Error> _lsError = [];
  List<ThongTinKiemTra>? _lsQualityControlInformationIdList;
  List<QualityControlInformation> _lsQualityControlInformation = [];
  List<ErrorList>? _lsErrorList;
  DataRawMeterial? _dataRawMaterial;
  final _controller_1 = TextEditingController();
  final _controller_2 = TextEditingController();
  final _controllerHideDetailLever = TextEditingController();
  final _controller_3 = TextEditingController();
  final _controller_4 = TextEditingController();
  // final List<FocusNode> _focusError = [];
  final List<TextEditingController> _lsControllerError = [];
  final List<File> _lsFileTabCheck = [];
  final ImagePicker _pickerImage = ImagePicker();
  final List<ThongTinKiemTra?> _lsSelectedInfo = [];
  final List<TextEditingController> _lsControllerInformation = [];
  // final List<FocusNode> _focusInformation = [];
  final List<List<File>> _lsGetFileInfor = [];
  final List<TextEditingController> _lsTextEditingController = [];
  final List<int> _lsGetIndexInfo = [];
  final List<bool> _lsErrorInfor = [];
  final List<ErrorList?> _lsSelectedError = [];
  final List<TextEditingController> _lsTextEditingControllerError_1 = [];
  // final List<TextEditingController> _lsTextEditingControllerError_2 = [];
  List<DataGetDefectLevel?> _lselectDataGetDefectLevel = [];
  List<DataGetDefectLevel> _lsDataGetDefetchLevel = [];
  final List<TextEditingController> _lsTextEditingControllerError_3 = [];
  final List<int> _lsGetIndexError = [];
  final List<List<File>> _lsGetFileError = [];
  final List<bool> _checkVisiButtonInformation = [];
  final List<bool> _checkVisiButtonError = [];
  // List<ErrorList> _lsErrorListFiler = [];
  TestMethodList? _selectedMethod;
  SamplingLevelList? _selectedLevel;
  ResultList? _selectedResult;
  ResultList? _selectedResultDetail;

  late int _indexError;
  late bool _timeOut;
  late int _indexInfo;
  bool _disableButtonTimeOut = false;
  bool _isNotWifi = false;
  bool _isLoading = false;
  bool _isError = false;
  bool _hideDetailLever = false;
  bool _errorSelectType = false;
  bool _errorP0 = false;
  bool _errorQuantityCheck = false;
  bool _errorSelectedResultQualityView = false;
  bool _errorTestMethodDetail = false;
  bool _errorLevelDetail = false;
  bool _errorAcceptableLevelDetail = false;
  bool _errorQuantityCheckDetail = false;
  bool _errorResultCheckDetail = false;
  bool _loadingGetQuanititySample = false;

  DateTime? _date;
  String _getDate = " ";
  QualityCheckerInfo? _selectedStaff;
  QualityTypeList? _selectedType;

  @override
  void initState() {
    super.initState();
    _loadDataAndSetDefault();
  }

  // void _resetState(){
  //   setState(() {});
  // }
  Future<void> _getGetQuanititySample(BuildContext context) async {
    setState(() {
      _loadingGetQuanititySample = true;
    });
    final data = await QualityControlFunction.fetchGetQuantitySample(
        _selectedLevel!.catalogCode.toString(), _controller_2.text, widget.user.token.toString(), context, mounted);
    if (!mounted) return;
    setState(() {
      _loadingGetQuanititySample = false;
      _controller_4.text = data == null ? "" : data.quantitySample.toString();
      _errorQuantityCheckDetail = _controller_4.text.isNotEmpty ? false : true;
    });
    FocusManager.instance.primaryFocus?.unfocus();
  }

  void _checkValidate() {
    setState(() {
      if (_selectedType == null || _selectedType!.catalogCode == " ") {
        _errorSelectType = true;
      } else {
        _errorSelectType = false;
      }
      if (_qualityControl!.qcType != "NVL") {
        if (_controller_1.text.isEmpty) {
          _errorP0 = true;
        } else {
          _errorP0 = false;
        }
      }
      if (_controller_2.text.isEmpty) {
        _errorQuantityCheck = true;
      } else {
        _errorQuantityCheck = false;
      }
      if (_selectedResult == null || _selectedResult!.catalogCode == " ") {
        _errorSelectedResultQualityView = true;
      } else {
        _errorSelectedResultQualityView = false;
      }
      if (_selectedMethod == null || _selectedMethod!.catalogCode == " ") {
        _errorTestMethodDetail = true;
      } else {
        _errorTestMethodDetail = false;
      }
      if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
        _errorLevelDetail = true;
      } else {
        _errorLevelDetail = false;
      }

      if (_controller_3.text.isEmpty) {
        _errorAcceptableLevelDetail = true;
      } else {
        _errorAcceptableLevelDetail = false;
      }
      debugPrint(_errorAcceptableLevelDetail.toString());

      if (_controller_4.text.isEmpty) {
        _errorQuantityCheckDetail = true;
      } else {
        _errorQuantityCheckDetail = false;
      }
      if (_selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ") {
        _errorResultCheckDetail = true;
      } else {
        _errorResultCheckDetail = false;
      }

      for (int i = 0; i < _lsSelectedInfo.length; i++) {
        if (_lsSelectedInfo[i] == null) {
          setState(() {
            _lsErrorInfor[i] = true;
          });
        } else {
          _lsErrorInfor[i] = false;
        }
      }
    });
  }

  Future<void> _loadDataAndSetDefault() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
        debugPrint(_timeOut.toString());
      } else {
        if (!mounted) return;
        await _getQuantityControl();
        if (!mounted) return;
        if (_qualityControl != null) {
          if (_qualityControl!.qcType == "NVL") {
            _getMaterial();
          } else {
            setState(() {
              _isLoading = false;
              _getDate = QualityControlFunction.getDateString(_qualityControl, _date);
              _selectedStaff = QualityCheckerInfo(
                  accountId: _qualityControl!.qualityChecker ?? widget.user.accountId.toString(),
                  salesEmployeeName: _qualityControl!.qualityChecker != null ? _qualityControl!.qcSaleEmployee : widget.user.fullName);
              if (_qualityControl!.qualityType != null) {
                int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityType);
                _selectedType = _qualityTypeList[indexType];
              }
              if (_qualityControl!.po != null) {
                _controller_1.text = _qualityControl!.po.toString();
              }
              if (_qualityControl!.inspectionLotQuantity != null) {
                _controller_2.text = (_qualityControl!.inspectionLotQuantity!.round()).toString();
              }
              if (_qualityControl!.result != null) {
                int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.result);
                _selectedResult = _lsResultList[indexResult];
              }
              if (_qualityControl!.qualityControlDetail != null) {
                if (_qualityControl!.qualityControlDetail!.testMethod != null) {
                  int indexMethod =
                      _lsTestMethodList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.testMethod);
                  _selectedMethod = _lsTestMethodList[indexMethod];
                } else {
                  _selectedMethod = null;
                }
                if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel != "OTHER") {
                  int indexLevel =
                      _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
                  _selectedLevel = _lsSamplingLevelList[indexLevel];
                } else if (_qualityControl!.qualityControlDetail!.samplingLevel != null &&
                    _qualityControl!.qualityControlDetail!.samplingLevel == "OTHER") {
                  int indexLevel =
                      _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
                  _selectedLevel = _lsSamplingLevelList[indexLevel];
                  _controllerHideDetailLever.text = _qualityControl!.qualityControlDetail!.samplingLevelName ?? "";
                  _hideDetailLever = true;
                } else {
                  _selectedLevel = null;
                }
                if (_qualityControl!.qualityControlDetail!.acceptableLevel != null) {
                  _controller_3.text = _qualityControl!.qualityControlDetail!.acceptableLevel.toString();
                } else {
                  _controller_3.text = _controller_3.text;
                }

                if (_qualityControl!.qualityControlDetail!.inspectionQuantity != null) {
                  _controller_4.text = (_qualityControl!.qualityControlDetail!.inspectionQuantity!.round()).toString();
                } else {
                  _controller_4.text = _controller_4.text;
                }
                if (_qualityControl!.qualityControlDetail!.result != null) {
                  int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.result);
                  _selectedResultDetail = _lsResultList[indexResult];
                } else {
                  _selectedResultDetail = null;
                }
              }
              if (_lsQualityControlInformation.isNotEmpty) {
                for (int i = 0; i < _lsQualityControlInformation.length; i++) {
                  _lsTextEditingController.add(TextEditingController());
                  _lsControllerInformation.add(TextEditingController());
                  // _focusInformation.add(FocusNode());
                  _lsTextEditingController[i].text = _lsQualityControlInformation[i].notes ?? _lsTextEditingController[i].text;
                  _indexInfo = _lsQualityControlInformationIdList!
                              .indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId) !=
                          -1
                      ? _lsQualityControlInformationIdList!
                          .indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId)
                      : _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
                  _lsGetIndexInfo.add(_indexInfo);
                  _lsSelectedInfo.add(_lsQualityControlInformationIdList![_lsGetIndexInfo[i]]);
                  _lsControllerInformation[i].text = _lsSelectedInfo[i] == null ? "" : _lsSelectedInfo[i]!.name ?? "";
                  _lsGetFileInfor.add([]);
                  _lsErrorInfor.add(false);
                  _checkVisiButtonInformation.add(false);
                }
              } else {
                _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
                _lsTextEditingController.add(TextEditingController());
                _lsControllerInformation.add(TextEditingController());
                // _focusInformation.add(FocusNode());
                _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
                _lsGetIndexInfo.add(_indexInfo);
                _lsGetFileInfor.add([]);
                _lsSelectedInfo.add(null);
                _lsErrorInfor.add(false);
                _checkVisiButtonInformation.add(false);
              }
              if (_lsError.isNotEmpty) {
                for (int i = 0; i < _lsError.length; i++) {
                  _checkVisiButtonError.add(false);
                  _lsTextEditingControllerError_1.add(TextEditingController());
                  _lselectDataGetDefectLevel.add(null);
                  _lsTextEditingControllerError_3.add(TextEditingController());
                  _lsControllerError.add(TextEditingController());
                  // _focusError.add(FocusNode());
                  _lsTextEditingControllerError_1[i].text =
                      _lsError[i].quantityError == null ? (0.toString()) : ((_lsError[i].quantityError ?? 0.0).round()).toString();
                  _lselectDataGetDefectLevel[i] = _lsError[i].levelError == null
                      ? _lselectDataGetDefectLevel[i]
                      : _lsDataGetDefetchLevel.firstWhereOrNull((element) => element.key == _lsError[i].levelError.toString());
                  // _lsError[i].levelError.toString();
                  _lsTextEditingControllerError_3[i].text =
                      _lsError[i].notes == null ? _lsTextEditingControllerError_3[i].text : _lsError[i].notes.toString();
                  _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
                  _lsGetIndexError.add(_indexError);
                  _lsGetFileError.add([]);
                  _lsSelectedError.add(_lsGetIndexError[i] == -1 ? null : _lsErrorList![_lsGetIndexError[i]]);
                  _lsControllerError[i].text = _lsSelectedError[i] == null ? "" : _lsSelectedError[i]!.catalogTextVi ?? "";
                }
              } else {
                _checkVisiButtonError.add(false);
                _lsError.add(QualityControlErrorFunction.defaultListError);
                _lsTextEditingControllerError_1.add(TextEditingController());
                _lselectDataGetDefectLevel.add(null);
                _lsTextEditingControllerError_3.add(TextEditingController());
                _lsControllerError.add(TextEditingController());
                // _focusError.add(FocusNode());
                _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
                _lsGetIndexError.add(_indexError);
                _lsGetFileError.add([]);
                _lsSelectedError.add(null);
              }
            });
          }
        } else {
          if (!mounted) return;
          setState(() {
            _isLoading = false;
          });
        }
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _isError = true;
        _timeOut = false;
      });
    }
  }

  Future<void> _getMaterial() async {
    final data = await ImportWareHouseFunction.fetchRawMaterial(_qualityControl!.customerReference.toString(), widget.user.token.toString(), "qr");
    if (!mounted) return;
    if (data != null) {
      setState(() {
        _isLoading = false;
        _getDate = QualityControlFunction.getDateString(_qualityControl, _date);
        _dataRawMaterial = data;
        _selectedStaff = QualityCheckerInfo(
            accountId: _qualityControl!.qualityChecker ?? widget.user.accountId.toString(),
            salesEmployeeName: _qualityControl!.qualityChecker != null ? _qualityControl!.qcSaleEmployee : widget.user.fullName);
        if (_qualityControl!.qualityType != null) {
          int indexType = _qualityTypeList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityType);
          _selectedType = _qualityTypeList[indexType];
        }
        if (_qualityControl!.po != null) {
          _controller_1.text = _qualityControl!.po.toString();
        }
        if (_qualityControl!.inspectionLotQuantity != null) {
          _controller_2.text = (_qualityControl!.inspectionLotQuantity!.round()).toString();
        }
        if (_qualityControl!.result != null) {
          int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.result);
          _selectedResult = _lsResultList[indexResult];
        }
        if (_qualityControl!.qualityControlDetail != null) {
          if (_qualityControl!.qualityControlDetail!.testMethod != null) {
            int indexMethod = _lsTestMethodList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.testMethod);
            _selectedMethod = _lsTestMethodList[indexMethod];
          } else {
            _selectedMethod = null;
          }
          if (_qualityControl!.qualityControlDetail!.samplingLevel != null && _qualityControl!.qualityControlDetail!.samplingLevel != "OTHER") {
            int indexLevel =
                _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
            _selectedLevel = _lsSamplingLevelList[indexLevel];
          } else if (_qualityControl!.qualityControlDetail!.samplingLevel != null &&
              _qualityControl!.qualityControlDetail!.samplingLevel == "OTHER") {
            int indexLevel =
                _lsSamplingLevelList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.samplingLevel);
            _selectedLevel = _lsSamplingLevelList[indexLevel];
            _controllerHideDetailLever.text = _qualityControl!.qualityControlDetail!.samplingLevelName ?? "";
            _hideDetailLever = true;
          } else {
            _selectedLevel = null;
          }
          if (_qualityControl!.qualityControlDetail!.acceptableLevel != null) {
            _controller_3.text = _qualityControl!.qualityControlDetail!.acceptableLevel.toString();
          } else {
            _controller_3.text = _controller_3.text;
          }

          if (_qualityControl!.qualityControlDetail!.inspectionQuantity != null) {
            _controller_4.text = (_qualityControl!.qualityControlDetail!.inspectionQuantity!.round()).toString();
          } else {
            _controller_4.text = _controller_4.text;
          }
          if (_qualityControl!.qualityControlDetail!.result != null) {
            int indexResult = _lsResultList.indexWhere((element) => element.catalogCode == _qualityControl!.qualityControlDetail!.result);
            _selectedResultDetail = _lsResultList[indexResult];
          } else {
            _selectedResultDetail = null;
          }
        }
        if (_lsQualityControlInformation.isNotEmpty) {
          for (int i = 0; i < _lsQualityControlInformation.length; i++) {
            _lsTextEditingController.add(TextEditingController());
            _lsControllerInformation.add(TextEditingController());
            // _focusInformation.add(FocusNode());
            _lsTextEditingController[i].text = _lsQualityControlInformation[i].notes ?? _lsTextEditingController[i].text;
            _indexInfo = _lsQualityControlInformationIdList!
                        .indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId) !=
                    -1
                ? _lsQualityControlInformationIdList!
                    .indexWhere((element) => element.id == _lsQualityControlInformation[i].qualityControlInformationId)
                : _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
            _lsGetIndexInfo.add(_indexInfo);
            _lsSelectedInfo.add(_lsQualityControlInformationIdList![_lsGetIndexInfo[i]]);
            _lsControllerInformation[i].text = _lsSelectedInfo[i] == null ? "" : _lsSelectedInfo[i]!.name ?? "";
            _lsGetFileInfor.add([]);
            _lsErrorInfor.add(false);
            _checkVisiButtonInformation.add(false);
          }
        } else {
          _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
          _lsTextEditingController.add(TextEditingController());
          _lsControllerInformation.add(TextEditingController());
          // _focusInformation.add(FocusNode());
          _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
          _lsGetIndexInfo.add(_indexInfo);
          _lsGetFileInfor.add([]);
          _lsSelectedInfo.add(null);
          _lsErrorInfor.add(false);
          _checkVisiButtonInformation.add(false);
        }
        if (_lsError.isNotEmpty) {
          for (int i = 0; i < _lsError.length; i++) {
            _checkVisiButtonError.add(false);
            _lsTextEditingControllerError_1.add(TextEditingController());
            _lselectDataGetDefectLevel.add(null);
            _lsTextEditingControllerError_3.add(TextEditingController());
            _lsControllerError.add(TextEditingController());
            // _focusError.add(FocusNode());
            _lsTextEditingControllerError_1[i].text =
                _lsError[i].quantityError == null ? (0.toString()) : ((_lsError[i].quantityError ?? 0.0).round()).toString();
            // _lsTextEditingControllerError_2[i].text =
            // _lsError[i].levelError == null
            //     ? _lsTextEditingControllerError_2[i].text
            //     : _lsError[i].levelError.toString();
            _lselectDataGetDefectLevel[i] = _lsError[i].levelError == null
                ? _lselectDataGetDefectLevel[i]
                : _lsDataGetDefetchLevel.firstWhereOrNull((element) => element.key == _lsError[i].levelError.toString());
            _lsTextEditingControllerError_3[i].text =
                _lsError[i].notes == null ? _lsTextEditingControllerError_3[i].text : _lsError[i].notes.toString();
            _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == _lsError[i].catalogCode);
            _lsGetIndexError.add(_indexError);
            _lsGetFileError.add([]);
            _lsSelectedError.add(_lsErrorList![_lsGetIndexError[i]]);
            _lsControllerError[i].text = _lsSelectedError[i] == null ? "" : _lsSelectedError[i]!.catalogTextVi ?? "";
          }
        } else {
          _lsError.add(QualityControlErrorFunction.defaultListError);
          _lsTextEditingControllerError_1.add(TextEditingController());
          _lselectDataGetDefectLevel.add(null);
          _lsTextEditingControllerError_3.add(TextEditingController());
          _lsControllerError.add(TextEditingController());
          // _focusError.add(FocusNode());
          _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
          _lsGetIndexError.add(_indexError);
          _lsGetFileError.add([]);
          _lsSelectedError.add(null);
          _checkVisiButtonError.add(false);
        }
      });
    }
  }

  void _setDate(DateTime? newDate) {
    if (!mounted) return;
    if (newDate != null) {
      setState(() {
        _date = DateFormat("yyyy-MM-ddTHH:mm:ss")
            .parse(DateFormat("yyyy-MM-ddTHH:mm:ss").format(DateTime(newDate.year, newDate.month, newDate.day, newDate.hour, newDate.minute)));
        _getDate = QualityControlFunction.getDateString(_qualityControl, _date);
        debugPrint(_date!.toIso8601String());
      });
    } else {
      return;
    }
  }

  Future<void> _getQuantityControl() async {
    setState(() {
      _timeOut = false;
      _isLoading = true;
      _isNotWifi = false;
    });
    final listData = await Future.wait([
      QualityControlFunction.fetchQualityControl(widget.qualityControlId, widget.user.token.toString(), widget.qrCode, widget.fromPage),
      QualityControlFunction.fetchGetDefectLevelApi(widget.user.token.toString())
    ]);
    // final dataQuality = await QualityControlFunction.fetchQualityControl(widget.qualityControlId, widget.user.token.toString(), widget.qrCode, widget.fromPage);
    if (!mounted) return;
    setState(() {
      if (listData.isNotEmpty) {
        if (listData[1] != null) {
          _lsDataGetDefetchLevel = listData[1] as List<DataGetDefectLevel>;
        }
        if (listData[0] != null) {
          _qualityControlModel = listData[0] as QualityControlModel?;
          if (_qualityControlModel != null) {
            _qualityControl = _qualityControlModel!.qualityControl;

            // _qualityControlDetail = _qualityControl!.qualityControlDetail;

            // _qualityCheckerList = _qualityControlModel!.qualityCheckerList ?? [];
            // _qualityCheckerList.insert(
            //     0, (QualityControlFunction.defaultValueQC));

            _qualityTypeList = _qualityControlModel!.qualityTypeList ?? [];
            _qualityTypeList.insert(0, (QualityControlFunction.defaultValueQualityTypeList));

            _lsTestMethodList = _qualityControlModel!.testMethodList ?? [];
            _lsTestMethodList.insert(0, (QualityControlDetailFunction.defaultValueTestMethodList));

            _lsResultList = _qualityControlModel!.resultList ?? [];
            _lsResultList.insert(0, (QualityControlDetailFunction.defaultResultList));

            _lsSamplingLevelList = _qualityControlModel!.samplingLevelList ?? [];
            _lsSamplingLevelList.insert(0, (QualityControlDetailFunction.defaultValueSamplingLevelList));
            _lsQualityControlInformationIdList = _qualityControlModel!.qualityControlInformationIdList;
            if (_qualityControl != null) {
              if (_qualityControl!.qualityControlInformation!.isNotEmpty) {
                _lsQualityControlInformation = _qualityControl!.qualityControlInformation!;
              } else {
                _lsQualityControlInformation = [];
              }
              if (_qualityControl!.error!.isNotEmpty) {
                _lsError = _qualityControl!.error!;
              } else {
                _lsError = [];
              }
              if (_qualityControl!.qualityDate != null) {
                _date = DateFormat("yyyy-MM-ddThh:mm:ss").parse(_qualityControl!.qualityDate!);
                debugPrint(_date!.toIso8601String());
              } else {
                _date = DateFormat("yyyy-MM-dd HH:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now()));
                debugPrint(_date!.toIso8601String());
              }
              _lsErrorList = _qualityControlModel!.errorList;
              // _lsErrorListFiler = QualityControlFunction.filterQualityControlErrorList(_lsErrorList ?? [], "");
            }
          }
        } else {
          if (!mounted) return;
          setState(() {
            _isLoading = false;
          });
        }
      }
    });
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButtonTimeOut = true;
    });
  }

  void _deleteListImageTabCheck(int index) {
    if (!mounted) return;
    setState(() {
      _lsFileTabCheck.removeAt(index);
    });
  }

  void _pickFileImage(File file) {
    if (!mounted) return;
    setState(() {
      _lsFileTabCheck.add(file);
    });
  }

  void _pickFileImageInformation(MultiListImageFile multiListImageFile) {
    setState(() {
      _lsGetFileInfor[multiListImageFile.index].add(multiListImageFile.file);
    });
  }

  void _getSelectedResult(ResultList? value) {
    setState(() {
      _selectedResult = value;
      if (_selectedResult == null || _selectedResult!.catalogCode == " ") {
        _errorSelectedResultQualityView = true;
      } else {
        _errorSelectedResultQualityView = false;
      }
    });
  }

  void _getSelectedDefectLevel(DropdownDefetchLevel value) {
    setState(() {
      _lselectDataGetDefectLevel[value.index] = value.value;
    });
  }

  void _getSelectedType(QualityTypeList? value) {
    setState(() {
      _selectedType = value;
      if (_selectedType == null || _selectedType!.catalogCode == " ") {
        _errorSelectType = true;
      } else {
        _errorSelectType = false;
      }
    });
  }

  void _getSelectedResultDetail(ResultList? value) {
    setState(() {
      _selectedResultDetail = value;
      if (_selectedResultDetail == null || _selectedResultDetail!.catalogCode == " ") {
        _errorResultCheckDetail = true;
      } else {
        _errorResultCheckDetail = false;
      }
    });
    // print(_selectedResultDetail!.catalogTextVi.toString());
  }

  void _getSelectedLevel(SamplingLevelList? value) {
    if (value!.catalogCode != "OTHER") {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = false;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    } else {
      setState(() {
        _selectedLevel = value;
        _hideDetailLever = true;
        if (_selectedLevel == null || _selectedLevel!.catalogCode == " ") {
          _errorLevelDetail = true;
        } else {
          _errorLevelDetail = false;
        }
      });
    }
  }

  void _getSelectedMethod(TestMethodList? value) {
    setState(() {
      _selectedMethod = value;
      if (_selectedMethod == null || _selectedMethod!.catalogCode == " ") {
        _errorTestMethodDetail = true;
      } else {
        _errorTestMethodDetail = false;
      }
    });
  }

  void _addNewCard() {
    setState(() {
      _lsQualityControlInformation.add(QualityControlInfoFunction.defaultValueQualityControlInformationQC());
      _lsTextEditingController.add(TextEditingController());
      _lsControllerInformation.add(TextEditingController());
      // _focusInformation.add(FocusNode());
      _indexInfo = _lsQualityControlInformationIdList!.indexWhere((element) => element.id == " ");
      _lsGetIndexInfo.add(_indexInfo);
      _lsGetFileInfor.add([]);
      _lsSelectedInfo.add(null);
      _lsErrorInfor.add(false);
      _checkVisiButtonInformation.add(true);
    });
  }

  void _deleteListQualityInformation(MultiListImageDeleteFile multiListImageDeleteFile) {
    setState(() {
      _lsGetFileInfor[multiListImageDeleteFile.index].removeAt(multiListImageDeleteFile.indexImage);
    });
  }

  void _addNewCardError() {
    setState(() {
      _lsError.add(QualityControlErrorFunction.defaultListError);
      _lsTextEditingControllerError_1.add(TextEditingController());
      _lselectDataGetDefectLevel.add(null);
      _lsTextEditingControllerError_3.add(TextEditingController());
      _indexError = _lsErrorList!.indexWhere((element) => element.catalogCode == " ");
      _lsControllerError.add(TextEditingController());
      // _focusError.add(FocusNode());
      _lsGetIndexError.add(_indexError);
      _lsGetFileError.add([]);
      _lsSelectedError.add(null);
      _checkVisiButtonError.add(true);
    });
  }

  void _deleteListFileError(MultiDeleteImageErrorQuality multiDeleteImageErrorQuality) {
    setState(() {
      _lsGetFileError[multiDeleteImageErrorQuality.index].removeAt(multiDeleteImageErrorQuality.indexImageError);
    });
  }

  // void _getSelectedError(MultiSelectedErrorQuality multiSelectedErrorQuality) {
  //   setState(() {
  //     _lsSelectedError[multiSelectedErrorQuality.index] =
  //         multiSelectedErrorQuality.value!;
  //   });
  // }

  void _pickFileImageErrorQuality(MultiSelectImageErrorQuality multiSelectImageErrorQuality) {
    setState(() {
      _lsGetFileError[multiSelectImageErrorQuality.index].add(multiSelectImageErrorQuality.file);
    });
  }

  void _checkErrorQuantityView() {
    // setState(() {
    if (_controller_1.text.isNotEmpty) {
      if (_errorP0 != false) {
        setState(() {
          _errorP0 = false;
        });
      }
    } else {
      if (_errorP0 != true) {
        setState(() {
          _errorP0 = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckerQuantityView() {
    setState(() {
      if (_controller_2.text.isNotEmpty) {
        if (_errorQuantityCheck != false) {
          _errorQuantityCheck = false;
        }
      } else {
        if (_errorQuantityCheck != true) {
          _errorQuantityCheck = true;
        }
      }
    });
  }

  // void _checkErrorHideDetailLevel() {
  //   if (_controllerHideDetailLever.text.isNotEmpty) {
  //     if (_errorHideDetail != false) {
  //       setState(() {
  //         _errorHideDetail = false;
  //       });
  //     }
  //   } else {
  //     if (_errorHideDetail != true) {
  //       setState(() {
  //         _errorHideDetail = true;
  //       });
  //     }
  //   }
  // }

  void _checkErrorAcceptableLevelDetail() {
    // setState(() {
    if (_controller_3.text.isNotEmpty) {
      if (_errorAcceptableLevelDetail != false) {
        setState(() {
          _errorAcceptableLevelDetail = false;
        });
      }
    } else {
      if (_errorAcceptableLevelDetail != true) {
        setState(() {
          _errorAcceptableLevelDetail = true;
        });
      }
    }
    // });
  }

  void _checkErrorQuantityCheckDetail() {
    // setState(() {
    if (_controller_4.text.isNotEmpty) {
      if (_errorQuantityCheckDetail != false) {
        setState(() {
          _errorQuantityCheckDetail = false;
        });
      }
    } else {
      if (_errorQuantityCheckDetail != true) {
        setState(() {
          _errorQuantityCheckDetail = true;
        });
      }
    }
    // });
  }

  void _checkClearLsSelectedInfo(int index) {
    if (_lsSelectedInfo[index] != null) {
      setState(() {
        _lsSelectedInfo[index] = null;
      });
    }
  }

  void _setTypeAhead(TypeAheadErrorQuatity typeAheadErrorQuatity) {
    setState(() {
      _lsControllerError[typeAheadErrorQuatity.index ?? 0].text =
          typeAheadErrorQuatity.errorList == null ? "" : typeAheadErrorQuatity.errorList!.catalogTextVi ?? "";
      _lsSelectedError[typeAheadErrorQuatity.index ?? 0] = typeAheadErrorQuatity.errorList;
    });
  }

  void _checkErrorSelectedInfo(QuantityInformationSelected quantityInformationSelected) {
    setState(() {
      _lsControllerInformation[quantityInformationSelected.index ?? 0].text = quantityInformationSelected.qualityControlInformationIdList!.name ?? "";
      _lsSelectedInfo[quantityInformationSelected.index ?? 0] = quantityInformationSelected.qualityControlInformationIdList;
      if (_lsSelectedInfo[quantityInformationSelected.index ?? 0] != null) {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != false) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = false;
        }
      } else {
        if (_lsErrorInfor[quantityInformationSelected.index ?? 0] != true) {
          _lsErrorInfor[quantityInformationSelected.index ?? 0] = true;
        }
      }
    });
  }

  void _checkClearLsSelectedError(int index) {
    if (_lsSelectedError[index] != null) {
      setState(() {
        _lsSelectedError[index] = null;
      });
    }
  }

  // void _checkErrorSelectedInfo(int index) {
  //   if (_lsSelectedInfo[index] != null) {
  //     if (_lsErrorInfor[index] != false) {
  //       setState(() {
  //         _lsErrorInfor[index] = false;
  //       });
  //     }
  //   } else {
  //     if (_lsErrorInfor[index] != true) {
  //       setState(() {
  //         _lsErrorInfor[index] = true;
  //       });
  //     }
  //   }
  // }
  void _deleteItemListInformation(int index) {
    setState(() {
      _lsQualityControlInformation.removeAt(index);
      _lsTextEditingController.removeAt(index);
      _lsControllerInformation.removeAt(index);
      // _focusInformation.removeAt(index);
      _lsGetIndexInfo.removeAt(index);
      _lsGetFileInfor.removeAt(index);
      _lsSelectedInfo.removeAt(index);
      _lsErrorInfor.removeAt(index);
      _checkVisiButtonInformation.removeAt(index);
    });
  }

  void _deleteItemListError(int index) {
    setState(() {
      _lsError.removeAt(index);
      _lsTextEditingControllerError_1.removeAt(index);
      _lselectDataGetDefectLevel.removeAt(index);
      _lsTextEditingControllerError_3.removeAt(index);
      _lsControllerError.removeAt(index);
      // _focusError.removeAt(index);
      _lsGetIndexError.removeAt(index);
      _lsGetFileError.removeAt(index);
      _lsSelectedError.removeAt(index);
      _checkVisiButtonError.removeAt(index);
      _lsErrorList = _qualityControlModel!.errorList;
    });
  }

  @override
  void dispose() {
    _controller_1.dispose();
    _controller_2.dispose();
    _controller_3.dispose();
    _controller_4.dispose();
    _controllerHideDetailLever.dispose();
    for (var i in _lsControllerInformation) {
      i.dispose();
    }
    for (var i in _lsTextEditingController) {
      i.dispose();
    }
    // for(var i in _focusError){
    //   i.dispose();
    // }
    for (var i in _lsControllerError) {
      i.dispose();
    }
    // for (var i in _focusInformation) {
    //   i.dispose();
    // }
    for (var i in _lsTextEditingControllerError_1) {
      i.dispose();
    }
    // for (var i in _lsTextEditingControllerError_2) {
    //   i.dispose();
    // }
    for (var i in _lsTextEditingControllerError_3) {
      i.dispose();
    }
    debugPrint('dispose');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimeOut)))
        : DefaultTabController(
            initialIndex: 0,
            length: 4,
            child: WillPopScope(
              onWillPop: () async {
                Navigator.pop(context, false);
                return false;
              },
              child: _isLoading == true
                  ? Scaffold(
                      appBar: AppBar(
                          titleSpacing: 0,
                          automaticallyImplyLeading: false,
                          backgroundColor: const Color(0xff0052cc),
                          elevation: 0,
                          centerTitle: true,
                          leading: IconButton(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                            onPressed: () {
                              Navigator.pop(context, false);
                            },
                          ),
                          title: Text(
                            "Kiểm tra chất lượng",
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                          )),
                      body: const Center(child: CircularProgressIndicator()),
                    )
                  : _isError == true
                      ? Scaffold(
                          appBar: AppBar(
                              titleSpacing: 0,
                              automaticallyImplyLeading: false,
                              backgroundColor: const Color(0xff0052cc),
                              elevation: 0,
                              centerTitle: true,
                              leading: IconButton(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                                onPressed: () {
                                  Navigator.pop(context, false);
                                },
                              ),
                              title: Text(
                                "Kiểm tra chất lượng",
                                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                              )),
                          body: const _ErrorView(),
                        )
                      : _isNotWifi == true
                          ? Scaffold(
                              backgroundColor: Colors.white,
                              appBar: AppBar(
                                  titleSpacing: 0,
                                  automaticallyImplyLeading: false,
                                  backgroundColor: const Color(0xff0052cc),
                                  elevation: 0,
                                  centerTitle: true,
                                  leading: IconButton(
                                    splashColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                                    onPressed: () {
                                      Navigator.pop(context, false);
                                    },
                                  ),
                                  title: Text(
                                    "Kiểm tra chất lượng",
                                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                                  )),
                              body: LostConnect(checkConnect: () => _loadDataAndSetDefault()))
                          : _qualityControlModel == null || _qualityControl == null
                              ? Scaffold(
                                  backgroundColor: Colors.white,
                                  appBar: AppBar(
                                      titleSpacing: 0,
                                      automaticallyImplyLeading: false,
                                      backgroundColor: const Color(0xff0052cc),
                                      elevation: 0,
                                      centerTitle: true,
                                      leading: IconButton(
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                        icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                                        onPressed: () {
                                          Navigator.pop(context, false);
                                        },
                                      ),
                                      title: Text(
                                        "Kiểm tra chất lượng",
                                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                                      )),
                                  body: const _ListNotFoundView(),
                                )
                              : Scaffold(
                                  backgroundColor: Colors.white,
                                  appBar: AppBar(
                                      titleSpacing: 0,
                                      automaticallyImplyLeading: false,
                                      backgroundColor: const Color(0xff0052cc),
                                      elevation: 0,
                                      centerTitle: true,
                                      leading: IconButton(
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                        icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                                        onPressed: () {
                                          Navigator.pop(context, false);
                                        },
                                      ),
                                      title: const TitleQuality(title: "Kiểm tra chất lượng")),
                                  bottomNavigationBar: SafeArea(
                                    child: TabBar(
                                      onTap: (_) => FocusManager.instance.primaryFocus?.unfocus(),
                                      unselectedLabelColor: Colors.black,
                                      labelColor: const Color(0xff0052cc),
                                      labelStyle: TextStyle(fontSize: 13.sp),
                                      indicatorColor: const Color(0xff0052cc),
                                      tabs: const <Widget>[
                                        Tab(
                                          icon: Icon(Icons.edit_note_rounded),
                                          text: 'Phiếu KT',
                                        ),
                                        Tab(
                                          icon: Icon(Icons.details_rounded),
                                          text: 'Mẫu CT',
                                        ),
                                        Tab(
                                          icon: Icon(Icons.info_outline_rounded),
                                          text: "T.Tin KT",
                                        ),
                                        Tab(
                                          icon: Icon(Icons.error_outline_outlined),
                                          text: "T.Tin Lỗi",
                                        ),
                                      ],
                                    ),
                                  ),
                                  body: TabBarView(
                                    physics: const NeverScrollableScrollPhysics(),
                                    children: <Widget>[
                                      SingleChildScrollView(
                                        child: SafeArea(
                                          minimum: EdgeInsets.symmetric(horizontal: 10.w),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: <Widget>[
                                              SizedBox(height: 15.h),
                                              _qualityControl != null
                                                  ? _qualityControl!.qcType != "NVL"
                                                      ? InfoBTP(qualityControl: _qualityControl)
                                                      : InfoNVL(dataRawMaterial: _dataRawMaterial)
                                                  : InfoBTP(qualityControl: _qualityControl),
                                              Visibility(
                                                  visible: _qualityControl!.qcType != "NVL", child: const FieldQuantity(field: "Tình trạng MT:")),
                                              SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(flex: 3, child: FieldQuantity(field: "Ngày kiểm tra:")),
                                                  SizedBox(width: 10.w),
                                                  Expanded(
                                                    flex: 7,
                                                    child: GestureDetector(
                                                        onTap: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                            ? null
                                                            : () async {
                                                                // _pickDateIOS(context);
                                                                if (Platform.isAndroid) {
                                                                  final getDate = await QualityControlFunction.pickDate(context, _date);
                                                                  if (!mounted) return;
                                                                  _setDate(getDate);
                                                                } else {
                                                                  final getDateIOS = await QualityControlFunction.pickDateIOS(context);
                                                                  if (!mounted) return;
                                                                  _setDate(getDateIOS);
                                                                }
                                                              },
                                                        child: TitleDateTimeQuality(date: _getDate)),
                                                  )
                                                ],
                                              ),
                                              SizedBox(height: 15.h),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(flex: 3, child: FieldQuantity(field: "NV kiểm tra")),
                                                  SizedBox(width: 10.w),
                                                  Expanded(flex: 7, child: TitleStaff(selectedStaff: _selectedStaff))
                                                ],
                                              ),
                                              SizedBox(height: 15.h),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(flex: 3, child: FieldQuantity(field: "Loại kiểm tra")),
                                                  SizedBox(width: 10.w),
                                                  Expanded(
                                                    flex: 7,
                                                    child: Column(children: <Widget>[
                                                      Container(
                                                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                          decoration: BoxDecoration(
                                                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                            borderRadius: BorderRadius.circular(3.r),
                                                          ),
                                                          child: DropdownType(
                                                              qualityControl: _qualityControl,
                                                              onChangeType: _getSelectedType,
                                                              qualityTypeList: _qualityTypeList,
                                                              selectedType: _selectedType)),
                                                      SizedBox(height: _errorSelectType == true ? 10.h : 0),
                                                      QualityErrorValidate(error: _errorSelectType, text: "Bạn chưa chọn loại kiểm tra")
                                                    ]),
                                                  )
                                                ],
                                              ),
                                              SizedBox(height: 15.h),
                                              Visibility(
                                                  visible: _qualityControl!.qcType != "NVL",
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: <Widget>[
                                                      const Expanded(
                                                        flex: 3,
                                                        child: QualityTitleField(title: "PO"),
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Expanded(
                                                        flex: 7,
                                                        child: Align(
                                                          alignment: Alignment.centerLeft,
                                                          child: Column(children: <Widget>[
                                                            Container(
                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                                              decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                  borderRadius: BorderRadius.circular(3.r)),
                                                              child: TextFormField(
                                                                maxLines: null,
                                                                keyboardType: TextInputType.number,
                                                                inputFormatters: <TextInputFormatter>[
                                                                  FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                                ],
                                                                textAlign: TextAlign.center,
                                                                controller: _controller_1,
                                                                style: TextStyle(fontSize: 12.sp),
                                                                decoration: InputDecoration(
                                                                  border: InputBorder.none,
                                                                  isDense: true,
                                                                  contentPadding: EdgeInsets.zero,
                                                                  errorBorder: InputBorder.none,
                                                                  disabledBorder: InputBorder.none,
                                                                  filled: true,
                                                                  fillColor: Colors.white,
                                                                  hintStyle: TextStyle(fontSize: 12.sp),
                                                                ),
                                                                onChanged: (value) {
                                                                  _checkErrorQuantityView();
                                                                },
                                                              ),
                                                            ),
                                                            SizedBox(height: _errorP0 == true ? 10.h : 0),
                                                            QualityErrorValidate(error: _errorP0, text: "Vui lòng nhập PO"),
                                                          ]),
                                                        ),
                                                      )
                                                    ],
                                                  )),
                                              SizedBox(height: _qualityControl!.qcType != "NVL" ? 15.h : 0),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(
                                                    flex: 3,
                                                    child: Align(
                                                      alignment: Alignment.centerLeft,
                                                      child: QualityTitleField(title: "SL kiểm tra"),
                                                    ),
                                                  ),
                                                  SizedBox(width: 10.w),
                                                  Expanded(
                                                    flex: 7,
                                                    child: Align(
                                                      alignment: Alignment.centerLeft,
                                                      child: Column(children: <Widget>[
                                                        Container(
                                                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                                          decoration: BoxDecoration(
                                                              border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                              borderRadius: BorderRadius.circular(3.r)),
                                                          child: TextFormField(
                                                            enabled: _qualityControl!.qcType == "NVL"
                                                                ? _qualityControl!.qualityChecker != null
                                                                    ? false
                                                                    : true
                                                                : true,
                                                            maxLines: null,
                                                            textAlign: TextAlign.center,
                                                            controller: _controller_2,
                                                            style: TextStyle(fontSize: 12.sp),
                                                            keyboardType: TextInputType.number,
                                                            inputFormatters: <TextInputFormatter>[
                                                              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                            ],
                                                            decoration: InputDecoration(
                                                              border: InputBorder.none,
                                                              isDense: true,
                                                              contentPadding: EdgeInsets.zero,
                                                              errorBorder: InputBorder.none,
                                                              disabledBorder: InputBorder.none,
                                                              filled: true,
                                                              fillColor: Colors.white,
                                                              hintStyle: TextStyle(fontSize: 12.sp),
                                                            ),
                                                            onChanged: (value) {
                                                              _checkErrorQuantityCheckerQuantityView();
                                                            },
                                                          ),
                                                        ),
                                                        Visibility(
                                                          visible: _errorQuantityCheck,
                                                          child: SizedBox(height: 10.h),
                                                        ),
                                                        QualityErrorValidate(error: _errorQuantityCheck, text: "Vui lòng nhập SL kiểm tra"),
                                                      ]),
                                                    ),
                                                  )
                                                ],
                                              ),
                                              SizedBox(height: 15.h),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(
                                                    flex: 3,
                                                    child: Align(alignment: Alignment.centerLeft, child: QualityTitleField(title: "Hình ảnh")),
                                                  ),
                                                  SizedBox(width: 10.w),
                                                  Expanded(
                                                      flex: 7,
                                                      child: _lsFileTabCheck.isEmpty
                                                          ? Row(
                                                              children: <Widget>[
                                                                Container(
                                                                  padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                                                  decoration: BoxDecoration(
                                                                    borderRadius: BorderRadius.circular(10.r),
                                                                    color: Colors.grey.shade100,
                                                                  ),
                                                                  child: InkWell(
                                                                    onTap: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                                        ? null
                                                                        : () async {
                                                                            final check = await QualityControlFunction.pickImage(context);
                                                                            debugPrint(check.toString());
                                                                            if (check != null) {
                                                                              bool checkPermission = await ImageFunction.handlePermission(check);
                                                                              if (checkPermission == true) {
                                                                                if (check == true) {
                                                                                  List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                                                    maxWidth: globalImageConfig.maxWidth,
                                                                                    maxHeight: globalImageConfig.maxHeight,
                                                                                    imageQuality: globalImageConfig.imageQuality,
                                                                                  );
                                                                                  if (selectedImages.isEmpty) return;
                                                                                  for (var i in selectedImages) {
                                                                                    final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                                                    if (!mounted) return;
                                                                                    _pickFileImage(itemImage);
                                                                                  }
                                                                                } else {
                                                                                  final image = await ImagePicker().pickImage(
                                                                                      maxWidth: globalImageConfig.maxWidth,
                                                                                      maxHeight: globalImageConfig.maxHeight,
                                                                                      imageQuality: globalImageConfig.imageQuality,
                                                                                      source: ImageSource.camera);
                                                                                  if (image == null) return;
                                                                                  final imageProfile = await ImageFunction.saveImage(image.path);
                                                                                  if (!mounted) return;
                                                                                  _pickFileImage(imageProfile);
                                                                                }
                                                                              }
                                                                            }
                                                                          },
                                                                    child: Text(
                                                                      "Chọn tệp",
                                                                      style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                                                    ),
                                                                  ),
                                                                ),
                                                                SizedBox(width: 10.w),
                                                                Center(
                                                                  child: Text(
                                                                    "Chưa chọn tệp nào",
                                                                    style: TextStyle(fontSize: 11.sp),
                                                                  ),
                                                                ),
                                                              ],
                                                            )
                                                          : Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                                                              Container(
                                                                padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                                                decoration: BoxDecoration(
                                                                  borderRadius: BorderRadius.circular(10.r),
                                                                  color: Colors.grey.shade100,
                                                                ),
                                                                child: InkWell(
                                                                  onTap: () async {
                                                                    // debugPrint('yes');
                                                                    final check = await QualityControlFunction.pickImage(context);
                                                                    debugPrint(check.toString());
                                                                    if (check != null) {
                                                                      bool checkPermission = await ImageFunction.handlePermission(check);
                                                                      if (checkPermission == true) {
                                                                        if (check == true) {
                                                                          List<XFile>? selectedImages = await _pickerImage.pickMultiImage(
                                                                            maxWidth: globalImageConfig.maxWidth,
                                                                            maxHeight: globalImageConfig.maxHeight,
                                                                            imageQuality: globalImageConfig.imageQuality,
                                                                          );
                                                                          if (selectedImages.isEmpty) return;
                                                                          for (var i in selectedImages) {
                                                                            final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                                            _pickFileImage(itemImage);
                                                                          }
                                                                        } else {
                                                                          final image = await ImagePicker().pickImage(
                                                                              maxWidth: globalImageConfig.maxWidth,
                                                                              maxHeight: globalImageConfig.maxHeight,
                                                                              imageQuality: globalImageConfig.imageQuality,
                                                                              source: ImageSource.camera);
                                                                          if (image == null) return;
                                                                          final imageProfile = await ImageFunction.saveImage(image.path);
                                                                          _pickFileImage(imageProfile);
                                                                        }
                                                                      }
                                                                    }
                                                                  },
                                                                  child: Text(
                                                                    "Chọn tệp",
                                                                    style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(height: 10.h),
                                                              ListChooseImage(
                                                                  lsFileTabCheck: _lsFileTabCheck, deleteListImageTabCheck: _deleteListImageTabCheck),
                                                              SizedBox(height: 10.h),
                                                            ])),
                                                ],
                                              ),
                                              SizedBox(height: 15.h),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const Expanded(flex: 3, child: QualityTitleField(title: "Kết quả")),
                                                  SizedBox(width: 10.w),
                                                  Expanded(
                                                    flex: 7,
                                                    child: Column(children: <Widget>[
                                                      Container(
                                                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                          decoration: BoxDecoration(
                                                            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                                            borderRadius: BorderRadius.circular(3.r),
                                                          ),
                                                          child: DropdownQuantityResult(
                                                              selectedResult: _selectedResult,
                                                              lsResultList: _lsResultList,
                                                              qualityControl: _qualityControl,
                                                              onChangeResult: _getSelectedResult)),
                                                      SizedBox(height: _errorSelectedResultQualityView == true ? 10.h : 0),
                                                      QualityErrorValidate(error: _errorSelectedResultQualityView, text: "Vui lòng chọn kết quả"),
                                                    ]),
                                                  )
                                                ],
                                              ),
                                              Visibility(
                                                visible:
                                                    _qualityControl!.fileViewModel == null || _qualityControl!.fileViewModel!.isEmpty ? false : true,
                                                child: Align(
                                                  alignment: Alignment.centerRight,
                                                  child: ElevatedButton.icon(
                                                    icon: Icon(
                                                      Icons.image,
                                                      color: Colors.white,
                                                      size: 15.sp,
                                                    ),
                                                    style: ButtonStyle(
                                                      side: MaterialStateProperty.all(
                                                        const BorderSide(
                                                          color: Color(0xff0052cc),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                    ),
                                                    onPressed: () {
                                                      showDialog(
                                                        context: context,
                                                        builder: (BuildContext context) {
                                                          return DialogImage(title: 'HÌNH ẢNH KIỂM TRA', listImage: _qualityControl!.fileViewModel);
                                                        },
                                                      );
                                                    },
                                                    label: Text(
                                                      'Hình ảnh',
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontWeight: FontWeight.bold,
                                                        fontSize: 11.sp,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(height: 15.h),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SingleChildScrollView(
                                        child: SafeArea(
                                          minimum: EdgeInsets.symmetric(horizontal: 5.w),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: <Widget>[
                                              SizedBox(height: 15.h),
                                              Container(
                                                decoration: BoxDecoration(
                                                  border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: <Widget>[
                                                    Container(
                                                      width: double.infinity,
                                                      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.h),
                                                      decoration: const BoxDecoration(
                                                        color: Color(0xff0052cc),
                                                      ),
                                                      child: Text(
                                                        "Mẫu thử chi tiết",
                                                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
                                                      ),
                                                    ),
                                                    SizedBox(height: 15.h),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: <Widget>[
                                                          const Expanded(flex: 3, child: QualityTitleField(title: "Phương pháp KT")),
                                                          SizedBox(width: 10.w),
                                                          Expanded(
                                                            flex: 7,
                                                            child: Column(children: <Widget>[
                                                              Container(
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                                decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                  borderRadius: BorderRadius.circular(3.r),
                                                                ),
                                                                child: DropdownButtonHideUnderline(
                                                                  child: DropdownButton<TestMethodList>(
                                                                    isExpanded: true,
                                                                    isDense: true,
                                                                    itemHeight: null,
                                                                    value: _selectedMethod ?? QualityControlDetailFunction.defaultValueTestMethodList,
                                                                    iconSize: 15.sp,
                                                                    style: const TextStyle(color: Colors.white),
                                                                    onChanged:
                                                                        _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                                            ? null
                                                                            : _getSelectedMethod,
                                                                    items: _lsTestMethodList.map((TestMethodList method) {
                                                                      return DropdownMenuItem<TestMethodList>(
                                                                          value: method,
                                                                          child: Padding(
                                                                            padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                            child: Text(
                                                                              method.catalogTextVi.toString(),
                                                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                            ),
                                                                          ));
                                                                    }).toList(),
                                                                    selectedItemBuilder: (BuildContext context) {
                                                                      return _lsTestMethodList.map<Widget>((TestMethodList method) {
                                                                        return Text(method.catalogTextVi.toString(),
                                                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                            overflow: TextOverflow.ellipsis);
                                                                      }).toList();
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(height: _errorTestMethodDetail == true ? 10.h : 0),
                                                              QualityErrorValidate(
                                                                  text: "Vui lòng chọn phương pháp KT", error: _errorTestMethodDetail)
                                                            ]),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(height: 5.h),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: <Widget>[
                                                          const Expanded(flex: 3, child: QualityTitleField(title: "Mức độ")),
                                                          SizedBox(width: 10.w),
                                                          Expanded(
                                                            flex: 7,
                                                            child: Column(children: <Widget>[
                                                              Container(
                                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                                decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                  borderRadius: BorderRadius.circular(3.r),
                                                                ),
                                                                child: DropdownButtonHideUnderline(
                                                                  child: DropdownButton<SamplingLevelList>(
                                                                    isExpanded: true,
                                                                    isDense: true,
                                                                    itemHeight: null,
                                                                    value:
                                                                        _selectedLevel ?? QualityControlDetailFunction.defaultValueSamplingLevelList,
                                                                    iconSize: 15.sp,
                                                                    style: const TextStyle(color: Colors.white),
                                                                    onChanged:
                                                                        _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                                            ? null
                                                                            : _getSelectedLevel,
                                                                    items: _lsSamplingLevelList.map((SamplingLevelList level) {
                                                                      return DropdownMenuItem<SamplingLevelList>(
                                                                          value: level,
                                                                          child: Padding(
                                                                            padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                            child: Text(
                                                                              level.catalogTextVi.toString(),
                                                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                            ),
                                                                          ));
                                                                    }).toList(),
                                                                    selectedItemBuilder: (BuildContext context) {
                                                                      return _lsSamplingLevelList.map<Widget>((SamplingLevelList level) {
                                                                        return Text(level.catalogTextVi.toString(),
                                                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                                            overflow: TextOverflow.ellipsis);
                                                                      }).toList();
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                              Visibility(
                                                                visible: _errorLevelDetail,
                                                                child: SizedBox(height: 10.h),
                                                              ),
                                                              QualityErrorValidate(text: "Vui lòng chọn mức độ", error: _errorLevelDetail)
                                                            ]),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(height: 5.h),
                                                    Visibility(
                                                      visible: _hideDetailLever,
                                                      child: Padding(
                                                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.start,
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: <Widget>[
                                                            const Expanded(
                                                              flex: 3,
                                                              child: QualityTitleField(title: "Nội dung chi tiết"),
                                                            ),
                                                            SizedBox(width: 10.w),
                                                            Expanded(
                                                              flex: 7,
                                                              child: Container(
                                                                padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                                                decoration: BoxDecoration(
                                                                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                    borderRadius: BorderRadius.circular(3.r)),
                                                                child: TextFormField(
                                                                  enabled: _qualityControl!.qcType == "NVL"
                                                                      ? _qualityControl!.qualityChecker != null
                                                                          ? false
                                                                          : true
                                                                      : true,
                                                                  maxLines: null,
                                                                  textAlign: TextAlign.center,
                                                                  controller: _controllerHideDetailLever,
                                                                  style: TextStyle(fontSize: 12.sp),
                                                                  decoration: InputDecoration(
                                                                    border: InputBorder.none,
                                                                    isDense: true,
                                                                    contentPadding: EdgeInsets.zero,
                                                                    errorBorder: InputBorder.none,
                                                                    disabledBorder: InputBorder.none,
                                                                    filled: true,
                                                                    fillColor: Colors.white,
                                                                    hintStyle: TextStyle(fontSize: 12.sp),
                                                                  ),
                                                                  // onChanged: (value){
                                                                  //   // resetState();
                                                                  // },
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    Visibility(
                                                      visible: _hideDetailLever,
                                                      child: SizedBox(height: 5.h),
                                                    ),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: <Widget>[
                                                          const Expanded(flex: 3, child: QualityTitleField(title: "Mức chấp nhận")),
                                                          SizedBox(width: 10.w),
                                                          Expanded(
                                                            flex: 7,
                                                            child: Column(children: <Widget>[
                                                              Container(
                                                                padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                                                decoration: BoxDecoration(
                                                                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                    borderRadius: BorderRadius.circular(3.r)),
                                                                child: TextFormField(
                                                                  enabled: _qualityControl!.qcType == "NVL"
                                                                      ? _qualityControl!.qualityChecker != null
                                                                          ? false
                                                                          : true
                                                                      : true,
                                                                  maxLines: null,
                                                                  textAlign: TextAlign.center,
                                                                  controller: _controller_3,
                                                                  style: TextStyle(fontSize: 12.sp),
                                                                  decoration: InputDecoration(
                                                                    border: InputBorder.none,
                                                                    isDense: true,
                                                                    contentPadding: EdgeInsets.zero,
                                                                    errorBorder: InputBorder.none,
                                                                    disabledBorder: InputBorder.none,
                                                                    filled: true,
                                                                    fillColor: Colors.white,
                                                                    hintStyle: TextStyle(fontSize: 12.sp),
                                                                  ),
                                                                  onChanged: (value) {
                                                                    _checkErrorAcceptableLevelDetail();
                                                                  },
                                                                ),
                                                              ),
                                                              Visibility(
                                                                visible: _errorAcceptableLevelDetail,
                                                                child: SizedBox(height: 10.h),
                                                              ),
                                                              QualityErrorValidate(
                                                                  text: "Vui lòng nhập mức chấp nhận", error: _errorAcceptableLevelDetail)
                                                            ]),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(height: 5.h),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: <Widget>[
                                                          const Expanded(flex: 3, child: QualityTitleField(title: "SL kiểm tra")),
                                                          SizedBox(width: 10.w),
                                                          Expanded(
                                                            flex: 7,
                                                            child: Column(children: <Widget>[
                                                              Container(
                                                                padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                                                decoration: BoxDecoration(
                                                                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                                                    borderRadius: BorderRadius.circular(3.r)),
                                                                child: TextFormField(
                                                                  enabled: _qualityControl!.qcType == "NVL"
                                                                      ? _qualityControl!.qualityChecker != null
                                                                          ? false
                                                                          : true
                                                                      : true,
                                                                  maxLines: null,
                                                                  keyboardType: TextInputType.number,
                                                                  inputFormatters: <TextInputFormatter>[
                                                                    FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                                  ],
                                                                  textAlign: TextAlign.center,
                                                                  controller: _controller_4,
                                                                  style: TextStyle(fontSize: 12.sp),
                                                                  decoration: InputDecoration(
                                                                    border: InputBorder.none,
                                                                    isDense: true,
                                                                    contentPadding: EdgeInsets.zero,
                                                                    errorBorder: InputBorder.none,
                                                                    disabledBorder: InputBorder.none,
                                                                    filled: true,
                                                                    fillColor: Colors.white,
                                                                    hintStyle: TextStyle(fontSize: 12.sp),
                                                                  ),
                                                                  onChanged: (value) {
                                                                    _checkErrorQuantityCheckDetail();
                                                                  },
                                                                ),
                                                              ),
                                                              Visibility(visible: _errorQuantityCheckDetail, child: SizedBox(height: 10.h)),
                                                              QualityErrorValidate(
                                                                  text: "Vui lòng nhập SL kiểm tra", error: _errorQuantityCheckDetail)
                                                            ]),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(height: 3.h),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Align(
                                                        alignment: Alignment.centerRight,
                                                        child: ElevatedButton(
                                                          style: ElevatedButton.styleFrom(
                                                            padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w),
                                                            backgroundColor: _qualityControl!.qcType == "NVL"
                                                                ? _qualityControl!.qualityChecker != null
                                                                    ? Colors.grey.shade400
                                                                    : const Color(0xff0052cc)
                                                                : const Color(0xff0052cc),
                                                          ),
                                                          onPressed: _controller_2.text.isEmpty ||
                                                                  (_selectedLevel == null || _selectedLevel!.catalogCode == " ") ||
                                                                  _loadingGetQuanititySample == true
                                                              ? null
                                                              : _qualityControl!.qcType == "NVL"
                                                                  ? _qualityControl!.qualityChecker != null
                                                                      ? null
                                                                      : () {
                                                                          _getGetQuanititySample(context);
                                                                        }
                                                                  : () => _getGetQuanititySample(context),
                                                          child: Text(_loadingGetQuanititySample == true ? "Loading..." : 'Lấy SL kiểm tra',
                                                              style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold)),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(height: 3.h),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: <Widget>[
                                                          const Expanded(flex: 3, child: QualityTitleField(title: "Kết quả")),
                                                          SizedBox(width: 10.w),
                                                          Expanded(
                                                            flex: 7,
                                                            child: Column(children: <Widget>[
                                                              DropdownDetailResult(
                                                                  getSelectedResultDetail: _getSelectedResultDetail,
                                                                  selectedResultDetail: _selectedResultDetail,
                                                                  lsResultList: _lsResultList,
                                                                  qualityControl: _qualityControl),
                                                              SizedBox(height: _errorResultCheckDetail == true ? 10.h : 0),
                                                              QualityErrorValidate(text: "Vui lòng chọn kết quả", error: _errorResultCheckDetail)
                                                            ]),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(height: 15.h),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: 15.h),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SingleChildScrollView(
                                        child: SafeArea(
                                          minimum: EdgeInsets.symmetric(horizontal: 5.w),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: <Widget>[
                                              SizedBox(height: 15.h),
                                              Container(
                                                decoration: BoxDecoration(
                                                  border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                                                ),
                                                child: Column(
                                                  children: <Widget>[
                                                    const QualityTitle(title: "Thông tin kiểm tra"), // Title
                                                    SizedBox(height: 10.h),
                                                    Column(
                                                        children: List.generate(
                                                            _lsQualityControlInformation.length,
                                                            (index) => Container(
                                                                  margin: EdgeInsets.symmetric(vertical: 5.h),
                                                                  child: Padding(
                                                                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                    child: Column(
                                                                      children: [
                                                                        itemHeader(),
                                                                        // ItemThongTinKiemTra(index, context),
                                                                        ItemThongTinKiemTra(
                                                                            index: index,
                                                                            qualityControl: _qualityControl,
                                                                            lsGetFileInfor: _lsGetFileInfor,
                                                                            lsQualityControlInformation: _lsQualityControlInformation,
                                                                            lsQualityControlInformationIdList: _lsQualityControlInformationIdList,
                                                                            checkVisiButtonInformation: _checkVisiButtonInformation,
                                                                            pickFileImageInformation: _pickFileImageInformation,
                                                                            deleteListQualityInformation: _deleteListQualityInformation,
                                                                            deleteItemListInformation: _deleteItemListInformation,
                                                                            lsControllerInformation: _lsControllerInformation,
                                                                            lsTextEditingController: _lsTextEditingController,
                                                                            checkClearLsSelectedInfo: _checkClearLsSelectedInfo,
                                                                            pickerImage: _pickerImage,
                                                                            lsErrorInfor: _lsErrorInfor,
                                                                            checkErrorSelectedInfo: _checkErrorSelectedInfo)
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ))),
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                      child: IntrinsicHeight(
                                                        child: Row(
                                                          crossAxisAlignment: CrossAxisAlignment.stretch,
                                                          children: <Widget>[
                                                            Expanded(
                                                              flex: 6,
                                                              child: Container(
                                                                decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                ),
                                                                child: const Text(""),
                                                              ),
                                                            ),
                                                            Expanded(
                                                              flex: 4,
                                                              child: Container(
                                                                padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                height: 40.h,
                                                                decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                ),
                                                                child: Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                                                                  child: ElevatedButton.icon(
                                                                    icon: Icon(
                                                                      Icons.add,
                                                                      color: Colors.white,
                                                                      size: 15.sp,
                                                                    ),
                                                                    style: ButtonStyle(
                                                                      padding: MaterialStateProperty.all<EdgeInsets>(
                                                                          EdgeInsets.symmetric(vertical: 0.h, horizontal: 15.w)),
                                                                      side: MaterialStateProperty.all(
                                                                        const BorderSide(
                                                                          color: Colors.green,
                                                                        ),
                                                                      ),
                                                                      backgroundColor: MaterialStateProperty.all(Colors.green),
                                                                    ),
                                                                    onPressed:
                                                                        _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                                            ? null
                                                                            : () {
                                                                                _addNewCard();
                                                                              },
                                                                    label: Text(
                                                                      'Thêm',
                                                                      style: TextStyle(
                                                                        color: Colors.white,
                                                                        fontWeight: FontWeight.bold,
                                                                        fontSize: 11.sp,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(height: 15.h),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: 15.h),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SingleChildScrollView(
                                        child: SafeArea(
                                          minimum: EdgeInsets.symmetric(horizontal: 5.w),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: <Widget>[
                                              SizedBox(height: 15.h),
                                              Container(
                                                decoration: BoxDecoration(
                                                  border: Border.all(width: 0.5, color: const Color(0xff0052cc)),
                                                ),
                                                child: Column(
                                                  children: <Widget>[
                                                    const QualityTitle(title: 'Thông tin lỗi'),
                                                    SizedBox(height: 10.h),
                                                    Column(
                                                        children: List.generate(
                                                            _lsError.length,
                                                            (index) => Container(
                                                                  margin: EdgeInsets.symmetric(vertical: 5.h),
                                                                  child: Padding(
                                                                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                                    child: IntrinsicHeight(
                                                                      child: Row(
                                                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                        children: <Widget>[
                                                                          Expanded(
                                                                            flex: 1,
                                                                            child: Container(
                                                                              decoration: BoxDecoration(
                                                                                border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
                                                                              ),
                                                                              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                                                                              child: Center(
                                                                                child: Text(
                                                                                  (index + 1).toString(),
                                                                                  style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          Expanded(
                                                                            flex: 9,
                                                                            child: IntrinsicHeight(
                                                                              child: Column(
                                                                                mainAxisSize: MainAxisSize.min,
                                                                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                children: <Widget>[
                                                                                  IntrinsicHeight(
                                                                                    child: Row(
                                                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                      children: <Widget>[
                                                                                        Expanded(
                                                                                          flex: 5,
                                                                                          child: Container(
                                                                                            padding:
                                                                                                EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                                            decoration: BoxDecoration(
                                                                                              border: Border.all(
                                                                                                width: 0.5,
                                                                                                color: Colors.grey.shade300,
                                                                                              ),
                                                                                            ),
                                                                                            child: Text(
                                                                                              "Danh sách lỗi",
                                                                                              style: TextStyle(
                                                                                                  fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        Expanded(
                                                                                          flex: 4,
                                                                                          child: Container(
                                                                                              padding: EdgeInsets.symmetric(
                                                                                                  vertical: 5.h, horizontal: 5.w),
                                                                                              decoration: BoxDecoration(
                                                                                                border: Border.all(
                                                                                                    width: 0.5, color: Colors.grey.shade300),
                                                                                              ),
                                                                                              child: Text(
                                                                                                "Số lượng lỗi",
                                                                                                style: TextStyle(
                                                                                                    fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                                              )),
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                  IntrinsicHeight(
                                                                                    child: Row(
                                                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                      children: <Widget>[
                                                                                        Expanded(
                                                                                          flex: 5,
                                                                                          child: Container(
                                                                                              decoration: BoxDecoration(
                                                                                                border: Border.all(
                                                                                                  width: 0.5,
                                                                                                  color: Colors.grey.shade300,
                                                                                                ),
                                                                                              ),
                                                                                              child: Container(
                                                                                                child: Padding(
                                                                                                  padding: REdgeInsets.all(5),
                                                                                                  child: Center(
                                                                                                    child: Container(
                                                                                                      decoration: BoxDecoration(
                                                                                                        border: Border.all(
                                                                                                          width: 0.5.w,
                                                                                                          color: Colors.grey.shade400,
                                                                                                        ),
                                                                                                      ),
                                                                                                      child: TypeAheadField(
                                                                                                        suggestionsBoxDecoration:
                                                                                                            SuggestionsBoxDecoration(
                                                                                                          constraints: BoxConstraints(
                                                                                                            minWidth: 150.w,
                                                                                                          ),
                                                                                                        ),
                                                                                                        textFieldConfiguration:
                                                                                                            TextFieldConfiguration(
                                                                                                                enabled: _qualityControl!.qcType ==
                                                                                                                            "NVL" &&
                                                                                                                        _qualityControl!
                                                                                                                                .qualityChecker !=
                                                                                                                            null
                                                                                                                    ? false
                                                                                                                    : true,
                                                                                                                decoration: InputDecoration(
                                                                                                                  labelStyle:
                                                                                                                      TextStyle(fontSize: 11.sp),
                                                                                                                  contentPadding:
                                                                                                                      EdgeInsets.symmetric(
                                                                                                                          vertical: 5.h,
                                                                                                                          horizontal: 5.w),
                                                                                                                  isDense: true,
                                                                                                                  border: InputBorder.none,
                                                                                                                  focusedBorder: InputBorder.none,
                                                                                                                  enabledBorder: InputBorder.none,
                                                                                                                ),
                                                                                                                controller: _lsControllerError[index],
                                                                                                                // focusNode: focusError[index],
                                                                                                                style: TextStyle(fontSize: 12.sp),
                                                                                                                onChanged: (value) {
                                                                                                                  _checkClearLsSelectedError(index);
                                                                                                                }),
                                                                                                        suggestionsCallback: (pattern) {
                                                                                                          return QualityControlFunction
                                                                                                              .filterQualityControlErrorList(
                                                                                                                  _lsErrorList ?? [], pattern);
                                                                                                        },
                                                                                                        itemBuilder: (context, suggestion) {
                                                                                                          return ListTile(
                                                                                                            title: Text(
                                                                                                                (suggestion as ErrorList)
                                                                                                                        .catalogTextVi ??
                                                                                                                    " ",
                                                                                                                style: TextStyle(fontSize: 12.sp)),
                                                                                                          );
                                                                                                        },
                                                                                                        onSuggestionSelected: (suggestion) {
                                                                                                          _setTypeAhead(TypeAheadErrorQuatity(
                                                                                                              index: index, errorList: suggestion));
                                                                                                        },
                                                                                                        noItemsFoundBuilder: (value) {
                                                                                                          return Padding(
                                                                                                              padding: EdgeInsets.symmetric(
                                                                                                                  vertical: 10.h, horizontal: 5.w),
                                                                                                              child: Text("Không tìm thấy kết quả",
                                                                                                                  style: TextStyle(fontSize: 11.sp)));
                                                                                                        },
                                                                                                      ),
                                                                                                    ),
                                                                                                  ),
                                                                                                ),
                                                                                              )),
                                                                                        ),
                                                                                        Expanded(
                                                                                          flex: 4,
                                                                                          child: Container(
                                                                                            decoration: BoxDecoration(
                                                                                              border:
                                                                                                  Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                                            ),
                                                                                            child: Padding(
                                                                                              padding: REdgeInsets.all(5),
                                                                                              child: Container(
                                                                                                padding: EdgeInsets.symmetric(
                                                                                                    horizontal: 3.w, vertical: 5.h),
                                                                                                decoration: BoxDecoration(
                                                                                                  border: Border.all(
                                                                                                      width: 0.5.w, color: Colors.grey.shade400),
                                                                                                ),
                                                                                                child: TextFormField(
                                                                                                  enabled: _qualityControl!.qcType == "NVL"
                                                                                                      ? _qualityControl!.qualityChecker != null
                                                                                                          ? false
                                                                                                          : true
                                                                                                      : true,
                                                                                                  textAlign: TextAlign.center,
                                                                                                  keyboardType: TextInputType.number,
                                                                                                  inputFormatters: <TextInputFormatter>[
                                                                                                    FilteringTextInputFormatter.allow(
                                                                                                        RegExp("[0-9]")),
                                                                                                  ],
                                                                                                  controller: _lsTextEditingControllerError_1[index],
                                                                                                  style: TextStyle(fontSize: 12.sp),
                                                                                                  decoration: InputDecoration(
                                                                                                    border: InputBorder.none,
                                                                                                    focusedBorder: InputBorder.none,
                                                                                                    enabledBorder: InputBorder.none,
                                                                                                    errorBorder: InputBorder.none,
                                                                                                    disabledBorder: InputBorder.none,
                                                                                                    filled: true,
                                                                                                    isDense: true,
                                                                                                    fillColor: Colors.white,
                                                                                                    hintStyle: TextStyle(fontSize: 12.sp),
                                                                                                    contentPadding: EdgeInsets.zero,
                                                                                                  ),
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                        )
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                  IntrinsicHeight(
                                                                                    child: Row(
                                                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                      children: <Widget>[
                                                                                        Expanded(
                                                                                          flex: 5,
                                                                                          child: Container(
                                                                                            padding:
                                                                                                EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                                            decoration: BoxDecoration(
                                                                                              border: Border.all(
                                                                                                width: 0.5,
                                                                                                color: Colors.grey.shade300,
                                                                                              ),
                                                                                            ),
                                                                                            child: Text(
                                                                                              "Mức độ lỗi",
                                                                                              style: TextStyle(
                                                                                                  fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        Expanded(
                                                                                          flex: 4,
                                                                                          child: Container(
                                                                                              padding: EdgeInsets.symmetric(
                                                                                                  vertical: 5.h, horizontal: 5.w),
                                                                                              decoration: BoxDecoration(
                                                                                                border: Border.all(
                                                                                                    width: 0.5, color: Colors.grey.shade300),
                                                                                              ),
                                                                                              child: Text(
                                                                                                "Ghi chú",
                                                                                                style: TextStyle(
                                                                                                    fontSize: 11.sp, fontWeight: FontWeight.bold),
                                                                                              )),
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                  IntrinsicHeight(
                                                                                    child: Row(
                                                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                      children: <Widget>[
                                                                                        Expanded(
                                                                                          flex: 5,
                                                                                          child: Container(
                                                                                            decoration: BoxDecoration(
                                                                                              border:
                                                                                                  Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                                            ),
                                                                                            child: Column(
                                                                                              children: <Widget>[
                                                                                                Padding(
                                                                                                  padding: REdgeInsets.all(5),
                                                                                                  child: Container(
                                                                                                    padding: EdgeInsets.symmetric(
                                                                                                        horizontal: 3.w, vertical: 3.h),
                                                                                                    decoration: BoxDecoration(
                                                                                                      border: Border.all(
                                                                                                          width: 0.5.w, color: Colors.grey.shade400),
                                                                                                    ),
                                                                                                    child: DropdownButtonHideUnderline(
                                                                                                      child: DropdownButton<DataGetDefectLevel>(
                                                                                                        isExpanded: true,
                                                                                                        isDense: true,
                                                                                                        itemHeight: null,
                                                                                                        value: _lselectDataGetDefectLevel[index],
                                                                                                        iconSize: 15.sp,
                                                                                                        style: const TextStyle(color: Colors.white),
                                                                                                        onChanged: _qualityControl!.qcType == "NVL" &&
                                                                                                                _qualityControl!.qualityChecker !=
                                                                                                                    null
                                                                                                            ? null
                                                                                                            : (DataGetDefectLevel? value) {
                                                                                                                _getSelectedDefectLevel(
                                                                                                                    DropdownDefetchLevel(
                                                                                                                        index: index, value: value));
                                                                                                              },
                                                                                                        items: _lsDataGetDefetchLevel
                                                                                                            .map((DataGetDefectLevel result) {
                                                                                                          return DropdownMenuItem<DataGetDefectLevel>(
                                                                                                              value: result,
                                                                                                              child: Padding(
                                                                                                                padding: EdgeInsets.symmetric(
                                                                                                                    vertical: 5.h),
                                                                                                                child: Text(
                                                                                                                  result.value.toString(),
                                                                                                                  style: TextStyle(
                                                                                                                      color: Colors.black,
                                                                                                                      fontSize: 11.sp),
                                                                                                                ),
                                                                                                              ));
                                                                                                        }).toList(),
                                                                                                        selectedItemBuilder: (BuildContext context) {
                                                                                                          return _lsDataGetDefetchLevel
                                                                                                              .map((DataGetDefectLevel result) {
                                                                                                            return Text(result.value.toString(),
                                                                                                                style: TextStyle(
                                                                                                                    color: Colors.black,
                                                                                                                    fontSize: 11.sp),
                                                                                                                overflow: TextOverflow.ellipsis);
                                                                                                          }).toList();
                                                                                                        },
                                                                                                      ),
                                                                                                    ),
                                                                                                  ),
                                                                                                ),
                                                                                              ],
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        // muc do loi controller
                                                                                        Expanded(
                                                                                          flex: 4,
                                                                                          child: Container(
                                                                                            decoration: BoxDecoration(
                                                                                              border:
                                                                                                  Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                                            ),
                                                                                            child: Column(
                                                                                              children: [
                                                                                                Padding(
                                                                                                  padding: REdgeInsets.all(5),
                                                                                                  child: Container(
                                                                                                    padding: EdgeInsets.symmetric(
                                                                                                        horizontal: 3.w, vertical: 5.h),
                                                                                                    decoration: BoxDecoration(
                                                                                                      border: Border.all(
                                                                                                          width: 0.5.w, color: Colors.grey.shade400),
                                                                                                    ),
                                                                                                    child: TextFormField(
                                                                                                      enabled: _qualityControl!.qcType == "NVL"
                                                                                                          ? _qualityControl!.qualityChecker != null
                                                                                                              ? false
                                                                                                              : true
                                                                                                          : true,
                                                                                                      maxLines: null,
                                                                                                      textAlign: TextAlign.center,
                                                                                                      controller:
                                                                                                          _lsTextEditingControllerError_3[index],
                                                                                                      style: TextStyle(fontSize: 12.sp),
                                                                                                      decoration: InputDecoration(
                                                                                                        border: InputBorder.none,
                                                                                                        focusedBorder: InputBorder.none,
                                                                                                        enabledBorder: InputBorder.none,
                                                                                                        errorBorder: InputBorder.none,
                                                                                                        disabledBorder: InputBorder.none,
                                                                                                        filled: true,
                                                                                                        isDense: true,
                                                                                                        fillColor: Colors.white,
                                                                                                        hintStyle: TextStyle(fontSize: 12.sp),
                                                                                                        contentPadding: EdgeInsets.zero,
                                                                                                      ),
                                                                                                    ),
                                                                                                  ),
                                                                                                ),
                                                                                              ],
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                  IntrinsicHeight(
                                                                                    child: Container(
                                                                                      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                                                      decoration: BoxDecoration(
                                                                                        border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                                      ),
                                                                                      child: Row(
                                                                                        children: <Widget>[
                                                                                          Expanded(
                                                                                            flex: 8,
                                                                                            child: _lsGetFileError[index].isEmpty
                                                                                                ? Row(
                                                                                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                                                    children: <Widget>[
                                                                                                      GestureDetector(
                                                                                                        onTap: _qualityControl!.qcType == "NVL" &&
                                                                                                                _qualityControl!.qualityChecker !=
                                                                                                                    null
                                                                                                            ? null
                                                                                                            : () async {
                                                                                                                final check =
                                                                                                                    await QualityControlFunction
                                                                                                                        .pickImage(context);
                                                                                                                debugPrint(check.toString());
                                                                                                                if (check != null) {
                                                                                                                  bool checkPermission =
                                                                                                                      await ImageFunction
                                                                                                                          .handlePermission(check);
                                                                                                                  if (checkPermission == true) {
                                                                                                                    if (check == true) {
                                                                                                                      List<XFile>? selectedImages =
                                                                                                                          await _pickerImage
                                                                                                                              .pickMultiImage(
                                                                                                                        maxWidth: globalImageConfig
                                                                                                                            .maxWidth,
                                                                                                                        maxHeight: globalImageConfig
                                                                                                                            .maxHeight,
                                                                                                                        imageQuality:
                                                                                                                            globalImageConfig
                                                                                                                                .imageQuality,
                                                                                                                      );
                                                                                                                      if (selectedImages.isEmpty)
                                                                                                                        return;
                                                                                                                      for (var i in selectedImages) {
                                                                                                                        final itemImage =
                                                                                                                            await ImageFunction
                                                                                                                                .saveImageMulti(
                                                                                                                                    i.path);
                                                                                                                        _pickFileImageErrorQuality(
                                                                                                                            MultiSelectImageErrorQuality(
                                                                                                                                index: index,
                                                                                                                                file: itemImage));
                                                                                                                      }
                                                                                                                    } else {
                                                                                                                      final image =
                                                                                                                          await ImagePicker()
                                                                                                                              .pickImage(
                                                                                                                                  source: ImageSource
                                                                                                                                      .camera);
                                                                                                                      if (image == null) return;
                                                                                                                      final imageProfile =
                                                                                                                          await ImageFunction
                                                                                                                              .saveImage(image.path);
                                                                                                                      _pickFileImageErrorQuality(
                                                                                                                          MultiSelectImageErrorQuality(
                                                                                                                              index: index,
                                                                                                                              file: imageProfile));
                                                                                                                    }
                                                                                                                  }
                                                                                                                }
                                                                                                              },
                                                                                                        child: Container(
                                                                                                          padding: EdgeInsets.symmetric(
                                                                                                              vertical: 3.h, horizontal: 9.w),
                                                                                                          decoration: BoxDecoration(
                                                                                                            borderRadius: BorderRadius.circular(10.r),
                                                                                                            color: Colors.grey.shade100,
                                                                                                          ),
                                                                                                          child: Center(
                                                                                                            child: Text(
                                                                                                              "Chọn tệp",
                                                                                                              style: TextStyle(
                                                                                                                  fontSize: 11.sp,
                                                                                                                  color: Colors.blue),
                                                                                                            ),
                                                                                                          ),
                                                                                                        ),
                                                                                                      ),
                                                                                                      SizedBox(width: 10.w),
                                                                                                      Center(
                                                                                                        child: Text(
                                                                                                          "Chưa chọn tệp nào",
                                                                                                          style: TextStyle(fontSize: 11.sp),
                                                                                                        ),
                                                                                                      ),
                                                                                                    ],
                                                                                                  )
                                                                                                : Column(
                                                                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                                                                    children: <Widget>[
                                                                                                      GestureDetector(
                                                                                                        onTap: () async {
                                                                                                          final check =
                                                                                                              await QualityControlFunction.pickImage(
                                                                                                                  context);
                                                                                                          debugPrint(check.toString());
                                                                                                          if (check != null) {
                                                                                                            bool checkPermission =
                                                                                                                await ImageFunction.handlePermission(
                                                                                                                    check);
                                                                                                            if (checkPermission == true) {
                                                                                                              if (check == true) {
                                                                                                                List<XFile>? selectedImages =
                                                                                                                    await _pickerImage.pickMultiImage(
                                                                                                                  maxWidth:
                                                                                                                      globalImageConfig.maxWidth,
                                                                                                                  maxHeight:
                                                                                                                      globalImageConfig.maxHeight,
                                                                                                                  imageQuality:
                                                                                                                      globalImageConfig.imageQuality,
                                                                                                                );
                                                                                                                if (selectedImages.isEmpty) return;
                                                                                                                for (var i in selectedImages) {
                                                                                                                  final itemImage =
                                                                                                                      await ImageFunction
                                                                                                                          .saveImageMulti(i.path);
                                                                                                                  _pickFileImageErrorQuality(
                                                                                                                      MultiSelectImageErrorQuality(
                                                                                                                          index: index,
                                                                                                                          file: itemImage));
                                                                                                                }
                                                                                                              } else {
                                                                                                                final image = await ImagePicker()
                                                                                                                    .pickImage(
                                                                                                                        source: ImageSource.camera);
                                                                                                                if (image == null) return;
                                                                                                                final imageProfile =
                                                                                                                    await ImageFunction.saveImage(
                                                                                                                        image.path);
                                                                                                                _pickFileImageErrorQuality(
                                                                                                                    MultiSelectImageErrorQuality(
                                                                                                                        index: index,
                                                                                                                        file: imageProfile));
                                                                                                              }
                                                                                                            }
                                                                                                          }
                                                                                                        },
                                                                                                        child: Container(
                                                                                                          padding: EdgeInsets.symmetric(
                                                                                                              vertical: 3.h, horizontal: 9.w),
                                                                                                          decoration: BoxDecoration(
                                                                                                            borderRadius: BorderRadius.circular(10.r),
                                                                                                            color: Colors.grey.shade100,
                                                                                                          ),
                                                                                                          child: Text(
                                                                                                            "Chọn tệp",
                                                                                                            style: TextStyle(
                                                                                                                fontSize: 11.sp, color: Colors.blue),
                                                                                                          ),
                                                                                                        ),
                                                                                                      ),
                                                                                                      SizedBox(height: 10.h),
                                                                                                      Wrap(
                                                                                                        spacing: 3.w,
                                                                                                        runSpacing: 3.h,
                                                                                                        children: List.generate(
                                                                                                          _lsGetFileError[index].length,
                                                                                                          (indexImageError) {
                                                                                                            // String filenameError = basename(
                                                                                                            //     lsGetFileError[index]
                                                                                                            //             [indexImageError]
                                                                                                            //         .path);
                                                                                                            return SizedBox(
                                                                                                              width: 50.w,
                                                                                                              child: Stack(
                                                                                                                children: <Widget>[
                                                                                                                  GestureDetector(
                                                                                                                    onTap: () {
                                                                                                                      Navigator.push(
                                                                                                                        context,
                                                                                                                        MaterialPageRoute(
                                                                                                                          builder: (context) =>
                                                                                                                              ImageQuatity(
                                                                                                                                  lsImage:
                                                                                                                                      _lsGetFileError[
                                                                                                                                          index],
                                                                                                                                  index:
                                                                                                                                      indexImageError),
                                                                                                                        ),
                                                                                                                      );
                                                                                                                    },
                                                                                                                    child: ListImagePicker(
                                                                                                                        fileImage:
                                                                                                                            _lsGetFileError[index]
                                                                                                                                [indexImageError]),
                                                                                                                  ),
                                                                                                                  Align(
                                                                                                                    alignment: Alignment.topRight,
                                                                                                                    child: IconButton(
                                                                                                                      highlightColor:
                                                                                                                          Colors.transparent,
                                                                                                                      hoverColor: Colors.transparent,
                                                                                                                      constraints:
                                                                                                                          const BoxConstraints(),
                                                                                                                      iconSize: 17.sp,
                                                                                                                      color: Colors.red.shade800,
                                                                                                                      icon: const Icon(
                                                                                                                          Icons.remove_circle),
                                                                                                                      onPressed: () {
                                                                                                                        _deleteListFileError(
                                                                                                                            MultiDeleteImageErrorQuality(
                                                                                                                                index: index,
                                                                                                                                indexImageError:
                                                                                                                                    indexImageError));
                                                                                                                      },
                                                                                                                    ),
                                                                                                                  )
                                                                                                                ],
                                                                                                              ),
                                                                                                            );
                                                                                                          },
                                                                                                        ),
                                                                                                      ),
                                                                                                      SizedBox(height: 10.h),
                                                                                                    ],
                                                                                                  ),
                                                                                          ),
                                                                                          Visibility(
                                                                                            visible:
                                                                                                (_lsError[index].errorFileViewModel ?? []).isNotEmpty
                                                                                                    ? true
                                                                                                    : false,
                                                                                            child: Expanded(
                                                                                              flex: 2,
                                                                                              child: GestureDetector(
                                                                                                onTap: () {
                                                                                                  String title = "";
                                                                                                  String error = _lsSelectedError[index] == null
                                                                                                      ? ""
                                                                                                      : _lsSelectedError[index]!
                                                                                                          .catalogTextVi
                                                                                                          .toString();
                                                                                                  int indexErrorName = error.indexOf('|');
                                                                                                  title = _lsSelectedError[index] == null
                                                                                                      ? ""
                                                                                                      : _lsSelectedError[index]!
                                                                                                          .catalogTextVi
                                                                                                          .toString()
                                                                                                          .substring(indexErrorName + 2)
                                                                                                          .toUpperCase();
                                                                                                  // String error = lsSelectedError[index]!.catalogTextVi.toString();
                                                                                                  showDialog(
                                                                                                    context: context,
                                                                                                    builder: (BuildContext context) {
                                                                                                      return DialogErrorQuality(
                                                                                                          title: 'HÌNH ẢNH LỖI ' + title,
                                                                                                          listImage:
                                                                                                              _lsError[index].errorFileViewModel ??
                                                                                                                  []);
                                                                                                    },
                                                                                                  );
                                                                                                },
                                                                                                child: Container(
                                                                                                  padding: EdgeInsets.symmetric(vertical: 5.h),
                                                                                                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                                                                                                  decoration: BoxDecoration(
                                                                                                    color: const Color(0xff0052cc),
                                                                                                    borderRadius: BorderRadius.all(
                                                                                                      Radius.circular(2.r),
                                                                                                    ),
                                                                                                  ),
                                                                                                  child: Icon(
                                                                                                    Icons.image,
                                                                                                    color: Colors.white,
                                                                                                    size: 15.sp,
                                                                                                  ),
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                          )
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                  Visibility(
                                                                                    visible: _checkVisiButtonError[index],
                                                                                    child: IntrinsicHeight(
                                                                                      child: Container(
                                                                                        decoration: BoxDecoration(
                                                                                          border: Border.all(width: 0.5, color: Colors.grey.shade300),
                                                                                        ),
                                                                                        child: Align(
                                                                                          alignment: Alignment.topRight,
                                                                                          child: IconButton(
                                                                                            highlightColor: Colors.transparent,
                                                                                            hoverColor: Colors.transparent,
                                                                                            constraints: const BoxConstraints(),
                                                                                            iconSize: 17.sp,
                                                                                            color: Colors.red.shade800,
                                                                                            icon: const Icon(Icons.delete),
                                                                                            onPressed: () {
                                                                                              _deleteItemListError(index);
                                                                                            },
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ))),
                                                    ButtonAddNewCardError(addNewCardError: _addNewCardError, qualityControl: _qualityControl),
                                                    SizedBox(height: 15.h),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(height: 15.h),
                                              GestureDetector(
                                                onTap: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                    ? null
                                                    : () {
                                                        String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                        DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                        DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                        if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                          Platform.isAndroid
                                                              ? showDialog(
                                                                  context: context,
                                                                  barrierDismissible: false,
                                                                  builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                              : showCupertinoDialog(
                                                                  context: context,
                                                                  barrierDismissible: false,
                                                                  builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                        } else {
                                                          _checkValidate();
                                                          FocusManager.instance.primaryFocus?.unfocus();
                                                          debugPrint(_errorAcceptableLevelDetail.toString());
                                                          // debugPrint(_lsTextEditingControllerError_1[0].text);
                                                          if (_errorSelectType == false &&
                                                              _errorP0 == false &&
                                                              _errorQuantityCheck == false &&
                                                              _errorSelectedResultQualityView == false &&
                                                              _errorTestMethodDetail == false &&
                                                              _errorLevelDetail == false &&
                                                              _errorAcceptableLevelDetail == false &&
                                                              _errorQuantityCheckDetail == false &&
                                                              _errorResultCheckDetail == false &&
                                                              _lsErrorInfor.where((element) => element == true).isEmpty) {
                                                            QualityControlFunction.sendQualityControl(
                                                                context,
                                                                _lsFileTabCheck,
                                                                _controller_2.text,
                                                                _qualityControl,
                                                                _selectedStaff,
                                                                _date!.toIso8601String(),
                                                                _selectedType,
                                                                _selectedResult,
                                                                _controller_1.text,
                                                                QualityControlDetailFunction.getSendQualityControlDetail(
                                                                    _qualityControl,
                                                                    _selectedMethod,
                                                                    _selectedLevel,
                                                                    _controller_3.text,
                                                                    _controller_4.text,
                                                                    _selectedResultDetail,
                                                                    _hideDetailLever,
                                                                    _controllerHideDetailLever.text),
                                                                QualityControlInfoFunction.getLsSendQualityControlInformation(
                                                                    _lsQualityControlInformation,
                                                                    _qualityControl,
                                                                    _lsSelectedInfo,
                                                                    _lsTextEditingController,
                                                                    _lsGetFileInfor),
                                                                QualityControlErrorFunction.getLsError(
                                                                  _lsError,
                                                                  _qualityControl,
                                                                  _lsSelectedError,
                                                                  _lselectDataGetDefectLevel,
                                                                  _lsTextEditingControllerError_1,
                                                                  _lsTextEditingControllerError_3,
                                                                  _lsGetFileError,
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                  [],
                                                                ),
                                                                widget.user.token.toString());
                                                          }
                                                        }
                                                      },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(vertical: 15.h),
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.all(
                                                      Radius.circular(5.r),
                                                    ),
                                                    color: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null
                                                        ? Colors.grey.shade400
                                                        : const Color(0xff0052cc),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      'Submit',
                                                      style: TextStyle(color: Colors.white, fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(height: 15.h),
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  )),
            ));
  }

  IntrinsicHeight itemHeader() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
              ),
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
              child: Text(
                "Stt",
                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
              ),
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Text(
                "Thông tin kiểm tra", // Child item
                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 0.5.w),
              ),
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              child: Text(
                "Ghi chú",
                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ErrorView extends StatelessWidget {
  const _ErrorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)));
  }
}

class _ListNotFoundView extends StatelessWidget {
  const _ListNotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text("Không tìm thấy thông tin phiếu kiểm tra!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
  }
}
