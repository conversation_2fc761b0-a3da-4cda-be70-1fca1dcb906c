﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DistrictModel", Schema = "tMasterData")]
    public partial class DistrictModel
    {
        [Key]
        public Guid DistrictId { get; set; }
        public Guid ProvinceId { get; set; }
        [Required]
        [StringLength(50)]
        public string Appellation { get; set; }
        [Required]
        [StringLength(200)]
        public string DistrictName { get; set; }
        [StringLength(50)]
        public string DistrictCode { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? RegisterVAT { get; set; }
        public int? OrderIndex { get; set; }
        public bool Actived { get; set; }
        [StringLength(200)]
        public string VehicleRegistrationSignature { get; set; }
    }
}