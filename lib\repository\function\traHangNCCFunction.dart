import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Widget/dialogWidget/DialogErrorValidate.dart';
import 'package:ttf/repository/api/traHangNCCApi.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/modalBottomSheet/imagePickerModalBottomSheet.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/GetQuantitySampleModel.dart';
import '../../model/RequestTraHangNCC.dart';
import '../../model/dataQualityControl.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/qualityControlApi.dart';
import '../../model/sendErrorQualityControl.dart';
import '../../model/sendQualityControlDetail.dart';
import '../api/getDefectLevel.dart';
import '../api/getQuantitySample.dart';
import '../api/qualityControlApi.dart';
import '../showDateTime.dart';

class TraHangNCCFunction {
  static bool checkIsSend = false;
  static QualityCheckerInfo defaultValueQC = QualityCheckerInfo(accountId: " ", salesEmployeeName: '--Vui lòng chọn--');
  static QualityTypeList defaultValueQualityTypeList = QualityTypeList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  static Future<DataRequestReturnVendorModel?> getData(String token, String id) async {
    final response = await TraHangNCCApi.getData(token, id);

    if (response.statusCode == 200) {
      final responseItem = jsonDecode(response.body);
      final responseData = RequestReturnVendorModelItem.fromJson(responseItem);
      if (responseData.code == 200 && responseData.isSuccess == true) {
        return responseData.data;
      } else {
        return null;
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<List<DataRequestReturnVendorModel>?> getListData(String token) async {
    final response = await TraHangNCCApi.getListData(token);

    if (response.statusCode == 200) {
      final responseGetList = jsonDecode(response.body);
      final responseData = RequestReturnVendorModelList.fromJson(responseGetList);
      if (responseData.code == 200 && responseData.isSuccess == true) {
        return responseData.data;
      } else {
        return [];
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<void> sendRequestReturnVendor(
      BuildContext context, List<RequestTraHangNCC> listRequestTraHangNCC, int _returnType, String token) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      final value = await TraHangNCCApi.postRequestReturnVendor(listRequestTraHangNCC, _returnType, token);

      Navigator.pop(context);
      checkIsSend = true;

      if (value.statusCode == 200) {
        final respStr = value.body;

        final dataResponse = DataQualityControl.fromJson(jsonDecode(respStr));

        if (dataResponse.code == 202 && dataResponse.success == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              dataResponse.data.toString(),
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
          ));
          Future.delayed(const Duration(seconds: 1), () {
            Navigator.pop(context, true);
          });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: dataResponse.data.toString()));
        }
      } else {
        final respStr = value.body;
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: respStr.toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      debugPrint(error.toString());
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Ứng dụng xày ra lỗi vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    }
  }

  static String dateFormatConfirm(QualityControl? qualityControl) {
    if (qualityControl != null) {
      if (qualityControl.confirmDate != null) {
        DateTime stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(qualityControl.confirmDate!);
        String dateFormatStringConfirm = DateFormat('dd/MM/yyyy').format(stringToDateTimeConfirm);
        return dateFormatStringConfirm;
      } else {
        return "";
      }
    } else {
      return "";
    }
  }

  static String getDate(QualityControl? qualityControl, DateTime? date) {
    String? dateFormatStringQualityDate;
    if (qualityControl!.qualityDate != null && date == null) {
      dateFormatStringQualityDate =
          DateFormat("dd-MM-yyyy hh:mm a").format(DateFormat("yyyy-MM-dd hh:mm").parse(qualityControl.qualityDate.toString()));
      // dateFormatStringQualityDate = DateFormat.yMMMMd('vi').format(stringToDateTimeQualityDate);
    } else if (qualityControl.qualityDate == null && date == null) {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.now());
    } else {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(date!);
      // return '${date.month}/${date.day}/${date.year}';
    }
    return dateFormatStringQualityDate;
  }

  // static String formatDatePost(QualityControl? qualityControl, DateTime? date){
  //    DateTime getFormatDateTime = DateFormat.yMMMMd('vi').parse(getDate(qualityControl,date));
  //    String getFormatDateString = DateFormat("yyyy-MM-dd").format(getFormatDateTime);
  //    return getFormatDateString;
  //  }

  static Future<QualityControlModel?> fetchQualityControl(String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    final response = await QuantityControlApi.getQuantityControl(qualityControlId, token, rawMaterialCardId, fromPage);
    if (response.statusCode == 200) {
      final qualityControlModel = QualityControlModel.fromJson(jsonDecode(response.body));
      return qualityControlModel;
    } else {
      return null;
    }
  }

  static Future<List<DataGetDefectLevel>?> fetchGetDefectLevelApi(String token) async {
    final response = await GetDefectLevelApi.getDefectLevelApi(token);
    if (response.statusCode == 200) {
      final responseGetDefectLevel = jsonDecode(response.body);
      final getGetDefectLevelApi = GetDefectLevel.fromJson(responseGetDefectLevel);
      if (getGetDefectLevelApi.code == 200 && getGetDefectLevelApi.isSuccess == true) {
        return getGetDefectLevelApi.data;
      } else {
        return [];
      }
    } else {
      throw (response.body.toString());
    }
  }

  static Future<DataGetQuantitySample?> fetchGetQuantitySample(
      String sampleName, String quantityCheck, String token, BuildContext context, bool checkMounted) async {
    try {
      final response = await GetQuantitySample.getQuantitySample(sampleName, quantityCheck, token);
      if (!checkMounted) return null;
      if (response.statusCode == 200) {
        final responseGetQuantitySample = jsonDecode(response.body);
        final getGetQuantitySample = GetQuantitySampleModel.fromJson(responseGetQuantitySample);
        if (getGetQuantitySample.code == 200 && getGetQuantitySample.isSuccess == true) {
          debugPrint(getGetQuantitySample.data.toString());
          return getGetQuantitySample.data;
        } else {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              getGetQuantitySample.message.toString(),
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
          ));
          return null;
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            response.reasonPhrase.toString(),
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
        ));
        return null;
      }
    } on SocketException catch (_) {
      if (!checkMounted) return null;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
      return null;
    } catch (error) {
      if (!checkMounted) return null;
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Ứng dụng xày ra lỗi vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
      return null;
    }
  }
}
