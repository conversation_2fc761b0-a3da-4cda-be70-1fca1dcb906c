import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class DiaLogErrorValidate extends StatelessWidget {
  final String message;
  const DiaLogErrorValidate({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(

      content: SingleChildScrollView(
          child:Column(
              mainAxisSize: MainAxisSize.min,
              children:<Widget>[
                Icon(Icons.warning_rounded,size: 50.sp),
                SizedBox(height: 15.h),
                Text(message,
                  style: TextStyle(fontSize: 15.sp),textAlign: TextAlign.center,)
              ])),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Hủy',style: TextStyle(fontSize: 15.sp),),
        ),
      ],
    );
  }
}
