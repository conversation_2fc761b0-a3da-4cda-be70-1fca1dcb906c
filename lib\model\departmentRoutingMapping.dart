class DepartmentRoutingMapping {
  String? departmentCode;
  String? routingCode;

  DepartmentRoutingMapping({this.departmentCode, this.routingCode});

  DepartmentRoutingMapping.fromJson(Map<String, dynamic> json) {
    departmentCode = json['departmentCode'];
    routingCode = json['routingCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['departmentCode'] = departmentCode;
    data['routingCode'] = routingCode;
    return data;
  }
}
