import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import 'package:ttf/model/qualityControlApi.dart';
import '../../model/RequestTraHangNCC.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/sendErrorQualityControl.dart';
import '../../model/sendQualityControlDetail.dart';
import '../../urlApi/urlApi.dart';

class TraHangNCCApi {
  static Future<http.Response> postRequestReturnVendor(List<RequestTraHangNCC> listRequestTraHangNCC, int returnType, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlRequestReturnVendor + "RequestReturnVendor");
    debugPrint(uri.toString());

    final List<Map<String, dynamic>> requestPayload = listRequestTraHangNCC.map((item) {
      return {
        "returnType": returnType,
        "poNumber": item.poNumber,
        "requestReturnVendorId": item.requestReturnVendorId,
        "dataList": item.dataList.map((dataItem) {
          return dataItem.toJson();
        }).toList()
      };
    }).toList();

    final response = await http.post(
      uri,
      headers: {
        ...UrlApi.headersToken(token),
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode(requestPayload),
    );

    return response;
  }

  static Future<http.Response> getListData(String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = baseUrl + UrlApi.baseUrlRequestReturnVendor + "GetListReturnRequest";
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getData(String token, String id) async {
    final data = {"RequestReturnVendorId": id};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = baseUrl + UrlApi.baseUrlRequestReturnVendor + "GetReturnRequest" + "?RequestReturnVendorId=${data['RequestReturnVendorId']}";
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
