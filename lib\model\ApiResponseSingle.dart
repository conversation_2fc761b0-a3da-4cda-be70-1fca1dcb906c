class ApiResponseSingle {
  int? code;
  bool? isSuccess;
  String? message;
  dynamic data; // Changed from List<dynamic>? to dynamic

  ApiResponseSingle({this.code, this.isSuccess, this.message, this.data});

  ApiResponseSingle.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data']; // Directly assign the data
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = this.data; // Directly assign the data

    return data;
  }
}
