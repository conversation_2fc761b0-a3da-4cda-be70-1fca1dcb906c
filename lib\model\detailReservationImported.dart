class DetailReservationImported {
  int? code;
  bool? isSuccess;
  String? message;
  DataDetailReservationImported? data;

  DetailReservationImported(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  DetailReservationImported.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataDetailReservationImported.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataDetailReservationImported {
  String? batchNumber;
  String? plant;
  String? slocExport;
  String? warehouseNoExport;
  String? storageBinExport;
  String? slocImport;
  String? warehouseNoImport;
  String? storageBinImport;
  String? productCode;
  String? productName;
  double? sumQuantityImported;
  List<QuantityImporteds>? quantityImporteds;

  DataDetailReservationImported(
      {
        this.batchNumber,
        this.plant,
        this.slocExport,
        this.warehouseNoExport,
        this.storageBinExport,
        this.slocImport,
        this.warehouseNoImport,
        this.storageBinImport,
        this.productCode,
        this.productName,
        this.sumQuantityImported,
        this.quantityImporteds});

  DataDetailReservationImported.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
    plant = json['plant'];
    slocExport = json['slocExport'];
    warehouseNoExport = json['warehouseNoExport'];
    storageBinExport = json['storageBinExport'];
    slocImport = json['slocImport'];
    warehouseNoImport = json['warehouseNoImport'];
    storageBinImport = json['storageBinImport'];
    productCode = json['productCode'];
    productName = json['productName'];
    sumQuantityImported = json['sumQuantityImported'];
    if (json['quantityImporteds'] != null) {
      quantityImporteds = <QuantityImporteds>[];
      json['quantityImporteds'].forEach((v) {
        quantityImporteds!.add(QuantityImporteds.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    data['plant'] = plant;
    data['slocExport'] = slocExport;
    data['warehouseNoExport'] = warehouseNoExport;
    data['storageBinExport'] = storageBinExport;
    data['slocImport'] = slocImport;
    data['warehouseNoImport'] = warehouseNoImport;
    data['storageBinImport'] = storageBinImport;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['sumQuantityImported'] = sumQuantityImported;
    if (quantityImporteds != null) {
      data['quantityImporteds'] =
          quantityImporteds!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
class QuantityImporteds {
  String? po;
  String? poLine;
  String? so;
  String? soLine;
  String? wbs;
  String? lsxdt;
  double? quantityImported;
  double? quantityStock;
  String? unit;

  QuantityImporteds(
      {this.po,
        this.poLine,
        this.so,
        this.soLine,
        this.wbs,
        this.lsxdt,
        this.quantityImported,
        this.quantityStock,
        this.unit});

  QuantityImporteds.fromJson(Map<String, dynamic> json) {
    po = json['po'];
    poLine = json['poLine'];
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    lsxdt = json['lsxdt'];
    quantityImported = json['quantityImported'];
    quantityStock = json['quantityStock'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['po'] = po;
    data['poLine'] = poLine;
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['lsxdt'] = lsxdt;
    data['quantityImported'] = quantityImported;
    data['quantityStock'] = quantityStock;
    data['unit'] = unit;
    return data;
  }
}