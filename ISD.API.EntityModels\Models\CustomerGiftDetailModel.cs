﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("CustomerGiftDetailModel", Schema = "tMasterData")]
    public partial class CustomerGiftDetailModel
    {
        [Key]
        public Guid GiftId { get; set; }
        [Key]
        public Guid CustomerId { get; set; }
        public bool? isRead { get; set; }

        [ForeignKey("CustomerId")]
        [InverseProperty("CustomerGiftDetailModel")]
        public virtual CustomerModel Customer { get; set; }
        [ForeignKey("GiftId")]
        [InverseProperty("CustomerGiftDetailModel")]
        public virtual CustomerGiftModel Gift { get; set; }
    }
}