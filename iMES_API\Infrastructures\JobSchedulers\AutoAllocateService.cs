﻿using GetSOWBSByLSX;
using Hangfire;
using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.EntityModels.Data;
using ISD.API.EntityModels.Models;
using ISD.API.Repositories.MES;
using ISD.API.Repositories;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.MESP2;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers
{
    public class AutoAllocateService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IOneSignalService _oneSignalService;

        public AutoAllocateService(
            IServiceProvider serviceProvider,
            IOneSignalService oneSignalService = null)
        {
            this._serviceProvider = serviceProvider;
            this._oneSignalService = oneSignalService;
        }

        public enum NotificationStatus
        {
            Pending, // 0 pending
            Failed, // 1 failed
            Sent // 2 sent
        }

        [DisableConcurrentExecution(30)]
        public async Task Run()
        {
            try
            {
                //Create scope db context
                using var scope = _serviceProvider.CreateScope();
                using var _context = scope.ServiceProvider.GetRequiredService<EntityDataContext>();

                var yesterday = DateTime.Now.Date.AddDays(-1);
                string formattedDate = yesterday.ToString("dd/MM/yyyy");
                await RunAutoAllocate(formattedDate, _context);

            }
            catch (Exception)
            {
                return;
            }
        }



        private async Task RunAutoAllocate(string date, EntityDataContext _context)
        {
            //DateTime currentDate = DateTime.Now;
            //DateTime previousDay = currentDate.AddDays(-1);

            //Console.WriteLine(previousDay);
            //string dateString = "02/01/2024";
            //string dateString = "03/01/2024"; // Data đã phân bổ
            string dateString = date;
            DateTime allocateDate = DateTime.ParseExact(dateString, "dd/MM/yyyy", CultureInfo.InvariantCulture);

            //var request = new AllocateMaterialRequest()
            //{
            //    AllocateDate = allocateDate,
            //    Plant = "1200"
            //};
            //await PhanBoNVLTheoPlant(_context, allocateDate, request);

            //request.AllocateDate.Dump();

            var plantList = new List<string> { "1000", "1100", "1200", "1300" };

            foreach (var plant in plantList)
            {
                var request = new AllocateMaterialRequest()
                {
                    AllocateDate = allocateDate,
                    Plant = plant
                };

                await PhanBoNVLTheoPlant(_context, allocateDate, request);
            }
        }

        private static async Task PhanBoNVLTheoPlant(EntityDataContext _context, DateTime allocateDate, AllocateMaterialRequest request)
        {
            //#### NOTE: change app setting to Production First

            //var _context = new EntityDataContext();
            // 1. Lấy danh sách phân bổ
            var allocateRepo = new AllocateRepository(_context);
            var _unitOfWork = new UnitOfWork(_context);

            var dataFull = allocateRepo.GeDataAllocateByDayPlant(request.AllocateDate, request.Plant);

            //dataFull.Dump("Danh sách");

            var dataChuaPhanBo = dataFull.Where(a => a.Status == 1);

            //dataChuaPhanBo.Dump("Chưa phân bổ");

            //dataChuaPhanBo.Dump();

            // 2. Chạy từng line mã sản phẩm và sl phân bổ trong ngày
            //var testLine = dataChuaPhanBo.First();
            //var testLine = dataChuaPhanBo.FirstOrDefault(i => i.Batch == "DHW09N23DS");
            //var testLine = dataChuaPhanBo.ElementAt(2);

            var totalLine = dataChuaPhanBo.Count();
            //totalLine.Dump("Tổng line");

            var successLine = 0;

            //var dataChuaPhanBoTest = dataChuaPhanBo.Skip(1).Take(1);
            //var dataChuaPhanBoTest = dataChuaPhanBo.Take(1);

            foreach (var testLine in dataChuaPhanBo)
            {
                //var testLine = dataFull.ElementAt(5);

                //testLine.Dump("Line đầu tiên");

                var req = new GetSOWBSByBatchRequest()
                {
                    BatchNumber = testLine.Batch,
                    Plant = testLine.Plant,
                    ProductCode = testLine.ProductCode,
                    Sloc = testLine.Sloc
                };

                var listSOWBSByBatch = new List<GetSOWBSByBatchResponse>();
                var warehouseRepo = new WarehouseRepository(_context);

                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == req.ProductCode);
                if (product == null)
                {
                    // TODO skip run
                    continue;
                }

                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == req.Sloc);
                if (sloc == null)
                {
                    // TODO skip run
                    continue;
                }

                var phanBoWBSDetail = new List<WarehouseExportDetailModel>();

                try
                {
                    listSOWBSByBatch = await warehouseRepo.GetSOWBSByBatch(req, product.MEINS, request.Plant);
                    phanBoWBSDetail = allocateRepo.GetPhanBoDetail(testLine.ProductCode, request.AllocateDate, testLine.StepCode);

                }
                catch (Exception ex)
                {
                    continue;
                }

                foreach (var item in listSOWBSByBatch)
                {
                    var soSolLine = $"{item.SO}/{item.SOLine}";
                    var wbs = item.WBS;

                    var matchingDetail = phanBoWBSDetail.FirstOrDefault(detail =>
                        detail.SOWBS == soSolLine ||
                        detail.SOWBS == wbs ||
                        (soSolLine == "null/null" && wbs == null && detail.SOWBS == "Tồn trơn"));
                    if (matchingDetail != null)
                    {
                        item.Quantity = matchingDetail.Quantity;
                    }
                    else
                    {
                        item.Quantity = 0;
                    }
                }

                //listSOWBSByBatch.Dump("Data ở trên");

                // Xử lý trường hợp đặc biệt, phân bổ ở trên sl 270, nhưng load listSOWBSByBatch sl 495, thì từng item của listSOWBSByBatch sẽ giữ nguyên số lượng cho tới khi = 270, những cái ở dưới sl sẽ = 0 hết
                var totalPhanBo = testLine.AllocateAmount;
                decimal? count = 0;

                foreach (var element in listSOWBSByBatch)
                {
                    if (count == totalPhanBo)
                    {
                        element.Quantity = 0;
                        continue;
                    }

                    if (count < totalPhanBo)
                    {
                        count += element.Quantity;
                    }
                }

                decimal totalOTren = 0;
                foreach (var item in listSOWBSByBatch)
                {
                    totalOTren = (decimal)(totalOTren + item.Quantity ?? 0);
                }

                if (totalOTren == 0)
                {
                    continue;
                }

                var materialByLSXRequest = new DetailAllocateMaterialsRequest()
                {
                    AllocateDate = request.AllocateDate,
                    Batch = testLine.Batch,
                    Plant = testLine.Plant,
                    ProductCode = testLine.ProductCode,
                    Sloc = testLine.Sloc,
                    StepCode = testLine.StepCode
                };

                var allocateMaterialsByLSX = allocateRepo.GetAllocateMaterialsByLSX(materialByLSXRequest);

                var detail = allocateRepo.GetAllocateMaterialsByLSXDetail(request.AllocateDate, allocateMaterialsByLSX.StepCode, allocateMaterialsByLSX.Unit);

                var zMesInputLSX = new ZMES_PRODUCTION_ORDER02
                {
                    IT_INPUT = detail.Select(x => new ZST_MES_INPUT_LSX
                    {
                        AUFNR = x.LSX,
                        WERKS = request.Plant
                    }).ToArray()
                };

                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.GetSOWBSByLSX(zMesInputLSX);
                    var resSAPs = responeToSAP.ZMES_PRODUCTION_ORDER02Response.ET_OUTPUT.ToList();

                    if (resSAPs.Any())
                    {
                        foreach (var item in detail)
                        {
                            //Data SAP
                            //var soWBS = resSAPs.FirstOrDefault(x => x.AUFNR == $"000{item.LSX}");
                            var soWBS = resSAPs.FirstOrDefault(x => x.AUFNR.TrimStart('0') == item.LSX.TrimStart('0'));
                            item.Unit = allocateMaterialsByLSX.Unit;

                            //Ưu tiên lấy WBS
                            if (!string.IsNullOrEmpty(soWBS?.PROJN))
                            {
                                //WBS
                                item.WBS = soWBS?.PROJN_TEXT;
                                //SO và SOLine
                                item.SONumber = null;
                                item.SOLine = null;
                            }

                            if (!string.IsNullOrEmpty(soWBS?.KDAUF))
                            {
                                //SO và SOLine
                                item.SONumber = soWBS?.KDAUF;
                                item.SOLine = soWBS?.KDPOS;
                                //WBS
                                item.WBS = null;
                            }
                        }
                    }
                }
                catch(Exception ex)
                {
                    // TODO skip run
                    Console.WriteLine("Lỗi SAP khi lấy GetSOWBSByLSX:" + ex.Message);
                    continue;
                }

                allocateMaterialsByLSX.DetailAllocateMaterials = detail;

                //allocateMaterialsByLSX.Dump("Data ở dưới - Header");

                //allocateMaterialsByLSX.DetailAllocateMaterials.Dump("Data ở dưới");

                var filteredItems = allocateMaterialsByLSX.DetailAllocateMaterials
                    .Where(item => listSOWBSByBatch.Select(b => b.WBS).Contains(item.WBS))
                    .ToList();

                //filteredItems.Dump("Data ở dưới đã lọc ra");

                var filteredItemsRemoveDuplicated = filteredItems
                        .GroupBy(item => new
                        {
                            item.LSX,
                            item.ProductCode,
                            item.WBS,
                            // Add more properties here
                        })
                        .Select(group => group.First())
                        .ToList();
                        //.Dump("Data ở dưới đã lọc ra, lọc trùng");


                foreach (var itemBenDuoi in filteredItemsRemoveDuplicated)
                {
                    var matchingWBS = listSOWBSByBatch.FirstOrDefault(itemTren => itemTren.WBS == itemBenDuoi.WBS);
                    if (matchingWBS != null)
                    {
                        //itemBenDuoi.AllocateAmount = (int)matchingWBS.Quantity;
                        itemBenDuoi.AllocateDate = request.AllocateDate;
                        itemBenDuoi.Batch = testLine.Batch;
                        itemBenDuoi.ProductCode = testLine.ProductCode;
                        itemBenDuoi.StepCode = testLine.StepCode;
                    }
                }


                var saveAllocate = new SaveAllocateMaterialsRequest();
                saveAllocate.TotalAllocateAmount = filteredItemsRemoveDuplicated.Select(f => f.AllocateAmount).Sum();

                foreach (var inputSaveAllocate in filteredItemsRemoveDuplicated)
                {
                    var matchingWBS = listSOWBSByBatch.FirstOrDefault(itemTren => itemTren.WBS == inputSaveAllocate.WBS);

                    //matchingWBS.Dump("Match WBS item");

                    if (matchingWBS != null)
                    {
                        var saveAllocateDetail = new DetailAllocateMaterialRequest()
                        {
                            TaskId = inputSaveAllocate.TaskId,
                            ProductCode = inputSaveAllocate.ProductCode,
                            AllocateAmount = matchingWBS.Quantity,
                            Unit = inputSaveAllocate.Unit,
                            Sloc = testLine.Sloc, //inputSaveAllocate.Sloc,
                            Batch = inputSaveAllocate.Batch,
                            AllocateDate = inputSaveAllocate.AllocateDate,
                            SONumber = inputSaveAllocate.SONumber,
                            SOLine = inputSaveAllocate.SOLine,
                            WBS = inputSaveAllocate.WBS,
                            StepCode = inputSaveAllocate.StepCode,

                        };

                        saveAllocate.DetailAllocates.Add(saveAllocateDetail);
                    }
                }

                //saveAllocate.DetailAllocates.Dump("Đã phân bổ");

                decimal totalODuoi = 0;
                foreach (var item in saveAllocate.DetailAllocates)
                {
                    totalODuoi = (decimal)(totalODuoi + item.AllocateAmount ?? 0);
                }

                //totalOTren.Dump("Total ở trên");

                //totalODuoi.Dump("Total ở dưới");

                if (totalOTren != totalODuoi)
                {
                    continue;
                }

                var listWExport = new List<WarehouseExportModel>();
                var SYSTEM = await _context.AccountModel.Where(p => p.UserName == "SYSTEM").Select(p => p.AccountId).FirstOrDefaultAsync();

                Guid? headerId = Guid.NewGuid();

                foreach (var item in saveAllocate.DetailAllocates)
                {
                    var unit = item.Unit;

                    if (unit == "DAY")
                    {
                        unit = "DAA";
                    }
                    //Save data phân bổ
                    var wExport = new WarehouseExportModel
                    {
                        WarhouseExportId = Guid.NewGuid(),
                        //Loại giao dịch
                        MovementType = MovementType.Allocate,
                        //NVL
                        ProductCode = item.ProductCode,
                        //Ngày chứng từ
                        DocumentDate = item.AllocateDate,
                        //LSX
                        ReferenceDocumentId = item.TaskId,
                        //Số lô
                        Batch = item.Batch,
                        Plant = testLine.Plant,
                        //Kho                    
                        SlocId = sloc?.Id,
                        //Số lượng phân bổ
                        Quantity = item.AllocateAmount,
                        HeaderIdSAP = headerId,
                        //ĐVT
                        Unit = unit,
                        //Công đoạn
                        StepCode = item.StepCode,
                        //So và SO LINE
                        SONumber = item.SONumber,
                        SOLine = item.SOLine,
                        WBS = item.WBS,
                        //Common
                        CreateBy = SYSTEM,
                        CreateTime = DateTime.Now,
                        Actived = true,

                        DateKey = int.Parse(item.AllocateDate?.ToString("yyyyMMdd")),
                        IsPhanBoSAP = true
                    };

                    listWExport.Add(wExport);
                    //_context.WarehouseExportModel.AddRange(listWExport); // TODO: lưu tạm
                }

                var slocs = _context.SlocModel.AsNoTracking();
                var tasks = _context.TaskModel.AsNoTracking();

                var zMes = allocateRepo.CreateZMES_FM_INF_TRANS_Input(listWExport, headerId, tasks, sloc, testLine.Plant);

                //listWExport.Dump();

                var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(zMes);
                //jsonString.Dump();

                //zMes.Dump("SEND TO SAP INPUT");

                //listWExport.Dump("WarehouseExportModel save to database");

                //_context.WarehouseExportModel.AddRange(listWExport);
                //await _context.SaveChangesAsync();
                // TODO: Nếu test thì uncomment để hiển thị data sẽ lưu và chuyển SAP
                //continue;

                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(zMes);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    //Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "PB",
                        FuntionnName = "Phân bổ",
                        RequestSAP = jsonString,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = SYSTEM
                    });

                    await _context.SaveChangesAsync();

                    //List response to SAP
                    if (resSAPs.Any())
                    {
                        //Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            //Danh sách msg lỗi
                            var msgErrArr = resSAPs.Select(x => x.MESSAGE).ToArray();
                            var msgError = msgErrArr.Any() ? string.Join(", ", msgErrArr) : null;
                            //return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });

                            //$"SAP Error: {msgError}".Dump("ERROR");

                            continue;
                        }

                        _context.WarehouseExportModel.AddRange(listWExport);
                        await _context.SaveChangesAsync();

                        //"Phân bổ thành công".Dump("Success");
                        successLine += 1;
                    }
                }
                catch (Exception ex)
                {
                    //$"Lỗi SAP: {ex.Message}".Dump("ERROR");
                    Console.WriteLine("Lỗi SAP khi phân bổ:" + ex.Message);
                    continue;
                }
                //var listDaPhanBo = filteredItemsRemoveDuplicated.Dump("Đã phân bổ");


                // 4. Test input lên SAP từ web phân bổ
                // 5. Test input lên SAP code mới giống web phân bổ
                // 6. Phân bổ thành công thêm records vào WarehouseExportModel IsDaPhanBo = 1 để hiển thị đã phân bổ
            }

            //totalLine.Dump("Total");
            //successLine.Dump("Thành công");
            var failedLine = totalLine - successLine;
            //failedLine.Dump("Thất bại");

            var log = new AllocateMaterialLogModel
            {
                AllocateDate = allocateDate.ToString("dd/MM/yyyy"),
                CreateTime = DateTime.Now,
                Description = $"{request.Plant} | Tổng line: {totalLine}, thành công: {successLine}, thất bại: {failedLine}"
            };

            _context.AllocateMaterialLogModel.Add(log);
            await _context.SaveChangesAsync();
        }

        private class AllocateMaterialRequest
        {
            public AllocateMaterialRequest()
            {
            }

            public DateTime AllocateDate { get; set; }
            public string Plant { get; set; }
        }
    }
}
