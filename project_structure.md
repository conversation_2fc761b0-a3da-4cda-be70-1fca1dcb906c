# Flutter Project Structure

## Main Project Structure
- `lib/`: Contains the main Dart code for the Flutter application
- `android/` and `ios/`: Platform-specific code for Android and iOS
- `assets/`: Contains images, fonts, and other static resources
- `test/`: Contains test files for the application
- `docs/` and `documentation/`: Project documentation
- `pubspec.yaml`: Flutter project configuration file that defines dependencies
- `analysis_options.yaml`: Configuration for Dart code analysis
- `build/`: Generated build files
- `jks/`: Contains keystore files for Android signing

## Lib Directory Structure

### Root Files
- `constants.dart`: Global constants for the application
- `main.dart`: Entry point of the application

### Directories
- **data/**: Contains data-related files
  - `dummy_data.dart`: Mock data for development/testing

- **element/**: UI components and widgets
  - Various button components
  - Form elements
  - Custom UI components for specific features
  - Loading screens and error views

- **model/**: Data models
  - API response models
  - Business logic models
  - Data transfer objects
  - Many models for specific features (QC, warehouse, inventory, etc.)

- **page/**: Screen implementations
  - Main screens of the application
  - Feature-specific screens organized by functionality
  - Sub-directories for complex features:
    - `Downtime/`
    - `KiemTraChatLuong/`
    - `MaintenanceOrder/`
    - `TyLeTieuHao/`

- **repository/**: Data access layer
  - `api/`: API communication
  - `function/`: Repository functions
  - `commatextInputFormatter.dart`: Text formatting utilities
  - `showDateTime.dart`: Date/time display utilities

- **route/**: Navigation configuration
  - `route.dart`: Route definitions

- **screenArguments/**: Arguments for screen navigation
  - Various argument classes for different screens

- **service/**: Application services
  - `globalValue.dart`: Global state/values
  - `navigatorService.dart`: Navigation service

- **Storage/**: Data persistence
  - `storageSecureStorage.dart`: Secure storage implementation
  - `storageSharedPreferences.dart`: SharedPreferences implementation

- **urlApi/**: API endpoint definitions
  - `urlApi.dart`: API URLs

- **utils/**: Utility functions and helpers
  - `appConfig.dart`: Application configuration
  - `input_formatters.dart`: Input formatting utilities
  - `numberHelper.dart`: Number manipulation utilities
  - `status_utils.dart`: Status-related utilities
  - `ui_helpers.dart` and `ui_utils.dart`: UI utility functions

- **Widget/**: Reusable widgets
  - `container/`: Container widgets
  - `dialogWidget/`: Dialog widgets
  - `errorWidget.dart`: Error display widgets
  - `modalBottomSheet/`: Bottom sheet widgets

## Application Overview
This appears to be a warehouse/inventory management application with quality control features. The application includes functionality for:

- Warehouse transfers
- Inventory management
- Quality control
- Material tracking
- Production management
- QR code scanning for various operations
- Reporting and statistics