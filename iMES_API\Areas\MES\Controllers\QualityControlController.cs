using AutoMapper;
using Azure;
using iMES_API.Areas.MES.Models;
using iMES_API.ServiceExtensions;
using ISD.API.Constant;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public partial class QualityControlController : ControllerBaseAPI
    {
        private readonly IUploadFilesLibrary _uploadFilesLibrary;
        private readonly IConfiguration _configuration;

        // declare _mapper as AutoMapper
        private readonly IMapper _mapper;

        public QualityControlController(IUploadFilesLibrary uploadFilesLibrary, IConfiguration configuration, IMapper mapper)
        {
            _uploadFilesLibrary = uploadFilesLibrary;
            _configuration = configuration;
            _mapper = mapper;
        }

        #region GĐ1 Kiểm tra chất lượng - Lấy thông tin 
        /// <summary>API "Kiểm tra chất lượng" - Lấy thông tin </summary>
        /// <param name="QualityControlId"></param>
        /// <param name="RawMaterialCardId"></param>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/QualityControl/QualityControl?QualityControlId={QualityControlId}
        ///     Params: 
        ///             + version : 1
        ///             + QualityControlTicketId  : e59103f5-e697-4bb8-9010-c5b1a62ffb3d
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "qualityControlVm": {
        ///         "productAttribute": "0101.1",
        ///         "qualityControlModel": "e59103f5-e697-4bb8-9010-c5b1a62ffb3d",
        ///         "qualityControlCode": 6902,
        ///         "saleOrgCode": "1000",
        ///         "storeName": "1000 | Công Ty Cổ Phần Tập Đoàn Kỹ Nghệ Gỗ Trường Thành",
        ///         "workShopCode": "20000050",
        ///         "workShopName": "20000050 | Phân xưởng Giường - Tủ 3",
        ///         "workCenterCode": "LRN",
        ///         "workCenterName": "LRN | Lắp Ráp - Hoàn Thiện",
        ///         "profileCode": "0020010615",
        ///         "profileName": "0020010615 | CBH(CB2)",
        ///         "lsxdt": "DT-341-21-CB2",
        ///         "lsxsap": "600003113",
        ///         "dsx": "DT-341-21-CB2-D1",
        ///         "productCode": "400011295",
        ///         "productName": "400011295 | Đầu thấp+vai+dạt giường Bodie WO Twin",
        ///         "customerReference": "6123c8ce-31b1-4721-a9a5-cbcd4044843d",
        ///         "confirmDate": "2022-05-09T00:00:00",
        ///         "quantityConfirm": null,
        ///         "qualityDate": null,
        ///         "qualityChecker": null,
        ///         "qcSaleEmployee": " | ",
        ///         "inspectionLotQuantity": null,
        ///         "environmental": null,
        ///         "status": false,
        ///         "result": null,
        ///         "qualityType": null,
        ///         "po": null,
        ///         "unit": "Cụm",
        ///         "qty": 200,
        ///         "file": null,
        ///         "errorViewModel": [],
        ///         "qualityControlInformationViewModel": [],
        ///         "fileViewModel": [],
        ///         "qualityControlDetailViewModel": null
        ///       },
        ///       "resultList": [
        ///         {
        ///          "catalogCode": "QualityControl_Result_Pass",
        ///           "catalogText_vi": "Pass"
        ///         },
        ///         {
        ///           "catalogCode": "QualityControl_Result_Fail",
        ///           "catalogText_vi": "Fail"
        ///         }
        ///       ],
        ///       "qualityTypeList": [
        ///         {
        ///           "catalogCode": "Material",
        ///           "catalogText_vi": "Material"
        ///         },
        ///         {
        ///           "catalogCode": "Inline",
        ///           "catalogText_vi": "Inline"
        ///         },
        ///         {
        ///           "catalogCode": "Midline",
        ///           "catalogText_vi": "Midline"
        ///         },
        ///         {
        ///           "catalogCode": "Final",
        ///           "catalogText_vi": "Final"
        ///         }
        ///       ],
        ///       "qualityCheckerList": [
        ///         {
        ///           "accountId": "8d3993a5-bfff-41a0-ae58-3136aa2171a3",
        ///           "salesEmployeeName": "Qc01"
        ///         },
        ///         {
        ///           "accountId": "5afd3545-bed8-4545-8b74-b3fd6245c625",
        ///           "salesEmployeeName": "Qc02"
        ///         },
        ///         {
        ///           "accountId": "c213379d-1512-4f47-b2d0-e074f9102708",
        ///           "salesEmployeeName": "Qc03"
        ///         },
        ///       ],
        ///       "testMethodList": [
        ///         {
        ///           "catalogCode": "AQL",
        ///           "catalogText_vi": "AQL"
        ///         },
        ///        {
        ///           "catalogCode": "10",
        ///           "catalogText_vi": "10%"
        ///         },
        ///         {
        ///           "catalogCode": "100",
        ///           "catalogText_vi": "100%"
        ///         }
        ///       ],
        ///       "samplingLevelList": [
        ///         {
        ///           "catalogCode": "S-1",
        ///           "catalogText_vi": "S-1"
        ///         },
        ///         {
        ///           "catalogCode": "S-2",
        ///           "catalogText_vi": "S-2"
        ///         },
        ///         {
        ///           "catalogCode": "S-3",
        ///           "catalogText_vi": "S-3"
        ///         },
        ///         {
        ///           "catalogCode": "S-4",
        ///           "catalogText_vi": "S-4"
        ///         },
        ///         {
        ///           "catalogCode": "I",
        ///           "catalogText_vi": "I"
        ///         },
        ///         {
        ///           "catalogCode": "II",
        ///           "catalogText_vi": "II"
        ///         },
        ///         {
        ///           "catalogCode": "III",
        ///           "catalogText_vi": "III"
        ///         },
        ///         {
        ///           "catalogCode": "OTHER",
        ///           "catalogText_vi": "Khác"
        ///         }
        ///       ],
        ///       "qualityControlInformationIdList": [
        ///         {
        ///           "id": "0ff9c910-22cb-45d0-af7e-e1278480f926",
        ///           "name": "1006 | Hình Ảnh SP/Kích thước tổng thể SP",
        ///           "workCenterCode": "LRN"
        ///         },
        ///         {
        ///           "id": "ca836365-88fc-4f84-b0fe-0bb8ea519d20",
        ///           "name": "1007 | Keo lắp ráp/ Assem bly glue",
        ///           "workCenterCode": "LRN"
        ///         },
        ///         {
        ///           "id": "b151bfcd-7a75-488d-8acb-e4118a7e0cff",
        ///           "name": "1008 | Kiểm tra chi tiết/Details checking",
        ///           "workCenterCode": "LRN"
        ///         },
        ///        {
        ///           "id": "23117f94-01db-4a47-a235-ad32dc369683",
        ///           "name": "1009 | Kích thước tổng thể /Overall size check",
        ///           "workCenterCode": "LRN"
        ///         },
        ///         {
        ///           "id": "06b8640a-ee89-4b3f-9e4e-7e56229770f5",
        ///           "name": "1010 | Kiểm tra so PPS/ PPS check",
        ///           "workCenterCode": "LRN"
        ///         },
        ///         {
        ///           "id": "86f7201d-67c5-4aa6-b37a-b7a06c56305a",
        ///           "name": "1011 | Độ ẩm/Moisture content",
        ///           "workCenterCode": "LRN"
        ///         },
        ///         {
        ///           "id": "a0d75aa5-9311-4cbd-804f-34b05f266ff3",
        ///           "name": "1012 | Test  cấu trúc/Structural Test",
        ///           "workCenterCode": "LRN"
        ///         }
        ///       ],
        ///       "errorList": [
        ///         {
        ///           "catalogCode": "Q001",
        ///           "catalogText_vi": "Q001 | Mốc"
        ///         },
        ///         {
        ///           "catalogCode": "Q002",
        ///           "catalogText_vi": "Q002 | Nứt tét"
        ///         },
        ///         {
        ///           "catalogCode": "Q003",
        ///           "catalogText_vi": "Q003 | Nứt ứng xuất"
        ///         },
        ///         {
        ///           "catalogCode": "Q004",
        ///           "catalogText_vi": "Q004 | Nứt tét răng mặt"
        ///         }
        ///       ]
        ///     }
        ///     
        ///</remarks>

        #endregion

        [HttpGet("QualityControl")]
        public IActionResult GetQualityControl(Guid? QualityControlId, Guid? RawMaterialCardId)
        {

            // 1. Nếu là BTP thì input là QualityControlId
            // 2. Nếu là NVL thì input là RawMaterialCardId

            //Get lệnh QC theo barcode NVL
            if (RawMaterialCardId.HasValue)
            {
                //Lệnh QC theo id thẻ treo
                var qcIdBarcode = _context.QualityControlModel.FirstOrDefault(x => x.CustomerReference == RawMaterialCardId)?.QualityControlId;

                //Check tồn tại lệnh QC
                if (!qcIdBarcode.HasValue)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Lệnh QC của thẻ treo") });

                //Lệnh QC Id
                QualityControlId = qcIdBarcode;
            }

            // Ret data
            var viewModel = new QualityControlViewModel();

            // Check nếu có ref tới RawMaterialCardModel
            // TRUE: thì là loại NVL
            // False: thì là loại BTP
            var isExistsRawMaterial = (from qc in _context.QualityControlModel
                                       join rawMtl in _context.RawMaterialCardModel on qc.CustomerReference equals rawMtl.RawMaterialCardId

                                       where qc.QualityControlId == QualityControlId

                                       select qc).FirstOrDefault();

            // Không tồn tại => Loại BTP
            if (isExistsRawMaterial == null)
            {
                viewModel = _unitOfWork.QualityControlRepository.GetById(QualityControlId);

                if (viewModel == null)
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Không tìm thấy phiếu quản lý chất lượng",
                    });
                }
            }
            // Có tồn tại => Loại NVL
            else
            {
                viewModel = _unitOfWork.QualityControlRepository.GetNVLById(QualityControlId);
                if (viewModel == null)
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Không tìm thấy phiếu quản lý chất lượng",
                    });
                }
            }


            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType);
            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);


            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);


            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);

            var HangMucKiemTraList = new List<QualityControlDropdownVm>();


            if (viewModel.QCType == "NVL")
            {
                HangMucKiemTraList = _context.QualityControlInformationModel.Where(q => q.Code < 2000).OrderBy(x => x.Code).Select(x => new QualityControlDropdownVm
                {
                    Id = x.Id,
                    Name = x.Code + " | " + x.Name
                }).ToList();
            }
            else
            {
                // Thông tin kiểm tra 
                HangMucKiemTraList = (from p in _context.QualityControlInformationModel
                                      from w in p.WorkCenterCode.DefaultIfEmpty()
                                      where p.Code < 2000
                                      orderby p.Code ascending
                                      select new QualityControlDropdownVm
                                      {
                                          Id = p.Id,
                                          Name = p.Code + " | " + p.Name,
                                          WorkCenterCode = w.WorkCenterCode
                                      }).ToList();
            }



            //if (viewModel != null)
            //{
            //    HangMucKiemTraList = HangMucKiemTraList.Where(x => x.WorkCenterCode == viewModel.WorkCenterCode).ToList();
            //}

            // danh sách lỗi
            // var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
            //                                              .Select(x => new CatalogDropdownVm
            //                                              {
            //                                                  CatalogCode = x.CatalogCode,
            //                                                  CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
            //                                              })
            //                                              .ToList();
            var ErrorList = GetErrorList();

            if (viewModel.SoLuongNhapKho == null && viewModel.SoLuongBlock == null && viewModel.SoLuongTraVe == null)
            {
                //viewModel.SoLuongNhapKho = 
                var palletNVL = _context.RawMaterialCardModel.Include(x => x.RawMaterial_PurchaseOrderDetail_Mapping)
                    .FirstOrDefault(n => n.RawMaterialCardId == viewModel.CustomerReference);
                if (palletNVL != null)
                {
                    viewModel.SoLuongNhapKho = palletNVL?.RawMaterial_PurchaseOrderDetail_Mapping.Sum(x => x.Quantity);
                    viewModel.SoLuongBlock = 0;
                    viewModel.SoLuongTraVe = 0;
                }
            }

            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,
                resultList = ResultList,
                qualityTypeList = QualityTypeList,
                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,
                limitList = QCLimit,

                qualityControlInformationIdList = HangMucKiemTraList,

                errorList = ErrorList
            });
        }


        private DataSet FetchHangTagData(string CustomerReference, string BatchPrinting, bool? isCum)
        {
            DataSet ds = new DataSet();
            //string constr = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                using (SqlCommand cmd = new SqlCommand("[MES].[GetHangTag]", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;

                    using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                    {
                        sda.SelectCommand.Parameters.AddWithValue("@CustomerReference", CustomerReference ?? (object)DBNull.Value);
                        sda.SelectCommand.Parameters.AddWithValue("@BatchPrinting", BatchPrinting ?? (object)DBNull.Value);
                        sda.SelectCommand.Parameters.AddWithValue("@IsCum", isCum ?? (object)DBNull.Value);
                        sda.Fill(ds);
                        ds.Tables[0].TableName = "HangTag";
                        ds.Tables[1].TableName = "HangTagDetail";

                        ds.Relations.Add(
                          "HangTag_HangTagDetail",
                          ds.Tables["HangTag"].Columns["HangTagId"],
                          ds.Tables["HangTagDetail"].Columns["HangTagId"]
                        );
                    }
                }
            }

            return ds;
        }

        // New for KCS, QC Final
        [HttpGet("QualityControl2")]
        public async Task<IActionResult> GetQualityControl2Async(Guid? QualityControlId, Guid? RawMaterialCardId, Guid? HangTagId, Guid? QualityControlDetailId)
        {

            // 1. Nếu là BTP thì input là QualityControlId
            // 2. Nếu là NVL thì input là RawMaterialCardId

            // 3. Nếu là kiểm tra trên chuyền thì QualityControlId là  HangTagId

            //Get lệnh QC theo barcode NVL
            //if (RawMaterialCardId.HasValue)
            //{
            //    //Lệnh QC theo id thẻ treo
            //    var qcIdBarcode = _context.QualityControlModel.FirstOrDefault(x => x.CustomerReference == RawMaterialCardId)?.QualityControlId;

            //    //Check tồn tại lệnh QC
            //    if (!qcIdBarcode.HasValue)
            //        return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Lệnh QC của thẻ treo") });

            //    //Lệnh QC Id
            //    QualityControlId = qcIdBarcode;
            //}


            // Tìm QualityControlId by HangTagId

            // HangTagModel
            //   HangTagId
            //   CustomerReference: 106d3085-c5dd-45c7-8e0a-afc7a8345c16
            //   ProductAttribute: 0101.1.1

            // ThucThiLenhSanXuatModel
            //   ParentTaskId: 106d3085-c5dd-45c7-8e0a-afc7a8345c16 (CustomerReference)
            //   TaskId: c42a4401-0190-419d-89c1-04aff6e75604
            //   Barcode: HangTagModel.HangTagId

            // QualityControlModel
            //   CustomerReference: c42a4401-0190-419d-89c1-04aff6e75604 (CustomerReference = ThucThiLenhSanXuatModel.TaskId)
            //   ProductAttribute: 0101.1.1


            // Ret data
            var viewModel = new QualityControlViewModel();

            // Check nếu có ref tới RawMaterialCardModel
            // TRUE: thì là loại NVL
            // False: thì là loại BTP
            //var isExistsRawMaterial = (from qc in _context.QualityControlModel
            //                           join rawMtl in _context.RawMaterialCardModel on qc.CustomerReference equals rawMtl.RawMaterialCardId

            //                           where qc.QualityControlId == QualityControlId



            //                           select qc).FirstOrDefault();

            ThucThiLenhSanXuatModel ttlsx = null;
            if (QualityControlId != null)
            {
                //var model = _context.QualityControlModel.FirstOrDefault(q => q.QualityControlId == QualityControlId);

                ttlsx = (from qc in _context.QualityControlModel
                         join ttl in _context.ThucThiLenhSanXuatModel on qc.CustomerReference equals ttl.TaskId
                         where qc.QualityControlId == QualityControlId
                         select ttl).FirstOrDefault();

                HangTagId = ttlsx.Barcode;

            }


            Guid? customerReference = null;
            string productAttribute = "";
            int? batchPrinting = null;

            if (HangTagId != null)
            {


                // Get QualityControl by HangTag

                var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == HangTagId);

                if (hangTag != null)
                {
                    customerReference = hangTag.CustomerReference;
                    productAttribute = hangTag.ProductAttribute;
                    batchPrinting = hangTag.BatchPrinting;

                }



                //var qualityControlModelTest = (from ht in _context.HangTagModel
                //                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                //                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                //                           where ht.HangTagId == HangTagId && qc.ProductAttribute == productAttribute
                //                               select qc).ToList();

                // Update 09/05/2025: lấy theo ProductAttribute trước, nếu ko lấy được thì lấy đại diện
                // Tiến update 1
                var qualityControlModel = (from ht in _context.HangTagModel
                                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                                           where ht.HangTagId == HangTagId
                                           && qc.ProductAttribute == productAttribute
                                           select qc).FirstOrDefault();

                if (qualityControlModel == null)
                {
                    // Update 21/04/2025: bỏ điều kiện qc.ProductAttribute == productAttribute
                    // Tiến update 1: lấy 1 cái đại diện trước, khi user chọn công đoạn thì sẽ load lại đúng
                    qualityControlModel = (from ht in _context.HangTagModel
                                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                                           where ht.HangTagId == HangTagId
                                           //&& qc.ProductAttribute == productAttribute
                                           select qc).FirstOrDefault();
                }



                if (qualityControlModel == null)
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Sản xuất chưa quét MES",
                    });
                }


                ttlsx = (from qc in _context.QualityControlModel
                         join ttl in _context.ThucThiLenhSanXuatModel on qc.CustomerReference equals ttl.TaskId
                         where qc.QualityControlId == qualityControlModel.QualityControlId
                         select ttl).FirstOrDefault();

                var qualityControlId = qualityControlModel.QualityControlId;

                if (QualityControlId != null)
                {
                    qualityControlId = QualityControlId.Value;
                }

                viewModel = _unitOfWork.QualityControlRepository.GetById2(qualityControlId, IdDetail: QualityControlDetailId);
                if (viewModel == null)
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Không tìm thấy phiếu kiểm tra chất lượng",
                    });
                }

                viewModel.HangTagId = HangTagId;


                //if (qualityControlModel.WorkShopCode != null)
                //{
                //    var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                //                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                //                     .FirstOrDefault();
                //} 

            }

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType);




            //var arrQCNghiemThu = new List<string>() {
            //    "Inline",
            //    "Inline hàng trắng",
            //    "Inline hàng màu",
            //    "Midline",
            //    "Final"
            //};

            // Update 1:
            // 1. Report Mẫu
            // 2. Inline (Hàng trắng)
            // 3. Enline/Midline
            // 4. Final
            var arrQCNghiemThu = new List<string>() {
                //"Report Mẫu",
                "Inline",
                "Inline hàng trắng",
                "Inline hàng màu",
                "Midline",
                "Final"
            };

            // Loại loại nghiệm thu (QAQC nghiệm thu Inline - Middle - Final)
            var NghiemThuTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType).Where(c => arrQCNghiemThu.Contains(c.CatalogCode)).ToList();


            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);


            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);


            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);


            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);


            var QCInfoList = new List<QualityControlDropdownVm>();

            var ListCongDoanNho = new List<CatalogDropdownVm>();
            var ListCongDoanLoi = new List<CatalogDropdownVm>();

            if (viewModel.QCType == "NVL")
            {
                QCInfoList = _context.QualityControlInformationModel.Where(q => q.Code < 2000).OrderBy(x => x.Code).Select(x => new QualityControlDropdownVm
                {
                    Id = x.Id,
                    Name = x.Code + " | " + x.Name
                }).ToList();
            }
            else
            {
                // Thông tin kiểm tra 
                QCInfoList = (from p in _context.QualityControlInformationModel
                              from w in p.WorkCenterCode.DefaultIfEmpty()
                              where p.Code < 2000
                              orderby p.Code ascending
                              select new QualityControlDropdownVm
                              {
                                  Id = p.Id,
                                  Name = p.Code + " | " + p.Name,
                                  WorkCenterCode = w.WorkCenterCode
                              }).ToList();


                //FetchHangTagData("", "1");


                if (customerReference != null)
                {

                    //if (batchPrinting == null)
                    //{
                    //    batchPrinting = 1;
                    //}

                    //var ds = FetchHangTagData(customerReference.ToString(), batchPrinting.ToString(), false);


                    //if (ds.Tables[1] != null)
                    //{

                    //    var congDoanListTemp = ds.Tables[1].AsEnumerable()
                    //            .Where(row => !string.IsNullOrEmpty(row.Field<string>("CongDoan"))).Select(row => row.Field<string>("CongDoan")).ToList();

                    //    //var congDoanListTempModified = congDoanListTemp
                    //    //    .Select(item => item.Split('|')[1].Trim()) // Split by '|' and take the second part, also remove any leading or trailing spaces
                    //    //    .ToList();

                    //    var congDoanListTempModified = congDoanListTemp
                    //        .Select(item =>
                    //        {
                    //            var splitItem = item.Split('|');
                    //            if (splitItem.Length == 1)
                    //            {
                    //                return splitItem[0].Trim();
                    //            }
                    //            else if (splitItem.Length >= 2)
                    //            {
                    //                return splitItem[1].Trim();
                    //            }

                    //            return string.Empty;
                    //        })
                    //        .ToList();

                    //    ListCongDoanNho = _context.RoutingModel
                    //            .Where(r => congDoanListTempModified.Contains(r.StepCode))
                    //            .Select(row => new CatalogDropdownVm
                    //            {
                    //                CatalogCode = row.StepCode,
                    //                CatalogText_vi = row.StepCode + " | " + row.StepName
                    //            })
                    //            .Distinct()
                    //            .ToList();

                    //    //ListCongDoanNho = ds.Tables[1].AsEnumerable()
                    //    //        .Where(row => !string.IsNullOrEmpty(row.Field<string>("CongDoan")))
                    //    //        .Select(row => new CatalogDropdownVm
                    //    //        {
                    //    //            CatalogCode = row.Field<string>("CongDoan"),
                    //    //            OrderIndex = row.Field<int?>("OrderIndex"),
                    //    //        }).ToList(); 

                    //}

                    var hangTagModel = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == HangTagId);

                    if (hangTagModel != null)
                    {
                        ListCongDoanNho = _unitOfWork.QualityControlRepository.GetCongDoanNho(viewModel.ProductCode, viewModel.SaleOrgCode, hangTagModel.ProductAttribute);
                    }

                    ListCongDoanLoi = _unitOfWork.QualityControlRepository.GetCongDoanLoi(viewModel.ProductCode, viewModel.SaleOrgCode);

                    var productTypeInfo = (from pm in _context.ProductModel
                                           join mtm in _context.MaterialTypeModel on pm.MTART equals mtm.MTART into grouping
                                           from mtm in grouping.DefaultIfEmpty()
                                           where pm.ProductCode == viewModel.ProductCode
                                           select new
                                           {
                                               ProductCode = pm.ProductCode,
                                               ProductName = pm.ProductName,
                                               MTART = pm.MTART,
                                               MTBEZ = mtm == null ? null : mtm.MTBEZ
                                           }).FirstOrDefault();

                    if (productTypeInfo != null)
                    {
                        viewModel.ProductType = productTypeInfo.MTART + " | " + productTypeInfo.MTBEZ;
                    }

                }

            }



            //if (viewModel != null)
            //{
            //    HangMucKiemTraList = HangMucKiemTraList.Where(x => x.WorkCenterCode == viewModel.WorkCenterCode).ToList();
            //}

            // danh sách lỗi
            var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
                                                         .Select(x => new CatalogDropdownVm
                                                         {
                                                             CatalogCode = x.CatalogCode,
                                                             CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
                                                         })
                                                         .ToList();
            // Update, get tình trạng môi truòng
            var ttmt = "";
            if (ttlsx != null)
            {
                var SO = ttlsx.Property1;
                var PO = viewModel.PO;


                if (!string.IsNullOrEmpty(SO))
                {

                    var ttmtModel = _context.SOTextHeader100Model.FirstOrDefault(s => s.SO == SO && s.TEXT_ID == "H022");

                    if (ttmtModel != null)
                    {
                        ttmt = ttmtModel.LONGTEXT;

                        viewModel.TinhTrangMoiTruong = ttmt;
                    }
                }

                viewModel.SO = SO;
            }

            // Update: màu hoàn thiện
            var mauHoanThien = "";
            //try
            //{
            //    using (var httpClient = new HttpClient())
            //    {
            //        var _mvcClientService = new MvcClientService(httpClient);
            //        mauHoanThien = await _mvcClientService.GetMauHoanThien(viewModel.ProductCode);

            //        viewModel.MauHoanThien = mauHoanThien;
            //    }
            //}
            //catch
            //{
            //    // Do nothing
            //}

            viewModel.MauHoanThien = GetMauHoanThien(viewModel.ProductCode);

            var CaNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(viewModel.SaleOrgCode);

            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,
                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                nghiemThuTypeList = NghiemThuTypeList, // new

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                congDoanNhoList = ListCongDoanNho,
                congDoanLoiList = ListCongDoanLoi,

                phuongAnXuLyList = PhuongAnXuLyList,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                caNhanGayLoiList = CaNhanGayLoiListAll

            });
        }

        [HttpGet("QualityControlQCMau")]
        public async Task<IActionResult> QualityControlQCMau(Guid? QualityControlId, string qualityType)
        {

            //Lấy danh sách mức độ lỗi 
            var DefectLevel = await _context.CatalogModel.Where(p => p.CatalogTypeCode == "DefectLevel")
                                                      .OrderBy(x => x.OrderIndex)
                                                      .Select(x => new CommonResponse
                                                      {
                                                          Key = x.CatalogCode,
                                                          Value = x.CatalogText_vi
                                                      }).ToListAsync();

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType); // Inline

            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);


            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);


            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);


            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);


            var QCInfoList = new List<QualityControlDropdownVm>();

            QCInfoList = (from p in _context.QualityControlInformationModel
                          from w in p.WorkCenterCode.DefaultIfEmpty()
                          where p.Code < 2000
                          orderby p.Code ascending
                          select new QualityControlDropdownVm
                          {
                              Id = p.Id,
                              Name = p.Code + " | " + p.Name,
                              WorkCenterCode = w.WorkCenterCode
                          }).ToList();

            // danh sách lỗi
            // var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
            //                                              .Select(x => new CatalogDropdownVm
            //                                              {
            //                                                  CatalogCode = x.CatalogCode,
            //                                                  CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
            //                                              })
            //                                              .ToList();
            var ErrorList = GetErrorList();

            var viewModel = _unitOfWork.QualityControlRepository.GetForQCMauById(QualityControlId);

            //var qualityControlModel = _context.QualityControlModel.FirstOrDefault(x => x.QualityControlId == QualityControlId);
            if (viewModel == null)
            {
                viewModel = new QualityControlViewModel();
                // Empty for new
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();
            }
            else
            {

                var latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == viewModel.QualityControlId)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();

                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel
                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    viewModel.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where p.QualityControlId == viewModel.QualityControlId
                                       && qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = viewModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi, 
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = viewModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                viewModel.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where p.QualityControlId == viewModel.QualityControlId
                                    && p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = viewModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, viewModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                viewModel.QualityControlInformation = checkedList;
                #endregion
            }

            // Hạng mục kiểm tra
            //var hangMucKiemTraOld = _context.QualityControlInformationModel
            //        .Where(p => p.QualityType == qualityType) //QualityTypes.QCMau
            //                                    .OrderBy(p => p.OrderIndex)
            //.ToList();



            // if QualityType == "QCMAU"
            // SP Mẫu: Giống Checklist Final
            // p.QualityType NGHIEMTHU
            // p.ItemType Final


            // if QualityType == "QCGIACONG"
            // BTP tại NGC: Giống checklist Inline (Hàng trắng)
            // p.QualityType NGHIEMTHU
            // p.ItemType Final
            // p.RoutingType HangTrang


            //var hangMucKiemTra = _context.QualityControlInformationModel
            //                            .Where(p => p.QualityType == QualityTypes.NghiemThu
            //                                        && p.ItemType.Contains("Final") ) // if not Inline, return true to not affect the result
            //                            .OrderBy(p => p.OrderIndex)
            //                            .ToList();

            //var hangMucKiemTra = _context.QualityControlInformationModel
            //                                            .Where(p => (qualityType == "QCMAU" && p.QualityType == QualityTypes.NghiemThu && p.ItemType == "Final")
            //                                                        || (qualityType == "QCGIACONG" && p.QualityType == QualityTypes.NghiemThu && p.ItemType.Contains("Inline")))
            //                                            .OrderBy(p => p.OrderIndex)
            //                                            .ToList();


            //// Còn KCS, ko hiện trên áp,nhưng sẽ xuất hiện trên báo cáo khi xuất ra
            //var hangMucKiemTra = _context.QualityControlInformationModel
            //                            .Where(p => p.QualityType == QualityTypes.Kcs
            //                                 && p.RoutingType.Contains(StepCode))
            //                            .OrderBy(p => p.OrderIndex)
            //                            .ToList();

            //// QAQC nghiệm thu
            //var hangMucKiemTra = _context.QualityControlInformationModel
            //                            .Where(p => p.QualityType == QualityTypes.NghiemThu
            //                                        && p.ItemType.Contains(qualityType.Contains("Inline") ? "Inline" : qualityType)
            //                                        && (qualityType.Contains("Inline") ? (qualityType == "Inline hàng trắng" ? p.RoutingType == "HangTrang" :
            //                                                                              qualityType == "Inline hàng màu" ? p.RoutingType == "HangMau" : true) : true)) // if not Inline, return true to not affect the result
            //                            .OrderBy(p => p.OrderIndex)
            //                            .ToList();


            var caNhanGayLoiList = GetCaNhanGayLoiListByPlant(viewModel.SaleOrgCode);

            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,


                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                phuongAnXuLyList = PhuongAnXuLyList,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                // New QCMau/QCGiaCong
                //hangMucKiemTra = hangMucKiemTra,
                defectLevel = DefectLevel,
                caNhanGayLoiList = caNhanGayLoiList

            });
        }


        [HttpGet("QualityControlQCHienTruong")]
        public async Task<IActionResult> QualityControlQCHienTruong(Guid? QualityControlId, string qualityType)
        {

            //Lấy danh sách mức độ lỗi 
            var DefectLevel = await _context.CatalogModel.Where(p => p.CatalogTypeCode.Contains("DefectLevel"))
                                                      .OrderBy(x => x.OrderIndex)
                                                      .Select(x => new CommonResponse
                                                      {
                                                          Key = x.CatalogCode,
                                                          Value = x.CatalogText_vi
                                                      }).ToListAsync();

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType); // Inline

            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);


            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);


            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);


            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);


            var QCInfoList = new List<QualityControlDropdownVm>();

            QCInfoList = (from p in _context.QualityControlInformationModel
                          from w in p.WorkCenterCode.DefaultIfEmpty()
                          where p.Code < 2000
                          orderby p.Code ascending
                          select new QualityControlDropdownVm
                          {
                              Id = p.Id,
                              Name = p.Code + " | " + p.Name,
                              WorkCenterCode = w.WorkCenterCode
                          }).ToList();

            // danh sách lỗi
            //var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
            //                                             .Select(x => new CatalogDropdownVm
            //                                             {
            //                                                 CatalogCode = x.CatalogCode,
            //                                                 CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
            //                                             })
            //                                             .ToList();

            var ErrorList = new List<CatalogDropdownVm>();

            try
            {
                ErrorList = GetErrorList("7"); // 7: Hiện trường
            }
            catch (Exception ex)
            {

                throw;
            }



            var viewModel = _unitOfWork.QualityControlRepository.GetForQCMauById(QualityControlId);

            //var qualityControlModel = _context.QualityControlModel.FirstOrDefault(x => x.QualityControlId == QualityControlId);
            if (viewModel == null)
            {
                viewModel = new QualityControlViewModel();
                // Empty for new
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();
            }
            else
            {

                var latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == viewModel.QualityControlId)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();

                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel
                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    viewModel.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where p.QualityControlId == viewModel.QualityControlId
                                       && qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = viewModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi, 
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = viewModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                viewModel.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where p.QualityControlId == viewModel.QualityControlId
                                    && p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = viewModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, viewModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                viewModel.QualityControlInformation = checkedList;
                #endregion
            }


            var hangMucKiemTra = _context.QualityControlInformationModel
                                                        .Where(p => p.QualityType == qualityType)
                                                        .OrderBy(p => p.OrderIndex)
                                                        .ToList();

            var caNhanGayLoiList = GetCaNhanGayLoiListByPlant(viewModel.SaleOrgCode);

            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,


                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                phuongAnXuLyList = PhuongAnXuLyList,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                // New QCMau/QCGiaCong
                hangMucKiemTra = hangMucKiemTra,
                defectLevel = DefectLevel,
                caNhanGayLoiList = caNhanGayLoiList

            });
        }


        [HttpGet("QualityControlQCSanPham")]
        public async Task<IActionResult> GetQualityControlQCSanPham(Guid? QualityControlId, string barcode, Guid? QualityControlDetailId)
        {

            var productBarcodeModel = _context.ProductBarcodeModel.FirstOrDefault(x => x.FriendlyCode == barcode || x.EAN13 == barcode);
            var isTTFBarcode = false;
            var PO = "";

            if (productBarcodeModel != null)
            {
                isTTFBarcode = true;
                PO = productBarcodeModel.PO;
            }

            //Lấy danh sách mức độ lỗi 
            var DefectLevel = await _context.CatalogModel.Where(p => p.CatalogTypeCode == "DefectLevel")
                                                      .OrderBy(x => x.OrderIndex)
                                                      .Select(x => new CommonResponse
                                                      {
                                                          Key = x.CatalogCode,
                                                          Value = x.CatalogText_vi
                                                      }).ToListAsync();

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType); // Inline

            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);


            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);


            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);


            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);


            var QCInfoList = new List<QualityControlDropdownVm>();

            QCInfoList = (from p in _context.QualityControlInformationModel
                          from w in p.WorkCenterCode.DefaultIfEmpty()
                          where p.Code < 2000
                          orderby p.Code ascending
                          select new QualityControlDropdownVm
                          {
                              Id = p.Id,
                              Name = p.Code + " | " + p.Name,
                              WorkCenterCode = w.WorkCenterCode
                          }).ToList();

            // danh sách lỗi
            // var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
            //                                              .Select(x => new CatalogDropdownVm
            //                                              {
            //                                                  CatalogCode = x.CatalogCode,
            //                                                  CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
            //                                              })
            //                                              .ToList();
            var ErrorList = GetErrorList();

            QualityControlViewModel viewModel = null;

            if (QualityControlId != null)
            {
                viewModel = _unitOfWork.QualityControlRepository.GetById2(QualityControlId, IdDetail: QualityControlDetailId);
            }
            else
            {
                viewModel = _unitOfWork.QualityControlRepository.GetById2(null, barcode: barcode, IdDetail: QualityControlDetailId);
            }

            //var qualityControlModel = _context.QualityControlModel.FirstOrDefault(x => x.QualityControlId == QualityControlId);
            if (viewModel == null)
            {
                viewModel = new QualityControlViewModel();
                // Empty for new
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();

                viewModel.ProductCode = productBarcodeModel != null ? productBarcodeModel.ProductCode : "";
                viewModel.SaleOrgCode = productBarcodeModel != null ? productBarcodeModel.Plant : "";
            }
            else
            {
                // Update 1 (11/02/2025): skip the check
                //var defaultStepCode = "SF-DGO";

                var latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == viewModel.QualityControlId
                                                            //&& x.StepCode == defaultStepCode
                                                            )
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();

                // Nếu không có làm QC công đoạn đóng gói thì lấy QC công đoạn mới nhất
                if (latestQualityControlDetail == null)
                {
                    latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == viewModel.QualityControlId)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();
                }

                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel
                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           CongDoanNho = dt.StepCode,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes ?? 1

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    viewModel.QualityControlDetail = detailViewModel;
                }
                #endregion

                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where p.QualityControlId == viewModel.QualityControlId
                                    && p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = viewModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, viewModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                viewModel.QualityControlInformation = checkedList;
                #endregion
            }

            // Hạng mục kiểm tra

            var hangMucKiemTra = _context.QualityControlInformationModel
                    .Where(p => p.QualityType == QualityTypes.QCSanPham)
                                                .OrderBy(p => p.OrderIndex)
                                                .ToList();


            var caNhanGayLoiList = GetCaNhanGayLoiListByPlant(viewModel.SaleOrgCode);

            var productTypeInfo = (from pm in _context.ProductModel
                                   join mtm in _context.MaterialTypeModel on pm.MTART equals mtm.MTART into grouping
                                   from mtm in grouping.DefaultIfEmpty()
                                   where pm.ProductCode == viewModel.ProductCode
                                   select new
                                   {
                                       ProductCode = pm.ProductCode,
                                       ProductName = pm.ProductName,
                                       MTART = pm.MTART,
                                       MTBEZ = mtm == null ? null : mtm.MTBEZ
                                   }).FirstOrDefault();

            if (productTypeInfo != null)
            {
                viewModel.ProductType = productTypeInfo.MTART + " | " + productTypeInfo.MTBEZ;
            }

            viewModel.MauHoanThien = GetMauHoanThien(viewModel.ProductCode);

            var congDoanNhoListTemp = (from r in _context.RoutingInventorModel
                                       join rm in _context.RoutingModel on r.ARBPL equals rm.StepCode
                                       where r.MATNR == viewModel.ProductCode && r.WERKS == viewModel.SaleOrgCode
                                       group r by new { r.WERKS, r.MATNR, r.ARBPL_SUB, r.ITMNO, r.MEINS, r.ARBPL, rm.StepName, rm.StepCode, rm.OrderIndex } into g
                                       select new
                                       {
                                           ARBPL = g.Key.ARBPL,
                                           StepName = g.Key.StepName,
                                           OrderIndex = g.Key.OrderIndex
                                       }).ToList();

            var congDoanList = congDoanNhoListTemp.OrderBy(c => c.OrderIndex).Distinct().Select(row => new CatalogDropdownVm
            {
                CatalogCode = row.ARBPL,
                CatalogText_vi = row.ARBPL + " | " + row.StepName
            }).ToList();

            var ListCongDoanLoi = _unitOfWork.QualityControlRepository.GetCongDoanLoi(viewModel.ProductCode, viewModel.SaleOrgCode);
            var ListCongDoanNho = ListCongDoanLoi;

            if (viewModel.QualityControlDetail != null)
            {
                // Fix routing not exists caused error
                var qcDetailCongDoanNho = viewModel.QualityControlDetail.CongDoanNho;
                if (qcDetailCongDoanNho != null)
                {
                    var qcDetailCongDoanNhoModel = ListCongDoanNho.FirstOrDefault(x => x.CatalogCode == qcDetailCongDoanNho);
                    if (qcDetailCongDoanNhoModel == null)
                    {
                        ListCongDoanNho.Add(new CatalogDropdownVm
                        {
                            CatalogCode = qcDetailCongDoanNho,
                            CatalogText_vi = qcDetailCongDoanNho
                        });
                    }
                }
            }



            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,


                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                phuongAnXuLyList = PhuongAnXuLyList,

                congDoanNhoList = ListCongDoanNho,
                congDoanLoiList = ListCongDoanLoi,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                // New QCMau/QCGiaCong
                hangMucKiemTra = hangMucKiemTra,
                defectLevel = DefectLevel,
                caNhanGayLoiList = caNhanGayLoiList,

                isTTFBarcode = isTTFBarcode,
                PO = PO

            });
        }

        // KCS, cong doan changed
        [HttpGet("CongDoanInfo")]
        public IActionResult GetCongDoanInfo(Guid? hangTagId, string StepCode, Guid? QualityControlDetailId)
        {

            var qualityControlModel = (from ht in _context.HangTagModel
                                       join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                                       join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                                       where ht.HangTagId == hangTagId && qc.StepCode == StepCode
                                       //&& qc.ProductAttribute == ht.ProductAttribute
                                       select qc).FirstOrDefault();

            // TODO: if not found then create new
            if (qualityControlModel == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Chưa confirm công đoạn " + StepCode,
                });
            }

            var congDoanInfo = new CongDoanInfoVm();

            QualityControlDetailModel latestQualityControlDetail = null;

            if (QualityControlDetailId != null)
            {
                latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlDetailId == QualityControlDetailId)
                                                        .FirstOrDefault();
            }
            else
            {
                latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == qualityControlModel.QualityControlId)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();
            }

            var storeModel = (from q in _context.QualityControlModel
                              join s in _context.StoreModel on q.SaleOrgCode equals s.SaleOrgCode
                              where q.QualityControlId == qualityControlModel.QualityControlId
                              select s).FirstOrDefault();

            if (storeModel != null)
            {
                congDoanInfo.SaleOrgCode = storeModel.SaleOrgCode;
                congDoanInfo.StoreName = storeModel.SaleOrgCode + " | " + storeModel.StoreName;
            }

            //_context.StoreModel.FirstOrDefault(x => x.StoreCode == qualityControlModel.StoreCode);

            if (latestQualityControlDetail != null) // proceed only if we got an item
            {


                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel

                                       join aTemp in _context.AccountModel on dt.QualityChecker equals aTemp.AccountId into aList
                                       from a in aList.DefaultIfEmpty()

                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QCSaleEmployee = a.EmployeeCode + " | " + a.FullName,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    congDoanInfo.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = qualityControlModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi,  
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = qualityControlModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                congDoanInfo.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = qualityControlModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, qualityControlModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                congDoanInfo.QualityControlInformation = checkedList;
                #endregion

            }


            congDoanInfo.QualityControl = qualityControlModel;


            var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                                     .FirstOrDefault();

            if (Workshop != null)
            {
                congDoanInfo.WorkShopCode = Workshop.WorkShopCode;
                congDoanInfo.WorkShopName = Workshop.WorkShopName;
            }

            var workshopCode = qualityControlModel.WorkShopCode;

            // Danh sách cá nhân gây lỗi thuộc phân xưởng của phiếu kiểm tra
            var salesEmployeeWorkshop =
                (from sem in _context.SalesEmployeeModel
                 join dm in _context.DepartmentModel on sem.DepartmentId equals dm.DepartmentId
                 join wsm in _context.WorkShopModel on dm.WorkShopId equals wsm.WorkShopId
                 where wsm.WorkShopCode == workshopCode
                 select new SalesEmployeeWorkshopVm
                 {
                     SalesEmployeeCode = sem.SalesEmployeeCode,
                     SalesEmployeeName = sem.SalesEmployeeName,
                     DepartmentCode = dm.DepartmentCode,
                     DepartmentName = dm.DepartmentName,
                     WorkShopCode = wsm.WorkShopCode,
                     WorkShopName = wsm.WorkShopName,
                     LevelCode = sem.LevelCode
                 }).Distinct().ToList();

            var caNhanGayLoiList = salesEmployeeWorkshop.Select(x => new CatalogDropdownVm
            {
                CatalogCode = x.SalesEmployeeCode,
                CatalogText_vi = x.SalesEmployeeCode + " | " + x.SalesEmployeeName
            }).ToList();

            congDoanInfo.SalesEmployeeWorkshopList = salesEmployeeWorkshop;

            var CaNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(qualityControlModel.SaleOrgCode);

            congDoanInfo.CaNhanGayLoiList = CaNhanGayLoiListAll;

            // Quản đốc
            var quanDocList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QuanDoc).ToList();
            // Tổ trưởng/kỹ thuật
            var toTruongList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.ToTruongKyThuat).ToList();
            // QA-QC
            var QAQCList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QAQC).ToList();
            // KCS
            var KCSList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.KCS).ToList();


            if (quanDocList.Any())
            {
                var quanDoc = quanDocList.FirstOrDefault();

                congDoanInfo.QuanDocCode = quanDoc.SalesEmployeeCode;
                congDoanInfo.QuanDocName = quanDoc.SalesEmployeeCode + " | " + quanDoc.SalesEmployeeName;
            }

            if (toTruongList.Any())
            {
                var toTruong = toTruongList.FirstOrDefault();

                congDoanInfo.ToTruongCode = toTruong.SalesEmployeeCode;
                congDoanInfo.ToTruongName = toTruong.SalesEmployeeCode + " | " + toTruong.SalesEmployeeName;
            }

            if (QAQCList.Any())
            {
                var QAQC = QAQCList.FirstOrDefault();

                congDoanInfo.QAQCCode = QAQC.SalesEmployeeCode;
                congDoanInfo.QAQCName = QAQC.SalesEmployeeCode + " | " + QAQC.SalesEmployeeName;
            }

            if (KCSList.Any())
            {
                var KCS = KCSList.FirstOrDefault();

                congDoanInfo.KCSCode = KCS.SalesEmployeeCode;
                congDoanInfo.KCSName = KCS.SalesEmployeeCode + " | " + KCS.SalesEmployeeName;
            }

            congDoanInfo.QuanDocList = quanDocList;
            congDoanInfo.ToTruongList = toTruongList;
            congDoanInfo.QAQCList = QAQCList;
            congDoanInfo.KCSList = KCSList;

            var stockReceivingModel = _context.StockReceivingDetailModel
                .Where(srdm => srdm.CustomerReference == qualityControlModel.CustomerReference && srdm.MovementType == "ADD")
                .OrderByDescending(srdm => srdm.CreateTime)
                .FirstOrDefault();

            if (stockReceivingModel != null)
            {

                var department = _context.DepartmentModel
                    .Where(d => d.DepartmentId == stockReceivingModel.DepartmentId)
                    .FirstOrDefault();

                if (department != null)
                {
                    congDoanInfo.DepartmentCode = department.DepartmentCode;
                    congDoanInfo.DepartmentName = department.DepartmentCode + " | " + department.DepartmentName;
                }
            }

            //// TODO: remove this
            //nghiemThuInfo.QuanDocName = "1002 | Tiến";
            //nghiemThuInfo.ToTruongName = "1003 | Tâm";
            //nghiemThuInfo.QAQCName = "1004 | Trang";
            //nghiemThuInfo.KCSName = "1005 | Thịnh";

            // Còn KCS, ko hiện trên áp,nhưng sẽ xuất hiện trên báo cáo khi xuất ra
            var hangMucKiemTra = _context.QualityControlInformationModel
                                        .Where(p => p.QualityType == QualityTypes.Kcs
                                             && p.RoutingType.Contains(StepCode))
                                        .OrderBy(p => p.OrderIndex)
                                        .ToList();


            congDoanInfo.HangMucKiemTraMasterData = hangMucKiemTra;

            return Ok(congDoanInfo);

        }

        [HttpGet("CongDoanInfoMauDauChuyen")]
        public IActionResult GetCongDoanInfoMauDauChuyen(string lsxSAP, string StepCode)
        {

            // Get quality control information for MAUDAUCHUYEN type using LSX SAP
            var qualityControlModel = (from qc in _context.QualityControlModel
                                       where 1 == 1
                                       && qc.LSXSAP == lsxSAP
                                       && qc.QualityType == "MAUDAUCHUYEN"
                                       && qc.StepCode == StepCode
                                       select qc).FirstOrDefault();


            if (qualityControlModel == null)
            {
                // Get information from LSX SAP to populate the QualityControlModel
                var ttlsxModel = (from ttl in _context.ThucThiLenhSanXuatModel
                                  where ttl.Summary == lsxSAP
                                  select ttl).FirstOrDefault();

                var taskCompanyInfoModel = ttlsxModel != null ? (from t in _context.TaskModel
                                                                 join tp in _context.TaskModel on t.ParentTaskId equals tp.TaskId
                                                                 join c in _context.CompanyModel on t.CompanyId equals c.CompanyId
                                                                 where t.TaskId == ttlsxModel.ParentTaskId
                                                                 select new { c, t, tp }).FirstOrDefault() : null;

                var productCode = ttlsxModel != null ? (from ttl in _context.ThucThiLenhSanXuatModel
                                                        join p in _context.ProductModel on ttl.ProductId equals p.ProductId
                                                        where ttl.Summary == lsxSAP
                                                        select p.ProductCode).FirstOrDefault() : null;

                var productModel = _context.ProductLatestModel.Where(p => p.ProductCode == productCode).FirstOrDefault();

                var productName = "";

                if (productModel != null)
                {
                    productCode = productModel.ProductCode;
                    productName = productModel.ProductName;
                }

                qualityControlModel = new QualityControlModel
                {
                    QualityControlId = Guid.NewGuid(),
                    LSXSAP = lsxSAP,
                    QualityType = "MAUDAUCHUYEN",
                    StepCode = StepCode,
                    SaleOrgCode = taskCompanyInfoModel?.c?.CompanyCode,
                    LSXDT = taskCompanyInfoModel?.t?.Property3,
                    ProductCode = productCode,
                    ProductName = productName,
                    CreateBy = CurrentUser.AccountId,
                    CreateTime = DateTime.Now
                };
                _context.QualityControlModel.Add(qualityControlModel);
                _context.SaveChanges();
            }

            var congDoanInfo = new CongDoanInfoVm();

            // Get the latest quality control detail for this step
            var latestQualityControlDetail = _context.QualityControlDetailModel
                                                    .Where(x => x.QualityControlId == qualityControlModel.QualityControlId
                                                           && x.StepCode == StepCode)
                                                    .OrderByDescending(x => x.QualityDate)
                                                    .FirstOrDefault();

            // Get store information
            var storeModel = (from q in _context.QualityControlModel
                              join s in _context.StoreModel on q.SaleOrgCode equals s.SaleOrgCode
                              where q.QualityControlId == qualityControlModel.QualityControlId
                              select s).FirstOrDefault();

            if (storeModel != null)
            {
                congDoanInfo.SaleOrgCode = storeModel.SaleOrgCode;
                congDoanInfo.StoreName = storeModel.SaleOrgCode + " | " + storeModel.StoreName;
            }

            if (latestQualityControlDetail != null)
            {
                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel
                                       join aTemp in _context.AccountModel on dt.QualityChecker equals aTemp.AccountId into aList
                                       from a in aList.DefaultIfEmpty()
                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QCSaleEmployee = a.EmployeeCode + " | " + a.FullName,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes,

                                           // Update 2: Barcode quét để làm QC Mẫu đầu chuyền
                                           Barcode = dt.Barcode

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    congDoanInfo.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = qualityControlModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi,  
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = qualityControlModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                congDoanInfo.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = qualityControlModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, qualityControlModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                congDoanInfo.QualityControlInformation = checkedList;
                #endregion

            }

            congDoanInfo.QualityControl = qualityControlModel;

            // Get workshop information
            var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                                     .FirstOrDefault();

            if (Workshop != null)
            {
                congDoanInfo.WorkShopCode = Workshop.WorkShopCode;
                congDoanInfo.WorkShopName = Workshop.WorkShopName;
            }

            var workshopCode = qualityControlModel.WorkShopCode;

            // Get sales employee workshop list for MAUDAUCHUYEN
            var salesEmployeeWorkshop = new List<SalesEmployeeWorkshopVm>();

            if (!string.IsNullOrEmpty(workshopCode))
            {
                salesEmployeeWorkshop =
                    (from sem in _context.SalesEmployeeModel
                     join dm in _context.DepartmentModel on sem.DepartmentId equals dm.DepartmentId
                     join wsm in _context.WorkShopModel on dm.WorkShopId equals wsm.WorkShopId
                     where wsm.WorkShopCode == workshopCode
                     select new SalesEmployeeWorkshopVm
                     {
                         SalesEmployeeCode = sem.SalesEmployeeCode,
                         SalesEmployeeName = sem.SalesEmployeeName,
                         DepartmentCode = dm.DepartmentCode,
                         DepartmentName = dm.DepartmentName,
                         WorkShopCode = wsm.WorkShopCode,
                         WorkShopName = wsm.WorkShopName,
                         LevelCode = sem.LevelCode
                     }).Distinct().ToList();
            }

            congDoanInfo.SalesEmployeeWorkshopList = salesEmployeeWorkshop;

            // Quản đốc
            var quanDocList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QuanDoc).ToList();
            // Tổ trưởng/kỹ thuật
            var toTruongList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.ToTruongKyThuat).ToList();
            // QA-QC
            var QAQCList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QAQC).ToList();
            // KCS
            var KCSList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.KCS).ToList();

            if (quanDocList.Any())
            {
                var quanDoc = quanDocList.FirstOrDefault();
                congDoanInfo.QuanDocCode = quanDoc.SalesEmployeeCode;
                congDoanInfo.QuanDocName = quanDoc.SalesEmployeeCode + " | " + quanDoc.SalesEmployeeName;
            }

            if (toTruongList.Any())
            {
                var toTruong = toTruongList.FirstOrDefault();
                congDoanInfo.ToTruongCode = toTruong.SalesEmployeeCode;
                congDoanInfo.ToTruongName = toTruong.SalesEmployeeCode + " | " + toTruong.SalesEmployeeName;
            }

            if (QAQCList.Any())
            {
                var QAQC = QAQCList.FirstOrDefault();
                congDoanInfo.QAQCCode = QAQC.SalesEmployeeCode;
                congDoanInfo.QAQCName = QAQC.SalesEmployeeCode + " | " + QAQC.SalesEmployeeName;
            }

            if (KCSList.Any())
            {
                var KCS = KCSList.FirstOrDefault();
                congDoanInfo.KCSCode = KCS.SalesEmployeeCode;
                congDoanInfo.KCSName = KCS.SalesEmployeeCode + " | " + KCS.SalesEmployeeName;
            }

            congDoanInfo.QuanDocList = quanDocList;
            congDoanInfo.ToTruongList = toTruongList;
            congDoanInfo.QAQCList = QAQCList;
            congDoanInfo.KCSList = KCSList;

            // Get department information
            var stockReceivingModel = _context.StockReceivingDetailModel
                .Where(srdm => srdm.CustomerReference == qualityControlModel.CustomerReference && srdm.MovementType == "ADD")
                .OrderByDescending(srdm => srdm.CreateTime)
                .FirstOrDefault();

            if (stockReceivingModel != null)
            {
                var department = _context.DepartmentModel
                    .Where(d => d.DepartmentId == stockReceivingModel.DepartmentId)
                    .FirstOrDefault();

                if (department != null)
                {
                    congDoanInfo.DepartmentCode = department.DepartmentCode;
                    congDoanInfo.DepartmentName = department.DepartmentCode + " | " + department.DepartmentName;
                }
            }

            // Get personal error list for MAUDAUCHUYEN
            var CaNhanGayLoiListAll = !string.IsNullOrEmpty(qualityControlModel.SaleOrgCode)
                ? GetCaNhanGayLoiListByPlant(qualityControlModel.SaleOrgCode)
                : new List<CatalogDropdownVm>();
            congDoanInfo.CaNhanGayLoiList = CaNhanGayLoiListAll;

            // Get checklist items for MAUDAUCHUYEN
            var hangMucKiemTra = _context.QualityControlInformationModel
                                        .Where(p => p.QualityType == "MAUDAUCHUYEN")
                                        .OrderBy(p => p.OrderIndex)
                                        .ToList()
                                        .Where(p => p.RoutingType == StepCode || p.RoutingType.Split(',').Contains(StepCode))
                                        .ToList();

            congDoanInfo.HangMucKiemTraMasterData = hangMucKiemTra;

            return Ok(congDoanInfo);
        }

        /// <summary>
        /// Get quality control information for sample types (Inline, Midline, Final)
        /// </summary>
        /// <param name="itemType">Sample type: Inline, Midline, or Final</param>
        /// <returns>Quality control information for the specified sample type</returns>
        [HttpGet("GetChecklistMau")]
        public IActionResult GetChecklistMau(string itemType)
        {
            try
            {
                // Get quality control information where quality type is QCMAU and item type matches the parameter
                var hangMucKiemTra = _context.QualityControlInformationModel
                                            .Where(p => p.QualityType == "QCMAU"
                                                 && p.ItemType == itemType)
                                            .OrderBy(p => p.OrderIndex)
                                            .ToList();

                // Return just the list of items directly
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = hangMucKiemTra
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.BadRequest,
                    Success = false,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// Get QC SP Info by QualityControlId and StepCode
        /// </summary>
        /// <param name="id"></param>
        /// <param name="StepCode"></param>
        /// <returns></returns>
        [HttpGet("CongDoanInfoSanPham")]
        public IActionResult GetCongDoanInfoSanPham(Guid? id, string StepCode)
        {
            var qualityControlId = id;

            var qualityControlModel = (from qc in _context.QualityControlModel
                                       join detail in _context.QualityControlDetailModel on qc.QualityControlId equals detail.QualityControlId
                                       where
                                                qc.QualityControlId == qualityControlId &&
                                                 //qc.QualityType == QualityTypes.QCSanPham &&
                                                 detail.StepCode == StepCode
                                       select qc
                                       ).FirstOrDefault();




            if (qualityControlModel == null)
            {

                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Chưa confirm công đoạn " + StepCode,
                });
            }

            var routing = StepCode;
            var PO = qualityControlModel.PO;

            var congDoanInfo = new CongDoanInfoVm();


            var latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == qualityControlModel.QualityControlId && x.StepCode == StepCode)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();
            var storeModel = (from q in _context.QualityControlModel
                              join s in _context.StoreModel on q.SaleOrgCode equals s.SaleOrgCode
                              where q.QualityControlId == qualityControlModel.QualityControlId
                              select s).FirstOrDefault();

            if (storeModel != null)
            {
                congDoanInfo.SaleOrgCode = storeModel.SaleOrgCode;
                congDoanInfo.StoreName = storeModel.SaleOrgCode + " | " + storeModel.StoreName;
            }

            //_context.StoreModel.FirstOrDefault(x => x.StoreCode == qualityControlModel.StoreCode);

            if (latestQualityControlDetail != null) // proceed only if we got an item
            {


                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel

                                       join aTemp in _context.AccountModel on dt.QualityChecker equals aTemp.AccountId into aList
                                       from a in aList.DefaultIfEmpty()

                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QCSaleEmployee = a.EmployeeCode + " | " + a.FullName,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    congDoanInfo.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where p.QualityControlId == qualityControlModel.QualityControlId
                                       && qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = qualityControlModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi,  
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = qualityControlModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                congDoanInfo.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where p.QualityControlId == qualityControlModel.QualityControlId
                                    && p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = qualityControlModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, qualityControlModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                congDoanInfo.QualityControlInformation = checkedList;
                #endregion

            }


            congDoanInfo.QualityControl = qualityControlModel;


            var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                                     .FirstOrDefault();

            if (Workshop != null)
            {
                congDoanInfo.WorkShopCode = Workshop.WorkShopCode;
                congDoanInfo.WorkShopName = Workshop.WorkShopName;
            }

            var workshopCode = qualityControlModel.WorkShopCode;

            var salesEmployeeWorkshop =
                (from sem in _context.SalesEmployeeModel
                 join dm in _context.DepartmentModel on sem.DepartmentId equals dm.DepartmentId
                 join wsm in _context.WorkShopModel on dm.WorkShopId equals wsm.WorkShopId
                 where wsm.WorkShopCode == workshopCode
                 select new SalesEmployeeWorkshopVm
                 {
                     SalesEmployeeCode = sem.SalesEmployeeCode,
                     SalesEmployeeName = sem.SalesEmployeeName,
                     DepartmentCode = dm.DepartmentCode,
                     DepartmentName = dm.DepartmentName,
                     WorkShopCode = wsm.WorkShopCode,
                     WorkShopName = wsm.WorkShopName,
                     LevelCode = sem.LevelCode
                 }).Distinct().ToList();

            var caNhanGayLoiList = salesEmployeeWorkshop.Select(x => new CatalogDropdownVm
            {
                CatalogCode = x.SalesEmployeeCode,
                CatalogText_vi = x.SalesEmployeeCode + " | " + x.SalesEmployeeName
            }).ToList();

            congDoanInfo.SalesEmployeeWorkshopList = salesEmployeeWorkshop;

            var CaNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(qualityControlModel.SaleOrgCode);

            congDoanInfo.CaNhanGayLoiList = CaNhanGayLoiListAll;

            // Quản đốc
            var quanDocList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QuanDoc).ToList();
            // Tổ trưởng/kỹ thuật
            var toTruongList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.ToTruongKyThuat).ToList();
            // QA-QC
            var QAQCList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QAQC).ToList();
            // KCS
            var KCSList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.KCS).ToList();


            if (quanDocList.Any())
            {
                var quanDoc = quanDocList.FirstOrDefault();

                congDoanInfo.QuanDocCode = quanDoc.SalesEmployeeCode;
                congDoanInfo.QuanDocName = quanDoc.SalesEmployeeCode + " | " + quanDoc.SalesEmployeeName;
            }

            if (toTruongList.Any())
            {
                var toTruong = toTruongList.FirstOrDefault();

                congDoanInfo.ToTruongCode = toTruong.SalesEmployeeCode;
                congDoanInfo.ToTruongName = toTruong.SalesEmployeeCode + " | " + toTruong.SalesEmployeeName;
            }

            if (QAQCList.Any())
            {
                var QAQC = QAQCList.FirstOrDefault();

                congDoanInfo.QAQCCode = QAQC.SalesEmployeeCode;
                congDoanInfo.QAQCName = QAQC.SalesEmployeeCode + " | " + QAQC.SalesEmployeeName;
            }

            if (KCSList.Any())
            {
                var KCS = KCSList.FirstOrDefault();

                congDoanInfo.KCSCode = KCS.SalesEmployeeCode;
                congDoanInfo.KCSName = KCS.SalesEmployeeCode + " | " + KCS.SalesEmployeeName;
            }

            congDoanInfo.QuanDocList = quanDocList;
            congDoanInfo.ToTruongList = toTruongList;
            congDoanInfo.QAQCList = QAQCList;
            congDoanInfo.KCSList = KCSList;

            var stockReceivingModel = _context.StockReceivingDetailModel
                .Where(srdm => srdm.CustomerReference == qualityControlModel.CustomerReference && srdm.MovementType == "ADD")
                .OrderByDescending(srdm => srdm.CreateTime)
                .FirstOrDefault();

            if (stockReceivingModel != null)
            {

                var department = _context.DepartmentModel
                    .Where(d => d.DepartmentId == stockReceivingModel.DepartmentId)
                    .FirstOrDefault();

                if (department != null)
                {
                    congDoanInfo.DepartmentCode = department.DepartmentCode;
                    congDoanInfo.DepartmentName = department.DepartmentCode + " | " + department.DepartmentName;
                }
            }

            //// TODO: remove this
            //nghiemThuInfo.QuanDocName = "1002 | Tiến";
            //nghiemThuInfo.ToTruongName = "1003 | Tâm";
            //nghiemThuInfo.QAQCName = "1004 | Trang";
            //nghiemThuInfo.KCSName = "1005 | Thịnh";

            // Còn KCS, ko hiện trên áp,nhưng sẽ xuất hiện trên báo cáo khi xuất ra
            var hangMucKiemTra = _context.QualityControlInformationModel
                                        .Where(p => p.QualityType == QualityTypes.Kcs
                                             && p.RoutingType.Contains(StepCode))
                                        .OrderBy(p => p.OrderIndex)
                                        .ToList();


            congDoanInfo.HangMucKiemTraMasterData = hangMucKiemTra;

            return Ok(congDoanInfo);

        }

        /// <summary>
        /// Get QC BTP Info by PO and StepCode
        /// </summary>
        /// <param name="id"></param>
        /// <param name="StepCode"></param>
        /// <returns></returns>
        [HttpGet("CongDoanInfoBTP")]
        public IActionResult CongDoanInfoBTP(string PO, string StepCode)
        {
            // Update 1 (2021-09-21): lấy thông tin QC công đoạn BTP
            var qualityControlModel = (from qc in _context.QualityControlModel
                                       join ttlsx in _context.ThucThiLenhSanXuatModel
                                       on qc.CustomerReference equals ttlsx.TaskId
                                       where ttlsx.Summary == PO
                                             && qc.StepCode == StepCode
                                             && qc.Result != null
                                       select qc)
                          .FirstOrDefault();

            if (qualityControlModel == null)
            {

                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = $"Công đoạn ${StepCode} chưa có thông tin QC",
                });
            }

            var routing = StepCode;

            var congDoanInfo = new CongDoanInfoVm();


            var latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == qualityControlModel.QualityControlId)
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();
            var storeModel = (from q in _context.QualityControlModel
                              join s in _context.StoreModel on q.SaleOrgCode equals s.SaleOrgCode
                              where q.QualityControlId == qualityControlModel.QualityControlId
                              select s).FirstOrDefault();

            if (storeModel != null)
            {
                congDoanInfo.SaleOrgCode = storeModel.SaleOrgCode;
                congDoanInfo.StoreName = storeModel.SaleOrgCode + " | " + storeModel.StoreName;
            }

            //_context.StoreModel.FirstOrDefault(x => x.StoreCode == qualityControlModel.StoreCode);

            if (latestQualityControlDetail != null) // proceed only if we got an item
            {


                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel

                                       join aTemp in _context.AccountModel on dt.QualityChecker equals aTemp.AccountId into aList
                                       from a in aList.DefaultIfEmpty()

                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QCSaleEmployee = a.EmployeeCode + " | " + a.FullName,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    congDoanInfo.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where
                                       //p.QualityControlId == qualityControlModel.QualityControlId && 
                                       qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = qualityControlModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi,  
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = qualityControlModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                congDoanInfo.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = qualityControlModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, qualityControlModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                congDoanInfo.QualityControlInformation = checkedList;
                #endregion

            }


            congDoanInfo.QualityControl = qualityControlModel;


            var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                                     .FirstOrDefault();

            if (Workshop != null)
            {
                congDoanInfo.WorkShopCode = Workshop.WorkShopCode;
                congDoanInfo.WorkShopName = Workshop.WorkShopName;
            }

            var workshopCode = qualityControlModel.WorkShopCode;

            var salesEmployeeWorkshop =
                (from sem in _context.SalesEmployeeModel
                 join dm in _context.DepartmentModel on sem.DepartmentId equals dm.DepartmentId
                 join wsm in _context.WorkShopModel on dm.WorkShopId equals wsm.WorkShopId
                 where wsm.WorkShopCode == workshopCode
                 select new SalesEmployeeWorkshopVm
                 {
                     SalesEmployeeCode = sem.SalesEmployeeCode,
                     SalesEmployeeName = sem.SalesEmployeeName,
                     DepartmentCode = dm.DepartmentCode,
                     DepartmentName = dm.DepartmentName,
                     WorkShopCode = wsm.WorkShopCode,
                     WorkShopName = wsm.WorkShopName,
                     LevelCode = sem.LevelCode
                 }).Distinct().ToList();

            var caNhanGayLoiList = salesEmployeeWorkshop.Select(x => new CatalogDropdownVm
            {
                CatalogCode = x.SalesEmployeeCode,
                CatalogText_vi = x.SalesEmployeeCode + " | " + x.SalesEmployeeName
            }).ToList();

            congDoanInfo.SalesEmployeeWorkshopList = salesEmployeeWorkshop;

            var CaNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(qualityControlModel.SaleOrgCode);

            congDoanInfo.CaNhanGayLoiList = CaNhanGayLoiListAll;

            // Quản đốc
            var quanDocList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QuanDoc).ToList();
            // Tổ trưởng/kỹ thuật
            var toTruongList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.ToTruongKyThuat).ToList();
            // QA-QC
            var QAQCList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QAQC).ToList();
            // KCS
            var KCSList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.KCS).ToList();


            if (quanDocList.Any())
            {
                var quanDoc = quanDocList.FirstOrDefault();

                congDoanInfo.QuanDocCode = quanDoc.SalesEmployeeCode;
                congDoanInfo.QuanDocName = quanDoc.SalesEmployeeCode + " | " + quanDoc.SalesEmployeeName;
            }

            if (toTruongList.Any())
            {
                var toTruong = toTruongList.FirstOrDefault();

                congDoanInfo.ToTruongCode = toTruong.SalesEmployeeCode;
                congDoanInfo.ToTruongName = toTruong.SalesEmployeeCode + " | " + toTruong.SalesEmployeeName;
            }

            if (QAQCList.Any())
            {
                var QAQC = QAQCList.FirstOrDefault();

                congDoanInfo.QAQCCode = QAQC.SalesEmployeeCode;
                congDoanInfo.QAQCName = QAQC.SalesEmployeeCode + " | " + QAQC.SalesEmployeeName;
            }

            if (KCSList.Any())
            {
                var KCS = KCSList.FirstOrDefault();

                congDoanInfo.KCSCode = KCS.SalesEmployeeCode;
                congDoanInfo.KCSName = KCS.SalesEmployeeCode + " | " + KCS.SalesEmployeeName;
            }

            congDoanInfo.QuanDocList = quanDocList;
            congDoanInfo.ToTruongList = toTruongList;
            congDoanInfo.QAQCList = QAQCList;
            congDoanInfo.KCSList = KCSList;

            var stockReceivingModel = _context.StockReceivingDetailModel
                .Where(srdm => srdm.CustomerReference == qualityControlModel.CustomerReference && srdm.MovementType == "ADD")
                .OrderByDescending(srdm => srdm.CreateTime)
                .FirstOrDefault();

            if (stockReceivingModel != null)
            {

                var department = _context.DepartmentModel
                    .Where(d => d.DepartmentId == stockReceivingModel.DepartmentId)
                    .FirstOrDefault();

                if (department != null)
                {
                    congDoanInfo.DepartmentCode = department.DepartmentCode;
                    congDoanInfo.DepartmentName = department.DepartmentCode + " | " + department.DepartmentName;
                }
            }

            //// TODO: remove this
            //nghiemThuInfo.QuanDocName = "1002 | Tiến";
            //nghiemThuInfo.ToTruongName = "1003 | Tâm";
            //nghiemThuInfo.QAQCName = "1004 | Trang";
            //nghiemThuInfo.KCSName = "1005 | Thịnh";

            // Còn KCS, ko hiện trên áp,nhưng sẽ xuất hiện trên báo cáo khi xuất ra
            var hangMucKiemTra = _context.QualityControlInformationModel
                                        .Where(p => p.QualityType == QualityTypes.Kcs
                                             && p.RoutingType.Contains(StepCode))
                                        .OrderBy(p => p.OrderIndex)
                                        .ToList();


            congDoanInfo.HangMucKiemTraMasterData = hangMucKiemTra;

            return Ok(congDoanInfo);

        }

        [HttpGet("NghiemThuInfo")]
        public IActionResult GetNghiemThuInfo(Guid? hangTagId, string qualityType, Guid? QualityControlDetailId)
        {


            // qualityType (in detail): Inline - Middle - Final

            //var qualityControlModel = (from ht in _context.HangTagModel
            //                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
            //                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
            //                           where ht.HangTagId == hangTagId
            //                           //&& qc.StepCode == StepCode
            //                           select qc).FirstOrDefault();

            if (qualityType.Contains("Inline") && !qualityType.Contains("Midline") && !qualityType.Contains("Final")) // Inline, Midline, Final
            {
                qualityType = "Inline";
            }

            var qualityControlModel = (from ttlsx in _context.ThucThiLenhSanXuatModel
                                       join qc in _context.QualityControlModel on ttlsx.TaskId equals qc.CustomerReference
                                       where ttlsx.Barcode == hangTagId
                                       select qc)
                                    .FirstOrDefault();

            if (qualityControlModel == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Chưa xác nhận công đoạn lớn",
                });
            }

            if (QualityControlDetailId.HasValue)
            {
                qualityControlModel = (from qc in _context.QualityControlModel
                                       join qcd in _context.QualityControlDetailModel
                                       on qc.QualityControlId equals qcd.QualityControlId
                                       where qcd.QualityControlDetailId == QualityControlDetailId
                                       select qc)
                                    .FirstOrDefault();

                if (qualityControlModel == null)
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Không tìm thấy thông tin QC",
                    });
                }
            }

            var nghiemThuInfo = new CongDoanInfoVm();


            QualityControlDetailModel latestQualityControlDetail = null;

            if (QualityControlDetailId != null)
            {
                latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlDetailId == QualityControlDetailId)
                                                        .FirstOrDefault();
            }
            else
            {
                latestQualityControlDetail = _context.QualityControlDetailModel
                                                        .Where(x => x.QualityControlId == qualityControlModel.QualityControlId
                                                                      && x.QualityType.Contains(qualityType))
                                                        .OrderByDescending(x => x.QualityDate)
                                                        .FirstOrDefault();
            }


            if (latestQualityControlDetail != null) // proceed only if we got an item
            {
                #region GetDetail
                var detailViewModel = (from dt in _context.QualityControlDetailModel
                                       join aTemp in _context.AccountModel on dt.QualityChecker equals aTemp.AccountId into aList
                                       from a in aList.DefaultIfEmpty()
                                           //Phương pháp kiểm tra
                                       join ppTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Method, CatalogCode = dt.TestMethod } equals new { CatalogTypeCode = ppTemp.CatalogTypeCode, CatalogCode = ppTemp.CatalogCode } into ppList
                                       from pp in ppList.DefaultIfEmpty()
                                           //Mức độ lấy mẫu
                                       join slTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.SamplingLevel, CatalogCode = dt.SamplingLevel } equals new { CatalogTypeCode = slTemp.CatalogTypeCode, CatalogCode = slTemp.CatalogCode } into slList
                                       from sl in slList.DefaultIfEmpty()
                                           //Kết quả mẫu
                                       join dtrTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = dt.Result } equals new { CatalogTypeCode = dtrTemp.CatalogTypeCode, CatalogCode = dtrTemp.CatalogCode } into dtrList
                                       from dtr in dtrList.DefaultIfEmpty()
                                       where dt.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                       select new QualityControlDetailViewModel()
                                       {
                                           QualityControlDetailId = dt.QualityControlDetailId,
                                           QualityControlId = dt.QualityControlId,
                                           //Phương thức
                                           TestMethod = pp.CatalogCode,

                                           LimitCritical = dt.LimitCritical,
                                           LimitHigh = dt.LimitHigh,
                                           LimitLow = dt.LimitLow,

                                           //Mức độ lấy mẫu
                                           SamplingLevel = sl != null ? sl.CatalogCode : "OTHER",
                                           SamplingLevelName = dt.SamplingLevel,
                                           //Mức chấp nhận
                                           AcceptableLevel = dt.AcceptableLevel,
                                           //Kết quả kiểm tra mẫu
                                           Result = dtr.CatalogCode,
                                           InspectionQuantity = dt.InspectionQuantity,

                                           TongSoSanPhamLoi = dt.TongSoSanPhamLoi,

                                           QualityChecker = dt.QualityChecker,
                                           QualityDate = dt.QualityDate,

                                           QCSaleEmployee = a.EmployeeCode + " | " + a.FullName,

                                           QuanDocCode = dt.QuanDocCode,
                                           ToTruongCode = dt.ToTruongCode,
                                           QAQCCode = dt.QAQCCode,
                                           KCSCode = dt.KCSCode,

                                           LoiNangChapNhan = dt.LoiNangChapNhan,
                                           LoiNheChapNhan = dt.LoiNheChapNhan,

                                           // Update 1: Lần kiểm tra
                                           CheckingTimes = dt.CheckingTimes

                                       }).FirstOrDefault();

                if (detailViewModel != null)
                {
                    nghiemThuInfo.QualityControlDetail = detailViewModel;
                }
                #endregion


                #region GetError
                var errorList = (from p in _context.QualityControl_Error_Mapping
                                 join erTemp in _context.CatalogModel
                                 on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode }
                                 equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
                                 from er in erList.DefaultIfEmpty()
                                 join qcd in _context.QualityControlDetailModel
                                 on p.QualityControlDetailId equals qcd.QualityControlDetailId
                                 // Thông tin vị trí lỗi
                                 join s in _context.StoreModel on p.SaleOrgErrorCode equals s.SaleOrgCode into sList
                                 from s1 in sList.DefaultIfEmpty() // Left Join for StoreModel
                                 join ws in _context.WorkShopModel on p.WorkshopErrorCode equals ws.WorkShopCode into wsList
                                 from ws1 in wsList.DefaultIfEmpty() // Left Join for WorkShopModel
                                 join d in _context.DepartmentModel on p.DepartmentErrorCode equals d.DepartmentCode into dList
                                 from d1 in dList.DefaultIfEmpty() // Left Join for DepartmentModel

                                 where
                                    //p.QualityControlId == qualityControlModel.QualityControlId && 
                                    qcd.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                 select new QualityControlErrorViewModel()
                                 {
                                     QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
                                     QualityControlId = qualityControlModel.QualityControlId,
                                     CatalogCode = p.CatalogCode,
                                     CatalogText_vi = er.CatalogText_vi,
                                     LevelError = p.LevelError,
                                     QuantityError = p.QuantityError,
                                     Notes = p.Notes,
                                     PersonCausedErrorCode = p.PersonCausedErrorCode,

                                     StepCodeError = p.StepCodeError ?? null, // CongDoanLoi, 
                                     PhuongAnXuLy = p.PhuongAnXuLy ?? null, // new
                                     SaleOrgErrorCode = s1 != null ? p.SaleOrgErrorCode + " | " + s1.StoreName : null, // NhaMayLoi,
                                     WorkshopErrorCode = ws1 != null ? p.WorkshopErrorCode + " | " + ws1.WorkShopName : null, // PhanXuongLoi, 
                                     DepartmentErrorCode = d1 != null ? p.DepartmentErrorCode + " | " + d1.DepartmentName : null, // ToChuyenLoi, 
                                     //QualityDate = qcd.QualityDate // this line is optional if you want to include the date in your ViewModel

                                     PersonCausedErrorCodeMany = p.PersonCausedErrorCodeMany,

                                     QuanDoc = p.QuanDoc,
                                     ToTruong = p.ToTruong,
                                     QAQC = p.QAQC,
                                     KCS = p.KCS,
                                 }).ToList();

                var qualityControlCode = qualityControlModel.QualityControlCode;
                var errorFileList = new List<FileAttachmentViewModel>();
                if (errorList != null && errorList.Count > 0)
                {
                    foreach (var item in errorList)
                    {
                        var commentFiles = (from p in _context.FileAttachmentModel
                                            join m in _context.QualityControl_Error_File_Mapping on p.FileAttachmentId equals m.FileAttachmentId
                                            where m.QuanlityControl_Error_Id == item.QuanlityControl_Error_Id
                                            && p.ObjectId == item.QuanlityControl_Error_Id
                                            select new FileAttachmentViewModel
                                            {
                                                FileAttachmentId = p.FileAttachmentId,
                                                ObjectId = p.ObjectId,
                                                FileAttachmentCode = p.FileAttachmentCode,
                                                FileAttachmentName = p.FileAttachmentName,
                                                FileExtention = p.FileExtention,
                                                FileUrl = "/Upload/QualityControl/" + qualityControlCode + "/Error/" + p.FileUrl,
                                                CreateTime = p.CreateTime
                                            }).ToList();
                        errorFileList.AddRange(commentFiles);
                    }
                }
                errorFileList = errorFileList.OrderBy(p => p.CreateTime).ToList();

                foreach (var item in errorList)
                {
                    foreach (var x in errorFileList)
                    {
                        if (item.QuanlityControl_Error_Id == x.ObjectId)
                        {
                            if (item.ErrorFileViewModel == null)
                            {
                                item.ErrorFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.ErrorFileViewModel.Add(x);
                        }
                    }
                }

                nghiemThuInfo.Error = errorList;
                #endregion


                #region QC Information
                var checkedList = (from p in _context.QualityControl_QCInformation_Mapping
                                       //join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode
                                   join w in _context.WorkCenterModel on p.WorkCenterCode equals w.WorkCenterCode into ps
                                   from w in ps.DefaultIfEmpty() //DefaultIfEmpty will ensure w is null if there are no matches
                                   join pp in _context.QualityControlInformationModel on p.QualityControlInformationId equals pp.Id
                                   where
                                   //p.QualityControlId == qualityControlModel.QualityControlId && 
                                   p.QualityControlDetailId == latestQualityControlDetail.QualityControlDetailId
                                   orderby p.OrderIndex ascending, pp.Code ascending
                                   select new QualityControlInformationMappingViewModel()
                                   {
                                       QualityControl_QCInformation_Id = p.QualityControl_QCInformation_Id,
                                       QualityControlId = qualityControlModel.QualityControlId,
                                       WorkCenterCode = p.WorkCenterCode,
                                       WorkCenterName = w.WorkCenterName,
                                       QualityControlInformationId = pp.Id,
                                       QualityControlInformationCode = pp.Code,
                                       QualityControlInformationName = pp.Name,
                                       Notes = p.Notes,
                                       OutcomeStatus = p.OutcomeStatus, // new
                                       SoSanPhamLoi = p.SoSanPhamLoi,
                                       QualityControlDetailId = p.QualityControlDetailId
                                   }).ToList();

                var checkedFileList = _unitOfWork.QualityControlRepository.GetCheckedFileList(checkedList, qualityControlModel.QualityControlCode);

                foreach (var item in checkedList)
                {
                    foreach (var x in checkedFileList)
                    {
                        if (item.QualityControl_QCInformation_Id == x.ObjectId)
                        {
                            if (item.CheckedFileViewModel == null)
                            {
                                item.CheckedFileViewModel = new List<FileAttachmentViewModel>();
                            }
                            item.CheckedFileViewModel.Add(x);
                        }
                    }
                }
                nghiemThuInfo.QualityControlInformation = checkedList;
                #endregion

            }

            //errorList = (from p in _context.QualityControl_Error_Mapping
            //             join erTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Error, CatalogCode = p.CatalogCode } equals new { CatalogTypeCode = erTemp.CatalogTypeCode, CatalogCode = erTemp.CatalogCode } into erList
            //             from er in erList.DefaultIfEmpty()
            //             where p.QualityControlId == qualityControlModel.QualityControlId
            //             select new QualityControlErrorViewModel()
            //             {
            //                 QuanlityControl_Error_Id = p.QuanlityControl_Error_Id,
            //                 QualityControlId = qualityControlModel.QualityControlId,
            //                 CatalogCode = p.CatalogCode,
            //                 CatalogText_vi = er.CatalogText_vi,
            //                 LevelError = p.LevelError,
            //                 QuantityError = p.QuantityError,
            //                 Notes = p.Notes,
            //                 PersonCausedErrorCode = p.PersonCausedErrorCode
            //             }).ToList();



            nghiemThuInfo.QualityControl = qualityControlModel;


            var Workshop = _context.WorkShopModel.Where(p => p.WorkShopCode == qualityControlModel.WorkShopCode)
                                     .Select(x => new { x.WorkShopCode, WorkShopName = x.WorkShopCode + " | " + x.WorkShopName })
                                     .FirstOrDefault();

            if (Workshop != null)
            {
                nghiemThuInfo.WorkShopCode = Workshop.WorkShopCode;
                nghiemThuInfo.WorkShopName = Workshop.WorkShopName;
            }

            var workshopCode = qualityControlModel.WorkShopCode;

            var workshopCodeArr = new string[] { };
            if (nghiemThuInfo.Error != null && nghiemThuInfo.Error.Count > 0)
            {
                workshopCodeArr = nghiemThuInfo.Error.Select(x =>
                {
                    if (x.WorkshopErrorCode == null)
                    {
                        return "";
                    }
                    return x.WorkshopErrorCode.Split('|')[0].Trim();
                }).ToArray();
            }

            var salesEmployeeWorkshop =
                (from sem in _context.SalesEmployeeModel
                 join dm in _context.DepartmentModel on sem.DepartmentId equals dm.DepartmentId
                 join wsm in _context.WorkShopModel on dm.WorkShopId equals wsm.WorkShopId
                 where workshopCodeArr.Contains(wsm.WorkShopCode) // wsm.WorkShopCode == workshopCode
                 select new SalesEmployeeWorkshopVm
                 {
                     SalesEmployeeCode = sem.SalesEmployeeCode,
                     SalesEmployeeName = sem.SalesEmployeeName,
                     DepartmentCode = dm.DepartmentCode,
                     DepartmentName = dm.DepartmentName,
                     WorkShopCode = wsm.WorkShopCode,
                     WorkShopName = wsm.WorkShopName,
                     LevelCode = sem.LevelCode
                 }).Distinct().ToList();

            var caNhanGayLoiList = salesEmployeeWorkshop.Select(x => new CatalogDropdownVm
            {
                CatalogCode = x.SalesEmployeeCode,
                CatalogText_vi = x.SalesEmployeeCode + " | " + x.SalesEmployeeName
            }).ToList();


            nghiemThuInfo.SalesEmployeeWorkshopList = salesEmployeeWorkshop;

            var CaNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(qualityControlModel.SaleOrgCode);

            nghiemThuInfo.CaNhanGayLoiList = CaNhanGayLoiListAll;

            // 1. Quản đốc
            var quanDocList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QuanDoc);
            // 2. Tổ trưởng/kỹ thuật
            var toTruongList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.ToTruongKyThuat);
            // 3. QA-QC
            var QAQCList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.QAQC);
            // 4. KCS
            var KCSList = salesEmployeeWorkshop.Where(s => s.LevelCode == (int)SalesEmployeeLevel.KCS);
            // 5. Công nhân



            if (workshopCode != null)
            {

                // A. Từ phân xưởng lấy qản đốc và QAQC
                var workshopEmployee = (from wsm in _context.WorkShopModel
                                        where wsm.WorkShopCode == workshopCode
                                        join sem1 in _context.SalesEmployeeModel
                                        on wsm.QuanDocCode equals sem1.SalesEmployeeCode into gj1
                                        from subsem1 in gj1.DefaultIfEmpty()
                                        join sem2 in _context.SalesEmployeeModel
                                        on wsm.QAQCCode equals sem2.SalesEmployeeCode into gj2
                                        from subsem2 in gj2.DefaultIfEmpty()
                                        select new
                                        {
                                            QuanDocCode = wsm.QuanDocCode,
                                            QuanDocName = subsem1 == null ? null : subsem1.SalesEmployeeName,
                                            QAQCCode = wsm.QAQCCode,
                                            QAQCName = subsem2 == null ? null : subsem2.SalesEmployeeName
                                        }).FirstOrDefault();


                if (workshopEmployee != null)
                {
                    // 1. Quản đốc
                    nghiemThuInfo.QuanDocCode = workshopEmployee.QuanDocCode;
                    nghiemThuInfo.QuanDocName = workshopEmployee.QuanDocCode + " | " + workshopEmployee.QuanDocName;

                    // 3. QAQC 
                    nghiemThuInfo.QAQCCode = workshopEmployee.QAQCCode;
                    nghiemThuInfo.QAQCName = workshopEmployee.QAQCCode + " | " + workshopEmployee.QAQCName;
                }


                //var qualityControlModel = (from ht in _context.HangTagModel
                //                           join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                //                           join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
                //                           where ht.HangTagId == hangTagId
                //                           //&& qc.StepCode == StepCode
                //                           select qc).FirstOrDefault();

                // B. Từ tổ lấy tổ trưởng
                var departmentModel = (from s in _context.StockReceivingDetailModel
                                       join d in _context.DepartmentModel on s.DepartmentId equals d.DepartmentId
                                       //join ttlsx in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
                                       where s.ProductAttributes == qualityControlModel.ProductAttribute
                                       && s.CustomerReference == qualityControlModel.CustomerReference
                                       //&& s.FromTime == fromTime
                                       //&& s.ToTime == toTime
                                       select d).FirstOrDefault();

                if (departmentModel != null)
                {

                    var workshopToTruong = (from s in _context.SalesEmployeeModel
                                            where s.SalesEmployeeCode == departmentModel.ToTruongCode
                                            select s).FirstOrDefault();

                    if (workshopToTruong != null)
                    {
                        // 2. Tổ trưởng
                        nghiemThuInfo.ToTruongCode = workshopToTruong.SalesEmployeeCode;
                        nghiemThuInfo.ToTruongName = workshopToTruong.SalesEmployeeCode + " | " + workshopToTruong.SalesEmployeeName;
                    }
                }

                // C. Từ công đoạn lấy KCS

            }





            //if (quanDocList.Any())
            //{
            //    var quanDoc = quanDocList.FirstOrDefault();

            //    nghiemThuInfo.QuanDocCode = quanDoc.SalesEmployeeCode;
            //    nghiemThuInfo.QuanDocName = quanDoc.SalesEmployeeCode + " | " + quanDoc.SalesEmployeeName;
            //}

            //if (toTruongList.Any())
            //{
            //    var toTruong = toTruongList.FirstOrDefault();

            //    nghiemThuInfo.ToTruongCode = toTruong.SalesEmployeeCode;
            //    nghiemThuInfo.ToTruongName = toTruong.SalesEmployeeCode + " | " + toTruong.SalesEmployeeName;
            //}

            //if (QAQCList.Any())
            //{
            //    var QAQC = QAQCList.FirstOrDefault();

            //    nghiemThuInfo.QAQCCode = QAQC.SalesEmployeeCode;
            //    nghiemThuInfo.QAQCName = QAQC.SalesEmployeeCode + " | " + QAQC.SalesEmployeeName;
            //}

            if (KCSList.Any())
            {
                var KCS = KCSList.FirstOrDefault();

                nghiemThuInfo.KCSCode = KCS.SalesEmployeeCode;
                nghiemThuInfo.KCSName = KCS.SalesEmployeeCode + " | " + KCS.SalesEmployeeName;
            }

            nghiemThuInfo.QuanDocList = quanDocList;
            nghiemThuInfo.ToTruongList = toTruongList;
            nghiemThuInfo.QAQCList = QAQCList;
            nghiemThuInfo.KCSList = KCSList;

            // QualityType: DAUVAO, KCS, NGHIEMTHU

            //var qualityTypeCheck = qualityType

            //var hangMucKiemTra = _context.QualityControlInformationModel.Where(p => p.QualityType == QualityTypes.NghiemThu
            //                                                                        && p.ItemType == qualityType) // Inline, Midline, Final
            //                                                            .OrderBy(p => p.OrderIndex)
            //                                                            .ToList();

            //var hangMucKiemTra = _context.QualityControlInformationModel
            //                        .Where(p => p.QualityType == QualityTypes.NghiemThu
            //                                    && p.ItemType.Contains(qualityType.Contains("Inline") ? "Inline" : qualityType) // Checks if qualityType contains "Inline", if so, use "Inline". If not, use qualityType.
            //                                    && p.RoutingType == (qualityType == "Inline hàng trắng" ? "HangTrang" :
            //                                                        qualityType == "Inline hàng màu" ? "HangMau" : null)) // Checks qualityType and sets RoutingType accordingly, if it doesn't match any conditions, set it as null.
            //                        .OrderBy(p => p.OrderIndex)
            //                        .ToList();

            var hangMucKiemTra = _context.QualityControlInformationModel
                                        .Where(p => p.QualityType == QualityTypes.NghiemThu
                                                    && p.ItemType.Contains(qualityType.Contains("Inline") ? "Inline" : qualityType)
                                                    && (qualityType.Contains("Inline") ? (qualityType == "Inline hàng trắng" ? p.RoutingType == "HangTrang" :
                                                                                          qualityType == "Inline hàng màu" ? p.RoutingType == "HangMau" : true) : true)) // if not Inline, return true to not affect the result
                                        .OrderBy(p => p.OrderIndex)
                                        .ToList();


            nghiemThuInfo.HangMucKiemTraMasterData = hangMucKiemTra;

            //// TODO: remove this
            //nghiemThuInfo.QuanDocName = "1002 | Tiến";
            //nghiemThuInfo.ToTruongName = "1003 | Tâm";
            //nghiemThuInfo.QAQCName = "1004 | Trang";
            //nghiemThuInfo.KCSName = "1005 | Thịnh";

            return Ok(nghiemThuInfo);

        }

        [HttpGet("GetQCSanPhamInfoByLSX")]
        public IActionResult GetQCSanPhamInfoByLSX([FromQuery] string lsxSAP, string barcode)
        {
            var qualityType = "QCSANPHAM";
            //var stepCode = "SF-DGO";

            // Update 1 (11/02/2025): skip the check
            //var stepCodes = new List<string> { "SF-DGO", "SF-BOC", "BOC", "DGO", "-DG", "TB-DG", "TB_DGO", "TB-DGO", "TB_DG" };

            var qualityControlModel = (from ttlsx in _context.ThucThiLenhSanXuatModel
                                       join qc in _context.QualityControlModel on ttlsx.TaskId equals qc.CustomerReference
                                       where ttlsx.Summary == lsxSAP
                                        && qc.Barcode == barcode
                                        && qc.QualityType == qualityType
                                       select qc)
                                .FirstOrDefault();

            var nghiemThuInfo = new GetQualityControlerReturnVm();
            var viewModel = new QualityControlViewModel();

            var congDoanInfo = new CongDoanInfoVm();

            ThucThiLenhSanXuatModel ttlsxModel = null;

            var plant = "";

            var productCode = "";

            // Khi user quét và xác nhận công đoạn SF-DGO thì sẽ có record trong ThucThiLenhSanXuatModel
            ttlsxModel = (from ttl in _context.ThucThiLenhSanXuatModel
                              //where ttl.Summary == lsxSAP && ttl.Property6 == stepCode
                          where ttl.Summary == lsxSAP
                          //&& stepCodes.Contains(ttl.Property6)
                          select ttl).FirstOrDefault();

            if (ttlsxModel == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Sản phẩm chưa xác nhận hoàn thành",
                });
            }

            var taskCompanyInfoModel = (from t in _context.TaskModel
                                        join tp in _context.TaskModel on t.ParentTaskId equals tp.TaskId
                                        join c in _context.CompanyModel on t.CompanyId equals c.CompanyId
                                        where t.TaskId == ttlsxModel.ParentTaskId
                                        select new { c, t, tp }).FirstOrDefault();

            plant = taskCompanyInfoModel?.c?.CompanyCode;

            // Not checked yet, NEW empty
            if (qualityControlModel == null)
            {
                viewModel = new QualityControlViewModel();
                // Empty for new
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();



                productCode = (from ttl in _context.ThucThiLenhSanXuatModel
                               join p in _context.ProductModel on ttl.ProductId equals p.ProductId
                               //where ttl.Summary == lsxSAP && ttl.Property6 == stepCode
                               where ttl.Summary == lsxSAP
                               //&& stepCodes.Contains(ttl.Property6)
                               select p.ProductCode).FirstOrDefault();

                // Plant
                if (taskCompanyInfoModel != null)
                {
                    viewModel.SaleOrgCode = taskCompanyInfoModel.c.CompanyCode;
                    viewModel.StoreName = taskCompanyInfoModel.c.CompanyCode + " | " + taskCompanyInfoModel.c.CompanyName;
                }

                var so = _context.SaleOrderHeader100Model.FirstOrDefault(s => s.VBELN == ttlsxModel.Property1);
                if (so != null)
                {
                    viewModel.ProfileCode = so.KUNNR;
                    viewModel.ProfileName = so.KUNNR + " | " + so.SORTL;
                }

                viewModel.LSXDT = taskCompanyInfoModel.t.Property5;
                viewModel.LSXSAP = lsxSAP;
                viewModel.DSX = taskCompanyInfoModel.tp.Summary;

            }
            else
            {
                viewModel = _unitOfWork.QualityControlRepository.GetById2(qualityControlModel.QualityControlId);

                var latestQualityControlDetail = _context.QualityControlDetailModel
                                                       .Where(x => x.QualityControlId == qualityControlModel.QualityControlId
                                                                     && x.QualityType.Contains(qualityType))
                                                       .OrderByDescending(x => x.QualityDate)
                                                       .FirstOrDefault();


                //var model = _context.QualityControlModel.FirstOrDefault(q => q.QualityControlId == QualityControlId);

                ttlsxModel = (from qc in _context.QualityControlModel
                              join ttl in _context.ThucThiLenhSanXuatModel on qc.CustomerReference equals ttl.TaskId
                              where qc.QualityControlId == qualityControlModel.QualityControlId
                              select ttl).FirstOrDefault();

                productCode = _context.ProductModel.FirstOrDefault(p => p.ProductId == ttlsxModel.ProductId).ProductCode;

                var workshopModel = _context.WorkShopModel.FirstOrDefault(w => w.WorkShopCode == viewModel.WorkShopCode);
                if (workshopModel != null)
                {
                    viewModel.WorkShopCode = workshopModel.WorkShopCode;
                    viewModel.WorkShopName = workshopModel.WorkShopCode + " | " + workshopModel.WorkShopName;

                    congDoanInfo.WorkShopCode = workshopModel.WorkShopCode;
                    congDoanInfo.WorkShopName = workshopModel.WorkShopCode + " | " + workshopModel.WorkShopName;
                }

                var stockReceivingModel = _context.StockReceivingDetailModel
                .Where(srdm => srdm.CustomerReference == qualityControlModel.CustomerReference && srdm.MovementType == "ADD")
                .OrderByDescending(srdm => srdm.CreateTime)
                .FirstOrDefault();

                if (stockReceivingModel != null)
                {

                    var department = _context.DepartmentModel
                        .Where(d => d.DepartmentId == stockReceivingModel.DepartmentId)
                        .FirstOrDefault();

                    if (department != null)
                    {
                        congDoanInfo.DepartmentCode = department.DepartmentCode;
                        congDoanInfo.DepartmentName = department.DepartmentCode + " | " + department.DepartmentName;
                    }
                }

            }

            var product = _context.ProductLatestModel.FirstOrDefault(p => p.ProductCode == productCode);
            if (product != null)
            {
                viewModel.ProductCode = product.ProductCode;
                viewModel.ProductName = product.ProductCode + " | " + product.ProductName;
                viewModel.MauHoanThien = product.MauHoanThien;
            }

            var ttmtModel = _context.SOTextHeader100Model.FirstOrDefault(s => s.SO == viewModel.SO && s.TEXT_ID == "H022");
            if (ttmtModel != null)
            {
                var ttmt = ttmtModel.LONGTEXT;
                viewModel.TinhTrangMoiTruong = ttmt;
            }

            var productTypeInfo = (from pm in _context.ProductModel
                                   join mtm in _context.MaterialTypeModel on pm.MTART equals mtm.MTART into grouping
                                   from mtm in grouping.DefaultIfEmpty()
                                   where pm.ProductCode == viewModel.ProductCode
                                   select new
                                   {
                                       ProductCode = pm.ProductCode,
                                       ProductName = pm.ProductName,
                                       MTART = pm.MTART,
                                       MTBEZ = mtm == null ? null : mtm.MTBEZ
                                   }).FirstOrDefault();

            if (productTypeInfo != null)
            {
                viewModel.ProductType = productTypeInfo.MTART + " | " + productTypeInfo.MTBEZ;
            }

            var HangTagId = ttlsxModel.Barcode;

            viewModel.SO = ttlsxModel.Property1;
            viewModel.HangTagId = HangTagId;
            viewModel.Qty = ttlsxModel.Qty;

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType);

            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);

            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);

            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);

            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);

            var QCInfoList = new List<QualityControlDropdownVm>();


            QCInfoList = (from p in _context.QualityControlInformationModel
                          from w in p.WorkCenterCode.DefaultIfEmpty()
                          where p.Code < 2000
                          orderby p.Code ascending
                          select new QualityControlDropdownVm
                          {
                              Id = p.Id,
                              Name = p.Code + " | " + p.Name,
                              WorkCenterCode = w.WorkCenterCode
                          }).ToList();


            var hangTag = _context.HangTagModel.FirstOrDefault(x => x.HangTagId == HangTagId);

            // "TB_DGO"
            var ListCongDoanLoi = _unitOfWork.QualityControlRepository.GetCongDoanLoi(viewModel.ProductCode, viewModel.SaleOrgCode);
            var ListCongDoanNho = ListCongDoanLoi;

            //var ErrorList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Error)
            //                                         .Select(x => new CatalogDropdownVm
            //                                         {
            //                                             CatalogCode = x.CatalogCode,
            //                                             CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi
            //                                         })
            //                                         .ToList();

            var ErrorList = GetErrorList();

            List<CatalogDropdownVm> caNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(plant);

            var ret = new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,

                congDoanInfo = congDoanInfo,

                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                congDoanNhoList = ListCongDoanNho,
                congDoanLoiList = ListCongDoanLoi,

                phuongAnXuLyList = PhuongAnXuLyList,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                caNhanGayLoiList = caNhanGayLoiListAll
            };

            return Ok(ret);

        }

        [HttpGet("GetQCMauDauChuyenByLSX")]
        public IActionResult GetQCMauDauChuyenByLSX([FromQuery] string lsxSAP)
        {
            var qualityType = "MAUDAUCHUYEN";

            var nghiemThuInfo = new GetQualityControlerReturnVm();
            var viewModel = new QualityControlViewModel();
            var congDoanInfo = new CongDoanInfoVm();

            ThucThiLenhSanXuatModel ttlsxModel = null;
            var plant = "";
            var productCode = "";

            // Get ThucThiLenhSanXuatModel based on LSX SAP
            ttlsxModel = (from ttl in _context.ThucThiLenhSanXuatModel
                          where ttl.Summary == lsxSAP
                          select ttl).FirstOrDefault();

            if (ttlsxModel == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy thông tin LSX SAP: " + lsxSAP,
                });
            }

            // Get task and company information
            var taskCompanyInfoModel = (from t in _context.TaskModel
                                        join tp in _context.TaskModel on t.ParentTaskId equals tp.TaskId
                                        join c in _context.CompanyModel on t.CompanyId equals c.CompanyId
                                        where t.TaskId == ttlsxModel.ParentTaskId
                                        select new { c, t, tp }).FirstOrDefault();

            plant = taskCompanyInfoModel?.c?.CompanyCode;

            // Always create new empty viewModel for header information only
            viewModel = new QualityControlViewModel();
            viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
            viewModel.Error = new List<QualityControlErrorViewModel>();

            // Get product code
            productCode = (from ttl in _context.ThucThiLenhSanXuatModel
                           join p in _context.ProductModel on ttl.ProductId equals p.ProductId
                           where ttl.Summary == lsxSAP
                           select p.ProductCode).FirstOrDefault();

            // Set plant information
            if (taskCompanyInfoModel != null)
            {
                viewModel.SaleOrgCode = taskCompanyInfoModel.c.CompanyCode;
                viewModel.StoreName = taskCompanyInfoModel.c.CompanyCode + " | " + taskCompanyInfoModel.c.CompanyName;
            }

            // Set customer information
            var so = _context.SaleOrderHeader100Model.FirstOrDefault(s => s.VBELN == ttlsxModel.Property1);
            if (so != null)
            {
                viewModel.ProfileCode = so.KUNNR;
                viewModel.ProfileName = so.KUNNR + " | " + so.SORTL;
            }

            // Set LSX information
            viewModel.LSXDT = taskCompanyInfoModel?.t?.Property5;
            viewModel.LSXSAP = lsxSAP;
            viewModel.DSX = taskCompanyInfoModel?.tp?.Summary;

            // Set product information
            var product = _context.ProductLatestModel.FirstOrDefault(p => p.ProductCode == productCode);
            if (product != null)
            {
                viewModel.ProductCode = product.ProductCode;
                viewModel.ProductName = product.ProductCode + " | " + product.ProductName;
                viewModel.MauHoanThien = product.MauHoanThien;
            }

            // Set environment condition
            var ttmtModel = _context.SOTextHeader100Model.FirstOrDefault(s => s.SO == ttlsxModel.Property1 && s.TEXT_ID == "H022");
            if (ttmtModel != null)
            {
                viewModel.TinhTrangMoiTruong = ttmtModel.LONGTEXT;
            }

            // Set product type information
            var productTypeInfo = (from pm in _context.ProductModel
                                   join mtm in _context.MaterialTypeModel on pm.MTART equals mtm.MTART into grouping
                                   from mtm in grouping.DefaultIfEmpty()
                                   where pm.ProductCode == viewModel.ProductCode
                                   select new
                                   {
                                       ProductCode = pm.ProductCode,
                                       ProductName = pm.ProductName,
                                       MTART = pm.MTART,
                                       MTBEZ = mtm == null ? null : mtm.MTBEZ
                                   }).FirstOrDefault();

            if (productTypeInfo != null)
            {
                viewModel.ProductType = productTypeInfo.MTART + " | " + productTypeInfo.MTBEZ;
            }

            // Set basic header information
            var HangTagId = ttlsxModel.Barcode;
            viewModel.SO = ttlsxModel.Property1;
            viewModel.HangTagId = HangTagId;
            viewModel.Qty = ttlsxModel.Qty;

            // Master Data
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType);
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);
            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);

            var QCInfoList = (from p in _context.QualityControlInformationModel
                              from w in p.WorkCenterCode.DefaultIfEmpty()
                              where p.Code < 2000
                              orderby p.Code ascending
                              select new QualityControlDropdownVm
                              {
                                  Id = p.Id,
                                  Name = p.Code + " | " + p.Name,
                                  WorkCenterCode = w.WorkCenterCode
                              }).ToList();

            // Get routing information for MAUDAUCHUYEN
            var ListCongDoanLoi = _unitOfWork.QualityControlRepository.GetCongDoanLoi(viewModel.ProductCode, viewModel.SaleOrgCode);
            var ListCongDoanNho = ListCongDoanLoi;

            var ErrorList = GetErrorList();
            List<CatalogDropdownVm> caNhanGayLoiListAll = GetCaNhanGayLoiListByPlant(plant);

            // Get checklist items specifically for MAUDAUCHUYEN
            var hangMucKiemTra = _context.QualityControlInformationModel
                    .Where(p => p.QualityType == "MAUDAUCHUYEN")
                    .OrderBy(p => p.OrderIndex)
                    .ToList();

            var ret = new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,
                congDoanInfo = congDoanInfo,

                resultList = ResultList,
                qualityTypeList = QualityTypeList,
                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,
                limitList = QCLimit,

                congDoanNhoList = ListCongDoanNho,
                congDoanLoiList = ListCongDoanLoi,
                phuongAnXuLyList = PhuongAnXuLyList,

                qualityControlInformationIdList = QCInfoList,
                errorList = ErrorList,
                caNhanGayLoiList = caNhanGayLoiListAll,

                // Specific for MAUDAUCHUYEN
                hangMucKiemTra = hangMucKiemTra
            };

            return Ok(ret);
        }

        [HttpGet("GetQCPassedStampInfo")]
        public IActionResult GetQCPassedStampInfo([FromQuery] string barcode)
        {
            var productBarcodeModel = _context.ProductBarcodeModel.FirstOrDefault(x => x.FriendlyCode == barcode || x.EAN13 == barcode);

            if (productBarcodeModel == null)
            {
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Không tìm thấy thông tin!" });
            }

            var ret = new QCPassedStampModel();

            ret.LsxSap = productBarcodeModel.PO;
            ret.Serial = productBarcodeModel.EAN13;

            var plant = _context.CompanyModel.FirstOrDefault(x => x.CompanyCode == productBarcodeModel.Plant);

            if (plant != null)
            {
                ret.PlaceOfManufacture = productBarcodeModel.Plant + " | " + plant.CompanyName;
            }

            var productModel = _context.ProductLatestModel.FirstOrDefault(x => x.ProductCode == productBarcodeModel.ProductCode);

            if (productModel != null)
            {
                ret.ProductCode = productModel.ProductCode;
                ret.ProductName = productModel.ProductName;
            }

            var qcmodel = _context.QualityControlModel.FirstOrDefault(x => x.Barcode == barcode);

            if (qcmodel != null)
            {
                ret.QualityControlTime = qcmodel.QualityDate?.ToString("dd/MM/yyyy HH:mm:ss");
                ret.DateOfManufacture = qcmodel.QualityDate?.ToString("dd/MM/yyyy");
            }

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = ret });


        }

        [HttpGet("GetPOHeaderMauDauChuyen")]
        public async Task<IActionResult> GetPOHeaderMauDauChuyen(string token, string? barcode)
        {
            var productBarcodeModel = _context.ProductBarcodeModel.FirstOrDefault(x => x.FriendlyCode == barcode || x.EAN13 == barcode);
            var isTTFBarcode = false;
            var PO = "";

            if (productBarcodeModel != null)
            {
                isTTFBarcode = true;
                PO = productBarcodeModel.PO;
            }

            // Only for TTF
            if (productBarcodeModel == null)
            {
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Không tìm thấy thông tin PO, vui lòng kiểm tra lại qr code thành phẩm/qc passed stamp!" });
            }

            //Lấy danh sách mức độ lỗi 
            var DefectLevel = await _context.CatalogModel.Where(p => p.CatalogTypeCode == "DefectLevel")
                                                      .OrderBy(x => x.OrderIndex)
                                                      .Select(x => new CommonResponse
                                                      {
                                                          Key = x.CatalogCode,
                                                          Value = x.CatalogText_vi
                                                      }).ToListAsync();

            // Master Data
            // Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            // Loại kiểm tra
            var QualityTypeList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControlType);

            // Nhân viên nhóm QC
            var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRolesForDropdown(ConstRoleCode.QC, true);

            //Detail
            // Phương pháp KT
            var TestMethodList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Method);

            // Mức độ giới hạn lỗi
            var QCLimit = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QCLimit);

            // Mức độ lấy mẫu
            var SamplingLevelList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.SamplingLevel);

            var PhuongAnXuLyList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_PhuongAnXuLy);

            var QCInfoList = new List<QualityControlDropdownVm>();

            QCInfoList = (from p in _context.QualityControlInformationModel
                          from w in p.WorkCenterCode.DefaultIfEmpty()
                          where p.Code < 2000
                          orderby p.Code ascending
                          select new QualityControlDropdownVm
                          {
                              Id = p.Id,
                              Name = p.Code + " | " + p.Name,
                              WorkCenterCode = w.WorkCenterCode
                          }).ToList();

            // danh sách lỗi
            var ErrorList = GetErrorList();

            QualityControlViewModel viewModel = null;

            //if (qualityControlId != null && qualityControlId != "null")
            //{
            //    viewModel = _unitOfWork.QualityControlRepository.GetById2(Guid.Parse(qualityControlId), barcode: barcode);
            //}
            //else
            //{
            //    viewModel = _unitOfWork.QualityControlRepository.GetById2(null, barcode: barcode);
            //}

            if (viewModel == null)
            {
                viewModel = new QualityControlViewModel();
                // Empty for new
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();

                viewModel.ProductCode = productBarcodeModel != null ? productBarcodeModel.ProductCode : "";
                viewModel.SaleOrgCode = productBarcodeModel != null ? productBarcodeModel.Plant : "";
                viewModel.Barcode = barcode;
            }
            else
            {
                // Don't load detailed information immediately like PhieuKCSCongDoanDetail
                // Detailed information will be loaded after selecting Công đoạn nhỏ via CongDoanInfoBTP endpoint
                // Initialize empty arrays for consistent UI
                viewModel.QualityControlInformation = new List<QualityControlInformationMappingViewModel>();
                viewModel.Error = new List<QualityControlErrorViewModel>();
            }

            // Hạng mục kiểm tra for MAUDAUCHUYEN
            var hangMucKiemTra = _context.QualityControlInformationModel
                    .Where(p => p.QualityType == "MAUDAUCHUYEN")
                    .OrderBy(p => p.OrderIndex)
                    .ToList();

            var caNhanGayLoiList = GetCaNhanGayLoiListByPlant(viewModel.SaleOrgCode);

            var productTypeInfo = (from pm in _context.ProductModel
                                   join mtm in _context.MaterialTypeModel on pm.MTART equals mtm.MTART into grouping
                                   from mtm in grouping.DefaultIfEmpty()
                                   where pm.ProductCode == viewModel.ProductCode
                                   select new
                                   {
                                       ProductCode = pm.ProductCode,
                                       ProductName = pm.ProductName,
                                       MTART = pm.MTART,
                                       MTBEZ = mtm == null ? null : mtm.MTBEZ
                                   }).FirstOrDefault();

            if (productTypeInfo != null)
            {
                viewModel.ProductType = productTypeInfo.MTART + " | " + productTypeInfo.MTBEZ;
            }

            viewModel.MauHoanThien = GetMauHoanThien(viewModel.ProductCode);

            var congDoanNhoListTemp = (from r in _context.RoutingInventorModel
                                       join rm in _context.RoutingModel on r.ARBPL equals rm.StepCode
                                       where r.MATNR == viewModel.ProductCode && r.WERKS == viewModel.SaleOrgCode
                                       group r by new { r.WERKS, r.MATNR, r.ARBPL_SUB, r.ITMNO, r.MEINS, r.ARBPL, rm.StepName, rm.StepCode, rm.OrderIndex } into g
                                       select new
                                       {
                                           ARBPL = g.Key.ARBPL,
                                           StepName = g.Key.StepName,
                                           OrderIndex = g.Key.OrderIndex
                                       }).ToList();

            var congDoanList = congDoanNhoListTemp.OrderBy(c => c.OrderIndex).Distinct().Select(row => new CatalogDropdownVm
            {
                CatalogCode = row.ARBPL,
                CatalogText_vi = row.ARBPL + " | " + row.StepName
            }).ToList();

            var ListCongDoanLoi = _unitOfWork.QualityControlRepository.GetCongDoanLoi(viewModel.ProductCode, viewModel.SaleOrgCode);
            var ListCongDoanNho = ListCongDoanLoi;

            if (viewModel.QualityControlDetail != null)
            {
                // Fix routing not exists caused error
                var qcDetailCongDoanNho = viewModel.QualityControlDetail.CongDoanNho;
                if (qcDetailCongDoanNho != null)
                {
                    var qcDetailCongDoanNhoModel = ListCongDoanNho.FirstOrDefault(x => x.CatalogCode == qcDetailCongDoanNho);
                    if (qcDetailCongDoanNhoModel == null)
                    {
                        ListCongDoanNho.Add(new CatalogDropdownVm
                        {
                            CatalogCode = qcDetailCongDoanNho,
                            CatalogText_vi = qcDetailCongDoanNho
                        });
                    }
                }
            }

            return Ok(new GetQualityControlerReturnVm
            {
                qualityControl = viewModel,

                resultList = ResultList,
                qualityTypeList = QualityTypeList,

                qualityCheckerList = QCEmployeeList,

                testMethodList = TestMethodList,
                samplingLevelList = SamplingLevelList,

                limitList = QCLimit,

                phuongAnXuLyList = PhuongAnXuLyList,

                congDoanNhoList = ListCongDoanNho,
                congDoanLoiList = ListCongDoanLoi,

                qualityControlInformationIdList = QCInfoList,

                errorList = ErrorList,

                // New for MAUDAUCHUYEN
                hangMucKiemTra = hangMucKiemTra,
                defectLevel = DefectLevel,
                caNhanGayLoiList = caNhanGayLoiList,

                isTTFBarcode = isTTFBarcode,
                PO = PO

            });
        }

        private List<CatalogDropdownVm> GetCaNhanGayLoiListByPlant(string plant)
        {
            // Exlcude subgroup
            var excludeSubgroup = new List<string> {
                    "10", // Chủ tịch, CEO  
                    //"12", // Giám đốc nhà máy
                    //"13", // Quản lý cấp cơ sở, quản đốc
                    //"14", // Phó ban
                    //"17" 
            };

            var organizationNameList = new List<string>
            {
                "kế toán",
                "Kế Toán",
                "Hành chính",
                "Nhân sự",
                "cung ứng",
                "Kinh Doanh",
                "Tài Chính"
            };

            // TEST 1: error convert to SQL query
            //excludeOrganization = _context.SalesEmployeeInfoModel
            //                .Where(x => organizationNameList.Any(name => x.Name.Contains(name)))
            //                .Select(x => x.Code)
            //                .ToList();

            // TEST 2: error convert to SQL query
            //excludeOrganization = _context.SalesEmployeeInfoModel
            //    .Where(x =>
            //        organizationNameList.Any(name =>
            //            EF.Functions.Like(x.Name, $"%{name}%")))
            //    .Select(x => x.Code)
            //    .ToList();

            // Build a SQL query with multiple LIKE conditions
            var likeConditions = string.Join(" OR ",
                organizationNameList.Select(name => $"Name LIKE '%{name}%'"));

            var excludeOrganization = _context.SalesEmployeeInfoModel
                                    .FromSqlRaw($"SELECT * FROM tMasterData.SalesEmployeeInfoModel WHERE {likeConditions}")
                                    .Select(x => x.Code)
                                    .ToList();

            var positionNameList = new List<string>
            {
                "Trưởng Ban",
                "Phó Ban",
                "Chủ Tịch",
                "Trợ Lý"
            };

            // Build a SQL query with multiple LIKE conditions for positions
            var positionLikeConditions = string.Join(" OR ",
                positionNameList.Select(name => $"Name LIKE '%{name}%'"));

            var excludePosition = _context.SalesEmployeeInfoModel
                                    .FromSqlRaw($"SELECT * FROM tMasterData.SalesEmployeeInfoModel WHERE {positionLikeConditions}")
                                    .Select(x => x.Code)
                                    .ToList();

            return _context.SalesEmployeeModel
                .Where(x => x.Actived == true
                        && !excludeSubgroup.Contains(x.SubGroup)
                        && !excludeOrganization.Contains(x.OrganizationalUnit_ORGEH)
                        && !excludePosition.Contains(x.Position_PLANS)
                )
                .Select(x => new CatalogDropdownVm
                {
                    CatalogCode = x.SalesEmployeeCode,
                    CatalogText_vi = x.SalesEmployeeCode + " | " + x.SalesEmployeeName
                })
                .ToList();
        }

        private List<CatalogDropdownVm> GetCaNhanGayLoiListByPlant_old(string plant)
        {
            return _context.AccountModel
                                    .Include("Store")
                                    .Where(x => x.VendorNumber == null &&
                                                x.EmployeeCode != null &&
                                                x.EmployeeCode != "" &&
                                                x.EmployeeCode != "0" &&
                                                x.Store.Any(sm => sm.SaleOrgCode == plant)
                                            )
                                    .Select(x => new CatalogDropdownVm
                                    {
                                        CatalogCode = x.EmployeeCode,
                                        CatalogText_vi = x.EmployeeCode + " | " + x.FullName
                                    }).ToList();
        }


        private string GetMauHoanThien(string productCode)
        {
            var product = _context.ProductLatestModel.FirstOrDefault(p => p.ProductCode == productCode);
            if (product != null && !string.IsNullOrEmpty(product.MauHoanThien))
            {
                return product.MauHoanThien;
            }
            return "";
        }



        //[HttpGet("ByHangTagId/{HangTagId}")]
        //public IActionResult GetByHangTagId(Guid? HangTagId)
        //{

        //    var qualityControlModel = (from ht in _context.HangTagModel
        //                               join ttl in _context.ThucThiLenhSanXuatModel on ht.CustomerReference equals ttl.ParentTaskId
        //                               join qc in _context.QualityControlModel on ttl.TaskId equals qc.CustomerReference
        //                               where ht.HangTagId == HangTagId
        //                               select qc).FirstOrDefault();

        //    return Ok(qualityControlModel);
        //}

        #region GĐ1 Kiểm tra chất lượng - Lưu thông tin

        /// <summary>API "Kiểm tra chất lượng" - Lưu thông tin </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/QualityControl/QualityControl
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///             {
        ///                 "qualityControlModel": "e59103f5-e697-4bb8-9010-c5b1a62ffb3d",
        ///                 "qualityChecker": "8d3993a5-bfff-41a0-ae58-3136aa2171a3",
        ///                 "qualityDate": "2022-05-14",
        ///                 "qualityType": "Material",
        ///                 "inspectionLotQuantity": 1,
        ///                 "result": "QualityControl_Result_Pass",
        ///                 "po": "po",
        ///                 "qualityControlDetail": {
        ///                   "qualityControlDetailId": "98db6e43-dba8-452f-ac19-2d40667a6fbf",
        ///                   "testMethod": "AQL",
        ///                   "samplingLevel": "S-1",
        ///                   "acceptableLevel": "muc chap nhan",
        ///                   "inspectionQuantity": 1,
        ///                   "result": "QualityControl_Result_Pass",
        ///                   "samplingLevelName": "noi dung detail neu samplingLevel == OTHER"
        ///                 },
        ///                 "qualityControlInformation": [
        ///                   {
        ///                     "qualityControlInformationId": "0ff9c910-22cb-45d0-af7e-e1278480f926",
        ///                     "qualityControl_QCInformation_Id": "6d8e7b63-3926-4bd2-831d-54b119016ba8",
        ///                     "notes": "kiem thu chat luong note co san"
        ///                   },
        ///                   {      
        ///                       "qualityControlInformationId": "0ff9c910-22cb-45d0-af7e-e1278480f926",
        ///                       "notes": "kiem thu chat luong note them moi"
        ///                   }
        ///                 ],
        ///                 "error": [
        ///                   {
        ///                     "quanlityControl_Error_Id": "820f1f6b-5eb1-43f7-9fe6-650fa2d7a71b",
        ///                     "catalogCode": "Q001",
        ///                     "levelError": "mucdoloi",
        ///                     "quantityError": 1,
        ///                     "notes": "loi ghi chu co san"
        ///                   },
        ///                   {
        ///                   "catalogCode": "Q001",
        ///                     "levelError": "mucdoloi 2",
        ///                     "quantityError": 1,
        ///                     "notes": "loi ghi chu them moi"
        ///                   }
        ///                 ]
        ///               }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 202,
        ///         "success": true,
        ///         "data": "Cập nhật phiếu kiểm tra chất lượng thành công"
        ///     }
        ///     
        ///</remarks>
        [HttpPost("QualityControl")]
        public async Task<IActionResult> PostQualityControl([FromForm] QualityControlPostViewModel viewModel)
        {
            #region CreateBy - CreateTime
            #endregion

            QualityControlModel model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId);
            if (model == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu quản lý chất lượng",
                });
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                foreach (var item in viewModel.QualityControlInformation)
                {
                    if (item.QualityControlInformationId != null && (item.QualityControl_QCInformation_Id == null || item.QualityControl_QCInformation_Id == Guid.Empty))
                    {
                        QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                        info.QualityControl_QCInformation_Id = Guid.NewGuid();
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.QualityControlId = model.QualityControlId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.Notes = item.Notes;
                        info.SoSanPhamLoi = item.SoSanPhamLoi; // new

                        _context.Entry(info).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.QualityControlInformationId != null && item.QualityControl_QCInformation_Id != null && item.QualityControl_QCInformation_Id != Guid.Empty)
                    {
                        QualityControl_QCInformation_Mapping info = _context.QualityControl_QCInformation_Mapping.Find(item.QualityControl_QCInformation_Id);
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.Notes = item.Notes;
                        info.SoSanPhamLoi = item.SoSanPhamLoi; // new

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    if (item.CatalogCode != null && (item.QuanlityControl_Error_Id == null || item.QuanlityControl_Error_Id == Guid.Empty))
                    {
                        QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                        error.QuanlityControl_Error_Id = Guid.NewGuid();
                        error.CatalogCode = item.CatalogCode;
                        error.QualityControlId = model.QualityControlId;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        _context.Entry(error).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.CatalogCode != null && item.QuanlityControl_Error_Id != null && item.QuanlityControl_Error_Id != Guid.Empty)
                    {
                        QualityControl_Error_Mapping error = _context.QualityControl_Error_Mapping.Find(item.QuanlityControl_Error_Id);
                        error.QualityControlId = model.QualityControlId;
                        error.CatalogCode = item.CatalogCode;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Cập nhật phiếu kiểm tra chất lượng thành công"
            });
        }

        [HttpPost("QualityControl2")]
        public async Task<IActionResult> PostQualityControl2([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion

            //viewModel.QualityType = "DAUVAO";

            QualityControlModel model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId);
            if (model == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu quản lý chất lượng",
                });
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                foreach (var item in viewModel.QualityControlInformation)
                {
                    if (item.QualityControlInformationId != null && (item.QualityControl_QCInformation_Id == null || item.QualityControl_QCInformation_Id == Guid.Empty))
                    {
                        QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                        info.QualityControl_QCInformation_Id = Guid.NewGuid();
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.QualityControlId = model.QualityControlId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.SoSanPhamLoi = item.SoSanPhamLoi;
                        info.Notes = item.Notes;
                        info.OutcomeStatus = item.OutcomeStatus;

                        _context.Entry(info).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.QualityControlInformationId != null && item.QualityControl_QCInformation_Id != null && item.QualityControl_QCInformation_Id != Guid.Empty)
                    {
                        QualityControl_QCInformation_Mapping info = _context.QualityControl_QCInformation_Mapping.Find(item.QualityControl_QCInformation_Id);
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.Notes = item.Notes;
                        info.OutcomeStatus = item.OutcomeStatus;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    if (item.CatalogCode != null && (item.QuanlityControl_Error_Id == null || item.QuanlityControl_Error_Id == Guid.Empty))
                    {
                        QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                        error.QuanlityControl_Error_Id = Guid.NewGuid();
                        error.CatalogCode = item.CatalogCode;
                        error.QualityControlId = model.QualityControlId;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        error.StepCodeError = item.CongDoanLoi;
                        error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                        error.SaleOrgErrorCode = item.NhaMayLoi;
                        error.WorkshopErrorCode = item.PhanXuongLoi;
                        error.DepartmentErrorCode = item.ToChuyenLoi;

                        _context.Entry(error).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.CatalogCode != null && item.QuanlityControl_Error_Id != null && item.QuanlityControl_Error_Id != Guid.Empty)
                    {
                        QualityControl_Error_Mapping error = _context.QualityControl_Error_Mapping.Find(item.QuanlityControl_Error_Id);
                        error.QualityControlId = model.QualityControlId;
                        error.CatalogCode = item.CatalogCode;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        error.StepCodeError = item.CongDoanLoi;
                        error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                        error.SaleOrgErrorCode = item.NhaMayLoi;
                        error.WorkshopErrorCode = item.PhanXuongLoi;
                        error.DepartmentErrorCode = item.ToChuyenLoi;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Cập nhật phiếu kiểm tra chất lượng thành công"
            });
        }
        #endregion


        /// <summary>
        /// This function is for QC dauvao tich hop SAP, tichhop SAP dauvao SAP
        /// </summary>
        /// <returns></returns>
        [HttpPost("QualityControl3")]
        public async Task<IActionResult> PostQualityControl3([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion

            //viewModel.QualityType = "DAUVAO";

            QualityControlModel model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId);
            if (model == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu quản lý chất lượng",
                });
            }

            if (model.IsSyncToSap == true)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Phiếu đã post SAP",
                });
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                foreach (var item in viewModel.QualityControlInformation)
                {
                    if (item.QualityControlInformationId != null && (item.QualityControl_QCInformation_Id == null || item.QualityControl_QCInformation_Id == Guid.Empty))
                    {
                        QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                        info.QualityControl_QCInformation_Id = Guid.NewGuid();
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.QualityControlId = model.QualityControlId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.SoSanPhamLoi = item.SoSanPhamLoi;
                        info.Notes = item.Notes;
                        info.OutcomeStatus = item.OutcomeStatus;

                        _context.Entry(info).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.QualityControlInformationId != null && item.QualityControl_QCInformation_Id != null && item.QualityControl_QCInformation_Id != Guid.Empty)
                    {
                        QualityControl_QCInformation_Mapping info = _context.QualityControl_QCInformation_Mapping.Find(item.QualityControl_QCInformation_Id);
                        info.QualityControlInformationId = item.QualityControlInformationId;
                        info.WorkCenterCode = model.WorkCenterCode;
                        info.Notes = item.Notes;
                        info.OutcomeStatus = item.OutcomeStatus;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                                QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    if (item.CatalogCode != null && (item.QuanlityControl_Error_Id == null || item.QuanlityControl_Error_Id == Guid.Empty))
                    {
                        QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                        error.QuanlityControl_Error_Id = Guid.NewGuid();
                        error.CatalogCode = item.CatalogCode;
                        error.QualityControlId = model.QualityControlId;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        error.StepCodeError = item.CongDoanLoi;
                        error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                        error.SaleOrgErrorCode = item.NhaMayLoi;
                        error.WorkshopErrorCode = item.PhanXuongLoi;
                        error.DepartmentErrorCode = item.ToChuyenLoi;

                        _context.Entry(error).State = EntityState.Added;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                    else if (item.CatalogCode != null && item.QuanlityControl_Error_Id != null && item.QuanlityControl_Error_Id != Guid.Empty)
                    {
                        QualityControl_Error_Mapping error = _context.QualityControl_Error_Mapping.Find(item.QuanlityControl_Error_Id);
                        error.QualityControlId = model.QualityControlId;
                        error.CatalogCode = item.CatalogCode;
                        error.LevelError = item.LevelError;
                        error.QuantityError = item.QuantityError;
                        error.Notes = item.Notes;

                        error.StepCodeError = item.CongDoanLoi;
                        error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                        error.SaleOrgErrorCode = item.NhaMayLoi;
                        error.WorkshopErrorCode = item.PhanXuongLoi;
                        error.DepartmentErrorCode = item.ToChuyenLoi;

                        if (item.File != null && item.File.Count > 0)
                        {
                            foreach (var x in item.File)
                            {
                                FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                                QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                                mapping.FileAttachmentId = fileNew.FileAttachmentId;
                                mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                                _context.Entry(mapping).State = EntityState.Added;
                            }
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            try
            {
                var ret = "";
                using (var httpClient = new HttpClient())
                {
                    var _mvcClientService = new MvcClientService(httpClient);
                    ret = await _mvcClientService.GetQCDauVaoSyncSAP(model.QualityControlId.ToString());
                }

                if (ret != "0" && ret != "1")
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Lỗi SAP: " + ret,
                    });
                }

                if (ret == "0")
                {
                    return NotFound(new
                    {
                        Code = HttpStatusCode.NotFound,
                        Success = false,
                        Data = "Lỗi MES: " + "hàng hóa chưa nhập kho theo luồng MES",
                    });
                }
            }
            catch (Exception e)
            {
                // Do nothing
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Lỗi MES: " + e.Message,
                });
            }

            model.IsSyncToSap = true;
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Cập nhật phiếu kiểm tra chất lượng thành công"
            });
        }


        #region POST submit QC

        [HttpPost("QualityControlCongDoan")]
        public async Task<IActionResult> PostQualityControlCongDoan([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion


            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };
            QualityControlModel model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId, true, newQCDetailVm);
            if (model == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu quản lý chất lượng",
                });
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    // NEW
                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;


                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Cập nhật phiếu kiểm tra chất lượng thành công"
            });
        }


        [HttpPost("QualityControlQCNghiemThu")]
        public async Task<IActionResult> PostQualityControlQCNghiemThu([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion


            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };
            QualityControlModel model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId, true, newQCDetailVm);
            if (model == null)
            {
                return NotFound(new
                {
                    Code = HttpStatusCode.NotFound,
                    Success = false,
                    Data = "Không tìm thấy phiếu quản lý chất lượng",
                });
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy;
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;

                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Cập nhật phiếu kiểm tra chất lượng thành công"
            });
        }
        #endregion

        [HttpPost("QualityControlQCMau")]
        public async Task<IActionResult> PostQualityControlQCMau([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion


            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };

            viewModel.QualityType = viewModel.QualityType;

            QualityControlModel model = await _unitOfWork.QualityControlRepository.Create(viewModel, CurrentUser.AccountId, true, newQCDetailVm);

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;

                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            //_context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Thêm phiếu kiểm tra chất lượng thành công"
            });
        }

        [HttpPost("QualityControlSanPham")]
        public async Task<IActionResult> PostQualityControlSanPham([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };

            var modelCheck = await _context.QualityControlModel.FirstOrDefaultAsync(x => x.QualityControlId == viewModel.QualityControlId);
            QualityControlModel model = null;
            if (modelCheck == null)
            {
                model = await _unitOfWork.QualityControlRepository.Create(viewModel, CurrentUser.AccountId, true, newQCDetailVm);
            }
            else
            {
                model = await _unitOfWork.QualityControlRepository.Edit(viewModel, CurrentUser.AccountId, true, newQCDetailVm);
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;

                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Tạo phiếu kiểm tra chất lượng thành công"
            });
        }


        [HttpPost("QualityControlQCHienTruong")]
        public async Task<IActionResult> PostQualityControlQCHienTruong([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion


            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };

            viewModel.QualityType = viewModel.QualityType;

            QualityControlModel model = await _unitOfWork.QualityControlRepository.Create(viewModel, CurrentUser.AccountId, true, newQCDetailVm);

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;

                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            // Bỏ không cập nhật lại QualityControlCode
            //_context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
            _context.SaveChanges();

            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Thêm phiếu kiểm tra chất lượng thành công"
            });
        }

        [HttpPost("QualityControlMauDauChuyen")]
        public async Task<IActionResult> PostQualityControlMauDauChuyen([FromForm] QualityControlPostViewModel viewModel, string ver2 = "")
        {
            #region CreateBy - CreateTime
            #endregion

            //viewModel.QualityType = "DAUVAO";
            NewQCDetailVm newQCDetailVm = new NewQCDetailVm { NewQCDetailId = null };

            viewModel.QualityType = viewModel.QualityType;

            // Find existing QualityControlModel first - use the correct StepCode from QualityControlDetail
            var stepCode = viewModel.QualityControlDetail?.CongDoanNho ?? viewModel.StepCode;
            var existingQualityControlModel = _context.QualityControlModel
                .Where(qc => qc.LSXSAP == viewModel.LSXSAP
                            && qc.QualityType == "MAUDAUCHUYEN"
                            && qc.StepCode == stepCode)
                .FirstOrDefault();

            QualityControlModel model;

            if (existingQualityControlModel != null)
            {
                // REUSE existing model - NEVER create a new master record
                model = existingQualityControlModel;

                // Update latest info on the master model
                model.QualityDate = viewModel.QualityDate;
                model.QualityChecker = viewModel.QualityChecker;
                model.Result = viewModel.Result;
                model.LastEditBy = CurrentUser.AccountId;
                model.LastEditTime = DateTime.Now;

                // Mark the existing model as modified for Entity Framework
                _context.Entry(model).State = EntityState.Modified;

                // ALWAYS create new detail record when reusing existing model
                if (viewModel.QualityControlDetail != null)
                {
                    var detailViewModel = viewModel.QualityControlDetail;
                    var newDetailModel = new QualityControlDetailModel();
                    var newQCDetailId = Guid.NewGuid();

                    newDetailModel.QualityControlDetailId = newQCDetailId;
                    newDetailModel.QualityControlId = model.QualityControlId;
                    newDetailModel.AcceptableLevel = detailViewModel.AcceptableLevel;
                    newDetailModel.SamplingLevel = detailViewModel.SamplingLevel == "OTHER" ? detailViewModel.SamplingLevelName : detailViewModel.SamplingLevel;
                    newDetailModel.InspectionQuantity = detailViewModel.InspectionQuantity;
                    newDetailModel.TestMethod = detailViewModel.TestMethod;

                    // new nghiem thu dau vao
                    newDetailModel.LimitCritical = detailViewModel.LimitCritical;
                    newDetailModel.LimitHigh = detailViewModel.LimitHigh;
                    newDetailModel.LimitLow = detailViewModel.LimitLow;
                    newDetailModel.TongSoSanPhamLoi = detailViewModel.TongSoSanPhamLoi;

                    newDetailModel.Result = detailViewModel.Result;

                    // new for KCS
                    newDetailModel.QualityDate = DateTime.Now;
                    newDetailModel.QualityChecker = viewModel.QualityChecker;

                    // new for QAQC nghiem thu
                    newDetailModel.QualityType = viewModel.QualityType;
                    newDetailModel.LoiNangChapNhan = viewModel.QualityControlDetail.LoiNangChapNhan;
                    newDetailModel.LoiNheChapNhan = viewModel.QualityControlDetail.LoiNheChapNhan;

                    newDetailModel.StepCode = viewModel.QualityControlDetail.CongDoanNho;
                    newDetailModel.CheckingTimes = viewModel.QualityControlDetail.CheckingTimes;

                    // Include barcode in the quality control detail
                    newDetailModel.Barcode = viewModel.Barcode;

                    // Set the new detail ID for use in related records
                    newQCDetailVm.NewQCDetailId = newQCDetailId;

                    _context.Entry(newDetailModel).State = EntityState.Added;
                }
                else
                {
                    // If no detail provided, still need to set a detail ID for consistency
                    newQCDetailVm.NewQCDetailId = Guid.NewGuid();
                }
            }
            else
            {
                // Create new master model only if none exists for this combination
                model = await _unitOfWork.QualityControlRepository.Create(viewModel, CurrentUser.AccountId, true, newQCDetailVm);
            }

            #region Lưu file QC
            if (viewModel.File != null && viewModel.File.Count > 0)
            {
                foreach (var item in viewModel.File)
                {
                    FileAttachmentModel fileNew = SaveFileAttachment(model.QualityControlId, item, "QualityControl/" + model.QualityControlCode);

                    QualityControl_FileAttachment_Mapping mapping = new QualityControl_FileAttachment_Mapping();
                    mapping.FileAttachmentId = fileNew.FileAttachmentId;
                    mapping.QualityControlId = model.QualityControlId;

                    _context.Entry(mapping).State = EntityState.Added;
                }
            }
            #endregion

            #region Lưu file thông tin kiểm tra
            if (viewModel.QualityControlInformation != null)
            {
                var indexTemp = 1;
                foreach (var item in viewModel.QualityControlInformation)
                {
                    QualityControl_QCInformation_Mapping info = new QualityControl_QCInformation_Mapping();
                    info.QualityControl_QCInformation_Id = Guid.NewGuid();
                    info.QualityControlInformationId = item.QualityControlInformationId;
                    info.QualityControlId = model.QualityControlId;
                    info.WorkCenterCode = model.WorkCenterCode;
                    info.SoSanPhamLoi = item.SoSanPhamLoi;
                    info.Notes = item.Notes;
                    info.OutcomeStatus = item.OutcomeStatus;
                    info.OrderIndex = indexTemp;

                    info.QualityControlDetailId = newQCDetailVm.NewQCDetailId; // new

                    _context.Entry(info).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(info.QualityControl_QCInformation_Id, x, "QualityControl/" + model.QualityControlCode + "/QCInfo");

                            QualityControl_QCInformation_File_Mapping mapping = new QualityControl_QCInformation_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QualityControl_QCInformation_Id = info.QualityControl_QCInformation_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                    indexTemp++;
                }
            }
            #endregion

            #region Lưu file lỗi
            if (viewModel.Error != null)
            {
                foreach (var item in viewModel.Error)
                {
                    // Tien update: KCS thì luôn thêm mới mỗi lần lưu, map với phiếu QC details mới nhất
                    QualityControl_Error_Mapping error = new QualityControl_Error_Mapping();
                    error.QuanlityControl_Error_Id = Guid.NewGuid();
                    error.CatalogCode = item.CatalogCode;
                    error.QualityControlId = model.QualityControlId;
                    error.LevelError = item.LevelError;
                    error.QuantityError = item.QuantityError;
                    error.Notes = item.Notes;

                    error.PersonCausedErrorCode = item.CaNhanGayLoi;
                    error.QualityControlDetailId = newQCDetailVm.NewQCDetailId;

                    error.StepCodeError = item.CongDoanLoi;
                    error.PhuongAnXuLy = item.PhuongAnXuLy; // new
                    error.SaleOrgErrorCode = item.NhaMayLoi;
                    error.WorkshopErrorCode = item.PhanXuongLoi;
                    error.DepartmentErrorCode = item.ToChuyenLoi;

                    error.PersonCausedErrorCodeMany = item.CaNhanGayLoiMany;

                    error.QuanDoc = item.QuanDoc;
                    error.ToTruong = item.ToTruong;
                    error.QAQC = item.QAQC;
                    error.KCS = item.KCS;

                    _context.Entry(error).State = EntityState.Added;

                    if (item.File != null && item.File.Count > 0)
                    {
                        foreach (var x in item.File)
                        {
                            FileAttachmentModel fileNew = SaveFileAttachment(error.QuanlityControl_Error_Id, x, "QualityControl/" + model.QualityControlCode + "/Error");

                            QualityControl_Error_File_Mapping mapping = new QualityControl_Error_File_Mapping();
                            mapping.FileAttachmentId = fileNew.FileAttachmentId;
                            mapping.QuanlityControl_Error_Id = error.QuanlityControl_Error_Id;

                            _context.Entry(mapping).State = EntityState.Added;
                        }
                    }
                }
            }
            #endregion

            try
            {
                // Bỏ không cập nhật lại QualityControlCode
                _context.Entry(model).Property(x => x.QualityControlCode).IsModified = false; // telling Entity Framework that the QualityControlCode property hasn't been changed, so there's no need to create an SQL UPDATE statement for that specific property when SaveChanges() is called. This is useful when you want to update certain fields of an entity and leave others as they are.
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Lỗi " + ex.Message });
            }



            return Ok(new
            {
                Code = HttpStatusCode.Accepted,
                Success = true,
                Data = "Thêm phiếu kiểm tra chất lượng mẫu đầu chuyền thành công"
            });
        }


        #region GĐ2 Qc Nguyên vật liệu - Lấy thông tin
        /// <summary>API "Kiểm tra chất lượng nguyên vật liệu" - Lấy danh sách thông tin </summary>
        /// <returns></returns>
        [HttpPost("GetLstQCNVLByFilter")]
        public IActionResult GetLstQCNVLByFilter([FromBody] QualityControlNVLSearchViewModel searchVm)
        {

            var query = (from qc in _context.QualityControlModel
                             // Product - Material
                         join prd in _context.ProductModel on qc.ProductCode equals prd.ProductCode into prdL
                         from subPrd in prdL.DefaultIfEmpty()
                             // Account - Nhân viên Qc
                         join nv in _context.AccountModel on qc.QualityChecker equals nv.AccountId into nvL
                         from subNv in nvL.DefaultIfEmpty()
                             // Thẻ treo pallet NVL 
                         join nvl in _context.RawMaterialCardModel on qc.CustomerReference equals nvl.RawMaterialCardId
                         join ncc in _context.VendorModel on nvl.VendorCode equals ncc.SupplierNumber into nccL
                         from subNcc in nccL.DefaultIfEmpty()
                             // pallet NVL Mapping PODEtail
                         join nvlDetail in _context.RawMaterial_PurchaseOrderDetail_Mapping on nvl.RawMaterialCardId equals nvlDetail.RawMaterialCardId into nvlD
                         from subnvlDetail in nvlD.DefaultIfEmpty()
                             // po Detail
                         join poDetail in _context.PurchaseOrderDetailModel on subnvlDetail.PurchaseOrderDetailId equals poDetail.PurchaseOrderDetailId into poD
                         from subPoDetail in poD.DefaultIfEmpty()
                             // Result
                         join rTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = qc.Result } equals new { CatalogTypeCode = rTemp.CatalogTypeCode, CatalogCode = rTemp.CatalogCode } into rList
                         from r in rList.DefaultIfEmpty()
                             //Thời gian giao hàng
                         join trans in _context.WarehouseTransactionModel on nvl.RawMaterialCardId equals trans.ReferenceDocumentId into transL
                         from subTrans in transL.DefaultIfEmpty()

                         where
                             // ProductCode
                             (string.IsNullOrEmpty(searchVm.ProductCode) || nvl.ProductCode == searchVm.ProductCode)
                             // Po
                             && (string.IsNullOrEmpty(searchVm.PoNumber) || subPoDetail.PurchaseOrderCode == searchVm.PoNumber)
                             // So
                             && (string.IsNullOrEmpty(searchVm.SO) || subPoDetail.SDDocument == searchVm.SO)
                             // Wbs
                             && (string.IsNullOrEmpty(searchVm.WBS) || subPoDetail.WBSElement == searchVm.WBS)
                             // VendorNumber
                             && (string.IsNullOrEmpty(searchVm.VendorNumber) || nvl.VendorCode == searchVm.VendorNumber)
                             // createBarCodeFromDate
                             && (!searchVm.CreateBarcodeFromDate.HasValue || nvl.CreateTime >= searchVm.CreateBarcodeFromDate)
                             // CreateBarCodeToDate
                             && (!searchVm.CreateBarcodeToDate.HasValue || searchVm.CreateBarcodeToDate >= nvl.CreateTime)
                             // DeliveryStatus
                             && (!searchVm.DeliveryStatus.HasValue || nvl.IsReceive == searchVm.DeliveryStatus)
                             // DeliveryFromDate
                             && (!searchVm.DeliveryFromDate.HasValue || subTrans.DocumentDate >= searchVm.DeliveryFromDate)
                              // DeliveryToDate
                              && (!searchVm.DeliveryToDate.HasValue || searchVm.DeliveryToDate >= subTrans.DocumentDate)
                             // QC Status
                             && (!searchVm.QcStatus.HasValue || qc.Status == searchVm.QcStatus)
                             // QC Result
                             && (!searchVm.QcResult.HasValue || (searchVm.QcResult == true ? qc.Result == "QualityControl_Result_Pass" : qc.Result == "QualityControl_Result_Fail"))
                             // QC FromDate
                             && (!searchVm.QcFromDate.HasValue || qc.CreateTime >= searchVm.QcFromDate)
                             // QC ToDate
                             && (!searchVm.QcToDate.HasValue || searchVm.QcToDate >= qc.CreateTime)
                             //GoodsArriveDate
                             && (!searchVm.GoodsArriveFromDate.HasValue || nvl.GoodsArriveDate >= searchVm.GoodsArriveFromDate)
                             && (!searchVm.GoodsArriveToDate.HasValue || searchVm.GoodsArriveToDate >= nvl.GoodsArriveDate)

                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR1") ? nvl.IsReceive == false : true)
                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR2") ? nvl.isGoodsArrive == true : true)
                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR3") ? nvl.IsReceive == true : true)
                         select new QualityControlNVLListViewModel
                         {
                             // QC ticket Id
                             QualityControlId = qc.QualityControlId,
                             //Thời gian tạo barcode
                             BarcodeCreateDate = nvl.CreateTime,
                             //Số PO
                             PONumber = subPoDetail.PurchaseOrderCode,
                             //NCC
                             Vendor = subNcc.LongName,
                             //Mã NVL | Tên NVL
                             Material = subPrd.ProductCode + " | " + subPrd.ProductName,
                             //SO/WBS
                             SoWbs = string.IsNullOrEmpty(subPoDetail.SDDocument) ? subPoDetail.WBSElement : subPoDetail.SDDocument + " / " + subPoDetail.Item,
                             //Ngày giao hàng thực tế
                             ActualDeliveryDate = subTrans.DocumentDate,
                             //Thời gian QC
                             QCCreateTime = qc.LastEditTime,
                             //Trạng thái QC
                             QCStatus = qc.Status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                             //Kết quả
                             QCResult = r.CatalogText_vi,
                             //nHÂN VIÊN
                             QCEmployee = subNv.FullName,
                             //Ngày hàng về
                             isGoodsArrive = nvl.isGoodsArrive,
                             GoodsArriveDate = nvl.GoodsArriveDate
                         });

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = CustomSearchRepository.CustomSearchFunc(new DatatableViewModel
            {
                start = ((searchVm.pageNumber - 1) * searchVm.pageSize),
                length = searchVm.pageSize
            }, out filteredResultsCount, out totalResultsCount, query, "STT");

            if (res != null && res.Count() > 0)
            {
                int i = ((searchVm.pageNumber - 1) * searchVm.pageSize) + 1;

                res = res.GroupBy(x => new
                {
                    x.QualityControlId,
                    x.BarcodeCreateDate,
                    x.Vendor,
                    x.Material,
                    x.ActualDeliveryDate,
                    x.QCCreateTime,
                    x.QCStatus,
                    x.QCResult,
                    x.QCEmployee
                }).Select(x => new QualityControlNVLListViewModel
                {
                    QualityControlId = x.Key.QualityControlId,
                    BarcodeCreateDate = x.Key.BarcodeCreateDate,
                    Vendor = x.Key.Vendor,
                    Material = x.Key.Material,
                    ActualDeliveryDate = x.Key.ActualDeliveryDate,
                    QCCreateTime = x.Key.QCCreateTime,
                    QCStatus = x.Key.QCStatus,
                    QCResult = x.Key.QCResult,
                    QCEmployee = x.Key.QCEmployee,
                    PONumber = string.Join(',', x.Select(x => x.PONumber).Distinct().ToArray()),
                    SoWbs = string.Join(',', x.Select(x => x.SoWbs).Distinct().ToArray()),
                }).ToList();

                foreach (var item in res)
                {
                    item.STT = i;
                    i++;
                }

                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Data = res });
            }
            else
            {
                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Message = "Không tìm thấy phiếu kiểm tra nào!", Data = res });
            }
        }

        #endregion


        [HttpPost("GetLstQCNVLByFilter2")]
        public IActionResult GetLstQCNVLByFilter2([FromBody] QualityControlNVLSearchViewModel searchVm, string ver2 = "", string ver3 = "")
        {

            var query = (from qc in _context.QualityControlModel
                             // Product - Material
                         join prd in _context.ProductModel on qc.ProductCode equals prd.ProductCode into prdL
                         from subPrd in prdL.DefaultIfEmpty()
                             // Account - Nhân viên Qc
                         join nv in _context.AccountModel on qc.QualityChecker equals nv.AccountId into nvL
                         from subNv in nvL.DefaultIfEmpty()
                             // Thẻ treo pallet NVL 
                         join nvl in _context.RawMaterialCardModel on qc.CustomerReference equals nvl.RawMaterialCardId
                         join ncc in _context.VendorModel on nvl.VendorCode equals ncc.SupplierNumber into nccL
                         from subNcc in nccL.DefaultIfEmpty()
                             // pallet NVL Mapping PODEtail
                         join nvlDetail in _context.RawMaterial_PurchaseOrderDetail_Mapping on nvl.RawMaterialCardId equals nvlDetail.RawMaterialCardId into nvlD
                         from subnvlDetail in nvlD.DefaultIfEmpty()
                             // po Detail
                         join poDetail in _context.PurchaseOrderDetailModel on subnvlDetail.PurchaseOrderDetailId equals poDetail.PurchaseOrderDetailId into poD
                         from subPoDetail in poD.DefaultIfEmpty()
                             // Result
                         join rTemp in _context.CatalogModel on new { CatalogTypeCode = ConstQualityControl.QualityControl_Result, CatalogCode = qc.Result } equals new { CatalogTypeCode = rTemp.CatalogTypeCode, CatalogCode = rTemp.CatalogCode } into rList
                         from r in rList.DefaultIfEmpty()
                             //Thời gian giao hàng
                         join trans in _context.WarehouseTransactionModel on nvl.RawMaterialCardId equals trans.ReferenceDocumentId into transL
                         from subTrans in transL.DefaultIfEmpty()

                         where
                             // ProductCode
                             (string.IsNullOrEmpty(searchVm.ProductCode) || nvl.ProductCode == searchVm.ProductCode)
                             // Po
                             && (string.IsNullOrEmpty(searchVm.PoNumber) || subPoDetail.PurchaseOrderCode == searchVm.PoNumber)
                             // So
                             && (string.IsNullOrEmpty(searchVm.SO) || subPoDetail.SDDocument == searchVm.SO)
                             // Wbs
                             && (string.IsNullOrEmpty(searchVm.WBS) || subPoDetail.WBSElement == searchVm.WBS)
                             // VendorNumber
                             && (string.IsNullOrEmpty(searchVm.VendorNumber) || nvl.VendorCode == searchVm.VendorNumber)
                             // createBarCodeFromDate
                             && (!searchVm.CreateBarcodeFromDate.HasValue || nvl.CreateTime >= searchVm.CreateBarcodeFromDate)
                             // CreateBarCodeToDate
                             && (!searchVm.CreateBarcodeToDate.HasValue || searchVm.CreateBarcodeToDate >= nvl.CreateTime)
                             // DeliveryStatus
                             && (!searchVm.DeliveryStatus.HasValue || nvl.IsReceive == searchVm.DeliveryStatus)
                             // DeliveryFromDate
                             && (!searchVm.DeliveryFromDate.HasValue || subTrans.DocumentDate >= searchVm.DeliveryFromDate)
                              // DeliveryToDate
                              && (!searchVm.DeliveryToDate.HasValue || searchVm.DeliveryToDate >= subTrans.DocumentDate)
                             // QC Status
                             && (!searchVm.QcStatus.HasValue || qc.Status == searchVm.QcStatus)
                             // QC Result
                             && (!searchVm.QcResult.HasValue || (searchVm.QcResult == true ? qc.Result == "QualityControl_Result_Pass" : qc.Result == "QualityControl_Result_Fail"))
                             // QC FromDate
                             && (!searchVm.QcFromDate.HasValue || qc.CreateTime >= searchVm.QcFromDate)
                             // QC ToDate
                             && (!searchVm.QcToDate.HasValue || searchVm.QcToDate >= qc.CreateTime)
                             //GoodsArriveDate
                             && (!searchVm.GoodsArriveFromDate.HasValue || nvl.GoodsArriveDate >= searchVm.GoodsArriveFromDate)
                             && (!searchVm.GoodsArriveToDate.HasValue || searchVm.GoodsArriveToDate >= nvl.GoodsArriveDate)

                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR1") ? nvl.IsReceive == false : true)
                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR2") ? nvl.isGoodsArrive == true : true)
                             && ((!string.IsNullOrEmpty(searchVm.GoodsArrive) && searchVm.GoodsArrive == "GR3") ? nvl.IsReceive == true : true)
                         select new QualityControlNVLListViewModel
                         {
                             // QC ticket Id
                             QualityControlId = qc.QualityControlId,
                             //Thời gian tạo barcode
                             BarcodeCreateDate = nvl.CreateTime,
                             //Số PO
                             PONumber = subPoDetail.PurchaseOrderCode,
                             //NCC
                             Vendor = subNcc.LongName,
                             //Mã NVL | Tên NVL
                             Material = subPrd.ProductCode + " | " + subPrd.ProductName,
                             //SO/WBS
                             SoWbs = string.IsNullOrEmpty(subPoDetail.SDDocument) ? subPoDetail.WBSElement : subPoDetail.SDDocument + " / " + subPoDetail.Item,
                             //Ngày giao hàng thực tế
                             ActualDeliveryDate = subTrans.DocumentDate,
                             //Thời gian QC
                             QCCreateTime = qc.LastEditTime,
                             //Trạng thái QC
                             QCStatus = qc.Status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                             //Kết quả
                             QCResult = r.CatalogText_vi,
                             //nHÂN VIÊN
                             QCEmployee = subNv.FullName,
                             //Ngày hàng về
                             isGoodsArrive = nvl.isGoodsArrive,
                             GoodsArriveDate = nvl.GoodsArriveDate
                         });

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = CustomSearchRepository.CustomSearchFunc(new DatatableViewModel
            {
                start = ((searchVm.pageNumber - 1) * searchVm.pageSize),
                length = searchVm.pageSize,
            }, out filteredResultsCount, out totalResultsCount, query, "STT");

            if (res != null && res.Count() > 0)
            {
                int i = ((searchVm.pageNumber - 1) * searchVm.pageSize) + 1;

                res = res.GroupBy(x => new
                {
                    x.QualityControlId,
                    x.BarcodeCreateDate,
                    x.Vendor,
                    x.Material,
                    x.ActualDeliveryDate,
                    x.QCCreateTime,
                    x.QCStatus,
                    x.QCResult,
                    x.QCEmployee
                }).Select(x => new QualityControlNVLListViewModel
                {
                    QualityControlId = x.Key.QualityControlId,
                    BarcodeCreateDate = x.Key.BarcodeCreateDate,
                    Vendor = x.Key.Vendor,
                    Material = x.Key.Material,
                    ActualDeliveryDate = x.Key.ActualDeliveryDate,
                    QCCreateTime = x.Key.QCCreateTime,
                    QCStatus = x.Key.QCStatus,
                    QCResult = x.Key.QCResult,
                    QCEmployee = x.Key.QCEmployee,
                    PONumber = string.Join(',', x.Select(x => x.PONumber).Distinct().ToArray()),
                    SoWbs = string.Join(',', x.Select(x => x.SoWbs).Distinct().ToArray()),
                }).ToList();

                foreach (var item in res)
                {
                    item.STT = i;
                    i++;
                }

                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Data = res });
            }
            else
            {
                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Message = "Không tìm thấy phiếu kiểm tra nào!", Data = res });
            }
        }

        [HttpPost("QCKCSFiltered")]
        public async Task<IActionResult> PostQCKCSFilteredAsync([FromForm] QualityControlSearchViewModel2 searchModel)
        {
            var searchVm = new QualityControlSearchViewModel();

            searchVm.SaleOrgCode = searchModel.SaleOrgCode;
            searchVm.WorkShopCode = searchModel.WorkShopCode;
            searchVm.WorkCenterCode = searchModel.WorkCenterCode;
            searchVm.ProfileCode = searchModel.ProfileCode;
            searchVm.ProfileName = searchModel.ProfileName;
            searchVm.LSXSAP = searchModel.LSXSAP;
            searchVm.ProfileCode = searchModel.ProfileCode;
            searchVm.ProductName = searchModel.ProductName;
            searchVm.Status = searchModel.Status;
            searchVm.Result = searchModel.Result;

            // Select
            searchVm.ConfirmCommonDate = searchModel.ConfirmCommonDate;
            searchVm.QualityCommonDate = searchModel.QualityCommonDate;

            //
            searchVm.ConfirmFromDate = searchModel.ConfirmFromDate;
            searchVm.ConfirmToDate = searchModel.ConfirmToDate;

            // 
            searchVm.QualityFromDate = searchModel.QualityFromDate;
            searchVm.QualityToDate = searchModel.QualityToDate;

            //searchVm.QRCode = searchModel.QRCode;

            if (searchModel.ConfirmCommonDate != "Custom")
            {
                DateTime? fromDate;
                DateTime? toDate;
                _unitOfWork.CommonDateRepository.GetDateBy(searchModel.ConfirmCommonDate, out fromDate, out toDate);
                //Tìm kiếm kỳ hiện tại
                searchVm.ConfirmFromDate = fromDate;
                searchVm.ConfirmToDate = toDate;
            }

            if (searchModel.QualityCommonDate != "Custom")
            {
                DateTime? fromDate;
                DateTime? toDate;
                _unitOfWork.CommonDateRepository.GetDateBy(searchModel.QualityCommonDate, out fromDate, out toDate);
                //Tìm kiếm kỳ hiện tại
                searchVm.QualityFromDate = fromDate;
                searchVm.QualityToDate = toDate;
            }

            //var query = await _unitOfWork.QualityControlRepository.SearchAsync(searchVm);
            var query = await _unitOfWork.QualityControlRepository.SearchKCSAsync(searchVm);

            int i = 0;
            foreach (var item in query)
            {
                i++;
                item.STT = i;
            }

            if (query == null || query.Count > 0)
                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Data = query });
            else
                return NotFound(new { Code = HttpStatusCode.NotFound, Success = false, Data = "Không tìm thấy phiếu kiểm tra nào!" });
        }

        #region Helper
        #region Lưu file đính kèm
        /// <summary>
        /// Lưu file đính kèm
        /// </summary>
        /// <param name="ObjectId"></param>
        /// <param name="item"></param>
        /// <returns></returns>
        private FileAttachmentModel SaveFileAttachment(Guid ObjectId, IFormFile item, string folder)
        {
            //FileAttachmentModel
            var fileNew = new FileAttachmentModel();
            //1. GUID
            fileNew.FileAttachmentId = Guid.NewGuid();
            //2. Mã Profile
            fileNew.ObjectId = ObjectId;
            //3. Tên file
            fileNew.FileAttachmentName = item.FileName;
            //4. Đường dẫn
            fileNew.FileUrl = _uploadFilesLibrary.UploadFile(item, folder).Result.Trim();
            //5. Đuôi file
            fileNew.FileExtention = _unitOfWork.UtilitiesRepository.FileExtension(item.FileName);
            //7. Loại file
            fileNew.FileAttachmentCode = _unitOfWork.UtilitiesRepository.GetFileTypeByExtension(fileNew.FileExtention);
            //7. Người tạo
            fileNew.CreateBy = CurrentUser.AccountId;
            //8. Thời gian tạo
            fileNew.CreateTime = DateTime.Now;
            _context.Entry(fileNew).State = EntityState.Added;
            return fileNew;
        }
        #endregion
        #endregion

        #region Lấy thông tin danh sách QC qua Barcode
        /// <summary>API "Kiểm tra chất lượng" - Lấy danh sách phiếu kiểm tra chất lượng </summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/QualityControl/GetListQCTicketByBarcode?Barcode={barcode}
        ///     Params: 
        ///             + version : 1
        ///             + barcode : 2974AB7A-5251-48A7-A1AE-656314C40D6E
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": [
        ///             {
        ///                 "qualityControlModel": "175cb80e-034c-4111-818e-00e84ea783a8",
        ///                 "saleOrgCode": "1000",
        ///                 "workCenterCode": "LRN",
        ///                 "lsxdt": "DT-369-21-SMA",
        ///                 "lsxsap": "100000763",
        ///                 "dsx": "DT-369-21-SMA-D2",
        ///                 "productCode": "500010914",
        ///                 "productName": "500010914 | Cụm khung bao-ĐG-171x140x2200",
        ///                 "confirmDate": "2022-02-21T00:00:00",
        ///                 "qualityDate": "2022-02-21T00:00:00",
        ///                 "status": true,
        ///                 "result": "Pass"
        ///             },
        ///             {
        ///                 "qualityControlModel": "b83e3450-b124-4530-b9f4-c898d17e87fe",
        ///                 "saleOrgCode": "1000",
        ///                 "workCenterCode": "SC",
        ///                 "lsxdt": "DT-369-21-SMA",
        ///                 "lsxsap": "100000763",
        ///                 "dsx": "DT-369-21-SMA-D2",
        ///                 "productCode": "500010914",
        ///                 "productName": "500010914 | Cụm khung bao-ĐG-171x140x2200",
        ///                 "confirmDate": "2022-02-21T00:00:00",
        ///                 "qualityDate": "2022-02-26T00:00:00",
        ///                 "status": true,
        ///                 "result": "Pass"
        ///             },
        ///             {
        ///         "qualityControlModel": "9a457a51-c17e-432a-abbb-f0854d96c4a5",
        ///                 "saleOrgCode": "1000",
        ///                 "workCenterCode": "TC",
        ///                 "lsxdt": "DT-369-21-SMA",
        ///                 "lsxsap": "100000763",
        ///                 "dsx": "DT-369-21-SMA-D2",
        ///                 "productCode": "500010914",
        ///                 "productName": "500010914 | Cụm khung bao-ĐG-171x140x2200",
        ///                 "confirmDate": "2022-02-21T00:00:00",
        ///                 "qualityDate": null,
        ///                 "status": false,
        ///                 "result": null
        ///             }
        ///         ],
        ///         "additionalData": null
        ///     }
        /// 
        ///     
        ///</remarks>
        [HttpGet("GetListQCTicketByBarcode")]
        public async Task<ActionResult> GetListQCTicketByBarcode(Guid Barcode)
        {
            var searchVm = new QualityControlSearchViewModel();

            searchVm.SaleOrgCode = CurrentUser.CompanyCode;
            searchVm.QRCode = Barcode;

            var query = await _unitOfWork.QualityControlRepository.SearchAsync(searchVm);

            if (query == null || query.Count > 0)
                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Data = query });
            else
                return NotFound(new { Code = HttpStatusCode.NotFound, Success = false, Data = "Không tìm thấy phiếu kiểm tra nào!" });
        }
        #endregion

        #region Lấy thông tin danh sách QC qua Filter

        [HttpPost("GetListQCTicketByFilter")]
        public async Task<ActionResult> GetListQCTicketByFilter([FromForm] QualityControlSearchViewModel2 searchModel)
        {
            var searchVm = new QualityControlSearchViewModel();

            searchVm.SaleOrgCode = searchModel.SaleOrgCode;
            searchVm.WorkShopCode = searchModel.WorkShopCode;
            searchVm.WorkCenterCode = searchModel.WorkCenterCode;
            searchVm.ProfileCode = searchModel.ProfileCode;
            searchVm.ProfileName = searchModel.ProfileName;
            searchVm.LSXSAP = searchModel.LSXSAP;
            searchVm.ProfileCode = searchModel.ProfileCode;
            searchVm.ProductName = searchModel.ProductName;
            searchVm.Status = searchModel.Status;
            searchVm.Result = searchModel.Result;

            // Select
            searchVm.ConfirmCommonDate = searchModel.ConfirmCommonDate;
            searchVm.QualityCommonDate = searchModel.QualityCommonDate;

            //
            searchVm.ConfirmFromDate = searchModel.ConfirmFromDate;
            searchVm.ConfirmToDate = searchModel.ConfirmToDate;

            // 
            searchVm.QualityFromDate = searchModel.QualityFromDate;
            searchVm.QualityToDate = searchModel.QualityToDate;

            searchVm.QualityType = searchModel.QualityType;


            //searchVm.QRCode = searchModel.QRCode;

            if (searchModel.ConfirmCommonDate != "Custom")
            {
                DateTime? fromDate;
                DateTime? toDate;
                _unitOfWork.CommonDateRepository.GetDateBy(searchModel.ConfirmCommonDate, out fromDate, out toDate);
                //Tìm kiếm kỳ hiện tại
                searchVm.ConfirmFromDate = fromDate;
                searchVm.ConfirmToDate = toDate;
            }

            if (searchModel.QualityCommonDate != "Custom")
            {
                DateTime? fromDate;
                DateTime? toDate;
                _unitOfWork.CommonDateRepository.GetDateBy(searchModel.QualityCommonDate, out fromDate, out toDate);
                //Tìm kiếm kỳ hiện tại
                searchVm.QualityFromDate = fromDate;
                searchVm.QualityToDate = toDate;
            }

            var query = await _unitOfWork.QualityControlRepository.SearchAsync(searchVm);

            int i = 0;
            foreach (var item in query)
            {
                i++;
                item.STT = i;
            }

            if (query == null || query.Count > 0)
            {
                return Ok(new ApiResponse { Code = (int)HttpStatusCode.OK, IsSuccess = true, Data = query });
            }
            else
            {
                return NotFound(new { Code = HttpStatusCode.NotFound, Success = false, Data = "Không tìm thấy phiếu kiểm tra nào!" });
            }
        }

        #endregion

        #region API Lấy thông tin DropDown Filter mặc định
        /// <summary>API "Lấy thông tin DropDown Filter mặc định"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/QualityControl/GetQCFilter
        ///     Params: 
        ///             + version : 1
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": {
        ///             "salesOrgCodes": [
        ///                 {
        ///                     "saleOrgCode": "1000",
        ///                     "storeName": "Công Ty Cổ Phần Tập Đoàn Kỹ Nghệ Gỗ Trường Thành"
        ///                 },
        ///                 {
        ///                     "saleOrgCode": "1200",
        ///                     "storeName": "TTF Sofa"
        ///                 }
        ///             ],
        ///             "workCenters": [
        ///                 {
        ///                     "workCenterCode": "HTD",
        ///                     "workCenterName": "Hoàn Thiện - Đóng Gói"
        ///                 },
        ///                 {
        ///                     "workCenterCode": "LRN",
        ///                     "workCenterName": "Lắp Ráp - Hoàn Thiện"
        ///                 },
        ///                 {
        ///                     "workCenterCode": "SC",
        ///                     "workCenterName": "Sơ chế"
        ///                 },
        ///                 {
        ///                     "workCenterCode": "TC",
        ///                     "workCenterName": "Tinh chế"
        ///                 }
        ///             ],
        ///             "workShops": [
        ///                 {
        ///                     "workShopCode": "20000051",
        ///                     "workShopName": "20000051 | Phân xưởng Bàn - Ghế 4"
        ///                 },
        ///                 {
        ///                     "workShopCode": "20000512",
        ///                     "workShopName": "20000512 | Phân xưởng Bàn - Ghế 5"
        ///                 }
        ///             ],
        ///             "commonDates": [
        ///                 {
        ///                     "catalogCode": "Custom",
        ///                     "catalogText_vi": "Tùy chỉnh"
        ///                 },
        ///                 {
        ///                     "catalogCode": "Yesterday",
        ///                     "catalogText_vi": "Hôm qua"
        ///                 },
        ///                 {
        ///                     "catalogCode": "Today",
        ///                     "catalogText_vi": "Hôm nay"
        ///                 }
        ///            ]
        ///         },
        ///         "additionalData": {
        ///             "selectedConfirmCommonDate": "Today",
        ///             "selectedQCCommonDate": "Custom",
        ///             "selectedSalesOrgCode": "1000"
        ///         }
        ///     }
        ///</remarks>
        [HttpGet("GetQCFilter")]
        public async Task<ActionResult> GET()
        {
            #region  Common Date
            var SelectedConfirmCommonDate = "Today";
            var SelectedQCCommonDate = "Custom";
            var SelectedSalesOrgCode = CurrentUser.SaleOrg;
            //Ngày confirm
            var commonDateList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstCatalogType.CommonDate);
            #endregion

            #region Công đoạn lớn
            var WorkCenterList = await _context.WorkCenterModel.Where(x => x.SaleOrgCode == CurrentUser.SaleOrg)
                                                         .Select(x => new
                                                         {
                                                             WorkCenterCode = x.WorkCenterCode,
                                                             WorkCenterName = x.WorkCenterName
                                                         })
                                                         .ToListAsync();
            #endregion

            #region Nhà máy
            var StoreList = await _context.StoreModel.Where(x => x.Actived == true)
                                               .Select(x => new
                                               {
                                                   SaleOrgCode = x.SaleOrgCode,
                                                   StoreName = x.SaleOrgCode + " | " + x.StoreName
                                               })
                                               .ToListAsync();
            #endregion

            #region Phân xưởng
            var WorkShopList = await _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == CurrentUser.CompanyId)
                                                     .OrderBy(p => p.OrderIndex)
                                                     .Select(x => new
                                                     {
                                                         x.WorkShopCode,
                                                         WorkShopName = x.WorkShopCode + " | " + x.WorkShopName
                                                     })
                                                     .ToListAsync();
            #endregion

            #region Loại kiểm tra
            //var QualityTypeList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControlType);
            //ViewBag.QualityType = new SelectList(QualityTypeList, "CatalogCode", "CatalogText_vi", viewModel?.QualityType);
            #endregion

            #region Nhân viên nhóm QC
            //var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRoles(ConstRoleCode.QC);
            //ViewBag.QualityChecker = new SelectList(QCEmployeeList, "AccountId", "SalesEmployeeName", viewModel?.QualityChecker ?? CurrentUser.AccountId);
            #endregion

            #region TestMethod  
            //var TestMethodList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Method);
            //ViewBag.TestMethod = new SelectList(TestMethodList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.TestMethod);
            #endregion

            #region Mức độ lấy mẫu
            //var SamplingLevelList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.SamplingLevel);
            //ViewBag.SamplingLevel = new SelectList(SamplingLevelList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.SamplingLevel);
            #endregion

            #region Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            //ViewBag.Result = new SelectList(ResultList, "CatalogCode", "CatalogText_vi", viewModel?.Result);
            #endregion

            #region Result Detail
            //var ResultDetailList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Result);
            //ViewBag.ResultDetail = new SelectList(ResultDetailList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.Result);
            #endregion

            #region Error
            //var ErrorList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Error).Select(x => new CatalogViewModel { CatalogCode = x.CatalogCode, CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi }).ToList();
            //ViewBag.CatalogCode = new SelectList(ErrorList, "CatalogCode", "CatalogText_vi");
            //ViewBag.ErrorList = ErrorList;
            #endregion

            #region Thông tin kiểm tra
            //var HangMucKiemTraList = (from p in _context.QualityControlInformationModel
            //                  from w in p.WorkCenterModel.DefaultIfEmpty()
            //                  orderby p.Code ascending
            //                  select new QualityControlInformationViewModel
            //                  {
            //                      Id = p.Id,
            //                      Code = p.Code,
            //                      Name = p.Code + " | " + p.Name,
            //                      WorkCenterCode = w.WorkCenterCode
            //                  }).ToList();
            //if (viewModel != null)
            //{
            //    HangMucKiemTraList = HangMucKiemTraList.Where(x => x.WorkCenterCode == viewModel.WorkCenterCode).ToList();

            //}
            //ViewBag.QualityControlInformationId = new SelectList(HangMucKiemTraList, "Id", "Name");
            //ViewBag.HangMucKiemTraList = HangMucKiemTraList;
            #endregion

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new { SalesOrgCodes = StoreList, WorkCenters = WorkCenterList, WorkShops = WorkShopList, CommonDates = commonDateList, Results = ResultList },
                AdditionalData = new { SelectedConfirmCommonDate, SelectedQCCommonDate, SelectedSalesOrgCode }
            });
        }
        #endregion

        private List<CatalogDropdownVm> GetErrorList(string reportIndices = null)
        {
            return _unitOfWork.CatalogRepository.GetByQCReportIndices(reportIndices);
        }


        [HttpGet("GetDefaultKCSFilter")]
        public async Task<ActionResult> GetDefaultKCSFilter()
        {
            #region  Common Date
            var SelectedConfirmCommonDate = "ThisWeek";
            var SelectedQCCommonDate = "Custom";
            var SelectedSalesOrgCode = CurrentUser.SaleOrg;
            //Ngày confirm
            var commonDateList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstCatalogType.CommonDate);
            #endregion

            #region Công đoạn lớn
            var WorkCenterList = await _context.WorkCenterModel.Where(x => x.SaleOrgCode == CurrentUser.SaleOrg)
                                                         .Select(x => new
                                                         {
                                                             WorkCenterCode = x.WorkCenterCode,
                                                             WorkCenterName = x.WorkCenterName
                                                         })
                                                         .ToListAsync();
            #endregion

            #region Nhà máy
            var StoreList = await _context.StoreModel.Where(x => x.Actived == true)
                                               .Select(x => new
                                               {
                                                   SaleOrgCode = x.SaleOrgCode,
                                                   StoreName = x.SaleOrgCode + " | " + x.StoreName
                                               })
                                               .ToListAsync();
            #endregion

            #region Phân xưởng
            var WorkShopList = await _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == CurrentUser.CompanyId)
                                                     .OrderBy(p => p.OrderIndex)
                                                     .Select(x => new
                                                     {
                                                         x.WorkShopCode,
                                                         WorkShopName = x.WorkShopCode + " | " + x.WorkShopName
                                                     })
                                                     .ToListAsync();
            #endregion

            #region Loại kiểm tra
            //var QualityTypeList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControlType);
            //ViewBag.QualityType = new SelectList(QualityTypeList, "CatalogCode", "CatalogText_vi", viewModel?.QualityType);
            #endregion

            #region Nhân viên nhóm QC
            //var QCEmployeeList = _unitOfWork.SalesEmployeeRepository.GetSalesEmployeeByRoles(ConstRoleCode.QC);
            //ViewBag.QualityChecker = new SelectList(QCEmployeeList, "AccountId", "SalesEmployeeName", viewModel?.QualityChecker ?? CurrentUser.AccountId);
            #endregion

            #region TestMethod  
            //var TestMethodList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Method);
            //ViewBag.TestMethod = new SelectList(TestMethodList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.TestMethod);
            #endregion

            #region Mức độ lấy mẫu
            //var SamplingLevelList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.SamplingLevel);
            //ViewBag.SamplingLevel = new SelectList(SamplingLevelList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.SamplingLevel);
            #endregion

            #region Result
            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);
            //ViewBag.Result = new SelectList(ResultList, "CatalogCode", "CatalogText_vi", viewModel?.Result);
            #endregion

            #region Result Detail
            //var ResultDetailList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Result);
            //ViewBag.ResultDetail = new SelectList(ResultDetailList, "CatalogCode", "CatalogText_vi", viewModel.QualityControlDetailViewModel?.Result);
            #endregion

            #region Error
            //var ErrorList = _unitOfWork.CatalogRepository.GetBy(ConstQualityControl.QualityControl_Error).Select(x => new CatalogViewModel { CatalogCode = x.CatalogCode, CatalogText_vi = x.CatalogCode + " | " + x.CatalogText_vi }).ToList();
            //ViewBag.CatalogCode = new SelectList(ErrorList, "CatalogCode", "CatalogText_vi");
            //ViewBag.ErrorList = ErrorList;
            #endregion

            #region Thông tin kiểm tra
            //var HangMucKiemTraList = (from p in _context.QualityControlInformationModel
            //                  from w in p.WorkCenterModel.DefaultIfEmpty()
            //                  orderby p.Code ascending
            //                  select new QualityControlInformationViewModel
            //                  {
            //                      Id = p.Id,
            //                      Code = p.Code,
            //                      Name = p.Code + " | " + p.Name,
            //                      WorkCenterCode = w.WorkCenterCode
            //                  }).ToList();
            //if (viewModel != null)
            //{
            //    HangMucKiemTraList = HangMucKiemTraList.Where(x => x.WorkCenterCode == viewModel.WorkCenterCode).ToList();

            //}
            //ViewBag.QualityControlInformationId = new SelectList(HangMucKiemTraList, "Id", "Name");
            //ViewBag.HangMucKiemTraList = HangMucKiemTraList;
            #endregion

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new { SalesOrgCodes = StoreList, WorkCenters = WorkCenterList, WorkShops = WorkShopList, CommonDates = commonDateList, Results = ResultList },
                AdditionalData = new { SelectedConfirmCommonDate, SelectedQCCommonDate, SelectedSalesOrgCode }
            });
        }

        [HttpGet("GetDefaultQCMauFilter")]
        public async Task<ActionResult> GetDefaultQCMauFilter()
        {
            #region  Common Date
            var SelectedConfirmCommonDate = "Custom";
            var SelectedQCCommonDate = "ThisWeek";
            var SelectedSalesOrgCode = CurrentUser.SaleOrg;
            //Ngày confirm
            var commonDateList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstCatalogType.CommonDate);
            #endregion

            #region Công đoạn lớn
            var WorkCenterList = await _context.WorkCenterModel.Where(x => x.SaleOrgCode == CurrentUser.SaleOrg)
                                                         .Select(x => new
                                                         {
                                                             WorkCenterCode = x.WorkCenterCode,
                                                             WorkCenterName = x.WorkCenterName
                                                         })
                                                         .OrderBy(w => w.WorkCenterCode)
                                                         .ToListAsync();
            #endregion

            #region Nhà máy
            var StoreList = await _context.StoreModel.Where(x => x.Actived == true)
                                               .Select(x => new
                                               {
                                                   SaleOrgCode = x.SaleOrgCode,
                                                   StoreName = x.SaleOrgCode + " | " + x.StoreName
                                               })
                                                .OrderBy(s => s.SaleOrgCode)
                                               .ToListAsync();
            #endregion

            #region Phân xưởng
            var WorkShopList = await _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == CurrentUser.CompanyId)
                                                     .OrderBy(p => p.WorkShopCode)
                                                     .Select(x => new
                                                     {
                                                         x.WorkShopCode,
                                                         WorkShopName = x.WorkShopCode + " | " + x.WorkShopName
                                                     })
                                                     .ToListAsync();
            #endregion

            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new { SalesOrgCodes = StoreList, WorkCenters = WorkCenterList, WorkShops = WorkShopList, CommonDates = commonDateList, Results = ResultList },
                AdditionalData = new { SelectedConfirmCommonDate, SelectedQCCommonDate, SelectedSalesOrgCode }
            });
        }

        [HttpGet("GetDefaultQCHienTruongFilter")]
        public async Task<ActionResult> GetDefaultQCHienTruongFilter()
        {
            #region  Common Date
            var SelectedConfirmCommonDate = "Custom";
            var SelectedQCCommonDate = "ThisWeek";
            var SelectedSalesOrgCode = CurrentUser.SaleOrg;
            //Ngày confirm
            var commonDateList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstCatalogType.CommonDate);
            #endregion

            #region Công đoạn lớn
            var WorkCenterList = await _context.WorkCenterModel.Where(x => x.SaleOrgCode == CurrentUser.SaleOrg)
                                                         .Select(x => new
                                                         {
                                                             WorkCenterCode = x.WorkCenterCode,
                                                             WorkCenterName = x.WorkCenterName
                                                         })
                                                         .ToListAsync();
            #endregion

            #region Nhà máy
            var StoreList = await _context.StoreModel.Where(x => x.Actived == true)
                                               .Select(x => new
                                               {
                                                   SaleOrgCode = x.SaleOrgCode,
                                                   StoreName = x.SaleOrgCode + " | " + x.StoreName
                                               })
                                               .OrderBy(s => s.SaleOrgCode)
                                               .ToListAsync();
            #endregion

            #region Phân xưởng
            var WorkShopList = await _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == CurrentUser.CompanyId)
                                                     .OrderBy(p => p.OrderIndex)
                                                     .Select(x => new
                                                     {
                                                         x.WorkShopCode,
                                                         WorkShopName = x.WorkShopCode + " | " + x.WorkShopName
                                                     })
                                                     .OrderBy(s => s.WorkShopCode)
                                                     .ToListAsync();
            #endregion



            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new { SalesOrgCodes = StoreList, WorkCenters = WorkCenterList, WorkShops = WorkShopList, CommonDates = commonDateList, Results = ResultList },
                AdditionalData = new { SelectedConfirmCommonDate, SelectedQCCommonDate, SelectedSalesOrgCode }
            });
        }

        [HttpGet("GetDefaultQCSanPhamFilter")]
        public async Task<ActionResult> GetDefaultQCSanPhamFilter()
        {
            #region  Common Date
            var SelectedConfirmCommonDate = "Custom";
            var SelectedQCCommonDate = "ThisWeek";
            var SelectedSalesOrgCode = CurrentUser.SaleOrg;
            //Ngày confirm
            var commonDateList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstCatalogType.CommonDate);
            #endregion

            #region Công đoạn lớn
            var WorkCenterList = await _context.WorkCenterModel.Where(x => x.SaleOrgCode == CurrentUser.SaleOrg)
                                                         .Select(x => new
                                                         {
                                                             WorkCenterCode = x.WorkCenterCode,
                                                             WorkCenterName = x.WorkCenterName
                                                         })
                                                         .ToListAsync();
            #endregion

            #region Nhà máy
            var StoreList = await _context.StoreModel.Where(x => x.Actived == true)
                                               .Select(x => new
                                               {
                                                   SaleOrgCode = x.SaleOrgCode,
                                                   StoreName = x.SaleOrgCode + " | " + x.StoreName
                                               })
                                               .ToListAsync();
            #endregion

            #region Phân xưởng
            var WorkShopList = await _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == CurrentUser.CompanyId)
                                                     .OrderBy(p => p.OrderIndex)
                                                     .Select(x => new
                                                     {
                                                         x.WorkShopCode,
                                                         WorkShopName = x.WorkShopCode + " | " + x.WorkShopName
                                                     })
                                                     .ToListAsync();
            #endregion

            var ResultList = _unitOfWork.CatalogRepository.GetByForDropdown(ConstQualityControl.QualityControl_Result);

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Data = new { SalesOrgCodes = StoreList, WorkCenters = WorkCenterList, WorkShops = WorkShopList, CommonDates = commonDateList, Results = ResultList },
                AdditionalData = new { SelectedConfirmCommonDate, SelectedQCCommonDate, SelectedSalesOrgCode }
            });
        }

        #region Update trạng thái về nhà máy
        /// <summary>
        /// Cập nhật trạng thái hàng về nhà máy
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("update-status-arrive")]
        public async Task<IActionResult> POST([FromBody] UpdateStatusGoodsArriveRequest request)
        {
            //Thẻ treo
            var rawMaterialCard = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == request.RawMaterialCardId);

            if (rawMaterialCard is null)
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.BadRequest,
                    IsSuccess = false,
                    Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo NVL của phiếu kiểm tra")
                });

            //Cập nhật hàng về nhà máy
            rawMaterialCard.isGoodsArrive = true;
            rawMaterialCard.GoodsArriveDate = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Cập nhật trạng thái hàng về nhà máy")
            });
        }
        #endregion



        [HttpGet("GetListLSQSAP")]
        public IActionResult GetListLSQSAP([FromQuery] string q)
        {
            try
            {
                var lsxSAPs = q;
                var ret = new List<string>();

                if (lsxSAPs == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Data = ret
                    });
                }

                //return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });

                //List<string> soToKhais = new List<string>();

                //soToKhais = _context.SoToKhaiModel.Where(x => x.Import == true && x.SoToKhai.Contains(q))
                //                                  .Distinct().Take(20)
                //                                  .Select(s => s.SoToKhai)
                //                                  .ToList();

                var customerList = new List<string> {
                    "NATUZZI S." // Note: must be exact match "Natuzzi" will not work
                    ,"TOV FURNIT"
                }; // You can add more variations if needed

                var result = (from pom in _context.ProductionOrderModel
                              where (from tm in _context.TaskModel
                                     where (from sohm in _context.SaleOrderHeader100Model
                                                //where sohm.SORTL.Contains("Natuzzi")
                                                //where customerList.Contains(sohm.SORTL)
                                                //where customerList.Any(c => sohm.SORTL.Contains(c)) // Update, show all
                                            select sohm.VBELN).
                                            Contains(tm.Property1)
                                     select tm.TaskId
                                     ).Contains(pom.ProductionOrderId)
                                     && pom.AUFNR_MES.Contains(q)
                              orderby pom.CreateTime descending

                              select pom.AUFNR_MES)
                              .Distinct()
                              .OrderByDescending(x => x)
                              .Take(100)
                              .ToList();


                ret = result;
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = ret
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        [HttpGet("GetListPhanXuong")]
        public IActionResult GetListPhanXuong([FromQuery] string q)
        {
            try
            {
                var companyCode = q;
                var ret = new List<CatalogDropdownVm>();

                if (companyCode == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Data = ret
                    });
                }

                var result = (from workshop in _context.WorkShopModel
                              join company in _context.CompanyModel on workshop.CompanyId equals company.CompanyId
                              where company.CompanyCode == companyCode && workshop.Actived == true
                              orderby workshop.WorkShopCode
                              select new CatalogDropdownVm
                              {
                                  CatalogCode = workshop.WorkShopCode,
                                  CatalogText_vi = workshop.WorkShopCode + " | " + workshop.WorkShopName
                              }).ToList();
                ret = result;
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = ret
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        [HttpGet("GetListCongDoan")]
        public IActionResult GetListCongDoan([FromQuery] string q)
        {
            try
            {
                var companyCode = q;
                var ret = new List<CatalogDropdownVm>();

                if (companyCode == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Data = ret
                    });
                }

                var result = (from congDoan in _context.RoutingModel
                              where congDoan.Plant == companyCode && congDoan.Actived == true
                              orderby congDoan.StepCode
                              select new CatalogDropdownVm
                              {
                                  CatalogCode = congDoan.StepCode,
                                  CatalogText_vi = congDoan.StepCode + " | " + congDoan.StepName
                              }).ToList();
                ret = result;
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = ret
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        [HttpGet("GetListPhanXuongCongDoan")]
        public IActionResult GetListPhanXuongCongDoan([FromQuery] string q)
        {
            try
            {
                var companyCode = q;
                var ret = new List<CatalogDropdownVm>();

                if (companyCode == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Data = ret
                    });
                }

                var listPhanXuong = (from workshop in _context.WorkShopModel
                                     join company in _context.CompanyModel on workshop.CompanyId equals company.CompanyId
                                     where company.CompanyCode == companyCode && workshop.Actived == true
                                     orderby workshop.WorkShopCode
                                     select new CatalogDropdownVm
                                     {
                                         CatalogCode = workshop.WorkShopCode,
                                         CatalogText_vi = workshop.WorkShopCode + " | " + workshop.WorkShopName
                                     }).ToList();

                var listCongDoan = (from congDoan in _context.RoutingModel
                                    where congDoan.Plant == companyCode && congDoan.Actived == true
                                    orderby congDoan.StepCode
                                    select new CatalogDropdownVm
                                    {
                                        CatalogCode = congDoan.StepCode,
                                        CatalogText_vi = congDoan.StepCode + " | " + congDoan.StepName
                                    }).ToList();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = new { PhanXuong = listPhanXuong, CongDoan = listCongDoan }
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message, ex.InnerException);
            }
        }
    }
}