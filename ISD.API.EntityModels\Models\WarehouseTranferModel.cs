﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseTranferModel", Schema = "MESP2")]
    public partial class WarehouseTranferModel
    {
        [Key]
        public Guid WarehouseTranferId { get; set; }
        public Guid? ReservationId { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        public Guid? SlocExportId { get; set; }
        public Guid? StorageBinExportId { get; set; }
        public Guid? SlocImportId { get; set; }
        public Guid? StorageBinImportId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        public Guid? RawMaterialCardId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExportDate { get; set; }
        public int? ExportDateKey { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ImportDate { get; set; }
        public int? ImportDateKey { get; set; }
        [StringLength(50)]
        public string StatusCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool? Actived { get; set; }

        [ForeignKey("ReservationId")]
        [InverseProperty("WarehouseTranferModel")]
        public virtual MaterialReservationModel Reservation { get; set; }
        [ForeignKey("SlocExportId")]
        [InverseProperty("WarehouseTranferModelSlocExport")]
        public virtual SlocModel SlocExport { get; set; }
        [ForeignKey("SlocImportId")]
        [InverseProperty("WarehouseTranferModelSlocImport")]
        public virtual SlocModel SlocImport { get; set; }
        [ForeignKey("StorageBinExportId")]
        [InverseProperty("WarehouseTranferModelStorageBinExport")]
        public virtual StorageBinModel StorageBinExport { get; set; }
        [ForeignKey("StorageBinImportId")]
        [InverseProperty("WarehouseTranferModelStorageBinImport")]
        public virtual StorageBinModel StorageBinImport { get; set; }
    }
}