import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/qualityControlApi.dart';
import 'package:path/path.dart';
import '../Widget/dialogWidget/DialogImage.dart';
import '../element/ImageQuatity.dart';
import '../element/RowDetail.dart';
import '../element/TableInfo.dart';
import '../element/listImagePicker.dart';
import '../model/rawMaterialCard.dart';
import '../repository/function/imageFunction.dart';
import '../repository/function/qualityControlDetailFunction.dart';
import '../repository/function/qualityControlFunction.dart';

class QualityControlView extends StatelessWidget {
  final DateTime? date;
  final ValueChanged<DateTime?> setDate;
  final QualityControl? qualityControl;
  final List<ResultList>? lsResultList;
  final List<QualityTypeList>? qualityTypeList;
  final DataRawMeterial? dataRawMaterial;
  final String getDate;
  final QualityCheckerInfo? selectedStaff;
  final ValueChanged<QualityTypeList?> getSelectedType;
  final QualityTypeList? selectedType;
  final TextEditingController controller_1;
  final TextEditingController controller_2;
  final List<File> lsFileTabCheck;
  final ImagePicker pickerImage;
  final ValueChanged<File> pickFile;
  final ValueChanged<int> deleteListImageTabCheck;
  final ResultList? selectedResult;
  final ValueChanged<ResultList?> getSelectedResult;
  final bool errorSelectType;
  final bool errorP0;
  final bool errorQuantityCheck;
  final bool errorSelectedResultQualityView;
  final VoidCallback onChangePO;
  final VoidCallback onChangeQuantityChecker;

  const QualityControlView(
      {Key? key,
      required this.date,
      required this.setDate,
      required this.qualityControl,
      required this.lsResultList,
      required this.qualityTypeList,
      required this.dataRawMaterial,
      required this.getDate,
      required this.selectedStaff,
      required this.getSelectedType,
      required this.selectedType,
      required this.controller_1,
      required this.controller_2,
      required this.lsFileTabCheck,
      required this.pickerImage,
      required this.pickFile,
      required this.deleteListImageTabCheck,
      required this.selectedResult,
      required this.getSelectedResult,
      required this.errorSelectType,
      required this.errorP0,
      required this.errorQuantityCheck,
      required this.errorSelectedResultQualityView,
      required this.onChangePO,
      required this.onChangeQuantityChecker})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: SafeArea(
        minimum: EdgeInsets.symmetric(horizontal: 10.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            SizedBox(height: 15.h),
            qualityControl != null
                ? qualityControl!.qcType != "NVL"
                    ? _InfoBTP(qualityControl: qualityControl)
                    : _InfoNVL(dataRawMaterial: dataRawMaterial)
                : _InfoBTP(qualityControl: qualityControl),
            Visibility(visible: qualityControl!.qcType != "NVL", child: const _FieldQuantity(field: "Tình trạng MT:")
                // Text(
                //   "Tình trạng MT:",
                //   style: TextStyle(
                //       fontSize: 12.sp,
                //       fontWeight: FontWeight.bold),d
                // )
                ),
            SizedBox(height: qualityControl!.qcType != "NVL" ? 15.h : 0),
            Row(
              children: <Widget>[
                const Expanded(flex: 3, child: _FieldQuantity(field: "Ngày kiểm tra:")
                    // Text(
                    //   "Ngày kiểm tra",
                    //   style: TextStyle(
                    //       fontSize: 12.sp,
                    //       fontWeight: FontWeight.bold),
                    // ),
                    ),
                SizedBox(width: 10.w),
                Expanded(
                  flex: 7,
                  child: GestureDetector(
                    onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                        ? null
                        : () async {
                            // _pickDateIOS(context);
                            if (Platform.isAndroid) {
                              final getDate = await QualityControlFunction.pickDate(context, date);
                              setDate(getDate);
                            } else {
                              final getDateIOS = await QualityControlFunction.pickDateIOS(context);
                              setDate(getDateIOS);
                            }
                            // Platform.isAndroid
                            //     ? _pickDate(context)
                            //     : _pickDateIOS(
                            //     context);
                            // _pickDateIos(context);
                          },
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.5, color: Colors.grey.shade400),
                        borderRadius: BorderRadius.circular(3.r),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 9,
                            child: Text(
                              getDate,
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                            ),
                            // Text(
                            //   _getDate,
                            //   style: TextStyle(fontSize: 12.sp),
                            // ),
                          ),
                          Expanded(flex: 1, child: Icon(Icons.edit_calendar_rounded, size: 15.sp, color: Colors.blue)),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: 15.h),
            Row(
              children: <Widget>[
                const Expanded(flex: 3, child: _FieldQuantity(field: "NV kiểm tra")
                    // Text(
                    //   "NV kiểm tra",
                    //   style: TextStyle(
                    //       fontSize: 12.sp,
                    //       fontWeight: FontWeight.bold),
                    // ),
                    ),
                SizedBox(width: 10.w),
                Expanded(
                  flex: 7,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                    decoration: BoxDecoration(
                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(3.r),
                    ),
                    child: Text(
                      selectedStaff == null ? "" : selectedStaff!.salesEmployeeName ?? "",
                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                    ),
                    // DropdownButtonHideUnderline(
                    //   child: DropdownButton<QualityCheckerList>(
                    //     isExpanded: true,
                    //     isDense: true,
                    //     itemHeight: null,
                    //     value: _selectedStaff ?? QualityControlFunction.defaultValueQC,
                    //     iconSize: 15.sp,
                    //     style: const TextStyle(
                    //         color: Colors.white),
                    //     onChanged: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null ? null:_getSelectedStaff,
                    //     items: _qualityCheckerList!.map((QualityCheckerList staff) {
                    //       return DropdownMenuItem<QualityCheckerList>(
                    //           value: staff,
                    //           child: Padding(
                    //             padding: EdgeInsets.symmetric(vertical: 5.h),
                    //             child: Text(
                    //               staff.salesEmployeeName.toString(),
                    //               style: TextStyle(
                    //                   color: Colors.black,
                    //                   fontSize: 11.sp),
                    //             ),
                    //
                    //           ));
                    //     }).toList(),
                    //     selectedItemBuilder: (BuildContext context) {
                    //       return _qualityCheckerList!.map<Widget>((QualityCheckerList staff) {
                    //         return Text(staff.salesEmployeeName.toString(), style: TextStyle(
                    //             color: Colors.black,
                    //             fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                    //       }).toList();
                    //     },
                    //   ),
                    // ),
                  ),
                  // Container(
                  //   // height: 33.h,
                  //   decoration: BoxDecoration(
                  //     border: Border.all(
                  //         width: 0.5,
                  //         color: _error == true
                  //             ? const Color(
                  //                 0xFFD32F2F)
                  //             : Colors.grey.shade400),
                  //   ),
                  //   child:
                  //       DropdownButtonHideUnderline(
                  //     child: DropdownButton<QualityCheckerList>(
                  //       isExpanded: true,
                  //       value: _selectedStaff ??
                  //           QualityControlFunction
                  //               .defaultValueQC,
                  //       iconSize: 15.sp,
                  //       style: const TextStyle(
                  //           color: Colors.black),
                  //       onChanged: _qualityControl!.qcType == "NVL" && _qualityControl!.qualityChecker != null ? null:_getSelectedStaff,
                  //       items: _qualityCheckerList!.map((QualityCheckerList staff) {
                  //         return DropdownMenuItem<
                  //                 QualityCheckerList>(
                  //             value: staff,
                  //             child: Center(
                  //               child: Text(
                  //                 staff
                  //                     .salesEmployeeName
                  //                     .toString(),
                  //                 style: TextStyle(
                  //                     color: Colors
                  //                         .black,
                  //                     fontSize:
                  //                         11.sp),
                  //               ),
                  //             ));
                  //       }).toList(),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(
                  //     height:
                  //         _error == true ? 10.h : 0),
                  // Visibility(
                  //     visible: _error,
                  //     child: Row(
                  //       mainAxisAlignment:
                  //           MainAxisAlignment.start,
                  //       children: <Widget>[
                  //         Flexible(
                  //           flex: 1,
                  //           child: Icon(
                  //               Icons.error_outline,
                  //               size: 13.sp,
                  //               color:
                  //                   Colors.red[700]),
                  //         ),
                  //         SizedBox(width: 5.w),
                  //         Flexible(
                  //             flex: 8,
                  //             child: Text(
                  //                 'Bạn chưa chọn NV kiểm tra',
                  //                 style: TextStyle(
                  //                     fontSize: 11.sp,
                  //                     color: Colors
                  //                         .red[700])))
                  //       ],
                  //     )),
                )
              ],
            ),
            SizedBox(height: 15.h),
            Row(
              children: <Widget>[
                const Expanded(flex: 3, child: _FieldQuantity(field: "Loại kiểm tra")
                    // Text(
                    //   "Loại kiểm tra",
                    //   style: TextStyle(
                    //       fontSize: 12.sp,
                    //       fontWeight: FontWeight.bold),
                    // ),
                    ),
                SizedBox(width: 10.w),
                Expanded(
                  flex: 7,
                  child: Column(children: <Widget>[
                    Container(
                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.5, color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(3.r),
                        ),
                        child: _DropdownType(
                            qualityControl: qualityControl,
                            onChangeType: getSelectedType,
                            qualityTypeList: qualityTypeList,
                            selectedType: selectedType)),
                    SizedBox(height: errorSelectType == true ? 10.h : 0),
                    _ErrorValidateView(error: errorSelectType, text: "Bạn chưa chọn loại kiểm tra")
                  ]),
                )
              ],
            ),
            SizedBox(height: 15.h),
            Visibility(
                visible: qualityControl!.qcType != "NVL",
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      flex: 3,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "PO",
                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      flex: 7,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(children: <Widget>[
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                            decoration:
                                BoxDecoration(border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                            child: TextFormField(
                              maxLines: null,
                              keyboardType: TextInputType.number,
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                              ],
                              textAlign: TextAlign.center,
                              controller: controller_1,
                              style: TextStyle(fontSize: 12.sp),
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                isDense: true,
                                contentPadding: EdgeInsets.zero,
                                errorBorder: InputBorder.none,
                                disabledBorder: InputBorder.none,
                                filled: true,
                                fillColor: Colors.white,
                                hintStyle: TextStyle(fontSize: 12.sp),
                              ),
                              onChanged: (value) {
                                onChangePO();
                              },
                            ),
                          ),
                          SizedBox(height: errorP0 == true ? 10.h : 0),
                          _ErrorValidateView(error: errorP0, text: "Vui lòng nhập PO"),
                        ]),
                      ),
                    )
                  ],
                )),

            SizedBox(height: qualityControl!.qcType != "NVL" ? 15.h : 0),
            Row(
              children: <Widget>[
                Expanded(
                  flex: 3,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "SL kiểm tra",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                  flex: 7,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Column(children: <Widget>[
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                        decoration:
                            BoxDecoration(border: Border.all(width: 0.5, color: Colors.grey.shade400), borderRadius: BorderRadius.circular(3.r)),
                        child: TextFormField(
                          enabled: qualityControl!.qcType == "NVL"
                              ? qualityControl!.qualityChecker != null
                                  ? false
                                  : true
                              : true,
                          maxLines: null,
                          textAlign: TextAlign.center,
                          controller: controller_2,
                          style: TextStyle(fontSize: 12.sp),
                          keyboardType: TextInputType.number,
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                          ],
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.zero,
                            errorBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                            filled: true,
                            fillColor: Colors.white,
                            hintStyle: TextStyle(fontSize: 12.sp),
                          ),
                          onChanged: (value) {
                            onChangeQuantityChecker();
                          },
                        ),
                      ),
                      SizedBox(height: errorQuantityCheck == true ? 10.h : 0),
                      _ErrorValidateView(error: errorQuantityCheck, text: "Vui lòng nhập SL kiểm tra"),
                    ]),
                  ),
                )
              ],
            ),
            SizedBox(height: 15.h),
            Row(
              children: <Widget>[
                Expanded(
                  flex: 3,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Hình ảnh",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                    flex: 7,
                    child: lsFileTabCheck.isEmpty
                        ? Row(
                            children: <Widget>[
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.r),
                                  color: Colors.grey.shade100,
                                ),
                                child: InkWell(
                                  onTap: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null
                                      ? null
                                      : () async {
                                          final check = await QualityControlFunction.pickImage(context);
                                          debugPrint(check.toString());
                                          if (check != null) {
                                            bool checkPermission = await ImageFunction.handlePermission(check);
                                            if (checkPermission == true) {
                                              if (check == true) {
                                                List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                                  maxWidth: globalImageConfig.maxWidth,
                                                  maxHeight: globalImageConfig.maxHeight,
                                                  imageQuality: globalImageConfig.imageQuality,
                                                );
                                                if (selectedImages.isEmpty) return;
                                                for (var i in selectedImages) {
                                                  final itemImage = await ImageFunction.saveImageMulti(i.path);
                                                  pickFile(itemImage);
                                                }
                                              } else {
                                                final image = await ImagePicker().pickImage(
                                                    maxWidth: globalImageConfig.maxWidth,
                                                    maxHeight: globalImageConfig.maxHeight,
                                                    imageQuality: globalImageConfig.imageQuality,
                                                    source: ImageSource.camera);
                                                if (image == null) return;
                                                final imageProfile = await ImageFunction.saveImage(image.path);
                                                pickFile(imageProfile);
                                              }
                                            }
                                          }
                                        },
                                  child: Text(
                                    "Chọn tệp",
                                    style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                  ),
                                ),
                              ),
                              SizedBox(width: 10.w),
                              Center(
                                child: Text(
                                  "Chưa chọn tệp nào",
                                  style: TextStyle(fontSize: 11.sp),
                                ),
                              ),
                            ],
                          )
                        : Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 9.w),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.r),
                                color: Colors.grey.shade100,
                              ),
                              child: InkWell(
                                onTap: () async {
                                  // debugPrint('yes');
                                  final check = await QualityControlFunction.pickImage(context);
                                  debugPrint(check.toString());
                                  if (check != null) {
                                    bool checkPermission = await ImageFunction.handlePermission(check);
                                    if (checkPermission == true) {
                                      if (check == true) {
                                        List<XFile>? selectedImages = await pickerImage.pickMultiImage(
                                          maxWidth: globalImageConfig.maxWidth,
                                          maxHeight: globalImageConfig.maxHeight,
                                          imageQuality: globalImageConfig.imageQuality,
                                        );
                                        if (selectedImages.isEmpty) return;
                                        for (var i in selectedImages) {
                                          final itemImage = await ImageFunction.saveImageMulti(i.path);
                                          pickFile(itemImage);
                                        }
                                      } else {
                                        final image = await ImagePicker().pickImage(
                                            maxWidth: globalImageConfig.maxWidth,
                                            maxHeight: globalImageConfig.maxHeight,
                                            imageQuality: globalImageConfig.imageQuality,
                                            source: ImageSource.camera);
                                        if (image == null) return;
                                        final imageProfile = await ImageFunction.saveImage(image.path);
                                        pickFile(imageProfile);
                                      }
                                    }
                                  }
                                },
                                child: Text(
                                  "Chọn tệp",
                                  style: TextStyle(fontSize: 11.sp, color: Colors.blue),
                                ),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            _ListChooseImage(lsFileTabCheck: lsFileTabCheck, deleteListImageTabCheck: deleteListImageTabCheck),
                            SizedBox(height: 10.h),
                          ])),
              ],
            ),
            SizedBox(height: 15.h),
            Row(
              children: <Widget>[
                Expanded(
                  flex: 3,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Kết quả",
                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                  flex: 7,
                  child: Column(children: <Widget>[
                    Container(
                        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                          borderRadius: BorderRadius.circular(3.r),
                        ),
                        child: _DropdownQuantityResult(
                            selectedResult: selectedResult,
                            lsResultList: lsResultList,
                            qualityControl: qualityControl,
                            onChangeResult: getSelectedResult)),
                    SizedBox(height: errorSelectedResultQualityView == true ? 10.h : 0),
                    _ErrorValidateView(error: errorSelectedResultQualityView, text: "Vui lòng chọn kết quả"),
                  ]),
                )
              ],
            ),
            Visibility(
              visible: qualityControl!.fileViewModel == null || qualityControl!.fileViewModel!.isEmpty ? false : true,
              child: Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton.icon(
                  icon: Icon(
                    Icons.image,
                    color: Colors.white,
                    size: 15.sp,
                  ),
                  style: ButtonStyle(
                    side: MaterialStateProperty.all(
                      const BorderSide(
                        color: Color(0xff0052cc),
                      ),
                    ),
                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                  ),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return DialogImage(title: 'HÌNH ẢNH KIỂM TRA', listImage: qualityControl!.fileViewModel);
                      },
                    );
                  },
                  label: Text(
                    'Hình ảnh',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 11.sp,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 15.h),
            // GestureDetector(
            //   onTap: () {
            //     _checkError();
            //     if (_error_2 == false &&
            //         _error_3 == false &&
            //         _error_4 == false &&
            //         _error_5 == false) {
            //       FocusScope.of(context).unfocus();
            //       final sendSelectedType = _selectedType!.catalogCode == " "
            //               ? _defaultSelectedType!
            //               : _selectedType!;
            //       final sendSelectedStaff = _selectedStaff!;
            //       final sendSelectedResult = _selectedResult!.catalogCode ==
            //                   " "
            //               ? _defaultSelectedResult!
            //               : _selectedResult!;
            //       Navigator.pushNamed(context,
            //           '/QualityControlDetailView',
            //           arguments: ScreenArgumentDetailQuality(
            //               widget.token,
            //               _qualityControlDetail,
            //               _qualityControlModel,
            //               _qualityControl,
            //               _lsTestMethodList,
            //               _lsResultList,
            //               _lsSamplingLevelList,
            //               _lsError,
            //               _lsQualityControlInformationIdList,
            //               _lsQualityControlInformation,
            //               _lsErrorList,
            //               sendSelectedStaff,
            //               sendSelectedType,
            //               _controller_1.text.isEmpty
            //                   ? _qualityControl!.po
            //                       .toString()
            //                   : _controller_1.text,
            //               _controller_2.text.isEmpty
            //                   ? (_qualityControl!.inspectionLotQuantity!.round()).toString()
            //                   : _controller_2.text,
            //               sendSelectedResult,
            //               _lsFileTabCheck,
            //               _date!.toIso8601String(),
            //               widget.dateTimeOld
            //               // QualityControlFunction.formatDatePost(_qualityControl, _date)
            //           ));
            //     }
            //   },
            //   child: Container(
            //     padding: EdgeInsets.symmetric(
            //         vertical: 15.h),
            //     width: double.infinity,
            //     decoration: BoxDecoration(
            //       borderRadius: BorderRadius.all(
            //         Radius.circular(5.r),
            //       ),
            //       color: const Color(0xff0052cc),
            //     ),
            //     child: Center(
            //       child: Text(
            //         'Tiếp tục',
            //         style: TextStyle(
            //             color: Colors.white,
            //             fontSize: 12.sp,
            //             fontWeight: FontWeight.bold),
            //       ),
            //     ),
            //   ),
            // ),
            // SizedBox(height: 15.h),
          ],
        ),
      ),
    );
  }
}

class _ListChooseImage extends StatelessWidget {
  const _ListChooseImage({
    Key? key,
    required this.lsFileTabCheck,
    required this.deleteListImageTabCheck,
  }) : super(key: key);

  final List<File> lsFileTabCheck;
  final ValueChanged<int> deleteListImageTabCheck;

  @override
  Widget build(BuildContext context) {
    return Wrap(
        spacing: 3.w,
        runSpacing: 3.h,
        children: List.generate(lsFileTabCheck.length, (indexImage) {
          return SizedBox(
            width: 50.w,
            child: Stack(children: <Widget>[
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ImageQuatity(lsImage: lsFileTabCheck, index: indexImage),
                    ),
                  );
                },
                child: ListImagePicker(fileImage: lsFileTabCheck[indexImage]),
              ),
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  constraints: const BoxConstraints(),
                  iconSize: 17.sp,
                  color: Colors.red.shade800,
                  icon: const Icon(Icons.remove_circle),
                  onPressed: () {
                    deleteListImageTabCheck(indexImage);
                  },
                ),
              ),
            ]),
          );
        }));
  }
}

class _InfoBTP extends StatelessWidget {
  final QualityControl? qualityControl;
  const _InfoBTP({Key? key, required this.qualityControl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        LabeledDetailRow(title: 'Nhà máy:', text: qualityControl!.storeName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Phân xưởng:', text: qualityControl!.workShopName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Thời gian Confirm:', text: QualityControlFunction.dateFormatConfirm(qualityControl)),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Khách hàng:', text: qualityControl!.profileName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'LSX ĐT:', text: qualityControl!.lsxdt ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'ĐSX:', text: qualityControl!.dsx ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'LSX SAP:', text: qualityControl!.lsxsap ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Sản phẩm:', text: qualityControl!.productName ?? ""),
        SizedBox(height: 15.h),
        LabeledDetailRow(
            title: 'Mã chi tiết:',
            text: '(${(qualityControl!.qty ?? 0.0).round().toString()} ${qualityControl!.unit ?? ""}) ${qualityControl!.productAttribute ?? ""}'),
        SizedBox(height: 15.h),
        LabeledDetailRow(title: 'Công đoạn lớn:', text: qualityControl!.workCenterName ?? ""),
        SizedBox(height: 15.h),
      ],
    );
  }
}

class _InfoNVL extends StatelessWidget {
  final DataRawMeterial? dataRawMaterial;
  const _InfoNVL({Key? key, required this.dataRawMaterial}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        TableInfo(
          textCL1: "Tên NCC:",
          textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.vendorName ?? "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
        ),
        TableInfoNoTop(
            textCL1: "Mã NVL:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.productCode ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        TableInfoNoTop(
            textCL1: "Tên NVL:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.productName ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        TableInfoNoTop3T(
          textCL1: "Số lượng đặt hàng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.poQuantity != null
                  ? dataRawMaterial!.poQuantity!.toStringAsFixed(2)
                  : ' ',
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng đã nhận 123:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.sumQuantityReceived == null
                  ? ""
                  : dataRawMaterial!.sumQuantityReceived!.toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng giao hàng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity != null
                  ? dataRawMaterial!.quantity!.toStringAsFixed(2)
                  : " ",
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Trọng lượng:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity2 == null
                  ? ""
                  : dataRawMaterial!.quantity2!.toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.quantity2Unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        TableInfoNoTop3T(
          textCL1: "Số lượng quy đổi:",
          textCL2: dataRawMaterial == null
              ? ""
              : dataRawMaterial!.quantity3 == null
                  ? ""
                  : (dataRawMaterial!.quantity3 ?? 0.0).toStringAsFixed(2),
          textCL3: dataRawMaterial == null
              ? ""
              : dataRawMaterial != null
                  ? dataRawMaterial!.quantity3Unit ?? ""
                  : "",
          colorCL1: 0xff0052cc,
          colorCL2: 0xFFFFFFFF,
          colorCL3: 0xFFFFFFFF,
        ),
        // TableInfoNoTop(
        //     textCL1: "Quy cách:",
        //     textCL2: "Nhám băng GXK-51-TQ 7''x86''xP100",
        //     colorCL1: 0xff303F9F,
        //     colorCL2: 0xFFFFFFFF
        // ),
        TableInfoNoTop(
            textCL1: "Ngày sản xuất:",
            textCL2: dataRawMaterial == null ? "" : dataRawMaterial!.manufacturingDateStr ?? "",
            colorCL1: 0xff0052cc,
            colorCL2: 0xFFFFFFFF),
        SizedBox(height: 5.h),
        // Text(
        //   "Được tạo bởi " + _dataRawMaterial!.createBy.toString() + " vào lúc " + _dataRawMaterial!.createTime.toString(),
        //   style: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade400),
        // ),
        SizedBox(height: 15.h),
        Text(
          "Số lượng giao hàng theo PO/PO Line:",
          style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 5.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Table(
                border: TableBorder.all(width: 0.5.w),
                columnWidths: <int, TableColumnWidth>{
                  0: FixedColumnWidth(100.w),
                  1: FixedColumnWidth(100.w),
                  2: FixedColumnWidth(70.w),
                  3: FixedColumnWidth(70.w),
                  4: FixedColumnWidth(70.w),
                  5: FixedColumnWidth(70.w),
                  6: FixedColumnWidth(70.w),
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: <TableRow>[
                  TableRow(
                    decoration: const BoxDecoration(
                      color: Color(0xff0052cc),
                    ),
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "PO/POLine",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SO /WBS",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL giao hàng",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "ĐVT",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL theo PO/PO Line",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL đã giao",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                        child: Text(
                          "SL còn lại",
                          style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: List.generate(
                  (dataRawMaterial == null ? [] : dataRawMaterial!.poDetailResponses ?? []).length,
                  (index) => Table(
                    border: TableBorder(
                      left: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      right: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      bottom: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                      verticalInside: BorderSide(
                        color: Colors.black,
                        width: 0.5.w,
                      ),
                    ),
                    columnWidths: <int, TableColumnWidth>{
                      0: FixedColumnWidth(100.w),
                      1: FixedColumnWidth(100.w),
                      2: FixedColumnWidth(70.w),
                      3: FixedColumnWidth(70.w),
                      4: FixedColumnWidth(70.w),
                      5: FixedColumnWidth(70.w),
                      6: FixedColumnWidth(70.w),
                    },
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    children: <TableRow>[
                      TableRow(
                        children: <Widget>[
                          Container(
                              margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                              child: Column(children: <Widget>[
                                Text(
                                  dataRawMaterial!.poDetailResponses![index].po ?? "",
                                  style: TextStyle(fontSize: 12.sp),
                                  textAlign: TextAlign.center,
                                ),
                                Text(
                                  dataRawMaterial!.poDetailResponses![index].poLine ?? "",
                                  style: TextStyle(fontSize: 12.sp),
                                  textAlign: TextAlign.center,
                                ),
                              ])),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: dataRawMaterial!.poDetailResponses == null
                                ? Text(" ", style: TextStyle(fontSize: 12.sp))
                                : (dataRawMaterial!.poDetailResponses![index].so != null && dataRawMaterial!.poDetailResponses![index].so != "") &&
                                        (dataRawMaterial!.poDetailResponses![index].soLine != null &&
                                            dataRawMaterial!.poDetailResponses![index].soLine != "") &&
                                        (dataRawMaterial!.poDetailResponses![index].wbs == null ||
                                            dataRawMaterial!.poDetailResponses![index].wbs == "")
                                    ? Column(children: <Widget>[
                                        Text(
                                          dataRawMaterial!.poDetailResponses![index].so ?? "",
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.center,
                                        ),
                                        Text(
                                          dataRawMaterial!.poDetailResponses![index].soLine ?? "",
                                          style: TextStyle(fontSize: 12.sp),
                                          textAlign: TextAlign.center,
                                        ),
                                      ])
                                    : (dataRawMaterial!.poDetailResponses![index].so == null ||
                                                dataRawMaterial!.poDetailResponses![index].so == "") &&
                                            (dataRawMaterial!.poDetailResponses![index].soLine == null ||
                                                dataRawMaterial!.poDetailResponses![index].soLine == "") &&
                                            (dataRawMaterial!.poDetailResponses![index].wbs != null &&
                                                dataRawMaterial!.poDetailResponses![index].wbs != "")
                                        ? Text(
                                            dataRawMaterial!.poDetailResponses![index].wbs ?? "",
                                            style: TextStyle(fontSize: 12.sp),
                                            textAlign: TextAlign.center,
                                          )
                                        : Text(
                                            "Tồn trơn",
                                            style: TextStyle(fontSize: 12.sp),
                                            textAlign: TextAlign.center,
                                          ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityImported == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityImported!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].unit ?? "",
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityByPO == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityByPO!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].quantityReceived == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].quantityReceived!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                            child: Text(
                              dataRawMaterial!.poDetailResponses![index].remainQuantity == null
                                  ? ""
                                  : dataRawMaterial!.poDetailResponses![index].remainQuantity!.toStringAsFixed(2),
                              style: TextStyle(fontSize: 12.sp),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 15.h),
              // Text(
              //   "Được tạo bởi " + _dataRawMaterial!.createBy.toString() + " vào lúc " + _dataRawMaterial!.createTime.toString(),
              //   style: TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade400),
              // ),
            ],
          ),
        ),
      ],
    );
  }
}

class _TitleQualityControl extends StatelessWidget {
  final String title;
  const _TitleQualityControl({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(fontSize: 14.sp),
    );
  }
}

class _FieldQuantity extends StatelessWidget {
  final String field;
  const _FieldQuantity({Key? key, required this.field}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      field,
      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
    );
  }
}

class _DropdownQuantityResult extends StatelessWidget {
  final ResultList? selectedResult;
  final QualityControl? qualityControl;
  final ValueChanged<ResultList?> onChangeResult;
  final List<ResultList>? lsResultList;
  const _DropdownQuantityResult(
      {Key? key, required this.selectedResult, required this.qualityControl, required this.onChangeResult, required this.lsResultList})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<ResultList>(
        isExpanded: true,
        isDense: true,
        itemHeight: null,
        value: selectedResult ?? QualityControlDetailFunction.defaultResultList,
        iconSize: 15.sp,
        style: const TextStyle(color: Colors.white),
        onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : onChangeResult,
        items: lsResultList!.map((ResultList result) {
          return DropdownMenuItem<ResultList>(
              value: result,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  result.catalogTextVi.toString(),
                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                ),
              ));
        }).toList(),
        selectedItemBuilder: (BuildContext context) {
          return lsResultList!.map<Widget>((ResultList result) {
            return Text(result.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
          }).toList();
        },
      ),
    );
  }
}

class _DropdownType extends StatelessWidget {
  final QualityTypeList? selectedType;
  final QualityControl? qualityControl;
  final ValueChanged<QualityTypeList?> onChangeType;
  final List<QualityTypeList>? qualityTypeList;
  const _DropdownType({Key? key, required this.selectedType, required this.qualityControl, required this.onChangeType, required this.qualityTypeList})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<QualityTypeList>(
        isExpanded: true,
        isDense: true,
        itemHeight: null,
        value: selectedType ?? QualityControlFunction.defaultValueQualityTypeList,
        iconSize: 15.sp,
        style: const TextStyle(color: Colors.white),
        onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : onChangeType,
        items: qualityTypeList!.map((QualityTypeList type) {
          return DropdownMenuItem<QualityTypeList>(
              value: type,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  type.catalogTextVi.toString(),
                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                ),
              ));
        }).toList(),
        selectedItemBuilder: (BuildContext context) {
          return qualityTypeList!.map<Widget>((QualityTypeList type) {
            return Text(type.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
          }).toList();
        },
      ),
    );
  }
}

class _ErrorValidateView extends StatelessWidget {
  final bool error;
  final String text;
  const _ErrorValidateView({Key? key, required this.error, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
        visible: error,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Flexible(
              flex: 1,
              child: Icon(Icons.error_outline, size: 13.sp, color: Colors.red[700]),
            ),
            SizedBox(width: 5.w),
            Flexible(flex: 8, child: Text(text, style: TextStyle(fontSize: 11.sp, color: Colors.red[700])))
          ],
        ));
  }
}
