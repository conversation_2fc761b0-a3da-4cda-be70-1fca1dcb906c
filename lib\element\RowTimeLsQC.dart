import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class RowTimeLsQC extends StatelessWidget {
  final String title;
  final String time;
  const RowTimeLsQC({Key? key, required this.title, required this.time}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 5.h),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            border: Border.all(
              width: 0.5.w,
              color: Colors.grey.shade400,
            ),
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Text(
            time,
            style: TextStyle(fontSize: 13.sp),
          ),
        ),
      ],
    );
  }
}

class ColumnDateLsQC extends StatelessWidget {
  final String title;
  final VoidCallback date;
  final String displayDate;
  const ColumnDateLsQC({Key? key, required this.title, required this.date, required this.displayDate}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 0, horizontal: 5.h),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              width: 0.5.w,
              color: Colors.grey.shade400,
            ),
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                flex: 8,
                child: Text(
                  displayDate,
                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                ),
              ),
              Expanded(
                flex: 2,
                child: InkWell(
                  onTap: date,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 5.h),
                    child: Icon(Icons.edit_calendar_outlined, size: 17.sp, color: Colors.blue),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
