import 'dart:io';

import '../model/qualityControlApi.dart';

class ScreenArgumentDetailQuality {
  final String token;
  final QualityControlDetail? qualityControlDetail;
  final QualityControlModel? qualityControlModel;
  final QualityControl? qualityControl;
  final List<TestMethodList>? lsTestMethodList;
  final List<ResultList>? lsResultList;
  final List<SamplingLevelList>? lsSamplingLevelList;
  final List<Error>? lsError;
  final List<ThongTinKiemTra>? lsQualityControlInformationIdList;
  final List<QualityControlInformation>? lsQualityControlInformation;
  final List<ErrorList>? lsErrorList;
  final QualityCheckerInfo selectedStaff;
  final QualityTypeList? selectedType;
  final String pO;
  final String quantityCheck;
  final ResultList? selectedResult;
  final List<File> lsFileTabCheck;
  final String formatDatePost;
  final String dateTimeOld;
  ScreenArgumentDetailQuality(
      this.token,
      this.qualityControlDetail,
      this.qualityControlModel,
      this.qualityControl,
      this.lsTestMethodList,
      this.lsResultList,
      this.lsSamplingLevelList,
      this.lsError,
      this.lsQualityControlInformationIdList,
      this.lsQualityControlInformation,
      this.lsErrorList,
      this.selectedStaff,
      this.selectedType,
      this.pO,
      this.quantityCheck,
      this.selectedResult,
      this.lsFileTabCheck,
      this.formatDatePost,
      this.dateTimeOld);
}
