import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import 'package:ttf/model/qualityControlApi.dart';
import '../../model/lsSendQualityControlInformation.dart';
import '../../model/sendErrorQualityControl.dart';
import '../../model/sendQualityControlDetail.dart';
import '../../urlApi/urlApi.dart';

class QuantityControlApi {
  static Future<http.Response> getCongDoanInfo(String hangTagId, String stepCode, String token) async {
    // if (kDebugMode) {
    //   print(token);
    // }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/CongDoanInfo?hangTagId=$hangTagId&stepCode=$stepCode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getCongDoanInfoMauDauChuyen(String lsxSAP, String stepCode, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/CongDoanInfoMauDauChuyen?lsxSAP=$lsxSAP&stepCode=$stepCode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getCongDoanInfoSanPham(String? qualityControlId, String stepCode, String token) async {
    // if (kDebugMode) {
    //   print(token);
    // }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/CongDoanInfoSanPham?id=$qualityControlId&stepCode=$stepCode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getCongDoanInfoBTP(String? lsxSAP, String stepCode, String token) async {
    // if (kDebugMode) {
    //   print(token);
    // }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/CongDoanInfoBTP?PO=$lsxSAP&stepCode=$stepCode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getChecklistMau(String loaiMau, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/GetChecklistMau?itemType=$loaiMau';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getNghiemThuInfo(String hangTagId, String loaiNghiemThu, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}/api/v1/MES/QualityControl/NghiemThuInfo?hangTagId=$hangTagId&qualityType=$loaiNghiemThu';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityControl(String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    if (kDebugMode) {
      print(fromPage);
    }
    if (fromPage != "qr") {
      // "normal"
      // 1. Kiểm tra chất lượng BTP
      final data = {"QualityControlId": qualityControlId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl?QualityControlId=${data['QualityControlId']}';

      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    } else {
      final data = {"RawMaterialCardId": rawMaterialCardId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl?RawMaterialCardId=${data['RawMaterialCardId']}';
      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    }
  }

  static Future<http.Response> getQuantityControl2(String qualityControlId, String token, String rawMaterialCardId, String fromPage) async {
    if (kDebugMode) {
      print(fromPage);
    }
    if (fromPage != "qr") {
      // "normal"
      // 1. Kiểm tra chất lượng BTP
      final data = {"HangTagId": rawMaterialCardId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl2?HangTagId=${data['HangTagId']}';

      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    } else {
      final data = {"RawMaterialCardId": rawMaterialCardId.isNotEmpty ? rawMaterialCardId : qualityControlId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl?RawMaterialCardId=${data['RawMaterialCardId']}';
      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    }
  }

  static Future<http.Response> getQuantityControlKCS(String qualityControlId, String token, String hangTagId, String fromPage) async {
    if (kDebugMode) {
      print(fromPage);
    }

    if (fromPage == "qr") {
      // "normal"
      // 1. Kiểm tra chất lượng BTP
      final data = {"HangTagId": hangTagId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl2?HangTagId=${data['HangTagId']}';

      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    }

    final data = {"QualityControlId": qualityControlId};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl2?QualityControlId=${data['QualityControlId']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityControlNghiemThu(String qualityControlId, String token, String hangTagId, String fromPage) async {
    if (kDebugMode) {
      print(fromPage);
    }

    if (fromPage == "qr") {
      // "normal"
      // 1. Kiểm tra chất lượng BTP
      final data = {"HangTagId": hangTagId};
      if (kDebugMode) {
        print(data);
      }

      final environment = await SecureStorage.getString("environment", null);
      final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

      final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl2?HangTagId=${data['HangTagId']}';

      final url = Uri.parse(urlString);

      debugPrint(url.toString());
      final response = await http.get(url, headers: UrlApi.headersToken(token));
      return response;
    }

    final data = {"QualityControlId": qualityControlId};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControl2?QualityControlId=${data['QualityControlId']}';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityControlQCMau(String qualityControlId, String token, String? qualityType) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControlQCMau?QualityControlId=$qualityControlId&qualityType=$qualityType';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityControlQCHienTruong(String qualityControlId, String token, String? qualityType) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControlQCHienTruong?QualityControlId=$qualityControlId&qualityType=$qualityType';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantityControlQCSanPham(String qualityControlId, String token, String? barcode) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}QualityControlQCSanPham?QualityControlId=$qualityControlId&barcode=$barcode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getPOHeaderMauDauChuyen(String qualityControlId, String token, String? barcode) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetPOHeaderMauDauChuyen?token=$token&barcode=$barcode';
    final url = Uri.parse(urlString);

    debugPrint('QualityControlMauDauChuyen API URL: ${url.toString()}');
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    debugPrint('QualityControlMauDauChuyen API Response Status: ${response.statusCode}');
    debugPrint('QualityControlMauDauChuyen API Response Body: ${response.body}');
    return response;
  }

  static Future<http.Response> getQCMauDauChuyenByLSX(String lsxSAP, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetQCMauDauChuyenByLSX?lsxSAP=$lsxSAP';
    final url = Uri.parse(urlString);

    debugPrint('GetQCMauDauChuyenByLSX API URL: ${url.toString()}');
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    debugPrint('GetQCMauDauChuyenByLSX API Response Status: ${response.statusCode}');
    debugPrint('GetQCMauDauChuyenByLSX API Response Body: ${response.body}');
    return response;
  }

  static Future<http.Response> fetchQCPassedStamp(String token, String? barcode) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetQCPassedStampInfo?barcode=$barcode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQualityControlHeader(String lsxSAP, String barcode, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetQCSanPhamInfoByLSX?lsxSAP=$lsxSAP&barcode=$barcode';
    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static void test(List<LsSendQualityControlInformation> lsSendQualityControlInformation, List<SendErrorControl>? error,
      SendQualityControlDetail sendQualityControlDetail) {
    for (int i = 0; i < lsSendQualityControlInformation.length; i++) {
      debugPrint("infor ${lsSendQualityControlInformation[i].notes}");
      debugPrint("infor ${lsSendQualityControlInformation[i].qualityControlInformationId}");
      for (int j = 0; j < lsSendQualityControlInformation[i].lsImageFile.length; j++) {
        debugPrint("infor ${lsSendQualityControlInformation[i].lsImageFile[j].path}");
      }
    }
    for (int i = 0; i < error!.length; i++) {
      debugPrint("error notes: ${error[i].notes}");
      debugPrint("error quanlityControlErrorId: ${error[i].quanlityControlErrorId}");
      debugPrint("error catalogCode: ${error[i].catalogCode}");
      debugPrint("error quantityError: ${error[i].quantityError}");
      debugPrint("error levelError: ${error[i].levelError}");
      for (int j = 0; j < error[i].lsFile!.length; j++) {
        debugPrint("infor ${error[i].lsFile![j].path}");
      }
      // debugPrint("infor ${error[i].lsFile![0].path}");
    }
    debugPrint("samping ${sendQualityControlDetail.samplingLevelName}");
  }

  static Future<http.StreamedResponse> postQuantityControl(
      QualityControl? qualityControl,
      List<File> lsImageTabCheck,
      String qualityControlId,
      String qualityChecker,
      String qualityDate,
      String qualityType,
      int inspectionLotQuantity,
      String result,
      String po,
      SendQualityControlDetail sendQualityControlDetail,
      List<LsSendQualityControlInformation> lsSendQualityControlInformation,
      List<SendErrorControl>? error,
      String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControl");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);
    for (int i = 0; i < lsSendQualityControlInformation.length; i++) {
      if (lsSendQualityControlInformation[i].qualityControlQCInformationId != "null") {
        request.files.add(http.MultipartFile.fromString(
            'qualityControlInformation[$i].qualityControl_QCInformation_Id', lsSendQualityControlInformation[i].qualityControlQCInformationId));
      }
      if (lsSendQualityControlInformation[i].qualityControlInformationId != "null") {
        request.files.add(http.MultipartFile.fromString(
            'QualityControlInformation[$i].qualityControlInformationId', lsSendQualityControlInformation[i].qualityControlInformationId));
        if (lsSendQualityControlInformation[i].notes != "null") {
          request.files.add(http.MultipartFile.fromString('qualityControlInformation[$i].notes', lsSendQualityControlInformation[i].notes));
        }
        if (lsSendQualityControlInformation[i].lsImageFile.isNotEmpty) {
          for (int j = 0; j < lsSendQualityControlInformation[i].lsImageFile.length; j++) {
            // print(basename(lsSendQualityControlInformation[i].lsImageFile[j].path).replaceAll('image_picker', ''));
            final stream = http.ByteStream(lsSendQualityControlInformation[i].lsImageFile[j].openRead());
            stream.cast();
            final length = await lsSendQualityControlInformation[i].lsImageFile[j].length();
            final multipartFile = http.MultipartFile('QualityControlInformation[$i].file', stream, length,
                filename: basename(lsSendQualityControlInformation[i].lsImageFile[j].path).replaceAll('image_picker', ''));
            request.files.add(multipartFile);
          }
        }
      }
    }

    for (int i = 0; i < error!.length; i++) {
      if (kDebugMode) {
        print(error[i].quantityError);
      }
      if (error[i].quanlityControlErrorId != "null") {
        request.files.add(http.MultipartFile.fromString('error[$i].quanlityControl_Error_Id', error[i].quanlityControlErrorId.toString()));
      }
      if (error[i].catalogCode != "null") {
        request.files.add(http.MultipartFile.fromString('error[$i].catalogCode', error[i].catalogCode.toString()));
        if (error[i].levelError != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].levelError', error[i].levelError.toString()));
        }
        if (error[i].quantityError != -1) {
          request.files.add(http.MultipartFile.fromString('error[$i].quantityError', error[i].quantityError.toString()));
        }
        if (error[i].notes != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].notes', error[i].notes!));
        }

        if (error[i].lsFile!.isNotEmpty) {
          for (int j = 0; j < error[i].lsFile!.length; j++) {
            final stream = http.ByteStream(error[i].lsFile![j].openRead());
            stream.cast();
            final length = await error[i].lsFile![j].length();
            final multipartFile =
                http.MultipartFile('error[$i].file', stream, length, filename: basename(error[i].lsFile![j].path).replaceAll('image_picker', ''));
            request.files.add(multipartFile);
          }
        }
      }
    }
    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }
    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = inspectionLotQuantity.toString();
    if (result != "null") {
      request.fields['result'] = result;
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }
    if (kDebugMode) {
      print(po);
      print(sendQualityControlDetail.result.toString());
      print(result.toString());
    }
    if (qualityControl.qualityControlDetail != null) {
      request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
    }
    if (sendQualityControlDetail.testMethod != "null") {
      request.fields['QualityControlDetail.TestMethod'] = sendQualityControlDetail.testMethod.toString();
    }
    if (sendQualityControlDetail.samplingLevel != "null") {
      request.fields['QualityControlDetail.SamplingLevel'] = sendQualityControlDetail.samplingLevel.toString();
    }
    if (sendQualityControlDetail.acceptableLevel != "null") {
      request.fields['QualityControlDetail.AcceptableLevel'] = sendQualityControlDetail.acceptableLevel.toString();
    }
    request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
    if (sendQualityControlDetail.result != "null") {
      request.fields['QualityControlDetail.Result'] = sendQualityControlDetail.result.toString();
    }
    debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    if (sendQualityControlDetail.samplingLevelName != "null") {
      request.fields['QualityControlDetail.SamplingLevelName'] = sendQualityControlDetail.samplingLevelName.toString();
    }
    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<http.StreamedResponse> postQuantityControlDauVao(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String sku,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControl2");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    if (qualityControl!.qcType != "NVL") {
      if (sku != "null") {
        request.fields['sku'] = sku;
      }
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<http.StreamedResponse> postQuantityControlDauVao3(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String sku,
    String soLuongNhapKho,
    String soLuongBlock,
    String soLuongTraVe,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControl3");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    if (qualityControl!.qcType != "NVL") {
      if (sku != "null") {
        request.fields['sku'] = sku;
      }
    }

    if (soLuongNhapKho != "null") {
      request.fields['soLuongNhapKho'] = soLuongNhapKho;
    }

    if (soLuongBlock != "null") {
      request.fields['soLuongBlock'] = soLuongBlock;
    }

    if (soLuongTraVe != "null") {
      request.fields['soLuongTraVe'] = soLuongTraVe;
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlCongDoan(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    // String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlCongDoan");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    // if (qualityType != "null") {
    //   request.fields['qualityType'] = qualityType;
    // }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }

      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();
      request.fields['QualityControlDetail.CheckingTimes'] = sendQualityControlDetail.checkingTimes.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlQCNghiemThu(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String sku,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlQCNghiemThu");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    if (qualityControl!.qcType != "NVL") {
      if (sku != "null") {
        request.fields['sku'] = sku;
      }
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);
      addFieldIfNotNull('QualityControlDetail.LoiNangChapNhan', sendQualityControlDetail.loiNangChapNhan);
      addFieldIfNotNull('QualityControlDetail.loiNheChapNhan', sendQualityControlDetail.loiNheChapNhan);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();
      request.fields['QualityControlDetail.CheckingTimes'] = sendQualityControlDetail.checkingTimes.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    // debugPrint(json.encode(response));
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlQCMau(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String sku,
    String loaiMau,
    String nhaMay,
    String phanXuong,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlQCMau");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }
    if (qualityControl!.qcType != "NVL") {
      if (sku != "null") {
        request.fields['sku'] = sku;
      }

      if (qualityControl.nhaGiaCong != null) {
        request.fields['nhaGiaCong'] = qualityControl.nhaGiaCong!;
      }

      if (qualityControl.confirmDate != null) {
        var confirmDate = DateFormat("dd-MM-yyyy hh:mm a").parse(qualityControl.confirmDate!);
        var formatter = DateFormat("yyyy-MM-dd hh:mm a");
        var confirmDateString = formatter.format(confirmDate);

        request.fields['confirmDate'] = confirmDateString;
      }

      request.fields['khachHangId'] = qualityControl.khachHangId ?? "";
      request.fields['nhaCungCap'] = qualityControl.nhaCungCap ?? "";
      request.fields['lsxsap'] = qualityControl.lsxsap ?? "";
      request.fields['productName'] = qualityControl.productName ?? "";
      request.fields['qty'] = (qualityControl.qty ?? 0).toString();
      request.fields['tinhTrangMoiTruong'] = qualityControl.tinhTrangMoiTruong ?? "";
      request.fields['productType'] = qualityControl.productType ?? "";
      request.fields['mauHoanThien'] = qualityControl.mauHoanThien ?? "";
      request.fields['loaiMau'] = loaiMau;
      request.fields['saleOrgCode'] = nhaMay;
      request.fields['workShopCode'] = phanXuong;
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);
      addFieldIfNotNull('QualityControlDetail.LoiNangChapNhan', sendQualityControlDetail.loiNangChapNhan);
      addFieldIfNotNull('QualityControlDetail.loiNheChapNhan', sendQualityControlDetail.loiNheChapNhan);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    // debugPrint(json.encode(response));
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlQCHienTruong(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String? phanXuongCode,
    String? congDoanCode,
    String saleOrgCode,
    String? note,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlQCHienTruong");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }

    if (qualityControl!.qcType != "NVL") {
      request.fields['qty'] = "0";
    }

    if (qualityControl!.qcType != "NVL") {
      request.fields['qty'] = "0";
    }

    if (phanXuongCode != null) {
      request.fields['WorkShopCode'] = phanXuongCode;
    }

    if (congDoanCode != null) {
      request.fields['StepCode'] = congDoanCode;
    }

    request.fields['saleOrgCode'] = saleOrgCode;
    //note
    if (note != null) {
      request.fields['note'] = note;
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);
      addFieldIfNotNull('QualityControlDetail.LoiNangChapNhan', sendQualityControlDetail.loiNangChapNhan);
      addFieldIfNotNull('QualityControlDetail.loiNheChapNhan', sendQualityControlDetail.loiNheChapNhan);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    // debugPrint(json.encode(response));
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlSanPham(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    // String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String barcode,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlSanPham");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }

    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    // if (qualityType != "null") {
    request.fields['qualityType'] = "QCSANPHAM";
    // }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    request.fields['lsxsap'] = qualityControl.lsxsap ?? "";
    request.fields['qty'] = (qualityControl.qty ?? 0).toString();

    request.fields['barcode'] = barcode;

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<http.StreamedResponse> postQualityControlMauDauChuyen(
    QualityControl? qualityControl,
    List<File> lsImageTabCheck,
    String qualityControlId,
    String qualityChecker,
    String qualityDate,
    String qualityType,
    int soLuongKiemTra,
    String? result,
    String po,
    SendQualityControlDetail2? sendQualityControlDetail,
    List<LsSendQualityControlInformation2> lsSendQualityControlInformation,
    List<SendErrorControl>? error,
    String token,
    String barcode,
    String loaiMau,
    String nhaMay,
    String phanXuong,
  ) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final uri = Uri.parse(baseUrl + UrlApi.baseUrlQualityControl + "QualityControlMauDauChuyen");
    debugPrint(uri.toString());
    final request = http.MultipartRequest("POST", uri);

    await _addSendQualityControlInformation(lsSendQualityControlInformation, request);
    await _addErrorInformation(error, request);

    if (lsImageTabCheck.isNotEmpty) {
      for (int i = 0; i < lsImageTabCheck.length; i++) {
        final stream = http.ByteStream(lsImageTabCheck[i].openRead());
        stream.cast();
        final length = await lsImageTabCheck[i].length();
        final multipartFile = http.MultipartFile('File', stream, length, filename: basename(lsImageTabCheck[i].path).replaceAll('image_picker', ''));
        request.files.add(multipartFile);
      }
    }

    if (kDebugMode) {
      print(qualityDate);
    }
    if (qualityControlId != "null") {
      request.fields['qualityControlId'] = qualityControlId;
    }
    if (qualityChecker != "null") {
      request.fields['qualityChecker'] = qualityChecker;
    }
    if (qualityDate != "null") {
      request.fields['qualityDate'] = qualityDate;
    }
    if (qualityType != "null") {
      request.fields['qualityType'] = qualityType;
    }
    request.fields['inspectionLotQuantity'] = soLuongKiemTra.toString();
    if (result != "null") {
      request.fields['result'] = result.toString();
    }
    if (qualityControl!.qcType != "NVL") {
      if (po != "null") {
        request.fields['po'] = po;
      }
    }

    request.fields['barcode'] = barcode;
    if (qualityControl!.qcType != "NVL") {
      if (qualityControl.nhaGiaCong != null) {
        request.fields['nhaGiaCong'] = qualityControl.nhaGiaCong!;
      }

      if (qualityControl.confirmDate != null) {
        var confirmDate = DateFormat("dd-MM-yyyy hh:mm a").parse(qualityControl.confirmDate!);
        var formatter = DateFormat("yyyy-MM-dd hh:mm a");
        var confirmDateString = formatter.format(confirmDate);

        request.fields['confirmDate'] = confirmDateString;
      }

      request.fields['khachHangId'] = qualityControl.khachHangId ?? "";
      request.fields['nhaCungCap'] = qualityControl.nhaCungCap ?? "";
      request.fields['lsxsap'] = qualityControl.lsxsap ?? "";
      request.fields['productName'] = qualityControl.productName ?? "";
      request.fields['qty'] = (qualityControl.qty ?? 0).toString();
      request.fields['tinhTrangMoiTruong'] = qualityControl.tinhTrangMoiTruong ?? "";
      request.fields['productType'] = qualityControl.productType ?? "";
      request.fields['mauHoanThien'] = qualityControl.mauHoanThien ?? "";
      request.fields['loaiMau'] = loaiMau;
      request.fields['saleOrgCode'] = nhaMay;
      request.fields['workShopCode'] = phanXuong;
    }

    if (sendQualityControlDetail != null) {
      if (kDebugMode) {
        print(po);
        print(sendQualityControlDetail.result.toString());
        print(result.toString());

        print("sendQualityControlDetail");
        print(jsonEncode(sendQualityControlDetail));
      }
      void addFieldIfNotNull(String fieldName, String? fieldValue) {
        if (fieldValue != "null") {
          request.fields[fieldName] = fieldValue.toString();
        }
      }

      if (qualityControl.qualityControlDetail != null) {
        request.fields['QualityControlDetail.QualityControlDetailId'] = sendQualityControlDetail.qualityControlDetailId.toString();
      }

      addFieldIfNotNull('QualityControlDetail.TestMethod', sendQualityControlDetail.testMethod);
      addFieldIfNotNull('QualityControlDetail.LimitCritical', sendQualityControlDetail.limitCritical as String?);
      addFieldIfNotNull('QualityControlDetail.LimitHigh', sendQualityControlDetail.limitHigh as String?);
      addFieldIfNotNull('QualityControlDetail.LimitLow', sendQualityControlDetail.limitLow as String?);
      addFieldIfNotNull('QualityControlDetail.SamplingLevel', sendQualityControlDetail.samplingLevel);
      addFieldIfNotNull('QualityControlDetail.AcceptableLevel', sendQualityControlDetail.acceptableLevel);
      addFieldIfNotNull('QualityControlDetail.Result', sendQualityControlDetail.result);
      addFieldIfNotNull('QualityControlDetail.SamplingLevelName', sendQualityControlDetail.samplingLevelName);
      addFieldIfNotNull('QualityControlDetail.LoiNangChapNhan', sendQualityControlDetail.loiNangChapNhan);
      addFieldIfNotNull('QualityControlDetail.loiNheChapNhan', sendQualityControlDetail.loiNheChapNhan);
      addFieldIfNotNull('QualityControlDetail.CheckingTimes', sendQualityControlDetail.checkingTimes?.toString());

      request.fields['QualityControlDetail.InspectionQuantity'] = sendQualityControlDetail.inspectionQuantity.toString();
      request.fields['QualityControlDetail.TongSoSanPhamLoi'] = sendQualityControlDetail.tongSoSanPhamLoi.toString();
      request.fields['QualityControlDetail.CongDoanNho'] = sendQualityControlDetail.congDoanNho.toString();
      request.fields['QualityControlDetail.MauHoanThien'] = sendQualityControlDetail.mauHoanThien.toString();

      debugPrint("samping${sendQualityControlDetail.samplingLevelName}");
    }

    request.headers.addAll(UrlApi.headersToken(token));
    final response = await request.send();
    return response;
  }

  static Future<void> _addErrorInformation(List<SendErrorControl>? error, http.MultipartRequest request) async {
    for (int i = 0; i < error!.length; i++) {
      if (kDebugMode) {
        print(error[i].quantityError);
      }
      if (error[i].quanlityControlErrorId != "null") {
        request.files.add(http.MultipartFile.fromString('error[$i].quanlityControl_Error_Id', error[i].quanlityControlErrorId.toString()));
      }
      if (error[i].catalogCode != "null") {
        request.files.add(http.MultipartFile.fromString('error[$i].catalogCode', error[i].catalogCode.toString()));
        if (error[i].levelError != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].levelError', error[i].levelError.toString()));
        }
        if (error[i].quantityError != -1) {
          request.files.add(http.MultipartFile.fromString('error[$i].quantityError', error[i].quantityError.toString()));
        }
        if (error[i].notes != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].notes', error[i].notes!));
        }
        if (error[i].caNhanGayLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].caNhanGayLoi', error[i].caNhanGayLoi!));
        }
        if (error[i].caNhanGayLoiMany != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].caNhanGayLoiMany', error[i].caNhanGayLoiMany!));
        }

        if (error[i].lsFile!.isNotEmpty) {
          for (int j = 0; j < error[i].lsFile!.length; j++) {
            final stream = http.ByteStream(error[i].lsFile![j].openRead());
            stream.cast();
            final length = await error[i].lsFile![j].length();
            final multipartFile =
                http.MultipartFile('error[$i].file', stream, length, filename: basename(error[i].lsFile![j].path).replaceAll('image_picker', ''));
            request.files.add(multipartFile);
          }
        }

        // New vị trí lỗi
        if (error[i].congDoanLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].congDoanLoi', error[i].congDoanLoi!));
        }

        if (error[i].congDoanLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].phuongAnXuLy', error[i].phuongAnXuLy!));
        }

        if (error[i].nhaMayLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].nhaMayLoi', error[i].nhaMayLoi!));
        }

        if (error[i].phanXuongLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].phanXuongLoi', error[i].phanXuongLoi!));
        }

        if (error[i].toChuyenLoi != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].toChuyenLoi', error[i].toChuyenLoi!));
        }

        // quanDoc
        // toTruong
        // qaqc
        // kcs

        if (error[i].quanDoc != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].quanDoc', error[i].quanDoc!));
        }

        if (error[i].toTruong != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].toTruong', error[i].toTruong!));
        }

        if (error[i].qaqc != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].qaqc', error[i].qaqc!));
        }

        if (error[i].kcs != "null") {
          request.files.add(http.MultipartFile.fromString('error[$i].kcs', error[i].kcs!));
        }
      }
    }
  }

  static Future<void> _addSendQualityControlInformation(List<LsSendQualityControlInformation2> infoList, http.MultipartRequest request) async {
    for (int i = 0; i < infoList.length; i++) {
      if (infoList[i].qualityControlQCInformationId != "null") {
        request.files.add(http.MultipartFile.fromString(
            'qualityControlInformation[$i].qualityControl_QCInformation_Id', infoList[i].qualityControlQCInformationId));
      }
      if (infoList[i].qualityControlInformationId != "null") {
        request.files.add(http.MultipartFile.fromString(
          'QualityControlInformation[$i].qualityControlInformationId',
          infoList[i].qualityControlInformationId,
        ));

        if (infoList[i].soSanPhamLoi != "null") {
          request.files.add(http.MultipartFile.fromString(
            'qualityControlInformation[$i].soSanPhamLoi',
            infoList[i].soSanPhamLoi,
          ));
        }

        if (infoList[i].notes != "null") {
          request.files.add(http.MultipartFile.fromString(
            'qualityControlInformation[$i].notes',
            infoList[i].notes,
          ));
        }

        if (infoList[i].outcomeStatus != "null") {
          request.files.add(http.MultipartFile.fromString(
            'qualityControlInformation[$i].outcomeStatus',
            infoList[i].outcomeStatus,
          ));
        }

        if (infoList[i].lsImageFile.isNotEmpty) {
          for (int j = 0; j < infoList[i].lsImageFile.length; j++) {
            // print(basename(lsSendQualityControlInformation[i].lsImageFile[j].path).replaceAll('image_picker', ''));
            final stream = http.ByteStream(infoList[i].lsImageFile[j].openRead());
            stream.cast();
            final length = await infoList[i].lsImageFile[j].length();
            final multipartFile = http.MultipartFile('QualityControlInformation[$i].file', stream, length,
                filename: basename(infoList[i].lsImageFile[j].path).replaceAll('image_picker', ''));
            request.files.add(multipartFile);
          }
        }
      }
    }
  }

  static Future<http.Response> getListLSXSAP(String lsxSAP, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // final urlString = '${baseUrl}/${UrlApi.baseUrlPurchaseOrder}GetSoToKhai?soToKhai=$soToKhai';
    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetListLSQSAP?q=$lsxSAP';

    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }

  static Future<http.Response> fetchPhanXuong(String plant, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // final urlString = '${baseUrl}/${UrlApi.baseUrlPurchaseOrder}GetSoToKhai?soToKhai=$soToKhai';
    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetListPhanXuong?q=$plant';

    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }

  static Future<http.Response> fetchPhanXuongCongDoan(String plant, String token) async {
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    // final urlString = '${baseUrl}/${UrlApi.baseUrlPurchaseOrder}GetSoToKhai?soToKhai=$soToKhai';
    final urlString = '${baseUrl}${UrlApi.baseUrlQualityControl}GetListPhanXuongCongDoan?q=$plant';

    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }

    final response = await http.get(url, headers: UrlApi.headersToken(token));

    if (kDebugMode) {
      print(response.statusCode);
    }

    return response;
  }
}
