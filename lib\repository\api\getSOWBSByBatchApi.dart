import 'package:flutter/foundation.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';
import 'package:http/http.dart' as http;

class GetSOWBSByBatchApi {
  static Future<http.Response> getSOWBSByBatchApi(String batchNumber, String token) async {
    Map<String, dynamic> data = {"BatchNumber": batchNumber};
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetSOWBSByBatch", data);
    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
