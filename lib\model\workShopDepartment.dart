import 'package:ttf/model/departmentRoutingMapping.dart';

class WorkShopDepartment {
  int? code;
  bool? isSuccess;
  String? message;
  DataWorkShop? dataWorkShop;
  String? additionalData;

  WorkShopDepartment({this.code, this.isSuccess, this.message, this.dataWorkShop, this.additionalData});

  WorkShopDepartment.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    dataWorkShop = json['data'] != null ? DataWorkShop.fromJson(json['data']) : null;
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (dataWorkShop != null) {
      data['data'] = dataWorkShop!.toJson();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class DataWorkShop {
  List<ListWorkShop>? listWorkShop;
  List<ListDepartment>? listDepartment;
  List<DepartmentRoutingMapping>? listDepartmentRoutingMapping;

  DataWorkShop({this.listWorkShop, this.listDepartment, this.listDepartmentRoutingMapping});

  DataWorkShop.fromJson(Map<String, dynamic> json) {
    if (json['listWorkShop'] != null) {
      listWorkShop = <ListWorkShop>[];
      json['listWorkShop'].forEach((v) {
        listWorkShop!.add(ListWorkShop.fromJson(v));
      });
    }
    if (json['listDepartment'] != null) {
      listDepartment = <ListDepartment>[];
      json['listDepartment'].forEach((v) {
        listDepartment!.add(ListDepartment.fromJson(v));
      });
    }
    if (json['listDepartmentRoutingMapping'] != null) {
      listDepartmentRoutingMapping = <DepartmentRoutingMapping>[];
      json['listDepartmentRoutingMapping'].forEach((v) {
        listDepartmentRoutingMapping!.add(DepartmentRoutingMapping.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listWorkShop != null) {
      data['listWorkShop'] = listWorkShop!.map((v) => v.toJson()).toList();
    }
    if (listDepartment != null) {
      data['listDepartment'] = listDepartment!.map((v) => v.toJson()).toList();
    }
    if (listDepartmentRoutingMapping != null) {
      data['listDepartmentRoutingMapping'] = listDepartmentRoutingMapping!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListWorkShop {
  String? workShopId;
  String? display;

  ListWorkShop({this.workShopId, this.display});

  ListWorkShop.fromJson(Map<String, dynamic> json) {
    workShopId = json['workShopId'];
    display = json['display'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workShopId'] = workShopId;
    data['display'] = display;
    return data;
  }
}

class ListDepartment {
  String? departmentId;
  String? display;
  String? workShopId;

  ListDepartment({this.departmentId, this.display, this.workShopId});

  ListDepartment.fromJson(Map<String, dynamic> json) {
    departmentId = json['departmentId'];
    display = json['display'];
    workShopId = json['workShopId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['departmentId'] = departmentId;
    data['display'] = display;
    data['workShopId'] = workShopId;
    return data;
  }
}
