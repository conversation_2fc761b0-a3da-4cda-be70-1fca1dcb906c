# Deployment

This document outlines the process for building, testing, and deploying the TTF MES Mobile application to various environments.

## Release Process

The application follows a structured release process to ensure quality and stability:

1. **Development Phase**
   - Features developed on feature branches
   - Code review through pull requests
   - Initial testing in development environment

2. **Quality Assurance**
   - Build deployed to QA environment
   - Functional testing by QA team
   - Bug fixes and regression testing

3. **User Acceptance Testing (UAT)**
   - Build deployed to UAT environment
   - Testing by end users and stakeholders
   - Feedback collection and final adjustments

4. **Production Release**
   - Final build for production environment
   - Staged rollout to control impact
   - Monitoring for issues post-deployment

## Version Management

### Versioning Scheme

The application follows semantic versioning in the format `major.minor.patch+build`:

- **Major version**: Incremented for incompatible API changes
- **Minor version**: Incremented for backward-compatible new features
- **Patch version**: Incremented for backward-compatible bug fixes
- **Build number**: Incremented for each build, regardless of version change

For example: `3.3.14+3314`

### Version Control

The version number is managed in the following locations:

- `pubspec.yaml`: Primary version definition
- Application footer: Displayed to users
- Build configuration: Used by app stores

### Version Updates

To update the version number:

1. Update the version in `pubspec.yaml`:
   ```yaml
   version: 3.3.14+3314
   ```

2. Update any version-dependent code
3. Create a version tag in git:
   ```bash
   git tag -a v3.3.14 -m "Release version 3.3.14"
   git push origin v3.3.14
   ```

## Building for Release

### Android Release Build

#### Prerequisites

1. Updated `pubspec.yaml` with correct version
2. Keystore file in `jks/` directory
3. Keystore credentials (password, key alias, key password)

#### Build Process

1. Update the `android/app/build.gradle` file if needed

2. Build the release APK:
   ```bash
   flutter build apk --release
   ```

3. Or build the Android App Bundle (recommended for Play Store):
   ```bash
   flutter build appbundle --release
   ```

4. The output files will be located at:
   - APK: `build/app/outputs/flutter-apk/app-release.apk`
   - AAB: `build/app/outputs/bundle/release/app-release.aab`

### iOS Release Build

#### Prerequisites

1. Updated `pubspec.yaml` with correct version
2. Apple Developer account access
3. Provisioning profiles and certificates
4. Xcode installed (on macOS)

#### Build Process

1. Update the version and build number in Xcode if needed

2. Build the release IPA:
   ```bash
   flutter build ios --release
   ```

3. Open the Xcode workspace:
   ```bash
   open ios/Runner.xcworkspace
   ```

4. In Xcode:
   - Select a development team
   - Select the appropriate provisioning profile
   - Set the build configuration to Release
   - Archive the build (Product > Archive)
   - Use the Organizer to distribute the app

## App Distribution

### Google Play Store

1. Access the Google Play Console
2. Create a new release for the appropriate track (internal, alpha, beta, production)
3. Upload the AAB file
4. Provide release notes
5. Configure rollout percentage (for staged rollout)
6. Submit for review

### Apple App Store

1. Access App Store Connect
2. Create a new iOS version
3. Upload the build using Xcode or Application Loader
4. Provide release notes and metadata
5. Configure phased release if desired
6. Submit for review

### Enterprise Distribution

For internal enterprise distribution:

#### Android

1. Build a signed APK
2. Host the APK on an internal server
3. Provide a direct download link or use an enterprise app store
4. Ensure devices have "Unknown Sources" enabled for installation

#### iOS

1. Use Apple Business Manager for enterprise distribution
2. Or use an Ad Hoc provisioning profile for limited testing
3. Use a Mobile Device Management (MDM) solution for distribution

## Continuous Integration/Continuous Deployment (CI/CD)

### CI/CD Pipeline Configuration

The application can be integrated with CI/CD services (e.g., Jenkins, GitHub Actions, Bitbucket Pipelines, GitLab CI) for automated building and testing:

1. **Build Triggers**:
   - Pull request creation
   - Push to specific branches
   - Tag creation
   - Manual triggering

2. **Pipeline Stages**:
   - Checkout code
   - Install dependencies
   - Run tests
   - Static code analysis
   - Build app
   - Deploy to environment (based on branch/tag)

### Example GitHub Actions Workflow

```yaml
name: Flutter Build & Deploy

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '2.19.2'
          
      - name: Install dependencies
        run: flutter pub get
        
      - name: Run tests
        run: flutter test
        
      - name: Build APK
        run: flutter build apk --release
        
      - name: Upload APK
        uses: actions/upload-artifact@v2
        with:
          name: app-release
          path: build/app/outputs/flutter-apk/app-release.apk
          
  deploy:
    needs: build
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Download APK
        uses: actions/download-artifact@v2
        with:
          name: app-release
          
      # Deployment steps would be added here
      # This could include uploading to app stores or distribution services
```

## Environment Configuration

### Multiple Environment Support

The application supports different environments through configuration:

1. **development**: For development and testing
2. **qa**: For quality assurance testing
3. **production**: For production use

### Environment Variables

Environment-specific variables are managed in `lib/utils/appConfig.dart`:

```dart
class AppConfig {
  static bool get isProduction => _getCurrentEnvironment() == 'production';
  static bool get isQA => _getCurrentEnvironment() == 'qa';
  static bool get isDevelopment => _getCurrentEnvironment() == 'development';
  
  static String get appVersion => _getAppVersion();
  
  // Environment-specific configuration
  static String get apiUrl {
    switch (_getCurrentEnvironment()) {
      case 'production':
        return 'https://api.production.example.com';
      case 'qa':
        return 'https://api.qa.example.com';
      case 'development':
      default:
        return 'https://api.dev.example.com';
    }
  }
  
  // Other environment-specific configurations...
}
```

## In-App Updates

The application uses the `upgrader` package to manage in-app updates:

### Update Configuration

In `lib/route/route.dart`:

```dart
Widget withUpgradeAlert(Widget child) {
  return UpgradeAlert(
    upgrader: Upgrader(
      canDismissDialog: true,
      durationUntilAlertAgain: const Duration(days: 1),
      dialogStyle: Platform.isIOS 
          ? UpgradeDialogStyle.cupertino 
          : UpgradeDialogStyle.material,
      countryCode: 'VN',
      languageCode: 'vi',
      shouldPopScope: () => true,
      messages: UpgraderMessages(code: 'vi'),
    ),
    child: child,
  );
}
```

### Upgrade Flow

1. Application checks for updates from the app store
2. If a newer version is available, a dialog prompts the user to update
3. User can choose to update now or later (if dismissible)
4. Update process directs the user to the appropriate app store

## Application Signing

### Android Signing

1. Keystore file is stored in the `jks/` directory
2. Signing configuration in `android/app/build.gradle`:
   ```gradle
   signingConfigs {
       release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile file(keystoreProperties['storeFile'])
           storePassword keystoreProperties['storePassword']
       }
   }
   ```

### iOS Signing

1. Signing is managed through Xcode and Apple Developer Portal
2. Provisioning profiles are configured in Xcode project
3. Code signing identity is selected based on the build configuration

## Post-Deployment Monitoring

After deploying the application, monitor:

1. **Crash Reports**:
   - Monitor app store consoles for crash reports
   - Integrate crash reporting tools if needed

2. **User Feedback**:
   - Monitor app store reviews
   - Collect feedback through in-app mechanisms
   - Analyze support tickets related to the application

3. **Analytics**:
   - Track user adoption of the new version
   - Monitor critical user journeys
   - Identify any performance issues

## Rollback Procedures

If critical issues are discovered after deployment:

### Google Play Store

1. Halt the rollout of the problematic version
2. Roll back to the previous version in the Play Console
3. Or expedite a fix and release a new version

### Apple App Store

1. Contact Apple to expedite review of a fixed version
2. In extreme cases, temporarily remove the app from the store
3. Release a fixed version as soon as possible

## Release Notes

For each release, prepare release notes that include:

1. **Version Number**: Clear indication of the version
2. **New Features**: List of new functionality
3. **Improvements**: Enhancements to existing features
4. **Bug Fixes**: List of resolved issues
5. **Known Issues**: Any known problems to be addressed in future releases

Example:
```
TTF MES Mobile v3.3.14

New Features:
- Added downtime tracking functionality
- Implemented multi-image upload for quality reports

Improvements:
- Enhanced QR code scanning speed
- Improved UI responsiveness on smaller devices

Bug Fixes:
- Fixed login issue on certain Android devices
- Resolved data synchronization problems in offline mode
- Corrected date formatting in reports

Known Issues:
- Some UI elements may not appear correctly in landscape mode
```

## Hotfix Process

For urgent fixes:

1. Create a hotfix branch from the production tag
2. Implement the necessary fix
3. Test thoroughly focusing on the specific fix
4. Update version (typically increment patch version)
5. Release through expedited channels
6. Merge the fix back to the development branch 