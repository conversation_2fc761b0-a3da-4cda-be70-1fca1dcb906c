﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ColorProductModel", Schema = "tSale")]
    public partial class ColorProductModel
    {
        public ColorProductModel()
        {
            ImageProductModel = new HashSet<ImageProductModel>();
        }

        [Key]
        public Guid ColorProductId { get; set; }
        public Guid ProductId { get; set; }
        public Guid? StyleId { get; set; }
        public Guid? MainColorId { get; set; }

        [ForeignKey("ProductId")]
        [InverseProperty("ColorProductModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("StyleId")]
        [InverseProperty("ColorProductModel")]
        public virtual StyleModel Style { get; set; }
        [InverseProperty("ColorProduct")]
        public virtual ICollection<ImageProductModel> ImageProductModel { get; set; }
    }
}