﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PageModel", Schema = "pms")]
    public partial class PageModel
    {
        public PageModel()
        {
            FavoriteReportModel = new HashSet<FavoriteReportModel>();
            PagePermissionModel = new HashSet<PagePermissionModel>();
            Function = new HashSet<FunctionModel>();
        }

        [Key]
        public Guid PageId { get; set; }
        [StringLength(100)]
        public string PageName { get; set; }
        [StringLength(300)]
        public string PageUrl { get; set; }
        public Guid? MenuId { get; set; }
        public int? OrderIndex { get; set; }
        [StringLength(100)]
        public string Icon { get; set; }
        public bool? Visiable { get; set; }
        public bool? isSystem { get; set; }
        public bool Actived { get; set; }
        [StringLength(100)]
        public string Parameter { get; set; }
        public bool? isWebPortal { get; set; }

        [ForeignKey("MenuId")]
        [InverseProperty("PageModel")]
        public virtual MenuModel Menu { get; set; }
        [InverseProperty("Page")]
        public virtual ICollection<FavoriteReportModel> FavoriteReportModel { get; set; }
        [InverseProperty("Page")]
        public virtual ICollection<PagePermissionModel> PagePermissionModel { get; set; }

        [ForeignKey("PageId")]
        [InverseProperty("Page")]
        public virtual ICollection<FunctionModel> Function { get; set; }
    }
}