import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/departmentRoutingMapping.dart';
import 'package:ttf/model/productionRecord.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogDetailReport.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../Widget/dialogWidget/dialogWorkShopDepartment.dart';
import '../element/RowDetail.dart';
import '../element/TableDetailReport.dart';
import '../element/timeOut.dart';
import '../model/workShopDepartment.dart';
import '../repository/function/detailReportFunction.dart';
import '../repository/showDateTime.dart';
import '../utils/input_formatters.dart';
import '../Storage/storageSecureStorage.dart';
import '../utils/user_helper.dart';
import 'LostConnect.dart';

class DetailReport extends StatefulWidget {
  final Data getData;
  final String token;
  final String dateTimeOld;
  const DetailReport({Key? key, required this.getData, required this.token, required this.dateTimeOld}) : super(key: key);

  @override
  State<DetailReport> createState() => _DetailReportState();
}

class _DetailReportState extends State<DetailReport> {
  String _day = "Một ngày";

  ListWorkShop? _selectedWorkShop;
  ListDepartment? _selectedDepartment;
  ListStepCode? _selectedStepCode;
  DateTime? _dateTo;
  DateTime? _dateFrom;
  TimeOfDay? _timeTo;
  TimeOfDay? _timeFrom;

  bool _visible = false;
  bool? _isLoading;
  int? _selectedIndex;
  int? _selectedIndex_2;

  bool _isNotWifi = false;
  bool _error = false;
  bool _error_2 = false;
  bool _error_3 = false;
  bool _error_4 = false;
  bool _error_5 = false;
  bool _error_6 = false;

  List<ListDepartment>? _lsGetDepartment;
  List<DepartmentRoutingMapping>? _lsDepartmentRoutingMapping;
  List<ListWorkShop>? _lsWorkShop;
  List<ListDetail> _lsOrderASC = [];

  final List<UsageQuantity> _lsItemUsageQuantity = [];
  final List<TextEditingController> _lsGetTextEditingControllerListView = [];
  final List<int> _lsGetBmsch = [];
  final _controller_1 = TextEditingController();
  final _controller_2 = TextEditingController();
  final _controller_3 = TextEditingController();
  final _focus_1 = FocusNode();
  final _focus_2 = FocusNode();
  final _focus_3 = FocusNode();
  final List<FocusNode> _lsFocusNode = [];
  bool? _viNotificate;
  bool? _viButtonError;
  late String _getDateTo;
  late String _getDateFrom;
  String? _viewTimeTo;
  String? _viewTimeFrom;
  List<ListStepCode> _getListStepCode = [];
  List<ListStepCode> _getListStepCodeFiltered = [];
  // late final KeyboardVisibilityController _keyboardVisibilityController;
  // late StreamSubscription<bool> _keyboardSubscription;
  late bool _timeOut;
  bool _disableButtonTimout = false;

  // final pointerUnits = {'M', 'M2', 'M3'};

  @override
  void initState() {
    super.initState();

    _setDefaultController();
    // _keyboardVisibilityController = KeyboardVisibilityController();
    // _keyboardSubscription = _keyboardVisibilityController.onChange.listen((isVisible) {
    //   if (!isVisible && mounted){
    //     if(_focus_1.hasFocus){
    //       _focus_1.unfocus();
    //     }
    //     if(_focus_2.hasFocus){
    //       _focus_2.unfocus();
    //     }
    //     if(_focus_3.hasFocus){
    //       _focus_3.unfocus();
    //     }
    //     for(var i in _lsFocusNode){
    //       if(i.hasFocus){
    //         i.unfocus();
    //       }
    //     }
    //   }
    // });
  }

  // Method to filter and get departments based on a step code
  List<ListDepartment>? _getFilteredDepartmentsByStepCode(String stepCode) {
    if (_lsDepartmentRoutingMapping == null || _lsDepartmentRoutingMapping!.isEmpty || _lsGetDepartment == null || _lsGetDepartment!.isEmpty) {
      return null;
    }

    // Find all mappings that match the stepCode as routingCode
    var matchingRoutings = _lsDepartmentRoutingMapping!.where((routing) => routing.routingCode?.toLowerCase() == stepCode.toLowerCase()).toList();

    if (matchingRoutings.isEmpty) {
      return null;
    }

    // Get all department codes from matching routings
    List<String?> matchingDepartmentCodes = matchingRoutings.map((routing) => routing.departmentCode?.toLowerCase()).toList();

    // Filter departments that match those department codes
    var filteredDepartments = _lsGetDepartment!.where((dept) {
      // Extract department code from display property
      String? deptCode;
      if (dept.display != null) {
        deptCode = dept.display!.split('|').first.trim().toLowerCase();
        return matchingDepartmentCodes.contains(deptCode);
      }
      return false;
    }).toList();

    // Sort the filtered departments by department code
    filteredDepartments.sort((a, b) {
      String codeA = a.display?.split('|').first.trim() ?? '';
      String codeB = b.display?.split('|').first.trim() ?? '';
      return codeA.compareTo(codeB);
    });

    debugPrint("Found ${filteredDepartments.length} matching departments for step code: $stepCode");
    // Create a test data by tripling the filtered departments
    // List<ListDepartment> tripledDepartments = [];

    // // Add the original filtered departments
    // tripledDepartments.addAll(filteredDepartments);

    // // Add two more copies of the filtered departments
    // tripledDepartments.addAll(filteredDepartments);
    // tripledDepartments.addAll(filteredDepartments);

    return filteredDepartments;
    // return tripledDepartments;
  }

  void _getWorkShopDepartment(String departmentId, BuildContext context) {
    debugPrint("--- _getWorkShopDepartment");
    try {
      setState(() {
        _getListStepCodeFiltered = [];
        // Clear selected step code
        _selectedStepCode = null;

        // Check if the department list exists and is not empty, then find and set the selected department
        // based on the scanned department ID (converting it to lowercase for case-insensitive comparison)
        if (_lsGetDepartment!.isNotEmpty || _lsGetDepartment != null) {
          _selectedDepartment = _lsGetDepartment!.firstWhere((element) => element.departmentId == departmentId.toLowerCase());
        }

        debugPrint("_selectedDepartment: ${json.encode(_selectedDepartment)}");
      });

      // Extract department code from display property if available
      String? departmentCode;
      if (_selectedDepartment?.display != null) {
        // Split by pipe and get the first part, then trim any whitespace
        departmentCode = _selectedDepartment!.display!.split('|').first.trim();
        debugPrint("Department Code: $departmentCode");

        // Get company code asynchronously
        _getCompanyCode().then((companyCode) async {
          debugPrint("Company Code: $companyCode");

          // Use the UserHelper to check if routing should be enabled for this company
          final userHelper = UserHelper();

          // Get the current list of routing-enabled company codes for logging
          final routingCodes = await userHelper.getRoutingEnabledCompanyCodes();
          debugPrint("Current routing-enabled company codes: $routingCodes");

          final shouldUseRoutingLogic = await userHelper.isRoutingEnabledForCompanyCode(companyCode);
          debugPrint("Should use routing logic: $shouldUseRoutingLogic");

          if (!mounted) return; // Check if widget is still mounted before using context

          if (shouldUseRoutingLogic) {
            // After getting department code, find the routing based on the department code
            if (departmentCode != null && _lsDepartmentRoutingMapping != null && _lsDepartmentRoutingMapping!.isNotEmpty) {
              // Find all routings that match the department code
              var matchingRoutings =
                  _lsDepartmentRoutingMapping!.where((routing) => routing.departmentCode?.toLowerCase() == departmentCode).toList();

              if (matchingRoutings.isNotEmpty) {
                debugPrint("Found ${matchingRoutings.length} matching routings for department code: $departmentCode");

                // Get all routing codes from matching routings
                List<String?> matchingRoutingCodes = matchingRoutings.map((routing) => routing.routingCode).toList();
                debugPrint("Matching routing codes: $matchingRoutingCodes");

                // Filter _getListStepCode to only show routings in this department
                var filteredStepCodes = _getListStepCode.where((stepCode) => matchingRoutingCodes.contains(stepCode.value)).toList();

                // If we have filtered step codes, update the dropdown options
                if (filteredStepCodes.isNotEmpty) {
                  if (!mounted) return; // Check again before setState
                  setState(() {
                    _getListStepCodeFiltered = filteredStepCodes;
                    // Set the first matching routing as selected
                    _selectedStepCode = filteredStepCodes.first;
                  });
                  debugPrint("Filtered step codes to ${filteredStepCodes.length} options");
                } else {
                  // display toast
                  if (mounted) {
                    // Check if still mounted before using context
                    showToast(context: context, message: "Không tìm thấy routing phù hợp cho tổ $departmentCode");
                  }
                  debugPrint("No matching step codes found in _getListStepCode");
                }
              } else {
                debugPrint("No routing found for department code: $departmentCode");
                if (!mounted) return; // Check again before setState
                setState(() {
                  _getListStepCodeFiltered = [];
                  _selectedStepCode = null;
                });
              }
            } else {
              debugPrint(
                  "Cannot find routing: departmentCode=$departmentCode, _lsDepartmentRoutingMapping is ${_lsDepartmentRoutingMapping == null ? 'null' : 'not null with ${_lsDepartmentRoutingMapping!.length} items'}");
            }
          } else {
            // For company codes not in the configured list, use previous logic
            if (!mounted) return; // Check again before setState
            setState(() {
              // Just use the original _getListStepCode without filtering
              _getListStepCodeFiltered = _getListStepCode;
              // Set default selection if available
              _selectedStepCode = _getListStepCode.isNotEmpty ? _getListStepCode.first : null;
            });
            debugPrint("Using previous unfiltered routing logic for company code: $companyCode");
          }
        });
      }

      setState(() {
        // Check if the workshop list exists and is not empty, then find and set the selected workshop
        // based on the workShopId of the selected department (converting it to lowercase for case-insensitive comparison)
        if (_lsWorkShop!.isNotEmpty || _lsWorkShop != null) {
          _selectedWorkShop = _lsWorkShop!.firstWhere((element) => element.workShopId == _selectedDepartment!.workShopId!.toLowerCase());
        }

        debugPrint("_selectedWorkShop: ${json.encode(_selectedWorkShop)}");

        // If both workshop and department lists exist and are not empty,
        // clear any error flags related to workshop and department selection
        if ((_lsWorkShop!.isNotEmpty || _lsWorkShop != null) && (_lsGetDepartment!.isNotEmpty || _lsGetDepartment != null)) {
          _error = false;
          _error_2 = false;
        }
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Quét thất bại! vui lòng kiểm tra lại mã QR và thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  // Method to show a dialog with suggested departments based on the stepCode
  void _showSuggestedDepartments(BuildContext context) {
    if (widget.getData.productionRecord!.stepCode == null) return;

    // Get departments filtered by the current step code
    final filteredDepartments = _getFilteredDepartmentsByStepCode(widget.getData.productionRecord!.stepCode!);

    if (filteredDepartments == null || filteredDepartments.isEmpty) {
      showToast(context: context, message: "Không tìm thấy tổ phù hợp cho công đoạn ${widget.getData.productionRecord!.stepCode}");
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
          titlePadding: EdgeInsets.only(left: 10.w, top: 10.h, right: 10.w, bottom: 5.h),
          insetPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 24.h),
          actionsPadding: EdgeInsets.only(bottom: 5.h, right: 5.w, top: 0),
          buttonPadding: EdgeInsets.zero,
          title: Text(
            'Chọn Tổ',
            style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            // constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.4),
            // constraints: BoxConstraints(maxHeight: 300.h),
            child: ListView.builder(
              // shrinkWrap: true,
              itemCount: filteredDepartments.length,
              itemBuilder: (context, index) {
                return ListTile(
                  // dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 0),
                  visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                  title: Text(
                    filteredDepartments[index].display ?? '',
                    style: TextStyle(fontSize: 13.sp),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Use direct selection instead of full _getWorkShopDepartment
                    _selectDepartmentDirectly(filteredDepartments[index]);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 0),
                // minimumSize: Size(40.w, 20.h),
                // tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Đóng',
                style: TextStyle(fontSize: 14.sp, color: const Color(0xff0052cc)),
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  // Method for direct department selection without additional checks
  void _selectDepartmentDirectly(ListDepartment department) {
    setState(() {
      // Set the department
      _selectedDepartment = department;
      debugPrint("Directly selected department: ${json.encode(_selectedDepartment)}");

      // Set the corresponding workshop
      if (_lsWorkShop != null && _lsWorkShop!.isNotEmpty) {
        _selectedWorkShop = _lsWorkShop!.firstWhere((element) => element.workShopId == _selectedDepartment!.workShopId!.toLowerCase(),
            orElse: () => _lsWorkShop!.first // Fallback to first workshop if not found
            );
        debugPrint("Directly selected workshop: ${json.encode(_selectedWorkShop)}");
      }

      // Since we're selecting from a filtered list, we know it's valid for this stepCode
      _error = false;
      _error_2 = false;

      // When stepCode is already defined, we don't need to set _selectedStepCode
      // as it's already coming from widget.getData.productionRecord!.stepCode
    });
  }

  // Helper method to derive company code from available data
  Future<String?> _getCompanyCode() async {
    // Try to get company code from UserHelper first
    final userHelper = UserHelper();
    final userCompanyCode = await userHelper.getCompanyCode();

    // If we got a valid company code from the user data, return it
    if (userCompanyCode != null && userCompanyCode.isNotEmpty) {
      debugPrint("Company code retrieved from UserHelper: $userCompanyCode");
      return userCompanyCode;
    }

    // If UserHelper couldn't provide the company code, use fallback methods
    debugPrint("Company code not found in user data, using fallback methods");

    // Fall back to trying to parse company code from production record
    // Try multiple approaches to get company code
    final productionRecord = widget.getData.productionRecord;
    if (productionRecord == null) return null;

    // Approach 1: Check if the property1 might contain company code
    // (assuming it might be in format like '1000-XXXX' or similar)
    if (productionRecord.property1 != null) {
      final property1 = productionRecord.property1!;
      if (property1.startsWith('1000') || property1.startsWith('1100') || property1.startsWith('1010') || property1.startsWith('1200')) {
        return property1.substring(0, 4);
      }
    }

    // Approach 2: If "productionOrderSAP" has a prefix that matches company code pattern
    if (productionRecord.productionOrderSAP != null && productionRecord.productionOrderSAP!.length >= 4) {
      final sapOrder = productionRecord.productionOrderSAP!;
      if (sapOrder.startsWith('1000') || sapOrder.startsWith('1100') || sapOrder.startsWith('1010') || sapOrder.startsWith('1200')) {
        return sapOrder.substring(0, 4);
      }
    }

    // Approach 3: Check for other fields or prefixes that might contain company code
    // This is a common pattern where company code is prefixed to IDs
    final possibleFields = [productionRecord.toStockCode, productionRecord.productCode, productionRecord.departmentId, productionRecord.property2];

    for (final field in possibleFields) {
      if (field != null && field.length >= 4) {
        if (field.startsWith('1000') || field.startsWith('1100') || field.startsWith('1010') || field.startsWith('1200')) {
          return field.substring(0, 4);
        }
      }
    }

    // Default to 1000 if we can't determine company code
    // (assuming 1000 is the most common company code)
    return '1000';
  }

  Future<void> _pickDateTo(BuildContext context) async {
    DateTime initialDate = DateTime.now();
    final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _dateTo);
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _dateTo = newDate;
      _getDateTo = DetailReportFunction.getDateTo(_dateTo);
    });
  }

  Future<void> _pickDateToIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _dateTo = newDate;
        _getDateTo = DetailReportFunction.getDateTo(_dateTo);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _pickDateFrom(BuildContext context) async {
    DateTime initialDate = DateTime.now();
    final newDate = await ShowDateTime.pickDateAndroid(context, initialDate, _dateFrom);
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _dateFrom = newDate;
      _getDateFrom = DetailReportFunction.getDateFrom(_dateFrom);
    });
  }

  Future<void> _pickDateFromIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _dateFrom = newDate;
        _getDateFrom = DetailReportFunction.getDateTo(_dateFrom);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _pickTimeTo(BuildContext context) async {
    try {
      const initialTime = TimeOfDay(hour: 9, minute: 0);
      TimeOfDay? newTime = await ShowDateTime.showTime(context, initialTime, _timeTo);
      debugPrint(newTime.toString());
      if (!mounted) return;
      if (newTime == null) return;
      setState(() {
        _timeTo = newTime;
        _viewTimeTo = DetailReportFunction.viewTimeTo(_timeTo);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _pickTimeFrom(BuildContext context) async {
    try {
      const initialTime = TimeOfDay(hour: 9, minute: 0);
      TimeOfDay? newTime = await ShowDateTime.showTime(context, initialTime, _timeFrom);
      if (!mounted) return;
      if (newTime == null) return;
      setState(() {
        _timeFrom = newTime;
        _viewTimeFrom = DetailReportFunction.viewTimeFrom(_timeFrom);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _pickTimeFromIOS(BuildContext context) async {
    try {
      DateTime now = DateTime.now();
      DateTime midnight = DateTime(now.year, now.month, now.day);
      DateTime? newTime = await ShowDateTime.showTimeIOS(context, midnight);
      if (!mounted) return;
      if (newTime == null) return;
      setState(() {
        _timeFrom = TimeOfDay.fromDateTime(newTime);
        _viewTimeFrom = DetailReportFunction.viewTimeFromIos(now, _timeFrom);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _pickTimeToIOS(BuildContext context) async {
    try {
      DateTime now = DateTime.now();
      DateTime midnight = DateTime(now.year, now.month, now.day);
      DateTime? newTime = await ShowDateTime.showTimeIOS(context, midnight);
      if (!mounted) return;
      if (newTime == null) return;
      setState(() {
        _timeTo = TimeOfDay.fromDateTime(newTime);
        _viewTimeTo = DetailReportFunction.viewTimeToIos(now, _timeTo);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  void _checkExistStage() {
    final nullListStepCode = ListStepCode();
    final check = widget.getData.listStepCode!
        .firstWhere((element) => element.value == widget.getData.productionRecord!.stepCode, orElse: () => nullListStepCode);
    if (widget.getData.productionRecord!.stepCode != null) {
      if (check != nullListStepCode) {
        _viNotificate = false;
        _viButtonError = true;
      } else {
        _viNotificate = true;
        _viButtonError = false;
      }
    } else {
      _viNotificate = false;
      _viButtonError = true;
    }
  }

  Future<void> _postProductionRecordHistoryApi(
      String ttlsx, String fromTime, String toTime, String itmno, String stepCode, int? index, BuildContext context) async {
    try {
      setState(() {
        _selectedIndex = index;
      });
      final dataRecordHistory = await DetailReportFunction.postProductionRecordHistoryApi(ttlsx, fromTime, toTime, itmno, stepCode, widget.token);
      if (!mounted) return;
      setState(() {
        _selectedIndex = -1;
      });

      if (dataRecordHistory != null) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return DialogDetailReport(getProductionRecordHistoryApi: dataRecordHistory, productionRecordUnit: widget.getData.productionRecord!.unit);
          },
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Có lỗi xảy ra, vui lòng thử lại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
        ));
      }
    } on SocketException catch (_) {
      setState(() {
        _selectedIndex = -1;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      setState(() {
        _selectedIndex = -1;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _fetchDepartentByApi(
      String ttlsx, String fromTime, String toTime, String itmno, String stepCode, int? index, BuildContext context) async {
    try {
      setState(() {
        _selectedIndex_2 = index;
      });
      final dataDepartent = await DetailReportFunction.fetchDepartentByApi(ttlsx, fromTime, toTime, itmno, stepCode, widget.token);
      if (!mounted) return;
      setState(() {
        _selectedIndex_2 = -1;
      });
      if (dataDepartent != null) {
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return DialogWorkShopDepartment(getDepartentByApi: dataDepartent);
            });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Có lỗi xảy ra, vui lòng thử lại',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
        ));
      }
    } on SocketException catch (_) {
      setState(() {
        _selectedIndex_2 = -1;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      setState(() {
        _selectedIndex_2 = -1;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  Future<void> _getWorkShopApi() async {
    try {
      setState(() {
        _timeOut = false;
        _isLoading = true;
        _isNotWifi = false;
      });
      final dataWorkShop = await DetailReportFunction.getWorkShopApi(widget.token);
      if (!mounted) return;
      setState(() {
        if (dataWorkShop != null) {
          _lsGetDepartment = dataWorkShop.listDepartment;
          _lsWorkShop = dataWorkShop.listWorkShop;
          _lsDepartmentRoutingMapping = dataWorkShop.listDepartmentRoutingMapping;
          _isLoading = false;
        } else {
          setState(() {
            _isLoading = true;
          });
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = true;
        _timeOut = false;
      });
    }
  }

  void _setError() {
    setState(() {
      if ((_selectedWorkShop != null && _selectedWorkShop!.workShopId == " ") || _selectedWorkShop == null) {
        _error = true;
      } else {
        _error = false;
      }
      if (_selectedDepartment == null) {
        _error_2 = true;
      } else {
        _error_2 = false;
      }
      if (widget.getData.productionRecord!.stepCode == null) {
        if (_selectedStepCode == null || _selectedStepCode!.value == " ") {
          _error_3 = true;
        } else {
          _error_3 = false;
        }
      } else {
        _error_3 = false;
      }
      if (_controller_1.text.isEmpty) {
        _error_4 = true;
      } else {
        _error_4 = false;
      }
      if (widget.getData.productionRecord!.stepCode != null) {
        if (_controller_2.text.isEmpty || (_controller_2.text == 0.toString() && _controller_3.text == 0.toString())) {
          _error_5 = true;
        } else {
          _error_5 = false;
        }
      } else {
        _error_5 = false;
      }
      if (widget.getData.productionRecord!.stepCode != null) {
        if (_controller_3.text.isEmpty || (_controller_3.text == 0.toString() && _controller_2.text == 0.toString())) {
          _error_6 = true;
        } else {
          _error_6 = false;
        }
      } else {
        _error_6 = false;
      }
    });
  }

  Future<void> _setDefaultController() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);

    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
    } else {
      if (!mounted) return;
      await _getWorkShopApi();
      if (!mounted) return;
      _checkExistStage();
      _getDateTo = DetailReportFunction.getDateTo(_dateTo);
      _getDateFrom = DetailReportFunction.getDateFrom(_dateFrom);
      _lsOrderASC = DetailReportFunction.orderASCListDetail(widget.getData);
      _viewTimeTo = DetailReportFunction.viewTimeTo(_timeTo);
      _viewTimeFrom = DetailReportFunction.viewTimeFrom(_timeFrom);
      _getListStepCode = DetailReportFunction.getListStepCode(widget.getData) ?? [];
      _controller_1.text = 1.toString();
      _controller_2.text = 0.toString();
      _controller_3.text = 0.toString();
      if (widget.getData.usageQuantity!.isNotEmpty && _isNotWifi == false) {
        for (int i = 0; i < widget.getData.usageQuantity!.length; i++) {
          final item = widget.getData.usageQuantity![i];
          _lsItemUsageQuantity.add(item);
          _lsGetTextEditingControllerListView.add(TextEditingController());
          _lsGetBmsch.add((item.bmsch ?? 0.0).round());
          _lsGetTextEditingControllerListView[i].text = 0.toString();
          _lsFocusNode.add(FocusNode());
        }
      }
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButtonTimout = true;
    });
  }

  @override
  void dispose() {
    _controller_1.dispose();
    _controller_2.dispose();
    _controller_3.dispose();
    _focus_1.dispose();
    _focus_2.dispose();
    _focus_3.dispose();
    for (var i in _lsGetTextEditingControllerListView) {
      i.dispose();
    }
    for (var i in _lsFocusNode) {
      i.dispose();
    }
    // _keyboardSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> dummyPhanXuong = [
      // {'title': 'Phan xuong 1', 'barcode': '34133b41-600c-4a76-9546-58113f741a8a'},
      {'title': 'px 1000', 'barcode': '314a88dc-c109-4585-8c59-f89df9ca26ae'},
      {'title': 'px 1000 2 step', 'barcode': 'f6853d47-f87a-4cfb-a484-cda848a58f7c'},
      {'title': 'px 1000 no map', 'barcode': '0a0d62a4-b991-42f2-b483-01cfaf66872a'},
      {'title': 'px 1200', 'barcode': '538e20c7-a176-4635-b188-054a09bdcabd'},
      {'title': '1200 SF-BOC', 'barcode': '7ae77744-60db-4e9d-a638-a394ab19c098'},

      // {'LSX': 'test', 'hangTagId': '9C600BD3-6E6C-4A3A-969F-0EB7941C5AB0'},
    ];
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimout)))
        : WillPopScope(
            onWillPop: () async {
              Navigator.pop(context, false);
              return false;
            },
            child: GestureDetector(
                onTap: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus) {
                    currentFocus.unfocus();
                  }
                },
                child: Scaffold(
                    backgroundColor: Colors.white,
                    appBar: AppBar(
                      titleSpacing: 0,
                      automaticallyImplyLeading: false,
                      backgroundColor: const Color(0xff0052cc),
                      elevation: 0,
                      centerTitle: true,
                      leading: IconButton(
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                        onPressed: () {
                          Navigator.pop(context, false);
                        },
                      ),
                      title: Text(
                        'Ghi nhận sản lượng',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                      ),
                    ),
                    body: _isNotWifi == false
                        ? _isLoading == true
                            ? const Center(child: CircularProgressIndicator())
                            : SingleChildScrollView(
                                child: SafeArea(
                                  minimum: EdgeInsets.symmetric(horizontal: 10.w),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Visibility(
                                        visible: _viNotificate ?? false,
                                        child: Container(
                                          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.h),
                                          decoration: const BoxDecoration(
                                            color: Color(0xffe08e0b),
                                          ),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 8,
                                                child: Text(
                                                  "Chi tiết \"" +
                                                      widget.getData.productionRecord!.productAttributes.toString() +
                                                      "\" không khai báo sản xuất tại công đoạn \"" +
                                                      widget.getData.productionRecord!.stepCode.toString() +
                                                      " | " +
                                                      widget.getData.productionRecord!.stepName.toString() +
                                                      "\"",
                                                  style: TextStyle(fontSize: 12.sp, color: Colors.white),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: IconButton(
                                                  icon: Icon(Icons.close_rounded, size: 13.sp),
                                                  onPressed: () {
                                                    setState(() {
                                                      _viNotificate = false;
                                                    });
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Visibility(
                                        visible: _viNotificate ?? false,
                                        child: SizedBox(
                                          height: 10.h,
                                        ),
                                      ),
                                      LabeledDetailRow(title: 'LSX ĐT:', text: widget.getData.productionRecord!.productionOrder.toString()),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      LabeledDetailRow(title: 'Đợt SX:', text: widget.getData.productionRecord!.summary.toString()),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      LabeledDetailRow(title: 'LSX SAP:', text: widget.getData.productionRecord!.productionOrderSAP.toString()),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      // RowDetail(
                                      //     title: 'Sản phẩm:',
                                      //     trailing:
                                      //         '(${widget.getData.productionRecord!.qty?.toStringAsFixed(3)} ${widget.getData.productionRecord!.unit.toString()}) ${widget.getData.productionRecord!.productCode.toString()} | ${widget.getData.productionRecord!.productName.toString()}'),
                                      LabeledDetailRow(
                                          title: 'Sản phẩm:',
                                          text:
                                              '(${pointerUnits.contains(widget.getData.productionRecord!.unit) ? widget.getData.productionRecord!.qty?.toStringAsFixed(3) : widget.getData.productionRecord!.qty?.round()} ${widget.getData.productionRecord!.unit.toString()}) ${widget.getData.productionRecord!.productCode.toString()} | ${widget.getData.productionRecord!.productName.toString()}'),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      // RowDetail(
                                      //     title: 'Chi tiết:',
                                      //     trailing:
                                      //         '(${(widget.getData.productionRecord!.productAttributesQtyD ?? 0.0).round().toString()}/${(widget.getData.productionRecord!.productAttributesQty ?? 0.0).round().toString()}  ${widget.getData.productionRecord!.productAttributesUnit.toString()}) ${widget.getData.productionRecord!.productAttributes.toString()} | ${widget.getData.productionRecord!.productAttributesName.toString()} (${widget.getData.productionRecord!.poT12 ?? " "})'),
                                      LabeledDetailRow(
                                        title: 'Chi tiết:',
                                        text:
                                            '(${pointerUnits.contains(widget.getData.productionRecord!.unit) ? (widget.getData.productionRecord!.productAttributesQtyD ?? 0.0).toStringAsFixed(3) : (widget.getData.productionRecord!.productAttributesQtyD ?? 0.0).round().toString()}/${pointerUnits.contains(widget.getData.productionRecord!.unit) ? (widget.getData.productionRecord!.productAttributesQty ?? 0.0).toStringAsFixed(3) : (widget.getData.productionRecord!.productAttributesQty ?? 0.0).round().toString()} ${widget.getData.productionRecord!.unit.toString()}) ${widget.getData.productionRecord!.productAttributes.toString()} | ${widget.getData.productionRecord!.productAttributesName.toString()} (${widget.getData.productionRecord!.poT12 ?? " "})',
                                      ),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Expanded(
                                            flex: 3,
                                            child: Row(
                                              children: [
                                                Flexible(
                                                  flex: 2,
                                                  child: Text(
                                                    "Một ngày:",
                                                    style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                  ),
                                                ),
                                                Flexible(
                                                  flex: 1,
                                                  child: Transform.scale(
                                                    scale: 1.sp,
                                                    child: Radio<String>(
                                                      value: "Một ngày",
                                                      groupValue: _day,
                                                      onChanged: (String? value) {
                                                        setState(() {
                                                          _day = value!;
                                                          _visible = false;
                                                        });
                                                      },
                                                      activeColor: const Color(0xff0052cc),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(width: 10.w),
                                          Expanded(
                                            flex: 7,
                                            child: Row(
                                              children: [
                                                Flexible(
                                                  flex: 2,
                                                  child: Text(
                                                    "Nhiều ngày:",
                                                    style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                  ),
                                                ),
                                                Flexible(
                                                  flex: 1,
                                                  child: Transform.scale(
                                                    scale: 1.sp,
                                                    child: Radio<String>(
                                                      value: "Nhiều ngày",
                                                      groupValue: _day,
                                                      onChanged: (String? value) {
                                                        setState(() {
                                                          _day = value!;
                                                          _visible = true;
                                                        });
                                                      },
                                                      activeColor: const Color(0xff0052cc),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Expanded(
                                            flex: 3,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: _visible == false
                                                  ? Text(
                                                      "Ngày:",
                                                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                    )
                                                  : Text(
                                                      "Từ ngày:",
                                                      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                    ),
                                            ),
                                          ),
                                          SizedBox(width: 10.w),
                                          Expanded(
                                            flex: 7,
                                            child: GestureDetector(
                                              onTap: () {
                                                FocusScope.of(context).unfocus();
                                                Platform.isAndroid ? _pickDateFrom(context) : _pickDateFromIOS(context);
                                              },
                                              child: Container(
                                                width: double.infinity,
                                                padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 5.w),
                                                decoration: BoxDecoration(
                                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      flex: 9,
                                                      child: Text(
                                                        _getDateFrom,
                                                        style: TextStyle(fontSize: 12.sp),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      flex: 1,
                                                      child: Icon(Icons.edit_calendar_rounded, color: const Color(0xff0052cc), size: 15.sp),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 5.h,
                                      ),
                                      Visibility(
                                        visible: _visible,
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Expanded(
                                              flex: 3,
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Text(
                                                  "Đến ngày:",
                                                  style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 10.w),
                                            Expanded(
                                              flex: 7,
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: GestureDetector(
                                                  onTap: () {
                                                    FocusScope.of(context).unfocus();
                                                    Platform.isAndroid ? _pickDateTo(context) : _pickDateToIOS(context);
                                                  },
                                                  child: Container(
                                                    width: double.infinity,
                                                    padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 5.w),
                                                    decoration: BoxDecoration(
                                                      border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Expanded(
                                                          flex: 9,
                                                          child: Text(
                                                            _getDateTo,
                                                            style: TextStyle(fontSize: 12.sp),
                                                          ),
                                                        ),
                                                        Expanded(
                                                            flex: 1,
                                                            child: Icon(Icons.edit_calendar_rounded, color: const Color(0xff0052cc), size: 15.sp)),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Text(
                                        "Phân xưởng:",
                                        style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Container(
                                          width: double.infinity,
                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                          decoration: BoxDecoration(
                                            border: Border.all(width: 0.5.w, color: _error == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                          ),
                                          child: Text(
                                            _selectedWorkShop == null ? "Quét mã để lấy phân xưởng và tổ" : _selectedWorkShop!.display.toString(),
                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                          )),
                                      SizedBox(height: _error == true ? 15.h : 0),
                                      ContainerError.widgetError(_error, "Bạn chưa chọn phân xưởng"),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Text(
                                        "Tổ:",
                                        style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Container(
                                          width: double.infinity,
                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                          decoration: BoxDecoration(
                                            border:
                                                Border.all(width: 0.5.w, color: _error_2 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                          ),
                                          child: Text(
                                            _selectedDepartment == null ? "Quét mã để lấy phân xưởng và tổ" : _selectedDepartment!.display.toString(),
                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                          )),
                                      SizedBox(height: _error_2 == true ? 15.h : 0),
                                      ContainerError.widgetError(_error_2, "Bạn chưa chọn Tổ"),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Center(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              decoration: const BoxDecoration(),
                                              child: ElevatedButton.icon(
                                                style: ButtonStyle(
                                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                  side: MaterialStateProperty.all(
                                                    const BorderSide(
                                                      color: Color(0xff0052cc),
                                                    ),
                                                  ),
                                                  backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                ),
                                                onPressed: () async {
                                                  var data = await Navigator.pushNamed(context, '/QRcodePageGetWorkShopDepartment');
                                                  if (!mounted) return;
                                                  if (data == null) return;
                                                  // print(data);
                                                  _getWorkShopDepartment(data.toString(), context);
                                                },
                                                icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                label: Text(
                                                  'Quét mã',
                                                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                ),
                                              ),
                                            ),
                                            // Show "Chọn tổ" button only when stepCode is available
                                            if (widget.getData.productionRecord!.stepCode != null) SizedBox(width: 10.w),
                                            if (widget.getData.productionRecord!.stepCode != null)
                                              Container(
                                                decoration: const BoxDecoration(),
                                                child: ElevatedButton.icon(
                                                  style: ButtonStyle(
                                                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                    side: MaterialStateProperty.all(
                                                      const BorderSide(
                                                        color: Color(0xff0052cc),
                                                      ),
                                                    ),
                                                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                  ),
                                                  onPressed: () => _showSuggestedDepartments(context),
                                                  icon: Icon(Icons.list_alt, size: 25.sp, color: Colors.white),
                                                  label: Text(
                                                    'Chọn tổ',
                                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      kDebugMode
                                          ? RenderDebugButtons(dummyPhanXuong, (item) {
                                              _getWorkShopDepartment(item['barcode'], context);
                                            }, 'title')
                                          : Container(),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Text(
                                        "CĐ hoàn tất:",
                                        style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      widget.getData.productionRecord!.stepCode == null && _getListStepCode.isNotEmpty
                                          ? Container(
                                              height: 33.h,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    width: 0.5.w, color: _error_3 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                              ),
                                              child: DropdownButtonHideUnderline(
                                                child: DropdownButton<ListStepCode>(
                                                  isExpanded: true,
                                                  value: _selectedStepCode ?? DetailReportFunction.defaultListStepCode,
                                                  iconSize: 15.sp,
                                                  style: const TextStyle(color: Colors.black),
                                                  onChanged: (ListStepCode? value) {
                                                    setState(() {
                                                      _selectedStepCode = value!;
                                                      if (_selectedStepCode!.value != " ") {
                                                        _error_3 = false;
                                                      }
                                                    });
                                                  },
                                                  items: _getListStepCodeFiltered.map((ListStepCode group) {
                                                    return DropdownMenuItem<ListStepCode>(
                                                        value: group,
                                                        child: Center(
                                                          child: Text(
                                                            group.text.toString(),
                                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                          ),
                                                        ));
                                                  }).toList(),
                                                  // items: _getListStepCode.map((ListStepCode group) {
                                                  //   return DropdownMenuItem<ListStepCode>(
                                                  //       value: group,
                                                  //       child: Center(
                                                  //         child: Text(
                                                  //           group.text.toString(),
                                                  //           style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                                  //         ),
                                                  //       ));
                                                  // }).toList(),
                                                ),
                                              ),
                                            )
                                          : Text(
                                              widget.getData.productionRecord!.stepCode.toString() +
                                                  " | " +
                                                  widget.getData.productionRecord!.stepName.toString(),
                                              style: TextStyle(
                                                fontSize: 13.sp,
                                              ),
                                            ),
                                      SizedBox(height: _error_3 == true ? 15.h : 0),
                                      ContainerError.widgetError(_error_3, "Bạn chưa chọn CĐ hoàn tất"),

                                      kDebugMode
                                          ? RenderDebugButtons([
                                              {'title': '_selectedStepCode'}
                                            ], (item) {
                                              debugPrint("Selected Step Code: ${_selectedStepCode?.toJson()}");
                                            }, 'title')
                                          : Container(),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Expanded(
                                            flex: 3,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Text(
                                                "Lần đi qua C.Đoạn:",
                                                style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10.w),
                                          Expanded(
                                            flex: 7,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Column(children: <Widget>[
                                                Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                        width: 0.5.w, color: _error_4 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                                  ),
                                                  child: TextFormField(
                                                    maxLines: null,
                                                    keyboardType: TextInputType.number,
                                                    inputFormatters: <TextInputFormatter>[
                                                      FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                    ],
                                                    focusNode: _focus_1,
                                                    textAlign: TextAlign.center,
                                                    controller: _controller_1,
                                                    style: TextStyle(fontSize: 12.sp),
                                                    decoration: InputDecoration(
                                                      border: InputBorder.none,
                                                      focusedBorder: InputBorder.none,
                                                      enabledBorder: InputBorder.none,
                                                      errorBorder: InputBorder.none,
                                                      disabledBorder: InputBorder.none,
                                                      filled: true,
                                                      isDense: true,
                                                      fillColor: Colors.white,
                                                      hintStyle: TextStyle(fontSize: 12.sp),
                                                      contentPadding: EdgeInsets.zero,
                                                    ),
                                                  ),
                                                ),
                                                // SizedBox(
                                                //   height: 30.h,
                                                //   child: TextFormField(
                                                //     textAlign: TextAlign.center,
                                                //     keyboardType: TextInputType.number,
                                                //     inputFormatters: <
                                                //         TextInputFormatter>[
                                                //       FilteringTextInputFormatter.allow(
                                                //           RegExp("[0-9|.]")),
                                                //     ],
                                                //     controller: _controller_1,
                                                //     focusNode: _focus_1,
                                                //     style: TextStyle(fontSize: 12.sp),
                                                //     decoration: InputDecoration(
                                                //       border: InputBorder.none,
                                                //       focusedBorder: OutlineInputBorder(
                                                //         borderRadius:
                                                //             BorderRadius.circular(0),
                                                //         borderSide: BorderSide(
                                                //             width: 0.5,
                                                //             color: _error_4 == true
                                                //                 ? const Color(
                                                //                     0xFFD32F2F)
                                                //                 : Colors.grey.shade400),
                                                //       ),
                                                //       enabledBorder: OutlineInputBorder(
                                                //         borderRadius:
                                                //             BorderRadius.circular(0),
                                                //         borderSide: BorderSide(
                                                //             width: 0.5,
                                                //             color: _error_4 == true
                                                //                 ? const Color(
                                                //                     0xFFD32F2F)
                                                //                 : Colors.grey.shade400),
                                                //       ),
                                                //       errorBorder: InputBorder.none,
                                                //       disabledBorder: InputBorder.none,
                                                //       filled: true,
                                                //       fillColor: Colors.white,
                                                //       hintStyle:
                                                //           TextStyle(fontSize: 12.sp),
                                                //       contentPadding:
                                                //           EdgeInsets.symmetric(
                                                //               horizontal: 20.w),
                                                //     ),
                                                //   ),
                                                // ),
                                                SizedBox(height: _error_4 == true ? 5.h : 0),
                                                Visibility(
                                                    visible: _error_4,
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: <Widget>[
                                                        Flexible(
                                                          flex: 1,
                                                          child: Icon(Icons.error_outline, size: 13.sp, color: Colors.red[700]),
                                                        ),
                                                        SizedBox(width: 5.w),
                                                        Flexible(
                                                            flex: 8,
                                                            child: Text("Bạn chưa nhập lần đi qua C.Đoạn",
                                                                style: TextStyle(fontSize: 11.sp, color: Colors.red[700])))
                                                      ],
                                                    )),
                                              ]),
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Visibility(
                                        visible: _lsOrderASC.isNotEmpty ? true : false,
                                        child: Text(
                                          "SP/CT/Cụm hoàn thành đã ghi nhận:",
                                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      SizedBox(
                                        height: _lsOrderASC.isNotEmpty ? 5.h : 0,
                                      ),
                                      _lsOrderASC.isNotEmpty
                                          ? ListView.builder(
                                              shrinkWrap: true,
                                              physics: const NeverScrollableScrollPhysics(),
                                              padding: EdgeInsets.zero,
                                              itemCount: _lsOrderASC.length,
                                              itemBuilder: (BuildContext context, int index) {
                                                final item = widget.getData.productionRecord!.listDetail![index];
                                                final stringToDateTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(item.toTime.toString());
                                                String dateToString = DateFormat("dd/MM/yyyy").format(stringToDateTime);
                                                final stringToTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(item.toTime.toString());
                                                String toTimeToString = DateFormat("HH:mm").format(stringToTime);
                                                final stringFormTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(item.fromTime.toString());
                                                String formTimeToString = DateFormat("HH:mm").format(stringFormTime);

                                                bool isPointer = pointerUnits.contains(widget.getData.productionRecord!.unit);

                                                return TableDetailReport(
                                                    stt: (index + 1).toString(),
                                                    cluster: item.ktext.toString(),
                                                    date: dateToString,
                                                    completedStage: '${item.stepCode} (${item.phase ?? ' '})',
                                                    // dKD: '${(item.quantityD ?? 0.0).round()} Đ | ${(item.quantityKD ?? 0.0).round()} KĐ',
                                                    dKD:
                                                        '${isPointer ? (item.quantityD ?? 0.0).toStringAsFixed(3) : (item.quantityD ?? 0.0).round()} Đ | ${isPointer ? (item.quantityKD ?? 0.0).toStringAsFixed(3) : (item.quantityKD ?? 0.0).round()} KĐ',
                                                    quantityD: item.quantityD,
                                                    quantityKD: item.quantityKD,
                                                    timeStart: formTimeToString,
                                                    timeEnd: toTimeToString,
                                                    creator: item.createByName ?? ' ',
                                                    creatorTime: item.createTime ?? " ",
                                                    selectedIndex: _selectedIndex,
                                                    selectedIndex_2: _selectedIndex_2,
                                                    getIndex: index,
                                                    onTap: () => _postProductionRecordHistoryApi(
                                                        item.customerReference.toString(),
                                                        item.fromTime.toString(),
                                                        item.toTime.toString(),
                                                        item.itmno.toString(),
                                                        item.stepCode.toString(),
                                                        index,
                                                        context),
                                                    onTap_2: () => _fetchDepartentByApi(item.customerReference.toString(), item.fromTime.toString(),
                                                        item.toTime.toString(), item.itmno.toString(), item.stepCode.toString(), index, context));
                                              })
                                          : Container(),
                                      SizedBox(
                                        height: 15.h,
                                      ),
                                      Visibility(
                                        visible: widget.getData.productionRecord!.stepCode == null
                                            ? _selectedStepCode != null && _selectedStepCode!.value != " "
                                                ? true
                                                : false
                                            : true,
                                        child: Text(
                                          "Ghi nhận thêm:",
                                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      SizedBox(
                                        height: widget.getData.productionRecord!.stepCode == null
                                            ? _selectedStepCode != null && _selectedStepCode!.value != " "
                                                ? 5.h
                                                : 0
                                            : 5.h,
                                      ),
                                      Visibility(
                                        visible: widget.getData.productionRecord!.stepCode == null
                                            ? _selectedStepCode != null && _selectedStepCode!.value != " "
                                                ? true
                                                : false
                                            : true,
                                        child: Table(
                                          border: TableBorder.all(color: Colors.grey.shade400, width: 0.5.w),
                                          columnWidths: const <int, TableColumnWidth>{
                                            0: FractionColumnWidth(0.12),
                                            1: FractionColumnWidth(0.12),
                                            2: FractionColumnWidth(0.25),
                                            3: FractionColumnWidth(0.05),
                                            4: FractionColumnWidth(0.1),
                                            5: FractionColumnWidth(0.25),
                                            6: FractionColumnWidth(0.11),
                                          },
                                          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                          children: <TableRow>[
                                            TableRow(
                                              children: <Widget>[
                                                Container(
                                                  padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                                  child: Text(
                                                    "Tồn",
                                                    style: TextStyle(fontSize: 10.sp),
                                                  ),
                                                ),
                                                Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    child: Center(
                                                        // child: Text(
                                                        //   (widget.getData.productionRecordRouting!.quantityDLD ?? 0.0).round().toString(),
                                                        //   style: TextStyle(fontSize: 10.sp),
                                                        // ),
                                                        child: Text(
                                                      pointerUnits.contains(widget.getData.productionRecord!.unit)
                                                          ? (widget.getData.productionRecordRouting!.quantityDLD ?? 0.0).toStringAsFixed(3)
                                                          : (widget.getData.productionRecordRouting!.quantityDLD ?? 0.0).round().toString(),
                                                      style: TextStyle(fontSize: 10.sp),
                                                    ))),
                                                Column(children: <Widget>[
                                                  Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.h),
                                                    child: Container(
                                                      padding: REdgeInsets.all(3),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            width: 0.5.w, color: _error_5 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                                      ),
                                                      child: Center(
                                                        child: TextFormField(
                                                          maxLines: null,
                                                          textAlign: TextAlign.center,
                                                          controller: _controller_2,
                                                          focusNode: _focus_2,
                                                          style: TextStyle(fontSize: 12.sp),
                                                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                                                          inputFormatters: [CommaToDotTextInputFormatter()],
                                                          // inputFormatters: <TextInputFormatter>[
                                                          //   FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                          // ],
                                                          decoration: InputDecoration(
                                                            border: InputBorder.none,
                                                            isDense: true,
                                                            contentPadding: EdgeInsets.zero,
                                                            errorBorder: InputBorder.none,
                                                            disabledBorder: InputBorder.none,
                                                            filled: true,
                                                            fillColor: Colors.white,
                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                          ),
                                                          onChanged: (value) {
                                                            if (widget.getData.usageQuantity!.isNotEmpty || widget.getData.usageQuantity != null) {
                                                              for (int i = 0; i < widget.getData.usageQuantity!.length; i++) {
                                                                if (_controller_2.text.isNotEmpty && _controller_3.text.isEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text =
                                                                      (0 + (int.parse(value)) * (_lsGetBmsch[i])).toString();
                                                                } else if (_controller_2.text.isNotEmpty && _controller_3.text.isNotEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text = (0 +
                                                                          (int.parse(value)) * (_lsGetBmsch[i]) +
                                                                          (0 + (int.parse(_controller_3.text)) * (_lsGetBmsch[i])))
                                                                      .toString();
                                                                } else if (_controller_2.text.isEmpty && _controller_3.text.isEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text = "0";
                                                                } else {
                                                                  _lsGetTextEditingControllerListView[i].text = _controller_3.text;
                                                                }
                                                              }
                                                            }
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  // Container(
                                                  //   height: 30.h,
                                                  //   padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                  //   child: TextFormField(
                                                  //     textAlign: TextAlign.center,
                                                  //     keyboardType: TextInputType.number,
                                                  //     inputFormatters: <TextInputFormatter>[
                                                  //       FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                  //     ],
                                                  //     controller: _controller_2,
                                                  //     focusNode: _focus_2,
                                                  //     style: TextStyle(fontSize: 10.sp),
                                                  //     decoration: InputDecoration(
                                                  //       border: InputBorder.none,
                                                  //       focusedBorder: OutlineInputBorder(
                                                  //         borderRadius: BorderRadius.circular(0),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: _error_5 == true
                                                  //                 ? const Color(0xFFD32F2F)
                                                  //                 : Colors.grey.shade400),
                                                  //       ),
                                                  //       enabledBorder: OutlineInputBorder(
                                                  //         borderRadius: BorderRadius.circular(0),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: _error_5 == true
                                                  //                 ? const Color(0xFFD32F2F)
                                                  //                 : Colors.grey.shade400),
                                                  //       ),
                                                  //       errorBorder: InputBorder.none,
                                                  //       disabledBorder: InputBorder.none,
                                                  //       filled: true,
                                                  //       fillColor: Colors.white,
                                                  //       hintStyle: TextStyle(fontSize: 12.sp),
                                                  //       contentPadding: EdgeInsets.symmetric(
                                                  //           horizontal: 20.w,
                                                  //           vertical: 0.h),
                                                  //     ),
                                                  //     onChanged: (value) {
                                                  //       if (widget.getData.usageQuantity!.isNotEmpty || widget.getData.usageQuantity != null) {
                                                  //         for (int i = 0; i < widget.getData.usageQuantity!.length; i++) {
                                                  //           if (_controller_2.text.isNotEmpty && _controller_3.text.isEmpty) {
                                                  //             _lsGetTextEditingControllerListView[
                                                  //             i].text = (0 + (int.parse(value)) * (_lsGetBmsch[i])).toString();
                                                  //           } else if (_controller_2.text.isNotEmpty && _controller_3.text.isNotEmpty) {
                                                  //             _lsGetTextEditingControllerListView[i].text = (0 + (int.parse(value)) * (_lsGetBmsch[i]) + (0 + (int.parse(_controller_3.text)) * (_lsGetBmsch[i]))).toString();
                                                  //           } else if (_controller_2.text.isEmpty && _controller_3.text.isEmpty) {
                                                  //             _lsGetTextEditingControllerListView[i].text = "0";
                                                  //           } else {
                                                  //             _lsGetTextEditingControllerListView[i].text = _controller_3.text;
                                                  //           }
                                                  //         }
                                                  //       }
                                                  //     },
                                                  //   ),
                                                  // ),
                                                  Visibility(
                                                      visible: _error_5,
                                                      child: Row(
                                                        children: <Widget>[
                                                          SizedBox(width: 5.w),
                                                          Flexible(
                                                            flex: 1,
                                                            child: Icon(Icons.error_outline, size: 8.sp, color: Colors.red[700]),
                                                          ),
                                                          SizedBox(width: 5.w),
                                                          Flexible(
                                                              flex: 7,
                                                              child: Text("Vui lòng nhập số lượng ghi nhận",
                                                                  style: TextStyle(fontSize: 8.sp, color: Colors.red[700])))
                                                        ],
                                                      )),
                                                  SizedBox(height: _error_5 == true ? 5.h : 0)
                                                ]),
                                                Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    child: Center(
                                                      child: Text(
                                                        "Đ",
                                                        style: TextStyle(fontSize: 10.sp),
                                                      ),
                                                    )),
                                                Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    child: Center(
                                                        // child: Text(
                                                        //   (widget.getData.productionRecordRouting!.quantityDLKD ?? 0.0).round().toString(),
                                                        //   style: TextStyle(fontSize: 10.sp),
                                                        // ),
                                                        child: Text(
                                                      pointerUnits.contains(widget.getData.productionRecord!.unit)
                                                          ? (widget.getData.productionRecordRouting!.quantityDLKD ?? 0.0).toStringAsFixed(3)
                                                          : (widget.getData.productionRecordRouting!.quantityDLKD ?? 0.0).round().toString(),
                                                      style: TextStyle(fontSize: 10.sp),
                                                    ))),
                                                Column(children: <Widget>[
                                                  Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.h),
                                                    child: Container(
                                                      padding: REdgeInsets.all(3),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            width: 0.5.w, color: _error_6 == true ? const Color(0xFFD32F2F) : Colors.grey.shade400),
                                                      ),
                                                      child: Center(
                                                        child: TextFormField(
                                                          textAlign: TextAlign.center,
                                                          // keyboardType: TextInputType.number,
                                                          // inputFormatters: <TextInputFormatter>[
                                                          //   FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                          // ],
                                                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                                                          inputFormatters: [CommaToDotTextInputFormatter()],
                                                          maxLines: null,
                                                          controller: _controller_3,
                                                          focusNode: _focus_3,
                                                          style: TextStyle(fontSize: 12.sp),
                                                          decoration: InputDecoration(
                                                            border: InputBorder.none,
                                                            focusedBorder: InputBorder.none,
                                                            enabledBorder: InputBorder.none,
                                                            errorBorder: InputBorder.none,
                                                            disabledBorder: InputBorder.none,
                                                            filled: true,
                                                            isDense: true,
                                                            fillColor: Colors.white,
                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                            contentPadding: EdgeInsets.zero,
                                                          ),
                                                          onChanged: (value) {
                                                            if (widget.getData.usageQuantity!.isNotEmpty || widget.getData.usageQuantity != null) {
                                                              for (int i = 0; i < widget.getData.usageQuantity!.length; i++) {
                                                                if (_controller_2.text.isEmpty && _controller_3.text.isNotEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text =
                                                                      (0 + (int.parse(value)) * (_lsGetBmsch[i])).toString();
                                                                } else if (_controller_2.text.isNotEmpty && _controller_3.text.isNotEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text = (0 +
                                                                          (int.parse(_controller_2.text)) * (_lsGetBmsch[i]) +
                                                                          (0 + (int.parse(value)) * (_lsGetBmsch[i])))
                                                                      .toString();
                                                                } else if (_controller_2.text.isEmpty && _controller_3.text.isEmpty) {
                                                                  _lsGetTextEditingControllerListView[i].text = "0";
                                                                } else {
                                                                  _lsGetTextEditingControllerListView[i].text = _controller_2.text;
                                                                }
                                                              }
                                                            }
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  // Container(
                                                  //   height: 30.h,
                                                  //   padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                  //   child: TextFormField(
                                                  //     textAlign: TextAlign.center,
                                                  //     keyboardType: TextInputType.number,
                                                  //     inputFormatters: <TextInputFormatter>[
                                                  //       FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                  //     ],
                                                  //     controller: _controller_3,
                                                  //     focusNode: _focus_3,
                                                  //     style: TextStyle(fontSize: 10.sp),
                                                  //     decoration: InputDecoration(
                                                  //       border: InputBorder.none,
                                                  //       focusedBorder: OutlineInputBorder(
                                                  //         borderRadius: BorderRadius.circular(0),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: _error_6 == true
                                                  //                 ? const Color(
                                                  //                 0xFFD32F2F)
                                                  //                 : Colors.grey.shade400),
                                                  //       ),
                                                  //       enabledBorder: OutlineInputBorder(
                                                  //         borderRadius: BorderRadius.circular(0),
                                                  //         borderSide: BorderSide(
                                                  //             width: 0.5.w,
                                                  //             color: _error_6 == true
                                                  //                 ? const Color(
                                                  //                 0xFFD32F2F)
                                                  //                 : Colors.grey.shade400),
                                                  //       ),
                                                  //       errorBorder: InputBorder.none,
                                                  //       disabledBorder: InputBorder.none,
                                                  //       filled: true,
                                                  //       fillColor: Colors.white,
                                                  //       hintStyle: TextStyle(fontSize: 12.sp),
                                                  //       contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 0.h),
                                                  //     ),
                                                  //     onChanged: (value) {
                                                  //       if (widget.getData.usageQuantity!.isNotEmpty || widget.getData.usageQuantity != null) {
                                                  //         for (int i = 0;
                                                  //         i < widget.getData.usageQuantity!.length; i++) {
                                                  //           if (_controller_2.text.isEmpty && _controller_3.text.isNotEmpty) {
                                                  //             _lsGetTextEditingControllerListView[i].text = (0 +
                                                  //                 (int.parse(value)) * (_lsGetBmsch[i])).toString();
                                                  //           } else if (_controller_2.text.isNotEmpty &&
                                                  //               _controller_3.text.isNotEmpty) {
                                                  //             _lsGetTextEditingControllerListView[i].text = (0 + (int.parse(_controller_2
                                                  //                     .text)) * (_lsGetBmsch[i]) +
                                                  //                 (0 + (int.parse(value)) * (_lsGetBmsch[i]))).toString();
                                                  //           } else if (_controller_2.text.isEmpty && _controller_3.text.isEmpty) {
                                                  //             _lsGetTextEditingControllerListView[i].text = "0";
                                                  //           } else {
                                                  //             _lsGetTextEditingControllerListView[i].text = _controller_2.text;
                                                  //           }
                                                  //         }
                                                  //       }
                                                  //     },
                                                  //   ),
                                                  // ),
                                                  Visibility(
                                                      visible: _error_6,
                                                      child: Row(
                                                        children: <Widget>[
                                                          SizedBox(width: 5.w),
                                                          Flexible(
                                                            flex: 1,
                                                            child: Icon(Icons.error_outline, size: 8.sp, color: Colors.red[700]),
                                                          ),
                                                          SizedBox(width: 5.w),
                                                          Flexible(
                                                              flex: 7,
                                                              child: Text("Vui lòng nhập số lượng ghi nhận",
                                                                  style: TextStyle(fontSize: 8.sp, color: Colors.red[700])))
                                                        ],
                                                      )),
                                                  SizedBox(height: _error_6 == true ? 5.h : 0)
                                                ]),
                                                Container(
                                                  padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                  child: Center(
                                                    child: Text(
                                                      "KĐ",
                                                      style: TextStyle(fontSize: 10.sp),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      Visibility(
                                        visible: widget.getData.productionRecord!.stepCode == null
                                            ? _selectedStepCode != null && _selectedStepCode!.value != " "
                                                ? true
                                                : false
                                            : true,
                                        child: Table(
                                          border: TableBorder(
                                            left: BorderSide(
                                              color: Colors.grey.shade500,
                                              width: 0.5.w,
                                            ),
                                            right: BorderSide(
                                              color: Colors.grey.shade500,
                                              width: 0.5.w,
                                            ),
                                            bottom: BorderSide(
                                              color: Colors.grey.shade500,
                                              width: 0.5.w,
                                            ),
                                            verticalInside: BorderSide(
                                              color: Colors.grey.shade500,
                                              width: 0.5.w,
                                            ),
                                          ),
                                          columnWidths: const <int, TableColumnWidth>{
                                            0: FractionColumnWidth(0.24),
                                            1: FractionColumnWidth(0.3),
                                            2: FractionColumnWidth(0.1),
                                            3: FractionColumnWidth(0.36),
                                          },
                                          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                          children: <TableRow>[
                                            TableRow(
                                              children: <Widget>[
                                                Container(
                                                  padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                                  child: Text(
                                                    "Từ",
                                                    style: TextStyle(fontSize: 10.sp),
                                                  ),
                                                ),
                                                GestureDetector(
                                                  onTap: () {
                                                    FocusScope.of(context).unfocus();
                                                    Platform.isAndroid ? _pickTimeFrom(context) : _pickTimeFromIOS(context);
                                                  },
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    child: Center(
                                                      child: Text(
                                                        _viewTimeFrom ?? "",
                                                        style: TextStyle(fontSize: 10.sp),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                  child: Text(
                                                    "Đến",
                                                    style: TextStyle(fontSize: 10.sp),
                                                  ),
                                                ),
                                                GestureDetector(
                                                  onTap: () {
                                                    FocusScope.of(context).unfocus();
                                                    Platform.isAndroid ? _pickTimeTo(context) : _pickTimeToIOS(context);
                                                  },
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    child: Center(
                                                      child: Text(
                                                        _viewTimeTo ?? "",
                                                        style: TextStyle(fontSize: 10.sp),
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: widget.getData.productionRecord!.stepCode == null
                                            ? _selectedStepCode != null && _selectedStepCode!.value != " "
                                                ? 15.h
                                                : 0
                                            : 15.h,
                                      ),
                                      Visibility(
                                        visible: widget.getData.usageQuantity!.isNotEmpty ? true : false,
                                        child: Text(
                                          "SL SP/CT/Cụm sử dụng cho ${widget.getData.productionRecord!.productAttributesName.toString()}:",
                                          style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      SizedBox(height: widget.getData.usageQuantity!.isNotEmpty ? 5.h : 0),
                                      widget.getData.usageQuantity!.isNotEmpty
                                          ? ListView.builder(
                                              shrinkWrap: true,
                                              physics: const NeverScrollableScrollPhysics(),
                                              padding: EdgeInsets.zero,
                                              itemCount: widget.getData.usageQuantity!.length,
                                              itemBuilder: (BuildContext context, int index) {
                                                return Table(
                                                  border: TableBorder.all(color: Colors.grey.shade400, width: 0.5.w),
                                                  columnWidths: const <int, TableColumnWidth>{
                                                    0: FractionColumnWidth(0.1),
                                                    1: FractionColumnWidth(0.9),
                                                  },
                                                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                  children: <TableRow>[
                                                    TableRow(
                                                      children: <Widget>[
                                                        Container(
                                                          // height: 100,
                                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 1.w),
                                                          child: Center(
                                                            child: Text(
                                                              (index + 1).toString(),
                                                              style: TextStyle(fontSize: 10.sp),
                                                            ),
                                                          ),
                                                        ),
                                                        Column(
                                                          children: <Widget>[
                                                            Container(
                                                              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                                                              width: double.infinity,
                                                              decoration: BoxDecoration(
                                                                border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                              ),
                                                              child: Text(
                                                                  _lsItemUsageQuantity[index].ktext.toString() +
                                                                      " (${_lsItemUsageQuantity[index].poT12 ?? " "})",
                                                                  style: TextStyle(fontSize: 10.sp)),
                                                            ),
                                                            IntrinsicHeight(
                                                                child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                                              children: <Widget>[
                                                                Container(
                                                                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                                                                  width: 50.w,
                                                                  decoration: BoxDecoration(
                                                                    border: Border(
                                                                      right: BorderSide(color: Colors.grey.shade400, width: 0.5.w),
                                                                    ),
                                                                  ),
                                                                  child: Text(
                                                                      "Tồn: ${(_lsItemUsageQuantity[index].quantity ?? 0.0).round().toString()}",
                                                                      style: TextStyle(fontSize: 10.sp)),
                                                                  // child: Text(
                                                                  //     "Tồn: ${pointerUnits.contains(widget.getData.productionRecord!.unit) ? (_lsItemUsageQuantity[index].quantity ?? 0.0).toStringAsFixed(3) : (_lsItemUsageQuantity[index].quantity ?? 0.0).round().toString()}",
                                                                  //     style: TextStyle(fontSize: 10.sp)),
                                                                ),
                                                                Expanded(
                                                                  child: Padding(
                                                                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 3.h),
                                                                    child: Container(
                                                                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                      decoration: BoxDecoration(
                                                                        border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                                      ),
                                                                      child: Center(
                                                                        child: TextFormField(
                                                                          maxLines: null,
                                                                          textAlign: TextAlign.center,
                                                                          keyboardType: TextInputType.number,
                                                                          inputFormatters: <TextInputFormatter>[
                                                                            FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                                          ],
                                                                          controller: _lsGetTextEditingControllerListView[index],
                                                                          focusNode: _lsFocusNode[index],
                                                                          style: TextStyle(fontSize: 12.sp),
                                                                          decoration: InputDecoration(
                                                                            border: InputBorder.none,
                                                                            isDense: true,
                                                                            contentPadding: EdgeInsets.zero,
                                                                            errorBorder: InputBorder.none,
                                                                            disabledBorder: InputBorder.none,
                                                                            filled: true,
                                                                            fillColor: Colors.white,
                                                                            hintStyle: TextStyle(fontSize: 12.sp),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  // Container(
                                                                  //   height: 30.h,
                                                                  //   padding: EdgeInsets.symmetric(
                                                                  //       vertical: 5.h,
                                                                  //       horizontal: 5.w),
                                                                  //   child: TextFormField(
                                                                  //     textAlign: TextAlign.center,
                                                                  //     keyboardType: TextInputType.number,
                                                                  //     inputFormatters: <TextInputFormatter>[
                                                                  //       FilteringTextInputFormatter.allow(RegExp("[0-9|.]")),
                                                                  //     ],
                                                                  //     controller: _lsGetTextEditingControllerListView[index],
                                                                  //     focusNode: _lsFocusNode[index],
                                                                  //     style: TextStyle(fontSize: 10.sp),
                                                                  //     decoration: InputDecoration(
                                                                  //       border: InputBorder.none,
                                                                  //       focusedBorder: OutlineInputBorder(
                                                                  //         borderRadius: BorderRadius.circular(0),
                                                                  //         borderSide: BorderSide(
                                                                  //             width: 0.5.w,
                                                                  //             color: Colors.grey.shade400),
                                                                  //       ),
                                                                  //       enabledBorder: OutlineInputBorder(
                                                                  //         borderRadius: BorderRadius.circular(0),
                                                                  //         borderSide: BorderSide(
                                                                  //             width: 0.5.w,
                                                                  //             color: Colors.grey.shade400),
                                                                  //       ),
                                                                  //       errorBorder: InputBorder.none,
                                                                  //       disabledBorder: InputBorder.none,
                                                                  //       filled: true,
                                                                  //       fillColor: Colors.white,
                                                                  //       hintStyle: TextStyle(
                                                                  //           fontSize: 12.sp),
                                                                  //       contentPadding:
                                                                  //       EdgeInsets.symmetric(
                                                                  //           horizontal: 20.w,
                                                                  //           vertical: 0.h),
                                                                  //     ),
                                                                  //   ),
                                                                  // ),
                                                                )
                                                              ],
                                                            )),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                );
                                              })
                                          : Container(),
                                      SizedBox(height: 25.h),
                                      Align(
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          "NV Tạo: ${widget.getData.productionRecord!.createByFullName ?? " "} - ${DetailReportFunction.getCreateTime(widget.getData)}",
                                          style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                                        ),
                                      ),
                                      const Divider(),
                                      SizedBox(height: 15.h),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          ElevatedButton(
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.grey.shade200,
                                              elevation: 0,
                                              side: BorderSide(
                                                color: Colors.grey.shade200,
                                              ),
                                            ),
                                            onPressed: () {
                                              Navigator.pop(context, false);
                                            },
                                            child: Text(
                                              'Đóng',
                                              style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                            ),
                                          ),
                                          Visibility(
                                            visible: _viButtonError ?? true,
                                            child: ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: const Color(0xff0052cc),
                                                elevation: 0,
                                                side: const BorderSide(
                                                  color: Color(0xff0052cc),
                                                ),
                                              ),
                                              onPressed: () {
                                                String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                  Platform.isAndroid
                                                      ? showDialog(
                                                          context: context,
                                                          barrierDismissible: false,
                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                      : showCupertinoDialog(
                                                          context: context,
                                                          barrierDismissible: false,
                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                } else {
                                                  _setError();
                                                  if (_error == false &&
                                                      _error_2 == false &&
                                                      _error_3 == false &&
                                                      _error_4 == false &&
                                                      _error_5 == false &&
                                                      _error_6 == false) {
                                                    FocusScope.of(context).unfocus();
                                                    DetailReportFunction.postProductionRecord(
                                                        context,
                                                        widget.getData,
                                                        widget.token,
                                                        _selectedStepCode,
                                                        _visible,
                                                        _controller_1.text,
                                                        _selectedDepartment,
                                                        _dateTo,
                                                        _dateFrom,
                                                        _timeFrom,
                                                        _timeTo,
                                                        _controller_2.text,
                                                        _controller_3.text,
                                                        _lsGetTextEditingControllerListView);
                                                  }
                                                }
                                              },
                                              child: Text(
                                                'Lưu',
                                                style: TextStyle(color: Colors.white, fontSize: 12.sp),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 200.h,
                                      ),
                                      Center(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              decoration: const BoxDecoration(),
                                              child: ElevatedButton.icon(
                                                style: ButtonStyle(
                                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                  side: MaterialStateProperty.all(
                                                    const BorderSide(
                                                      color: Color(0xff0052cc),
                                                    ),
                                                  ),
                                                  backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                ),
                                                onPressed: () async {
                                                  var data = await Navigator.pushNamed(context, '/QRcodePageGetWorkShopDepartment');
                                                  if (!mounted) return;
                                                  if (data == null) return;
                                                  // print(data);
                                                  _getWorkShopDepartment(data.toString(), context);
                                                },
                                                icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                label: Text(
                                                  'Quét mã',
                                                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                ),
                                              ),
                                            ),
                                            // Show "Chọn tổ" button only when stepCode is available
                                            if (widget.getData.productionRecord!.stepCode != null) SizedBox(width: 10.w),
                                            if (widget.getData.productionRecord!.stepCode != null)
                                              Container(
                                                decoration: const BoxDecoration(),
                                                child: ElevatedButton.icon(
                                                  style: ButtonStyle(
                                                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                    side: MaterialStateProperty.all(
                                                      const BorderSide(
                                                        color: Color(0xff0052cc),
                                                      ),
                                                    ),
                                                    backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                  ),
                                                  onPressed: () => _showSuggestedDepartments(context),
                                                  icon: Icon(Icons.list_alt, size: 25.sp, color: Colors.white),
                                                  label: Text(
                                                    'Chọn tổ',
                                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      kDebugMode
                                          ? RenderDebugButtons(dummyPhanXuong, (item) {
                                              _getWorkShopDepartment(item['barcode'], context);
                                            }, 'title')
                                          : Container(),
                                    ],
                                  ),
                                ),
                              )
                        : LostConnect(checkConnect: () => _setDefaultController()))));
  }
}
