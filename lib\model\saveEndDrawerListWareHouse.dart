import 'package:ttf/model/postFilterWareHouseTranfer.dart';

class SaveEndDrawerListWareHouse{
  PostFilterWareHouseTranfer? postFilterWareHouseTranfer;
  String? value;
  SaveEndDrawerListWareHouse(
      {this.postFilterWareHouseTranfer,
        this.value});
  SaveEndDrawerListWareHouse.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    postFilterWareHouseTranfer = json['postFilterWareHouseTranfer'] != null
        ? PostFilterWareHouseTranfer.fromJson(json['postFilterWareHouseTranfer'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['value'] = value;
    data['postFilterWareHouseTranfer'] = postFilterWareHouseTranfer;
    return data;
  }

}