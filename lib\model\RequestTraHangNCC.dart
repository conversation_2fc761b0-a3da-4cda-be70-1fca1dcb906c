import 'DataTraHangNCC.dart';

class RequestTraHangNCC {
  String poNumber;
  String? requestReturnVendorId;
  List<DataTraHangNCC> dataList;

  RequestTraHangNCC({required this.poNumber, required this.dataList, this.requestReturnVendorId});
}

class RequestReturnVendorModelList {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataRequestReturnVendorModel>? data;
  DataRequestReturnVendorModel? additionalData;

  RequestReturnVendorModelList({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  RequestReturnVendorModelList.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataRequestReturnVendorModel>[];
      json['data'].forEach((v) {
        data!.add(DataRequestReturnVendorModel.fromJson(v));
      });
    }
    additionalData = json['additionalData'];
  }
}

class RequestReturnVendorModelItem {
  int? code;
  bool? isSuccess;
  String? message;
  DataRequestReturnVendorModel? data;

  RequestReturnVendorModelItem({this.code, this.isSuccess, this.message, this.data});

  RequestReturnVendorModelItem.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = DataRequestReturnVendorModel.fromJson(json['data']);
    }
  }
}

class DataRequestReturnVendorModel {
  String id;
  String requestReturnVendorCode;
  String vendorCode;
  String vendorName;
  DateTime createTime;
  String creatorId;
  String creatorName;
  String? warehouseStaffId;
  String? warehouseStaffName;
  DateTime? warehouseVerifyTime;
  String? accountantId;
  String? accountantName;
  DateTime? accountantVerifyTime;
  int returnType;
  String? listPo;
  List<DataTraHangNCC>? dataList;

  DataRequestReturnVendorModel(
      {required this.id,
      this.requestReturnVendorCode = '',
      this.vendorCode = '',
      this.vendorName = '',
      required this.createTime,
      required this.creatorId,
      this.creatorName = '',
      this.warehouseStaffId = '',
      this.warehouseStaffName = '',
      this.warehouseVerifyTime,
      this.accountantId = '',
      this.accountantName = '',
      this.accountantVerifyTime,
      required this.returnType,
      this.listPo = '',
      this.dataList});

  factory DataRequestReturnVendorModel.fromJson(Map<String, dynamic> json) => DataRequestReturnVendorModel(
        id: json["id"],
        requestReturnVendorCode: json["requestReturnVendorCode"],
        vendorCode: json["vendorCode"] ?? "",
        vendorName: json["vendorName"] ?? '"',
        createTime: DateTime.parse(json["createTime"]),
        creatorId: json["creatorId"],
        creatorName: json["creatorName"],
        warehouseStaffId: json["warehouseStaffId"],
        warehouseStaffName: json["warehouseStaffName"],
        warehouseVerifyTime: json["warehouseVerifyTime"] != null ? DateTime.parse(json["warehouseVerifyTime"]) : null,
        accountantId: json["accountantId"],
        accountantName: json["accountantName"],
        accountantVerifyTime: json["accountantVerifyTime"] != null ? DateTime.parse(json["accountantVerifyTime"]) : null,
        returnType: json["returnType"],
        listPo: json["listPo"],
        dataList: json["dataList"] == null ? [] : (json["dataList"] as List<dynamic>).map((item) => DataTraHangNCC.fromJson(item)).toList(),
      );
}
