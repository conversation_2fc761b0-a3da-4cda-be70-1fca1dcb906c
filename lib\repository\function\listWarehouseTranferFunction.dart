import 'dart:convert';
import 'package:ttf/model/postProduction.dart';

import '../../model/CodeNameProduct.dart';
import '../../model/PurchaseOrderDetail.dart';
import '../../model/getStatusReservation.dart';
import '../../model/getStatusWarehouseTranfer.dart';
import '../../model/listWarehouseTranfer.dart';
import '../../model/postFilterWareHouseTranfer.dart';
import '../api/getCodeNameProductFTTran.dart';
import '../api/listWarehouseTranferAPI.dart';

class ListWarehouseTranferFunction {
  static Future<List<DataListWarehouseTranfer>?> fetchListWareHouseTranfer(
      String token, PostFilterWareHouseTranfer postFilterWareHouseTranfer, int start, int draw, int length) async {
    final response =
        await ListWareHouseTranferApi.getListWareHouseTranfer(token, postFilterWareHouseTranfer, Paging(start: start, length: length, draw: draw));

    if (response.statusCode == 200) {
      final responseDataWareHouse = jsonDecode(response.body);
      final getListWarehouseByFilter = ListWarehouseTranfer.fromJson(responseDataWareHouse);
      if (getListWarehouseByFilter.code == 200 && getListWarehouseByFilter.isSuccess == true) {
        return getListWarehouseByFilter.data;
      } else {
        throw (getListWarehouseByFilter.message.toString());
      }
    } else {
      throw (response.reasonPhrase.toString());
    }
  }

  static Future<List<DataGetStatusReservation>?> fechStatusReservation(String token) async {
    final response = await ListWareHouseTranferApi.getStatusReservation(token);
    if (response.statusCode == 200) {
      final responseStatusReservation = jsonDecode(response.body);

      final getStatusReservation = GetStatusReservation.fromJson(responseStatusReservation);
      if (getStatusReservation.code == 200 && getStatusReservation.isSuccess == true) {
        return getStatusReservation.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataGetStatusWarehouseTranfer>?> fechStatusWarehouseTranfer(String token) async {
    final response = await ListWareHouseTranferApi.getStatusWarehouseTranfer(token);
    if (response.statusCode == 200) {
      final responseStatusReservation = jsonDecode(response.body);

      final getStatusWarehouseTranfer = GetStatusWarehouseTranfer.fromJson(responseStatusReservation);
      if (getStatusWarehouseTranfer.code == 200 && getStatusWarehouseTranfer.isSuccess == true) {
        return getStatusWarehouseTranfer.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataCodenameProduct>?> fechCodeNameWarehouseTranfer(String valueSearch, String token) async {
    final response = await GetCodeNameProductFTTran.getCodeName(valueSearch, token);
    if (response.statusCode == 200) {
      final responseCodeName = jsonDecode(response.body);

      final getCodeName = CodeNameProduct.fromJson(responseCodeName);
      if (getCodeName.code == 200 && getCodeName.isSuccess == true) {
        return getCodeName.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataCodenameProduct>?> fechCodeNameDvt(String valueSearch, String token) async {
    final response = await GetCodeNameProductFTTran.getCodeNameDvt(valueSearch, token);
    if (response.statusCode == 200) {
      final responseCodeName = jsonDecode(response.body);

      final getCodeName = CodeNameProduct.fromJson(responseCodeName);
      if (getCodeName.code == 200 && getCodeName.isSuccess == true) {
        return getCodeName.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataCodenameProduct>?> fetchPO(String valueSearch, String token) async {
    final response = await GetCodeNameProductFTTran.getPO(valueSearch, token);
    if (response.statusCode == 200) {
      final responseCodeName = jsonDecode(response.body);

      final getCodeName = CodeNameProduct.fromJson(responseCodeName);
      if (getCodeName.code == 200 && getCodeName.isSuccess == true) {
        return getCodeName.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataPurchaseOrderDetail>?> fetchPODetails(String valueSearch, String token) async {
    final response = await GetCodeNameProductFTTran.getPODetails(valueSearch, token);
    if (response.statusCode == 200) {
      final responsePODetails = jsonDecode(response.body);

      final getCodeName = PurchaseOrderDetail.fromJson(responsePODetails);
      if (getCodeName.code == 200 && getCodeName.isSuccess == true) {
        return getCodeName.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}
