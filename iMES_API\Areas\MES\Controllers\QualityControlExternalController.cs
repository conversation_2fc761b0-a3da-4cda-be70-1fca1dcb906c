using AutoMapper;
using iMES_API.ServiceExtensions;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.Permissions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/External/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    [ApiKeyAuthorization("QualityControl.Read")]
    public class QualityControlExternalController : ControllerBaseAPI
    {
        private readonly IUploadFilesLibrary _uploadFilesLibrary;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;

        public QualityControlExternalController(IUploadFilesLibrary uploadFilesLibrary, IConfiguration configuration, IMapper mapper)
        {
            _uploadFilesLibrary = uploadFilesLibrary;
            _configuration = configuration;
            _mapper = mapper;
        }

        #region Third-Party QC APIs

        /// <summary>Third-Party API - Get Quality Control Information by QC ID</summary>
        /// <param name="qualityControlId">Quality Control ID</param>
        /// <returns>Quality Control details</returns>
        /// <remarks>
        /// Sample Request:
        /// 
        /// GET /api/v1/External/QualityControlExternal/QualityControl/{qualityControlId}
        /// Headers:
        ///     X-API-Key: your-api-key-here
        ///     
        /// Response:
        /// {
        ///   "isSuccess": true,
        ///   "code": 200,
        ///   "message": "Success",
        ///   "data": {
        ///     "qualityControlId": "...",
        ///     "qualityControlCode": "...",
        ///     "productCode": "...",
        ///     "productName": "...",
        ///     "status": true,
        ///     "result": "Pass"
        ///   }
        /// }
        /// </remarks>
        [HttpGet("QualityControl/{qualityControlId}")]
        public IActionResult GetQualityControl(Guid qualityControlId)
        {
            try
            {
                // Get API key context for company/store filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];
                
                // Validate that the API key has access to this company
                var qcData = _context.QualityControlModel
                    .Where(qc => qc.QualityControlId == qualityControlId)
                    .Select(qc => new
                    {
                        qc.QualityControlId,
                        qc.QualityControlCode,
                        qc.SaleOrgCode,
                        qc.WorkShopCode,
                        qc.WorkCenterCode,
                        //qc.ProfileCode,
                        qc.ProductCode,
                        qc.ProductName,
                        qc.LSXDT,
                        qc.LSXSAP,
                        //qc.DSX,
                        qc.ConfirmDate,
                        qc.QualityDate,
                        qc.QualityChecker,
                        qc.Status,
                        qc.Result,
                        qc.QualityType,
                        //qc.Qty,
                        //qc.Unit,
                        qc.InspectionLotQuantity,
                        qc.Environmental
                    })
                    .FirstOrDefault();

                if (qcData == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Message = "Quality Control record not found"
                    });
                }

                // Check if API key has access to this company/store
                if (!string.IsNullOrEmpty(apiKeyContext.SaleOrgCode) && 
                    qcData.SaleOrgCode != apiKeyContext.SaleOrgCode)
                {
                    return Forbid("Access denied: Quality Control record belongs to different organization");
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = qcData
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get Quality Control List with Filtering</summary>
        /// <param name="saleOrgCode">Sale Organization Code (optional)</param>
        /// <param name="workShopCode">Workshop Code (optional)</param>
        /// <param name="productCode">Product Code (optional)</param>
        /// <param name="status">Status filter (optional)</param>
        /// <param name="fromDate">From Date (optional)</param>
        /// <param name="toDate">To Date (optional)</param>
        /// <param name="pageIndex">Page index (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20, max: 100)</param>
        /// <returns>Paginated list of Quality Control records</returns>
        [HttpGet("QualityControl")]
        public IActionResult GetQualityControlList(
            string saleOrgCode = null,
            string workShopCode = null,
            string productCode = null,
            bool? status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int pageIndex = 1,
            int pageSize = 20)
        {
            try
            {
                // Validate page size
                if (pageSize > 100) pageSize = 100;
                if (pageSize < 1) pageSize = 20;
                if (pageIndex < 1) pageIndex = 1;

                // Get API key context for company/store filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                var query = _context.QualityControlModel.AsQueryable();

                // Apply API key company/store restrictions
                if (!string.IsNullOrEmpty(apiKeyContext.SaleOrgCode))
                {
                    query = query.Where(qc => qc.SaleOrgCode == apiKeyContext.SaleOrgCode);
                }

                // Apply additional filters
                if (!string.IsNullOrEmpty(saleOrgCode))
                {
                    query = query.Where(qc => qc.SaleOrgCode == saleOrgCode);
                }

                if (!string.IsNullOrEmpty(workShopCode))
                {
                    query = query.Where(qc => qc.WorkShopCode == workShopCode);
                }

                if (!string.IsNullOrEmpty(productCode))
                {
                    query = query.Where(qc => qc.ProductCode == productCode);
                }

                if (status.HasValue)
                {
                    query = query.Where(qc => qc.Status == status.Value);
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(qc => qc.ConfirmDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(qc => qc.ConfirmDate <= toDate.Value);
                }

                var totalCount = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var data = query
                    .OrderByDescending(qc => qc.ConfirmDate)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .Select(qc => new
                    {
                        qc.QualityControlId,
                        qc.QualityControlCode,
                        qc.SaleOrgCode,
                        qc.WorkShopCode,
                        qc.ProductCode,
                        qc.ProductName,
                        qc.ConfirmDate,
                        qc.QualityDate,
                        qc.Status,
                        qc.Result,
                        qc.QualityType,
                        //qc.Qty
                    })
                    .ToList();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = new
                    {
                        Items = data,
                        Pagination = new
                        {
                            PageIndex = pageIndex,
                            PageSize = pageSize,
                            TotalCount = totalCount,
                            TotalPages = totalPages,
                            HasPreviousPage = pageIndex > 1,
                            HasNextPage = pageIndex < totalPages
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get Quality Control by Product Barcode</summary>
        /// <param name="barcode">Product barcode/serial number</param>
        /// <returns>Quality Control information for the product</returns>
        [HttpGet("QualityControl/ByBarcode/{barcode}")]
        public IActionResult GetQualityControlByBarcode(string barcode)
        {
            try
            {
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                // Find QC records by barcode (this might need adjustment based on your barcode field)
                var qcData = _context.QualityControlModel
                    .Where(qc =>
                            //qc.DSX == barcode || 
                            qc.LSXDT == barcode || qc.LSXSAP == barcode)
                    .Where(qc => string.IsNullOrEmpty(apiKeyContext.SaleOrgCode) || qc.SaleOrgCode == apiKeyContext.SaleOrgCode)
                    .Select(qc => new
                    {
                        qc.QualityControlId,
                        qc.QualityControlCode,
                        qc.ProductCode,
                        qc.ProductName,
                        //qc.DSX,
                        qc.LSXDT,
                        qc.LSXSAP,
                        qc.Status,
                        qc.Result,
                        qc.QualityType,
                        qc.QualityDate,
                        qc.QualityChecker
                    })
                    .FirstOrDefault();

                if (qcData == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Message = $"No Quality Control record found for barcode: {barcode}"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = qcData
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        #endregion
    }
}