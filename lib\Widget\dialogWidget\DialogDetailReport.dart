import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/productionRecordHistoryApi.dart';

class DialogDetailReport extends StatelessWidget {
  final List<ProductionRecordHistoryApi>? getProductionRecordHistoryApi;
  final String? productionRecordUnit;

  const DialogDetailReport({Key? key, required this.getProductionRecordHistoryApi, this.productionRecordUnit}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isPointer = pointerUnits.contains(productionRecordUnit);

    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: SingleChildScrollView(
        child: ListBody(
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
              decoration: const BoxDecoration(color: Color(0xff0052cc)),
              child: Text(
                "LỊCH SỬ GHI NHẬN: NỐI VAI TỰA DƯỚI",
                style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 15.h),
            IntrinsicHeight(
              child: Row(
                children: <Widget>[
                  Expanded(
                    flex: 2,
                    child: Container(
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Text(
                        "No.",
                        style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: Container(
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Text(
                        "Chi tiết",
                        style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: Container(
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                      child: Text(
                        "Người tạo",
                        style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: double.minPositive,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: getProductionRecordHistoryApi!.length,
                itemBuilder: (BuildContext context, int index) {
                  final item = getProductionRecordHistoryApi![index];
                  final stringCreateTime = DateFormat("yyyy-MM-ddThh:mm:ss").parse(item.createTime.toString());
                  String createTimeToString = DateFormat("MM/dd/yyyy hh:mm:ss a").format(stringCreateTime);
                  return IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        Expanded(
                          flex: 2,
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                            ),
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                            child: Center(
                              child: Text(
                                (index + 1).toString(),
                                style: TextStyle(fontSize: 10.sp, fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 8,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              IntrinsicHeight(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: <Widget>[
                                    Expanded(
                                      flex: 4,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                                        ),
                                        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                        child: Text(
                                          item.ktext.toString(),
                                          style: TextStyle(fontSize: 10.sp),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 4,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                                        ),
                                        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                        child: Text(
                                          item.createByName ?? " ",
                                          style: TextStyle(fontSize: 10.sp),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              IntrinsicHeight(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: <Widget>[
                                    Expanded(
                                      flex: 4,
                                      child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.grey.shade100,
                                            border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                                          ),
                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                          // child: Text(
                                          //   item.stockRecevingType != "D"
                                          //       ? "${(item.quantity ?? 0.0).round().toString()} KD"
                                          //       : "${(item.quantity ?? 0.0).round().toString()} D",
                                          //   style: TextStyle(fontSize: 10.sp),
                                          // ),

                                          child: Text(
                                            item.stockRecevingType != "D"
                                                ? "${isPointer ? (item.quantity ?? 0.0).toStringAsFixed(3) : (item.quantity ?? 0.0).round().toString()} KD"
                                                : "${isPointer ? (item.quantity ?? 0.0).toStringAsFixed(3) : (item.quantity ?? 0.0).round().toString()} D",
                                            style: TextStyle(fontSize: 10.sp),
                                          )),
                                    ),
                                    Expanded(
                                      flex: 4,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade100,
                                          border: Border.all(color: Colors.grey.shade200, width: 0.5.w),
                                        ),
                                        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                        child: Text(
                                          createTimeToString,
                                          style: TextStyle(fontSize: 10.sp),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
