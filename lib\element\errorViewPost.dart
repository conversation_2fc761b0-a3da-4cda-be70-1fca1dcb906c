import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ErrorViewPost extends StatelessWidget {
  final String error;
  const ErrorViewPost({Key? key, required this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: SingleChildScrollView(
            child: Column(
      children: [
        Text('Có lỗi xảy ra! vui lòng thử lại sau', style: TextStyle(fontSize: 15.sp, color: Colors.black)),
        SizedBox(height: 10.h),
        Text(error, style: TextStyle(fontSize: 15.sp, color: Colors.black))
      ],
    )));

//       Center(
//     child:Text(error,style: TextStyle(fontSize: 15.sp,color: Colors.black)
// )
//       );
  }
}
