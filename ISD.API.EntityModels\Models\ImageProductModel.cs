﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ImageProductModel", Schema = "tSale")]
    public partial class ImageProductModel
    {
        [Key]
        public Guid ImageId { get; set; }
        public Guid ProductId { get; set; }
        public Guid ColorProductId { get; set; }
        [StringLength(1000)]
        public string ImageUrl { get; set; }
        public bool? isDefault { get; set; }

        [ForeignKey("ColorProductId")]
        [InverseProperty("ImageProductModel")]
        public virtual ColorProductModel ColorProduct { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("ImageProductModel")]
        public virtual ProductModel Product { get; set; }
    }
}