class FilterQCModel {
  int? code;
  bool? isSuccess;
  String? message;
  Data? data;
  AdditionalData? additionalData;

  FilterQCModel({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  FilterQCModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    additionalData = json['additionalData'] != null ? AdditionalData.fromJson(json['additionalData']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (additionalData != null) {
      data['additionalData'] = additionalData!.toJson();
    }
    return data;
  }
}

class Data {
  List<SalesOrgCodes>? salesOrgCodes;
  List<WorkCenters>? workCenters;
  List<WorkShops>? workShops;
  List<CommonDates>? commonDates;
  List<ResultsDataQC>? results;

  Data({this.salesOrgCodes, this.workCenters, this.workShops, this.commonDates, this.results});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['salesOrgCodes'] != null) {
      salesOrgCodes = <SalesOrgCodes>[];
      json['salesOrgCodes'].forEach((v) {
        salesOrgCodes!.add(SalesOrgCodes.fromJson(v));
      });
    }
    if (json['workCenters'] != null) {
      workCenters = <WorkCenters>[];
      json['workCenters'].forEach((v) {
        workCenters!.add(WorkCenters.fromJson(v));
      });
    }
    if (json['workShops'] != null) {
      workShops = <WorkShops>[];
      json['workShops'].forEach((v) {
        workShops!.add(WorkShops.fromJson(v));
      });
    }
    if (json['commonDates'] != null) {
      commonDates = <CommonDates>[];
      json['commonDates'].forEach((v) {
        commonDates!.add(CommonDates.fromJson(v));
      });
    }
    if (json['results'] != null) {
      results = <ResultsDataQC>[];
      json['results'].forEach((v) {
        results!.add(ResultsDataQC.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (salesOrgCodes != null) {
      data['salesOrgCodes'] = salesOrgCodes!.map((v) => v.toJson()).toList();
    }
    if (workCenters != null) {
      data['workCenters'] = workCenters!.map((v) => v.toJson()).toList();
    }
    if (workShops != null) {
      data['workShops'] = workShops!.map((v) => v.toJson()).toList();
    }
    if (commonDates != null) {
      data['commonDates'] = commonDates!.map((v) => v.toJson()).toList();
    }
    if (results != null) {
      data['results'] = results!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SalesOrgCodes {
  String? saleOrgCode;
  String? storeName;

  SalesOrgCodes({this.saleOrgCode, this.storeName});

  SalesOrgCodes.fromJson(Map<String, dynamic> json) {
    saleOrgCode = json['saleOrgCode'];
    storeName = json['storeName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['saleOrgCode'] = saleOrgCode;
    data['storeName'] = storeName;
    return data;
  }
}

class WorkCenters {
  String? workCenterCode;
  String? workCenterName;

  WorkCenters({this.workCenterCode, this.workCenterName});

  WorkCenters.fromJson(Map<String, dynamic> json) {
    workCenterCode = json['workCenterCode'];
    workCenterName = json['workCenterName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workCenterCode'] = workCenterCode;
    data['workCenterName'] = workCenterName;
    return data;
  }
}

class WorkShops {
  String? workShopCode;
  String? workShopName;

  WorkShops({this.workShopCode, this.workShopName});

  WorkShops.fromJson(Map<String, dynamic> json) {
    workShopCode = json['workShopCode'];
    workShopName = json['workShopName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workShopCode'] = workShopCode;
    data['workShopName'] = workShopName;
    return data;
  }
}

class CommonDates {
  String? catalogCode;
  String? catalogTextVi;

  CommonDates({this.catalogCode, this.catalogTextVi});

  CommonDates.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class ResultsDataQC {
  String? catalogCode;
  String? catalogTextVi;

  ResultsDataQC({this.catalogCode, this.catalogTextVi});

  ResultsDataQC.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class AdditionalData {
  String? selectedConfirmCommonDate;
  String? selectedQCCommonDate;
  String? selectedSalesOrgCode;

  AdditionalData({this.selectedConfirmCommonDate, this.selectedQCCommonDate, this.selectedSalesOrgCode});

  AdditionalData.fromJson(Map<String, dynamic> json) {
    selectedConfirmCommonDate = json['selectedConfirmCommonDate'];
    selectedQCCommonDate = json['selectedQCCommonDate'];
    selectedSalesOrgCode = json['selectedSalesOrgCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['selectedConfirmCommonDate'] = selectedConfirmCommonDate;
    data['selectedQCCommonDate'] = selectedQCCommonDate;
    data['selectedSalesOrgCode'] = selectedSalesOrgCode;
    return data;
  }
}

class StatusData {
  String? nameStatus;
  bool? status;

  StatusData({this.nameStatus, this.status});

  StatusData.fromJson(Map<String, dynamic> json) {
    nameStatus = json['nameStatus'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nameStatus'] = nameStatus;
    data['status'] = status;
    return data;
  }
}
