import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecureStorage {
  static const _secureStorage = FlutterSecureStorage();
  // static String? accountName;
  static IOSOptions _getIOSOptions(String? accountName) => IOSOptions(
        accountName: _getAccountName(accountName),
        // synchronizable: false,
        // accessibility: KeychainAccessibility.first_unlock
      );
  static String? _getAccountName(String? accountName) => accountName;

  static AndroidOptions _getAndroidOptions() => const AndroidOptions(
        encryptedSharedPreferences: true,
      );

  static Future<void> setString(String valueString, String keyString, String? accountName) async {
    await _secureStorage.write(key: keyString, value: valueString, iOptions: _getIOSOptions(accountName), aOptions: _getAndroidOptions());
  }

  static Future<String?> getString(String keyString, String? accountName) async {
    if (keyString == "environment") {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString('environment') ?? "PRD";
      return data;
    } else {
      return await _secureStorage.read(key: keyString, iOptions: _getIOSOptions(accountName), aOptions: _getAndroidOptions());
    }
  }
  // static Future setString(String data, String key) async => await _secureStorage.write(
  //     key:key,
  //     value: data,
  //     iOptions: _getIOSOptions(accountName),
  //     aOptions: _getAndroidOptions()
  // );
  //
  // static  Future<String?> getString(String key) async => await _secureStorage.read(
  //     key:key,
  //     iOptions: _getIOSOptions(accountName),
  //     aOptions: _getAndroidOptions()
  // );

  static Future<void> removeSecure(String key, String? accountName) async {
    await _secureStorage.delete(key: key, iOptions: _getIOSOptions(accountName), aOptions: _getAndroidOptions());
  }

  static Future<void> removeAll(String? accountName) async {
    await _secureStorage.deleteAll(iOptions: _getIOSOptions(accountName), aOptions: _getAndroidOptions());
  }
}
