import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

class ApiSuggestionField extends StatefulWidget {
  // Required props
  final String label;
  final TextEditingController controller;
  final Future<List<String>> Function(String pattern) suggestionsCallback;
  final Future<void> Function(String selected) onSuggestionSelected;
  final VoidCallback? onClear;

  // Optional props
  final bool enabled;
  final int minCharsForSuggestions;
  final String hintText;
  final String noItemsFoundText;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool showClearButton;
  final bool Function()? canClear;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double? maxHeight;
  final double? minHeight;
  final EdgeInsets? contentPadding;
  final BorderRadius? borderRadius;
  final Color? borderColor;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextStyle? suggestionStyle;
  final bool isLoading;
  final Widget? loadingWidget;
  final ValueChanged<String>? onChanged;

  // Camera scan props
  final bool showCameraButton;
  final Future<void> Function()? onCameraScan;
  final bool Function()? canShowCamera;
  final Widget? cameraIcon;
  final String? cameraTooltip;

  const ApiSuggestionField({
    Key? key,
    required this.label,
    required this.controller,
    required this.suggestionsCallback,
    required this.onSuggestionSelected,
    this.onClear,
    this.enabled = true,
    this.minCharsForSuggestions = 3,
    this.hintText = '',
    this.noItemsFoundText = 'Không tìm thấy kết quả',
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.showClearButton = true,
    this.canClear,
    this.prefixIcon,
    this.suffixIcon,
    this.maxHeight = 200,
    this.minHeight = 200,
    this.contentPadding,
    this.borderRadius,
    this.borderColor,
    this.textStyle,
    this.hintStyle,
    this.suggestionStyle,
    this.isLoading = false,
    this.loadingWidget,
    this.onChanged,
    // Camera scan props
    this.showCameraButton = false,
    this.onCameraScan,
    this.canShowCamera,
    this.cameraIcon,
    this.cameraTooltip = 'Quét mã',
  }) : super(key: key);

  @override
  State<ApiSuggestionField> createState() => _ApiSuggestionFieldState();
}

class _ApiSuggestionFieldState extends State<ApiSuggestionField> {
  bool _isLoadingSuggestions = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_handleControllerChange);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleControllerChange);
    super.dispose();
  }

  void _handleControllerChange() {
    if (mounted) {
      setState(() {
        // Rebuild to show/hide clear button
      });
    }
  }

  bool _shouldShowClearButton() {
    if (!widget.showClearButton) return false;
    if (widget.canClear != null) return widget.canClear!();
    return widget.controller.text.isNotEmpty && widget.enabled;
  }

  bool _shouldShowCameraButton() {
    if (!widget.showCameraButton) return false;
    if (widget.canShowCamera != null) return widget.canShowCamera!();
    return widget.enabled;
  }

  Future<void> _handleCameraScan() async {
    if (widget.onCameraScan != null) {
      await widget.onCameraScan!();
    }
  }

  double _calculateRightPadding() {
    double padding = 3.w;
    bool hasActions = _shouldShowClearButton() || widget.isLoading || _shouldShowCameraButton();

    if (hasActions) {
      // Base padding for actions
      padding = 30.w;

      // Additional padding if both clear and camera buttons are visible
      if (_shouldShowClearButton() && _shouldShowCameraButton()) {
        padding = 55.w; // Space for both buttons
      }
    }

    return padding;
  }

  Future<List<String>> _handleSuggestionsCallback(String pattern) async {
    if (pattern.length < widget.minCharsForSuggestions) {
      return [];
    }

    setState(() {
      _isLoadingSuggestions = true;
    });

    try {
      final suggestions = await widget.suggestionsCallback(pattern);
      return suggestions;
    } catch (e) {
      debugPrint('Error fetching suggestions: $e');
      return [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingSuggestions = false;
        });
      }
    }
  }

  void _handleClear() {
    setState(() {
      widget.controller.clear();
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        if (widget.label.isNotEmpty) ...[
          Expanded(
            flex: 3,
            child: Text(
              widget.label,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 10.w),
        ],
        Expanded(
          flex: widget.label.isEmpty ? 1 : 7,
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 0.5,
                    color: widget.borderColor ?? Colors.grey.shade400,
                  ),
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(3.r),
                ),
                child: TypeAheadField<String>(
                  minCharsForSuggestions: widget.minCharsForSuggestions,
                  suggestionsBoxDecoration: SuggestionsBoxDecoration(
                    constraints: BoxConstraints(
                      minHeight: widget.minHeight ?? 200.h,
                      maxHeight: widget.maxHeight ?? 200.h,
                    ),
                  ),
                  textFieldConfiguration: TextFieldConfiguration(
                    enabled: widget.enabled,
                    decoration: InputDecoration(
                      labelStyle: TextStyle(fontSize: 11.sp),
                      contentPadding: widget.contentPadding ??
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 9.h).copyWith(
                            right: _calculateRightPadding(),
                          ),
                      isDense: true,
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      hintText: widget.hintText,
                      hintStyle: widget.hintStyle ?? TextStyle(fontSize: 11.sp, fontStyle: FontStyle.italic),
                      prefixIcon: widget.prefixIcon,
                      suffixIcon: widget.suffixIcon,
                    ),
                    style: widget.textStyle ?? TextStyle(fontSize: 12.sp),
                    controller: widget.controller,
                    keyboardType: widget.keyboardType,
                    inputFormatters: widget.inputFormatters,
                    onChanged: (value) {
                      if (widget.onChanged != null) {
                        widget.onChanged!(value);
                      }
                      setState(() {
                        widget.controller.text = value;
                        widget.controller.selection = TextSelection.collapsed(
                          offset: widget.controller.text.length,
                        );
                      });
                    },
                  ),
                  suggestionsCallback: _handleSuggestionsCallback,
                  itemBuilder: (context, suggestion) {
                    return Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                      child: Text(
                        suggestion,
                        style: widget.suggestionStyle ?? TextStyle(fontSize: 12.sp),
                      ),
                    );
                  },
                  onSuggestionSelected: (suggestion) async {
                    await widget.onSuggestionSelected(suggestion);
                  },
                  noItemsFoundBuilder: (context) {
                    if (_isLoadingSuggestions) {
                      return widget.loadingWidget ??
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 16.w,
                                  height: 16.w,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  'Đang tải...',
                                  style: TextStyle(fontSize: 11.sp, color: Colors.grey),
                                ),
                              ],
                            ),
                          );
                    }
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                      child: Text(
                        widget.noItemsFoundText,
                        style: TextStyle(fontSize: 11.sp, color: Colors.grey),
                      ),
                    );
                  },
                ),
              ),
              // Clear button or loading indicator
              if (_shouldShowClearButton() || widget.isLoading)
                Positioned(
                  top: 1,
                  right: 5,
                  child: widget.isLoading
                      ? Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                        )
                      : InkWell(
                          onTap: _handleClear,
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Icon(
                              Icons.close,
                              size: 20.sp,
                              color: Colors.red,
                            ),
                          ),
                        ),
                ),
              // Camera scan button
              if (_shouldShowCameraButton())
                Positioned(
                  top: 1,
                  right: _shouldShowClearButton() ? 30.w : 5, // Position based on clear button visibility
                  child: InkWell(
                    onTap: _handleCameraScan,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
                      child: Tooltip(
                        message: widget.cameraTooltip ?? 'Quét mã',
                        child: widget.cameraIcon ??
                            Icon(
                              Icons.camera_alt_outlined,
                              size: 20.sp,
                              color: Colors.black,
                            ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
