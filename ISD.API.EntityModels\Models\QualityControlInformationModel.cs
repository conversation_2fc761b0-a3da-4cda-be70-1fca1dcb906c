﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("QualityControlInformationModel", Schema = "tMasterData")]
    public partial class QualityControlInformationModel
    {
        public QualityControlInformationModel()
        {
            WorkCenterCode = new HashSet<WorkCenterModel>();
        }

        [Key]
        public Guid Id { get; set; }
        public int Code { get; set; }
        [StringLength(50)]
        public string SaleOrgCode { get; set; }
        [StringLength(500)]
        public string Name { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [StringLength(255)]
        public string QualityType { get; set; }
        [StringLength(255)]
        public string ItemType { get; set; }
        [StringLength(255)]
        public string RoutingType { get; set; }
        public int? NumberMin { get; set; }
        public int? NumberMax { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string MaterialType { get; set; }
        [StringLength(500)]
        public string MaterialDescription { get; set; }

        [ForeignKey("QualityControlInformationId")]
        [InverseProperty("QualityControlInformation")]
        public virtual ICollection<WorkCenterModel> WorkCenterCode { get; set; }
    }
}