import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/confirmWorkCenterApi.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/RowDetail.dart';
import '../repository/function/completeTheBigStageFunction.dart';
import '../repository/showDateTime.dart';

class CompleteTheBigStage extends StatefulWidget {
  final DataConfirmWorkCenter getData;
  final String token;
  final String confirmWorkCenter;
  final String dateTimeOld;
  const CompleteTheBigStage({
    Key? key,
    required this.getData,
    required this.token,
    required this.confirmWorkCenter,
    required this.dateTimeOld,
  }) : super(key: key);

  @override
  _CompleteTheBigStageState createState() => _CompleteTheBigStageState();
}

class _CompleteTheBigStageState extends State<CompleteTheBigStage> {
  DateTime? _date;
  late String _getDate;
  late String _getCreateTime;

  @override
  void initState() {
    super.initState();
    _setDateCreateTime();
  }

  void _setDateCreateTime() {
    _getDate = CompleteTheBigStageFunction.getDate(_date);
    _getCreateTime = CompleteTheBigStageFunction.getCreateTime(widget.getData);
  }

  Future<void> _pickDate(BuildContext context) async {
    DateTime? newDate = await showDatePicker(
      context: context,
      initialDate: _date != null ? _date! : DateTime.now(),
      firstDate: DateTime(DateTime.now().year - 50),
      lastDate: DateTime(DateTime.now().year + 5),
    );
    if (!mounted) return;
    if (newDate == null) return;
    setState(() {
      _date = newDate;
      _getDate = CompleteTheBigStageFunction.getDate(_date);
    });
  }

  Future<void> _pickDateIOS(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (!mounted) return;
      if (newDate == null) return;
      setState(() {
        _date = newDate;
        _getDate = CompleteTheBigStageFunction.getDate(_date);
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
        duration: const Duration(seconds: 1),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, false);
          return false;
        },
        child: Scaffold(
            backgroundColor: Colors.grey.shade200,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context, false);
                },
              ),
              title: Text(
                'Xác nhận hoàn tất công đoạn lớn',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  Container(
                      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
                      decoration: const BoxDecoration(color: Colors.white),
                      child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
                        LabeledDetailRow(title: 'LSX ĐT:', text: widget.getData.productionOrder.toString()),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(title: 'Đợt SX:', text: widget.getData.summary.toString()),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(title: 'LSX SAP:', text: widget.getData.productionOrderSAP.toString()),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(
                            title: 'Sản phẩm:',
                            text:
                                '(${widget.getData.qty.toString()} ${widget.getData.unit.toString()}) ${widget.getData.productCode.toString()} | ${widget.getData.productName.toString()}'),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(
                            title: 'CĐ hoàn tất:', text: '${widget.getData.fromStepCode.toString()} | ${widget.getData.fromStepName.toString()}'),
                        SizedBox(
                          height: 15.h,
                        ),
                        LabeledDetailRow(title: 'Công đoạn lớn:', text: widget.confirmWorkCenter),
                        SizedBox(
                          height: 15.h,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Thời gian xác nhận:',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                                flex: 7,
                                child: GestureDetector(
                                  onTap: () {
                                    Platform.isAndroid ? _pickDate(context) : _pickDateIOS(context);
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 5.w),
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(flex: 9, child: Text(_getDate, style: TextStyle(fontSize: 12.sp, color: Colors.black))),
                                        Expanded(flex: 1, child: Icon(Icons.edit_calendar_rounded, color: const Color(0xff0052cc), size: 15.sp)),
                                      ],
                                    ),
                                  ),
                                ))
                          ],
                        ),
                        SizedBox(height: widget.getData.listDetail!.isNotEmpty ? 25.h : 0),
                        Visibility(
                          visible: widget.getData.listDetail!.isNotEmpty ? true : false,
                          child: Text(
                            'SP/CT/Cụm hoàn thành:',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(height: widget.getData.listDetail!.isNotEmpty ? 5.h : 0),
                        Visibility(
                          visible: widget.getData.listDetail!.isNotEmpty ? true : false,
                          child: ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: widget.getData.listDetail!.length,
                              itemBuilder: (context, index) {
                                final item = widget.getData.listDetail![index];
                                return Table(
                                  border: TableBorder.all(color: Colors.grey.shade400, width: 0.5),
                                  columnWidths: const <int, TableColumnWidth>{
                                    0: FractionColumnWidth(0.2),
                                    1: FractionColumnWidth(0.4),
                                    2: FractionColumnWidth(0.4),
                                  },
                                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                  children: <TableRow>[
                                    TableRow(
                                      children: <Widget>[
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                                          child: Center(
                                            child: Text(
                                              (index + 1).toString(),
                                              style: TextStyle(fontSize: 12.sp),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                          child: Text(
                                            item.ktext.toString(),
                                            style: TextStyle(fontSize: 12.sp),
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                          child: Text(
                                            ((item.quantity ?? 0.0) < 1.0
                                                    ? (item.quantity ?? 0.0).toStringAsFixed(3)
                                                    : (item.quantity ?? 0.0).round().toString()) +
                                                ' Đ',
                                            style: TextStyle(fontSize: 12.sp),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                );
                              }),
                        ),
                        SizedBox(
                          height: 25.h,
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            "NV Tạo: ${widget.getData.createByFullName ?? ' '} - $_getCreateTime",
                            style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                          ),
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              decoration: const BoxDecoration(),
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xffeceff1)))),
                                  side: MaterialStateProperty.all(
                                    const BorderSide(
                                      color: Color(0xffeceff1),
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(const Color(0xffeceff1)),
                                ),
                                onPressed: () {
                                  Navigator.pop(context, false);
                                },
                                child: Container(
                                  margin: EdgeInsets.symmetric(vertical: 10.h),
                                  child: Text(
                                    'Đóng',
                                    style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Container(
                              decoration: const BoxDecoration(),
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xff0052cc)))),
                                  side: MaterialStateProperty.all(
                                    const BorderSide(
                                      color: Color(0xff0052cc),
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                ),
                                onPressed: () {
                                  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                  if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                    Platform.isAndroid
                                        ? showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                        : showCupertinoDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                  } else {
                                    CompleteTheBigStageFunction.postCompleteTheBigStage(
                                        context, widget.getData, widget.confirmWorkCenter, widget.token, _date);
                                  }
                                },
                                child: Container(
                                  margin: EdgeInsets.symmetric(vertical: 10.h),
                                  child: Text(
                                    'Xác nhận',
                                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12.sp),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ]))
                ],
              ),
            )));
  }
}
