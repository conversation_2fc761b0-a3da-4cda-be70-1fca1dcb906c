class GetQuantityExported {
  int? code;
  bool? isSuccess;
  String? message;
  DataQuantityExported? data;


  GetQuantityExported(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetQuantityExported.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataQuantityExported.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataQuantityExported {
  double? quantity;
  String? unit;

  DataQuantityExported({this.quantity, this.unit});

  DataQuantityExported.fromJson(Map<String, dynamic> json) {
    quantity = json['quantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}