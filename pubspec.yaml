name: ttf
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# version: 3.2.15+3215
version: 3.3.20+3320

environment:
  sdk: ">=2.19.2 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_screenutil: ^5.6.1
  qr_code_scanner: ^1.0.1
  http: ^0.13.5
  flutter_secure_storage: ^8.0.0
  shared_preferences: ^2.0.17
  intl: ^0.18.0
  # image_picker: ^0.8.6+1
  permission_handler: ^10.2.0
  path_provider: ^2.0.12
  connectivity_plus: ^5.0.2
  gallery_saver: ^2.3.2
  xml: ^6.2.2
  collection: ^1.17.0
  flutter_typeahead: ^4.8.0
  flutter_keyboard_visibility: ^5.4.0
  yaml: ^3.1.1
  upgrader: ^8.4.0
  image_picker: ^1.0.2
  photo_view: ^0.14.0
  device_info_plus: ^9.1.2
  onesignal_flutter: ^5.2.0
  multiple_search_selection: ^2.5.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  # photo_view:
  #   git:
  #     url: https://github.com/bluefireteam/photo_view
  #     ref: master
  flutter_launcher_icons: ^0.13.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  flutter_native_splash: ^2.2.7
flutter_native_splash:
  image: assets/plash/logo.png
  color: "#ffffff"
  # android_12:
  #   color: "#ffffff"
  #   image: assets/plash/logo.png
  #   icon_background_color: "#ffffff"
flutter_icons:
  image_path: "assets/logoapp/logo.png"
  android: true
  ios: true
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo/
    - assets/banner/
    - assets/errorimage/
    - pubspec.yaml
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
