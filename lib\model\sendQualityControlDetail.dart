class SendQualityControlDetail {
  String? qualityControlDetailId;
  String? testMethod;
  String? limitCritical;
  String? limitHigh;
  String? limitLow;
  String? samplingLevel;
  String? acceptableLevel;
  double? inspectionQuantity;
  double? tongSoSanPhamLoi;
  String? result;
  String? samplingLevelName;

  SendQualityControlDetail(
    this.qualityControlDetailId,
    this.testMethod,
    this.samplingLevel,
    this.acceptableLevel,
    this.inspectionQuantity,
    this.result,
    this.samplingLevelName,
  );

  SendQualityControlDetail.fromJson(Map<String, dynamic> json) {
    qualityControlDetailId = json['qualityControlDetailId'];
    testMethod = json['testMethod'];
    limitCritical = json['limitCritical'];
    limitHigh = json['limitHigh'];
    limitLow = json['limitLow'];
    samplingLevel = json['samplingLevel'];
    acceptableLevel = json['acceptableLevel'];
    inspectionQuantity = json['inspectionQuantity'];
    tongSoSanPhamLoi = json['tongSoSanPhamLoi'];
    result = json['result'];
    samplingLevelName = json['samplingLevelName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlDetailId'] = qualityControlDetailId;
    data['testMethod'] = testMethod;
    data['limitCritical'] = limitCritical;
    data['limitHigh'] = limitHigh;
    data['limitLow'] = limitLow;
    data['samplingLevel'] = samplingLevel;
    data['acceptableLevel'] = acceptableLevel;
    data['inspectionQuantity'] = inspectionQuantity;
    data['tongSoSanPhamLoi'] = tongSoSanPhamLoi;
    data['result'] = result;
    data['samplingLevelName'] = samplingLevelName;
    return data;
  }
}

class SendQualityControlDetail2 {
  String? qualityControlDetailId;
  String? testMethod;
  String? limitCritical;
  String? limitHigh;
  String? limitLow;
  String? samplingLevel;
  String? acceptableLevel;
  double? inspectionQuantity;
  double? tongSoSanPhamLoi;
  String? result;
  String? samplingLevelName;
  String? congDoanNho;
  String? mauHoanThien;
  String? loiNangChapNhan;
  String? loiNheChapNhan;
  String? checkingTimes;

  SendQualityControlDetail2(
    this.qualityControlDetailId,
    this.testMethod,
    this.limitCritical,
    this.limitHigh,
    this.limitLow,
    this.samplingLevel,
    this.acceptableLevel,
    this.inspectionQuantity,
    this.tongSoSanPhamLoi,
    this.result,
    this.samplingLevelName,
    this.congDoanNho,
    this.mauHoanThien,
    this.loiNangChapNhan,
    this.loiNheChapNhan,
    this.checkingTimes,
  );

  SendQualityControlDetail2.fromJson(Map<String, dynamic> json) {
    qualityControlDetailId = json['qualityControlDetailId'];
    testMethod = json['testMethod'];
    limitCritical = json['limitCritical'];
    limitHigh = json['limitHigh'];
    limitLow = json['limitLow'];
    samplingLevel = json['samplingLevel'];
    acceptableLevel = json['acceptableLevel'];
    inspectionQuantity = json['inspectionQuantity'];
    tongSoSanPhamLoi = json['tongSoSanPhamLoi'];
    result = json['result'];
    samplingLevelName = json['samplingLevelName'];
    congDoanNho = json['congDoanNho'];
    mauHoanThien = json['mauHoanThien'];
    loiNangChapNhan = json['loiNangChapNhan'];
    loiNheChapNhan = json['loiNheChapNhan'];
    checkingTimes = json['checkingTimes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlDetailId'] = qualityControlDetailId;
    data['testMethod'] = testMethod;
    data['limitCritical'] = limitCritical;
    data['limitHigh'] = limitHigh;
    data['limitLow'] = limitLow;
    data['samplingLevel'] = samplingLevel;
    data['acceptableLevel'] = acceptableLevel;
    data['inspectionQuantity'] = inspectionQuantity;
    data['tongSoSanPhamLoi'] = tongSoSanPhamLoi;
    data['result'] = result;
    data['samplingLevelName'] = samplingLevelName;
    data['congDoanNho'] = congDoanNho;
    data['mauHoanThien'] = mauHoanThien;
    data['loiNangChapNhan'] = loiNangChapNhan;
    data['loiNheChapNhan'] = loiNheChapNhan;
    data['checkingTimes'] = checkingTimes;
    return data;
  }
}
