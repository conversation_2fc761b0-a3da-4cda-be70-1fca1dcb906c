# Quality Control Screen Development Guide

This guide provides best practices and patterns for working with the quality control screens in TTF_MES_Mobile.

## Screen Structure

### Main Screens
- Located in `lib/page/KiemTraChatLuong/`
- Naming pattern: `BaoCao{Type}.dart` or `Phieu{Type}.dart` 
- Examples: `BaoCaoDauVao.dart`, `PhieuKCSCongDoan.dart`

### Detail Screens
- Naming pattern: `{MainScreenName}Detail.dart`
- Examples: `BaoCaoDauVaoDetail.dart`, `PhieuKCSCongDoanDetail.dart`

## Data Models

Quality control screens rely on these key models:

- `QualityControlModel`: The main model containing all quality control data
- `QualityControl`: Contains basic information about the quality control record
- `QualityControlDetail`: Contains detailed inspection information
- `CongDoanInfoVm`: Contains information about the work process

## Standard Variables

Use these naming conventions for variables:

- Controllers: `_controller{FieldName}` (e.g., `_controllerTongSoSanPhamLoi`)
- Selected values: `_selected{FieldName}` (e.g., `_selectedResultDetail`)
- Lists: `_ls{FieldName}` (e.g., `_lsThongTinKiemTra`)
- Error flags: `_error{FieldName}` (e.g., `_errorQuantityCheck`)

## Standard Methods

Implement these standard methods in detail screens:

### Update Methods
```dart
void updateTongSoSanPhamLoi() {
  if (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi != null) {
    _controllerTongSoSanPhamLoi.text = (_qualityControl!.qualityControlDetail!.tongSoSanPhamLoi!.round()).toString();
  } else {
    _controllerTongSoSanPhamLoi.text = _controllerTongSoSanPhamLoi.text;
  }
}
```

### Validation Methods
```dart
bool canSendQualityControl() {
  bool ret = true;

  if (_errorQuantityCheck) {
    showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
    ret = false;
  }
  
  // More validations...
  
  return ret;
}
```

### Setup Methods
```dart
void setupThongTinKiemtra() {
  if (_lsThongTinKiemTra.isNotEmpty) {
    for (int i = 0; i < _lsThongTinKiemTra.length; i++) {
      addThongTinKiemTraSaved(i);
    }
  } else {
    addDefaultThongTinKiemTra();
  }
}
```

## API Integration

Use the `QualityControlFunction` class for API calls:

```dart
// Fetching data
final dataDropdown = await ListQCFunction.getDefaultKCSFilter(widget.user.token.toString());

// Posting data
QualityControlFunction.postQualityControlQCMau(
  context,
  _lsFileHeader,
  _controllerInspectQuantityDetail.text,
  _qualityControl,
  _qualityChckerInfo,
  _qualityDate!.toIso8601String(),
  _selectedLoaiNghiemThu,
  _selectedResultDetail,
  // More parameters...
);
```

## Photo Documentation

Implement photo capture using:

```dart
ImagePicker _pickerImage = ImagePicker();

// Store images in appropriate lists
List<File> _lsFileHeader = []; // Main images
List<List<File>> _lsFileThongTinKiemTra = []; // Inspection point images
List<List<File>> _lsFileHinhAnhLoi = []; // Defect images

// Use QualityControlFunction for image picker
Future<bool?> isGallery = await QualityControlFunction.pickImage(context);
if (isGallery != null) {
  final XFile? image = await _pickerImage.pickImage(
    source: isGallery ? ImageSource.gallery : ImageSource.camera,
    imageQuality: 50,
  );
  // Process image...
}
```

## Form Validation

Implement comprehensive validation before submitting:

```dart
if (canSendQualityControl()) {
  // Submit data
} else {
  // Show error
}
```

## Localization

Use Vietnamese for UI messages:

```dart
showToast(context: context, message: "Vui lòng nhập SL kiểm tra");
```

## Quality Control Types

The system supports these quality control types:

1. **Input Quality Control** (BaoCaoDauVao) - For checking incoming materials
2. **Workshop Quality Control** (PhieuKCSCongDoan) - For checking quality on the production line
3. **Sample Quality Control** (BaoCaoQCMau) - For sample product testing
4. **Outsourced Quality Control** (BaoCaoQCGiaCong) - For outsourced manufacturing
5. **Field Quality Control** (BaoCaoQCHienTruong) - For field inspections
6. **Product QA Acceptance** (BaoCaoQAQCNghiemThu) - For final product quality acceptance

When creating a new quality control screen, follow the patterns established in existing screens. 