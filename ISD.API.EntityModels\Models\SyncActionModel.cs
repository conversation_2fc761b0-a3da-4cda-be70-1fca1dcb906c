﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SyncActionModel", Schema = "MES")]
    public partial class SyncActionModel
    {
        [Key]
        public int SyncActionId { get; set; }
        public Guid AccountId { get; set; }
        [Required]
        [StringLength(50)]
        public string SyncMethod { get; set; }
        [Required]
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(500)]
        public string Input { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
    }
}