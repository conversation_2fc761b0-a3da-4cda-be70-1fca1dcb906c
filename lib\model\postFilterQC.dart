class FilterQCVm {
  String? saleOrgCode;
  String? workShopCode;
  String? workCenterCode;
  String? profileCode;
  String? profileName;
  String? lsxSap;
  String? productCode;
  String? productName;
  bool? status;
  String? result;
  String? confirmCommonDate;
  String? confirmFormDate;
  String? confirmToDate;
  String? qualityCommonDate;
  String? qualityFromDate;
  String? qualityToDate;
  String? qualityType;

  FilterQCVm(
    this.saleOrgCode,
    this.workShopCode,
    this.workCenterCode,
    this.profileCode,
    this.profileName,
    this.lsxSap,
    this.productCode,
    this.productName,
    this.status,
    this.result,
    this.confirmCommonDate,
    this.confirmFormDate,
    this.confirmToDate,
    this.qualityCommonDate,
    this.qualityFromDate,
    this.qualityToDate,
    this.qualityType,
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['SaleOrgCode'] = saleOrgCode;
    data['WorkShopCode'] = workShopCode;
    data['WorkCenterCode'] = workCenterCode;
    data['ProfileCode'] = profileCode;
    data['ProfileName'] = profileName;
    data['LSXSAP'] = lsxSap;
    data['ProductCode'] = productCode;
    data['ProductName'] = productName;
    data['Status'] = status;
    data['Result'] = result;
    data['ConfirmCommonDate'] = confirmCommonDate;
    data['ConfirmFromDate'] = confirmFormDate;
    data['ConfirmToDate'] = confirmToDate;
    data['QualityCommonDate'] = qualityCommonDate;
    data['QualityFromDate'] = qualityFromDate;
    data['QualityToDate'] = qualityToDate;
    data['qualityType'] = qualityType;
    return data;
  }
}
