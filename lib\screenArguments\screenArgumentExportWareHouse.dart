import '../model/listWarehouseTranfer.dart';
import '../model/slocAddresse.dart';

class ScreenArgumentExportWareHouse {
  final DataListWarehouseTranfer dataListWarehouseTranfer;
  final String token;
  final String plant;
  final List<DataSlocAddress> lsDataSlocAddress;
  final String dateTimeOld;

  ScreenArgumentExportWareHouse(this.dataListWarehouseTranfer,  this.token, this.plant,this.lsDataSlocAddress, this.dateTimeOld);
}