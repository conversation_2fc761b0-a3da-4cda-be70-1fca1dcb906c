﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using ISD.API.EntityModels.Models;

namespace ISD.API.EntityModels.Data
{
    public partial class EntityDataContext : DbContext
    {
        public EntityDataContext()
        {
        }

        public EntityDataContext(DbContextOptions<EntityDataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<APIModel> APIModel { get; set; }
        public virtual DbSet<AccessoryCategoryModel> AccessoryCategoryModel { get; set; }
        public virtual DbSet<AccessoryDetailModel> AccessoryDetailModel { get; set; }
        public virtual DbSet<AccessoryModel> AccessoryModel { get; set; }
        public virtual DbSet<AccessoryPriceModel> AccessoryPriceModel { get; set; }
        public virtual DbSet<AccessoryProductModel> AccessoryProductModel { get; set; }
        public virtual DbSet<AccessorySaleOrderDetailModel> AccessorySaleOrderDetailModel { get; set; }
        public virtual DbSet<AccessorySaleOrderModel> AccessorySaleOrderModel { get; set; }
        public virtual DbSet<AccessorySellTypeModel> AccessorySellTypeModel { get; set; }
        public virtual DbSet<AccountModel> AccountModel { get; set; }
        public virtual DbSet<Account_Device_Mapping> Account_Device_Mapping { get; set; }
        public virtual DbSet<AddressBookModel> AddressBookModel { get; set; }
        public virtual DbSet<AggregatedCounter> AggregatedCounter { get; set; }
        public virtual DbSet<AggregatedCounter1> AggregatedCounter1 { get; set; }
        public virtual DbSet<AllDepartmentModel> AllDepartmentModel { get; set; }
        public virtual DbSet<AllocateMaterialLogModel> AllocateMaterialLogModel { get; set; }
        public virtual DbSet<ApplicationConfig> ApplicationConfig { get; set; }
        public virtual DbSet<ApplicationLog> ApplicationLog { get; set; }
        public virtual DbSet<AppointmentModel> AppointmentModel { get; set; }
        public virtual DbSet<AssignmentModel> AssignmentModel { get; set; }
        public virtual DbSet<Assignment_ProductionOrderModel> Assignment_ProductionOrderModel { get; set; }
        public virtual DbSet<Assignment_StepModel> Assignment_StepModel { get; set; }
        public virtual DbSet<AuditLogModel> AuditLogModel { get; set; }
        public virtual DbSet<AutoConditionModel> AutoConditionModel { get; set; }
        public virtual DbSet<BC01Model> BC01Model { get; set; }
        public virtual DbSet<BC03Model> BC03Model { get; set; }
        public virtual DbSet<BC07Model> BC07Model { get; set; }
        public virtual DbSet<BC15Model> BC15Model { get; set; }
        public virtual DbSet<BC16Model> BC16Model { get; set; }
        public virtual DbSet<BC18Model> BC18Model { get; set; }
        public virtual DbSet<BC19Model> BC19Model { get; set; }
        public virtual DbSet<BCUuTienModel> BCUuTienModel { get; set; }
        public virtual DbSet<BOMDetailModel> BOMDetailModel { get; set; }
        public virtual DbSet<BOMHeaderModel> BOMHeaderModel { get; set; }
        public virtual DbSet<BOM_Header_InventorModel> BOM_Header_InventorModel { get; set; }
        public virtual DbSet<BOM_Item_InventorModel> BOM_Item_InventorModel { get; set; }
        public virtual DbSet<BangTinModel> BangTinModel { get; set; }
        public virtual DbSet<BannerModel> BannerModel { get; set; }
        public virtual DbSet<BookingModel> BookingModel { get; set; }
        public virtual DbSet<CampaignModel> CampaignModel { get; set; }
        public virtual DbSet<CapacityRegisterModel> CapacityRegisterModel { get; set; }
        public virtual DbSet<CareerModel> CareerModel { get; set; }
        public virtual DbSet<CatalogModel> CatalogModel { get; set; }
        public virtual DbSet<CatalogTypeModel> CatalogTypeModel { get; set; }
        public virtual DbSet<CategoryModel> CategoryModel { get; set; }
        public virtual DbSet<ChangeDataLogModel> ChangeDataLogModel { get; set; }
        public virtual DbSet<CheckInOutModel> CheckInOutModel { get; set; }
        public virtual DbSet<CheckingTimesNotificationModel> CheckingTimesNotificationModel { get; set; }
        public virtual DbSet<ClaimAccessoryLogModel> ClaimAccessoryLogModel { get; set; }
        public virtual DbSet<ClaimAccessoryModel> ClaimAccessoryModel { get; set; }
        public virtual DbSet<ClaimAccessoryStatusModel> ClaimAccessoryStatusModel { get; set; }
        public virtual DbSet<CollectingAuthorityModel> CollectingAuthorityModel { get; set; }
        public virtual DbSet<ColorModel> ColorModel { get; set; }
        public virtual DbSet<ColorProductModel> ColorProductModel { get; set; }
        public virtual DbSet<Comment_File_Mapping> Comment_File_Mapping { get; set; }
        public virtual DbSet<CompanyModel> CompanyModel { get; set; }
        public virtual DbSet<ConfigurationModel> ConfigurationModel { get; set; }
        public virtual DbSet<ConsumableMaterialsDeliveryModel> ConsumableMaterialsDeliveryModel { get; set; }
        public virtual DbSet<ContConfigModel> ContConfigModel { get; set; }
        public virtual DbSet<ContRegisterModel> ContRegisterModel { get; set; }
        public virtual DbSet<ContRegisterSO60Model> ContRegisterSO60Model { get; set; }
        public virtual DbSet<ContactDetailModel> ContactDetailModel { get; set; }
        public virtual DbSet<ContactModel> ContactModel { get; set; }
        public virtual DbSet<ContainerRequirementModel> ContainerRequirementModel { get; set; }
        public virtual DbSet<ContentModel> ContentModel { get; set; }
        public virtual DbSet<Counter> Counter { get; set; }
        public virtual DbSet<Counter1> Counter1 { get; set; }
        public virtual DbSet<CreditLimitModel> CreditLimitModel { get; set; }
        public virtual DbSet<CustomerGiftDetailModel> CustomerGiftDetailModel { get; set; }
        public virtual DbSet<CustomerGiftModel> CustomerGiftModel { get; set; }
        public virtual DbSet<CustomerLevelModel> CustomerLevelModel { get; set; }
        public virtual DbSet<CustomerModel> CustomerModel { get; set; }
        public virtual DbSet<CustomerPromotionModel> CustomerPromotionModel { get; set; }
        public virtual DbSet<CustomerTastesModel> CustomerTastesModel { get; set; }
        public virtual DbSet<CustomerTastes_CollectionModel> CustomerTastes_CollectionModel { get; set; }
        public virtual DbSet<CustomerTastes_ColorToneModel> CustomerTastes_ColorToneModel { get; set; }
        public virtual DbSet<CustomerTastes_ProductGroupModel> CustomerTastes_ProductGroupModel { get; set; }
        public virtual DbSet<CustomerTastes_WoodGrainModel> CustomerTastes_WoodGrainModel { get; set; }
        public virtual DbSet<DatatableConfig> DatatableConfig { get; set; }
        public virtual DbSet<DateClosedHistoryModel> DateClosedHistoryModel { get; set; }
        public virtual DbSet<DateClosedModel> DateClosedModel { get; set; }
        public virtual DbSet<DeliveryDetailModel> DeliveryDetailModel { get; set; }
        public virtual DbSet<DeliveryModel> DeliveryModel { get; set; }
        public virtual DbSet<DepartmentModel> DepartmentModel { get; set; }
        public virtual DbSet<DepartmentRoutingMapping> DepartmentRoutingMapping { get; set; }
        public virtual DbSet<Department_Routing_Mapping> Department_Routing_Mapping { get; set; }
        public virtual DbSet<DimDateModel> DimDateModel { get; set; }
        public virtual DbSet<DistrictModel> DistrictModel { get; set; }
        public virtual DbSet<DowntimeHistoryModel> DowntimeHistoryModel { get; set; }
        public virtual DbSet<DowntimeModel> DowntimeModel { get; set; }
        public virtual DbSet<EmailAccountModel> EmailAccountModel { get; set; }
        public virtual DbSet<EmailConfig> EmailConfig { get; set; }
        public virtual DbSet<EquipmentGroupModel> EquipmentGroupModel { get; set; }
        public virtual DbSet<EquipmentMdModel> EquipmentMdModel { get; set; }
        public virtual DbSet<EquipmentModel> EquipmentModel { get; set; }
        public virtual DbSet<ErrorListModel> ErrorListModel { get; set; }
        public virtual DbSet<FaceCheckInOutModel> FaceCheckInOutModel { get; set; }
        public virtual DbSet<FavoriteReportModel> FavoriteReportModel { get; set; }
        public virtual DbSet<FileAttachmentModel> FileAttachmentModel { get; set; }
        public virtual DbSet<FixingTypeModel> FixingTypeModel { get; set; }
        public virtual DbSet<FunctionModel> FunctionModel { get; set; }
        public virtual DbSet<GH_NotificationModel> GH_NotificationModel { get; set; }
        public virtual DbSet<HangTagModel> HangTagModel { get; set; }
        public virtual DbSet<Hash> Hash { get; set; }
        public virtual DbSet<Hash1> Hash1 { get; set; }
        public virtual DbSet<HeSoNhanCongModel> HeSoNhanCongModel { get; set; }
        public virtual DbSet<HistoryModel> HistoryModel { get; set; }
        public virtual DbSet<HolidayModel> HolidayModel { get; set; }
        public virtual DbSet<ImageProductModel> ImageProductModel { get; set; }
        public virtual DbSet<ImportProductModel> ImportProductModel { get; set; }
        public virtual DbSet<Job> Job { get; set; }
        public virtual DbSet<Job1> Job1 { get; set; }
        public virtual DbSet<JobParameter> JobParameter { get; set; }
        public virtual DbSet<JobParameter1> JobParameter1 { get; set; }
        public virtual DbSet<JobQueue> JobQueue { get; set; }
        public virtual DbSet<JobQueue1> JobQueue1 { get; set; }
        public virtual DbSet<KanbanDetailModel> KanbanDetailModel { get; set; }
        public virtual DbSet<KanbanModel> KanbanModel { get; set; }
        public virtual DbSet<Kanban_TaskStatus_Mapping> Kanban_TaskStatus_Mapping { get; set; }
        public virtual DbSet<KiemKeEquipmentMdModel> KiemKeEquipmentMdModel { get; set; }
        public virtual DbSet<LaborModel> LaborModel { get; set; }
        public virtual DbSet<LastRunCheckInOutModel> LastRunCheckInOutModel { get; set; }
        public virtual DbSet<List> List { get; set; }
        public virtual DbSet<List1> List1 { get; set; }
        public virtual DbSet<LogApiModel> LogApiModel { get; set; }
        public virtual DbSet<LogSAPModel> LogSAPModel { get; set; }
        public virtual DbSet<Log_BC01Model> Log_BC01Model { get; set; }
        public virtual DbSet<Log_BC18Model> Log_BC18Model { get; set; }
        public virtual DbSet<MaiDaoHistoryModel> MaiDaoHistoryModel { get; set; }
        public virtual DbSet<MaiDaoModel> MaiDaoModel { get; set; }
        public virtual DbSet<MailServerProviderModel> MailServerProviderModel { get; set; }
        public virtual DbSet<MarmModel> MarmModel { get; set; }
        public virtual DbSet<MaterialCardModel> MaterialCardModel { get; set; }
        public virtual DbSet<MaterialGroupModel> MaterialGroupModel { get; set; }
        public virtual DbSet<MaterialModel> MaterialModel { get; set; }
        public virtual DbSet<MaterialReservationModel> MaterialReservationModel { get; set; }
        public virtual DbSet<MaterialTypeModel> MaterialTypeModel { get; set; }
        public virtual DbSet<MemberOfExternalProfileTargetGroupModel> MemberOfExternalProfileTargetGroupModel { get; set; }
        public virtual DbSet<MemberOfTargetGroupModel> MemberOfTargetGroupModel { get; set; }
        public virtual DbSet<MenuModel> MenuModel { get; set; }
        public virtual DbSet<MesSyncLogModel> MesSyncLogModel { get; set; }
        public virtual DbSet<MigoModel> MigoModel { get; set; }
        public virtual DbSet<MobileScreenModel> MobileScreenModel { get; set; }
        public virtual DbSet<MobileScreenPermissionModel> MobileScreenPermissionModel { get; set; }
        public virtual DbSet<ModuleModel> ModuleModel { get; set; }
        public virtual DbSet<NFCCheckInOutModel> NFCCheckInOutModel { get; set; }
        public virtual DbSet<NewsCategoryModel> NewsCategoryModel { get; set; }
        public virtual DbSet<NewsModel> NewsModel { get; set; }
        public virtual DbSet<News_Company_Mapping> News_Company_Mapping { get; set; }
        public virtual DbSet<NotificationAccountMappingModel> NotificationAccountMappingModel { get; set; }
        public virtual DbSet<NotificationModel> NotificationModel { get; set; }
        public virtual DbSet<POTEXT_PR_SO_Model> POTEXT_PR_SO_Model { get; set; }
        public virtual DbSet<PageModel> PageModel { get; set; }
        public virtual DbSet<PagePermissionModel> PagePermissionModel { get; set; }
        public virtual DbSet<Page_Module_Mapping> Page_Module_Mapping { get; set; }
        public virtual DbSet<PartnerModel> PartnerModel { get; set; }
        public virtual DbSet<PaymentMethodModel> PaymentMethodModel { get; set; }
        public virtual DbSet<PaymentNationalBudgetModel> PaymentNationalBudgetModel { get; set; }
        public virtual DbSet<PeriodicallyCheckingModel> PeriodicallyCheckingModel { get; set; }
        public virtual DbSet<PersonInChargeDeletedModel> PersonInChargeDeletedModel { get; set; }
        public virtual DbSet<PersonInChargeModel> PersonInChargeModel { get; set; }
        public virtual DbSet<PhysicsWorkShopModel> PhysicsWorkShopModel { get; set; }
        public virtual DbSet<PlantRoutingConfigModel> PlantRoutingConfigModel { get; set; }
        public virtual DbSet<PlateFeeDetailModel> PlateFeeDetailModel { get; set; }
        public virtual DbSet<PlateFeeModel> PlateFeeModel { get; set; }
        public virtual DbSet<PriceProductModel> PriceProductModel { get; set; }
        public virtual DbSet<ProductAttributeModel> ProductAttributeModel { get; set; }
        public virtual DbSet<ProductBarcodeModel> ProductBarcodeModel { get; set; }
        public virtual DbSet<ProductHierarchyModel> ProductHierarchyModel { get; set; }
        public virtual DbSet<ProductImageModel> ProductImageModel { get; set; }
        public virtual DbSet<ProductLatestModel> ProductLatestModel { get; set; }
        public virtual DbSet<ProductModel> ProductModel { get; set; }
        public virtual DbSet<ProductTypeModel> ProductTypeModel { get; set; }
        public virtual DbSet<ProductVersionModel> ProductVersionModel { get; set; }
        public virtual DbSet<ProductWarrantyModel> ProductWarrantyModel { get; set; }
        public virtual DbSet<ProductionComponent80Model> ProductionComponent80Model { get; set; }
        public virtual DbSet<ProductionComponentModel> ProductionComponentModel { get; set; }
        public virtual DbSet<ProductionOperation80Model> ProductionOperation80Model { get; set; }
        public virtual DbSet<ProductionOperationModel> ProductionOperationModel { get; set; }
        public virtual DbSet<ProductionOrder80Model> ProductionOrder80Model { get; set; }
        public virtual DbSet<ProductionOrderConfigModel> ProductionOrderConfigModel { get; set; }
        public virtual DbSet<ProductionOrderModel> ProductionOrderModel { get; set; }
        public virtual DbSet<ProfileBAttributeModel> ProfileBAttributeModel { get; set; }
        public virtual DbSet<ProfileCAttributeModel> ProfileCAttributeModel { get; set; }
        public virtual DbSet<ProfileCareerModel> ProfileCareerModel { get; set; }
        public virtual DbSet<ProfileCategoryModel> ProfileCategoryModel { get; set; }
        public virtual DbSet<ProfileConfigModel> ProfileConfigModel { get; set; }
        public virtual DbSet<ProfileContactAttributeDeletedModel> ProfileContactAttributeDeletedModel { get; set; }
        public virtual DbSet<ProfileContactAttributeModel> ProfileContactAttributeModel { get; set; }
        public virtual DbSet<ProfileDeletedModel> ProfileDeletedModel { get; set; }
        public virtual DbSet<ProfileEmailDeletedModel> ProfileEmailDeletedModel { get; set; }
        public virtual DbSet<ProfileEmailModel> ProfileEmailModel { get; set; }
        public virtual DbSet<ProfileFieldModel> ProfileFieldModel { get; set; }
        public virtual DbSet<ProfileGroupModel> ProfileGroupModel { get; set; }
        public virtual DbSet<ProfileLevelModel> ProfileLevelModel { get; set; }
        public virtual DbSet<ProfileModel> ProfileModel { get; set; }
        public virtual DbSet<ProfilePhoneDeletedModel> ProfilePhoneDeletedModel { get; set; }
        public virtual DbSet<ProfilePhoneModel> ProfilePhoneModel { get; set; }
        public virtual DbSet<ProfileTypeModel> ProfileTypeModel { get; set; }
        public virtual DbSet<Profile_File_Mapping> Profile_File_Mapping { get; set; }
        public virtual DbSet<Profile_Opportunity_CompetitorModel> Profile_Opportunity_CompetitorModel { get; set; }
        public virtual DbSet<Profile_Opportunity_InternalModel> Profile_Opportunity_InternalModel { get; set; }
        public virtual DbSet<Profile_Opportunity_MaterialModel> Profile_Opportunity_MaterialModel { get; set; }
        public virtual DbSet<Profile_Opportunity_PartnerModel> Profile_Opportunity_PartnerModel { get; set; }
        public virtual DbSet<ProfitCenterModel> ProfitCenterModel { get; set; }
        public virtual DbSet<PrognosisModel> PrognosisModel { get; set; }
        public virtual DbSet<PromotionByStoreModel> PromotionByStoreModel { get; set; }
        public virtual DbSet<PromotionGiftAccessoryModel> PromotionGiftAccessoryModel { get; set; }
        public virtual DbSet<PromotionModel> PromotionModel { get; set; }
        public virtual DbSet<PropertiesProductModel> PropertiesProductModel { get; set; }
        public virtual DbSet<ProspectModel> ProspectModel { get; set; }
        public virtual DbSet<ProvinceModel> ProvinceModel { get; set; }
        public virtual DbSet<PurchaseOrderDetailModel> PurchaseOrderDetailModel { get; set; }
        public virtual DbSet<PurchaseOrderMasterModel> PurchaseOrderMasterModel { get; set; }
        public virtual DbSet<PurchaseOrderModel> PurchaseOrderModel { get; set; }
        public virtual DbSet<PurchaseRequisitionModel> PurchaseRequisitionModel { get; set; }
        public virtual DbSet<PushNotificationModel> PushNotificationModel { get; set; }
        public virtual DbSet<PushNotificationTargetModel> PushNotificationTargetModel { get; set; }
        public virtual DbSet<QualityControlDetailModel> QualityControlDetailModel { get; set; }
        public virtual DbSet<QualityControlInformationModel> QualityControlInformationModel { get; set; }
        public virtual DbSet<QualityControlModel> QualityControlModel { get; set; }
        public virtual DbSet<QualityControl_Error_File_Mapping> QualityControl_Error_File_Mapping { get; set; }
        public virtual DbSet<QualityControl_Error_Mapping> QualityControl_Error_Mapping { get; set; }
        public virtual DbSet<QualityControl_FileAttachment_Mapping> QualityControl_FileAttachment_Mapping { get; set; }
        public virtual DbSet<QualityControl_QCInformation_File_Mapping> QualityControl_QCInformation_File_Mapping { get; set; }
        public virtual DbSet<QualityControl_QCInformation_Mapping> QualityControl_QCInformation_Mapping { get; set; }
        public virtual DbSet<QuestionBankModel> QuestionBankModel { get; set; }
        public virtual DbSet<RatingModel> RatingModel { get; set; }
        public virtual DbSet<RawMaterialCardManualModel> RawMaterialCardManualModel { get; set; }
        public virtual DbSet<RawMaterialCardModel> RawMaterialCardModel { get; set; }
        public virtual DbSet<RawMaterialCard_SO_Mapping> RawMaterialCard_SO_Mapping { get; set; }
        public virtual DbSet<RawMaterialCard_WBS_Mapping> RawMaterialCard_WBS_Mapping { get; set; }
        public virtual DbSet<RawMaterial_PurchaseOrderDetail_Mapping> RawMaterial_PurchaseOrderDetail_Mapping { get; set; }
        public virtual DbSet<ReceiveInformationModel> ReceiveInformationModel { get; set; }
        public virtual DbSet<RecordCounts> RecordCounts { get; set; }
        public virtual DbSet<RefreshToken> RefreshToken { get; set; }
        public virtual DbSet<RegisterReceiveNewsModel> RegisterReceiveNewsModel { get; set; }
        public virtual DbSet<RemindTaskModel> RemindTaskModel { get; set; }
        public virtual DbSet<RequestEccEmailConfigModel> RequestEccEmailConfigModel { get; set; }
        public virtual DbSet<RequestReturnVendorDetailModel> RequestReturnVendorDetailModel { get; set; }
        public virtual DbSet<RequestReturnVendorModel> RequestReturnVendorModel { get; set; }
        public virtual DbSet<ReservationHeaderModel> ReservationHeaderModel { get; set; }
        public virtual DbSet<ReservationModel> ReservationModel { get; set; }
        public virtual DbSet<ResourceModel> ResourceModel { get; set; }
        public virtual DbSet<RoleInChargeDeletedModel> RoleInChargeDeletedModel { get; set; }
        public virtual DbSet<RoleInChargeModel> RoleInChargeModel { get; set; }
        public virtual DbSet<RolesModel> RolesModel { get; set; }
        public virtual DbSet<RoutingInventorModel> RoutingInventorModel { get; set; }
        public virtual DbSet<RoutingModel> RoutingModel { get; set; }
        public virtual DbSet<RoutingSapModel> RoutingSapModel { get; set; }
        public virtual DbSet<SMSModel> SMSModel { get; set; }
        public virtual DbSet<SO100ScheduleLineModel> SO100ScheduleLineModel { get; set; }
        public virtual DbSet<SO100TextModel> SO100TextModel { get; set; }
        public virtual DbSet<SO80ScheduleLineModel> SO80ScheduleLineModel { get; set; }
        public virtual DbSet<SO80TextModel> SO80TextModel { get; set; }
        public virtual DbSet<SOTEXT_PR_Model> SOTEXT_PR_Model { get; set; }
        public virtual DbSet<SOTextHeader100Model> SOTextHeader100Model { get; set; }
        public virtual DbSet<SOTextHeader80Model> SOTextHeader80Model { get; set; }
        public virtual DbSet<SOTextItem100Model> SOTextItem100Model { get; set; }
        public virtual DbSet<SOTextItem80Model> SOTextItem80Model { get; set; }
        public virtual DbSet<SQLProfilerModel> SQLProfilerModel { get; set; }
        public virtual DbSet<SaleOrderDetailModel> SaleOrderDetailModel { get; set; }
        public virtual DbSet<SaleOrderHeader100Model> SaleOrderHeader100Model { get; set; }
        public virtual DbSet<SaleOrderHeader80Model> SaleOrderHeader80Model { get; set; }
        public virtual DbSet<SaleOrderItem100Model> SaleOrderItem100Model { get; set; }
        public virtual DbSet<SaleOrderItem80Model> SaleOrderItem80Model { get; set; }
        public virtual DbSet<SaleOrderMasterModel> SaleOrderMasterModel { get; set; }
        public virtual DbSet<SaleProcessModel> SaleProcessModel { get; set; }
        public virtual DbSet<SaleUnitModel> SaleUnitModel { get; set; }
        public virtual DbSet<SalesEmployeeInfoModel> SalesEmployeeInfoModel { get; set; }
        public virtual DbSet<SalesEmployeeLevelModel> SalesEmployeeLevelModel { get; set; }
        public virtual DbSet<SalesEmployeeModel> SalesEmployeeModel { get; set; }
        public virtual DbSet<SampMethodModel> SampMethodModel { get; set; }
        public virtual DbSet<Schema> Schema { get; set; }
        public virtual DbSet<Schema1> Schema1 { get; set; }
        public virtual DbSet<SearchResultDetailTemplateModel> SearchResultDetailTemplateModel { get; set; }
        public virtual DbSet<SearchResultTemplateModel> SearchResultTemplateModel { get; set; }
        public virtual DbSet<SearchTemplateModel> SearchTemplateModel { get; set; }
        public virtual DbSet<SendMailCalendarModel> SendMailCalendarModel { get; set; }
        public virtual DbSet<Server> Server { get; set; }
        public virtual DbSet<Server1> Server1 { get; set; }
        public virtual DbSet<ServiceAppointmentModel> ServiceAppointmentModel { get; set; }
        public virtual DbSet<ServiceFlagModel> ServiceFlagModel { get; set; }
        public virtual DbSet<ServiceOrderConsultModel> ServiceOrderConsultModel { get; set; }
        public virtual DbSet<ServiceOrderDetailAccessoryModel> ServiceOrderDetailAccessoryModel { get; set; }
        public virtual DbSet<ServiceOrderDetailModel> ServiceOrderDetailModel { get; set; }
        public virtual DbSet<ServiceOrderDetailServiceModel> ServiceOrderDetailServiceModel { get; set; }
        public virtual DbSet<ServiceOrderModel> ServiceOrderModel { get; set; }
        public virtual DbSet<ServiceOrderPoolModel> ServiceOrderPoolModel { get; set; }
        public virtual DbSet<ServiceOrderTypeModel> ServiceOrderTypeModel { get; set; }
        public virtual DbSet<ServiceTypeModel> ServiceTypeModel { get; set; }
        public virtual DbSet<Set> Set { get; set; }
        public virtual DbSet<Set1> Set1 { get; set; }
        public virtual DbSet<SettingJob> SettingJob { get; set; }
        public virtual DbSet<SettingSyncSAPModel> SettingSyncSAPModel { get; set; }
        public virtual DbSet<ShowroomCategoryModel> ShowroomCategoryModel { get; set; }
        public virtual DbSet<SlocModel> SlocModel { get; set; }
        public virtual DbSet<SoToKhaiModel> SoToKhaiModel { get; set; }
        public virtual DbSet<SourceModel> SourceModel { get; set; }
        public virtual DbSet<SpecificationsModel> SpecificationsModel { get; set; }
        public virtual DbSet<SpecificationsProductModel> SpecificationsProductModel { get; set; }
        public virtual DbSet<State> State { get; set; }
        public virtual DbSet<State1> State1 { get; set; }
        public virtual DbSet<StateTreasuryModel> StateTreasuryModel { get; set; }
        public virtual DbSet<StatusTransitionModel> StatusTransitionModel { get; set; }
        public virtual DbSet<StatusTransition_Task_Mapping> StatusTransition_Task_Mapping { get; set; }
        public virtual DbSet<StockModel> StockModel { get; set; }
        public virtual DbSet<StockReceivingDetailModel> StockReceivingDetailModel { get; set; }
        public virtual DbSet<StockReceivingMasterModel> StockReceivingMasterModel { get; set; }
        public virtual DbSet<StockTransferRequestDetailModel> StockTransferRequestDetailModel { get; set; }
        public virtual DbSet<StockTransferRequestModel> StockTransferRequestModel { get; set; }
        public virtual DbSet<Stock_Store_Mapping> Stock_Store_Mapping { get; set; }
        public virtual DbSet<StorageBinModel> StorageBinModel { get; set; }
        public virtual DbSet<StoreModel> StoreModel { get; set; }
        public virtual DbSet<StoreTypeModel> StoreTypeModel { get; set; }
        public virtual DbSet<StyleModel> StyleModel { get; set; }
        public virtual DbSet<SupplierModel> SupplierModel { get; set; }
        public virtual DbSet<SyncActionModel> SyncActionModel { get; set; }
        public virtual DbSet<TDSModel> TDSModel { get; set; }
        public virtual DbSet<TargetGroupModel> TargetGroupModel { get; set; }
        public virtual DbSet<TaskAssignModel> TaskAssignModel { get; set; }
        public virtual DbSet<TaskCommentModel> TaskCommentModel { get; set; }
        public virtual DbSet<TaskContactModel> TaskContactModel { get; set; }
        public virtual DbSet<TaskGroupDetailModel> TaskGroupDetailModel { get; set; }
        public virtual DbSet<TaskGroupModel> TaskGroupModel { get; set; }
        public virtual DbSet<TaskModel> TaskModel { get; set; }
        public virtual DbSet<TaskProductAccessoryModel> TaskProductAccessoryModel { get; set; }
        public virtual DbSet<TaskProductModel> TaskProductModel { get; set; }
        public virtual DbSet<TaskProductUsualErrorModel> TaskProductUsualErrorModel { get; set; }
        public virtual DbSet<TaskReferenceModel> TaskReferenceModel { get; set; }
        public virtual DbSet<TaskReporterModel> TaskReporterModel { get; set; }
        public virtual DbSet<TaskRoleInChargeModel> TaskRoleInChargeModel { get; set; }
        public virtual DbSet<TaskStatusModel> TaskStatusModel { get; set; }
        public virtual DbSet<Task_File_Mapping> Task_File_Mapping { get; set; }
        public virtual DbSet<TaxConfigModel> TaxConfigModel { get; set; }
        public virtual DbSet<TemperatureConditionModel> TemperatureConditionModel { get; set; }
        public virtual DbSet<TemplateAndGiftCampaignModel> TemplateAndGiftCampaignModel { get; set; }
        public virtual DbSet<TemplateAndGiftMemberAddressModel> TemplateAndGiftMemberAddressModel { get; set; }
        public virtual DbSet<TemplateAndGiftMemberModel> TemplateAndGiftMemberModel { get; set; }
        public virtual DbSet<TemplateAndGiftTargetGroupModel> TemplateAndGiftTargetGroupModel { get; set; }
        public virtual DbSet<ThucThiLenhSanXuatModel> ThucThiLenhSanXuatModel { get; set; }
        public virtual DbSet<TransferDetailModel> TransferDetailModel { get; set; }
        public virtual DbSet<TransferModel> TransferModel { get; set; }
        public virtual DbSet<TyLeTieuHaoModel> TyLeTieuHaoModel { get; set; }
        public virtual DbSet<Unfollow> Unfollow { get; set; }
        public virtual DbSet<VIEW_TonKhoTheo_SO_CongDoan> VIEW_TonKhoTheo_SO_CongDoan { get; set; }
        public virtual DbSet<VehicleInfoModel> VehicleInfoModel { get; set; }
        public virtual DbSet<VendorModel> VendorModel { get; set; }
        public virtual DbSet<View_BOM_Inventor_Rip> View_BOM_Inventor_Rip { get; set; }
        public virtual DbSet<View_Catalog_Category> View_Catalog_Category { get; set; }
        public virtual DbSet<View_FaceCheckIn> View_FaceCheckIn { get; set; }
        public virtual DbSet<View_FaceCheckOut> View_FaceCheckOut { get; set; }
        public virtual DbSet<View_MES_StockReceiving> View_MES_StockReceiving { get; set; }
        public virtual DbSet<View_PriorityModel> View_PriorityModel { get; set; }
        public virtual DbSet<View_Product_Detail> View_Product_Detail { get; set; }
        public virtual DbSet<View_Product_Detail2> View_Product_Detail2 { get; set; }
        public virtual DbSet<View_Product_Material> View_Product_Material { get; set; }
        public virtual DbSet<View_ProfileDeleted_ContactPhone> View_ProfileDeleted_ContactPhone { get; set; }
        public virtual DbSet<View_Profile_Address> View_Profile_Address { get; set; }
        public virtual DbSet<View_Profile_Company_Mapping> View_Profile_Company_Mapping { get; set; }
        public virtual DbSet<View_Profile_ContactPhone> View_Profile_ContactPhone { get; set; }
        public virtual DbSet<View_Profile_ExtendInfo> View_Profile_ExtendInfo { get; set; }
        public virtual DbSet<View_Profile_MainContact> View_Profile_MainContact { get; set; }
        public virtual DbSet<View_Profile_ProfilePhone> View_Profile_ProfilePhone { get; set; }
        public virtual DbSet<View_SO_Active> View_SO_Active { get; set; }
        public virtual DbSet<View_Stock_Delivery> View_Stock_Delivery { get; set; }
        public virtual DbSet<View_Stock_Receive> View_Stock_Receive { get; set; }
        public virtual DbSet<View_Stock_TransferFrom_Delivery> View_Stock_TransferFrom_Delivery { get; set; }
        public virtual DbSet<View_Stock_TransferTo_Receive> View_Stock_TransferTo_Receive { get; set; }
        public virtual DbSet<View_Task_Area> View_Task_Area { get; set; }
        public virtual DbSet<View_Task_GTB> View_Task_GTB { get; set; }
        public virtual DbSet<WardModel> WardModel { get; set; }
        public virtual DbSet<WarehouseExportDetailModel> WarehouseExportDetailModel { get; set; }
        public virtual DbSet<WarehouseExportModel> WarehouseExportModel { get; set; }
        public virtual DbSet<WarehouseModel> WarehouseModel { get; set; }
        public virtual DbSet<WarehouseProductModel> WarehouseProductModel { get; set; }
        public virtual DbSet<WarehouseTranferModel> WarehouseTranferModel { get; set; }
        public virtual DbSet<WarehouseTransactionModel> WarehouseTransactionModel { get; set; }
        public virtual DbSet<WarrantyModel> WarrantyModel { get; set; }
        public virtual DbSet<WorkCenterModel> WorkCenterModel { get; set; }
        public virtual DbSet<WorkFlowCategoryModel> WorkFlowCategoryModel { get; set; }
        public virtual DbSet<WorkFlowConfigModel> WorkFlowConfigModel { get; set; }
        public virtual DbSet<WorkFlowFieldModel> WorkFlowFieldModel { get; set; }
        public virtual DbSet<WorkFlowModel> WorkFlowModel { get; set; }
        public virtual DbSet<WorkShopModel> WorkShopModel { get; set; }
        public virtual DbSet<WorkingDateModel> WorkingDateModel { get; set; }
        public virtual DbSet<WorkingTimeConfigModel> WorkingTimeConfigModel { get; set; }
        public virtual DbSet<WorkingTimeDetailModel> WorkingTimeDetailModel { get; set; }
        public virtual DbSet<WorkingTimeModel> WorkingTimeModel { get; set; }
        public virtual DbSet<ZTB_PPR02> ZTB_PPR02 { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AccessoryCategoryModel>(entity =>
            {
                entity.Property(e => e.AccessoryCategoryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<AccessoryDetailModel>(entity =>
            {
                entity.Property(e => e.AccessoryDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.Accessory)
                    .WithMany(p => p.AccessoryDetailModel)
                    .HasForeignKey(d => d.AccessoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AccessoryDetailModel_AccessoryModel");
            });

            modelBuilder.Entity<AccessoryModel>(entity =>
            {
                entity.Property(e => e.AccessoryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<AccessoryPriceModel>(entity =>
            {
                entity.Property(e => e.AccessoryPriceId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<AccessoryProductModel>(entity =>
            {
                entity.Property(e => e.AccessoryProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Accessory)
                    .WithMany(p => p.AccessoryProductModel)
                    .HasForeignKey(d => d.AccessoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AccessoryProductModel_AccessoryModel");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.AccessoryProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AccessoryProductModel_ProductModel");
            });

            modelBuilder.Entity<AccessorySaleOrderDetailModel>(entity =>
            {
                entity.Property(e => e.AccessorySaleOrderDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.AccessorySaleOrder)
                    .WithMany(p => p.AccessorySaleOrderDetailModel)
                    .HasForeignKey(d => d.AccessorySaleOrderId)
                    .HasConstraintName("FK_AccessorySaleOrderDetailModel_AccessorySaleOrderModel");
            });

            modelBuilder.Entity<AccessorySaleOrderModel>(entity =>
            {
                entity.Property(e => e.AccessorySaleOrderId).ValueGeneratedNever();

                entity.Property(e => e.GeneratedCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.AccessorySaleOrderModel)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_AccessorySaleOrderModel_CustomerModel");

                entity.HasOne(d => d.Vehicle)
                    .WithMany(p => p.AccessorySaleOrderModel)
                    .HasForeignKey(d => d.VehicleId)
                    .HasConstraintName("FK_AccessorySaleOrderModel_VehicleInfoModel");
            });

            modelBuilder.Entity<AccessorySellTypeModel>(entity =>
            {
                entity.Property(e => e.AccessorySellTypeId).ValueGeneratedNever();

                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<AccountModel>(entity =>
            {
                entity.HasIndex(e => new { e.AccountId, e.UserName }, "IX_AccountModel_AccountId_UserName")
                    .HasFillFactor(90);

                entity.Property(e => e.AccountId).ValueGeneratedNever();

                entity.Property(e => e.IsDeleted).HasDefaultValueSql("((0))");

                entity.HasMany(d => d.Roles)
                    .WithMany(p => p.Account)
                    .UsingEntity<Dictionary<string, object>>(
                        "AccountInRoleModel",
                        l => l.HasOne<RolesModel>().WithMany().HasForeignKey("RolesId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_AccountInRoleModel_RolesModel"),
                        r => r.HasOne<AccountModel>().WithMany().HasForeignKey("AccountId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_AccountInRoleModel_AccountModel"),
                        j =>
                        {
                            j.HasKey("AccountId", "RolesId");

                            j.ToTable("AccountInRoleModel", "pms");
                        });

                entity.HasMany(d => d.Store)
                    .WithMany(p => p.Account)
                    .UsingEntity<Dictionary<string, object>>(
                        "AccountInStoreModel",
                        l => l.HasOne<StoreModel>().WithMany().HasForeignKey("StoreId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_AccountInStoreModel_StoreModel"),
                        r => r.HasOne<AccountModel>().WithMany().HasForeignKey("AccountId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_AccountInStoreModel_AccountModel"),
                        j =>
                        {
                            j.HasKey("AccountId", "StoreId");

                            j.ToTable("AccountInStoreModel", "pms");
                        });
            });

            modelBuilder.Entity<Account_Device_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.AccountId, e.DeviceId });
            });

            modelBuilder.Entity<AddressBookModel>(entity =>
            {
                entity.Property(e => e.AddressBookId).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.AddressBookModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_AddressBookModel_ProfileModel");
            });

            modelBuilder.Entity<AggregatedCounter>(entity =>
            {
                entity.HasKey(e => e.Key)
                    .HasName("PK_HangFire_CounterAggregated");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_AggregatedCounter_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<AggregatedCounter1>(entity =>
            {
                entity.HasKey(e => e.Key)
                    .HasName("PK_HangFire_CounterAggregated");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_AggregatedCounter_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<AllDepartmentModel>(entity =>
            {
                entity.HasKey(e => e.AllDepartmentId)
                    .HasName("PK_AllDeparmentModel");

                entity.Property(e => e.AllDepartmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<AllocateMaterialLogModel>(entity =>
            {
                entity.HasKey(e => e.LogId)
                    .HasName("PK__Allocate__5E548648D8E7D897");

                entity.Property(e => e.CreateTime).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<ApplicationLog>(entity =>
            {
                entity.Property(e => e.ApplicationLogId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.PerformedBy_Account)
                    .WithMany(p => p.ApplicationLog)
                    .HasForeignKey(d => d.PerformedBy_AccountId)
                    .HasConstraintName("FK_ApplicationLog_AccountModel");
            });

            modelBuilder.Entity<AppointmentModel>(entity =>
            {
                entity.HasIndex(e => e.VisitDate, "index_AppointmentModel_VisitDate")
                    .HasFillFactor(90);

                entity.Property(e => e.AppointmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<AssignmentModel>(entity =>
            {
                entity.Property(e => e.AssignmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Assignment_ProductionOrderModel>(entity =>
            {
                entity.Property(e => e.AssignmentProductionOrderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Assignment_StepModel>(entity =>
            {
                entity.Property(e => e.AssignmentStepId).ValueGeneratedNever();
            });

            modelBuilder.Entity<AutoConditionModel>(entity =>
            {
                entity.Property(e => e.AutoConditionId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC01Model>(entity =>
            {
                entity.Property(e => e.BC01Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC03Model>(entity =>
            {
                entity.Property(e => e.BC03Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC07Model>(entity =>
            {
                entity.Property(e => e.BC07Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC15Model>(entity =>
            {
                entity.Property(e => e.BC15Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC16Model>(entity =>
            {
                entity.Property(e => e.BC16Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC18Model>(entity =>
            {
                entity.Property(e => e.BC18Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BC19Model>(entity =>
            {
                entity.Property(e => e.BC19Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<BCUuTienModel>(entity =>
            {
                entity.HasKey(e => new { e.ZZLSX, e.Plant })
                    .HasName("PK_BCUuTienModel_ZZLSX");
            });

            modelBuilder.Entity<BOMDetailModel>(entity =>
            {
                entity.HasKey(e => e.BomDetailId)
                    .HasName("PK_BomDetailModel");

                entity.HasIndex(e => new { e.MATNR, e.WERKS, e.STLNR, e.STLKN, e.STPOZ }, "IX_BOMDetailModel_Covering")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.MATNR, e.WERKS, e.STLNR, e.STLKN, e.STPOZ }, "IX_BOMDetailModel_Filter")
                    .HasFillFactor(90);

                entity.Property(e => e.BomDetailId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BOMHeaderModel>(entity =>
            {
                entity.HasKey(e => e.BomHeaderId)
                    .HasName("PK_BomHeaderModel");

                entity.Property(e => e.BomHeaderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BOM_Header_InventorModel>(entity =>
            {
                entity.Property(e => e.BOMHeaderInventorId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BOM_Item_InventorModel>(entity =>
            {
                entity.HasKey(e => e.BOMItemInventorId)
                    .HasName("PK_MES.BOM_Item_InventorModel");

                entity.HasIndex(e => new { e.PART_ID, e.MATNR, e.VERSO, e.POT11 }, "IX_BOM_Item_Inventor_PartMaterial")
                    .HasFilter("([POT21] IS NOT NULL AND [POT21]<>'')")
                    .HasFillFactor(90);

                entity.Property(e => e.BOMItemInventorId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BangTinModel>(entity =>
            {
                entity.Property(e => e.NewsId).ValueGeneratedNever();

                entity.Property(e => e.NewsCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<BannerModel>(entity =>
            {
                entity.Property(e => e.BannerId).ValueGeneratedNever();
            });

            modelBuilder.Entity<BookingModel>(entity =>
            {
                entity.Property(e => e.BookingModelId).ValueGeneratedNever();

                entity.Property(e => e.BookingCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<CampaignModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.CampaignCode).ValueGeneratedOnAdd();

                entity.Property(e => e.Type).HasComment("Marketing|Event");

                entity.HasOne(d => d.StatusNavigation)
                    .WithMany(p => p.CampaignModel)
                    .HasForeignKey(d => d.Status)
                    .HasConstraintName("FK_CampaignModel_CatalogModel");
            });

            modelBuilder.Entity<CapacityRegisterModel>(entity =>
            {
                entity.Property(e => e.CapacityRegisterId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CareerModel>(entity =>
            {
                entity.Property(e => e.CareerId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CatalogModel>(entity =>
            {
                entity.HasIndex(e => new { e.CatalogTypeCode, e.CatalogCode }, "IX_CatalogModel_CatalogTypeCode_CatalogCode_Result")
                    .HasFilter("([CatalogTypeCode]=N'QualityControl_Result')")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.CatalogTypeCode, e.CatalogCode }, "IX_CatalogModel_QualityControlResult_Covering")
                    .HasFilter("([CatalogTypeCode]=N'QualityControl_Result')");

                entity.HasIndex(e => new { e.CatalogTypeCode, e.CatalogCode }, "IX_CatalogModel_QualityControl_Method")
                    .HasFilter("([CatalogTypeCode]=N'QualityControl_Method')");

                entity.HasIndex(e => new { e.CatalogTypeCode, e.CatalogCode }, "IX_CatalogModel_SamplingLevel")
                    .HasFilter("([CatalogTypeCode]=N'SamplingLevel')");

                entity.Property(e => e.CatalogId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<CategoryModel>(entity =>
            {
                entity.HasKey(e => e.CategoryId)
                    .HasName("PK_Category2Model");

                entity.Property(e => e.CategoryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CheckInOutModel>(entity =>
            {
                entity.Property(e => e.CheckInOutId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CheckingTimesNotificationModel>(entity =>
            {
                entity.Property(e => e.CheckingTimesId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<ClaimAccessoryLogModel>(entity =>
            {
                entity.Property(e => e.ClaimAccessoryLogId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ClaimAccessoryModel>(entity =>
            {
                entity.Property(e => e.ClaimAccessoryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ClaimAccessoryStatusModel>(entity =>
            {
                entity.Property(e => e.StatusId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CollectingAuthorityModel>(entity =>
            {
                entity.Property(e => e.CollectingAuthorityId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ColorModel>(entity =>
            {
                entity.Property(e => e.ColorId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ColorProductModel>(entity =>
            {
                entity.Property(e => e.ColorProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ColorProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ColorProductModel_ProductModel");

                entity.HasOne(d => d.Style)
                    .WithMany(p => p.ColorProductModel)
                    .HasForeignKey(d => d.StyleId)
                    .HasConstraintName("FK_ColorProductModel_StyleModel");
            });

            modelBuilder.Entity<Comment_File_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.TaskCommentId, e.FileAttachmentId });

                entity.HasOne(d => d.FileAttachment)
                    .WithMany(p => p.Comment_File_Mapping)
                    .HasForeignKey(d => d.FileAttachmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Comment_File_Mapping_FileAttachmentModel");

                entity.HasOne(d => d.TaskComment)
                    .WithMany(p => p.Comment_File_Mapping)
                    .HasForeignKey(d => d.TaskCommentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Comment_File_Mapping_TaskCommentModel");
            });

            modelBuilder.Entity<CompanyModel>(entity =>
            {
                entity.Property(e => e.CompanyId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ConfigurationModel>(entity =>
            {
                entity.Property(e => e.ConfigurationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ConsumableMaterialsDeliveryModel>(entity =>
            {
                entity.Property(e => e.ConsumableMaterialsDeliveryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContConfigModel>(entity =>
            {
                entity.Property(e => e.ContConfigId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContRegisterModel>(entity =>
            {
                entity.Property(e => e.ContRegisterId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContRegisterSO60Model>(entity =>
            {
                entity.Property(e => e.ContRegister60Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContactDetailModel>(entity =>
            {
                entity.Property(e => e.ContactDetailId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContactModel>(entity =>
            {
                entity.Property(e => e.ContactId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ContainerRequirementModel>(entity =>
            {
                entity.HasKey(e => e.ContainerRequirementCode)
                    .HasName("PK_OptionModel");

                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<ContentModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.ContentCode).ValueGeneratedOnAdd();

                entity.Property(e => e.Type).HasComment("Marketing|Event");

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.ContentModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_ContentModel_AccountModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.ContentModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_ContentModel_AccountModel1");
            });

            modelBuilder.Entity<Counter>(entity =>
            {
                entity.HasIndex(e => e.Key, "CX_HangFire_Counter")
                    .IsClustered()
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Counter1>(entity =>
            {
                entity.HasIndex(e => e.Key, "CX_HangFire_Counter")
                    .IsClustered()
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<CreditLimitModel>(entity =>
            {
                entity.Property(e => e.CreditLimitId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerGiftDetailModel>(entity =>
            {
                entity.HasKey(e => new { e.GiftId, e.CustomerId });

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.CustomerGiftDetailModel)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_CustomerGiftDetailModel_CustomerModel");

                entity.HasOne(d => d.Gift)
                    .WithMany(p => p.CustomerGiftDetailModel)
                    .HasForeignKey(d => d.GiftId)
                    .HasConstraintName("FK_CustomerGiftDetailModel_CustomerGiftModel");
            });

            modelBuilder.Entity<CustomerGiftModel>(entity =>
            {
                entity.Property(e => e.GiftId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerLevelModel>(entity =>
            {
                entity.Property(e => e.CustomerLevelId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerModel>(entity =>
            {
                entity.Property(e => e.CustomerId).ValueGeneratedNever();

                entity.Property(e => e.GeneratedCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<CustomerPromotionModel>(entity =>
            {
                entity.Property(e => e.PromotionId).ValueGeneratedNever();

                entity.HasMany(d => d.Product)
                    .WithMany(p => p.Promotion)
                    .UsingEntity<Dictionary<string, object>>(
                        "CustomerPromotionProductModel",
                        l => l.HasOne<ProductModel>().WithMany().HasForeignKey("ProductId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_CustomerPromotionProductModel_ProductModel"),
                        r => r.HasOne<CustomerPromotionModel>().WithMany().HasForeignKey("PromotionId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_CustomerPromotionProductModel_CustomerPromotionModel"),
                        j =>
                        {
                            j.HasKey("PromotionId", "ProductId");

                            j.ToTable("CustomerPromotionProductModel", "tSale");
                        });
            });

            modelBuilder.Entity<CustomerTastesModel>(entity =>
            {
                entity.HasKey(e => e.CustomerTasteId)
                    .HasName("PK_Customer_Tastes_Model_1");

                entity.Property(e => e.CustomerTasteId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerTastes_CollectionModel>(entity =>
            {
                entity.HasKey(e => e.CollectionId)
                    .HasName("PK_Customer_Tastes_Collection_Model_1");

                entity.Property(e => e.CollectionId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerTastes_ColorToneModel>(entity =>
            {
                entity.HasKey(e => e.ColorToneId)
                    .HasName("PK_Customer_Tastes_ColorTone_Model_1");

                entity.Property(e => e.ColorToneId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerTastes_ProductGroupModel>(entity =>
            {
                entity.HasKey(e => e.ProductGroupId)
                    .HasName("PK_Customer_Tastes_ProductGroup_Model_1");

                entity.Property(e => e.ProductGroupId).ValueGeneratedNever();
            });

            modelBuilder.Entity<CustomerTastes_WoodGrainModel>(entity =>
            {
                entity.HasKey(e => e.WoodGrainId)
                    .HasName("PK_Customer_Tastes_WoodGrain_Model_1");

                entity.Property(e => e.WoodGrainId).ValueGeneratedNever();
            });

            modelBuilder.Entity<DatatableConfig>(entity =>
            {
                entity.Property(e => e.ConfigId).ValueGeneratedNever();
            });

            modelBuilder.Entity<DateClosedHistoryModel>(entity =>
            {
                entity.Property(e => e.DateClosedId).ValueGeneratedNever();
            });

            modelBuilder.Entity<DateClosedModel>(entity =>
            {
                entity.Property(e => e.DateClosedId).ValueGeneratedNever();
            });

            modelBuilder.Entity<DeliveryDetailModel>(entity =>
            {
                entity.Property(e => e.DeliveryDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.DateKeyNavigation)
                    .WithMany(p => p.DeliveryDetailModel)
                    .HasForeignKey(d => d.DateKey)
                    .HasConstraintName("FK_DeliveryDetailModel_DimDate");

                entity.HasOne(d => d.Delivery)
                    .WithMany(p => p.DeliveryDetailModel)
                    .HasForeignKey(d => d.DeliveryId)
                    .HasConstraintName("FK_DeliveryDetailModel_DeliveryModel");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.DeliveryDetailModel)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_DeliveryDetailModel_ProductModel");

                entity.HasOne(d => d.Stock)
                    .WithMany(p => p.DeliveryDetailModel)
                    .HasForeignKey(d => d.StockId)
                    .HasConstraintName("FK_DeliveryDetailModel_StockModel");
            });

            modelBuilder.Entity<DeliveryModel>(entity =>
            {
                entity.Property(e => e.DeliveryId).ValueGeneratedNever();

                entity.Property(e => e.DeliveryCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.DeliveryModel)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_DeliveryModel_CompanyModel");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.DeliveryModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_DeliveryModel_ProfileModel");

                entity.HasOne(d => d.SalesEmployeeCodeNavigation)
                    .WithMany(p => p.DeliveryModel)
                    .HasForeignKey(d => d.SalesEmployeeCode)
                    .HasConstraintName("FK_DeliveryModel_SalesEmployeeModel");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.DeliveryModel)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_DeliveryModel_StoreModel");
            });

            modelBuilder.Entity<DepartmentModel>(entity =>
            {
                entity.HasIndex(e => e.DepartmentCode, "IX_DepartmentModel_DepartmentCode")
                    .HasFilter("([DepartmentCode] IS NOT NULL)");

                entity.HasIndex(e => new { e.DepartmentCode, e.Actived }, "IX_DepartmentModel_DepartmentCode_Actived")
                    .HasFilter("([DepartmentCode] IS NOT NULL AND [Actived] IS NOT NULL)");

                entity.Property(e => e.DepartmentId).ValueGeneratedNever();

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.DepartmentModel)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_DepartmentModel_StoreModel");
            });

            modelBuilder.Entity<DepartmentRoutingMapping>(entity =>
            {
                entity.HasKey(e => new { e.DepartmentCode, e.RoutingCode })
                    .HasName("PK_DeptRouting");

                entity.HasIndex(e => e.DepartmentCode, "IX_DeptRouting_DepartmentCode")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.RoutingCode, "IX_DeptRouting_RoutingCode")
                    .HasFillFactor(90);

                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<Department_Routing_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.DepartmentId, e.StepId });

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.Department_Routing_Mapping)
                    .HasForeignKey(d => d.DepartmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Department_Routing_Mapping_DepartmentModel");

                entity.HasOne(d => d.Step)
                    .WithMany(p => p.Department_Routing_Mapping)
                    .HasForeignKey(d => d.StepId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Department_Routing_Mapping_RoutingModel");
            });

            modelBuilder.Entity<DimDateModel>(entity =>
            {
                entity.HasKey(e => e.DateKey)
                    .HasName("PK__DimDateM__40DF45E33D4BCE98");

                entity.Property(e => e.DateKey).ValueGeneratedNever();

                entity.Property(e => e.DayOfWeekUK).IsFixedLength();

                entity.Property(e => e.DayOfWeekUSA).IsFixedLength();

                entity.Property(e => e.FiscalMMYYYY).IsFixedLength();

                entity.Property(e => e.FiscalMonthYear).IsFixedLength();

                entity.Property(e => e.FiscalQuarter).IsFixedLength();

                entity.Property(e => e.FiscalYear).IsFixedLength();

                entity.Property(e => e.FiscalYearName).IsFixedLength();

                entity.Property(e => e.FullDateUK).IsFixedLength();

                entity.Property(e => e.FullDateUSA).IsFixedLength();

                entity.Property(e => e.MMYYYY).IsFixedLength();

                entity.Property(e => e.MonthYear).IsFixedLength();

                entity.Property(e => e.Quarter).IsFixedLength();

                entity.Property(e => e.Year).IsFixedLength();

                entity.Property(e => e.YearName).IsFixedLength();
            });

            modelBuilder.Entity<DistrictModel>(entity =>
            {
                entity.Property(e => e.DistrictId).ValueGeneratedNever();
            });

            modelBuilder.Entity<DowntimeHistoryModel>(entity =>
            {
                entity.HasKey(e => e.HistoryId)
                    .HasName("PK__Downtime__4D7B4ABD4AB4F01D");

                entity.Property(e => e.HistoryId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.Downtime)
                    .WithMany(p => p.DowntimeHistoryModel)
                    .HasForeignKey(d => d.DowntimeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DowntimeHistory_Downtime");
            });

            modelBuilder.Entity<DowntimeModel>(entity =>
            {
                entity.HasKey(e => e.DowntimeId)
                    .HasName("PK__Downtime__99876E357E309D65");

                entity.HasIndex(e => e.DepartmentCode, "IX_DowntimeModel_DepartmentCode")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.StepCode, "IX_DowntimeModel_StepCode")
                    .HasFillFactor(90);

                entity.Property(e => e.DowntimeId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<EmailAccountModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<EmailConfig>(entity =>
            {
                entity.Property(e => e.EmailConfigId).ValueGeneratedNever();
            });

            modelBuilder.Entity<EquipmentGroupModel>(entity =>
            {
                entity.Property(e => e.EquipmentGroupId).ValueGeneratedNever();
            });

            modelBuilder.Entity<EquipmentMdModel>(entity =>
            {
                entity.HasKey(e => e.EquipmentMdId)
                    .HasName("PK_EquipmentMdModel_EquipmentMdId");

                entity.Property(e => e.EquipmentMdId).ValueGeneratedNever();
            });

            modelBuilder.Entity<EquipmentModel>(entity =>
            {
                entity.Property(e => e.EquipmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ErrorListModel>(entity =>
            {
                entity.Property(e => e.ErrorListId).ValueGeneratedNever();
            });

            modelBuilder.Entity<FavoriteReportModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.FavoriteReportModel)
                    .HasForeignKey(d => d.AccountId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FavoriteReportModel_AccountModel");

                entity.HasOne(d => d.Page)
                    .WithMany(p => p.FavoriteReportModel)
                    .HasForeignKey(d => d.PageId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FavoriteReportModel_PageModel");
            });

            modelBuilder.Entity<FileAttachmentModel>(entity =>
            {
                entity.Property(e => e.FileAttachmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<FixingTypeModel>(entity =>
            {
                entity.Property(e => e.FixingTypeId).ValueGeneratedNever();

                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<GH_NotificationModel>(entity =>
            {
                entity.Property(e => e.NotificationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<HangTagModel>(entity =>
            {
                entity.HasIndex(e => new { e.CustomerReference, e.BatchPrinting }, "IX_HangTagModel_CustomerRef_BatchPrinting")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.CustomerReference, e.BatchPrinting }, "IX_HangTagModel_CustomerReference_BatchPrinting")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.HangTagId, e.CustomerReference }, "IX_HangTagModel_HangTagId_CustomerReference")
                    .HasFillFactor(90);

                entity.Property(e => e.HangTagId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Hash>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Field })
                    .HasName("PK_HangFire_Hash");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Hash_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Hash1>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Field })
                    .HasName("PK_HangFire_Hash");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Hash_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<HeSoNhanCongModel>(entity =>
            {
                entity.Property(e => e.CreateTime).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<HistoryModel>(entity =>
            {
                entity.Property(e => e.HistoryModifyId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ImageProductModel>(entity =>
            {
                entity.Property(e => e.ImageId).ValueGeneratedNever();

                entity.HasOne(d => d.ColorProduct)
                    .WithMany(p => p.ImageProductModel)
                    .HasForeignKey(d => d.ColorProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ImageProductModel_ColorProductModel");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ImageProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ImageProductModel_ProductModel");
            });

            modelBuilder.Entity<ImportProductModel>(entity =>
            {
                entity.Property(e => e.ImportProductId).HasDefaultValueSql("(newid())");

                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");

                entity.Property(e => e.CreateTime).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DocumentDate).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ImportSequence).HasDefaultValueSql("((1))");

                entity.Property(e => e.Status).HasDefaultValueSql("('Pending')");

                entity.Property(e => e.Unit).HasDefaultValueSql("('CAI')");
            });

            modelBuilder.Entity<Job>(entity =>
            {
                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Job_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.StateName, "IX_HangFire_Job_StateName")
                    .HasFilter("([StateName] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Job1>(entity =>
            {
                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Job_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.StateName, "IX_HangFire_Job_StateName")
                    .HasFilter("([StateName] IS NOT NULL)")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<JobParameter>(entity =>
            {
                entity.HasKey(e => new { e.JobId, e.Name })
                    .HasName("PK_HangFire_JobParameter");

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.JobParameter)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_JobParameter_Job");
            });

            modelBuilder.Entity<JobParameter1>(entity =>
            {
                entity.HasKey(e => new { e.JobId, e.Name })
                    .HasName("PK_HangFire_JobParameter");

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.JobParameter1)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_JobParameter_Job");
            });

            modelBuilder.Entity<JobQueue>(entity =>
            {
                entity.HasKey(e => new { e.Queue, e.Id })
                    .HasName("PK_HangFire_JobQueue");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<JobQueue1>(entity =>
            {
                entity.HasKey(e => new { e.Queue, e.Id })
                    .HasName("PK_HangFire_JobQueue");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<KanbanDetailModel>(entity =>
            {
                entity.Property(e => e.KanbanDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.Kanban)
                    .WithMany(p => p.KanbanDetailModel)
                    .HasForeignKey(d => d.KanbanId)
                    .HasConstraintName("FK_KanbanDetailModel_KanbanModel");
            });

            modelBuilder.Entity<KanbanModel>(entity =>
            {
                entity.Property(e => e.KanbanId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Kanban_TaskStatus_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.KanbanDetailId, e.TaskStatusId })
                    .HasName("PK_Kanban_Task_Mapping");

                entity.HasOne(d => d.KanbanDetail)
                    .WithMany(p => p.Kanban_TaskStatus_Mapping)
                    .HasForeignKey(d => d.KanbanDetailId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Kanban_TaskStatus_Mapping_KanbanDetailModel");

                entity.HasOne(d => d.TaskStatus)
                    .WithMany(p => p.Kanban_TaskStatus_Mapping)
                    .HasForeignKey(d => d.TaskStatusId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Kanban_TaskStatus_Mapping_TaskStatusModel");
            });

            modelBuilder.Entity<KiemKeEquipmentMdModel>(entity =>
            {
                entity.Property(e => e.KiemKeEquipmentMdModelId).ValueGeneratedNever();
            });

            modelBuilder.Entity<LaborModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<LastRunCheckInOutModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<List>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Id })
                    .HasName("PK_HangFire_List");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_List_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<List1>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Id })
                    .HasName("PK_HangFire_List");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_List_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.Property(e => e.Id).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<LogApiModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<LogSAPModel>(entity =>
            {
                entity.HasIndex(e => e.CreateBy, "IX_LogSAPModel_CreateBy")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.CreateTime, "IX_LogSAPModel_CreateTime")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.Funtion, "IX_LogSAPModel_Function")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.Funtion, e.CreateTime }, "IX_LogSAPModel_Function_CreateTime")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Log_BC01Model>(entity =>
            {
                entity.Property(e => e.Log_BC01ID).ValueGeneratedNever();
            });

            modelBuilder.Entity<Log_BC18Model>(entity =>
            {
                entity.Property(e => e.Log_BC18ID).ValueGeneratedNever();
            });

            modelBuilder.Entity<MaiDaoHistoryModel>(entity =>
            {
                entity.HasKey(e => e.HistoryId)
                    .HasName("PK__MaiDaoHist__4D7B4ABD4AB4F01D");

                entity.Property(e => e.HistoryId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.MaiDao)
                    .WithMany(p => p.MaiDaoHistoryModel)
                    .HasForeignKey(d => d.MaiDaoId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MaiDaoHistory_MaiDao");
            });

            modelBuilder.Entity<MaiDaoModel>(entity =>
            {
                entity.HasKey(e => e.MaiDaoId)
                    .HasName("PK__MaiDaoModel__Id");

                entity.Property(e => e.MaiDaoId).HasDefaultValueSql("(newid())");

                entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<MailServerProviderModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<MarmModel>(entity =>
            {
                entity.Property(e => e.MarnId).ValueGeneratedNever();
            });

            modelBuilder.Entity<MaterialCardModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.WeightUnit).IsFixedLength();
            });

            modelBuilder.Entity<MaterialGroupModel>(entity =>
            {
                entity.HasKey(e => e.MaterialGroupId)
                    .HasName("PK_MaterialGroup");

                entity.Property(e => e.MaterialGroupId).ValueGeneratedNever();
            });

            modelBuilder.Entity<MaterialModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.ContainerRequirementCodeNavigation)
                    .WithMany(p => p.MaterialModel)
                    .HasForeignKey(d => d.ContainerRequirementCode)
                    .HasConstraintName("FK_MaterialModel_ContainerRequirementModel");

                entity.HasOne(d => d.LaborCodeNavigation)
                    .WithMany(p => p.MaterialModel)
                    .HasForeignKey(d => d.LaborCode)
                    .HasConstraintName("FK_MaterialModel_LaborModel");

                entity.HasOne(d => d.ProductHierarchyCodeNavigation)
                    .WithMany(p => p.MaterialModel)
                    .HasForeignKey(d => d.ProductHierarchyCode)
                    .HasConstraintName("FK_MaterialModel_ProductHierarchyModel");

                entity.HasOne(d => d.ProfitCenterCodeNavigation)
                    .WithMany(p => p.MaterialModel)
                    .HasForeignKey(d => d.ProfitCenterCode)
                    .HasConstraintName("FK_MaterialModel_ProfitCenterModel");

                entity.HasOne(d => d.TemperatureConditionCodeNavigation)
                    .WithMany(p => p.MaterialModel)
                    .HasForeignKey(d => d.TemperatureConditionCode)
                    .HasConstraintName("FK_MaterialModel_TemperatureConditionModel");
            });

            modelBuilder.Entity<MaterialReservationModel>(entity =>
            {
                entity.Property(e => e.ReservationId).ValueGeneratedNever();

                entity.HasOne(d => d.ReservationHeader)
                    .WithMany(p => p.MaterialReservationModel)
                    .HasForeignKey(d => d.ReservationHeaderId)
                    .HasConstraintName("FK_MaterialReservationModel_ReservationHeaderModel");
            });

            modelBuilder.Entity<MaterialTypeModel>(entity =>
            {
                entity.HasKey(e => e.MaterialTypeId)
                    .HasName("PK_MaterialType");

                entity.Property(e => e.MaterialTypeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<MemberOfExternalProfileTargetGroupModel>(entity =>
            {
                entity.Property(e => e.ExternalProfileTargetGroupId).ValueGeneratedNever();

                entity.HasOne(d => d.TargetGroup)
                    .WithMany(p => p.MemberOfExternalProfileTargetGroupModel)
                    .HasForeignKey(d => d.TargetGroupId)
                    .HasConstraintName("FK_MemberOfExternalProfileTargetGroupModel_TargetGroupModel");
            });

            modelBuilder.Entity<MemberOfTargetGroupModel>(entity =>
            {
                entity.HasKey(e => new { e.TargetGroupId, e.ProfileId })
                    .HasName("PK_MemberOfTagetGroupModel");

                entity.HasOne(d => d.TargetGroup)
                    .WithMany(p => p.MemberOfTargetGroupModel)
                    .HasForeignKey(d => d.TargetGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MemberOfTagetGroupModel_TargetGroupModel");
            });

            modelBuilder.Entity<MenuModel>(entity =>
            {
                entity.Property(e => e.MenuId).ValueGeneratedNever();

                entity.HasOne(d => d.Module)
                    .WithMany(p => p.MenuModel)
                    .HasForeignKey(d => d.ModuleId)
                    .HasConstraintName("FK_MenuModel_ModuleModel");
            });

            modelBuilder.Entity<MigoModel>(entity =>
            {
                entity.HasIndex(e => new { e.MBLNR, e.MJAHR, e.ZEILE }, "IX_MigoModel_MBLNR_MJAHR_ZEILE")
                    .HasFillFactor(90);

                entity.Property(e => e.MigoId).ValueGeneratedNever();
            });

            modelBuilder.Entity<MobileScreenModel>(entity =>
            {
                entity.Property(e => e.MobileScreenId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.Menu)
                    .WithMany(p => p.MobileScreenModel)
                    .HasForeignKey(d => d.MenuId)
                    .HasConstraintName("FK_MobileScreenModel_MenuModel");

                entity.HasMany(d => d.Function)
                    .WithMany(p => p.MobileScreen)
                    .UsingEntity<Dictionary<string, object>>(
                        "MobileScreenFunctionModel",
                        l => l.HasOne<FunctionModel>().WithMany().HasForeignKey("FunctionId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_MobileScreenFunctionModel_FunctionModel"),
                        r => r.HasOne<MobileScreenModel>().WithMany().HasForeignKey("MobileScreenId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_MobileScreenFunctionModel_MobileScreenModel"),
                        j =>
                        {
                            j.HasKey("MobileScreenId", "FunctionId");

                            j.ToTable("MobileScreenFunctionModel", "ghMasterData");

                            j.IndexerProperty<string>("FunctionId").HasMaxLength(50);
                        });
            });

            modelBuilder.Entity<MobileScreenPermissionModel>(entity =>
            {
                entity.HasKey(e => new { e.RolesId, e.MobileScreenId, e.FunctionId });

                entity.HasOne(d => d.Function)
                    .WithMany(p => p.MobileScreenPermissionModel)
                    .HasForeignKey(d => d.FunctionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MobileScreenPermissionModel_FunctionModel");

                entity.HasOne(d => d.MobileScreen)
                    .WithMany(p => p.MobileScreenPermissionModel)
                    .HasForeignKey(d => d.MobileScreenId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MobileScreenPermissionModel_MobileScreenModel");

                entity.HasOne(d => d.Roles)
                    .WithMany(p => p.MobileScreenPermissionModel)
                    .HasForeignKey(d => d.RolesId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MobileScreenPermissionModel_RolesModel");
            });

            modelBuilder.Entity<ModuleModel>(entity =>
            {
                entity.Property(e => e.ModuleId).ValueGeneratedNever();
            });

            modelBuilder.Entity<NFCCheckInOutModel>(entity =>
            {
                entity.Property(e => e.CheckInId).ValueGeneratedNever();
            });

            modelBuilder.Entity<NewsCategoryModel>(entity =>
            {
                entity.Property(e => e.NewsCategoryId).ValueGeneratedNever();

                entity.Property(e => e.NewsCategoryCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<NewsModel>(entity =>
            {
                entity.Property(e => e.NewsId).ValueGeneratedNever();

                entity.Property(e => e.NewsCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<News_Company_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.NewsId, e.CompanyId });
            });

            modelBuilder.Entity<NotificationAccountMappingModel>(entity =>
            {
                entity.HasKey(e => new { e.NotificationId, e.AccountId });

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.NotificationAccountMappingModel)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_NotificationAccountMappingModel_AccountModel");

                entity.HasOne(d => d.Notification)
                    .WithMany(p => p.NotificationAccountMappingModel)
                    .HasForeignKey(d => d.NotificationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NotificationAccountMappingModel_NotificationModel");
            });

            modelBuilder.Entity<NotificationModel>(entity =>
            {
                entity.Property(e => e.NotificationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<POTEXT_PR_SO_Model>(entity =>
            {
                entity.Property(e => e.POTEXTId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PageModel>(entity =>
            {
                entity.Property(e => e.PageId).ValueGeneratedNever();

                entity.HasOne(d => d.Menu)
                    .WithMany(p => p.PageModel)
                    .HasForeignKey(d => d.MenuId)
                    .HasConstraintName("FK_PageModel_MenuModel");

                entity.HasMany(d => d.Function)
                    .WithMany(p => p.Page)
                    .UsingEntity<Dictionary<string, object>>(
                        "PageFunctionModel",
                        l => l.HasOne<FunctionModel>().WithMany().HasForeignKey("FunctionId").HasConstraintName("FK_PageFunctionModel_FunctionModel"),
                        r => r.HasOne<PageModel>().WithMany().HasForeignKey("PageId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_PageFunctionModel_PageModel"),
                        j =>
                        {
                            j.HasKey("PageId", "FunctionId");

                            j.ToTable("PageFunctionModel", "pms");

                            j.IndexerProperty<string>("FunctionId").HasMaxLength(50);
                        });
            });

            modelBuilder.Entity<PagePermissionModel>(entity =>
            {
                entity.HasKey(e => new { e.RolesId, e.PageId, e.FunctionId });

                entity.HasOne(d => d.Function)
                    .WithMany(p => p.PagePermissionModel)
                    .HasForeignKey(d => d.FunctionId)
                    .HasConstraintName("FK_PagePermissionModel_FunctionModel");

                entity.HasOne(d => d.Page)
                    .WithMany(p => p.PagePermissionModel)
                    .HasForeignKey(d => d.PageId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PagePermissionModel_PageModel");

                entity.HasOne(d => d.Roles)
                    .WithMany(p => p.PagePermissionModel)
                    .HasForeignKey(d => d.RolesId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PagePermissionModel_RolesModel");
            });

            modelBuilder.Entity<Page_Module_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.ModuleId, e.PageId });
            });

            modelBuilder.Entity<PartnerModel>(entity =>
            {
                entity.Property(e => e.PartnerId).ValueGeneratedNever();

                entity.HasOne(d => d.PartnerProfile)
                    .WithMany(p => p.PartnerModelPartnerProfile)
                    .HasForeignKey(d => d.PartnerProfileId)
                    .HasConstraintName("FK_PartnerModel_ProfileModel1");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.PartnerModelProfile)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_PartnerModel_ProfileModel");
            });

            modelBuilder.Entity<PaymentMethodModel>(entity =>
            {
                entity.Property(e => e.PaymentMethodId).ValueGeneratedNever();

                entity.Property(e => e.PaymentMethodType).HasComment("0: Tiền mặt, 1: Chuyển khoản, 2: Trả góp");
            });

            modelBuilder.Entity<PaymentNationalBudgetModel>(entity =>
            {
                entity.HasKey(e => e.PaymentNationalId)
                    .HasName("PK_ConfigPaymentNational");

                entity.Property(e => e.PaymentNationalId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PeriodicallyCheckingModel>(entity =>
            {
                entity.Property(e => e.PeriodicallyCheckingId).ValueGeneratedNever();

                entity.HasMany(d => d.Product)
                    .WithMany(p => p.PeriodicallyChecking)
                    .UsingEntity<Dictionary<string, object>>(
                        "Product_PeriodicallyChecking_Mapping",
                        l => l.HasOne<ProductModel>().WithMany().HasForeignKey("ProductId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Product_PeriodicallyChecking_Mapping_ProductModel"),
                        r => r.HasOne<PeriodicallyCheckingModel>().WithMany().HasForeignKey("PeriodicallyCheckingId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Product_PeriodicallyChecking_Mapping_PeriodicallyCheckingModel"),
                        j =>
                        {
                            j.HasKey("PeriodicallyCheckingId", "ProductId");

                            j.ToTable("Product_PeriodicallyChecking_Mapping", "tSale");
                        });
            });

            modelBuilder.Entity<PersonInChargeDeletedModel>(entity =>
            {
                entity.Property(e => e.PersonInChargeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PersonInChargeModel>(entity =>
            {
                entity.Property(e => e.PersonInChargeId).ValueGeneratedNever();

                entity.Property(e => e.SalesEmployeeType).HasComment("1: NV kinh doanh, 2: NV sales admin");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.PersonInChargeModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_PersonInChargeModel_ProfileModel");

                entity.HasOne(d => d.SalesEmployeeCodeNavigation)
                    .WithMany(p => p.PersonInChargeModel)
                    .HasForeignKey(d => d.SalesEmployeeCode)
                    .HasConstraintName("FK_PersonInChargeModel_SalesEmployeeModel");
            });

            modelBuilder.Entity<PhysicsWorkShopModel>(entity =>
            {
                entity.Property(e => e.PhysicsWorkShopId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PlateFeeDetailModel>(entity =>
            {
                entity.Property(e => e.PlateFeeDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.PlateFee)
                    .WithMany(p => p.PlateFeeDetailModel)
                    .HasForeignKey(d => d.PlateFeeId)
                    .HasConstraintName("FK_PlateFeeDetailModel_PlateFeeModel");
            });

            modelBuilder.Entity<PlateFeeModel>(entity =>
            {
                entity.Property(e => e.PlateFeeId).ValueGeneratedNever();

                entity.HasMany(d => d.Product)
                    .WithMany(p => p.PlateFee)
                    .UsingEntity<Dictionary<string, object>>(
                        "Product_PlateFee_Mapping",
                        l => l.HasOne<ProductModel>().WithMany().HasForeignKey("ProductId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Product_PlateFee_Mapping_ProductModel"),
                        r => r.HasOne<PlateFeeModel>().WithMany().HasForeignKey("PlateFeeId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Product_PlateFee_Mapping_PlateFeeModel"),
                        j =>
                        {
                            j.HasKey("PlateFeeId", "ProductId");

                            j.ToTable("Product_PlateFee_Mapping", "tSale");
                        });
            });

            modelBuilder.Entity<PriceProductModel>(entity =>
            {
                entity.Property(e => e.PriceProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PriceProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PriceProductModel_ProductModel");

                entity.HasOne(d => d.Style)
                    .WithMany(p => p.PriceProductModel)
                    .HasForeignKey(d => d.StyleId)
                    .HasConstraintName("FK_PriceProductModel_StyleModel");
            });

            modelBuilder.Entity<ProductAttributeModel>(entity =>
            {
                entity.Property(e => e.ProductId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductBarcodeModel>(entity =>
            {
                entity.HasKey(e => e.ProductBarcodeId)
                    .HasName("PK__ProductB__B80D151C2182FD95");

                entity.Property(e => e.ProductBarcodeId).ValueGeneratedNever();

                entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<ProductHierarchyModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<ProductImageModel>(entity =>
            {
                entity.HasKey(e => e.ERPProductCode)
                    .HasName("PK_ProductImage");
            });

            modelBuilder.Entity<ProductModel>(entity =>
            {
                entity.HasIndex(e => e.ERPProductCode, "IX_ProductModel_ERPProductCode")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ERPProductCode, e.CompanyId }, "IX_ProductModel_ERPProductCode_CompanyId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ERPProductCode, e.ProductName }, "IX_ProductModel_ERPProductCode_ProductName")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ProductCode, "IX_ProductModel_ProductCode")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ProductId, "IX_ProductModel_ProductId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ERPProductCode, "IX_Product_ERPCode")
                    .HasFillFactor(90);

                entity.Property(e => e.ProductId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductTypeModel>(entity =>
            {
                entity.Property(e => e.ProductTypeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductVersionModel>(entity =>
            {
                entity.Property(e => e.ProductVersionId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductWarrantyModel>(entity =>
            {
                entity.Property(e => e.ProductWarrantyId).ValueGeneratedNever();

                entity.Property(e => e.ProductWarrantyCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductWarrantyModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductWarrantyModel_ProductModel");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.ProductWarrantyModel)
                    .HasForeignKey(d => d.ProfileId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductWarrantyModel_ProfileModel");

                entity.HasOne(d => d.Warranty)
                    .WithMany(p => p.ProductWarrantyModel)
                    .HasForeignKey(d => d.WarrantyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductWarrantyModel_WarrantyModel");
            });

            modelBuilder.Entity<ProductionComponent80Model>(entity =>
            {
                entity.HasIndex(e => new { e.AUFNR, e.WERKS }, "IX_ProductionComponent80Model_AUFNR_WERKS")
                    .HasFillFactor(90);

                entity.Property(e => e.ProductComponentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductionComponentModel>(entity =>
            {
                entity.HasIndex(e => new { e.XWAOK, e.MEINS }, "IX_ProductionComponentModel_XWAOK_MEINS")
                    .HasFillFactor(90);

                entity.Property(e => e.ProductComponentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductionOperation80Model>(entity =>
            {
                entity.Property(e => e.ProductOperationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductionOperationModel>(entity =>
            {
                entity.Property(e => e.ProductOperationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductionOrder80Model>(entity =>
            {
                entity.Property(e => e.ProductionOrderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProductionOrderModel>(entity =>
            {
                entity.HasIndex(e => e.AUFNR, "IX_ProductionOrderModel_AUFNR")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.AUFNR_MES, "IX_ProductionOrderModel_AUFNR_MES")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.DWERK, e.AUFNR, e.AUFNR_MES }, "IX_ProductionOrderModel_DWERK_AUFNR_AUFNR_MES")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.DWERK, e.DAUAT }, "IX_ProductionOrderModel_DWERK_DAUAT")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.LOEKZ, "IX_ProductionOrderModel_LOEKZ")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ZCLOSE, "IX_ProductionOrderModel_ZCLOSE")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ZZLSX, "IX_ProductionOrderModel_ZZLSX")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ZZLSX, e.DWERK, e.AUFNR_MES }, "IX_ProductionOrderModel_ZZLSX_DWERK_AUFNR_MES")
                    .HasFillFactor(90);

                entity.Property(e => e.ProductionOrderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileBAttributeModel>(entity =>
            {
                entity.Property(e => e.ProfileId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileCAttributeModel>(entity =>
            {
                entity.HasKey(e => e.ProfileId)
                    .HasName("PK_ProfileCAttributeMode");

                entity.Property(e => e.ProfileId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileCareerModel>(entity =>
            {
                entity.Property(e => e.ProfileCareerId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileConfigModel>(entity =>
            {
                entity.HasKey(e => new { e.ProfileCategoryCode, e.FieldCode });
            });

            modelBuilder.Entity<ProfileContactAttributeDeletedModel>(entity =>
            {
                entity.Property(e => e.ProfileId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileContactAttributeModel>(entity =>
            {
                entity.HasKey(e => e.ProfileId)
                    .HasName("PK_ProfileContactAttributeModel_1");

                entity.Property(e => e.ProfileId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileDeletedModel>(entity =>
            {
                entity.Property(e => e.ProfileId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileEmailDeletedModel>(entity =>
            {
                entity.Property(e => e.EmailId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileEmailModel>(entity =>
            {
                entity.Property(e => e.EmailId).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.ProfileEmailModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_ProfileEmailModel_ProfileModel");
            });

            modelBuilder.Entity<ProfileGroupModel>(entity =>
            {
                entity.Property(e => e.ProfileGroupId).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.ProfileGroupModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_ProfileGroupModel_ProfileModel");
            });

            modelBuilder.Entity<ProfileLevelModel>(entity =>
            {
                entity.HasKey(e => e.CustomerLevelId)
                    .HasName("PK_CustomerLevelModel_1");

                entity.Property(e => e.CustomerLevelId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfileModel>(entity =>
            {
                entity.HasIndex(e => e.ProfileForeignCode, "UQ_Profile_ProfileForeignCode")
                    .IsUnique()
                    .HasFilter("([ProfileForeignCode] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.Property(e => e.ProfileId).ValueGeneratedNever();

                entity.Property(e => e.CreateRequestTime).HasComment("Thời gian yêu cầu tạo khách ở ECC");

                entity.Property(e => e.ProfileCode).ValueGeneratedOnAdd();

                entity.Property(e => e.ReferenceProfileId).HasComment("Chủ Đầu Tư");

                entity.Property(e => e.ReferenceProfileId2).HasComment("Tư vấn & TK");

                entity.Property(e => e.isCreateRequest).HasComment("yêu cầu tạo khách ở ECC");
            });

            modelBuilder.Entity<ProfilePhoneDeletedModel>(entity =>
            {
                entity.Property(e => e.PhoneId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ProfilePhoneModel>(entity =>
            {
                entity.Property(e => e.PhoneId).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.ProfilePhoneModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_ProfilePhoneModel_ProfileModel");
            });

            modelBuilder.Entity<ProfileTypeModel>(entity =>
            {
                entity.Property(e => e.ProfileTypeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Profile_File_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.ProfileId, e.FileAttachmentId });

                entity.Property(e => e.Note).IsFixedLength();

                entity.HasOne(d => d.FileAttachment)
                    .WithMany(p => p.Profile_File_Mapping)
                    .HasForeignKey(d => d.FileAttachmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Profile_File_Mapping_FileAttachmentModel");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.Profile_File_Mapping)
                    .HasForeignKey(d => d.ProfileId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Profile_File_Mapping_ProfileModel");
            });

            modelBuilder.Entity<Profile_Opportunity_CompetitorModel>(entity =>
            {
                entity.Property(e => e.OpportunityCompetitorId).ValueGeneratedNever();

                entity.Property(e => e.SuccessfulBidder).HasComment("Trúng thầu");

                entity.HasOne(d => d.Competitor)
                    .WithMany(p => p.Profile_Opportunity_CompetitorModelCompetitor)
                    .HasForeignKey(d => d.CompetitorId)
                    .HasConstraintName("FK_Profile_Opportunity_CompetitorModel_ProfileModel_Competitor");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.Profile_Opportunity_CompetitorModelProfile)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_Profile_Opportunity_CompetitorModel_ProfileModel_Profile");
            });

            modelBuilder.Entity<Profile_Opportunity_InternalModel>(entity =>
            {
                entity.Property(e => e.OpportunityInternalId).ValueGeneratedNever();

                entity.HasOne(d => d.Internal)
                    .WithMany(p => p.Profile_Opportunity_InternalModelInternal)
                    .HasForeignKey(d => d.InternalId)
                    .HasConstraintName("FK_Profile_Opportunity_InternalModel_ProfileModel_Internal");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.Profile_Opportunity_InternalModelProfile)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_Profile_Opportunity_InternalModel_ProfileModel_Profile");
            });

            modelBuilder.Entity<Profile_Opportunity_MaterialModel>(entity =>
            {
                entity.Property(e => e.OpportunityMaterialId).ValueGeneratedNever();

                entity.Property(e => e.MaterialType).HasComment("1: Nội thất bàn giao");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.Profile_Opportunity_MaterialModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_Profile_Opportunity_MaterialModel_ProfileModel_Profile");
            });

            modelBuilder.Entity<Profile_Opportunity_PartnerModel>(entity =>
            {
                entity.Property(e => e.OpportunityPartnerId).ValueGeneratedNever();

                entity.Property(e => e.PartnerType).HasComment("1: Chủ đầu tư, 2: Thiết kế, 3: Tổng thầu, 4: Căn mẫu, 5: Đại trà");

                entity.HasOne(d => d.Partner)
                    .WithMany(p => p.Profile_Opportunity_PartnerModelPartner)
                    .HasForeignKey(d => d.PartnerId)
                    .HasConstraintName("FK_Profile_Opportunity_PartnerModel_ProfileModel_Partner");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.Profile_Opportunity_PartnerModelProfile)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_Profile_Opportunity_PartnerModel_ProfileModel_Profile");
            });

            modelBuilder.Entity<ProfitCenterModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<PrognosisModel>(entity =>
            {
                entity.Property(e => e.PrognosisId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PromotionByStoreModel>(entity =>
            {
                entity.HasKey(e => new { e.PromotionId, e.StoreId });

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.PromotionByStoreModel)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PromotionByStoreModel_StoreModel");
            });

            modelBuilder.Entity<PromotionGiftAccessoryModel>(entity =>
            {
                entity.HasKey(e => e.GiftMaterialId)
                    .HasName("PK_PromotionGiftMaterialModel");

                entity.Property(e => e.GiftMaterialId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PromotionModel>(entity =>
            {
                entity.Property(e => e.PromotionId).ValueGeneratedNever();

                entity.HasMany(d => d.Product)
                    .WithMany(p => p.PromotionNavigation)
                    .UsingEntity<Dictionary<string, object>>(
                        "PromotionProductModel",
                        l => l.HasOne<ProductModel>().WithMany().HasForeignKey("ProductId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_ProductInPromotion_ProductModel"),
                        r => r.HasOne<PromotionModel>().WithMany().HasForeignKey("PromotionId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_ProductInPromotion_PromotionModel"),
                        j =>
                        {
                            j.HasKey("PromotionId", "ProductId").HasName("PK_ProductInPromotion");

                            j.ToTable("PromotionProductModel", "tSale");
                        });
            });

            modelBuilder.Entity<PropertiesProductModel>(entity =>
            {
                entity.Property(e => e.PropertiesId).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PropertiesProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PropertiesProductModel_ProductModel");
            });

            modelBuilder.Entity<ProspectModel>(entity =>
            {
                entity.Property(e => e.ProspectId).ValueGeneratedNever();

                entity.Property(e => e.GeneratedCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<ProvinceModel>(entity =>
            {
                entity.HasIndex(e => e.ProvinceCode, "ProvinceCode_UNIQUE")
                    .IsUnique()
                    .HasFilter("([ProvinceCode] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.Property(e => e.ProvinceId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PurchaseOrderDetailModel>(entity =>
            {
                entity.HasIndex(e => e.PurchaseOrderDetailId, "IX_PurchaseOrderDetailModel_PurchaseOrderDetailId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.PurchaseOrderId, e.DelivCompl }, "IX_PurchaseOrderDetailModel_PurchaseOrderId_DelivCompl")
                    .HasFillFactor(90);

                entity.Property(e => e.PurchaseOrderDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.PurchaseOrderDetailModel)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .HasConstraintName("FK_PurchaseOrderDetailModel_PurchaseOrderMasterModel");
            });

            modelBuilder.Entity<PurchaseOrderMasterModel>(entity =>
            {
                entity.HasKey(e => e.PurchaseOrderId)
                    .HasName("PK_PurchaseOrderModel_1");

                entity.HasIndex(e => new { e.PurchaseOrderCode, e.Actived, e.ReleaseIndicator }, "IX_PurchaseOrderMasterModel_Main")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.PurchaseOrderCode, "IX_PurchaseOrderMasterModel_PurchaseOrderCode")
                    .HasFillFactor(90);

                entity.Property(e => e.PurchaseOrderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PurchaseOrderModel>(entity =>
            {
                entity.Property(e => e.PurchaseOrderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PurchaseRequisitionModel>(entity =>
            {
                entity.Property(e => e.PurchaseRequisitionId).ValueGeneratedNever();
            });

            modelBuilder.Entity<PushNotificationModel>(entity =>
            {
                entity.HasIndex(e => e.Status, "IX_PushNotificationModel_Status")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.Status, e.CreateTime }, "IX_PushNotificationModel_Status_CreateTime")
                    .HasFillFactor(90);

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.IsRead).HasDefaultValueSql("((0))");
            });

            modelBuilder.Entity<QualityControlDetailModel>(entity =>
            {
                entity.Property(e => e.QualityControlDetailId).ValueGeneratedNever();

                entity.Property(e => e.CheckingTimes).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<QualityControlInformationModel>(entity =>
            {
                entity.Property(e => e.Id).HasDefaultValueSql("(newid())");

                entity.Property(e => e.Code).ValueGeneratedOnAdd();

                entity.HasMany(d => d.WorkCenterCode)
                    .WithMany(p => p.QualityControlInformation)
                    .UsingEntity<Dictionary<string, object>>(
                        "QualityControlInformation_WorkCenter_Mapping",
                        l => l.HasOne<WorkCenterModel>().WithMany().HasForeignKey("WorkCenterCode").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_QualityControlInformation_WorkCenter_Mapping_WorkCenterModel"),
                        r => r.HasOne<QualityControlInformationModel>().WithMany().HasForeignKey("QualityControlInformationId").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_QualityControlInformation_WorkCenter_Mapping_QualityControlInformation_WorkCenter_Mapping1"),
                        j =>
                        {
                            j.HasKey("QualityControlInformationId", "WorkCenterCode");

                            j.ToTable("QualityControlInformation_WorkCenter_Mapping", "MES");

                            j.IndexerProperty<string>("WorkCenterCode").HasMaxLength(50);
                        });
            });

            modelBuilder.Entity<QualityControlModel>(entity =>
            {
                entity.HasKey(e => e.QualityControlId)
                    .HasName("PK_QualityControlModel_1");

                entity.HasIndex(e => new { e.CustomerReference, e.ProductAttribute }, "IX_QualityControlModel_CustomerRef_ProdAttr")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.SaleOrgCode, e.Status, e.ConfirmDate }, "IX_QualityControlModel_SaleOrg_Status_ConfirmDate")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.SaleOrgCode, e.WorkShopCode, e.Status, e.ConfirmDate }, "IX_QualityControlModel_SaleOrg_WorkShop_Status_ConfirmDate")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.Status, "IX_QualityControlModel_Status")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.Status, e.ProductCode, e.QualityChecker, e.CustomerReference }, "IX_QualityControlModel_Status_ProductCode_QualityChecker_CustomerReference")
                    .HasFillFactor(90);

                entity.Property(e => e.QualityControlId).ValueGeneratedNever();

                entity.Property(e => e.IsSyncToSap).HasDefaultValueSql("((0))");

                entity.Property(e => e.QualityControlCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<QualityControl_Error_File_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.FileAttachmentId, e.QuanlityControl_Error_Id });
            });

            modelBuilder.Entity<QualityControl_Error_Mapping>(entity =>
            {
                entity.HasKey(e => e.QuanlityControl_Error_Id)
                    .HasName("PK_QuanlityControl_Error_Mapping");

                entity.Property(e => e.QuanlityControl_Error_Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<QualityControl_FileAttachment_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.FileAttachmentId, e.QualityControlId });

                entity.HasOne(d => d.FileAttachment)
                    .WithMany(p => p.QualityControl_FileAttachment_Mapping)
                    .HasForeignKey(d => d.FileAttachmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QualityControl_FileAttachment_Mapping_FileAttachmentModel");

                entity.HasOne(d => d.QualityControl)
                    .WithMany(p => p.QualityControl_FileAttachment_Mapping)
                    .HasForeignKey(d => d.QualityControlId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QualityControl_FileAttachment_Mapping_QualityControlModel");
            });

            modelBuilder.Entity<QualityControl_QCInformation_File_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.FileAttachmentId, e.QualityControl_QCInformation_Id });
            });

            modelBuilder.Entity<QualityControl_QCInformation_Mapping>(entity =>
            {
                entity.Property(e => e.QualityControl_QCInformation_Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<QuestionBankModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.QuestionBankCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.QuestionBankModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_QuestionBankModel_AccountModel");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.QuestionBankModelDepartment)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_QuestionBankModel_CatalogModel1");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.QuestionBankModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_QuestionBankModel_AccountModel1");

                entity.HasOne(d => d.QuestionCategory)
                    .WithMany(p => p.QuestionBankModelQuestionCategory)
                    .HasForeignKey(d => d.QuestionCategoryId)
                    .HasConstraintName("FK_QuestionBankModel_CatalogModel");
            });

            modelBuilder.Entity<RatingModel>(entity =>
            {
                entity.Property(e => e.RatingId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RawMaterialCardManualModel>(entity =>
            {
                entity.Property(e => e.RawMaterialCardManualId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RawMaterialCardModel>(entity =>
            {
                entity.HasIndex(e => e.RawMaterialCardId, "IX_RawMaterialCardModel_RawMaterialCardId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.RawMaterialCardId, e.VendorCode }, "IX_RawMaterialCardModel_RawMaterialCardId_VendorCode")
                    .HasFillFactor(90);

                entity.Property(e => e.RawMaterialCardId).ValueGeneratedNever();

                entity.Property(e => e.RawMaterialCardCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<RawMaterialCard_SO_Mapping>(entity =>
            {
                entity.Property(e => e.RawMaterialCardId).ValueGeneratedNever();

                entity.HasOne(d => d.RawMaterialCard)
                    .WithOne(p => p.RawMaterialCard_SO_Mapping)
                    .HasForeignKey<RawMaterialCard_SO_Mapping>(d => d.RawMaterialCardId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RawMaterialCard_SO_Mapping_RawMaterialCardModel");
            });

            modelBuilder.Entity<RawMaterialCard_WBS_Mapping>(entity =>
            {
                entity.HasKey(e => e.RawMaterialCardId)
                    .HasName("PK_RawMaterialCard_WBS_Mapping_1");

                entity.Property(e => e.RawMaterialCardId).ValueGeneratedNever();

                entity.HasOne(d => d.RawMaterialCard)
                    .WithOne(p => p.RawMaterialCard_WBS_Mapping)
                    .HasForeignKey<RawMaterialCard_WBS_Mapping>(d => d.RawMaterialCardId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RawMaterialCard_WBS_Mapping_RawMaterialCardModel");
            });

            modelBuilder.Entity<RawMaterial_PurchaseOrderDetail_Mapping>(entity =>
            {
                entity.HasIndex(e => e.RawMaterialCardId, "IX_RawMaterial_PurchaseOrderDetail_Mapping_RawMaterialCardId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.RawMaterialCardId, e.PurchaseOrderDetailId }, "IX_RawMaterial_PurchaseOrderDetail_Mapping_RawMaterialCardId_PurchaseOrderDetailId")
                    .HasFillFactor(90);

                entity.Property(e => e.RawMaterialCardDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.PurchaseOrderDetail)
                    .WithMany(p => p.RawMaterial_PurchaseOrderDetail_Mapping)
                    .HasForeignKey(d => d.PurchaseOrderDetailId)
                    .HasConstraintName("FK_RawMaterial_PurchaseOrderDetail_Mapping_RawMaterialCardModel");

                entity.HasOne(d => d.RawMaterialCard)
                    .WithMany(p => p.RawMaterial_PurchaseOrderDetail_Mapping)
                    .HasForeignKey(d => d.RawMaterialCardId)
                    .HasConstraintName("FK_RawMaterial_PurchaseOrderDetail_Mapping_RawMaterialCardModel1");
            });

            modelBuilder.Entity<ReceiveInformationModel>(entity =>
            {
                entity.Property(e => e.ReceiveInfoId).ValueGeneratedNever();

                entity.HasOne(d => d.Sloc)
                    .WithMany(p => p.ReceiveInformationModel)
                    .HasForeignKey(d => d.SlocId)
                    .HasConstraintName("FK_ReceiveInformationModel_ReceiveInformationModel");
            });

            modelBuilder.Entity<RegisterReceiveNewsModel>(entity =>
            {
                entity.Property(e => e.RegisterReceiveNewsId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RemindTaskModel>(entity =>
            {
                entity.Property(e => e.TaskId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RequestEccEmailConfigModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<RequestReturnVendorDetailModel>(entity =>
            {
                entity.HasOne(d => d.RequestReturnVendorModel)
                    .WithMany(p => p.RequestReturnVendorDetailModel)
                    .HasForeignKey(d => d.RequestReturnVendorModelId)
                    .HasConstraintName("FK__RequestRe__Reque__6C98FCFF");
            });

            modelBuilder.Entity<RequestReturnVendorModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<ReservationHeaderModel>(entity =>
            {
                entity.Property(e => e.ReservationHeaderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ReservationModel>(entity =>
            {
                entity.HasIndex(e => new { e.RSNUM, e.RSPOS, e.BDART }, "IX_Reservation_NonNull")
                    .HasFilter("([RSNUM] IS NOT NULL AND [RSPOS] IS NOT NULL AND [BDART] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.RSNUM, e.RSPOS, e.BDART }, "IX_Reservation_RSNUM_RSPOS_BDART")
                    .HasFillFactor(90);

                entity.Property(e => e.ReservationId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RoleInChargeDeletedModel>(entity =>
            {
                entity.Property(e => e.RoleInChargeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RoleInChargeModel>(entity =>
            {
                entity.Property(e => e.RoleInChargeId).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.RoleInChargeModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_RoleInChargeModel_ProfileModel");

                entity.HasOne(d => d.Roles)
                    .WithMany(p => p.RoleInChargeModel)
                    .HasForeignKey(d => d.RolesId)
                    .HasConstraintName("FK_RoleInChargeModel_RolesModel");
            });

            modelBuilder.Entity<RolesModel>(entity =>
            {
                entity.HasKey(e => e.RolesId)
                    .HasName("PK__RolesMod__C4B278410A3A22E7")
                    .IsClustered(false);

                entity.Property(e => e.RolesId).HasDefaultValueSql("(newsequentialid())");
            });

            modelBuilder.Entity<RoutingInventorModel>(entity =>
            {
                entity.HasIndex(e => new { e.MATNR, e.WERKS, e.BMEIN }, "IX_RoutingInventor_CurrentVersion")
                    .HasFilter("([VERSO] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.MATNR, e.ITMNO, e.WERKS, e.BMEIN }, "IX_RoutingInventor_Lookup")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.MATNR, e.ITMNO }, "IX_RoutingInventor_MATNR_ITMNO")
                    .HasFilter("([VERSO]=N'1')")
                    .HasFillFactor(90);

                entity.Property(e => e.RoutingInventorId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RoutingModel>(entity =>
            {
                entity.Property(e => e.StepId).ValueGeneratedNever();
            });

            modelBuilder.Entity<RoutingSapModel>(entity =>
            {
                entity.Property(e => e.RoutingSapId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SMSModel>(entity =>
            {
                entity.Property(e => e.SMSId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SO100ScheduleLineModel>(entity =>
            {
                entity.HasIndex(e => e.VBELN, "IX_SO100ScheduleLineModel_VBELN")
                    .HasFillFactor(90);

                entity.Property(e => e.SO100ScheduleLineId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SO100TextModel>(entity =>
            {
                entity.Property(e => e.SO100TextId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SO80ScheduleLineModel>(entity =>
            {
                entity.Property(e => e.SO80ScheduleLineId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SO80TextModel>(entity =>
            {
                entity.Property(e => e.SO80TextId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SOTEXT_PR_Model>(entity =>
            {
                entity.HasKey(e => new { e.WERKS, e.BANFN, e.BNFPO, e.ZMES_VBELN, e.MATNR });
            });

            modelBuilder.Entity<SOTextHeader100Model>(entity =>
            {
                entity.Property(e => e.SOTextHeader100Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SOTextHeader80Model>(entity =>
            {
                entity.Property(e => e.SOTextHeader80Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SOTextItem100Model>(entity =>
            {
                entity.Property(e => e.SOTextItem100Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SOTextItem80Model>(entity =>
            {
                entity.Property(e => e.SOTextItem80Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SQLProfilerModel>(entity =>
            {
                entity.HasKey(e => e.RowNumber)
                    .HasName("PK__SQLProfi__AAAC09D8070B5D27");
            });

            modelBuilder.Entity<SaleOrderDetailModel>(entity =>
            {
                entity.HasKey(e => e.SaleOrderDetailId)
                    .HasName("PK_SaleOrderDetailModel_1");

                entity.Property(e => e.SaleOrderDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.SaleOrderMaster)
                    .WithMany(p => p.SaleOrderDetailModel)
                    .HasForeignKey(d => d.SaleOrderMasterId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_SaleOrderDetailModel_SaleOrderMasterModel");
            });

            modelBuilder.Entity<SaleOrderHeader100Model>(entity =>
            {
                entity.HasIndex(e => e.VBELN, "IX_SaleOrderHeader100Model_VBELN")
                    .HasFillFactor(90);

                entity.Property(e => e.SO100HeaderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SaleOrderHeader80Model>(entity =>
            {
                entity.HasKey(e => e.SOHeaderId)
                    .HasName("PK_SaleOrderHeaderModel");

                entity.Property(e => e.SOHeaderId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SaleOrderItem100Model>(entity =>
            {
                entity.HasIndex(e => e.UPMAT, "IX_SaleOrderItem100Model_UPMAT")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.VBELN, e.POSNR }, "IX_SaleOrderItem100Model_VBELN_POSNR")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.VBELN, e.POSNR, e.UPMAT }, "IX_SaleOrderItem100Model_VBELN_POSNR_UPMAT")
                    .HasFillFactor(90);

                entity.Property(e => e.SO100ItemId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SaleOrderItem80Model>(entity =>
            {
                entity.HasKey(e => e.SOItemId)
                    .HasName("PK_SaleOrderItemModel");

                entity.Property(e => e.SOItemId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SaleOrderMasterModel>(entity =>
            {
                entity.HasKey(e => e.SaleOrderMasterId)
                    .HasName("PK_SaleOrderMaster");

                entity.Property(e => e.SaleOrderMasterId).ValueGeneratedNever();

                entity.Property(e => e.GeneratedCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SaleOrderMasterModel)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SaleOrderMasterModel_CustomerModel");

                entity.HasOne(d => d.MaterialCodeNavigation)
                    .WithMany(p => p.SaleOrderMasterModel)
                    .HasForeignKey(d => d.MaterialCode)
                    .HasConstraintName("FK_SaleOrderMasterModel_MaterialModel");
            });

            modelBuilder.Entity<SaleProcessModel>(entity =>
            {
                entity.Property(e => e.SaleProcessId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SaleUnitModel>(entity =>
            {
                entity.Property(e => e.SaleUnitId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SalesEmployeeInfoModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");

                entity.Property(e => e.CreateTime).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<SalesEmployeeLevelModel>(entity =>
            {
                entity.HasKey(e => e.LevelCode)
                    .HasName("PK_EmployeeLevel");

                entity.Property(e => e.LevelCode).ValueGeneratedNever();
            });

            modelBuilder.Entity<SalesEmployeeModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.SalesEmployeeModel)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_SalesEmployeeModel_DepartmentModel");
            });

            modelBuilder.Entity<SampMethodModel>(entity =>
            {
                entity.Property(e => e.SampMethodId).ValueGeneratedNever();
            });

            modelBuilder.Entity<Schema>(entity =>
            {
                entity.HasKey(e => e.Version)
                    .HasName("PK_HangFire_Schema");

                entity.Property(e => e.Version).ValueGeneratedNever();
            });

            modelBuilder.Entity<Schema1>(entity =>
            {
                entity.HasKey(e => e.Version)
                    .HasName("PK_HangFire_Schema");

                entity.Property(e => e.Version).ValueGeneratedNever();
            });

            modelBuilder.Entity<SearchResultDetailTemplateModel>(entity =>
            {
                entity.Property(e => e.SearchResultDetailTemplateId).ValueGeneratedNever();

                entity.HasOne(d => d.SearchResultTemplate)
                    .WithMany(p => p.SearchResultDetailTemplateModel)
                    .HasForeignKey(d => d.SearchResultTemplateId)
                    .HasConstraintName("FK_SearchResultDetailTemplateModel_SearchResultTemplateModel");
            });

            modelBuilder.Entity<SearchResultTemplateModel>(entity =>
            {
                entity.Property(e => e.SearchResultTemplateId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SearchTemplateModel>(entity =>
            {
                entity.Property(e => e.SearchTemplateId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SendMailCalendarModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.CheckinTime).HasComment("Thời gian checkin");

                entity.Property(e => e.ConfirmTime).HasComment("Thời gian xác nhận");

                entity.Property(e => e.isCheckin).HasComment("Đã check in");

                entity.Property(e => e.isConfirm).HasComment("Đã xác nhận");
            });

            modelBuilder.Entity<Server>(entity =>
            {
                entity.HasIndex(e => e.LastHeartbeat, "IX_HangFire_Server_LastHeartbeat")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Server1>(entity =>
            {
                entity.HasIndex(e => e.LastHeartbeat, "IX_HangFire_Server_LastHeartbeat")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<ServiceAppointmentModel>(entity =>
            {
                entity.Property(e => e.AppointmentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ServiceFlagModel>(entity =>
            {
                entity.Property(e => e.ServiceFlagId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ServiceOrderConsultModel>(entity =>
            {
                entity.Property(e => e.ServiceOrderConsultId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.ServiceOrder)
                    .WithMany(p => p.ServiceOrderConsultModel)
                    .HasForeignKey(d => d.ServiceOrderId)
                    .HasConstraintName("FK_ServiceOrderConsultModel_ServiceOrderModel");
            });

            modelBuilder.Entity<ServiceOrderDetailAccessoryModel>(entity =>
            {
                entity.HasKey(e => e.ServiceOrderDetailAccessoryId)
                    .HasName("PK_ServiceOrderDetailAccessoryMode");

                entity.Property(e => e.ServiceOrderDetailAccessoryId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.ServiceOrder)
                    .WithMany(p => p.ServiceOrderDetailAccessoryModel)
                    .HasForeignKey(d => d.ServiceOrderId)
                    .HasConstraintName("FK_ServiceOrderDetailAccessoryModel_ServiceOrderModel");
            });

            modelBuilder.Entity<ServiceOrderDetailModel>(entity =>
            {
                entity.Property(e => e.ServiceOrderDetailId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ServiceOrderDetailServiceModel>(entity =>
            {
                entity.Property(e => e.ServiceOrderDetailServiceId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.ServiceOrder)
                    .WithMany(p => p.ServiceOrderDetailServiceModel)
                    .HasForeignKey(d => d.ServiceOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceOrderDetailServiceModel_ServiceOrderModel");

                entity.HasOne(d => d.ServiceTypeCodeNavigation)
                    .WithMany(p => p.ServiceOrderDetailServiceModel)
                    .HasForeignKey(d => d.ServiceTypeCode)
                    .HasConstraintName("FK_ServiceOrderDetailServiceModel_ServiceTypeModel");
            });

            modelBuilder.Entity<ServiceOrderModel>(entity =>
            {
                entity.Property(e => e.ServiceOrderId).HasDefaultValueSql("(newid())");

                entity.Property(e => e.GeneratedCode).ValueGeneratedOnAdd();

                entity.Property(e => e.IsNew).HasDefaultValueSql("((0))");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.ServiceOrderModel)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_ServiceOrderModel_CustomerModel");

                entity.HasOne(d => d.SaleOrderMaster)
                    .WithMany(p => p.ServiceOrderModel)
                    .HasForeignKey(d => d.SaleOrderMasterId)
                    .HasConstraintName("FK_ServiceOrderModel_SaleOrderMasterModel");

                entity.HasOne(d => d.Vehicle)
                    .WithMany(p => p.ServiceOrderModel)
                    .HasForeignKey(d => d.VehicleId)
                    .HasConstraintName("FK_ServiceOrderModel_VehicleInfoModel");
            });

            modelBuilder.Entity<ServiceOrderPoolModel>(entity =>
            {
                entity.Property(e => e.ServiceOrderPoolId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ServiceOrderTypeModel>(entity =>
            {
                entity.HasKey(e => e.ServiceOrderTypeCode)
                    .HasName("PK_ServiceCategoryModel_1");
            });

            modelBuilder.Entity<ServiceTypeModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<Set>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Value })
                    .HasName("PK_HangFire_Set");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Set_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.Key, e.Score }, "IX_HangFire_Set_Score")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<Set1>(entity =>
            {
                entity.HasKey(e => new { e.Key, e.Value })
                    .HasName("PK_HangFire_Set");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Set_ExpireAt")
                    .HasFilter("([ExpireAt] IS NOT NULL)")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.Key, e.Score }, "IX_HangFire_Set_Score")
                    .HasFillFactor(90);
            });

            modelBuilder.Entity<SettingSyncSAPModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SlocModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SoToKhaiModel>(entity =>
            {
                entity.HasKey(e => e.SoToKhaiId)
                    .HasName("PK__SoToKhai__9D70B51E99BC5BC4");
            });

            modelBuilder.Entity<SourceModel>(entity =>
            {
                entity.Property(e => e.SourceId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SpecificationsModel>(entity =>
            {
                entity.Property(e => e.SpecificationsId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SpecificationsProductModel>(entity =>
            {
                entity.Property(e => e.SpecificationsProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SpecificationsProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SpecificationsProductModel_ProductModel");

                entity.HasOne(d => d.Specifications)
                    .WithMany(p => p.SpecificationsProductModel)
                    .HasForeignKey(d => d.SpecificationsId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SpecificationsProductModel_SpecificationsModel");
            });

            modelBuilder.Entity<State>(entity =>
            {
                entity.HasKey(e => new { e.JobId, e.Id })
                    .HasName("PK_HangFire_State");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.State)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_State_Job");
            });

            modelBuilder.Entity<State1>(entity =>
            {
                entity.HasKey(e => new { e.JobId, e.Id })
                    .HasName("PK_HangFire_State");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.State1)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_State_Job");
            });

            modelBuilder.Entity<StateTreasuryModel>(entity =>
            {
                entity.Property(e => e.StateTreasuryId).ValueGeneratedNever();
            });

            modelBuilder.Entity<StatusTransitionModel>(entity =>
            {
                entity.Property(e => e.StatusTransitionId).ValueGeneratedNever();

                entity.HasOne(d => d.FromStatus)
                    .WithMany(p => p.StatusTransitionModelFromStatus)
                    .HasForeignKey(d => d.FromStatusId)
                    .HasConstraintName("FK_StatusTransitionModel_TaskStatusModel");

                entity.HasOne(d => d.ToStatus)
                    .WithMany(p => p.StatusTransitionModelToStatus)
                    .HasForeignKey(d => d.ToStatusId)
                    .HasConstraintName("FK_StatusTransitionModel_TaskStatusModel1");

                entity.HasOne(d => d.WorkFlow)
                    .WithMany(p => p.StatusTransitionModel)
                    .HasForeignKey(d => d.WorkFlowId)
                    .HasConstraintName("FK_StatusTransitionModel_WorkFlowModel");
            });

            modelBuilder.Entity<StatusTransition_Task_Mapping>(entity =>
            {
                entity.HasKey(e => e.TaskTransitionLogId)
                    .HasName("PK_TaskTransitionLogModel");

                entity.Property(e => e.TaskTransitionLogId).ValueGeneratedNever();
            });

            modelBuilder.Entity<StockModel>(entity =>
            {
                entity.HasIndex(e => e.StockCode, "IX_StockModel_StockCode")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.StockId, "IX_Stock_StockId")
                    .HasFillFactor(90);

                entity.Property(e => e.StockId).ValueGeneratedNever();
            });

            modelBuilder.Entity<StockReceivingDetailModel>(entity =>
            {
                entity.HasKey(e => e.StockReceivingDetailId)
                    .HasName("PK_StockRecevingDetailModel");

                entity.HasIndex(e => new { e.ProductAttributes, e.StockRecevingType }, "IX_StockReceivingDetailModel_Performance")
                    .HasFilter("([StockRecevingType]=N'D')")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.CustomerReference, e.MovementType }, "IX_StockReceivingDetail_CustomerRef")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ProductAttributes, e.StockRecevingType, e.CustomerReference }, "IX_StockReceivingDetail_Lookup")
                    .HasFilter("([StockRecevingType]='D')")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ProductAttributes, e.StockRecevingType, e.ProductId, e.StockId }, "IX_StockReceivingDetail_QueryOptimized")
                    .HasFilter("([StockRecevingType]='D')")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.isSendToSAP, "idx_isSendToSAP")
                    .HasFillFactor(90);

                entity.Property(e => e.StockReceivingDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.CustomerReferenceNavigation)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.CustomerReference)
                    .HasConstraintName("FK_StockReceivingDetailModel_ThucThiLenhSanXuatModel");

                entity.HasOne(d => d.DateKeyNavigation)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.DateKey)
                    .HasConstraintName("FK_StockRecevingDetailModel_DimDate");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_StockReceivingDetailModel_DepartmentModel");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockRecevingDetailModel_ProductModel");

                entity.HasOne(d => d.Stock)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.StockId)
                    .HasConstraintName("FK_StockRecevingDetailModel_StockModel");

                entity.HasOne(d => d.StockReceiving)
                    .WithMany(p => p.StockReceivingDetailModel)
                    .HasForeignKey(d => d.StockReceivingId)
                    .HasConstraintName("FK_StockRecevingDetailModel_StockReceivingMasterModel");
            });

            modelBuilder.Entity<StockReceivingMasterModel>(entity =>
            {
                entity.Property(e => e.StockReceivingId).ValueGeneratedNever();

                entity.Property(e => e.StockReceivingCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockReceivingMasterModel)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockReceivingMasterModel_CompanyModel");

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.StockReceivingMasterModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_StockReceivingMasterModel_ProfileModel");

                entity.HasOne(d => d.SalesEmployeeCodeNavigation)
                    .WithMany(p => p.StockReceivingMasterModel)
                    .HasForeignKey(d => d.SalesEmployeeCode)
                    .HasConstraintName("FK_StockReceivingMasterModel_SalesEmployeeModel");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StockReceivingMasterModel)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_StockReceivingMasterModel_StoreModel");
            });

            modelBuilder.Entity<StockTransferRequestDetailModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockTransferRequestDetailModel)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockTransferRequestDetailModel_ProductModel");

                entity.HasOne(d => d.StockTransferRequest)
                    .WithMany(p => p.StockTransferRequestDetailModel)
                    .HasForeignKey(d => d.StockTransferRequestId)
                    .HasConstraintName("FK_StockTransferRequestDetailModel_StockTransferRequestModel");
            });

            modelBuilder.Entity<StockTransferRequestModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.StockTransferRequestCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockTransferRequestModel)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockTransferRequestModel_CompanyModel");

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.StockTransferRequestModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_StockTransferRequestModel_AccountModel");

                entity.HasOne(d => d.DeletedByNavigation)
                    .WithMany(p => p.StockTransferRequestModelDeletedByNavigation)
                    .HasForeignKey(d => d.DeletedBy)
                    .HasConstraintName("FK_StockTransferRequestModel_AccountModel2");

                entity.HasOne(d => d.FromStockNavigation)
                    .WithMany(p => p.StockTransferRequestModelFromStockNavigation)
                    .HasForeignKey(d => d.FromStock)
                    .HasConstraintName("FK_StockTransferRequestModel_StockModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.StockTransferRequestModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_StockTransferRequestModel_AccountModel1");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.StockTransferRequestModel)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_StockTransferRequestModel_StoreModel");

                entity.HasOne(d => d.ToStockNavigation)
                    .WithMany(p => p.StockTransferRequestModelToStockNavigation)
                    .HasForeignKey(d => d.ToStock)
                    .HasConstraintName("FK_StockTransferRequestModel_StockModel1");
            });

            modelBuilder.Entity<Stock_Store_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.StockId, e.StoreId });

                entity.HasOne(d => d.Stock)
                    .WithMany(p => p.Stock_Store_Mapping)
                    .HasForeignKey(d => d.StockId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Stock_Store_Mapping_StockModel");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.Stock_Store_Mapping)
                    .HasForeignKey(d => d.StoreId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Stock_Store_Mapping_StoreModel");
            });

            modelBuilder.Entity<StorageBinModel>(entity =>
            {
                entity.Property(e => e.StorageBinId).ValueGeneratedNever();
            });

            modelBuilder.Entity<StoreModel>(entity =>
            {
                entity.Property(e => e.StoreId).ValueGeneratedNever();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StoreModel)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StoreModel_CompanyModel");
            });

            modelBuilder.Entity<StoreTypeModel>(entity =>
            {
                entity.HasKey(e => e.StoreTypeId)
                    .HasName("PK_StoreTypeMode");

                entity.Property(e => e.StoreTypeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<StyleModel>(entity =>
            {
                entity.Property(e => e.StyleId).ValueGeneratedNever();
            });

            modelBuilder.Entity<SupplierModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<SyncActionModel>(entity =>
            {
                entity.HasKey(e => e.SyncActionId)
                    .HasName("PK__SyncActi__65DF61289813FB4D");

                entity.HasIndex(e => e.AccountId, "IDX_SyncActionModel_AccountId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.CreateTime, "IDX_SyncActionModel_CreateTime")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.SyncMethod, "IDX_SyncActionModel_SyncMethod")
                    .HasFillFactor(90);

                entity.Property(e => e.CreateTime).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<TDSModel>(entity =>
            {
                entity.HasKey(e => e.TdsCode)
                    .HasName("PK__TDSModel__359EBBADBC215E23");

                entity.Property(e => e.TdsCode).ValueGeneratedNever();

                entity.Property(e => e.CreatedTime).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.LastEditTime).HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<TargetGroupModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.TargetGroupCode).ValueGeneratedOnAdd();

                entity.Property(e => e.Type).HasComment("Marketing|Event");

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.TargetGroupModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TargetGroupModel_AccountModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.TargetGroupModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_TargetGroupModel_AccountModel1");
            });

            modelBuilder.Entity<TaskAssignModel>(entity =>
            {
                entity.Property(e => e.TaskAssignId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskCommentModel>(entity =>
            {
                entity.Property(e => e.TaskCommentId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskContactModel>(entity =>
            {
                entity.Property(e => e.TaskContactId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskGroupDetailModel>(entity =>
            {
                entity.HasKey(e => new { e.GroupId, e.AccountId });
            });

            modelBuilder.Entity<TaskGroupModel>(entity =>
            {
                entity.Property(e => e.GroupId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskModel>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.TaskId }, "IX_TaskModel_CompanyId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.CreateBy, e.CreateTime }, "IX_TaskModel_CreateBy_CreateTime")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ParentTaskId, "IX_TaskModel_ParentTaskId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.ProductId, "IX_TaskModel_ProductId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.TaskId, "IX_TaskModel_TaskId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.WorkFlowId, "IX_TaskModel_WorkFlowId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.WorkFlowId, e.ProductId }, "IX_TaskModel_WorkFlowId_ProductId")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.CreateTime, "index_v_CreateTime")
                    .HasFillFactor(90);

                entity.Property(e => e.TaskId).ValueGeneratedNever();

                entity.Property(e => e.ConstructionUnit).HasComment("đơn vị thi công");

                entity.Property(e => e.ConstructionUnitContact).HasComment("liên hệ của đơn vị thi công");

                entity.Property(e => e.Property6).HasComment("Giá trị ĐTB");

                entity.Property(e => e.ShortNote).HasComment("Ghi chú ngắn dùng để edit trực tiếp trên lịch");

                entity.Property(e => e.TaskCode).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<TaskProductAccessoryModel>(entity =>
            {
                entity.Property(e => e.TaskProductAccessoryId).ValueGeneratedNever();

                entity.HasOne(d => d.TaskProduct)
                    .WithMany(p => p.TaskProductAccessoryModel)
                    .HasForeignKey(d => d.TaskProductId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_TaskProductAccessoryModel_TaskProductModel");
            });

            modelBuilder.Entity<TaskProductModel>(entity =>
            {
                entity.Property(e => e.TaskProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Task)
                    .WithMany(p => p.TaskProductModel)
                    .HasForeignKey(d => d.TaskId)
                    .HasConstraintName("FK_TaskProductModel_TaskModel");
            });

            modelBuilder.Entity<TaskProductUsualErrorModel>(entity =>
            {
                entity.Property(e => e.TaskProductUsualErrorId).ValueGeneratedNever();

                entity.HasOne(d => d.TaskProduct)
                    .WithMany(p => p.TaskProductUsualErrorModel)
                    .HasForeignKey(d => d.TaskProductId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_TaskProductUsualErrorModel_TaskProductModel");
            });

            modelBuilder.Entity<TaskReferenceModel>(entity =>
            {
                entity.Property(e => e.TaskReferenceId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskReporterModel>(entity =>
            {
                entity.Property(e => e.TaskReporterId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskRoleInChargeModel>(entity =>
            {
                entity.Property(e => e.RoleInChargeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TaskStatusModel>(entity =>
            {
                entity.Property(e => e.TaskStatusId).ValueGeneratedNever();

                entity.HasOne(d => d.WorkFlow)
                    .WithMany(p => p.TaskStatusModel)
                    .HasForeignKey(d => d.WorkFlowId)
                    .HasConstraintName("FK_TaskStatusModel_WorkFlowModel");
            });

            modelBuilder.Entity<Task_File_Mapping>(entity =>
            {
                entity.HasKey(e => new { e.TaskId, e.FileAttachmentId });

                entity.HasOne(d => d.FileAttachment)
                    .WithMany(p => p.Task_File_Mapping)
                    .HasForeignKey(d => d.FileAttachmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Task_File_Mapping_FileAttachmentModel");

                entity.HasOne(d => d.Task)
                    .WithMany(p => p.Task_File_Mapping)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Task_File_Mapping_TaskModel");
            });

            modelBuilder.Entity<TaxConfigModel>(entity =>
            {
                entity.Property(e => e.TaxId).ValueGeneratedNever();
            });

            modelBuilder.Entity<TemperatureConditionModel>(entity =>
            {
                entity.Property(e => e.Actived).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<TemplateAndGiftCampaignModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.TemplateAndGiftCampaignCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.TemplateAndGiftCampaignModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_TemplateAndGiftCampaignModel_AccountModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.TemplateAndGiftCampaignModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_TemplateAndGiftCampaignModel_AccountModel1");

                entity.HasOne(d => d.TemplateAndGiftTargetGroup)
                    .WithMany(p => p.TemplateAndGiftCampaignModel)
                    .HasForeignKey(d => d.TemplateAndGiftTargetGroupId)
                    .HasConstraintName("FK_TemplateAndGiftCampaignModel_TemplateAndGiftTargetGroupModel");
            });

            modelBuilder.Entity<TemplateAndGiftMemberAddressModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.TemplateAndGiftMemberAddressModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_TemplateAndGiftMemberAddressModel_AccountModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.TemplateAndGiftMemberAddressModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_TemplateAndGiftMemberAddressModel_AccountModel1");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.TemplateAndGiftMemberAddressModel)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_TemplateAndGiftMemberAddressModel_ProductModel");

                entity.HasOne(d => d.TempalteAndGiftMember)
                    .WithMany(p => p.TemplateAndGiftMemberAddressModel)
                    .HasForeignKey(d => d.TempalteAndGiftMemberId)
                    .HasConstraintName("FK_TemplateAndGiftMemberAddressModel_TemplateAndGiftMemberModel");
            });

            modelBuilder.Entity<TemplateAndGiftMemberModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.HasOne(d => d.Profile)
                    .WithMany(p => p.TemplateAndGiftMemberModel)
                    .HasForeignKey(d => d.ProfileId)
                    .HasConstraintName("FK_TemplateAndGiftMemberModel_ProfileModel");

                entity.HasOne(d => d.TemplateAndGiftTargetGroup)
                    .WithMany(p => p.TemplateAndGiftMemberModel)
                    .HasForeignKey(d => d.TemplateAndGiftTargetGroupId)
                    .HasConstraintName("FK_TemplateAndGiftMemberModel_TemplateAndGiftTargetGroupModel");
            });

            modelBuilder.Entity<TemplateAndGiftTargetGroupModel>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.TargetGroupCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.CreateByNavigation)
                    .WithMany(p => p.TemplateAndGiftTargetGroupModelCreateByNavigation)
                    .HasForeignKey(d => d.CreateBy)
                    .HasConstraintName("FK_TemplateAndGiftTargetGroupModel_AccountModel");

                entity.HasOne(d => d.LastEditByNavigation)
                    .WithMany(p => p.TemplateAndGiftTargetGroupModelLastEditByNavigation)
                    .HasForeignKey(d => d.LastEditBy)
                    .HasConstraintName("FK_TemplateAndGiftTargetGroupModel_AccountModel1");
            });

            modelBuilder.Entity<ThucThiLenhSanXuatModel>(entity =>
            {
                entity.HasKey(e => e.TaskId)
                    .HasName("PK_ThucThiLenhSanXuat");

                entity.HasIndex(e => e.Barcode, "IX_ThucThiLenhSanXuatModel_Barcode")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.CreateTime, e.ParentTaskId, e.ProductId, e.CreateBy }, "IX_ThucThiLenhSanXuatModel_CreateTime")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.TaskId, e.ParentTaskId }, "IX_ThucThiLenhSanXuat_Lookup")
                    .HasFillFactor(90);

                entity.Property(e => e.TaskId).ValueGeneratedNever();

                entity.HasOne(d => d.ParentTask)
                    .WithMany(p => p.ThucThiLenhSanXuatModel)
                    .HasForeignKey(d => d.ParentTaskId)
                    .HasConstraintName("FK_ThucThiLenhSanXuatModel_TaskModel");
            });

            modelBuilder.Entity<TransferDetailModel>(entity =>
            {
                entity.Property(e => e.TransferDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.DateKeyNavigation)
                    .WithMany(p => p.TransferDetailModel)
                    .HasForeignKey(d => d.DateKey)
                    .HasConstraintName("FK_TransferDetailModel_DimDate");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.TransferDetailModel)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_TransferDetailModel_ProductModel");

                entity.HasOne(d => d.ToStock)
                    .WithMany(p => p.TransferDetailModel)
                    .HasForeignKey(d => d.ToStockId)
                    .HasConstraintName("FK_TransferDetailModel_StockModel");
            });

            modelBuilder.Entity<TransferModel>(entity =>
            {
                entity.Property(e => e.TransferId).ValueGeneratedNever();

                entity.Property(e => e.TransferCode).ValueGeneratedOnAdd();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TransferModel)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TransferModel_CompanyModel");

                entity.HasOne(d => d.SalesEmployeeCodeNavigation)
                    .WithMany(p => p.TransferModel)
                    .HasForeignKey(d => d.SalesEmployeeCode)
                    .HasConstraintName("FK_TransferModel_SalesEmployeeModel");

                entity.HasOne(d => d.Store)
                    .WithMany(p => p.TransferModel)
                    .HasForeignKey(d => d.StoreId)
                    .HasConstraintName("FK_TransferModel_StoreModel");
            });

            modelBuilder.Entity<TyLeTieuHaoModel>(entity =>
            {
                entity.HasKey(e => e.TyLeTieuHaoId)
                    .HasName("PK__TyLeTieu__Id");

                entity.HasIndex(e => e.LSXSAP, "IX_TyLeTieuHaoModel_LSXSAP")
                    .HasFillFactor(90);

                entity.HasIndex(e => e.MaSanPham, "IX_TyLeTieuHaoModel_MaSanPham")
                    .HasFillFactor(90);

                entity.Property(e => e.TyLeTieuHaoId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<Unfollow>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<VIEW_TonKhoTheo_SO_CongDoan>(entity =>
            {
                entity.ToView("VIEW_TonKhoTheo_SO_CongDoan", "MES");
            });

            modelBuilder.Entity<VehicleInfoModel>(entity =>
            {
                entity.HasKey(e => e.VehicleId)
                    .HasName("PK_VehicleInfoModel_1");

                entity.Property(e => e.VehicleId).HasDefaultValueSql("(newid())");
            });

            modelBuilder.Entity<VendorModel>(entity =>
            {
                entity.HasIndex(e => e.SupplierNumber, "IX_VendorModel_SupplierNumber")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.SupplierNumber, e.VendorId }, "IX_VendorModel_SupplierNumber_VendorId")
                    .HasFillFactor(90);

                entity.Property(e => e.VendorId).ValueGeneratedNever();
            });

            modelBuilder.Entity<View_BOM_Inventor_Rip>(entity =>
            {
                entity.ToView("View_BOM_Inventor_Rip", "MES");
            });

            modelBuilder.Entity<View_Catalog_Category>(entity =>
            {
                entity.ToView("View_Catalog_Category");
            });

            modelBuilder.Entity<View_FaceCheckIn>(entity =>
            {
                entity.ToView("View_FaceCheckIn", "Task");
            });

            modelBuilder.Entity<View_FaceCheckOut>(entity =>
            {
                entity.ToView("View_FaceCheckOut", "Task");
            });

            modelBuilder.Entity<View_MES_StockReceiving>(entity =>
            {
                entity.ToView("View_MES_StockReceiving");
            });

            modelBuilder.Entity<View_PriorityModel>(entity =>
            {
                entity.ToView("View_PriorityModel");
            });

            modelBuilder.Entity<View_Product_Detail>(entity =>
            {
                entity.ToView("View_Product_Detail", "MES");
            });

            modelBuilder.Entity<View_Product_Detail2>(entity =>
            {
                entity.ToView("View_Product_Detail2", "MES");
            });

            modelBuilder.Entity<View_Product_Material>(entity =>
            {
                entity.ToView("View_Product_Material", "MES");
            });

            modelBuilder.Entity<View_ProfileDeleted_ContactPhone>(entity =>
            {
                entity.ToView("View_ProfileDeleted_ContactPhone", "Customer");
            });

            modelBuilder.Entity<View_Profile_Address>(entity =>
            {
                entity.ToView("View_Profile_Address", "Customer");
            });

            modelBuilder.Entity<View_Profile_Company_Mapping>(entity =>
            {
                entity.ToView("View_Profile_Company_Mapping", "Customer");
            });

            modelBuilder.Entity<View_Profile_ContactPhone>(entity =>
            {
                entity.ToView("View_Profile_ContactPhone", "Customer");
            });

            modelBuilder.Entity<View_Profile_ExtendInfo>(entity =>
            {
                entity.ToView("View_Profile_ExtendInfo", "Customer");
            });

            modelBuilder.Entity<View_Profile_MainContact>(entity =>
            {
                entity.ToView("View_Profile_MainContact", "Customer");
            });

            modelBuilder.Entity<View_Profile_ProfilePhone>(entity =>
            {
                entity.ToView("View_Profile_ProfilePhone", "Customer");
            });

            modelBuilder.Entity<View_SO_Active>(entity =>
            {
                entity.ToView("View_SO_Active", "MES");
            });

            modelBuilder.Entity<View_Stock_Delivery>(entity =>
            {
                entity.ToView("View_Stock_Delivery", "Warehouse");
            });

            modelBuilder.Entity<View_Stock_Receive>(entity =>
            {
                entity.ToView("View_Stock_Receive", "Warehouse");
            });

            modelBuilder.Entity<View_Stock_TransferFrom_Delivery>(entity =>
            {
                entity.ToView("View_Stock_TransferFrom_Delivery", "Warehouse");
            });

            modelBuilder.Entity<View_Stock_TransferTo_Receive>(entity =>
            {
                entity.ToView("View_Stock_TransferTo_Receive", "Warehouse");
            });

            modelBuilder.Entity<View_Task_Area>(entity =>
            {
                entity.ToView("View_Task_Area", "Customer");
            });

            modelBuilder.Entity<View_Task_GTB>(entity =>
            {
                entity.ToView("View_Task_GTB", "Task");
            });

            modelBuilder.Entity<WardModel>(entity =>
            {
                entity.Property(e => e.WardId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WarehouseExportDetailModel>(entity =>
            {
                entity.HasKey(e => new { e.WarhouseExportId, e.SOWBS })
                    .HasName("PK__Warehous__8FF0B014919781D6");

                entity.HasOne(d => d.WarhouseExport)
                    .WithMany(p => p.WarehouseExportDetailModel)
                    .HasForeignKey(d => d.WarhouseExportId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK__Warehouse__Warho__2878DCDC");
            });

            modelBuilder.Entity<WarehouseExportModel>(entity =>
            {
                entity.Property(e => e.WarhouseExportId).ValueGeneratedNever();

                entity.HasOne(d => d.Sloc)
                    .WithMany(p => p.WarehouseExportModel)
                    .HasForeignKey(d => d.SlocId)
                    .HasConstraintName("FK_WarehouseExportModel_SlocModel");

                entity.HasOne(d => d.StorageBin)
                    .WithMany(p => p.WarehouseExportModel)
                    .HasForeignKey(d => d.StorageBinId)
                    .HasConstraintName("FK_WarehouseExportModel_StorageBinModel");
            });

            modelBuilder.Entity<WarehouseModel>(entity =>
            {
                entity.Property(e => e.WarehouseId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WarehouseProductModel>(entity =>
            {
                entity.Property(e => e.WarehouseProductId).ValueGeneratedNever();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.WarehouseProductModel)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseProductModel_ProductModel");

                entity.HasOne(d => d.Style)
                    .WithMany(p => p.WarehouseProductModel)
                    .HasForeignKey(d => d.StyleId)
                    .HasConstraintName("FK_WarehouseProductModel_StyleModel");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.WarehouseProductModel)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_WarehouseProductModel_WarehouseModel");
            });

            modelBuilder.Entity<WarehouseTranferModel>(entity =>
            {
                entity.HasKey(e => e.WarehouseTranferId)
                    .HasName("PK_WarhouseTranferModel");

                entity.Property(e => e.WarehouseTranferId).ValueGeneratedNever();

                entity.HasOne(d => d.Reservation)
                    .WithMany(p => p.WarehouseTranferModel)
                    .HasForeignKey(d => d.ReservationId)
                    .HasConstraintName("FK_WarehouseTranferModel_MaterialReservationModel");

                entity.HasOne(d => d.SlocExport)
                    .WithMany(p => p.WarehouseTranferModelSlocExport)
                    .HasForeignKey(d => d.SlocExportId)
                    .HasConstraintName("FK_WarhouseTranferModel_SlocModel");

                entity.HasOne(d => d.SlocImport)
                    .WithMany(p => p.WarehouseTranferModelSlocImport)
                    .HasForeignKey(d => d.SlocImportId)
                    .HasConstraintName("FK_WarhouseTranferModel_SlocModel1");

                entity.HasOne(d => d.StorageBinExport)
                    .WithMany(p => p.WarehouseTranferModelStorageBinExport)
                    .HasForeignKey(d => d.StorageBinExportId)
                    .HasConstraintName("FK_WarhouseTranferModel_StorageBinModel");

                entity.HasOne(d => d.StorageBinImport)
                    .WithMany(p => p.WarehouseTranferModelStorageBinImport)
                    .HasForeignKey(d => d.StorageBinImportId)
                    .HasConstraintName("FK_WarhouseTranferModel_StorageBinModel1");
            });

            modelBuilder.Entity<WarehouseTransactionModel>(entity =>
            {
                entity.HasIndex(e => e.ReferenceDocumentId, "IX_WarehouseTransactionModel_ReferenceDocumentId")
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.ReferenceDocumentId, e.WarhouseTransactionId }, "IX_WarehouseTransactionModel_ReferenceDocumentId_WarhouseTransactionId")
                    .HasFillFactor(90);

                entity.Property(e => e.WarhouseTransactionId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WarrantyModel>(entity =>
            {
                entity.Property(e => e.WarrantyId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WorkFlowConfigModel>(entity =>
            {
                entity.HasKey(e => new { e.WorkFlowId, e.FieldCode });
            });

            modelBuilder.Entity<WorkFlowModel>(entity =>
            {
                entity.HasIndex(e => e.WorkFlowCode, "IX_WorkFlowModel_WorkFlowCode")
                    .IsUnique()
                    .HasFillFactor(90);

                entity.HasIndex(e => new { e.WorkflowCategoryCode, e.CompanyCode }, "IX_WorkFlowModel_WorkflowCategoryCode_CompanyCode")
                    .HasFillFactor(90);

                entity.Property(e => e.WorkFlowId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WorkShopModel>(entity =>
            {
                entity.HasKey(e => e.WorkShopId)
                    .HasName("PK_WorkShop");

                entity.Property(e => e.WorkShopId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WorkingDateModel>(entity =>
            {
                entity.HasKey(e => e.WorkingDateId)
                    .HasName("PK_WorkingDateModel_1");

                entity.Property(e => e.WorkingDateId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WorkingTimeConfigModel>(entity =>
            {
                entity.Property(e => e.WorkingTimeConfigId).ValueGeneratedNever();
            });

            modelBuilder.Entity<WorkingTimeDetailModel>(entity =>
            {
                entity.Property(e => e.WorkingTimeDetailId).ValueGeneratedNever();

                entity.HasOne(d => d.WorkingTime)
                    .WithMany(p => p.WorkingTimeDetailModel)
                    .HasForeignKey(d => d.WorkingTimeId)
                    .HasConstraintName("FK_WorkingTimeDetailModel_WorkingTimeModel");
            });

            modelBuilder.Entity<WorkingTimeModel>(entity =>
            {
                entity.Property(e => e.WorkingTimeId).ValueGeneratedNever();
            });

            modelBuilder.Entity<ZTB_PPR02>(entity =>
            {
                entity.HasKey(e => new { e.Dot_san_xuat, e.SD_Document, e.Component_trong_BOM })
                    .HasName("PK_ProductionBatchDetails");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}