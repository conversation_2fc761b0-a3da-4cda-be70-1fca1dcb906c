import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/switchingStagesApi.dart';
import '../screenArguments/screenArgumentSwitchingStage.dart';

class ButtonSwitchingStages extends StatelessWidget {
  final DataSwitchingStages? switchingStateData;
  final String token;
  final String dateTimeOld;
  final SwitchingStages? switchingStages;
  final VoidCallback refersh;
  final String toBarcode;

  const ButtonSwitchingStages(
      {Key? key,
      required this.switchingStateData,
      required this.token,
      required this.dateTimeOld,
      required this.switchingStages,
      required this.refersh,
      required this.toBarcode})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(),
      child: ElevatedButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Color(0xff0052cc)))),
          side: MaterialStateProperty.all(
            const BorderSide(
              color: Color(0xff0052cc),
            ),
          ),
          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
        ),
        onPressed: () async {
          var data = await Navigator.pushNamed(context, "/SwitchingStage",
              arguments: ScreenArgumentSwitchingStage(switchingStateData, token, switchingStages, dateTimeOld, toBarcode));
          if (data == null) return;
          if (data == true) {
            refersh();
          }
        },
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 5.h),
          child: Text(
            'Chuyển công đoạn',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
          ),
        ),
      ),
    );
  }
}
