import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/maintenanceOrderModel.dart';
import '../../urlApi/urlApi.dart';
import '../../utils/status_utils.dart';

class MaintenanceOrderFunction {
  // static const bool useMockData = true;
  static const bool useMockData = false;

  static const String _baseEndpoint = '/api/v1/MES/';
  static const String _maintenanceOrderEndpoint = '${_baseEndpoint}MaintenanceOrder/';

  static Future<String> _getBaseUrl() async {
    final environment = await SecureStorage.getString("environment", null);
    return environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
  }

  static Future<MaintenanceOrderModel?> fetchMaintenanceOrderList(
    String token,
    MaintenanceOrderSearchModel searchModel,
  ) async {
    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1));

      // Apply filters to allRecords
      var filteredRecords = allRecords.where((record) {
        // Company Code filter
        if (searchModel.companyCode.isNotEmpty && record.plant != searchModel.companyCode) {
          return false;
        }

        // Order Number filter
        if (searchModel.orderNumber != null &&
            searchModel.orderNumber!.isNotEmpty &&
            !record.orderNumber!.toLowerCase().contains(searchModel.orderNumber!.toLowerCase())) {
          return false;
        }

        // Status filter
        if (searchModel.status != null && searchModel.status!.isNotEmpty) {
          if (record.status != searchModel.status) {
            return false;
          }
        }

        // Date range filter
        if (searchModel.fromDate != null || searchModel.toDate != null) {
          final recordDate = DateTime.parse(record.creationDate!);

          if (searchModel.fromDate != null) {
            final fromDate = DateTime(
              searchModel.fromDate!.year,
              searchModel.fromDate!.month,
              searchModel.fromDate!.day,
            );
            if (recordDate.isBefore(fromDate)) {
              return false;
            }
          }

          if (searchModel.toDate != null) {
            final toDate = DateTime(
              searchModel.toDate!.year,
              searchModel.toDate!.month,
              searchModel.toDate!.day,
            ).add(const Duration(days: 1)); // Include the entire end date

            if (recordDate.isAfter(toDate)) {
              return false;
            }
          }
        }

        return true;
      }).toList();

      // Apply pagination
      final startIndex = (searchModel.pageNumber - 1) * searchModel.pageSize;
      final endIndex = startIndex + searchModel.pageSize;

      filteredRecords = filteredRecords.sublist(
        startIndex.clamp(0, filteredRecords.length),
        endIndex.clamp(0, filteredRecords.length),
      );

      return MaintenanceOrderModel(
        data: filteredRecords,
        message: "Success",
        status: true,
        totalCount: filteredRecords.length,
        pageNumber: searchModel.pageNumber,
        pageSize: searchModel.pageSize,
        totalPages: (filteredRecords.length / searchModel.pageSize).ceil(),
      );
    }

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maintenanceOrderEndpoint}MaintenanceOrderList';

      debugPrint('Fetching maintenance orders with params: ${searchModel.toJson()}');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: jsonEncode(searchModel.toJson()),
      );

      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          final data = jsonResponse['data'];

          return MaintenanceOrderModel(
            data: (data['items'] as List).map((item) => MaintenanceOrder.fromJson(item)).toList(),
            message: jsonResponse['message'] ?? "Success",
            status: true,
            totalCount: data['totalCount'] ?? 0,
            pageNumber: data['pageNumber'] ?? searchModel.pageNumber,
            pageSize: data['pageSize'] ?? searchModel.pageSize,
            totalPages: data['totalPages'] ?? 1,
          );
        }
      }
      debugPrint('Failed to fetch maintenance orders');
      return null;
    } catch (e) {
      debugPrint('Error fetching maintenance order list: $e');
      return null;
    }
  }

  static Future<MaintenanceOrder?> fetchMaintenanceOrderDetail(
    String token,
    String id,
  ) async {
    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      return allRecords.firstWhere(
        (record) => record.orderNumber == id,
        orElse: () => MaintenanceOrder(),
      );
    }

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_maintenanceOrderEndpoint$id';

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return MaintenanceOrder.fromJson(jsonResponse['data']);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching maintenance order detail: $e');
      return null;
    }
  }

  static Future<List<String>> fetchWorkCenters(String token, String companyCode) async {
    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1));
      return ['PMTTFTCD | Maintenance Team TTF CD', 'PMTTFTXN | Maintenance Team TTF XN', 'PMTTFTVH | Maintenance Team TTF VH'];
    }

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maintenanceOrderEndpoint}workcenters/$companyCode';

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return List<String>.from(jsonResponse['data']);
        }
        throw Exception(jsonResponse['message'] ?? 'Failed to fetch workcenters');
      }
      throw Exception('Failed to fetch workcenters');
    } catch (e) {
      debugPrint('Error fetching workcenters: $e');
      return [];
    }
  }

  static Future<MaintenanceOrderResponse> createMaintenanceOrder(
    String token,
    CreateMaintenanceOrderModel model,
  ) async {
    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1));
      return MaintenanceOrderResponse(success: true, message: "Order created successfully", orderNumber: "20003295");
    }

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maintenanceOrderEndpoint}Create';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: jsonEncode(model.toJson()),
      );

      final jsonResponse = jsonDecode(response.body);
      debugPrint('Response body: ${jsonResponse}');
      final success = jsonResponse['isSuccess'] == true;
      final message = jsonResponse['message'] ?? '';

      // Trim leading zeros from order number if it exists
      final orderNumber = success && jsonResponse['data']?['orderNumber'] != null
          ? jsonResponse['data']['orderNumber'].toString().replaceFirst(RegExp(r'^0+'), '')
          : null;

      return MaintenanceOrderResponse(
        success: success,
        message: message,
        orderNumber: orderNumber,
      );
    } catch (e) {
      debugPrint('Error creating maintenance order: $e');
      return MaintenanceOrderResponse(
        success: false,
        message: e.toString(),
      );
    }
  }

  static Future<MaintenanceOrderResponse> closeMaintenanceOrder(
    String token,
    String orderNumber,
  ) async {
    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1));
      return MaintenanceOrderResponse(success: true, message: "Order closed successfully");
    }

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_maintenanceOrderEndpoint}Close/$orderNumber';

      final response = await http.post(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      final jsonResponse = jsonDecode(response.body);
      return MaintenanceOrderResponse(success: jsonResponse['isSuccess'] == true, message: jsonResponse['message'] ?? 'Unknown error occurred');
    } catch (e) {
      debugPrint('Error closing maintenance order: $e');
      return MaintenanceOrderResponse(success: false, message: e.toString());
    }
  }

  static Future<List<String>> fetchEquipment(String token, String companyCode, [String searchTerm = '']) async {
    debugPrint('Fetching equipment - Company Code: $companyCode, Search Term: $searchTerm');

    if (useMockData) {
      await Future.delayed(const Duration(seconds: 1));

      // Filter mock data based on search term
      final mockData = [
        '10000369 | HỆ THỐNG GHÉP DỌC TỰ ĐỘNG',
        '10000282 | THÁC SƠN NƯỚC 05.04',
        '40000010 | XE NÂNG ĐIỆN 1.5TẤN',
      ];

      if (searchTerm.isEmpty) return mockData;

      return mockData.where((item) => item.toLowerCase().contains(searchTerm.toLowerCase())).toList();
    }

    try {
      if (searchTerm.length < 3) {
        debugPrint('Search term too short (< 3 characters). Returning empty list.');
        return [];
      }

      final baseUrl = await _getBaseUrl();
      final url = Uri.parse('$baseUrl${_maintenanceOrderEndpoint}equipment/$companyCode').replace(
        queryParameters: {
          'searchTerm': searchTerm,
        },
      );

      debugPrint('Fetching equipment from URL: $url');

      final response = await http.get(
        url,
        headers: UrlApi.headersToken(token),
      );

      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return List<String>.from(jsonResponse['data']);
        }
        throw Exception(jsonResponse['message'] ?? 'Failed to fetch equipment');
      }
      throw Exception('Failed to fetch equipment');
    } catch (e) {
      debugPrint('Error fetching equipment: $e');
      return [];
    }
  }

  static final List<MaintenanceOrder> allRecords = [
    MaintenanceOrder(
      index: 1,
      orderNumber: "20003286",
      orderType: "M2",
      description: "Lệnh sửa chữa H THỐNG GHÉP DỌC TỰ ĐỘNG",
      creationDate: "20241029",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1042",
      costCenter: "1000C15",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000369",
      equipmentName: "HỆ THỐNG GHÉP DỌC TỰ ĐỘNG",
      status: "CREATED",
    ),
    MaintenanceOrder(
      index: 2,
      orderNumber: "20003268",
      orderType: "M2",
      description: "Lệnh sửa chữa THÁC SƠN NƯỚC 05.04",
      creationDate: "20241011",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1023",
      costCenter: "1000C07",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000282",
      equipmentName: "THÁC SƠN NƯỚC 05.04",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 3,
      orderNumber: "20003289",
      orderType: "M2",
      description: "Lệnh sửa chữa xe nâng điện 1.5 tấn ticke",
      creationDate: "20241104",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1023",
      costCenter: "1000C07",
      mainWorkCenter: "PMTTFTXN",
      equipmentNumber: "40000010",
      equipmentName: "XE NÂNG ĐIỆN 1.5TẤN",
      status: "IN_PROCESS",
    ),
    MaintenanceOrder(
      index: 4,
      orderNumber: "20003271",
      orderType: "M2",
      description: "LỆnh sửa chữa THÁC SƠN NƯỚC ticket 21601",
      creationDate: "20241015",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1023",
      costCenter: "1000C07",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000281",
      equipmentName: "THÁC SƠN NƯỚC 05.05",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 5,
      orderNumber: "20003272",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY DÁN CẠNH THẲNG ĐA CHỨC",
      creationDate: "20241016",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1020",
      costCenter: "1000C04",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000038",
      equipmentName: "MÁY DÁN CẠNH THẲNG ĐA CHỨC NĂNG",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 6,
      orderNumber: "20003276",
      orderType: "M2",
      description: "Lệnh sửa chữa Máy tạo vân gỗ Model 1000-",
      creationDate: "20241017",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1031",
      costCenter: "1000C13",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000978",
      equipmentName: "Máy tạo vân gỗ Model 1000-4G - HENGMAC",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 7,
      orderNumber: "20003263",
      orderType: "M2",
      description: "Lệnh sửa chữa XE NÂNG ĐIỆN 1.5TẤN ticket",
      creationDate: "20241002",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1020",
      costCenter: "1000C04",
      mainWorkCenter: "PMTTFTXN",
      equipmentNumber: "40000006",
      equipmentName: "XE NÂNG ĐIỆN 1.5TẤN",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 8,
      orderNumber: "20003290",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY NÉN KHÍ HITACHI ticket",
      creationDate: "20241104",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1042",
      costCenter: "1000C15",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000771",
      equipmentName: "MÁY NÉN KHÍ HITACHI",
      status: "CREATED",
    ),
    MaintenanceOrder(
      index: 9,
      orderNumber: "20003277",
      orderType: "M2",
      description: "Lệnh sửa chữa XE NÂNG DẦU 3.0TẤN ticket",
      creationDate: "20241021",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1021",
      costCenter: "1000C05",
      mainWorkCenter: "PMTTFTXN",
      equipmentNumber: "40000018",
      equipmentName: "XE NÂNG DẦU 3.0TẤN",
      status: "IN_PROCESS",
    ),
    MaintenanceOrder(
      index: 10,
      orderNumber: "20003287",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY CNC ROUTER 8 ĐẦU ticke",
      creationDate: "20241030",
      createdBy: "BT01",
      plant: "1200",
      locationPlant: "1021",
      costCenter: "1000C05",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000075",
      equipmentName: "MÁY CNC ROUTER 8 ĐẦU",
      status: "CREATED",
    ),
    MaintenanceOrder(
      index: 11,
      orderNumber: "20003293",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY DÁN CẠNH DÀY RỘNG 35MM",
      creationDate: "20241105",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1020",
      costCenter: "1000C04",
      mainWorkCenter: "PMTTFTVH",
      equipmentNumber: "10001271",
      equipmentName: "MÁY DÁN CẠNH DÀY RỘNG 35MM",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 12,
      orderNumber: "20003283",
      orderType: "M2",
      description: "LỆnh sửa chữa Máy router CNC K3 ticket 2",
      creationDate: "20241028",
      createdBy: "BT01",
      plant: "1200",
      locationPlant: "1022",
      costCenter: "1000C06",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000802",
      equipmentName: "Máy router CNC K3",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 13,
      orderNumber: "20003294",
      orderType: "M2",
      description: "LỆnh sửa chữa MÁY BÀO 2 MẶT ticket 22399",
      creationDate: "20241109",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1042",
      costCenter: "1000C15",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000370",
      equipmentName: "MÁY BÀO 2 MẶT",
      status: "CREATED",
    ),
    MaintenanceOrder(
      index: 14,
      orderNumber: "20003291",
      orderType: "M2",
      description: "Lệnh sửa chữa Máy cắt ván CNC MJ270 tick",
      creationDate: "20241105",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1021",
      costCenter: "1000C05",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000824",
      equipmentName: "Máy cắt ván CNC MJ270",
      status: "CREATED",
    ),
    MaintenanceOrder(
      index: 15,
      orderNumber: "20003284",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY GIA CÔNG CNC 5 AXIS ti",
      creationDate: "20241028",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1021",
      costCenter: "1000C05",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000074",
      equipmentName: "MÁY GIA CÔNG CNC 5 AXIS",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 16,
      orderNumber: "20003292",
      orderType: "M2",
      description: "Lệnh sửa chữa Máy khoan ngang CNC ticket",
      creationDate: "20241105",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1021",
      costCenter: "1000C05",
      mainWorkCenter: "PMTTFTVH",
      equipmentNumber: "10001302",
      equipmentName: "Máy khoan ngang CNC laser 2 trục Model M",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 17,
      orderNumber: "20003285",
      orderType: "M2",
      description: "Lệnh sửa chửa my bào bốn mặt ticket 219",
      creationDate: "20241028",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1022",
      costCenter: "1000C06",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000254",
      equipmentName: "BÀO BỐN MẶT",
      status: "COMPLETED",
    ),
    MaintenanceOrder(
      index: 18,
      orderNumber: "20003288",
      orderType: "M2",
      description: "Lệnh sửa chữa MÁY CHÀ NHÁM THÙNG 1.2M ti",
      creationDate: "20241031",
      createdBy: "BT01",
      plant: "1000",
      locationPlant: "1020",
      costCenter: "1000C04",
      mainWorkCenter: "PMTTFTCD",
      equipmentNumber: "10000231",
      equipmentName: "MÁY CHÀ NHÁM THÙNG 1.2M",
      status: "IN_PROCESS",
    ),
  ];
}
