﻿using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.Repositories.MES;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Warehouse;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using SAPGetLSXDT;
using SAPGetStock;
using SAPWarehouseReceive;
using SAPWarehouseTransaction;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    //[ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class WarehouseTransactionController : ControllerBaseAPI
    {
        private IHostEnvironment _env;

        public WarehouseTransactionController(IHostEnvironment env)
        {
            _env = env;
        }

        #region Danh sách chuyển kho

        /// <summary>Danh sách chuyển kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/ListWarehouseTranfer
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "plant": null,
        ///                "sloc": null,
        ///                "reservationCode": null,
        ///                "statusReservation": "1",
        ///                "productCode": null,
        ///                "statusWarehouse": "0"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                     "code": 200,
        ///                     "isSuccess": true,
        ///                     "message": "GET danh sách chuyển kho thành công.",
        ///                     "data": [
        ///                       {
        ///                         "plant": "1000",
        ///                         "storageLocation": "N010",
        ///                         "reservationNumber": "15241",
        ///                         "statusReservation": "Đã duyệt",
        ///                         "quantity": 1,
        ///                         "so": "",
        ///                         "wbs": "",
        ///                         "lsx": "",
        ///                         "riPlant": "1000",
        ///                         "riStorageLocation": "X005",
        ///                         "item": "1",
        ///                         "statusWarehouse": "Chờ xuất kho"
        ///                       }
        ///                     ]
        ///                }
        /// </remarks>
        [HttpPost("ListWarehouseTranfer")]
        public IActionResult POST([FromBody] ListTranferWarehouseRequest req)
        {
            try
            {
                //Danh sách kho và vị trí
                //var slocs = _context.SlocModel.AsNoTracking();
                //var storageBin = _context.StorageBinModel.AsNoTracking();
                //var products = _context.ProductModel.AsNoTracking();

                //Danh sách giao dịch kho
                var listMovementType = new List<string>
                {
                    MovementType.DeliverOneStep,
                    MovementType.DeliverTwoStep
                };

                //Danh sách chi tiết reservation theo điều kiện tìm kiếm: Plant, Sloc, ReservationCode, Material Number
                var queryTemp = from x in _context.MaterialReservationModel
                                join h in _context.ReservationHeaderModel on x.ReservationHeaderId equals h.ReservationHeaderId
                                where x.ItemDeleted != "X" &&
                                      listMovementType.Contains(h.MovementType) &&
                                      (!string.IsNullOrEmpty(req.SlocExport) ? x.StorageLocation == req.SlocExport : true) &&
                                      (!string.IsNullOrEmpty(req.SlocImport) ? x.RIStorageLocation == req.SlocImport : true) &&
                                      (!string.IsNullOrEmpty(req.ReservationCode) ? x.ReservationNumber == req.ReservationCode : true) &&
                                      (!string.IsNullOrEmpty(req.ProductCode) ? x.MaterialNumber == req.ProductCode : true) &&
                                      (!string.IsNullOrEmpty(req.Plant) ? x.Plant == req.Plant : true) &&
                                      (string.IsNullOrEmpty(req.StatusReservation) || (req.StatusReservation == StatusReservation.Approved ? h.IsApproved == true : h.IsApproved == false || h.IsApproved == null))
                                orderby x.ReservationNumber, x.ReservationItemNumber
                                select x;

                switch (req.StatusWarehouse)
                {
                    case StatusWarehouseTranfer.NotDelivered:
                        queryTemp = queryTemp.Where(x =>
                            x.ReservationHeader.IsApproved == true &&
                            !_context.WarehouseTranferModel.Any(e => e.ReservationId == x.ReservationId &&
                                                             e.SlocExportId.HasValue &&
                                                             e.SlocImportId.HasValue));
                        break;

                    case StatusWarehouseTranfer.Delivered:
                        queryTemp = queryTemp.Where(x =>
                            _context.WarehouseTranferModel.Any(e => e.ReservationId == x.ReservationId &&
                                                            e.SlocExportId.HasValue &&
                                                            e.SlocImportId.HasValue));
                        break;

                    default:
                        // Handle unexpected status if needed.
                        break;
                }

                //var total = queryTemp.Count();

                var query = queryTemp
                                       .Skip(req.Paging.start)
                                       .Take(req.Paging.length);

                //Danh sách sản phẩm
                var listProductId = query.Select(x => x.MaterialNumber).ToArray();
                var listReservaionId = query.Select(x => x.ReservationId).ToArray();

                var materialsDb = _context.ProductModel.Where(x => listProductId.Contains(x.ProductCode)).AsNoTracking();
                var warehouseTransferDb = _context.WarehouseTranferModel.Where(w => listReservaionId.Contains((Guid)w.ReservationId));

                var response = query.Select(e => new TranferListResponse
                {
                    ReservationId = e.ReservationId,
                    //Nhà máy xuất
                    Plant = e.Plant,
                    //Kho xuất
                    StorageLocation = e.StorageLocation,
                    //Mã NVL
                    ReservationNumber = e.ReservationNumber,
                    //Trạng thái reservation
                    StatusReservation = e.ReservationHeader.IsApproved == true ? "Đã duyệt" : "Chưa duyệt",
                    //Số lượng yêu cầu
                    Quantity = e.ReqQuantity,
                    Unit = e.MeasureUnit,
                    //Số SO
                    SO = e.SalesOrderNumber,
                    //Product code/name
                    ProductCodeAndName = $"{e.MaterialNumber}/{materialsDb.FirstOrDefault(x => x.ProductCode == e.MaterialNumber).ProductName}",
                    //WBS
                    WBS = e.WBSElement,
                    //Lệnh sản xuất
                    LSX = e.OrderNumber,
                    //Nhà máy nhập
                    RIPlant = e.RIPlant,
                    //Kho nhập
                    RIStorageLocation = e.RIStorageLocation,
                    //Số lô,
                    BatchNumber = e.BatchNumber,
                    //Reservation Item
                    Item = e.ReservationItemNumber,
                    //Đã chuyển kho: Reservation đã duyệt và đã có dữ liệu xuất kho & chuyển kho 
                    StatusWarehouse = warehouseTransferDb.Where(x => x.ReservationId == e.ReservationId && x.SlocExportId.HasValue && x.SlocImportId.HasValue).Any() ? MESP2Resource.DELIVERED : MESP2Resource.NOTDELIVERED,
                    //Loại giao dịch kho
                    MovementType = e.ReservationHeader.MovementType,
                    //Ngày yêu cầu
                    ReqDate = e.ReqComponentDate
                });


                var datatblModel = new DatatableViewModel()
                {
                    draw = req.Paging.draw,
                    start = req.Paging.start,
                    length = req.Paging.length,
                };

                int filteredResultsCount = 0;
                int totalResultsCount = 0;

                //var RecordsTotal = searchVm.Paging.length;
                var RecordsFiltered = req.Paging.length;

                //var res = CustomSearchRepository.CustomSearchFunc<TranferListResponse>(datatblModel, out filteredResultsCount, out totalResultsCount, response, "STT");

                var res = response.ToList();

                if (res.Any())
                {
                    int i = datatblModel.start;
                    foreach (var item in res)
                    {
                        i++;
                        item.STT = i;
                    }
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = res,
                    Message = string.Format(CommonResource.Msg_Succes, "GET danh sách chuyển kho"),
                    AdditionalData = new
                    {
                        draw = datatblModel.draw,
                        recordsTotal = totalResultsCount,
                        recordsFiltered = RecordsFiltered,
                    }
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Nhập kho

        #region Nhập kho tích hợp SAP
        /// <summary>API nhập kho tích hợp SAP</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/ReceiveIntegrationSAP
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "rawMaterialCardId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                "quantityImports": [
        ///                  {
        ///                    "so": "string",
        ///                    "soLine": "string",
        ///                    "wbs": "string",
        ///                    "quantity": 0,
        ///                    "unit": "string"
        ///                  }
        ///                ],
        ///                "slocId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                "storageBinId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///                "batch": "string"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                    "code": 200,
        ///                    "isSuccess": true,
        ///                    "message": "Nhập kho thành công.",
        ///                    "data": true,
        ///                    "additionalData": null
        ///               }
        /// </remarks>
        [HttpPost("ReceiveIntegrationSAP")]
        public async Task<IActionResult> ReceiveIntegrationSAP([FromBody] ReceiveIntegrationSAPViewModel vm)
        {
            try
            {

                var POs = vm.QuantityImports.Select(x => x.PO).Distinct().ToArray();
                var soToKhai = vm.SoToKhai;

                //check if first 2 characters of PO is 42 and vm.SoToKhai is null or empty or whitespace
                if (POs.Any(x => x.StartsWith("42")) && string.IsNullOrWhiteSpace(soToKhai))
                {
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng nhập số tờ khai cho NVL mua nhập khẩu!" });
                }

                var palletNVL = new RawMaterialCardModel();

                //Trường hợp quét thẻ treo hoặc nhập mã thẻ treo
                //1: Nhập mã 
                if (vm.RawMaterialCardId.All(char.IsDigit))
                {
                    palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardCode == int.Parse(vm.RawMaterialCardId));
                }
                //2: Qúet thẻ treo
                else
                {
                    palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == Guid.Parse(vm.RawMaterialCardId));
                }

                //Thẻ treo
                if (palletNVL == null)
                    return Ok(new ApiResponse { Code = 400, Message = string.Format(CommonResource.Msg_NotFound, "Thẻ treo") });

                var rawMaterialCardId = palletNVL.RawMaterialCardId;

                //Kiểm tra kho
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == vm.SlocId);
                if (sloc == null)
                    return Ok(new ApiResponse { Code = 400, Message = string.Format(CommonResource.Msg_NotFound, "Kho") });

                if (sloc.WarehouseNo == "100" && !vm.StorageBinId.HasValue)
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những Sloc có quản lý vị trí!" });

                //Kiểm tra vị trí kho
                var storageBin = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == vm.StorageBinId);

                var listWT = new List<WarehouseTransactionModel>();

                //Số lượng nhập kho theo SO/WBS
                if (vm.QuantityImports.Any())
                {
                    foreach (var item in vm.QuantityImports)
                    {
                        //Tạo thông tin nhập kho
                        var wT = CreateWarehouseReceive(vm, rawMaterialCardId);
                        //PO và POLine
                        wT.PO = item.PO;
                        wT.POLine = item.POLine;
                        //SO
                        wT.SO = item.SO;
                        //SO Line
                        wT.SOLine = item.SOLine == "0" ? null : item.SOLine;
                        //WBS
                        wT.WBS = item.WBS;
                        //Số lượng và đơn vị tính
                        wT.Quantity = item.Quantity;
                        wT.Unit = item.Unit;

                        //NVL
                        wT.ProductCode = palletNVL?.ProductCode;
                        listWT.Add(wT);
                    }
                }

                //Lưu danh sách thông tin nhập kho
                _context.WarehouseTransactionModel.AddRange(listWT);

                //Response to SAP
                var ZMES_FM_INF_TRANS = new ZMES_FM_INF_TRANS
                {
                    INF_TRAN = new ZST_MES_INF_TRANS_HEADER
                    {
                        //Với nhập kho chứng từ là thẻ treo
                        HEADER_ID = rawMaterialCardId.ToString(),
                        //Ngày chứng từ
                        BLDAT = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),
                        BUDAT_MKPF = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),

                        //Loại giao dịch kho
                        DOCUMENT_TYPE = StatusWarehouseSAP.NKMH,

                        TDS_NUMBER = vm.Tds,
                        UNLOAD_PT = vm.SoToKhai,

                        //Chi tiết nhập kho
                        DETAILS = listWT.Select(e => new ZST_MES_INF_TRANS
                        {
                            HEADER_ID = rawMaterialCardId.ToString(),
                            //ID
                            DOCUMENT_NUMBER = e.WarhouseTransactionId.ToString(),

                            //PO và POLine
                            BSTNR = e.PO,
                            EBELP = e.POLine,
                            //Nhà máy
                            WERKS = CurrentUser?.SaleOrg,
                            //Mã NVL,
                            MATNR = e.ProductCode,
                            //Số SO và SOLine
                            KDAUF = e.SO,
                            KDPOS = e.SOLine,
                            //WBS
                            PS_PSP_PNR = e.WBS,
                            //Số lượng và ĐVT
                            ERFMG = e.Quantity.HasValue ? e.Quantity.Value : 0,
                            ERFME = e.Unit,
                            //Lô
                            CHARG = e.Batch,
                            //Sloc
                            LGORT = sloc.Sloc,
                            //WarehouseNo
                            LGNUM = string.IsNullOrEmpty(sloc.WarehouseNo) ? null : sloc.WarehouseNo,
                            //Storage Bin
                            LGPLA = storageBin?.StorageBin,

                            // Update 1: add Storage Type
                            LGTYP = storageBin?.StorageType,
                        }).ToArray()
                    }
                };

                //Phiếu nhập kho
                string grNote = null;

#if DEBUG
                XmlSerializer xsSubmit = new XmlSerializer(typeof(ZMES_FM_INF_TRANS));
                var xml = "";

                using (var sww = new StringWriter())
                {
                    using (XmlWriter writer = XmlWriter.Create(sww))
                    {
                        xsSubmit.Serialize(writer, ZMES_FM_INF_TRANS);
                        xml = sww.ToString(); // Your XML
                    }
                }

                return Ok(new ApiResponse
                {
                    Code = 400,
                    IsSuccess = false,
                    Data = true,
                    Message = $"TEST"
                });
#endif

                //SEND SAP
                var jsonStringRequest = Newtonsoft.Json.JsonConvert.SerializeObject(ZMES_FM_INF_TRANS);
                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(ZMES_FM_INF_TRANS);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    //Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "NK",
                        FuntionnName = "Nhập kho",
                        RequestSAP = jsonStringRequest,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId
                    });

                    await _context.SaveChangesAsync();

                    //List response to SAP
                    if (resSAPs.Any())
                    {
                        //Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            //Danh sách msg lỗi
                            var msgErrArr = resSAPs.Select(x => x.MESSAGE).ToArray();
                            var msgError = msgErrArr.Any() ? string.Join(", ", msgErrArr) : null;
                            return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });
                        }

                        //Cập nhật trạng thái thành công và save dữ liệu nhập kho vào MES
                        foreach (var item in listWT)
                        {
                            //Flag send SAP
                            item.isSendToSAP = true;
                            item.SendToSAPTime = DateTime.Now;
                        }

                        //Cập nhật trạng thái nhập kho của thẻ treo
                        palletNVL.IsReceive = true;

                        //Check thông tin nhập kho SAP. Có thì cập nhật, chưa có thêm mới
                        //Phiếu nhập kho
                        var goodsReceivedNote = resSAPs.FirstOrDefault().MESSAGE_V1;
                        grNote = goodsReceivedNote;
                        //Năm nhập kho
                        var yearRecive = resSAPs.FirstOrDefault().MESSAGE_V2;

                        //var RawMaterialCardIdGuid = new Guid(vm.RawMaterialCardId);
                        var RawMaterialCardIdGuid = rawMaterialCardId;

                        var infoReceive = await _context.ReceiveInformationModel.FirstOrDefaultAsync(x => x.GoodsReceivedNote == goodsReceivedNote && x.YearReceive == yearRecive);
                        if (infoReceive is null)
                        {
                            //Thêm mới thông tin giao dịch kho
                            _context.ReceiveInformationModel.Add(new ReceiveInformationModel
                            {
                                //Id
                                ReceiveInfoId = Guid.NewGuid(),
                                //Số phiếu nhập kho
                                GoodsReceivedNote = goodsReceivedNote,
                                Plant = CurrentUser?.SaleOrg,
                                //Năm nhập kho
                                YearReceive = yearRecive,
                                //Kho nhập
                                SlocId = sloc?.Id,
                                //Ngày nhập kho
                                ReceiveDate = DateTime.Now,
                                //Loại NVL
                                MaterialType = vm.AutoBatch.MaterialType,
                                //NVL Nội địa/Nhập khẩu
                                ImportExportType = vm.AutoBatch.ImportExportType,
                                //Nhóm hàng
                                MaterialGroup = vm.AutoBatch.MaterialGroup,
                                //Tình trạng hàng hóa
                                MaterialStatus = vm.AutoBatch.MaterialStatus,
                                //NSXHSD
                                NSXHSD = vm.AutoBatch.NSXHSD,
                                //Loại da
                                SkinType = vm.AutoBatch.SkinType,
                                //Màu da
                                SkinColor = vm.AutoBatch.SkinColor,
                                //Mã kiện
                                CaseCode = vm.AutoBatch.CaseCode,
                                //Common
                                CreateTime = DateTime.Now,
                                CreateBy = CurrentUser?.AccountId,
                                Actived = true,
                                RawMaterialCardId = RawMaterialCardIdGuid,
                                TdsNumber = vm.Tds,
                                SoToKhai = vm.SoToKhai
                            });
                        }
                        else
                        {
                            //Kho nhập
                            infoReceive.SlocId = sloc?.Id;
                            //Ngày nhập kho
                            infoReceive.ReceiveDate = DateTime.Now;
                            infoReceive.Plant = CurrentUser?.SaleOrg;
                            //Loại NVL
                            infoReceive.MaterialType = vm.AutoBatch.MaterialType;
                            //NVL Nội địa/Nhập khẩu
                            infoReceive.ImportExportType = vm.AutoBatch.ImportExportType;
                            //Nhóm hàng
                            infoReceive.MaterialGroup = vm.AutoBatch.MaterialGroup;
                            //Tình trạng hàng hóa
                            infoReceive.MaterialStatus = vm.AutoBatch.MaterialStatus;
                            //NSXHSD
                            infoReceive.NSXHSD = vm.AutoBatch.NSXHSD;
                            //Loại da
                            infoReceive.SkinType = vm.AutoBatch.SkinType;
                            //Màu da
                            infoReceive.SkinColor = vm.AutoBatch.SkinColor;
                            //Mã kiện
                            infoReceive.CaseCode = vm.AutoBatch.CaseCode;
                            //Common
                            infoReceive.LastEditTime = DateTime.Now;
                            infoReceive.LastEditBy = CurrentUser?.AccountId;
                            infoReceive.RawMaterialCardId = RawMaterialCardIdGuid;
                            infoReceive.TdsNumber = vm.Tds;
                            infoReceive.SoToKhai = vm.SoToKhai;
                        }
                    }
                }
                catch (Exception)
                {
                    return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = $"Nhập kho thành công. Số phiếu nhập kho {grNote}"
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        private WarehouseTransactionModel CreateWarehouseReceive(ReceiveIntegrationSAPViewModel vm, Guid palletId)
        {
            //Date now
            var dateNow = DateTime.Now;
            var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

            return new WarehouseTransactionModel
            {
                WarhouseTransactionId = Guid.NewGuid(),
                //Thẻ treo
                ReferenceDocumentId = palletId,
                //Kho
                SlocId = vm.SlocId,
                //Vị trí kho
                StorageBinId = vm.StorageBinId,
                //Số lô
                Batch = vm.Batch,
                //Nhà máy
                Plant = CurrentUser?.SaleOrg,
                //Loại giao dịch kho
                MovementType = MovementType.Receive,
                //Ngày nhập kho
                DocumentDate = dateNow,
                DateKey = int.Parse(dateNowStr),
                //Common
                CreateTime = dateNow,
                CreateBy = CurrentUser?.AccountId,
                Actived = true,
                TdsNumber = vm.Tds,
                SoToKhai = vm.SoToKhai,
            };
        }

        #endregion

        #region Get số lượng đã giao
        /// <summary>Get số lượng theo PO/POLine</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetQuantityByPO
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              [
        ///                {
        ///                  "po": "**********",
        ///                  "poLine": "10"
        ///                }
        ///              ]
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                    "code": 200,
        ///                    "isSuccess": true,
        ///                    "data": true,
        ///                    "additionalData": null
        ///               }
        /// </remarks>
        [HttpPost("GetQuantityByPO")]
        public async Task<IActionResult> POST([FromBody] List<GetQuantityReceiveRequest> POPOLines)
        {
            try
            {
                var response = new List<GetQuantityByPOResponse>();

                if (POPOLines.Any())
                {
                    foreach (var item in POPOLines)
                    {
                        //Entity
                        var entity = new GetQuantityByPOResponse();
                        var poDetail = await _context.PurchaseOrderDetailModel.FirstOrDefaultAsync(x => x.PurchaseOrderCode == item.PO && x.POItem == item.POLine);
                        if (poDetail == null)
                            return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"PO {item.PO} / POLine {item.POLine}") });

                        //Call SAP get số lượng đã nhập
                        var querySAP = await _unitOfWork.SAPAPIRepository.SyncQuantityReceiveToSAP(new ZFM_MES_PURCHASE_ORDER
                        {
                            EBELN = item.PO,
                            EBELP = item.POLine,
                            WERKS = CurrentUser?.CompanyCode
                        });

                        //Số lượng đã giao
                        entity.QuantityReceived = querySAP.ZFM_MES_PURCHASE_ORDERResponse.LS_OUTPUT.LIST_DETAIL.Sum(x => x.CUMULATIVE_QUANTITY);

                        //Số lượng theo PO Line
                        entity.QuantityByPO = poDetail?.POQuantity;

                        //PO
                        entity.PO = item.PO;
                        //PO Line
                        entity.POLine = item.POLine;
                        //ĐVT
                        entity.Unit = poDetail.OrderUnit;

                        response.Add(entity);
                    }

                    response.OrderBy(x => x.PO).ThenBy(x => x.POLine);
                }

                return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Get số lượng, số lô đã nhập (Dành cho trường hợp barcode đã nhập kho)
        /// <summary>Get số lượng đã nhập kho (Dành cho trường hợp barcode đã nhập kho)</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetQuantityImported?RawMaterialCardId={}
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              [
        ///                {
        ///                  "quantityImported": 150
        ///                  "so": "**********",
        ///                  "soLine": "10",
        ///                  "wbs": null
        ///                }
        ///              ]
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                    "code": 200,
        ///                    "isSuccess": true,
        ///                    "data": true,
        ///                    "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetQuantityImported")]
        public async Task<IActionResult> GET([FromQuery] GetQuantityImportedRequest req)
        {
            try
            {
                var listQtyImported = new List<QuantityImportedResponse>();

                //Get số lượng đã nhập kho
                var warehouseTransactions = await _context.WarehouseTransactionModel.Where(x => x.ReferenceDocumentId == req.RawMaterialCardId && x.MovementType == MovementType.Receive).AsNoTracking().ToListAsync();

                //Check exist
                if (!warehouseTransactions.Any())
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.MSG_REQUIRED_RECEIVE });

                //Thông tin nhập kho
                var wT = warehouseTransactions.FirstOrDefault();

                //Kho
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == wT.SlocId);
                //Vị trị kho
                var storageBin = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == wT.StorageBinId);

                //Sản phẩm
                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == wT.ProductCode);

                //Call SAP get số lượng tồn kho theo material code
                var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                {
                    LGORT = sloc.Sloc,
                    MATNR = wT.ProductCode,
                    WERKS = CurrentUser?.SaleOrg,
                    CHARG = wT.Batch
                });

                foreach (var x in warehouseTransactions)
                {
                    var qtyImported = new QuantityImportedResponse
                    {
                        PO = x.PO,
                        POLine = x.POLine,
                        SO = x.SO,
                        SOLine = x.SOLine,
                        WBS = x.WBS,
                        QuantityImported = x.Quantity,
                        Unit = x.Unit
                    };

                    //SO/SOLine
                    var soSoLine = $"{x.SO}/ {x.SOLine}";

                    //Số lượng tồn kho theo SO của reservation
                    qtyImported.QuantityStock = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.SSNUM == soSoLine).Sum(x => x.LABST);

                    listQtyImported.Add(qtyImported);
                }

                listQtyImported = listQtyImported.OrderBy(x => x.PO).ThenBy(x => x.POLine).ToList();

                var response = new InforReceived
                {
                    //Số lô
                    BatchNumber = wT?.Batch,
                    //Nhà máy
                    Plant = sloc?.Plant,
                    //Kho
                    Sloc = sloc?.Sloc,
                    WarehouseNo = sloc?.Sloc,
                    //Vị trí kho
                    StorageBin = storageBin?.StorageBin,
                    QuantityImporteds = listQtyImported,
                    ProductCode = wT.ProductCode,
                    ProductName = product?.ProductName,
                    SumQuantityImported = listQtyImported.Sum(x => x.QuantityImported)
                };


                return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Get thông tin nhập kho SAP
        /// <summary>
        /// GET thông tin nhập kho SAP
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet("GetInfoReceiSAP")]
        public async Task<IActionResult> GET([FromQuery] GetReceiveInfoSAPRequest req)
        {
            //Plant
            var plant = CurrentUser?.SaleOrg;
            //Ngày hiện tại
            var dateNow = DateTime.Now;
            var yearNow = DateTime.Now.Year.ToString();

            var infoReceiSAP = await _context.ReceiveInformationModel.FirstOrDefaultAsync(x => x.GoodsReceivedNote == req.GoodsReceivedNote &&
                                                                                               x.YearReceive == yearNow &&
                                                                                               x.ReceiveDate.Value.Date == dateNow.Date &&
                                                                                               x.Plant == plant);

            var response = new GetReceiveInfoSAPResponse
            {
                //Kho
                SlocId = infoReceiSAP?.SlocId,

                MaterialType = infoReceiSAP?.MaterialType,
                //NVL Nội địa/Nhập khẩu
                ImportExportType = infoReceiSAP?.ImportExportType,
                //Nhóm hàng
                MaterialGroup = infoReceiSAP?.MaterialGroup,
                //Tình trạng hàng hóa
                MaterialStatus = infoReceiSAP?.MaterialStatus,
                //NSXHSD
                NSXHSD = infoReceiSAP?.NSXHSD,
                //Loại da
                SkinType = infoReceiSAP?.SkinType,
                //Màu da
                SkinColor = infoReceiSAP?.SkinColor,
                //Mã kiện
                CaseCode = infoReceiSAP?.CaseCode,
            };

            return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
        }
        #endregion

        #endregion

        #region Chuyển kho

        #region GET thông tin kho xuất
        /// <summary>
        /// GET thông tin kho xuất
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetSlocExported")]
        public async Task<IActionResult> GetSlocExported([FromQuery] GetSlocExportedRequest req)
        {
            try
            {
                //Thông tin đã xuất kho
                var reservation = await _context.WarehouseTranferModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId && x.SlocExportId.HasValue && !x.SlocImportId.HasValue);

                //Sloc 
                var slocId = reservation?.SlocExportId;
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == slocId);

                //StorageBin
                var storageBinId = reservation?.StorageBinExportId;
                var storageBin = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == storageBinId);

                var response = new
                {
                    //Sloc
                    Sloc = sloc?.Sloc,
                    //WarehouseNo
                    WarehouseNo = sloc?.WarehouseNo,
                    //Storage Bin
                    StorageBin = storageBin?.StorageBin
                };

                return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion      

        #region Get số lượng tồn kho, SO/SOLine, WBS SAP
        /// <summary>Get số lượng tồn kho, SO/SOLine, WBS SAP</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetInventorySAP
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "materialCode": "string",
        ///                "plant": "string",
        ///                "sloc": "string",
        ///                "storageBin": "string"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "GET tồn kho SAP thành công.",
        ///                "data": {
        ///                  "quantity": 100
        ///                },
        ///                "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("GetInventorySAP")]
        public async Task<IActionResult> POST([FromBody] GetInventorySAPRequest req)
        {
            try
            {
                var response = new GetInventorySAPResponse();

                //Lệnh xuất vật tư
                var reservation = await _context.MaterialReservationModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId);

                //Check sản phẩm tồn tại
                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == req.ProductCode);
                if (product == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"NVL {req.ProductCode}") });

                //Check sloc
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == req.Sloc);
                if (sloc == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"Sloc {req.Sloc}") });

                //Call SAP get số lượng tồn kho theo material code
                var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                {
                    LGORT = req.Sloc,
                    MATNR = req.ProductCode,
                    WERKS = req.Plant,
                    CHARG = req.BatchNumber
                });

                //Trường hợp 1: SO ở Reservation có giá trị. Check SO của reservation == SO của response SAP để lấy thông tin
                if (!string.IsNullOrEmpty(reservation?.SalesOrderNumber) && string.IsNullOrEmpty(reservation?.WBSElement))
                {
                    //SO/SOLine
                    var soSoLine = $"{reservation.SalesOrderNumber}/ {reservation.SalesOrderNumberItem}";

                    //Số lượng tồn kho theo SO của reservation
                    response.Quantity = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.SSNUM == soSoLine).Sum(x => x.LABST);

                    //SO
                    response.SO = reservation.SalesOrderNumber;
                    response.SOLine = reservation.SalesOrderNumberItem;

                    //ĐVT
                    response.Unit = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.FirstOrDefault(x => x.SSNUM == soSoLine)?.MEINS;
                }

                //Trường hợp 2: WBS ở Reservation có giá trị. Check WBS của reservation == WBS của response SAP để lấy thông tin
                if (!string.IsNullOrEmpty(reservation?.WBSElement) && string.IsNullOrEmpty(reservation?.SalesOrderNumber))
                {
                    //Số lượng tồn kho theo WBS của reservation
                    response.Quantity = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.SSNUM == reservation.WBSElement).Sum(x => x.LABST);

                    //WBS
                    response.WBS = reservation?.WBSElement;
                    //ĐVT
                    response.Unit = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.FirstOrDefault(x => x.SSNUM == reservation.SalesOrderNumber)?.MEINS;
                }

                //Trường hợp 3: SO và WBS không có giá trị
                if (string.IsNullOrEmpty(reservation?.WBSElement) && string.IsNullOrEmpty(reservation?.SalesOrderNumber))
                {
                    //Số lượng tồn kho tồn trơn theo reservation
                    //Chỉ lấy tồn trơn
                    response.Quantity = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => string.IsNullOrEmpty(x.SSNUM)).Sum(x => x.LABST);

                    //ĐVT
                    response.Unit = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.FirstOrDefault(x => x.SSNUM == reservation.SalesOrderNumber)?.MEINS;
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Succes, "GET tồn kho SAP")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Get barcode chuyển kho
        /// <summary>Get barcode chuyển kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetBarcodeReceive
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "plant": null,
        ///                "sloc": null,
        ///                "reservationCode": null,
        ///                "statusReservation": "1",
        ///                "productCode": null,
        ///                "statusWarehouse": "0"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                     "code": 200,
        ///                     "isSuccess": true,
        ///                     "message": "GET danh sách chuyển kho thành công.",
        ///                     "data": [
        ///                       {
        ///                         "plant": "1000",
        ///                         "storageLocation": "N010",
        ///                         "reservationNumber": "15241",
        ///                         "statusReservation": "Đã duyệt",
        ///                         "quantity": 1,
        ///                         "so": "",
        ///                         "wbs": "",
        ///                         "lsx": "",
        ///                         "riPlant": "1000",
        ///                         "riStorageLocation": "X005",
        ///                         "item": "1",
        ///                         "statusWarehouse": "Chờ xuất kho"
        ///                       }
        ///                     ]
        ///                }
        /// </remarks>
        [HttpGet("GetBarcodeReceive")]
        public async Task<IActionResult> GET([FromQuery] GetBarcodeReceiveRequest req)
        {

            var response = new GetBarcodeReponse();
            //Tìm ở bảng RawMaterialCard
            var rawMaterialCard = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId);
            //Dữ liệu ở bảng RawMaterialCardModel null thì tìm ở bảng RawMaterialCardManualModel
            if (rawMaterialCard is null)
            {
                //Tìm ở bảng RawMaterialManual
                var rawMaterialCardManual = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == req.RawMaterialCardId);
                if (rawMaterialCardManual is not null)
                {
                    //NVL
                    response.ProductCode = rawMaterialCardManual.ProductCode;
                    response.ProductName = _context.ProductLatestModel.FirstOrDefault(x => x.ProductCode == rawMaterialCardManual.ProductCode)?.ProductName;

                    //Số lô
                    response.BatchNumber = rawMaterialCardManual.Batch;
                    response.ManuDate = DateTime.Now.ToString("dd/MM/yyyy");
                }
            }
            else
            {
                //NVL
                response.ProductCode = rawMaterialCard.ProductCode;
                response.ProductName = _context.ProductLatestModel.FirstOrDefault(x => x.ProductCode == rawMaterialCard.ProductCode)?.ProductName;

                //Số lô
                response.BatchNumber = _context.WarehouseTransactionModel.FirstOrDefault(x => x.ReferenceDocumentId == req.RawMaterialCardId && x.MovementType == MovementType.Receive)?.Batch;
                response.ManuDate = DateTime.Now.ToString("dd/MM/yyyy");
            }

            return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
        }
        #endregion

        #region Chuyển kho 1 bước
        /// <summary>Chuyển kho 1 bước</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/WarehouseTranfer
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "reservationId": "A0AF5F36-D205-4FBA-A4EA-3286CC8911F5",
        ///                "slocExportId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinExportId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060",
        ///                "slocImportId": "824DA888-DC75-45E8-B87A-05EB6D70506E",
        ///                "storageBinImportId": "D082AABA-DB01-44D6-9479-000097046A92",
        ///                "quantity": 500,
        ///                "unit": "Thanh",
        ///                "rawMaterialCardId": "d58d7300-1d35-4ddc-8f80-34a75c9c9668"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "Chuyển kho thành công.",
        ///                "data": true
        ///                "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("WarehouseTranfer")]
        public async Task<IActionResult> PostWarehouseTranfer([FromBody] WarehouseTranferRequest req)
        {
            try
            {
                //Kho và vị trí kho
                var slocs = _context.SlocModel.AsNoTracking();
                var storageBins = _context.StorageBinModel.AsNoTracking();

                //Kho xuất
                var slocExport = slocs.FirstOrDefault(x => x.Id == req.SlocExportId);

                // Update 1: Skip check
                //Requierd bin với kho xuất có quản lý vị trí
                //if (slocExport.WarehouseNo == "100" && !req.StorageBinExportId.HasValue)
                //    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những kho xuất có quản lý vị trí!" });

                //Kho nhập
                var slocImport = slocs.FirstOrDefault(x => x.Id == req.SlocImportId);

                //Requierd bin với kho nhập có quản lý vị trí
                if (slocImport.WarehouseNo == "100" && !req.StorageBinImportId.HasValue)
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những kho nhập có quản lý vị trí!" });

                //Vị trí kho xuất
                var stgExport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinExportId);
                //Vị trí kho nhập
                var stgImport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinImportId);

                //Check item chi tiết chuyển kho
                if (!req.WarehouseTranferDetails.Any())
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_RequierdFile, "Số lượng chuyển kho chi tiết") });


                //Get pallet
                var palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId);
                var palletManualNVL = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == req.RawMaterialCardId);
                //Check exist
                if (palletNVL is null && palletManualNVL is null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Pallet NVL") });

                //Reservation (Lệnh xuất vật tư)
                var reservation = await _context.MaterialReservationModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId);
                //Check exist
                if (reservation == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Lệnh cấp vật tư") });

                //Số lượng yêu cầu chuyển
                var reqQuantity = reservation.ReqQuantity;

                //Check kho reservation và kho chọn ở màn hình
                if (reservation.StorageLocation != slocExport.Sloc)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_CheckSame, "Kho xuất của Reservation", "Kho xuất ở thông tin giao dịch chuyển kho") });

                if (reservation.RIStorageLocation != slocImport.Sloc)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_CheckSame, "Kho nhập của Reservation", "Kho nhập ở thông tin giao dịch chuyển kho") });

                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Số lượng thực chuyển và đvt
                var quantity = req.WarehouseTranferDetails.Sum(x => x.Quantity);

                //Check số lượng yêu cầu chuyển của reservation và số lượng chuyển của user nhập
                if (quantity > reqQuantity)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Số lượng chuyển (" + quantity + ") không được lớn hơn số lượng yêu cầu (" + reqQuantity + ")!" });


                var unit = req.WarehouseTranferDetails.FirstOrDefault().Unit;

                var productCode = palletNVL == null ? palletManualNVL.ProductCode : palletNVL.ProductCode;

                //Save to WarehouseTranferModel
                var warehouseTranfer = new WarehouseTranferModel
                {
                    //Id
                    WarehouseTranferId = Guid.NewGuid(),
                    //NVL
                    ProductCode = productCode,
                    //Lệnh xuất vật tư
                    ReservationId = req.ReservationId,
                    //Kho xuất
                    SlocExportId = req.SlocExportId,
                    //Vị trí kho xuất
                    StorageBinExportId = req.StorageBinExportId,
                    //Kho nhập
                    SlocImportId = req.SlocImportId,
                    //Vị trí kho nhập
                    StorageBinImportId = req.StorageBinImportId,
                    //Số lượng và ĐVT
                    Quantity = quantity,
                    Unit = reservation.MeasureUnit,
                    //Thẻ treo
                    RawMaterialCardId = req.RawMaterialCardId.Value,
                    //Ngày xuất kho
                    ExportDate = dateNow,
                    ExportDateKey = int.Parse(dateNowStr),
                    //Ngày nhập kho
                    ImportDate = dateNow,
                    ImportDateKey = int.Parse(dateNowStr),
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                };

                var listWT = new List<WarehouseTransactionModel>();
                var detailWTSyncSAPs = new List<ZST_MES_INF_TRANS>();

                //Save 2 record to WarehouseTransactionModel
                //Xuất => -quantity
                //Nhập => +quantity
                //Record xuất lưu vào WarehouseTransaction
                req.WarehouseTranferDetails.ForEach(e =>
                {
                    var wTExport = CreateWT(unit, req.SlocExportId, req.StorageBinExportId, -e.Quantity, reservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, e.SO, e.SOLine, e.WBS, e.BatchNumber);
                    listWT.Add(wTExport);

                    var wT = CreateWT(reservation.MeasureUnit, req.SlocImportId, req.StorageBinImportId, +e.Quantity, reservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, e.SO, e.SOLine, e.WBS, e.BatchNumber);
                    listWT.Add(wT);

                    var detail = new ZST_MES_INF_TRANS
                    {
                        //ID
                        DOCUMENT_NUMBER = wT.WarhouseTransactionId.ToString(),

                        //Reservation và Item
                        MES_RESERVATION_NUMBER = reservation.ReservationNumber,
                        RSNUM = reservation.ReservationNumber,
                        RSPOS = reservation.ReservationItemNumber,

                        //PO và POLine
                        //BSTNR = warehouseItemDetail.PO,
                        //EBELP = warehouseItemDetail.POLine,
                        //Nhà máy
                        WERKS = CurrentUser?.SaleOrg,
                        //Mã NVL,
                        MATNR = productCode,
                        //Số SO và SOLine
                        KDAUF = wT.SO,
                        KDPOS = wT.SOLine,
                        //WBS
                        PS_PSP_PNR = wT.WBS,
                        //Số lượng và ĐVT
                        ERFMG = wT.Quantity.HasValue ? wT.Quantity.Value : 0,
                        ERFME = wT.Unit,
                        //Lô
                        CHARG = wT.Batch,
                        //Kho xuất
                        LGORT = slocExport?.Sloc,
                        //WarehouseNo kho xuất
                        LGNUM = slocExport?.WarehouseNo,
                        //Vị trí kho xuất
                        LGPLA = stgExport?.StorageBin,

                        LGTYP = stgExport?.StorageType, //Update 1: storage type xuat

                        //Nhà máy nhận
                        UMWRK = CurrentUser?.SaleOrg,
                        //Kho nhận
                        UMLGO = slocImport?.Sloc,
                        //Vị trị kho nhận
                        UMLGP = stgImport?.StorageBin,

                        UMLGT = stgImport?.StorageType, //Update 1: storage type nhap

                        //Tồn kho đặc biệt. Có giá trị SO => E, có giá trị WBS => Q; SO và WBS không có giá trị => null
                        SOBKZ = !string.IsNullOrEmpty(wT.SO) ? "E" : (!string.IsNullOrEmpty(wT.WBS) ? "Q" : null),
                    };
                    detailWTSyncSAPs.Add(detail);
                });

                var ZMES_FM_INF_TRANS = new ZMES_FM_INF_TRANS
                {
                    INF_TRAN = new ZST_MES_INF_TRANS_HEADER
                    {
                        //Với nhập kho chứng từ là thẻ treo
                        HEADER_ID = warehouseTranfer.WarehouseTranferId.ToString(),
                        //Ngày chứng từ
                        BLDAT = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),
                        BUDAT_MKPF = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),

                        //Loại giao dịch kho
                        DOCUMENT_TYPE = StatusWarehouseSAP.CK,

                        //Chi tiết nhập kho
                        DETAILS = detailWTSyncSAPs.ToArray()
                    }
                };
                //Request string send SAP (tracking)
                var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(ZMES_FM_INF_TRANS);

                XmlSerializer xsSubmit = new XmlSerializer(typeof(ZMES_FM_INF_TRANS));
                var xml = "";

                using (var sww = new StringWriter())
                {
                    using (XmlWriter writer = XmlWriter.Create(sww))
                    {
                        xsSubmit.Serialize(writer, ZMES_FM_INF_TRANS);
                        xml = sww.ToString(); // Your XML
                    }
                }

                //Số phiếu chuyển kho SAP
                string grNote = string.Empty;

                //SEND SAP
                try
                {
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(ZMES_FM_INF_TRANS);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    //Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "CKR",
                        FuntionnName = "Chuyển kho có reservation",
                        RequestSAP = jsonString,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId
                    });

                    await _context.SaveChangesAsync();

                    //List response to SAP
                    if (resSAPs.Any())
                    {
                        //Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            //Msg lỗi
                            var msgError = resSAPs.FirstOrDefault().MESSAGE;
                            return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });
                        }

                        //Cập nhật trạng thái thành công và save dữ liệu nhập kho vào MES
                        foreach (var item in listWT)
                        {
                            //Flag send SAP
                            item.isSendToSAP = true;
                            item.SendToSAPTime = DateTime.Now;
                        }

                        //Phiếu chuyển kho
                        var goodsReceivedNote = resSAPs.FirstOrDefault().MESSAGE_V1;
                        grNote = goodsReceivedNote;
                    }

                    _context.WarehouseTranferModel.Add(warehouseTranfer);
                    _context.WarehouseTransactionModel.AddRange(listWT);
                }
                catch (Exception)
                {
                    return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
                }


                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = $"Chuyển kho thành công. Số phiếu chuyển kho {grNote}"
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        [HttpPost("WarehouseTranfer2")]
        public async Task<IActionResult> PostWarehouseTranfer2([FromBody] WarehouseTranferRequest req)
        {
            try
            {
                //Kho và vị trí kho
                var slocs = _context.SlocModel.AsNoTracking();
                var storageBins = _context.StorageBinModel.AsNoTracking();

                //Kho xuất
                var slocExport = slocs.FirstOrDefault(x => x.Id == req.SlocExportId);

                // Update 1: app xuất chưa cho điền từng vị trí line xuất, làm sau
                //Requierd bin với kho xuất có quản lý vị trí
                //if (slocExport.WarehouseNo == "100" && !req.StorageBinExportId.HasValue)
                //{
                //    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những kho xuất có quản lý vị trí!" });
                //}

                //Kho nhập
                var slocImport = slocs.FirstOrDefault(x => x.Id == req.SlocImportId);

                //Requierd bin với kho nhập có quản lý vị trí
                if (slocImport.WarehouseNo == "100" && !req.StorageBinImportId.HasValue)
                {
                    return Ok(new ApiResponse { Code = 400, Message = "Vui lòng chọn Storage Bin với những kho nhập có quản lý vị trí!" });
                }

                //Vị trí kho xuất
                var stgExport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinExportId);
                //Vị trí kho nhập
                var stgImport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinImportId);

                //Check item chi tiết chuyển kho
                if (!req.WarehouseTranferDetails.Any())
                {
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_RequierdFile, "Số lượng chuyển kho chi tiết") });
                }


                foreach (var detail in req.WarehouseTranferDetails)
                {
                    if (string.IsNullOrEmpty(detail.STTLine) || detail.STTLine.Split(',').Length == 0)
                    {
                        return Ok(new ApiResponse
                        {
                            Code = 400,
                            IsSuccess = false,
                            Data = true,
                            Message = $"Vui lòng điền đủ STT lệnh cấp"
                        });
                    }
                }


                var listNote = new List<string>();
                var listSTTLineSuccess = new List<string>();
                var listSTTLineError = new List<string>();
                var listError = new List<string>();



                var _reservationItemsTemp = await _context.MaterialReservationModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId);

                // 3 line
                //var reservationItems = await _context.MaterialReservationModel
                //    .Where(x => x.ReservationHeaderId == _reservationItemsTemp.ReservationHeaderId
                //               && x.ItemDeleted != "X")
                //    //.OrderBy(x => x.ReservationItemNumber)
                //    .OrderBy(x => x.ReservationNumber)
                //    .ThenBy(x => x.StorageLocation)
                //    .ThenBy(x => x.RIStorageLocation)
                //    .ThenBy(x => x.MaterialNumber)
                //    .ThenBy(x => x.ReservationItemNumber)
                //    .ToListAsync();

                var reservationItemsTemp = await _context.MaterialReservationModel.Include(x => x.ReservationHeader)
                                                                          .Where(x => x.ReservationHeaderId == _reservationItemsTemp.ReservationHeaderId
                                                                                    && x.ItemDeleted != "X")
                                                                          .ToListAsync();

                var reservationItems = reservationItemsTemp
                                                                          .OrderBy(x => x.ReservationNumber)
                                                                          .ThenBy(x => x.StorageLocation)
                                                                          .ThenBy(x => x.RIStorageLocation)
                                                                          .ThenBy(x => x.MaterialNumber)
                                                                          .ThenBy(x => int.Parse(x.ReservationItemNumber)) // Sorting in memory
                                                                          .ToList();

                //var reservationItems = reservationItemsTemp.OrderBy(item => int.Parse(item.ReservationItemNumber)).ToList();

                for (int i = 0; i < reservationItems.Count; i++)
                {
                    var itemReservation = reservationItems[i];

                    var itemReservatoinSTT = i + 1;

                    // quantityCurrentLine
                    var itemInput = req.WarehouseTranferDetails.FirstOrDefault(u =>
                        u.ProductCode == itemReservation.MaterialNumber
                        //&& u.STTLine == itemReservatoinSTT
                        && u.STTLine.Split(',').Select(s => s.Trim()).Contains(itemReservatoinSTT.ToString())
                    );

                    if (itemInput != null)
                    {
                        //Get pallet
                        var palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == itemInput.RawMaterialCardId);
                        var palletManualNVL = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == itemInput.RawMaterialCardId);
                        //Check exist
                        if (palletNVL is null && palletManualNVL is null)
                            return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Pallet NVL") });

                        //Số lượng yêu cầu chuyển
                        // Trường hợp 1: 1 line input 1 line lệnh cấp
                        //var reqQuantity = itemReservation.ReqQuantity;

                        // Trường hợp 2: 1 line input nhiều line lệnh cấp
                        // Lệnh (itemReservation)
                        // 1: 400 <--
                        // 5: 400
                        // Input
                        // 1,5: 800 (itemInput)



                        // if STTLine  is "1,5" then sum only reservationItems[0] and reservationItems[4]
                        string sttLine = itemInput.STTLine;
                        List<int> indexes = sttLine.Split(',')
                            .Select(s => int.Parse(s.Trim()) - 1)
                            .ToList();

                        var sttLineArray = sttLine.Split(',');
                        var numberOfSttLine = sttLineArray.Length;

                        // total 800
                        var reqQuantity = indexes
                            .Where(index => index >= 0 && index < reservationItems.Count) // // Ensure indices are valid
                            .Sum(index => reservationItems[index].ReqQuantity);

                        // TEST:

                        var reqQuantity1 = indexes
                            .Where(index => index >= 0 && index < reservationItems.Count).ToList();

                        //Số lượng thực chuyển và đvt
                        //var quantity = req.WarehouseTranferDetails.Where(u => u.ProductCode == itemReservation.MaterialNumber
                        //                                                                 //&& u.STTLine == itemReservatoinSTT
                        //                                                                 ).Sum(x => x.Quantity);


                        //var quantityCheck = itemInput.Quantity;
                        var inputQuantityCheck = req.WarehouseTranferDetails.Where(u => u.ProductCode == itemReservation.MaterialNumber
                                                                                        //&& currentLine == itemReservatoinSTT
                                                                                        && u.STTLine.Split(',').Select(s => s.Trim()).Contains(itemReservatoinSTT.ToString())
                                                                                       ).ToList();

                        var inputQuality = inputQuantityCheck.Sum(x => x.Quantity);
                        //Check số lượng yêu cầu chuyển của reservation và số lượng chuyển của user nhập
                        //if (quantity > reqQuantity)
                        //{
                        //    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Số lượng chuyển không được lớn hơn số lượng yêu cầu!" });
                        //}

                        if (inputQuality != reqQuantity)
                        {
                            return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = "Số lượng chuyển (" + inputQuality + ") line " + sttLine + " không đủ so với số lượng yêu cầu (" + reqQuantity + ")!" });
                        }

                        // 
                        var quantity = itemReservation.ReqQuantity; // Lấy số lượng của header


                        //Date now
                        var dateNow = DateTime.Now;
                        var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                        var unit = itemInput.Unit;

                        var productCode = palletNVL == null ? palletManualNVL.ProductCode : palletNVL.ProductCode;

                        //Save to WarehouseTranferModel
                        var warehouseTranfer = new WarehouseTranferModel
                        {
                            //Id
                            WarehouseTranferId = Guid.NewGuid(),
                            //NVL
                            ProductCode = productCode,
                            //Lệnh xuất vật tư
                            ReservationId = itemReservation.ReservationId,
                            //Kho xuất
                            SlocExportId = req.SlocExportId,
                            //Vị trí kho xuất
                            StorageBinExportId = req.StorageBinExportId,
                            //Kho nhập
                            SlocImportId = req.SlocImportId,
                            //Vị trí kho nhập
                            StorageBinImportId = req.StorageBinImportId,
                            //Số lượng và ĐVT
                            Quantity = quantity,
                            Unit = itemInput.Unit,
                            //Thẻ treo
                            RawMaterialCardId = itemInput.RawMaterialCardId,
                            //Ngày xuất kho
                            ExportDate = dateNow,
                            ExportDateKey = int.Parse(dateNowStr),
                            //Ngày nhập kho
                            ImportDate = dateNow,
                            ImportDateKey = int.Parse(dateNowStr),
                            //Common
                            CreateTime = dateNow,
                            CreateBy = CurrentUser?.AccountId,
                            Actived = true
                        };

                        var listWT = new List<WarehouseTransactionModel>();
                        var detailWTSyncSAPs = new List<ZST_MES_INF_TRANS>();


                        //Save 2 record to WarehouseTransactionModel
                        //Xuất => -quantity
                        //Nhập => +quantity
                        //Record xuất lưu vào 

                        // Số lượng tính toán:
                        // - Nếu STTLine là 1,4,6 và line hiện tại là 1 thì quantity = quantityInputLine - (4 + 6)


                        if (numberOfSttLine == 1)
                        {
                            // 1 line thì cứ chạy như cũ
                            var currentLine = int.Parse(sttLineArray[0]);
                            var itemReservatoinSTTString = itemReservatoinSTT.ToString();
                            var itemInputDetailsList = req.WarehouseTranferDetails.Where(u => u.ProductCode == itemReservation.MaterialNumber
                                                                                       && u.STTLine == itemReservatoinSTTString
                                                                                       ).ToList();

                            itemInputDetailsList.ForEach(warehouseItemDetail =>
                            {
                                var wTExport = CreateWT(unit, req.SlocExportId, req.StorageBinExportId, -warehouseItemDetail.Quantity, itemReservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, warehouseItemDetail.SO, warehouseItemDetail.SOLine, warehouseItemDetail.WBS, warehouseItemDetail.BatchNumber);
                                listWT.Add(wTExport);

                                var wT = CreateWT(itemReservation.MeasureUnit, req.SlocImportId, req.StorageBinImportId, +warehouseItemDetail.Quantity, itemReservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, warehouseItemDetail.SO, warehouseItemDetail.SOLine, warehouseItemDetail.WBS, warehouseItemDetail.BatchNumber);
                                listWT.Add(wT);

                                var detail = new ZST_MES_INF_TRANS
                                {
                                    //ID
                                    DOCUMENT_NUMBER = wT.WarhouseTransactionId.ToString(),

                                    //Reservation và Item
                                    MES_RESERVATION_NUMBER = itemReservation.ReservationNumber,
                                    RSNUM = itemReservation.ReservationNumber,
                                    RSPOS = itemReservation.ReservationItemNumber,

                                    //PO và POLine
                                    //BSTNR = warehouseItemDetail.PO,
                                    //EBELP = warehouseItemDetail.POLine,
                                    //Nhà máy
                                    WERKS = CurrentUser?.SaleOrg,
                                    //Mã NVL,
                                    MATNR = productCode,
                                    //Số SO và SOLine
                                    KDAUF = wT.SO,
                                    KDPOS = wT.SOLine,
                                    //WBS
                                    PS_PSP_PNR = wT.WBS,
                                    //Số lượng và ĐVT
                                    ERFMG = wT.Quantity.HasValue ? wT.Quantity.Value : 0,
                                    ERFME = wT.Unit,
                                    //Lô
                                    CHARG = wT.Batch,
                                    //Kho xuất
                                    LGORT = slocExport?.Sloc,
                                    //WarehouseNo kho xuất
                                    LGNUM = slocExport?.WarehouseNo,
                                    //Vị trí kho xuất
                                    LGPLA = stgExport?.StorageBin,

                                    LGTYP = stgExport?.StorageType, //Update 1: storage type xuat

                                    //Nhà máy nhận
                                    UMWRK = CurrentUser?.SaleOrg,
                                    //Kho nhận
                                    UMLGO = slocImport?.Sloc,
                                    //Vị trị kho nhận
                                    UMLGP = stgImport?.StorageBin,

                                    UMLGT = stgImport?.StorageType, //Update 1: storage type nhap

                                    //Tồn kho đặc biệt. Có giá trị SO => E, có giá trị WBS => Q; SO và WBS không có giá trị => null
                                    SOBKZ = !string.IsNullOrEmpty(wT.SO) ? "E" : (!string.IsNullOrEmpty(wT.WBS) ? "Q" : null),
                                };
                                detailWTSyncSAPs.Add(detail);
                            });
                        }
                        else // numberOfSttLine > 1 (2,3,4...)
                        {
                            var itemInputDetailsList = req.WarehouseTranferDetails.Where(u => u.ProductCode == itemReservation.MaterialNumber
                                                                                        //&& currentLine == itemReservatoinSTT
                                                                                        && u.STTLine.Split(',').Select(s => s.Trim()).Contains(itemReservatoinSTT.ToString())
                                                                                       ).ToList();
                            // itemInput.Quantity: 800
                            int currentIndex = itemReservatoinSTT - 1; // Convert to zero-based index
                            List<int> indexOtherLineArray = sttLine.Split(',')
                                .Select(s => int.Parse(s.Trim()) - 1)
                                .Where(i => i != currentIndex) // Exclude the current itemReservatoinSTT
                                .ToList();

                            decimal sumReqQuantity = (decimal)indexOtherLineArray
                                .Where(index => index >= 0 && index < reservationItems.Count) // Ensure indices are valid
                                .Sum(index => reservationItems[index].ReqQuantity);


                            var quantityCurrent = itemInput.Quantity - sumReqQuantity;

                            itemInputDetailsList.ForEach(warehouseItemDetail =>
                            {
                                var wTExport = CreateWT(unit, req.SlocExportId, req.StorageBinExportId, -quantityCurrent, itemReservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, warehouseItemDetail.SO, warehouseItemDetail.SOLine, warehouseItemDetail.WBS, warehouseItemDetail.BatchNumber);
                                listWT.Add(wTExport);

                                var wT = CreateWT(itemReservation.MeasureUnit, req.SlocImportId, req.StorageBinImportId, +quantityCurrent, itemReservation.ReservationNumber, productCode, warehouseTranfer.WarehouseTranferId, warehouseItemDetail.SO, warehouseItemDetail.SOLine, warehouseItemDetail.WBS, warehouseItemDetail.BatchNumber);
                                listWT.Add(wT);

                                var detail = new ZST_MES_INF_TRANS
                                {
                                    //ID
                                    DOCUMENT_NUMBER = wT.WarhouseTransactionId.ToString(),

                                    //Reservation và Item
                                    MES_RESERVATION_NUMBER = itemReservation.ReservationNumber,
                                    RSNUM = itemReservation.ReservationNumber,
                                    RSPOS = itemReservation.ReservationItemNumber,

                                    //PO và POLine
                                    //BSTNR = warehouseItemDetail.PO,
                                    //EBELP = warehouseItemDetail.POLine,
                                    //Nhà máy
                                    WERKS = CurrentUser?.SaleOrg,
                                    //Mã NVL,
                                    MATNR = productCode,
                                    //Số SO và SOLine
                                    KDAUF = wT.SO,
                                    KDPOS = wT.SOLine,
                                    //WBS
                                    PS_PSP_PNR = wT.WBS,
                                    //Số lượng và ĐVT
                                    ERFMG = wT.Quantity.HasValue ? wT.Quantity.Value : 0,
                                    ERFME = wT.Unit,
                                    //Lô
                                    CHARG = wT.Batch,
                                    //Kho xuất
                                    LGORT = slocExport?.Sloc,
                                    //WarehouseNo kho xuất
                                    LGNUM = slocExport?.WarehouseNo,
                                    //Vị trí kho xuất
                                    LGPLA = stgExport?.StorageBin,

                                    LGTYP = stgExport?.StorageType, //Update 1: storage type xuat

                                    //Nhà máy nhận
                                    UMWRK = CurrentUser?.SaleOrg,
                                    //Kho nhận
                                    UMLGO = slocImport?.Sloc,
                                    //Vị trị kho nhận
                                    UMLGP = stgImport?.StorageBin,

                                    UMLGT = stgImport?.StorageType, //Update 1: storage type nhap

                                    //Tồn kho đặc biệt. Có giá trị SO => E, có giá trị WBS => Q; SO và WBS không có giá trị => null
                                    SOBKZ = !string.IsNullOrEmpty(wT.SO) ? "E" : (!string.IsNullOrEmpty(wT.WBS) ? "Q" : null),
                                };
                                detailWTSyncSAPs.Add(detail);
                            });
                        }

                        // TODO: Không lặp từng line nữa mà header 1 cái này, còn detail là tất cả các line
                        var ZMES_FM_INF_TRANS = new ZMES_FM_INF_TRANS
                        {
                            INF_TRAN = new ZST_MES_INF_TRANS_HEADER
                            {
                                //Với nhập kho chứng từ là thẻ treo
                                HEADER_ID = warehouseTranfer.WarehouseTranferId.ToString(),
                                //Ngày chứng từ
                                BLDAT = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),
                                BUDAT_MKPF = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),

                                //Loại giao dịch kho
                                DOCUMENT_TYPE = StatusWarehouseSAP.CK,

                                //Chi tiết nhập kho
                                DETAILS = detailWTSyncSAPs.ToArray()
                            }
                        };

                        //Request string send SAP (tracking)
                        var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(ZMES_FM_INF_TRANS);

                        //Số phiếu chuyển kho SAP
                        string grNote = string.Empty;

#if DEBUG

                        //// TODO TEST ONLY, REMOVE THIS
                        ////Lưu log SyncSAP
                        //_context.LogSAPModel.Add(new LogSAPModel
                        //{
                        //    Funtion = "CKR",
                        //    FuntionnName = "Chuyển kho có reservation",
                        //    RequestSAP = jsonString,
                        //    ResonponseSAP = "TEST " + itemReservation.ReservationNumber,
                        //    CreateTime = DateTime.Now,
                        //    CreateBy = CurrentUser?.AccountId
                        //});
                        //await _context.SaveChangesAsync();

                        XmlSerializer xsSubmit = new XmlSerializer(typeof(ZMES_FM_INF_TRANS));
                        var xml = "";

                        using (var sww = new StringWriter())
                        {
                            using (XmlWriter writer = XmlWriter.Create(sww))
                            {
                                xsSubmit.Serialize(writer, ZMES_FM_INF_TRANS);
                                xml = sww.ToString(); // Your XML
                            }
                        }

                        return Ok(new ApiResponse
                        {
                            Code = 400,
                            IsSuccess = false,
                            Data = true,
                            Message = $"TEST"
                        });
#endif


                        var isAlreadySend = _context.WarehouseTranferModel.FirstOrDefault(w => w.ReservationId == itemReservation.ReservationId);

                        if (isAlreadySend != null)
                        {
                            // Đã send SAP, không send nữa, cùng request cái lỗi cái không, fix rồi send lại
                        }
                        else
                        {
                            //SEND SAP
                            try
                            {
                                var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(ZMES_FM_INF_TRANS);
                                var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                                var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                                //Lưu log SyncSAP
                                _context.LogSAPModel.Add(new LogSAPModel
                                {
                                    Funtion = "CKR",
                                    FuntionnName = "Chuyển kho có reservation",
                                    RequestSAP = jsonString,
                                    ResonponseSAP = jsonStringRes,
                                    CreateTime = DateTime.Now,
                                    CreateBy = CurrentUser?.AccountId
                                });

                                await _context.SaveChangesAsync();

                                //List response to SAP
                                if (resSAPs.Any())
                                {
                                    //Loại msg = 'S' => send sap thành công
                                    var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                                    if (!msgSucces.Any())
                                    {
                                        // ERROR

                                        //Msg lỗi
                                        var msgError = resSAPs.FirstOrDefault().MESSAGE;
                                        listError.Add(msgError);
                                        listSTTLineError.Add(itemReservatoinSTT.ToString());

                                        //return Ok(new ApiResponse { Code = 400, Message = $"SAP Error: {msgError}", IsSuccess = false });
                                    }
                                    else
                                    {
                                        // SUCCESS
                                        //Cập nhật trạng thái thành công và save dữ liệu nhập kho vào MES
                                        foreach (var item in listWT)
                                        {
                                            //Flag send SAP
                                            item.isSendToSAP = true;
                                            item.SendToSAPTime = DateTime.Now;
                                        }

                                        //Phiếu chuyển kho
                                        var goodsReceivedNote = resSAPs.FirstOrDefault().MESSAGE_V1;
                                        grNote = goodsReceivedNote;
                                        listNote.Add(grNote);
                                        listSTTLineSuccess.Add(itemReservatoinSTT.ToString());

                                        _context.WarehouseTranferModel.Add(warehouseTranfer);
                                        _context.WarehouseTransactionModel.AddRange(listWT);
                                    }
                                }


                            }
                            catch (Exception)
                            {
                                //return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
                            }

                            await _context.SaveChangesAsync();
                        }

                    }

                }

                //// TODO: REMOVE, TEST ONLY
                //return Ok(new ApiResponse
                //{
                //    Code = 400,
                //    IsSuccess = false,
                //    Data = true,
                //    Message = $"TEST"
                //});

                if (listError.Count > 0)
                {

                    if (listSTTLineSuccess.Count > 0)
                    {
                        return Ok(new ApiResponse
                        {
                            Code = 400,
                            IsSuccess = false,
                            Data = true,
                            Message = $"Các line chuyển kho thành công: {string.Join(", ", listSTTLineSuccess)}. Các line chuyển kho lỗi: {string.Join(", ", listSTTLineError)}. {string.Join(", ", listError)}"
                        });
                    }
                    else
                    {
                        return Ok(new ApiResponse
                        {
                            Code = 400,
                            IsSuccess = false,
                            Data = true,
                            Message = $"Có lỗi xảy ra khi chuyển kho: {string.Join(", ", listError)}"
                        });
                    }


                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = $"Chuyển kho thành công. Số phiếu chuyển kho: {string.Join(", ", listNote)}"
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }


        /// <summary>
        /// Lưu thông tin dữ liệu giao dịch kho
        /// </summary>
        /// <param name="unit"></param>
        /// <param name="slocId"></param>
        /// <param name="storageBinId"></param>
        /// <param name="quantity"></param>
        /// <param name="reservationCode"></param>
        /// <param name="productCode"></param>
        /// <param name="warehouseTranferId"></param>
        /// <param name="SO"></param>
        /// <param name="SOLine"></param>
        /// <param name="wbs"></param>
        /// <param name="batchNumber"></param>
        /// <returns></returns>
        private WarehouseTransactionModel CreateWT(string unit, Guid? slocId, Guid? storageBinId, decimal? quantity, string reservationCode, string productCode,
                                                   Guid warehouseTranferId, string SO, string SOLine, string wbs, string batchNumber)
        {
            //Danh sách PO và PO line của NVL

            //Date now
            var dateNow = DateTime.Now;
            var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

            return new WarehouseTransactionModel
            {
                //Id
                WarhouseTransactionId = Guid.NewGuid(),
                //Kho
                SlocId = slocId,
                //Số lô
                Batch = batchNumber,
                //Nhà máy
                Plant = CurrentUser?.SaleOrg,
                //SO và SOLine
                SO = SO,
                SOLine = SOLine,
                //WBS
                WBS = wbs,
                //NVL
                ProductCode = productCode,
                //Vị trí kho
                StorageBinId = storageBinId,
                //ReservationId
                ReferenceDocumentId = warehouseTranferId,
                //Số lượng và ĐVT
                Quantity = quantity,
                Unit = unit,
                //ReservationCode
                ReferenceDocumentText = reservationCode,
                //Loại giao dịch kho
                MovementType = MovementType.DeliverOneStep,
                //Document Date
                DocumentDate = dateNow,
                DateKey = int.Parse(dateNowStr),
                //Common
                CreateTime = dateNow,
                CreateBy = CurrentUser?.AccountId,
                Actived = true
            };
        }

        #endregion

        #region Chuyển xuất kho có Reservation
        /// <summary>Chuyển xuất kho có Reservation</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/WarehouseTranferExport
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "reservationId": "A0AF5F36-D205-4FBA-A4EA-3286CC8911F5",
        ///                "slocExportId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinExportId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060",
        ///                "quantity": 500,
        ///                "unit": "Thanh",
        ///                "rawMaterialCardId": "d58d7300-1d35-4ddc-8f80-34a75c9c9668"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "Chuyển xuất kho thành công.",
        ///                "data": true
        ///                "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("WarehouseTranferExport")]
        public async Task<IActionResult> POST([FromBody] WarehouseTranferExportRequest req)
        {
            try
            {
                //Get pallet
                var palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId);
                //Check exist
                if (palletNVL == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Pallet NVL") });

                //Reservation (Lệnh xuất vật tư)
                var reservation = await _context.MaterialReservationModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId);
                //Check exist
                if (reservation == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Reservation") });

                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Save to WarehouseTranferModel
                var warehouseTranfer = new WarehouseTranferModel
                {
                    //Id
                    WarehouseTranferId = Guid.NewGuid(),
                    //NVL
                    ProductCode = palletNVL.ProductCode,
                    //Lệnh xuất vật tư
                    ReservationId = req.ReservationId,
                    //Kho xuất
                    SlocExportId = req.SlocExportId,
                    //Vị trí kho xuất
                    StorageBinExportId = req.StorageBinExportId,
                    //Số lượng và ĐVT
                    Quantity = req.Quantity,
                    Unit = req.Unit,
                    //Thẻ treo
                    RawMaterialCardId = req.RawMaterialCardId.Value,
                    //Ngày xuất kho
                    ExportDate = dateNow,
                    ExportDateKey = int.Parse(dateNowStr),
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                };

                _context.WarehouseTranferModel.Add(warehouseTranfer);

                //Save to WarehouseTransaction
                _context.WarehouseTransactionModel.Add(new WarehouseTransactionModel
                {
                    //Id
                    WarhouseTransactionId = Guid.NewGuid(),
                    //NVL 
                    ProductCode = palletNVL.ProductCode,
                    //Kho
                    SlocId = req.SlocExportId,
                    //Vị trí kho
                    StorageBinId = req.StorageBinExportId,
                    //ReservationId
                    ReferenceDocumentId = warehouseTranfer.WarehouseTranferId,
                    //Số lượng và ĐVT
                    Quantity = -req.Quantity,
                    Unit = req.Unit,
                    //ReservationCode
                    ReferenceDocumentText = reservation.ReservationNumber,
                    //Thẻ treo 
                    //Loại giao dịch kho = 313. Chuyển xuất kho
                    MovementType = MovementType.DeliverTwoStep,
                    //Document Date
                    DocumentDate = dateNow,
                    DateKey = int.Parse(dateNowStr),
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                });

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Chuyển xuất kho")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Chuyển kho không có reservation
        /// <summary>Chuyển kho không có reservation</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/WarehouseTranferNoReservation
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "slocExportId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinExportId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060",
        ///                "slocImportId": "824DA888-DC75-45E8-B87A-05EB6D70506E",
        ///                "storageBinImportId": "D082AABA-DB01-44D6-9479-000097046A92",
        ///                "quantity": 500,
        ///                "unit": "Thanh",
        ///                "rawMaterialCardId": "d58d7300-1d35-4ddc-8f80-34a75c9c9668"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "Chuyển kho thành công.",
        ///                "data": true
        ///                "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("WarehouseTranferNoReservation")]
        public async Task<IActionResult> POST([FromBody] WarehouseTranferExportNoRSRequest req)
        {
            try
            {
                //Kho và vị trí kho
                var slocs = _context.SlocModel.AsNoTracking();
                var storageBins = _context.StorageBinModel.AsNoTracking();

                //Kho xuất
                var slocExport = slocs.FirstOrDefault(x => x.Id == req.SlocExportId);
                //Kho nhập
                var slocImport = slocs.FirstOrDefault(x => x.Id == req.SlocImportId);

                //Vị trí kho xuất
                var stgExport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinExportId);
                //Vị trí kho nhập
                var stgImport = storageBins.FirstOrDefault(x => x.StorageBinId == req.StorageBinImportId);

                //Get pallet
                var palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId);
                var palletManualNVL = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == req.RawMaterialCardId);
                //Check exist
                if (palletNVL is null && palletManualNVL is null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Pallet NVL") });

                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Check item chi tiết chuyển kho
                if (!req.WarehouseTranferDetails.Any())
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_RequierdFile, "Số lượng chuyển kho chi tiết") });

                //Số lượng thực chuyển và đvt
                var quantity = req.WarehouseTranferDetails.Sum(x => x.Quantity);
                var unit = req.WarehouseTranferDetails.FirstOrDefault().Unit;

                //Thẻ treo NVL null thì lấy ở thẻ treo manual
                var productCode = palletNVL == null ? palletManualNVL.ProductCode : palletNVL.ProductCode;

                //Save to WarehouseTranferModel
                var warehouseTranfer = new WarehouseTranferModel
                {
                    //Id
                    WarehouseTranferId = Guid.NewGuid(),
                    //NVL
                    ProductCode = productCode,
                    //Kho xuất
                    SlocExportId = req.SlocExportId,
                    //Vị trí kho xuất
                    StorageBinExportId = req.StorageBinExportId,
                    //Kho nhập
                    SlocImportId = req.SlocImportId,
                    //Vị trí kho nhập
                    StorageBinImportId = req.StorageBinImportId,
                    //Số lượng và ĐVT
                    Quantity = quantity,
                    Unit = unit,
                    //Thẻ treo
                    RawMaterialCardId = req.RawMaterialCardId.Value,
                    //Ngày xuất kho
                    ExportDate = dateNow,
                    ExportDateKey = int.Parse(dateNowStr),
                    //Ngày nhập kho
                    ImportDate = dateNow,
                    ImportDateKey = int.Parse(dateNowStr),
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                };

                var listWT = new List<WarehouseTransactionModel>();
                var detailWTSyncSAPs = new List<ZST_MES_INF_TRANS>();

                req.WarehouseTranferDetails.ForEach(e =>
                {
                    //Save 2 record to WarehouseTransactionModel
                    //Xuất => -quantity
                    //Nhập => +quantity
                    var wTExport = CreateWTNoRS(req, req.SlocExportId, req.StorageBinExportId, -e.Quantity, productCode, req.BatchNumber, warehouseTranfer.WarehouseTranferId, e.SO, e.SOLine, e.WBS);
                    listWT.Add(wTExport);

                    //Record nhập lưu vào WarehouseTransaction
                    var wT = CreateWTNoRS(req, req.SlocImportId, req.StorageBinImportId, +e.Quantity, productCode, req.BatchNumber, warehouseTranfer.WarehouseTranferId, e.SO, e.SOLine, e.WBS);
                    listWT.Add(wT);

                    var detail = new ZST_MES_INF_TRANS
                    {
                        //ID
                        DOCUMENT_NUMBER = wT.WarhouseTransactionId.ToString(),

                        //PO và POLine
                        //BSTNR = warehouseItemDetail.PO,
                        //EBELP = warehouseItemDetail.POLine,
                        //Nhà máy
                        WERKS = CurrentUser?.SaleOrg,
                        //Mã NVL,
                        MATNR = productCode,
                        //Số SO và SOLine
                        KDAUF = wT.SO,
                        KDPOS = wT.SOLine,
                        //WBS
                        PS_PSP_PNR = wT.WBS,
                        //Số lượng và ĐVT
                        ERFMG = wT.Quantity.HasValue ? wT.Quantity.Value : 0,
                        ERFME = wT.Unit,
                        //Lô
                        CHARG = wT.Batch,
                        //Kho xuất
                        LGORT = slocExport?.Sloc,
                        //WarehouseNo kho xuất
                        LGNUM = slocExport?.WarehouseNo,
                        //Vị trí kho xuất
                        LGPLA = stgExport?.StorageBin,
                        //Nhà máy nhận
                        UMWRK = CurrentUser?.SaleOrg,
                        //Kho nhận
                        UMLGO = slocImport?.Sloc,
                        //Tồn kho đặc biệt. Có giá trị SO => E, có giá trị WBS => Q; SO và WBS không có giá trị => null
                        SOBKZ = !string.IsNullOrEmpty(wT.SO) ? "E" : (!string.IsNullOrEmpty(wT.WBS) ? "Q" : null),
                        //Vị trị kho nhận
                        UMLGP = stgExport?.StorageBin
                    };
                    detailWTSyncSAPs.Add(detail);
                });

                var ZMES_FM_INF_TRANS = new ZMES_FM_INF_TRANS
                {
                    INF_TRAN = new ZST_MES_INF_TRANS_HEADER
                    {
                        //Với nhập kho chứng từ là thẻ treo
                        HEADER_ID = warehouseTranfer.WarehouseTranferId.ToString(),
                        //Ngày chứng từ
                        BLDAT = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),
                        BUDAT_MKPF = listWT.FirstOrDefault().DocumentDate.Value.ToString(DateTimeFormat.DateFormatString),

                        //Loại giao dịch kho
                        DOCUMENT_TYPE = StatusWarehouseSAP.CK,

                        //Chi tiết nhập kho
                        DETAILS = detailWTSyncSAPs.ToArray()
                    }
                };
                //Request string send SAP (string)
                var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(ZMES_FM_INF_TRANS);

                try
                {
                    //SEND SAP
                    var responeToSAP = await _unitOfWork.SAPAPIRepository.SyncTransactionToSAP(ZMES_FM_INF_TRANS);
                    var resSAPs = responeToSAP.ZMES_FM_INF_TRANSResponse.RETURNS.ToList();

                    var jsonStringRes = Newtonsoft.Json.JsonConvert.SerializeObject(resSAPs);

                    //Lưu log SyncSAP
                    _context.LogSAPModel.Add(new LogSAPModel
                    {
                        Funtion = "CKNR",
                        FuntionnName = "Chuyển kho không có reservation",
                        RequestSAP = jsonString,
                        ResonponseSAP = jsonStringRes,
                        CreateTime = DateTime.Now,
                        CreateBy = CurrentUser?.AccountId
                    });

                    await _context.SaveChangesAsync();

                    //List response to SAP
                    if (resSAPs.Any())
                    {
                        //Loại msg = 'S' => send sap thành công
                        var msgSucces = resSAPs.Where(x => x.TYPE == MessageSAP.Success);

                        if (!msgSucces.Any())
                        {
                            //Msg lỗi
                            var msgError = resSAPs.FirstOrDefault().MESSAGE;
                            return Ok(new ApiResponse { Code = 400, Message = msgError, IsSuccess = false });
                        }

                        //Cập nhật trạng thái thành công và save dữ liệu nhập kho vào MES
                        foreach (var item in listWT)
                        {
                            //Flag send SAP
                            item.isSendToSAP = true;
                            item.SendToSAPTime = DateTime.Now;
                        }
                    }

                    _context.WarehouseTranferModel.Add(warehouseTranfer);
                    _context.WarehouseTransactionModel.AddRange(listWT);
                }
                catch (Exception)
                {
                    return Ok(new ApiResponse { Code = 500, Message = "Lỗi SAP !", IsSuccess = false });
                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Chuyển kho")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        /// <summary>
        /// Create WarehouseTransaction No reservation
        /// </summary>
        /// <param name="req"></param>
        /// <param name="slocId"></param>
        /// <param name="storageBinId"></param>
        /// <param name="quantity"></param>
        /// <param name="productCode"></param>
        /// <param name="batchNumber"></param>
        /// <param name="warehouseTranferId"></param>
        /// <param name="SO"></param>
        /// <param name="SOLine"></param>
        /// <param name="WBS"></param>
        /// <returns></returns>
        private WarehouseTransactionModel CreateWTNoRS(WarehouseTranferExportNoRSRequest req, Guid? slocId, Guid? storageBinId, decimal? quantity, string productCode, string batchNumber, Guid warehouseTranferId, string SO, string SOLine, string WBS)
        {
            //Date now
            var dateNow = DateTime.Now;
            var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

            return new WarehouseTransactionModel
            {
                //Id
                WarhouseTransactionId = Guid.NewGuid(),
                //NVL
                ProductCode = productCode,
                //SO và SOLine
                SO = SO,
                SOLine = SOLine,
                //WBS
                WBS = WBS,
                //Số lô
                Batch = batchNumber,
                //Kho
                SlocId = slocId,
                //Vị trí kho
                StorageBinId = storageBinId,
                //Số lượng và ĐVT
                Quantity = quantity,
                Unit = req.WarehouseTranferDetails.FirstOrDefault()?.Unit,
                //Thẻ treo
                ReferenceDocumentId = warehouseTranferId,
                //Loại giao dịch kho
                MovementType = MovementType.DeliverOneStep,
                //Document Date
                DocumentDate = dateNow,
                DateKey = int.Parse(dateNowStr),
                //Common
                CreateTime = dateNow,
                CreateBy = CurrentUser?.AccountId,
                Actived = true
            };
        }
        #endregion

        #region Get số lượng đã xuất kho (hiển thị lên màn nhập kho)
        /// <summary>Get số lượng đã xuất kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetQuantityExported?ReservationId={}
        ///     Params: 
        ///             + version : 1       
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///               "code": 200,
        ///               "isSuccess": true,
        ///               "message": "GET số lượng đã xuất kho thành công.",
        ///               "data": {
        ///                 "quantity": 500,
        ///                 "unit": "Thanh"
        ///               },
        ///               "additionalData": null
        ///             }
        /// </remarks>
        [HttpGet("GetQuantityExported")]
        public async Task<IActionResult> GET([FromQuery] Guid ReservationId)
        {
            try
            {
                //Tìm dữ liệu xuất kho. Đã có dữ liệu xuất kho và chưa có dữ liệu nhập kho (Tìm theo kho)
                var warehouseTranfer = await _context.WarehouseTranferModel.FirstOrDefaultAsync(x => x.ReservationId == ReservationId &&
                                                                                                     x.SlocExportId.HasValue &&
                                                                                                    !x.SlocImportId.HasValue);
                //Check dữ liệu xuất kho
                if (warehouseTranfer == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Dữ liệu xuất kho") });

                var response = new QuantityExportedResponse
                {
                    //Số lượng và đơn vị trính đã xuất kho
                    Quantity = warehouseTranfer.Quantity,
                    Unit = warehouseTranfer.Unit,
                };

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Succes, "GET số lượng đã xuất kho")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        #endregion

        #region Chuyển nhập kho
        /// <summary>Chuyển nhập kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/WarehouseTranferImport
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "reservationId": "A0AF5F36-D205-4FBA-A4EA-3286CC8911F5",
        ///                "slocImportId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinImportId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "Chuyển nhập kho thành công.",
        ///                "data": true
        ///                "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("WarehouseTranferImport")]
        public async Task<IActionResult> POST([FromBody] WarehouseTranferImportRequest req)
        {
            try
            {
                //Reservation (Lệnh xuất vật tư)
                var reservation = await _context.MaterialReservationModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId);
                //Check exist
                if (reservation == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Reservation") });

                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Tìm dữ liệu xuất kho. Đã có dữ liệu xuất kho và chưa có dữ liệu nhập kho (Tìm theo kho)
                var warehouseTranfer = await _context.WarehouseTranferModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId &&
                                                                                                     x.SlocExportId.HasValue &&
                                                                                                    !x.SlocImportId.HasValue);
                //Check dữ liệu xuất kho
                if (warehouseTranfer == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Dữ liệu xuất kho") });


                var warehouseuImport = new WarehouseTranferModel
                {
                    WarehouseTranferId = Guid.NewGuid(),
                    //Reservation
                    ReservationId = warehouseTranfer.ReservationId,
                    //Kho xuất và vị trí kho xuất
                    SlocExportId = warehouseTranfer.SlocExportId,
                    StorageBinExportId = warehouseTranfer.StorageBinExportId,
                    //Kho nhập và vị trí kho nhập
                    SlocImportId = req.SlocImportId,
                    StorageBinImportId = req.StorageBinImportId,
                    //Mã NVL
                    ProductCode = warehouseTranfer.ProductCode,
                    //Ngày xuất nhập kho
                    ExportDate = warehouseTranfer.ExportDate,
                    ExportDateKey = warehouseTranfer.ExportDateKey,
                    ImportDate = dateNow,
                    ImportDateKey = int.Parse(dateNowStr),
                    //Số lượng và ĐVT
                    Quantity = +warehouseTranfer.Quantity,
                    Unit = warehouseTranfer.Unit,
                    //Thẻ treo 
                    RawMaterialCardId = warehouseTranfer.RawMaterialCardId,
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                };
                _context.WarehouseTranferModel.Add(warehouseTranfer);

                //Save to WarehouseTransaction
                _context.WarehouseTransactionModel.Add(new WarehouseTransactionModel
                {
                    //Id
                    WarhouseTransactionId = Guid.NewGuid(),
                    //NVL
                    ProductCode = reservation.MaterialNumber,
                    //Kho
                    SlocId = req.SlocImportId,
                    //Vị trí kho
                    StorageBinId = req.StorageBinImportId,
                    //ReservationId
                    ReferenceDocumentId = warehouseuImport.WarehouseTranferId,
                    //Số lượng và ĐVT
                    Quantity = +warehouseTranfer.Quantity,
                    Unit = warehouseTranfer.Unit,
                    //ReservationCode
                    ReferenceDocumentText = reservation.ReservationNumber,
                    //Loại giao dịch kho = 315. Chuyển nhập kho
                    MovementType = MovementType.ReceiveTwoStep,
                    //Document Date
                    DocumentDate = dateNow,
                    DateKey = int.Parse(dateNowStr),
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true
                });

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Chuyển nhập kho")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        #endregion

        #region Get thông tin đã chuyển kho

        /// <summary>Get thông tin đã chuyển kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/DetailReservationImported?ReservationId={reservationId}'
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "reservationId": "A0AF5F36-D205-4FBA-A4EA-3286CC8911F5",
        ///                "slocImportId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinImportId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///               "code": 200,
        ///               "isSuccess": true,
        ///               "message": "Chuyển nhập kho thành công.",
        ///               "data": {
        ///                 "rawMaterialCardId": "d58d7300-1d35-4ddc-8f80-34a75c9c9668",
        ///                 "slocExport": "TP04",
        ///                 "warehouseNoExport": "100",
        ///                 "storageBinExport": "91K053-02X",
        ///                 "slocImport": "TP04",
        ///                 "warehouseNoImport": "100",
        ///                 "storageBinImport": "91K053-02X",
        ///                 "quantity": 500,
        ///                 "unit": "Thanh"
        ///               },
        ///               "additionalData": null
        ///             }
        /// </remarks>
        [HttpGet("DetailReservationImported")]
        public async Task<IActionResult> GET([FromQuery] DetailReservationImportedRequest req)
        {
            try
            {
                var listQtyImported = new List<QuantityImportedResponse>();

                //Tìm dữ liệu nhập kho. Đã có dữ liệu xuất kho và dữ liệu nhập kho (Tìm theo kho)
                var warehouseTranfer = await _context.WarehouseTranferModel.FirstOrDefaultAsync(x => x.ReservationId == req.ReservationId &&
                                                                                                     x.SlocExportId.HasValue &&
                                                                                                     x.SlocImportId.HasValue);
                //Check dữ liệu xuất kho
                if (warehouseTranfer == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Dữ liệu nhập kho") });

                //Get số lượng đã nhập kho
                var warehouseTransactions = await _context.WarehouseTransactionModel.Where(x => x.ReferenceDocumentId == warehouseTranfer.WarehouseTranferId && x.Quantity > 0 && x.MovementType == MovementType.DeliverOneStep).AsNoTracking().ToListAsync();

                //Thông tin nhập kho
                var wT = warehouseTransactions.FirstOrDefault();

                //Kho
                var slocExport = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == warehouseTranfer.SlocExportId);
                var slocImport = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == warehouseTranfer.SlocImportId);

                //Vị trị kho
                var storageBinExport = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == warehouseTranfer.StorageBinExportId);
                var storageBinImport = await _context.StorageBinModel.FirstOrDefaultAsync(x => x.StorageBinId == warehouseTranfer.StorageBinImportId);

                //Sản phẩm
                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == wT.ProductCode);

                //Call SAP get số lượng tồn kho theo material code
                var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                {
                    LGORT = slocExport.Sloc,
                    MATNR = wT.ProductCode,
                    WERKS = CurrentUser?.SaleOrg,
                    CHARG = wT.Batch
                });


                //List SO, SOLine
                var listSOSOline = warehouseTransactions.Select(x => new ZST_MES_SOLINE
                {
                    //SO
                    VBELN = x.SO,
                    //SO Line
                    POSNR = x.SOLine,
                }).ToArray();

                //List WBS
                var listWBS = warehouseTransactions.Select(x => new ZST_MES_PROJN
                {
                    //WBS
                    PROJN = x.WBS,
                }).ToArray();

                var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                {
                    //SO và SOLine
                    SOLINES = listSOSOline,
                    PROJN = listWBS,
                    WERKS = CurrentUser?.SaleOrg
                });
                var listLSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse.LIST_LSXDT;

                foreach (var x in warehouseTransactions)
                {
                    var qtyImported = new QuantityImportedResponse
                    {
                        SO = x.SO,
                        SOLine = x.SOLine,
                        WBS = x.WBS,
                        QuantityImported = x.Quantity,
                        Unit = x.Unit
                    };

                    //SO/SOLine
                    var soSoLine = $"{x.SO}/ {x.SOLine}";

                    //Số lượng tồn kho theo SO của reservation
                    qtyImported.QuantityStock = querySAP?.ZFM_MES_STOCKResponse.STOCKLIST?.Where(x => x.SSNUM == soSoLine)?.Sum(x => x.LABST);


                    //SO và SOLine null thì không có WBS và ngược lại
                    if (!string.IsNullOrEmpty(qtyImported.SO))
                    {
                        //LSX đại trà
                        qtyImported.LSXDT = listLSXDT?.Where(k => k.VBELN == qtyImported.SO && k.POSNR == qtyImported.SOLine)?.FirstOrDefault()?.ZZLSX;
                    }
                    if (!string.IsNullOrEmpty(qtyImported.WBS))
                    {
                        //LSX đại trà
                        qtyImported.LSXDT = listLSXDT?.Where(k => k.PROJN == qtyImported.WBS)?.FirstOrDefault()?.ZZLSX;
                    }

                    listQtyImported.Add(qtyImported);
                }

                listQtyImported = listQtyImported.OrderBy(x => x.PO).ThenBy(x => x.POLine).ToList();

                var response = new InforTranfer
                {
                    //Số lô
                    BatchNumber = wT?.Batch,
                    //Nhà máy
                    Plant = slocExport?.Plant,
                    //Kho
                    SlocExport = slocExport?.Sloc,
                    WarehouseNoExport = slocExport?.WarehouseNo,
                    //Vị trí kho
                    StorageBinExport = storageBinExport?.StorageBin,
                    SlocImport = slocImport?.Sloc,
                    WarehouseNoImport = slocImport?.WarehouseNo,
                    //Vị trí kho
                    StorageBinImport = storageBinImport?.StorageBin,
                    QuantityImporteds = listQtyImported,
                    ProductCode = wT.ProductCode,
                    ProductName = product?.ProductName,
                    SumQuantityImported = listQtyImported.Sum(x => x.QuantityImported)
                };


                return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        #endregion

        #endregion

        #region Xuất vào sản xuất

        #region Get danh sách theo SO/WBS theo số lô SAP
        /// <summary>Get danh sách theo SO/WBS theo số lô SAP</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetSOWBSByBatch
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///             {
        ///               "productCode": "250000037",
        ///               "plant": "1000",
        ///               "sloc": "N010",
        ///               "batchNumber": "TT28021904"
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///            {
        ///               "code": 200,
        ///               "isSuccess": true,
        ///               "message": "GET dữ liệu SO/WBS thành công.",
        ///               "data": [
        ///                   {
        ///                     "so": null,
        ///                     "soLine": null,
        ///                     "wbs": null,
        ///                     "quantity": 23,
        ///                     "unit" : "M3"
        ///                   },
        ///                   {
        ///                     "so": "2250000054",
        ///                     "soLine": "10",
        ///                     "wbs": null,
        ///                     "quantity": 41,
        ///                     "unit" : "M3"
        ///                   },
        ///                   {
        ///                   "so": "2250000054",
        ///                     "soLine": "20",
        ///                     "wbs": null,
        ///                     "quantity": 18,
        ///                     "unit" : "M3"
        ///                   }
        ///                 ],
        ///                 "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("GetListSOWBSByBatch")]
        public async Task<IActionResult> POST([FromBody] GetSOWBSByBatchRequest req)
        {
            try
            {
                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == req.ProductCode);
                if (product == null)
                {
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"NVL {req.ProductCode}") });
                }

                //Check sloc
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == req.Sloc);
                if (sloc == null)
                {
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"Sloc {req.Sloc}") });
                }

                var response = new List<GetSOWBSByBatchResponse>();

                var warehouseRepo = new WarehouseRepository(_context);

                response = await warehouseRepo.GetSOWBSByBatch(req, product.MEINS, CurrentUser?.SaleOrg);

                //await _context.SaveChangesAsync();

                foreach (var item in response)
                {
                    if(item.Unit == "DAY"){
                        item.Unit = "DAA";
                    }
                }

                //#if DEBUG

                //#endif

                if (_env.IsDevelopment())
                {

                    var mockList = new List<GetSOWBSByBatchResponse>()
                    {
                        new GetSOWBSByBatchResponse
                        {
                            SO = "2200002865",
                            SOLine = "160",
                            WBS = "",
                            Unit = "TAM",
                            LSXDT = "DT-447-23-DCN-TRINH",
                            Quantity = 10
                        },
                        new GetSOWBSByBatchResponse
                        {
                            SO = "2200002865",
                            SOLine = "170",
                            WBS = "",
                            Unit = "TAM",
                            LSXDT = "DT-447-23-DCN-TRINH",
                            Quantity = 5
                        },
                    };

                    // Slock 10KT
                    var mockListTonTron = new List<GetSOWBSByBatchResponse>()
                    {
                        new GetSOWBSByBatchResponse
                        {
                            LSXDT = "DT-194-24-ART-LOI-D2",
                            Quantity = (decimal?)22.7,
                            SO = null,
                            SOLine = null,
                            Unit = "M2",
                            WBS = "PRJ2-2092/1/1/013"
                        },
                        new GetSOWBSByBatchResponse
                        {
                            LSXDT = null,
                            Quantity = (decimal?)111.835,
                            SO = null,
                            SOLine = null,
                            Unit = "M2",
                            WBS = null
                        }
                    };

                    //response = response.Concat(mockList).ToList();
                }
                else
                {

                }

                return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = response });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region GET số lượng tồn theo SO/WBS đã chọn
        /// <summary>GET số lượng tồn theo SO/WBS đã chọn</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetSOWBSByBatch
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///             {
        ///               "productCode": "250000037",
        ///               "plant": "1000",
        ///               "sloc": "N010",
        ///               "batchNumber": "TT28021904",
        ///               "sowbSs": [
        ///                 {
        ///                   "so": "2250000054",
        ///                   "soLine": "10"
        ///                 },
        ///                 {
        ///                   "so": "2250000054",
        ///                   "soLine": "20"
        ///                 }
        ///               ]
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///            {
        ///               "code": 200,
        ///               "isSuccess": true,
        ///               "data": 
        ///                   {
        ///                     "quantity": 59
        ///                   }
        ///                 ],
        ///                 "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("GetInventoryBySOWBS")]
        public async Task<IActionResult> POST([FromBody] GetInventoryBySOWBSRequest req)
        {
            try
            {
                var response = new GetQuantityInventoryBySOWBSResponse();

                //Check sản phẩm tồn tại
                var product = await _context.ProductModel.FirstOrDefaultAsync(x => x.ProductCode == req.ProductCode);
                if (product == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"NVL {req.ProductCode}") });

                //Check sloc
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Sloc == req.Sloc);
                if (sloc == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, $"Sloc {req.Sloc}") });

                //Call SAP get số lượng tồn kho theo material code
                var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                {
                    LGORT = req.Sloc,
                    MATNR = req.ProductCode,
                    WERKS = req.Plant,
                    CHARG = req.BatchNumber
                });

                decimal? quantityInventory = 0;

                //Response to SAP
                var responseToSAP = querySAP.ZFM_MES_STOCKResponse.STOCKLIST;
                //Check tồn tại Response to SAP
                if (responseToSAP.Any())
                {
                    //Check tồn tại request
                    if (req.SOWBSs.Any())
                    {
                        ////Không được chọn tồn trôn
                        //if (req.SOWBSs.Where(x => string.IsNullOrEmpty(x.SO) && string.IsNullOrEmpty(x.WBS)).Any())
                        //    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.MSG_NOT_ACCESS, "Tồn trơn") });

                        foreach (var x in req.SOWBSs)
                        {
                            //Số lượng theo SO/SOLine
                            if (x.SO is not null && x.SOLine is not null)
                            {
                                //Nối SO/ SOLine theo đúng định dạng SAP trả về
                                var SOSOLine = $"{x.SO}/ {x.SOLine}";

                                //Số lượng SO/SOLine
                                quantityInventory += responseToSAP.Where(e => e.SSNUM == SOSOLine).Sum(x => x.LABST);
                            }

                            // Số lượng theo WBS
                            if (x.WBS is not null)
                            {
                                // Số lượng theo WBS
                                quantityInventory += responseToSAP.Where(e => e.SSNUM == x.WBS).Sum(x => x.LABST);
                            }

                            // Số lượng theo tồn trơn
                            if (x.SO is null && x.WBS is null)
                            {
                                // Số lượng theo WBS
                                quantityInventory += responseToSAP.Where(e => string.IsNullOrEmpty(e.SSNUM)).Sum(x => x.LABST);
                            }
                        }

                    }
                }

                response.Quantity = quantityInventory;

                var unit = responseToSAP.FirstOrDefault()?.MEINS;

                if (unit == "DAY")
                {
                    unit = "DAA";
                }

                response.Unit = unit;

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Get số lô theo pallet NVL
        /// <summary>Get số lô theo pallet NVL</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetBatchNumberByPallet?RawMaterialCardId={rawMaterialCardId}'
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Get số lô thành công.",
        ///                 "data": {
        ///                   "batchNumber": "BH232131"
        ///                 },
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetBatchNumberByPallet")]
        public async Task<IActionResult> GET([FromQuery] GetBatchNumberByPalletRequest req)
        {
            try
            {
                //Lấy thông tin số lô ở bước nhập kho theo thẻ treo
                var wareHouseTransaction = await _context.WarehouseTransactionModel.FirstOrDefaultAsync(x => x.ReferenceDocumentId == req.RawMaterialCardId && x.MovementType == MovementType.Receive);
                if (wareHouseTransaction is null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.MSG_REQUIRED_RECEIVE });

                var response = new
                {
                    BatchNumber = wareHouseTransaction?.Batch
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Succes, "Get số lô theo pallet")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Thống kế NVL trong ngày
        /// <summary>Thống kế NVL trong ngày</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/MaterialUsedShift
        ///     Params: 
        ///             + version : 1    
        ///             
        /// Body
        /// 
        ///             {
        ///                "so": "00001551",
        ///                "soLine": "12",
        ///                "batchNumber": "DGO25122DH",
        ///                "quantity": 150,
        ///                "unit": "Thanh",
        ///                "slocId": "538A5C41-0FF8-4484-9DBF-007819A326B2",
        ///                "storageBinId": "7D5C7159-5C68-4B6B-ABDE-00000DE5A060",
        ///                "rawMaterialCardId": "D58D7300-1D35-4DDC-8F80-34A75C9C9668"
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///             {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Thống kê NVL sử dụng trong ca thành công.",
        ///                 "data": true,
        ///                 "additionalData": null
        ///             }
        /// </remarks>
        [HttpPost("MaterialUsedShift")]
        public async Task<IActionResult> POST([FromBody] MaterialUsedShiftRequest req)
        {
            try
            {
                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Pallet thẻ treo
                var palletNVL = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId);
                var manualPallerNVL = await _context.RawMaterialCardManualModel.FirstOrDefaultAsync(x => x.RawMaterialCardManualId == req.RawMaterialCardId);

                //Pallet NVL
                var productCode = palletNVL == null ? manualPallerNVL?.ProductCode : palletNVL.ProductCode;
                var idPallet = palletNVL == null ? manualPallerNVL?.RawMaterialCardManualId : palletNVL.RawMaterialCardId;

                //Check sloc
                var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == req.SlocId);
                if (sloc == null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Sloc") });

                //Call SAP get số lượng tồn kho theo material code
                var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
                {
                    LGORT = sloc?.Sloc,
                    MATNR = productCode,
                    WERKS = CurrentUser?.SaleOrg,
                    CHARG = req.BatchNumber
                });


                //Response to SAP
                var responseToSAP = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.ToList();

                decimal? quantityInventory = 0;

                //Check tồn tại Response to SAP
                if (responseToSAP.Any())
                {
                    //Check tồn tại request
                    if (req.SOWBSQuantities.Any())
                    {
                        ////Không được chọn tồn trôn
                        //if (req.SOWBSs.Where(x => string.IsNullOrEmpty(x.SO) && string.IsNullOrEmpty(x.WBS)).Any())
                        //    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.MSG_NOT_ACCESS, "Tồn trơn") });

                        foreach (var x in req.SOWBSQuantities)
                        {
                            //Số lượng theo SO/SOLine
                            if (x.SO is not null && x.SOLine is not null)
                            {
                                //Nối SO/ SOLine theo đúng định dạng SAP trả về
                                var SOSOLine = $"{x.SO}/ {x.SOLine}";

                                //Số lượng SO/SOLine
                                quantityInventory += responseToSAP.Where(e => e.SSNUM == SOSOLine).Sum(x => x.LABST);
                            }

                            // Số lượng theo WBS
                            if (x.WBS is not null)
                            {
                                // Số lượng theo WBS
                                quantityInventory += responseToSAP.Where(e => e.SSNUM == x.WBS).Sum(x => x.LABST);
                            }

                            // Số lượng theo tồn trơn
                            if (x.SO is null && x.WBS is null)
                            {
                                // Số lượng theo WBS
                                quantityInventory += responseToSAP.Where(e => string.IsNullOrEmpty(e.SSNUM)).Sum(x => x.LABST);
                            }
                        }
                    }

                    if (req.Quantity > quantityInventory)
                        return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.MSG_CHECKQUANTITY3, "sử dụng trong ngày", "số lượng tồn kho") });
                }

                if (req.SOWBSQuantities.Any())
                {
                    var newGuid = Guid.NewGuid();
                    //Save to WarhouseExportModel
                    _context.WarehouseExportModel.Add(new WarehouseExportModel
                    {
                        WarhouseExportId = newGuid,
                        //Số lô
                        Batch = req.BatchNumber,
                        //Số lượng và ĐVT
                        Quantity = req.Quantity,
                        Unit = req.Unit,
                        //Kho và vị trí kho
                        SlocId = req.SlocId,
                        StorageBinId = req.StorageBinId,
                        //Thẻ treo
                        RawMaterialCardId = idPallet,
                        //NVL
                        ProductCode = productCode,
                        //Loại giao dịch
                        MovementType = MovementType.MaterialUsedShift,
                        //Mã công đoạn
                        StepCode = req.StepCode,
                        //Document Date
                        DocumentDate = dateNow,
                        DateKey = int.Parse(dateNowStr),
                        //Common
                        CreateTime = dateNow,
                        CreateBy = CurrentUser?.AccountId,
                        Actived = true,
                        Plant = CurrentUser?.SaleOrg
                    });

                    // loop each req.SOWBSQuantities and insert into _context.WarehouseExportDetailModel
                    foreach (var x in req.SOWBSQuantities)
                    {
                        decimal? soLuongSX = null;
                        if (x.SoLuongSX == 0 || x.SoLuongSX == null)
                        {
                            soLuongSX = null;
                        }
                        else
                        {
                            soLuongSX = x.SoLuongSX;
                        }

                        var SOWBS = "Tồn trơn";

                        var SOWBSTemp = "";// (x.SO + "/" + x.SOLine) ?? x.WBS;

                        if (!string.IsNullOrEmpty(x.SO))
                        {
                            SOWBSTemp = x.SO + "/" + x.SOLine;
                        }

                        if (!string.IsNullOrEmpty(x.WBS))
                        {
                            SOWBSTemp = x.WBS;
                        }

                        if (!string.IsNullOrEmpty(SOWBSTemp))
                        {
                            SOWBS = SOWBSTemp;
                        }

                        _context.WarehouseExportDetailModel.Add(new WarehouseExportDetailModel
                        {
                            WarhouseExportId = newGuid,
                            SOWBS = SOWBS, // there can be SO, SOLine, WBS, get the data
                            //Update 1: add quantity
                            //Quantity = x.SoLuongSX
                            Quantity = soLuongSX
                        });
                    }

                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Thống kê NVL sử dụng trong ca")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region Get thông tin NVL chưa sử dụng hết

        /// <summary>Get thông tin NVL chưa sử dụng hết</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetInforMaterialUsedShift?RawMaterialCardId={}
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Get số lượng sử dụng trong ca thành công.",
        ///                 "data": {
        ///                   "Quantity": "250",
        ///                   "Unit": "Thanh"
        ///                 },
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetInforMaterialUsedShift")]
        public async Task<IActionResult> GET([FromQuery] GetInforMaterialUsedShiftRequest req)
        {
            try
            {
                //Lấy thông tin nvl đã sử dụng trong ca
                var nvl = await _context.WarehouseExportModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == req.RawMaterialCardId &&
                                                                                       x.DocumentDate.Value.Date == DateTime.Now.Date &&
                                                                                       x.MovementType == MovementType.MaterialUsedShift);
                if (nvl is null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.MSG_PALLET_INVALID });

                //Product Code
                var productCode = nvl?.ProductCode;

                //Response
                var response = new
                {

                    //Mã sản phẩm
                    ProductCode = productCode,
                    //Tên sản phẩm
                    ProductName = _context.ProductModel.FirstOrDefault(x => x.ProductCode == productCode)?.ProductName,
                    //Số lô
                    BatchNumber = nvl?.Batch
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Succes, "Get thông tin NVL chưa sử dụng hết")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        /// <summary>
        /// GET danh sách NVL đã sử dụng trong ca
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetListMaterialUsed")]
        public async Task<IActionResult> GET()
        {
            try
            {
                var response = new List<GetBatchByProductResponse>();

                //Lấy thông tin danh sách nvl đã sử dụng trong ca
                var listNVLUsed = await _context.WarehouseExportModel.Where(x => x.MovementType == MovementType.MaterialUsedShift && x.DocumentDate.Value.Date == DateTime.Now.Date).AsNoTracking().ToListAsync();

                //Danh sách số lô theo mã NVL
                var listBatchByNumber = listNVLUsed.GroupBy(x => x.ProductCode, (k, v) => new BatchByProductResponse { ProductCode = k, WareHouserExports = v.ToList() }).ToList();

                listBatchByNumber.ForEach(e =>
                {
                    var productName = _context.ProductModel.FirstOrDefault(x => x.ProductCode == e.ProductCode)?.ProductName;

                    var obj = new GetBatchByProductResponse
                    {
                        //Hiển thị
                        ProductDisplay = $"{e.ProductCode} | {productName}",
                        ProductName = productName,
                        //Mã nvl
                        ProductCode = e.ProductCode,
                        //Danh sách số lô theo NVL
                        BatchsNumber = e.WareHouserExports.Select(x => x.Batch).Distinct().ToList()
                    };

                    response.Add(obj);
                });

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = null
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }

        #endregion

        #region Get danh sách mã công đoạn theo Mã NVL, và số lô
        /// <summary>
        /// Get danh sách mã công đoạn theo Mã NVL, và số lô
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetStepByProductBach")]
        public async Task<IActionResult> GET([FromQuery] GetStepByProductBach req)
        {
            try
            {
                var response = new GetStepByProductResponse();

                //Lấy thông tin danh sách nvl đã sử dụng trong ca
                var listNVLUsed = await _context.WarehouseExportModel.Where(x => x.MovementType == MovementType.MaterialUsedShift && x.ProductCode == req.ProductCode && x.Batch == req.BatchNumber && x.DocumentDate.Value.Date == DateTime.Now.Date).AsNoTracking().ToListAsync();

                //Danh sách công đoạn
                var listStep = _context.RoutingModel.AsNoTracking();

                var stepByPrdBatchs = listNVLUsed.Select(x => new StepByProduct
                {
                    StepCode = x.StepCode,
                    StepCodeDisplay = $"{x.StepCode} | {listStep.FirstOrDefault(e => e.StepCode == x.StepCode)?.StepName}"
                }).Distinct().ToList();

                //NVL
                response.ProductCode = req.ProductCode;
                //Số lô
                response.BatchNumber = req.BatchNumber;
                //Danh sách công đoạn
                response.Steps = stepByPrdBatchs;

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = null
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region GET số lượng sử dụng trong ngày
        /// <summary>GET số lượng sử dụng trong ngày</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetQuantityMaterialUsedShift?RawMaterialCardId={}
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Get số lượng sử dụng trong ngày thành công.",
        ///                 "data": {
        ///                   "Quantity": "250",
        ///                   "Unit": "Thanh"
        ///                 },
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetQuantityMaterialUsedShift")]
        public async Task<IActionResult> GET([FromQuery] GetQuantityMaterialUsedShiftRequest req)
        {
            try
            {
                var response = new QuantityResponse();

                var wTs = _context.WarehouseExportModel.Where(x => x.ProductCode == req.ProductCode && x.Batch == req.BatchNumber && x.DocumentDate.Value.Date == DateTime.Now.Date && x.StepCode == req.StepCode).AsNoTracking();

                //Lấy thông tin nvl đã sử dụng trong ngày theo Mã NVL, Số lô, Mã công đoạn
                var nvlUsedShifhts = wTs.Where(x => x.MovementType == MovementType.MaterialUsedShift);

                //Lấy thông tin nvl trả lại trong ngày 
                var nvlUnUsed = wTs.Where(x => x.MovementType == MovementType.MaterialUnused);
                var nvlUsedShifht = await nvlUsedShifhts?.FirstOrDefaultAsync();

                if (!nvlUsedShifhts.Any())
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = $"NVL {req.ProductCode} với số lô {req.BatchNumber} chưa được được sử dụng trong ca" });

                //Danh sách sl sử dụng trong ngày theo sloc
                var qtyUseBySloc = nvlUsedShifhts.GroupBy(x => x.SlocId, (k, v) => new { SlocId = k, WarehouseExports = v.ToList() }).ToList();

                var qtyReponse = new List<QuantityBySlocResponse>();
                foreach (var item in qtyUseBySloc)
                {
                    var objQty = new QuantityBySlocResponse();
                    //Sloc
                    var sloc = await _context.SlocModel.FirstOrDefaultAsync(x => x.Id == item.SlocId);

                    //Số lượng thống kê sử dụng trong ngày
                    var qtyUsedDay = item.WarehouseExports.Sum(x => x.Quantity);

                    //Số lượng trả lại
                    var qtyUnUsed = nvlUnUsed.Where(x => x.SlocId == item.SlocId).Sum(x => x.Quantity);

                    //Số lượng sử dụng trong ngày (số lượng đã thống kê sd trong ngày - số lượng trả lại)
                    var remainQty = qtyUsedDay - qtyUnUsed;

                    //Sloc
                    objQty.SlocDisplay = $"{sloc?.Sloc} | {sloc.SlocName}";
                    objQty.SlocId = item.SlocId;
                    //Số lượng và đvt
                    objQty.Quantity = remainQty;
                    objQty.Unit = nvlUsedShifht?.Unit;
                    qtyReponse.Add(objQty);

                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = qtyReponse,
                    Message = string.Format(CommonResource.Msg_Succes, "Get số lượng sử dụng trong ca")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        private class QuantityResponse
        {
            public decimal? Quantity { get; set; }
            public string Unit { get; set; }
        }
        #endregion

        #region Xác định NVL chưa sử dụng hết 
        /// <summary>Xác định NVL chưa sử dụng hết </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/MaterialUnused
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Get số lượng sử dụng trong ca thành công.",
        ///                 "data": true,
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpPost("MaterialUnused")]
        public async Task<IActionResult> POST([FromBody] MaterialUnusedRequest req)
        {
            try
            {
                var wTs = _context.WarehouseExportModel.Where(x => x.SlocId == req.SlocId && x.ProductCode == req.ProductCode && x.Batch == req.BatchNumber && x.DocumentDate.Value.Date == DateTime.Now.Date && x.StepCode == req.StepCode).AsNoTracking();

                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                //Sum số lượng theo mã NVL và số lô và Mã công đoạn trong ngày
                var quantityUsed = wTs.Where(x => x.MovementType == MovementType.MaterialUsedShift).Sum(x => x.Quantity);
                //Số lượng trả lại
                var qtyUnUsed = wTs.Where(x => x.MovementType == MovementType.MaterialUnused).Sum(x => x.Quantity);

                var qtyUseDay = quantityUsed - qtyUnUsed;

                //Kiểm tra số lượng trả lại và số lượng đã xuất sử dụng
                if (req.Quantity > qtyUseDay)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = MESP2Resource.MSG_CHECKQUANTITY });

                _context.WarehouseExportModel.Add(new WarehouseExportModel
                {
                    WarhouseExportId = Guid.NewGuid(),
                    //Số lô
                    Batch = req.BatchNumber,
                    //Số lượng và ĐVT
                    SlocId = req.SlocId,
                    Quantity = req.Quantity,
                    Unit = req.Unit,
                    //Kho và vị trí kho
                    //Thẻ treo
                    RawMaterialCardId = req.RawMaterialCardId,
                    ProductCode = req.ProductCode,
                    //Loại giao dịch
                    MovementType = MovementType.MaterialUnused,
                    //Document Date
                    DocumentDate = dateNow,
                    DateKey = int.Parse(dateNowStr),
                    //Mã công đoạn
                    StepCode = req.StepCode,
                    //Common
                    CreateTime = dateNow,
                    CreateBy = CurrentUser?.AccountId,
                    Actived = true,
                    Plant = CurrentUser?.SaleOrg
                });

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Ghi nhận NVL chưa sử dụng hết")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region GET SO/SOLine theo LSX
        /// <summary>GET SO/SOLine theo LSX</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetSOWBSByTask?TaskId={}
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "GET SO/SOLine theo LSX thành công.",
        ///                 "data": {
        ///                   "SO": "250",
        ///                   "SOLine": "Thanh" 
        ///                 },
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetSOWBSByTask")]
        public async Task<IActionResult> GET([FromQuery] GetSOWBSByTaskRequest req)
        {
            try
            {
                //Lệnh sản xuất
                var lsx = await _context.ThucThiLenhSanXuatModel.FirstOrDefaultAsync(x => x.Barcode == req.TaskId);
                if (lsx is null)
                    return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "LSX") });


                //SO/SOLine
                //SO/SOLine
                var response = new
                {
                    SO = lsx?.Property1,
                    SOLine = lsx?.Property2,
                    WBS = ""

                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = response,
                    Message = string.Format(CommonResource.Msg_Succes, "GET SO/SOLine theo LSX")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #region GET danh sách sloc của NVL sử dụng trong ca
        /// <summary>GET danh sách sloc của NVL sử dụng trong ca</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/GetSlocByProduct?ProductCode={}
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "data": [
        ///                   {
        ///                     "Id": "",
        ///                   }"Sloc": "" 
        ///                 ],
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpGet("GetSlocByProduct")]
        public async Task<IActionResult> GET([FromQuery] GetSlocByProductRequest req)
        {
            //Danh sách sloc
            var listSloc = _context.WarehouseExportModel.Where(x => x.ProductCode == req.ProductCode && x.DocumentDate.Value.Date == DateTime.Now.Date && x.MovementType == MovementType.MaterialUsedShift)
                                                        .Include(x => x.Sloc).Distinct();
            //Check tồn tại
            if (!listSloc.Any())
                return Ok(new ApiResponse { Code = 400, Message = MESP2Resource.MSG_MATERIAL_USED, IsSuccess = false, Data = false });

            var response = await listSloc.Select(x => new GetSlocByProductResponse
            {
                Id = x.SlocId.Value,
                Sloc = $"{x.Sloc.Sloc} | {x.Sloc.SlocName}"
            }).ToListAsync();

            return Ok(new ApiResponse { Code = 200, Data = response, IsSuccess = true });
        }
        #endregion

        #region Xác định NVL hàng lẻ
        /// <summary>Xác định NVL cho trường hợp hàng lẻ </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/MaterialRetail
        ///     Params: 
        ///             + version : 1    
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Xác định NVL cho trường hợp hàng lẻ thành công.",
        ///                 "data": true,
        ///                 "additionalData": null
        ///               }
        /// </remarks>
        [HttpPost("MaterialRetail")]
        public async Task<IActionResult> POST([FromBody] MaterialRetailRequest req)
        {
            try
            {
                //Date now
                var dateNow = DateTime.Now;
                var dateNowStr = DateTime.Now.ToString(DateTimeFormat.DateKey);

                if (req.MaterialRetailDetails.Any())
                {
                    var wTs = _context.WarehouseExportModel.AsNoTracking();

                    foreach (var item in req.MaterialRetailDetails)
                    {
                        //Lấy thông tin nvl đã sử dụng trong ca
                        var nvl = await wTs.FirstOrDefaultAsync(x => x.RawMaterialCardId == item.RawMaterialCardId && x.MovementType == MovementType.MaterialUsedShift);

                        //Mã NVL và số lô
                        var productCode = nvl?.ProductCode;
                        var batchNumber = nvl?.Batch;

                        //Danh sách xuất kho theo NVL ,Số lô, Mã công đoạn 
                        var wExports = wTs.Where(x => x.ProductCode == productCode && x.StepCode == item.StepCode && x.Batch == batchNumber && x.DocumentDate.Value.Date == DateTime.Now.Date && x.SlocId == item.SlocId);

                        //Sum số lượng theo mã NVL và số lô trong ngày và số lượng trả lại
                        var quantityUsed = wExports.Where(x => x.MovementType == MovementType.MaterialUsedShift).Sum(x => x.Quantity);
                        var quantityUnUsed = wExports.Where(x => x.MovementType == MovementType.MaterialUnused).Sum(x => x.Quantity);

                        var remainQty = quantityUsed - quantityUnUsed;

                        //Kiểm tra số lượng xuất vào lệnh sản xuất và số lượng đã xuất sử dụng
                        if (item.Quantity > remainQty)
                            return Ok(new ApiResponse { Code = 400, IsSuccess = false, Message = string.Format(MESP2Resource.MSG_CHECKQUANTITY2, productCode) });

                        var warehouseExport = new WarehouseExportModel
                        {
                            WarhouseExportId = Guid.NewGuid(),
                            //Kho
                            SlocId = item.SlocId,
                            //LSX
                            ReferenceDocumentId = req.TaskId,
                            //Công đoạn
                            StepCode = item.StepCode,
                            //Số lô
                            Batch = item.BatchNumber,
                            //Số lượng và ĐVT
                            Quantity = item.Quantity,
                            Unit = item.Unit,
                            //Kho và vị trí kho
                            //Thẻ treo
                            RawMaterialCardId = item.RawMaterialCardId,
                            ProductCode = productCode,
                            //Loại giao dịch
                            MovementType = MovementType.Deliver,
                            //Document Date
                            DocumentDate = dateNow,
                            DateKey = int.Parse(dateNowStr),
                            //Common
                            CreateTime = dateNow,
                            CreateBy = CurrentUser?.AccountId,
                            Actived = true,
                            Plant = CurrentUser?.SaleOrg
                        };
                        _context.WarehouseExportModel.Add(warehouseExport);
                    }
                }

                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Xác định NVL cho trường hợp hàng lẻ")
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion

        #endregion
    }
}
