# TTF_MES_Mobile Project Documentation

## Table of Contents
1. [Code Guidelines](#code-guidelines)
2. [Downtime Feature](#downtime-feature)
3. [Timeout Pattern](#timeout-pattern)
4. [Project Structure](#project-structure)
5. [API Documentation](#api-documentation)
6. [Testing Strategy](#testing-strategy)
7. [Best Practices](#best-practices)
8. [Future Enhancements](#future-enhancements)

## Code Guidelines
[Refer to CODE_GUIDELINES.md for detailed coding standards and conventions]

## Downtime Feature
### Overview
The Downtime feature tracks and manages production downtime events in a manufacturing environment.

### Key Components
- List View (`DowntimeList.dart`)
- Detail View (`DowntimeDetail.dart`)
- Data Models (`DowntimeRecord`, `DowntimeSearchModel`, `DowntimeHistory`)

### API Integration
- List Operations
- Detail Operations
- Master Data

### Status Workflow
1. Created
2. Pending
3. Approved
4. Rejected

[Refer to downtime-feature.md for complete documentation]

## Timeout Pattern
### Overview
Handles session expiration and forces users to re-authenticate when their token expires.

### Key Components
- `isTokenLive()` validation function
- Timeout state management
- Conditional rendering of timeout view
- Platform-specific dialogs

[Refer to timeout-pattern.md for complete documentation]

## Project Structure
```
lib/
├── controllers/
├── models/
├── pages/
├── repositories/
├── widgets/
├── utils/
└── main.dart
```

[Refer to CODE_GUIDELINES.md for detailed project structure]

## API Documentation
### Base Configuration
- QAS and PRD environments
- Authentication
- Endpoints

[Refer to downtime-api.md for complete API documentation]

## Testing Strategy
### Mock Data Testing
- Set `useMockData = true`
- Test CRUD operations
- Verify history display

### API Testing
- Set `useMockData = false`
- Test with QAS and PRD environments

[Refer to downtime-feature-changes.md for testing notes]

## Best Practices
### Code Quality
- Follow Effective Dart guidelines
- Use proper error handling
- Implement proper state management

### UI/UX
- Consistent design patterns
- Responsive layouts
- Clear user feedback

[Refer to CODE_GUIDELINES.md and downtime-feature.md for more details]

## Future Enhancements
- Enhanced filtering capabilities
- Batch operations
- Advanced reporting
- Additional API endpoints

[Refer to downtime-feature.md for complete list of planned improvements]