import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../model/qualityControlApi.dart';
import '../urlApi/urlApi.dart';

class GalleryWidget extends StatefulWidget {
  final PageController pageController;
  final List<FileViewModel>? imageComment;
  final int index;

  GalleryWidget({Key? key, required this.imageComment, this.index = 0})
      : pageController = PageController(initialPage: index),
        super(key: key);

  @override
  _GalleryWidgetState createState() => _GalleryWidgetState();
}

class _GalleryWidgetState extends State<GalleryWidget> {
  late int _index;

  late String imageUrl;

  @override
  void initState() {
    _index = widget.index;
    super.initState();
    _setup();
  }

  void _setIndex(int index) {
    setState(() {
      _index = index;
    });
  }

  Future<void> _setup() async {
    final environment = await SecureStorage.getString("environment", null);
    final url = environment == 'PRD' ? UrlApi.urlImagePrd : await UrlApi.urlImageQas;
    setState(() {
      imageUrl = url;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        body: _ListImageView(
          imageComment: widget.imageComment,
          index: _index,
          pageController: widget.pageController,
          setIndex: _setIndex,
          imageUrl: imageUrl,
        ));
  }
}

class _ListImageView extends StatelessWidget {
  final List<FileViewModel>? imageComment;
  final int index;
  final PageController pageController;
  final ValueChanged<int> setIndex;

  final String imageUrl;

  const _ListImageView({
    Key? key,
    required this.index,
    required this.imageComment,
    required this.pageController,
    required this.setIndex,
    required this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Column(children: [
      Container(
        color: Colors.black,
        alignment: Alignment.topLeft,
        child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              Navigator.pop(context);
            }),
      ),
      Expanded(
        child: PhotoViewGallery.builder(
          pageController: pageController,
          itemCount: imageComment!.length,
          builder: (context, index) {
            final imageComm = imageComment![index];
            return PhotoViewGalleryPageOptions(
                imageProvider: NetworkImage(imageUrl + imageComm.fileUrl.toString()),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.contained * 4);
          },
          onPageChanged: setIndex,
        ),
      ),
      Container(
          alignment: Alignment.bottomCenter,
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
          child: Text(
            'Hình ${index + 1} / ${imageComment!.length}',
            style: TextStyle(color: Colors.white, fontSize: 15.sp),
          )),
    ]));
  }
}
