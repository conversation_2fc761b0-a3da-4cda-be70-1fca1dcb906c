import 'dart:io';

import 'package:flutter/src/widgets/editable_text.dart';
import 'package:ttf/model/GetDefectLevel.dart';
import 'package:ttf/model/HangMucKiemTraVm.dart';
import 'package:ttf/page/KiemTraChatLuong/model/CongDoanInfoVm.dart';

class QualityControlModel {
  QualityControl? qualityControl;
  List<ResultList>? resultList;
  List<QualityTypeList>? qualityTypeList;
  List<QualityCheckerInfo>? qualityCheckerList;
  List<TestMethodList>? testMethodList;

  List<DropdownItemList>? congDoanNhoList;
  List<DropdownItemList>? congDoanLoiList;

  List<DropdownItemList>? phuongAnXuLyList;

  List<DropdownItemList>? limitList;

  List<SamplingLevelList>? samplingLevelList;
  List<ThongTinKiemTra>? qualityControlInformationIdList;
  List<ErrorList>? errorList;

  List<QualityTypeList>? nghiemThuTypeList;

  List<DataGetDefectLevel>? defectLevel;
  List<HangMucKiemTraVm>? hangMucKiemTra;
  List<DropdownItemList>? caNhanGayLoiList;

  CongDoanInfoVm? congDoanInfoVm;

  bool? isTTFBarcode;
  String? po;
  // defectLevel

  QualityControlModel({
    this.qualityControl,
    this.resultList,
    this.qualityTypeList,
    this.qualityCheckerList,
    this.testMethodList,
    this.congDoanNhoList,
    this.phuongAnXuLyList,
    this.limitList,
    this.samplingLevelList,
    this.qualityControlInformationIdList,
    this.errorList,
    this.nghiemThuTypeList,
    this.defectLevel,
    this.hangMucKiemTra,
    this.caNhanGayLoiList,
    this.congDoanInfoVm,
    this.congDoanLoiList,
    this.isTTFBarcode,
    this.po,
  });

  QualityControlModel.fromJson(Map<String, dynamic> json) {
    qualityControl = json['qualityControl'] != null ? QualityControl.fromJson(json['qualityControl']) : null;
    if (json['resultList'] != null) {
      resultList = <ResultList>[];
      json['resultList'].forEach((v) {
        resultList!.add(ResultList.fromJson(v));
      });
    }
    if (json['qualityTypeList'] != null) {
      qualityTypeList = <QualityTypeList>[];
      json['qualityTypeList'].forEach((v) {
        qualityTypeList!.add(QualityTypeList.fromJson(v));
      });
    }
    if (json['qualityCheckerList'] != null) {
      qualityCheckerList = <QualityCheckerInfo>[];
      json['qualityCheckerList'].forEach((v) {
        qualityCheckerList!.add(QualityCheckerInfo.fromJson(v));
      });
    }
    if (json['testMethodList'] != null) {
      testMethodList = <TestMethodList>[];
      json['testMethodList'].forEach((v) {
        testMethodList!.add(TestMethodList.fromJson(v));
      });
    }
    if (json['congDoanNhoList'] != null) {
      congDoanNhoList = <DropdownItemList>[];
      json['congDoanNhoList'].forEach((v) {
        congDoanNhoList!.add(DropdownItemList.fromJson(v));
      });
    }
    if (json['congDoanLoiList'] != null) {
      congDoanLoiList = <DropdownItemList>[];
      json['congDoanLoiList'].forEach((v) {
        congDoanLoiList!.add(DropdownItemList.fromJson(v));
      });
    }
    if (json['phuongAnXuLyList'] != null) {
      phuongAnXuLyList = <DropdownItemList>[];
      json['phuongAnXuLyList'].forEach((v) {
        phuongAnXuLyList!.add(DropdownItemList.fromJson(v));
      });
    }
    if (json['limitList'] != null) {
      limitList = <DropdownItemList>[];
      json['limitList'].forEach((v) {
        limitList!.add(DropdownItemList.fromJson(v));
      });
    }
    if (json['samplingLevelList'] != null) {
      samplingLevelList = <SamplingLevelList>[];
      json['samplingLevelList'].forEach((v) {
        samplingLevelList!.add(SamplingLevelList.fromJson(v));
      });
    }
    if (json['qualityControlInformationIdList'] != null) {
      qualityControlInformationIdList = <ThongTinKiemTra>[];
      json['qualityControlInformationIdList'].forEach((v) {
        qualityControlInformationIdList!.add(ThongTinKiemTra.fromJson(v));
      });
    }
    if (json['errorList'] != null) {
      errorList = <ErrorList>[];
      json['errorList'].forEach((v) {
        errorList!.add(ErrorList.fromJson(v));
      });
    }

    if (json['nghiemThuTypeList'] != null) {
      nghiemThuTypeList = <QualityTypeList>[];
      json['nghiemThuTypeList'].forEach((v) {
        nghiemThuTypeList!.add(QualityTypeList.fromJson(v));
      });
    }

    if (json['defectLevel'] != null) {
      defectLevel = <DataGetDefectLevel>[];
      json['defectLevel'].forEach((v) {
        defectLevel!.add(DataGetDefectLevel.fromJson(v));
      });
    }

    if (json['hangMucKiemTra'] != null) {
      hangMucKiemTra = <HangMucKiemTraVm>[];
      json['hangMucKiemTra'].forEach((v) {
        hangMucKiemTra!.add(HangMucKiemTraVm.fromJson(v));
      });
    }

    if (json['caNhanGayLoiList'] != null) {
      caNhanGayLoiList = <DropdownItemList>[];
      json['caNhanGayLoiList'].forEach((v) {
        caNhanGayLoiList!.add(DropdownItemList.fromJson(v));
      });
    }

    congDoanInfoVm = json['congDoanInfoVm'] != null ? CongDoanInfoVm.fromJson(json['congDoanInfoVm']) : null;

    isTTFBarcode = json['isTTFBarcode'];
    po = json['po'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (qualityControl != null) {
      data['qualityControl'] = qualityControl!.toJson();
    }
    if (resultList != null) {
      data['resultList'] = resultList!.map((v) => v.toJson()).toList();
    }
    if (qualityTypeList != null) {
      data['qualityTypeList'] = qualityTypeList!.map((v) => v.toJson()).toList();
    }
    if (qualityCheckerList != null) {
      data['qualityCheckerList'] = qualityCheckerList!.map((v) => v.toJson()).toList();
    }
    if (testMethodList != null) {
      data['testMethodList'] = testMethodList!.map((v) => v.toJson()).toList();
    }
    if (samplingLevelList != null) {
      data['samplingLevelList'] = samplingLevelList!.map((v) => v.toJson()).toList();
    }
    if (qualityControlInformationIdList != null) {
      data['qualityControlInformationIdList'] = qualityControlInformationIdList!.map((v) => v.toJson()).toList();
    }
    if (errorList != null) {
      data['errorList'] = errorList!.map((v) => v.toJson()).toList();
    }
    if (nghiemThuTypeList != null) {
      data['nghiemThuTypeList'] = nghiemThuTypeList!.map((v) => v.toJson()).toList();
    }
    if (defectLevel != null) {
      data['defectLevel'] = defectLevel!.map((v) => v.toJson()).toList();
    }
    if (hangMucKiemTra != null) {
      data['hangMucKiemTra'] = hangMucKiemTra!.map((v) => v.toJson()).toList();
    }
    if (caNhanGayLoiList != null) {
      data['caNhanGayLoiList'] = caNhanGayLoiList!.map((v) => v.toJson()).toList();
    }

    if (congDoanInfoVm != null) {
      data['congDoanInfoVm'] = congDoanInfoVm!.toJson();
    }

    data['isTTFBarcode'] = isTTFBarcode;
    data['po'] = po;

    return data;
  }
}

class QualityControl {
  String? productAttribute;
  String? qcType;
  String? qualityControlId;
  int? qualityControlCode;
  String? saleOrgCode;
  String? storeName;
  String? workShopCode;
  String? workShopName;
  String? workCenterCode;
  String? workCenterName;
  String? profileCode;
  String? profileName;
  String? lsxdt;
  String? lsxsap;
  String? dsx;
  String? productCode;
  String? productName;
  String? productType;
  String? customerReference;
  String? confirmDate;
  double? quantityConfirm;
  String? qualityDate;
  String? qualityChecker;
  String? qcSaleEmployee;
  double? inspectionLotQuantity;
  String? environmental;
  bool? status;
  String? result;
  String? qualityType;
  String? po;
  String? unit;
  double? qty;
  String? file;
  List<FileViewModel>? fileViewModel;
  QualityControlDetail? qualityControlDetail;
  List<QualityControlInformation>? qualityControlInformation;
  String? finishedColor;
  List<Error>? error;
  String? hangTagId;
  String? sku;
  String? tinhTrangMoiTruong;
  String? mauHoanThien;
  String? so;

  String? nhaGiaCong;
  String? khachHangId;
  String? nhaCungCap;
  String? barcode;
  String? stepCode;
  String? note;
  String? loaiMau;

  double? soLuongNhapKho;
  double? soLuongBlock;
  double? soLuongTraVe;

  QualityControl({
    this.productAttribute,
    this.qcType,
    this.qualityControlId,
    this.qualityControlCode,
    this.saleOrgCode,
    this.storeName,
    this.workShopCode,
    this.workShopName,
    this.workCenterCode,
    this.workCenterName,
    this.profileCode,
    this.profileName,
    this.lsxdt,
    this.lsxsap,
    this.dsx,
    this.productCode,
    this.productName,
    this.productType,
    this.customerReference,
    this.confirmDate,
    this.quantityConfirm,
    this.qualityDate,
    this.qualityChecker,
    this.qcSaleEmployee,
    this.inspectionLotQuantity,
    this.environmental,
    this.status,
    this.result,
    this.qualityType,
    this.po,
    this.unit,
    this.qty,
    this.file,
    this.fileViewModel,
    this.qualityControlDetail,
    this.qualityControlInformation,
    this.finishedColor,
    this.error,
    this.hangTagId,
    this.sku,
    this.tinhTrangMoiTruong,
    this.mauHoanThien,
    this.so,
    this.nhaGiaCong,
    this.khachHangId,
    this.nhaCungCap,
    this.barcode,
    this.stepCode,
    this.note,
    this.loaiMau,
    this.soLuongNhapKho,
    this.soLuongBlock,
    this.soLuongTraVe,
  });

  QualityControl.fromJson(Map<String, dynamic> json) {
    productAttribute = json['productAttribute'];
    qcType = json['qcType'];
    qualityControlId = json['qualityControlId'];
    qualityControlCode = json['qualityControlCode'];
    saleOrgCode = json['saleOrgCode'];
    storeName = json['storeName'];
    workShopCode = json['workShopCode'];
    workShopName = json['workShopName'];
    workCenterCode = json['workCenterCode'];
    workCenterName = json['workCenterName'];
    profileCode = json['profileCode'];
    profileName = json['profileName'];
    lsxdt = json['lsxdt'];
    lsxsap = json['lsxsap'];
    dsx = json['dsx'];
    productCode = json['productCode'];
    productName = json['productName'];
    productType = json['productType'];
    customerReference = json['customerReference'];
    confirmDate = json['confirmDate'];
    quantityConfirm = json['quantityConfirm'];
    qualityDate = json['qualityDate'];
    qualityChecker = json['qualityChecker'];
    qcSaleEmployee = json['qcSaleEmployee'];
    inspectionLotQuantity = json['inspectionLotQuantity'];
    environmental = json['environmental'];
    status = json['status'];
    result = json['result'];
    qualityType = json['qualityType'];
    po = json['po'];
    unit = json['unit'];
    qty = json['qty'];
    file = json['file'];
    finishedColor = json['finishedColor'];
    if (json['fileViewModel'] != null) {
      fileViewModel = <FileViewModel>[];
      json['fileViewModel'].forEach((v) {
        fileViewModel!.add(FileViewModel.fromJson(v));
      });
    }
    qualityControlDetail = json['qualityControlDetail'] != null ? QualityControlDetail.fromJson(json['qualityControlDetail']) : null;

    if (json['qualityControlInformation'] != null) {
      qualityControlInformation = <QualityControlInformation>[];
      json['qualityControlInformation'].forEach((v) {
        qualityControlInformation!.add(QualityControlInformation.fromJson(v));
      });
    }
    if (json['error'] != null) {
      error = <Error>[];
      json['error'].forEach((v) {
        error!.add(Error.fromJson(v));
      });
    }

    hangTagId = json['hangTagId'];
    sku = json['sku'];
    tinhTrangMoiTruong = json['tinhTrangMoiTruong'];
    mauHoanThien = json['mauHoanThien'];
    so = json['so'];

    nhaGiaCong = json['nhaGiaCong'];
    khachHangId = json['khachHangId'];
    nhaCungCap = json['nhaCungCap'];
    barcode = json['barcode'];
    stepCode = json['stepCode'];
    note = json['note'];
    loaiMau = json['loaiMau'];

    soLuongNhapKho = json['soLuongNhapKho'];
    soLuongBlock = json['soLuongBlock'];
    soLuongTraVe = json['soLuongTraVe'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productAttribute'] = productAttribute;
    data['qcType'] = qcType;
    data['qualityControlId'] = qualityControlId;
    data['qualityControlCode'] = qualityControlCode;
    data['saleOrgCode'] = saleOrgCode;
    data['storeName'] = storeName;
    data['workShopCode'] = workShopCode;
    data['workShopName'] = workShopName;
    data['workCenterCode'] = workCenterCode;
    data['workCenterName'] = workCenterName;
    data['profileCode'] = profileCode;
    data['profileName'] = profileName;
    data['lsxdt'] = lsxdt;
    data['lsxsap'] = lsxsap;
    data['dsx'] = dsx;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['productType'] = productType;
    data['customerReference'] = customerReference;
    data['confirmDate'] = confirmDate;
    data['quantityConfirm'] = quantityConfirm;
    data['qualityDate'] = qualityDate;
    data['qualityChecker'] = qualityChecker;
    data['qcSaleEmployee'] = qcSaleEmployee;
    data['inspectionLotQuantity'] = inspectionLotQuantity;
    data['environmental'] = environmental;
    data['status'] = status;
    data['result'] = result;
    data['qualityType'] = qualityType;
    data['po'] = po;
    data['unit'] = unit;
    data['qty'] = qty;
    data['file'] = file;
    data['finishedColor'] = finishedColor;

    if (fileViewModel != null) {
      data['fileViewModel'] = fileViewModel!.map((v) => v.toJson()).toList();
    }
    if (qualityControlDetail != null) {
      data['qualityControlDetail'] = qualityControlDetail!.toJson();
    }
    if (qualityControlInformation != null) {
      data['qualityControlInformation'] = qualityControlInformation!.map((v) => v.toJson()).toList();
    }
    if (error != null) {
      data['error'] = error!.map((v) => v.toJson()).toList();
    }

    data['hangTagId'] = hangTagId;
    data['sku'] = sku;
    data['tinhTrangMoiTruong'] = tinhTrangMoiTruong;
    data['mauHoanThien'] = mauHoanThien;
    data['so'] = so;

    data['nhaGiaCong'] = nhaGiaCong;
    data['khachHangId'] = khachHangId;
    data['nhaCungCap'] = nhaCungCap;

    data['barcode'] = barcode;
    data['stepCode'] = stepCode;
    data['note'] = note;
    data['loaiMau'] = loaiMau;

    return data;
  }

  dynamic getPropertyValue(String propertyName) {
    return {
      'productAttribute': productAttribute,
      'qcType': qcType,
      'qualityControlId': qualityControlId,
      'qualityControlCode': qualityControlCode,
      'saleOrgCode': saleOrgCode,
      'storeName': storeName,
      'workShopCode': workShopCode,
      'workShopName': workShopName,
      'workCenterCode': workCenterCode,
      'workCenterName': workCenterName,
      'profileCode': profileCode,
      'profileName': profileName,
      'lsxdt': lsxdt,
      'lsxsap': lsxsap,
      'dsx': dsx,
      'productCode': productCode,
      'productName': productName,
      'productType': productType,
      'customerReference': customerReference,
      'confirmDate': confirmDate,
      'quantityConfirm': quantityConfirm,
      'qualityDate': qualityDate,
      'qualityChecker': qualityChecker,
      'qcSaleEmployee': qcSaleEmployee,
      'inspectionLotQuantity': inspectionLotQuantity,
      'environmental': environmental,
      'status': status,
      'result': result,
      'qualityType': qualityType,
      'po': po,
      'unit': unit,
      'qty': qty,
      'file': file,
      'fileViewModel': fileViewModel,
      'qualityControlDetail': qualityControlDetail,
      'qualityControlInformation': qualityControlInformation,
      'finishedColor': finishedColor,
      'error': error,
      'hangTagId': hangTagId,
      'sku': sku,
      'tinhTrangMoiTruong': tinhTrangMoiTruong,
      'mauHoanThien': mauHoanThien,
      'so': so,
      'nhaGiaCong': nhaGiaCong,
      'khachHangId': khachHangId,
      'nhaCungCap': nhaCungCap,
      'loaiMau': loaiMau,
    }[propertyName];
  }

  void setPropertyValue(String propertyName, dynamic newValue) {
    switch (propertyName) {
      case 'productAttribute':
        productAttribute = newValue;
        break;
      case 'qcType':
        qcType = newValue;
        break;
      case 'qualityControlId':
        qualityControlId = newValue;
        break;
      case 'qualityControlCode':
        qualityControlCode = newValue;
        break;
      case 'saleOrgCode':
        saleOrgCode = newValue;
        break;
      case 'storeName':
        storeName = newValue;
        break;
      case 'workShopCode':
        workShopCode = newValue;
        break;
      case 'workShopName':
        workShopName = newValue;
        break;
      case 'workCenterCode':
        workCenterCode = newValue;
        break;
      case 'workCenterName':
        workCenterName = newValue;
        break;
      case 'profileCode':
        profileCode = newValue;
        break;
      case 'profileName':
        profileName = newValue;
        break;
      case 'lsxdt':
        lsxdt = newValue;
        break;
      case 'lsxsap':
        lsxsap = newValue;
        break;
      case 'dsx':
        dsx = newValue;
        break;
      case 'productCode':
        productCode = newValue;
        break;
      case 'productName':
        productName = newValue;
        break;
      case 'productType':
        productType = newValue;
        break;
      case 'customerReference':
        customerReference = newValue;
        break;
      case 'confirmDate':
        confirmDate = newValue;
        break;
      case 'quantityConfirm':
        quantityConfirm = newValue;
        break;
      case 'qualityDate':
        qualityDate = newValue;
        break;
      case 'qualityChecker':
        qualityChecker = newValue;
        break;
      case 'qcSaleEmployee':
        qcSaleEmployee = newValue;
        break;
      case 'inspectionLotQuantity':
        inspectionLotQuantity = newValue;
        break;
      case 'environmental':
        environmental = newValue;
        break;
      case 'status':
        status = newValue;
        break;
      case 'result':
        result = newValue;
        break;
      case 'qualityType':
        qualityType = newValue;
        break;
      case 'po':
        po = newValue;
        break;
      case 'unit':
        unit = newValue;
        break;
      case 'qty':
        qty = newValue;
        break;
      case 'file':
        file = newValue;
        break;
      case 'fileViewModel':
        fileViewModel = newValue;
        break;
      case 'qualityControlDetail':
        qualityControlDetail = newValue;
        break;
      case 'qualityControlInformation':
        qualityControlInformation = newValue;
        break;
      case 'finishedColor':
        finishedColor = newValue;
        break;
      case 'error':
        error = newValue;
        break;
      case 'hangTagId':
        hangTagId = newValue;
        break;
      case 'sku':
        sku = newValue;
        break;
      case 'tinhTrangMoiTruong':
        tinhTrangMoiTruong = newValue;
        break;
      case 'mauHoanThien':
        mauHoanThien = newValue;
        break;
      case 'so':
        so = newValue;
        break;
      case 'nhaGiaCong':
        nhaGiaCong = newValue;
        break;
      case 'khachHangId':
        khachHangId = newValue;
        break;
      case 'nhaCungCap':
        nhaCungCap = newValue;
        break;
      case 'loaiMau':
        loaiMau = newValue;
        break;
    }
  }
}

class FileViewModel {
  String? fileAttachmentId;
  String? fileAttachmentCode;
  String? fileAttachmentTypeName;
  String? fileAttachmentName;
  String? fileExtention;
  String? fileUrl;
  String? objectId;
  String? profileId;
  String? fileType;
  String? fileTypeName;
  String? createBy;
  String? createTime;
  String? lastEditBy;
  String? lastEditTime;
  String? createUser;

  FileViewModel(
      {this.fileAttachmentId,
      this.fileAttachmentCode,
      this.fileAttachmentTypeName,
      this.fileAttachmentName,
      this.fileExtention,
      this.fileUrl,
      this.objectId,
      this.profileId,
      this.fileType,
      this.fileTypeName,
      this.createBy,
      this.createTime,
      this.lastEditBy,
      this.lastEditTime,
      this.createUser});

  FileViewModel.fromJson(Map<String, dynamic> json) {
    fileAttachmentId = json['fileAttachmentId'];
    fileAttachmentCode = json['fileAttachmentCode'];
    fileAttachmentTypeName = json['fileAttachmentTypeName'];
    fileAttachmentName = json['fileAttachmentName'];
    fileExtention = json['fileExtention'];
    fileUrl = json['fileUrl'];
    objectId = json['objectId'];
    profileId = json['profileId'];
    fileType = json['fileType'];
    fileTypeName = json['fileTypeName'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastEditBy = json['lastEditBy'];
    lastEditTime = json['lastEditTime'];
    createUser = json['createUser'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileAttachmentId'] = fileAttachmentId;
    data['fileAttachmentCode'] = fileAttachmentCode;
    data['fileAttachmentTypeName'] = fileAttachmentTypeName;
    data['fileAttachmentName'] = fileAttachmentName;
    data['fileExtention'] = fileExtention;
    data['fileUrl'] = fileUrl;
    data['objectId'] = objectId;
    data['profileId'] = profileId;
    data['fileType'] = fileType;
    data['fileTypeName'] = fileTypeName;
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['lastEditBy'] = lastEditBy;
    data['lastEditTime'] = lastEditTime;
    data['createUser'] = createUser;
    return data;
  }
}

class QualityControlInformation {
  List<CheckedFileViewModel>? checkedFileViewModel;
  List<File>? file;
  String? workCenterName;
  int? qualityControlInformationCode;
  String? qualityControlInformationName;
  String? qualityControlQCInformationId;
  String? qualityControlId;
  String? qualityControlInformationId;
  String? workCenterCode;
  double? soSanPhamLoi;
  String? notes;
  String? outcomeStatus;
  QualityControlInformation({
    this.checkedFileViewModel,
    this.file,
    this.workCenterName,
    this.qualityControlInformationCode,
    this.qualityControlInformationName,
    this.qualityControlQCInformationId,
    this.qualityControlId,
    this.qualityControlInformationId,
    this.workCenterCode,
    this.soSanPhamLoi,
    this.notes,
    this.outcomeStatus,
  });

  QualityControlInformation.fromJson(Map<String, dynamic> json) {
    if (json['checkedFileViewModel'] != null) {
      checkedFileViewModel = <CheckedFileViewModel>[];
      json['checkedFileViewModel'].forEach((v) {
        checkedFileViewModel!.add(CheckedFileViewModel.fromJson(v));
      });
    }
    file = json['file'];
    workCenterName = json['workCenterName'];
    qualityControlInformationCode = json['qualityControlInformationCode'];
    qualityControlInformationName = json['qualityControlInformationName'];
    qualityControlQCInformationId = json['qualityControl_QCInformation_Id'];
    qualityControlId = json['qualityControlId'];
    qualityControlInformationId = json['qualityControlInformationId'];
    workCenterCode = json['workCenterCode'];
    soSanPhamLoi = json['soSanPhamLoi'];
    notes = json['notes'];
    outcomeStatus = json['outcomeStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (checkedFileViewModel != null) {
      data['checkedFileViewModel'] = checkedFileViewModel!.map((v) => v.toJson()).toList();
    }
    data['file'] = file;
    data['workCenterName'] = workCenterName;
    data['qualityControlInformationCode'] = qualityControlInformationCode;
    data['qualityControlInformationName'] = qualityControlInformationName;
    data['qualityControl_QCInformation_Id'] = qualityControlQCInformationId;
    data['qualityControlId'] = qualityControlId;
    data['qualityControlInformationId'] = qualityControlInformationId;
    data['workCenterCode'] = workCenterCode;
    data['soSanPhamLoi'] = soSanPhamLoi;
    data['notes'] = notes;
    data['outcomeStatus'] = outcomeStatus;
    return data;
  }
}

class CheckedFileViewModel {
  String? fileAttachmentId;
  String? fileAttachmentCode;
  String? fileAttachmentTypeName;
  String? fileAttachmentName;
  String? fileExtention;
  String? fileUrl;
  String? objectId;
  String? profileId;
  String? fileType;
  String? fileTypeName;
  String? createBy;
  String? createTime;
  String? lastEditBy;
  String? lastEditTime;
  String? createUser;

  CheckedFileViewModel(
      {this.fileAttachmentId,
      this.fileAttachmentCode,
      this.fileAttachmentTypeName,
      this.fileAttachmentName,
      this.fileExtention,
      this.fileUrl,
      this.objectId,
      this.profileId,
      this.fileType,
      this.fileTypeName,
      this.createBy,
      this.createTime,
      this.lastEditBy,
      this.lastEditTime,
      this.createUser});

  CheckedFileViewModel.fromJson(Map<String, dynamic> json) {
    fileAttachmentId = json['fileAttachmentId'];
    fileAttachmentCode = json['fileAttachmentCode'];
    fileAttachmentTypeName = json['fileAttachmentTypeName'];
    fileAttachmentName = json['fileAttachmentName'];
    fileExtention = json['fileExtention'];
    fileUrl = json['fileUrl'];
    objectId = json['objectId'];
    profileId = json['profileId'];
    fileType = json['fileType'];
    fileTypeName = json['fileTypeName'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastEditBy = json['lastEditBy'];
    lastEditTime = json['lastEditTime'];
    createUser = json['createUser'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileAttachmentId'] = fileAttachmentId;
    data['fileAttachmentCode'] = fileAttachmentCode;
    data['fileAttachmentTypeName'] = fileAttachmentTypeName;
    data['fileAttachmentName'] = fileAttachmentName;
    data['fileExtention'] = fileExtention;
    data['fileUrl'] = fileUrl;
    data['objectId'] = objectId;
    data['profileId'] = profileId;
    data['fileType'] = fileType;
    data['fileTypeName'] = fileTypeName;
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['lastEditBy'] = lastEditBy;
    data['lastEditTime'] = lastEditTime;
    data['createUser'] = createUser;
    return data;
  }
}

class QualityControlDetail {
  String? qualityControlDetailId;
  String? qualityControlId;
  String? testMethod;
  String? congDoanNho;
  String? limitCritical;
  String? limitHigh;
  String? limitLow;
  String? samplingLevel;
  String? acceptableLevel;
  double? inspectionQuantity;
  double? tongSoSanPhamLoi;
  String? result;
  String? samplingLevelName;

  String? quanDocCode;
  String? toTruongCode;
  String? qAQCCode;
  String? kCSCode;
  String? qualityDate;
  String? qualityChecker;
  int? loiNangChapNhan;
  int? loiNheChapNhan;
  String? qcSaleEmployee;

  int? checkingTimes;

  QualityControlDetail({
    this.qualityControlDetailId,
    this.qualityControlId,
    this.testMethod,
    this.congDoanNho,
    this.limitCritical,
    this.limitHigh,
    this.limitLow,
    this.samplingLevel,
    this.acceptableLevel,
    this.inspectionQuantity,
    this.tongSoSanPhamLoi,
    this.result,
    this.samplingLevelName,
    this.quanDocCode,
    this.toTruongCode,
    this.qAQCCode,
    this.kCSCode,
    this.qualityDate,
    this.qualityChecker,
    this.loiNangChapNhan,
    this.loiNheChapNhan,
    this.qcSaleEmployee,
    this.checkingTimes,
  });

  QualityControlDetail.fromJson(Map<String, dynamic> json) {
    qualityControlDetailId = json['qualityControlDetailId'];
    qualityControlId = json['qualityControlId'];
    testMethod = json['testMethod'];
    congDoanNho = json['congDoanNho'];
    limitCritical = json['limitCritical'];
    limitHigh = json['limitHigh'];
    limitLow = json['limitLow'];
    samplingLevel = json['samplingLevel'];
    acceptableLevel = json['acceptableLevel'];
    inspectionQuantity = json['inspectionQuantity'];
    tongSoSanPhamLoi = json['tongSoSanPhamLoi'];
    result = json['result'];
    samplingLevelName = json['samplingLevelName'];

    quanDocCode = json['quanDocCode'];
    toTruongCode = json['toTruongCode'];
    qAQCCode = json['qAQCCode'];
    kCSCode = json['kCSCode'];
    qualityDate = json['qualityDate'];
    qualityChecker = json['qualityChecker'];
    loiNangChapNhan = json['loiNangChapNhan'];
    loiNheChapNhan = json['loiNheChapNhan'];
    qcSaleEmployee = json['qcSaleEmployee'];
    checkingTimes = json['checkingTimes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlDetailId'] = qualityControlDetailId;
    data['qualityControlId'] = qualityControlId;
    data['testMethod'] = testMethod;
    data['congDoanNho'] = congDoanNho;
    data['limitCritical'] = limitCritical;
    data['limitHigh'] = limitHigh;
    data['limitLow'] = limitLow;
    data['samplingLevel'] = samplingLevel;
    data['acceptableLevel'] = acceptableLevel;
    data['inspectionQuantity'] = inspectionQuantity;
    data['tongSoSanPhamLoi'] = tongSoSanPhamLoi;
    data['result'] = result;
    data['samplingLevelName'] = samplingLevelName;

    data['quanDocCode'] = quanDocCode;
    data['toTruongCode'] = toTruongCode;
    data['qAQCCode'] = qAQCCode;
    data['kCSCode'] = kCSCode;
    data['qualityDate'] = qualityDate;
    data['qualityChecker'] = qualityChecker;
    data['loiNangChapNhan'] = loiNangChapNhan;
    data['loiNheChapNhan'] = loiNheChapNhan;
    data['qcSaleEmployee'] = qcSaleEmployee;
    data['checkingTimes'] = checkingTimes;
    return data;
  }
}

class Error {
  String? qualityControlErrorId;
  String? qualityControlId;
  String? catalogCode;
  String? levelError;
  double? quantityError;
  String? notes;
  String? caNhanGayLoi;
  List<ErrorFileViewModel>? errorFileViewModel;
  String? congDoanLoi;
  String? phuongAnXuLy;

  String? nhaMayLoi;
  String? phanXuongLoi;
  String? toChuyenLoi;

  List<String>? caNhanGayLoiList;

  String? caNhanGayLoiMany;

  String? quanDoc;
  String? toTruong;
  String? qaqc;
  String? kcs;

  Error({
    this.qualityControlErrorId,
    this.qualityControlId,
    this.catalogCode,
    this.levelError,
    this.quantityError,
    this.notes,
    this.caNhanGayLoi,
    this.errorFileViewModel,
    this.congDoanLoi,
    this.phuongAnXuLy,
    this.nhaMayLoi,
    this.phanXuongLoi,
    this.toChuyenLoi,
    this.caNhanGayLoiList,
    this.caNhanGayLoiMany,
    this.quanDoc,
    this.toTruong,
    this.qaqc,
    this.kcs,
  });

  Error.fromJson(Map<String, dynamic> json) {
    if (json['errorFileViewModel'] != null) {
      errorFileViewModel = <ErrorFileViewModel>[];
      json['errorFileViewModel'].forEach((v) {
        errorFileViewModel!.add(new ErrorFileViewModel.fromJson(v));
      });
    }
    qualityControlErrorId = json['quanlityControl_Error_Id'];
    qualityControlId = json['qualityControlId'];
    catalogCode = json['catalogCode'];
    levelError = json['levelError'];
    quantityError = json['quantityError'];
    notes = json['notes'];

    caNhanGayLoi = json['personCausedErrorCode'];

    congDoanLoi = json['stepCodeError'];
    phuongAnXuLy = json['phuongAnXuLy'];
    nhaMayLoi = json['saleOrgErrorCode'];
    phanXuongLoi = json['workshopErrorCode'];
    toChuyenLoi = json['departmentErrorCode'];

    if (json['caNhanGayLoiList'] != null) {
      caNhanGayLoiList = <String>[];
      caNhanGayLoiList = List<String>.from(json['caNhanGayLoiList']);
    }

    caNhanGayLoiMany = json['personCausedErrorCodeMany'];

    quanDoc = json['quanDoc'];
    toTruong = json['toTruong'];
    qaqc = json['qaqc'];
    kcs = json['kcs'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (errorFileViewModel != null) {
      data['errorFileViewModel'] = errorFileViewModel!.map((v) => v.toJson()).toList();
    }
    data['quanlityControl_Error_Id'] = qualityControlErrorId;
    data['qualityControlId'] = qualityControlId;
    data['catalogCode'] = catalogCode;
    data['levelError'] = levelError;
    data['quantityError'] = quantityError;
    data['notes'] = notes;
    data['personCausedErrorCode'] = caNhanGayLoi;
    data['stepCodeError'] = congDoanLoi;
    data['phuongAnXuLy'] = phuongAnXuLy;
    data['saleOrgErrorCode'] = nhaMayLoi;
    data['workshopErrorCode'] = phanXuongLoi;
    data['departmentErrorCode'] = toChuyenLoi;

    data['caNhanGayLoiList'] = caNhanGayLoiList;

    data['personCausedErrorCodeMany'] = caNhanGayLoiMany;

    data['quanDoc'] = quanDoc;
    data['toTruong'] = toTruong;
    data['qaqc'] = qaqc;
    data['kcs'] = kcs;

    return data;
  }
}

class ResultList {
  String? catalogCode;
  String? catalogTextVi;

  ResultList({this.catalogCode, this.catalogTextVi});

  ResultList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class QualityTypeList {
  String? catalogCode;
  String? catalogTextVi;

  QualityTypeList({this.catalogCode, this.catalogTextVi});

  QualityTypeList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class QualityCheckerInfo {
  String? accountId;
  String? salesEmployeeCode;
  String? salesEmployeeName;

  QualityCheckerInfo({this.accountId, this.salesEmployeeName, this.salesEmployeeCode});

  QualityCheckerInfo.fromJson(Map<String, dynamic> json) {
    accountId = json['accountId'];
    salesEmployeeName = json['salesEmployeeName'];
    salesEmployeeCode = json['salesEmployeeCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['accountId'] = accountId;
    data['salesEmployeeName'] = salesEmployeeName;
    data['salesEmployeeCode'] = salesEmployeeCode;
    return data;
  }
}

class TestMethodList {
  String? catalogCode;
  String? catalogTextVi;

  TestMethodList({this.catalogCode, this.catalogTextVi});

  TestMethodList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class DropdownItemList {
  String? catalogCode;
  String? catalogTextVi;

  DropdownItemList({this.catalogCode, this.catalogTextVi});

  DropdownItemList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

// class LimitList {
//   String? catalogCode;
//   String? catalogTextVi;

//   LimitList({this.catalogCode, this.catalogTextVi});

//   LimitList.fromJson(Map<String, dynamic> json) {
//     catalogCode = json['catalogCode'];
//     catalogTextVi = json['catalogText_vi'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['catalogCode'] = catalogCode;
//     data['catalogText_vi'] = catalogTextVi;
//     return data;
//   }
// }

class SamplingLevelList {
  String? catalogCode;
  String? catalogTextVi;

  SamplingLevelList({this.catalogCode, this.catalogTextVi});

  SamplingLevelList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    return data;
  }
}

class ThongTinKiemTra {
  String? id;
  String? name;
  String? workCenterCode;

  ThongTinKiemTra({this.id, this.name, this.workCenterCode});

  ThongTinKiemTra.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    workCenterCode = json['workCenterCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['workCenterCode'] = workCenterCode;
    return data;
  }
}

class ErrorList {
  String? catalogCode;
  String? catalogTextVi;
  String? errorLevel;

  ErrorList({this.catalogCode, this.catalogTextVi, this.errorLevel});

  ErrorList.fromJson(Map<String, dynamic> json) {
    catalogCode = json['catalogCode'];
    catalogTextVi = json['catalogText_vi'];
    errorLevel = json['errorLevel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['catalogCode'] = catalogCode;
    data['catalogText_vi'] = catalogTextVi;
    data['errorLevel'] = errorLevel;
    return data;
  }
}

class ErrorFileViewModel {
  String? fileAttachmentId;
  String? fileAttachmentCode;
  String? fileAttachmentTypeName;
  String? fileAttachmentName;
  String? fileExtention;
  String? fileUrl;
  String? objectId;
  String? profileId;
  String? fileType;
  String? fileTypeName;
  String? createBy;
  String? createTime;
  String? lastEditBy;
  String? lastEditTime;
  String? createUser;

  ErrorFileViewModel(
      {this.fileAttachmentId,
      this.fileAttachmentCode,
      this.fileAttachmentTypeName,
      this.fileAttachmentName,
      this.fileExtention,
      this.fileUrl,
      this.objectId,
      this.profileId,
      this.fileType,
      this.fileTypeName,
      this.createBy,
      this.createTime,
      this.lastEditBy,
      this.lastEditTime,
      this.createUser});

  ErrorFileViewModel.fromJson(Map<String, dynamic> json) {
    fileAttachmentId = json['fileAttachmentId'];
    fileAttachmentCode = json['fileAttachmentCode'];
    fileAttachmentTypeName = json['fileAttachmentTypeName'];
    fileAttachmentName = json['fileAttachmentName'];
    fileExtention = json['fileExtention'];
    fileUrl = json['fileUrl'];
    objectId = json['objectId'];
    profileId = json['profileId'];
    fileType = json['fileType'];
    fileTypeName = json['fileTypeName'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastEditBy = json['lastEditBy'];
    lastEditTime = json['lastEditTime'];
    createUser = json['createUser'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileAttachmentId'] = fileAttachmentId;
    data['fileAttachmentCode'] = fileAttachmentCode;
    data['fileAttachmentTypeName'] = fileAttachmentTypeName;
    data['fileAttachmentName'] = fileAttachmentName;
    data['fileExtention'] = fileExtention;
    data['fileUrl'] = fileUrl;
    data['objectId'] = objectId;
    data['profileId'] = profileId;
    data['fileType'] = fileType;
    data['fileTypeName'] = fileTypeName;
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['lastEditBy'] = lastEditBy;
    data['lastEditTime'] = lastEditTime;
    data['createUser'] = createUser;
    return data;
  }
}

class QCPassedStampModel {
  String? po;
  String? productCode;
  String? productName;
  String? lsxSap;
  String? serial;
  String? qualityControlTime;
  String? workshop;
  String? sku;
  String? dateOfManufacture;
  String? placeOfManufacture;
  String? poKhach;

  QCPassedStampModel({
    this.po,
    this.productCode,
    this.productName,
    this.lsxSap,
    this.serial,
    this.qualityControlTime,
    this.workshop,
    this.sku,
    this.dateOfManufacture,
    this.placeOfManufacture,
    this.poKhach,
  });

  factory QCPassedStampModel.fromJson(Map<String, dynamic> json) {
    return QCPassedStampModel(
      po: json['po'],
      productCode: json['productCode'],
      productName: json['productName'],
      lsxSap: json['lsxSap'],
      serial: json['serial'],
      qualityControlTime: json['qualityControlTime'],
      workshop: json['workshop'],
      sku: json['sku'],
      dateOfManufacture: json['dateOfManufacture'],
      placeOfManufacture: json['placeOfManufacture'],
      poKhach: json['poKhach'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'po': po,
      'productCode': productCode,
      'productName': productName,
      'lsxSap': lsxSap,
      'serial': serial,
      'qualityControlTime': qualityControlTime,
      'workshop': workshop,
      'sku': sku,
      'dateOfManufacture': dateOfManufacture,
      'placeOfManufacture': placeOfManufacture,
      'poKhach': poKhach,
    };
  }
}
