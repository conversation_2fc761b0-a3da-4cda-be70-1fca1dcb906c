﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MaterialTypeModel", Schema = "MES")]
    public partial class MaterialTypeModel
    {
        [Key]
        public Guid MaterialTypeId { get; set; }
        [StringLength(10)]
        public string MANDT { get; set; }
        [StringLength(50)]
        public string MTART { get; set; }
        [StringLength(1000)]
        public string MTBEZ { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}