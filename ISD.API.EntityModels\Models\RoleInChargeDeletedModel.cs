﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RoleInChargeDeletedModel", Schema = "Customer")]
    public partial class RoleInChargeDeletedModel
    {
        [Key]
        public Guid RoleInChargeId { get; set; }
        public Guid? ProfileId { get; set; }
        public Guid? RolesId { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
    }
}