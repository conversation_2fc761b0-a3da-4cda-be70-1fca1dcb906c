﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TemplateAndGiftMemberModel", Schema = "Marketing")]
    public partial class TemplateAndGiftMemberModel
    {
        public TemplateAndGiftMemberModel()
        {
            TemplateAndGiftMemberAddressModel = new HashSet<TemplateAndGiftMemberAddressModel>();
        }

        [Key]
        public Guid Id { get; set; }
        public Guid? TemplateAndGiftTargetGroupId { get; set; }
        public Guid? ProfileId { get; set; }

        [ForeignKey("ProfileId")]
        [InverseProperty("TemplateAndGiftMemberModel")]
        public virtual ProfileModel Profile { get; set; }
        [ForeignKey("TemplateAndGiftTargetGroupId")]
        [InverseProperty("TemplateAndGiftMemberModel")]
        public virtual TemplateAndGiftTargetGroupModel TemplateAndGiftTargetGroup { get; set; }
        [InverseProperty("TempalteAndGiftMember")]
        public virtual ICollection<TemplateAndGiftMemberAddressModel> TemplateAndGiftMemberAddressModel { get; set; }
    }
}