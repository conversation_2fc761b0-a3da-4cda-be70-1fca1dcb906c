﻿using System;

namespace iMES_API.Infrastructures.JobSchedulers
{
    public class EmailService
    {
        public void SendDailyReport()
        {
            // Logic for sending daily report
            Console.WriteLine("Daily report sent.");
        }

        public void SendWeeklyReport()
        {
            // Logic for sending weekly report
            Console.WriteLine("Weekly report sent.");
        }

        public void CheckSystemHealth()
        {
            // Logic for checking system health
            Console.WriteLine("System health checked.");
        }

        public void FrequentTask()
        {
            // Logic for a task that runs frequently (every X seconds)
            Console.WriteLine("Frequent task executed.");
        }
    }
}
