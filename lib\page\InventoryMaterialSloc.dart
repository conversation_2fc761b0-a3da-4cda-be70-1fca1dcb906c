import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/TableInfo.dart';
import '../element/errorViewPost.dart';
import '../element/timeOut.dart';
import '../model/GetBackDataInventMaterialSloc.dart';
import '../model/InventoryMaterialSloc.dart';
import '../model/rawMaterialCard.dart';
import '../model/slocAddresse.dart';
import '../repository/function/importWareHouseFunction.dart';
import '../repository/function/inventoryMaterialSlocFunction.dart';
import 'LostConnect.dart';

class InventoryMaterialSloc extends StatefulWidget {
  final String token;
  final String plant;
  final String dateTimeOld;
  const InventoryMaterialSloc({Key? key, required this.plant, required this.token, required this.dateTimeOld}) : super(key: key);

  @override
  State<InventoryMaterialSloc> createState() => _InventoryMaterialSlocState();
}

class _InventoryMaterialSlocState extends State<InventoryMaterialSloc> {
  bool _notWifi = false;
  bool _isLoading = false;
  bool _timeOut = false;
  String _error = "";
  DataSlocAddress? _selectedSloc;
  List<DataSlocAddress> _listDataSlocAddress = [];
  bool _disableButton = false;
  DataRawMeterial? _dataMaterial;
  String? _productCode;
  bool _isLoadingFetchMaterial = false;
  bool _isLoadingPostInventory = false;
  List<DataInventoryMaterialSloc> _lsDataInventoryMaterialSloc = [];
  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);

      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        if (!mounted) return;
        setState(() {
          _timeOut = true;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _isLoading = true;
        });
        final data = await ImportWareHouseFunction.fetchSlocAddress(widget.plant, widget.token);
        if (!mounted) return;
        setState(() {
          _listDataSlocAddress = data ?? [];
          _listDataSlocAddress.removeWhere((element) => element.slocDisplay == null || element.slocDisplay == "");
          _listDataSlocAddress.sort((a, b) {
            return a.slocDisplay!.toLowerCase().compareTo(b.slocDisplay!.toLowerCase());
          });
          _isLoading = false;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _notWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _notWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _getMaterial(String rawMaterialID, String? productCode, BuildContext context) async {
    try {
      setState(() {
        _isLoadingFetchMaterial = true;
      });
      final dataMaterial = await ImportWareHouseFunction.fetchRawMaterial(rawMaterialID, widget.token.toString(), "qr");
      if (!mounted) return;
      if (dataMaterial != null) {
        setState(() {
          _productCode = dataMaterial.productCode;
          _dataMaterial = dataMaterial;
          _isLoadingFetchMaterial = false;
        });
      } else {
        setState(() {
          _isLoadingFetchMaterial = false;
          _productCode = productCode;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              'Lấy thông tin NVL thất bại! hãy kiểm tra thẻ treo có tồn tai hay không',
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingFetchMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingFetchMaterial = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  Future<void> _postInventoryMaterialSloc() async {
    try {
      setState(() {
        _isLoadingPostInventory = true;
      });
      final data = await InventoryMaterialSlocFunction.fetchInventoryMaterialSloc(
          _productCode!.isNotEmpty ? _productCode! : _dataMaterial!.productCode!, _selectedSloc!.sloc!, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoadingPostInventory = false;
        _lsDataInventoryMaterialSloc = data ?? [];
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingPostInventory = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingPostInventory = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            error.toString(),
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  void _setSloc(DataSlocAddress? value) {
    setState(() {
      _selectedSloc = value!;
    });
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton)))
        : Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              backgroundColor: const Color(0xff0052cc),
              elevation: 0,
              centerTitle: true,
              leading: IconButton(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context, false);
                },
              ),
              title: Text(
                'Kiểm tra tồn kho NVL',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
              ),
            ),
            body: _notWifi == true
                ? LostConnect(checkConnect: () => _init())
                : _error != ""
                    ? ErrorViewPost(error: _error)
                    : _isLoading == true
                        ? const Center(child: CircularProgressIndicator())
                        : SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10.w),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 10.h),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: Container(
                                      decoration: const BoxDecoration(),
                                      child: ElevatedButton.icon(
                                        style: ButtonStyle(
                                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                          side: MaterialStateProperty.all(
                                            BorderSide(
                                              color: _isLoadingFetchMaterial == true ? Colors.grey.shade300 : const Color(0xff303F9F),
                                            ),
                                          ),
                                          backgroundColor: MaterialStateProperty.all(
                                              _isLoadingFetchMaterial == true ? Colors.grey.shade300 : const Color(0xff303F9F)),
                                        ),
                                        onPressed: _isLoadingFetchMaterial == true
                                            ? null
                                            : () async {
                                                String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                  Platform.isAndroid
                                                      ? showDialog(
                                                          context: context,
                                                          barrierDismissible: false,
                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                      : showCupertinoDialog(
                                                          context: context,
                                                          barrierDismissible: false,
                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                } else {
                                                  final data = await Navigator.pushNamed(context, '/QRCodeInventoryMaterialSloc');
                                                  if (data == null) return;
                                                  if ((data as GetBackDataInventMaterialSloc).isScan == true) {
                                                    debugPrint("dsadas ${data.productID}");
                                                    debugPrint("dsadas ${data.rawMaterialID}");
                                                    if (!mounted) return;
                                                    _getMaterial(data.rawMaterialID, data.productID, context);
                                                  }
                                                }
                                              },
                                        icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                        label: Text(
                                          "Quét mã",
                                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  TableInfo(
                                    textCL1: "Nhà máy",
                                    textCL2: widget.plant,
                                    colorCL1: 0xff303F9F,
                                    colorCL2: 0xffffffff,
                                  ),
                                  TableInfoNoTop(
                                    textCL1: "Mã NVL",
                                    textCL2: _isLoadingFetchMaterial == true
                                        ? "loading..."
                                        : _dataMaterial == null
                                            ? ""
                                            : _dataMaterial!.productCode == null
                                                ? ""
                                                : _dataMaterial!.productCode!,
                                    colorCL1: 0xff303F9F,
                                    colorCL2: 0xffffffff,
                                  ),
                                  TableInfoNoTop(
                                    textCL1: "Tên NVL",
                                    textCL2: _isLoadingFetchMaterial == true
                                        ? "loading..."
                                        : _dataMaterial == null
                                            ? ""
                                            : _dataMaterial!.productName == null
                                                ? ""
                                                : _dataMaterial!.productName!,
                                    colorCL1: 0xff303F9F,
                                    colorCL2: 0xffffffff,
                                  ),
                                  IntrinsicHeight(
                                    child: Row(
                                      children: <Widget>[
                                        Expanded(
                                          flex: 4,
                                          child: Container(
                                            height: double.infinity,
                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                            decoration: BoxDecoration(
                                              color: const Color(0xff303F9F),
                                              border: Border(
                                                left: BorderSide(
                                                  color: Colors.black,
                                                  width: 0.5.w,
                                                ),
                                                right: BorderSide(
                                                  color: Colors.black,
                                                  width: 0.5.w,
                                                ),
                                                bottom: BorderSide(
                                                  color: Colors.black,
                                                  width: 0.5.w,
                                                ),
                                              ),
                                            ),
                                            child: Text(
                                              "Sloc",
                                              style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 6,
                                          child: Container(
                                            height: double.infinity,
                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                            decoration: BoxDecoration(
                                              color: const Color(0xffffffff),
                                              border: Border(
                                                right: BorderSide(
                                                  color: Colors.black,
                                                  width: 0.5.w,
                                                ),
                                                bottom: BorderSide(
                                                  color: Colors.black,
                                                  width: 0.5.w,
                                                ),
                                              ),
                                            ),
                                            child: Container(
                                                height: double.infinity,
                                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                    color: const Color(0xFFFFFFFF), border: Border.all(color: Colors.black, width: 0.5.w)),
                                                child: _DropdownSelectSloc(
                                                  listDataSlocAddress: _listDataSlocAddress,
                                                  selectedSloc: _selectedSloc,
                                                  setSloc: (DataSlocAddress? value) => _setSloc(value),
                                                )),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  _isLoadingPostInventory == true
                                      ? const Center(
                                          child: CircularProgressIndicator(),
                                        )
                                      : Container(
                                          decoration: const BoxDecoration(),
                                          child: Center(
                                            child: ElevatedButton(
                                              style: ButtonStyle(
                                                shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                side: MaterialStateProperty.all(
                                                  BorderSide(
                                                    color: _selectedSloc == null || _dataMaterial == null || _productCode == null
                                                        ? Colors.grey.shade300
                                                        : const Color(0xff303F9F),
                                                  ),
                                                ),
                                                backgroundColor: _selectedSloc == null || _dataMaterial == null || _productCode == null
                                                    ? MaterialStateProperty.all(Colors.grey.shade300)
                                                    : MaterialStateProperty.all(const Color(0xff303F9F)),
                                              ),
                                              onPressed: _selectedSloc == null || _dataMaterial == null || _productCode == null
                                                  ? null
                                                  : () async {
                                                      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                        Platform.isAndroid
                                                            ? showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                            : showCupertinoDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                      } else {
                                                        _postInventoryMaterialSloc();
                                                      }
                                                    },
                                              child: Text(
                                                "Kiểm tra",
                                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                              ),
                                            ),
                                          ),
                                        ),
                                  SizedBox(height: 10.h),
                                  _TableTitleResult(lsDataInventoryMaterialSloc: _lsDataInventoryMaterialSloc),
                                  _TableResult(
                                    lsDataInventoryMaterialSloc: _lsDataInventoryMaterialSloc,
                                    dataMaterial: _dataMaterial,
                                  )
                                ],
                              ),
                            ),
                          ),
          );
  }
}

class _TableTitleResult extends StatelessWidget {
  final List<DataInventoryMaterialSloc> lsDataInventoryMaterialSloc;
  const _TableTitleResult({Key? key, required this.lsDataInventoryMaterialSloc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: lsDataInventoryMaterialSloc.isNotEmpty,
      child: Table(
        border: TableBorder.all(width: 0.5.w),
        columnWidths: const <int, TableColumnWidth>{
          0: FractionColumnWidth(0.25),
          1: FractionColumnWidth(0.25),
          2: FractionColumnWidth(0.25),
          3: FractionColumnWidth(0.25),
        },
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: const <TableRow>[
          TableRow(
            decoration: BoxDecoration(
              color: Color(0xff303F9F),
            ),
            children: <Widget>[
              _TitleTable(text: "SO/WBS"),
              _TitleTable(text: "Số lô"),
              _TitleTable(text: "Số lượng"),
              _TitleTable(text: "ĐVT"),
            ],
          ),
        ],
      ),
    );
  }
}

class _TableResult extends StatelessWidget {
  final List<DataInventoryMaterialSloc> lsDataInventoryMaterialSloc;
  final DataRawMeterial? dataMaterial;
  const _TableResult({Key? key, required this.lsDataInventoryMaterialSloc, required this.dataMaterial}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: lsDataInventoryMaterialSloc.isNotEmpty,
      child: Column(
        children: List.generate(
          lsDataInventoryMaterialSloc.length,
          (index) => Table(
            border: TableBorder.all(width: 0.5.w),
            columnWidths: const <int, TableColumnWidth>{
              0: FractionColumnWidth(0.25),
              1: FractionColumnWidth(0.25),
              2: FractionColumnWidth(0.25),
              3: FractionColumnWidth(0.25),
            },
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: <TableRow>[
              TableRow(
                decoration: const BoxDecoration(
                  color: Color(0xffFFFFFF),
                ),
                children: <Widget>[
                  _ContentTable(
                      text: lsDataInventoryMaterialSloc[index].sowbs == null || lsDataInventoryMaterialSloc[index].sowbs == ""
                          ? "Tồn trơn"
                          : lsDataInventoryMaterialSloc[index].sowbs ?? ""),
                  _ContentTable(text: lsDataInventoryMaterialSloc[index].batch ?? ""),
                  _ContentTable(text: (lsDataInventoryMaterialSloc[index].quantity ?? 0).toString()),
                  _ContentTable(text: dataMaterial == null ? "" : dataMaterial!.unit ?? ""),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _TitleTable extends StatelessWidget {
  final String text;
  const _TitleTable({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _ContentTable extends StatelessWidget {
  final String text;
  const _ContentTable({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.black),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _DropdownSelectSloc extends StatelessWidget {
  final List<DataSlocAddress> listDataSlocAddress;
  final DataSlocAddress? selectedSloc;
  final ValueChanged<DataSlocAddress?> setSloc;
  const _DropdownSelectSloc({Key? key, required this.listDataSlocAddress, required this.selectedSloc, required this.setSloc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: ButtonTheme(
        child: DropdownButton<DataSlocAddress>(
          isExpanded: true,
          itemHeight: null,
          isDense: true,
          value: selectedSloc,
          iconSize: 15.sp,
          style: const TextStyle(color: Colors.white),
          onChanged: setSloc,
          items: listDataSlocAddress.map((DataSlocAddress? sloc) {
            return DropdownMenuItem<DataSlocAddress>(
              value: sloc,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  sloc!.slocDisplay.toString(),
                  style: TextStyle(color: Colors.black, fontSize: 11.sp),
                ),
              ),
            );
          }).toList(),
          selectedItemBuilder: (BuildContext context) {
            return listDataSlocAddress.map<Widget>((DataSlocAddress? sloc) {
              return Text(sloc!.slocDisplay.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
            }).toList();
          },
        ),
      ),
    );
  }
}
