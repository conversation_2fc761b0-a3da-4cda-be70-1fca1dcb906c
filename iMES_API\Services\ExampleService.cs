using iMES_API.Services;
using ISD.API.EntityModels.Data;
using System.Collections.Generic;
using System.Linq;

namespace iMES_API.Services
{
    public class ExampleService
    {
        private readonly DatabaseService _databaseService;

        public ExampleService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        // Example method that uses the development database
        public List<string> GetAccountNamesFromDevelopment()
        {
            // Get the development context
            var devContext = _databaseService.GetDevelopmentContext();
            
            // Use the context to fetch data
            return devContext.AccountModel
                .Select(a => a.UserName)
                .Take(10)
                .ToList();
        }

        // Example method that uses the production database
        public List<string> GetAccountNamesFromProduction()
        {
            // Get the production context
            var prodContext = _databaseService.GetProductionContext();
            
            // Use the context to fetch data
            return prodContext.AccountModel
                .Select(a => a.UserName)
                .Take(10)
                .ToList();
        }

        // Example method that dynamically chooses the database based on environment
        public List<string> GetAccountNamesByEnvironment(string environment)
        {
            // Get the appropriate context based on environment
            var context = _databaseService.GetContextByEnvironment(environment);
            
            // Use the context to fetch data
            return context.AccountModel
                .Select(a => a.UserName)
                .Take(10)
                .ToList();
        }
    }
} 