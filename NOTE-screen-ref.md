# Application Screens Description

| **Tên màn hình**                                      | **File**                        |
|-------------------------------------------------------|---------------------------------|
| **Home**                                              | `Mainpage.dart`                 |
| **Quét barcode TP/BTP**                               | `QRcodePage.dart`               |
| **Thông tin (GNSL, HTCDL, CCD)**                      | `Info.dart`                     |
| **Ghi nhận sản lượng, xác nhận công đoạn**            | `DetailReport.dart`             |
| **Hoàn tất công đoạn lớn**                            | `CompleteTheBigStage.dart`      |
| **Chuyển công đoạn**                                  | `SwitchingStages.dart`          |
| **Nhập kho NVL**                                      | `ImportWarehouseSAP.dart`       |
| **Danh sách chuyển kho**                              | `ListTranferMaterial.dart`      |
| **Chuyển kho 1 line**                                 | `ExportWarehouse.dart`          |
| **Danh sách chuyển kho (nhiều line)**                  | `ListTranferMaterial2.dart`     |
| **Chuyển kho nhiều line**                             | `ExportWarehouse2.dart`         |
| **Thống kê NVL sử dụng trong ngày**                   | `StatisticsMaterials.dart`      |
| **Ghi nhận NVL chưa sử dụng hết**                     | `MaterialUnused.dart`           |
| **Kiểm tra tồn kho NVL**                              | `InventoryMaterialSloc.dart`    |
| **Quét QC passed stamp**                               | `QRcodePage.dart`               |
| **QC passed stamp info**                               | `QCPassedStampScan.dart`        |
| **Báo cáo nghiệm thu đầu vào**                        | `BaoCaoDauVao.dart`             |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoDauVaoDetail.dart`       |
| **Báo cáo nghiệm thu đầu vào 2**                      | `BaoCaoDauVao2.dart`            |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoDauVao2Detail.dart`      |
| **Phiếu KCS công đoạn (KCS kiểm tra trên chuyền)**    | `PhieuKCSCongDoan.dart`          |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `PhieuKCSCongDoanDetail.dart`    |
| **Báo cáo QAQC nghiệm thu**                           | `BaoCaoQAQCNghiemThu.dart`      |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoQAQCNghiemThuDetail.dart`|
| **Báo cáo nghiệm thu SP mẫu**                         | `BaoCaoQCMau.dart`              |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoQCMauDetail.dart`        |
| **Báo cáo nghiệm thu tại nhà gia công (NGC)**          | `BaoCaoQCGiaCong.dart`          |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoQCGiaCongDetail.dart`    |
| **Báo cáo QAQC sản phẩm dùng barcode khách**           | `BaoCaoQCSanPham.dart`          |
| &nbsp;&nbsp;&nbsp;**Detail**                          | `BaoCaoQCSanPhamDetail.dart`     |
| **Báo cáo QA hiện trường**                             | `BaoCaoQCHienTruong.dart`       |

Route: route.dart




# Note

This file, NOTE-screen-ref.md, appears to be a markdown document that serves as a reference guide for an application's screen structure. Here's what I understand about it:

1. It's titled "Application Screens Description".

2. The file contains a table with two columns: "Tên màn hình" (Screen Name in Vietnamese) and "File" (the corresponding Dart file for each screen).

3. It lists various screens of what seems to be a mobile or web application, likely developed using Flutter (given the .dart file extensions).

4. The application appears to be related to manufacturing, quality control, and inventory management. Some key features include:

- Barcode scanning
- Production logging
- Warehouse operations (import/export)
- Quality control (QC) processes
- Various types of reports (input acceptance, QA/QC, sample product testing, etc.)

5. The screens are organized into main screens and some have associated detail screens (indicated by indentation and "Detail" label).

6. The application seems to be bilingual, with screen names in Vietnamese and file names in English.

7. Some screens appear to have multiple purposes or variations (e.g., "QRcodePage.dart" is used for both "Quét barcode TP/BTP" and "Quét QC passed stamp").

This document likely serves as a quick reference for developers or project managers to understand the structure of the application, mapping screen names to their corresponding implementation files. It would be particularly useful for navigation, maintenance, and further development of the application.