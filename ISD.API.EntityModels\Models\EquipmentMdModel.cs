﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("EquipmentMdModel", Schema = "MES")]
    public partial class EquipmentMdModel
    {
        [Key]
        public Guid EquipmentMdId { get; set; }
        [StringLength(50)]
        public string EQUNR { get; set; }
        [StringLength(4)]
        public string IWERK { get; set; }
        [StringLength(50)]
        public string GEWRK { get; set; }
        [StringLength(500)]
        public string EQKTX { get; set; }
        [StringLength(50)]
        public string ILOAN { get; set; }
        [StringLength(50)]
        public string TPLNR { get; set; }
        [StringLength(50)]
        public string STORT { get; set; }
        [StringLength(50)]
        public string KOSTL { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [StringLength(20)]
        public string ANLNR { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string ARBPL { get; set; }
    }
}