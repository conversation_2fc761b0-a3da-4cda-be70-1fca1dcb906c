﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DeliveryDetailModel", Schema = "Warehouse")]
    public partial class DeliveryDetailModel
    {
        [Key]
        public Guid DeliveryDetailId { get; set; }
        public Guid? DeliveryId { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? StockId { get; set; }
        public int? DateKey { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? UnitPrice { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
        public bool? isDeleted { get; set; }

        [ForeignKey("DateKey")]
        [InverseProperty("DeliveryDetailModel")]
        public virtual DimDateModel DateKeyNavigation { get; set; }
        [ForeignKey("DeliveryId")]
        [InverseProperty("DeliveryDetailModel")]
        public virtual DeliveryModel Delivery { get; set; }
        [ForeignKey("ProductId")]
        [InverseProperty("DeliveryDetailModel")]
        public virtual ProductModel Product { get; set; }
        [ForeignKey("StockId")]
        [InverseProperty("DeliveryDetailModel")]
        public virtual StockModel Stock { get; set; }
    }
}