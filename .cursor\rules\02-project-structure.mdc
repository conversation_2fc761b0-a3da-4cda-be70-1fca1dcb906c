---
description: 
globs: 
alwaysApply: false
---
# Project Structure

## Main Directories
- [lib/](mdc:TTF_MES_Mobile/lib) - Core application code
- [assets/](mdc:TTF_MES_Mobile/assets) - Images, fonts, and static resources
- [android/](mdc:TTF_MES_Mobile/android) and [ios/](mdc:TTF_MES_Mobile/ios) - Platform-specific code

## Key Lib Directories
- **data/** - Data-related files including dummy data for testing
- **element/** - UI components and widgets
- **model/** - Data models for API responses and business logic
- **page/** - Screen implementations organized by feature
- **repository/** - Data access layer with API communication
- **route/** - Navigation configuration in [route.dart](mdc:TTF_MES_Mobile/lib/route/route.dart)
- **service/** - Application services like navigation and global state
- **Storage/** - Data persistence implementations
- **urlApi/** - API endpoint definitions
- **utils/** - Utility functions and helpers
- **Widget/** - Reusable widgets for the UI

## Configuration Files
- [pubspec.yaml](mdc:TTF_MES_Mobile/pubspec.yaml) - Project configuration and dependencies
- [analysis_options.yaml](mdc:TTF_MES_Mobile/analysis_options.yaml) - Dart code analysis configuration

