# Feature Request Template

## 1. Feature Overview
Please create a [Feature Name] feature for our manufacturing system with the following specifications:

### Basic Information
- Feature Name: [e.g., Machine Maintenance]
- Purpose: [e.g., Track and manage machine maintenance schedules and records]
- Target Users: [e.g., Maintenance staff, Supervisors, Operators]

### Core Functionality
Please implement the following core functions:
- List view of all records with pagination
- Detail view for creating/editing records
- Search and filter capabilities
- Master data integration [specify which master data]

## 2. Technical Requirements

### Backend (.NET Core API)
Please create:
1. API Controllers with these endpoints:
   - GET list with pagination and filters
   - GET by ID
   - POST new record
   - PUT update record
   - [Any additional endpoints]

2. Data Models with these key fields:
   - [List key fields and their types]
   - [Specify any relationships with other entities]

3. Repository layer with:
   - CRUD operations
   - Custom queries
   - Data validation rules

### Mobile App (Flutter)
Please create:
1. List Page with:
   - Paginated list view
   - Pull-to-refresh
   - Search/filter functionality
   - Offline support
   - Error handling

2. Detail Page with:
   - Form validation
   - Master data integration
   - Date/time pickers
   - Save/update functionality
   - Error handling

3. Integration Requirements:
   - Route configuration
   - Permission handling
   - Session management
   - Network state handling

## 3. Technical Specifications

### API Structure
- Base URL: /api/v1/MES/[FeatureName]
- Response Format: ApiResponse<T>
- Authentication: JWT Token
- Error Handling: Standard HTTP status codes

### Mobile Implementation
- State Management: Local state with StatefulWidget
- Data Persistence: Local storage for offline support
- UI Components: Material Design
- Network Layer: HTTP client with interceptors

### Required Dependencies
Backend:
- EntityFramework Core
- AutoMapper
- [Other backend packages]

Mobile:
- http: ^0.13.0
- intl: ^0.17.0
- connectivity_plus: ^2.0.0
- [Other Flutter packages]

## 4. Additional Requirements

### Security
- Role-based access control
- Data validation
- Secure storage for sensitive data
- Session management

### Performance
- Pagination (20 items per page)
- Caching strategies
- Lazy loading
- Request debouncing

### Error Handling
- Network errors
- Validation errors
- Session timeout
- Server errors

### UI/UX Requirements
- Responsive design
- Loading indicators
- Error messages
- Confirmation dialogs
- Form validation feedback

## 5. Testing Requirements
- Unit tests for core functionality
- Widget tests for UI components
- Integration tests for API endpoints
- Error scenario testing

## 6. Documentation Requirements
Please provide:
- API endpoint documentation
- Data model documentation
- Implementation details
- State management documentation
- Error handling documentation
- Testing guidelines

## Step-by-Step Feature Creation Guide

### Step 1: Initial Feature Definition
Please help me create a [Feature Name] feature for our manufacturing system. Here are the basic requirements:

- Feature Name: [e.g., Machine Maintenance]
- Purpose: [e.g., Track and manage machine maintenance schedules]
- Target Users: [e.g., Maintenance staff, Supervisors]
- Core Functions Needed:
  1. List view with pagination
  2. Detail view for create/edit
  3. [Other key functions]

Please provide the initial API endpoints and data model design.

### Step 2: Backend API Development
For the [Feature Name] feature, please create the API implementation with:

1. API Controller with these endpoints:
   - GET list (with pagination)
   - GET by ID
   - POST new record
   - PUT update record
   [Add other endpoints if needed]

2. Data Model with these fields:
   [List your key fields and types]

Please provide the controller and model implementation in C#.

### Step 3: Repository Layer
For the [Feature Name] feature, please create the repository layer with:

1. Basic CRUD operations
2. Custom queries for:
   [List specific query requirements]
3. Data validation rules:
   [List validation requirements]

Please provide the repository implementation in C#.

### Step 4: Mobile List Page
For the [Feature Name] feature, please create the mobile list page with:

1. Paginated list view (20 items)
2. Search and filter capabilities
3. Pull-to-refresh
4. Error handling
5. Offline support

Please provide the Flutter implementation.

### Step 5: Mobile Detail Page
For the [Feature Name] feature, please create the detail page with:

1. Form fields for:
   [List your form fields]
2. Validation rules
3. Master data integration
4. Save/update functionality
5. Error handling

Please provide the Flutter implementation.

### Step 6: Route Integration
Please help integrate the [Feature Name] feature into our mobile app routing:

1. Add routes for list and detail pages
2. Handle navigation arguments
3. Set up permission checks
4. Add to main menu

Please provide the route configuration and main page integration.

### Step 7: Error Handling
Please implement error handling for the [Feature Name] feature:

1. Network errors
2. Validation errors
3. Session timeout
4. Offline mode
5. Error messages and UI feedback

Please provide both API and mobile implementations.

### Step 8: Testing
Please create tests for the [Feature Name] feature:

1. API endpoint tests
2. Repository tests
3. Mobile widget tests
4. Integration tests

Please provide the test implementations.

## Example Usage (Machine Maintenance)

### Step 1: Initial Feature Definition
Please help me create a Machine Maintenance feature for our manufacturing system. Here are the basic requirements:

- Feature Name: Machine Maintenance
- Purpose: Track and manage preventive and corrective maintenance activities
- Target Users: Maintenance Engineers, Supervisors, Operators
- Core Functions Needed:
  1. List view of maintenance records with pagination
  2. Detail view for creating/editing records
  3. Maintenance schedule calendar view
  4. File attachments for maintenance reports

Please provide the initial API endpoints and data model design.

### Step 2: Backend API Development
For the Machine Maintenance feature, please create the API implementation with:

1. API Controller with these endpoints:
   - GET MaintenanceList (with pagination)
   - GET Maintenance/{id}
   - POST Maintenance
   - PUT Maintenance/{id}
   - POST MaintenanceAttachment

2. Data Model with these fields:
   - Id (string)
   - MachineId (string)
   - MaintenanceType (string) - Preventive/Corrective
   - ScheduledDate (DateTime)
   - Status (string)
   - AssignedTechnician (string)
   - Description (string)
   - AttachmentUrls (List<string>)

Please provide the controller and model implementation in C#.

[Continue with subsequent steps following the same pattern...]

## Tips for Using These Prompts:

1. **Sequential Development**:
   - Follow the steps in order
   - Wait for each step to complete before moving to the next
   - Verify the output before proceeding

2. **Customization**:
   - Modify field names and types for your needs
   - Add or remove functionality as required
   - Adjust validation rules to your business needs

3. **Integration**:
   - Make sure to mention any existing systems to integrate with
   - Specify any specific coding patterns to follow
   - Mention any dependencies or constraints

4. **Testing**:
   - Request specific test scenarios
   - Include edge cases
   - Specify expected behaviors

5. **Documentation**:
   - Ask for comments in complex logic
   - Request API documentation
   - Ask for usage examples