import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/maintenanceOrderModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import 'package:ttf/element/DropdownLsQC.dart';
import 'package:ttf/element/RowTimeLsQC.dart';
import 'package:ttf/repository/showDateTime.dart';
import 'package:ttf/repository/function/loginFunction.dart';

class FilterListMaintenanceOrder extends StatefulWidget {
  final MaintenanceOrderSearchModel? searchModel;
  final Function(MaintenanceOrderSearchModel) onFilterSelected;
  final String token;
  final DataUser user;

  const FilterListMaintenanceOrder({
    Key? key,
    this.searchModel,
    required this.onFilterSelected,
    required this.token,
    required this.user,
  }) : super(key: key);

  @override
  _FilterListMaintenanceOrderState createState() => _FilterListMaintenanceOrderState();
}

class _FilterListMaintenanceOrderState extends State<FilterListMaintenanceOrder> {
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _showErrorNoti = false;
  String _messageErrorNoti = "";

  final TextEditingController _orderNumberController = TextEditingController();
  String? _fromTime;
  String? _toTime;
  String? _selectedStatus;
  CommonDates? _selectedCommonDates;
  List<CommonDates>? _commonDates;
  CommonDateModel? _commonDateModel;
  FilterQCModel? _filterLSQC;

  final List<String> _statusOptions = [
    'ALL',
    'CREATED',
    'IN_PROCESS',
    'COMPLETED',
  ];

  final _focusCompany = TextEditingController();
  SalesOrgCodes? _selectedSalesOrgCodes;
  List<SalesOrgCodes>? _salesOrgCodes;

  final FocusNode _orderNumberFocusNode = FocusNode();
  bool _isOrderNumberFocused = false;

  @override
  void initState() {
    super.initState();
    _orderNumberController.text = widget.searchModel?.orderNumber ?? '';
    _selectedStatus = widget.searchModel?.status ?? 'ALL';
    _orderNumberFocusNode.addListener(() {
      setState(() {
        _isOrderNumberFocused = _orderNumberFocusNode.hasFocus;
      });
    });
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      // Load company list first
      final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);

      if (!mounted) return;

      if (companyList != null) {
        setState(() {
          _salesOrgCodes = companyList.map((company) => SalesOrgCodes(saleOrgCode: company.companyCode, storeName: company.companyName)).toList();

          if (_salesOrgCodes!.isNotEmpty) {
            _selectedSalesOrgCodes = widget.searchModel?.companyCode != null
                ? _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.searchModel!.companyCode,
                    orElse: () => _salesOrgCodes!.firstWhere(
                      (element) => element.saleOrgCode == widget.user.companyCode,
                      orElse: () => _salesOrgCodes!.first,
                    ),
                  )
                : _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.user.companyCode,
                    orElse: () => _salesOrgCodes!.first,
                  );
          }
        });
      }

      // Load common dates
      final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.token);
      if (dataDropdown != null) {
        setState(() {
          _commonDates = dataDropdown.data?.commonDates;

          if (widget.searchModel?.fromDate != null && widget.searchModel?.toDate != null) {
            _fromTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.fromDate!);
            _toTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.toDate!);
            _selectedCommonDates = _findMatchingCommonDate(
              widget.searchModel!.fromDate!,
              widget.searchModel!.toDate!,
            );
          } else {
            _selectedCommonDates = _commonDates?.firstWhere(
              (element) => element.catalogCode == "ThisMonth",
              orElse: () => _commonDates!.first,
            );
            _getCommonDate(_selectedCommonDates);
          }
        });
      }
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _getCommonDate(CommonDates? selectedCommonDates) async {
    if (!mounted || selectedCommonDates == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      if (selectedCommonDates.catalogCode == "Custom") {
        setState(() {
          _fromTime = null;
          _toTime = null;
        });
        return;
      }

      final getCommonDateModel = await ListQCFunction.getCommonDateModel(
        selectedCommonDates.catalogCode!,
        widget.token,
      );

      if (!mounted) return;

      if (getCommonDateModel != null && getCommonDateModel.fromDate != null && getCommonDateModel.toDate != null) {
        setState(() {
          _commonDateModel = getCommonDateModel;
          _fromTime = getCommonDateModel.fromDate;
          _toTime = getCommonDateModel.toDate;
        });
      } else {
        debugPrint("Invalid common date model received");
        setState(() {
          _showErrorNoti = true;
          _messageErrorNoti = "Lỗi khi tải thời gian";
        });
      }
    } catch (error) {
      debugPrint("Error in _getCommonDate: $error");
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi khi tải thời gian";
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _setSelectedSalesOrgCodes(SalesOrgCodes? value) async {
    if (value == null) return;

    setState(() {
      _selectedSalesOrgCodes = value;
    });

    // Reload data for new company if needed
    await _getCommonDate(_selectedCommonDates);
  }

  void _applyFilter() {
    final filter = MaintenanceOrderSearchModel(
      companyCode: _selectedSalesOrgCodes?.saleOrgCode ?? widget.user.companyCode ?? '',
      orderNumber: _orderNumberController.text.isEmpty ? null : _orderNumberController.text,
      status: _selectedStatus == 'ALL' ? null : _selectedStatus,
      fromDate: _fromTime != null ? DateTime.parse(_fromTime!) : null,
      toDate: _toTime != null ? DateTime.parse(_toTime!) : null,
      pageNumber: 1,
      pageSize: 20,
    );

    widget.onFilterSelected(filter);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => Future.value(false),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 40.h),
          child: Drawer(
            backgroundColor: Colors.white,
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                        color: const Color(0xff0052cc),
                        child: Text(
                          "Tìm kiếm Maintenance Orders",
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(15.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DropdownSalesOrgCodes(
                              title: 'Nhà máy',
                              onTap: _setSelectedSalesOrgCodes,
                              lsSalesOrgCodes: _salesOrgCodes,
                              selectedSalesOrgCodes: _selectedSalesOrgCodes,
                            ),
                            SizedBox(height: 15.h),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "Số phiếu:",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                Stack(
                                  children: [
                                    TextFormField(
                                      controller: _orderNumberController,
                                      focusNode: _orderNumberFocusNode,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(3.r),
                                          borderSide: BorderSide(
                                            color: Colors.grey.shade400,
                                            width: 0.5.w,
                                          ),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(3.r),
                                          borderSide: BorderSide(
                                            color: Colors.grey.shade400,
                                            width: 0.5.w,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(3.r),
                                          borderSide: BorderSide(
                                            color: Colors.grey.shade400,
                                            width: 0.5.w,
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {}); // Rebuild to show/hide clear button
                                      },
                                    ),
                                    if (_isOrderNumberFocused && _orderNumberController.text.isNotEmpty)
                                      Positioned(
                                        right: 8.w,
                                        top: 0,
                                        bottom: 0,
                                        child: IconButton(
                                          icon: Icon(Icons.close, size: 16.sp, color: Colors.red),
                                          onPressed: () {
                                            setState(() {
                                              _orderNumberController.clear();
                                            });
                                          },
                                          padding: EdgeInsets.zero,
                                          constraints: const BoxConstraints(),
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: 15.h),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  "Trạng thái:",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 5.h),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                                    borderRadius: BorderRadius.circular(3.r),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      isExpanded: true,
                                      itemHeight: null,
                                      isDense: true,
                                      value: _selectedStatus,
                                      iconSize: 15.sp,
                                      style: const TextStyle(color: Colors.black),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedStatus = value;
                                        });
                                      },
                                      items: _statusOptions.map((status) {
                                        String displayText;
                                        switch (status) {
                                          case 'ALL':
                                            displayText = 'Tất cả';
                                            break;
                                          case 'COMPLETED':
                                            displayText = 'Đã hoàn thành';
                                            break;
                                          case 'IN_PROCESS':
                                            displayText = 'Đang thực hiện';
                                            break;
                                          case 'CREATED':
                                            displayText = 'Mới tạo';
                                            break;
                                          default:
                                            displayText = status;
                                        }
                                        return DropdownMenuItem<String>(
                                          value: status,
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(vertical: 5.h),
                                            child: Text(
                                              displayText,
                                              style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      selectedItemBuilder: (BuildContext context) {
                                        return _statusOptions.map<Widget>((status) {
                                          String displayText;
                                          switch (status) {
                                            case 'ALL':
                                              displayText = 'Tất cả';
                                              break;
                                            case 'COMPLETED':
                                              displayText = 'Đã hoàn thành';
                                              break;
                                            case 'IN_PROCESS':
                                              displayText = 'Đang thực hiện';
                                              break;
                                            case 'CREATED':
                                              displayText = 'Mới tạo';
                                              break;
                                            default:
                                              displayText = status;
                                          }
                                          return Text(
                                            displayText,
                                            style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                            overflow: TextOverflow.ellipsis,
                                          );
                                        }).toList();
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 15.h),
                            _buildDateSelection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (_showErrorNoti)
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.red.shade900),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 9,
                          child: Text(
                            _messageErrorNoti,
                            style: TextStyle(fontSize: 12.sp, color: Colors.white),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: IconButton(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () {
                              setState(() {
                                _showErrorNoti = false;
                              });
                            },
                            icon: const Icon(Icons.cancel),
                            iconSize: 15.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: EdgeInsets.all(15.w),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xff0052cc),
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                        ),
                        onPressed: _applyFilter,
                        icon: Icon(Icons.search, size: 18.sp),
                        label: Text(
                          "Tìm kiếm",
                          style: TextStyle(fontSize: 14.sp),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      children: [
        DropdownCommonDates(
          title: 'Thời gian',
          onTap: (value) async {
            setState(() {
              _selectedCommonDates = value;
            });
            if (value?.catalogCode != "Custom") {
              await _getCommonDate(value);
            } else {
              setState(() {
                _fromTime = null;
                _toTime = null;
              });
            }
          },
          lsWorkCommonDates: _commonDates,
          selectedCommonDates: _selectedCommonDates,
        ),
        SizedBox(height: 10.h),
        if (_selectedCommonDates?.catalogCode == "Custom") ...[
          ColumnDateLsQC(
            title: 'Từ ngày',
            date: () => Platform.isAndroid ? _pickDateFromComplete(context) : _pickDateIOSFormComplete(context),
            displayDate: _fromTime == null ? "dd/mm/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!)),
          ),
          SizedBox(height: 10.h),
          ColumnDateLsQC(
            title: 'Đến ngày',
            date: () => Platform.isAndroid ? _pickDateToComplete(context) : _pickDateIOSToComplete(context),
            displayDate: _toTime == null ? "dd/mm/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!)),
          ),
        ] else ...[
          RowTimeLsQC(
            title: 'Từ ngày',
            time: _isLoading
                ? "Loading..."
                : _fromTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!))
                    : "",
          ),
          SizedBox(height: 10.h),
          RowTimeLsQC(
            title: 'Đến ngày',
            time: _isLoading
                ? "Loading..."
                : _toTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!))
                    : "",
          ),
        ],
      ],
    );
  }

  // Date picker methods
  Future<void> _pickDateFromComplete(BuildContext context) async {
    try {
      DateTime initialDate = _fromTime != null ? DateTime.parse(_fromTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        if (_toTime != null) {
          final endDate = DateTime.parse(_toTime!);
          if (picked.isAfter(endDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày bắt đầu không thể sau ngày kết thúc";
            });
            return;
          }
        }

        setState(() {
          _fromTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateToComplete(BuildContext context) async {
    try {
      DateTime initialDate = _toTime != null ? DateTime.parse(_toTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        if (_fromTime != null) {
          final startDate = DateTime.parse(_fromTime!);
          if (picked.isBefore(startDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày kết thúc không thể trước ngày bắt đầu";
            });
            return;
          }
        }

        setState(() {
          _toTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateIOSFormComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _fromTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Error selecting date";
      });
    }
  }

  Future<void> _pickDateIOSToComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _toTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Error selecting date";
      });
    }
  }

  CommonDates? _findMatchingCommonDate(DateTime fromDate, DateTime toDate) {
    if (_commonDates == null) return null;

    for (var commonDate in _commonDates!) {
      if (commonDate.catalogCode == "Custom") continue;

      final now = DateTime.now();
      DateTime? startDate;
      DateTime? endDate;

      switch (commonDate.catalogCode) {
        case "Today":
          startDate = DateTime(now.year, now.month, now.day);
          endDate = startDate;
          break;
        case "Yesterday":
          startDate = DateTime(now.year, now.month, now.day - 1);
          endDate = startDate;
          break;
        case "ThisWeek":
          startDate = now.subtract(Duration(days: now.weekday - 1));
          endDate = startDate.add(const Duration(days: 6));
          break;
        case "LastWeek":
          final lastWeekStart = now.subtract(Duration(days: now.weekday + 6));
          startDate = lastWeekStart;
          endDate = lastWeekStart.add(const Duration(days: 6));
          break;
        case "ThisMonth":
          startDate = DateTime(now.year, now.month, 1);
          endDate = DateTime(now.year, now.month + 1, 0);
          break;
        case "LastMonth":
          startDate = DateTime(now.year, now.month - 1, 1);
          endDate = DateTime(now.year, now.month, 0);
          break;
        default:
          continue;
      }

      if (startDate != null && endDate != null) {
        final normalizedFromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        final normalizedToDate = DateTime(toDate.year, toDate.month, toDate.day);
        final normalizedStartDate = DateTime(startDate.year, startDate.month, startDate.day);
        final normalizedEndDate = DateTime(endDate.year, endDate.month, endDate.day);

        if (normalizedFromDate.isAtSameMomentAs(normalizedStartDate) && normalizedToDate.isAtSameMomentAs(normalizedEndDate)) {
          return commonDate;
        }
      }
    }

    return _commonDates!.firstWhere(
      (element) => element.catalogCode == "Custom",
      orElse: () => _commonDates!.first,
    );
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _focusCompany.dispose();
    _orderNumberFocusNode.dispose();
    super.dispose();
  }
}
