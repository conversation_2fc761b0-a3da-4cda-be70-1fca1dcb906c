import 'package:shared_preferences/shared_preferences.dart';

class StorageSharedPreferences {
  static SharedPreferences? _prefs;


  static Future init() async =>
      _prefs = await SharedPreferences.getInstance();

  static Future setList(String key,List<String> value, ) async =>
      await _prefs?.setStringList(key, value);

  static  List<String>? getList(String key) => _prefs?.getStringList(key);




  static Future setBool(String key,bool value) async =>
      await _prefs?.setBool(key, value);
  static bool? getBool(String key) => _prefs?.getBool(key);

  static Future setString(String key,String imagePath) async =>
      await  _prefs?.setString(key, imagePath);

  static String? getString(String key) =>  _prefs?.getString(key);

  static Future setInt(String key,int value) async =>
      await  _prefs?.setInt(key, value);

  static int? getInt(String key) =>  _prefs?.getInt(key);
  static Future removeShared(String key) async => await _prefs?.remove(key);


}