import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Widget/dialogWidget/DialogErrorValidate.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../model/switchingStagesApi.dart';
import '../api/switchingStagesApi.dart';

class SwitchingStagesFunction {
  static AdditionalData defaultValueAdditionalData = AdditionalData(arbpLSUB: " ", display: '--Vui lòng chọn--');
  static bool checkIsSend = false;
  static Future<void> postSwitchingSages(
      BuildContext context, String toBarcode, String toStepCode, DataSwitchingStages dataSwitchingStages, String token) async {
    try {
      checkIsSend = false;
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await SwitchingStagesApi.postSwitchingStages(dataSwitchingStages.taskId.toString(), toBarcode, toStepCode, token);
      Navigator.pop(context);
      checkIsSend = true;
      if (kDebugMode) {
        print(response.statusCode);
      }
      final check = jsonDecode(response.body);
      if (check['code'] == 201 && check['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            check['data'],
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
        ));
        Future.delayed(const Duration(seconds: 0), () {
          Navigator.pop(context, true);
        });
      } else if (check['code'] == 304) {
        showDialog(
            context: context, barrierDismissible: false, builder: (BuildContext context) => DiaLogErrorValidate(message: check['data'].toString()));
      } else {
        showDialog(
            context: context, barrierDismissible: false, builder: (BuildContext context) => DiaLogErrorValidate(message: check['data'].toString()));
      }
    } on SocketException catch (_) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Không có kết nối mạng',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    } catch (error) {
      if (checkIsSend == false) {
        Navigator.pop(context);
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        content: Text(
          'Xảy ra lỗi! Vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp, color: Colors.white),
        ),
      ));
    }
  }

  static String getCreateTime(DataSwitchingStages dataSwitchingStages) {
    DateTime stringToDateTime = DateFormat("yyyy-MM-ddThh:mm").parse(dataSwitchingStages.createTime.toString());
    String dateToString = DateFormat("dd/MM/yyyy hh:mm").format(stringToDateTime);
    return dateToString;
  }

  static List<AdditionalData> getAdditionalData(SwitchingStages? switchingStages) {
    List<AdditionalData> lsGetAdditionData = [];
    lsGetAdditionData.insert(0, (defaultValueAdditionalData));
    if (switchingStages != null && switchingStages.additionalData!.isNotEmpty) {
      for (var i in switchingStages.additionalData!) {
        lsGetAdditionData.add(i);
      }
    }
    return lsGetAdditionData;
  }
}
