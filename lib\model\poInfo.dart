class POInfo {
  final String po;
  final int totalQuantity;
  final int importedQuantity;
  final String productCode;
  final String productName;
  final String? so;
  final String? soItem;
  final String? wbs;
  final int importCount;
  final int lastImportSequence;

  POInfo({
    required this.po,
    required this.totalQuantity,
    required this.importedQuantity,
    required this.productCode,
    required this.productName,
    this.so,
    this.soItem,
    this.wbs,
    required this.importCount,
    required this.lastImportSequence,
  });

  // Computed properties
  int get quantity => totalQuantity; // For backward compatibility
  int get remainingQuantity => totalQuantity - importedQuantity;
  bool get canImportMore => remainingQuantity > 0;

  factory POInfo.fromJson(Map<String, dynamic> json) {
    return POInfo(
      po: json['po'] ?? '',
      totalQuantity: json['totalQuantity'] ?? 0,
      importedQuantity: json['importedQuantity'] ?? 0,
      productCode: json['productCode'] ?? '',
      productName: json['productName'] ?? '',
      so: json['so'],
      soItem: json['soItem'],
      wbs: json['wbs'],
      importCount: json['importCount'] ?? 0,
      lastImportSequence: json['lastImportSequence'] ?? 0,
    );
  }
}
