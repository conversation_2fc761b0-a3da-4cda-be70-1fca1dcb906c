﻿using iMES_API.Configures;
using iMES_API.Infrastructures.Services;
using iMES_API.ServiceExtensions;
using iMES_API.ViewModel.SAP;
using ISD.API.EntityModels.Data;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.ServiceExtensions;
using ISD.API.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SapNwRfc.Pooling;
using System;

namespace iMES_API.Extensions
{
    public static class ServiceExtensions
    {
        private static readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            var origins = configuration.GetValue<string>("AllowedOrigins").Split(";");
            services.AddCors(options =>
            {
                options.AddPolicy(name: MyAllowSpecificOrigins,
                                  builder =>
                                  {
                                      builder.WithOrigins(origins).AllowAnyMethod().AllowAnyHeader();
                                  });
            });

            services.AddControllers();
            services.AddJWTTokenServices(configuration);

            // Swagger Config
            SwaggerConfig.Configure(services, configuration);

            services.Configure<JwtSettings>(configuration.GetSection("JsonWebTokenKeys"));
            services.AddUnitOfwork(); // Dependency Injection, AddScoped
            services.AddSyncData();
            services.AddRepositories();
            services.AddCustomServices();

            var is300 = configuration.GetValue<bool>("Is300");

            if (!is300)
            {
                HangfireConfig.Configure(services, configuration);
            };

            services.AddSapConnection(configuration, is300);

            services.AddControllers().AddNewtonsoftJson();
            services.AddAutoMapper(typeof(AutoMapperProfile).Assembly);
            services.AddHttpContextAccessor();
            //services.AddSingleton(new SAPService);

            var connectionStringName = configuration.GetValue<string>("ConnectionStringName") ?? "DefaultConnection";
            var connectionString = configuration.GetConnectionString(connectionStringName);

            services.AddDbContext<EntityDataContext>(options => options.UseSqlServer(connectionString));
            
            // Register the DbContextFactory
            services.AddScoped<iMES_API.Helpers.DbContextFactory>();
            
            // Register the DatabaseService
            services.AddScoped<iMES_API.Services.DatabaseService>();
            
            // Register the ExampleService
            services.AddScoped<iMES_API.Services.ExampleService>();

            //services.AddDbContext<EntityDataContext>(options =>
            //{
            //    options.UseSqlServer(connectionString, sqlOptions =>
            //    {
            //        sqlOptions.MaxBatchSize(100);
            //        sqlOptions.CommandTimeout(90);
            //        sqlOptions.EnableRetryOnFailure(
            //            maxRetryCount: 3,
            //            maxRetryDelay: TimeSpan.FromSeconds(5),
            //            errorNumbersToAdd: null);
            //        sqlOptions.EnableRetryOnFailure();
            //    });
            //}, ServiceLifetime.Scoped);

            // Configure connection resiliency
            services.Configure<DbContextOptionsBuilder>(options => 
            {
                options.EnableSensitiveDataLogging(false);          // Disable sensitive logging in production
                options.EnableDetailedErrors(false);                // Disable detailed errors in production
            });

            services.AddApiVersioning(o =>
            {
            });

            services.AddHttpClient<MvcClientService>(client =>
            {
                client.BaseAddress = new Uri("https://localhost:44326/");
            });

            // Register AuditService
            services.AddScoped<IAuditService, AuditService>();


            services.AddMemoryCache();
            // // or
            // services.AddStackExchangeRedisCache(options => {
            //     options.Configuration = "localhost:6379";
            // });

            // services.AddRateLimiter(options =>
            // {
            //     options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(context =>
            //         RateLimitPartition.GetFixedWindowLimiter(
            //             partitionKey: "",
            //             factory: partition => new FixedWindowRateLimiterOptions
            //             {
            //                 AutoReplenishment = true,
            //                 PermitLimit = 1000,
            //                 Window = TimeSpan.FromSeconds(1)
            //             }));
            // });

            return services;
        }

        private static IServiceCollection AddSapConnection(this IServiceCollection services, IConfiguration configuration, bool is300)
        {
            var environment = is300 ? "DEV" : "PRD";
            var sapConfig = configuration.GetSection($"SAP:{environment}").Get<SAPConfiguration>();

            if (sapConfig == null)
            {
                throw new InvalidOperationException($"SAP configuration for environment '{environment}' not found");
            }

            string sapConnectionString =
                $"AppServerHost={sapConfig.SAPAppServerHost};" +
                $"SystemNumber={sapConfig.SAPSystemNumber};" +
                $"User={sapConfig.SAPUsername};" +
                $"Password={sapConfig.SAPPassword};" +
                $"Client={sapConfig.SAPClient};" +
                $"Language={sapConfig.SAPLanguage};" +
                $"PoolSize={sapConfig.SAPMaxPoolSize};";

            services.AddSingleton<ISapConnectionPool>(_ => new SapConnectionPool(sapConnectionString));
            services.AddScoped<ISapPooledConnection, SapPooledConnection>();
            services.AddScoped<ISDAuthorizationAttribute>();

            return services;
        }
    }
}
