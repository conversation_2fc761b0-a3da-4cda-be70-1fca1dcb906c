# Frontend Development Patterns and Standards (Flutter)

## 1. Project Structure
```yaml
lib/
  ├── main.dart
  ├── route/
  │   └── route.dart
  ├── page/
  │   └── feature/
  │       ├── feature_list.dart
  │       └── feature_detail.dart
  ├── model/
  │   └── feature_model.dart
  ├── repository/
  │   ├── function/
  │   │   └── feature_function.dart
  │   └── model/
  │       └── feature_repository_model.dart
  ├── widget/
  │   └── common/
  └── util/
      └── helpers.dart
```

## 2. State Management Pattern
```dart
class FeaturePage extends StatefulWidget {
  final String dateTimeOld;
  final DataUser user;

  const FeaturePage({
    Key? key,
    required this.dateTimeOld,
    required this.user,
  }) : super(key: key);

  @override
  State<FeaturePage> createState() => _FeaturePageState();
}

class _FeaturePageState extends State<FeaturePage> {
  // State variables
  bool _isLoading = true;
  bool _isNotWifi = false;
  List<FeatureModel>? _records;
  
  // Controllers
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupConnectivity();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
```

## 3. Network Layer Pattern
```dart
class FeatureFunction {
  static Future<ApiResponse<List<FeatureModel>>> getList({
    required String dateTimeOld,
    required DataUser user,
    required int pageNumber,
    required int pageSize,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/Feature/List'),
        headers: {
          'Authorization': 'Bearer ${user.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'pageNumber': pageNumber,
          'pageSize': pageSize,
          'dateTimeOld': dateTimeOld,
        }),
      );

      if (response.statusCode == 200) {
        return ApiResponse.fromJson(
          jsonDecode(response.body),
          (json) => (json as List)
              .map((item) => FeatureModel.fromJson(item))
              .toList(),
        );
      }
      
      throw Exception('Failed to load data');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}
```

## 4. Model Pattern
```dart
class FeatureModel {
  final String id;
  final String name;
  final DateTime date;
  final String status;

  FeatureModel({
    required this.id,
    required this.name,
    required this.date,
    required this.status,
  });

  factory FeatureModel.fromJson(Map<String, dynamic> json) {
    return FeatureModel(
      id: json['id'] as String,
      name: json['name'] as String,
      date: DateTime.parse(json['date'] as String),
      status: json['status'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'date': date.toIso8601String(),
      'status': status,
    };
  }
}
```

## 5. UI Patterns

### List Page Pattern
```dart
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      title: Text('Feature List'),
      actions: [
        IconButton(
          icon: Icon(Icons.add),
          onPressed: () => _navigateToDetail(null),
        ),
      ],
    ),
    body: _buildBody(),
  );
}

Widget _buildBody() {
  if (_isNotWifi) {
    return LostConnect(onRetry: _refreshData);
  }

  if (_isLoading) {
    return LoadingWidget();
  }

  return RefreshIndicator(
    onRefresh: _refreshData,
    child: ListView.builder(
      itemCount: _records?.length ?? 0,
      itemBuilder: (context, index) {
        final item = _records![index];
        return _buildListItem(item);
      },
    ),
  );
}
```

### Detail Page Pattern
```dart
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      title: Text(_id == null ? 'Create' : 'Edit'),
      actions: [
        if (_id != null)
          IconButton(
            icon: Icon(Icons.delete),
            onPressed: _confirmDelete,
          ),
      ],
    ),
    body: Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            _buildFormFields(),
            _buildButtons(),
          ],
        ),
      ),
    ),
  );
}
```

## 6. Form Management Pattern
```dart
// Controller Declaration
final _formKey = GlobalKey<FormState>();
final _nameController = TextEditingController();
final _dateController = TextEditingController();

// Validation
String? _validateRequired(String? value) {
  if (value == null || value.isEmpty) {
    return 'This field is required';
  }
  return null;
}

String? _validateDate(String? value) {
  if (value == null || value.isEmpty) {
    return 'Date is required';
  }
  try {
    final date = DateFormat('dd-MM-yyyy').parse(value);
    if (date.isAfter(DateTime.now())) {
      return 'Date cannot be in the future';
    }
  } catch (e) {
    return 'Invalid date format';
  }
  return null;
}

// Form Fields
Widget _buildFormFields() {
  return Column(
    children: [
      TextFormField(
        controller: _nameController,
        decoration: InputDecoration(labelText: 'Name'),
        validator: _validateRequired,
      ),
      TextFormField(
        controller: _dateController,
        decoration: InputDecoration(labelText: 'Date'),
        validator: _validateDate,
        onTap: () => _selectDate(context),
        readOnly: true,
      ),
    ],
  );
}
```

## 7. Error Handling Pattern
```dart
Future<void> _handleApiCall(Future<void> Function() apiCall) async {
  try {
    setState(() => _isLoading = true);
    await apiCall();
  } on SocketException {
    setState(() => _isNotWifi = true);
  } catch (e) {
    _showErrorDialog('Error', e.toString());
  } finally {
    setState(() => _isLoading = false);
  }
}

void _showErrorDialog(String title, String message) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('OK'),
        ),
      ],
    ),
  );
}
```

## 8. Navigation Pattern
```dart
// Route Definition
case '/Feature':
  return MaterialPageRoute(
    settings: const RouteSettings(name: '/Feature'),
    builder: (_) {
      CommonScreenUserArgument argument = args as CommonScreenUserArgument;
      return FeatureList(
        dateTimeOld: argument.dateTimeOld,
        user: argument.user,
      );
    },
  );

// Navigation
void _navigateToDetail(String? id) {
  Navigator.pushNamed(
    context,
    '/FeatureDetail',
    arguments: {
      'id': id,
      'dateTimeOld': widget.dateTimeOld,
      'user': widget.user,
    },
  );
}
```

## 9. Permission Handling Pattern
```dart
bool _checkViewPermission() {
  return widget.user.mobileScreenModel
      ?.any((element) => element.screenCode == 'Feature') ?? false;
}

@override
void initState() {
  super.initState();
  if (!_checkViewPermission()) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.pop(context);
    });
  }
}
```

## 10. Testing Pattern
```dart
testWidgets('FeatureList displays items correctly',
    (WidgetTester tester) async {
  // Arrange
  await tester.pumpWidget(MaterialApp(
    home: FeatureList(
      dateTimeOld: '2024-01-01',
      user: mockUser,
    ),
  ));

  // Act
  await tester.pumpAndSettle();

  // Assert
  expect(find.byType(ListView), findsOneWidget);
  expect(find.text('Item 1'), findsOneWidget);
});
```

## 11. Performance Patterns
```yaml
image_optimization:
  - "Use CachedNetworkImage"
  - "Implement proper image sizing"
  - "Lazy loading for lists"

state_management:
  - "Minimize setState calls"
  - "Use const constructors"
  - "Implement proper widget keys"

memory_management:
  - "Dispose controllers"
  - "Cancel streams"
  - "Clear caches when needed"
```

## 12. Common Widgets Pattern
```dart
class LoadingWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(),
    );
  }
}

class ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const ErrorWidget({
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(message),
          ElevatedButton(
            onPressed: onRetry,
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }
}
```
