# Downtime Tracking Module

The Downtime Tracking module in TTF MES Mobile provides tools for recording, analyzing, and managing production downtime events. This module helps identify downtime causes, reduce unplanned stoppages, and improve overall equipment effectiveness in the manufacturing environment.

## Module Structure

The Downtime Tracking module is located primarily in `lib/page/Downtime/` and consists of the following components:

### Main Components

- `DowntimeList.dart`: List of downtime events
- `DowntimeDetail.dart`: Detailed view of downtime records
- Related model files in `lib/model/` directory

### Supporting Files

- Related components in `lib/element/Downtime/` for UI elements
- Connection to maintenance management for equipment issues
- Integration with production management for impact analysis

## Features

### Downtime Recording

The downtime recording functionality allows users to:

- Log production stoppage events
- Classify downtime by type and reason
- Record start and end times accurately
- Document affected equipment and work centers
- Attach photos or documentation of issues

### Downtime Analysis

The downtime analysis feature enables:

- Calculation of downtime metrics (frequency, duration)
- Pareto analysis of downtime causes
- Trend analysis over time
- Impact assessment on production
- Comparison against targets and benchmarks

### Reason Code Management

The reason code management functionality supports:

- Standardized downtime reason codes
- Hierarchical classification (category, reason, subreason)
- Custom reason documentation
- Reason code mapping to root causes
- Continuous improvement of coding structure

### Action Tracking

The action tracking system allows:

- Assignment of corrective actions
- Action status monitoring
- Effectiveness measurement
- Action priority management
- Recurrence prevention verification

### Integration with OEE

The OEE (Overall Equipment Effectiveness) integration provides:

- Downtime impact on availability calculations
- Equipment reliability analysis
- Scheduled vs. unscheduled downtime tracking
- Shift and operator analysis
- Production loss quantification

## Workflows

### Downtime Recording Process

1. Access the Downtime List (`DowntimeList.dart`)
2. Create new downtime record
3. Scan equipment QR code or select from list
4. Select downtime start time or use current time
5. Classify downtime using reason codes
6. Add detailed description and documentation
7. Assign responsibility if appropriate
8. Submit downtime record
9. Update when issue is resolved with end time

### Downtime Analysis Process

1. Navigate to Downtime List (`DowntimeList.dart`)
2. Filter by time period, equipment, or reason
3. View downtime metrics and trends
4. Analyze top downtime causes
5. Identify patterns and recurring issues
6. Generate downtime reports
7. Share findings with relevant teams

### Corrective Action Process

1. Open Downtime Detail (`DowntimeDetail.dart`)
2. Review downtime event details
3. Create corrective action assignment
4. Specify action details and responsibilities
5. Set priority and target completion date
6. Track action implementation
7. Verify effectiveness of action
8. Close action when complete

### Improvement Process

1. Access Downtime Analysis
2. Identify chronic or high-impact downtime causes
3. Initiate improvement project
4. Document baseline performance
5. Implement countermeasures
6. Monitor impact on downtime metrics
7. Standardize successful solutions

## Data Models

The downtime tracking module uses the following data models:

- **DowntimeRecord**: Downtime event information
- **DowntimeReason**: Reason code structure
- **DowntimeAction**: Corrective action tracking
- **DowntimeSearchModel**: Search parameters for filtering
- **DowntimeHistory**: Historical downtime data
- **DowntimeAnalytics**: Analysis and metrics data

## API Integration

Downtime operations are synchronized with backend systems through the following API endpoints:

- `/api/downtime/records`: Downtime record management
- `/api/downtime/reasons`: Reason code management
- `/api/downtime/actions`: Corrective action tracking
- `/api/downtime/analysis`: Downtime analysis and metrics
- `/api/downtime/history`: Historical downtime data

## User Interfaces

### List Views
Downtime list views display:
- Equipment identification
- Downtime duration
- Reason code
- Status (ongoing/resolved)
- Impact level
- Action status

### Detail Views
Downtime detail views include:
- Comprehensive downtime information
- Timeline visualization
- Reason code hierarchy
- Action assignments and status
- Related maintenance information
- Documentation attachments
- Resolution details

### Analysis Views
Downtime analysis views provide:
- Pareto charts of downtime causes
- Trend analysis over time
- Equipment comparison
- Shift/operator analysis
- Cost impact calculation
- KPI dashboard

## Integration with Other Modules

The Downtime Tracking module integrates with other modules in the following ways:

- **Maintenance Management**: Creates maintenance requests for equipment issues
- **Production Management**: Updates production schedules based on downtime
- **Quality Control**: Links quality issues to equipment problems
- **Material Consumption**: Identifies material waste from equipment issues
- **Inventory Management**: Adjusts material availability during downtime

## Best Practices

For effective use of the Downtime Tracking module:

1. Record all downtime events, even brief ones
2. Use standardized reason codes consistently
3. Document downtime in real-time when possible
4. Include detailed descriptions for complex issues
5. Assign corrective actions for significant events
6. Regularly review downtime metrics and trends
7. Focus improvement efforts on highest-impact causes

## Future Enhancements

Planned improvements for the Downtime Tracking module include:

1. Predictive analytics for downtime prevention
2. Automated downtime detection from equipment signals
3. Enhanced mobile notification system for critical events
4. Advanced root cause analysis tools
5. Integration with predictive maintenance
6. Real-time OEE dashboards
7. Machine learning for downtime pattern recognition 