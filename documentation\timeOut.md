# `timeOut.dart` Documentation

## Overview

The `timeOut.dart` file manages session timeouts within the Flutter application. It alerts users when their session has expired and prompts them to log back in, ensuring security and proper session management across various screens.

## Key Component

### `TimeOutView` Widget

- **Purpose**: Displays a message indicating that the user’s session has expired and provides a logout button.
- **Parameters**:
  - `disableButton` (bool): Determines if the logout button is disabled.
  - `setButton` (VoidCallback): Callback to handle button state changes.

#### Example Usage

```dart
TimeOutView(
  setButton: _setButton,
  disableButton: _disableButtonTimeout,
)
```

## How It Works

1. **Session Validation**: Each screen checks if the current date matches the stored `dateTimeOld`.
   
   ```dart
   String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
   DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
   DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
   if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
     // Trigger timeout
   }
   ```

2. **Trigger Timeout**:
   - **Android**: Shows `DiaLogTimeOutSessionAndroid`.
   - **iOS**: Shows `DiaLogTimeOutSessionIOS`.

   ```dart
   Platform.isAndroid
     ? showDialog(
         context: context,
         barrierDismissible: false,
         builder: (context) => const DiaLogTimeOutSessionAndroid())
     : showCupertinoDialog(
         context: context,
         barrierDismissible: false,
         builder: (context) => const DiaLogTimeOutSessionIOS());
   ```

3. **Display `TimeOutView`**: Replaces the screen content with `TimeOutView` when a timeout is detected.

   ```dart
   return _timeOut 
     ? Scaffold(
         backgroundColor: Colors.grey.shade200,
         body: TimeOutView(setButton: _setButton, disableButton: _disableButtonTimout))
     : Scaffold(
         // Normal screen content
       );
   ```

4. **User Logout**:
   - Clears session data.
   - Navigates to the login screen.

   ```dart
   ElevatedButton.icon(
     onPressed: disableButton ? null : () async {
       showDialog(
         context: context,
         barrierDismissible: false,
         builder: (_) => const Center(child: CircularProgressIndicator()),
       );
       bool success = await MainPageFunction.removeCurrentUser();
       Navigator.pop(context);
       if (success) {
         ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Logged out successfully')));
         Navigator.pushNamedAndRemoveUntil('/Login', (route) => false);
       } else {
         ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Logout failed')));
       }
     },
     icon: Icon(Icons.refresh),
     label: Text("Logout"),
   ),
   ```

## Workflow Diagram

```mermaid
graph TD;
    A[Start] --> B[Check Session]
    B -->|Valid| C[Proceed]
    B -->|Expired| D[Show Timeout View]
    D --> E[User Clicks Logout]
    E --> F[Clear Session Data]
    F --> G[Navigate to Login]
    G --> H[End]
```

## Screens Using `TimeOutView`

`TimeOutView` is integrated into multiple screens to handle session timeouts consistently:

1. **CompleteTheBigStage.dart**
2. **Info.dart**
3. **ExportWarehouse.dart**
4. **InforSwitchingStages.dart**
5. **SwitchingStages.dart**
6. **DetailReport.dart**
7. **ExportWarehouse2.dart**
8. **InventoryMaterialSloc.dart**
9. **StatisticsMaterials.dart**
10. **CreateNewTraHangNCC.dart**
11. **BaoCaoQCSanPham.dart**
12. **BaoCaoDauVao.dart**
13. **BaoCaoDauVao2.dart**
14. **BaoCaoQCGiaCong.dart**
15. **BaoCaoQCHienTruong.dart**
16. **BaoCaoQCMau.dart**
17. **DetailReservation.dart**

Each of these screens performs session checks and displays the `TimeOutView` when necessary to ensure users are prompted to re-authenticate.

## Dialog Widgets

### Android Dialog (`DiaLogTimeOutSessionAndroid.dart`)

Displays an alert dialog for Android users when the session expires.

```dart
class DiaLogTimeOutSessionAndroid extends StatelessWidget {
  const DiaLogTimeOutSessionAndroid({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Session Expired'),
      content: Text('Your session has expired. Please log in again.'),
      actions: [
        TextButton(
          onPressed: () async {
            await MainPageFunction.removeCurrentUser();
            Navigator.pushNamedAndRemoveUntil('/Login', (route) => false);
          },
          child: Text('OK'),
        ),
      ],
    );
  }
}
```

### iOS Dialog (`DiaLogTimeOutSessionIOS.dart`)

Displays a Cupertino-style alert dialog for iOS users when the session expires.

```dart
class DiaLogTimeOutSessionIOS extends StatelessWidget {
  const DiaLogTimeOutSessionIOS({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text('Session Expired'),
      content: Text('Your session has expired. Please log in again.'),
      actions: [
        CupertinoDialogAction(
          onPressed: () async {
            await MainPageFunction.removeCurrentUser();
            Navigator.pushNamedAndRemoveUntil('/Login', (route) => false);
          },
          child: Text('OK'),
        ),
      ],
    );
  }
}
```

## Technical Details

### `TimeOutView` Widget

```dart
class TimeOutView extends StatelessWidget {
  final bool disableButton;
  final VoidCallback setButton;

  const TimeOutView({
    Key? key,
    required this.disableButton,
    required this.setButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.access_time, size: 45),
          SizedBox(height: 10),
          Text(
            "Session Expired!",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Text(
            "Your session has expired. Please log in again.",
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: disableButton ? null : () async {
              // Handle logout
            },
            icon: Icon(Icons.logout),
            label: Text("Logout"),
          ),
        ],
      ),
    );
  }
}
```

## Summary

`timeOut.dart` ensures that user sessions are monitored and managed effectively across the application. By displaying a consistent timeout view and handling logout operations, it maintains security and enhances the user experience.