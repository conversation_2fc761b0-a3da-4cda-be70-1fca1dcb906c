﻿using ISD.API.EntityModels.Data;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers.WarehouseSyncSAP
{
    public class RemoveLogApiService
    {
        private readonly IServiceProvider serviceProvider;

        public RemoveLogApiService(IServiceProvider serviceProvider)
        {
            this.serviceProvider = serviceProvider;
        }     
        public async Task Run()
        {
            try
            {
                //Create scope db context
                using var scope = serviceProvider.CreateScope();
                using var context = scope.ServiceProvider.GetRequiredService<EntityDataContext>();

                //Xóa data log
                var logApis = context.LogApiModel.AsQueryable();
                context.RemoveRange(logApis);

                await context.SaveChangesAsync();
            }
            catch (Exception)
            {
                return;
            }

            
        }
    }
}
