﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("QuestionBankModel", Schema = "tMasterData")]
    public partial class QuestionBankModel
    {
        [Key]
        public Guid Id { get; set; }
        public int QuestionBankCode { get; set; }
        [Required]
        [StringLength(2000)]
        public string Question { get; set; }
        public string Answer { get; set; }
        public string AnswerC { get; set; }
        public string AnswerB { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        public Guid? QuestionCategoryId { get; set; }
        public Guid? DepartmentId { get; set; }

        [ForeignKey("CreateBy")]
        [InverseProperty("QuestionBankModelCreateByNavigation")]
        public virtual AccountModel CreateByNavigation { get; set; }
        [ForeignKey("DepartmentId")]
        [InverseProperty("QuestionBankModelDepartment")]
        public virtual CatalogModel Department { get; set; }
        [ForeignKey("LastEditBy")]
        [InverseProperty("QuestionBankModelLastEditByNavigation")]
        public virtual AccountModel LastEditByNavigation { get; set; }
        [ForeignKey("QuestionCategoryId")]
        [InverseProperty("QuestionBankModelQuestionCategory")]
        public virtual CatalogModel QuestionCategory { get; set; }
    }
}