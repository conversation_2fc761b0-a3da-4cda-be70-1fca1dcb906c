# Quality Control Product Report System Documentation

## Architecture Overview

The Quality Control Product Report system follows a layered architecture pattern with clear separation between frontend and backend components:

```
Frontend (Flutter/Dart)     |    Backend (.NET Core/C#)
┌─────────────────────────┐  |  ┌──────────────────────────┐
│  BaoCaoQCSanPhamDetail  │  |  │  QualityControlController │
│       (UI Layer)        │  |  │      (API Layer)         │
└─────────────────────────┘  |  └──────────────────────────┘
           │                 |             │
┌─────────────────────────┐  |  ┌──────────────────────────┐
│  qualityControlFunction │  |  │  QualityControlRepository │
│   (Business Logic)      │  |  │    (Data Access Layer)   │
└─────────────────────────┘  |  └──────────────────────────┘
           │                 |             │
┌─────────────────────────┐  |  ┌──────────────────────────┐
│   qualityControlApi     │  |  │      Database            │
│    (HTTP Client)        │  |  │   (Entity Framework)     │
└─────────────────────────┘  |  └──────────────────────────┘
```

## Core Features

### 1. Quality Control Product Inspection
- **LSX SAP Input**: Production order number input with auto-complete functionality
- **Multi-tab Interface**: Organized workflow across 4 main sections
- **Image Management**: Upload and manage inspection photos
- **Defect Tracking**: Record and categorize product defects
- **Personnel Accountability**: Track individuals responsible for defects

### 2. Data Management
- **Real-time Validation**: Client-side form validation
- **Offline Support**: Local data caching capabilities
- **File Upload**: Multi-part form data for images and documents
- **State Management**: Complex form state handling

### 3. Workflow Management
- **Step-by-step Process**: Guided QC inspection workflow
- **Role-based Access**: Different views for different user roles
- **Approval Chain**: Quality checker assignment and validation

## Execution Flow

### Frontend Flow

```mermaid
graph TD
    A[User Opens BaoCaoQCSanPhamDetail] --> B[Initialize State]
    B --> C[Load Master Data]
    C --> D[User Inputs LSX SAP]
    D --> E[Fetch QC Header Data]
    E --> F[Display Form Tabs]
    F --> G[User Fills Inspection Data]
    G --> H[Add Images/Files]
    H --> I[Submit Data]
    I --> J[Validate & Send to API]
    J --> K[Show Result]
```

#### Detailed Frontend Execution:

1. **Initialization Phase**
   ```dart
   @override
   void initState() {
     super.initState();
     if (!isTokenLive(widget.dateTimeOld)) {
       setState(() { _timeOut = true; });
       return;
     }
     _loadDataAndSetDefault();
   }
   ```

2. **Data Loading Phase**
   ```dart
   Future<void> _loadDataAndSetDefault() async {
     await _getQualitityControl();
     if (_qualityControl == null) return;
     updateValues();
   }
   ```

3. **API Call Phase**
   ```dart
   final listData = await Future.wait([
     QualityControlFunction.fetchQualityControlSanPham(
       widget.qualityControlId, 
       widget.user.token.toString(), 
       widget.qrCode
     ),
     QualityControlFunction.fetchGetDefectLevelApi(
       widget.user.token.toString()
     )
   ]);
   ```

### Backend Flow

```mermaid
graph TD
    A[HTTP Request] --> B[QualityControlController]
    B --> C[Authentication & Authorization]
    C --> D[Input Validation]
    D --> E[QualityControlRepository]
    E --> F[Database Query/Operation]
    F --> G[Data Mapping]
    G --> H[Business Logic Processing]
    H --> I[Response Formation]
    I --> J[HTTP Response]
```

#### Detailed Backend Execution:

1. **API Endpoint Reception**
   ```csharp
   [HttpGet("QualityControlQCSanPham")]
   public async Task<IActionResult> GetQualityControlQCSanPham(
       Guid? QualityControlId, 
       string barcode, 
       Guid? QualityControlDetailId)
   ```

2. **Repository Layer Processing**
   ```csharp
   public QualityControlViewModel GetById2(
       Guid? Id, 
       string StepCode = "", 
       string barcode = "", 
       Guid? IdDetail = null)
   ```

3. **Database Operations**
   ```csharp
   var data = (from qc in _context.QualityControlModel
              join prd in _context.ProductModel on qc.ProductCode equals prd.ProductCode
              // Complex LINQ queries for data retrieval
              select new QualityControlViewModel { ... });
   ```

## Data Processing

### Frontend Data Processing

#### 1. Form State Management
```dart
class _BaoCaoQCSanPhamDetailState extends State<BaoCaoQCSanPhamDetail> {
  // State variables
  QualityControlDetail? _qualityControlDetail;
  QualityControlModel? _qualityControlModel;
  List<QualityControlInformation> _lsThongTinKiemTra = [];
  List<Error> _lsError = [];
  
  // Controllers for form inputs
  List<TextEditingController> _lsControllerThongTinKiemTra = [];
  List<TextEditingController> _lsControllerSoLuongLoi = [];
}
```

#### 2. Data Validation
```dart
void _checkValidate() {
  setState(() {
    _errorTestMethodDetail = _selectedTestMethod == null || 
                           _selectedTestMethod!.catalogCode == " ";
    _errorQuantityCheckDetail = _controllerInspectQuantityDetail.text.isEmpty;
    _errorResultCheckDetail = _selectedResultDetail == null || 
                             _selectedResultDetail!.catalogCode == " ";
    
    for (int i = 0; i < _lsSelectedThongTinKiemTra.length; i++) {
      _lsErrorInfor[i] = _lsSelectedThongTinKiemTra[i] == null;
    }
  });
}
```

#### 3. File Upload Processing
```dart
void _pickFileImage(File file) {
  if (!mounted) return;
  setState(() {
    _lsFileHeader.add(file);
  });
}
```

### Backend Data Processing

#### 1. Data Models and ViewModels
```csharp
public class QualityControlViewModel
{
    public Guid? QualityControlId { get; set; }
    public string ProductCode { get; set; }
    public DateTime? QualityDate { get; set; }
    public List<QualityControlInformationMappingViewModel> QualityControlInformation { get; set; }
    public List<QualityControlErrorViewModel> Error { get; set; }
    public List<FileAttachmentViewModel> FileViewModel { get; set; }
}
```

#### 2. Data Mapping and Transformation
```csharp
private QualityControlViewModel GetDataById(Guid? Id, string StepCode)
{
    var qualityControl = _context.QualityControlModel
        .Where(x => x.QualityControlId == Id)
        .Select(x => new QualityControlViewModel
        {
            QualityControlId = x.QualityControlId,
            ProductCode = x.ProductCode,
            QualityDate = x.QualityDate,
            QualityControlInformation = GetQualityControlInformation(),
            Error = GetErrors(),
            FileViewModel = GetFileAttachments()
        }).FirstOrDefault();
    
    return qualityControl;
}
```

#### 3. File Processing
```csharp
private FileAttachmentModel SaveFileAttachment(Guid ObjectId, IFormFile item, string folder)
{
    var fileExtension = Path.GetExtension(item.FileName);
    var fileName = $"{Guid.NewGuid()}{fileExtension}";
    var filePath = Path.Combine(_uploadPath, folder, fileName);
    
    using (var stream = new FileStream(filePath, FileMode.Create))
    {
        item.CopyTo(stream);
    }
    
    return new FileAttachmentModel
    {
        ObjectId = ObjectId,
        FileName = fileName,
        FilePath = filePath
    };
}
```

## API Endpoints

### Primary Endpoints

1. **GET `/api/v1/MES/QualityControl/QualityControlQCSanPham`**
   - Purpose: Retrieve QC product data
   - Parameters: `QualityControlId`, `barcode`, `QualityControlDetailId`
   - Response: `QualityControlViewModel`

2. **GET `/api/v1/MES/QualityControl/GetQCSanPhamInfoByLSX`**
   - Purpose: Get QC product info by production order
   - Parameters: `lsxSAP`, `barcode`
   - Response: `QualityControlModel`

3. **POST `/api/v1/MES/QualityControl/QualityControlSanPham`**
   - Purpose: Submit QC inspection data
   - Body: `QualityControlPostViewModel` (multipart form)
   - Response: Success/Error status

### Supporting Endpoints

4. **GET `/api/v1/MES/QualityControl/CongDoanInfoBTP`**
   - Purpose: Get production step information
   - Parameters: `PO`, `StepCode`

5. **GET `/api/v1/MES/QualityControl/GetListLSQSAP`**
   - Purpose: Get production order suggestions
   - Parameters: `q` (search query)

## Error Handling

### Frontend Error Handling
```dart
try {
  final data = await QualityControlFunction.fetchQualityControlSanPham(/*...*/);
  // Process data
} on SocketException catch (_) {
  setState(() {
    _isNotWifi = true;
    _isLoading = false;
  });
} catch (error) {
  setState(() {
    _isError = true;
    _isLoading = false;
  });
}
```

### Backend Error Handling
```csharp
try
{
    var result = await repository.ProcessQualityControl(viewModel);
    return Ok(new { success = true, data = result });
}
catch (ValidationException ex)
{
    return BadRequest(new { success = false, message = ex.Message });
}
catch (Exception ex)
{
    return StatusCode(500, new { success = false, message = "Internal server error" });
}
```

## Security Features

1. **Authentication**: Token-based authentication with expiration checks
2. **Authorization**: Role-based access control via `[ISDWebAuthorization]`
3. **Input Validation**: Comprehensive validation on both client and server
4. **File Upload Security**: File type and size restrictions
5. **SQL Injection Prevention**: Entity Framework parameterized queries

## Performance Considerations

1. **Lazy Loading**: Data loaded on-demand
2. **Caching**: Master data caching on frontend
3. **Async Operations**: Non-blocking operations throughout
4. **Pagination**: Large datasets handled with pagination
5. **Image Compression**: Configurable image quality settings

This system provides a robust, scalable solution for quality control product inspection with comprehensive error handling, security measures, and performance optimizations.