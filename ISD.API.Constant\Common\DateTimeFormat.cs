﻿namespace ISD.API.Constant.Common
{
    public class DateTimeFormat
    {
        public const string DateTimeIsoString = "yyyy-MM-dd'T'HH:mm:ss.fffZ";
        public const string DateTimeLocalString = "yyyy-MM-dd HH:mm:ss.fff";
        public const string DateString = "dd/MM/yyyy";
        public const string DateFormatString = "yyyy-MM-dd";
        public const string TimeString = "HH:mm";
        public const string DateTimeString = "yyyy-MM-dd'T'HH:mm:ss";
        public const string DateTimeFormatString = "dd-MM-yyyy";
        public const string DateTimeHourMinuteSecondString = "dd-MM-yyyy-hh-mm-ss";
        public const string DateKey = "yyyyMMdd";
    }
}
