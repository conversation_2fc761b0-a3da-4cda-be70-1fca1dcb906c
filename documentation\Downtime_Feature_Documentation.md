# Downtime Feature Documentation

## Metadata
- **Feature Name**: Manufacturing Downtime Management
- **Project**: iMES_API (Manufacturing Execution System)
- **Version**: 1.0
- **API Version**: v1.0
- **Database Schema**: MES
- **Primary Purpose**: Track and manage equipment downtime events in manufacturing processes
- **Authentication**: Requires ISDWebAuthorization

## Core Concepts
```yaml
DowntimeEvent:
  definition: "A period when manufacturing equipment or process is not operational"
  database_schema: "MES.DowntimeModel"
  indexes:
    - "IX_DowntimeModel_DepartmentCode"
    - "IX_DowntimeModel_StepCode"
  relationships:
    - "One-to-Many with DowntimeHistoryModel"
  key_features:
    - "Department and Step-based tracking"
    - "Time period management"
    - "Responsible team assignment"
    - "Verification workflow"
    - "Multi-company support"
```

## Component Architecture
### 1. Controller Component
```yaml
name: DowntimeController
path: iMES_API/Areas/MES/Controllers/DowntimeController.cs
base_class: ControllerBaseAPI
route: "api/v{version:apiVersion}/MES/[controller]"
api_group: "mobile"
dependencies:
  - "IMapper (AutoMapper)"
  - "IDowntimeRepository"
  - "ILogger<DowntimeController>"
endpoints:
  get_list:
    path: "POST /DowntimeList"
    accepts: "DowntimeSearchModel"
    returns: "ApiResponse<List<DowntimeModel>>"
    filters:
      - "CompanyCode"
      - "DepartmentCode"
      - "StepCode"
      - "Reason"
      - "DateRange"
  get_by_id:
    path: "GET /{id}"
    accepts: "string id, string companyCode"
    returns: "ApiResponse<DowntimeViewModel>"
  create:
    path: "POST"
    accepts: "DowntimePostViewModel"
    returns: "ApiResponse<DowntimeViewModel>"
  update:
    path: "PUT /{id}"
    accepts: "string id, DowntimePostViewModel"
    returns: "ApiResponse<DowntimeViewModel>"
error_handling:
  - "Detailed exception logging"
  - "Standardized ApiResponse format"
  - "HTTP status code mapping"
```

### 2. Model Component
```yaml
name: DowntimeModel
path: ISD.API.EntityModels/Models/DowntimeModel.cs
type: "Entity Framework Core Model"
key_field: "DowntimeId (Guid)"
required_fields:
  - "Date: string(10)"
  - "DepartmentCode: string(50)"
  - "StepCode: string(50)"
  - "StartTime: string(8)"
  - "Reason: string(500)"
  - "ResponsibleTeam: string(100)"
optional_fields:
  - "EndTime: string(8)"
  - "ResponsibleDepartment: string(100)"
  - "PersonCausedDowntimeCodeMany: string(500)"
  - "Note: string(500)"
  - "Status: string(20)"
  - "VerificationStatus: string(20)"
  - "CompanyCode: string(10)"
  - "TDS: string(10)"
audit_fields:
  - "CreatedDate: DateTime"
  - "CreateBy: Guid?"
  - "UpdatedDate: DateTime?"
  - "UpdateBy: Guid?"
navigation_properties:
  - "DowntimeHistoryModel: ICollection<DowntimeHistoryModel>"
```

### 3. Repository Component
```yaml
name: DowntimeRepository
path: ISD.API.Repositories/MES/DowntimeRepository.cs
interface: IDowntimeRepository
dependencies:
  - "EntityDataContext"
  - "ILogger<DowntimeRepository>"
  - "IMapper"
key_operations:
  get_list:
    method: "GetDowntimeListAsync"
    features:
      - "Company code filtering"
      - "Department code filtering"
      - "Step code filtering"
      - "Reason text search"
      - "Date range filtering"
      - "Joins with DepartmentModel and RoutingModel"
      - "Ordered by CreatedDate descending"
  get_by_id:
    method: "GetDowntimeByIdAsync"
    features:
      - "Company-specific lookup"
      - "Includes related department and step data"
  create:
    method: "CreateDowntimeAsync"
    features:
      - "User tracking"
      - "Timestamp recording"
      - "Data validation"
  update:
    method: "UpdateDowntimeAsync"
    features:
      - "Concurrent update handling"
      - "Audit trail maintenance"
error_handling:
  - "Null context validation"
  - "Database connection verification"
  - "Detailed error logging"
```

## Data Flow and Validation
### Request Flow
```mermaid
sequenceDiagram
    Client->>+DowntimeController: HTTP Request with Auth
    DowntimeController->>DowntimeController: Validate Request
    DowntimeController->>+DowntimeRepository: Process Request
    DowntimeRepository->>+EntityDataContext: Query/Update
    EntityDataContext->>-DowntimeRepository: Data/Result
    DowntimeRepository->>-DowntimeController: Mapped ViewModel
    DowntimeController->>-Client: ApiResponse
```

### Validation Rules
```yaml
business_rules:
  date_validation:
    - "Date must be in valid format (length 10)"
    - "EndTime must be after StartTime"
  code_validation:
    - "DepartmentCode must exist in DepartmentModel"
    - "StepCode must exist in RoutingModel"
  data_integrity:
    - "CompanyCode-based data isolation"
    - "User-based audit trailing"
    - "Status workflow validation"
```

### Error Handling
```yaml
error_types:
  validation_error:
    code: 400
    response: "ApiResponse with validation details"
    logging: "Warning level with validation context"
  not_found:
    code: 404
    response: "ApiResponse with 'Không tìm thấy dữ liệu'"
    logging: "Information level with search parameters"
  server_error:
    code: 500
    response: "ApiResponse with error message"
    logging: "Error level with full exception details"
```

## Integration Points
```yaml
internal_dependencies:
  models:
    - "DepartmentModel: Department information"
    - "RoutingModel: Step information"
    - "DowntimeHistoryModel: Change history"
  services:
    - "AutoMapper: Object mapping"
    - "EntityFramework: Data access"
    - "ISDWebAuthorization: Security"
external_integrations:
  - "Mobile API consumers"
  - "MES reporting systems"
  - "Production planning systems"
```

## Performance Considerations
```yaml
optimizations:
  database:
    - "Indexed DepartmentCode and StepCode"
    - "Efficient joins with Department and Routing"
    - "Filtered company-specific queries"
  caching:
    - "Master data caching potential"
    - "Department and Step lookups"
  query:
    - "Async operations throughout"
    - "Efficient LINQ queries"
    - "Selective loading of related data"
```

## Schema Version
```yaml
version: "1.0"
last_updated: "2025-02-07"
maintainers:
  - "MES Development Team"
database_schema: "MES"
api_version: "v1.0"
compatibility:
  - "EntityFrameworkCore"
  - "ASP.NET Core"

## Mobile Implementation (Flutter)

## Mobile Architecture Overview
```yaml
platform: Flutter
state_management: StatefulWidget
network_layer: HTTP with Repository Pattern
ui_framework: Material Design
responsive_design: flutter_screenutil
```

## Component Structure
### 1. Views
```yaml
DowntimeList:
  path: lib/page/Downtime/DowntimeList.dart
  type: StatefulWidget
  features:
    - "Paginated list of downtime records"
    - "Pull-to-refresh functionality"
    - "Offline detection"
    - "Search and filtering"
  dependencies:
    - "connectivity_plus: Network status"
    - "intl: Date formatting"
    - "flutter_screenutil: Responsive UI"
  state_management:
    - "_isLoading: Loading state"
    - "_isNotWifi: Network state"
    - "_isError: Error state"
    - "_records: List<DowntimeRecord>"
    - "_searchModel: DowntimeSearchModel"

DowntimeDetail:
  path: lib/page/Downtime/DowntimeDetail.dart
  type: StatefulWidget
  features:
    - "Create/Edit/View downtime records"
    - "Form validation"
    - "Date/Time picking"
    - "Employee selection"
  form_fields:
    - "date: Date picker (dd-MM-yyyy)"
    - "department: AutoComplete"
    - "workstation: AutoComplete"
    - "startTime: Time picker (hh:mm AM/PM)"
    - "endTime: Time picker (hh:mm AM/PM)"
    - "reason: TextField"
    - "responsibleTeam: TextField"
    - "responsibleDepartment: TextField"
    - "note: TextField"
    - "tds: TextField"
  validation:
    - "Required fields validation"
    - "Date/Time range validation"
    - "Department code validation"
```

### 2. Repository Layer
```yaml
DowntimeFunction:
  path: lib/repository/function/downtimeFunction.dart
  type: Static Utility Class
  environment_support:
    - "PRD: Production environment"
    - "QAS: QA environment"
  api_endpoints:
    base: "/api/v1/MES/"
    endpoints:
      - "GetDowntimeList: GET /Downtime/DowntimeList"
      - "GetDowntimeById: GET /Downtime/{id}"
      - "CreateDowntime: POST /Downtime"
      - "UpdateDowntime: PUT /Downtime/{id}"
      - "GetListDepartment: GET /Downtime/GetListDepartment"
      - "GetListStepCode: GET /Downtime/GetListStepCode"
      - "GetEmployees: GET /Downtime/GetEmployees"
  error_handling:
    - "Network error detection"
    - "HTTP status code handling"
    - "JSON parsing error handling"
  features:
    - "Environment-based URL switching"
    - "Token-based authentication"
    - "Response caching"
```

### 3. Models
```yaml
DowntimeModel:
  path: lib/model/downtimeModel.dart
  properties:
    - "data: List<DowntimeRecord>"
    - "message: String"
    - "status: bool"
  serialization:
    - "JSON serialization/deserialization"
    - "Null safety support"

DowntimeRecord:
  properties:
    - "id: String"
    - "date: String (yyyy-MM-dd)"
    - "departmentCode: String"
    - "departmentName: String"
    - "stepCode: String"
    - "stepName: String"
    - "startTime: String (HH:mm)"
    - "endTime: String (HH:mm)"
    - "reason: String"
    - "responsibleTeam: String"
    - "responsibleDepartment: String"
    - "personCausedDowntimeCodeMany: String"
    - "note: String"
    - "status: String"
    - "verificationStatus: String"
    - "history: List<DowntimeHistory>"
```

## Data Flow
```mermaid
sequenceDiagram
    View->>+DowntimeFunction: API Request
    DowntimeFunction->>+HTTP Client: HTTP Request with Token
    HTTP Client->>-DowntimeFunction: JSON Response
    DowntimeFunction->>-View: Parsed Model
    View->>View: Update UI State
```

## Network Handling
```yaml
connectivity_checks:
  - "Initial connection check"
  - "Continuous connection monitoring"
  - "Offline mode detection"
error_handling:
  network_error:
    - "Show LostConnect screen"
    - "Auto-retry mechanism"
  api_error:
    - "Error message display"
    - "Form validation feedback"
```

## UI/UX Features
```yaml
responsive_design:
  - "ScreenUtil adaptation"
  - "Dynamic font sizing"
  - "Flexible layouts"
form_features:
  - "Keyboard visibility handling"
  - "Auto-complete suggestions"
  - "Date/Time pickers"
  - "Form validation"
loading_states:
  - "Initial loading indicator"
  - "Save operation indicator"
  - "Refresh indicator"
```

## Integration Points
```yaml
backend_integration:
  - "Token-based authentication"
  - "API version compatibility"
  - "Data format consistency"
local_storage:
  - "User preferences"
  - "Authentication tokens"
  - "Environment settings"
```

## Error Handling
```yaml
error_types:
  network:
    handling: "Show LostConnect screen"
    recovery: "Auto-retry on connection restore"
  validation:
    handling: "Form field error messages"
    recovery: "Real-time validation"
  api:
    handling: "Error message display"
    recovery: "Retry mechanism"
```

## Performance Optimizations
```yaml
caching:
  - "API response caching"
  - "Master data caching"
  - "Image caching"
lazy_loading:
  - "Paginated list loading"
  - "On-demand data fetching"
memory_management:
  - "Dispose controllers"
  - "Clear caches when needed"
```

## Version Information
```yaml
version: "1.0"
last_updated: "2025-02-07"
flutter_version: ">=2.12.0"
dart_version: ">=2.12.0"
dependencies:
  - "flutter_screenutil: ^5.0.0"
  - "http: ^0.13.0"
  - "intl: ^0.17.0"
  - "connectivity_plus: ^2.0.0"

## Technical Implementation Details

### 1. API Endpoints and Models
```yaml
api_version: "v1.0"
base_url: "/api/v1/MES/Downtime"
response_format:
  type: "ApiResponse<T>"
  structure:
    - "Code: int (HTTP status code)"
    - "IsSuccess: bool"
    - "Message: string?"
    - "Data: T?"

endpoints:
  get_list:
    method: "POST"
    path: "/DowntimeList"
    request_model:
      type: "DowntimeSearchModel"
      fields:
        - "companyCode: string"
        - "pageNumber: int = 1"
        - "pageSize: int = 20"
        - "fromDate: DateTime?"
        - "toDate: DateTime?"
    response: "ApiResponse<List<DowntimeRecord>>"
    error_codes:
      - "400: Invalid search parameters"
      - "401: Unauthorized access"
      - "500: Server processing error"

  get_by_id:
    method: "GET"
    path: "/{id}"
    parameters:
      - "id: string (required)"
      - "companyCode: string (optional)"
    response: "ApiResponse<DowntimeViewModel>"
    error_codes:
      - "404: Record not found"
      - "401: Unauthorized access"

  create:
    method: "POST"
    path: "/"
    request_model: "DowntimePostViewModel"
    response: "ApiResponse<DowntimeViewModel>"
    validation:
      - "Required fields must not be null"
      - "Date format: yyyy-MM-dd"
      - "Time format: HH:mm"

  update:
    method: "PUT"
    path: "/{id}"
    request_model: "DowntimePostViewModel"
    response: "ApiResponse<DowntimeViewModel>"
    validation:
      - "Record must exist"
      - "Status transitions must be valid"
```

### 2. Mobile Implementation Details
```yaml
state_management:
  pattern: "StatefulWidget with Local State"
  key_states:
    loading:
      type: "bool"
      usage: "Controls loading overlay"
      initialization: "true in initState"
    
    network:
      type: "ConnectivityResult"
      monitoring: "connectivity_plus package"
      handling: "Shows LostConnect widget when offline"
    
    records:
      type: "List<DowntimeRecord>?"
      update_triggers:
        - "Initial load"
        - "Pull-to-refresh"
        - "Search/Filter"
        - "Create/Update operations"

form_management:
  controllers:
    - "dateController: TextEditingController"
    - "departmentController: TextEditingController"
    - "workstationController: TextEditingController"
    - "startTimeController: TextEditingController"
    - "endTimeController: TextEditingController"
    - "reasonController: TextEditingController"
    - "responsibleTeamController: TextEditingController"
    - "noteController: TextEditingController"
  
  validation:
    implementation: "Form widget with GlobalKey<FormState>"
    rules:
      date:
        - "Required field"
        - "Format: dd-MM-yyyy"
        - "Must be valid date"
      time:
        - "Required field"
        - "Format: hh:mm AM/PM"
        - "End time > Start time"
      department:
        - "Required field"
        - "Must exist in master data"
      reason:
        - "Required field"
        - "Max length: 500"

  disposal:
    - "All controllers disposed in dispose()"
    - "Listeners removed"
    - "Timer cancellations"
```

### 3. Data Flow and State Management
```yaml
initialization_flow:
  1_check_permissions:
    method: "_timeOutSession"
    checks:
      - "Screen code permission"
      - "Session validity"
      - "Network status"
  
  2_load_master_data:
    method: "_initializeData"
    operations:
      - "Load default filters"
      - "Initialize search model"
      - "Fetch initial data"
  
  3_setup_listeners:
    connectivity:
      package: "connectivity_plus"
      events: "onConnectivityChanged"
    
    keyboard:
      package: "flutter_keyboard_visibility"
      events: "onVisibilityChanged"

data_refresh_flow:
  triggers:
    manual:
      - "Pull-to-refresh gesture"
      - "Retry button click"
    automatic:
      - "Network reconnection"
      - "Return to screen"
      - "Session renewal"
  
  implementation:
    - "Cancel existing requests"
    - "Show loading indicator"
    - "Execute API call"
    - "Update UI state"
    - "Handle errors"
```

### 4. Error Handling and Recovery
```yaml
error_types:
  network_error:
    detection: "SocketException catch block"
    ui_state:
      - "_isNotWifi = true"
      - "_isLoading = false"
    recovery:
      - "Auto-retry on network restore"
      - "Manual retry button"
  
  api_error:
    detection: "Non-200 HTTP response"
    handling:
      400:
        - "Show validation errors"
        - "Highlight invalid fields"
      401:
        - "Clear session"
        - "Redirect to login"
      404:
        - "Show 'Record not found'"
        - "Option to create new"
      500:
        - "Show error message"
        - "Log error details"
        - "Offer retry option"

  state_error:
    detection: "try-catch in setState"
    handling:
      - "Reset to safe state"
      - "Log error context"
      - "Show error view"
```

### 5. Performance Optimizations
```yaml
memory_management:
  disposal:
    - "Controller disposal in dispose()"
    - "Stream subscription cancellation"
    - "Timer cancellation"
  
  caching:
    master_data:
      - "Department list"
      - "Step codes"
      - "Employee data"
    implementation:
      - "In-memory cache during session"
      - "Periodic refresh (30 minutes)"

lazy_loading:
  list_view:
    - "20 items per page"
    - "Load more on scroll"
    - "Cache previous pages"
  
  images:
    - "Cached network image"
    - "Placeholder during load"
    - "Error fallback image"

debouncing:
  search:
    - "500ms delay"
    - "Cancel previous requests"
  
  api_calls:
    - "Prevent duplicate submissions"
    - "Rate limiting"
```

### 6. Security Implementation
```yaml
authentication:
  token_management:
    storage: "SecureStorage"
    refresh: "Auto-refresh near expiry"
    invalidation: "Clear on logout/error"
  
  session_tracking:
    timeout: "1 day"
    renewal: "On active use"
    validation: "Every API call"

data_protection:
  sensitive_data:
    - "User credentials"
    - "Company codes"
    - "Authentication tokens"
  storage:
    type: "Flutter Secure Storage"
    encryption: "Platform-specific encryption"
```

### 7. Localization and Formatting
```yaml
date_time_formatting:
  display_formats:
    date: "dd-MM-yyyy"
    time: "hh:mm a"
    timestamp: "yyyy-MM-dd'T'HH:mm:ss"
  parsing:
    - "Handle various input formats"
    - "Timezone consideration"
    - "Invalid date handling"

text_formatting:
  numbers:
    - "Decimal separator: comma"
    - "Thousand separator: dot"
  status:
    - "Enum to display text mapping"
    - "Color coding"
```

### 8. Testing Guidelines
```yaml
unit_tests:
  coverage:
    - "Model serialization"
    - "Validation logic"
    - "Date formatting"
    - "Permission checks"
  
  widget_tests:
    - "Form validation"
    - "Error states"
    - "Loading states"
    - "User interactions"

integration_tests:
  scenarios:
    - "Complete CRUD flow"
    - "Offline handling"
    - "Session timeout"
    - "Permission changes"
```

## Navigation and Integration

### Route Configuration
```yaml
name: RouteGenerator
routes:
  downtime_list:
    path: "/Downtime"
    settings_name: "/Downtime"
    arguments:
      type: CommonScreenUserArgument
      properties:
        - "dateTimeOld: String"
        - "user: DataUser"
    returns: "MaterialPageRoute<DowntimeList>"

  downtime_detail:
    path: "/DowntimeDetail"
    settings_name: "/DowntimeDetail"
    arguments:
      type: "Map<String, dynamic>"
      properties:
        - "id: String"
        - "dateTimeOld: String"
        - "user: DataUser"
    returns: "MaterialPageRoute<DowntimeDetail>"
```

### Main Page Integration
```yaml
MainPage:
  path: lib/page/Mainpage.dart
  type: StatefulWidget
  permission_check:
    field: "_checkViewDowntimePermission"
    code: "Downtime"
    validation: "mobileScreenModel?.firstWhereOrNull((element) => element.screenCode == 'Downtime') != null"

  menu_integration:
    type: "Navigation Button"
    conditions:
      - "checkViewDowntimePermission == true"
    properties:
      text: "Downtime"
      route: "/Downtime"
      icon: "Icons.timer_outlined"
      arguments:
        type: "CommonScreenUserArgument"
        data:
          - "id: null"
          - "dateTimeOld: String"
          - "qrCode: null"
          - "fromPage: null"
          - "user: DataUser"
```

### Navigation Flow
```mermaid
graph TD
    A[Main Page] -->|Permission Check| B{Has Downtime Permission?}
    B -->|Yes| C[Show Downtime Button]
    B -->|No| D[Hide Downtime Button]
    C -->|Tap| E[Downtime List]
    E -->|Select Item| F[Downtime Detail]
    E -->|Add New| G[Downtime Detail]
```

### Permission Structure
```yaml
permission_model:
  screen_code: "Downtime"
  validation:
    type: "Boolean Check"
    location: "mobileScreenModel"
    field: "screenCode"
  implementation:
    - "Checked during MainPage initialization"
    - "Controls visibility of Downtime menu item"
    - "Persists through user session"

menu_button:
  visibility_control:
    - "Controlled by _checkViewDowntimePermission"
    - "Dynamically updated with permission changes"
  styling:
    - "Consistent with other menu items"
    - "Uses standard icon and layout"
```

### Screen Arguments
```yaml
CommonScreenUserArgument:
  usage: "Passing data between screens"
  required_fields:
    - "dateTimeOld: Current session timestamp"
    - "user: Logged in user data"
  optional_fields:
    - "id: Record identifier"
    - "qrCode: QR code data"
    - "fromPage: Navigation source"

DetailScreenArgument:
  usage: "Passing data to detail screen"
  required_fields:
    - "id: Record identifier"
    - "dateTimeOld: Current session timestamp"
    - "user: User data for authentication"
```

### State Management
```yaml
session_management:
  timestamp_tracking:
    - "dateTimeOld: Tracks session validity"
    - "Stored in SharedPreferences"
    - "Used for session timeout"
  
  permission_state:
    - "Cached during initialization"
    - "Updated on permission changes"
    - "Controls feature accessibility"

navigation_state:
  route_history:
    - "Maintained by Navigator"
    - "Supports back navigation"
    - "Handles deep linking"
```

### Integration Points
```yaml
dependencies:
  ui_components:
    - "ButtonMainPage: Custom button widget"
    - "TimeOutView: Session timeout handler"
    - "ErrorView: Error state display"
  
  storage:
    - "StorageSharedPreferences: Local data persistence"
    - "SecureStorage: Secure data storage"
  
  utilities:
    - "DateFormat: Date/time formatting"
    - "ScreenUtil: Responsive sizing"
```

### Error Handling
```yaml
error_scenarios:
  permission_error:
    handling: "Hide menu item"
    fallback: "Show error view"
  
  navigation_error:
    handling: "Show error message"
    recovery: "Return to main menu"
  
  session_timeout:
    handling: "Show timeout view"
    recovery: "Redirect to login"

```
