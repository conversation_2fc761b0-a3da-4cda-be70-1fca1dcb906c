class GetStatusReservation {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetStatusReservation>? data;


  GetStatusReservation(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetStatusReservation.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetStatusReservation>[];
      json['data'].forEach((v) {
        data!.add(DataGetStatusReservation.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetStatusReservation {
  String? key;
  String? value;

  DataGetStatusReservation({this.key, this.value});

  DataGetStatusReservation.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}