# Power BI Integration Guide - MaiDao API

## Overview

This step-by-step guide shows you how to connect Power BI to your TTF iMES MaiDao (Knife Grinding) API for real-time operational dashboards and analytics.

## What You'll Build

By the end of this guide, you'll have:
- **Real-time MaiDao data** in Power BI
- **Equipment performance dashboards**
- **Maintenance tracking reports**
- **Operational analytics**

## Prerequisites

1. ✅ **API Key**: MaiDao external API key with `MaiDao.Read` scope
2. ✅ **Power BI Desktop** or **Power BI Service** access
3. ✅ **API Base URL**: Your server URL (e.g., `https://your-server.com`)

## Step 1: Get Your API Key

**Admin Task** - Contact your system administrator to create an API key:

```http
POST /api/v1/Permission/ApiKeyManagement/Create
Authorization: Bearer {admin-jwt-token}

{
  "keyName": "Power BI MaiDao Integration",
  "description": "MaiDao data access for Power BI dashboards",
  "companyCode": "1000",
  "allowedScopes": "MaiDao.Read",
  "rateLimitPerHour": 3000
}
```

**Result**: You'll receive an API key like `ttf_AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCd`

⚠️ **Important**: Store this key securely - it's shown only once!

## Step 2: Test Your API Connection

Before setting up Power BI, test your API access:

```bash
curl -X GET "https://your-server.com/api/v1/External/MaiDaoExternal/MaiDao?pageSize=5" \
  -H "X-API-Key: ttf_your_api_key_here"
```

**Expected Response**:
```json
{
  "isSuccess": true,
  "code": 200,
  "message": "Success",
  "data": {
    "items": [...],
    "pagination": {...}
  }
}
```

## Step 3: Connect Power BI to MaiDao API

### Method 1: Simple Connection (Power BI Desktop)

1. **Open Power BI Desktop**
2. **Get Data** → **Web**
3. **URL**: `https://your-server.com/api/v1/External/MaiDaoExternal/MaiDao?pageSize=100`
4. **Advanced Options**:
   - **HTTP request header parameters**:
     - Name: `X-API-Key`
     - Value: `ttf_your_api_key_here`
5. **OK** → **Transform Data**

### Method 2: Advanced Power Query (Recommended)

1. **Get Data** → **Blank Query**
2. **Advanced Editor** → Paste this code:

```m
let
    // Configuration
    ApiKey = "ttf_your_api_key_here",
    BaseUrl = "https://your-server.com",
    
    // Headers
    Headers = [
        #"X-API-Key" = ApiKey,
        #"Content-Type" = "application/json"
    ],
    
    // Function to get a single page of data
    GetPage = (pageIndex as number) =>
        let
            Url = BaseUrl & "/api/v1/External/MaiDaoExternal/MaiDao?pageIndex=" & Number.ToText(pageIndex) & "&pageSize=100",
            Response = Json.Document(Web.Contents(Url, [Headers=Headers])),
            Data = Response[data],
            Items = Data[items],
            HasNextPage = Data[pagination][hasNextPage]
        in
            [Items = Items, HasNextPage = HasNextPage],
    
    // Function to get all pages recursively
    GetAllPages = () =>
        let
            GetPagesRecursive = (pageIndex as number, accumulator as list) =>
                let
                    CurrentPage = GetPage(pageIndex),
                    NewAccumulator = List.Combine({accumulator, CurrentPage[Items]})
                in
                    if CurrentPage[HasNextPage] then
                        @GetPagesRecursive(pageIndex + 1, NewAccumulator)
                    else
                        NewAccumulator
        in
            GetPagesRecursive(1, {}),
    
    // Get all data and convert to table
    AllData = GetAllPages(),
    #"Converted to Table" = Table.FromList(AllData, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    
    // Expand all columns
    #"Expanded Data" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"maiDaoId", "date", "equipmentCode", "equipmentName", 
         "materialCode", "materialName", "materialBatch", "operationType", 
         "employeeCodes", "employeeNames", "requestingEmployeeCode", 
         "requestingEmployeeName", "note", "status", "createdDate", 
         "updatedDate", "companyCode"}, 
        {"MaiDao ID", "Date", "Equipment Code", "Equipment Name", 
         "Material Code", "Material Name", "Material Batch", "Operation Type", 
         "Employee Codes", "Employee Names", "Requesting Employee Code", 
         "Requesting Employee Name", "Note", "Status", "Created Date", 
         "Updated Date", "Company Code"}),
    
    // Data type conversions
    #"Changed Type" = Table.TransformColumnTypes(#"Expanded Data",{
        {"Created Date", type datetime}, 
        {"Updated Date", type datetime},
        {"Date", type text}
    }),
    
    // Add calculated columns
    #"Added Custom Columns" = 
        let
            WithYear = Table.AddColumn(#"Changed Type", "Year", each Date.Year([Created Date]), Int64.Type),
            WithMonth = Table.AddColumn(WithYear, "Month", each Date.Month([Created Date]), Int64.Type),
            WithQuarter = Table.AddColumn(WithMonth, "Quarter", each Date.QuarterOfYear([Created Date]), Int64.Type),
            WithWeek = Table.AddColumn(WithQuarter, "Week", each Date.WeekOfYear([Created Date]), Int64.Type),
            WithDayOfWeek = Table.AddColumn(WithWeek, "Day of Week", each Date.DayOfWeek([Created Date]), Int64.Type)
        in
            WithDayOfWeek
in
    #"Added Custom Columns"
```

3. **Rename** the query to "MaiDao Operations"
4. **Close & Apply**

## Step 4: Create Additional Data Sources

### Equipment List Query

Create a new query for equipment reference data:

```m
let
    ApiKey = "ttf_your_api_key_here",
    BaseUrl = "https://your-server.com",
    Headers = [#"X-API-Key" = ApiKey],
    
    Source = Json.Document(Web.Contents(
        BaseUrl & "/api/v1/External/MaiDaoExternal/Equipment",
        [Headers=Headers]
    )),
    
    data = Source[data],
    #"Converted to Table" = Table.FromList(data, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Column" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"equipmentCode", "equipmentName"}, 
        {"Equipment Code", "Equipment Name"})
in
    #"Expanded Column"
```

### Materials List Query

```m
let
    ApiKey = "ttf_your_api_key_here",
    BaseUrl = "https://your-server.com",
    Headers = [#"X-API-Key" = ApiKey],
    
    Source = Json.Document(Web.Contents(
        BaseUrl & "/api/v1/External/MaiDaoExternal/Materials",
        [Headers=Headers]
    )),
    
    data = Source[data],
    #"Converted to Table" = Table.FromList(data, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    #"Expanded Column" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"materialCode", "materialName"}, 
        {"Material Code", "Material Name"})
in
    #"Expanded Column"
```

### Statistics Query (for KPIs)

```m
let
    ApiKey = "ttf_your_api_key_here",
    BaseUrl = "https://your-server.com",
    Headers = [#"X-API-Key" = ApiKey],
    
    // Get last 30 days statistics
    FromDate = Date.ToText(Date.AddDays(DateTime.Date(DateTime.LocalNow()), -30), "yyyy-MM-dd"),
    ToDate = Date.ToText(DateTime.Date(DateTime.LocalNow()), "yyyy-MM-dd"),
    
    Source = Json.Document(Web.Contents(
        BaseUrl & "/api/v1/External/MaiDaoExternal/Statistics?fromDate=" & FromDate & "&toDate=" & ToDate,
        [Headers=Headers]
    )),
    
    data = Source[data],
    
    // Create a table with key metrics
    MetricsTable = #table(
        {"Metric", "Value"},
        {
            {"Total Records", data[totalRecords]},
            {"Average Per Day", data[summary][averagePerDay]},
            {"Most Common Operation", data[summary][mostCommonOperationType]},
            {"Most Active Equipment", data[summary][mostActiveEquipment]}
        }
    )
in
    MetricsTable
```

## Step 5: Create Data Model Relationships

1. **Model View** → Create relationships:
   - **MaiDao Operations[Equipment Code]** ↔ **Equipment[Equipment Code]**
   - **MaiDao Operations[Material Code]** ↔ **Materials[Material Code]**

2. **Mark tables** as:
   - **MaiDao Operations**: Fact table
   - **Equipment**: Dimension table
   - **Materials**: Dimension table

## Step 6: Create Calculated Measures

Add these measures to your **MaiDao Operations** table:

### Basic Metrics

```dax
// Total Operations
Total Operations = COUNT('MaiDao Operations'[MaiDao ID])

// Completed Operations
Completed Operations = 
CALCULATE(
    COUNT('MaiDao Operations'[MaiDao ID]),
    'MaiDao Operations'[Status] = "Completed"
)

// Completion Rate
Completion Rate = 
DIVIDE(
    [Completed Operations],
    [Total Operations],
    0
) * 100

// Operations This Month
Operations This Month = 
CALCULATE(
    [Total Operations],
    DATESMTD('MaiDao Operations'[Created Date])
)

// Operations Last Month
Operations Last Month = 
CALCULATE(
    [Total Operations],
    DATEADD('MaiDao Operations'[Created Date], -1, MONTH)
)

// Month over Month Growth
MoM Growth % = 
DIVIDE(
    [Operations This Month] - [Operations Last Month],
    [Operations Last Month],
    0
) * 100
```

### Equipment Performance

```dax
// Equipment Utilization
Equipment Utilization = 
DIVIDE(
    COUNT('MaiDao Operations'[Equipment Code]),
    DISTINCTCOUNT('Equipment'[Equipment Code]),
    0
)

// Average Operations per Equipment
Avg Operations per Equipment = 
DIVIDE(
    [Total Operations],
    DISTINCTCOUNT('MaiDao Operations'[Equipment Code]),
    0
)

// Most Active Equipment
Most Active Equipment = 
CALCULATE(
    VALUES('MaiDao Operations'[Equipment Code]),
    TOPN(1, VALUES('MaiDao Operations'[Equipment Code]), [Total Operations], DESC)
)
```

### Time-based Analytics

```dax
// Operations by Operation Type
Operations by Type = 
CALCULATE(
    [Total Operations],
    ALLEXCEPT('MaiDao Operations', 'MaiDao Operations'[Operation Type])
)

// YTD Operations
YTD Operations = 
CALCULATE(
    [Total Operations],
    DATESYTD('MaiDao Operations'[Created Date])
)

// Same Period Last Year
SPLY Operations = 
CALCULATE(
    [Total Operations],
    SAMEPERIODLASTYEAR('MaiDao Operations'[Created Date])
)

// Year over Year Growth
YoY Growth % = 
DIVIDE(
    [YTD Operations] - [SPLY Operations],
    [SPLY Operations],
    0
) * 100
```

### Advanced Analytics

```dax
// Average Days Between Operations (per equipment)
Avg Days Between Operations = 
AVERAGEX(
    VALUES('MaiDao Operations'[Equipment Code]),
    VAR CurrentEquipment = 'MaiDao Operations'[Equipment Code]
    VAR EquipmentOperations = 
        CALCULATETABLE(
            'MaiDao Operations',
            'MaiDao Operations'[Equipment Code] = CurrentEquipment
        )
    VAR SortedDates = 
        ADDCOLUMNS(
            EquipmentOperations,
            "PreviousDate", 
            CALCULATE(
                MAX('MaiDao Operations'[Created Date]),
                'MaiDao Operations'[Created Date] < EARLIER('MaiDao Operations'[Created Date])
            )
        )
    RETURN
        AVERAGEX(
            FILTER(SortedDates, NOT ISBLANK([PreviousDate])),
            DATEDIFF([PreviousDate], [Created Date], DAY)
        )
)

// Maintenance Frequency Score
Maintenance Frequency Score = 
VAR TotalDays = DATEDIFF(MIN('MaiDao Operations'[Created Date]), MAX('MaiDao Operations'[Created Date]), DAY)
VAR OperationsPerDay = DIVIDE([Total Operations], TotalDays, 0)
RETURN
SWITCH(
    TRUE(),
    OperationsPerDay >= 2, "High",
    OperationsPerDay >= 1, "Medium",
    OperationsPerDay >= 0.5, "Low",
    "Very Low"
)
```

## Step 7: Build Your Dashboard

### Page 1: Executive Summary

**Visuals to create:**

1. **Cards** (KPIs):
   - Total Operations (This Month)
   - Completion Rate
   - Most Active Equipment
   - MoM Growth %

2. **Line Chart**: Operations trend over time
   - X-axis: Created Date (Month)
   - Y-axis: Total Operations
   - Line: Operations This Month

3. **Donut Chart**: Operations by Type
   - Values: Total Operations
   - Legend: Operation Type

4. **Bar Chart**: Top 10 Equipment by Operations
   - Y-axis: Equipment Name
   - X-axis: Total Operations

### Page 2: Equipment Performance

**Visuals to create:**

1. **Matrix**: Equipment Performance Grid
   - Rows: Equipment Name
   - Values: Total Operations, Completion Rate, Avg Days Between Operations

2. **Heat Map**: Equipment Activity by Month
   - Rows: Equipment Name
   - Columns: Month
   - Values: Total Operations

3. **Scatter Chart**: Equipment Utilization Analysis
   - X-axis: Total Operations
   - Y-axis: Completion Rate
   - Details: Equipment Name

4. **Table**: Recent Operations (Top 20)
   - Columns: Date, Equipment Name, Operation Type, Status, Employee Names

### Page 3: Operational Analytics

**Visuals to create:**

1. **Column Chart**: Operations by Status
   - X-axis: Status
   - Y-axis: Total Operations

2. **Area Chart**: Cumulative Operations
   - X-axis: Created Date
   - Y-axis: Running Total Operations

3. **Tree Map**: Operations by Material
   - Group: Material Name
   - Values: Total Operations

4. **Funnel**: Operation Process Flow
   - Values: Created, In Progress, Completed operations

## Step 8: Add Filters and Slicers

**Recommended slicers:**

1. **Date Range Slicer**: Created Date (Between dates)
2. **Equipment Slicer**: Equipment Name (Multi-select)
3. **Operation Type Slicer**: Operation Type (Multi-select)
4. **Status Slicer**: Status (Multi-select)
5. **Year Slicer**: Year (Single select)

## Step 9: Set Up Automatic Refresh

### Power BI Desktop (Manual)
- **Home** → **Refresh** (manual refresh)

### Power BI Service (Automatic)

1. **Publish** your report to Power BI Service
2. Go to **Settings** → **Dataset** → **Scheduled Refresh**
3. **Configure refresh frequency**:
   - **Recommended**: Every 2-4 hours for MaiDao data
   - **Maximum**: 8 times per day (Pro license)

4. **Data source credentials**:
   - Add your API key to the credential store

### Gateway Setup (On-premises API)

If your API is on-premises:

1. **Install Power BI Gateway** on a server with API access
2. **Configure data source** with API key authentication
3. **Set up scheduled refresh** through gateway

## Step 10: Performance Optimization

### Query Optimization

```m
// Add this to your main query for better performance
#"Filtered Recent Data" = Table.SelectRows(#"Changed Type", 
    each [Created Date] >= Date.AddDays(DateTime.Date(DateTime.LocalNow()), -90))
```

### Incremental Refresh (Power BI Pro/Premium)

1. **Power BI Desktop** → **Manage Parameters**
2. **Create parameters**:
   - `RangeStart` (DateTime)
   - `RangeEnd` (DateTime)

3. **Update your query**:
```m
// Add date filtering with parameters
#"Filtered by Date Range" = Table.SelectRows(#"Changed Type", 
    each [Created Date] >= RangeStart and [Created Date] < RangeEnd)
```

4. **Configure incremental refresh**:
   - **Archive data**: 2 years
   - **Refresh data**: 10 days
   - **Detect data changes**: ✓

## Step 11: Mobile Optimization

### Configure for Mobile

1. **View** → **Mobile Layout**
2. **Resize visuals** for mobile screens
3. **Prioritize key metrics** for mobile view

## Sample Power Query Template

Here's a complete, ready-to-use Power Query for MaiDao data:

```m
let
    // ===== CONFIGURATION SECTION =====
    ApiKey = "ttf_your_api_key_here",  // Replace with your actual API key
    BaseUrl = "https://your-server.com",  // Replace with your server URL
    DaysToLoad = 90,  // How many days of historical data to load
    
    // ===== HEADERS =====
    Headers = [
        #"X-API-Key" = ApiKey,
        #"Content-Type" = "application/json"
    ],
    
    // ===== DATE FILTERING =====
    FromDate = Date.ToText(Date.AddDays(DateTime.Date(DateTime.LocalNow()), -DaysToLoad), "yyyy-MM-dd"),
    
    // ===== PAGINATION FUNCTION =====
    GetPage = (pageIndex as number) =>
        let
            Url = BaseUrl & "/api/v1/External/MaiDaoExternal/MaiDao?pageIndex=" & Number.ToText(pageIndex) & "&pageSize=100&fromDate=" & FromDate,
            Response = try Json.Document(Web.Contents(Url, [Headers=Headers])) otherwise null,
            IsValidResponse = Response <> null and Record.HasFields(Response, "data"),
            Data = if IsValidResponse then Response[data] else [items = {}, pagination = [hasNextPage = false]],
            Items = Data[items],
            HasNextPage = Data[pagination][hasNextPage]
        in
            [Items = Items, HasNextPage = HasNextPage, IsValid = IsValidResponse],
    
    // ===== GET ALL DATA =====
    GetAllPages = () =>
        let
            GetPagesRecursive = (pageIndex as number, accumulator as list) =>
                let
                    CurrentPage = GetPage(pageIndex)
                in
                    if not CurrentPage[IsValid] then
                        accumulator
                    else
                        let
                            NewAccumulator = List.Combine({accumulator, CurrentPage[Items]})
                        in
                            if CurrentPage[HasNextPage] and pageIndex < 50 then  // Safety limit
                                @GetPagesRecursive(pageIndex + 1, NewAccumulator)
                            else
                                NewAccumulator
        in
            GetPagesRecursive(1, {}),
    
    // ===== DATA TRANSFORMATION =====
    AllData = GetAllPages(),
    #"Converted to Table" = Table.FromList(AllData, Splitter.SplitByNothing(), null, null, ExtraValues.Error),
    
    #"Expanded Data" = Table.ExpandRecordColumn(#"Converted to Table", "Column1", 
        {"maiDaoId", "date", "equipmentCode", "equipmentName", 
         "materialCode", "materialName", "materialBatch", "operationType", 
         "employeeCodes", "employeeNames", "requestingEmployeeCode", 
         "requestingEmployeeName", "note", "status", "createdDate", 
         "updatedDate", "companyCode"}, 
        {"MaiDao ID", "Date", "Equipment Code", "Equipment Name", 
         "Material Code", "Material Name", "Material Batch", "Operation Type", 
         "Employee Codes", "Employee Names", "Requesting Employee Code", 
         "Requesting Employee Name", "Note", "Status", "Created Date", 
         "Updated Date", "Company Code"}),
    
    // ===== DATA TYPE CONVERSION =====
    #"Changed Type" = Table.TransformColumnTypes(#"Expanded Data",{
        {"Created Date", type datetime}, 
        {"Updated Date", type datetime},
        {"Date", type text}
    }),
    
    // ===== CLEAN DATA =====
    #"Cleaned Data" = 
        let
            WithoutNulls = Table.SelectRows(#"Changed Type", each [MaiDao ID] <> null),
            TrimmedText = Table.TransformColumns(WithoutNulls, {
                {"Equipment Name", Text.Trim},
                {"Material Name", Text.Trim},
                {"Operation Type", Text.Trim},
                {"Status", Text.Trim}
            })
        in
            TrimmedText,
    
    // ===== ADD CALCULATED COLUMNS =====
    #"Added Calculated Columns" = 
        let
            WithYear = Table.AddColumn(#"Cleaned Data", "Year", each Date.Year([Created Date]), Int64.Type),
            WithMonth = Table.AddColumn(WithYear, "Month", each Date.Month([Created Date]), Int64.Type),
            WithMonthName = Table.AddColumn(WithMonth, "Month Name", each Date.MonthName([Created Date]), type text),
            WithQuarter = Table.AddColumn(WithMonthName, "Quarter", each "Q" & Text.From(Date.QuarterOfYear([Created Date])), type text),
            WithWeek = Table.AddColumn(WithQuarter, "Week", each Date.WeekOfYear([Created Date]), Int64.Type),
            WithDayOfWeek = Table.AddColumn(WithWeek, "Day of Week", each Date.DayOfWeekName([Created Date]), type text),
            WithIsCompleted = Table.AddColumn(WithDayOfWeek, "Is Completed", each [Status] = "Completed", type logical),
            WithDaysFromCreation = Table.AddColumn(WithIsCompleted, "Days Since Creation", 
                each Duration.Days(DateTime.LocalNow() - [Created Date]), Int64.Type)
        in
            WithDaysFromCreation
in
    #"Added Calculated Columns"
```

## Troubleshooting

### Common Issues

1. **"Couldn't authenticate"**
   - ✅ Check API key format
   - ✅ Verify API key has `MaiDao.Read` scope
   - ✅ Ensure API key is active

2. **"No data returned"**
   - ✅ Check date filters (may be filtering out all data)
   - ✅ Verify company has MaiDao records
   - ✅ Test API directly with curl/Postman

3. **"Query timeout"**
   - ✅ Reduce `DaysToLoad` parameter
   - ✅ Add pagination limits
   - ✅ Use incremental refresh

4. **"Rate limit exceeded"**
   - ✅ Reduce refresh frequency
   - ✅ Request higher rate limits
   - ✅ Optimize queries

## Best Practices

### 📊 **Dashboard Design**
- Start with high-level KPIs
- Use consistent color schemes
- Keep it simple - avoid overcrowding
- Add context with comparisons (MoM, YoY)

### 🔄 **Data Refresh**
- Schedule during off-peak hours
- Test refresh performance
- Monitor for failures
- Use incremental refresh for large datasets

### 🚀 **Performance**
- Limit historical data (90-180 days)
- Use query folding when possible
- Optimize DAX measures
- Enable result caching

### 🔐 **Security**
- Store API keys securely
- Use service accounts for automation
- Monitor API usage
- Rotate keys regularly

## Support

Need help? Contact:
- **Technical Support**: <EMAIL>
- **Power BI Help**: Check Microsoft Power BI documentation
- **API Documentation**: Review MaiDao Integration Guide

---

**Congratulations!** 🎉 You now have a complete Power BI integration consuming real-time MaiDao data from your external API!