import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/warehouseTranfer.dart';
import '../../urlApi/urlApi.dart';

class ExportWareHouseApi {
  static Future<http.Response> getBatch(String rawMaterialCardId, String token) async {
    Map<String, dynamic> data = {"RawMaterialCardId": rawMaterialCardId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetBatchNumberByPallet", data);
    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getReservation(String materialReservationId, String token) async {
    Map<String, dynamic> data = {"MaterialReservationId": materialReservationId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}/${UrlApi.baseUrlCommon}GetReservation?MaterialReservationId=${Uri.encodeQueryComponent(data['MaterialReservationId'].toString())}';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getReservationItems(String materialReservationId, String token) async {
    Map<String, dynamic> data = {"MaterialReservationId": materialReservationId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}/${UrlApi.baseUrlCommon}GetReservationItems?MaterialReservationId=${Uri.encodeQueryComponent(data['MaterialReservationId'].toString())}';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postWarehouseTranfer(WarehouseTranfer warehouseTranfer, String token) async {
    final dataPost = jsonEncode(warehouseTranfer);
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "WarehouseTranfer");
    if (kDebugMode) {
      print(dataPost);
    }
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> postWarehouseTranfer2(WarehouseTranfer warehouseTranfer, String token) async {
    final dataPost = jsonEncode(warehouseTranfer);
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "WarehouseTranfer2");
    if (kDebugMode) {
      print(dataPost);
    }
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
