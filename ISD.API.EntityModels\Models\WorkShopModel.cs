﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WorkShopModel", Schema = "MES")]
    public partial class WorkShopModel
    {
        [Key]
        public Guid WorkShopId { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? StoreId { get; set; }
        [StringLength(50)]
        public string WorkShopCode { get; set; }
        [StringLength(200)]
        public string WorkShopName { get; set; }
        [StringLength(200)]
        public string WorkShopNameUnsigned { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        [StringLength(100)]
        public string CreatedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedTime { get; set; }
        [StringLength(100)]
        public string LastModifiedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedTime { get; set; }
        [StringLength(50)]
        public string QuanDocCode { get; set; }
        [StringLength(50)]
        public string QAQCCode { get; set; }
    }
}