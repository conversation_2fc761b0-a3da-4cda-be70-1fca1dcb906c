class GetInventorySAP {
  int? code;
  bool? isSuccess;
  String? message;
  Data? data;

  GetInventorySAP({this.code, this.isSuccess, this.message, this.data});

  GetInventorySAP.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;

  Data({this.so, this.soLine, this.wbs, this.quantity, this.unit});

  Data.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantity = json['quantity'];
    unit = json['unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    return data;
  }
}
class PostInventorySAP {
  String? reservationId;
  String? productCode;
  String? plant;
  String? sloc;
  String? batchNumber;

  PostInventorySAP(
      {this.reservationId,
        this.productCode,
        this.plant,
        this.sloc,
        this.batchNumber});

  PostInventorySAP.fromJson(Map<String, dynamic> json) {
    reservationId = json['reservationId'];
    productCode = json['productCode'];
    plant = json['plant'];
    sloc = json['sloc'];
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reservationId'] = reservationId;
    data['productCode'] = productCode;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['batchNumber'] = batchNumber;
    return data;
  }
}