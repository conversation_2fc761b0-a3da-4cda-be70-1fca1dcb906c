*----------------------------------------------------------------------*
***INCLUDE ZPG_FI_EINV_PROPOSAL_I01.
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*&      Module  EXIT_COMMAND_0100  INPUT
*&---------------------------------------------------------------------*
*       Exit command processing
*----------------------------------------------------------------------*
MODULE EXIT_COMMAND_0100 INPUT.

  IF OK_CODE IS NOT INITIAL.
    GW_OK_CODE = OK_CODE.
    CLEAR OK_CODE.
  ENDIF.

* If data has changed by user
  IF GW_CHANGED_FLG IS NOT INITIAL.
*    PERFORM CALL_POPUP.
* If there is no change
  ELSE.
*    PERFORM REFRESH_DATA.
    LEAVE TO SCREEN 0.
  ENDIF.
ENDMODULE.                 " EXIT_COMMAND_0100  INPUT
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_0100  INPUT
*&---------------------------------------------------------------------*
*       User command
*----------------------------------------------------------------------*
MODULE USER_COMMAND_0100 INPUT.

  GO_ALV_H->CHECK_CHANGED_DATA( ).
  GO_ALV_D->CHECK_CHANGED_DATA( ).
  PERFORM user_command_0100.
  PERFORM REFRESH_ALV.
ENDMODULE.                 " USER_COMMAND_0100  INPUT