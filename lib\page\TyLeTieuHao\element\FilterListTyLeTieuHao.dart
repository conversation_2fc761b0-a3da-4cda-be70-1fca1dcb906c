import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/TyLeTieuHaoSearchModel.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';
import 'package:ttf/repository/function/listQcFunction.dart';
import 'package:ttf/element/RowTimeLsQC.dart';
import 'package:ttf/element/DropdownLsQC.dart';
import 'package:ttf/repository/showDateTime.dart';
import 'package:ttf/element/RowTextFieldLsQC.dart';
import 'package:ttf/repository/function/loginFunction.dart';
import 'package:ttf/utils/ui_utils.dart';

class FilterListTyLeTieuHao extends StatefulWidget {
  final TyLeTieuHaoSearchModel? searchModel;
  final Function(TyLeTieuHaoSearchModel) onFilterSelected;
  final String token;
  final DataUser user;

  const FilterListTyLeTieuHao({
    Key? key,
    this.searchModel,
    required this.onFilterSelected,
    required this.token,
    required this.user,
  }) : super(key: key);

  @override
  _FilterListTyLeTieuHaoState createState() => _FilterListTyLeTieuHaoState();
}

class _FilterListTyLeTieuHaoState extends State<FilterListTyLeTieuHao> {
  bool _isLoading = false;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _showErrorNoti = false;
  String _messageErrorNoti = "";

  String? _fromTime;
  String? _toTime;
  CommonDates? _selectedCommonDates;
  List<CommonDates>? _commonDates;
  CommonDateModel? _commonDateModel;

  final _focusCompany = TextEditingController();
  final _focusDepartment = TextEditingController();
  final _focusStep = TextEditingController();
  final _focusReason = TextEditingController();

  SalesOrgCodes? _selectedSalesOrgCodes;
  List<SalesOrgCodes>? _salesOrgCodes;

  List<String> _departmentSuggestions = [];
  List<String> _stepCodeSuggestions = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      // Load master data first
      await _loadMasterData();

      final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);

      if (!mounted) return;

      if (companyList != null) {
        setState(() {
          _salesOrgCodes = companyList.map((company) => SalesOrgCodes(saleOrgCode: company.companyCode, storeName: company.companyName)).toList();

          if (_salesOrgCodes!.isNotEmpty) {
            _selectedSalesOrgCodes = widget.searchModel?.companyCode != null
                ? _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.searchModel!.companyCode,
                    orElse: () => _salesOrgCodes!.firstWhere(
                      (element) => element.saleOrgCode == widget.user.companyCode,
                      orElse: () => _salesOrgCodes!.first,
                    ),
                  )
                : _salesOrgCodes!.firstWhere(
                    (element) => element.saleOrgCode == widget.user.companyCode,
                    orElse: () => _salesOrgCodes!.first,
                  );
          }
        });
      }

      final dataDropdown = await ListQCFunction.getDefaultQCMauFilter(widget.token);

      if (!mounted) return;

      if (dataDropdown != null) {
        setState(() {
          _commonDates = dataDropdown.data?.commonDates;

          // If we have existing search model with dates, try to match with a common date
          if (widget.searchModel?.fromDate != null && widget.searchModel?.toDate != null) {
            _fromTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.fromDate!);
            _toTime = DateFormat('yyyy-MM-dd').format(widget.searchModel!.toDate!);

            // Try to find matching common date by comparing date ranges
            _selectedCommonDates = UIUtils.findMatchingCommonDate(
              _commonDates,
              widget.searchModel!.fromDate!,
              widget.searchModel!.toDate!,
            );
          } else {
            _selectedCommonDates = _commonDates?.firstWhere(
              (element) => element.catalogCode == "ThisWeek",
              orElse: () => _commonDates!.first,
            );
            _getCommonDate(_selectedCommonDates);
          }
        });
      }

      // Initialize other filters if they exist
      if (widget.searchModel != null) {
        // _focusDepartment.text = widget.searchModel!.departmentCode ?? '';
        // _focusStep.text = widget.searchModel!.stepCode ?? '';
        // _focusReason.text = widget.searchModel!.reason ?? '';
      }
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
      });
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      setState(() {
        _isError = true;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMasterData() async {
    try {
      // // Load departments
      // final departmentsResponse = await DowntimeFunction.fetchDepartments(
      //   widget.token,
      //   widget.user.companyCode ?? '',
      // );

      // if (!mounted) return;

      // if (departmentsResponse?.departments != null) {
      //   setState(() {
      //     _departmentSuggestions.clear();
      //     _departmentSuggestions.addAll(departmentsResponse!.departments!
      //         .where((dept) => dept.departmentCode != null && dept.departmentName != null)
      //         .map((dept) => "${dept.departmentCode} | ${dept.departmentName}")
      //         .toList());
      //   });
      // }

      // // Load step codes
      // final stepsResponse = await DowntimeFunction.fetchStepCodes(
      //   widget.token,
      //   widget.user.companyCode ?? '',
      // );

      // if (!mounted) return;

      // if (stepsResponse?.steps != null) {
      //   setState(() {
      //     _stepCodeSuggestions.clear();
      //     _stepCodeSuggestions.addAll(stepsResponse!.steps!
      //         .where((step) => step.stepCode != null && step.stepName != null)
      //         .map((step) => "${step.stepCode} | ${step.stepName}")
      //         .toList());
      //   });
      // }
    } catch (error) {
      debugPrint('Error loading master data: $error');
      rethrow;
    }
  }

  Future<void> _getCommonDate(CommonDates? selectedCommonDates) async {
    if (!mounted || selectedCommonDates == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      if (selectedCommonDates.catalogCode == "Custom") {
        setState(() {
          _isLoading = false;
          _fromTime = null;
          _toTime = null;
        });
        return;
      }

      final getCommonDateModel = await ListQCFunction.getCommonDateModel(
        selectedCommonDates.catalogCode!,
        widget.token,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _commonDateModel = getCommonDateModel;
        if (getCommonDateModel != null) {
          _fromTime = getCommonDateModel.fromDate;
          _toTime = getCommonDateModel.toDate;
        }
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _showErrorNoti = true;
        _messageErrorNoti = "Không có kết nối mạng!";
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _messageErrorNoti = error.toString();
        _showErrorNoti = true;
      });
    }
  }

  void _initializeFilters() {
    if (widget.searchModel != null) {
      _focusCompany.text = widget.searchModel!.companyCode!;
      // _focusDepartment.text = widget.searchModel!.departmentCode ?? '';
      // _focusStep.text = widget.searchModel!.stepCode ?? '';
    }
  }

  void _applyFilter() {
    // Extract just the codes from the combined strings
    // final departmentCode = _focusDepartment.text.split(' | ').first;
    // final stepCode = _focusStep.text.isNotEmpty ? _focusStep.text.split(' | ').first : null;

    final filter = TyLeTieuHaoSearchModel(
      companyCode: _selectedSalesOrgCodes?.saleOrgCode ?? widget.user.companyCode ?? '',
      // materialCode: _focusMaterial.text.isEmpty ? null : _focusMaterial.text,
      fromDate: _fromTime != null ? DateTime.parse(_fromTime!) : null,
      toDate: _toTime != null ? DateTime.parse(_toTime!) : null,
      pageNumber: 1,
      pageSize: 20,
    );

    widget.onFilterSelected(filter);
    Navigator.pop(context);
  }

  Future<void> _setSelectedSalesOrgCodes(SalesOrgCodes? value) async {
    if (value == null) return;

    setState(() {
      _selectedSalesOrgCodes = value;
      // Clear existing values since they're for a different company
      _focusDepartment.clear();
      _focusStep.clear();
      _departmentSuggestions.clear();
      _stepCodeSuggestions.clear();
    });

    // Move the await outside of setState
    try {
      if (!mounted) return;

      // Fetch common dates after updating sales org codes
      await _getCommonDate(_selectedCommonDates);
    } catch (error) {
      debugPrint('Error loading master data after company change: $error');
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi khi tải dữ liệu cho nhà máy mới";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => Future.value(false),
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 40.h),
          child: Drawer(
            backgroundColor: Colors.white,
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
                        color: const Color(0xff0052cc),
                        child: Text(
                          "Tìm kiếm Tỷ lệ tiêu hao",
                          style: TextStyle(fontSize: 15.sp, color: Colors.white),
                        ),
                      ),
                      SizedBox(height: 10.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            DropdownSalesOrgCodes(
                              title: 'Nhà máy',
                              onTap: _setSelectedSalesOrgCodes,
                              lsSalesOrgCodes: _salesOrgCodes,
                              selectedSalesOrgCodes: _selectedSalesOrgCodes,
                            ),
                            SizedBox(height: 10.h),
                            _buildDateSelection(),
                            SizedBox(height: 100.h),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (_showErrorNoti)
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.red.shade900),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 9,
                          child: Text(
                            _messageErrorNoti,
                            style: TextStyle(fontSize: 12.sp, color: Colors.white),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: IconButton(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () {
                              setState(() {
                                _showErrorNoti = false;
                              });
                            },
                            icon: const Icon(Icons.cancel),
                            iconSize: 15.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white)),
                          ),
                          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                        ),
                        onPressed: _applyFilter,
                        icon: Icon(Icons.search, size: 23.sp, color: Colors.white),
                        label: Text(
                          "Tìm kiếm",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      children: [
        DropdownCommonDates(
          title: 'Thời gian',
          onTap: (value) async {
            setState(() {
              _selectedCommonDates = value;
            });
            if (value?.catalogCode != "Custom") {
              await _getCommonDate(value);
            } else {
              setState(() {
                _fromTime = null;
                _toTime = null;
              });
            }
          },
          lsWorkCommonDates: _commonDates,
          selectedCommonDates: _selectedCommonDates,
        ),
        SizedBox(height: 10.h),
        if (_selectedCommonDates?.catalogCode == "Custom") ...[
          ColumnDateLsQC(
            title: 'Từ ngày',
            date: () => Platform.isAndroid ? _pickDateFromComplete(context) : _pickDateIOSFormComplete(context),
            displayDate: _fromTime == null ? "mm/dd/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!)),
          ),
          SizedBox(height: 10.h),
          ColumnDateLsQC(
            title: 'Đến ngày',
            date: () => Platform.isAndroid ? _pickDateToComplete(context) : _pickDateIOSToComplete(context),
            displayDate: _toTime == null ? "mm/dd/yyyy" : DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!)),
          ),
        ] else ...[
          RowTimeLsQC(
            title: 'Từ ngày',
            time: _isLoading
                ? "Loading..."
                : _fromTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_fromTime!))
                    : "",
          ),
          SizedBox(height: 10.h),
          RowTimeLsQC(
            title: 'Đến ngày',
            time: _isLoading
                ? "Loading..."
                : _toTime != null
                    ? DateFormat("dd/MM/yyyy").format(DateFormat("yyyy-MM-dd").parse(_toTime!))
                    : "",
          ),
        ],
      ],
    );
  }

  // Add date picker methods similar to FillterListKCS
  Future<void> _pickDateFromComplete(BuildContext context) async {
    try {
      DateTime initialDate = _fromTime != null ? DateTime.parse(_fromTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        // If toTime exists, validate the date range
        if (_toTime != null) {
          final endDate = DateTime.parse(_toTime!);
          if (picked.isAfter(endDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày bắt đầu không thể sau ngày kết thúc";
            });
            return;
          }
        }

        setState(() {
          _fromTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateToComplete(BuildContext context) async {
    try {
      DateTime initialDate = _toTime != null ? DateTime.parse(_toTime!) : DateTime.now();

      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(2000),
        lastDate: DateTime(2101),
      );

      if (picked != null) {
        // If fromTime exists, validate the date range
        if (_fromTime != null) {
          final startDate = DateTime.parse(_fromTime!);
          if (picked.isBefore(startDate)) {
            setState(() {
              _showErrorNoti = true;
              _messageErrorNoti = "Ngày kết thúc không thể trước ngày bắt đầu";
            });
            return;
          }
        }

        setState(() {
          _toTime = DateFormat("yyyy-MM-dd").format(picked);
        });
      }
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateIOSFormComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _fromTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  Future<void> _pickDateIOSToComplete(BuildContext context) async {
    try {
      final newDate = await ShowDateTime.showDatePickerIOSToComplete(context);
      if (newDate == null) return;
      setState(() {
        _toTime = DateFormat("yyyy-MM-dd").format(newDate);
      });
    } catch (error) {
      setState(() {
        _showErrorNoti = true;
        _messageErrorNoti = "Lỗi chọn ngày";
      });
    }
  }

  @override
  void dispose() {
    _focusCompany.dispose();
    _focusDepartment.dispose();
    _focusStep.dispose();
    _focusReason.dispose();
    super.dispose();
  }
}
