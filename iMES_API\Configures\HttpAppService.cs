﻿using Microsoft.Extensions.DependencyInjection;
using System;

namespace iMES_API.Configures
{
    public class HttpAppService
    {
        private static IServiceProvider _serviceProvider;

        /// <summary>
        /// Get Application Collection Service
        /// </summary>
        /// <param name="serviceProvider"></param>
        public static void Configure(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public static IServiceProvider ServiceProvider => _serviceProvider;

        public static TService GetRequestService<TService>()
        {
            return HttpAppContext.Current.RequestServices.GetService<TService>();
        }
    }
}
