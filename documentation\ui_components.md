# UI Components

The TTF MES Mobile application uses a consistent set of UI components to provide a cohesive user experience. This document outlines the key UI components, their usage patterns, and implementation details.

## Layout Components

### App Structure

The application follows a consistent layout structure:

- **AppBar**: Contains title, navigation buttons, and actions
- **Body**: Main content area
- **Bottom Navigation**: Access to main sections (when applicable)
- **Floating Action Button**: For primary actions

### Screen Templates

Common screen templates include:

1. **List View Template**:
   - Search/filter bar
   - Sorting options
   - List of items with consistent styling
   - Pull-to-refresh functionality
   - Pagination controls

2. **Detail View Template**:
   - Hierarchical information display
   - Form sections with consistent styling
   - Action buttons at consistent locations
   - Navigation options

3. **Form Template**:
   - Input fields with labels
   - Validation indicators
   - Submit/cancel buttons
   - Consistent spacing and alignment

4. **Dashboard Template**:
   - Summary cards
   - Quick action buttons
   - Status indicators
   - Navigation to detailed views

## Form Elements

The application uses a consistent set of form elements found primarily in `lib/element/` directory:

### Text Inputs

- **Regular Text Input**: Standard text input with labels
- **Multiline Text Input**: For longer text entry
- **Numeric Input**: With appropriate keyboard and formatting
- **Date/Time Input**: With date picker integration
- **Autocomplete Input**: For typeahead functionality

### Selection Inputs

- **Dropdown Select**: For selection from fixed options
- **Radio Buttons**: For mutually exclusive options
- **Checkboxes**: For multiple selections
- **Switches**: For toggling settings on/off

### Custom Inputs

- **QR Code Input**: Integration with QR code scanner
- **Image Upload**: For capturing and uploading photos
- **Signature Capture**: For digital signatures
- **Quantity Selector**: With increment/decrement controls

## Custom Widgets

The application includes custom widgets for specific functionality, found in `lib/Widget/` directory:

### Containers

In `lib/Widget/container/`:
- **CardContainer**: Styled container for information display
- **InfoContainer**: For displaying read-only information
- **StatusContainer**: For displaying status with appropriate colors

### Dialogs

In `lib/Widget/dialogWidget/`:
- **AlertDialog**: For important notifications
- **ConfirmDialog**: For user confirmations
- **ProgressDialog**: For showing operation progress
- **ErrorDialog**: For displaying errors

### Modal Bottom Sheets

In `lib/Widget/modalBottomSheet/`:
- **FilterSheet**: For filtering options
- **ActionSheet**: For presenting contextual actions
- **DetailSheet**: For showing additional details
- **SelectionSheet**: For making selections

### Error Handling

- **ErrorWidget**: Custom widget for error display
- **EmptyStateWidget**: Displayed when no data is available
- **LoadingScreen**: Consistent loading indicator

### Data Display

- **DataTable**: For tabular data display
- **StatusBadge**: Visual indicator of item status
- **ProgressIndicator**: Custom progress visualization
- **Charts and Graphs**: For data visualization

## Styling Guidelines

The application follows consistent styling guidelines:

### Colors

- **Primary Color**: `Color(0xff303F9F)` (deep blue)
- **Secondary Color**: Black for text and accents
- **Background Color**: White for most screens
- **Status Colors**:
  - Success: Green
  - Warning: Amber/Yellow
  - Error: Red
  - Info: Light Blue
  - Disabled: Gray

### Typography

- **Text Styles**:
  - Headings: Bold, larger size
  - Body text: Regular weight
  - Captions: Smaller size, sometimes italic
  - Error text: Red color
- **Responsive Sizing**:
  - Using `flutter_screenutil` for responsive text sizing
  - Base design size: 360x690

### Spacing

- **Consistent Padding**:
  - Standard edge padding: 15 logical pixels
  - Between elements: 10-15 logical pixels
  - Within elements: 5-10 logical pixels
- **Alignment Rules**:
  - Form labels consistently aligned
  - Action buttons consistently positioned
  - Information hierarchically organized

### Responsiveness

The application implements a responsive design approach:

- **Screen Adaptation**:
  - Using `flutter_screenutil` for responsive sizing
  - Flexible layouts that adapt to different screen sizes
  - Proper handling of keyboard appearance

- **Orientation Support**:
  - Primary focus on portrait mode
  - Graceful handling of orientation changes

## Implementation Examples

### Basic ListTile Usage

```dart
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      child: ListTile(
        leading: Icon(Icons.description),
        title: Text(items[index].title, style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold)),
        subtitle: Text(items[index].description, style: TextStyle(fontSize: 12.sp)),
        trailing: StatusBadge(status: items[index].status),
        onTap: () => navigateToDetail(items[index]),
      ),
    );
  }
);
```

### Form Element Example

```dart
Column(
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    Text("Material Code", style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold)),
    SizedBox(height: 5.h),
    Container(
      height: inputHeight,
      child: TextField(
        controller: materialCodeController,
        decoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
          border: OutlineInputBorder(),
          errorText: validateMaterialCode(materialCodeController.text),
        ),
      ),
    ),
    SizedBox(height: 15.h),
  ],
);
```

### Button Style Example

```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    padding: EdgeInsets.symmetric(vertical: 2, horizontal: 15),
  ),
  onPressed: isValid ? submitForm : null,
  child: Text("Submit", style: TextStyle(fontSize: 14.sp)),
);
```

## Best Practices

When implementing UI in the TTF MES Mobile application:

1. **Follow Existing Patterns**:
   - Match the style of existing screens
   - Use established component patterns
   - Maintain consistent spacing and alignment

2. **Ensure Responsiveness**:
   - Use flutter_screenutil for sizing
   - Test on different screen sizes
   - Handle landscape mode gracefully

3. **Optimize for Factory Environment**:
   - Create touch-friendly tap targets
   - Ensure readability in various lighting
   - Design for potential glove usage

4. **Maintain Consistency**:
   - Use the defined color palette
   - Follow typography guidelines
   - Position actions in expected locations

5. **Error Handling**:
   - Provide clear error messages
   - Use consistent error styling
   - Offer recovery options for errors

6. **Accessibility**:
   - Ensure sufficient contrast
   - Provide adequate touch targets
   - Use semantic labels for screen readers 