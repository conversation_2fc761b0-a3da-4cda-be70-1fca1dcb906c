import 'dart:convert';
import '../../model/MaterialUsed.dart';
import '../api/ListMaterialUsed.dart';

class GetListMaterialUsedFunction{
  static Future<List<DataMaterialUsed>?> getListMaterialUsed(String token) async {
    final dataResponse = await ListMaterialUsedAPI.getListMaterialUsed(token);
    if (dataResponse.statusCode == 200) {
      final responseDataMaterialUsed = jsonDecode(dataResponse.body);
      if (responseDataMaterialUsed != false) {
        final lsMaterialUsed = ListMaterialUsedmd.fromJson(
            responseDataMaterialUsed);
        if (lsMaterialUsed.code == 200 && lsMaterialUsed.isSuccess == true) {
          return lsMaterialUsed.data;
        } else {
          return null;
        }
      }else{
        return null;
      }
    }
    else{
      return null;
    }
  }

}