﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.ViewModels;
using ISD.API.Repositories.Utility;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.SAPIntegration.Controllers
{
    [Route("api/v{version:apiVersion}/SAPIntegration/[controller]")]
    [ApiVersion("3.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    public class PurchaseOrderIntegrationController : ControllerBaseAPI
    {
        private readonly ISapTableService _sapTableService;

        public PurchaseOrderIntegrationController(ISapTableService sapTableService)
        {
            _sapTableService = sapTableService ?? throw new ArgumentNullException(nameof(sapTableService));
        }

        private int GetSAPRecordCount(string tableName, string condition)
        {
            try
            {
                var countTable = _sapTableService.FetchFromSAPTable(tableName, condition, new List<string> { "EBELN" }, 0, 0);
                return countTable?.Rows?.Count ?? 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error counting SAP records: {ex.Message}");
                return 0;
            }
        }
        /// <summary>API "Tích hợp thông tin PurchaseOrder" - Thêm / Cập nhật</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/SAPIntegration/PurchaseOrderIntegration/PurchaseOrder
        ///     Params: 
        ///             + version : 3
        ///             
        /// BODY
        ///  
        ///     {
        ///         "header": {
        ///             "purchaseOrderCode": "2100000999",
        ///             "companyCode": "1000",
        ///             "documentType": "ZPO1",
        ///             "deletionInd": "LOEKZ",
        ///             "vendorNumber": "LIFNR",
        ///             "purchasingOrg": "EKORG",
        ///         },
        ///         "listDetail": [
        ///             {
        ///                 "purchaseOrderCode": "2100000999",
        ///                 "poItem": 10
        ///                 "plant": ""
        ///                 "material": "",
        ///                 "deletionInd": "",
        ///                 "shortText": "",
        ///                 "storageLocation": "",
        ///                 "poQuantity": 0,
        ///                 "orderUnit": "",
        ///                 "delivCompl": "",
        ///                 "acctAssgmtCat": "",
        ///                 "itemCategory": "",
        ///                 "goodsReceipt": "",
        ///                 "sdDocument": "",
        ///                 "item": 0,
        ///                 "wbsElement": 0,
        ///                 "cumulativeQuantity": 12.000
        ///             }
        ///         ]
        ///     }
        ///     
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": "Tích hợp thành công PurchaseOrder",
        ///         "data": null
        ///     }
        ///</remarks>
        [HttpPost("PurchaseOrder")]
        public async Task<IActionResult> PurchaseOrderIntegrationAsync([FromBody] PurchaseOrderIntegrationViewModel IntegrationModel)
        {
            // Where PurchaseOrderCode
            var existPO = await _context.PurchaseOrderMasterModel.Where(x => x.PurchaseOrderCode == IntegrationModel.Header.PurchaseOrderCode).Include(x => x.PurchaseOrderDetailModel).FirstOrDefaultAsync();

            if (existPO != null)
            {

                // Get delivery dates from SAP tables
                Dictionary<string, string> deliveryDateDictionary = new Dictionary<string, string>();
                try
                {
                    // Get EKET data for delivery schedule lines
                    var eketTable = _sapTableService.FetchFromSAPTable("EKET",
                        $"EBELN = '{IntegrationModel.Header.PurchaseOrderCode}'",
                        new List<string> {
                            "EBELN", "EBELP", "ETENR", "EINDT", "MENGE"
                        });

                    // Process delivery dates if data exists
                    if (eketTable?.Rows?.Count > 0)
                    {
                        // Group by EBELP and get the first (earliest) delivery date for each item
                        var deliveryDatesByItem = eketTable.Rows
                            .Cast<System.Data.DataRow>()
                            .GroupBy(row => row["EBELP"]?.ToString())
                            .ToDictionary(
                                g => g.Key,
                                g => g.OrderBy(row => row["ETENR"]?.ToString())
                                      .FirstOrDefault()?["EINDT"]?.ToString()
                            );

                        deliveryDateDictionary = deliveryDatesByItem
                            .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but continue with the integration
                    // Handle SAP connection issues gracefully
                    Console.WriteLine($"Error fetching delivery dates from SAP: {ex.Message}");
                }

                // Update
                #region Master
                // DocumentType
                existPO.DocumentType = IntegrationModel.Header.DocumentType;
                // CompanyCode
                existPO.CompanyCode = IntegrationModel.Header.CompanyCode;
                //DeletionInd
                existPO.DeletionInd = IntegrationModel.Header.DeletionInd;
                //VendorNumber
                existPO.VendorNumber = IntegrationModel.Header.VendorNumber;
                // PurchasingOrg
                existPO.PurchasingOrg = IntegrationModel.Header.PurchasingOrg;

                //DocumentDate
                existPO.DocumentDate = IntegrationModel.Header.DocumentDate;

                existPO.LastEditTime = DateTime.Now;

                //Trạng thái duyệt
                existPO.ReleaseIndicator = IntegrationModel.Header.ReleaseIndicator;

                _context.Entry(existPO).State = EntityState.Modified;
                #endregion

                #region Detail

                foreach (var item in IntegrationModel.ListDetail)
                {
                    var detailPO = await _context.PurchaseOrderDetailModel.FirstOrDefaultAsync(x => x.PurchaseOrderCode == item.PurchaseOrderCode && x.POItem == item.POItem.ToString());
                    if (detailPO == null)
                    {
                        var addModel = new PurchaseOrderDetailModel();

                        addModel.PurchaseOrderDetailId = Guid.NewGuid();
                        addModel.PurchaseOrderId = existPO.PurchaseOrderId;

                        addModel.PurchaseOrderCode = item.PurchaseOrderCode;
                        addModel.POItem = item.POItem.ToString();
                        addModel.Plant = item.Plant;
                        addModel.Material = item.Material;
                        addModel.ProductCode = !string.IsNullOrEmpty(item.Material) ? item.Material.Replace("000000000", "") : null;
                        addModel.DeletionInd = item.DeletionInd;
                        addModel.ShortText = item.ShortText;
                        addModel.StorageLocation = item.StorageLocation;
                        addModel.POQuantity = item.POQuantity;
                        addModel.OrderUnit = item.OrderUnit;
                        addModel.DelivCompl = item.DelivCompl;
                        addModel.AcctAssgmtCat = item.AcctAssgmtCat;
                        addModel.ItemCategory = item.ItemCategory;
                        addModel.GoodsReceipt = item.GoodsReceipt;
                        addModel.SDDocument = item.SDDocument;
                        addModel.Item = item.Item;
                        addModel.WBSElement = item.WBSElement;
                        addModel.CumulativeQuantity = item.CumulativeQuantity.ToString();

                        addModel.POItemInt = item.POItem;
                        addModel.CumulativeQuantityInt = item.CumulativeQuantity;
                        addModel.BasicDataText = item.BasicDataText;
                        addModel.Sso = item.SSO;

                        // Set delivery date from SAP EKET.EINDT (format: YYYYMMDD)
                        if (deliveryDateDictionary.ContainsKey(item.POItem.ToString().PadLeft(5, '0')))
                        {
                            var dateString = deliveryDateDictionary[item.POItem.ToString().PadLeft(5, '0')];
                            if (DateTime.TryParseExact(dateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime parsedDate))
                            {
                                addModel.DeliveryDate = parsedDate;
                            }
                        }

                        _context.PurchaseOrderDetailModel.Add(addModel);
                    }
                    else
                    {
                        detailPO.Plant = item.Plant;
                        detailPO.Material = item.Material;
                        detailPO.ProductCode = !string.IsNullOrEmpty(item.Material) ? item.Material.Replace("000000000", "") : null;
                        detailPO.DeletionInd = item.DeletionInd;
                        detailPO.ShortText = item.ShortText;
                        detailPO.StorageLocation = item.StorageLocation;
                        detailPO.POQuantity = item.POQuantity;
                        detailPO.OrderUnit = item.OrderUnit;
                        detailPO.DelivCompl = item.DelivCompl;
                        detailPO.AcctAssgmtCat = item.AcctAssgmtCat;
                        detailPO.ItemCategory = item.ItemCategory;
                        detailPO.GoodsReceipt = item.GoodsReceipt;
                        detailPO.SDDocument = item.SDDocument;
                        detailPO.Item = item.Item;
                        detailPO.WBSElement = item.WBSElement;
                        detailPO.CumulativeQuantity = item.CumulativeQuantity.ToString();
                        detailPO.BasicDataText = item.BasicDataText;
                        detailPO.Sso = item.SSO;

                        detailPO.POItemInt = item.POItem;
                        detailPO.CumulativeQuantityInt = item.CumulativeQuantity;

                        // Set delivery date from SAP EKET.EINDT (format: YYYYMMDD)
                        if (deliveryDateDictionary.ContainsKey(item.POItem.ToString().PadLeft(5, '0')))
                        {
                            var dateString = deliveryDateDictionary[item.POItem.ToString().PadLeft(5, '0')];
                            if (DateTime.TryParseExact(dateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime parsedDate))
                            {
                                detailPO.DeliveryDate = parsedDate;
                            }
                        }
                    }
                }
                #endregion
            }
            else
            {
                //Create
                // Get delivery dates from SAP tables for new PO
                Dictionary<string, string> deliveryDateDictionary = new Dictionary<string, string>();
                try
                {
                    // Get EKET data for delivery schedule lines
                    var eketTable = _sapTableService.FetchFromSAPTable("EKET",
                        $"EBELN = '{IntegrationModel.Header.PurchaseOrderCode}'",
                        new List<string> {
                            "EBELN", "EBELP", "ETENR", "EINDT", "MENGE"
                        });

                    // Process delivery dates if data exists
                    if (eketTable?.Rows?.Count > 0)
                    {
                        // Group by EBELP and get the first (earliest) delivery date for each item
                        var deliveryDatesByItem = eketTable.Rows
                            .Cast<System.Data.DataRow>()
                            .GroupBy(row => row["EBELP"]?.ToString())
                            .ToDictionary(
                                g => g.Key,
                                g => g.OrderBy(row => row["ETENR"]?.ToString())
                                      .FirstOrDefault()?["EINDT"]?.ToString()
                            );

                        deliveryDateDictionary = deliveryDatesByItem
                            .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but continue with the integration
                    // Handle SAP connection issues gracefully
                    Console.WriteLine($"Error fetching delivery dates from SAP: {ex.Message}");
                }

                existPO = new PurchaseOrderMasterModel();
                existPO.PurchaseOrderId = Guid.NewGuid();
                existPO.PurchaseOrderCode = IntegrationModel.Header.PurchaseOrderCode;
                existPO.CompanyCode = IntegrationModel.Header.CompanyCode;
                existPO.DocumentType = IntegrationModel.Header.DocumentType;
                existPO.DeletionInd = IntegrationModel.Header.DeletionInd;
                existPO.VendorNumber = IntegrationModel.Header.VendorNumber;
                existPO.PurchasingOrg = IntegrationModel.Header.PurchasingOrg;
                existPO.DocumentDate = IntegrationModel.Header.DocumentDate;
                existPO.ReleaseIndicator = IntegrationModel.Header.ReleaseIndicator;

                existPO.CreateTime = DateTime.Now;
                existPO.Actived = true;

                var detailPOs = IntegrationModel.ListDetail.Select(x =>
                {
                    var detailModel = new PurchaseOrderDetailModel
                    {
                        PurchaseOrderDetailId = Guid.NewGuid(),
                        PurchaseOrderId = existPO.PurchaseOrderId,

                        PurchaseOrderCode = x.PurchaseOrderCode,
                        POItem = x.POItem.ToString(),
                        Plant = x.Plant,
                        Material = x.Material,
                        ProductCode = !string.IsNullOrEmpty(x.Material) ? x.Material.Replace("000000000", "") : null,
                        DeletionInd = x.DeletionInd,
                        ShortText = x.ShortText,
                        StorageLocation = x.StorageLocation,
                        POQuantity = x.POQuantity,
                        OrderUnit = x.OrderUnit,
                        DelivCompl = x.DelivCompl,
                        AcctAssgmtCat = x.AcctAssgmtCat,
                        ItemCategory = x.ItemCategory,
                        GoodsReceipt = x.GoodsReceipt,
                        SDDocument = x.SDDocument,
                        Item = x.Item,
                        WBSElement = x.WBSElement,
                        CumulativeQuantity = x.CumulativeQuantity.ToString(),
                        POItemInt = x.POItem,
                        CumulativeQuantityInt = x.CumulativeQuantity,
                        BasicDataText = x.BasicDataText,
                        Sso = x.SSO
                    };

                    // Set delivery date from SAP EKET.EINDT (format: YYYYMMDD)
                    if (deliveryDateDictionary.ContainsKey(x.POItem.ToString().PadLeft(5, '0')))
                    {
                        var dateString = deliveryDateDictionary[x.POItem.ToString().PadLeft(5, '0')];
                        if (DateTime.TryParseExact(dateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime parsedDate))
                        {
                            detailModel.DeliveryDate = parsedDate;
                        }
                    }

                    return detailModel;
                }).ToList();

                existPO.PurchaseOrderDetailModel = detailPOs;
                _context.PurchaseOrderMasterModel.Add(existPO);
            }



            await _context.SaveChangesAsync();

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Message = "Tích hợp thành công PurchaseOrder", Data = existPO.PurchaseOrderId });
        }

        /// <summary>Test function to update DeliveryDate for all POs created in 2025</summary>
        /// <remarks>
        /// This test function will:
        /// 1. Find all Purchase Orders created in 2025 (currently filtered to June 2025)
        /// 2. Process POs in batches of 50
        /// 3. For each batch, count SAP EKET records first, then fetch them in batches of 500
        /// 4. Update the DeliveryDate field in PurchaseOrderDetailModel
        /// 
        /// POST /api/v{version}/SAPIntegration/PurchaseOrderIntegration/UpdateDeliveryDates2025
        /// </remarks>
        [HttpPost("UpdateDeliveryDates2025")]
        public async Task<IActionResult> UpdateDeliveryDates2025Async()
        {
            try
            {
                // Find all POs created in 2025
                var pos2025 = await _context.PurchaseOrderMasterModel
                    .Where(x => x.CreateTime.HasValue
                        && x.CreateTime.Value.Year == 2025
                        && x.CreateTime.Value.Month == 6
                    )
                    .Include(x => x.PurchaseOrderDetailModel)
                    .ToListAsync();

                if (!pos2025.Any())
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Message = "No Purchase Orders found for 2025",
                        Data = null
                    });
                }

                int totalPOs = pos2025.Count;
                int updatedPOs = 0;
                int totalDetailsUpdated = 0;
                var errors = new List<string>();

                // Process POs in batches of 50
                const int batchSize = 50;
                for (int i = 0; i < pos2025.Count; i += batchSize)
                {
                    var batch = pos2025.Skip(i).Take(batchSize).ToList();

                    try
                    {
                        // Get delivery dates from SAP EKET table for all POs in this batch
                        Dictionary<string, Dictionary<string, string>> allDeliveryDates = new Dictionary<string, Dictionary<string, string>>();

                        // Build condition for all POs in batch
                        var poConditions = batch.Select(po => $"EBELN = '{po.PurchaseOrderCode}'").ToList();
                        var whereCondition = string.Join(" OR ", poConditions);

                        // First count the records
                        int totalRecords = GetSAPRecordCount("EKET", whereCondition);

                        if (totalRecords > 0)
                        {
                            // Fetch records in batches of 500
                            const int sapBatchSize = 500;
                            var allRows = new List<System.Data.DataRow>();

                            for (int skip = 0; skip < totalRecords; skip += sapBatchSize)
                            {
                                var eketTable = _sapTableService.FetchFromSAPTable("EKET",
                                    whereCondition,
                                    new List<string> {
                                        "EBELN", "EBELP", "ETENR", "EINDT", "MENGE"
                                    },
                                    sapBatchSize, // rowCount
                                    skip);       // skip

                                if (eketTable?.Rows?.Count > 0)
                                {
                                    allRows.AddRange(eketTable.Rows.Cast<System.Data.DataRow>());
                                }
                            }

                            // Process delivery dates if data exists
                            if (allRows.Count > 0)
                            {
                                // Group by EBELN (PO Code) first, then by EBELP (PO Item)
                                var deliveryDatesByPO = allRows
                                    .GroupBy(row => row["EBELN"]?.ToString())
                                    .ToDictionary(
                                        poGroup => poGroup.Key,
                                        poGroup => poGroup
                                            .GroupBy(row => row["EBELP"]?.ToString())
                                            .ToDictionary(
                                                itemGroup => itemGroup.Key,
                                                itemGroup => itemGroup
                                                    .OrderBy(row => row["ETENR"]?.ToString())
                                                    .FirstOrDefault()?["EINDT"]?.ToString()
                                            )
                                            .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                                            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                                    );

                                allDeliveryDates = deliveryDatesByPO;
                            }
                        }

                        // Update delivery dates for each PO in the batch
                        foreach (var po in batch)
                        {
                            try
                            {
                                bool poUpdated = false;
                                var deliveryDateDictionary = allDeliveryDates.ContainsKey(po.PurchaseOrderCode)
                                    ? allDeliveryDates[po.PurchaseOrderCode]
                                    : new Dictionary<string, string>();

                                foreach (var detail in po.PurchaseOrderDetailModel)
                                {
                                    if (deliveryDateDictionary.ContainsKey(detail.POItem.PadLeft(5, '0')))
                                    {
                                        var dateString = deliveryDateDictionary[detail.POItem.PadLeft(5, '0')];
                                        if (DateTime.TryParseExact(dateString, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out DateTime parsedDate))
                                        {
                                            detail.DeliveryDate = parsedDate;
                                            _context.Entry(detail).State = EntityState.Modified;
                                            totalDetailsUpdated++;
                                            poUpdated = true;
                                        }
                                    }
                                }

                                if (poUpdated)
                                {
                                    updatedPOs++;
                                }
                            }
                            catch (Exception ex)
                            {
                                errors.Add($"Error updating PO {po.PurchaseOrderCode}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Error processing batch {i / batchSize + 1}: {ex.Message}");
                    }
                }

                // Save all changes
                await _context.SaveChangesAsync();

                var result = new
                {
                    TotalPOsFound = totalPOs,
                    UpdatedPOs = updatedPOs,
                    TotalDetailsUpdated = totalDetailsUpdated,
                    Errors = errors
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = $"Updated delivery dates for {updatedPOs} out of {totalPOs} POs from 2025. Total detail records updated: {totalDetailsUpdated}",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse
                {
                    Code = 400,
                    IsSuccess = false,
                    Message = $"Error updating delivery dates: {ex.Message}",
                    Data = null
                });
            }
        }
    }
}
