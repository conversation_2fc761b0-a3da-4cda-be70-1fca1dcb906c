// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:intl/intl.dart';
// import '../Storage/storageSharedPreferences.dart';
// import '../element/RowDetail.dart';
// import '../element/buttonSwitchingStages.dart';
// import '../element/timeOut.dart';
// import '../model/switchingStagesApi.dart';
// import '../repository/api/switchingStagesApi.dart';
// import '../repository/function/inforSwitchingStagesFunction.dart';
// import 'LostConnect.dart';
//
// class InfoSwitchingStages extends StatefulWidget {
//   final String barcode;
//   final String token;
//   final String dateTimeOld;
//   const InfoSwitchingStages({Key? key, required this.barcode, required this.token, required this.dateTimeOld }) : super(key: key);
//
//   @override
//   _InfoSwitchingStagesState createState() => _InfoSwitchingStagesState();
// }
//
// class _InfoSwitchingStagesState extends State<InfoSwitchingStages> {
//
//   bool? _isLoading;
//   DataSwitchingStages? _switchingStateData;
//   SwitchingStages? _switchingStages;
//   bool _isNotWifi = false;
//   bool _error = false;
//   String _convertDateCreate = " ";
//   late bool _timeOut;
//   bool? _disableButton;
//
//   @override
//   void initState() {
//     super.initState();
//     _getSwitchingStages();
//   }
//
//   Future<void> _getSwitchingStages() async {
//     try {
//       String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
//       DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
//       DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
//       if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
//         _timeOut = true;
//       }else {
//         setState(() {
//           _timeOut = false;
//           _isLoading = true;
//           _isNotWifi = false;
//         });
//         final responses = await SwitchingStagesApi.getSwitchingStages(widget.barcode, widget.token);
//         if (!mounted) return;
//         setState(() {
//           _isLoading = false;
//         });
//         if (responses.statusCode == 200) {
//           if (responses.body.isNotEmpty) {
//             final responseDataSwitchingStages = jsonDecode(responses.body);
//             setState(() {
//               _switchingStages =
//                   SwitchingStages.fromJson(responseDataSwitchingStages);
//             });
//             if (_switchingStages!.isSuccess != false) {
//               setState(() {
//                 _switchingStateData = _switchingStages!.data;
//                 _convertDateCreate =
//                     InfoSwitchingStagesFunction.convertDateCreate(
//                         _switchingStateData);
//               });
//             } else {
//               setState(() {
//                 _switchingStateData = null;
//               });
//             }
//           } else {
//             setState(() {
//               _switchingStateData = null;
//             });
//           }
//         } else {
//           setState(() {
//             _switchingStateData = null;
//           });
//         }
//       }
//     }on SocketException catch(_){
//       if (!mounted) return;
//       setState(() {
//         _isNotWifi = true;
//         _isLoading = false;
//         _timeOut = false;
//       });
//     }catch (error) {
//       if (!mounted) return;
//       setState(() {
//         _isNotWifi = true;
//         _isLoading = false;
//         _error = true;
//         _timeOut = false;
//       });
//     }
//   }
//   void _setButton() {
//     setState(() {
//       _disableButton = true;
//     });
//   }
//   // Future<void> _removeCurrentUser(BuildContext context) async {
//   //   setState(() {
//   //     _disableButton = true;
//   //   });
//   //   showDialog<String>(
//   //     barrierDismissible: false,
//   //     context: context,
//   //     builder: (BuildContext context) => WillPopScope(
//   //       onWillPop: () async => false,
//   //       child: const Center(
//   //         child: CircularProgressIndicator(),
//   //       ),
//   //     ),
//   //   );
//   //   await Future.wait([
//   //     StorageSharedPreferences.removeShared("id"),
//   //     StorageSharedPreferences.removeShared("datetimeNow"),
//   //     SecureStorage.removeSecure("user", null)
//   //   ]);
//   //   Navigator.pop(context);
//   //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//   //       backgroundColor: Colors.black,
//   //       content: Text(
//   //         'Đăng xuất thành công',
//   //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
//   //       ),
//   //       duration: const Duration(seconds: 2)));
//   //   Future.delayed(const Duration(seconds: 0), () {
//   //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
//   //   });
//   // }
//
//   @override
//   Widget build(BuildContext context) {
//     return
//       _timeOut == true ? WillPopScope(
//           onWillPop: () => Future.value(false),
//           child:Scaffold(
//           backgroundColor: Colors.grey.shade200,
//           body:TimeOutView(
//               setButton: _setButton, disableButton: _disableButton ?? false))):Scaffold(
//           backgroundColor: Colors.grey.shade200,
//           appBar: AppBar(
//             titleSpacing: 0,
//             automaticallyImplyLeading: false,
//             backgroundColor: const Color(0xff0052cc),
//             elevation: 0,
//             centerTitle: true,
//             leading: IconButton(
//               splashColor: Colors.transparent,
//               highlightColor: Colors.transparent,
//               padding: EdgeInsets.zero,
//               constraints: const BoxConstraints(),
//               icon: Icon(Icons.arrow_back_ios_new_rounded,
//                   size: 14.sp),
//               onPressed: () {
//                 Navigator.pop(context);
//               },
//             ),
//             title: Text('Thông tin chuyển công đoạn', style: TextStyle(fontWeight: FontWeight.bold,fontSize: 14.sp, color: Colors.white),),
//           ),
//           body: _error == true ?  Center(
//               child:Text('Có lỗi xảy ra! vui lòng thử lại sau',style: TextStyle(fontSize: 15.sp,color: Colors.black)
//               )):_isNotWifi == false ? _isLoading == true?const Center(child: CircularProgressIndicator()):
//           _switchingStateData != null ?SingleChildScrollView(
//             child: Column(
//               children: <Widget> [
//                 Container(
//                   padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
//                   decoration: const BoxDecoration(
//                       color: Colors.white
//                   ),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: <Widget> [
//                       Text("DANH SÁCH CHUYỂN CÔNG ĐOẠN",
//                         style: TextStyle(
//                           fontSize: 14.sp,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       SizedBox(
//                         height: 30.h,
//                       ),
//                       RowDetail(
//                           title: 'LSX ĐT:',
//                           trailing: _switchingStateData!.productionOrder.toString()
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'Đợt SX:',
//                           trailing: _switchingStateData!.summary.toString()
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'LSX SAP:',
//                           trailing: _switchingStateData!.productionOrderSAP.toString()
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'Mã SP:',
//                           trailing: _switchingStateData!.productCode.toString()
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'Tên SP:',
//                           trailing: _switchingStateData!.productName.toString()
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'CĐ hoàn tất:',
//                           trailing: _switchingStateData!.fromStepCode ?? " "
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                         title: 'Người tạo:',
//                         trailing: _switchingStateData!.createByFullName == null ? " ":_switchingStateData!.createByFullName.toString(),
//                       ),
//                       SizedBox(
//                         height: 15.h,
//                       ),
//                       RowDetail(
//                           title: 'Ngày tạo:',
//                           trailing: _convertDateCreate
//                       ),
//                       SizedBox(
//                         height: 25.h,
//                       ),
//                       ButtonSwitchingStages(
//                           dateTimeOld: widget.dateTimeOld,
//                           token: widget.token,
//                           switchingStateData: _switchingStateData,
//                           switchingStages: _switchingStages,
//                           getSwitchingStages: _getSwitchingStages,
//                           toBarcode: widget.barcode)
//             // Container(
//             //     width: double.infinity,
//             //     decoration: const BoxDecoration(
//             //     ),
//             //     child: ElevatedButton(
//             //       style: ButtonStyle(
//             //         shape: MaterialStateProperty.all<
//             //             RoundedRectangleBorder>(
//             //             RoundedRectangleBorder(
//             //                 borderRadius: BorderRadius.circular(5.r),
//             //                 side: const BorderSide(color: Color(0xff0052cc)))),
//             //         side: MaterialStateProperty.all(
//             //           const BorderSide(
//             //             color: Color(0xff0052cc),
//             //           ),
//             //         ),
//             //         backgroundColor:
//             //         MaterialStateProperty.all(const Color(0xff0052cc)),
//             //       ),
//             //       onPressed: () async {
//             //         var data = await Navigator.pushNamed(context, "/SwitchingStage",
//             //               arguments: ScreenArgumentSwitchingStage(_switchingStateData!, widget.token, _switchingStages,widget.dateTimeOld));
//             //           if (data == null) return;
//             //           if (data == true) {
//             //             _getSwitchingStages();
//             //           }
//             //
//             //
//             //       },
//             //       child: Container(
//             //         margin: EdgeInsets.symmetric(vertical: 10.h),
//             //         child: Text(
//             //           'Chuyển công đoạn',
//             //           style: TextStyle(
//             //               color: Colors.white,
//             //               fontWeight: FontWeight.bold,
//             //               fontSize: 14.sp),
//             //         ),
//             //       ),
//             //     ),
//             //   ),
//                   ])
//
//                 )
//               ],
//             ),
//           ): Padding(
//                padding: EdgeInsets.symmetric(horizontal: 5.w),
//            child: Center(child:Text("Không tim thấy thông tin thẻ treo! vui lòng kiểm tra thẻ treo đã được ghi nhận sản lượng chưa",style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center))):
//           LostConnect(
//               checkConnect:()=>_getSwitchingStages()
//           )
//       );
//   }
//
// }