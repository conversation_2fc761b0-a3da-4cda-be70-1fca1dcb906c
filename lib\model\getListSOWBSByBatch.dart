class GetListSOWBSByBatch {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetListSOWBSByBatch>? data;


  GetListSOWBSByBatch(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetListSOWBSByBatch.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetListSOWBSByBatch>[];
      json['data'].forEach((v) {
        data!.add(DataGetListSOWBSByBatch.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetListSOWBSByBatch {
  String? so;
  String? soLine;
  String? wbs;
  double? quantity;
  String? unit;
  String? lsxdt;
  bool? isCheck;

  DataGetListSOWBSByBatch({this.so, this.soLine, this.wbs, this.quantity, this.unit, this.lsxdt});

  DataGetListSOWBSByBatch.fromJson(Map<String, dynamic> json) {
    so = json['so'];
    soLine = json['soLine'];
    wbs = json['wbs'];
    quantity = json['quantity'];
    unit = json['unit'];
    lsxdt = json['lsxdt'];
    isCheck = false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['so'] = so;
    data['soLine'] = soLine;
    data['wbs'] = wbs;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['lsxdt'] = lsxdt;
    return data;
  }
}