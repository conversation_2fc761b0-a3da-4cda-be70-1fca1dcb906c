﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RawMaterial_PurchaseOrderDetail_Mapping", Schema = "MESP2")]
    public partial class RawMaterial_PurchaseOrderDetail_Mapping
    {
        [Key]
        public Guid RawMaterialCardDetailId { get; set; }
        public Guid? RawMaterialCardId { get; set; }
        public Guid? PurchaseOrderDetailId { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }

        [ForeignKey("PurchaseOrderDetailId")]
        [InverseProperty("RawMaterial_PurchaseOrderDetail_Mapping")]
        public virtual PurchaseOrderDetailModel PurchaseOrderDetail { get; set; }
        [ForeignKey("RawMaterialCardId")]
        [InverseProperty("RawMaterial_PurchaseOrderDetail_Mapping")]
        public virtual RawMaterialCardModel RawMaterialCard { get; set; }
    }
}