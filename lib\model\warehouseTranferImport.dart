class WarehouseTranferImportModel {
  String? reservationId;
  String? slocImportId;
  String? storageBinImportId;

  WarehouseTranferImportModel(
      {this.reservationId, this.slocImportId, this.storageBinImportId});



  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reservationId'] = reservationId;
    data['slocImportId'] = slocImportId;
    data['storageBinImportId'] = storageBinImportId;
    return data;
  }
}
class WarehouseTranferImportMessage {
  int? code;
  bool? isSuccess;
  String? message;
  bool? dataTranferImportMessage;


  WarehouseTranferImportMessage(
      {this.code,
        this.isSuccess,
        this.message,
        this.dataTranferImportMessage});

  WarehouseTranferImportMessage.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    dataTranferImportMessage = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    data['data'] = dataTranferImportMessage;
    return data;
  }
}