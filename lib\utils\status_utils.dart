import 'package:flutter/material.dart';

class StatusUtils {
  static Color getMaintenanceStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'ĐÃ HOÀN THÀNH':
        return Colors.green;
      case 'IN_PROCESS':
      case 'IN_PROGRESS':
      case 'ĐANG THỰC HIỆN':
        return Colors.orange;
      case 'CREATED':
      case 'MỚI TẠO':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  static String getMaintenanceStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'ĐÃ HOÀN THÀNH':
        return 'Đã hoàn thành';
      case 'IN_PROCESS':
      case 'IN_PROGRESS':
      case 'ĐANG THỰC HIỆN':
        return 'Đang thực hiện';
      case 'CREATED':
      case 'MỚI TẠO':
      default:
        return 'Mới tạo';
    }
  }
}
