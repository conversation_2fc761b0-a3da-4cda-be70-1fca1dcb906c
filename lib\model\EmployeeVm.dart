class EmployeeVm {
  String? accountId;
  String? userName;
  String? fullName;
  String? employeeCode;
  String? saleOrgCode;
  String? storeName;

  EmployeeVm({
    this.accountId,
    this.userName,
    this.fullName,
    this.employeeCode,
    this.saleOrgCode,
    this.storeName,
  });

  factory EmployeeVm.fromJson(Map<String, dynamic> json) {
    return EmployeeVm(
      accountId: json['accountId'],
      userName: json['userName'],
      fullName: json['fullName'],
      employeeCode: json['employeeCode'],
      saleOrgCode: json['saleOrgCode'],
      storeName: json['storeName'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['accountId'] = accountId;
    data['userName'] = userName;
    data['fullName'] = fullName;
    data['employeeCode'] = employeeCode;
    data['saleOrgCode'] = saleOrgCode;
    data['storeName'] = storeName;

    return data;
  }
}
