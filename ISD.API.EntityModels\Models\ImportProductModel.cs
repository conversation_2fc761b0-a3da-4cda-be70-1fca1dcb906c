﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ImportProductModel", Schema = "MES")]
    [Index("CreateTime", Name = "IX_ImportProductModel_CreateTime")]
    [Index("PO", Name = "IX_ImportProductModel_PO")]
    [Index("PO", "Status", Name = "IX_ImportProductModel_PO_Status")]
    [Index("Plant", Name = "IX_ImportProductModel_Plant")]
    [Index("ProductCode", Name = "IX_ImportProductModel_ProductCode")]
    public partial class ImportProductModel
    {
        [Key]
        public Guid ImportProductId { get; set; }
        [Required]
        [StringLength(50)]
        public string PO { get; set; }
        [Required]
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(500)]
        public string ProductName { get; set; }
        [StringLength(50)]
        public string SO { get; set; }
        [StringLength(10)]
        public string SOItem { get; set; }
        [StringLength(50)]
        public string WBS { get; set; }
        [Required]
        [StringLength(10)]
        public string Plant { get; set; }
        public Guid? SlocId { get; set; }
        [StringLength(4)]
        public string Sloc { get; set; }
        public Guid? StorageBinId { get; set; }
        [StringLength(10)]
        public string StorageBin { get; set; }
        [Required]
        [StringLength(50)]
        public string Batch { get; set; }
        public int ImportQuantity { get; set; }
        [Required]
        [StringLength(10)]
        public string Unit { get; set; }
        public int ImportSequence { get; set; }
        public DateTime DocumentDate { get; set; }
        [StringLength(50)]
        public string SAPDocumentNumber { get; set; }
        [StringLength(4)]
        public string SAPYear { get; set; }
        [Required]
        [StringLength(20)]
        public string Status { get; set; }
        public DateTime? SendToSAPTime { get; set; }
        public string SAPResponse { get; set; }
        public DateTime CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Required]
        public bool? Actived { get; set; }
    }
}