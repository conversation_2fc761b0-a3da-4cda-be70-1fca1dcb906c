# API Integration

The TTF MES Mobile application communicates with backend services through a RESTful API. This document outlines the API integration approach, including configuration, authentication, request/response handling, and error management.

## API Overview

### Architecture

The application follows a repository pattern for API integration:

- **URL Definitions**: API endpoint URLs are defined in `lib/urlApi/urlApi.dart`
- **API Repositories**: Located in `lib/repository/api/` directory, handle specific API calls
- **Repository Functions**: Located in `lib/repository/function/` directory, implement business logic
- **Data Models**: Located in `lib/model/` directory, define data structures

### Environment Configuration

The application supports multiple environments with different API endpoints:

- **Development**: Points to development backend services
- **QA**: Points to QA/testing backend services
- **Production**: Points to production backend services

Environment-specific configurations are managed in `lib/utils/appConfig.dart`.

## Authentication

### Token-based Authentication

The application uses token-based authentication:

1. **Login Process**:
   - User provides credentials
   - Backend validates and returns authentication token
   - Token is stored securely using `flutter_secure_storage`

2. **Token Usage**:
   - Token is included in API request headers
   - Format: `Authorization: Bearer {token}`

3. **Token Expiration**:
   - Tokens have a limited lifetime
   - Session timeout is handled automatically
   - Users are redirected to login when token expires

### Secure Storage

Authentication tokens are stored securely using:

- **SecureStorage**: Implemented in `lib/Storage/storageSecureStorage.dart`
- **Usage**: `await SecureStorage.setString("user", jsonEncode(user), null)`

## Request/Response Handling

### HTTP Client

The application uses the `http` package for API communication:

```dart
import 'package:http/http.dart' as http;

// Example HTTP GET request
Future<http.Response> getExample(String endpoint, String token) async {
  final response = await http.get(
    Uri.parse(endpoint),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    },
  );
  return response;
}

// Example HTTP POST request
Future<http.Response> postExample(String endpoint, Map<String, dynamic> data, String token) async {
  final response = await http.post(
    Uri.parse(endpoint),
    body: jsonEncode(data),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    },
  );
  return response;
}
```

### Request Types

The application implements the following HTTP request types:

- **GET**: Retrieve data from the server
- **POST**: Create new resources or submit data
- **PUT**: Update existing resources
- **DELETE**: Remove resources

### Request Headers

Standard headers included in API requests:

```dart
headers: {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authorization': 'Bearer $token',
}
```

### Response Parsing

API responses are parsed into model objects:

```dart
// Example response parsing
Future<UserModel> getUserData(String token) async {
  final response = await getExample('${ApiUrl.baseUrl}/user', token);
  if (response.statusCode == 200) {
    return UserModel.fromJson(jsonDecode(response.body));
  } else {
    throw Exception('Failed to load user data');
  }
}
```

## Error Handling

### Status Code Handling

The application handles different HTTP status codes:

- **2xx (Success)**: Process the response normally
- **401 (Unauthorized)**: Redirect to login screen
- **403 (Forbidden)**: Show permission error
- **404 (Not Found)**: Show resource not found error
- **5xx (Server Error)**: Show server error message

### Exception Handling

API calls are wrapped in try-catch blocks:

```dart
try {
  final result = await repository.fetchData(token);
  // Process successful result
} catch (e) {
  // Handle error
  if (e is TimeoutException) {
    // Handle timeout
  } else if (e is SocketException) {
    // Handle network error
  } else {
    // Handle other errors
  }
}
```

### Error Messages

User-friendly error messages are displayed:

- **Generic Error Message**: "An error occurred. Please try again."
- **Network Error**: "No internet connection. Please check your connection and try again."
- **Server Error**: "Server error. Please try again later."
- **Authentication Error**: "Your session has expired. Please log in again."

## Connectivity Management

### Handling Network Changes

The application monitors network connectivity:

```dart
StreamSubscription<ConnectivityResult> subscription;

void initConnectivity() {
  subscription = Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      // Handle offline mode
    } else {
      // Handle online mode
    }
  });
}
```

### Offline Support

For offline scenarios, the application:

1. **Caches Critical Data**: Stores essential data locally
2. **Queues Operations**: Saves operations to be performed later
3. **Synchronizes on Reconnect**: Executes queued operations when online

## API Endpoints

### Base URL Configuration

The base URL is configured based on the environment:

```dart
class ApiUrl {
  static String get baseUrl {
    if (AppConfig.isProduction) {
      return 'https://api.production.example.com';
    } else if (AppConfig.isQA) {
      return 'https://api.qa.example.com';
    } else {
      return 'https://api.dev.example.com';
    }
  }
  
  // Endpoint definitions
  static String get login => '$baseUrl/auth/login';
  static String get userData => '$baseUrl/user';
  // ... other endpoints
}
```

### Key Endpoints

The application uses the following key API endpoints:

#### Authentication
- **Login**: `POST /auth/login`
- **Refresh Token**: `POST /auth/refresh-token`
- **Logout**: `POST /auth/logout`

#### User Management
- **User Data**: `GET /user`
- **Update Profile**: `PUT /user`
- **Change Password**: `POST /user/change-password`

#### Inventory Management
- **List Inventory**: `GET /inventory`
- **Inventory Details**: `GET /inventory/{id}`
- **Update Inventory**: `PUT /inventory/{id}`
- **Transfer Materials**: `POST /inventory/transfer`

#### Quality Control
- **List QC Tasks**: `GET /qc/tasks`
- **QC Task Details**: `GET /qc/tasks/{id}`
- **Submit QC Report**: `POST /qc/reports`
- **Upload QC Images**: `POST /qc/images`

#### Production Management
- **Production Orders**: `GET /production/orders`
- **Production Stages**: `GET /production/stages`
- **Update Production**: `PUT /production/orders/{id}`
- **Complete Stage**: `POST /production/complete-stage`

#### Downtime Tracking
- **List Downtimes**: `GET /downtime`
- **Downtime Details**: `GET /downtime/{id}`
- **Create Downtime**: `POST /downtime`
- **Update Downtime**: `PUT /downtime/{id}`

#### Maintenance Management
- **Maintenance Orders**: `GET /maintenance/orders`
- **Maintenance Details**: `GET /maintenance/orders/{id}`
- **Create Maintenance**: `POST /maintenance/orders`
- **Update Maintenance**: `PUT /maintenance/orders/{id}`

## File Upload

### Image Upload Handling

The application supports image uploads:

```dart
Future<String> uploadImage(File image, String token) async {
  final request = http.MultipartRequest(
    'POST',
    Uri.parse('${ApiUrl.baseUrl}/upload/image'),
  );
  
  // Add authentication header
  request.headers.addAll({
    'Authorization': 'Bearer $token',
  });
  
  // Add file to request
  final file = await http.MultipartFile.fromPath(
    'image',
    image.path,
    contentType: MediaType('image', 'jpeg'),
  );
  request.files.add(file);
  
  // Send request
  final response = await request.send();
  
  if (response.statusCode == 200) {
    final responseData = await response.stream.toBytes();
    final responseString = String.fromCharCodes(responseData);
    final jsonData = jsonDecode(responseString);
    return jsonData['url'];
  } else {
    throw Exception('Failed to upload image');
  }
}
```

### Image Configuration

Image uploads are configured with:

```dart
const ImageConfiguration globalImageConfig = ImageConfiguration(
  maxWidth: 1440,
  maxHeight: 1440,
  imageQuality: 80,
);
```

## Best Practices

### Performance Optimization

- **Minimize API Calls**: Batch operations when possible
- **Caching**: Cache frequently accessed data
- **Pagination**: Use pagination for large data sets
- **Compression**: Enable gzip compression for API requests

### Security Best Practices

- **Secure Token Storage**: Store authentication tokens securely
- **HTTPS Only**: Use only HTTPS for all API communications
- **Token Expiration**: Handle token expiration gracefully
- **Input Validation**: Validate all user inputs before sending to API

### Error Handling

- **Consistent Error Handling**: Use consistent approach across the app
- **Detailed Logging**: Log detailed error information for debugging
- **User-Friendly Messages**: Present user-friendly error messages
- **Retry Mechanism**: Implement retry for transient failures

### Testing

- **Mock API Responses**: Use mock data for testing
- **Test Edge Cases**: Test error scenarios and edge cases
- **Timeout Testing**: Test behavior with slow responses
- **Offline Testing**: Test application behavior when offline 