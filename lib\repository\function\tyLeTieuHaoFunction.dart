import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../model/tyLeTieuHaoModel.dart';
import '../../model/TyLeTieuHaoSearchModel.dart';
import '../../urlApi/urlApi.dart';
import '../../Storage/storageSecureStorage.dart';
import '../../model/downtimeModel.dart';
import '../function/commonFunction.dart';

// Update TyLeTieuHaoMasterDataResponse to include employees
class TyLeTieuHaoMasterDataResponse {
  final List<DepartmentItem>? departments;
  final List<StepCodeItem>? steps;
  final List<EmployeeRecord>? employees;
  final bool status;
  final String message;

  TyLeTieuHaoMasterDataResponse({
    this.departments,
    this.steps,
    this.employees,
    this.status = true,
    this.message = 'Success',
  });

  factory TyLeTieuHaoMasterDataResponse.fromJson(Map<String, dynamic> json) {
    return TyLeTieuHaoMasterDataResponse(
      departments: (json['departments'] as List?)?.map((item) => DepartmentItem.fromJson(item)).toList(),
      steps: (json['steps'] as List?)?.map((item) => StepCodeItem.fromJson(item)).toList(),
      employees: (json['employees'] as List?)?.map((item) => EmployeeRecord.fromJson(item)).toList(),
      status: json['status'] ?? true,
      message: json['message'] ?? 'Success',
    );
  }
}

class TyLeTieuHaoFunction {
  static const bool useMockData = false;

  static const String _baseEndpoint = '/api/v1/MES/';
  static const String _tyLeTieuHaoEndpoint = '${_baseEndpoint}TyLeTieuHao/';
  static const String _employeeEndpoint = '${_baseEndpoint}GetEmployees';

  static Future<String> _getBaseUrl() async {
    final environment = await SecureStorage.getString("environment", null);
    return environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
  }

  static Future<TyLeTieuHaoMasterDataResponse?> fetchEmployees(String token, String companyCode) async {
    try {
      final response = await CommonFunction.fetchEmployees(token, companyCode);
      return TyLeTieuHaoMasterDataResponse(employees: response.employees, status: response.status, message: response.message);
    } catch (e) {
      debugPrint('TyLeTieuHaoFunction.fetchEmployees | error: $e');
      return null;
    }
  }

  static Future<TyLeTieuHaoMasterDataResponse?> getEmployees(String token, String companyCode) async {
    debugPrint("--- TIEN getEmployees | useMockData: $useMockData | companyCode: $companyCode");
    return useMockData ? _fetchEmployeesFromMock(token, companyCode) : fetchEmployees(token, companyCode);
  }

  static Future<TyLeTieuHaoMasterDataResponse?> _fetchEmployeesFromMock(String token, String companyCode) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return TyLeTieuHaoMasterDataResponse(
      employees: [
        EmployeeRecord(employeeId: "EMP001", employeeName: "Nguyễn Văn A"),
        EmployeeRecord(employeeId: "EMP002", employeeName: "Trần Thị B"),
        EmployeeRecord(employeeId: "EMP003", employeeName: "Lê Văn C"),
      ],
      status: true,
      message: 'Success',
    );
  }

  static Future<TyLeTieuHaoModel?> fetchTyLeTieuHaoList(String token, TyLeTieuHaoSearchModel searchModel) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl/api/v1/MES/TyLeTieuHao/List';

      debugPrint('Fetching ty le tieu hao list with params: ${jsonEncode(searchModel.toJson())}');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: jsonEncode(searchModel.toJson()),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        debugPrint('Ty le tieu hao list response: $jsonResponse');
        return TyLeTieuHaoModel.fromJson(jsonResponse);
      } else {
        debugPrint('Error status code: ${response.statusCode}');
        debugPrint('Error response: ${response.body}');
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching ty le tieu hao list: $e');
      return null;
    }
  }

  static Future<TyLeTieuHaoRecord?> fetchTyLeTieuHaoDetail(String token, String id) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl/api/v1/MES/TyLeTieuHao/$id';

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['data'] != null) {
          return TyLeTieuHaoRecord.fromJson(jsonResponse['data']);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching ty le tieu hao detail: $e');
      return null;
    }
  }

  static Future<List<LSXSAPSuggestion>> fetchLSXSAPSuggestions(String token, String query, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl/api/v1/MES/TyLeTieuHao/LSXSAPSuggestions?query=$query&companyCode=$companyCode';

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['data'] != null) {
          return (jsonResponse['data'] as List).map((item) => LSXSAPSuggestion.fromJson(item)).toList();
        }
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching LSX SAP suggestions: $e');
      return [];
    }
  }

  static Future<List<MaterialSuggestion>> fetchMaterialSuggestions(String token, String productCode, String companyCode, {String query = ''}) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url =
          '$baseUrl/api/v1/MES/TyLeTieuHao/MaterialSuggestions?productCode=$productCode&companyCode=$companyCode&query=${Uri.encodeComponent(query)}';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['data'] != null) {
          return (jsonResponse['data'] as List).map((item) => MaterialSuggestion.fromJson(item)).toList();
        }
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching material suggestions: $e');
      return [];
    }
  }

  static Future<bool> saveTyLeTieuHao(String token, TyLeTieuHaoRecord record) async {
    debugPrint("--- TIEN saveTyLeTieuHao | useMockData: $useMockData");
    debugPrint("Operation: ${record.id == null || record.id!.isEmpty ? 'Create' : 'Update'}");

    try {
      final baseUrl = await _getBaseUrl();
      final isCreate = record.id == null || record.id!.isEmpty;
      final url = isCreate ? '$baseUrl$_tyLeTieuHaoEndpoint' : '$baseUrl$_tyLeTieuHaoEndpoint${record.id}';

      final response = await (isCreate
          ? http.post(
              Uri.parse(url),
              headers: {
                ...UrlApi.headersToken(token),
                'Content-Type': 'application/json',
              },
              body: jsonEncode(record.toJson()),
            )
          : http.put(
              Uri.parse(url),
              headers: {
                ...UrlApi.headersToken(token),
                'Content-Type': 'application/json',
              },
              body: jsonEncode(record.toJson()),
            ));

      debugPrint('Save response status: ${response.statusCode}');
      debugPrint('Save response body: ${response.body}');

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error saving ty le tieu hao: $e');
      return false;
    }
  }
}
