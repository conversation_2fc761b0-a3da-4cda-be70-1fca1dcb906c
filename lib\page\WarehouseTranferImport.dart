// import 'dart:io';
// import 'package:collection/collection.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import '../Widget/container/errorFormatCheck.dart';
// import '../element/RowDetail.dart';
// import '../element/TableImExportWH.dart';
// import '../model/getBatch.dart';
// import '../model/getInventorySAP.dart';
// import '../model/getQuantityExported.dart';
// import '../model/getReservation.dart';
// import '../model/listWarehouseTranfer.dart';
// import '../model/rawMaterialCard.dart';
// import '../model/slocAddresse.dart';
// import '../model/warehouseTranferImport.dart';
// import '../repository/function/exportWarehouseFunction.dart';
// import '../repository/function/importWareHouseFunction.dart';
// import '../repository/function/wareHouseTranferImportFunction.dart';
// import '../screenArguments/sceemArgumentGetBackData.dart';
// import '../service/globalValue.dart';
// import 'LostConnect.dart';
//
// class WarehouseTranferImport extends StatefulWidget {
//   const WarehouseTranferImport(
//       {Key? key,
//         required this.dataListWarehouseTranfer,
//         required this.barcode,
//         required this.token,
//         required this.plant,
//         required this.lsDataSlocAddress
//       }) : super(key: key);
//   final DataListWarehouseTranfer dataListWarehouseTranfer;
//   final String barcode;
//   final String token;
//   final String plant;
//   final List<DataSlocAddress> lsDataSlocAddress;
//   @override
//   _WarehouseTranferImportState createState() => _WarehouseTranferImportState();
// }
//
// class _WarehouseTranferImportState extends State<WarehouseTranferImport> {
//
//   final List<PoDetails> _lsNullPoDetails = [];
//   final List<SoDetails> _lsNullSoDetail = [];
//   final List<WbsDetails> _lsNullWbsDetails = [];
//   final bool _disableDropdown = true;
//
//   bool _isLoading = false;
//   bool _isNotWifi = false;
//   DataRawMeterial? _dataRawMeterial;
//   DataSlocAddress? _selectedSloc;
//   DataSlocAddress? _selectedSlocInput;
//   DataBatch? _getBatch;
//   DataReservation? _dataReservation;
//
//   bool _errorSelectedSlocInput = false;
//   bool _errorInventorySAP = false;
//   bool _disableDropdownInput = false;
//   bool _isLoadingInventory = false;
//
//   Data? _inventorySAPApi;
//   DataQuantityExported? _dataQuantityExported;
//   late List<DataSlocAddress?> _getLsDataSlocAddress;
//
//   @override
//   void initState() {
//     super.initState();
//     _cloneList();
//   }
//
//   Future<void> _getDataRawMaterial() async {
//     setState(() {
//       _isLoading = true;
//       _isNotWifi = false;
//     });
//     final data = await Future.wait([
//       ImportWareHouseFunction.fetchRawMaterial(widget.barcode, widget.token),
//       ExportWareHouseFunction.fetchBatch(widget.barcode, widget.token),
//       ExportWareHouseFunction.fetchReservation(widget.dataListWarehouseTranfer.reservationId.toString(), widget.token),
//       WareHouseTranferImportFunction.fetchQuantityExported(widget.dataListWarehouseTranfer.reservationId.toString(),widget.token)
//     ]);
//     if (!mounted) return;
//     setState(() {
//       _isLoading = false;
//     });
//     if (data.isNotEmpty) {
//       if (data[0] != null) {
//         _dataRawMeterial = data[0] as DataRawMeterial?;
//       }
//       if (data[1] != null) {
//         _getBatch = data[1] as DataBatch?;
//       }
//       if (data[2] != null) {
//         _dataReservation = data[2] as DataReservation?;
//       }
//       if(data[3] != null){
//         _dataQuantityExported = data[3] as DataQuantityExported?;
//       }
//     }
//   }
//
//   Future<void> _cloneList() async {
//     try {
//       await _getDataRawMaterial();
//       _getLsDataSlocAddress = widget.lsDataSlocAddress.map((e) => DataSlocAddress.clone(e)).toList();
//       if (widget.dataListWarehouseTranfer.storageLocation != null && widget.dataListWarehouseTranfer.storageLocation != "") {
//         _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == widget.dataListWarehouseTranfer.storageLocation);
//       }
//     } on SocketException catch (_) {
//       if (!mounted) return;
//       setState(() {
//         _isNotWifi = true;
//       });
//     } catch (error) {
//       print(error);
//       if (!mounted) return;
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//
//   Future<void> _setAddressQRCode(DataSlocAddress? data, BuildContext context) async {
//     try {
//       if(!mounted) return;
//       setState(() {
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((
//             element) => element!.sloc == data!.sloc)]!.defaultStorageBin =
//             data!.defaultStorageBin;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((
//             element) => element!.sloc == data.sloc)]!.sloc = data.sloc;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((
//             element) => element!.sloc == data.sloc)]!.warehouseNo =
//             data.warehouseNo;
//         _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((
//             element) => element!.sloc == data.sloc)]!.defaultStorageBinId =
//             data.defaultStorageBinId;
//         _selectedSlocInput = _getLsDataSlocAddress.firstWhereOrNull((element) => element!.sloc == data.sloc);
//         _disableDropdownInput = true;
//         _isLoadingInventory = true;
//         if (_selectedSlocInput != null) {
//           _errorSelectedSlocInput = false;
//         }
//       });
//       PostInventorySAP postInventorySAP = PostInventorySAP(
//           materialCode: _dataRawMeterial!.productCode,
//           plant: widget.plant,
//           sloc: _selectedSlocInput!.sloc,
//           storageBin: _selectedSlocInput!.defaultStorageBinId);
//       final dataInventory = await ExportWareHouseFunction.fetchInventorySAP(postInventorySAP, widget.token);
//       if (!mounted) return;
//       setState(() {
//         _isLoadingInventory = false;
//       });
//       if (dataInventory != null) {
//         _inventorySAPApi = dataInventory;
//       }
//     } catch (error) {
//       if(!mounted) return;
//       setState(() {
//         _inventorySAPApi = null;
//         _selectedSloc = null;
//         _isLoadingInventory = false;
//       });
//       debugPrint("error: $error");
//     }
//   }
//
//
//   Future<void> _setSlocInput(DataSlocAddress? value) async {
//     try {
//       setState(() {
//         _selectedSlocInput = value!;
//           if (_selectedSlocInput == null) {
//             _errorSelectedSlocInput = true;
//           } else {
//             _errorSelectedSlocInput = false;
//           }
//         _isLoadingInventory = true;
//       });
//           PostInventorySAP postInventorySAP = PostInventorySAP(
//               materialCode: _dataRawMeterial!.productCode,
//               plant: widget.plant,
//               sloc: _selectedSlocInput!.sloc,
//               storageBin: _selectedSlocInput!.defaultStorageBinId);
//           final data = await ExportWareHouseFunction.fetchInventorySAP(postInventorySAP, widget.token);
//           if (!mounted) return;
//           setState(() {
//             _isLoadingInventory = false;
//           });
//           if (data != null) {
//             _inventorySAPApi = data;
//           }
//     } on SocketException catch (_) {
//       if (!mounted) return;
//       setState(() {
//         _selectedSlocInput = value!;
//         if (_selectedSlocInput == null) {
//           _errorSelectedSlocInput = true;
//         } else {
//           _errorSelectedSlocInput = false;
//         }
//         _isLoadingInventory = false;
//       });
//     } catch (error) {
//       print(error);
//       if (!mounted) return;
//       setState(() {
//         _selectedSlocInput = value!;
//         if (_selectedSlocInput == null) {
//           _errorSelectedSlocInput = true;
//         } else {
//           _errorSelectedSlocInput = false;
//         }
//         _isLoadingInventory = false;
//       });
//     }
//   }
//   void _setSloc(DataSlocAddress? value){
//     setState(() {
//       _selectedSloc = value!;
//     });
//   }
//
//   void _checkError() {
//     setState(() {
//         if (_selectedSlocInput == null) {
//           _errorSelectedSlocInput = true;
//         }else{
//           _errorSelectedSlocInput = false;
//         }
//       if(_inventorySAPApi == null){
//         _errorInventorySAP = true;
//       }else{
//         _errorInventorySAP = false;
//       }
//     });
//   }
//
//
//
//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//         onWillPop: () async {
//           Navigator.pop(context,false);
//       return false;
//     },
//     child:Scaffold(
//       backgroundColor: Colors.grey.shade200,
//       appBar: AppBar(
//         titleSpacing: 0,
//         automaticallyImplyLeading: false,
//         backgroundColor: const Color(0xff0052cc),
//         elevation: 0,
//         centerTitle: true,
//         leading: IconButton(
//           splashColor: Colors.transparent,
//           highlightColor: Colors.transparent,
//           padding: EdgeInsets.zero,
//           constraints: const BoxConstraints(),
//           icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
//           onPressed: () {
//             Navigator.pop(context,false);
//           },
//         ),
//         title: Text(
//           "Chuyển nhập Kho NVL",
//           style: TextStyle(
//               fontWeight: FontWeight.bold,
//               fontSize: 14.sp,
//               color: Colors.white),
//         ),
//       ),
//       body: _isNotWifi
//           ? LostConnect(checkConnect: () => _cloneList())
//           : _isLoading == true
//           ? const Center(child: CircularProgressIndicator())
//           : _dataRawMeterial == null
//           ? Center(
//           child: Text("Không tìm thấy thông tin pallet NVL!",
//               style: TextStyle(fontSize: 15.sp)))
//           :
//       SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: <Widget>[
//             Container(
//               padding: EdgeInsets.symmetric(vertical: 10.h),
//               decoration: const BoxDecoration(color: Colors.white),
//               child: Align(
//                 alignment: Alignment.center,
//                 child: Text(
//                   _dataRawMeterial!.materialType == "1"
//                       ? "NVL Chính (Gỗ xẻ, ván, ván lạng)"
//                       : _dataRawMeterial!.materialType ==
//                       "2"
//                       ? "Hóa chất"
//                       : "Vật tư khác (phụ kiện, ốc vít, thanh trượt)",
//                   style: TextStyle(
//                       fontSize: 13.sp,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.grey),
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//             ),
//             SizedBox(height: 10.h),
//             Container(
//               width: double.infinity,
//               padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//               decoration: const BoxDecoration(color: Colors.white),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: <Widget> [
//                   Text(
//                     "I. LỆNH CẤP VẬT TƯ",
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   SizedBox(
//                     height: 15.h,
//                   ),
//                    RowDetail(
//                       title: "Số reservation:",
//                       trailing: widget.dataListWarehouseTranfer.reservationNumber ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Item:",
//                       trailing: widget.dataListWarehouseTranfer.item ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Mã material:",
//                       trailing: _dataReservation!.materialCode ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Nhà máy:",
//                       trailing: widget.plant),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Kho:",
//                       trailing: widget.dataListWarehouseTranfer.storageLocation ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "Số lô:",
//                       trailing: widget.dataListWarehouseTranfer.batchNumber ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "Số lượng yêu cầu:",
//                       trailing:  widget.dataListWarehouseTranfer.quantity != null ?  widget.dataListWarehouseTranfer.quantity!.round().toString() : " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                    RowDetail(
//                       title: "ĐVT:",
//                       trailing: widget.dataListWarehouseTranfer.unit ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "Nhà máy nhận:",
//                       trailing: widget.dataListWarehouseTranfer.riPlant ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Kho nhận:",
//                       trailing: widget.dataListWarehouseTranfer.riStorageLocation ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "SO/ SO Line:",
//                       trailing: widget.dataListWarehouseTranfer.so ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "WBS:",
//                       trailing: widget.dataListWarehouseTranfer.wbs ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "LSX:",
//                       trailing: widget.dataListWarehouseTranfer.lsx ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   // Align(
//                   //     alignment: Alignment.centerRight,
//                   //     child: Text(
//                   //         "Được tạo bởi 20002013 vào lúc 22/08/2022 13:44:06",
//                   //         style: TextStyle(fontSize: 12.sp, color: Colors.grey),
//                   //         textAlign: TextAlign.end)),
//                 ],
//               ),
//             ),
//             SizedBox(height: 10.h),
//             Container(
//               width: double.infinity,
//               padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//               decoration: const BoxDecoration(color: Colors.white),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: <Widget> [
//                   Text(
//                     "II. VỊ TRÍ XUẤT NHẬP",
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   Container(
//                     decoration: const BoxDecoration(),
//                     child: ElevatedButton.icon(
//                       style: ButtonStyle(
//                         shape: MaterialStateProperty.all<
//                             RoundedRectangleBorder>(
//                             RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(5.r),
//                                 side: const BorderSide(
//                                     color: Colors.white))),
//                         side: MaterialStateProperty.all(
//                           const BorderSide(
//                             color: Color(0xff0052cc),
//                           ),
//                         ),
//                         backgroundColor: MaterialStateProperty.all(
//                             const Color(0xff0052cc)),
//                       ),
//                       onPressed: () async {
//                           final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
//                           if (data == null) return;
//                           if(!mounted) return;
//                           _setAddressQRCode(data as DataSlocAddress?, context);
//
//                       },
//                       icon: Icon(Icons.camera_alt_outlined,
//                           size: 25.sp, color: Colors.white),
//                       label: Text(
//                         "Quét mã",
//                         style: TextStyle(
//                             color: Colors.white,
//                             fontWeight: FontWeight.bold,
//                             fontSize: 13.sp),
//                       ),
//                     ),
//                   ),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   const HeaderTableImExportWH(
//                       textCL1: "Kho xuất (*)",
//                       textCL2: "Kho nhập",
//                       colorCL2: 0xff0052cc,
//                       colorCL1: 0xff0052cc),
//                   TableImExportWHDD(
//                     textCL1: "Sloc",
//                     selectedSloc:  _selectedSloc,
//                     onChange:(value) => _setSloc(value),
//                     disableDropdown:_disableDropdown,
//                     disableDropdownInput:_disableDropdownInput,
//                     lsDataSlocAddress:_getLsDataSlocAddress,
//                     textCL3: "Sloc",
//                     selectedSlocInput:  _selectedSlocInput,
//                     onChangeInput:(value) => _setSlocInput(value),
//                     colorCL1: 0xffEEEEEE,
//                     colorCL2: 0xffFFFFFF,
//                     colorCL3: 0xffEEEEEE,
//                     colorCL4: 0xffFFFFFF,
//                   ),
//                   TableImExportWHText(
//                     textCL1: "Warehouse No",
//                     textCL2: _selectedSloc != null ?_selectedSloc!.warehouseNo ?? "": "",
//                     textCL3: "Warehouse No",
//                     textCL4: _selectedSlocInput != null ?_selectedSlocInput!.warehouseNo ?? "": "",
//                     colorCL1: 0xffEEEEEE,
//                     colorCL2: 0xffFFFFFF,
//                     colorCL3: 0xffEEEEEE,
//                     colorCL4: 0xffFFFFFF,
//                   ),
//                   TableImExportWHText(
//                     textCL1: "Storage Bin",
//                     textCL2: _selectedSloc != null ?_selectedSloc!.defaultStorageBin ?? "": "",
//                     textCL3: "Storage Bin",
//                     textCL4: _selectedSlocInput != null ?_selectedSlocInput!.defaultStorageBin ?? "": "",
//                     colorCL1: 0xffEEEEEE,
//                     colorCL2: 0xffFFFFFF,
//                     colorCL3: 0xffEEEEEE,
//                     colorCL4: 0xffFFFFFF,
//                   ),
//                   SizedBox(height: _errorSelectedSlocInput == true ? 10.h : 0),
//                   ContainerError.widgetError(_errorSelectedSlocInput, 'Kho nhập chưa được nhập'),
//                 ],
//               ),
//             ),
//             SizedBox(height: 10.h),
//             Container(
//               width: double.infinity,
//               padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
//               decoration: const BoxDecoration(color: Colors.white),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: <Widget> [
//                   Text(
//                     "III. THÔNG TIN NVL NHẬP",
//                     style: TextStyle(
//                       fontSize: 14.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   SizedBox(
//                     height: 15.h,
//                   ),
//                  RowDetail(
//                       title: "Tên NCC:",
//                       trailing: _dataRawMeterial!.vendorName ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   Row(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment:
//                     MainAxisAlignment.spaceBetween,
//                     children: <Widget>[
//                       Expanded(
//                         flex: 3,
//                         child: Align(
//                           alignment: Alignment.centerLeft,
//                           child: Text(
//                             "Số PO:",
//                             style: TextStyle(
//                               fontSize: 12.sp,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                           flex: 7,
//                           child: Align(
//                             alignment: Alignment.centerLeft,
//                             child: Column(
//                               crossAxisAlignment:
//                               CrossAxisAlignment.start,
//                               mainAxisAlignment:
//                               MainAxisAlignment.start,
//                               children: List.generate(
//                                 (_dataRawMeterial!.poDetails ??
//                                     _lsNullPoDetails)
//                                     .length,
//                                     (index) => Text(
//                                   (_dataRawMeterial!.poDetails ??
//                                       _lsNullPoDetails)[
//                                   index]
//                                       .item ??
//                                       " ",
//                                   style: TextStyle(
//                                     fontSize: 12.sp,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ))
//                     ],
//                   ),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                  RowDetail(
//                       title: "Mã hàng (mã SAP):",
//                       trailing: _dataRawMeterial!.productCode ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Tên hàng:",
//                       trailing: _dataRawMeterial!.productName ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   Row(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment:
//                     MainAxisAlignment.spaceBetween,
//                     children: <Widget>[
//                       Expanded(
//                         flex: 3,
//                         child: Align(
//                           alignment: Alignment.centerLeft,
//                           child: Text(
//                             "SO/SO Line:",
//                             style: TextStyle(
//                               fontSize: 12.sp,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                           flex: 7,
//                           child: Align(
//                             alignment: Alignment.centerLeft,
//                             child: Column(
//                               crossAxisAlignment:
//                               CrossAxisAlignment.start,
//                               mainAxisAlignment:
//                               MainAxisAlignment.start,
//                               children: List.generate(
//                                 (_dataRawMeterial!.soDetails ??
//                                     _lsNullSoDetail)
//                                     .length,
//                                     (index) => Text(
//                                   (_dataRawMeterial!.soDetails ??
//                                       _lsNullSoDetail)[
//                                   index]
//                                       .item ??
//                                       " ",
//                                   style: TextStyle(
//                                     fontSize: 12.sp,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ))
//                     ],
//                   ),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   Row(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment:
//                     MainAxisAlignment.spaceBetween,
//                     children: <Widget>[
//                       Expanded(
//                         flex: 3,
//                         child: Align(
//                           alignment: Alignment.centerLeft,
//                           child: Text(
//                             "WBS:",
//                             style: TextStyle(
//                               fontSize: 12.sp,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                           flex: 7,
//                           child: Align(
//                             alignment: Alignment.centerLeft,
//                             child: Column(
//                               crossAxisAlignment:
//                               CrossAxisAlignment.start,
//                               mainAxisAlignment:
//                               MainAxisAlignment.start,
//                               children: List.generate(
//                                 (_dataRawMeterial!.wbsDetails ??
//                                     _lsNullWbsDetails)
//                                     .length,
//                                     (index) => Text(
//                                   (_dataRawMeterial!.wbsDetails ??
//                                       _lsNullWbsDetails)[
//                                   index]
//                                       .item ??
//                                       " ",
//                                   style: TextStyle(
//                                     fontSize: 12.sp,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ))
//                     ],
//                   ),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Số lô:",
//                       trailing: _getBatch != null  ? _getBatch!.batchNumber ?? " ": " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Ngày sản xuất:",
//                       trailing: _dataRawMeterial!.manufacturingDateStr ?? " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Tồn kho SAP:",
//                       trailing: _isLoadingInventory == true  ? "Loading":_inventorySAPApi != null ?_inventorySAPApi!.quantity != null ?_inventorySAPApi!.quantity!.round().toString(): " ": " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "Số lượng thực chuyển:",
//                       trailing: _dataQuantityExported != null ? _dataQuantityExported!.quantity != null ?_dataQuantityExported!.quantity!.round().toString(): " " : " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                   RowDetail(
//                       title: "ĐVT:",
//                       trailing: _dataQuantityExported != null ?_dataQuantityExported!.unit != null ?_dataQuantityExported!.unit.toString(): " ": " "),
//                   SizedBox(
//                     height: 10.h,
//                   ),
//                 ],
//               ),
//             ),
//             SizedBox(
//               height: 20.h,
//             ),
//             Align(
//               alignment: Alignment.centerRight,
//               child: Container(
//                 padding: EdgeInsets.symmetric(horizontal: 10.w),
//                 width: double.infinity,
//                 decoration: const BoxDecoration(),
//                 child: ElevatedButton(
//                   style: ButtonStyle(
//                     shape: MaterialStateProperty.all<
//                         RoundedRectangleBorder>(
//                         RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(5.r),
//                             side: const BorderSide(color: Colors.white))),
//                     side: MaterialStateProperty.all(
//                       const BorderSide(
//                         color: Color(0xff0052cc),
//                       ),
//                     ),
//                     backgroundColor: MaterialStateProperty.all(
//                         const Color(0xff0052cc)),
//                   ),
//                   onPressed: () {
//                     _checkError();
//                     if (_errorSelectedSlocInput == false) {
//                       if (_errorInventorySAP != true) {
//                           WarehouseTranferImportModel warehouseTranferImport = WarehouseTranferImportModel(
//                               reservationId: widget.dataListWarehouseTranfer
//                                   .reservationId,
//                               slocImportId: _selectedSlocInput!.slocId,
//                               storageBinImportId: _selectedSlocInput!
//                                   .defaultStorageBinId
//                           );
//                           WareHouseTranferImportFunction
//                               .sendWarehouseTranferImport(
//                               warehouseTranferImport,
//                               widget.token,
//                               context,
//                               widget.plant);
//
//                       }else{
//                         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                             backgroundColor: Colors.black,
//                             content: Text(
//                               'Số tồn kho sap không được trống',
//                               style: TextStyle(fontSize: 15.sp, color: Colors.white),
//                             ),
//                             duration: const Duration(seconds: 2)));
//                       }
//                     }
//                   },
//                   child: Container(
//                     margin: EdgeInsets.symmetric(
//                         vertical: 12.h, horizontal: 15.w),
//                     child: Text(
//                       "Xuất",
//                       style: TextStyle(
//                           color: Colors.white,
//                           fontWeight: FontWeight.bold,
//                           fontSize: 13.sp),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 20.h,
//             ),
//           ],
//         ),
//       ),
//     ));
//   }
// }
