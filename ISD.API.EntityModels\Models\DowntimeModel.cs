﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("DowntimeModel", Schema = "MES")]
    public partial class DowntimeModel
    {
        public DowntimeModel()
        {
            DowntimeHistoryModel = new HashSet<DowntimeHistoryModel>();
        }

        [Key]
        public Guid DowntimeId { get; set; }
        [Required]
        [StringLength(10)]
        public string Date { get; set; }
        [Required]
        [StringLength(200)]
        public string DepartmentCode { get; set; }
        [Required]
        [StringLength(50)]
        public string StepCode { get; set; }
        [Required]
        [StringLength(8)]
        public string StartTime { get; set; }
        [StringLength(8)]
        public string EndTime { get; set; }
        [Required]
        [StringLength(500)]
        public string Reason { get; set; }
        [Required]
        [StringLength(100)]
        public string ResponsibleTeam { get; set; }
        [StringLength(100)]
        public string ResponsibleDepartment { get; set; }
        [StringLength(500)]
        public string PersonCausedDowntimeCodeMany { get; set; }
        [StringLength(500)]
        public string Note { get; set; }
        [StringLength(20)]
        public string Status { get; set; }
        [StringLength(20)]
        public string VerificationStatus { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedDate { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdateBy { get; set; }
        [StringLength(10)]
        public string CompanyCode { get; set; }
        [StringLength(10)]
        public string TDS { get; set; }

        [InverseProperty("Downtime")]
        public virtual ICollection<DowntimeHistoryModel> DowntimeHistoryModel { get; set; }
    }
}