﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("LogSAPModel", Schema = "system")]
    public partial class LogSAPModel
    {
        [Key]
        public long Id { get; set; }
        [StringLength(50)]
        public string Funtion { get; set; }
        [StringLength(200)]
        public string FuntionnName { get; set; }
        public string RequestSAP { get; set; }
        public string ResonponseSAP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
    }
}