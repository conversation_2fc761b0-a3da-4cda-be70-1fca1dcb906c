import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import 'input_formatters.dart';

class DecimalTextInputField extends StatelessWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType keyboardType;
  final String hintText;
  final void Function(String)? onChanged;
  final int? maxLines;
  final bool? enabled;
  final bool readOnly;

  DecimalTextInputField({
    Key? key,
    this.controller,
    this.focusNode,
    this.keyboardType = TextInputType.text,
    this.hintText = '',
    this.onChanged,
    this.maxLines,
    this.enabled,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: enabled,
      readOnly: readOnly,
      maxLines: maxLines,
      textAlign: TextAlign.center,
      controller: controller,
      focusNode: focusNode,
      style: TextStyle(fontSize: 12.sp),
      keyboardType: keyboardType,
      inputFormatters: [CommaToDotTextInputFormatter()],
      decoration: InputDecoration(
        border: InputBorder.none,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 0),
        errorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        filled: true,
        fillColor: Colors.white,
        hintStyle: TextStyle(fontSize: 12.sp),
        hintText: hintText,
      ),
      onChanged: onChanged,
    );
  }
}

/*
Usage:
  showAlert(
    context: context,
    title: 'Alert Title',
    content: 'Alert content',
    buttonsWithOnPressed: {
      'OK': () => Navigator.of(context).pop(),
      'Cancel': () => Navigator.of(context).pop(),
    },
  );
*/
void showAlert({
  required BuildContext context,
  required String title,
  required String content,
  required Map<String, VoidCallback> buttonsWithOnPressed,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: buttonsWithOnPressed.entries.map((element) {
          return TextButton(
            child: Text(element.key),
            onPressed: element.value,
          );
        }).toList(),
      );
    },
  );
}

void showMessage(BuildContext context, String title, String message) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}

void showToast({
  required BuildContext context,
  required String message,
  int duration = 1,
}) {
  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      backgroundColor: Colors.black,
      content: Text(
        message,
        style: TextStyle(fontSize: 15.sp, color: Colors.white),
      ),
      duration: Duration(seconds: duration)));
}

// Check token expried
/*

    if (!isTokenLive(widget.dateTimeOld)) {
      setState(() {
        _timeOut = true;
      });
      return;
    }

 */
bool isTokenLive(String oldDateString) {
  String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
  DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
  DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(oldDateString);

  return convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld);
}

Widget NotFoundView([String text = "Không có dữ liệu"]) {
  return Center(
    child: Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: REdgeInsets.all(30.0),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              width: 1.w,
              color: Colors.grey,
            ),
          ),
          child: Icon(Icons.notifications_none, size: 50.sp, color: Colors.grey),
        ),
        SizedBox(height: 20.h),
        Text(
          text,
          style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
        ),
      ],
    ),
  );
}

ElevatedButton DebugButton({
  required VoidCallback onPressed,
  required String buttonText,
}) {
  return ElevatedButton(
    style: ElevatedButton.styleFrom(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h), // Adjust padding here
      minimumSize: const Size(0, 0), // Add this
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0.0),
      ),
    ),
    onPressed: onPressed,
    child: Text(buttonText, style: TextStyle(fontSize: 9.sp)),
  );
}

typedef OnPressed = Function(Map<String, dynamic> item);

Widget RenderDebugButtons(List<Map<String, dynamic>> items, OnPressed onPressed, String displayProperty) {
  return Wrap(
    alignment: WrapAlignment.start,
    runAlignment: WrapAlignment.start,
    spacing: 2.w,
    runSpacing: 0.h,
    children: items.map((item) {
      return ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 0, maxHeight: 20),
        child: DebugButton(
          onPressed: () => onPressed(item),
          buttonText: item[displayProperty].toString(),
        ),
      );
    }).toList(),
  );
}

enum RadioValue { dat, khongDat, khongKiemTra }

String getOutcomeStatus(RadioValue value) {
  switch (value) {
    case RadioValue.dat:
      return 'PASS';
    case RadioValue.khongDat:
      return 'FAIL';
    case RadioValue.khongKiemTra:
      return 'SKIP';
    default:
      return 'SKIP'; // or throw an error if an unknown value is encountered
  }
}

RadioValue getRadioValue(String? status) {
  switch (status) {
    case 'PASS':
      return RadioValue.dat;
    case 'FAIL':
      return RadioValue.khongDat;
    case 'SKIP':
      return RadioValue.khongKiemTra;
    default:
      return RadioValue.khongKiemTra; // or throw an error if an unknown status is encountered
  }
}

class RadioLabel extends StatelessWidget {
  final String text;

  const RadioLabel({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: const TextStyle(fontSize: 11),
      softWrap: true,
      overflow: TextOverflow.ellipsis,
    );
  }
}

class RadioHelper<T> extends StatefulWidget {
  final T groupValue;
  final T value;
  final String text;
  final bool enabled;
  final Function(T?) onRadioChanged;
  // final int index;

  RadioHelper({
    required this.groupValue,
    required this.value,
    required this.text,
    required this.enabled,
    required this.onRadioChanged,
    // required this.index,
  });

  @override
  _RadioHelperState<T> createState() => _RadioHelperState<T>();
}

class _RadioHelperState<T> extends State<RadioHelper<T>> {
  // T? _groupValue;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        children: <Widget>[
          Radio<T>(
            visualDensity: const VisualDensity(horizontal: -4.0, vertical: -4.0),
            value: widget.value,
            groupValue: widget.groupValue,
            onChanged: widget.enabled
                ? (T? newValue) {
                    // setState(() {
                    //   _groupValue = newValue!;
                    // });
                    widget.onRadioChanged(newValue);
                  }
                : null,
            activeColor: const Color(0xff0052cc),
          ),
          GestureDetector(
            onTap: widget.enabled
                ? () {
                    // setState(() {
                    //   _groupValue = widget.groupValue;
                    // });
                    widget.onRadioChanged(widget.value);
                  }
                : null,
            child: RadioLabel(text: widget.text),
            // child: Text(widget.po != null ? widget.po.toString() : ''),
          ),
        ],
      ),
    );
  }
}
