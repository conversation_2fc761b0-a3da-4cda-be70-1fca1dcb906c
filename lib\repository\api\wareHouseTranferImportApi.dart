import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/warehouseTranferImport.dart';
import '../../urlApi/urlApi.dart';

class WareHouseTranferImportApi {
  static Future<http.Response> postWareHouseTranferImport(WarehouseTranferImportModel warehouseTranferImport, String token) async {
    final dataPost = jsonEncode(warehouseTranferImport);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "WarehouseTranferImport");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> getQuantityExported(String reservationId, String token) async {
    Map<String, dynamic> data = {"ReservationId": reservationId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetQuantityExported", data);
    debugPrint(url.toString());
    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
