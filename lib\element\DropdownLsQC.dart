import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../model/FilterLsQC.dart';
import '../model/getStatusGoodsArrive.dart';
import '../model/listQCNVL.dart';

class DropdownSalesOrgCodes extends StatelessWidget {
  final String title;
  final SalesOrgCodes? selectedSalesOrgCodes;
  final ValueChanged<SalesOrgCodes?> onTap;
  final List<SalesOrgCodes>? lsSalesOrgCodes;
  const DropdownSalesOrgCodes(
      {Key? key, required this.onTap, required this.selectedSalesOrgCodes, required this.lsSalesOrgCodes, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
              child: DropdownButton<SalesOrgCodes>(
            isDense: true,
            itemHeight: null,
            isExpanded: true,
            value: selectedSalesOrgCodes,
            iconSize: 15.sp,
            style: const TextStyle(color: Colors.black),
            onChanged: onTap,
            items: lsSalesOrgCodes?.map((SalesOrgCodes code) {
              return DropdownMenuItem<SalesOrgCodes>(
                  value: code,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 5.h),
                    child: Text(
                      code.storeName.toString(),
                      style: TextStyle(color: Colors.black, fontSize: 11.sp),
                    ),
                  ));
            }).toList(),
            selectedItemBuilder: (BuildContext context) {
              return lsSalesOrgCodes?.map<Widget>((SalesOrgCodes code) {
                    return Text(code.storeName.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                  }).toList() ??
                  [];
            },
          )),
        ),
      ],
    );
  }
}

class DropdownworkCenters extends StatelessWidget {
  final String title;
  final WorkCenters? selectedWorkCenters;
  final ValueChanged<WorkCenters?> onTap;
  final List<WorkCenters>? lsWorkCenters;
  const DropdownworkCenters({Key? key, required this.selectedWorkCenters, required this.onTap, required this.lsWorkCenters, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<WorkCenters>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedWorkCenters,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsWorkCenters!.map((WorkCenters Name) {
                return DropdownMenuItem<WorkCenters>(
                    value: Name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        Name.workCenterName.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsWorkCenters!.map<Widget>((WorkCenters Name) {
                  return Text(Name.workCenterName.toString(),
                      style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownworkShops extends StatelessWidget {
  final String title;
  final WorkShops? selectedWorkShops;
  final ValueChanged<WorkShops?> onTap;
  final List<WorkShops>? lsWorkShops;
  const DropdownworkShops({Key? key, required this.selectedWorkShops, required this.onTap, required this.lsWorkShops, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<WorkShops>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedWorkShops,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsWorkShops!.map((WorkShops name) {
                return DropdownMenuItem<WorkShops>(
                    value: name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        name.workShopName.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsWorkShops!.map<Widget>((WorkShops name) {
                  return Text(name.workShopName.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownCommonDates extends StatelessWidget {
  final String title;
  final CommonDates? selectedCommonDates;
  final ValueChanged<CommonDates?> onTap;
  final List<CommonDates>? lsWorkCommonDates;
  const DropdownCommonDates({Key? key, required this.selectedCommonDates, required this.onTap, required this.lsWorkCommonDates, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<CommonDates>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedCommonDates,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsWorkCommonDates?.map((CommonDates name) {
                return DropdownMenuItem<CommonDates>(
                    value: name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        name.catalogTextVi.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsWorkCommonDates?.map<Widget>((CommonDates name) {
                      return Text(name.catalogTextVi.toString(),
                          style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                    }).toList() ??
                    [];
              },
            ),
          ),
        ),
      ],
    );
  }
}

class Dropdownresults extends StatelessWidget {
  final String title;
  final ResultsDataQC? selectedResultsDataQC;
  final ValueChanged<ResultsDataQC?> onTap;
  final List<ResultsDataQC>? lsResultsDataQC;
  const Dropdownresults({Key? key, required this.selectedResultsDataQC, required this.onTap, required this.lsResultsDataQC, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ResultsDataQC>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedResultsDataQC,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsResultsDataQC!.map((ResultsDataQC name) {
                return DropdownMenuItem<ResultsDataQC>(
                    value: name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        name.catalogTextVi.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsResultsDataQC!.map<Widget>((ResultsDataQC name) {
                  return Text(name.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownStatusData extends StatelessWidget {
  final String title;
  final StatusData? selectedStatusData;
  final ValueChanged<StatusData?> onTap;
  final List<StatusData>? lsStatusData;
  const DropdownStatusData({Key? key, required this.selectedStatusData, required this.onTap, required this.lsStatusData, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<StatusData>(
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedStatusData,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsStatusData!.map((StatusData name) {
                return DropdownMenuItem<StatusData>(
                    value: name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        name.nameStatus.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsStatusData!.map<Widget>((StatusData name) {
                  return Text(name.nameStatus.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownTimeQC extends StatelessWidget {
  final String title;
  final CommonDates? selectedTimeQC;
  final ValueChanged<CommonDates?> onTap;
  final List<CommonDates>? lsTimeQC;
  const DropdownTimeQC({Key? key, required this.selectedTimeQC, required this.onTap, required this.lsTimeQC, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<CommonDates>(
              dropdownColor: Colors.white,
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedTimeQC,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsTimeQC!.map((CommonDates name) {
                return DropdownMenuItem<CommonDates>(
                    value: name,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        name.catalogTextVi.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsTimeQC!.map<Widget>((CommonDates name) {
                  return Text(name.catalogTextVi.toString(), style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

// class DropdownTimeCreateBarcode extends StatelessWidget {
//   final String title;
//   final String? selectedTimeCreateBarcode;
//   final ValueChanged<String?> onTap;
//   final List<String> lsTimeCreateBarcode;
//   const DropdownTimeCreateBarcode({Key? key,required this.selectedTimeCreateBarcode, required this.onTap,required this.lsTimeCreateBarcode, required this.title }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: <Widget> [
//         Text(
//           title+":",
//           style: TextStyle(
//             fontSize: 12.sp,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         SizedBox(height: 5.h),
//         Container(
//           padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
//           decoration: BoxDecoration(
//             border: Border.all(
//                 color: Colors.grey.shade400,
//                 width: 0.5.w),
//             borderRadius:
//             BorderRadius.circular(3.r),
//           ),
//           child: DropdownButtonHideUnderline(
//             child: DropdownButton<String>(
//               dropdownColor: Colors.white,
//               isExpanded: true,
//               itemHeight: null,
//               isDense: true,
//               value: selectedTimeCreateBarcode,
//               iconSize: 15.sp,
//               style: const TextStyle(color: Colors.black),
//               onChanged: onTap,
//               items: lsTimeCreateBarcode.map<DropdownMenuItem<String>>((String time) {
//                 return DropdownMenuItem<String>(
//                     value: time,
//                     child: Padding(
//                       padding: EdgeInsets.symmetric(vertical: 5.h),
//                       child: Text(
//                         time,
//                         style: TextStyle(
//                             color: Colors.black,
//                             fontSize: 11.sp),
//                       ),
//                     ));
//               }).toList(),
//               selectedItemBuilder: (BuildContext context) {
//                 return lsTimeCreateBarcode.map<Widget>((String time) {
//                   return Text( time,
//                       style: TextStyle(
//                       color: Colors.black,
//                       fontSize: 11.sp), overflow: TextOverflow.ellipsis);
//                 }).toList();
//               },
//             ),),),
//       ],
//     );
//   }
// }

class DropdownDeliveryStatus extends StatelessWidget {
  final String title;
  final DeliveryStatus? selectedDeliveryStatus;
  final ValueChanged<DeliveryStatus?> onTap;
  final List<DeliveryStatus> lsDeliveryStatus;
  const DropdownDeliveryStatus(
      {Key? key, required this.selectedDeliveryStatus, required this.onTap, required this.lsDeliveryStatus, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DeliveryStatus>(
              dropdownColor: Colors.white,
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedDeliveryStatus,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsDeliveryStatus.map<DropdownMenuItem<DeliveryStatus>>((DeliveryStatus delivery) {
                return DropdownMenuItem<DeliveryStatus>(
                    value: delivery,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        delivery.value!,
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsDeliveryStatus.map<Widget>((DeliveryStatus delivery) {
                  return Text(delivery.value!, style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownStatusQC extends StatelessWidget {
  final String title;
  final QCStatus? selectedStatusQC;
  final ValueChanged<QCStatus?> onTap;
  final List<QCStatus> lsStatusQC;
  const DropdownStatusQC({Key? key, required this.selectedStatusQC, required this.onTap, required this.lsStatusQC, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<QCStatus>(
              dropdownColor: Colors.white,
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedStatusQC,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsStatusQC.map<DropdownMenuItem<QCStatus>>((QCStatus status) {
                return DropdownMenuItem<QCStatus>(
                    value: status,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        status.value!,
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsStatusQC.map<Widget>((QCStatus status) {
                  return Text(status.value!, style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownResult extends StatelessWidget {
  final String title;
  final ResultListQCNVL? selectedResult;
  final ValueChanged<ResultListQCNVL?> onTap;
  final List<ResultListQCNVL> lsResult;
  const DropdownResult({Key? key, required this.selectedResult, required this.onTap, required this.lsResult, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ResultListQCNVL>(
              dropdownColor: Colors.white,
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedResult,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsResult.map<DropdownMenuItem<ResultListQCNVL>>((ResultListQCNVL result) {
                return DropdownMenuItem<ResultListQCNVL>(
                    value: result,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        result.value.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsResult.map<Widget>((ResultListQCNVL result) {
                  return Text(result.value!, style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DropdownFactoryReturnDate extends StatelessWidget {
  final String title;
  final DataGetStatusGoodsArrive? selectedFactoryReturnDate;
  final ValueChanged<DataGetStatusGoodsArrive?> onTap;
  final List<DataGetStatusGoodsArrive> lsDataGetStatusGoodArrive;
  const DropdownFactoryReturnDate(
      {Key? key, required this.selectedFactoryReturnDate, required this.onTap, required this.lsDataGetStatusGoodArrive, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title + ":",
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DataGetStatusGoodsArrive>(
              dropdownColor: Colors.white,
              isExpanded: true,
              itemHeight: null,
              isDense: true,
              value: selectedFactoryReturnDate,
              iconSize: 15.sp,
              style: const TextStyle(color: Colors.black),
              onChanged: onTap,
              items: lsDataGetStatusGoodArrive.map<DropdownMenuItem<DataGetStatusGoodsArrive>>((DataGetStatusGoodsArrive status) {
                return DropdownMenuItem<DataGetStatusGoodsArrive>(
                    value: status,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.h),
                      child: Text(
                        status.value.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 11.sp),
                      ),
                    ));
              }).toList(),
              selectedItemBuilder: (BuildContext context) {
                return lsDataGetStatusGoodArrive.map<Widget>((DataGetStatusGoodsArrive status) {
                  return Text(status.value!, style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                }).toList();
              },
            ),
          ),
        ),
      ],
    );
  }
}
