﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PlantRoutingConfigModel", Schema = "MES")]
    public partial class PlantRoutingConfigModel
    {
        [Key]
        public int PlantRoutingCode { get; set; }
        [StringLength(100)]
        public string PlantRoutingName { get; set; }
        [StringLength(50)]
        public string PlantRoutingGroup { get; set; }
        [StringLength(50)]
        public string FromData { get; set; }
        [StringLength(2000)]
        public string Attribute1 { get; set; }
        [StringLength(2000)]
        public string Attribute2 { get; set; }
        public bool? LeadTimeType { get; set; }
        public int? LeadTime { get; set; }
        [StringLength(2000)]
        public string FromDate { get; set; }
        [StringLength(2000)]
        public string ToDate { get; set; }
        [StringLength(2000)]
        public string Condition { get; set; }
        public int? OrderIndex { get; set; }
        public bool? Actived { get; set; }
        [StringLength(100)]
        public string CreatedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedTime { get; set; }
        [StringLength(100)]
        public string LastModifiedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedTime { get; set; }
        [StringLength(2000)]
        public string Attribute3 { get; set; }
        [StringLength(2000)]
        public string Attribute4 { get; set; }
        [StringLength(2000)]
        public string Attribute5 { get; set; }
        [StringLength(2000)]
        public string Attribute6 { get; set; }
        [StringLength(2000)]
        public string Attribute7 { get; set; }
        [StringLength(2000)]
        public string Attribute8 { get; set; }
        [StringLength(2000)]
        public string Attribute9 { get; set; }
        [StringLength(2000)]
        public string Attribute10 { get; set; }
        public bool? IsPrimaryStep { get; set; }
        [StringLength(4000)]
        public string DescriptionFromDate { get; set; }
        [StringLength(4000)]
        public string DescriptionToDate { get; set; }
        [StringLength(4000)]
        public string DescriptionCondition { get; set; }
        [StringLength(2000)]
        public string LeadTimeFormula { get; set; }
    }
}