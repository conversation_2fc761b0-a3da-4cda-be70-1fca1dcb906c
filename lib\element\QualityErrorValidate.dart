import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QualityErrorValidate extends StatelessWidget {
  final bool error;
  final String text;
  const QualityErrorValidate({Key? key, required this.error, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
        visible: error,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Flexible(
              flex: 1,
              child: Icon(Icons.error_outline, size: 13.sp, color: Colors.red[700]),
            ),
            SizedBox(width: 5.w),
            Flexible(flex: 8, child: Text(text, style: TextStyle(fontSize: 11.sp, color: Colors.red[700])))
          ],
        ));
  }
}
