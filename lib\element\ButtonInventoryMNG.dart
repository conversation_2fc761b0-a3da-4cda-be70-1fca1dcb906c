import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Component category class to set list
class ButtonInventoryMNG extends StatelessWidget {
  final String txt;
  final GestureTapCallback route;
  final Icon icon;

  const ButtonInventoryMNG({Key? key, required this.txt, required this.route, required this.icon}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: route,
      child: Container(
        padding: REdgeInsets.all(5),
        decoration:  BoxDecoration(
          color: Colors.white,
          border: Border.all(width: 0.5.w, color: const Color(0xff0052cc)),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          children: <Widget> [
            Expanded(
              flex:1,
              child:  CircleAvatar(
                backgroundColor: const Color(0xff0052cc),
                child: icon,
              ),
            ),
            SizedBox(width: 5.w),
            Expanded(
              flex: 8,
              child:Center(
                child: Text(
                    txt, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: const Color(0xff0052cc)),
                  ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Icon(Icons.arrow_forward_ios_rounded, size: 15.sp, color: const Color(0xff0052cc)),
            ),
          ],
        ),
      ),
    );
  }
}