class GetDefectLevel {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetDefectLevel>? data;

  GetDefectLevel(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetDefectLevel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetDefectLevel>[];
      json['data'].forEach((v) {
        data!.add(DataGetDefectLevel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetDefectLevel {
  String? key;
  String? value;

  DataGetDefectLevel({this.key, this.value});

  DataGetDefectLevel.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}
