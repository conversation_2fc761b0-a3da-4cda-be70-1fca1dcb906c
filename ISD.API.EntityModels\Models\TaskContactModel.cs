﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskContactModel", Schema = "Task")]
    public partial class TaskContactModel
    {
        [Key]
        public Guid TaskContactId { get; set; }
        public Guid? TaskId { get; set; }
        public Guid? ContactId { get; set; }
        public bool? isMain { get; set; }
        [StringLength(200)]
        public string Note { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
    }
}