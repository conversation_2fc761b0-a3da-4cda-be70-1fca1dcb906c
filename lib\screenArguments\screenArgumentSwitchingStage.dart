import '../model/productionRecord.dart';
import '../model/switchingStagesApi.dart';

class ScreenArgumentSwitchingStage {
  final DataSwitchingStages? switchingStateData;
  final String token;
  final SwitchingStages? switchingStages;
  final String dateTimeOld;
  final String toBarcode;


  ScreenArgumentSwitchingStage(this.switchingStateData,this.token,this.switchingStages, this.dateTimeOld, this.toBarcode);
}