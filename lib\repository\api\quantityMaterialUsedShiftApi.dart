import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class QuantityMaterialUsedShiftApi {
  static Future<http.Response> getQuantityMaterialUsedShift(String rawMaterialCardId, String token) async {
    Map<String, dynamic> data = {"RawMaterialCardId": rawMaterialCardId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetQuantityMaterialUsedShift", data);
    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
