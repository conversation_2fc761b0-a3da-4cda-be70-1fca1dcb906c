﻿using AutoMapper;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Repositories;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class DowntimeTestController : ControllerBaseAPI
    {

        // declare _mapper as AutoMapper
        private readonly IMapper _mapper;

        public DowntimeTestController(IMapper mapper)
        {
            _mapper = mapper;
        }

        public static List<DowntimeModel> GetDummyData()
        {
            return new List<DowntimeModel>
            {
                new DowntimeModel
                {
                    DowntimeId = Guid.NewGuid(),
                    StepCode = "STP001",
                },
                new DowntimeModel
                {
                    DowntimeId = Guid.NewGuid(),
                    StepCode = "STP002",
                }
            };
        }

        [HttpGet("GetDowntimeList")]
        public async Task<IActionResult> GetDowntimeList([FromQuery] DowntimeSearchModel searchModel)
        {
            return Ok(new ApiResponse
            {
                Code = (int)HttpStatusCode.OK,
                IsSuccess = true,
                Message = "Debug data",
                Data = GetDummyData()
            });
        }
    }
}