﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Keyless]
    public partial class View_BOM_Inventor_Rip
    {
        [StringLength(50)]
        public string PART_ID { get; set; }
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(50)]
        public string IDNRK_MES { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? P2 { get; set; }
        [StringLength(500)]
        public string POT12 { get; set; }
        [StringLength(500)]
        public string POT21 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? MENGE { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [StringLength(10)]
        public string VERSO { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? P1 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? P3 { get; set; }
        [Column("Row#")]
        public long? Row_ { get; set; }
    }
}