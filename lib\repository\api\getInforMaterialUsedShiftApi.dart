import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../model/materialUnused.dart';
import '../../urlApi/urlApi.dart';
import 'package:http/http.dart' as http;

class GetInforMaterialUsedShiftApi {
  static Future<http.Response> getInforMaterialUsedShiftApi(String rawMaterialCardId, String token) async {
    Map<String, dynamic> data = {"RawMaterialCardId": rawMaterialCardId};
    if (kDebugMode) {
      print(data);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetInforMaterialUsedShift", data);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> postMaterialUnused(MaterialUnusedModel materialUnused, String token) async {
    final dataPost = jsonEncode(materialUnused);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "MaterialUnused");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }
}
