import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../Widget/dialogWidget/DialogErrorValidate.dart';
import '../../model/dataSlocAddressList.dart';
import '../../model/dataStepCodeList.dart';
import '../../model/getSOWBSByTask.dart';
import '../../model/getSlocByProduct.dart';
import '../../model/materialRetailList.dart';
import '../../model/postMaterialRetail.dart';
import '../api/materialRetailApi.dart';

class MaterialRetailFunction {
  static Future<DataGetSOWBSByTask?> fetchSOWBSByTask(String taskId, String token) async {
    final response = await MaterialRetailApi.getSOWBSByTask(taskId, token);
    if (response.statusCode == 200) {
      final getData = GetSOWBSByTask.fromJson(jsonDecode(response.body));
      if (getData.code == 200 && getData.isSuccess == true) {
        return getData.data;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<List<DataGetSlocByProduct>?> fetchSlocByProduct(String productCode, String token) async {
    final response = await MaterialRetailApi.getSlocByProduct(productCode, token);
    if (response.statusCode == 200) {
      if (jsonDecode(response.body)['data'] != false) {
        final getData = GetSlocByProduct.fromJson(jsonDecode(response.body));
        if (getData.code == 200 && getData.isSuccess == true) {
          return getData.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  // static Future<String?> getMaterialRetailMessage(PostMaterialRetail postMaterialRetail, String token) async {
  //   final response = await MaterialRetailApi.postMaterialRetailApi(postMaterialRetail, token);
  //
  //   // if (response.statusCode == 200) {
  //   //   final getMessage = MessagePostMaterialRetail.fromJson(jsonDecode(response.body));
  //   //   if (getMessage.code == 200 && getMessage.isSuccess == true) {
  //   //     return getMessage.message;
  //   //   } else {
  //   //     return null;
  //   //   }
  //   // } else {
  //   //   return null;
  //   // }
  // }
  static Future<void> sendMaterialRetail(PostMaterialRetail postMaterialRetail, String token, BuildContext context) async {
    try {
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final response = await MaterialRetailApi.postMaterialRetailApi(postMaterialRetail, token);
      if (response.statusCode == 200) {
        final getMessage = MessagePostMaterialRetail.fromJson(jsonDecode(response.body));
        // final message = await getMaterialRetailMessage(postMaterialRetail, token);
        Navigator.pop(context);
        if (getMessage.code == 200 && getMessage.isSuccess == true) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              backgroundColor: Colors.black,
              content: Text(
                getMessage.message.toString(),
                style: TextStyle(fontSize: 15.sp, color: Colors.white),
              ),
              duration: const Duration(seconds: 2)));
          Future.delayed(const Duration(seconds: 0), () {
            Navigator.pop(context, true);
          });
        } else {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) => DiaLogErrorValidate(message: getMessage.message.toString()));
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     backgroundColor: Colors.black,
          //     content: Text(
          //         getMessage.message.toString(),
          //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
          //     ),
          //     duration: const Duration(seconds: 2)));
        }
      } else {
        showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: response.body.toString()));
        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //     backgroundColor: Colors.black,
        //     content: Text(
        //       "Hệ thống xày ra lỗi! Vui lòng thử lại sau",
        //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
        //     ),
        //     duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      Navigator.pop(context);
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Ứng dụng xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static List<MaterialRetailDetails>? getMaterialRetailDetails(
      List<TextEditingController> lsTextEditingController,
      List<MaterialRetailList> lsMaterialRetailList,
      List<DataSlocQuantityUsedShift> lsDataSlocAddressList,
      DataGetSOWBSByTask? dataGetSOWBSByTask,
      List<DataStepCodeList> lsDataStepCodeList) {
    List<MaterialRetailDetails>? lsMaterialRetailDetails = [];
    for (int i = 0; i < lsTextEditingController.length; i++) {
      MaterialRetailDetails materialRetailDetails = MaterialRetailDetails(
          slocId: lsDataSlocAddressList[i].selectedDataQuantityMaterialUsedShift!.slocId,
          so: dataGetSOWBSByTask!.so == "" ? null : dataGetSOWBSByTask.so,
          soLine: dataGetSOWBSByTask.soLine == "" ? null : dataGetSOWBSByTask.soLine,
          wbs: dataGetSOWBSByTask.wbs == "" ? null : dataGetSOWBSByTask.wbs,
          batchNumber: lsMaterialRetailList[i].batchNumber,
          rawMaterialCardId: lsMaterialRetailList[i].rawMaterialCardId,
          quantityUsed: lsDataSlocAddressList[i].selectedDataQuantityMaterialUsedShift!.quantity,
          unitQuantityUsed: lsDataSlocAddressList[i].selectedDataQuantityMaterialUsedShift!.unit,
          quantity: double.parse(lsTextEditingController[i].text),
          unit: lsDataSlocAddressList[i].selectedDataQuantityMaterialUsedShift!.unit,
          stepCode: lsDataStepCodeList[i].selectedStepCode!.stepCode);
      lsMaterialRetailDetails.add(materialRetailDetails);
    }
    return lsMaterialRetailDetails;
  }
}
