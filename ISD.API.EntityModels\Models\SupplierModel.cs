﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SupplierModel", Schema = "tMasterData")]
    public partial class SupplierModel
    {
        [Key]
        public Guid Id { get; set; }
        [StringLength(50)]
        public string SupplierNumber { get; set; }
        [StringLength(50)]
        public string ShortName { get; set; }
        [StringLength(200)]
        public string LongName { get; set; }
        [StringLength(500)]
        public string Address { get; set; }
        [StringLength(20)]
        public string Telephone { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
    }
}