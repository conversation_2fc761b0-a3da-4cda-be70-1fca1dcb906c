import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetStepByProductBatchApi {
  static Future<http.Response> getStepByProductBatchApi(String productCode, String batchNumber, String token) async {
    Map<String, dynamic> data = {"ProductCode": productCode, "BatchNumber": batchNumber};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString =
        '${baseUrl}/${UrlApi.baseUrlWarehousTransaction_2}GetStepByProductBach?ProductCode=${Uri.encodeQueryComponent(data['ProductCode'].toString())}&BatchNumber=${Uri.encodeQueryComponent(data['BatchNumber'].toString())}';
    final url = Uri.parse(urlString);

    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
