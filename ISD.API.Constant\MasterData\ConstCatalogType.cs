﻿namespace ISD.API.Constant
{
    public static class ConstCatalogType
    {
        //Loại địa chỉ
        public const string AddressType = "AddressType";

        //Quốc gia
        public const string Country = "Country";

        //Ngành nghề
        public const string CustomerCareer = "CustomerCareer";

        //Nhóm KH doanh nghiệp
        public const string CustomerCategory = "CustomerCategory";

        //Nhóm KH (SAP)
        public const string CustomerGroup = "CustomerGroup";

        //Nguồn KH doanh nghiệp
        public const string CustomerSource = "CustomerSource";

        //Loại khách hàng
        public const string CustomerType = "CustomerType";
        //Loại doanh thu
        public const string CustomerAccountAssignmentGroup = "CustomerAccountAssignmentGroup";

        //Loại file
        public const string FileType = "FileType";

        //Đối tác
        public const string PartnerType = "PartnerType";

        //Đi<PERSON>u khoản thanh toán
        public const string PaymentTerm = "PaymentTerm";

        //Tài khoản công nợ
        public const string ReconcileAccount = "ReconcileAccount";

        //Nhóm khách hàng
        public const string ProfileGroup = "ProfileGroup";

        //Nhiệm vụ
        public const string Mission = "Mission";

        //Độ tuổi
        public const string Age = "Age";

        //Vùng
        public const string SaleOffice = "SaleOffice";

        //Vai trò
        public const string Role = "Role";

        //Mức độ
        public const string Priority = "Priority";

        //Lối thường gặp
        public const string CommonMistake = "CommonMistake";

        //Showroom
        //public const string Showroom = "Showroom";

        //Danh mục ghé thăm
        public const string Appoitment_Category = "Appoitment_Category";

        //Kênh ghé thăm
        public const string Appoitment_Channel = "Appoitment_Channel";

        //Phân loại khách hàng
        public const string CustomerClass = "CustomerClass";

        //Giai doan
        public const string Process = "Process";

        //CommonDate
        public const string CommonDate = "CommonDate";
        public const string CommonDate2 = "CommonDate2";

        //SMS Template
        public const string SMSTemplate = "SMSTemplate";

        //Danh xưng
        public const string Title = "Title";

        //TaskAssignType
        public const string TaskAssignType = "TaskAssignType";

        //ConstructionUnit
        //Đơn vị thi công
        public const string ConstructionUnit = "ConstructionUnit";

        //ServiceTechnicalTeam
        public const string ServiceTechnicalTeam = "ServiceTechnicalTeam";

        //ErrorType
        public const string ErrorType = "ErrorType";

        //Error
        public const string Error = "Error";

        //VisitType
        public const string VisitType = "VisitType";

        //Chức vụ
        public const string Position = "Position";

        //Phòng ban
        public const string Department = "Department";

        //Loại phụ kiện
        public const string ProductAccessoryType = "ProductAccessoryType";

        //Phân loại quyền xem công việc
        public const string TaskFilter = "TaskFilter";

        //Chu kỳ nhắc nhở
        public const string RemindCycle = "RemindCycle";

        //Phân cấp SP
        public const string ProductLevel = "ProductLevel";

        //Mã màu SP
        public const string ProductColor = "ProductColor";

        //Nhóm vật tư
        public const string ProductCategory = "ProductCategory";

        //Các lỗi bảo hành thường gặp
        public const string UsualError = "UsualError";

        //Nguồn tiếp nhận TaskSource
        public const string TaskSource = "TaskSource";

        //Yêu cầu cần xử lý
        public const string HasRequest = "HasRequest";

        //Phân loại quyền xem dữ liệu
        public const string ViewPermission = "ViewPermission";

        //Trạng thái dự án
        public const string ProjectStatus = "ProjectStatus";

        //Mức độ xác định
        public const string QualificationLevel = "QualificationLevel";

        //Danh mục cơ hội bán hàng
        public const string OpportunityCategory = "OpportunityCategory";

        //Xác suất
        public const string OpportunityPercentage = "OpportunityPercentage";

        //Loại hình
        public const string OpportunityType = "OpportunityType";

        //Quy mô
        public const string ProjectScale = "ProjectScale";

        //Trạng thái cơ hội bán hàng
        public const string OpportunityStatus = "OpportunityStatus";

        //Nguồn thông tin 
        public const string ProjectSource = "ProjectSource";

        //ĐVT
        public const string Unit = "Unit";

        //Ý kiến khách hàng (Ratings)
        public const string CustomerReviews = "CustomerReviews";

        //Nhóm vân gỗ
        public const string WoodGrainCategory = "WoodGrainCategory";

        //Dòng sản phẩm
        public const string ProductGroup = "ProductGroup";

        //Bộ sưu tập
        public const string ProductCollection = "ProductCollection";

        //Đánh giá bộ sưu tập
        public const string CustomerTastes = "CustomerTastes";

        //Phân nhóm khách hàng
        public const string CustomerAccountGroup = "CustomerAccountGroup";

        //Phuương thức gửi
        public const string ShippingTypeCode = "ShippingTypeCode";

        //Phuương thức thanh toán
        public const string PaymentMethod = "PaymentMethod";

        //Vai trò trong giao dịch
        public const string PartnerFunction = "PartnerFunction";

        //Đơn vị tiền tệ
        public const string Currency = "Currency"; 

        //Phân loại thuế VAT
        public const string TaxClassification = "TaxClassification";


        //Hài lòng khách hàng
        public const string CustomerSatisfaction = "CustomerSatisfaction";

        //Bảo hành - Ý kiến khách hàng - Đánh giá theo sao
        //1. Về sản phẩm
        public const string Ticket_CustomerReviews_Product = "Ticket_CustomerReviews_Product";
        //2. Về dịch vụ
        public const string Ticket_CustomerReviews_Service = "Ticket_CustomerReviews_Service";

        //ĐVT của quy mô
        public const string OpportunityUnit = "OpportunityUnit";

        //Nội thất bàn giao
        public const string HandoverFurniture = "HandoverFurniture";

        //Nơi tham quan
        public const string VisitPlace = "VisitPlace";

        //Đánh giá nhân viên
        public const string EmployeeRatings = "EmployeeRatings";

        public const string RequestEccConfig_ToEmail = "RequestEccConfig_ToEmail";
        public const string RequestEccConfig_Subject = "RequestEccConfig_Subject";

        //Đánh giá tầm cỡ
        public const string CaliberAssessment = "CaliberAssessment";

        //Danh sách spec và thi công
        public const string Opportunity_Industry = "Opportunity_Industry"; 
        public const string Spec = "Spec"; 
        public const string Construction = "Construction";

        //Đối thủ - Khu vực
        public const string Competitor_Area = "Competitor_Area";

        //Đối thủ - Lĩnh vực
        public const string Competitor_Field = "Competitor_Field";
        public const string VatLieu = "VatLieu";
        public const string SanGo = "SanGo";
        public const string PhuKien = "PhuKien";
        public const string ThietBi = "ThietBi";
        public const string SmartHome = "SmartHome";
        public const string ThiCong = "ThiCong";
        public const string ConLai = "ConLai";

        //Ngành hàng phân phối
        public const string DistributionIndustry = "DistributionIndustry";

        //Danh mục câu hỏi
        public const string QuestionCategory = "QuestionCategory";
        public const string QuestionDepartment = "QuestionDepartment";

        //Bảo hành_Kết quả
        public const string Ticket_Result = "Ticket_Result";

        //Vai trò (NV theo dõi/giám sát)
        public const string TaskReporterType = "TaskReporterType";

        //Dự án Khu vực
        public const string Opportunity_Region = "Opportunity_Region";

        //Tình trạng dự án
        public const string Opportunity_Status = "Opportunity_Status";

        //Tên dự án (tên ngắn)
        public const string ProfileShortName = "ProfileShortName";

        //Marketing 
        //Loại nội dung (EMAIL|SMS)
        public const string Marketing_Content = "Marketing_Content";
        public const string Email = "Email";
        public const string SMS = "SMS";
        //Loại email (Có sẵn|Cá nhân)
        public const string Marketing_Content_Email_Type = "Marketing_Content_Email_Type";
        public const string DefaultEmail = "DefaultEmail";
        public const string PersonalEmail = "PersonalEmail";

        //Cấu hình SMS
        public const string SMSConfig = "SMSConfig";
        //Status chiến dịch
        public const string Campaign_Planned = "Campaign_Planned";
        public const string Campaign_Actived = "Campaign_Actived";
        public const string Campaign_Finnished = "Campaign_Finnished";
        public const string Campaign_Pending = "Campaign_Pending";
        public const string CampaignStatus = "CampaignStatus";

        //Góp ý_Cấu hình email
        public const string FeedbackEmailConfig = "FeedbackEmailConfig";


        //Cấu hình email
        public const string EmailTemplateConfig = "EmailTemplateConfig";

        //Bảng tin _ Chủ đề
        public const string BANGTIN_SUMMARY = "BANGTIN_SUMMARY";

        //Bảng tin _ Chi tiết
        public const string BANGTIN_DETAIL = "BANGTIN_DETAIL";
        //Phương thức gửi
        public const string ProductPromotionSendType = "ProductPromotionSendType";
        //Thói quen dùng sản phẩm mới
        public const string HabitUsingNewProducts = "HabitUsingNewProducts";
        //Chỉ định dùng vật liệu An Cường
        public const string IndicationsUseACMaterial = "IndicationsUseACMaterial";

        //Phân loại tin tức
        public const string News_Type = "News_Type";

        //Phân loại tài trợ
        public const string Customer_Spons = "Customer_Spons";

    }
}