import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:multiple_search_selection/multiple_search_selection.dart';
import 'package:ttf/model/EmployeeVm.dart';
import 'package:ttf/model/PushNotificationVm.dart';
import 'package:ttf/repository/function/notificationFunction.dart';
import 'package:ttf/utils/ui_helpers.dart';
import 'package:ttf/utils/ui_utils.dart';

import '../model/RequestTraHangNCC.dart';
import '../model/userModel.dart';
import '../element/ButtonInventoryMNG.dart';
import '../repository/function/traHangNCCFunction.dart';
import '../screenArguments/screenArgumentMaterialUnused.dart';
import '../screenArguments/screenArgumentStatisticsMaterials.dart';
import '../screenArguments/screenArgumentsCreateNewTraHangNCC.dart';
import 'ListQCNVL.dart';

enum ActionButton { forwardButton }

class NotificationPage extends StatefulWidget {
  const NotificationPage({
    Key? key,
    required this.permission,
    required this.token,
    required this.plant,
    required this.dateTimeOld,
    required this.accountId,
  }) : super(
          key: key,
        );

  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;

  @override
  _NotificationPageState createState() => _NotificationPageState();
}

class CustomModalContent extends StatefulWidget {
  List<EmployeeVm> lsEmployeeMasterData = []; // Add this line
  final VoidCallback onForwardConfirmed;
  final accountId;
  final token;
  final notificationId;

  CustomModalContent({
    super.key,
    required this.lsEmployeeMasterData,
    required this.onForwardConfirmed,
    required this.token,
    required this.accountId,
    required this.notificationId,
  });

  @override
  State<CustomModalContent> createState() => _CustomModalContentState();
}

class _CustomModalContentState extends State<CustomModalContent> {
  List<EmployeeVm> selectedEmployees = [];

  Future<void> _onXacNhanPress() async {
    var ret = await NotificationFunction.postForward(widget.token.toString(), widget.accountId, widget.notificationId, selectedEmployees);

    Navigator.of(context).pop();
    Navigator.of(context).pop();

    debugPrint(ret.toString());

    showToast(context: context, message: 'Forward thành công');
    // Modal callback
    widget.onForwardConfirmed();
  }

  void _onForwardPress() {
    if (selectedEmployees.isEmpty) {
      showAlert(
        context: context,
        title: 'Thông báo',
        content: 'Vui lòng chọn ít nhất một nhân viên để forward thông báo',
        buttonsWithOnPressed: {
          'Ok': () => Navigator.of(context).pop(),
        },
      );
      return;
    }

    showAlert(
      context: context,
      title: 'Xác nhận',
      content: 'Thông báo sẽ được forward đến những nhân viên đã chọn',
      buttonsWithOnPressed: {
        'Xác nhận': () {
          _onXacNhanPress();
        },
        'Quay lại': () => Navigator.of(context).pop(),
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Close the keyboard when tapping outside text fields
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height,
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Forward thông báo',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              // ignore: unnecessary_null_comparison
              widget.lsEmployeeMasterData.isNotEmpty
                  ? MultipleSearchSelection<EmployeeVm>(
                      showPickedItemScrollbar: true,
                      pickedItemsScrollbarThickness: 4,
                      items: widget.lsEmployeeMasterData,
                      pickedItemsBoxDecoration: BoxDecoration(
                        // borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.grey[400]!, width: 0.5),
                        // color: Colors.grey[200],
                      ),
                      // searchFieldBoxDecoration: BoxDecoration(
                      //   borderRadius: BorderRadius.circular(6),
                      //   border: Border.all(color: Colors.grey[400]!, width: 0.5),
                      //   color: Colors.grey[200],
                      // ),
                      // showedItemsBoxDecoration: BoxDecoration(
                      //   borderRadius: BorderRadius.circular(6),
                      //   border: Border.all(color: Colors.grey[400]!, width: 0.5),
                      //   color: Colors.grey[200],
                      // ),
                      fieldToCheck: (employee) {
                        return employee.employeeCode! + " " + employee.fullName! + " " + UIUtils.removeVietnameseAccent(employee.fullName!); // String
                      },
                      itemBuilder: (employee, index) {
                        return Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              color: Colors.white,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 8.0,
                                horizontal: 12,
                              ),
                              child: Text(
                                employee.employeeCode! + " - " + employee.fullName!,
                                style: const TextStyle(fontSize: 11),
                              ),
                            ),
                          ),
                        );
                      },
                      pickedItemBuilder: (employee) {
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            // border: Border.all(color: Colors.grey[400]!),
                            color: Colors.grey[200],
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min, // Set to make the Row only as wide as its children
                              children: [
                                Text(
                                  employee.employeeCode! + " - " + employee.fullName!,
                                  style: TextStyle(fontSize: 11),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    // Handle the removal of the selected item when the "X" button is tapped.
                                    // You should implement the logic to remove the item from your selected items list.
                                    // You can use the `onItemRemoved` callback for this purpose.
                                    // widget.onItemRemoved(employee);
                                  },
                                  child: const Icon(Icons.clear, color: Colors.grey, size: 12),
                                )
                              ],
                            ),
                          ),
                        );
                      },
                      onTapShowedItem: () {},
                      onPickedChange: (items) {},
                      onItemAdded: (item) {
                        setState(() {
                          selectedEmployees.add(item);
                        });
                        debugPrint(jsonEncode(item));
                        // debugPrint(jsonEncode(selectedEmployees));
                      },
                      onItemRemoved: (EmployeeVm item) {
                        setState(() {
                          var itemToRemove = selectedEmployees.firstWhereOrNull((employee) => employee.accountId == item.accountId);
                          if (itemToRemove != null) {
                            selectedEmployees.remove(itemToRemove);
                          }
                        });
                        debugPrint(jsonEncode(item));
                        // debugPrint(jsonEncode(selectedEmployees));
                      },
                      onTapClearAll: () {
                        setState(() {
                          selectedEmployees.clear();
                        });
                      },
                      sortShowedItems: false,
                      sortPickedItems: false,
                      caseSensitiveSearch: false,
                      fuzzySearch: FuzzySearch.none,
                      itemsVisibility: ShowedItemsVisibility.alwaysOn,
                      title: const Text(
                        'Đã chọn:',
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      hintText: "Tìm kiếm, chọn:",
                      noResultsWidget: const Text(
                        'Không tìm thấy kết quả',
                        style: TextStyle(
                          fontSize: 12,
                        ),
                      ),
                      clearAllButton: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Text(
                          'Xóa tất cả',
                          style: TextStyle(fontSize: 13, color: Colors.blue),
                        ),
                      ),
                      showSelectAllButton: false,
                      maximumShowItemsHeight: 200,
                    )
                  : Container(),
              SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  _onForwardPress();
                },
                child: const Text('Forward'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _NotificationPageState extends State<NotificationPage> {
  bool _isLoadingScroll = false;
  late bool _timeOut;
  bool _isLoading = false;
  bool _isNotWifi = false;
  String _error = "";
  ActionButton? selectedMenu;

  List<PushNotificationVm> _lsPushNotification = [];
  List<EmployeeVm> _lsEmployeeMasterData = [];

  final ScrollController _scrollController = ScrollController();
  int _pageIndex = 0; // Add this line
  static const int _pageSize = 20; // Add this line

  @override
  void initState() {
    super.initState();
    _getListData(_pageIndex, _pageSize);
    // _controllerListView.addListener(_goScroll);
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    debugPrint("_onScroll");
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      // If we are at the bottom of the page, load more data
      _pageIndex++; // Add this line

      _getListData(_pageIndex, _pageSize);
    }
  }

  Future<void> _refreshData() async {
    // Add your refresh logic here, e.g., fetching updated data
    // This is an async function where you can fetch the data you want to refresh.
    // Example:
    _pageIndex = 0;
    await _getListData(_pageIndex, _pageSize); // Replace with your data fetching logic
  }

  Future<void> _getListData(int pageIndex, int pageSize) async {
    try {
      if (!isTokenLive(widget.dateTimeOld)) {
        setState(() {
          _timeOut = true;
        });
        return;
      }

      setState(() {
        _timeOut = false;
        _isLoading = true;
        // _getLstQCNVLByFilter = GetLstQCNVLByFilter(pageNumber: _pageNumber, pageSize: _pageSize, qcStatus: false);
      });
      final dataResponse = await Future.wait([
        // NotificationFunction.getListData(widget.token.toString(), widget.accountId),
        NotificationFunction.getListDataNew(widget.token.toString(), widget.accountId, pageIndex, pageSize),
      ]);
      if (!mounted) return;

      if (dataResponse.isNotEmpty) {
        if (dataResponse[0] != null) {
          // var mappedData = dataResponse[0]?.map((item) => PushNotificationVm.fromJson(item)).toList() ?? [];
          // debugPrint(jsonEncode(mappedData));
          var newData = dataResponse[0]?.map((item) => PushNotificationVm.fromJson(item)).toList() ?? [];
          debugPrint(jsonEncode(newData));
          setState(() {
            // _lsPushNotification = mappedData;
            _lsPushNotification.addAll(newData);
          });
        }
        // _lsDataGetStatusGoodArrive = dataResponse[1] as List<DataGetStatusGoodsArrive>;
      }

      setState(() {
        _isLoading = false;
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      debugPrint(error.toString());
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _timeOut = false;
        _error = error.toString();
      });
    }
  }

  void showCustomModal(BuildContext context, String notificationId, VoidCallback onClose) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(children: [
            CustomModalContent(
              lsEmployeeMasterData: _lsEmployeeMasterData,
              accountId: widget.accountId,
              token: widget.token,
              onForwardConfirmed: onClose,
              notificationId: notificationId,
            ),
            Positioned(
              top: 4, // Adjust the position as needed
              right: 10, // Adjust the position as needed
              child: IconButton(
                icon: const Icon(Icons.close, weight: 50), // Customize the icon as needed
                color: Colors.grey.shade800,
                style: ButtonStyle(
                  textStyle: MaterialStateProperty.resolveWith<TextStyle>((states) {
                    return const TextStyle(fontWeight: FontWeight.bold);
                  }),
                ),
                onPressed: () {
                  Navigator.pop(context);
                  // if (onClose != null) {
                  //   onClose();
                  // }
                },
              ),
            ),
          ]), // Your custom modal content widget
        );
      },
    );
  }

  Future<void> _onForwardPress(String notificationId) async {
    var employeeList = await NotificationFunction.getListEmployee(widget.token.toString(), widget.plant);

    setState(() {
      _lsEmployeeMasterData = employeeList?.map((item) => EmployeeVm.fromJson(item)).toList() ?? [];
    });

    showCustomModal(context, notificationId, () {
      // Callback
      // showToast(context: context, message: 'Test 2');
      _refreshData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: _lsPushNotification.isNotEmpty ? Colors.grey.shade300 : Colors.white,
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          // 'Trả hàng NCC',
          'Danh sách thông báo',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
        actions: [],
      ),
      // body: _isLoading == true ? _buildLoading() : _buildBody(),
      // body: _isLoading == true
      //     ? _buildLoading()
      //     : RefreshIndicator(
      //         onRefresh: _refreshData, // Add the refresh callback
      //         child: _lsPushNotification.isNotEmpty ? _buildBody() : NotFoundView("Chưa có thông báo"),
      //       ),
      body: RefreshIndicator(
        onRefresh: _refreshData, // Add the refresh callback
        child: _lsPushNotification.isNotEmpty
            ? Stack(
                children: [
                  _buildBody(),
                  if (_isLoading) _buildLoading(),
                ],
              )
            : SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height - 120.h,
                  child: NotFoundView("Chưa có thông báo"),
                ),
              ),
      ),
    );
  }

  SingleChildScrollView _buildBody() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _scrollController,
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Stack(
              alignment: Alignment.topCenter,
              children: [
                ListView.separated(
                  physics: _isLoadingScroll == true ? const NeverScrollableScrollPhysics() : const ScrollPhysics(),
                  // key: ObjectKey(_listTraHangNCC[0]),
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: _lsPushNotification.length,
                  separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                  itemBuilder: (context, index) {
                    final item = _lsPushNotification[index];
                    return Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          // Text(item.title!, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11.sp)),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Text(
                                  item.title!,
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11.sp),
                                ),
                              ),
                              Row(
                                children: [
                                  Text(
                                    item.isForwarded == true ? "(đã forward)" : "",
                                    style: TextStyle(fontSize: 9.sp, fontStyle: FontStyle.italic, color: const Color(0xFFA1A1A1)),
                                  ),
                                  PopupMenuButton<ActionButton>(
                                    // initialValue: selectedMenu,
                                    padding: EdgeInsets.zero, // Set padding to zero to remove extra spacing
                                    child: const Padding(
                                      padding: EdgeInsets.only(right: 10),
                                      child: Icon(
                                        Icons.more_vert, // Three-dot icon
                                        color: Colors.black,
                                        size: 18, // Set the icon color to black
                                      ),
                                    ),
                                    onSelected: (ActionButton button) {
                                      debugPrint('Selected: ${button.toString()}');

                                      if (button == ActionButton.forwardButton) {
                                        // showToast(context: context, message: 'Test 2');
                                        _onForwardPress(item.id!);
                                      }
                                      // setState(() {
                                      //   selectedMenu = item;
                                      // });
                                    },
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.r),
                                      side: const BorderSide(color: Colors.white),
                                    ),
                                    itemBuilder: (BuildContext context) => <PopupMenuEntry<ActionButton>>[
                                      PopupMenuItem<ActionButton>(
                                        height: 25, // use this instead of padding, padding not working
                                        value: ActionButton.forwardButton,
                                        // child: Text('Forward', style: TextStyle(fontSize: 11)),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            const Text('Forward', style: TextStyle(fontSize: 12)),
                                            Transform(
                                              transform: Matrix4.identity()..scale(-1.0, 1.0),
                                              alignment: Alignment.center,
                                              child: const Icon(
                                                Icons.reply, // Replace with the icon you want to use
                                                size: 18,
                                                color: Colors.black, // Adjust the size and color as needed
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),

                          const Divider(),
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  item.message!,
                                  style: TextStyle(fontSize: 10.sp),
                                ),
                              ),
                              const SizedBox(width: 8), // Add some spacing between the text and the DateTime
                              Text(
                                // item.createTime.toString(), // Replace 'yourDateTime' with the actual DateTime you want to display
                                UIUtils.timeAgo(item.createTime!).toString(), // Replace 'yourDateTime' with the actual DateTime you want to display
                                style: TextStyle(fontSize: 9.sp, fontStyle: FontStyle.italic, color: const Color(0xFFA1A1A1)),
                              ),
                            ],
                          )
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: 200.0),
          ],
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }
}
