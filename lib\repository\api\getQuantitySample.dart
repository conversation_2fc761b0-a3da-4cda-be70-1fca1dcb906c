import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class GetQuantitySample {
  static Future<http.Response> getQuantitySample(String sampleName, String quantityCheck, String token) async {
    Map<String, String> data = {"SampleName": sampleName, "QuantityCheck": quantityCheck};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url =
        Uri.parse('${baseUrl}${UrlApi.baseUrlCommon_2}get-quantity-sample?SampleName=${data["SampleName"]}&QuantityCheck=${data["QuantityCheck"]}');

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getQuantitySample2(String sampleName, String quantityCheck, String token) async {
    Map<String, String> data = {"SampleName": sampleName, "QuantityCheck": quantityCheck};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url =
        Uri.parse('${baseUrl}${UrlApi.baseUrlCommon_2}get-quantity-sample2?SampleName=${data["SampleName"]}&QuantityCheck=${data["QuantityCheck"]}');

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
