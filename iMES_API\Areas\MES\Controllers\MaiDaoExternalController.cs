using AutoMapper;
using iMES_API.ServiceExtensions;
using ISD.API.Core;
using ISD.API.Extensions;
using ISD.API.Repositories.Interfaces;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MaiDao;
using ISD.API.ViewModels.Permissions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/External/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "integrate")]
    [ApiKeyAuthorization("MaiDao.Read")]
    public class MaiDaoExternalController : ControllerBaseAPI
    {
        private readonly IMapper _mapper;
        private readonly IMaiDaoRepository _repository;
        private readonly ILogger<MaiDaoExternalController> _logger;
        private readonly IConfiguration _configuration;

        public MaiDaoExternalController(
            IMapper mapper,
            IMaiDaoRepository repository,
            ILogger<MaiDaoExternalController> logger,
            IConfiguration configuration)
        {
            _mapper = mapper;
            _repository = repository;
            _logger = logger;
            _configuration = configuration;
        }

        #region Third-Party MaiDao APIs

        /// <summary>Third-Party API - Get MaiDao Records with Filtering</summary>
        /// <param name="equipmentCode">Equipment Code (optional)</param>
        /// <param name="materialCode">Material Code (optional)</param>
        /// <param name="operationType">Operation Type (optional)</param>
        /// <param name="status">Status filter (optional)</param>
        /// <param name="fromDate">From Date (optional)</param>
        /// <param name="toDate">To Date (optional)</param>
        /// <param name="pageIndex">Page index (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20, max: 100)</param>
        /// <returns>Paginated list of MaiDao records</returns>
        /// <remarks>
        /// Sample Request:
        /// 
        /// GET /api/v1/External/MaiDaoExternal/MaiDao?equipmentCode=EQ001&amp;pageIndex=1&amp;pageSize=20
        /// Headers:
        ///     X-API-Key: your-api-key-here
        ///     
        /// Response:
        /// {
        ///   "isSuccess": true,
        ///   "code": 200,
        ///   "message": "Success",
        ///   "data": {
        ///     "items": [...],
        ///     "pagination": {
        ///       "pageIndex": 1,
        ///       "pageSize": 20,
        ///       "totalCount": 150,
        ///       "totalPages": 8
        ///     }
        ///   }
        /// }
        /// </remarks>
        [HttpGet("MaiDao")]
        public async Task<IActionResult> GetMaiDaoList(
            string equipmentCode = null,
            string materialCode = null,
            string operationType = null,
            string status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int pageIndex = 1,
            int pageSize = 20)
        {
            try
            {
                // Validate page size
                if (pageSize > 100) pageSize = 100;
                if (pageSize < 1) pageSize = 20;
                if (pageIndex < 1) pageIndex = 1;

                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                // Create search model with API key context
                var searchModel = new MaiDaoSearchModel
                {
                    CompanyCode = apiKeyContext.CompanyCode,
                    EquipmentCode = equipmentCode,
                    MaterialCode = materialCode,
                    OperationType = operationType,
                    Status = status,
                    FromDate = fromDate,
                    ToDate = toDate,
                    PageNumber = pageIndex,
                    PageSize = pageSize
                };

                _logger.LogInformation($"External API - GetMaiDaoList called with filters: CompanyCode={searchModel.CompanyCode}, EquipmentCode={equipmentCode}, OperationType={operationType}");

                var result = await _repository.GetMaiDaoListAsync(searchModel);

                if (!result.Status)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = 400,
                        IsSuccess = false,
                        Message = result.Message ?? "Failed to retrieve MaiDao records"
                    });
                }

                var totalPages = (int)Math.Ceiling((double)result.TotalCount / pageSize);

                // Transform data to remove sensitive internal fields
                var externalData = result.Data?.Select(item => new
                {
                    item.MaiDaoId,
                    item.Date,
                    item.EquipmentCode,
                    item.EquipmentName,
                    item.MaterialCode,
                    item.MaterialName,
                    item.MaterialBatch,
                    item.OperationType,
                    item.EmployeeCodes,
                    item.EmployeeNames,
                    item.RequestingEmployeeCode,
                    item.RequestingEmployeeName,
                    item.Note,
                    item.Status,
                    item.CreatedDate,
                    item.UpdatedDate,
                    item.CompanyCode
                }).ToList();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = new
                    {
                        Items = externalData,
                        Pagination = new
                        {
                            PageIndex = pageIndex,
                            PageSize = pageSize,
                            TotalCount = result.TotalCount,
                            TotalPages = totalPages,
                            HasPreviousPage = pageIndex > 1,
                            HasNextPage = pageIndex < totalPages
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetMaiDaoList: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get MaiDao Record by ID</summary>
        /// <param name="maiDaoId">MaiDao ID</param>
        /// <returns>MaiDao record details</returns>
        /// <remarks>
        /// Sample Request:
        /// 
        /// GET /api/v1/External/MaiDaoExternal/MaiDao/{maiDaoId}
        /// Headers:
        ///     X-API-Key: your-api-key-here
        /// </remarks>
        [HttpGet("MaiDao/{maiDaoId}")]
        public async Task<IActionResult> GetMaiDaoById(Guid maiDaoId)
        {
            try
            {
                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                _logger.LogInformation($"External API - GetMaiDaoById called for ID: {maiDaoId}");

                var result = await _repository.GetMaiDaoByIdAsync(maiDaoId);

                if (!result.Status || result.Data == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Message = "MaiDao record not found"
                    });
                }

                // Check if API key has access to this company's data
                if (result.Data.CompanyCode != apiKeyContext.CompanyCode)
                {
                    return Forbid("Access denied: MaiDao record belongs to different organization");
                }

                // Transform data to remove sensitive internal fields
                var externalData = new
                {
                    result.Data.MaiDaoId,
                    result.Data.Date,
                    result.Data.EquipmentCode,
                    result.Data.EquipmentName,
                    result.Data.MaterialCode,
                    result.Data.MaterialName,
                    result.Data.MaterialBatch,
                    result.Data.OperationType,
                    result.Data.EmployeeCodes,
                    result.Data.EmployeeNames,
                    result.Data.RequestingEmployeeCode,
                    result.Data.RequestingEmployeeName,
                    result.Data.Note,
                    result.Data.Status,
                    result.Data.CreatedDate,
                    result.Data.UpdatedDate,
                    result.Data.CompanyCode
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = externalData
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetMaiDaoById: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get MaiDao Records by Equipment</summary>
        /// <param name="equipmentCode">Equipment Code</param>
        /// <param name="fromDate">From Date (optional)</param>
        /// <param name="toDate">To Date (optional)</param>
        /// <param name="pageIndex">Page index (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20, max: 100)</param>
        /// <returns>MaiDao records for specific equipment</returns>
        [HttpGet("MaiDao/ByEquipment/{equipmentCode}")]
        public async Task<IActionResult> GetMaiDaoByEquipment(
            string equipmentCode,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int pageIndex = 1,
            int pageSize = 20)
        {
            try
            {
                if (string.IsNullOrEmpty(equipmentCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = 400,
                        IsSuccess = false,
                        Message = "Equipment code is required"
                    });
                }

                // Validate page size
                if (pageSize > 100) pageSize = 100;
                if (pageSize < 1) pageSize = 20;
                if (pageIndex < 1) pageIndex = 1;

                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                var searchModel = new MaiDaoSearchModel
                {
                    CompanyCode = apiKeyContext.CompanyCode,
                    EquipmentCode = equipmentCode,
                    FromDate = fromDate,
                    ToDate = toDate,
                    PageNumber = pageIndex,
                    PageSize = pageSize
                };

                var result = await _repository.GetMaiDaoListAsync(searchModel);

                if (!result.Status)
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = 400,
                        IsSuccess = false,
                        Message = result.Message ?? "Failed to retrieve MaiDao records"
                    });
                }

                var totalPages = (int)Math.Ceiling((double)result.TotalCount / pageSize);

                var externalData = result.Data?.Select(item => new
                {
                    item.MaiDaoId,
                    item.Date,
                    item.EquipmentCode,
                    item.EquipmentName,
                    item.MaterialCode,
                    item.MaterialName,
                    item.OperationType,
                    item.EmployeeNames,
                    item.Status,
                    item.CreatedDate
                }).ToList();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = new
                    {
                        EquipmentCode = equipmentCode,
                        Items = externalData,
                        Pagination = new
                        {
                            PageIndex = pageIndex,
                            PageSize = pageSize,
                            TotalCount = result.TotalCount,
                            TotalPages = totalPages,
                            HasPreviousPage = pageIndex > 1,
                            HasNextPage = pageIndex < totalPages
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetMaiDaoByEquipment: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get Equipment List</summary>
        /// <param name="searchTerm">Search term for equipment (optional)</param>
        /// <returns>List of equipment for the company</returns>
        [HttpGet("Equipment")]
        public async Task<IActionResult> GetEquipment(string searchTerm = "")
        {
            try
            {
                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                var result = await _repository.GetEquipmentAsync(apiKeyContext.CompanyCode, searchTerm ?? "");

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetEquipment: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get Materials List</summary>
        /// <param name="searchTerm">Search term for materials (optional)</param>
        /// <returns>List of materials for the company</returns>
        [HttpGet("Materials")]
        public async Task<IActionResult> GetMaterials(string searchTerm = "")
        {
            try
            {
                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                var result = await _repository.GetMaterialsAsync(apiKeyContext.CompanyCode, searchTerm ?? "");

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetMaterials: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        /// <summary>Third-Party API - Get MaiDao Statistics</summary>
        /// <param name="fromDate">From Date (optional, defaults to last 30 days)</param>
        /// <param name="toDate">To Date (optional, defaults to today)</param>
        /// <returns>Statistical summary of MaiDao operations</returns>
        [HttpGet("Statistics")]
        public async Task<IActionResult> GetMaiDaoStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                // Default to last 30 days if no dates provided
                var endDate = toDate ?? DateTime.Now.Date;
                var startDate = fromDate ?? endDate.AddDays(-30);

                // Get API key context for company filtering
                var apiKeyContext = (ApiKeyContext)HttpContext.Items["ApiKeyContext"];

                var searchModel = new MaiDaoSearchModel
                {
                    CompanyCode = apiKeyContext.CompanyCode,
                    FromDate = startDate,
                    ToDate = endDate,
                    PageNumber = 1,
                    PageSize = int.MaxValue // Get all for statistics
                };

                var result = await _repository.GetMaiDaoListAsync(searchModel);

                if (!result.Status || result.Data == null)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Message = "No data found for the specified period",
                        Data = new
                        {
                            Period = new { FromDate = startDate, ToDate = endDate },
                            TotalRecords = 0,
                            ByOperationType = new Dictionary<string, int>(),
                            ByStatus = new Dictionary<string, int>(),
                            ByEquipment = new Dictionary<string, int>()
                        }
                    });
                }

                // Calculate statistics
                var byOperationType = result.Data
                    .GroupBy(x => x.OperationType ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count());

                var byStatus = result.Data
                    .GroupBy(x => x.Status ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count());

                var byEquipment = result.Data
                    .GroupBy(x => x.EquipmentCode ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count());

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "Success",
                    Data = new
                    {
                        Period = new { FromDate = startDate, ToDate = endDate },
                        TotalRecords = result.TotalCount,
                        ByOperationType = byOperationType,
                        ByStatus = byStatus,
                        ByEquipment = byEquipment.Take(10).ToDictionary(x => x.Key, x => x.Value), // Top 10 equipment
                        Summary = new
                        {
                            AveragePerDay = Math.Round((double)result.TotalCount / Math.Max(1, (endDate - startDate).Days), 2),
                            MostCommonOperationType = byOperationType.OrderByDescending(x => x.Value).FirstOrDefault().Key,
                            MostActiveEquipment = byEquipment.OrderByDescending(x => x.Value).FirstOrDefault().Key
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in External GetMaiDaoStatistics: {ex}");
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        #endregion
    }
}