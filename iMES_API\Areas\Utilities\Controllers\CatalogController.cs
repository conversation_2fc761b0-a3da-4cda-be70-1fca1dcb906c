﻿using ISD.API.Core;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using System.Linq;
using ISD.API.ViewModels.MESP2;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;

namespace iMES_API.Areas.Utilities.Controllers
{
    [Route("api/v{version:apiVersion}/Utilities/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    public class CatalogController : ControllerBaseAPI
    {
        #region Lấy danh sách catalog
        /// <summary>
        /// L<PERSON>y danh sách catalog
        /// </summary>
        /// <param name="CatalogTypeCode"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetListCatalog")]
        public async Task<IActionResult> GetListCatalog([FromQuery] string CatalogTypeCode)
        {
            try
            {
                //Danh sách loại NVL
                var response = await _context.CatalogModel.Where(x => x.CatalogTypeCode == CatalogTypeCode)
                .OrderBy(x => x.OrderIndex)
                .Select(x => new CommonResponse
                {
                    Key = x.CatalogCode,
                    Value = $"{x.CatalogCode} | {x.CatalogText_vi}"
                }).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }
        #endregion

        #region Lấy danh sách CatalogTypeCode
        /// <summary>
        /// Lấy danh sách CatalogTypeCode
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetListCatalogTypeCode")]
        public async Task<IActionResult> GetListCatalogTypeCode()
        {
            try
            {
                List<string> CatalogTypeCode = new List<string>
                {
                    "ProductType",
                    "ProductOrigin",
                    "ProductStatus",
                    "ProductGr",
                    "CompanyCode"
                };
                //Danh sách loại NVL
                var response = await _context.CatalogTypeModel.Where(x => CatalogTypeCode.Contains(x.CatalogTypeCode))
                .Select(x => new CommonResponse
                {
                    Key = x.CatalogTypeCode,
                    Value = x.CatalogTypeName
                }).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }
        #endregion
    }
}
