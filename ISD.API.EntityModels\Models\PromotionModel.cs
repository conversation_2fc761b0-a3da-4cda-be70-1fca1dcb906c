﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PromotionModel", Schema = "tMasterData")]
    public partial class PromotionModel
    {
        public PromotionModel()
        {
            Product = new HashSet<ProductModel>();
        }

        [Key]
        public Guid PromotionId { get; set; }
        [Required]
        [StringLength(50)]
        public string PromotionCode { get; set; }
        [Required]
        [StringLength(100)]
        public string PromotionName { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EffectFromDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EffectToDate { get; set; }
        public string Description { get; set; }
        [StringLength(100)]
        public string ImageUrl { get; set; }
        [StringLength(4000)]
        public string Notes { get; set; }

        [ForeignKey("PromotionId")]
        [InverseProperty("PromotionNavigation")]
        public virtual ICollection<ProductModel> Product { get; set; }
    }
}