﻿using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDAuthorizationAttribute]
    public class ProductionRecordingController : ControllerBaseAPI
    {
        #region Ghi nhận sản lượng - Lấy thông tin
        /// <summary>API "Ghi nhân sản lượng" - L<PERSON>y thông tin </summary>
        /// <param name="Barcode"></param>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/ProductionRecording/ProductionRecord?Barcode={Barcode}
        ///     Params: 
        ///             + version : 1
        ///             + Barcode  : B0C17D8A-E82A-490A-A6F6-CA5A2F582422e
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code":200,
        ///         "isSuccess":true,
        ///         "message":null,
        ///         "data":{
        ///             "usageQuantity":[{
        ///                     "productAttributes":"0101.b.9",
        ///                     "productName":"Nối vai tựa dưới",
        ///                     "itmno":"0101.b.9",
        ///                     "ktext":"Nối vai tựa dưới",
        ///                     "poT12":null,
        ///                     "bmschdc":null,
        ///                     "bmsch":1.0,
        ///                     "quantity":0.00,
        ///                     "listITMNO":null
        ///                 }],
        ///             "productionRecordRouting":{
        ///                     "quantity_DLD":120.00,
        ///                     "quantity_DLKD":0.00,
        ///                     "productAttributes":"0101.b.9"
        ///             },
        ///             "productionRecord":{
        ///                     "taskId":"bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///                     "parentTaskId":"56fb2574-5ad5-421a-82fd-22c593fdb854",
        ///                     "unit":"CAI",
        ///                     "barcode":"c90d94f2-f2f1-41d6-97aa-9c5fec6aac51",
        ///                     "qty":60,
        ///                     "productAttributesQty":120.000,
        ///                     "productId":"95d35eb5-2d5f-440a-8714-453d17d3f7b7",
        ///                     "createTime":"2022-05-09T14:08:59.357",
        ///                     "lastEditTime":null,
        ///                     "stepCode":"LON",
        ///                     "stepName":"Lọng (m/ph)",
        ///                     "stepId":"b762b961-be6d-454b-b741-065073b99b9a",
        ///                     "productionOrder_EstimateEndDate":"2022-06-20T00:00:00",
        ///                     "productionOrder_StartDate":"2022-05-06T00:00:00",
        ///                     "workDate":"2022-05-06T00:00:00",
        ///                     "productCode":"530004331",
        ///                     "productName":"Ghế dài Aspen-CH025ARHP Matte Black",
        ///                     "productAttributes":"0101.b.9",
        ///                     "productionOrder_SAP":"500016230",
        ///                     "createByFullName":"Thongke002",
        ///                     "lastEditByFullName":null,
        ///                     "actived":true,
        ///                     "toStockCode":null,
        ///                     "toStockName":null,
        ///                     "isWorkCenterCompleted":null,
        ///                     "workCenterConfirmTime":null,
        ///                     "confirmWorkCenter":"SC",
        ///                     "property1":"2250001227",
        ///                     "property2":"000050",
        ///                     "productionOrder":"DT-346-21-FOH",
        ///                     "summary":"DT-346-21-FOH-D1",
        ///                     "productAttributesName":"Nối vai tựa dưới",
        ///                     "productAttributesUnit":"Cái",
        ///                     "poT12":"25x30x163",
        ///                     "productAttributesQtyD":120.00,
        ///                     "phase":1,
        ///                     "quantity_DLD":null,
        ///                     "quantity_DLKD":null,
        ///                     "listDetail":[{
        ///                             "stockRecevingType":null,
        ///                             "createTime":null,
        ///                             "createByName":null,
        ///                             "quantity":null,
        ///                             "itmno":"0101.b.9",
        ///                             "fromTime":"2022-05-09T07:30:00",
        ///                             "toTime":"2022-05-09T11:00:00",
        ///                             "customerReference":"bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///                             "quantity_D":120.00,
        ///                             "quantity_KD":null,
        ///                             "ktext":"Nối vai tựa dưới",
        ///                             "stepCode":"BA2",
        ///                             "phase":1,
        ///                             "fromDate":null,
        ///                             "toDate":"2022-05-09T11:00:00"
        ///                        },
        ///                        {
        ///                             "stockRecevingType":null,
        ///                             "createTime":null,
        ///                             "createByName":null,
        ///                             "quantity":null,
        ///                             "itmno":"0101.b.9",
        ///                             "fromTime":"2022-05-09T07:30:00",
        ///                             "toTime":"2022-05-09T11:00:00",
        ///                             "customerReference":"bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///                             "quantity_D":120.00,
        ///                             "quantity_KD":null,
        ///                             "ktext":"Nối vai tựa dưới",
        ///                             "stepCode":"LON",
        ///                             "phase":1,
        ///                             "fromDate":null,
        ///                             "toDate":"2022-05-09T11:00:00"
        ///                        }]
        ///                  }
        ///               }
        ///         ,"additionalData":null
        ///      }
        ///
        /// 
        ///</remarks>
        [HttpGet("ProductionRecord")]
        public IActionResult GET(Guid? Barcode)
        {
            #region Tìm LSX SAP theo barcode
            //
            Guid? TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

            //Nếu có lệnh thực thi thì hiển thị popup ghi nhận sản lượng
            //Nếu chưa có thì thêm thực thi lệnh sản xuất=> get data để hiển thị popup ghi nhận sản lượng
            if (TaskId == null || TaskId == Guid.Empty)
            {
                Guid HangTagId = (Guid)Barcode;
                var handTag = _context.HangTagModel.Where(p => p.HangTagId == HangTagId).FirstOrDefault();
                if (handTag != null && handTag.CustomerReference.HasValue)
                {
                    //Thêm mới lệnh thực thi và lấy dữ liệu
                    TaskId = _unitOfWork.ProductionManagementRepository.CreateNewExecutionTask(handTag.CustomerReference.Value, Barcode, CurrentUserId: CurrentUser.AccountId);
                }
                else
                {
                    //Báo lỗi không tìm thấy barcode
                    return BadRequest(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Data = "Thẻ treo không hợp lệ!",
                    });
                }
            }
            #endregion

            var CompanyCode = CurrentUser.CompanyCode;

            #region _RecordProductTion
            #region Nếu có task theo barcode => show popup theo dữ liệu


            var dataProductionRecord = GetExecutionTaskByTaskId(TaskId);

            #endregion

            #region Create ViewBag "Công đoạn"
            var ListStepCode = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataProductionRecord.ProductCode, dataProductionRecord.ProductAttributes, CompanyCode).Select(x => new ISDSelectItem2 { value = x.ARBPL_SUB, text = x.ARBPL_SUB + " | " + x.LTXA1 }).ToList();
            #endregion

            // Check nếu có routing mới cho phép ghi nhận tiếp
            var routingCount = _context.View_Product_Material.Where(x => x.WERKS == CompanyCode
                                                                    && x.MATNR == dataProductionRecord.ProductCode
                                                                    && x.ITMNO == dataProductionRecord.ProductAttributes
                                                                    && x.ARBPL_SUB == dataProductionRecord.StepCode).Count();

            if (routingCount > 0)
                dataProductionRecord.isHasRouting = true;
            #endregion

            #region ProductionRecord - Detail
            var dataProductRecordDetail = _unitOfWork.ProductionManagementRepository.GetProductDetailsHistoryByTTLSX((Guid)TaskId);
            dataProductionRecord.ListDetail = dataProductRecordDetail;
            #endregion

            #region _UsageQuantity
            var radio = "";
            if (string.IsNullOrEmpty(dataProductionRecord.StepCode))
                radio = null;
            else if (dataProductionRecord.StepCode == "TKP")
                radio = "TKP";
            else if (dataProductionRecord.StepCode == "RAC" || dataProductionRecord.StepCode == "RAH" || dataProductionRecord.StepCode == "RAT")
                radio = "RAP";
            else
                radio = "OTHER";

            var dataUsageQuantity = _unitOfWork.ProductionManagementRepository.UsageQuantity(CompanyCode, dataProductionRecord.StepCode, radio, (Guid)TaskId);
            #endregion

            #region _RecordProductionRouting
            var ProductionRecordRouting = new ProductionRecordRoutingVm();

            if (dataProductionRecord != null)
            {
                ProductionRecordRouting.ProductAttributes = dataProductionRecord.ProductAttributes;

                ProductionRecordRouting.Quantity_DLD = (from sR in _context.StockReceivingDetailModel
                                                            //Stock
                                                        join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                                                        from s in sList.DefaultIfEmpty()
                                                            //ttlsx
                                                        join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                                                        where
                                                        //Sản phẩm
                                                        sR.ProductId == dataProductionRecord.ProductId
                                                        //Mã Chi tiết
                                                        && sR.ProductAttributes == dataProductionRecord.ProductAttributes
                                                        && s.StockCode == dataProductionRecord.StepCode
                                                        && sR.StockRecevingType == "D"
                                                        && ttlsx.ParentTaskId == dataProductionRecord.ParentTaskId
                                                        select sR.Quantity).Sum();

                ProductionRecordRouting.Quantity_DLKD = (from sR in _context.StockReceivingDetailModel
                                                             //Stock
                                                         join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                                                         from s in sList.DefaultIfEmpty()
                                                             //ttlsx
                                                         join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                                                         where
                                                          //Sản phẩm
                                                          sR.ProductId == dataProductionRecord.ProductId
                                                         //Mã Chi tiết
                                                         && sR.ProductAttributes == dataProductionRecord.ProductAttributes
                                                         && s.StockCode == dataProductionRecord.StepCode
                                                         && sR.StockRecevingType == "KD"
                                                         && ttlsx.ParentTaskId == dataProductionRecord.ParentTaskId
                                                         select sR.Quantity).Sum();
            }
            #endregion

            //TODO : Chuyển Phân xưởng, tổ qua API đăng nhập
            //#region Phân xưởng
            //var companyId = _context.CompanyModel.Where(x => x.CompanyCode == CompanyCode).Select(x => x.CompanyId).FirstOrDefault();
            //var ListWorkShopId = _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == companyId).OrderBy(p => p.OrderIndex).Select(x => new { x.WorkShopId, display = x.WorkShopCode + " | " + x.WorkShopName }).ToList();
            //#endregion

            //#region Tổ
            //var ListDepartment = _context.DepartmentModel.Where(x => x.Actived == true).Select(x => new { x.DepartmentId, display = x.DepartmentCode + " | " + x.DepartmentName, x.WorkShopId });
            //#endregion

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Message = null,
                Data = new ProductionRecordApiModel
                {
                    ProductionRecord = dataProductionRecord,
                    UsageQuantity = dataUsageQuantity,
                    ProductionRecordRouting = ProductionRecordRouting,
                    ListStepCode = ListStepCode
                },
                AdditionalData = null
            });
        }

        [HttpGet("ProductionRecord2")]
        public IActionResult GET(Guid? Barcode, string ver2 = null)
        {
            #region Tìm LSX SAP theo barcode
            //
            Guid? TaskId = _unitOfWork.ProductionManagementRepository.GetTTLSXByBarcode(Barcode);

            Guid HangTagId = (Guid)Barcode;
            var handTag = _context.HangTagModel.Where(p => p.HangTagId == HangTagId).FirstOrDefault();

            //Nếu có lệnh thực thi thì hiển thị popup ghi nhận sản lượng
            //Nếu chưa có thì thêm thực thi lệnh sản xuất=> get data để hiển thị popup ghi nhận sản lượng
            if (TaskId == null || TaskId == Guid.Empty)
            {
                if (handTag != null && handTag.CustomerReference.HasValue)
                {
                    //Thêm mới lệnh thực thi và lấy dữ liệu
                    TaskId = _unitOfWork.ProductionManagementRepository.CreateNewExecutionTask(handTag.CustomerReference.Value, Barcode, CurrentUserId: CurrentUser.AccountId);
                }
                else
                {
                    //Báo lỗi không tìm thấy barcode
                    return BadRequest(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Data = "Thẻ treo không hợp lệ!",
                    });
                }
            }
            else
            {
                // Tien update lại số lượng fix lỗi lệnh số thập phân
                var LSXSAP = (from t in _context.TaskModel
                              where t.TaskId == handTag.CustomerReference.Value
                              select t).FirstOrDefault();

                var Task = (from t in _context.ThucThiLenhSanXuatModel
                            where t.Barcode == Barcode
                            select t).FirstOrDefault();

                Task.Qty = LSXSAP.Qty;
                if (LSXSAP.Number2 != null)
                {
                    //Task.Qty = (int)LSXSAP.Number2;
                    Task.Qty = LSXSAP.Number2;
                }
                _context.SaveChanges();
            }
            #endregion

            var CompanyCode = CurrentUser.CompanyCode;

            #region _RecordProductTion
            #region Nếu có task theo barcode => show popup theo dữ liệu


            var dataProductionRecord = GetExecutionTaskByTaskId2(TaskId);

            #endregion

            #region Create ViewBag "Công đoạn"
            var ListStepCode = _unitOfWork.ProductionManagementRepository.LoadRoutingOf(dataProductionRecord.ProductCode, dataProductionRecord.ProductAttributes, CompanyCode)
                                                                            .Select(x => new ISDSelectItem2 { value = x.ARBPL_SUB, text = x.ARBPL_SUB + " | " + x.LTXA1 })
                                                                            .ToList();
            #endregion

            // Check nếu có routing mới cho phép ghi nhận tiếp
            var routingCount = _context.View_Product_Material.Where(x => x.WERKS == CompanyCode
                                                                    && x.MATNR == dataProductionRecord.ProductCode
                                                                    && x.ITMNO == dataProductionRecord.ProductAttributes
                                                                    && x.ARBPL_SUB == dataProductionRecord.StepCode).Count();

            if (routingCount > 0)
            {
                dataProductionRecord.isHasRouting = true;
            }
            #endregion

            #region ProductionRecord - Detail
            var dataProductRecordDetail = _unitOfWork.ProductionManagementRepository.GetProductDetailsHistoryByTTLSX((Guid)TaskId);
            dataProductionRecord.ListDetail = dataProductRecordDetail;
            #endregion

            #region _UsageQuantity
            var radio = "";
            if (string.IsNullOrEmpty(dataProductionRecord.StepCode))
                radio = null;
            else if (dataProductionRecord.StepCode == "TKP")
                radio = "TKP";
            else if (dataProductionRecord.StepCode == "RAC" || dataProductionRecord.StepCode == "RAH" || dataProductionRecord.StepCode == "RAT")
                radio = "RAP";
            else
                radio = "OTHER";

            var dataUsageQuantity = _unitOfWork.ProductionManagementRepository.UsageQuantity(CompanyCode, dataProductionRecord.StepCode, radio, (Guid)TaskId);
            #endregion

            #region _RecordProductionRouting
            var ProductionRecordRouting = new ProductionRecordRoutingVm();

            if (dataProductionRecord != null)
            {
                ProductionRecordRouting.ProductAttributes = dataProductionRecord.ProductAttributes;

                ProductionRecordRouting.Quantity_DLD = (from sR in _context.StockReceivingDetailModel
                                                            //Stock
                                                        join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                                                        from s in sList.DefaultIfEmpty()
                                                            //ttlsx
                                                        join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                                                        where
                                                        //Sản phẩm
                                                        sR.ProductId == dataProductionRecord.ProductId
                                                        //Mã Chi tiết
                                                        && sR.ProductAttributes == dataProductionRecord.ProductAttributes
                                                        && s.StockCode == dataProductionRecord.StepCode
                                                        && sR.StockRecevingType == "D"
                                                        && ttlsx.ParentTaskId == dataProductionRecord.ParentTaskId
                                                        select sR.Quantity).Sum();

                ProductionRecordRouting.Quantity_DLKD = (from sR in _context.StockReceivingDetailModel
                                                             //Stock
                                                         join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                                                         from s in sList.DefaultIfEmpty()
                                                             //ttlsx
                                                         join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                                                         where
                                                          //Sản phẩm
                                                          sR.ProductId == dataProductionRecord.ProductId
                                                         //Mã Chi tiết
                                                         && sR.ProductAttributes == dataProductionRecord.ProductAttributes
                                                         && s.StockCode == dataProductionRecord.StepCode
                                                         && sR.StockRecevingType == "KD"
                                                         && ttlsx.ParentTaskId == dataProductionRecord.ParentTaskId
                                                         select sR.Quantity).Sum();
            }
            #endregion

            //TODO : Chuyển Phân xưởng, tổ qua API đăng nhập
            //#region Phân xưởng
            //var companyId = _context.CompanyModel.Where(x => x.CompanyCode == CompanyCode).Select(x => x.CompanyId).FirstOrDefault();
            //var ListWorkShopId = _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == companyId).OrderBy(p => p.OrderIndex).Select(x => new { x.WorkShopId, display = x.WorkShopCode + " | " + x.WorkShopName }).ToList();
            //#endregion

            //#region Tổ
            //var ListDepartment = _context.DepartmentModel.Where(x => x.Actived == true).Select(x => new { x.DepartmentId, display = x.DepartmentCode + " | " + x.DepartmentName, x.WorkShopId });
            //#endregion

            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Message = null,
                Data = new ProductionRecordApiModel2
                {
                    ProductionRecord = dataProductionRecord,
                    UsageQuantity = dataUsageQuantity,
                    ProductionRecordRouting = ProductionRecordRouting,
                    ListStepCode = ListStepCode
                },
                AdditionalData = null
            });
        }

        #endregion


        /// <summary>API "Ghi nhân sản lượng" - Lưu thông tin </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/ProductionRecording/ProductionRecord
        ///     Params: 
        ///             + version : 1
        /// 
        /// BODY
        /// 
        ///     {
        ///        "productionOrderViewModel": {
        ///          "taskId": "bf152d1b-5773-4b3d-911e-bc887c066bce",
        ///          "productId": "95d35eb5-2d5f-440a-8714-453d17d3f7b7",
        ///          "productAttributes": "0101.b.9",
        ///          "phase": 1,
        ///          "stepCode": "CAX",
        ///          "departmentId": "ea93cb2d-9aa6-4491-b866-17ed169f4165",
        ///          "workDate": "2022-05-13"
        ///        },
        ///        "productionOrderDetailOld": [
        ///          {
        ///            "fromTime": "2022-05-13 00:01:00 AM",
        ///            "toTime": "2022-05-13 00:30:00 AM",
        ///            "quantity_D": 1,
        ///            "quantity_KD": 1
        ///          }
        ///        ],
        ///        "usageQuantityViewModels": [
        ///          {
        ///            "productAttributes": "0101.b.9",
        ///            "itmno": "0101.b.9",
        ///            "bmschdc": 2
        ///          }
        ///        ]
        ///     }
        ///
        /// 
        /// OUT PUT
        /// 
        ///     {
        ///        "code": 201,
        ///        "success": true,
        ///        "data": "Ghi nhận thông tin thành công!"
        ///     }
        /// 
        /// 
        ///</remarks>
        [HttpPost("ProductionRecord")]
        public IActionResult POST([FromBody] ProductionRecordPostVm recordPostVm)
        {
            var CurrUserId = CurrentUser.AccountId;

            var productionOrderViewModel = recordPostVm.productionOrderViewModel;
            var productionOrderDetailOld = recordPostVm.productionOrderDetailOld;
            var usageQuantityViewModels = recordPostVm.usageQuantityViewModels;

            //Báo lỗi nếu chưa nhập tổ
            if (productionOrderViewModel.DepartmentId == Guid.Empty)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn thông tin tổ!",
                });
            }
            //Báo lỗi nếu chưa nhập Stepcode
            if (productionOrderViewModel.StepCode == null)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn thông tin công đoạn!",
                });
            }

            if (productionOrderViewModel.WorkDate == null && (productionOrderViewModel.FromDate == null || productionOrderViewModel.ToDate == null))
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn ngày!",
                });

            }
            //Lấy ngày làm việc

            DateTime? FromDate = productionOrderViewModel.WorkDate;
            DateTime? WorkDate = productionOrderViewModel.WorkDate;
            //Nếu có ngày bắt đầu và ngày kết thúc thì sẽ tính theo ngày bắt đầu và ngày kết thúc
            if (productionOrderViewModel.FromDate != null)
            {
                FromDate = productionOrderViewModel.FromDate;
            }
            if (FromDate.Value > WorkDate.Value)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Ngày bắt đầu không được nhỏ hơn ngày kết thúc!",
                });
            }

            //Ghi nhận sản lượng - không cho ghi nhận trước ngày khóa sổ
            var dateClosed = _context.DateClosedModel.FirstOrDefault();
            if (dateClosed != null)
            {
                if (productionOrderViewModel.WorkDate.HasValue && productionOrderViewModel.WorkDate.Value.Date < dateClosed.DateClosed.Date)
                {
                    return BadRequest(new
                    {
                        Code = HttpStatusCode.NotModified,
                        Success = false,
                        Data = string.Format("Không thể ghi nhận sản lượng trước ngày khóa sổ! ({0:dd/MM/yyyy})", dateClosed.DateClosed.Date),
                    });
                }
            }

            var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            //Ghi nhận sản lượng - không cho nhập thời gian trong tương lai
            if (productionOrderViewModel.WorkDate.HasValue && productionOrderViewModel.WorkDate.Value.Date > currentDate)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Không thể ghi nhận sản lượng trong tương lai!",
                });
            }
            //Lấy thông tin stock
            var stockid = _unitOfWork.StockRepository.GetStockIdByStockCode(productionOrderViewModel.StepCode);
            //Lấy thông tin lênh thực thi
            var ttlxs = _context.ThucThiLenhSanXuatModel.FirstOrDefault(x => x.TaskId == productionOrderViewModel.TaskId);
            if (ttlxs != null)
            {
                #region //Cập nhật công đoạn sản xuất
                ttlxs.Property6 = productionOrderViewModel.StepCode;
                ttlxs.StockId = stockid;
                #endregion

            }

            var dataGN = GetExecutionTaskByTaskId(productionOrderViewModel.TaskId, productionOrderViewModel.StepCode);

            var qtyGND = dataGN.ProductAttributesQtyD;
            var qtyGN = dataGN.ProductAttributesQty;

            _context.Entry(ttlxs).State = EntityState.Modified;
            //Tạo Stock master
            var catalogType = _context.CatalogModel.Where(x => x.CatalogTypeCode == "StockRecevingType").ToList();
            //Add Stock Receiving Detail
            if (productionOrderDetailOld != null && productionOrderDetailOld.Count > 0)
            {
                foreach (var stockRecevingDetailVM in productionOrderDetailOld)
                {
                    foreach (var item in catalogType)
                    {
                        //Thêm stock receiving detail nếu type =  đạt
                        if (item.CatalogCode == "D")
                        {
                            #region //Lưu tồn kho
                            if (stockRecevingDetailVM.Quantity_D != null && stockRecevingDetailVM.Quantity_D != 0)
                            {
                                //Get datekey from documentDate

                                var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);
                                if ((stockRecevingDetailVM.FromTime.Value.Hour == 0 && stockRecevingDetailVM.FromTime.Value.Minute == 0) || (stockRecevingDetailVM.ToTime.Value.Hour == 0 && stockRecevingDetailVM.ToTime.Value.Minute == 0))
                                {
                                    return BadRequest(new
                                    {
                                        Code = HttpStatusCode.NotModified,
                                        Success = false,
                                        Data = "Vui lòng chọn thời gian ghi nhận!",
                                    });
                                }

                                //Check số lượng ghi nhận
                                if ((qtyGND + stockRecevingDetailVM.Quantity_D) > qtyGN)
                                    return BadRequest(new { Code = HttpStatusCode.NotModified, Success = false, Data = $"Số lượng ghi nhận không được lớn hơn {qtyGN}." });

                                var stockReceivingDetailViewModel = new StockReceivingDetailViewModel
                                {
                                    StockReceivingDetailId = Guid.NewGuid(),
                                    DateKey = dateKey,
                                    FromTime = new DateTime(FromDate.Value.Year, FromDate.Value.Month, FromDate.Value.Day, stockRecevingDetailVM.FromTime.Value.Hour, stockRecevingDetailVM.FromTime.Value.Minute, 0),
                                    ToTime = new DateTime(WorkDate.Value.Year, WorkDate.Value.Month, WorkDate.Value.Day, stockRecevingDetailVM.ToTime.Value.Hour, stockRecevingDetailVM.ToTime.Value.Minute, 0),
                                    ProductId = productionOrderViewModel.ProductId,
                                    ProductAttributes = productionOrderViewModel.ProductAttributes,
                                    Quantity = stockRecevingDetailVM.Quantity_D,
                                    StockRecevingType = item.CatalogCode,
                                    CustomerReference = productionOrderViewModel.TaskId,
                                    StockId = stockid,
                                    CreateTime = currentDate,
                                    CreateBy = CurrUserId,
                                    DepartmentId = productionOrderViewModel.DepartmentId,
                                    Phase = productionOrderViewModel.Phase,
                                    MovementType = "ADD"

                                };
                                _unitOfWork.StockRecevingDetailRepository.Create(stockReceivingDetailViewModel);
                            }
                            else
                            {
                                return BadRequest(new
                                {
                                    Code = HttpStatusCode.NotModified,
                                    Success = false,
                                    Data = "Vui lòng nhập số lượng ghi nhận!",
                                });
                            }
                            #endregion


                        }
                        //Thêm stock receiving detail nếu type = không  đạt
                        if (item.CatalogCode == "KD")
                        {

                            if (stockRecevingDetailVM.Quantity_KD != null && stockRecevingDetailVM.Quantity_KD != 0)
                            {
                                if ((stockRecevingDetailVM.FromTime.Value.Hour == 0 && stockRecevingDetailVM.FromTime.Value.Minute == 0) || (stockRecevingDetailVM.ToTime.Value.Hour == 0 && stockRecevingDetailVM.ToTime.Value.Minute == 0))
                                {
                                    return BadRequest(new
                                    {
                                        Code = HttpStatusCode.NotModified,
                                        Success = false,
                                        Data = "Vui lòng chọn thời gian ghi nhận!",
                                    });
                                }

                                #region //Lưu tồn kho
                                //Get datekey from documentDate
                                var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);

                                StockReceivingDetailViewModel stockReceivingDetailViewModel = new StockReceivingDetailViewModel
                                {
                                    StockReceivingDetailId = Guid.NewGuid(),
                                    DateKey = dateKey,
                                    FromTime = new DateTime(FromDate.Value.Year, FromDate.Value.Month, FromDate.Value.Day, stockRecevingDetailVM.FromTime.Value.Hour, stockRecevingDetailVM.FromTime.Value.Minute, 0),
                                    ToTime = new DateTime(WorkDate.Value.Year, WorkDate.Value.Month, WorkDate.Value.Day, stockRecevingDetailVM.ToTime.Value.Hour, stockRecevingDetailVM.ToTime.Value.Minute, 0),
                                    ProductId = productionOrderViewModel.ProductId,
                                    ProductAttributes = productionOrderViewModel.ProductAttributes,
                                    Quantity = stockRecevingDetailVM.Quantity_KD,
                                    StockRecevingType = item.CatalogCode,
                                    CustomerReference = productionOrderViewModel.TaskId,
                                    StockId = stockid,
                                    CreateTime = currentDate,
                                    CreateBy = CurrUserId,
                                    DepartmentId = productionOrderViewModel.DepartmentId,
                                    Phase = productionOrderViewModel.Phase,
                                    MovementType = "ADD"
                                };
                                _unitOfWork.StockRecevingDetailRepository.Create(stockReceivingDetailViewModel);
                            }
                            #endregion
                        }
                    }
                }
            }

            #region Giảm tồn kho (transferModel)
            if (usageQuantityViewModels != null && usageQuantityViewModels.Count > 0)
            {
                foreach (var stockTransferVM in usageQuantityViewModels)
                {
                    #region //Giảm tồn kho
                    if (stockTransferVM.BMSCHDC != null && stockTransferVM.BMSCHDC != 0 && stockTransferVM.ProductAttributes == productionOrderViewModel.ProductAttributes)
                    {
                        //Get datekey from documentDate

                        var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);

                        var transferDetailViewModel = new TransferDetailViewModel
                        {
                            TransferDetailId = Guid.NewGuid(),
                            DateKey = dateKey,
                            FromTime = currentDate,
                            ToTime = currentDate,
                            ProductId = productionOrderViewModel.ProductId,
                            ProductAttributes = stockTransferVM.ITMNO,
                            Quantity = -stockTransferVM.BMSCHDC,
                            StockRecevingType = "D",
                            FromCustomerReference = productionOrderViewModel.TaskId,
                            //CustomerReference = switchingStagesViewModel.CustomerReference,
                            //FromStockId = stockid,
                            ToStockId = stockid,
                            CreateTime = currentDate,
                            CreateBy = CurrUserId,
                            Phase = productionOrderViewModel.Phase,
                            MovementType = "USING"
                        };
                        _unitOfWork.TransferDetailRepository.Create(transferDetailViewModel);
                    }
                    #endregion
                }
            }
            #endregion
            try
            {
                // TIEN UPDATE: xác nhận công đoạn app sẽ thêm phiếu QC KCS kiểm tra
                // TODO: UPDATE 2: khi chưa xác nhận công đoạn có thể nào tạo luôn phiếu QC kiểm tra do sx làm xong nhưng chưa xác nhận trên app mà QC đã quét

                var confirmWorkCenterViewModel = new ConfirmWorkCenterViewModel();
                confirmWorkCenterViewModel.TaskId = productionOrderViewModel.TaskId;
                confirmWorkCenterViewModel.DepartmentId = productionOrderViewModel.DepartmentId;
                confirmWorkCenterViewModel.StepCode = productionOrderViewModel.StepCode;
                confirmWorkCenterViewModel.WorkCenterConfirmTime = currentDate;

                //confirmWorkCenterViewModel.StepCode = productionOrderViewModel.StepCode;

                //  Phase // 1, 2, 3,... mỗi lần đi qua cùng 1 công đoạn thì tăng số Phase lên
                //productionOrderViewModel.StepCode                                             // CAT, TKV, EPV (công đoạn nhỏ)
                //productionOrderViewModel.ProductAttributes                                    // "0101.1.5"
                //productionOrderViewModel.DepartmentId: "e8433498-c04c-42ba-9c82-b45132ef0704" // Tổ lắp ráp PXB-G4

                _unitOfWork.QualityControlRepository.CreateQualityControl2(confirmWorkCenterViewModel);

                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.BadRequest,
                    Success = true,
                    Data = ex.Message,
                });
            }
            return Ok(new
            {
                Code = HttpStatusCode.Created,
                Success = true,
                Data = "Ghi nhận thông tin thành công!",
            });
        }

        [HttpPost("ProductionRecord2")]
        public IActionResult POST([FromBody] ProductionRecordPostVm recordPostVm, string ver2 = null)
        {
            var CurrUserId = CurrentUser.AccountId;

            var productionOrderViewModel = recordPostVm.productionOrderViewModel;
            var productionOrderDetailOld = recordPostVm.productionOrderDetailOld;
            var usageQuantityViewModels = recordPostVm.usageQuantityViewModels;

            //Báo lỗi nếu chưa nhập tổ
            if (productionOrderViewModel.DepartmentId == Guid.Empty)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn thông tin tổ!",
                });
            }
            //Báo lỗi nếu chưa nhập Stepcode
            if (productionOrderViewModel.StepCode == null)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn thông tin công đoạn!",
                });
            }

            if (productionOrderViewModel.WorkDate == null && (productionOrderViewModel.FromDate == null || productionOrderViewModel.ToDate == null))
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Vui lòng chọn ngày!",
                });
            }
            //Lấy ngày làm việc

            DateTime? FromDate = productionOrderViewModel.WorkDate;
            DateTime? WorkDate = productionOrderViewModel.WorkDate;
            //Nếu có ngày bắt đầu và ngày kết thúc thì sẽ tính theo ngày bắt đầu và ngày kết thúc
            if (productionOrderViewModel.FromDate != null)
            {
                FromDate = productionOrderViewModel.FromDate;
            }
            if (FromDate.Value > WorkDate.Value)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Ngày bắt đầu không được nhỏ hơn ngày kết thúc!",
                });
            }

            // Update 04.03/2024: chỉ cho phép ghi nhận sản lượng trong ngày hiện tại và lùi lại 1 ngày
            if (productionOrderViewModel.WorkDate.Value.Date < DateTime.Now.Date.AddDays(-1))
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Không thể ghi nhận sản lượng lùi quá 1 ngày!",
                });
            }
            
            //Ghi nhận sản lượng - không cho ghi nhận trước ngày khóa sổ
            var dateClosed = _context.DateClosedModel.FirstOrDefault();
            if (dateClosed != null)
            {
                if (productionOrderViewModel.WorkDate.HasValue && productionOrderViewModel.WorkDate.Value.Date < dateClosed.DateClosed.Date)
                {
                    return BadRequest(new
                    {
                        Code = HttpStatusCode.NotModified,
                        Success = false,
                        Data = string.Format("Không thể ghi nhận sản lượng trước ngày khóa sổ! ({0:dd/MM/yyyy})", dateClosed.DateClosed.Date),
                    });
                }
            }

            var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            //Ghi nhận sản lượng - không cho nhập thời gian trong tương lai
            if (productionOrderViewModel.WorkDate.HasValue && productionOrderViewModel.WorkDate.Value.Date > currentDate)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.NotModified,
                    Success = false,
                    Data = "Không thể ghi nhận sản lượng trong tương lai!",
                });
            }
            //Lấy thông tin stock
            var stockid = _unitOfWork.StockRepository.GetStockIdByStockCode(productionOrderViewModel.StepCode);
            //Lấy thông tin lênh thực thi
            var ttlxs = _context.ThucThiLenhSanXuatModel.FirstOrDefault(x => x.TaskId == productionOrderViewModel.TaskId);
            if (ttlxs != null)
            {
                #region //Cập nhật công đoạn sản xuất
                ttlxs.Property6 = productionOrderViewModel.StepCode;
                ttlxs.StockId = stockid;
                #endregion

            }

            var dataGN = GetExecutionTaskByTaskId2(productionOrderViewModel.TaskId, productionOrderViewModel.StepCode);

            var qtyGND = dataGN.ProductAttributesQtyD;
            var qtyGN = dataGN.ProductAttributesQty;

            _context.Entry(ttlxs).State = EntityState.Modified;
            //Tạo Stock master
            var catalogType = _context.CatalogModel.Where(x => x.CatalogTypeCode == "StockRecevingType").ToList();
            //Add Stock Receiving Detail
            if (productionOrderDetailOld != null && productionOrderDetailOld.Count > 0)
            {
                foreach (var stockRecevingDetailVM in productionOrderDetailOld)
                {
                    foreach (var item in catalogType)
                    {
                        //Thêm stock receiving detail nếu type =  đạt
                        if (item.CatalogCode == "D")
                        {
                            #region //Lưu tồn kho
                            if (stockRecevingDetailVM.Quantity_D != null && stockRecevingDetailVM.Quantity_D != 0)
                            {
                                //Get datekey from documentDate

                                var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);
                                if ((stockRecevingDetailVM.FromTime.Value.Hour == 0 && stockRecevingDetailVM.FromTime.Value.Minute == 0) || (stockRecevingDetailVM.ToTime.Value.Hour == 0 && stockRecevingDetailVM.ToTime.Value.Minute == 0))
                                {
                                    return BadRequest(new
                                    {
                                        Code = HttpStatusCode.NotModified,
                                        Success = false,
                                        Data = "Vui lòng chọn thời gian ghi nhận!",
                                    });
                                }

                                //Check số lượng ghi nhận
                                if ((qtyGND + stockRecevingDetailVM.Quantity_D) > qtyGN)
                                    return BadRequest(new { Code = HttpStatusCode.NotModified, Success = false, Data = $"Số lượng ghi nhận không được lớn hơn {qtyGN}." });

                                var stockReceivingDetailViewModel = new StockReceivingDetailViewModel
                                {
                                    StockReceivingDetailId = Guid.NewGuid(),
                                    DateKey = dateKey,
                                    FromTime = new DateTime(FromDate.Value.Year, FromDate.Value.Month, FromDate.Value.Day, stockRecevingDetailVM.FromTime.Value.Hour, stockRecevingDetailVM.FromTime.Value.Minute, 0),
                                    ToTime = new DateTime(WorkDate.Value.Year, WorkDate.Value.Month, WorkDate.Value.Day, stockRecevingDetailVM.ToTime.Value.Hour, stockRecevingDetailVM.ToTime.Value.Minute, 0),
                                    ProductId = productionOrderViewModel.ProductId,
                                    ProductAttributes = productionOrderViewModel.ProductAttributes,
                                    Quantity = stockRecevingDetailVM.Quantity_D,
                                    StockRecevingType = item.CatalogCode,
                                    CustomerReference = productionOrderViewModel.TaskId,
                                    StockId = stockid,
                                    CreateTime = currentDate,
                                    CreateBy = CurrUserId,
                                    DepartmentId = productionOrderViewModel.DepartmentId,
                                    Phase = productionOrderViewModel.Phase,
                                    MovementType = "ADD"

                                };
                                _unitOfWork.StockRecevingDetailRepository.Create(stockReceivingDetailViewModel);
                            }
                            else
                            {
                                return BadRequest(new
                                {
                                    Code = HttpStatusCode.NotModified,
                                    Success = false,
                                    Data = "Vui lòng nhập số lượng ghi nhận!",
                                });
                            }
                            #endregion


                        }
                        //Thêm stock receiving detail nếu type = không  đạt
                        if (item.CatalogCode == "KD")
                        {

                            if (stockRecevingDetailVM.Quantity_KD != null && stockRecevingDetailVM.Quantity_KD != 0)
                            {
                                if ((stockRecevingDetailVM.FromTime.Value.Hour == 0 && stockRecevingDetailVM.FromTime.Value.Minute == 0) || (stockRecevingDetailVM.ToTime.Value.Hour == 0 && stockRecevingDetailVM.ToTime.Value.Minute == 0))
                                {
                                    return BadRequest(new
                                    {
                                        Code = HttpStatusCode.NotModified,
                                        Success = false,
                                        Data = "Vui lòng chọn thời gian ghi nhận!",
                                    });
                                }

                                #region //Lưu tồn kho
                                //Get datekey from documentDate
                                var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);

                                StockReceivingDetailViewModel stockReceivingDetailViewModel = new StockReceivingDetailViewModel
                                {
                                    StockReceivingDetailId = Guid.NewGuid(),
                                    DateKey = dateKey,
                                    FromTime = new DateTime(FromDate.Value.Year, FromDate.Value.Month, FromDate.Value.Day, stockRecevingDetailVM.FromTime.Value.Hour, stockRecevingDetailVM.FromTime.Value.Minute, 0),
                                    ToTime = new DateTime(WorkDate.Value.Year, WorkDate.Value.Month, WorkDate.Value.Day, stockRecevingDetailVM.ToTime.Value.Hour, stockRecevingDetailVM.ToTime.Value.Minute, 0),
                                    ProductId = productionOrderViewModel.ProductId,
                                    ProductAttributes = productionOrderViewModel.ProductAttributes,
                                    Quantity = stockRecevingDetailVM.Quantity_KD,
                                    StockRecevingType = item.CatalogCode,
                                    CustomerReference = productionOrderViewModel.TaskId,
                                    StockId = stockid,
                                    CreateTime = currentDate,
                                    CreateBy = CurrUserId,
                                    DepartmentId = productionOrderViewModel.DepartmentId,
                                    Phase = productionOrderViewModel.Phase,
                                    MovementType = "ADD"
                                };
                                _unitOfWork.StockRecevingDetailRepository.Create(stockReceivingDetailViewModel);
                            }
                            #endregion
                        }
                    }
                }
            }

            #region Giảm tồn kho (transferModel)
            if (usageQuantityViewModels != null && usageQuantityViewModels.Count > 0)
            {
                foreach (var stockTransferVM in usageQuantityViewModels)
                {
                    #region //Giảm tồn kho
                    if (stockTransferVM.BMSCHDC != null && stockTransferVM.BMSCHDC != 0 && stockTransferVM.ProductAttributes == productionOrderViewModel.ProductAttributes)
                    {
                        //Get datekey from documentDate

                        var dateKey = _unitOfWork.UtilitiesRepository.ConvertDateTimeToInt(WorkDate);

                        var transferDetailViewModel = new TransferDetailViewModel
                        {
                            TransferDetailId = Guid.NewGuid(),
                            DateKey = dateKey,
                            FromTime = currentDate,
                            ToTime = currentDate,
                            ProductId = productionOrderViewModel.ProductId,
                            ProductAttributes = stockTransferVM.ITMNO,
                            Quantity = -stockTransferVM.BMSCHDC,
                            StockRecevingType = "D",
                            FromCustomerReference = productionOrderViewModel.TaskId,
                            //CustomerReference = switchingStagesViewModel.CustomerReference,
                            //FromStockId = stockid,
                            ToStockId = stockid,
                            CreateTime = currentDate,
                            CreateBy = CurrUserId,
                            Phase = productionOrderViewModel.Phase,
                            MovementType = "USING"
                        };
                        _unitOfWork.TransferDetailRepository.Create(transferDetailViewModel);
                    }
                    #endregion
                }
            }


            var confirmWorkCenterViewModel = new ConfirmWorkCenterViewModel();
            confirmWorkCenterViewModel.TaskId = productionOrderViewModel.TaskId;
            confirmWorkCenterViewModel.DepartmentId = productionOrderViewModel.DepartmentId;
            confirmWorkCenterViewModel.StepCode = productionOrderViewModel.StepCode;
            confirmWorkCenterViewModel.WorkCenterConfirmTime = currentDate;

            //confirmWorkCenterViewModel.StepCode = productionOrderViewModel.StepCode;

            //  Phase // 1, 2, 3,... mỗi lần đi qua cùng 1 công đoạn thì tăng số Phase lên
            //productionOrderViewModel.StepCode                                             // CAT, TKV, EPV (công đoạn nhỏ)
            //productionOrderViewModel.ProductAttributes                                    // "0101.1.5"
            //productionOrderViewModel.DepartmentId: "e8433498-c04c-42ba-9c82-b45132ef0704" // Tổ lắp ráp PXB-G4

            _unitOfWork.QualityControlRepository.CreateQualityControl2(confirmWorkCenterViewModel);


            #endregion
            try
            {
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Code = HttpStatusCode.BadRequest,
                    Success = true,
                    Data = ex.Message,
                });
            }
            return Ok(new
            {
                Code = HttpStatusCode.Created,
                Success = true,
                Data = "Ghi nhận thông tin thành công!",
            });
        }



        private ProductionManagementViewModel GetExecutionTaskByTaskId(Guid? TaskId, string stepCode = null)
        {
            var Task = (from t in _context.ThucThiLenhSanXuatModel
                            //HangTag
                        join hTemp in _context.HangTagModel on t.Barcode equals hTemp.HangTagId into hList
                        from h in hList.DefaultIfEmpty()

                            //Sản phẩm
                        join pTemp in _context.ProductModel on t.ProductId equals pTemp.ProductId into pList
                        from p in pList.DefaultIfEmpty()
                            //Công đoạn hoàn tất
                        join sTemp in _context.StockModel on t.StockId equals sTemp.StockId into sList
                        from s in sList.DefaultIfEmpty()
                            //Routing
                        join rTemp in _context.RoutingModel on t.Property6 equals rTemp.StepCode into rList
                        from r in rList.DefaultIfEmpty()
                            //Công đoạn kế tiếp
                        join tsTemp in _context.StockModel on t.ToStockId equals tsTemp.StockId into tsList
                        from ts in tsList.DefaultIfEmpty()
                            //Create By
                        join a in _context.AccountModel on t.CreateBy equals a.AccountId
                        join btemp in _context.AccountModel on t.LastEditBy equals btemp.AccountId into list1
                        from b in list1.DefaultIfEmpty()
                        where t.TaskId == TaskId
                        select new ProductionManagementViewModel
                        {
                            TaskId = t.TaskId,
                            //WorkFlowId = t.WorkFlowId,
                            ParentTaskId = t.ParentTaskId,
                            Unit = t.Unit,
                            //WorkShop = "Phôi",
                            //LoaiGiaoDich = "M02 - Nhập chi tiết sau gia công",
                            StepCode = s.StockCode,
                            StepName = s.StockName,
                            StepId = s.StockId,
                            Barcode = t.Barcode,
                            ProductionOrder_StartDate = t.StartDate,
                            ProductionOrder_EstimateEndDate = t.EstimateEndDate,
                            WorkDate = t.StartDate,
                            Qty = (int)t.Qty,
                            ProductCode = p.ERPProductCode,
                            ProductName = p.ProductName,
                            ProductAttributes = h.ProductAttribute,
                            ProductAttributesQty = t.ProductAttributesQty,
                            ProductId = t.ProductId,
                            ProductionOrder_SAP = t.Summary,
                            CreateByFullName = a.FullName,
                            CreateTime = t.CreateTime,
                            LastEditByFullName = b.FullName,
                            LastEditTime = t.LastEditTime,
                            Actived = t.Actived,
                            ToStockCode = t.ToStockCode,
                            ToStockName = ts.StockName,
                            IsWorkCenterCompleted = t.IsWorkCenterCompleted,
                            WorkCenterConfirmTime = t.WorkCenterConfirmTime,
                            ConfirmWorkCenter = r.WorkCenter,
                            Property1 = t.Property1,
                            Property2 = t.Property2
                        }).FirstOrDefault();

            if (Task != null)
            {
                var data = _context.TaskModel.Where(x => x.TaskId == Task.ParentTaskId).FirstOrDefault();
                if (data != null)
                {
                    Task.ProductionOrder = data.Property5;
                    var Dot = _context.TaskModel.Where(x => x.TaskId == data.ParentTaskId).FirstOrDefault();
                    if (Dot != null)
                    {
                        Task.Summary = Dot.Summary;

                    }
                }
                //RoutingInventer
                var routing = _context.RoutingInventorModel.Where(x => x.ITMNO == Task.ProductAttributes && x.MATNR == Task.ProductCode).FirstOrDefault();

                if (routing != null)
                {
                    Task.ProductAttributesName = routing.KTEXT;
                    Task.ProductAttributesUnit = routing.BMEIN;
                    Task.ProductAttributesQty = routing.BMSCH * (Task.Qty ?? 0);

                    //string sqlQueryView_BOM_Inventor_Rip = "SELECT * FROM MES.View_BOM_Inventor_Rip WHERE MATNR = '" + routing.MATNR + "' AND PART_ID = '" + routing.ITMNO + "' ";
                    //var bom = _context.Database.SqlQuery<View_BOM_Inventor_RipViewModel>(sqlQueryView_BOM_Inventor_Rip).FirstOrDefault();

                    var bom = _context.View_BOM_Inventor_Rip.Where(x => x.MATNR == routing.MATNR && x.PART_ID == routing.ITMNO)
                                                            .Select(x => new View_BOM_Inventor_RipViewModel { POT12 = x.POT12 }).FirstOrDefault();
                    if (bom != null)
                        Task.POT12 = bom.POT12;
                }

                //Lấy thông tin chi tiết đã làm
                //Số lượng confirm: trên 1 pallet lấy số lượng phase lớn nhất
                var QtyD = (from sR in _context.StockReceivingDetailModel
                                //Product
                            join pTemp in _context.ProductModel on sR.ProductId equals pTemp.ProductId into pList
                            from p in pList.DefaultIfEmpty()
                                //Stock
                            join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                            from s in sList.DefaultIfEmpty()
                                //ttlsx
                            join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                            where
                            //Sản phẩm
                            p.ProductCode == Task.ProductCode
                            //Mã Chi tiết
                            && sR.ProductAttributes == Task.ProductAttributes
                            && s.StockCode == Task.StepCode
                            && sR.StockRecevingType == "D"
                            && ttlsx.ParentTaskId == Task.ParentTaskId
                            && ttlsx.TaskId == Task.TaskId
                            group new { sR } by new { sR.Phase } into g
                            select new
                            {
                                Phase = g.Key.Phase,
                                Quantity = g.Sum(x => x.sR.Quantity)
                            }).OrderByDescending(x => x.Phase).FirstOrDefault();
                //Task.ProductAttributesQtyD = QtyD?.Quantity ?? 0;
                Task.Phase = QtyD?.Phase;

                //Số lượng ghi nhận hàng đạt
                var qtyGN = (from sR in _context.StockReceivingDetailModel
                                 //ttlsx
                             join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                             where
                             sR.ProductAttributes == Task.ProductAttributes
                             && (!string.IsNullOrEmpty(stepCode) ? ttlsx.Property6 == stepCode : ttlsx.Property6 == Task.StepCode)
                             && sR.StockRecevingType == "D"
                             && ttlsx.ParentTaskId == Task.ParentTaskId
                             //&& ttlsx.TaskId == Task.TaskId
                             select new
                             {
                                 Quantity = sR.Quantity
                             }).ToList().Sum(x => x.Quantity);

                Task.ProductAttributesQtyD = qtyGN ?? 0;
            }

            return Task;
        }
        private ProductionManagementViewModel2 GetExecutionTaskByTaskId2(Guid? TaskId, string stepCode = null)
        {
            var Task = (from t in _context.ThucThiLenhSanXuatModel
                            //HangTag
                        join hTemp in _context.HangTagModel on t.Barcode equals hTemp.HangTagId into hList
                        from h in hList.DefaultIfEmpty()

                            //Sản phẩm
                        join pTemp in _context.ProductModel on t.ProductId equals pTemp.ProductId into pList
                        from p in pList.DefaultIfEmpty()
                            //Công đoạn hoàn tất
                        join sTemp in _context.StockModel on t.StockId equals sTemp.StockId into sList
                        from s in sList.DefaultIfEmpty()
                            //Routing
                        join rTemp in _context.RoutingModel on t.Property6 equals rTemp.StepCode into rList
                        from r in rList.DefaultIfEmpty()
                            //Công đoạn kế tiếp
                        join tsTemp in _context.StockModel on t.ToStockId equals tsTemp.StockId into tsList
                        from ts in tsList.DefaultIfEmpty()

                            //Create By
                            //join a in _context.AccountModel on t.CreateBy equals a.AccountId
                        join aTemp in _context.AccountModel on t.CreateBy equals aTemp.AccountId into aList
                        from a in aList.DefaultIfEmpty()

                        join btemp in _context.AccountModel on t.LastEditBy equals btemp.AccountId into list1
                        from b in list1.DefaultIfEmpty()
                        where t.TaskId == TaskId
                        select new ProductionManagementViewModel2
                        {
                            TaskId = t.TaskId,
                            //WorkFlowId = t.WorkFlowId,
                            ParentTaskId = t.ParentTaskId,
                            Unit = t.Unit,
                            //WorkShop = "Phôi",
                            //LoaiGiaoDich = "M02 - Nhập chi tiết sau gia công",
                            StepCode = s.StockCode,
                            StepName = s.StockName,
                            StepId = s.StockId,
                            Barcode = t.Barcode,
                            ProductionOrder_StartDate = t.StartDate,
                            ProductionOrder_EstimateEndDate = t.EstimateEndDate,
                            WorkDate = t.StartDate,
                            Qty = t.Qty,
                            ProductCode = p.ERPProductCode,
                            ProductName = p.ProductName,
                            ProductAttributes = h.ProductAttribute,
                            ProductAttributesQty = t.ProductAttributesQty,
                            ProductId = t.ProductId,
                            ProductionOrder_SAP = t.Summary,
                            CreateByFullName = a.FullName,
                            CreateTime = t.CreateTime,
                            LastEditByFullName = b.FullName,
                            LastEditTime = t.LastEditTime,
                            Actived = t.Actived,
                            ToStockCode = t.ToStockCode,
                            ToStockName = ts.StockName,
                            IsWorkCenterCompleted = t.IsWorkCenterCompleted,
                            WorkCenterConfirmTime = t.WorkCenterConfirmTime,
                            ConfirmWorkCenter = r.WorkCenter,
                            Property1 = t.Property1,
                            Property2 = t.Property2
                        }).FirstOrDefault();

            if (Task != null)
            {
                var data = _context.TaskModel.Where(x => x.TaskId == Task.ParentTaskId).FirstOrDefault();
                if (data != null)
                {
                    Task.ProductionOrder = data.Property5;
                    var Dot = _context.TaskModel.Where(x => x.TaskId == data.ParentTaskId).FirstOrDefault();
                    if (Dot != null)
                    {
                        Task.Summary = Dot.Summary;

                    }
                }
                //RoutingInventer
                var routing = _context.RoutingInventorModel.Where(x => x.ITMNO == Task.ProductAttributes && x.MATNR == Task.ProductCode).FirstOrDefault();

                if (routing != null)
                {
                    Task.ProductAttributesName = routing.KTEXT;
                    Task.ProductAttributesUnit = routing.BMEIN;
                    Task.ProductAttributesQty = routing.BMSCH * (Task.Qty ?? 0);

                    //string sqlQueryView_BOM_Inventor_Rip = "SELECT * FROM MES.View_BOM_Inventor_Rip WHERE MATNR = '" + routing.MATNR + "' AND PART_ID = '" + routing.ITMNO + "' ";
                    //var bom = _context.Database.SqlQuery<View_BOM_Inventor_RipViewModel>(sqlQueryView_BOM_Inventor_Rip).FirstOrDefault();

                    var bom = _context.View_BOM_Inventor_Rip.Where(x => x.MATNR == routing.MATNR && x.PART_ID == routing.ITMNO)
                                                            .Select(x => new View_BOM_Inventor_RipViewModel { POT12 = x.POT12 }).FirstOrDefault();
                    if (bom != null)
                        Task.POT12 = bom.POT12;
                }

                //Lấy thông tin chi tiết đã làm
                //Số lượng confirm: trên 1 pallet lấy số lượng phase lớn nhất
                var QtyD = (from sR in _context.StockReceivingDetailModel
                                //Product
                            join pTemp in _context.ProductModel on sR.ProductId equals pTemp.ProductId into pList
                            from p in pList.DefaultIfEmpty()
                                //Stock
                            join sTemp in _context.StockModel on sR.StockId equals sTemp.StockId into sList
                            from s in sList.DefaultIfEmpty()
                                //ttlsx
                            join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                            where
                            //Sản phẩm
                            p.ProductCode == Task.ProductCode
                            //Mã Chi tiết
                            && sR.ProductAttributes == Task.ProductAttributes
                            && s.StockCode == Task.StepCode
                            && sR.StockRecevingType == "D"
                            && ttlsx.ParentTaskId == Task.ParentTaskId
                            && ttlsx.TaskId == Task.TaskId
                            group new { sR } by new { sR.Phase } into g
                            select new
                            {
                                Phase = g.Key.Phase,
                                Quantity = g.Sum(x => x.sR.Quantity)
                            }).OrderByDescending(x => x.Phase).FirstOrDefault();
                //Task.ProductAttributesQtyD = QtyD?.Quantity ?? 0;
                Task.Phase = QtyD?.Phase;

                //Số lượng ghi nhận hàng đạt
                var qtyGN_old = (from sR in _context.StockReceivingDetailModel
                                     //ttlsx
                                 join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                                 where
                                 sR.ProductAttributes == Task.ProductAttributes
                                 && (!string.IsNullOrEmpty(stepCode) ? ttlsx.Property6 == stepCode : ttlsx.Property6 == Task.StepCode)
                                 && sR.StockRecevingType == "D"
                                 && ttlsx.ParentTaskId == Task.ParentTaskId
                                 //&& ttlsx.TaskId == Task.TaskId
                                 select new
                                 {
                                     Quantity = sR.Quantity
                                 }).ToList().Sum(x => x.Quantity);

                // 23.11.2023 Tien update: tính toán lại số lượng ghi nhận theo công đoạn của TẤT CẢ các thẻ treo cùng 1 lệnh
                // sR.CustomerReference: thẻ treo ID (HangTagId)
                var qtyGN = (from sR in _context.StockReceivingDetailModel
                                 //ttlsx
                             join ttlsx in _context.ThucThiLenhSanXuatModel on sR.CustomerReference equals ttlsx.TaskId
                             where
                             sR.ProductAttributes == Task.ProductAttributes
                             && (!string.IsNullOrEmpty(stepCode) ? ttlsx.Property6 == stepCode : ttlsx.Property6 == Task.StepCode)
                             && sR.StockRecevingType == "D"
                             && ttlsx.ParentTaskId == Task.ParentTaskId
                             //&& ttlsx.TaskId == Task.TaskId
                             select new
                             {
                                 Quantity = sR.Quantity
                             }).ToList().Sum(x => x.Quantity);

                // Lấy tất cả lệnh liên quan
                var taskIds = (from ttlsx in _context.ThucThiLenhSanXuatModel
                               where ttlsx.ParentTaskId == Task.ParentTaskId
                               select ttlsx.TaskId).Distinct().ToList();

                // Tính toán tất cả sl ghi nhận theo công đoạn
                var qtyGN_new = (from sR in _context.StockReceivingDetailModel
                                 join sm in _context.StockModel on sR.StockId equals sm.StockId
                                 where taskIds.Contains((Guid)sR.CustomerReference)
                                 && sR.ProductAttributes == Task.ProductAttributes
                                 && sR.MovementType == "ADD"
                                 && sR.StockRecevingType == "D"
                                 && sm.StockCode == stepCode
                                 select sR.Quantity).Sum();

                //Task.ProductAttributesQtyD = qtyGN ?? 0;
                Task.ProductAttributesQtyD = qtyGN_new ?? 0;
            }

            return Task;
        }


        #region Ghi nhận sản lượng - Lưu thông tin
        /// <summary>
        /// Lấy thông tin lịch sử ghi nhận sản lượng
        /// </summary>
        /// <returns></returns>
        [HttpGet("ProductionRecordHistory")]
        public ActionResult GET([FromQuery] ProductionRecordHistoryVm recordHistoryVm)
        {
            var data = _unitOfWork.ProductionManagementRepository.GetProductionRecordhistory(recordHistoryVm.TTLSX, recordHistoryVm.fromTime, recordHistoryVm.toTime, recordHistoryVm.itmno, recordHistoryVm.StepCode);

            return Ok(data);
        }
        #endregion

        #region Ghi nhận sản lượng - Lấy thông tin tổ
        /// <summary>API Lấy thông tin tổ</summary>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/Productionrecording/GetDepartentBy?TTLSX={TTLSX}&fromTime={FromTime}&toTime={&toTime}&itmno={itmno}
        ///     Params: 
        ///             + version : 1
        ///             + TTLSX : bf152d1b-5773-4b3d-911e-bc887c066bce
        ///             + fromTime : 05/09/2022 7:30:00 AM
        ///             + toTime : 05/09/2022 11:00:00 AM
        ///             + itmno : 0101.b.9
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": {
        ///             "department": "20000132 | Tổ Ghép - Lọng PX Phôi",
        ///             "workshop": "20000052 | Phân xưởng Phôi"
        ///         },
        ///         "additionalData": null
        ///     }
        /// 
        ///</remarks>
        [HttpGet("GetDepartentBy")]
        public ActionResult GET(Guid TTLSX, DateTime fromTime, DateTime toTime, string itmno)
        {
            var Department = _unitOfWork.ProductionManagementRepository.GetDepartment(TTLSX, fromTime, toTime, itmno);
            if (Department != null && (Department.DepartmentId != null || Department.DepartmentId != Guid.Empty))
            {
                var Workshop = (from d in _context.DepartmentModel
                                    //Department
                                join w in _context.WorkShopModel on d.WorkShopId equals w.WorkShopId
                                where d.DepartmentId == Department.DepartmentId
                                select new WorkShopViewModel
                                {
                                    WorkShopId = w.WorkShopId,
                                    WorkShopCode = w.WorkShopCode,
                                    WorkShopName = w.WorkShopName,
                                    OrderIndex = w.OrderIndex,
                                    Actived = w.Actived
                                }).FirstOrDefault();
                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Data = new
                    {
                        Department = Department.DepartmentCode + " | " + Department.DepartmentName,
                        Workshop = Workshop.WorkShopCode + " | " + Workshop.WorkShopName

                    }
                });
            }
            else
            {
                return NotFound(new ApiResponse { Code = 404, IsSuccess = true });
            }
        }
        #endregion

        #region Lấy thông tin phòng ban - tổ
        /// <summary> API lấy thông tin phân xưởng - tổ</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// GET
        /// 
        ///     Url: /api/v{version}/MES/ProductionRecording/GetWorkShopDepartment
        ///     Params: 
        ///             + version : 1
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": null,
        ///         "data": {
        ///             "listWorkShop": [
        ///                 {
        ///                     "workShopId": "02d1f8f8-2f4a-4c37-be4b-0efaab78ad31",
        ///                     "display": "20000051 | Phân xưởng Bàn - Ghế 4"
        ///                 },
        ///                 {
        ///                     "workShopId": "d691e032-b211-4c75-a984-03ce68febb91",
        ///                     "display": "20000512 | Phân xưởng Bàn - Ghế 5"
        ///                 }
        ///             ],
        ///             "listDepartment": [
        ///                 {
        ///                     "departmentId": "85d08d29-3df0-4f0a-98da-02dff62a7c35",
        ///                     "display": "20000075 | Tổ lắp ráp đóng gói PXB-G5",
        ///                     "workShopId": "d691e032-b211-4c75-a984-03ce68febb91"
        ///                 },
        ///                 {
        ///                     "departmentId": "03e1e751-0677-4d0d-b31c-141e59fdeaef",
        ///                     "display": "20000103 | Tổ sơn PXB-G5",
        ///                     "workShopId": "d691e032-b211-4c75-a984-03ce68febb91"
        ///                 }
        ///             ]
        ///         },
        ///         "additionalData": null
        ///     }
        ///
        /// 
        ///</remarks>
        //[HttpGet("GetWorkShopDepartment_old")]
        //public IActionResult GET()
        //{
        //    var CompanyCode = CurrentUser.CompanyCode;
        //    #region Phân xưởng
        //    var companyId = _context.CompanyModel.Where(x => x.CompanyCode == CompanyCode).Select(x => x.CompanyId).FirstOrDefault();
        //    var ListWorkShopId = _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == companyId).OrderBy(p => p.OrderIndex).Select(x => new { x.WorkShopId, display = x.WorkShopCode + " | " + x.WorkShopName }).ToList();
        //    #endregion

        //    #region Tổ
        //    var ListDepartment = _context.DepartmentModel.Where(x => x.Actived == true).Select(x => new { x.DepartmentId, display = x.DepartmentCode + " | " + x.DepartmentName, x.WorkShopId });
        //    #endregion

        //    return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = new { ListWorkShop = ListWorkShopId, ListDepartment } });
        //}


        [HttpGet("GetWorkShopDepartment")]
        public IActionResult GET()
        {
            var CompanyCode = CurrentUser.CompanyCode;
            #region Phân xưởng
            var companyId = _context.CompanyModel.Where(x => x.CompanyCode == CompanyCode).Select(x => x.CompanyId).FirstOrDefault();
            var ListWorkShopId = _context.WorkShopModel.Where(p => p.Actived == true && p.CompanyId == companyId).OrderBy(p => p.OrderIndex).Select(x => new { x.WorkShopId, display = x.WorkShopCode + " | " + x.WorkShopName }).ToList();
            #endregion

            #region Tổ
            var ListDepartmentTemp = _context.DepartmentModel.Where(x => x.Actived == true && x.CompanyId == companyId).ToList();
            var ListDepartment = ListDepartmentTemp.Select(x => new { x.DepartmentId, display = x.DepartmentCode + " | " + x.DepartmentName, x.WorkShopId });
            #endregion

            var departmentArr = ListDepartmentTemp.Select(d => d.DepartmentCode).ToArray();

            var departmentRoutingMapping = _context.DepartmentRoutingMapping.Where(d => departmentArr.Contains(d.DepartmentCode)).ToList();

            return Ok(new ApiResponse { Code = 200, IsSuccess = true, Data = new { ListWorkShop = ListWorkShopId, ListDepartment, ListDepartmentRoutingMapping = departmentRoutingMapping } });
        }

        #endregion

    }
}