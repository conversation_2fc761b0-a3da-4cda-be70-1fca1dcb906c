FUNCTION zmes_fm_trans_nkmh_qc.
*"----------------------------------------------------------------------
*"*"Local Interface:
*"  IMPORTING
*"     VALUE(I_MBLNR) TYPE  MBLNR
*"     VALUE(I_MJAHR) TYPE  MJAHR
*"     VALUE(I_WERKS) TYPE  WERKS_D
*"     VALUE(I_SAMPLE) TYPE  ZST_MES_INF_QC_SAMPLE OPTIONAL
*"     VALUE(I_QSTICHVERF) TYPE  QSTICHVERF
*"     VALUE(I_MATNR) TYPE  MATNR OPTIONAL
*"     VALUE(I_RESULT) TYPE  CHAR10
*"     VALUE(I_QTY_POSTING) TYPE  RQEVA
*"     VALUE(I_VORNR) TYPE  VORNR DEFAULT 0010
*"     VALUE(I_BLDAT) TYPE  BLDAT
*"     VALUE(I_BUDAT) TYPE  BUDAT
*"     VALUE(I_BREAK) TYPE  CHAR1 OPTIONAL
*"     VALUE(I_GUID) TYPE  GUID OPTIONAL
*"  EXPORTING
*"     VALUE(E_RETURN) TYPE  BAPIRETURN
*"  TABLES
*"      T_CHAR STRUCTURE  ZST_MES_INF_QC_CHARAC OPTIONAL
*"      T_DEF STRUCTURE  ZST_MES_INF_QC_DEFECT OPTIONAL
*"      T_RETURN STRUCTURE  BAPIRET2
*"----------------------------------------------------------------------
  TYPES: ty_mt_qamk          TYPE STANDARD TABLE OF qamkr.
  TYPES BEGIN OF lty_def.
  TYPES: prueflos	TYPE qplos.
      INCLUDE TYPE zst_mes_inf_qc_defect.
  TYPES END OF lty_def.
  TYPES: BEGIN OF lty_qpmz,
           zaehler    TYPE qzaehler, "Plant
           mkmnr      TYPE qmerknr, "Char name
           version    TYPE qversnrmk, "version
           katab1     TYPE qkttab, " selected set
           katalgart1 TYPE qkatausw, " Group type
           auswmenge1 TYPE qcgrausw, "Group code
           version1   TYPE qversnr, "group version
           code       TYPE qcode, "code
           version_c  TYPE qversnr, "version
           kurztext   TYPE kurztext,
         END OF lty_qpmz.
  TYPES: BEGIN OF lty_lot_qty,
           prueflos TYPE qplos,
           losmenge TYPE qlosmenge,
         END OF lty_lot_qty.

  DATA: lt_def TYPE STANDARD TABLE OF lty_def,
        ls_def TYPE lty_def.
  DATA: lt_qamb   TYPE STANDARD TABLE OF qamb,
        lt_char   TYPE STANDARD TABLE OF gty_qc_char,
        lt_t_char TYPE STANDARD TABLE OF zst_mes_inf_qc_charac.
  DATA: ls_return TYPE bapireturn.
  DATA: ls_qals   TYPE qals,
        ls_qals_d TYPE qals,
        lt_qamv   TYPE STANDARD TABLE OF qamv,
        ls_qamv   TYPE qamv,
        lt_qamr   TYPE STANDARD TABLE OF qamr,
        lt_qapp   TYPE STANDARD TABLE OF qapp,
        lt_qmfel  TYPE STANDARD TABLE OF qmfel,
        ls_qmfel  TYPE qmfel.
  DATA: lv_vorglfnr TYPE qapo-vorglfnr.
  DATA: ls_qamkr     TYPE qamkr,
        lt_return_ac TYPE STANDARD TABLE OF bapiret2.
  DATA: lt_return_result TYPE STANDARD TABLE OF bapiret2,
        ls_return_result TYPE bapiret2,
        lt_char_results  TYPE STANDARD TABLE OF bapi2045d2,
        ls_char_result   TYPE bapi2045d2.
  DATA: lt_sample_results TYPE STANDARD TABLE OF  bapi2045d3,
        lt_single_results TYPE STANDARD TABLE OF  bapi2045d4.
  DATA: lt_qals TYPE STANDARD TABLE OF qals.
  DATA: ls_ud_data    TYPE bapi2045ud,
        ls_ud_return  TYPE bapi2045ud_return,
        ls_ud_stock   TYPE bapi2045d_il2,
        ls_ud_return2 TYPE bapireturn1,
        ls_stock_data TYPE /cwm/bapi2045d_il2.
  DATA: lt_system_status TYPE STANDARD TABLE OF	bapi2045ss,
        lt_user_status   TYPE STANDARD TABLE OF	bapi2045us.
  DATA: ls_t_return TYPE bapiret2.
  DATA: lt_his TYPE STANDARD TABLE OF zmes_nkmh_hd.
  DATA: lt_lot_qty TYPE STANDARD TABLE OF lty_lot_qty.
  DATA: lv_updkz LIKE qalsvb-upsl VALUE 'U'.
  DATA: ls_qdsv       TYPE qdsv,
        lv_sample_qty TYPE qprobefak.
  DATA: r_prueflos TYPE RANGE OF qplos.

  FIELD-SYMBOLS: <fs_qty_posting> TYPE rqeva,
                 <fs_lot_qty>     TYPE lty_lot_qty.
  FIELD-SYMBOLS: <fs_qamb>  LIKE LINE OF lt_qamb,
                 <fs_char>  LIKE LINE OF lt_char,
                 <fs_his>   TYPE zmes_nkmh_hd,
                 <fs_def>   TYPE lty_def,
                 <fs_qmife> TYPE qmife.

  SELECT SINGLE * FROM ztb_fm_check_act
    WHERE werks = @i_werks AND
    fname = 'MES_INF_QC' AND
    act = 'X'
    INTO @DATA(lv_fm_check).
  IF sy-subrc NE 0.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '001'.
    ls_t_return-message = TEXT-001. "'FM closed due to maintenance'.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

  IF i_guid IS INITIAL.
    i_guid = cl_system_uuid=>create_uuid_x16_static( ).
  ENDIF.

*--> get details of sampling procedure
  SELECT SINGLE *
    FROM qdsv WHERE stichprver = @i_qstichverf INTO @ls_qdsv.
  IF sy-subrc NE 0.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '002'.
    ls_t_return-message = TEXT-002.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

*** Get list of Characteristics with detail data
  MOVE-CORRESPONDING t_char[] TO lt_t_char[].

*--> Check input data.
  SELECT SINGLE werks FROM t001w INTO @DATA(lv_werks) WHERE werks = @i_werks.
  IF sy-subrc NE 0.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '003'.
    ls_t_return-message = TEXT-003.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

  SELECT SINGLE mblnr FROM mseg INTO @DATA(lv_mblnr) WHERE mblnr = @i_mblnr AND werks = @i_werks AND bwart = '101'.
  IF sy-subrc NE 0.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '004'.
    ls_t_return-message = TEXT-004.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.
  DATA: r_result TYPE RANGE OF char10.
  r_result = VALUE #( ( sign = 'I' option = 'EQ' low = 'FAIL' )
                      ( sign = 'I' option = 'EQ' low = 'PASS' ) ).
  IF i_result NOT IN r_result.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '005'.
    ls_t_return-message = TEXT-005.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

  DELETE lt_t_char WHERE outcomestatus = 'SKIP'.
  LOOP AT lt_t_char ASSIGNING FIELD-SYMBOL(<fs_t_char>).
    IF <fs_t_char>-outcomevariance IS NOT INITIAL AND
      <fs_t_char>-outcomevariance CN '0123456789'.
      CLEAR ls_t_return.
      ls_t_return-type = 'E'.
      ls_t_return-number = '001'.
      ls_t_return-message = |Char { <fs_t_char>-qcinfoname }/outcomevariance should contains number only|.
      APPEND ls_t_return TO t_return.
    ENDIF.
  ENDLOOP.

  IF t_return[] IS NOT INITIAL.
    EXIT.
  ENDIF.


  SELECT
    1~zaehler,
    1~mkmnr,
    MAX( 1~version ) AS version,
    1~kurztext,
    3~outcomestatus,
    3~outcomevariance,
    3~sosanphamloi,
    3~notes,
    4~steuerkz
    FROM qpmt AS 1
    INNER JOIN @lt_t_char AS 3 ON upper( 1~kurztext ) = 3~qcinfoname
    INNER JOIN qpmk AS 4 ON 1~zaehler = 4~zaehler AND
                            1~version = 4~version AND
                            1~mkmnr = 4~mkmnr
    WHERE 1~zaehler = @i_werks AND
    1~sprache = 'E'
    GROUP BY
    1~zaehler,
    1~mkmnr,
    1~kurztext,
    3~outcomestatus,
    3~outcomevariance,
    3~sosanphamloi,
    3~notes,
    4~steuerkz
    INTO CORRESPONDING FIELDS OF TABLE @lt_char.

  DATA: r_char TYPE RANGE OF qmerknr.
  SELECT
    'I' AS sign,
    'EQ' AS option,
    mkmnr AS low
    FROM @lt_char AS 1
    INTO CORRESPONDING FIELDS OF TABLE @r_char.

  DATA: lt_qpmz TYPE STANDARD TABLE OF lty_qpmz.
  FIELD-SYMBOLS: <fs_qpmz> TYPE lty_qpmz.

  SELECT
    1~zaehler,
    1~mkmnr,
    1~katab1,
    1~katalgart1,
    1~auswmenge1,
    1~version1,
    2~code,
    3~kurztext
    FROM qpmz AS 1
    LEFT JOIN ( qpcd AS 2 INNER JOIN qpct AS 3 ON 2~katalogart = 3~katalogart AND
                                                  2~codegruppe = 3~codegruppe AND
                                                  2~code = 3~code AND
                                                  2~version = 3~version )
               ON 1~auswmenge1 = 2~codegruppe AND
                  1~katalgart1 = 2~katalogart AND
                  1~version1 = 2~version
    WHERE 1~zaehler = @i_werks AND
          1~mkmnr IN @r_char AND
          1~katab1 NE ''
    INTO CORRESPONDING FIELDS OF TABLE @lt_qpmz.

  SELECT
    1~zaehler,
    1~mkmnr,
    1~katab1,
    1~katalgart1,
    1~auswmenge1,
    1~version1,
    2~code,
    3~kurztext
    FROM qpmz AS 1
    LEFT JOIN ( qpcd AS 2 INNER JOIN qpct AS 3 ON 2~katalogart = 3~katalogart AND
                                                  2~codegruppe = 3~codegruppe AND
                                                  2~code = 3~code AND
                                                  2~version = 3~version )
               ON 1~auswmenge1 = 2~codegruppe AND
                  1~katalgart1 = 2~katalogart AND
                  1~version1 = 2~version
    WHERE 1~zaehler = @i_werks AND
          1~mkmnr IN @r_char AND
          1~katab1 NE ''AND
          1~katab1 = 'X' AND
          1~katalgart1 = '1'
    INTO CORRESPONDING FIELDS OF TABLE @lt_qpmz.

*--------------------------------------------------------------------*
*--> Get Mapping material document and inspection lot.
  SELECT FROM qamb AS t1
    FIELDS t1~*
    WHERE mblnr = @i_mblnr
    INTO CORRESPONDING FIELDS OF TABLE @lt_qamb .
  IF sy-subrc NE 0.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '001'.
    ls_t_return-message = 'No Inspection lot found'.
    APPEND ls_t_return TO t_return.
    EXIT.
  ELSE.
    SELECT *
      FROM zmes_nkmh_hd
      WHERE mblnr = @i_mblnr AND
      mjahr = @i_mjahr
      INTO CORRESPONDING FIELDS OF TABLE @lt_his.
    IF sy-subrc NE 0.
      SELECT
         1~prueflos,
         @i_mblnr AS mblnr,
        @i_mjahr AS mjahr
        FROM @lt_qamb AS 1
        INTO CORRESPONDING FIELDS OF TABLE @lt_his.
      MODIFY zmes_nkmh_hd FROM TABLE lt_his.
      COMMIT WORK AND WAIT.
    ENDIF.
  ENDIF.

  SELECT 'I' AS sign, 'EQ' AS option, prueflos AS low FROM @lt_qamb AS 1 INTO CORRESPONDING FIELDS OF TABLE @r_prueflos.

  SELECT t1~*
    FROM qals AS t1
    FOR ALL ENTRIES IN @lt_qamb
    WHERE prueflos = @lt_qamb-prueflos
    INTO CORRESPONDING FIELDS OF TABLE @lt_qals.

  SELECT
    prueflos,
    losmenge
    FROM qals
    FOR ALL ENTRIES IN @lt_qamb
    WHERE prueflos = @lt_qamb-prueflos
    INTO CORRESPONDING FIELDS OF TABLE @lt_lot_qty.

  ASSIGN i_qty_posting TO <fs_qty_posting>.

*--> Convert quantity to based unit
*--> Check po item with order unit. All po item must have same order unit.
  SELECT
    t1~prueflos,
    t2~meins,
    t2~umrez,
    t2~umren
    FROM qals AS t1
    INNER JOIN ekpo AS t2
    ON t1~ebeln = t2~ebeln AND t1~ebelp = t2~ebelp
    WHERE prueflos IN @r_prueflos
    INTO TABLE @DATA(lt_po).

  SELECT DISTINCT meins FROM @lt_po AS t1 INTO TABLE @DATA(lt_meins).
  DESCRIBE TABLE lt_meins LINES DATA(lv_meins_line).
  IF lv_meins_line GT 1.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '001'.
    ls_t_return-message = 'Different Order unit between PO lines'.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

  SELECT DISTINCT umrez, umren FROM @lt_po AS t1 INTO TABLE @DATA(lt_conv).
  DESCRIBE TABLE lt_conv LINES DATA(lv_conv_line).
  IF lv_meins_line GT 1.
    CLEAR ls_t_return.
    ls_t_return-type = 'E'.
    ls_t_return-number = '001'.
    ls_t_return-message = 'Different conversion factors from Order unit to Based unit between PO lines'.
    APPEND ls_t_return TO t_return.
    EXIT.
  ENDIF.

  READ TABLE lt_po INTO DATA(ls_po) INDEX 1.
  IF sy-subrc = 0.
    IF i_qty_posting-vmenge01 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge02 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge03 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge04 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge05 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge06 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge07 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge08 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
    IF i_qty_posting-vmenge09 IS NOT INITIAL.
      i_qty_posting-vmenge01 = i_qty_posting-vmenge01 * ls_po-umrez / ls_po-umren.
    ENDIF.
  ENDIF.

*--------------------------------------------------------------------*
*--> Step 1
*--> Add unplanned characteristics to inspection lot.
  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s1 IS INITIAL.
    CLEAR: ls_qals.
    FREE: lt_qamv, lt_qamr, lt_qapp, lt_qmfel.

*** Get Inspection lot details.
    CALL FUNCTION 'QG01_LOT_READ_WITH_RESULTS'
      EXPORTING
        i_prueflos    = <fs_qamb>-prueflos
      IMPORTING
        e_qals        = ls_qals
      TABLES
        t_qamv        = lt_qamv "List of characteristics
        t_qamr        = lt_qamr
        t_qapp        = lt_qapp
        t_qmfel       = lt_qmfel
      EXCEPTIONS
        lot_not_found = 1
        OTHERS        = 2.

**** Get Current Node number of Inspection lot.
*    CLEAR: lv_vorglfnr.
*    CALL FUNCTION 'QIBP_GET_VORGLFNR'
*      EXPORTING
*        i_insp_lot           = <fs_qamb>-prueflos
*        i_oper_no            = '0010'
*      IMPORTING
*        e_vorglfnr           = lv_vorglfnr
*      EXCEPTIONS
*        wrong_inspection_lot = 1
*        wrong_operation_no   = 2
*        OTHERS               = 3.

*** Adding characteristics to the Inspection lot.
    "The characteristics will be same for all inspction lot within mat. doc.
    LOOP AT lt_char ASSIGNING <fs_char>.
      CLEAR: ls_qamv, lv_sample_qty.
      TRY.
          "-->Check Characteristics exist?
          ls_qamv = lt_qamv[ verwmerkm = <fs_char>-mkmnr ].
        CATCH cx_sy_itab_line_not_found.
          "--> Add chacracteristics
          CLEAR: ls_qamkr.
          FREE: lt_return_ac.
          CASE ls_qdsv-fbkey.
            WHEN '10'.
              IF ls_qdsv-stprumf LE ls_qals-losmenge.
                lv_sample_qty = ls_qdsv-stprumf.
              ELSE.
                lv_sample_qty = ls_qals-losmenge.
              ENDIF.
            WHEN '20'.
              lv_sample_qty = ls_qals-losmenge.
            WHEN '40'.
              lv_sample_qty = trunc( ls_qdsv-prozumf * ls_qals-losmenge ).
            WHEN OTHERS.
              lv_sample_qty = ls_qals-losmenge.
          ENDCASE.

          zcl_qum_inspchar=>create_add_characteristic(
            EXPORTING
              iv_prueflos = <fs_qamb>-prueflos " Inspection Lot Number
              iv_vornr = i_vornr " Operation/Activity Number
              iv_probenr = '000001' " Sample Number (Based on an Inspection Point)
              iv_verwmerkm = <fs_char>-mkmnr " Master Inspection Characteristics
              iv_qpmk_werks = i_werks " Plant
              iv_mkversion = <fs_char>-version " Version Number of the Master Inspection Characteristic
              iv_kurztext = '' " Short Text for Inspection Characteristic
              iv_stichprver = i_qstichverf " Sampling Procedure in Inspection Characteristic
              iv_pruefeinh = lv_sample_qty " Sample Quantity Factor for Sample(Mult. Sample Unit of Msr.)
            IMPORTING
              et_return = lt_return_ac " Table with BAPI Return Information
              es_qamkr = ls_qamkr ). " Specifications and results for the characteristic
          DELETE lt_return_ac WHERE type NE 'E' AND type NE 'A' AND type NE 'F'.
          IF lt_return_ac IS INITIAL.
            COMMIT WORK AND WAIT.
          ELSE.
            LOOP AT lt_return_ac ASSIGNING FIELD-SYMBOL(<fs_return_ac>).
              CLEAR ls_t_return.
              ls_t_return-type = <fs_return_ac>-type.
              ls_t_return-id = <fs_return_ac>-id.
              ls_t_return-number = <fs_return_ac>-number.
              ls_t_return-message = <fs_return_ac>-message.
              ls_t_return-message_v2 = <fs_qamb>-prueflos.
              ls_t_return-message_v3 = 'S1'.
              APPEND ls_t_return TO t_return.
            ENDLOOP.
          ENDIF.
      ENDTRY.
    ENDLOOP.
    IF t_return[] IS NOT INITIAL.
      EXIT.
    ELSE.
      <fs_his>-s1 = 'X'.
      UPDATE zmes_nkmh_hd SET s1 = <fs_his>-s1 WHERE prueflos = <fs_qamb>-prueflos.
      COMMIT WORK AND WAIT.
      UNASSIGN <fs_his>.
    ENDIF.
  ENDLOOP.

  IF t_return[] IS NOT INITIAL.
    EXIT.
  ENDIF.

*--------------------------------------------------------------------*
*--> Step 2
*--> Add Characteristics result
  UNASSIGN <fs_his>.
  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s2 IS INITIAL.

    CLEAR: ls_qals.
    FREE: lt_qamv, lt_qamr, lt_qapp, lt_qmfel.
    " Get Inspection lot details.
    CALL FUNCTION 'QG01_LOT_READ_WITH_RESULTS'
      EXPORTING
        i_prueflos    = <fs_qamb>-prueflos
      IMPORTING
        e_qals        = ls_qals
      TABLES
        t_qamv        = lt_qamv "List of characteristics
        t_qamr        = lt_qamr
        t_qapp        = lt_qapp
        t_qmfel       = lt_qmfel
      EXCEPTIONS
        lot_not_found = 1
        OTHERS        = 2.

    FREE: lt_char_results, lt_sample_results, lt_single_results, lt_return_result.
    CLEAR: ls_char_result, ls_return_result.

    "Prepare data for uppdate characteristics result.
    LOOP AT lt_char ASSIGNING <fs_char>.
      CLEAR: ls_char_result.
      ls_char_result-insplot = <fs_qamb>-prueflos.
      ls_char_result-inspoper = i_vornr.
      TRY.
          ls_char_result-inspchar = lt_qamv[ verwmerkm = <fs_char>-mkmnr ]-merknr.
          ls_char_result-nonconf = lt_qamv[ verwmerkm = <fs_char>-mkmnr ]-sollstpumf.
        CATCH cx_sy_itab_line_not_found.
          CLEAR ls_t_return.
          ls_t_return-type = 'E'.
          ls_t_return-number = '002'.
          ls_t_return-message = |Char { <fs_char>-mkmnr } not found in Ins.lot { <fs_qamb>-prueflos }|.
          ls_t_return-message_v2 = <fs_qamb>-prueflos.
          ls_t_return-message_v3 = 'S2'.
          APPEND ls_t_return TO t_return.
          CONTINUE.
      ENDTRY.

      CASE <fs_char>-outcomestatus.
        WHEN 'PASS'.
          ls_char_result-evaluation = 'A'.
          READ TABLE lt_qpmz ASSIGNING <fs_qpmz> WITH KEY  zaehler = i_werks mkmnr = <fs_char>-mkmnr kurztext = 'Đạt yêu cầu'.
          IF sy-subrc = 0.
            ls_char_result-code1 = <fs_qpmz>-code.
            ls_char_result-code_grp1 = <fs_qpmz>-auswmenge1.
          ELSE.
            ls_char_result-mean_value = <fs_char>-outcomevariance.
          ENDIF.
          UNASSIGN <fs_qpmz>.
        WHEN 'FAIL'.
          ls_char_result-evaluation = 'R'.
          READ TABLE lt_qpmz ASSIGNING <fs_qpmz> WITH KEY  zaehler = i_werks mkmnr = <fs_char>-mkmnr kurztext = 'Không đạt yêu cầu'.
          IF sy-subrc = 0.
            ls_char_result-code1 = <fs_qpmz>-code.
            ls_char_result-code_grp1 = <fs_qpmz>-auswmenge1.
          ELSE.
            ls_char_result-mean_value = <fs_char>-outcomevariance.
          ENDIF.
          UNASSIGN <fs_qpmz>.
      ENDCASE.

      ls_char_result-closed = 'X'. "Close
      ls_char_result-evaluated = 'X'.
      "ls_char_result-valid_vals = 'X'.


      APPEND ls_char_result TO lt_char_results.
    ENDLOOP.

    CALL FUNCTION 'BAPI_INSPOPER_RECORDRESULTS'
      EXPORTING
        insplot        = <fs_qamb>-prueflos
        inspoper       = '0010'
      IMPORTING
        return         = ls_return_result
      TABLES
        char_results   = lt_char_results
        sample_results = lt_sample_results
        single_results = lt_single_results
        returntable    = lt_return_result.

    READ TABLE lt_return_result ASSIGNING FIELD-SYMBOL(<fs_return_result>) WITH KEY type = 'E'.
    IF sy-subrc NE 0.
      CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'
        EXPORTING
          wait = 'X'.
      "COMMIT WORK AND WAIT.
      WAIT UP TO 5 SECONDS.

      <fs_his>-s2 = 'X'.
      UPDATE zmes_nkmh_hd SET s2 = <fs_his>-s2 WHERE prueflos = <fs_qamb>-prueflos.
      COMMIT WORK AND WAIT.
      UNASSIGN <fs_his>.
    ELSE.
      CLEAR ls_t_return.
      ls_t_return-type = <fs_return_result>-type.
      ls_t_return-id = <fs_return_result>-id.
      ls_t_return-number = <fs_return_result>-number.
      ls_t_return-message = <fs_return_result>-message.
      ls_t_return-message_v2 = <fs_qamb>-prueflos.
      ls_t_return-message_v3 = 'S2'.
      APPEND ls_t_return TO t_return.
    ENDIF.
  ENDLOOP.

  IF t_return[] IS NOT INITIAL.
    EXIT.
  ENDIF.

*--------------------------------------------------------------------*
*--> Step 3
*--> Update Defect

  DATA: lt_qmife TYPE STANDARD TABLE OF qmife,
        lt_qierr TYPE STANDARD TABLE OF qierr.
  DATA: lv_fenum TYPE felfd.

  "Distribute defect quantity
  SELECT SUM( losmenge ) FROM @lt_lot_qty AS 1 INTO @DATA(lv_sum_losmenge).
  LOOP AT t_def ASSIGNING FIELD-SYMBOL(<fs_ddef>).
    LOOP AT lt_lot_qty ASSIGNING <fs_lot_qty>.
      APPEND INITIAL LINE TO lt_def ASSIGNING <fs_def>.
      <fs_def>-prueflos = <fs_lot_qty>-prueflos.
      <fs_def>-catalogcode = <fs_ddef>-catalogcode.
      <fs_def>-cataloggroup = <fs_ddef>-cataloggroup.
      <fs_def>-levelerror = <fs_ddef>-levelerror.
      <fs_def>-notes = <fs_ddef>-notes.
      <fs_def>-quantityerror = trunc( <fs_ddef>-quantityerror / lv_sum_losmenge ).
    ENDLOOP.
  ENDLOOP.

  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    FREE: lt_qmife, lt_qierr.
    CLEAR lv_fenum.

    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s3 IS INITIAL.

    CLEAR: ls_qals.
    FREE: lt_qamv, lt_qamr, lt_qapp, lt_qmfel.
    " Get Inspection lot details.
    CALL FUNCTION 'QG01_LOT_READ_WITH_RESULTS'
      EXPORTING
        i_prueflos    = <fs_qamb>-prueflos
      IMPORTING
        e_qals        = ls_qals
      TABLES
        t_qamv        = lt_qamv "List of characteristics
        t_qamr        = lt_qamr
        t_qapp        = lt_qapp
        t_qmfel       = lt_qmfel
      EXCEPTIONS
        lot_not_found = 1
        OTHERS        = 2.

    LOOP AT lt_def ASSIGNING <fs_def> WHERE prueflos = <fs_qamb>-prueflos.
      CLEAR ls_qmfel.
      TRY.
          ls_qmfel = lt_qmfel[ fegrp = <fs_def>-cataloggroup fecod = <fs_def>-catalogcode ].
        CATCH cx_sy_itab_line_not_found.
          SELECT MAX( fenum )
            FROM @lt_qmfel AS 1
            INTO @lv_fenum.
          lv_fenum = lv_fenum + 1.
          APPEND INITIAL LINE TO lt_qmife ASSIGNING <fs_qmife>.
          <fs_qmife>-satzart = 'Q91'.
          <fs_qmife>-prueflos = <fs_qamb>-prueflos.
          <fs_qmife>-vornr = i_vornr.
          <fs_qmife>-plnfl = '000000'.
          <fs_qmife>-posnr = lv_fenum.
          <fs_qmife>-fekat = '9'.
          <fs_qmife>-fecod = <fs_def>-catalogcode.
          <fs_qmife>-fegrp = <fs_def>-cataloggroup.
          <fs_qmife>-anzfehler = <fs_def>-quantityerror.
          UNASSIGN <fs_qmife>.
      ENDTRY.
    ENDLOOP.

    CHECK lt_qmife[] IS NOT INITIAL.

    CALL FUNCTION 'QIRF_GET_DEFECT_ITEMS2'
      EXPORTING
*       I_SEND_PROTOCOL_MAIL       = ' '
        i_subsys     = '000001'
      TABLES
        t_qmifetab   = lt_qmife
        t_qierrtab   = lt_qierr
      EXCEPTIONS
        no_authority = 1
        OTHERS       = 2.
    LOOP AT lt_qierr ASSIGNING FIELD-SYMBOL(<fs_qierr>) WHERE msgtype = 'E'.
      CLEAR ls_t_return.
      ls_t_return-type = <fs_qierr>-msgtype.
      ls_t_return-id = <fs_qierr>-msgid.
      ls_t_return-number = <fs_qierr>-msgnr.
      ls_t_return-message = <fs_qierr>-msgtext.
      ls_t_return-message_v2 = <fs_qamb>-prueflos.
      ls_t_return-message_v3 = 'S3'.
      APPEND ls_t_return TO t_return.
    ENDLOOP.
    IF sy-subrc NE 0.
      <fs_his>-s3 = 'X'.
      UPDATE zmes_nkmh_hd SET s3 = <fs_his>-s3 WHERE prueflos = <fs_qamb>-prueflos.
      COMMIT WORK AND WAIT.
      UNASSIGN <fs_his>.
    ENDIF.
  ENDLOOP.

  IF t_return[] IS NOT INITIAL.
    EXIT.
  ENDIF.

*--------------------------------------------------------------------*
*--> Step 4
*--> UD code
  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s4 IS INITIAL.

    CLEAR: ls_ud_return,
    ls_ud_stock,
    ls_ud_return2,
    ls_stock_data.
    FREE: lt_system_status,
          lt_user_status.

    ls_ud_data-insplot = <fs_qamb>-prueflos.
    ls_ud_data-ud_plant = i_werks.
    ls_ud_data-ud_selected_set = 'QDCL'.
    ls_ud_data-ud_code_group = 'QDCL'.
    ls_ud_data-ud_force_completion = 'X'.
    ls_ud_data-ud_recorded_by_user = sy-uname.
    ls_ud_data-ud_recorded_on_date = sy-datum.
    ls_ud_data-ud_recorded_at_time = sy-uzeit.
    CASE i_result.
      WHEN 'PASS'.
        ls_ud_data-ud_code = 'QD01'.
      WHEN 'FAIL'.
        ls_ud_data-ud_code = 'QD02'.
      WHEN 'ACCEPT_FAIL'.
        ls_ud_data-ud_code = 'QD03'.
    ENDCASE.

    CALL FUNCTION 'BAPI_INSPLOT_SETUSAGEDECISION'
      EXPORTING
        number          = <fs_qamb>-prueflos
        ud_data         = ls_ud_data
        ud_mode         = 'D'
      IMPORTING
        ud_return_data  = ls_ud_return
        stock_data      = ls_ud_stock
        return          = ls_ud_return2
        /cwm/stock_data = ls_stock_data
      TABLES
        system_status   = lt_system_status
        user_status     = lt_user_status.
    IF ls_ud_return2-type NE 'E'.
      COMMIT WORK AND WAIT.
      <fs_his>-s4 = 'X'.
      UPDATE zmes_nkmh_hd SET s4 = <fs_his>-s4 WHERE prueflos = <fs_qamb>-prueflos.
      COMMIT WORK AND WAIT.
      UNASSIGN <fs_his>.
    ELSE.
      CLEAR ls_t_return.
      ls_t_return-type = ls_ud_return2-type.
      ls_t_return-id = ls_ud_return2-id.
      ls_t_return-number = ls_ud_return2-number.
      ls_t_return-message = ls_ud_return2-message.
      ls_t_return-message_v2 = <fs_qamb>-prueflos.
      ls_t_return-message_v3 = 'S4'.
      APPEND ls_t_return TO t_return.
    ENDIF.
  ENDLOOP.

  IF t_return[] IS NOT INITIAL.
    EXIT.
  ENDIF.

*--------------------------------------------------------------------*
*--> Step 5
*--> Stock posting
  TYPES BEGIN OF lty_rqeva.
  TYPES: prueflos	TYPE qplos.
      INCLUDE TYPE rqeva.
  TYPES END OF lty_rqeva.

  DATA: lt_qty TYPE STANDARD TABLE OF lty_rqeva,
        ls_qty TYPE lty_rqeva.
  DATA: ls_imkpf     TYPE imkpf,
        ls_emkpf     TYPE emkpf,
        lt_imseg_tab TYPE STANDARD TABLE OF imseg,
        lt_emseg_tab TYPE STANDARD TABLE OF emseg.
  DATA: ls_qals_post LIKE qals.
  DATA: lv_lmengezub LIKE qals-lmengezub,
        lv_lmengegeb LIKE qals-lmengezub.
  DATA: lt_tq07m TYPE STANDARD TABLE OF tq07m.
  DATA: lv_seq           TYPE c LENGTH 2,
        lv_fieldname     TYPE char20,
        lv_fieldname2    TYPE char20,
        lv_fieldname_mvt TYPE char20,
        lv_tabix         LIKE sy-tabix.

  FIELD-SYMBOLS: <fs_qty>       TYPE lty_rqeva,
                 <fs_tq07m>     TYPE tq07m,
                 <fs_imseg>     TYPE imseg,
                 <fs_value>     TYPE any,
                 <fs_value_f>   TYPE any,
                 <fs_value_t>   TYPE any,
                 <fs_qty_post>  TYPE any,
                 <fs_qals_post> LIKE qals.
  "Distribute posting quantity

  LOOP AT lt_lot_qty ASSIGNING <fs_lot_qty>.
    APPEND INITIAL LINE TO lt_qty ASSIGNING <fs_qty>.
    <fs_qty>-prueflos = <fs_lot_qty>-prueflos.
    lv_seq = 0.
    WHILE lv_seq LT 9.
      lv_seq = lv_seq + 1.
      CLEAR: lv_fieldname, lv_fieldname_mvt.
      CONCATENATE 'VMENGE0' lv_seq INTO lv_fieldname.
      ASSIGN COMPONENT lv_fieldname OF STRUCTURE <fs_qty_posting> TO <fs_value_f>.
      ASSIGN COMPONENT lv_fieldname OF STRUCTURE <fs_qty> TO <fs_value_t>.
      IF <fs_lot_qty>-losmenge GT 0.
        IF <fs_value_f> GT 0.
          IF <fs_value_f> = <fs_lot_qty>-losmenge.
            <fs_value_t> = <fs_value_f>.
            <fs_value_f> = 0.
            <fs_lot_qty>-losmenge = 0.
          ELSEIF <fs_value_f> GT <fs_lot_qty>-losmenge.
            <fs_value_t> = <fs_lot_qty>-losmenge.
            <fs_value_f> = <fs_value_f> - <fs_lot_qty>-losmenge.
            <fs_lot_qty>-losmenge = 0.
          ELSEIF <fs_value_f> LT <fs_lot_qty>-losmenge.
            <fs_value_t> = <fs_value_f>.
            <fs_value_f> = 0.
            <fs_lot_qty>-losmenge = <fs_lot_qty>-losmenge - <fs_value_t>.
          ENDIF.
        ENDIF.
      ENDIF.
      UNASSIGN: <fs_value_f>, <fs_value_t>.
    ENDWHILE.
  ENDLOOP.

  "Get list of movement type
  SELECT *
    FROM tq07m
    INTO CORRESPONDING FIELDS OF TABLE @lt_tq07m.

  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    CLEAR: ls_imkpf, ls_emkpf, ls_qals_post, lv_lmengezub, lv_lmengegeb.
    FREE: lt_imseg_tab, lt_emseg_tab.

* Check if current step was processed or not. Cancel if step was processed.
    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s5 IS INITIAL.
* In some cases, the stock was processed manually. In this case, this step will be cancelled.
    DATA: lt_qamb_his TYPE STANDARD TABLE OF qamb.
    FREE: lt_qamb_his.
    SELECT *
      FROM qamb
      WHERE prueflos = @<fs_qamb>-prueflos AND
      typ = '3'
      INTO CORRESPONDING FIELDS OF TABLE @lt_qamb_his.
    CHECK lt_qamb_his[] IS INITIAL. "Process only if no material documents for stock posting (mvt321) was found.

    "Initial
    CALL FUNCTION 'QAMB_REFRESH_DATA'.
    "Header
    ls_imkpf-bldat = i_bldat.
    ls_imkpf-budat = i_budat.
    ls_imkpf-bktxt = 'MES/QC/Pur/ Auto Posting'.
    "Read inspection lot details
    CALL FUNCTION 'QPSE_LOT_READ'
      EXPORTING
        i_prueflos  = <fs_qamb>-prueflos
        i_reset_lot = 'X'
      IMPORTING
        e_qals      = ls_qals_post
      EXCEPTIONS
        no_lot      = 1.
    ASSIGN ls_qals_post TO <fs_qals_post>.

    CLEAR: lv_tabix.
    READ TABLE lt_qty ASSIGNING <fs_qty> WITH KEY prueflos = <fs_qamb>-prueflos.
    IF sy-subrc = 0.
      lv_seq = 0.
      WHILE lv_seq LT 10.
        lv_seq = lv_seq + 1.
        CLEAR: lv_fieldname, lv_fieldname_mvt.
        CONCATENATE 'VMENGE0' lv_seq INTO lv_fieldname.
        ASSIGN COMPONENT lv_fieldname OF STRUCTURE <fs_qty> TO <fs_value>.
        IF <fs_value> IS NOT INITIAL.
          APPEND INITIAL LINE TO lt_imseg_tab ASSIGNING <fs_imseg>.
          lv_tabix = lv_tabix + 1.
          <fs_imseg>-line_id = lv_tabix. "Line id
          <fs_imseg>-qm_zustd = '1'. "Batch status
          <fs_imseg>-qploa = <fs_qamb>-prueflos. "Inspection lot
          <fs_imseg>-tafkz = 'X'.
          <fs_imseg>-lifnr = ls_qals_post-lifnr. "Vendor number
          <fs_imseg>-ebeln = ls_qals_post-ebeln. "Purchase order number
          <fs_imseg>-ebelp = ls_qals_post-ebelp. "Purchase order item
          <fs_imseg>-matnr = ls_qals_post-matnr. "material number
          <fs_imseg>-werks = ls_qals_post-werk. "Plant
          <fs_imseg>-lgort = ls_qals_post-lagortchrg. "Sloc
          <fs_imseg>-charg = ls_qals_post-charg. "Batch number
          <fs_imseg>-umwrk = ls_qals_post-werk. "Receiving Plant
          <fs_imseg>-umlgo = ls_qals_post-lagortchrg. "Receiving Sloc
          <fs_imseg>-umcha = ls_qals_post-charg. "Receiving batch
          <fs_imseg>-erfmg = <fs_value>. "<fs_qty>-vmenge01. "Quantity
          <fs_imseg>-erfme = ls_qals_post-mengeneinh. "Entry unit
          <fs_imseg>-bwart = lt_tq07m[ feldname = lv_fieldname ]-bwartqbst. "movement type
          <fs_imseg>-sobkz = ls_qals_post-sobkz.
          <fs_imseg>-mat_kdauf = ls_qals_post-kdauf.
          <fs_imseg>-mat_kdpos = ls_qals_post-kdpos.
          <fs_imseg>-mat_pspnr = ls_qals_post-ps_psp_pnr.

          <fs_qals_post>-lmengezub = <fs_qals_post>-lmengezub - <fs_imseg>-erfmg. "To be transfer quantity
          CONCATENATE 'LMENGE0' lv_seq INTO lv_fieldname_mvt.
          ASSIGN COMPONENT lv_fieldname_mvt OF STRUCTURE <fs_qals_post> TO <fs_qty_post>.
          <fs_qty_post> = <fs_qty_post> + <fs_imseg>-erfmg.

          IF lv_fieldname_mvt = 'LMENGE07'.
            <fs_imseg>-sgtxt = 'MES/QC/Pur/ Auto Reverse'.
            <fs_imseg>-grund = '0001'.
            <fs_imseg>-insmk = 'X'.
            <fs_imseg>-kzbew = 'B'.
            <fs_imseg>-lgtyp = <fs_qals_post>-lgtyp.
            <fs_imseg>-lgpla = <fs_qals_post>-lgpla.
            <fs_imseg>-lfbja = <fs_qals_post>-mjahr.
            <fs_imseg>-lfbnr = <fs_qals_post>-mblnr.
            <fs_imseg>-lfpos = <fs_qals_post>-zeile.
            <fs_imseg>-qm_zustd = '1'.
            <fs_imseg>-tbbel = <fs_qals_post>-zeile.
            <fs_imseg>-tbbpo = <fs_qals_post>-mblnr.
            <fs_imseg>-tbbjr = <fs_qals_post>-mjahr.
          ENDIF.
          UNASSIGN: <fs_value>, <fs_qty_post>.
        ENDIF.
      ENDWHILE.
    ELSE.
      CLEAR ls_t_return.
      ls_t_return-type = 'E'.
      ls_t_return-id = 'ZZZ'.
      ls_t_return-message = 'No posting data found'.
      APPEND ls_t_return TO t_return.
      CONTINUE.
    ENDIF.

    CALL FUNCTION 'QAAT_QM_ACTIVE_INACTIVE'
      EXPORTING
        aktiv = space.
    CALL FUNCTION 'MB_CREATE_GOODS_MOVEMENT'
      EXPORTING
        imkpf = ls_imkpf
        xallp = 'X'
        xallr = 'X'
        ctcod = 'QA11'
        xqmcl = ' '
      IMPORTING
        emkpf = ls_emkpf
      TABLES
        emseg = lt_emseg_tab
        imseg = lt_imseg_tab.
    CALL FUNCTION 'QAAT_QM_ACTIVE_INACTIVE'
      EXPORTING
        aktiv = 'X'.
    IF ls_emkpf-mblnr IS INITIAL.
      IF i_break IS NOT INITIAL.
        BREAK-POINT.
      ENDIF.
      LOOP AT lt_emseg_tab ASSIGNING FIELD-SYMBOL(<fs_emseg>) WHERE msgty = 'E'.
        CLEAR ls_t_return.
        ls_t_return-type = <fs_emseg>-msgty.
        ls_t_return-number = <fs_emseg>-msgno.
        ls_t_return-id = <fs_emseg>-msgid.
        CONCATENATE <fs_emseg>-msgv1
                    <fs_emseg>-msgv2
                    <fs_emseg>-msgv3
                    <fs_emseg>-msgv4
                    INTO ls_t_return-message SEPARATED BY space.
        ls_t_return-message_v2 = <fs_qamb>-prueflos.
        ls_t_return-message_v3 = 'S5'.
        APPEND ls_t_return TO t_return.
      ENDLOOP.
    ELSE.
      COMMIT WORK AND WAIT.
      CALL FUNCTION 'QAMB_COLLECT_RECORD'
        EXPORTING
          lotnumber   = <fs_qamb>-prueflos
          docyear     = ls_emkpf-mjahr
          docnumber   = ls_emkpf-mblnr
          docposition = '1'
          type        = '3'.

      "Post Goods movement
      CALL FUNCTION 'MB_POST_GOODS_MOVEMENT'.
      COMMIT WORK AND WAIT.
      WAIT UP TO 5 SECONDS.
      "Update Inspection stock
      DATA:
        l_stat     LIKE jstat,
        l_stat_tab LIKE jstat OCCURS 0.

      CLEAR l_stat. CLEAR l_stat_tab.
      l_stat-inact = ''.
      l_stat-stat = 'I0219'. APPEND l_stat TO l_stat_tab. "BTEI
      l_stat-stat = 'I0220'. APPEND l_stat TO l_stat_tab. "BEND

      CALL FUNCTION 'STATUS_CHANGE_INTERN'
        EXPORTING
          objnr         = ls_qals_post-objnr
        TABLES
          status        = l_stat_tab
        EXCEPTIONS
          error_message = 1.

      CALL FUNCTION 'QPL1_UPDATE_MEMORY'
        EXPORTING
          i_qals  = ls_qals_post
          i_updkz = lv_updkz.
      CALL FUNCTION 'QPL1_INSPECTION_LOTS_POSTING'
        EXPORTING
          i_mode = '1'.
      CALL FUNCTION 'STATUS_UPDATE_ON_COMMIT'.
      CALL FUNCTION 'QAMB_REFRESH_DATA'.

      <fs_his>-s5 = 'X'.
      UPDATE zmes_nkmh_hd SET s5 = <fs_his>-s5 WHERE prueflos = <fs_qamb>-prueflos.
      COMMIT WORK AND WAIT.

      WAIT UP TO 2 SECONDS.
      UNASSIGN <fs_his>.
    ENDIF.

  ENDLOOP.

  WAIT UP TO 5 SECONDS.

*--------------------------------------------------------------------*
*--> Step 6
*--> WM posting change
  "Get material document data
  SELECT
    1~prueflos,
    1~mblnr,
    1~mjahr,
    1~zeile,
    2~matnr,
    2~werks,
    2~lgort,
    2~charg,
    2~sobkz,
    2~kdauf,
    2~kdpos,
    2~menge,
    2~meins,
    2~lgnum, "warehouse number
    2~lgtyp, "storage type
    2~lgpla "storage bin
    FROM @lt_qamb AS 1
    INNER JOIN mseg AS 2 ON 1~mblnr = 2~mblnr AND 1~zeile = 2~zeile AND 1~mjahr = 2~mjahr
    INNER JOIN t320 AS 3 ON 2~lgnum = 3~lgnum AND 2~werks = 3~werks AND 2~lgort = 3~lgort
    INTO TABLE @DATA(lt_mseg_nk).

  "Get posting document number
  SELECT * FROM qamb WHERE prueflos IN @r_prueflos AND
    typ = '3'
    INTO TABLE @DATA(lt_qamb_n).

  CHECK lt_qamb_n[] IS NOT INITIAL.

  LOOP AT lt_qamb ASSIGNING <fs_qamb>.
    DATA: lv_error_wm TYPE boolean.

    lv_error_wm = abap_false.

    READ TABLE lt_his ASSIGNING <fs_his> WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK <fs_his>-s6 IS INITIAL.

    READ TABLE lt_qamb_n INTO DATA(ls_qamb_n) WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK ls_qamb_n IS NOT INITIAL.

    READ TABLE lt_mseg_nk INTO DATA(ls_mseg_nk) WITH KEY prueflos = <fs_qamb>-prueflos.
    CHECK ls_mseg_nk IS NOT INITIAL.

    "Get posting change number
    SELECT *
      FROM lubu AS 1
      WHERE 1~lgnum = '100' AND
      1~mblnr = @ls_qamb_n-mblnr AND
      1~mjahr = @ls_qamb_n-mjahr
      INTO TABLE @DATA(lt_lubu).

    CHECK lt_lubu[] IS NOT INITIAL.

    LOOP AT lt_lubu ASSIGNING FIELD-SYMBOL(<fs_lubu>).
      IF lv_error_wm = abap_true.
        EXIT.
      ENDIF.
      SELECT *
        FROM lqua
        WHERE lgnum = '100' AND
        matnr = @<fs_lubu>-matn1 AND
        werks = @<fs_lubu>-werk1 AND
        lgort = @<fs_lubu>-lgor1 AND
        charg = @<fs_lubu>-chrg1 AND
        bestq = @<fs_lubu>-bstq1 AND
        sobkz = @<fs_lubu>-sokz1 AND
        sonum = @<fs_lubu>-sonr1 AND
*        lgtyp = @ls_mseg_nk-lgtyp AND
*        lgpla = @ls_mseg_nk-lgpla AND
*        bestq = 'Q' AND
        wenum = @ls_mseg_nk-mblnr AND
        wepos = @ls_mseg_nk-zeile
        INTO TABLE @DATA(lt_lqua).
      CHECK lt_lqua[] IS NOT INITIAL.

      DATA: lv_qty TYPE lubu_menge.
      lv_qty = <fs_lubu>-menge.

      DATA: lt_lubqu TYPE STANDARD TABLE OF lubqu,
            ls_lubqu LIKE LINE OF lt_lubqu,
            lv_tanum TYPE ltak-tanum.
      FREE: lt_lubqu.

      LOOP AT lt_lqua ASSIGNING FIELD-SYMBOL(<fs_lqua>).
        CLEAR: ls_lubqu.
        CHECK lv_qty IS NOT INITIAL.

        IF lv_qty = <fs_lqua>-verme.
          ls_lubqu-lqnum = <fs_lqua>-lqnum.
          ls_lubqu-menge = lv_qty.
          "ls_lubqu-nltyp = ls_mseg_nk-lgtyp.
          "ls_lubqu-nlpla = ls_mseg_nk-lgpla.
          ls_lubqu-squit = 'X'.
          ls_lubqu-letyp = 'Z1'.
          APPEND ls_lubqu TO lt_lubqu.
          CLEAR lv_qty.
          EXIT.
        ELSEIF lv_qty LT <fs_lqua>-verme.
          ls_lubqu-lqnum = <fs_lqua>-lqnum.
          ls_lubqu-menge = lv_qty.
*          ls_lubqu-nltyp = ls_mseg_nk-lgtyp.
*          ls_lubqu-nlpla = ls_mseg_nk-lgpla.
          ls_lubqu-squit = 'X'.
          ls_lubqu-letyp = 'Z1'.
          APPEND ls_lubqu TO lt_lubqu.
          CLEAR lv_qty.
          EXIT.
        ELSEIF lv_qty GT <fs_lqua>-verme.
          ls_lubqu-lqnum = <fs_lqua>-lqnum.
          ls_lubqu-menge = <fs_lqua>-verme.
*          ls_lubqu-nltyp = ls_mseg_nk-lgtyp.
*          ls_lubqu-nlpla = ls_mseg_nk-lgpla.
          ls_lubqu-squit = 'X'.
          ls_lubqu-letyp = 'Z1'.
          APPEND ls_lubqu TO lt_lubqu.
          lv_qty = lv_qty - <fs_lqua>-verme.
        ENDIF.
      ENDLOOP.
      CALL FUNCTION 'L_TO_CREATE_POSTING_CHANGE'
        EXPORTING
          i_lgnum                      = '100'
          i_ubnum                      = <fs_lubu>-ubnum
*         I_LUBUI                      = ' '
          i_squit                      = 'X'
*         I_NIDRU                      = ' '
*         I_DRUKZ                      = ' '
*         I_LDEST                      = ' '
*         I_UPDATE_TASK                = ' '
          i_commit_work                = 'X'
*         I_BNAME                      = SY-UNAME
*         I_SOLEX                      = 0
*         I_PERNR                      = 0
        IMPORTING
          e_tanum                      = lv_tanum
        TABLES
          t_lubqu                      = lt_lubqu
*         T_LTAP_VB                    =
*         T_LTAK                       =
        EXCEPTIONS
          foreign_lock                 = 1
          tp_completed                 = 2
          xfeld_wrong                  = 3
          ldest_wrong                  = 4
          drukz_wrong                  = 5
          tp_wrong                     = 6
          squit_forbidden              = 7
          no_to_created                = 8
          update_without_commit        = 9
          no_authority                 = 10
          i_ubnum_or_i_lubu            = 11
          bwlvs_wrong                  = 12
          material_not_found           = 13
          manual_to_forbidden          = 14
          bestq_wrong                  = 15
          sobkz_missing                = 16
          sobkz_wrong                  = 17
          meins_wrong                  = 18
          conversion_not_found         = 19
          no_quants                    = 20
          t_lubqu_required             = 21
          le_bulk_quant_not_selectable = 22
          quant_not_selectable         = 23
          quantnumber_initial          = 24
          kzuap_or_bin_location        = 25
          date_wrong                   = 26
          nltyp_missing                = 27
          nlpla_missing                = 28
          lgber_wrong                  = 29
          lenum_wrong                  = 30
          menge_wrong                  = 31
          menge_to_big                 = 32
          open_tr_kzuap                = 33
          lock_exists                  = 34
          double_quant                 = 35
          quantity_wrong               = 36
          OTHERS                       = 37.
      IF sy-subrc = 0.
        COMMIT WORK AND WAIT.
        DATA: ls_zmes_nkmh_to TYPE zmes_nkmh_to.
        CLEAR: ls_zmes_nkmh_to.
        ls_zmes_nkmh_to-prueflos = <fs_qamb>-prueflos.
        ls_zmes_nkmh_to-tanum = lv_tanum.
        MODIFY zmes_nkmh_to FROM ls_zmes_nkmh_to.
        COMMIT WORK AND WAIT.
        ls_zmes_nkmh_to-s1 = 'X'.
        <fs_his>-s6 = 'X'.
        UPDATE zmes_nkmh_hd SET s6 = <fs_his>-s6 WHERE prueflos = <fs_qamb>-prueflos.
        COMMIT WORK AND WAIT.
      ELSE.
        CLEAR ls_t_return.
        ls_t_return-type = 'E'.
        ls_t_return-number = '100'.
        ls_t_return-message = |Error posting change WM: { <fs_qamb>-prueflos }/{ sy-subrc }|.
        APPEND ls_t_return TO t_return.
        lv_error_wm = abap_true.
      ENDIF.
    ENDLOOP.
    UNASSIGN <fs_his>.
  ENDLOOP.

* save response data to database.
  DATA: lt_p_his TYPE STANDARD TABLE OF zmes_inf_qc_ret,
        ls_p_his TYPE zmes_inf_qc_ret.
  DATA: lv_seq_his TYPE int4.
  CLEAR lv_seq.
  LOOP AT t_return ASSIGNING FIELD-SYMBOL(<fs_return>).
    CLEAR ls_p_his.
    lv_seq_his = lv_seq_his + 1.
    ls_p_his-mblnr = i_mblnr.
    ls_p_his-guid = i_guid.
    ls_p_his-seq = lv_seq_his.
    ls_p_his-ztype = <fs_return>-type.
    ls_p_his-zid = <fs_return>-id.
    ls_p_his-znumber = <fs_return>-number.
    ls_p_his-zmessage = <fs_return>-message.
    ls_p_his-zlog_no = <fs_return>-log_no.
    ls_p_his-zlog_msg_no = <fs_return>-log_msg_no.
    ls_p_his-zmessage_v1 = <fs_return>-message_v1.
    ls_p_his-zmessage_v2 = <fs_return>-message_v2.
    ls_p_his-zmessage_v3 = <fs_return>-message_v3.
    ls_p_his-zmessage_v4 = <fs_return>-message_v4.
    ls_p_his-zparameter = <fs_return>-parameter.
    ls_p_his-zrow = <fs_return>-row.
    ls_p_his-zfield = <fs_return>-field.
    ls_p_his-zsystem = <fs_return>-system.
    APPEND ls_p_his TO lt_p_his.
  ENDLOOP.
  IF sy-subrc NE 0.
    CLEAR ls_p_his.
    ls_p_his-mblnr = i_mblnr.
    ls_p_his-guid = i_guid.
    ls_p_his-seq = lv_seq_his.
    ls_p_his-ztype = 'S'.
    ls_p_his-zmessage = 'Success'.
    APPEND ls_p_his TO lt_p_his.
  ENDIF.
  IF lt_p_his[] IS NOT INITIAL.
    MODIFY zmes_inf_qc_ret FROM TABLE lt_p_his.
    COMMIT WORK AND WAIT.
  ENDIF.



ENDFUNCTION.