class SlocAddress {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataSlocAddress>? data;


  SlocAddress(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  SlocAddress.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataSlocAddress>[];
      json['data'].forEach((v) {
        data!.add(DataSlocAddress.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataSlocAddress {
  String? slocId;
  String? sloc;
  String? plant;
  String? warehouseNo;
  String? defaultStorageBin;
  String? defaultStorageBinId;
  String? slocDisplay;

  DataSlocAddress(
      { this.slocId,
        this.sloc,
        this.plant,
        this.warehouseNo,
        this.defaultStorageBin,
        this.defaultStorageBinId,
        this.slocDisplay
      });

  DataSlocAddress.fromJson(Map<String, dynamic> json) {
    slocId = json['slocId'];
    sloc = json['sloc'];
    plant = json['plant'];
    warehouseNo = json['warehouseNo'];
    defaultStorageBin = json['defaultStorageBin'];
    defaultStorageBinId  = json['defaultStorageBinId'];
    slocDisplay = json['slocDisplay'];
  }
  static DataSlocAddress empty =  DataSlocAddress(slocDisplay: "--Bỏ chọn--");
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['slocId'] = slocId;
    data['sloc'] = sloc;
    data['plant'] = plant;
    data['warehouseNo'] = warehouseNo;
    data['defaultStorageBin'] = defaultStorageBin;
    data['defaultStorageBinId'] = defaultStorageBinId;
    data['slocDisplay'] = slocDisplay;
    return data;
  }

  DataSlocAddress.clone(DataSlocAddress source) :
        slocId = source.slocId,
        sloc = source.sloc,
        plant = source.plant,
        warehouseNo = source.warehouseNo,
        defaultStorageBin = source.defaultStorageBin,
        defaultStorageBinId = source.defaultStorageBinId,
        slocDisplay = source.slocDisplay;

}