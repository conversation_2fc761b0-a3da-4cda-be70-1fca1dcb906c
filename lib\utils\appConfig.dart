// class AppConfig {
//   static const String appVersion = 'v3.2.7';
//   static bool isShowDebug = true;
// }

import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import 'package:yaml/yaml.dart';

class AppConfig {
  static String appVersion = '3.0.0'; // default value
  static bool isShowDebug = true;

  static Future<void> initialize() async {
    OneSignal.initialize("************************************");
    OneSignal.Notifications.requestPermission(true);

    final data = await rootBundle.loadString('pubspec.yaml');
    // final Map<String, dynamic> yamlMap = loadYaml(data);
    final Map<String, dynamic> yamlMap = Map<String, dynamic>.from(loadYaml(data));

    final versionString = yamlMap['version'];

    if (versionString != null) {
      final versionComponents = versionString.split('+');
      appVersion = 'v' + versionComponents[0];
    }
  }

  // add a function to get current environment
  //
  static Future<String> getEnvironment() async {
    var ret = "";
    final environment = await SecureStorage.getString("environment", null);

    if (environment == null) {
      ret = "dev";
    } else {
      ret = environment;
    }

    return ret;
  }
}
