import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import '../../../model/downtimeModel.dart';

class EmployeeTypeAhead extends StatelessWidget {
  final bool enabled;
  final bool showRemoveButton;
  final List<EmployeeRecord> masterDataList;
  final List<String> excludedEmployeeIds;
  final Function(EmployeeRecord) onSuggestionSelected;
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  final int employeeIndex;
  final Function? onAddEmployee;
  final Function? onRemoveEmployee;
  final int totalEmployees;
  final TextStyle? textStyle;

  const EmployeeTypeAhead({
    Key? key,
    required this.enabled,
    required this.showRemoveButton,
    required this.masterDataList,
    required this.excludedEmployeeIds,
    required this.onSuggestionSelected,
    required this.controller,
    required this.onChanged,
    required this.employeeIndex,
    required this.totalEmployees,
    this.onAddEmployee,
    this.onRemoveEmployee,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLastItem = employeeIndex == totalEmployees - 1;

    return Container(
      margin: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(3.r),
              ),
              child: TypeAheadField<EmployeeRecord>(
                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                  constraints: BoxConstraints(
                    maxHeight: 200.h,
                  ),
                ),
                textFieldConfiguration: TextFieldConfiguration(
                  enabled: enabled,
                  controller: controller,
                  style: TextStyle(fontSize: 12.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: onChanged,
                ),
                suggestionsCallback: (pattern) {
                  return masterDataList
                      .where((item) =>
                          (item.employeeName?.toLowerCase().contains(pattern.toLowerCase()) ?? false) ||
                          (item.employeeId?.toLowerCase().contains(pattern.toLowerCase()) ?? false))
                      .where((item) => !excludedEmployeeIds.contains(item.employeeId))
                      .toList();
                },
                itemBuilder: (context, EmployeeRecord suggestion) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                    child: Text(
                      "${suggestion.employeeId} - ${suggestion.employeeName}",
                      style: TextStyle(fontSize: 12.sp),
                    ),
                  );
                },
                onSuggestionSelected: onSuggestionSelected,
                noItemsFoundBuilder: (context) => Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                  child: Text(
                    'Không tìm thấy kết quả',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 4.w),
          IconButton(
            padding: const EdgeInsets.all(4),
            constraints: BoxConstraints(maxHeight: 20.h),
            iconSize: 17.sp,
            // style: IconButton.styleFrom(
            //   padding: EdgeInsets.zero,
            //   minimumSize: Size.zero,
            //   tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            //   alignment: Alignment.center,
            //   backgroundColor: Colors.red
            // ),
            color: isLastItem ? Colors.blue : Colors.red,
            icon: Icon(isLastItem ? Icons.add : Icons.remove),
            onPressed: () {
              if (isLastItem) {
                onAddEmployee?.call();
              } else {
                onRemoveEmployee?.call();
              }
            },
          ),
        ],
      ),
    );
  }
}
