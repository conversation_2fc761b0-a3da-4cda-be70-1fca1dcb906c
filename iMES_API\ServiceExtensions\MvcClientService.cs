﻿using ISD.API.Repositories;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace iMES_API.ServiceExtensions
{
    public class MvcClientService
    {
        private readonly HttpClient _httpClient;
        private CookieContainer _cookieContainer;
        private IISDConfigManager _configuration;
        public MvcClientService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _cookieContainer = new CookieContainer();
            _httpClient.DefaultRequestHeaders.ConnectionClose = false; // Use persistent connections
            _configuration = new ISDConfigManager();
        }

        public async Task AuthenticateAsync()
        {

            var defaultUsername = _configuration.DEFAUTL_USERNAME;
            var defaultPassword = _configuration.DEFAUTL_PASSWORD;
            var defaultSaleOrg = _configuration.DEFAUTL_SALE_ORG;

            var formContent = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("UserName", defaultUsername),
                new KeyValuePair<string, string>("Password", defaultPassword),
                new KeyValuePair<string, string>("SaleOrg", defaultSaleOrg),
            });

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };

            var defaultHost = _configuration.DEFAUTL_MVC_HOST;

            var response = await client.PostAsync(defaultHost + "/Permission/Auth/Login", formContent);
            response.EnsureSuccessStatusCode();  // Ensure we have a successful response
        }

        public async Task AuthenticateTDSAsync()
        {
            var defaultUsername = "tienlx";
            var defaultPassword = "tdsp@1234";

            var formContent = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("username", defaultUsername),
                new KeyValuePair<string, string>("password", defaultPassword),
            });

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };

            // Add headers similar to the curl request
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
            client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9,vi;q=0.8");
            client.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            client.DefaultRequestHeaders.Add("Pragma", "no-cache");
            client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            client.DefaultRequestHeaders.Add("sec-ch-ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"");
            client.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
            client.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");

            var defaultHost = "https://tdsp.ttf.com.vn";

            var response = await client.PostAsync(defaultHost + "/login.jspa", formContent);
            response.EnsureSuccessStatusCode();  // Ensure we have a successful response
        }


        public async Task<string> GetTDSInfo(string endpoint)
        {
            await AuthenticateTDSAsync();

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };
        
            var response = await client.GetAsync(endpoint);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> GetAsync(string endpoint)
        {
            await AuthenticateAsync();

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };

            var response = await client.GetAsync(endpoint);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> GetMauHoanThien(string productCode)
        {
            await AuthenticateAsync();

            var defaultHost = _configuration.DEFAUTL_MVC_HOST;
            var endpoint = defaultHost + "/Home/GetMauHoanThien?productCode=" + productCode;

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };

            var response = await client.GetAsync(endpoint);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> GetQCDauVaoSyncSAP(string id)
        {
            await AuthenticateAsync();

            var defaultHost = _configuration.DEFAUTL_MVC_HOST;
            var endpoint = defaultHost + "/Home/GetQCDauVaoSyncSAP?id=" + id;

            var messageHandler = new HttpClientHandler() { CookieContainer = _cookieContainer };
            var client = new HttpClient(messageHandler) { BaseAddress = _httpClient.BaseAddress };
            client.Timeout = TimeSpan.FromMinutes(5);

            var response = await client.GetAsync(endpoint);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
    }
}
