﻿using Azure.Core;
using ISD.API.Core;
using ISD.API.Resources;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System;
using System.Threading.Tasks;
using ISD.API.ViewModels;
using OfficeOpenXml;
using System.Threading;
using ISD.API.EntityModels.Models;
using System.Collections.Generic;
using ISD.API.ViewModels.MasterData.SampMethod;
using Microsoft.EntityFrameworkCore;

namespace iMES_API.Areas.MasterData.FavoriteReport
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    public class SampMethodController : ControllerBaseAPI
    {
        /// <summary>
        /// Import excel phương pháp lấy mẫu
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("import-excel-samp-method")]
        public async Task<IActionResult> ImportExcelSampMethod([FromForm] ImportExcelSampMethodRequest request)
        {
            //Check length file
            if (request.File == null || request.File.Length <= 0)
                return Ok(new ApiResponse { Code = 400, Message = "File rỗng !"});

            //Check định dạng file
            if (!Path.GetExtension(request.File.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase) && !Path.GetExtension(request.File.FileName).Equals(".xls", StringComparison.OrdinalIgnoreCase))
                return Ok(new ApiResponse { Code = 400, Message = "File không đúng định dạng !" });

            using (var stream = new MemoryStream())
            {
                //Copy file
                await request.File.CopyToAsync(stream);

                // Creating an instance of ExcelPackage
                var excel = new ExcelPackage(stream);

                //Get sheet excel
                ExcelWorksheet worksheet = excel.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;

                var sampMethods = new List<SampMethodModel>();

                //Row index      
                for (int rowIndex = 2; rowIndex <= rowCount; rowIndex++)
                {
                    //Check tồn tại
                    var checkExist = worksheet.Cells[$"A{rowIndex}"].Value == null ? null : worksheet.Cells[$"A{rowIndex}"].Value.ToString().Trim();

                    if (!string.IsNullOrEmpty(checkExist))
                    {
                        var sampMethod = new SampMethodModel();

                        //Id
                        sampMethod.SampMethodId = Guid.NewGuid();
                        //Cỡ lô
                        var sampleSize = worksheet.Cells[$"A{rowIndex}"].Value == null ? null : worksheet.Cells[$"A{rowIndex}"].Value.ToString().Trim();

                        //Cỡ lô chi tiết
                        sampMethod.SampleSize = sampleSize;

                        //Cỡ lô từ
                        var sampleSizeSpl = sampleSize.Split(" Đến ");

                        //Cỡ lô từ
                        sampMethod.SampleSizeFrom = int.Parse(sampleSizeSpl[0].Replace(",", ""));

                        //Cỡ lỗ đến
                        if (sampleSizeSpl[1] == "Trở lên")
                            sampMethod.SampleSizeTo = Int32.MaxValue;
                        else
                            sampMethod.SampleSizeTo = int.Parse(sampleSizeSpl[1].Replace(",", ""));

                        //Tên phương pháp lấy mẫu
                        sampMethod.SampleName = worksheet.Cells[$"B{rowIndex}"].Value == null ? null : worksheet.Cells[$"B{rowIndex}"].Value.ToString().Trim();
                        //Số lượng lấy mẫu
                        sampMethod.SampleQuantity = worksheet.Cells[$"C{rowIndex}"].Value == null ? null : int.Parse(worksheet.Cells[$"C{rowIndex}"].Value.ToString().Trim());

                        //Common
                        sampMethod.CreateTime = DateTime.Now;
                        sampMethod.CreateBy = CurrentUser?.AccountId;
                        sampMethod.Actived = true;

                        sampMethods.Add(sampMethod);
                    }
                }

                _context.SampMethodModel.AddRange(sampMethods);

                await _context.SaveChangesAsync();
            }

            return Ok(new ApiSuccessResponse<bool> { Data = true, Message = string.Format(CommonResource.Msg_Succes, "Import dữ liệu") });

        }


        /// <summary>
        /// Import account
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("import-excel-acc-ncc")]
        public async Task<IActionResult> ImportExcelAccountNCC([FromForm] ImportExcelSampMethodRequest request)
        {
            //Check length file
            if (request.File == null || request.File.Length <= 0)
                return Ok(new ApiResponse { Code = 400, Message = "File rỗng !" });

            //Check định dạng file
            if (!Path.GetExtension(request.File.FileName).Equals(".xlsx", StringComparison.OrdinalIgnoreCase) && !Path.GetExtension(request.File.FileName).Equals(".xls", StringComparison.OrdinalIgnoreCase))
                return Ok(new ApiResponse { Code = 400, Message = "File không đúng định dạng !" });

            using (var stream = new MemoryStream())
            {
                //Copy file
                await request.File.CopyToAsync(stream);

                // Creating an instance of ExcelPackage
                var excel = new ExcelPackage(stream);

                //Get sheet excel
                ExcelWorksheet worksheet = excel.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;

                var accounts = new List<AccountModel>();

                //Row index      
                for (int rowIndex = 634; rowIndex <= rowCount; rowIndex++)
                {
                    //Check tồn tại
                    var checkExist = worksheet.Cells[$"A{rowIndex}"].Value == null ? null : worksheet.Cells[$"A{rowIndex}"].Value.ToString().Trim();

                    if (!string.IsNullOrEmpty(checkExist))
                    {
                        var userName = checkExist;
                        var fullName = worksheet.Cells[$"B{rowIndex}"].Value == null ? null : worksheet.Cells[$"B{rowIndex}"].Value.ToString().Trim();
                        var passWord = worksheet.Cells[$"C{rowIndex}"].Value == null ? null : worksheet.Cells[$"C{rowIndex}"].Value.ToString().Trim();
                        var passWordEncode = _unitOfWork.RepositoryLibrary.GetMd5Sum(passWord);

                        var user = await _context.AccountModel.FirstOrDefaultAsync(x => x.UserName.Trim().ToLower() == userName.Trim().ToLower());
                        if (user is null)
                        {
                            var account = new AccountModel
                            {
                                AccountId = Guid.NewGuid(),
                                UserName = user.UserName,
                                FullName = fullName,
                                Password = passWord
                            };

                            accounts.Add(account);
                        }
                    }
                }

                _context.AccountModel.AddRange(accounts);

                await _context.SaveChangesAsync();
            }

            return Ok(new ApiSuccessResponse<bool> { Data = true, Message = string.Format(CommonResource.Msg_Succes, "Import dữ liệu") });

        }
    }
}
