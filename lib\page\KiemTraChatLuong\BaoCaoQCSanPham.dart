import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/model/commonDateModel.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/page/KiemTraChatLuong/element/FillterListKCS.dart';
import 'package:ttf/page/KiemTraChatLuong/element/FilterListQCMau.dart';
import 'package:ttf/page/KiemTraChatLuong/element/FilterListQCSanPham.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../../element/errorViewPost.dart';
import '../../element/timeOut.dart';
import '../../model/drawerFilterQC.dart';
import '../../model/getListQCByFilter.dart';
import '../../model/postFilterQC.dart';
import '../../element/FilterLsQC.dart';
import '../../model/qrCodePageListQCModel.dart';
import '../../repository/function/listQcFunction.dart';
import '../../screenArguments/ScreenArgumentNavigatorBar.dart';
import '../LostConnect.dart';

class BaoCaoQCSanPham extends StatefulWidget {
  // final String token;
  // final String accountID;
  final String dateTimeOld;
  final DataUser user;
  const BaoCaoQCSanPham(
      {Key? key,
      // required this.token, required this.accountID,
      required this.dateTimeOld,
      required this.user})
      : super(key: key);

  @override
  _BaoCaoQCSanPhamState createState() => _BaoCaoQCSanPhamState();
}

class _BaoCaoQCSanPhamState extends State<BaoCaoQCSanPham> {
  bool? _isLoading;
  ConnectivityResult _result = ConnectivityResult.none;
  DateTime? _stringToDateTimeConfirm;
  String? _dateFormatStringConfirm;
  DateTime? _stringToDateTimeQualityDate;
  String? _dateFormatStringQualityDate;
  bool _isNotWifi = false;
  FilterQCModel? _filterLSQC;
  CommonDateModel? _commonDateModel;
  List<DataListQC>? _defaultDataFilter = [];
  List<SalesOrgCodes>? _salesOrgCodes;
  List<WorkCenters>? _workCenters;
  List<WorkShops>? _workShops;
  List<CommonDates>? _commonDates;
  List<ResultsDataQC>? _results;
  String _notFoundFilter = " ";
  FilterQCVm? _postFilterQC;
  String _error = "";
  late bool _timeOut = false;
  bool? _disableButton;

  String title = 'Danh sách QAQC sản phẩm';

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _getDefaultDropDownForListFiltered() async {
    try {
      setState(() {
        _isLoading = true;
        _isNotWifi = false;
        _timeOut = false;
      });
      final dataDropdown = await ListQCFunction.getDefaultQCSanPhamFilter(widget.user.token.toString());

      debugPrint("--- TIEN getDefaultQCSanPhamFilter | _filterLSQC");
      debugPrint(json.encode(dataDropdown));

      if (dataDropdown == null) {
        if (!mounted) return;
        setState(() {
          _isLoading = true;
        });
      } else {
        if (!mounted) return;
        setState(() {
          _filterLSQC = dataDropdown;
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
        _timeOut = false;
      });
    }
  }

  Future<void> _getCommonDateForFilter() async {
    try {
      final dataCommonDateModel = await ListQCFunction.getCommonDateModel(
        _filterLSQC!.additionalData!.selectedQCCommonDate.toString(),
        widget.user.token.toString(),
      );
      if (!mounted) return;

      debugPrint("--- TIEN dataCommonDateModel");
      debugPrint(json.encode(dataCommonDateModel));

      debugPrint("--- TIEN _filterLSQC!.additionalData!.selectedConfirmCommonDate");
      debugPrint(_filterLSQC!.additionalData!.selectedConfirmCommonDate);

      debugPrint("--- TIEN _filterLSQC!.additionalData!.selectedQCCommonDate");
      debugPrint(_filterLSQC!.additionalData!.selectedQCCommonDate);

      debugPrint("--- TIEN dataCommonDateModel");
      debugPrint(dataCommonDateModel!.fromDate);
      debugPrint(dataCommonDateModel!.toDate);
      debugPrint("--- TIEN end dataCommonDateModel");

      setState(() {
        _commonDateModel = dataCommonDateModel;
        _salesOrgCodes = _filterLSQC!.data!.salesOrgCodes;
        _salesOrgCodes!.insert(0, (ListQCFunction.defaultSalesOrgCodes));
        _workCenters = _filterLSQC!.data!.workCenters;
        _workCenters!.insert(0, (ListQCFunction.defaultWorkCenters));
        _results = _filterLSQC!.data!.results;
        _results!.insert(0, (ListQCFunction.defaultResultsDataQC));
        _workShops = _filterLSQC!.data!.workShops;
        _workShops!.insert(0, (ListQCFunction.defaultWorkShops));
        _commonDates = _filterLSQC!.data!.commonDates;
        _postFilterQC = FilterQCVm(
          _filterLSQC!.additionalData!.selectedSalesOrgCode,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          // _filterLSQC!.additionalData!.selectedConfirmCommonDate,
          // _commonDateModel!.fromDate,
          // _commonDateModel!.toDate,
          null,
          null,
          null,
          _filterLSQC!.additionalData!.selectedQCCommonDate,
          _commonDateModel!.fromDate,
          _commonDateModel!.toDate,
          "QCSANPHAM",
        );
      });
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _fetchListSaved() async {
    // setState(() {
    //   _isLoading = false;
    // });
    // return;
    try {
      if (_postFilterQC != null) {
        _postFilterQC!.qualityType = "QCSANPHAM";
      }
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        debugPrint(_error);
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _rePostListQCApi() async {
    try {
      setState(() {
        _isLoading = true;
      });
      if (_postFilterQC != null) {
        _postFilterQC!.qualityType = "QCSANPHAM";
      }
      final dataListQC = await ListQCFunction.postFilterList(widget.user.token.toString(), _postFilterQC!);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (dataListQC != null) {
        setState(() {
          _defaultDataFilter = dataListQC.data;
        });
      } else {
        setState(() {
          _notFoundFilter = 'Không tìm thấy phiếu kiểm tra nào!';
        });
      }
    } on SocketException catch (_) {
      setState(() {
        _isNotWifi = true;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isNotWifi = false;
        _isLoading = false;
        _error = error.toString();
      });
    }
  }

  Future<void> _init() async {
    String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
    DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
    DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
    if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
      _timeOut = true;
      return;
    }

    await _getDefaultDropDownForListFiltered();
    if (_filterLSQC == null) {
      return;
    }

    await _getCommonDateForFilter();
    if (_commonDateModel == null) {
      return;
    }

    _fetchListSaved();
  }

  Future<void> _checkConnectNetwork() async {
    _result = await Connectivity().checkConnectivity();
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }
  // Future<void> _removeCurrentUser(BuildContext context) async {
  //   setState(() {
  //     _disableButton = true;
  //   });
  //   showDialog<String>(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => WillPopScope(
  //       onWillPop: () async => false,
  //       child: const Center(
  //         child: CircularProgressIndicator(),
  //       ),
  //     ),
  //   );
  //   await Future.wait([
  //     StorageSharedPreferences.removeShared("id"),
  //     StorageSharedPreferences.removeShared("datetimeNow"),
  //     SecureStorage.removeSecure("user", null)
  //   ]);
  //   Navigator.pop(context);
  //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //       backgroundColor: Colors.black,
  //       content: Text(
  //         'Đăng xuất thành công',
  //         style: TextStyle(fontSize: 15.sp, color: Colors.white),
  //       ),
  //       duration: const Duration(seconds: 2)));
  //   Future.delayed(const Duration(seconds: 0), () {
  //     Navigator.of(context).pushNamedAndRemoveUntil('/Login', (Route<dynamic> route) => false);
  //   });
  // }

  Widget _buildDataFiltered() {
    if (_defaultDataFilter != null && _defaultDataFilter!.isNotEmpty) {
      return ListView.builder(
          key: ObjectKey(_defaultDataFilter![0]),
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemCount: _defaultDataFilter!.length,
          itemBuilder: (context, index) {
            final item = _defaultDataFilter![index];
            if (item.confirmDate != null) {
              _stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(item.confirmDate.toString());
              _dateFormatStringConfirm = DateFormat('dd/MM/yyyy ').format(_stringToDateTimeConfirm!);
            }
            if (item.qualityDate != null) {
              _stringToDateTimeQualityDate = DateFormat("yyyy-MM-dd").parse(item.qualityDate.toString());
              _dateFormatStringQualityDate = DateFormat('dd-MM-yyyy').format(_stringToDateTimeQualityDate!);
            } else {
              _dateFormatStringQualityDate = item.qualityDate;
            }
            return GestureDetector(
                onTap: () async {
                  debugPrint(item.qualityControlId);
                  Navigator.pushNamed(
                    context,
                    '/BaoCaoQCSanPhamDetail',
                    arguments: ScreenArgumentNavigatorBar(
                      item.qualityControlId.toString(),
                      widget.dateTimeOld,
                      "",
                      "normal",
                      widget.user,
                    ),
                  ).then((value) {
                    // final arguments = ModalRoute.of(context)!.settings.arguments as ScreenArgumentListQCPopUntil;
                    // final check = arguments.refresh;
                    // if(GlobalValue.refreshListQC == true){
                    if (value == true) {
                      _rePostListQCApi();
                    }
                  }).onError((error, stackTrace) {
                    debugPrint('Lỗi:$error');
                  });
                },
                child: _ListKCSView(
                  item: item,
                  dateFormatStringConfirm: _dateFormatStringConfirm,
                  dateFormatStringQualityDate: _dateFormatStringQualityDate,
                ));
          });
    }

    return _NotFoundFilterView(
      notFoundFilter: _notFoundFilter,
    );
  }

  Widget _buildScreen(BuildContext context) {
    return Scaffold(
        endDrawerEnableOpenDragGesture: false,
        backgroundColor: _defaultDataFilter != null && _defaultDataFilter!.isNotEmpty ? Colors.grey.shade200 : Colors.white,
        // key: _key,
        endDrawer: _isLoading == true && _filterLSQC != null
            ? null
            : _filterLSQC != null
                ? FilterListQCSanPham(
                    filterLSQC: _filterLSQC!,
                    lsStatus: ListQCFunction.lsStatus,
                    results: _results,
                    commonDates: _commonDates,
                    workCenters: _workCenters,
                    commonDateModel: _commonDateModel ?? CommonDateModel(),
                    salesOrgCodes: _salesOrgCodes,
                    workShops: _workShops,
                    token: widget.user.token.toString(),
                    onFilterSelected: (List<DrawerFilterQC> filter) {
                      setState(() {
                        _defaultDataFilter = filter[0].getDataFilter;
                        _notFoundFilter = filter[0].notFoundFilter ?? " ";
                        _postFilterQC = filter[0].postFilterQC;
                      });
                    },
                  )
                : null,
        appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          actions: [
            IconButton(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              hoverColor: Colors.transparent,
              icon: Icon(
                Icons.camera_alt,
                size: 19.sp,
                color: Colors.white,
              ),
              onPressed: _isLoading == true
                  ? null
                  : () async {
                      final check = await Navigator.pushNamed(context, "/QRCodePageListQC2");
                      if (!mounted) return;
                      if (check == null) return;
                      if ((check as QRCodePageListQCModel).isScan == true) {
                        final checkSave = await Navigator.pushNamed(
                          context,
                          '/BaoCaoQCSanPhamDetail',
                          arguments: ScreenArgumentNavigatorBar(
                            "",
                            widget.dateTimeOld,
                            check.qrCode.toString(), // thẻ treo
                            "qr",
                            widget.user,
                          ),
                        );
                        if (checkSave == null) return;
                        if (checkSave == true) {
                          _rePostListQCApi();
                        }
                      }
                    },
            ),
            Builder(
              builder: (BuildContext context) {
                return IconButton(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  icon: Icon(
                    Icons.search_outlined,
                    size: 19.sp,
                    color: Colors.white,
                  ),
                  onPressed: _isLoading == true
                      ? null
                      : () async {
                          await _checkConnectNetwork();
                          if (!mounted) return;
                          if (_result != ConnectivityResult.none) {
                            Scaffold.of(context).openEndDrawer();
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              backgroundColor: Colors.black,
                              content: Text(
                                'Tính năng cần có kết nối internet để sử dụng',
                                style: TextStyle(fontSize: 15.sp, color: Colors.white),
                              ),
                            ));
                          }
                        },
                );
              },
            ),
          ],
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          ),
        ),
        body: _error != ""
            ? ErrorViewPost(error: _error)
            : _isLoading == true
                ? _buildLoading()
                : _buildDataFilteredWithHeader());
  }

  Widget _buildDataFilteredWithHeader() {
    List<Map<String, String>> dummyBarcode = [
      // {'LSX': 'NTZ chưa QC', 'barcode': '300008115931018'},
      // {'LSX': 'NTZ đã QC', 'barcode': '300008101828030'},
      // {'LSX': 'TTF chưa QC', 'barcode': '350000934420FF5'},
      // {'LSX': 'TTF đã QC', 'barcode': '35000093448F1C9'},
      // {'LSX': 'TTF', 'barcode': '3500009344FE475'},
      // {'LSX': 'TTF SF-BOC', 'barcode': '3500008607DFEA8'},
      // {'LSX': 'PRD', 'barcode': '3500009402E78D8'},
      // {'LSX': 'Test Friendly A', 'barcode': '350000860756912'},
      // {'LSX': 'Test EAN A', 'barcode': '3508607873432'},
      // {'LSX': 'Test Random', 'barcode': '12312312412312'},

      {'LSX': '3506843735835', 'barcode': '3506843735835'},
      {'LSX': '1752687884587', 'barcode': '1752687884587'},
      {'LSX': 'Error 1', 'barcode': '3506812592360'},
      // {'LSX': 'Error 2', 'barcode': '1752950238130'},

      {'LSX': 'Test', 'barcode': '17529512334'},
      {'LSX': 'Test 2', 'barcode': '300008430604008'},

      // {'LSX': 'info only', 'barcode': '1750815055304'},
      // {'LSX': 'error', 'barcode': '300008120974008'},
      // {'LSX': 'test TB_DGO', 'barcode': '3000081209749997'},
      // {'LSX': 'err', 'barcode': '1752238242125'},
    ];

    return Column(
      children: <Widget>[
        kDebugMode
            ? RenderDebugButtons(dummyBarcode, (item) {
                Navigator.pushNamed(
                  context,
                  '/BaoCaoQCSanPhamDetail',
                  arguments: ScreenArgumentNavigatorBar(
                    "",
                    widget.dateTimeOld,
                    item['barcode']!, // thẻ treo
                    "qr",
                    widget.user,
                  ),
                );
              }, 'LSX')
            : Container(),
        Expanded(
          child: _buildDataFiltered(),
        )
      ],
    );
  }

  Widget _buildTimeOutView() {
    return WillPopScope(
        onWillPop: () => Future.value(false),
        child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)));
  }

  Widget _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildWifi() {
    return Scaffold(
        backgroundColor: Colors.grey.shade200,
        appBar: AppBar(
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xff0052cc),
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            'Danh Sách Kiểm Tra BTP',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
          ),
        ),
        body: LostConnect(checkConnect: () => _init()));
  }

  Widget _buildChildBasedOnCondition(BuildContext context) {
    if (_isNotWifi) {
      return _buildWifi();
    }

    return _buildScreen(context);
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true ? _buildTimeOutView() : _buildChildBasedOnCondition(context);
  }
}

class _NotFoundFilterView extends StatelessWidget {
  final String notFoundFilter;
  const _NotFoundFilterView({Key? key, required this.notFoundFilter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                width: 1.w,
                color: const Color(0xff0052cc).withOpacity(0.6),
              ),
            ),
            child: Icon(Icons.edit_note_rounded, size: 50.sp, color: const Color(0xff0052cc).withOpacity(0.6)),
          ),
          SizedBox(height: 20.h),
          Text(
            notFoundFilter,
            style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }
}

class _ListKCSView extends StatelessWidget {
  final DataListQC item;
  final String? dateFormatStringConfirm;
  final String? dateFormatStringQualityDate;
  const _ListKCSView({
    Key? key,
    required this.item,
    required this.dateFormatStringConfirm,
    required this.dateFormatStringQualityDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          buildTextItem("Barcode:", item.barcode.toString()),
          buildInfoRow("Nhà máy: ${item.saleOrgCode.toString()}", "LSX SAP: ${item.lsxsap.toString()}", item.status, result: item.result),
          SizedBox(height: 10.h),
          // buildInfoRow("DSX: ${item.dsx.toString()}", "LSX DT: ${item.lsxdt.toString()}", item.result, isResult: true),
          // buildInfoRow("DSX: ${item.dsx.toString()}", "LSX DT: ${item.lsxdt.toString()}", item.result, isResult: true),
          // buildTextItem("DSX:", item.dsx.toString()),
          buildTextItem("Sản phẩm:", item.productName.toString()),
          buildTextItem("LSX DT:", item.lsxdt.toString()),
          // buildTextItem(item.productName.toString(), "", Colors.grey.shade600),
          SizedBox(height: 10.h),
          // buildTextItem("Chi tiết:", item.productAttribute ?? ""),
          // buildTextItem("Công đoạn:", item.stepCode.toString()),
          // buildTextItem("Công đoạn:", item.stepCode != null ? item.stepName.toString() : ""),
          // buildTextItem("Màu hoàn thiện:", item.finishedColor ?? ""),
          const Divider(),
          buildDateRow(),
        ],
      ),
    );
  }

  Widget buildTextItem(String label,
      [String text = "",
      Color labelColor = Colors.black,
      FontWeight labelFontWeight = FontWeight.normal,
      Color textColor = Colors.black,
      FontWeight textFontWeight = FontWeight.bold]) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: labelColor,
            fontWeight: labelFontWeight,
          ),
        ),
        text.isNotEmpty
            ? Text(
                " " + text,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: textColor,
                  fontWeight: textFontWeight,
                ),
              )
            : Container(),
      ],
    );
  }

  Widget buildInfoRow(String text1, String text2, dynamic status, {bool isResult = false, String? result = ""}) {
    // Your widget implementation goes here...
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        Expanded(
          flex: 6,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              buildTextItem(text1, "", Colors.black, FontWeight.bold),
              SizedBox(height: 1.h),
              buildTextItem(text2),
            ],
          ),
        ),
        Expanded(
          flex: 4,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: status == false ? Colors.deepOrange : const Color(0xff0052cc),
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: Center(
                  child: Text(
                    status == false ? "Chưa kiểm tra" : "Đã kiểm tra",
                    style: TextStyle(fontSize: 12.sp, color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              SizedBox(height: 2.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: result != null ? (result == 'Fail' ? Colors.red.shade800 : Colors.green) : Colors.yellow.shade800,
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: Center(
                  child: Text(
                    result != null ? result.toString() : "Chưa có kết quả",
                    style: TextStyle(fontSize: 12.sp, color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildDateRow() {
    return Row(
      children: <Widget>[
        // buildDateColumn("Ngày Confirm", dateFormatStringConfirm.toString()),
        buildDateColumn("Ngày kiểm tra", dateFormatStringQualityDate ?? " "),
      ],
    );
  }

  Widget buildDateColumn(String title, String date) {
    return Expanded(
      flex: 5,
      child: Column(
        children: <Widget>[
          buildTextItem(title),
          SizedBox(height: 1.h),
          buildTextItem(date),
        ],
      ),
    );
  }
}
