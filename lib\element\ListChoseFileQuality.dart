import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'ImageQuatity.dart';
import 'listImagePicker.dart';

class ListChooseImage extends StatelessWidget {
  const ListChooseImage({
    Key? key,
    required this.lsFileTabCheck,
    required this.deleteListImageTabCheck,
  }) : super(key: key);

  final List<File> lsFileTabCheck;
  final ValueChanged<int> deleteListImageTabCheck;

  @override
  Widget build(BuildContext context) {
    return Wrap(
        spacing: 3.w,
        runSpacing: 3.h,
        children: List.generate(
            lsFileTabCheck.length,
                (indexImage) {
              return SizedBox(
                width: 50.w,
                child: Stack(
                    children: <Widget>[
                      GestureDetector(
                        onTap: (){
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ImageQuatity(
                                  lsImage: lsFileTabCheck,
                                  index:indexImage
                              ),
                            ),
                          );
                        },
                        child: ListImagePicker(
                            fileImage: lsFileTabCheck[indexImage]),
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          constraints: const BoxConstraints(),
                          iconSize: 17.sp,
                          color: Colors.red.shade800,
                          icon: const Icon(Icons.remove_circle),
                          onPressed: () {deleteListImageTabCheck(indexImage);},
                        ),
                      ),
                    ]),
              );
            }));
  }
}