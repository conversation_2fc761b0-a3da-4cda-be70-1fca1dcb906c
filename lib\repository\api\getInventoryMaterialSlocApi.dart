import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';

import '../../urlApi/urlApi.dart';

class GetInventoryMaterialSlocApi {
  static Future<http.Response> postInventoryMaterialSloc(String productCode, String sloc, String token) async {
    Map data = {"productCode": productCode, "sloc": sloc};

    var body = json.encode(data);
    if (kDebugMode) {
      print(body);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    var url = Uri.parse(baseUrl + UrlApi.baseUrlBatch + "get-inventory-material-sloc");
    debugPrint(url.toString());
    http.Response response = await http.post(url, headers: UrlApi.headersToken(token), body: body);
    return response;
  }
}
