import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/getAutoBatch.dart';
import '../../urlApi/urlApi.dart';

class GetAuToBatchApi {
  static Future<http.Response> postAutoBATCH(GetAutoBatch getAutoBatch, String token) async {
    Map<String, dynamic> data = {};
    data = {
      "materialType": getAutoBatch.materialType,
      "importExportType": getAutoBatch.importExportType,
      "materialGroup": getAutoBatch.materialGroup,
      "materialStatus": getAutoBatch.materialStatus,
      "nsxhsd": getAutoBatch.nsxhsd,
      "skinType": getAutoBatch.skinType,
      "skinColor": getAutoBatch.skinColor,
      "caseCode": getAutoBatch.caseCode
    };
    data.removeWhere((key, value) => value == null);
    final dataPost = json.encode(data);
    if (kDebugMode) {
      debugPrint(dataPost.toString());
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlBatch + "get-auto-batch");
    // print(url);
    debugPrint(url.toString());
    debugPrint(dataPost);

    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    if (kDebugMode) {
      print(response.body);
    }
    return response;
  }
}
