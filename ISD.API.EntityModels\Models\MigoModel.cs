﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MigoModel", Schema = "MES")]
    public partial class MigoModel
    {
        [Key]
        public Guid MigoId { get; set; }
        [StringLength(50)]
        public string MBLNR { get; set; }
        [StringLength(10)]
        public string MJAHR { get; set; }
        [StringLength(10)]
        public string ZEILE { get; set; }
        [StringLength(50)]
        public string VBELN { get; set; }
        [StringLength(10)]
        public string BWART { get; set; }
        [StringLength(10)]
        public string XAUTO { get; set; }
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(10)]
        public string LGORT { get; set; }
        [StringLength(50)]
        public string CHARG { get; set; }
        [StringLength(10)]
        public string INSMK { get; set; }
        [StringLength(10)]
        public string SOBKZ { get; set; }
        [StringLength(10)]
        public string SHKZG { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? MENGE { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ERFMG { get; set; }
        [StringLength(10)]
        public string ERFME { get; set; }
        [StringLength(50)]
        public string EBELN { get; set; }
        [StringLength(10)]
        public string EBELP { get; set; }
        [StringLength(50)]
        public string RSNUM { get; set; }
        [StringLength(10)]
        public string RSPOS { get; set; }
        [StringLength(50)]
        public string AUFNR { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? isDeleted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DeletedTime { get; set; }
        [StringLength(50)]
        public string MType { get; set; }
    }
}