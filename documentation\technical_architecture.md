# Technical Architecture

## Technology Stack

TTF MES Mobile is built using the following technology stack:

### Frontend
- **Framework**: Flutter (SDK v2.19.2 or higher)
- **Programming Language**: Dart
- **State Management**: Simple state management using StatefulWidget
- **Navigation**: Flutter Navigator 2.0 with named routes

### Mobile Features
- **QR Code Scanning**: qr_code_scanner (v1.0.1)
- **Secure Storage**: flutter_secure_storage (v8.0.0)
- **Local Storage**: shared_preferences (v2.0.17)
- **HTTP Client**: http (v0.13.5)
- **Image Handling**: image_picker (v1.0.2), gallery_saver (v2.3.2), photo_view (v0.14.0)
- **Permissions**: permission_handler (v10.2.0)
- **Connectivity**: connectivity_plus (v5.0.2)
- **Localization**: intl (v0.18.0), flutter_localizations

### Backend Integration
- **API Communication**: RESTful API communication using HTTP package
- **Data Format**: JSON
- **Authentication**: Token-based authentication

### Development Tools
- **IDE**: VS Code, Android Studio, or Cursor
- **Linting**: flutter_lints (v2.0.0)
- **Asset Management**: flutter_launcher_icons, flutter_native_splash
- **Versioning**: Semantic versioning (major.minor.patch+build)

## Application Architecture

The TTF MES Mobile application follows a layered architecture pattern:

### Presentation Layer
- **Pages**: Contains screen implementations for all features
- **Widgets**: Reusable UI components
- **Element**: Feature-specific UI components

### Business Logic Layer
- **Services**: Application-wide services like navigation, authentication
- **Utils**: Helper functions and utilities

### Data Layer
- **Repositories**: Handles data operations and API communication
- **Models**: Data structures representing business entities
- **Storage**: Local data persistence

### Main Components
1. **Router**: Centralized routing system (route.dart)
2. **Storage Service**: Handles persistent data storage
3. **Navigation Service**: Manages app navigation
4. **API Client**: Handles communication with backend services
5. **UI Components**: Reusable widgets for consistent UI

## Project Structure

The application follows a feature-based project structure:

```
lib/
├── constants.dart               # Global constants
├── main.dart                    # Application entry point
├── data/                        # Data-related files and mock data
├── element/                     # UI components and feature-specific widgets
├── model/                       # Data models for all features
├── page/                        # Screen implementations
│   ├── Downtime/                # Downtime tracking feature
│   ├── KiemTraChatLuong/        # Quality control feature
│   ├── MaintenanceOrder/        # Maintenance order feature
│   ├── TyLeTieuHao/             # Material consumption feature
│   └── [Other feature folders]  # Other feature-specific screens
├── repository/                  # Data access layer
│   ├── api/                     # API communication
│   └── function/                # Repository functions
├── route/                       # Navigation configuration
├── screenArguments/             # Arguments for screen navigation
├── service/                     # Application services
│   ├── globalValue.dart         # Global state/values
│   └── navigatorService.dart    # Navigation service
├── Storage/                     # Data persistence
│   ├── storageSecureStorage.dart     # Secure storage implementation
│   └── storageSharedPreferences.dart # SharedPreferences implementation
├── urlApi/                      # API endpoint definitions
├── utils/                       # Utility functions and helpers
└── Widget/                      # Reusable widgets
    ├── container/               # Container widgets
    ├── dialogWidget/            # Dialog widgets
    └── modalBottomSheet/        # Bottom sheet widgets
```

### Key Files and Directories

- **main.dart**: Entry point of the application, contains initial setup and app configuration
- **constants.dart**: Global constants used throughout the application
- **route/route.dart**: Routing configuration for the entire application
- **service/globalValue.dart**: Global values and state management
- **Storage/**: Contains implementations for data persistence
- **page/**: Contains all screens organized by feature modules
- **model/**: Data model classes for different features
- **repository/api/**: API communication layer
- **utils/**: Utility functions and helpers for the application

## Environment Configuration

The application supports multiple environments with different configurations:

### Environment Setup
- **Development**: Points to development backend services
- **QA**: Points to testing backend services
- **Production**: Points to production backend services

### Configuration Parameters
- **API Endpoints**: Base URLs for different API endpoints
- **Authentication**: Authentication settings and endpoints
- **Application Settings**: Timeout duration, cache settings, etc.
- **Feature Flags**: Toggles for enabling/disabling features

### Configuration Management
- **AppConfig**: Located in `utils/appConfig.dart`, handles environment-specific configuration
- **Environment Detection**: Determines the current environment based on build settings
- **Feature Toggles**: Enables/disables features based on environment

### Version Management
- **Version Information**: Available in the pubspec.yaml file (currently v3.3.14+3314)
- **Version Display**: Shown in the application UI (main page footer)
- **Upgrade Management**: Using upgrader package (v8.4.0) for handling app updates

This architecture ensures a clean separation of concerns, maintainable code organization, and flexibility to adapt to changing requirements. 