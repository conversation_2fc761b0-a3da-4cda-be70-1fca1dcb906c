﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ServiceOrderConsultModel", Schema = "ghService")]
    public partial class ServiceOrderConsultModel
    {
        [Key]
        public Guid ServiceOrderConsultId { get; set; }
        public Guid? ServiceOrderId { get; set; }
        [StringLength(400)]
        public string RequestMessage { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateDate { get; set; }
        public Guid? AccountId { get; set; }
        public bool? IsThayThe { get; set; }

        [ForeignKey("ServiceOrderId")]
        [InverseProperty("ServiceOrderConsultModel")]
        public virtual ServiceOrderModel ServiceOrder { get; set; }
    }
}