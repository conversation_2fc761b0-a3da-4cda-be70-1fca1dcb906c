﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MarmModel", Schema = "MES")]
    public partial class MarmModel
    {
        [Key]
        public Guid MarnId { get; set; }
        [Required]
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(50)]
        public string MEINH { get; set; }
        public int? UMREZ { get; set; }
        public int? UMREN { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? LAENG { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? BREIT { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? HOEHE { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? VOLUM { get; set; }
        [StringLength(50)]
        public string VOLEH { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? BRGEW { get; set; }
        [Column(TypeName = "decimal(18, 0)")]
        public decimal? NTGEW { get; set; }
        [StringLength(50)]
        public string GEWEI { get; set; }
        [StringLength(50)]
        public string MEABM { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}