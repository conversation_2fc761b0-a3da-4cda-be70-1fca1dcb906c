﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("MaterialReservationModel", Schema = "MES")]
    public partial class MaterialReservationModel
    {
        public MaterialReservationModel()
        {
            WarehouseTranferModel = new HashSet<WarehouseTranferModel>();
        }

        [Key]
        public Guid ReservationId { get; set; }
        [StringLength(20)]
        public string ReservationNumber { get; set; }
        [StringLength(20)]
        public string ReservationItemNumber { get; set; }
        [StringLength(20)]
        public string ItemDeleted { get; set; }
        [StringLength(20)]
        public string ReservationGoodsMove { get; set; }
        [StringLength(20)]
        public string FinalIssue { get; set; }
        [StringLength(40)]
        public string MaterialNumber { get; set; }
        [StringLength(20)]
        public string StorageLocation { get; set; }
        [StringLength(10)]
        public string BatchNumber { get; set; }
        [StringLength(20)]
        public string SpecialStockIndicator { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ReqComponentDate { get; set; }
        public int? ReqComponentYear { get; set; }
        public int? ReqComponentMonth { get; set; }
        public int? ReqComponentDay { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? ReqQuantity { get; set; }
        [StringLength(20)]
        public string MeasureUnit { get; set; }
        [StringLength(20)]
        public string DCIndicator { get; set; }
        [StringLength(20)]
        public string UnitEntry { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? UnitEntryQuantity { get; set; }
        [StringLength(20)]
        public string OrderNumber { get; set; }
        [StringLength(20)]
        public string SalesOrderNumber { get; set; }
        [StringLength(20)]
        public string SalesOrderNumberItem { get; set; }
        [StringLength(20)]
        public string RIPlant { get; set; }
        [StringLength(20)]
        public string RIStorageLocation { get; set; }
        [Column(TypeName = "decimal(13, 3)")]
        public decimal? InputQuantity { get; set; }
        [Column(TypeName = "decimal(5, 0)")]
        public decimal? MeasureUnitNumerator { get; set; }
        [Column(TypeName = "decimal(5, 0)")]
        public decimal? MeasureUnitDenominator { get; set; }
        [StringLength(50)]
        public string WBSElement { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
        [StringLength(20)]
        public string Plant { get; set; }
        public Guid? ReservationHeaderId { get; set; }
        [StringLength(100)]
        public string Note { get; set; }

        [ForeignKey("ReservationHeaderId")]
        [InverseProperty("MaterialReservationModel")]
        public virtual ReservationHeaderModel ReservationHeader { get; set; }
        [InverseProperty("Reservation")]
        public virtual ICollection<WarehouseTranferModel> WarehouseTranferModel { get; set; }
    }
}