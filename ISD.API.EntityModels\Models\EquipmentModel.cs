﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("EquipmentModel", Schema = "MES")]
    public partial class EquipmentModel
    {
        [Key]
        public Guid EquipmentId { get; set; }
        [StringLength(50)]
        public string EQART { get; set; }
        [StringLength(500)]
        public string EARTX { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}