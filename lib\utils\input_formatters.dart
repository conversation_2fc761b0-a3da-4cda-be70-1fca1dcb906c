import 'package:flutter/services.dart';

/// This is a text input formatter that turns commas into dots.
///
/// Whenever you type in a text field, it checks what you've typed.
/// If you've typed a comma, it changes it into a dot.
/// This is helpful for typing numbers that use a dot for decimals.
///
/// Here's how you can use it:
/// ```dart
/// TextField(
///   inputFormatters: [CommaToDotTextInputFormatter()],
/// )
/// ```
class CommaToDotTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text.replaceAll(',', '.');
    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}
