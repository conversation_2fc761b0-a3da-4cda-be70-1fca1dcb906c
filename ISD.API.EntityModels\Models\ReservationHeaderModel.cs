﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ReservationHeaderModel", Schema = "MES")]
    public partial class ReservationHeaderModel
    {
        public ReservationHeaderModel()
        {
            MaterialReservationModel = new HashSet<MaterialReservationModel>();
        }

        [Key]
        public Guid ReservationHeaderId { get; set; }
        [StringLength(20)]
        public string ReservationNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? RequestDate { get; set; }
        [StringLength(20)]
        public string MovementType { get; set; }
        [StringLength(50)]
        public string GoodsRecipient { get; set; }
        [StringLength(50)]
        public string CostCenter { get; set; }
        [StringLength(50)]
        public string AssetNumber { get; set; }
        [StringLength(50)]
        public string AccountNumberCustomer { get; set; }
        [StringLength(50)]
        public string OrderNumber { get; set; }
        [StringLength(50)]
        public string SalesOrderNumber { get; set; }
        [StringLength(50)]
        public string SalesOrderNumberItem { get; set; }
        [StringLength(50)]
        public string RIPlant { get; set; }
        [StringLength(50)]
        public string RIStorageLocation { get; set; }
        [StringLength(50)]
        public string WBSElement { get; set; }
        public bool? IsApproved { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public Guid? ApprovedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ApprovedDate { get; set; }

        [InverseProperty("ReservationHeader")]
        public virtual ICollection<MaterialReservationModel> MaterialReservationModel { get; set; }
    }
}