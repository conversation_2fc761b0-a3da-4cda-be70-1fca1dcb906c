import 'FilterLsQC.dart';

class GetListQCTicketByFilter {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataListQC>? data;
  AdditionalData? additionalData;

  GetListQCTicketByFilter({this.code, this.isSuccess, this.message, this.data, this.additionalData});

  GetListQCTicketByFilter.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataListQC>[];
      json['data'].forEach((v) {
        data!.add(DataListQC.fromJson(v));
      });
    }
    additionalData = json['additionalData'] != null ? AdditionalData.fromJson(json['additionalData']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (additionalData != null) {
      data['additionalData'] = additionalData!.toJson();
    }
    return data;
  }
}

class DataListQC {
  String? qualityControlId;
  String? saleOrgCode;
  String? workCenterCode;
  String? lsxdt;
  String? lsxsap;
  String? dsx;
  String? productCode;
  String? productName;
  String? confirmDate;
  String? qualityDate;

  bool? status;
  String? result;
  int? stt;

  String? productAttribute;
  String? stepCode;
  String? stepName;
  String? finishedColor;

  String? nhaCungCap;
  String? nhaGiaCong;
  String? khachHangId;
  double? qty;
  String? barcode;
  String? loaiMau;

  DataListQC({
    this.qualityControlId,
    this.saleOrgCode,
    this.workCenterCode,
    this.lsxdt,
    this.lsxsap,
    this.dsx,
    this.productCode,
    this.productName,
    this.confirmDate,
    this.qualityDate,
    this.status,
    this.result,
    this.stt,
    this.productAttribute,
    this.stepCode,
    this.stepName,
    this.finishedColor,
    this.nhaCungCap,
    this.nhaGiaCong,
    this.khachHangId,
    this.qty,
    this.barcode,
    this.loaiMau,
  });

  DataListQC.fromJson(Map<String, dynamic> json) {
    qualityControlId = json['qualityControlId'];
    saleOrgCode = json['saleOrgCode'];
    workCenterCode = json['workCenterCode'];
    lsxdt = json['lsxdt'];
    lsxsap = json['lsxsap'];
    dsx = json['dsx'];
    productCode = json['productCode'];
    productName = json['productName'];
    confirmDate = json['confirmDate'];
    qualityDate = json['qualityDate'];
    status = json['status'];
    result = json['result'];
    stt = json['stt'];
    productAttribute = json['productAttribute'];
    stepCode = json['stepCode'];
    stepName = json['stepName'];
    finishedColor = json['finishedColor'];

    nhaCungCap = json['nhaCungCap'];
    nhaGiaCong = json['nhaGiaCong'];
    khachHangId = json['khachHangId'];
    qty = json['qty'];
    barcode = json['barcode'];
    loaiMau = json['loaiMau'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qualityControlId'] = qualityControlId;
    data['saleOrgCode'] = saleOrgCode;
    data['workCenterCode'] = workCenterCode;
    data['lsxdt'] = lsxdt;
    data['lsxsap'] = lsxsap;
    data['dsx'] = dsx;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['confirmDate'] = confirmDate;
    data['qualityDate'] = qualityDate;
    data['status'] = status;
    data['result'] = result;
    data['stt'] = stt;
    data['productAttribute'] = productAttribute;
    data['stepCode'] = stepCode;
    data['stepName'] = stepName;
    data['finishedColor'] = finishedColor;

    data['nhaCungCap'] = nhaCungCap;
    data['nhaGiaCong'] = nhaGiaCong;
    data['khachHangId'] = khachHangId;
    data['qty'] = qty;
    data['barcode'] = barcode;
    data['loaiMau'] = loaiMau;
    return data;
  }
}
