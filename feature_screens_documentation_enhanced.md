# TTF MES Mobile Application Screen Documentation (Enhanced)

This document provides a comprehensive overview of all screens in the TTF MES Mobile application, organized by main features.

## Table of Contents
1. [<PERSON><PERSON> nhận sản lượ<PERSON> (Production Recording)](#1-ghi-nhận-sản-lượng-production-recording)
2. [Quản lý NVL (Raw Material Management)](#2-quản-lý-nvl-raw-material-management)
3. [Quản lý TP (Product Management)](#3-quản-lý-tp-product-management)
4. [<PERSON><PERSON><PERSON> tra chất lượ<PERSON> (Quality Control)](#4-kiểm-tra-chất-lượng-quality-control)
5. [QC Passed Stamp](#5-qc-passed-stamp)
6. [Downtime](#6-downtime)
7. [Maintenance Order](#7-maintenance-order)
8. [Tỷ lệ tiêu hao (Consumption Rate)](#8-tỷ-lệ-tiêu-hao-consumption-rate)
9. [<PERSON><PERSON><PERSON> hàng NCC (Supplier Return)](#9-trả-hàng-ncc-supplier-return)
10. [Notifications](#10-notifications)
11. [Authentication and General Screens](#11-authentication-and-general-screens)

---

## 1. <PERSON><PERSON> nhận sản lượng (Production Recording)

### Main Screens
- **SwitchingStages**: Records production progress between stages
- **CompleteTheBigStage**: Completes major production stages
- **InforSwitchingStages**: Displays information about stage transitions
- **DetailReport**: Shows detailed production reports
- **Info**: Displays detailed information about a production barcode

### Screen Flow
1. **SwitchingStage**
   - Path: `/SwitchingStage`
   - Purpose: Allows users to record transitions between production stages
   - Parameters: 
     - `switchingStateData`: Data about the current switching state
     - `token`: Authentication token
     - `switchingStages`: Stages information
     - `dateTimeOld`: Timestamp
     - `toBarcode`: Target barcode

2. **CompleteTheBigStage**
   - Path: `/CompleteTheBigStage`
   - Purpose: Marks completion of major production stages
   - Parameters:
     - `getData`: Data to be processed
     - `token`: Authentication token
     - `confirmWorkCenter`: Work center confirmation data
     - `dateTimeOld`: Timestamp

3. **DetailReport**
   - Path: `/DetailReport`
   - Purpose: Displays detailed production reports
   - Parameters:
     - `getData`: Report data
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

4. **Info**
   - Path: `/info`
   - Purpose: Displays detailed information about a production barcode
   - Parameters:
     - `barcode`: The scanned barcode
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

5. **QRCodePage**
   - Path: `/QRCodePage`
   - Purpose: Scans barcodes for production information
   - Parameters:
     - `fromPage`: Source page
     - `token`: Authentication token
     - `permission`: User permissions
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

## 2. Quản lý NVL (Raw Material Management)

### Main Screens
- **InventoryManagement**: Main inventory management screen
- **ImportWarehouseSAP**: Imports materials from SAP
- **ListTranferMaterial**: Lists material transfers
- **ExportWarehouse**: Exports materials from warehouse
- **AddTranferWarehouse**: Adds new material transfers
- **MaterialRetail**: Manages retail materials
- **MaterialUnused**: Manages unused materials
- **StatisticsMaterials**: Shows material statistics
- **DetailReservation**: Shows detailed reservation information

### Screen Flow
1. **InventoryManagement**
   - Path: `/InventoryManagement`
   - Purpose: Main screen for inventory management
   - Parameters:
     - `permission`: User permissions
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID

2. **ImportWarehouseSAP**
   - Path: `/ImportWarehouse`
   - Purpose: Imports materials from SAP system
   - Parameters:
     - `barcode`: Material barcode
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `fromPage`: Source page

3. **ListTranferMaterial** and **ListTranferMaterial2**
   - Paths: `/ListTranferMaterial` and `/ListTranferMaterial2`
   - Purpose: Lists all material transfers
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp
     - `accountId`: User account ID

4. **ExportWarehouse** and **ExportWarehouse2**
   - Paths: `/ExportWarehouse` and `/ExportWarehouse2`
   - Purpose: Exports materials from warehouse
   - Parameters:
     - `dataListWarehouseTranfer`: Transfer data
     - `token`: Authentication token
     - `plant`: Plant information
     - `lsDataSlocAddress`: Storage location addresses
     - `dateTimeOld`: Timestamp

5. **AddTranferWarehouse**
   - Path: `/AddTranferWarehouse`
   - Purpose: Adds new material transfers
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `lsDataSlocAddress`: Storage location addresses
     - `dateTimeOld`: Timestamp

6. **MaterialRetail**
   - Path: `/MaterialRetail`
   - Purpose: Manages retail materials
   - Parameters:
     - `token`: Authentication token
     - `barcode`: Material barcode
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

7. **MaterialUnused**
   - Path: `/MaterialUnused`
   - Purpose: Manages unused materials
   - Parameters:
     - `token`: Authentication token
     - `dateTimeOld`: Timestamp

8. **StatisticsMaterials**
   - Path: `/StatisticsMaterials`
   - Purpose: Shows material statistics
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

9. **InventoryMaterialSloc**
   - Path: `/InventoryMaterialSloc`
   - Purpose: Manages inventory by storage location
   - Parameters:
     - `token`: Authentication token
     - `plant`: Plant information
     - `dateTimeOld`: Timestamp

10. **DetailReservation**
    - Path: `/DetailReservation`
    - Purpose: Shows detailed reservation information
    - Parameters:
      - `dataListWarehouseTranfer`: Transfer data
      - `token`: Authentication token
      - `dateTimeOld`: Timestamp

### QR Code Scanning Screens for Raw Material Management
- **QRCodePageChooseAnAddress**: Scans QR code to choose an address
- **QRcodePageTranferMaterial**: QR code for material transfers
- **QRCodePageGetSlocChooseAnTranferWareHouse**: QR code for storage location in transfers
- **QRCodePageGetSlocExport**: QR code for storage location in exports
- **QRCodeInventoryMaterialSloc**: QR code for inventory by storage location