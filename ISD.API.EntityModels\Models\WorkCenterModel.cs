﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WorkCenterModel", Schema = "MES")]
    public partial class WorkCenterModel
    {
        public WorkCenterModel()
        {
            QualityControlInformation = new HashSet<QualityControlInformationModel>();
        }

        [Key]
        [StringLength(50)]
        public string WorkCenterCode { get; set; }
        [StringLength(100)]
        public string WorkCenterName { get; set; }
        [StringLength(50)]
        public string SaleOrgCode { get; set; }
        public int? OrderIndex { get; set; }

        [ForeignKey("WorkCenterCode")]
        [InverseProperty("WorkCenterCode")]
        public virtual ICollection<QualityControlInformationModel> QualityControlInformation { get; set; }
    }
}