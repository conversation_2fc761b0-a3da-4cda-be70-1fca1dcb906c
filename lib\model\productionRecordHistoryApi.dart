class ProductionRecordHistoryApi {
  String? stockRecevingType;
  String? createTime;
  String? createByName;
  double? quantity;
  String? itmno;
  String? fromTime;
  String? toTime;
  String? customerReference;
  double? quantityD;
  double? quantityKD;
  String? ktext;
  String? stepCode;
  int? phase;
  String? fromDate;
  String? toDate;

  ProductionRecordHistoryApi(
      {this.stockRecevingType,
        this.createTime,
        this.createByName,
        this.quantity,
        this.itmno,
        this.fromTime,
        this.toTime,
        this.customerReference,
        this.quantityD,
        this.quantityKD,
        this.ktext,
        this.stepCode,
        this.phase,
        this.fromDate,
        this.toDate});

  ProductionRecordHistoryApi.fromJson(Map<String, dynamic> json) {
    stockRecevingType = json['stockRecevingType'];
    createTime = json['createTime'];
    createByName = json['createByName'];
    quantity = json['quantity'];
    itmno = json['itmno'];
    fromTime = json['fromTime'];
    toTime = json['toTime'];
    customerReference = json['customerReference'];
    quantityD = json['quantity_D'];
    quantityKD = json['quantity_KD'];
    ktext = json['ktext'];
    stepCode = json['stepCode'];
    phase = json['phase'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stockRecevingType'] = stockRecevingType;
    data['createTime'] = createTime;
    data['createByName'] = createByName;
    data['quantity'] = quantity;
    data['itmno'] = itmno;
    data['fromTime'] = fromTime;
    data['toTime'] = toTime;
    data['customerReference'] = customerReference;
    data['quantity_D'] = quantityD;
    data['quantity_KD'] = quantityKD;
    data['ktext'] = ktext;
    data['stepCode'] = stepCode;
    data['phase'] = phase;
    data['fromDate'] = fromDate;
    data['toDate'] = toDate;
    return data;
  }
}
