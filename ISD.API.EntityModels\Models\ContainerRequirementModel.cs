﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("ContainerRequirementModel", Schema = "ghMasterData")]
    public partial class ContainerRequirementModel
    {
        public ContainerRequirementModel()
        {
            MaterialModel = new HashSet<MaterialModel>();
        }

        [Key]
        [StringLength(50)]
        public string ContainerRequirementCode { get; set; }
        [StringLength(400)]
        public string ContainerRequirementName { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("ContainerRequirementCodeNavigation")]
        public virtual ICollection<MaterialModel> MaterialModel { get; set; }
    }
}