class GetStepByProductBach {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetStepByProductBach? data;


  GetStepByProductBach(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetStepByProductBach.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetStepByProductBach.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetStepByProductBach {
  String? batchNumber;
  String? productCode;
  List<Steps>? steps;

  DataGetStepByProductBach({this.batchNumber, this.productCode, this.steps});

  DataGetStepByProductBach.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
    productCode = json['productCode'];
    if (json['steps'] != null) {
      steps = <Steps>[];
      json['steps'].forEach((v) {
        steps!.add(Steps.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    data['productCode'] = productCode;
    if (steps != null) {
      data['steps'] = steps!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Steps {
  String? stepCode;
  String? stepCodeDisplay;

  Steps({this.stepCode, this.stepCodeDisplay});

  Steps.fromJson(Map<String, dynamic> json) {
    stepCode = json['stepCode'];
    stepCodeDisplay = json['stepCodeDisplay'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stepCode'] = stepCode;
    data['stepCodeDisplay'] = stepCodeDisplay;
    return data;
  }
}