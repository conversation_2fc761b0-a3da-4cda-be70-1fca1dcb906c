﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISD.API.Constant
{
    public class ConstWorkFlowCategory
    {
        public static string ACTIVITIES = "ACTIVITIES";
        public static string THKH = "THKH";
        public static string TICKET = "TICKET";
        public static string TICKET_MLC = "TICKET_MLC";
        public static string GTB = "GTB";
        public static string TASK = "TASK";
        public static string MISSION = "MISSION";
        public static string GT = "GT";
        //Book lịch tham quan
        public static string BOOKING_VISIT = "BOOKING_VISIT";
        public static string SUBTASK_BOOKINGVISIT = "SUBTASK_VISIT";

        //Hỏi đáp
        public static string QNA = "QNA";

        public static string MyFollow = "MyFollow";
        public static string MyWork = "MyWork";
        public static string MyCalendar = "MyCalendar";

        public static string ALL = "ALL";

        //<PERSON><PERSON>ệt đơn hàng
        public static string REQUEST_ORDER_1000 = "REQUEST_ORDER_1000";
        public static string REQUEST_ORDER_2000 = "REQUEST_ORDER_2000";
    }
}
