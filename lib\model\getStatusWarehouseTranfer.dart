class GetStatusWarehouseTranfer {
  int? code;
  bool? isSuccess;
  String? message;
  List<DataGetStatusWarehouseTranfer>? data;


  GetStatusWarehouseTranfer(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetStatusWarehouseTranfer.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    if (json['data'] != null) {
      data = <DataGetStatusWarehouseTranfer>[];
      json['data'].forEach((v) {
        data!.add(DataGetStatusWarehouseTranfer.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataGetStatusWarehouseTranfer {
  String? key;
  String? value;

  DataGetStatusWarehouseTranfer({this.key, this.value});

  DataGetStatusWarehouseTranfer.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}