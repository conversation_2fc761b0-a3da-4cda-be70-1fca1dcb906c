class PostBatch {
  String? reservationId;
  String? productCode;
  String? plant;
  String? sloc;
  String? batchNumber;

  PostBatch(
      {this.reservationId,
        this.productCode,
        this.plant,
        this.sloc,
        this.batchNumber});

  PostBatch.fromJson(Map<String, dynamic> json) {
    reservationId = json['reservationId'];
    productCode = json['productCode'];
    plant = json['plant'];
    sloc = json['sloc'];
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['reservationId'] = reservationId;
    data['productCode'] = productCode;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['batchNumber'] = batchNumber;
    return data;
  }
}