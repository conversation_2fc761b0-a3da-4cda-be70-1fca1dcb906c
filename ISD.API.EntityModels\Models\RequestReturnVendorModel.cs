﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RequestReturnVendorModel", Schema = "MES")]
    public partial class RequestReturnVendorModel
    {
        public RequestReturnVendorModel()
        {
            RequestReturnVendorDetailModel = new HashSet<RequestReturnVendorDetailModel>();
        }

        [Key]
        public Guid Id { get; set; }
        [StringLength(50)]
        public string RequestReturnVendorCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        public Guid CreatorId { get; set; }
        public Guid? WarehouseStaffId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WarehouseVerifyTime { get; set; }
        public Guid? AccountantId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? AccountantVerifyTime { get; set; }
        public int? ReturnType { get; set; }
        [StringLength(50)]
        [Unicode(false)]
        public string VendorCode { get; set; }
        [StringLength(50)]
        public string VendorName { get; set; }
        [StringLength(10)]
        [Unicode(false)]
        public string CompanyCode { get; set; }

        [InverseProperty("RequestReturnVendorModel")]
        public virtual ICollection<RequestReturnVendorDetailModel> RequestReturnVendorDetailModel { get; set; }
    }
}