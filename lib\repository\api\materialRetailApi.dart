import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/postMaterialRetail.dart';
import 'package:http/http.dart' as http;
import '../../urlApi/urlApi.dart';

class MaterialRetailApi {
  static Future<http.Response> postMaterialRetailApi(PostMaterialRetail postMaterialRetail, String token) async {
    final dataPost = jsonEncode(postMaterialRetail);
    if (kDebugMode) {
      print(dataPost);
    }
    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final url = Uri.parse(baseUrl + UrlApi.baseUrlWarehouseTransaction + "MaterialRetail");
    debugPrint(url.toString());
    final response = await http.post(url, headers: UrlApi.headersToken(token), body: dataPost);
    return response;
  }

  static Future<http.Response> getSlocByProduct(String productCode, String token) async {
    Map<String, dynamic> data = {"ProductCode": productCode};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetSlocByProduct", data);
    if (kDebugMode) {
      print(url);
    }
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }

  static Future<http.Response> getSOWBSByTask(String taskId, String token) async {
    Map<String, dynamic> data = {"TaskId": taskId};

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrlPrd : await UrlApi.baseUrlQas;

    final url = Uri.https(baseUrl, "${UrlApi.baseUrlWarehousTransaction_2}GetSOWBSByTask", data);
    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
