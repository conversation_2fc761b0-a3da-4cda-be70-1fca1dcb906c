import 'dart:ui';

final Set<String> pointerUnits = {'M', 'M2', 'M3', 'DAA'};
const double inputHeight = 30.0;

// global color: 0xff303F9F
const Color primaryColor = Color(0xff303F9F);

class ImageConfiguration {
  final double? maxWidth;
  final double? maxHeight;
  final int? imageQuality;

  const ImageConfiguration({
    required this.maxWidth,
    required this.maxHeight,
    required this.imageQuality,
  });
}

const ImageConfiguration globalImageConfig = ImageConfiguration(
  maxWidth: 1440,
  maxHeight: 1440,
  imageQuality: 80,
);
