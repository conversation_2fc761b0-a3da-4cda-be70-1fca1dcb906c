﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("StyleModel", Schema = "tSale")]
    public partial class StyleModel
    {
        public StyleModel()
        {
            ColorProductModel = new HashSet<ColorProductModel>();
            PriceProductModel = new HashSet<PriceProductModel>();
            WarehouseProductModel = new HashSet<WarehouseProductModel>();
        }

        [Key]
        public Guid StyleId { get; set; }
        [Required]
        [StringLength(50)]
        public string StyleCode { get; set; }
        [Required]
        [StringLength(100)]
        public string StyleName { get; set; }
        public int? OrderIndex { get; set; }
        public bool Actived { get; set; }

        [InverseProperty("Style")]
        public virtual ICollection<ColorProductModel> ColorProductModel { get; set; }
        [InverseProperty("Style")]
        public virtual ICollection<PriceProductModel> PriceProductModel { get; set; }
        [InverseProperty("Style")]
        public virtual ICollection<WarehouseProductModel> WarehouseProductModel { get; set; }
    }
}