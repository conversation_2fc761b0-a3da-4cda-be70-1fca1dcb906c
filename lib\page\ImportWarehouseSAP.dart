import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:intl/intl.dart';
import 'package:ttf/constants.dart';
import 'package:ttf/model/receiveIntegrationSAP.dart';
import 'package:ttf/utils/ui_helpers.dart';
import '../Widget/container/errorFormatCheck.dart';
import '../Widget/dialogWidget/DialogConfirmImportWH.dart';
import '../Widget/dialogWidget/DialogError.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountAndroid.dart';
import '../Widget/dialogWidget/DialogExceedTheAmountiOS.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionAndroid.dart';
import '../Widget/dialogWidget/DialogTimeOutSessionIOS.dart';
import '../element/DropdownImportWHSap.dart';
import '../element/TableInfo.dart';
import '../element/errorViewPost.dart';
import '../element/timeOut.dart';
import '../model/GetInfoReceiSap.dart';
import '../model/GetListCatalog.dart';
import '../model/GetListCatalogTypeCode.dart';
import '../model/SelectedGoodReceivedNotes.dart';
import '../model/getAutoBatch.dart';
import '../model/getStorageBin.dart';
import '../model/rawMaterialCard.dart';
import '../model/slocAddresse.dart';
import '../repository/commatextInputFormatter.dart';
import '../repository/function/importWareHouseFunction.dart';
import 'LostConnect.dart';

class ImportWarehouseSAP extends StatefulWidget {
  const ImportWarehouseSAP(
      {Key? key, required this.barcode, required this.token, required this.plant, required this.dateTimeOld, required this.fromPage})
      : super(key: key);
  final String barcode;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String fromPage;

  @override
  _ImportWarehouseSAPState createState() => _ImportWarehouseSAPState();
}

class _ImportWarehouseSAPState extends State<ImportWarehouseSAP> {
  final _controllerNumberLot = TextEditingController();
  final _storageBinController = TextEditingController();
  final _skinTypesController = TextEditingController();
  final _skinColorController = TextEditingController();
  final _numberCodeController = TextEditingController();
  final _TDSController = TextEditingController();
  final _SoToKhaiController = TextEditingController();
  final List<TextEditingController> _lsControllers = [];
  final _focusNumberLot = FocusNode();
  final _focusStorageBin = FocusNode();
  final _focusSkinTypes = FocusNode();
  final _focusSkinColor = FocusNode();
  final _focusTDS = FocusNode();
  final _focusSoToKhai = FocusNode();
  final _focusNumberCode = FocusNode();
  final List<FocusNode> _lsFocusNode = [];
  // List<DataGetListCatalogTypeCode> _lsDataGetListCatalogTypeCode = [];

  Timer? _timer;
  String? _storageBinID;
  DataSlocAddress? _selectedSloc;
  // String? _selectedGoodReceivedNotes;
  DataRawMeterial? _dataRawMeterial;
  List<PoDetailResponses>? _poDetailResponses = [];
  double _sumAmountEntered = 0.0;
  List<DataSlocAddress> _getLsDataSlocAddress = [];
  List<DataSlocAddress>? _listDataSlocAddress = [];
  bool _isLoading = false;
  bool _isLoadingStorageBin = false;
  bool _notWifi = false;
  bool _errorSelectedSloc = false;
  // bool _errorSelectedGoodReceivedNotes = false;
  List<DataGetStorageBin> _lsDataGetStorageBn = [];
  bool _errorController = false;
  bool _disableDropdownSloc = false;
  bool _errorStorageBin = false;
  bool _errorBatch = false;
  String _error = "";
  bool _isLoadingUpdateStatus = false;
  bool _viTab = true;
  late bool _timeOut;
  bool? _disableButton;
  DataGetListCatalog? _dataGetListCatalogTypeProduct;
  DataGetListCatalog? _dataGetListCatalogProductOrigin;
  DataGetListCatalog? _dataGetListCatalogProductGr;
  DataGetListCatalog? _dataGetListCatalogProductStatus;
  DataGetListCatalog? _dataGetListCatalogNSXHSD;
  bool _isLoadingAutoBatch = false;
  bool _validateProductType = false;
  bool _validateImportExportType = false;
  bool _validateMaterialGroup = false;
  bool _validateMaterialStatus = false;
  bool _validateNsxhsd = false;
  bool _validateSkinType = false;
  bool _validatecCaseCode = false;
  bool _validatecSkinColor = false;
  DataGetInfoReceiSap? _dataGetInfoReceiSap;

  List<DataGetListCatalog> _lsProductType = [];
  List<DataGetListCatalog> _lsProductOrigin = [];
  List<DataGetListCatalog> _lsProductGrp = [];
  List<DataGetListCatalog> _lsProductStatus = [];
  final List<DataGetListCatalog> _lsNSXHSD = [
    DataGetListCatalog(key: "1", value: "1"),
    DataGetListCatalog(key: "2", value: "2"),
    DataGetListCatalog(key: "3", value: "3"),
    DataGetListCatalog(key: "4", value: "4"),
    DataGetListCatalog(key: "5", value: "5"),
    DataGetListCatalog(key: "6", value: "6"),
    DataGetListCatalog(key: "7", value: "7"),
    DataGetListCatalog(key: "8", value: "8"),
    DataGetListCatalog(key: "9", value: "9"),
  ];

  bool? poNhapKhau42;

  @override
  void initState() {
    super.initState();
    _setListController();
  }

  void _checkErrorAutoBatch() {
    setState(() {
      if (_dataGetListCatalogTypeProduct == null) {
        _validateProductType = true;
      } else {
        _validateProductType = false;
      }
      if (_dataGetListCatalogProductOrigin == null) {
        _validateImportExportType = true;
      } else {
        _validateImportExportType = false;
      }

      if (_dataGetListCatalogTypeProduct != null &&
          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
              _dataGetListCatalogTypeProduct!.key == "HC" ||
              _dataGetListCatalogTypeProduct!.key == "TP")) {
        if (_dataGetListCatalogProductGr == null) {
          _validateMaterialGroup = true;
        } else {
          _validateMaterialGroup = false;
        }
      } else {
        _validateMaterialGroup = false;
      }
      if (_dataGetListCatalogTypeProduct != null &&
          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
              _dataGetListCatalogTypeProduct!.key == "HC" ||
              _dataGetListCatalogTypeProduct!.key == "DA" ||
              _dataGetListCatalogTypeProduct!.key == "TP")) {
        if (_dataGetListCatalogProductStatus == null) {
          _validateMaterialStatus = true;
        } else {
          _validateMaterialStatus = false;
        }
      } else {
        _validateMaterialStatus = false;
      }
      if (_dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC") {
        if (_dataGetListCatalogNSXHSD == null) {
          _validateNsxhsd = true;
        } else {
          _validateNsxhsd = false;
        }
      } else {
        _validateNsxhsd = false;
      }
      if (_dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA") {
        if (_skinTypesController.text.isEmpty) {
          _validateSkinType = true;
        } else {
          _validateSkinType = false;
        }
      } else {
        _validateSkinType = false;
      }
      if (_dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA") {
        if (_skinColorController.text.isEmpty) {
          _validatecSkinColor = true;
        } else {
          _validatecSkinColor = false;
        }
      } else {
        _validatecSkinColor = false;
      }

      if (_dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN") {
        if (_numberCodeController.text.isEmpty) {
          _validatecCaseCode = true;
        } else {
          _validatecCaseCode = false;
        }
      } else {
        _validatecCaseCode = false;
      }
    });
  }

  void _setTab() {
    setState(() {
      _viTab = !_viTab;
      _controllerNumberLot.text = "";
      _dataGetListCatalogTypeProduct = null;
      _dataGetListCatalogProductOrigin = null;
      _dataGetListCatalogProductGr = null;
      _dataGetListCatalogProductStatus = null;
      _numberCodeController.text = "";
      _dataGetListCatalogNSXHSD = null;
      _skinTypesController.text = "";
      // _selectedGoodReceivedNotes = null;
      _selectedSloc = null;
      _storageBinController.text = "";
      _storageBinID = null;
    });
  }

  List<String> _lsSoToKhaiMasterData = [];
  bool isSelectedSoToKhai = false;

  Future<List<String>> filterSoToKhai(String query) async {
    if (query.isEmpty || query.length < 3) {
      setState(() {
        _lsSoToKhaiMasterData = [];
      });
      return [];
    }
    // Call api to get top 10 SoToKhai
    // _lsSoToKhaiMasterData = [
    //   "123456",
    //   "123457",
    //   "133458",
    //   "143459",
    //   "153460",
    //   "163461",
    //   "173462",
    //   "183463",
    //   "193464",
    //   "1103465",
    // ];

    // Add API request to get master data
    // Replace 'API_ENDPOINT' with the actual API endpoint
    // Replace 'API_KEY' with the actual API key
    // Replace 'TOP_10' with the desired number of top results
    final response = await ImportWareHouseFunction.fetchSoToKhai(_SoToKhaiController.text, widget.token);
    setState(() {
      _lsSoToKhaiMasterData = response!;
    });

    // var soTiKhaiListFound = _lsSoToKhaiMasterData?.where((element) => element.contains(query)).toList();
    return _lsSoToKhaiMasterData;
  }

  Future<void> _setListController() async {
    try {
      String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
      DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
      DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
      if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
        _timeOut = true;
      } else {
        setState(() {
          _timeOut = false;
          _isLoading = true;
          _notWifi = false;
        });
        // await _getDataRawMaterial();
        final data = await Future.wait([
          ImportWareHouseFunction.fetchRawMaterial(widget.barcode, widget.token, widget.fromPage),
          ImportWareHouseFunction.fetchSlocAddress(widget.plant, widget.token),
          ImportWareHouseFunction.fetchGetListCatalogTypeCodeApi(widget.token)
        ]);
        final dataDataGetListCatalog = await Future.wait([
          ImportWareHouseFunction.fetchGetListCatalog(
              ((data[2] as List<DataGetListCatalogTypeCode>?) ?? []).firstWhereOrNull((element) => element.key == "ProductType") == null
                  ? ""
                  : ((data[2] as List<DataGetListCatalogTypeCode>?) ?? [])
                      .firstWhereOrNull((element) => element.key == "ProductType")!
                      .key
                      .toString(),
              widget.token),
          ImportWareHouseFunction.fetchGetListCatalog(
              ((data[2] as List<DataGetListCatalogTypeCode>?) ?? []).firstWhereOrNull((element) => element.key == "ProductOrigin") == null
                  ? ""
                  : ((data[2] as List<DataGetListCatalogTypeCode>?) ?? [])
                      .firstWhereOrNull((element) => element.key == "ProductOrigin")!
                      .key
                      .toString(),
              widget.token),
          ImportWareHouseFunction.fetchGetListCatalog(
              ((data[2] as List<DataGetListCatalogTypeCode>?) ?? []).firstWhereOrNull((element) => element.key == "ProductGr") == null
                  ? ""
                  : ((data[2] as List<DataGetListCatalogTypeCode>?) ?? []).firstWhereOrNull((element) => element.key == "ProductGr")!.key.toString(),
              widget.token),
          ImportWareHouseFunction.fetchGetListCatalog(
              ((data[2] as List<DataGetListCatalogTypeCode>?) ?? []).firstWhereOrNull((element) => element.key == "ProductStatus") == null
                  ? ""
                  : ((data[2] as List<DataGetListCatalogTypeCode>?) ?? [])
                      .firstWhereOrNull((element) => element.key == "ProductStatus")!
                      .key
                      .toString(),
              widget.token)
        ]);

        if (!mounted) return;

        // Get first PO

        setState(() {
          _isLoading = false;
          if (data.isNotEmpty) {
            _dataRawMeterial = (data[0] as DataRawMeterial?) != null ? _dataRawMeterial = (data[0] as DataRawMeterial?) : null;
            _listDataSlocAddress = (data[1] as List<DataSlocAddress>?) ?? <DataSlocAddress>[];
            _lsProductType = dataDataGetListCatalog[0];
            _lsProductOrigin = dataDataGetListCatalog[1];
            _lsProductGrp = dataDataGetListCatalog[2];
            _lsProductStatus = dataDataGetListCatalog[3];
            if (_dataRawMeterial != null) {
              _poDetailResponses = _dataRawMeterial!.poDetailResponses;

              if (_dataRawMeterial?.latestTDS != null) {
                _TDSController.text = _dataRawMeterial!.latestTDS.toString();
              }
            }
            poNhapKhau42 = _dataRawMeterial != null ? _dataRawMeterial!.poDetailResponses?.any((element) => element.po!.startsWith("42")) : false;

            if (poNhapKhau42 == true) {
              _dataGetListCatalogProductOrigin = _lsProductOrigin.firstWhereOrNull((element) => element.key == "I");
            } else {
              _dataGetListCatalogProductOrigin = _lsProductOrigin.firstWhereOrNull((element) => element.key == "D");
            }

            debugPrint("PO nhap khau: " + poNhapKhau42.toString());
          }
          if (_listDataSlocAddress != null && _listDataSlocAddress!.isNotEmpty) {
            _getLsDataSlocAddress = _listDataSlocAddress!.map((e) => DataSlocAddress.clone(e)).toList();
            _getLsDataSlocAddress.sort((a, b) {
              return a.sloc!.toLowerCase().compareTo(b.sloc!.toLowerCase());
            });
          } else {
            _getLsDataSlocAddress = [];
          }
          if (_dataRawMeterial != null) {
            if (_dataRawMeterial!.isReceive == 0) {
              if (_poDetailResponses != null && _poDetailResponses!.isNotEmpty) {
                for (int i = 0; i < _poDetailResponses!.length; i++) {
                  _lsControllers.add(TextEditingController());
                  _lsFocusNode.add(FocusNode());
                  _lsControllers[i].text = _poDetailResponses![i].quantity == null ? "0.0" : _poDetailResponses![i].quantity!.toStringAsFixed(3);
                  // _errorFormat.add(false);
                }

                _sumAmountEntered = ImportWareHouseFunction.amount(_lsControllers);
              }
            } else {
              if (_poDetailResponses!.isNotEmpty) {
                _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element.sloc == _dataRawMeterial!.inforReceived!.sloc);
                _controllerNumberLot.text = _dataRawMeterial!.inforReceived!.batchNumber ?? " ";
                _storageBinController.text = _dataRawMeterial!.inforReceived!.storageBin ?? "";
                _disableDropdownSloc = true;
                for (int i = 0; i < _poDetailResponses!.length; i++) {
                  _lsControllers.add(TextEditingController());
                  _lsFocusNode.add(FocusNode());
                  _lsControllers[i].text =
                      _poDetailResponses![i].quantityImported != null ? _poDetailResponses![i].quantityImported!.toStringAsFixed(3) : "";
                }
                _sumAmountEntered = _dataRawMeterial!.sumQuantityImported ?? 0.0;
              }
            }
          }
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _notWifi = true;
        _isLoading = false;
        _timeOut = false;
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _timeOut = false;
        _error = error.toString();
      });
    }
  }

  void _checkError() {
    setState(() {
      // if(_selectedGoodReceivedNotes == null){
      //   _errorSelectedGoodReceivedNotes = true;
      // }else{
      //   _errorSelectedGoodReceivedNotes = false;
      // }
      if (_selectedSloc == null) {
        _errorSelectedSloc = true;
      } else {
        _errorSelectedSloc = false;
        if (_selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo != "") {
          if (_storageBinController.text.isEmpty) {
            _errorStorageBin = true;
          } else {
            _errorStorageBin = false;
          }
        } else {
          _errorStorageBin = false;
        }
      }
      if (_lsControllers.where((element) => element.text.isEmpty).isNotEmpty) {
        _errorController = true;
      } else {
        _errorController = false;
      }

      if (_controllerNumberLot.text.isEmpty) {
        _errorBatch = true;
      } else {
        _errorBatch = false;
      }
    });
  }

  Future<void> _luuPressAndroid(BuildContext context) async {
    double amount = ImportWareHouseFunction.amount(_lsControllers);
    FocusScope.of(context).unfocus();

    bool shouldSend = true;

    if (amount != _dataRawMeterial!.quantity!) {
      bool? check = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) =>
              const DialogExceedTheAmountAndroid(message: 'Số lượng thực nhập khác Số lượng giao hàng, bạn có muốn tiếp tục?'));
      if (!mounted) return;

      if (check == null || check == false) {
        shouldSend = false;
      }
    }

    if (_dataGetListCatalogProductOrigin?.key == "I") {
      if (_SoToKhaiController.text.isNotEmpty && isSelectedSoToKhai == false) {
        showAlert(
          context: context,
          title: 'Thông báo',
          content: 'Vui lòng nhập số tờ khai và chọn trong danh sách',
          buttonsWithOnPressed: {
            'Ok': () => Navigator.of(context).pop(),
          },
        );
        shouldSend = false;
      }
    }

    if (shouldSend) {
      ImportWareHouseFunction.sendImportWareHouse(
        _poDetailResponses,
        _lsControllers,
        _dataRawMeterial,
        widget.barcode,
        _selectedSloc,
        _controllerNumberLot.text,
        widget.token,
        _storageBinController.text.isNotEmpty ? _storageBinID : null,
        AutoBatch(
          materialType: _dataGetListCatalogTypeProduct?.key,
          importExportType: _dataGetListCatalogProductOrigin?.key,
          materialGroup: _dataGetListCatalogProductGr?.key,
          materialStatus: _dataGetListCatalogProductStatus?.key,
          companyCode: _dataGetInfoReceiSap?.companyCode,
          nsxhsd: _dataGetListCatalogNSXHSD == null ? null : int.parse(_dataGetListCatalogNSXHSD!.key!),
          skinType: _skinTypesController.text.isEmpty ? null : _skinTypesController.text,
          skinColor: _skinColorController.text.isEmpty ? null : _skinColorController.text,
          caseCode: _numberCodeController.text.isEmpty ? null : _numberCodeController.text,
        ),
        _viTab,
        _TDSController.text.isEmpty ? "" : _TDSController.text,
        _SoToKhaiController.text.isEmpty ? "" : _SoToKhaiController.text,
        context,
      );
    }
  }

  // Submit, save, luu
  Future<void> _luuPressiOS(BuildContext context) async {
    double amount = ImportWareHouseFunction.amount(_lsControllers);
    FocusScope.of(context).unfocus();

    bool shouldSend = true;

    if (amount != _dataRawMeterial!.quantity!) {
      bool? check = await showCupertinoDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) => const DialogExceedTheAmountIos(
              title: 'SL thực nhập khác SL giao hàng', message: 'Số lượng thực nhập khác Số lượng giao hàng, bạn có muốn tiếp tục?'));
      if (!mounted) return;

      if (check == null || check == false) {
        shouldSend = false;
      }
    }

    if (_dataGetListCatalogProductOrigin?.key == "I") {
      if (isSelectedSoToKhai == false) {
        showAlert(
          context: context,
          title: 'Thông báo',
          content: 'Vui lòng nhập số tờ khai',
          buttonsWithOnPressed: {
            'Ok': () => Navigator.of(context).pop(),
          },
        );
        shouldSend = false;
      }
    }

    if (shouldSend) {
      ImportWareHouseFunction.sendImportWareHouse(
        _poDetailResponses,
        _lsControllers,
        _dataRawMeterial,
        widget.barcode,
        _selectedSloc,
        _controllerNumberLot.text,
        widget.token,
        _storageBinController.text.isNotEmpty ? _storageBinID : null,
        AutoBatch(
          materialType: _dataGetListCatalogTypeProduct?.key,
          importExportType: _dataGetListCatalogProductOrigin?.key,
          materialGroup: _dataGetListCatalogProductGr?.key,
          materialStatus: _dataGetListCatalogProductStatus?.key,
          companyCode: _dataGetInfoReceiSap?.companyCode,
          nsxhsd: _dataGetListCatalogNSXHSD == null ? null : int.parse(_dataGetListCatalogNSXHSD!.key!),
          skinType: _skinTypesController.text.isEmpty ? null : _skinTypesController.text,
          skinColor: _skinColorController.text.isEmpty ? null : _skinColorController.text,
          caseCode: _numberCodeController.text.isEmpty ? null : _numberCodeController.text,
        ),
        _viTab,
        _TDSController.text.isEmpty ? "" : _TDSController.text,
        _SoToKhaiController.text.isEmpty ? "" : _SoToKhaiController.text,
        context,
      );
    }
  }

  void _setAddressQRCode(DataSlocAddress? data, BuildContext context) {
    try {
      if (!mounted) return;
      setState(() {
        _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element.sloc == data!.sloc)].defaultStorageBin = data!.defaultStorageBin;
        _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element.sloc == data.sloc)].sloc = data.sloc;
        _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element.sloc == data.sloc)].warehouseNo = data.warehouseNo;
        _getLsDataSlocAddress[_getLsDataSlocAddress.indexWhere((element) => element.sloc == data.sloc)].defaultStorageBinId =
            data.defaultStorageBinId;
        _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element.sloc == data.sloc);
        if (_selectedSloc != null) {
          _errorSelectedSloc = false;
          _disableDropdownSloc = true;
          if (_selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo != "") {
            if (_storageBinController.text.isEmpty) {
              _errorStorageBin = true;
            } else {
              _errorStorageBin = false;
            }
          } else {
            _errorStorageBin = false;
          }
        } else {
          _errorSelectedSloc = true;
          _disableDropdownSloc = false;
        }
      });
    } catch (error) {
      debugPrint(error.toString());
      if (!mounted) return;
      setState(() {
        _selectedSloc = null;
      });
    }
  }

  void _resetImport(BuildContext context) {
    _controllerNumberLot.clear();
    _storageBinController.clear();
    setState(() {
      _selectedSloc = null;
      _storageBinID = null;
      _sumAmountEntered = 0.0;
      _disableDropdownSloc = false;
      for (int i = 0; i < _lsControllers.length; i++) {
        _lsControllers[i].text = "";
      }
      _errorStorageBin = false;
      _errorController = false;
      _errorSelectedSloc = false;
      _dataGetListCatalogTypeProduct = null;
      _dataGetListCatalogProductOrigin = null;
      _dataGetListCatalogProductGr = null;
      _dataGetListCatalogProductStatus = null;
      _numberCodeController.text = "";
      _dataGetListCatalogNSXHSD = null;
      _skinTypesController.text = "";
      _skinColorController.text = "";
      // _selectedGoodReceivedNotes = null;
      _isLoadingAutoBatch = false;
      _validateProductType = false;
      _validateImportExportType = false;
      _validateMaterialGroup = false;
      _validateMaterialStatus = false;
      _validateNsxhsd = false;
      _validateSkinType = false;
      _validatecSkinColor = false;
      _validatecCaseCode = false;
      // _errorSelectedGoodReceivedNotes = false;
    });
    if (_focusNumberLot.hasFocus || _lsFocusNode.where((element) => element.hasFocus).isNotEmpty || _focusStorageBin.hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  void _setButton() {
    if (!mounted) return;
    setState(() {
      _disableButton = true;
    });
  }

  Future<void> _onStorageBinChanged(String value) async {
    setState(() {
      if (value.isEmpty) {
        if (_errorStorageBin != true) {
          _errorStorageBin = true;
        }
      } else {
        if (_errorStorageBin != false) {
          _errorStorageBin = false;
        }
      }
    });
    if (!mounted) return;
    _timer?.cancel();
    if (_errorStorageBin == false) {
      _getAutocompleteStorageBin(value);
    }
  }

  Future<void> _getAutocompleteStorageBin(String value) async {
    try {
      const duration = Duration(seconds: 1);
      if (!mounted) return;
      setState(() => _timer = Timer(duration, () async {
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = true;
            });
            final data = await ImportWareHouseFunction.fetchStorageBinByParam(value, widget.token);
            if (!mounted) return;
            setState(() {
              _isLoadingStorageBin = false;
              if (data != null) {
                _lsDataGetStorageBn = data;
              }
            });
          }));
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  void _setFilter(DataGetStorageBin dataGetStorageBin) {
    setState(() {
      _storageBinController.text = dataGetStorageBin.value ?? " ";
      _storageBinID = dataGetStorageBin.key ?? "";
      if (_selectedSloc!.warehouseNo != null && _selectedSloc!.warehouseNo != "") {
        if (_storageBinController.text.isEmpty) {
          _errorStorageBin = true;
        } else {
          _errorStorageBin = false;
        }
      } else {
        _errorStorageBin = false;
      }
      _lsDataGetStorageBn = [];
    });
  }

  void _setDropdown(DataSlocAddress? value) {
    setState(() {
      _selectedSloc = value!;
      _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
      _storageBinID = _selectedSloc!.defaultStorageBinId;
      if (_selectedSloc == null) {
        _errorSelectedSloc = true;
      } else {
        _errorSelectedSloc = false;
      }
    });
  }

  void _setOnChangeTextField() {
    setState(() {
      _sumAmountEntered = ImportWareHouseFunction.amount(_lsControllers);
      if (_lsControllers.where((element) => element.text.isEmpty).isNotEmpty) {
        if (_errorController != true) {
          _errorController = true;
        }
      } else {
        if (_errorController != false) {
          _errorController = false;
        }
      }
    });
  }

  void _setOnChangeTextFieldNumberLot() {
    if (_controllerNumberLot.text.isEmpty) {
      if (_errorBatch != true) {
        setState(() {
          _errorBatch = true;
        });
      }
    } else {
      if (_errorBatch != false) {
        setState(() {
          _errorBatch = false;
        });
      }
    }
  }

  void _updatingStatus() {
    setState(() {
      _isLoadingUpdateStatus = true;
    });
  }

  void _updateStatusDone() {
    setState(() {
      _isLoadingUpdateStatus = false;
    });
  }

  void _setNSXHSD(DataGetListCatalog? dataGetListCatalog) {
    setState(() {
      _dataGetListCatalogNSXHSD = dataGetListCatalog;
    });
  }

  Future<void> _postAutoBatch(BuildContext context, GetAutoBatch getAutoBatch) async {
    try {
      setState(() {
        _isLoadingAutoBatch = true;
      });
      final batch = await ImportWareHouseFunction.fetchAutoBatch(getAutoBatch, widget.token);
      if (!mounted) return;
      if (batch != null) {
        setState(() {
          _isLoadingAutoBatch = false;
          _controllerNumberLot.text = batch.batchNumber ?? "";
          if (_controllerNumberLot.text.isEmpty) {
            if (_errorBatch != true) {
              _errorBatch = true;
            }
          } else {
            if (_errorBatch != false) {
              _errorBatch = false;
            }
          }
        });
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoadingAutoBatch = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoadingAutoBatch = false;
      });
      debugPrint(error.toString());
      showDialog(context: context, barrierDismissible: false, builder: (BuildContext context) => DialogError(message: error.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      //     backgroundColor: Colors.black,
      //     content: Text(
      //       error.toString(),
      //       style: TextStyle(fontSize: 15.sp, color: Colors.white),
      //     ),
      //     duration: const Duration(seconds: 1)));
    }
  }

  void _setSelectedTypeProduct(DataGetListCatalog? value) {
    setState(() {
      _dataGetListCatalogTypeProduct = value!;

      // _dataGetListCatalogProductOrigin = null;

      _dataGetListCatalogProductGr = null;
      _dataGetListCatalogProductStatus = null;
      _dataGetListCatalogNSXHSD = null;
      _skinTypesController.text = "";
      _numberCodeController.text = "";
      _skinColorController.text = "";
    });
  }

  void _setSelectedProductOrigin(DataGetListCatalog? value) {
    setState(() {
      _dataGetListCatalogProductOrigin = value!;
    });
  }

  void _setSelectedProductGrp(DataGetListCatalog? value) {
    setState(() {
      _dataGetListCatalogProductGr = value!;
    });
  }

  void _setSelectedProductStatus(DataGetListCatalog? value) {
    setState(() {
      _dataGetListCatalogProductStatus = value!;
    });
  }
  // void _setSelectedGoodReceivedNotes(SelectedGoodReceivedNotes selectedGoodReceivedNotes)  {
  //     setState(() {
  //       if(selectedGoodReceivedNotes.value != null) {
  //         _selectedGoodReceivedNotes = selectedGoodReceivedNotes.value!;
  //       }
  //       // if(_selectedGoodReceivedNotes == null){
  //       //   _errorSelectedGoodReceivedNotes = true;
  //       // }else{
  //       //   _errorSelectedGoodReceivedNotes = false;
  //       // }
  //       if(selectedGoodReceivedNotes.dataGetInfoReceiSap != null) {
  //         _dataGetInfoReceiSap = selectedGoodReceivedNotes.dataGetInfoReceiSap;
  //         _dataGetListCatalogTypeProduct = _lsProductType.firstWhereOrNull((element) => element.key == selectedGoodReceivedNotes.dataGetInfoReceiSap!.materialType);
  //         _dataGetListCatalogProductOrigin = _lsProductOrigin.firstWhereOrNull((element) => element.key == selectedGoodReceivedNotes.dataGetInfoReceiSap!.importExportType);
  //         _dataGetListCatalogProductGr = _lsProductGrp.firstWhereOrNull((element) => element.key == selectedGoodReceivedNotes.dataGetInfoReceiSap!.materialGroup);
  //         _dataGetListCatalogProductStatus = _lsProductStatus.firstWhereIndexedOrNull((index, element) => element.key == selectedGoodReceivedNotes.dataGetInfoReceiSap!.materialStatus);
  //         _dataGetListCatalogNSXHSD = _lsNSXHSD.firstWhereIndexedOrNull((index, element) => element.key == selectedGoodReceivedNotes.dataGetInfoReceiSap!.nsxhsd);
  //         _skinColorController.text = selectedGoodReceivedNotes.dataGetInfoReceiSap!.skinColor ?? "";
  //         _skinTypesController.text = selectedGoodReceivedNotes.dataGetInfoReceiSap!.skinType ?? "";
  //         _numberCodeController.text = selectedGoodReceivedNotes.dataGetInfoReceiSap!.caseCode ?? "";
  //         _selectedSloc = _getLsDataSlocAddress.firstWhereOrNull((element) => element.slocId == selectedGoodReceivedNotes.dataGetInfoReceiSap!.slocId);
  //         _storageBinController.text = _selectedSloc!.defaultStorageBin ?? "";
  //         _storageBinID = _selectedSloc!.defaultStorageBinId;
  //         if (_selectedSloc == null) {
  //           _errorSelectedSloc = true;
  //         } else {
  //           _errorSelectedSloc = false;
  //         }
  //       }
  //       });
  // }

  @override
  void dispose() {
    _controllerNumberLot.dispose();
    _focusNumberLot.dispose();
    _storageBinController.dispose();
    _focusStorageBin.dispose();
    _skinTypesController.dispose();
    _skinColorController.dispose();
    _numberCodeController.dispose();
    _focusSkinTypes.dispose();
    _focusSkinColor.dispose();
    _focusNumberCode.dispose();
    for (var i in _lsControllers) {
      i.dispose();
    }
    for (var i in _lsFocusNode) {
      i.dispose();
    }
    if (_timer != null) {
      _timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _timeOut == true
        ? WillPopScope(
            onWillPop: () => Future.value(false),
            child: Scaffold(backgroundColor: Colors.grey.shade200, body: TimeOutView(setButton: _setButton, disableButton: _disableButton ?? false)))
        : GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: WillPopScope(
                onWillPop: () async {
                  Navigator.pop(context, false);
                  return false;
                },
                child: Scaffold(
                  backgroundColor: Colors.grey.shade200,
                  appBar: AppBar(
                    titleSpacing: 0,
                    automaticallyImplyLeading: false,
                    backgroundColor: const Color(0xff0052cc),
                    elevation: 0,
                    centerTitle: true,
                    leading: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
                      onPressed: () {
                        Navigator.pop(context, false);
                      },
                    ),
                    title: Text(
                      'Nhập Kho NVL',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
                    ),
                  ),
                  body: _notWifi == true
                      ? LostConnect(checkConnect: () => _setListController())
                      : _error != ""
                          ? ErrorViewPost(error: _error)
                          : _isLoading == true
                              ? const Center(child: CircularProgressIndicator())
                              : _dataRawMeterial == null
                                  ? const _NotFoundView()
                                  : SingleChildScrollView(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          // _HeaderImporWareHouse(dataRawMeterial: _dataRawMeterial),
                                          SizedBox(height: 5.h),
                                          // _DropdownGoodsReceivedNotes(
                                          //   disableDropdownSloc: _disableDropdownSloc,
                                          //   // errorSelectedGoodReceivedNotes: _errorSelectedGoodReceivedNotes,
                                          //   lsGoodsReceivedNotes: _dataRawMeterial!.goodsReceivedNotes ?? [],
                                          //   selectedGoodReceivedNotes: _selectedGoodReceivedNotes,
                                          //   setDropdownGoodsReceivedNotes: _setSelectedGoodReceivedNotes,
                                          //   token: widget.token,
                                          //   checkMounted: mounted
                                          // ),
                                          // SizedBox(height: 5.h),
                                          Visibility(
                                              visible: _dataRawMeterial!.isReceive == 0,
                                              child: Row(
                                                children: <Widget>[
                                                  Expanded(
                                                    flex: 5,
                                                    child: GestureDetector(
                                                      onTap: _viTab == false
                                                          ? () {
                                                              _setTab();
                                                              // FocusScope.of(context).unfocus();
                                                            }
                                                          : null,
                                                      child: _ChangeView(
                                                          text: "Số lô tự động", colorContainer: _viTab == true ? 0xff0052cc : 0xffffffff),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    flex: 5,
                                                    child: GestureDetector(
                                                      onTap: _viTab == true
                                                          ? () {
                                                              _setTab();
                                                              // FocusScope.of(context).unfocus();
                                                            }
                                                          : null,
                                                      child: _ChangeView(
                                                          text: "Số lô nhập tay", colorContainer: _viTab == false ? 0xff0052cc : 0xffffffff),
                                                    ),
                                                  ),
                                                ],
                                              )),
                                          SizedBox(height: 5.h),
                                          Visibility(
                                            visible: _dataRawMeterial!.isReceive == 0 && _viTab,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(horizontal: 10.w),
                                              decoration: const BoxDecoration(color: Colors.white),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                children: <Widget>[
                                                  SizedBox(height: 10.h),
                                                  const _TitleTypeNVL(title: "Loại NVL"),
                                                  SizedBox(height: 5.h),
                                                  DropdownCatalog(
                                                    onchange: _setSelectedTypeProduct,
                                                    lsCatalog: _lsProductType,
                                                    dataGetListCatalog: _dataGetListCatalogTypeProduct,
                                                  ),
                                                  // Container(
                                                  //   decoration: BoxDecoration(
                                                  //     border: Border.all(
                                                  //       width: 0.5.w,
                                                  //       color: Colors
                                                  //           .grey.shade400,
                                                  //     ),
                                                  //   ),
                                                  //   child: TypeAheadField(
                                                  //     suggestionsBoxDecoration:
                                                  //         SuggestionsBoxDecoration(
                                                  //       constraints:
                                                  //           BoxConstraints(
                                                  //         minWidth: 100.w,
                                                  //       ),
                                                  //     ),
                                                  //     textFieldConfiguration:
                                                  //         TextFieldConfiguration(
                                                  //             enabled: true,
                                                  //             decoration:
                                                  //                 InputDecoration(
                                                  //               labelStyle:
                                                  //                   TextStyle(
                                                  //                       fontSize:
                                                  //                           11.sp),
                                                  //               contentPadding:
                                                  //                   EdgeInsets.symmetric(
                                                  //                       vertical:
                                                  //                           5.h,
                                                  //                       horizontal:
                                                  //                           5.w),
                                                  //               isDense: true,
                                                  //               border:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //               focusedBorder:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //               enabledBorder:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //             ),
                                                  //             controller:
                                                  //                 _typeNVLController,
                                                  //             focusNode:
                                                  //                 _focusTypeNVL,
                                                  //             style: TextStyle(
                                                  //                 fontSize:
                                                  //                     12.sp),
                                                  //             onChanged:
                                                  //                 (value) {
                                                  //               if (_dataGetListCatalogTypeProduct != null ||
                                                  //                   _hinterlandController
                                                  //                       .text
                                                  //                       .isNotEmpty ||
                                                  //                   _dataGetListCatalogProductOrigin !=
                                                  //                       null ||
                                                  //                   _groupCommoditiesController
                                                  //                       .text
                                                  //                       .isNotEmpty ||
                                                  //                   _dataGetListCatalogProductGr !=
                                                  //                       null ||
                                                  //                   _statusCommoditiesController
                                                  //                       .text
                                                  //                       .isNotEmpty ||
                                                  //                   _dataGetListCatalogProductStatus !=
                                                  //                       null ||
                                                  //                   _numberCodeController
                                                  //                       .text
                                                  //                       .isNotEmpty ||
                                                  //                   _dataGetListCatalogNSXHSD !=
                                                  //                       null ||
                                                  //                   _skinTypesController
                                                  //                       .text
                                                  //                       .isNotEmpty) {
                                                  //                 setState(() {
                                                  //                   _dataGetListCatalogTypeProduct =
                                                  //                       null;
                                                  //                   _hinterlandController
                                                  //                       .text = "";
                                                  //                   _dataGetListCatalogProductOrigin =
                                                  //                       null;
                                                  //                   _groupCommoditiesController
                                                  //                       .text = "";
                                                  //                   _dataGetListCatalogProductGr =
                                                  //                       null;
                                                  //                   _statusCommoditiesController
                                                  //                       .text = "";
                                                  //                   _dataGetListCatalogProductStatus =
                                                  //                       null;
                                                  //                   _numberCodeController
                                                  //                       .text = "";
                                                  //                   _dataGetListCatalogNSXHSD =
                                                  //                       null;
                                                  //                   _skinTypesController
                                                  //                       .text = "";
                                                  //                 });
                                                  //               }
                                                  //             }),
                                                  //     suggestionsCallback:
                                                  //         (pattern) async {
                                                  //       return await ImportWareHouseFunction.fetchGetListCatalog(
                                                  //           pattern,
                                                  //           _isCheckTest,
                                                  //           _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) =>
                                                  //                       element
                                                  //                           .key ==
                                                  //                       "ProductType") ==
                                                  //                   null
                                                  //               ? ""
                                                  //               : _lsDataGetListCatalogTypeCode
                                                  //                   .firstWhereOrNull((element) =>
                                                  //                       element
                                                  //                           .key ==
                                                  //                       "ProductType")!
                                                  //                   .key
                                                  //                   .toString(),
                                                  //           widget.token);
                                                  //     },
                                                  //     itemBuilder: (context,
                                                  //         suggestion) {
                                                  //       return ListTile(
                                                  //         title: Text(
                                                  //             (suggestion as DataGetListCatalog)
                                                  //                     .value ??
                                                  //                 "",
                                                  //             style: TextStyle(
                                                  //                 fontSize:
                                                  //                     12.sp)),
                                                  //       );
                                                  //     },
                                                  //     onSuggestionSelected:
                                                  //         (suggestion) {
                                                  //       setState(() {
                                                  //         _typeNVLController
                                                  //             .text = (suggestion
                                                  //                     as DataGetListCatalog)
                                                  //                 .value ??
                                                  //             "";
                                                  //         _dataGetListCatalogTypeProduct =
                                                  //             suggestion;
                                                  //         _hinterlandController
                                                  //             .text = "";
                                                  //         _dataGetListCatalogProductOrigin =
                                                  //             null;
                                                  //         _groupCommoditiesController
                                                  //             .text = "";
                                                  //         _dataGetListCatalogProductGr =
                                                  //             null;
                                                  //         _statusCommoditiesController
                                                  //             .text = "";
                                                  //         _dataGetListCatalogProductStatus =
                                                  //             null;
                                                  //         _numberCodeController
                                                  //             .text = "";
                                                  //         _dataGetListCatalogNSXHSD =
                                                  //             null;
                                                  //         _skinTypesController
                                                  //             .text = "";
                                                  //       });
                                                  //     },
                                                  //     noItemsFoundBuilder:
                                                  //         (value) {
                                                  //       return Padding(
                                                  //           padding: EdgeInsets
                                                  //               .symmetric(
                                                  //                   vertical:
                                                  //                       10.h,
                                                  //                   horizontal:
                                                  //                       5.w),
                                                  //           child: Text(
                                                  //               "Không tìm thấy kết quả",
                                                  //               style: TextStyle(
                                                  //                   fontSize: 11
                                                  //                       .sp)));
                                                  //     },
                                                  //   ),
                                                  // ),
                                                  SizedBox(height: 10.h),
                                                  const _TitleTypeNVL(title: "NVL nội địa/nhập khẩu"),
                                                  SizedBox(height: 5.h),
                                                  DropdownCatalog(
                                                    onchange: _setSelectedProductOrigin,
                                                    lsCatalog: _lsProductOrigin,
                                                    dataGetListCatalog: _dataGetListCatalogProductOrigin,
                                                  ),
                                                  // Container(
                                                  //   decoration: BoxDecoration(
                                                  //     border: Border.all(
                                                  //       width: 0.5.w,
                                                  //       color: Colors
                                                  //           .grey.shade400,
                                                  //     ),
                                                  //   ),
                                                  //   child: TypeAheadField(
                                                  //     suggestionsBoxDecoration:
                                                  //         SuggestionsBoxDecoration(
                                                  //       constraints:
                                                  //           BoxConstraints(
                                                  //         minWidth: 150.w,
                                                  //       ),
                                                  //     ),
                                                  //     textFieldConfiguration:
                                                  //         TextFieldConfiguration(
                                                  //             enabled: true,
                                                  //             decoration:
                                                  //                 InputDecoration(
                                                  //               labelStyle:
                                                  //                   TextStyle(
                                                  //                       fontSize:
                                                  //                           11.sp),
                                                  //               contentPadding:
                                                  //                   EdgeInsets.symmetric(
                                                  //                       vertical:
                                                  //                           5.h,
                                                  //                       horizontal:
                                                  //                           5.w),
                                                  //               isDense: true,
                                                  //               border:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //               focusedBorder:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //               enabledBorder:
                                                  //                   InputBorder
                                                  //                       .none,
                                                  //             ),
                                                  //             controller:
                                                  //                 _hinterlandController,
                                                  //             focusNode:
                                                  //                 _focusHinterland,
                                                  //             style: TextStyle(
                                                  //                 fontSize:
                                                  //                     12.sp),
                                                  //             onChanged:
                                                  //                 (value) {
                                                  //               if (_dataGetListCatalogProductOrigin !=
                                                  //                   null) {
                                                  //                 setState(() {
                                                  //                   _dataGetListCatalogProductOrigin =
                                                  //                       null;
                                                  //                 });
                                                  //               }
                                                  //             }),
                                                  //     suggestionsCallback:
                                                  //         (pattern) async {
                                                  //       return await ImportWareHouseFunction.fetchGetListCatalog(
                                                  //           pattern,
                                                  //           _isCheckTest,
                                                  //           _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) =>
                                                  //                       element
                                                  //                           .key ==
                                                  //                       "ProductOrigin") ==
                                                  //                   null
                                                  //               ? ""
                                                  //               : _lsDataGetListCatalogTypeCode
                                                  //                   .firstWhereOrNull((element) =>
                                                  //                       element
                                                  //                           .key ==
                                                  //                       "ProductOrigin")!
                                                  //                   .key
                                                  //                   .toString(),
                                                  //           widget.token);
                                                  //     },
                                                  //     itemBuilder: (context,
                                                  //         suggestion) {
                                                  //       return ListTile(
                                                  //         title: Text(
                                                  //             (suggestion as DataGetListCatalog)
                                                  //                     .value ??
                                                  //                 "",
                                                  //             style: TextStyle(
                                                  //                 fontSize:
                                                  //                     12.sp)),
                                                  //       );
                                                  //     },
                                                  //     onSuggestionSelected:
                                                  //         (suggestion) {
                                                  //       setState(() {
                                                  //         _hinterlandController
                                                  //             .text = (suggestion
                                                  //                     as DataGetListCatalog)
                                                  //                 .value ??
                                                  //             "";
                                                  //         _dataGetListCatalogProductOrigin =
                                                  //             suggestion;
                                                  //       });
                                                  //     },
                                                  //     noItemsFoundBuilder:
                                                  //         (value) {
                                                  //       return Padding(
                                                  //           padding: EdgeInsets
                                                  //               .symmetric(
                                                  //                   vertical:
                                                  //                       10.h,
                                                  //                   horizontal:
                                                  //                       5.w),
                                                  //           child: Text(
                                                  //               "Không tìm thấy kết quả",
                                                  //               style: TextStyle(
                                                  //                   fontSize: 11
                                                  //                       .sp)));
                                                  //     },
                                                  //   ),
                                                  // ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null &&
                                                          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                              _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                              _dataGetListCatalogTypeProduct!.key == "TP"),
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null &&
                                                        (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                            _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                            _dataGetListCatalogTypeProduct!.key == "TP"),
                                                    child: const _TitleTypeNVL(title: "Nhóm hàng NVL/TP"),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null &&
                                                          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                              _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                              _dataGetListCatalogTypeProduct!.key == "TP"),
                                                      child: SizedBox(height: 5.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null &&
                                                        (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                            _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                            _dataGetListCatalogTypeProduct!.key == "TP"),
                                                    child: DropdownCatalog(
                                                      onchange: _setSelectedProductGrp,
                                                      lsCatalog: _lsProductGrp,
                                                      dataGetListCatalog: _dataGetListCatalogProductGr,
                                                    ),
                                                  ),
                                                  // Visibility(
                                                  //   visible: _dataGetListCatalogTypeProduct !=
                                                  //           null &&
                                                  //       (_dataGetListCatalogTypeProduct!
                                                  //                   .key ==
                                                  //               "NVLKK" ||
                                                  //           _dataGetListCatalogTypeProduct!
                                                  //                   .key ==
                                                  //               "HC" || _dataGetListCatalogTypeProduct!.key ==  "TP"),
                                                  //   child: Container(
                                                  //     decoration: BoxDecoration(
                                                  //       border: Border.all(
                                                  //         width: 0.5.w,
                                                  //         color: Colors
                                                  //             .grey.shade400,
                                                  //       ),
                                                  //     ),
                                                  //     child: TypeAheadField(
                                                  //       suggestionsBoxDecoration:
                                                  //           SuggestionsBoxDecoration(
                                                  //         constraints:
                                                  //             BoxConstraints(
                                                  //           minWidth: 150.w,
                                                  //         ),
                                                  //       ),
                                                  //       textFieldConfiguration:
                                                  //           TextFieldConfiguration(
                                                  //               enabled: true,
                                                  //               decoration:
                                                  //                   InputDecoration(
                                                  //                 labelStyle:
                                                  //                     TextStyle(
                                                  //                         fontSize:
                                                  //                             11.sp),
                                                  //                 contentPadding: EdgeInsets.symmetric(
                                                  //                     vertical:
                                                  //                         5.h,
                                                  //                     horizontal:
                                                  //                         5.w),
                                                  //                 isDense: true,
                                                  //                 border:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //                 focusedBorder:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //                 enabledBorder:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //               ),
                                                  //               controller:
                                                  //                   _groupCommoditiesController,
                                                  //               focusNode:
                                                  //                   _focusGroupCommodities,
                                                  //               style: TextStyle(
                                                  //                   fontSize:
                                                  //                       12.sp),
                                                  //               onChanged:
                                                  //                   (value) {
                                                  //                 if (_dataGetListCatalogProductGr !=
                                                  //                     null) {
                                                  //                   setState(
                                                  //                       () {
                                                  //                     _dataGetListCatalogProductGr =
                                                  //                         null;
                                                  //                   });
                                                  //                 }
                                                  //               }),
                                                  //       suggestionsCallback:
                                                  //           (pattern) async {
                                                  //         return await ImportWareHouseFunction.fetchGetListCatalog(
                                                  //             pattern,
                                                  //             _isCheckTest,
                                                  //             _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) =>
                                                  //                         element
                                                  //                             .key ==
                                                  //                         "ProductGr") ==
                                                  //                     null
                                                  //                 ? ""
                                                  //                 : _lsDataGetListCatalogTypeCode
                                                  //                     .firstWhereOrNull((element) =>
                                                  //                         element
                                                  //                             .key ==
                                                  //                         "ProductGr")!
                                                  //                     .key
                                                  //                     .toString(),
                                                  //             widget.token);
                                                  //       },
                                                  //       itemBuilder: (context,
                                                  //           suggestion) {
                                                  //         return ListTile(
                                                  //           title: Text(
                                                  //               (suggestion as DataGetListCatalog)
                                                  //                       .value ??
                                                  //                   "",
                                                  //               style: TextStyle(
                                                  //                   fontSize:
                                                  //                       12.sp)),
                                                  //         );
                                                  //       },
                                                  //       onSuggestionSelected:
                                                  //           (suggestion) {
                                                  //         setState(() {
                                                  //           _groupCommoditiesController
                                                  //               .text = (suggestion
                                                  //                       as DataGetListCatalog)
                                                  //                   .value ??
                                                  //               "";
                                                  //           _dataGetListCatalogProductGr =
                                                  //               suggestion;
                                                  //         });
                                                  //       },
                                                  //       noItemsFoundBuilder:
                                                  //           (value) {
                                                  //         return Padding(
                                                  //             padding: EdgeInsets
                                                  //                 .symmetric(
                                                  //                     vertical:
                                                  //                         10.h,
                                                  //                     horizontal:
                                                  //                         5.w),
                                                  //             child: Text(
                                                  //                 "Không tìm thấy kết quả",
                                                  //                 style: TextStyle(
                                                  //                     fontSize:
                                                  //                         11.sp)));
                                                  //       },
                                                  //     ),
                                                  //   ),
                                                  // ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null &&
                                                          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                              _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                              _dataGetListCatalogTypeProduct!.key == "DA" ||
                                                              _dataGetListCatalogTypeProduct!.key == "TP"),
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null &&
                                                          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                              _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                              _dataGetListCatalogTypeProduct!.key == "DA" ||
                                                              _dataGetListCatalogTypeProduct!.key == "TP"),
                                                      child: const _TitleTypeNVL(title: "Tình trạng hàng hóa")),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null &&
                                                          (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                              _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                              _dataGetListCatalogTypeProduct!.key == "DA" ||
                                                              _dataGetListCatalogTypeProduct!.key == "TP"),
                                                      child: SizedBox(height: 5.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null &&
                                                        (_dataGetListCatalogTypeProduct!.key == "NVLKK" ||
                                                            _dataGetListCatalogTypeProduct!.key == "HC" ||
                                                            _dataGetListCatalogTypeProduct!.key == "DA" ||
                                                            _dataGetListCatalogTypeProduct!.key == "TP"),
                                                    child: DropdownCatalog(
                                                      onchange: _setSelectedProductStatus,
                                                      lsCatalog: _lsProductStatus,
                                                      dataGetListCatalog: _dataGetListCatalogProductStatus,
                                                    ),
                                                  ),
                                                  // Visibility(
                                                  //   visible: _dataGetListCatalogTypeProduct != null &&
                                                  //       (_dataGetListCatalogTypeProduct!
                                                  //                   .key ==
                                                  //               "NVLKK" ||
                                                  //           _dataGetListCatalogTypeProduct!
                                                  //                   .key ==
                                                  //               "HC" ||
                                                  //           _dataGetListCatalogTypeProduct!
                                                  //                   .key ==
                                                  //               "DA" || _dataGetListCatalogTypeProduct!.key ==  "TP"),
                                                  //   child: Container(
                                                  //     decoration: BoxDecoration(
                                                  //       border: Border.all(
                                                  //         width: 0.5.w,
                                                  //         color: Colors
                                                  //             .grey.shade400,
                                                  //       ),
                                                  //     ),
                                                  //     child: TypeAheadField(
                                                  //       suggestionsBoxDecoration:
                                                  //           SuggestionsBoxDecoration(
                                                  //         constraints:
                                                  //             BoxConstraints(
                                                  //           minWidth: 150.w,
                                                  //         ),
                                                  //       ),
                                                  //       textFieldConfiguration:
                                                  //           TextFieldConfiguration(
                                                  //               enabled: true,
                                                  //               decoration:
                                                  //                   InputDecoration(
                                                  //                 labelStyle:
                                                  //                     TextStyle(
                                                  //                         fontSize:
                                                  //                             11.sp),
                                                  //                 contentPadding: EdgeInsets.symmetric(
                                                  //                     vertical:
                                                  //                         5.h,
                                                  //                     horizontal:
                                                  //                         5.w),
                                                  //                 isDense: true,
                                                  //                 border:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //                 focusedBorder:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //                 enabledBorder:
                                                  //                     InputBorder
                                                  //                         .none,
                                                  //               ),
                                                  //               controller:
                                                  //                   _statusCommoditiesController,
                                                  //               focusNode:
                                                  //                   _focusStatusCommodities,
                                                  //               style: TextStyle(
                                                  //                   fontSize:
                                                  //                       12.sp),
                                                  //               onChanged:
                                                  //                   (value) {
                                                  //                 if (_dataGetListCatalogProductStatus !=
                                                  //                     null) {
                                                  //                   setState(
                                                  //                       () {
                                                  //                     _dataGetListCatalogProductStatus =
                                                  //                         null;
                                                  //                   });
                                                  //                 }
                                                  //               }),
                                                  //       suggestionsCallback:
                                                  //           (pattern) async {
                                                  //         return await ImportWareHouseFunction.fetchGetListCatalog(
                                                  //             pattern,
                                                  //             _isCheckTest,
                                                  //             _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) =>
                                                  //                         element
                                                  //                             .key ==
                                                  //                         "ProductStatus") ==
                                                  //                     null
                                                  //                 ? ""
                                                  //                 : _lsDataGetListCatalogTypeCode
                                                  //                     .firstWhereOrNull((element) =>
                                                  //                         element
                                                  //                             .key ==
                                                  //                         "ProductStatus")!
                                                  //                     .key
                                                  //                     .toString(),
                                                  //             widget.token);
                                                  //       },
                                                  //       itemBuilder: (context,
                                                  //           suggestion) {
                                                  //         return ListTile(
                                                  //           title: Text(
                                                  //               (suggestion as DataGetListCatalog)
                                                  //                       .value ??
                                                  //                   "",
                                                  //               style: TextStyle(
                                                  //                   fontSize:
                                                  //                       12.sp)),
                                                  //         );
                                                  //       },
                                                  //       onSuggestionSelected:
                                                  //           (suggestion) {
                                                  //         setState(() {
                                                  //           _statusCommoditiesController
                                                  //               .text = (suggestion
                                                  //                       as DataGetListCatalog)
                                                  //                   .value ??
                                                  //               "";
                                                  //           _dataGetListCatalogProductStatus =
                                                  //               suggestion;
                                                  //         });
                                                  //       },
                                                  //       noItemsFoundBuilder:
                                                  //           (value) {
                                                  //         return Padding(
                                                  //             padding: EdgeInsets
                                                  //                 .symmetric(
                                                  //                     vertical:
                                                  //                         10.h,
                                                  //                     horizontal:
                                                  //                         5.w),
                                                  //             child: Text(
                                                  //                 "Không tìm thấy kết quả",
                                                  //                 style: TextStyle(
                                                  //                     fontSize:
                                                  //                         11.sp)));
                                                  //       },
                                                  //     ),
                                                  //   ),
                                                  // ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                    child: const _TitleTypeNVL(title: "Phân biệt NSX/HSD"),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                      child: const _SubTitle(text: "( Số chạy 1->9 )")),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "HC",
                                                    child: DropdownCatalog(
                                                        dataGetListCatalog: _dataGetListCatalogNSXHSD, lsCatalog: _lsNSXHSD, onchange: _setNSXHSD),
                                                    // Container(
                                                    //   decoration: BoxDecoration(
                                                    //     border: Border.all(
                                                    //       width: 0.5.w,
                                                    //       color: Colors.grey.shade400,
                                                    //     ),
                                                    //   ),
                                                    //   child: TypeAheadField(
                                                    //     suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                                    //       constraints: BoxConstraints(
                                                    //         minWidth: 150.w,
                                                    //       ),
                                                    //     ),
                                                    //     textFieldConfiguration: TextFieldConfiguration(
                                                    //         enabled: true,
                                                    //         decoration:  InputDecoration(
                                                    //           labelStyle: TextStyle(fontSize: 11.sp),
                                                    //           contentPadding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
                                                    //           isDense: true,
                                                    //           border: InputBorder.none,
                                                    //           focusedBorder: InputBorder.none,
                                                    //           enabledBorder: InputBorder.none,
                                                    //         ),
                                                    //         controller: _discernController,
                                                    //         focusNode: _focusDiscern,
                                                    //         style: TextStyle(fontSize: 12.sp),
                                                    //         onChanged: (value){}
                                                    //     ),
                                                    //     suggestionsCallback: (pattern) async {
                                                    //       return await ImportWareHouseFunction.fetchGetListCatalog(
                                                    //           pattern,
                                                    //           _isCheckTest,
                                                    //           _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) => element.key == "ProductStatus") == null ? "" :
                                                    //           _lsDataGetListCatalogTypeCode.firstWhereOrNull((element) => element.key == "ProductStatus")!.key.toString(), widget.token)
                                                    //     },
                                                    //     itemBuilder: (context, suggestion) {
                                                    //       return ListTile(
                                                    //         title: Text(" ",style: TextStyle(fontSize: 12.sp)),
                                                    //       );
                                                    //     },
                                                    //     onSuggestionSelected: (suggestion) {},
                                                    //     noItemsFoundBuilder: (value) {
                                                    //       return Padding(
                                                    //           padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                                                    //           child: Text(
                                                    //               "Không tìm thấy kết quả",
                                                    //               style: TextStyle(fontSize: 11.sp)));
                                                    //     },
                                                    //   ),
                                                    // ),
                                                  ),
                                                  Visibility(
                                                      visible:
                                                          _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                    child: const _TitleTypeNVL(title: "Số mã kiện"),
                                                  ),
                                                  Visibility(
                                                      visible:
                                                          _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                    child: const _SubTitle(text: "( 6 ký tự )"),
                                                  ),
                                                  Visibility(
                                                      visible:
                                                          _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "KIEN",
                                                    child: Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        inputFormatters: [
                                                          LengthLimitingTextInputFormatter(6),
                                                        ],
                                                        focusNode: _focusNumberCode,
                                                        controller: _numberCodeController,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          border: InputBorder.none,
                                                          focusedBorder: InputBorder.none,
                                                          enabledBorder: InputBorder.none,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          hintText: "XXXXXX",
                                                          filled: true,
                                                          isDense: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp, color: Colors.grey),
                                                          contentPadding: EdgeInsets.zero,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                    child: const _TitleTypeNVL(title: "Màu Da"),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: Text(
                                                        "( 2 ký tự )",
                                                        style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                                                      )),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                    child: Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        inputFormatters: [
                                                          LengthLimitingTextInputFormatter(2),
                                                          FilteringTextInputFormatter.allow(RegExp('[a-zA-Z]')),
                                                        ],
                                                        focusNode: _focusSkinColor,
                                                        controller: _skinColorController,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          border: InputBorder.none,
                                                          focusedBorder: InputBorder.none,
                                                          enabledBorder: InputBorder.none,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          filled: true,
                                                          isDense: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                          contentPadding: EdgeInsets.zero,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 10.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                    child: const _TitleTypeNVL(title: "Chủng loại da"),
                                                  ),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: Text(
                                                        "( 1 ký tự )",
                                                        style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                                                      )),
                                                  Visibility(
                                                      visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                      child: SizedBox(height: 2.h)),
                                                  Visibility(
                                                    visible: _dataGetListCatalogTypeProduct != null && _dataGetListCatalogTypeProduct!.key == "DA",
                                                    child: Container(
                                                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      decoration: BoxDecoration(
                                                          border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                          borderRadius: BorderRadius.circular(3.r)),
                                                      child: TextFormField(
                                                        maxLines: null,
                                                        inputFormatters: [
                                                          LengthLimitingTextInputFormatter(1),
                                                          FilteringTextInputFormatter.allow(RegExp('[a-zA-Z]')),
                                                        ],
                                                        focusNode: _focusSkinTypes,
                                                        controller: _skinTypesController,
                                                        style: TextStyle(fontSize: 12.sp),
                                                        decoration: InputDecoration(
                                                          border: InputBorder.none,
                                                          focusedBorder: InputBorder.none,
                                                          enabledBorder: InputBorder.none,
                                                          errorBorder: InputBorder.none,
                                                          disabledBorder: InputBorder.none,
                                                          // hintText: "Nhập tài khoản",
                                                          filled: true,
                                                          isDense: true,
                                                          fillColor: Colors.white,
                                                          hintStyle: TextStyle(fontSize: 12.sp),
                                                          contentPadding: EdgeInsets.zero,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(height: 10.h),
                                                  Center(
                                                    child: SizedBox(
                                                      child: ElevatedButton(
                                                        style: ButtonStyle(
                                                          padding: MaterialStateProperty.all<EdgeInsets>(
                                                              EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w)),
                                                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                          side: MaterialStateProperty.all(
                                                            const BorderSide(
                                                              color: Color(0xff0052cc),
                                                            ),
                                                          ),
                                                          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                                        ),
                                                        onPressed: () async {
                                                          debugPrint(_skinColorController.text);
                                                          _checkErrorAutoBatch();
                                                          if (_validateProductType == false &&
                                                              _validateImportExportType == false &&
                                                              _validateMaterialGroup == false &&
                                                              _validateMaterialStatus == false &&
                                                              _validateNsxhsd == false &&
                                                              _validateSkinType == false &&
                                                              _validatecCaseCode == false &&
                                                              _validatecSkinColor == false) {
                                                            GetAutoBatch getAutoBatch = GetAutoBatch(
                                                                materialType: _dataGetListCatalogTypeProduct?.key,
                                                                importExportType: _dataGetListCatalogProductOrigin?.key,
                                                                materialGroup: _dataGetListCatalogProductGr?.key,
                                                                materialStatus: _dataGetListCatalogProductStatus?.key,
                                                                nsxhsd: _dataGetListCatalogNSXHSD == null
                                                                    ? null
                                                                    : int.parse(_dataGetListCatalogNSXHSD!.key!),
                                                                skinType: _skinTypesController.text.isEmpty ? null : _skinTypesController.text,
                                                                skinColor: _skinColorController.text.isEmpty ? null : _skinColorController.text,
                                                                caseCode: _numberCodeController.text.isEmpty ? null : _numberCodeController.text);
                                                            FocusScope.of(context).unfocus();
                                                            await _postAutoBatch(context, getAutoBatch);
                                                          } else {
                                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                                backgroundColor: Colors.black,
                                                                content: Text(
                                                                  'Vui lòng nhập đủ trường thông tin!',
                                                                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                                                                ),
                                                                duration: const Duration(seconds: 1)));
                                                          }
                                                        },
                                                        child: _isLoadingAutoBatch == true
                                                            ? SizedBox(
                                                                height: 20.h, width: 20.w, child: const Center(child: CircularProgressIndicator()))
                                                            : Text(
                                                                "Lấy số lô",
                                                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14.sp),
                                                                textAlign: TextAlign.center,
                                                              ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(height: 10.h),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Visibility(
                                            visible: _dataRawMeterial!.isReceive == 0 && _viTab,
                                            child: SizedBox(height: 5.h),
                                          ),
                                          // _ContentImportWareHouseSap(
                                          //     poDetailResponses: _poDetailResponses,
                                          //     dataRawMeterial: _dataRawMeterial,
                                          //     token: widget.token,
                                          //     rawMaterialCardId: widget.barcode,
                                          //     checkIsTest: _isCheckTest,
                                          //     isLoadingStatus: _isLoadingUpdateStatus,
                                          //     updatingStatus: _updatingStatus,
                                          //     updateStatusDone: _updateStatusDone),
                                          // SizedBox(
                                          //   height: 10.h,
                                          // ),
                                          _ContentImportWareHouseSap(
                                              poDetailResponses: _poDetailResponses,
                                              dataRawMeterial: _dataRawMeterial,
                                              token: widget.token,
                                              rawMaterialCardId: widget.barcode,
                                              isLoadingStatus: _isLoadingUpdateStatus,
                                              updatingStatus: _updatingStatus,
                                              updateStatusDone: _updateStatusDone,
                                              checkMounted: mounted),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          Container(
                                            width: double.infinity,
                                            padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
                                            decoration: const BoxDecoration(color: Colors.white),
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: <Widget>[
                                                const _HeaderTwoImportWareHouseSap(title: "II. CHỌN VỊ TRÍ KHO NHẬP"),
                                                SizedBox(
                                                  height: _dataRawMeterial!.isReceive == 0 ? 5.h : 0,
                                                ),
                                                Visibility(
                                                  visible: _dataRawMeterial!.isReceive == 0,
                                                  child: Container(
                                                    decoration: const BoxDecoration(),
                                                    child: ElevatedButton.icon(
                                                      style: ButtonStyle(
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                        side: MaterialStateProperty.all(
                                                          const BorderSide(color: Color(0xff303F9F)),
                                                        ),
                                                        backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                                                      ),
                                                      onPressed: () async {
                                                        if (_focusNumberLot.hasFocus ||
                                                            _lsFocusNode.where((element) => element.hasFocus).isNotEmpty) {
                                                          FocusScope.of(context).unfocus();
                                                          await Future<void>.delayed(const Duration(milliseconds: 500));
                                                          final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
                                                          if (data == null) return;
                                                          if (!mounted) return;
                                                          _setAddressQRCode(data as DataSlocAddress?, context);
                                                        } else {
                                                          final data = await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
                                                          if (data == null) return;
                                                          if (!mounted) return;
                                                          _setAddressQRCode(data as DataSlocAddress?, context);
                                                        }
                                                      },
                                                      icon: Icon(Icons.camera_alt_outlined, size: 25.sp, color: Colors.white),
                                                      label: Text(
                                                        'Quét mã',
                                                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 5.h),
                                                _FieldPlantView(plant: widget.plant),
                                                _DropdownImportWareHouseSap(
                                                    errorSelectedSloc: _errorSelectedSloc,
                                                    disableDropdownSloc: _disableDropdownSloc,
                                                    selectedSloc: _selectedSloc,
                                                    getLsDataSlocAddress: _getLsDataSlocAddress,
                                                    setDropdown: _setDropdown),
                                                _WareHouseNumberView(selectedSloc: _selectedSloc),
                                                IntrinsicHeight(
                                                  child: Row(
                                                    children: <Widget>[
                                                      Expanded(
                                                        flex: 4,
                                                        child: Container(
                                                          height: double.infinity,
                                                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                          decoration: BoxDecoration(
                                                            color: const Color(0xff303F9F),
                                                            border: Border(
                                                              left: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                              right: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                              bottom: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                            ),
                                                          ),
                                                          child: Text(
                                                            "Storage Bin:",
                                                            style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 6,
                                                        child: Container(
                                                          height: double.infinity,
                                                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                          decoration: BoxDecoration(
                                                            color: const Color(0xFFFFFFFF),
                                                            border: Border(
                                                              right: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                              bottom: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                            ),
                                                          ),
                                                          child: Column(children: <Widget>[
                                                            Container(
                                                              // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                              decoration: BoxDecoration(
                                                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                  borderRadius: BorderRadius.circular(3.r)),
                                                              child: Stack(
                                                                children: [
                                                                  TextFormField(
                                                                    maxLines: null,
                                                                    enabled: _dataRawMeterial!.isReceive == 0
                                                                        ? _selectedSloc != null
                                                                        : _dataRawMeterial!.isReceive == 0,
                                                                    textAlign: TextAlign.center,
                                                                    controller: _storageBinController,
                                                                    focusNode: _focusStorageBin,
                                                                    style: TextStyle(fontSize: 12.sp),
                                                                    decoration: InputDecoration(
                                                                      focusedBorder: InputBorder.none,
                                                                      enabledBorder: InputBorder.none,
                                                                      border: InputBorder.none,
                                                                      // contentPadding: EdgeInsets.zero,
                                                                      contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                                      errorBorder: InputBorder.none,
                                                                      disabledBorder: InputBorder.none,
                                                                      filled: true,
                                                                      isDense: true,
                                                                      fillColor: Colors.white,
                                                                      hintStyle: TextStyle(fontSize: 12.sp),
                                                                    ),
                                                                    onChanged: (value) {
                                                                      _onStorageBinChanged(value);
                                                                    },
                                                                  ),
                                                                  Visibility(
                                                                    visible: _selectedSloc != null,
                                                                    child: Positioned(
                                                                      top: 0,
                                                                      right: 5,
                                                                      child: InkWell(
                                                                        onTap: () async {
                                                                          // setState(() {
                                                                          //   _controllerLSXSAP.text = "";
                                                                          //   _isLSXSAPSelected = false;
                                                                          //   _qualityControl = QualityControl();
                                                                          //   _lsCongDoanNhoMasterData = [QualityControlDetailFunction.defaultValueCongDoanNho];
                                                                          //   _selectedCongDoanNho = QualityControlDetailFunction.defaultValueCongDoanNho;
                                                                          // });

                                                                          // clearForNew();
                                                                          // showToast(context: context, message: "Test");

                                                                          final data =
                                                                              await Navigator.pushNamed(context, '/QRCodePageChooseAnAddress');
                                                                          if (data == null) return;
                                                                          if (!mounted) return;

                                                                          // debugPrint(json.encode(data));

                                                                          var storageBin = data as DataSlocAddress;

                                                                          if (storageBin.defaultStorageBin != null) {
                                                                            _storageBinController.text = storageBin.defaultStorageBin!;
                                                                            _storageBinID = storageBin.defaultStorageBinId!;
                                                                          }

                                                                          // _setAddressQRCode(data as DataSlocAddress?, context);
                                                                        },
                                                                        child: Padding(
                                                                          padding: const EdgeInsets.all(4.0),
                                                                          child: Icon(Icons.camera_alt_outlined, size: 20.sp, color: Colors.black),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  )
                                                                ],
                                                              ),
                                                            ),
                                                            SizedBox(
                                                                height: _lsDataGetStorageBn.isNotEmpty || _isLoadingStorageBin == true ? 5.h : 0),
                                                            _ListFilter(
                                                              isLoadingStorageBin: _isLoadingStorageBin,
                                                              lsDataGetStorageBn: _lsDataGetStorageBn,
                                                              setFilter: (DataGetStorageBin value) {
                                                                _setFilter(value);
                                                              },
                                                            ),
                                                            SizedBox(height: _errorStorageBin == true ? 5.h : 0),
                                                            ContainerError.widgetError(_errorStorageBin, "Vui lòng nhập storage Bin"),
                                                            SizedBox(height: _errorStorageBin == true ? 5.h : 0),
                                                          ]),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                                TableInfoNoTop(
                                                  textCL2: _sumAmountEntered.toStringAsFixed(3),
                                                  colorCL2: 0xFFFFFFFF,
                                                  colorCL1: 0xff303F9F,
                                                  textCL1: "Số lượng thực nhập:",
                                                ),
                                                TableInfoNoTop(
                                                  textCL2: _dataRawMeterial!.unit ?? "",
                                                  colorCL2: 0xFFFFFFFF,
                                                  colorCL1: 0xff303F9F,
                                                  textCL1: "ĐVT:",
                                                ),
                                                IntrinsicHeight(
                                                  child: Row(
                                                    children: <Widget>[
                                                      Expanded(
                                                        flex: 4,
                                                        child: Container(
                                                            height: double.infinity,
                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                            decoration: BoxDecoration(
                                                              color: const Color(0xff303F9F),
                                                              border: Border(
                                                                left: BorderSide(
                                                                  color: Colors.black,
                                                                  width: 0.5.w,
                                                                ),
                                                                right: BorderSide(
                                                                  color: Colors.black,
                                                                  width: 0.5.w,
                                                                ),
                                                                bottom: BorderSide(
                                                                  color: Colors.black,
                                                                  width: 0.5.w,
                                                                ),
                                                              ),
                                                            ),
                                                            child: const _TitleNumberLot()), // "Số lô:"
                                                      ),
                                                      Expanded(
                                                        flex: 6,
                                                        child: Container(
                                                          height: double.infinity,
                                                          decoration: BoxDecoration(
                                                            color: const Color(0xFFFFFFFF),
                                                            border: Border(
                                                              right: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                              bottom: BorderSide(
                                                                color: Colors.black,
                                                                width: 0.5.w,
                                                              ),
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Padding(
                                                              padding: REdgeInsets.all(3),
                                                              child: Column(
                                                                children: [
                                                                  Container(
                                                                    padding: REdgeInsets.all(3),
                                                                    decoration: BoxDecoration(
                                                                        border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                        borderRadius: BorderRadius.circular(3.r)),
                                                                    child: TextFormField(
                                                                      maxLines: null,
                                                                      enabled: _dataRawMeterial!.isReceive == 0 && _viTab == false,
                                                                      focusNode: _focusNumberLot,
                                                                      textAlign: TextAlign.center,
                                                                      controller: _controllerNumberLot,
                                                                      style: TextStyle(fontSize: 12.sp),
                                                                      decoration: InputDecoration(
                                                                        border: InputBorder.none,
                                                                        focusedBorder: InputBorder.none,
                                                                        enabledBorder: InputBorder.none,
                                                                        errorBorder: InputBorder.none,
                                                                        disabledBorder: InputBorder.none,
                                                                        filled: true,
                                                                        isDense: true,
                                                                        fillColor: Colors.white,
                                                                        hintStyle: TextStyle(fontSize: 12.sp),
                                                                        contentPadding: EdgeInsets.zero,
                                                                      ),
                                                                      onChanged: (value) {
                                                                        _setOnChangeTextFieldNumberLot();
                                                                      },
                                                                    ),
                                                                  ),
                                                                  SizedBox(height: _errorBatch == true ? 5.h : 0),
                                                                  Padding(
                                                                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                                                                    child: ContainerError.widgetError(_errorBatch, "Vui lòng nhập số lô"),
                                                                  ),
                                                                  SizedBox(height: _errorBatch == true ? 5.h : 0),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )
                                                // _NumberLotTextFieldView(
                                                //     errorBatch: _errorBatch,
                                                //     dataRawMeterial:
                                                //         _dataRawMeterial,
                                                //     focusNumberLot:
                                                //         _focusNumberLot,
                                                //     controllerNumberLot:
                                                //         _controllerNumberLot,
                                                //     onChangeTextField:
                                                //         _setOnChangeTextFieldNumberLot)
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          Container(
                                            width: double.infinity,
                                            decoration: const BoxDecoration(color: Colors.white),
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: <Widget>[
                                                SizedBox(height: 15.h),
                                                Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                                                  child: Text(
                                                    "III. SỐ LƯỢNG NHẬP KHO THEO SO/WBS",
                                                    style: TextStyle(
                                                      fontSize: 14.sp,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 15.h),
                                                Padding(
                                                  padding: EdgeInsets.only(left: 10.w),
                                                  child: SingleChildScrollView(
                                                    scrollDirection: Axis.horizontal,
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Table(
                                                          border: TableBorder.all(width: 0.5.w),
                                                          columnWidths: <int, TableColumnWidth>{
                                                            0: FixedColumnWidth(100.w),
                                                            1: FixedColumnWidth(100.w),
                                                            2: FixedColumnWidth(100.w),
                                                            3: FixedColumnWidth(70.w),
                                                            4: FixedColumnWidth(70.w),
                                                            5: FixedColumnWidth(70.w),
                                                            6: FixedColumnWidth(70.w),
                                                            7: FixedColumnWidth(70.w),
                                                          },
                                                          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                          children: const <TableRow>[
                                                            TableRow(
                                                              decoration: BoxDecoration(
                                                                color: Color(0xff303F9F),
                                                              ),
                                                              children: <Widget>[
                                                                _TitleTable(text: "PO/POLine"),
                                                                _TitleTable(text: "SO /WBS"),
                                                                _TitleTable(text: "LSX Đại trà"),
                                                                _TitleTable(text: "SL giao hàng"),
                                                                _TitleTable(text: "ĐVT"),
                                                                _TitleTable(text: "SL theo PO/PO Line"),
                                                                _TitleTable(text: "SL đã nhập kho"),
                                                                _TitleTable(text: "SL còn lại"),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                        _dataRawMeterial!.isReceive == 1
                                                            ? Column(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                mainAxisAlignment: MainAxisAlignment.start,
                                                                children: List.generate(
                                                                  (_poDetailResponses ?? []).length,
                                                                  (index) => Table(
                                                                    border: TableBorder(
                                                                      left: BorderSide(color: Colors.black, width: 0.5.w),
                                                                      right: BorderSide(color: Colors.black, width: 0.5.w),
                                                                      bottom: BorderSide(color: Colors.black, width: 0.5.w),
                                                                      verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
                                                                    ),
                                                                    columnWidths: <int, TableColumnWidth>{
                                                                      0: FixedColumnWidth(100.w),
                                                                      1: FixedColumnWidth(100.w),
                                                                      2: FixedColumnWidth(100.w),
                                                                      3: FixedColumnWidth(70.w),
                                                                      4: FixedColumnWidth(70.w),
                                                                      5: FixedColumnWidth(70.w),
                                                                      6: FixedColumnWidth(70.w),
                                                                      7: FixedColumnWidth(70.w),
                                                                    },
                                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                                    children: <TableRow>[
                                                                      TableRow(
                                                                        children: <Widget>[
                                                                          _PoView(poDetailResponses: _poDetailResponses![index]),
                                                                          _poDetailResponses == null
                                                                              ? const _EmptyField()
                                                                              : _SOWBSView(poDetailResponses: _poDetailResponses![index]),
                                                                          _TableColumnWidget(
                                                                            text: _poDetailResponses![index].lsxdt != null
                                                                                ? _poDetailResponses![index].lsxdt.toString()
                                                                                : " ",
                                                                          ),
                                                                          Padding(
                                                                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                            child: Container(
                                                                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                                              decoration: BoxDecoration(
                                                                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                                  borderRadius: BorderRadius.circular(3.r)),
                                                                              child: TextFormField(
                                                                                enabled: false,
                                                                                maxLines: null,
                                                                                focusNode: _lsFocusNode[index],
                                                                                textAlign: TextAlign.center,
                                                                                controller: _lsControllers[index],
                                                                                style: TextStyle(fontSize: 12.sp),
                                                                                keyboardType: TextInputType.number,
                                                                                inputFormatters: <TextInputFormatter>[
                                                                                  FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                                                ],
                                                                                decoration: InputDecoration(
                                                                                  border: InputBorder.none,
                                                                                  isDense: true,
                                                                                  contentPadding: EdgeInsets.zero,
                                                                                  errorBorder: InputBorder.none,
                                                                                  disabledBorder: InputBorder.none,
                                                                                  filled: true,
                                                                                  hintStyle: TextStyle(fontSize: 12.sp),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          _TableColumnWidget(text: _poDetailResponses![index].unit ?? " "),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].quantityByPO == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].quantityByPO!.toStringAsFixed(3)),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].quantityReceived == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].quantityReceived!.toStringAsFixed(3)),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].remainQuantity == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].remainQuantity!.toStringAsFixed(3)),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              )
                                                            : Column(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                mainAxisAlignment: MainAxisAlignment.start,
                                                                children: List.generate(
                                                                  _poDetailResponses!.length,
                                                                  (index) => Table(
                                                                    border: TableBorder(
                                                                      left: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                      right: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                      bottom: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                      verticalInside: BorderSide(
                                                                        color: Colors.black,
                                                                        width: 0.5.w,
                                                                      ),
                                                                    ),
                                                                    columnWidths: <int, TableColumnWidth>{
                                                                      0: FixedColumnWidth(100.w),
                                                                      1: FixedColumnWidth(100.w),
                                                                      2: FixedColumnWidth(100.w),
                                                                      3: FixedColumnWidth(70.w),
                                                                      4: FixedColumnWidth(70.w),
                                                                      5: FixedColumnWidth(70.w),
                                                                      6: FixedColumnWidth(70.w),
                                                                      7: FixedColumnWidth(70.w),
                                                                    },
                                                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                                                    children: <TableRow>[
                                                                      TableRow(
                                                                        children: <Widget>[
                                                                          _PoView(poDetailResponses: _poDetailResponses![index]),
                                                                          _SOWBSView(poDetailResponses: _poDetailResponses![index]),
                                                                          // Container(
                                                                          //     padding: EdgeInsets.symmetric(
                                                                          //         horizontal: 3.w, vertical: 3.h),
                                                                          //     child: (_poDetailResponses![index].so !=
                                                                          //         null &&
                                                                          //         _poDetailResponses![index]
                                                                          //             .so !=
                                                                          //             "") &&
                                                                          //         (_poDetailResponses![index]
                                                                          //             .soLine !=
                                                                          //             null &&
                                                                          //             _poDetailResponses![index]
                                                                          //                 .soLine !=
                                                                          //                 "")
                                                                          //         ? Column(children: <Widget>[
                                                                          //       Text(
                                                                          //         _poDetailResponses![index]
                                                                          //             .so ??
                                                                          //             "",
                                                                          //         style: TextStyle(
                                                                          //             fontSize: 12.sp),
                                                                          //         textAlign: TextAlign.center,
                                                                          //       ),
                                                                          //       Text(
                                                                          //         _poDetailResponses![index]
                                                                          //             .soLine ??
                                                                          //             "",
                                                                          //         style: TextStyle(
                                                                          //             fontSize: 12.sp),
                                                                          //         textAlign: TextAlign.center,
                                                                          //       )
                                                                          //     ])
                                                                          //         : _poDetailResponses![index].wbs !=
                                                                          //         null &&
                                                                          //         _poDetailResponses![index]
                                                                          //             .wbs !=
                                                                          //             ""
                                                                          //         ? Text(
                                                                          //       _poDetailResponses![index]
                                                                          //           .wbs ??
                                                                          //           "",
                                                                          //       style: TextStyle(
                                                                          //           fontSize: 12.sp),
                                                                          //       textAlign: TextAlign.center,
                                                                          //     )
                                                                          //         : Text(
                                                                          //       'Tồn trơn',
                                                                          //       style: TextStyle(
                                                                          //           fontSize: 12.sp),
                                                                          //       textAlign: TextAlign.center,
                                                                          //     )),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].lsxdt != null
                                                                                  ? _poDetailResponses![index].lsxdt.toString()
                                                                                  : " "),
                                                                          Padding(
                                                                            padding: REdgeInsets.all(3),
                                                                            child: Container(
                                                                              padding: REdgeInsets.all(3),
                                                                              decoration: BoxDecoration(
                                                                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                                                  borderRadius: BorderRadius.circular(3.r)),
                                                                              child: TextFormField(
                                                                                maxLines: null,
                                                                                focusNode: _lsFocusNode[index],
                                                                                textAlign: TextAlign.center,
                                                                                controller: _lsControllers[index],
                                                                                style: TextStyle(fontSize: 12.sp),
                                                                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                                                inputFormatters: <TextInputFormatter>[
                                                                                  FilteringTextInputFormatter.allow(RegExp(r'^\d+[\,\.]?\d*')),
                                                                                  CommaTextInputFormatter()
                                                                                ],
                                                                                decoration: InputDecoration(
                                                                                  border: InputBorder.none,
                                                                                  isDense: true,
                                                                                  contentPadding: EdgeInsets.zero,
                                                                                  errorBorder: InputBorder.none,
                                                                                  disabledBorder: InputBorder.none,
                                                                                  filled: true,
                                                                                  fillColor: Colors.white,
                                                                                  hintStyle: TextStyle(fontSize: 12.sp),
                                                                                ),
                                                                                onChanged: (value) {
                                                                                  _setOnChangeTextField();
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          _TableColumnWidget(text: _poDetailResponses![index].unit ?? " "),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].quantityByPO == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].quantityByPO!.toStringAsFixed(3)),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].quantityReceived == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].quantityReceived!.toStringAsFixed(3)),
                                                                          _TableColumnWidget(
                                                                              text: _poDetailResponses![index].remainQuantity == null
                                                                                  ? ""
                                                                                  : _poDetailResponses![index].remainQuantity!.toStringAsFixed(3)),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: _errorController == true ? 10.h : 0),
                                                Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                                                    child: ContainerError.widgetError(_errorController, "Vui lòng nhập số lượng SO/WBS")),
                                                SizedBox(height: 10.h),
                                              ],
                                            ),
                                          ),
                                          // _PartThreeImportWareHouseSap(
                                          //     dataRawMeterial: _dataRawMeterial,
                                          //     poDetailResponses:
                                          //         _poDetailResponses,
                                          //     lsFocusNode: _lsFocusNode,
                                          //     lsControllers: _lsControllers,
                                          //     errorController: _errorController,
                                          //     setOnChange:
                                          //         _setOnChangeTextField),
                                          SizedBox(height: 15.h),

                                          Container(
                                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                                            decoration: const BoxDecoration(color: Colors.white),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: <Widget>[
                                                SizedBox(height: 15.h),
                                                const _TitleTypeNVL(title: "TDS"),
                                                Text(
                                                  "(6 ký tự, ví dụ: 150007), số gần nhất: ${_dataRawMeterial?.latestTDS}",
                                                  style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
                                                ),
                                                SizedBox(height: 2.h),
                                                Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                  decoration: BoxDecoration(
                                                      border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                      borderRadius: BorderRadius.circular(3.r)),
                                                  child: TextFormField(
                                                    // maxLines: null,
                                                    inputFormatters: [
                                                      LengthLimitingTextInputFormatter(6),
                                                      // FilteringTextInputFormatter.allow(RegExp('[a-zA-Z]')),
                                                    ],
                                                    focusNode: _focusTDS,
                                                    controller: _TDSController,
                                                    style: TextStyle(fontSize: 12.sp),
                                                    decoration: InputDecoration(
                                                      border: InputBorder.none,
                                                      contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                      focusedBorder: InputBorder.none,
                                                      enabledBorder: InputBorder.none,
                                                      errorBorder: InputBorder.none,
                                                      disabledBorder: InputBorder.none,
                                                      filled: true,
                                                      isDense: true,
                                                      fillColor: Colors.white,
                                                      hintStyle: TextStyle(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                ),
                                                Visibility(
                                                  visible: poNhapKhau42 == true || !_viTab,
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      SizedBox(height: 15.h),
                                                      const _TitleTypeNVL(title: "Số tờ khai"),
                                                      SizedBox(height: 2.h),
                                                      Container(
                                                        // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                        decoration: BoxDecoration(
                                                            border: Border.all(width: 0.5.w, color: Colors.grey.shade500),
                                                            borderRadius: BorderRadius.circular(3.r)),
                                                        child: Stack(
                                                          children: [
                                                            TextFormField(
                                                              focusNode: _focusSoToKhai,
                                                              controller: _SoToKhaiController,
                                                              style: TextStyle(fontSize: 12.sp),
                                                              onChanged: (value) {
                                                                filterSoToKhai(value);
                                                                setState(() {
                                                                  isSelectedSoToKhai = false;
                                                                });
                                                              },
                                                              decoration: InputDecoration(
                                                                border: InputBorder.none,
                                                                contentPadding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                                                focusedBorder: InputBorder.none,
                                                                enabledBorder: InputBorder.none,
                                                                errorBorder: InputBorder.none,
                                                                disabledBorder: InputBorder.none,
                                                                filled: true,
                                                                isDense: true,
                                                                fillColor: Colors.white,
                                                                hintStyle: TextStyle(fontSize: 12.sp),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible: _SoToKhaiController.text.isNotEmpty,
                                                              child: Positioned(
                                                                top: 1,
                                                                right: 10,
                                                                child: GestureDetector(
                                                                  onTap: () {
                                                                    setState(() {
                                                                      _SoToKhaiController.text = "";
                                                                      _lsSoToKhaiMasterData = [];
                                                                      isSelectedSoToKhai = false;
                                                                    });
                                                                  },
                                                                  child: Padding(
                                                                    padding: const EdgeInsets.all(2.0),
                                                                    child: Icon(Icons.close, size: 20.sp, color: Colors.red),
                                                                  ),
                                                                ),
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                      Visibility(
                                                        visible: _lsSoToKhaiMasterData!.isNotEmpty || _isLoading == true,
                                                        child: SizedBox(
                                                          height: 280.h,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: Colors.white,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Colors.grey.withOpacity(0.5),
                                                                  spreadRadius: 5,
                                                                  blurRadius: 7,
                                                                  offset: const Offset(0, 3), // changes position of shadow
                                                                ),
                                                              ],
                                                            ),
                                                            child: _isLoading == true
                                                                ? const Center(child: CircularProgressIndicator())
                                                                : ListView.separated(
                                                                    padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.h),
                                                                    itemCount: _lsSoToKhaiMasterData!.length,
                                                                    itemBuilder: (BuildContext context, int index) {
                                                                      return InkWell(
                                                                        onTap: () {
                                                                          setState(() {
                                                                            _SoToKhaiController.text = _lsSoToKhaiMasterData[index];
                                                                            // _keyNVL = _lsSoToKhaiMasterData[index];
                                                                            _lsSoToKhaiMasterData = [];
                                                                            isSelectedSoToKhai = true;
                                                                          });
                                                                        },
                                                                        child: Text(
                                                                          _lsSoToKhaiMasterData?[index] ?? "",
                                                                          style: TextStyle(fontSize: 11.sp),
                                                                        ),
                                                                      );
                                                                    },
                                                                    separatorBuilder: (BuildContext context, int index) => SizedBox(height: 10.h),
                                                                  ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                // Text(isSelectedSoToKhai.toString()),
                                                SizedBox(height: 15.h),
                                              ],
                                            ),
                                          ),

                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Flexible(
                                                  flex: 5,
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                    decoration: const BoxDecoration(),
                                                    child: Visibility(
                                                      visible: _dataRawMeterial!.isReceive == 0,
                                                      child: ElevatedButton(
                                                        style: ButtonStyle(
                                                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                          side: MaterialStateProperty.all(
                                                            BorderSide(
                                                              color: Colors.grey.shade500,
                                                            ),
                                                          ),
                                                          backgroundColor: MaterialStateProperty.all(Colors.grey.shade500),
                                                        ),
                                                        onPressed: _dataRawMeterial!.isReceive == 1
                                                            ? null
                                                            : () {
                                                                _resetImport(context);
                                                              },
                                                        child: Container(
                                                          margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                                          child: Text(
                                                            "Đặt lại",
                                                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  )),
                                              Flexible(
                                                flex: 5,
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                                  decoration: const BoxDecoration(),
                                                  child: ElevatedButton(
                                                    style: ButtonStyle(
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                                      side: MaterialStateProperty.all(
                                                        BorderSide(
                                                          color: Color(_dataRawMeterial!.isReceive == 0
                                                              ? _isLoadingUpdateStatus == true || _isLoadingAutoBatch == true
                                                                  ? 0xffd6d6d6
                                                                  : 0xff0052cc
                                                              : 0xffd6d6d6),
                                                        ),
                                                      ),
                                                      backgroundColor: MaterialStateProperty.all(Color(_dataRawMeterial!.isReceive == 0
                                                          ? _isLoadingUpdateStatus == true || _isLoadingAutoBatch == true
                                                              ? 0xffd6d6d6
                                                              : 0xff0052cc
                                                          : 0xffd6d6d6)),
                                                    ),
                                                    onPressed: _dataRawMeterial!.isReceive == 1
                                                        ? null
                                                        : _isLoadingUpdateStatus == true || _isLoadingAutoBatch == true
                                                            ? null
                                                            : () async {
                                                                String dateTimeNow = DateFormat('yyyy-MM-dd').format(DateTime.now());
                                                                DateTime convertDateTimeNow = DateFormat("yyyy-MM-dd").parse(dateTimeNow);
                                                                DateTime convertDateTimeOld = DateFormat("yyyy-MM-dd").parse(widget.dateTimeOld);
                                                                if (!convertDateTimeNow.isAtSameMomentAs(convertDateTimeOld)) {
                                                                  Platform.isAndroid
                                                                      ? showDialog(
                                                                          context: context,
                                                                          barrierDismissible: false,
                                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionAndroid())
                                                                      : showCupertinoDialog(
                                                                          context: context,
                                                                          barrierDismissible: false,
                                                                          builder: (BuildContext context) => const DiaLogTimeOutSessionIOS());
                                                                } else {
                                                                  _checkError();
                                                                  if (_errorSelectedSloc == false &&
                                                                      _errorController == false &&
                                                                      _errorStorageBin == false &&
                                                                      _errorBatch == false) {
                                                                    if (Platform.isAndroid) {
                                                                      debugPrint("Platform.isAndroid");
                                                                      _luuPressAndroid(context);
                                                                    } else {
                                                                      debugPrint("_luuPressiOS");
                                                                      _luuPressiOS(context);
                                                                    }
                                                                  }
                                                                }
                                                              },
                                                    child: Container(
                                                      margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                                      child: Text(
                                                        "Lưu",
                                                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 250.h),
                                        ],
                                      ),
                                    ),
                )));
  }
}

class _SubTitle extends StatelessWidget {
  final String text;
  const _SubTitle({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(fontSize: 12.sp, fontStyle: FontStyle.italic, color: Colors.grey.shade500),
    );
  }
}

class _TitleTypeNVL extends StatelessWidget {
  final String title;
  const _TitleTypeNVL({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold),
    );
  }
}

class _ChangeView extends StatelessWidget {
  final String text;
  final int colorContainer;
  const _ChangeView({
    Key? key,
    required this.text,
    required this.colorContainer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 15.h),
      width: double.infinity,
      decoration: BoxDecoration(color: Colors.white, border: Border.all(color: Color(colorContainer))),
      child: Center(
          child: Text(
        text,
        style: TextStyle(fontSize: 12.sp, color: Colors.black, fontWeight: FontWeight.bold),
      )),
    );
  }
}

class _TableColumnWidget extends StatelessWidget {
  final String text;
  const _TableColumnWidget({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _TitleTable extends StatelessWidget {
  final String text;
  const _TitleTable({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Text(
        text,
        style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _NotFoundView extends StatelessWidget {
  const _NotFoundView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(child: Text("Không tìm thấy thông tin pallet NVL!", style: TextStyle(fontSize: 15.sp), textAlign: TextAlign.center));
  }
}

class _ListFilter extends StatelessWidget {
  final bool isLoadingStorageBin;
  final List<DataGetStorageBin> lsDataGetStorageBn;
  final ValueChanged<DataGetStorageBin> setFilter;
  const _ListFilter({Key? key, required this.isLoadingStorageBin, required this.lsDataGetStorageBn, required this.setFilter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: lsDataGetStorageBn.isNotEmpty || isLoadingStorageBin == true,
      child: SizedBox(
        height: 150.h,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 7,
                offset: const Offset(0, 1), // changes position of shadow
              ),
            ],
          ),
          child: isLoadingStorageBin == true
              ? const Center(child: CircularProgressIndicator())
              : Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Colors.white),
                  child: SingleChildScrollView(
                    child: Column(
                      children: List.generate(lsDataGetStorageBn.length, (index) {
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 5.h),
                          child: InkWell(
                            onTap: () {
                              setFilter(lsDataGetStorageBn[index]);
                            },
                            child: Text(
                              lsDataGetStorageBn[index].value.toString(),
                              style: TextStyle(fontSize: 13.sp, color: Colors.black),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}

class _HeaderImporWareHouse extends StatelessWidget {
  final DataRawMeterial? dataRawMeterial;
  const _HeaderImporWareHouse({Key? key, required this.dataRawMeterial}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
      decoration: const BoxDecoration(color: Colors.white),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          dataRawMeterial!.materialType == "1"
              ? "NVL Chính (Gỗ xẻ, ván, ván lạng)"
              : dataRawMeterial!.materialType == "2"
                  ? "Hóa chất"
                  : "Vật tư khác (phụ kiện, ốc vít, thanh trượt)",
          style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class _ContentImportWareHouseSap extends StatelessWidget {
  final List<PoDetailResponses>? poDetailResponses;
  final DataRawMeterial? dataRawMeterial;
  final String rawMaterialCardId;
  final String token;
  final VoidCallback updatingStatus;
  final VoidCallback updateStatusDone;
  final bool isLoadingStatus;
  final bool checkMounted;
  const _ContentImportWareHouseSap(
      {Key? key,
      required this.poDetailResponses,
      required this.dataRawMeterial,
      required this.rawMaterialCardId,
      required this.token,
      required this.updatingStatus,
      required this.updateStatusDone,
      required this.isLoadingStatus,
      required this.checkMounted})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 10.w),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            "I. THÔNG TIN PALLET NVL",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(
            height: 5.h,
          ),
          TableInfo(textCL1: "Tên NCC:", colorCL1: 0xff303F9F, textCL2: dataRawMeterial!.vendorName ?? " ", colorCL2: 0xFFFFFFFF),
          IntrinsicHeight(
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 4,
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: const Color(0xff303F9F),
                      border: Border(
                        left: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        right: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        bottom: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                      ),
                    ),
                    child: Text(
                      "PO/PO Line:",
                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Container(
                      height: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        border: Border(
                          right: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                          bottom: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: List.generate(
                            poDetailResponses!.length,
                            (index) => Text(
                              poDetailResponses![index].po == null && poDetailResponses![index].poLine == null
                                  ? ""
                                  : "${poDetailResponses![index].po ?? ""}/${poDetailResponses![index].poLine ?? ""}",
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            ),
          ),
          TableInfoNoTop(textCL1: "Mã hàng(mã SAP):", colorCL1: 0xff303F9F, textCL2: dataRawMeterial!.productCode ?? " ", colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(textCL1: "Tên hàng:", colorCL1: 0xff303F9F, textCL2: dataRawMeterial!.productName ?? " ", colorCL2: 0xFFFFFFFF),
          IntrinsicHeight(
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 4,
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: const Color(0xff303F9F),
                      border: Border(
                        left: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        right: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        bottom: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                      ),
                    ),
                    child: Text(
                      "SO/SO Line:",
                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Container(
                      height: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        border: Border(
                          right: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                          bottom: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: List.generate(
                            poDetailResponses!.length,
                            (index) => Text(
                              (poDetailResponses![index].so != null && poDetailResponses![index].so != "") &&
                                      (poDetailResponses![index].soLine != null && poDetailResponses![index].soLine != "")
                                  ? '${poDetailResponses![index].so ?? ""}/${poDetailResponses![index].soLine ?? ""}'
                                  : "",
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            ),
          ),
          IntrinsicHeight(
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 4,
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: const Color(0xff303F9F),
                      border: Border(
                        left: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        right: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        bottom: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                      ),
                    ),
                    child: Text(
                      "WBS:",
                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Container(
                      height: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        border: Border(
                          right: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                          bottom: BorderSide(
                            color: Colors.black,
                            width: 0.5.w,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: List.generate(
                            poDetailResponses!.length,
                            (index) => Text(
                              poDetailResponses![index].wbs != null && poDetailResponses![index].wbs != "" ? poDetailResponses![index].wbs ?? "" : "",
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ),
                        ),
                      )),
                ),
              ],
            ),
          ),
          TableInfoNoTop(
              textCL1: "Số lượng đặt hàng:",
              colorCL1: 0xff303F9F,
              textCL2:
                  '${dataRawMeterial!.poQuantity != null ? dataRawMeterial!.poQuantity!.toStringAsFixed(3) : ' '} ${dataRawMeterial!.poQuantityUnit ?? ' '}',
              colorCL2: 0xFFFFFFFF),
          IntrinsicHeight(
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 4,
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: const Color(0xff303F9F),
                      border: Border(
                        left: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        right: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        bottom: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                      ),
                    ),
                    child: Text(
                      "Số lượng đã nhập kho:",
                      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      border: Border(
                        right: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                        bottom: BorderSide(
                          color: Colors.black,
                          width: 0.5.w,
                        ),
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "${dataRawMeterial!.sumQuantityReceived == null ? "" : dataRawMeterial!.sumQuantityReceived!.toStringAsFixed(3)} ${dataRawMeterial!.unit}"
                            .toString(),
                        style: TextStyle(fontSize: 11.sp),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          TableInfoNoTop(
              textCL1: "Số lượng giao hàng:",
              colorCL1: 0xff303F9F,
              textCL2: "${dataRawMeterial!.quantity != null ? dataRawMeterial!.quantity!.toStringAsFixed(3) : " "}  ${dataRawMeterial!.unit ?? " "}",
              colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(
              textCL1: "Trọng lượng:",
              colorCL1: 0xff303F9F,
              textCL2: dataRawMeterial!.quantity2 == null
                  ? ""
                  : "${(dataRawMeterial!.quantity2!.toStringAsFixed(3)).toString()} | ${dataRawMeterial!.quantity2Unit ?? " "}",
              colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(
              textCL1: "Quy cách:",
              colorCL1: 0xff303F9F,
              textCL2: dataRawMeterial!.specifications == null ? "" : dataRawMeterial!.specifications.toString(),
              colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(
              textCL1: "Ngày hết hạn:",
              colorCL1: 0xff303F9F,
              textCL2: dataRawMeterial!.expirationDate == null ? "" : dataRawMeterial!.expirationDate.toString(),
              colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(
              textCL1: "Số lượng quy đổi:",
              colorCL1: 0xff303F9F,
              textCL2: dataRawMeterial!.quantity3 == null
                  ? ""
                  : "${(dataRawMeterial!.quantity3 ?? 0.0).toStringAsFixed(3)} | ${dataRawMeterial!.quantity3Unit ?? " "}",
              colorCL2: 0xFFFFFFFF),
          TableInfoNoTop(
              textCL1: "Ngày sản xuất:", colorCL1: 0xff303F9F, textCL2: dataRawMeterial!.manufacturingDateStr ?? " ", colorCL2: 0xFFFFFFFF),
          SizedBox(height: 10.h),
          Align(
              alignment: Alignment.centerRight,
              child: Text("Được tạo bởi ${dataRawMeterial!.createBy ?? " "} vào lúc ${dataRawMeterial!.createTime ?? " "}",
                  style: TextStyle(fontSize: 12.sp, color: Colors.grey), textAlign: TextAlign.end)),
          SizedBox(height: 10.h),
          Visibility(
            visible: dataRawMeterial!.isReceive != 1 ? dataRawMeterial!.isGoodsArrive == null || dataRawMeterial!.isGoodsArrive == false : false,
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(),
              child: ElevatedButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                  side: MaterialStateProperty.all(
                    const BorderSide(
                      color: Color(0xff303F9F),
                    ),
                  ),
                  backgroundColor: MaterialStateProperty.all(const Color(0xff303F9F)),
                ),
                onPressed: () async {
                  final check = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext context) {
                      return const DialogConfirmImportWH();
                    },
                  );
                  if (!checkMounted) return;
                  if (check == null) return;
                  if (check == true) {
                    updatingStatus();
                    await ImportWareHouseFunction.sendUpdateStatusService(rawMaterialCardId, token, context);
                    if (!checkMounted) return;
                    updateStatusDone();
                  }
                },
                child: isLoadingStatus == true
                    ? SizedBox(height: 20.h, width: 20.w, child: const Center(child: CircularProgressIndicator()))
                    : Container(
                        margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                        child: Text(
                          "Hàng về nhà máy",
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                        ),
                      ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _HeaderTwoImportWareHouseSap extends StatelessWidget {
  final String title;
  const _HeaderTwoImportWareHouseSap({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

class _DropdownImportWareHouseSap extends StatelessWidget {
  final bool errorSelectedSloc;
  final bool disableDropdownSloc;
  final DataSlocAddress? selectedSloc;
  final List<DataSlocAddress> getLsDataSlocAddress;
  final ValueChanged<DataSlocAddress?> setDropdown;
  const _DropdownImportWareHouseSap(
      {Key? key,
      required this.errorSelectedSloc,
      required this.disableDropdownSloc,
      required this.selectedSloc,
      required this.getLsDataSlocAddress,
      required this.setDropdown})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
                height: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                decoration: BoxDecoration(
                  color: const Color(0xff303F9F),
                  border: Border(
                    left: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  ),
                ),
                child: RichText(
                  text: TextSpan(
                    text: 'Sloc',
                    style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold),
                    children: <TextSpan>[
                      TextSpan(text: ': ', style: TextStyle(fontSize: 12.sp, color: Colors.white)),
                      TextSpan(text: '*', style: TextStyle(fontSize: 12.sp, color: Colors.red)),
                    ],
                  ),
                )),
          ),
          Expanded(
            flex: 6,
            child: Container(
                height: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  border: Border(
                    right: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                    bottom: BorderSide(
                      color: Colors.black,
                      width: 0.5.w,
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
                        borderRadius: BorderRadius.circular(3.r),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DataSlocAddress>(
                          isExpanded: true,
                          isDense: true,
                          itemHeight: null,
                          value: selectedSloc,
                          iconSize: 15.sp,
                          style: const TextStyle(color: Colors.white),
                          onChanged: disableDropdownSloc == true
                              ? null
                              : (DataSlocAddress? value) {
                                  setDropdown(value);
                                },
                          items: getLsDataSlocAddress.map((DataSlocAddress wareHouse) {
                            return DropdownMenuItem<DataSlocAddress>(
                                value: wareHouse,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15.w),
                                  child: Text(
                                    wareHouse.slocDisplay ?? " ",
                                    style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                  ),
                                ));
                          }).toList(),
                          selectedItemBuilder: (BuildContext context) {
                            return getLsDataSlocAddress.map<Widget>((DataSlocAddress wareHouse) {
                              return Text(wareHouse.slocDisplay.toString(),
                                  style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                            }).toList();
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: errorSelectedSloc == true ? 5.h : 0),
                    ContainerError.widgetError(errorSelectedSloc, "Vui lòng chọn Sloc"),
                  ],
                )),
          ),
        ],
      ),
    );
  }
}

// class _DropdownGoodsReceivedNotes extends StatelessWidget {
//   // final bool errorSelectedGoodReceivedNotes;
//   final bool disableDropdownSloc;
//   final String? selectedGoodReceivedNotes;
//   final List<String> lsGoodsReceivedNotes;
//   final ValueChanged<SelectedGoodReceivedNotes> setDropdownGoodsReceivedNotes;
//
//   final String token;
//   final bool checkMounted;
//   const _DropdownGoodsReceivedNotes(
//       {Key? key,
//         // required this.errorSelectedGoodReceivedNotes,
//         required this.disableDropdownSloc,
//         required this.selectedGoodReceivedNotes,
//         required this.lsGoodsReceivedNotes,
//         required this.setDropdownGoodsReceivedNotes,
//         required this.token,
//         required this.checkMounted})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return IntrinsicHeight(
//       child: Container(
//         padding: EdgeInsets.symmetric(horizontal: 10.w,vertical: 5.h),
//         decoration: const BoxDecoration(color: Colors.white),
//         child: Row(
//           children: <Widget>[
//             Expanded(
//               flex: 4,
//               child: Container(
//                   height: double.infinity,
//                   padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                   decoration: BoxDecoration(
//                     color: const Color(0xff303F9F),
//                     border: Border.all(
//                       color: Colors.black,
//                       width: 0.5.w
//                     ),
//                   ),
//                   child: Text("Số phiếu nhập kho:",
//                       style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold))),
//             ),
//             Expanded(
//               flex: 6,
//               child: Container(
//                   height: double.infinity,
//                   padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                   decoration: BoxDecoration(
//                     color: const Color(0xFFFFFFFF),
//                     border: Border(
//                       right: BorderSide(
//                         color: Colors.black,
//                         width: 0.5.w,
//                       ),
//                       bottom: BorderSide(
//                         color: Colors.black,
//                         width: 0.5.w,
//                       ),
//                       top: BorderSide(
//                       color: Colors.black,
//                       width: 0.5.w,
//                     ),
//                     ),
//                   ),
//                   child:
//                   // Column(
//                   //   children: [
//                       Container(
//                         padding:
//                         EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
//                         decoration: BoxDecoration(
//                           border: Border.all(
//                               color: Colors.grey.shade400, width: 0.5.w),
//                           borderRadius: BorderRadius.circular(3.r),
//                         ),
//                         child: DropdownButtonHideUnderline(
//                           child: DropdownButton<String>(
//                             isExpanded: true,
//                             isDense: true,
//                             itemHeight: null,
//                             value: selectedGoodReceivedNotes,
//                             iconSize: 15.sp,
//                             style: const TextStyle(color: Colors.white),
//                             onChanged: disableDropdownSloc == true
//                                 ? null
//                                 : (String? value) async {
//                               final data = await ImportWareHouseFunction.takeGetInfoReceiSapApi(value.toString(),token,context,checkMounted);
//                               if(!checkMounted) return;
//                               setDropdownGoodsReceivedNotes(SelectedGoodReceivedNotes(value: value,dataGetInfoReceiSap: data));
//                             },
//                             items: lsGoodsReceivedNotes.map((String goodReceivedNotes) {
//                               return DropdownMenuItem<String>(
//                                   value: goodReceivedNotes,
//                                   child: Padding(
//                                     padding:
//                                     EdgeInsets.symmetric(horizontal: 15.w),
//                                     child: Text(
//                                       goodReceivedNotes,
//                                       style: TextStyle(
//                                           color: Colors.black, fontSize: 11.sp),
//                                     ),
//                                   ));
//                             }).toList(),
//                             selectedItemBuilder: (BuildContext context) {
//                               return lsGoodsReceivedNotes.map<Widget>((String goodReceivedNotes) {
//                                 return Text(goodReceivedNotes,
//                                     style: TextStyle(
//                                         color: Colors.black, fontSize: 11.sp),
//                                     overflow: TextOverflow.ellipsis);
//                               }).toList();
//                             },
//                           ),
//                         ),
//                       ),
//                       // Visibility(
//                       //   visible: errorSelectedGoodReceivedNotes,
//                       //     child: SizedBox(height:  5.h)),
//                       // ContainerError.widgetError(
//                       //     errorSelectedGoodReceivedNotes, "Vui lòng chọn số phiếu nhập kho"),
//                   //   ],
//                   // )
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
class _WareHouseNumberView extends StatelessWidget {
  final DataSlocAddress? selectedSloc;
  const _WareHouseNumberView({Key? key, required this.selectedSloc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: const Color(0xff303F9F),
                border: Border(
                  left: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                "Warehouse No:",
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                border: Border(
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                selectedSloc == null ? "" : selectedSloc!.warehouseNo ?? "",
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// class _PartThreeImportWareHouseSap extends StatelessWidget {
//   final DataRawMeterial? dataRawMeterial;
//   final List<PoDetailResponses>? poDetailResponses;
//   final List<FocusNode> lsFocusNode;
//   final List<TextEditingController> lsControllers;
//   final bool errorController;
//   final VoidCallback setOnChange;
//
//   const _PartThreeImportWareHouseSap(
//       {Key? key,
//       required this.dataRawMeterial,
//       required this.poDetailResponses,
//       required this.lsFocusNode,
//       required this.lsControllers,
//       required this.errorController,
//       required this.setOnChange})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return
//   }
// }

// class _NumberLotTextFieldView extends StatelessWidget {
//   final bool errorBatch;
//   final DataRawMeterial? dataRawMeterial;
//   final FocusNode focusNumberLot;
//   final TextEditingController controllerNumberLot;
//   final VoidCallback onChangeTextField;
//   const _NumberLotTextFieldView(
//       {Key? key,
//       required this.errorBatch,
//       required this.dataRawMeterial,
//       required this.focusNumberLot,
//       required this.controllerNumberLot,
//       required this.onChangeTextField})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return
//   }
// }

class _FieldPlantView extends StatelessWidget {
  final String plant;
  const _FieldPlantView({Key? key, required this.plant}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: const Color(0xff303F9F),
                border: Border(
                  top: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  left: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                "Plant:",
                style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Container(
              height: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                border: Border(
                  top: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  right: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                  bottom: BorderSide(
                    color: Colors.black,
                    width: 0.5.w,
                  ),
                ),
              ),
              child: Text(
                plant,
                style: TextStyle(fontSize: 11.sp, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TitleNumberLot extends StatelessWidget {
  const _TitleNumberLot({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      "Số lô:",
      style: TextStyle(fontSize: 11.sp, color: Colors.white, fontWeight: FontWeight.bold),
    );
  }
}

class _PoView extends StatelessWidget {
  final PoDetailResponses poDetailResponses;
  const _PoView({Key? key, required this.poDetailResponses}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
        child: Column(children: <Widget>[
          Text(
            (poDetailResponses.po != null && poDetailResponses.po != "") ? poDetailResponses.po ?? "" : "",
            style: TextStyle(fontSize: 12.sp),
            textAlign: TextAlign.center,
          ),
          Text(
            (poDetailResponses.poLine != null && poDetailResponses.poLine != "") ? poDetailResponses.poLine ?? "" : "",
            style: TextStyle(fontSize: 12.sp),
            textAlign: TextAlign.center,
          ),
        ]));
  }
}

class _SOWBSView extends StatelessWidget {
  final PoDetailResponses poDetailResponses;

  const _SOWBSView({Key? key, required this.poDetailResponses}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: (poDetailResponses.so != null && poDetailResponses.so != "") &&
              (poDetailResponses.soLine != null && poDetailResponses.soLine != "") &&
              (poDetailResponses.wbs == null || poDetailResponses.wbs == "")
          ? Column(children: <Widget>[
              Text(
                poDetailResponses.so ?? "",
                style: TextStyle(fontSize: 12.sp),
                textAlign: TextAlign.center,
              ),
              Text(
                poDetailResponses.soLine ?? "",
                style: TextStyle(fontSize: 12.sp),
                textAlign: TextAlign.center,
              ),
            ])
          : (poDetailResponses.so == null || poDetailResponses.so == "") &&
                  (poDetailResponses.soLine == null || poDetailResponses.soLine == "") &&
                  (poDetailResponses.wbs != null && poDetailResponses.wbs != "")
              ? Text(
                  poDetailResponses.wbs!,
                  style: TextStyle(fontSize: 12.sp),
                  textAlign: TextAlign.center,
                )
              : Text('Tồn trơn', style: TextStyle(fontSize: 12.sp), textAlign: TextAlign.center),
    );
  }
}

class _EmptyField extends StatelessWidget {
  const _EmptyField({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(" ", style: TextStyle(fontSize: 12.sp));
  }
}

class _DropdownNSXHSD extends StatelessWidget {
  final List<DataGetListCatalog> lsNSXHSD;
  final DataGetListCatalog? dataGetListCatalogNSXHSD;
  final ValueChanged<DataGetListCatalog?> onTap;
  const _DropdownNSXHSD({Key? key, required this.lsNSXHSD, required this.dataGetListCatalogNSXHSD, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400, width: 0.5.w),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: DropdownButtonHideUnderline(
            child: DropdownButton<DataGetListCatalog>(
                dropdownColor: Colors.white,
                isExpanded: true,
                itemHeight: null,
                isDense: true,
                value: dataGetListCatalogNSXHSD,
                iconSize: 15.sp,
                style: const TextStyle(color: Colors.black),
                onChanged: onTap,
                items: lsNSXHSD.map<DropdownMenuItem<DataGetListCatalog>>((DataGetListCatalog data) {
                  return DropdownMenuItem<DataGetListCatalog>(
                      value: data,
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 5.h),
                        child: Text(
                          data.value ?? "",
                          style: TextStyle(color: Colors.black, fontSize: 11.sp),
                        ),
                      ));
                }).toList(),
                selectedItemBuilder: (BuildContext context) {
                  return lsNSXHSD.map<Widget>((DataGetListCatalog data) {
                    return Text(data.value ?? "", style: TextStyle(color: Colors.black, fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                  }).toList();
                })));
  }
}
