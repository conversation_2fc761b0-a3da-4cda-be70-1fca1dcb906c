# Database and Storage

The TTF MES Mobile application uses multiple storage mechanisms to persist data locally. This document details the storage implementation, including secure storage, shared preferences, and file storage strategies.

## Storage Architecture

The application implements a layered storage architecture:

1. **Secure Storage**: For sensitive data (credentials, tokens)
2. **Shared Preferences**: For application settings and non-sensitive data
3. **File Storage**: For images and larger data sets

### Directory Structure

The storage-related code is organized as follows:

```
lib/
├── Storage/
│   ├── storageSecureStorage.dart     # Secure storage implementation
│   └── storageSharedPreferences.dart # SharedPreferences implementation
├── service/
│   └── globalValue.dart              # In-memory global state
└── utils/
    └── appConfig.dart                # Configuration settings
```

## Secure Storage Implementation

The application uses <PERSON>lut<PERSON>'s `flutter_secure_storage` package to store sensitive information securely, with platform-specific encryption.

### Implementation

The secure storage implementation is encapsulated in `lib/Storage/storageSecureStorage.dart`:

```dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  static final _storage = FlutterSecureStorage();

  // Store a string value securely
  static Future<void> setString(String key, String value, String? androidOptions) async {
    AndroidOptions getAndroidOptions() => const AndroidOptions(
      encryptedSharedPreferences: true,
    );
    await _storage.write(
      key: key,
      value: value,
      aOptions: androidOptions != null ? getAndroidOptions() : null,
    );
  }

  // Retrieve a stored string value
  static Future<String?> getString(String key, String? androidOptions) async {
    AndroidOptions getAndroidOptions() => const AndroidOptions(
      encryptedSharedPreferences: true,
    );
    return await _storage.read(
      key: key,
      aOptions: androidOptions != null ? getAndroidOptions() : null,
    );
  }

  // Delete a stored value
  static Future<void> removeValue(String key, String? androidOptions) async {
    AndroidOptions getAndroidOptions() => const AndroidOptions(
      encryptedSharedPreferences: true,
    );
    await _storage.delete(
      key: key,
      aOptions: androidOptions != null ? getAndroidOptions() : null,
    );
  }

  // Clear all stored values
  static Future<void> removeAll(String? androidOptions) async {
    AndroidOptions getAndroidOptions() => const AndroidOptions(
      encryptedSharedPreferences: true,
    );
    await _storage.deleteAll(
      aOptions: androidOptions != null ? getAndroidOptions() : null,
    );
  }
}
```

### Usage

Secure storage is used for sensitive data like authentication tokens:

```dart
// Store user authentication data
await SecureStorage.setString("user", jsonEncode(user), null);

// Retrieve user authentication data
final userString = await SecureStorage.getString("user", null);
if (userString != null && userString != ' ') {
  final getSaveUser = UserModel.fromJson(jsonDecode(userString));
  // Use the user data
}

// Clear authentication data on logout
await SecureStorage.removeAll(null);
```

### Security Considerations

- Data is encrypted using platform-specific mechanisms
- On Android, uses EncryptedSharedPreferences
- On iOS, uses Keychain services
- Keys should be consistent and well-documented
- First-run detection clears secure storage on iOS

## Shared Preferences Usage

The application uses the `shared_preferences` package for storing non-sensitive application settings and user preferences.

### Implementation

The shared preferences implementation is encapsulated in `lib/Storage/storageSharedPreferences.dart`:

```dart
import 'package:shared_preferences/shared_preferences.dart';

class StorageSharedPreferences {
  static SharedPreferences? _prefs;

  // Initialize shared preferences
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get a boolean value
  static bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  // Set a boolean value
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs!.setBool(key, value);
  }

  // Get a string value
  static String? getString(String key) {
    return _prefs?.getString(key);
  }

  // Set a string value
  static Future<bool> setString(String key, String value) async {
    return await _prefs!.setString(key, value);
  }

  // Get an integer value
  static int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  // Set an integer value
  static Future<bool> setInt(String key, int value) async {
    return await _prefs!.setInt(key, value);
  }

  // Remove a value
  static Future<bool> remove(String key) async {
    return await _prefs!.remove(key);
  }

  // Clear all values
  static Future<bool> clear() async {
    return await _prefs!.clear();
  }
}
```

### Usage

Shared preferences are used for application settings and user preferences:

```dart
// Initialize shared preferences (in main.dart)
await StorageSharedPreferences.init();

// Check first run
final isFirstRun = StorageSharedPreferences.getBool('first_run') ?? true;
if (isFirstRun) {
  // Perform first-run operations
  StorageSharedPreferences.setBool('first_run', false);
}

// Store user preferences
StorageSharedPreferences.setString('preferred_language', 'en');
StorageSharedPreferences.setBool('notifications_enabled', true);

// Retrieve user preferences
final language = StorageSharedPreferences.getString('preferred_language') ?? 'en';
final notificationsEnabled = StorageSharedPreferences.getBool('notifications_enabled') ?? true;
```

### Best Practices

- Initialize in `main()` before accessing
- Use consistent key naming conventions
- Provide default values when retrieving
- Keep stored data minimal and non-sensitive
- Document all preference keys used in the application

## Data Caching Strategy

The application implements a multi-tiered caching strategy to optimize performance and reduce network usage.

### In-Memory Cache

For frequently accessed data that doesn't need to persist between app restarts:

- Global variables in `service/globalValue.dart`
- State management within widgets
- Singleton service classes with cached values

### Persistent Cache

For data that should persist between app restarts:

- Application settings in SharedPreferences
- User settings in SharedPreferences
- Authentication data in SecureStorage
- Offline data cache in structured storage

### Cache Invalidation

Strategies for ensuring data freshness:

- Time-based expiration (e.g., cache valid for 30 minutes)
- Event-based invalidation (e.g., refresh on certain actions)
- User-initiated refresh (pull-to-refresh, refresh button)
- Version-based invalidation (e.g., cache invalid after API version change)

## File Storage

For storing larger data like images and files.

### Image Storage

The application uses `path_provider` and `gallery_saver` for image operations:

```dart
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:gallery_saver/gallery_saver.dart';

// Save an image to temporary directory
Future<File> saveImageToTemp(File image) async {
  final directory = await getTemporaryDirectory();
  final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
  final path = '${directory.path}/$fileName';
  return await image.copy(path);
}

// Save image to gallery
Future<bool> saveImageToGallery(String imagePath) async {
  return await GallerySaver.saveImage(imagePath);
}
```

### Document Storage

For storing and retrieving document files:

```dart
// Save document to application documents directory
Future<File> saveDocument(List<int> bytes, String fileName) async {
  final directory = await getApplicationDocumentsDirectory();
  final path = '${directory.path}/$fileName';
  final file = File(path);
  return await file.writeAsBytes(bytes);
}

// Get document from application documents directory
Future<File?> getDocument(String fileName) async {
  final directory = await getApplicationDocumentsDirectory();
  final path = '${directory.path}/$fileName';
  final file = File(path);
  if (await file.exists()) {
    return file;
  }
  return null;
}
```

## Offline Data Management

The application supports offline operation through strategic data caching.

### Offline Queue

Operations performed offline are queued for later execution:

1. Operation is attempted online
2. If offline, operation is stored in a queue
3. When connectivity is restored, queued operations are executed

### Data Synchronization

When reconnecting to the network:

1. Check for queued operations
2. Execute operations in order
3. Handle conflicts if server data has changed
4. Update local cache with latest server data

## Multi-Device Synchronization

The application handles the same user accessing from multiple devices:

- Backend maintains the source of truth
- Client sends timestamps with operations
- Server resolves conflicts based on timestamp and rules
- Changes from other devices are pulled during refresh

## Security Considerations

### Data Protection

- Sensitive data is only stored in secure storage
- Authentication tokens have limited lifetime
- Data is encrypted at rest where possible

### Data Cleanup

- Temporary files are cleaned up regularly
- Cache size is monitored and limited
- User can clear cached data manually
- Authentication data is cleared on logout

## Error Handling

### Storage Failures

The application handles storage-related errors gracefully:

- Permission issues
- Disk space limitations
- Corruption or access problems

### Recovery Strategies

When storage errors occur:

1. Attempt to recover corrupted data
2. Fallback to default values if recovery fails
3. Clear problematic data and reload from server
4. Notify user when necessary with clear instructions

## Best Practices

### Efficient Data Storage

- Store only necessary data
- Use appropriate storage mechanism for each data type
- Compress large data when possible
- Clean up temporary files regularly

### Data Versioning

- Include version information with stored data
- Implement migration strategies for version changes
- Handle backward compatibility

### Testing

- Test storage operations in various conditions
- Verify data persistence across app restarts
- Simulate storage failures
- Verify security of sensitive data

## Troubleshooting

Common issues and solutions:

1. **Data Not Persisting**
   - Check initialization of storage services
   - Verify storage permissions
   - Ensure synchronous operations complete before app termination

2. **Out of Storage Space**
   - Implement size checks before storage operations
   - Clean up temporary files regularly
   - Compress data when appropriate

3. **Slow Storage Performance**
   - Use in-memory cache for frequently accessed data
   - Optimize data structures for storage
   - Perform heavy storage operations asynchronously on background threads 