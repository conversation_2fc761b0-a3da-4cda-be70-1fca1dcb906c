import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../model/qualityControlApi.dart';
import '../repository/function/qualityControlDetailFunction.dart';


class QualityControlDetailView extends StatelessWidget {
  final TestMethodList? selectedMethod;
  final QualityControl? qualityControl;
  final ValueChanged<TestMethodList?> getSelectedMethod;
  final List<TestMethodList>? lsTestMethodList;
  final SamplingLevelList? selectedLevel;
  final ValueChanged<SamplingLevelList?> getSelectedLevel;
  final List<SamplingLevelList>? lsSamplingLevelList;
  final bool hideDetailLever;
  final TextEditingController controllerHideDetailLever;
  final TextEditingController controller_3;
  final TextEditingController controller_4;
  final ResultList? selectedResult;
  final List<ResultList>? lsResultList;
  final ValueChanged<ResultList?> getSelectedResult;
  final bool errorTestMethodDetail;
  final bool errorLevelDetail;
  final bool errorAcceptableLevelDetail;
  final bool errorQuantityCheckDetail;
  final bool errorResultCheckDetail;
  final VoidCallback checkErrorAcceptableLevelDetail;
  final VoidCallback checkErrorQuantityCheckDetail;
  final VoidCallback resetState;
  final VoidCallback getQuantitySample;
  final String quanlityCheck;
  final bool loadingGetQuanititySample;
  const QualityControlDetailView({Key? key,
    required this.selectedMethod,
    required this.qualityControl,
    required this.getSelectedMethod,
    required this.lsTestMethodList,
    required this.selectedLevel,
    required this.getSelectedLevel,
    required this.lsSamplingLevelList,
    required this.hideDetailLever,
    required this.controllerHideDetailLever,
    required this.controller_3,
    required this.controller_4,
    required this.selectedResult,
    required this.lsResultList,
    required this.getSelectedResult,
    required this.errorTestMethodDetail,
    required this.errorLevelDetail,
    required this.errorAcceptableLevelDetail,
    required this.errorQuantityCheckDetail,
    required this.errorResultCheckDetail,
    required this.checkErrorAcceptableLevelDetail,
    required this.checkErrorQuantityCheckDetail,
    required this.resetState,
    required this.getQuantitySample,
    required this.quanlityCheck,
    required this.loadingGetQuanititySample

  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // print(quanlityCheck.isEmpty );
    // print(quanlityCheck.isEmpty || (selectedLevel == null || selectedLevel!.catalogCode == " "));
    return SingleChildScrollView(
        child: SafeArea(
          minimum: EdgeInsets.symmetric(horizontal: 5.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 15.h),
              Container(
                decoration: BoxDecoration(
                  border:
                  Border.all(width: 0.5, color: const Color(0xff0052cc)),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                          vertical: 15.h, horizontal: 15.h),
                      decoration: const BoxDecoration(
                        color: Color(0xff0052cc),
                      ),
                      child: Text(
                        "Mẫu thử chi tiết",
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 13.sp,
                            color: Colors.white),
                      ),
                    ),
                    SizedBox(height: 15.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                              "Phương pháp KT",
                              style: TextStyle(
                                  fontSize: 12.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child:  Column(
                              children: <Widget>[
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        width: 0.5,color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(3.r),
                                  ),
                                  child:
                                  DropdownButtonHideUnderline(
                                    child: DropdownButton<TestMethodList>(
                                      isExpanded: true,
                                      isDense: true,
                                      itemHeight: null,
                                      value: selectedMethod ?? QualityControlDetailFunction.defaultValueTestMethodList,
                                      iconSize: 15.sp,
                                      style: const TextStyle(color: Colors.white),
                                      onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null :getSelectedMethod,
                                      items:lsTestMethodList!.map((TestMethodList method) {
                                        return DropdownMenuItem<TestMethodList>(
                                            value: method,
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                              child: Text(
                                                method.catalogTextVi.toString(),
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 11.sp),
                                              ),

                                            ));
                                      }).toList(),
                                      selectedItemBuilder: (BuildContext context) {
                                        return lsTestMethodList!.map<Widget>((TestMethodList method) {
                                          return Text(method.catalogTextVi.toString(), style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                        }).toList();
                                      },
                                    ),
                                  ),
                                ),
                                SizedBox(
                                    height:  errorTestMethodDetail == true
                                        ? 10.h
                                        : 0),
                                _ErrorValidateView(text: "Vui lòng chọn phương pháp KT" ,error: errorTestMethodDetail)
                          ]),
                    )],
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                              "Mức độ",
                              style: TextStyle(
                                  fontSize: 12.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: Column(
                              children:<Widget>[
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        width: 0.5,color:Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(3.r),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<SamplingLevelList>(
                                      isExpanded: true,
                                      isDense: true,
                                      itemHeight: null,
                                      value: selectedLevel ?? QualityControlDetailFunction.defaultValueSamplingLevelList,
                                      iconSize: 15.sp,
                                      style: const TextStyle(color: Colors.white),
                                      onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null :getSelectedLevel,
                                      items: lsSamplingLevelList!.map((SamplingLevelList level) {
                                        return DropdownMenuItem<SamplingLevelList>(
                                            value: level,
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                              child: Text(
                                                level.catalogTextVi.toString(),
                                                style: TextStyle(color: Colors.black, fontSize: 11.sp),
                                              ),

                                            ));
                                      }).toList(),
                                      selectedItemBuilder: (BuildContext context) {
                                        return lsSamplingLevelList!.map<Widget>((SamplingLevelList level) {
                                          return Text(level.catalogTextVi.toString(), style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                        }).toList();
                                      },
                                    ),
                                  ),
                                ),
                                SizedBox(
                                    height:  errorLevelDetail == true
                                        ? 10.h
                                        : 0),
                                _ErrorValidateView(text: "Vui lòng chọn mức độ" ,error: errorLevelDetail)
                          ]),
                    )],
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Visibility(
                      visible: hideDetailLever,
                      child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                                "Nội dung chi tiết",
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                              ),

                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: Container(
                                    padding:  EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                    decoration: BoxDecoration(
                                        border: Border.all(width: 0.5,
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(3.r)
                                    ),
                                    child: TextFormField(
                                      enabled:  qualityControl!.qcType == "NVL" ? qualityControl!.qualityChecker != null?false:true:true,
                                      maxLines: null,
                                      textAlign: TextAlign.center,
                                      controller: controllerHideDetailLever,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.zero,
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintStyle: TextStyle(
                                            fontSize: 12.sp),
                                      ),
                                      onChanged: (value){
                                      resetState();
                                      },
                                    ),
                                  // SizedBox(
                                  //     height:  errorHideDetail == true
                                  //         ? 10.h
                                  //         : 0),
                                  // _ErrorValidateView(text: "Vui lòng nhập nội dung chi tiết" ,error: errorHideDetail)
                          ),
                      )],
                      ),
                    ),
                    ),
                Visibility(
                  visible: hideDetailLever,
                  child: SizedBox(height: 5.h),),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                                "Mức chấp nhận",
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child:  Column(
                                children:<Widget>[
                                  Container(
                                    padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                    decoration: BoxDecoration(
                                        border: Border.all( width: 0.5,
                                            color:Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(3.r)
                                    ),
                                    child: TextFormField(
                                      enabled:  qualityControl!.qcType == "NVL" ? qualityControl!.qualityChecker != null?false:true:true,
                                      maxLines: null,
                                      textAlign: TextAlign.center,
                                      controller: controller_3,
                                      style: TextStyle(fontSize: 12.sp),
                                      // keyboardType:
                                      // TextInputType.number,
                                      // inputFormatters: <TextInputFormatter>[
                                      //   FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*')),
                                      // ],
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.zero,
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintStyle: TextStyle(
                                            fontSize: 12.sp),
                                      ),
                                      onChanged: (value){
                                        checkErrorAcceptableLevelDetail();
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                      height:  errorAcceptableLevelDetail == true
                                          ? 10.h
                                          : 0),
                                  _ErrorValidateView(text: "Vui lòng nhập mức chấp nhận" ,error: errorAcceptableLevelDetail)
                            ]),

                    )],
                      ),
                    ),
                    SizedBox(height: 5.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                                "SL kiểm tra",
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: Column(
                                children:<Widget>[
                                  Container(
                                    padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 3.w),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            width: 0.5,
                                            color: Colors.grey.shade400
                                        ),
                                        borderRadius: BorderRadius.circular(3.r)
                                    ),
                                    child: TextFormField(
                                      enabled: qualityControl!.qcType == "NVL" ? qualityControl!.qualityChecker != null?false:true:true,
                                      maxLines: null,
                                      keyboardType: TextInputType.number,
                                      inputFormatters: <TextInputFormatter>[
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      textAlign: TextAlign.center,
                                      controller: controller_4,
                                      style: TextStyle(fontSize: 12.sp),
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.zero,
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintStyle: TextStyle(
                                            fontSize: 12.sp),
                                      ),
                                      onChanged: (value){
                                        checkErrorQuantityCheckDetail();
                                      },
                                    ),
                                  ),
                                  SizedBox(height:  errorQuantityCheckDetail == true ? 10.h : 0),
                                  _ErrorValidateView(text: "Vui lòng nhập SL kiểm tra" ,error: errorQuantityCheckDetail)
                            ]),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 3.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child:
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: qualityControl!.qcType == "NVL" ?qualityControl!.qualityChecker != null ? Colors.grey.shade400:  const Color(0xff0052cc): const Color(0xff0052cc),
                          ),
                          onPressed: quanlityCheck.isEmpty || (selectedLevel == null || selectedLevel!.catalogCode == " ") || loadingGetQuanititySample == true ? null :qualityControl!.qcType == "NVL" ? qualityControl!.qualityChecker != null? null :(){
                            getQuantitySample();
                          }:getQuantitySample,
                          child:
                          Text(loadingGetQuanititySample == true ?"Loading...":'Lấy SL kiểm tra', style: TextStyle(fontSize: 12.sp, color: Colors.white, fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ),
                    SizedBox(height: 3.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 3,
                            child: Text(
                              "Kết quả",
                              style: TextStyle(
                                  fontSize: 12.sp, fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            flex: 7,
                            child: Column(
                              children:<Widget>[
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        width: 0.5, color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(3.r),
                                  ),
                                  child:
                                  DropdownButtonHideUnderline(
                                    child: DropdownButton<ResultList>(
                                      isExpanded: true,
                                      isDense: true,
                                      itemHeight: null,
                                      value: selectedResult ?? QualityControlDetailFunction.defaultResultList,
                                      iconSize: 15.sp,
                                      style: const TextStyle(
                                          color: Colors.white),
                                      onChanged: qualityControl!.qcType == "NVL" && qualityControl!.qualityChecker != null ? null : getSelectedResult,
                                      items: lsResultList!.map((ResultList result) {
                                        return DropdownMenuItem<ResultList>(
                                            value: result,
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(vertical: 5.h),
                                              child: Text(
                                                result.catalogTextVi.toString(),
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 11.sp),
                                              ),
                                            ));
                                      }).toList(),
                                      selectedItemBuilder: (BuildContext context) {
                                        return lsResultList!.map((ResultList result) {
                                          return Text(result.catalogTextVi.toString(), style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                                        }).toList();
                                      },
                                    ),
                                  ),
                                ),
                                SizedBox(
                                    height:  errorResultCheckDetail == true
                                        ? 10.h
                                        : 0),
                                _ErrorValidateView(text: "Vui lòng chọn kết quả" ,error: errorResultCheckDetail)
                          ]),
                    )],
                      ),
                    ),
                    SizedBox(height: 15.h),
                  ],
                ),
              ),
              SizedBox(height: 15.h),
              // GestureDetector(
              //   onTap: () {
              //     checkError();
              //     if(_error == false && _error_2 == false && _error_3 == false && _error_4 == false && _error_5 == false && _error_6 == false){
              //       FocusScope.of(context).unfocus();
              //       Navigator.pushNamed(context, '/QuantityInformationView',
              //           arguments: ScreenArgumentQualityInformation(
              //               widget.token,
              //               widget.qualityControlDetail,
              //               widget.qualityControl,
              //               widget.lsTestMethodList,
              //               widget.lsResultList,
              //               widget.lsSamplingLevelList,
              //               widget.lsError,
              //               widget.lsQualityControlInformationIdList,
              //               widget.lsQualityControlInformation,
              //               widget.lsErrorList,
              //               QualityControlDetailFunction.getSendQualityControlDetail(
              //                   widget.qualityControl,
              //                   _selectedMethod,
              //                   _selectedLevel,
              //                   _controller_3.text,
              //                   _controller_4.text,
              //                   _selectedResult,
              //                   _hideDetailLever,
              //                   _controllerHideDetailLever.text
              //               ),
              //               widget.selectedStaff,
              //               widget.selectedType,
              //               widget.pO,
              //               widget.quantityCheck,
              //               widget.selectedResult,
              //               widget.lsFileTabCheck,
              //               widget.formatDatePost,
              //               widget.dateTimeOld
              //           ));
              //     }
              //   },
              //   child: Container(
              //     padding: EdgeInsets.symmetric(vertical: 15.h),
              //     width: double.infinity,
              //     decoration: BoxDecoration(
              //       borderRadius: BorderRadius.all(
              //         Radius.circular(5.r),
              //       ),
              //       color: const Color(0xff0052cc),
              //     ),
              //     child: Center(
              //       child: Text(
              //         'Tiếp tục',
              //         style: TextStyle(
              //             color: Colors.white,
              //             fontSize: 13.sp,
              //             fontWeight: FontWeight.bold),
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      );
  }
}

class _ErrorValidateView extends StatelessWidget {
  final bool error;
  final String text;
  const _ErrorValidateView({Key? key,required this.error, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  Visibility(
        visible: error,
        child: Row(
          mainAxisAlignment:
          MainAxisAlignment.start,
          children: <Widget>[
            Flexible(
              flex: 1,
              child: Icon(
                  Icons.error_outline,
                  size: 13.sp,
                  color:
                  Colors.red[700]),
            ),
            SizedBox(width: 5.w),
            Flexible(
                flex: 8,
                child: Text(
                    text,
                    style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors
                            .red[700])))
          ],
        ));
  }
}