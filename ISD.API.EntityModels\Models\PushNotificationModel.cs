﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    public partial class PushNotificationModel
    {
        [Key]
        public Guid Id { get; set; }
        [Required]
        [StringLength(255)]
        public string Title { get; set; }
        [Required]
        public string Message { get; set; }
        [StringLength(500)]
        public string Url { get; set; }
        [StringLength(255)]
        public string NotificationType { get; set; }
        [StringLength(255)]
        public string TargetUser { get; set; }
        [StringLength(255)]
        public string ExternalId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SentAt { get; set; }
        public int Status { get; set; }
        [StringLength(255)]
        public string CreatedBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? UpdatedAt { get; set; }
        public bool? IsRead { get; set; }
        public bool IsForwarded { get; set; }
        public string ForwardedUsers { get; set; }
        [StringLength(255)]
        public string ForwardBy { get; set; }
        public Guid? NotificationIdForward { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(500)]
        public string Description { get; set; }
        [StringLength(50)]
        public string Client { get; set; }
    }
}