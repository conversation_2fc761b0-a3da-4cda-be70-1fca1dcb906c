---
description: 
globs: 
alwaysApply: false
---
# Code Patterns & Architecture

## App Initialization
The app initialization flow starts in [lib/main.dart](mdc:TTF_MES_Mobile/lib/main.dart), which:
1. Initializes storage services
2. Sets up system configurations
3. Launches the main app component

## Navigation
- Uses <PERSON>lut<PERSON>'s named routes system
- Route definitions in [lib/route/route.dart](mdc:TTF_MES_Mobile/lib/route/route.dart)
- Screen arguments defined in [lib/screenArguments/](mdc:TTF_MES_Mobile/lib/screenArguments) directory
- Navigation service: [lib/service/navigatorService.dart](mdc:TTF_MES_Mobile/lib/service/navigatorService.dart)

## Data Management
- Secure storage: [lib/Storage/storageSecureStorage.dart](mdc:TTF_MES_Mobile/lib/Storage/storageSecureStorage.dart)
- SharedPreferences: [lib/Storage/storageSharedPreferences.dart](mdc:TTF_MES_Mobile/lib/Storage/storageSharedPreferences.dart)
- API communication handled in repository layer
- Data models defined in model directory

## UI Components
- Screen size adaptation using flutter_screenutil
- Reusable widgets in Widget directory
- Feature-specific UI elements in element directory
- Material Design as the base design system

