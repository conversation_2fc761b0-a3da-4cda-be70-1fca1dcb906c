﻿using ISD.API.Constant.Common;
using ISD.API.Constant.MESP2;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Repositories;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MES;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using ISD.API.ViewModels.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NuGet.Packaging.Signing;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("2.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "web")]

    public class DeliveryHistoryController : ControllerBaseAPI
    {
        #region Get lịch sử giao dịch 
        /// <summary>API Search "Tìm kiếm danh sách DeliveryHistory"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/DeliveryHistory/Search
        ///     Params: 
        ///             + version : 2
        ///     Body: 
        ///             {
        ///                 "paging": {
        ///                     "draw": 1,
        ///                     "start": 0,
        ///                     "length": 10
        ///                 },
        ///                 "productCode": "",
        ///                 "purchaseOrderCode": "",
        ///                 "vendorCode": "",
        ///                 "so": "",
        ///                 "wbs": "",
        ///                 "fromDate_DocumentDate": null,
        ///                 "toDate_DocumentDate": null,
        ///                 "fromDate_CreateTime": null,
        ///                 "toDate_CreateTime": null,
        ///                 "isReceive": null,
        ///                 "actived": null
        ///             }
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "message": null,
        ///         "data": [
        ///         {
        ///             "stt": 1,
        ///             "rawMaterialCardId": "d58d7300-1d35-4ddc-8f80-34a75c9c9668",
        ///             "createTime": "2022-08-22T10:11:53.88",
        ///             "purchaseOrderCode": null,
        ///             "sosoLine": [
        ///                 {
        ///                     "so": "524000",
        ///                     "soLine": "40"
        ///                  },
        ///                 {
        ///                      "so": "524000",
        ///                      "soLine": "50"
        ///                  }
        ///                         ],
        ///             "wbs": [
        ///                      "HAHA"
        ///                     ],
        ///             "plant": "1000",
        ///             "documentDate": null,
        ///             "productCode": "520010175",
        ///             "productName": "Kệ đơn hông Trái 302-ĐG, Tủ bếp thẳng",
        ///             "poQuantity": null,
        ///             "poQuantity_Delivered": 0,
        ///             "quantity": 50,
        ///             "poQuantityUnit": null,
        ///             "vendorCode": "0010000352",
        ///             "vendorName": "CÔNG TY CP TƯ VẤN ĐẦU TƯ VÀ ĐẦU TƯ NAM",
        ///             "isReceive": null,
        ///             "receiveDate": null,
        ///             "actived": null
        ///         }
        ///        ]
        ///          "draw": 1,
        ///          "recordsTotal": 10,
        ///          "recordsFiltered": 12
        ///     }
        /// </remarks>
        [HttpPost("Search")]
        public IActionResult GetDeliveryHistory([FromBody] DeliveryHistorySearchViewModel searchVM)
        {
            //Get List DeliveryHistory
            var querySearch = _unitOfWork.PurchaseOrderRepository.GetDeliveryHistory(CurrentUser?.AccountId, searchVM.ProductCode, searchVM.PurchaseOrderCode, searchVM.VendorCode, searchVM.SO, searchVM.WBS, searchVM.FromDate_DocumentDate, searchVM.ToDate_DocumentDate, searchVM.FromDate_CreateTime, searchVM.ToDate_CreateTime, searchVM.IsReceive, searchVM.Actived);

            var datatblModel = new DatatableViewModel()
            {
                draw = searchVM.Paging.draw,
                start = searchVM.Paging.start,
                length = searchVM.Paging.length
            };

            int filteredResultsCount = 0;
            int totalResultsCount = 0;

            var res = CustomSearchRepository.CustomSearchFunc<DeliveryHistoryResultViewModel>(datatblModel, out filteredResultsCount, out totalResultsCount, querySearch, "STT");

            if (res != null && res.Count() > 0)
            {
                int i = datatblModel.start;
                foreach (var item in res)
                {
                    i++;
                    item.STT = i;
                }
            }
            return Ok(new ApiSuccessResponse<List<DeliveryHistoryResultViewModel>>
            {
                Data = res,
                Draw = datatblModel.draw,
                RecordsFiltered = filteredResultsCount,
                RecordsTotal = totalResultsCount
            });
        }
        #endregion

        #region Huỷ lịch sử giao hàng
        /// <summary>API Search "Huỷ lịch sử giao hàng"</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/DeliveryHistory/Cancel
        ///     Params: 
        ///             + version : 2
        ///             
        /// OUT PUT
        /// 
        ///     {
        ///         "code": 200,
        ///         "isSuccess": true,
        ///         "message": "Tích hợp thành công PurchaseOrder",
        ///         "data": "face10a0-9351-47e8-b031-6501f6076ab2",
        ///         "additionalData": null
        ///     }
        /// </remarks>
        [HttpPut("Cancel")]
        public async Task<IActionResult> CancelDeliveryHistory([FromBody] CancelRawMaterialCardRequest request)
        {
            //Where RawMaterialCardId
            var existDH = await _context.RawMaterialCardModel.FirstOrDefaultAsync(x => x.RawMaterialCardId == request.RawMaterialCardId);

            //Nếu tồn tại cập nhật trạng thái 
            if (existDH != null)
            {
                //Cập nhật trạng thái 
                //- true: đang giao 
                //- false: đã huỷ
                existDH.Actived = false;
                existDH.LastEditTime = DateTime.Now;
                existDH.LastEditBy = CurrentUser?.AccountId;

                _context.Entry(existDH).State = EntityState.Modified;
                await _context.SaveChangesAsync();
            }
            return Ok(new ApiResponse
            {
                Code = 200,
                IsSuccess = true,
                Message = "Tích hợp thành công PurchaseOrder",
                Data = existDH.RawMaterialCardId
            });
        }
        public class CancelRawMaterialCardRequest
        {
            public Guid RawMaterialCardId { get; set; }
        }
        #endregion

        #region Danh sách chuyển kho

        /// <summary>Danh sách chuyển kho</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v{version}/MES/WarehouseTransaction/ListWarehouseTranfer
        ///     Params: 
        ///             + version : 1
        ///     Body: 
        ///              {
        ///                "plant": null,
        ///                "sloc": null,
        ///                "reservationCode": null,
        ///                "statusReservation": "1",
        ///                "productCode": null,
        ///                "statusWarehouse": "0"
        ///              }
        ///             
        /// OUT PUT
        /// 
        ///               {
        ///                     "code": 200,
        ///                     "isSuccess": true,
        ///                     "message": "GET danh sách chuyển kho thành công.",
        ///                     "data": [
        ///                       {
        ///                         "plant": "1000",
        ///                         "storageLocation": "N010",
        ///                         "reservationNumber": "15241",
        ///                         "statusReservation": "Đã duyệt",
        ///                         "quantity": 1,
        ///                         "so": "",
        ///                         "wbs": "",
        ///                         "lsx": "",
        ///                         "riPlant": "1000",
        ///                         "riStorageLocation": "X005",
        ///                         "item": "1",
        ///                         "statusWarehouse": "Chờ xuất kho"
        ///                       }
        ///                     ]
        ///                }
        /// </remarks>
        [HttpPost("ListWarehouseTranfer")]
        public IActionResult POST([FromBody] ListTranferWarehouseRequest1 req)
        {
            try
            {
                //Danh sách giao dịch kho
                var listMovementType = new List<string>
                {
                    MovementType.DeliverOneStep,
                    MovementType.DeliverTwoStep
                };

                //Danh sách chi tiết reservation theo điều kiện tìm kiếm: Plant, Sloc, ReservationCode, Material Number
                //var query = _context.MaterialReservationModel.Include(x => x.ReservationHeader)
                //                                             .Where(x => (!string.IsNullOrEmpty(req.SlocExport) ? x.StorageLocation == req.SlocExport : true) &&
                //                                                          (!string.IsNullOrEmpty(req.SlocImport) ? x.RIStorageLocation == req.SlocImport : true) &&
                //                                                          (!string.IsNullOrEmpty(req.ReservationCode) ? x.ReservationNumber == req.ReservationCode : true) &&
                //                                                          (!string.IsNullOrEmpty(req.ProductCode) ? x.MaterialNumber == req.ProductCode : true) &&
                //                                                          (!string.IsNullOrEmpty(req.Plant) ? x.Plant == req.Plant : true) &&
                //                                                          //Search Ngày yêu cầu
                //                                                          (req.ReqDate_From.HasValue ? x.ReservationHeader.RequestDate >= req.ReqDate_From : true) &&
                //                                                          (req.ReqDate_To.HasValue ? x.ReservationHeader.RequestDate <= req.ReqDate_To : true))
                //                                             .AsNoTracking();

                var queryTemp = from x in _context.MaterialReservationModel
                                join h in _context.ReservationHeaderModel on x.ReservationHeaderId equals h.ReservationHeaderId
                                where
                                 listMovementType.Contains(h.MovementType) &&
                                 (!string.IsNullOrEmpty(req.SlocExport) ? x.StorageLocation == req.SlocExport : true) &&
                                  (!string.IsNullOrEmpty(req.SlocImport) ? x.RIStorageLocation == req.SlocImport : true) &&
                                  (!string.IsNullOrEmpty(req.ReservationCode) ? x.ReservationNumber == req.ReservationCode : true) &&
                                  (!string.IsNullOrEmpty(req.ProductCode) ? x.MaterialNumber == req.ProductCode : true) &&
                                  (!string.IsNullOrEmpty(req.Plant) ? x.Plant == req.Plant : true) &&
                                  //Search Ngày yêu cầu
                                  (req.ReqDate_From.HasValue ? h.RequestDate >= req.ReqDate_From : true) &&
                                  (req.ReqDate_To.HasValue ? h.RequestDate <= req.ReqDate_To : true) &&

                                  (string.IsNullOrEmpty(req.StatusReservation) || (req.StatusReservation == StatusReservation.Approved ? h.IsApproved == true : h.IsApproved == false || h.IsApproved == null))
                                orderby x.CreateTime descending, x.ReservationItemNumber descending
                                select x;

                //Search theo loại giao dịch kho
                //queryTemp = queryTemp.Where(x => listMovementType.Contains(x.ReservationHeader.MovementType));

                switch (req.StatusWarehouse)
                {
                    case StatusWarehouseTranfer.NotDelivered:
                        queryTemp = queryTemp.Where(x =>
                            x.ReservationHeader.IsApproved == true &&
                            !_context.WarehouseTranferModel.Any(e => e.ReservationId == x.ReservationId &&
                                                             e.SlocExportId.HasValue &&
                                                             e.SlocImportId.HasValue));
                        break;

                    case StatusWarehouseTranfer.Delivered:
                        queryTemp = queryTemp.Where(x =>
                            _context.WarehouseTranferModel.Any(e => e.ReservationId == x.ReservationId &&
                                                            e.SlocExportId.HasValue &&
                                                            e.SlocImportId.HasValue));
                        break;

                    default:
                        // Handle unexpected status if needed.
                        break;
                }

                var total = queryTemp.Count();

                var query = queryTemp
                                      .Skip(req.Paging.start)
                                      .Take(req.Paging.length);

                //Danh sách sản phẩm
                var listProductId = query.Select(x => x.MaterialNumber).ToArray();
                var listReservaionId = query.Select(x => x.ReservationId).ToArray();

                var materialsDb = _context.ProductModel.Where(x => listProductId.Contains(x.ProductCode)).AsNoTracking();
                var warehouseTransferDb = _context.WarehouseTranferModel.Where(w => listReservaionId.Contains((Guid)w.ReservationId));

                var response = query.Select(e => new TranferListResponse
                {
                    ReservationId = e.ReservationId,
                    //Nhà máy xuất
                    Plant = e.Plant,
                    //Kho xuất
                    StorageLocation = e.StorageLocation,
                    //Mã NVL
                    ReservationNumber = e.ReservationNumber,
                    //Trạng thái reservation
                    StatusReservation = e.ReservationHeader.IsApproved == true ? "Đã duyệt" : "Chưa duyệt",
                    //Số lượng yêu cầu
                    Quantity = e.ReqQuantity,
                    Unit = e.UnitEntry,
                    //Số SO
                    SO = e.SalesOrderNumber,
                    //Product code/name
                    ProductCodeAndName = $"{e.MaterialNumber} | {materialsDb.FirstOrDefault(x => x.ProductCode == e.MaterialNumber).ProductName}",
                    //WBS
                    WBS = e.WBSElement,
                    //Lệnh sản xuất
                    LSX = e.OrderNumber,
                    //Nhà máy nhập
                    RIPlant = e.RIPlant,
                    //Kho nhập
                    RIStorageLocation = e.RIStorageLocation,
                    //Số lô,
                    BatchNumber = e.BatchNumber,
                    //Reservation Item
                    Item = e.ReservationItemNumber,
                    //Đã chuyển kho: Reservation đã duyệt và đã có dữ liệu xuất kho & chuyển kho 
                    StatusWarehouse = e.ReservationHeader.IsApproved == true && warehouseTransferDb.Where(x => x.ReservationId == e.ReservationId && x.SlocExportId.HasValue && x.SlocImportId.HasValue).Any() ? MESP2Resource.DELIVERED : MESP2Resource.NOTDELIVERED,
                    //Loại giao dịch kho
                    MovementType = e.ReservationHeader.MovementType,
                    //Ngày yêu cầu
                    ReqDate = e.ReservationHeader.RequestDate,
                });

                var datatblModel = new DatatableViewModel()
                {
                    draw = req.Paging.draw,
                    start = req.Paging.start,
                    length = req.Paging.length,
                };

                //int filteredResultsCount = 0;
                //int totalResultsCount = 0;


                var RecordsFiltered = req.Paging.length;


                //var res = NewCustomSearchRepository.CustomSearchFunc<TranferListResponse>(datatblModel, out filteredResultsCount, out totalResultsCount, response, "stt");
                var res = response.ToList();

                if (res.Any())
                {
                    int i = datatblModel.start;
                    foreach (var item in res)
                    {
                        i++;
                        item.STT = i;
                    }
                }


                return Ok(new ApiSuccessResponse<List<TranferListResponse>>
                {
                    Data = res,
                    Draw = req.Paging.draw,
                    RecordsTotal = total,
                    RecordsFiltered = total
                });
            }
            catch (Exception ex)
            {
                throw new Exception(CommonResource.Msg_SystemError, ex.InnerException);
            }
        }
        #endregion
    }
}