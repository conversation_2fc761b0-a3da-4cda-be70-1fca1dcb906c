*&---------------------------------------------------------------------*
*& Include          ZPG_GANTTCHART_TOP
*&---------------------------------------------------------------------*
TABLES: afko, t001l, aufk, mara, vbak, stpo.

TYPES:
  BEGIN OF gty_afko,
    aufnr TYPE aufnr,
    zzlsx TYPE zde_lsx,
    gmein TYPE meins,
  END OF gty_afko.

DATA: gt_data  TYPE STANDARD TABLE OF zst_ppr02_1,
      gt_data2 TYPE STANDARD TABLE OF zst_ppr02_2,
      gt_alv   TYPE STANDARD TABLE OF zst_ppr02_1,
      gt_alv2  TYPE STANDARD TABLE OF zst_ppr02_2,
      gt_mm007 TYPE STANDARD TABLE OF zmes_tb_poso,
*      gt_gthao TYPE STANDARD TABLE OF ztb_g_tieuhao,
*      gt_athao TYPE STANDARD TABLE OF ztb_a_tieuhao,
      gt_ekkn  TYPE STANDARD TABLE OF ekkn,
      BEGIN OF gt_vbak OCCURS 0,
        vbeln TYPE vbeln,
        kunnr TYPE kunnr,
        auart TYPE auart,
      END OF gt_vbak,
      BEGIN OF gt_but000 OCCURS 0,
        partner   TYPE bu_partner,
        bpext     TYPE bu_bpext,
        name_org1 TYPE bu_nameor1,
      END OF gt_but000,
*      gt_co09b TYPE TABLE OF ztb_zco09_ns,
*      BEGIN OF gt_ck13n OCCURS 0,
*        matnr   TYPE matnr,
*        tvers   TYPE ck_tvers,
*        matnr_c TYPE matnr,
*        werks   TYPE werks_d,
*        wertn   TYPE ck_kwt,
*        fwaer   TYPE ck_twaer,
*        menge   TYPE menge_pos,
*        meeht    TYPE meins,
*      END OF gt_ck13n,
      BEGIN OF gt_mkal OCCURS 0,
        matnr TYPE csap_mbom-matnr,
        werks TYPE csap_mbom-werks,
        verid TYPE verid,
        stlal TYPE csap_mbom-stlal,
        stlan TYPE csap_mbom-stlan,
        aufnr TYPE aufnr,
        zzlsx TYPE zde_lsx,
        ausch TYPE ausch,
      END OF gt_mkal,
      BEGIN OF gt_bom OCCURS 0,
        matnr TYPE matnr,
        werks TYPE werks_d,
        stlnr TYPE stnum,
        stlal TYPE stlal,
        menge TYPE kmpmg,
        bmeng TYPE basmn,
        ausch TYPE kausf,
        meins TYPE meins,
        verid TYPE verid,
      END OF gt_bom,
      BEGIN OF gt_cbom OCCURS 0,
        zzlsx   TYPE zde_lsx,
        aufnr   TYPE aufnr,
        matnr   TYPE matnr,
        werks   TYPE werks_d,
        maktx   TYPE maktx,
        mtart   TYPE mtart,
        matkl   TYPE matkl,
        idnrk   TYPE idnrk,
        meins   TYPE meins,
        wgbez   TYPE wgbez,
        verid   TYPE verid,
        menge   TYPE basmn,
        ausch   TYPE kausf,
        wdmng   TYPE basmn,
        obmng   TYPE obmng,
        mtart_r TYPE mtart,
        matkl_r TYPE matkl,
        zzpspnr TYPE ps_psp_pnr,
        plifz   TYPE plifz,
      END OF gt_cbom,
      BEGIN OF gt_cbom2 OCCURS 0,
        zzlsx   TYPE zde_lsx,
        idnrk   TYPE idnrk,
        aufnr   TYPE aufnr,
        matnr   TYPE matnr,
        werks   TYPE werks_d,
        maktx   TYPE maktx,
        mtart   TYPE mtart,
        matkl   TYPE matkl,
        meins   TYPE meins,
        wgbez   TYPE wgbez,
        verid   TYPE verid,
        menge   TYPE basmn,
        ausch   TYPE kausf,
        wdmng   TYPE basmn,
        obmng   TYPE obmng,
        mtart_r TYPE mtart,
        matkl_r TYPE matkl,
        sldbg   TYPE gamng,
        sldbv   TYPE gamng,
        sldbb   TYPE gamng,
        sldbs   TYPE gamng,
        sldbt   TYPE gamng,
        sldbh   TYPE gamng,
        sldbp   TYPE gamng,
        sldbk   TYPE gamng,
        zttkx   TYPE gamng,
        zuutien TYPE dec10,
        zzpspnr TYPE ps_psp_pnr,
        plifz   TYPE plifz,
      END OF gt_cbom2,
      BEGIN OF gt_statu OCCURS 0,
        aufnr TYPE aufnr,
        stat  TYPE j_status,
        udate TYPE cddatum,
      END OF gt_statu,
*      BEGIN OF gt_kkbc OCCURS 0,
*        aufnr       TYPE aufnr,
*        kstar       TYPE kstar,
*        kstar_text  TYPE kkb_ktext,
*        matnr       TYPE matnr,
*        meinh       TYPE kkb_meinh_output,
*        herku       TYPE kkb_herku,
*        istmeng_g   TYPE kkb_ismgg,
*        istkost_g   TYPE istkost_g,
*        sois_kost_a TYPE kkbsois_kost_a,
*        waers       TYPE waers,
*        werks       TYPE werks_d,
*      END OF gt_kkbc,
*      BEGIN OF gt_marm OCCURS 0,
*        matnr TYPE matnr,
*        meins TYPE meins,
*        meinh TYPE lrmei,
*        umrez  TYPE umrez,
*        umren TYPE umren,
*      END OF gt_marm,
      BEGIN OF gt_resb OCCURS 0,
        matnr TYPE matnr,
        werks TYPE werks_d,
        vbeln TYPE vbeln,
        pernr TYPE p_pernr,
        vorna TYPE pad_vorna,
        nachn TYPE pad_nachn,
      END OF gt_resb,
*      BEGIN OF gt_mseg OCCURS 0,
*        matnr      TYPE matnr,
*        werks      TYPE werks_d,
*        aufnr      TYPE aufnr,
*        budat_mkpf TYPE budat,
*      END OF gt_mseg,
      BEGIN OF gt_matnr OCCURS 0,
        matnr TYPE matnr,
        werks TYPE werks_d,
        verid TYPE verid,
      END OF gt_matnr,
      BEGIN OF gt_aufnr OCCURS 0,
        zzlsx   TYPE zde_lsx,
        aufnr   TYPE aufnr,
        zzpspnr TYPE ps_psp_pnr,
      END OF gt_aufnr,
      gt_afko   TYPE TABLE OF gty_afko,
      gt_afko_t TYPE TABLE OF gty_afko.
.

TYPES: BEGIN OF ty_json_res,
         plant       TYPE werks_d,
         lsxdt       TYPE char50,
         dsx         TYPE char50,
         lsxsap      TYPE aufnr,
         thutuuutien TYPE dec10,
         potype      TYPE char10,
       END OF ty_json_res.
TYPES: gty_uutien TYPE STANDARD TABLE OF ty_json_res.

DATA: gt_uutien TYPE gty_uutien.

DATA: gr_aufnr TYPE RANGE OF afko-aufnr WITH HEADER LINE,
      gr_aufnn TYPE RANGE OF afko-aufnr WITH HEADER LINE,
      gr_werks TYPE RANGE OF t001l-werks WITH HEADER LINE,
      gr_werkn TYPE RANGE OF t001l-werks WITH HEADER LINE,
      gr_matnr TYPE RANGE OF mara-matnr WITH HEADER LINE,
      gr_cbmat TYPE RANGE OF mara-matnr WITH HEADER LINE,
      gr_bomat TYPE RANGE OF mara-matnr WITH HEADER LINE,
      gr_vbeln TYPE RANGE OF vbak-vbeln WITH HEADER LINE.


DATA: iexcluding TYPE  slis_t_extab.
DATA: xexcluding LIKE LINE OF iexcluding.