﻿using ISD.API.Core;
using ISD.API.Extensions;
using Microsoft.AspNetCore.Mvc;
using System;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiController]
    [ApiExplorerSettings(GroupName = "mobile")]
    [ISDWebAuthorization]
    public class WorkOrderController : ControllerBaseAPI
    {
        //public IActionResult Index()
        //{
        //    return View();
        //}

        [HttpGet("GetWorkOrderInfo")]
        public IActionResult GetWorkOrderInfo(string wo)
        {

            return Ok(new { });
        }
    }
}
