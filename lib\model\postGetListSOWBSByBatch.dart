class PostGetListSOWBSByBatch {
  String? productCode;
  String? plant;
  String? sloc;
  String? batchNumber;

  PostGetListSOWBSByBatch(
      {this.productCode, this.plant, this.sloc, this.batchNumber});

  PostGetListSOWBSByBatch.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    plant = json['plant'];
    sloc = json['sloc'];
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['plant'] = plant;
    data['sloc'] = sloc;
    data['batchNumber'] = batchNumber;
    return data;
  }
}