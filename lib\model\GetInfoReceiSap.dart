class GetInfoReceiSap {
  int? code;
  bool? isSuccess;
  DataGetInfoReceiSap? data;


  GetInfoReceiSap(
      {this.code,
        this.isSuccess,
        this.data});

  GetInfoReceiSap.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    data = json['data'] != null ? DataGetInfoReceiSap.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetInfoReceiSap {
  String? slocId;
  String? materialType;
  String? importExportType;
  String? materialGroup;
  String? materialStatus;
  String? companyCode;
  String? nsxhsd;
  String? skinType;
  String? skinColor;
  String? caseCode;

  DataGetInfoReceiSap(
      {this.slocId,
        this.materialType,
        this.importExportType,
        this.materialGroup,
        this.materialStatus,
        this.companyCode,
        this.nsxhsd,
        this.skinType,
        this.skinColor,
        this.caseCode});

  DataGetInfoReceiSap.fromJson(Map<String, dynamic> json) {
    slocId = json['slocId'];
    materialType = json['materialType'];
    importExportType = json['importExportType'];
    materialGroup = json['materialGroup'];
    materialStatus = json['materialStatus'];
    companyCode = json['companyCode'];
    nsxhsd = json['nsxhsd'].toString();
    skinType = json['skinType'];
    skinColor = json['skinColor'];
    caseCode = json['caseCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['slocId'] = slocId;
    data['materialType'] = materialType;
    data['importExportType'] = importExportType;
    data['materialGroup'] = materialGroup;
    data['materialStatus'] = materialStatus;
    data['companyCode'] = companyCode;
    data['nsxhsd'] = nsxhsd;
    data['skinType'] = skinType;
    data['skinColor'] = skinColor;
    data['caseCode'] = caseCode;
    return data;
  }
}