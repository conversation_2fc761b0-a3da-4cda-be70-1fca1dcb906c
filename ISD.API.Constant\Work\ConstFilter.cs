﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISD.API.Constant
{
    public class ConstFilter
    {
        public static string Reporter = "Reporter";
        public static string Assignee = "Assignee";
        public static string ProfileId = "ProfileId";
        public static string WorkFlowId = "WorkFlowId";
        public static string ContactId = "ContactId";
        public static string CreateBy = "CreateBy";
        public static string PriorityCode = "PriorityCode";
        public static string ReceiveDate = "ReceiveDate";
        public static string StartDate = "StartDate";
        public static string EstimateEndDate = "EstimateEndDate";
        public static string EndDate = "EndDate";
        public static string CreateDate = "CreateDate";
        //Đơn vị thi công
        public static string ConstructionUnit = "ConstructionUnit";
        //Lỗi hay gặp
        public static string CommonMistakeCode = "CommonMistakeCode";
        //Hình thức bảo hành
        public static string ErrorTypeCode = "ErrorTypeCode";
        //Phương thức xử lý
        public static string ErrorCode = "ErrorCode";
        //Nhóm vật tư
        public static string ProductCategoryCode = "ProductCategoryCode";
        //Các lỗi thường gặp
        public static string UsualErrorCode = "UsualErrorCode";
        //Mã màu
        public static string ProductColorCode = "ProductColorCode";
        //Nhóm KH
        public static string CustomerGroupCode = "CustomerGroupCode";
        //NV kinh doanh
        public static string SalesSupervisorCode = "SalesSupervisorCode";
        //Phòng ban
        public static string DepartmentCode = "DepartmentCode";
        //Phân loại chuyến thăm
        public static string VisitTypeCode = "VisitTypeCode";
        //Trạng thái hoạt động
        public static string Actived = "Actived";
        //Nhóm VT
        public static string CategoryId = "CategoryId";
        //Trung tâm bảo hành
        public static string ServiceTechnicalTeamCode = "ServiceTechnicalTeamCode";
        //NV kết thúc
        public static string CompletedEmployee = "CompletedEmployee";
        //ý kiến kh
        public static string Property5 = "Property5";
        //mã sap sp
        public static string ERPProductCode = "ERPProductCode";
        //mã sap phụ kiện
        public static string ERPAccessoryCode = "ERPAccessoryCode";
        //loại phụ kiện
        public static string AccessoryTypeCode = "AccessoryTypeCode";
        //Loại địa chỉ
        public static string AddressType = "AddressType";
        //Khu vực
        public static string SaleOfficeCode = "SaleOfficeCode";
        //Tỉnh/Thành phố
        public static string ProvinceId = "ProvinceId";
        //Quận/Huyện
        public static string DistrictId = "DistrictId";
        //Phường/Xã
        public static string WardId = "WardId";

        //Xem công việc theo
        public static string CaNhan = "CaNhan";
        public static string ChiNhanh = "ChiNhanh";
        public static string CongTy = "CongTy";
        public static string TatCa = "TatCa";
    }
}
