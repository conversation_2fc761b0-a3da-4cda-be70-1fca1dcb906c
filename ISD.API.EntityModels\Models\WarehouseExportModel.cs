﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("WarehouseExportModel", Schema = "MESP2")]
    public partial class WarehouseExportModel
    {
        public WarehouseExportModel()
        {
            WarehouseExportDetailModel = new HashSet<WarehouseExportDetailModel>();
        }

        [Key]
        public Guid WarhouseExportId { get; set; }
        public Guid? RawMaterialCardId { get; set; }
        [StringLength(50)]
        public string Plant { get; set; }
        [StringLength(50)]
        public string MovementType { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocumentDate { get; set; }
        public int? DateKey { get; set; }
        public Guid? ReferenceDocumentId { get; set; }
        [StringLength(50)]
        public string SONumber { get; set; }
        [StringLength(50)]
        public string SOLine { get; set; }
        [StringLength(50)]
        public string WBS { get; set; }
        [StringLength(50)]
        public string Batch { get; set; }
        public Guid? SlocId { get; set; }
        public Guid? StorageBinId { get; set; }
        public Guid? HeaderIdSAP { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? Quantity { get; set; }
        [StringLength(50)]
        public string Unit { get; set; }
        [StringLength(50)]
        public string StepCode { get; set; }
        public bool? IsPhanBoSAP { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool? Actived { get; set; }

        [ForeignKey("SlocId")]
        [InverseProperty("WarehouseExportModel")]
        public virtual SlocModel Sloc { get; set; }
        [ForeignKey("StorageBinId")]
        [InverseProperty("WarehouseExportModel")]
        public virtual StorageBinModel StorageBin { get; set; }
        [InverseProperty("WarhouseExport")]
        public virtual ICollection<WarehouseExportDetailModel> WarehouseExportDetailModel { get; set; }
    }
}