import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../model/GetListCatalog.dart';

class DropdownCatalog extends StatelessWidget {
  final DataGetListCatalog? dataGetListCatalog;
  final ValueChanged<DataGetListCatalog?> onchange;
  final List<DataGetListCatalog>? lsCatalog ;
  const DropdownCatalog({Key? key,required this.dataGetListCatalog, required this.onchange,required this.lsCatalog}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w,vertical: 5.h),
          decoration: BoxDecoration(
            border: Border.all(
                color: Colors.grey.shade400,
                width: 0.5.w),
            borderRadius:
            BorderRadius.circular(3.r),
          ),
          child: DropdownButtonHideUnderline(
              child: DropdownButton<DataGetListCatalog>(
                menuMaxHeight: 250.h,
                isDense: true,
                itemHeight: null,
                isExpanded: true,
                value: dataGetListCatalog,
                iconSize: 15.sp,
                style: const TextStyle(color: Colors.black),
                onChanged: onchange,
                items: lsCatalog!.map((DataGetListCatalog product) {
                  return DropdownMenuItem<DataGetListCatalog>(
                      value: product,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 5.h),
                        child: Text(
                          product.value.toString(),
                          style: TextStyle(
                              color: Colors.black,
                              fontSize: 11.sp),
                        ),
                      ));
                }).toList(),
                selectedItemBuilder: (BuildContext context) {
                  return lsCatalog!.map<Widget>((DataGetListCatalog product) {
                    return Text(product.value.toString(), style: TextStyle(
                        color: Colors.black,
                        fontSize: 11.sp), overflow: TextOverflow.ellipsis);
                  }).toList();
                },
              )
          ),
    );
  }
}
