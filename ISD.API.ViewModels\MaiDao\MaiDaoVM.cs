using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ISD.API.ViewModels.MaiDao
{
    public class MaiDaoRecordVM
    {
        public Guid? MaiDaoId { get; set; }
        public string Date { get; set; }
        public string EquipmentCode { get; set; }
        public string EquipmentName { get; set; }
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
        public string MaterialBatch { get; set; }
        public string OperationType { get; set; }
        public string EmployeeCodes { get; set; }
        public string EmployeeNames { get; set; }
        public string RequestingEmployeeCode { get; set; }
        public string RequestingEmployeeName { get; set; }
        public string Note { get; set; }
        public string Status { get; set; }
        public DateTime? CreatedDate { get; set; }
        public Guid? CreateBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdateBy { get; set; }
        public string CompanyCode { get; set; }
    }

    public class MaiDaoSearchModel
    {
        public string EquipmentCode { get; set; }
        public string MaterialCode { get; set; }
        public string OperationType { get; set; }
        public string Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        [Required]
        public string CompanyCode { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class MaiDaoListResponse
    {
        public List<MaiDaoRecordVM> Data { get; set; }
        public int TotalCount { get; set; }
        public bool Status { get; set; }
        public string Message { get; set; }
    }

    public class MaiDaoResponse
    {
        public MaiDaoRecordVM Data { get; set; }
        public bool Status { get; set; }
        public string Message { get; set; }
    }

    public class EquipmentItem
    {
        public string EquipmentCode { get; set; }
        public string EquipmentName { get; set; }
    }

    public class MaterialItem
    {
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
    }

    public class EmployeeItem
    {
        public string EmployeeCode { get; set; }
        public string EmployeeName { get; set; }
    }
} 