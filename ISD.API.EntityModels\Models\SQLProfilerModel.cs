﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    public partial class SQLProfilerModel
    {
        [Key]
        public int RowNumber { get; set; }
        public int? EventClass { get; set; }
        [Column(TypeName = "ntext")]
        public string TextData { get; set; }
        [StringLength(128)]
        public string ApplicationName { get; set; }
        [StringLength(128)]
        public string NTUserName { get; set; }
        [StringLength(128)]
        public string LoginName { get; set; }
        public int? CPU { get; set; }
        public long? Reads { get; set; }
        public long? Writes { get; set; }
        public long? Duration { get; set; }
        public int? ClientProcessID { get; set; }
        public int? SPID { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndTime { get; set; }
        [Column(TypeName = "image")]
        public byte[] BinaryData { get; set; }
    }
}