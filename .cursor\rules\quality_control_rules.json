{"rules": [{"name": "QC Screen Naming Convention", "description": "Quality control screens should follow the naming pattern: '<PERSON><PERSON><PERSON><PERSON>{Type}' or 'Phieu{Type}' for main screens and add 'Detail' suffix for detail screens", "pattern": "^(<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON>)[A-Za-z0-9]+(\\.dart|Detail\\.dart)$", "examples": ["BaoCaoDauVao.dart", "BaoCaoQCMauDetail.dart", "PhieuKCSCongDoan.dart"]}, {"name": "QC Model Structure", "description": "Quality control models should include standard fields like qualityControl, resultList, etc.", "pattern": "qualityControl\\?:\\s*QualityControl\\?;", "examples": ["qualityControl?: QualityControl?;", "QualityControl? qualityControl;"]}, {"name": "QC State Variables", "description": "Quality control screens should define state variables with standardized prefixes (_controller, _selected, _ls for lists)", "pattern": "(_controller|_selected|_ls)[A-Za-z]+\\s*=", "examples": ["_controllerTongSoSanPhamLoi = TextEditingController();", "_selectedResultDetail = _lsResultList[indexResult];", "_lsThongTinKiemTra = [];"]}, {"name": "QC Validation Methods", "description": "Quality control validation methods should follow the 'canSendQualityControl' pattern and return boolean", "pattern": "bool\\s+canSendQualityControl\\(\\)", "examples": ["bool canSendQualityControl() {", "bool canSendQualityControl() => _errorQuantityCheck == false && ..."]}, {"name": "QC Update Methods", "description": "Methods to update form fields should follow the 'update{FieldName}' pattern", "pattern": "void\\s+update[A-Za-z]+\\(\\)", "examples": ["void updateTongSoSanPhamLoi() {", "void updateSelectedResultDetail() {"]}, {"name": "QC API Integration", "description": "API calls for quality control should use the QualityControlFunction class methods", "pattern": "QualityControlFunction\\.(post|get|fetch)[A-Za-z]+\\(", "examples": ["QualityControlFunction.postQualityControlQCMau(", "QualityControlFunction.getDefaultKCSFilter("]}, {"name": "QC Form Validation", "description": "Form validation should check specific error flags and show appropriate toast messages", "pattern": "if\\s+\\(_error[A-Za-z]+\\)\\s*{\\s*showToast\\(", "examples": ["if (_errorQuantityCheck) {\n  showToast(context: context, message: \"<PERSON><PERSON> lòng nhập SL kiểm tra\");\n  ret = false;\n}", "if (_errorTestMethodDetail) {\n  showToast(context: context, message: \"<PERSON>ui lòng chọn phương pháp KT\");\n  ret = false;\n}"]}, {"name": "QC Thai Language UI", "description": "UI messages for Thai users should be in Thai language", "pattern": "message:\\s*\"[^\"]*[ก-๙]+[^\"]*\"", "examples": ["message: \"กรุณาเลือกวิธีการตรวจสอบ\"", "message: \"ไม่พบข้อมูลการตรวจสอบ\""]}, {"name": "QC Vietnamese Language UI", "description": "UI messages for Vietnamese users should be in Vietnamese language", "pattern": "message:\\s*\"[^\"]*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+[^\"]*\"", "examples": ["message: \"<PERSON><PERSON> lòng nhập SL kiểm tra\"", "message: \"<PERSON><PERSON> lòng chọn kết quả\""]}, {"name": "QC Photo Documentation", "description": "Photo documentation code should use standardized image picker patterns", "pattern": "(ImagePicker|_pickerImage|lsFile(Header|ThongTinKiemTra|HinhAnhLoi))", "examples": ["ImagePicker _pickerImage = ImagePicker();", "List<File> _lsFileHeader = [];", "_lsFileThongTinKiemTra.add([]);"]}, {"name": "QC Model Field Naming", "description": "Class fields in QC models should follow camelCase naming with descriptive names", "pattern": "(qualityControl|testMethod|samplingLevel|inspectionQuantity|tongSoSanPhamLoi|result)", "examples": ["QualityControl? qualityControl;", "double? tongSoSanPhamLoi;", "String? result;"]}]}