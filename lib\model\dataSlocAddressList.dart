import 'getQuantityMaterialUsedShift.dart';


// class DataSlocAddressList{
//   List<DataGetSlocByProduct>? lsDataGetSlocByProduct;
//   DataGetSlocByProduct? selectedDataGetSlocByProduct;
//   DataSlocAddressList({this.lsDataGetSlocByProduct,this.selectedDataGetSlocByProduct});
// }
class DataSlocQuantityUsedShift{
  List<DataQuantityMaterialUsedShift>? dataQuantityMaterialUsedShift;
  DataQuantityMaterialUsedShift? selectedDataQuantityMaterialUsedShift;
  DataSlocQuantityUsedShift({this.dataQuantityMaterialUsedShift, this.selectedDataQuantityMaterialUsedShift});
}
