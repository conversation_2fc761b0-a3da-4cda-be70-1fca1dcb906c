# Downtime Feature Technical Documentation

## Feature Overview
The Downtime feature is a comprehensive system for tracking and managing production downtime events in a manufacturing environment. It allows users to record, track, and manage periods when production equipment or processes are not operating.

## Features
- Create and manage downtime records
- Track start and end times with automatic duration calculation
- Assign responsibility to departments and individuals
- Record reasons for downtime
- Filter and search downtime records
- View detailed history of changes
- Status tracking and verification workflow

## Frontend Architecture

### 1. Routing Configuration (`route.dart`)
The Downtime feature is defined in the application routing system with two main routes:

```dart
// List screen route
case '/Downtime':
  return MaterialPageRoute(
    settings: const RouteSettings(name: '/Downtime'),
    builder: (_) {
      CommonScreenUserArgument argument = args as CommonScreenUserArgument;
      return DowntimeList(
        dateTimeOld: argument.dateTimeOld.toString(),
        user: argument.user,
      );
    },
  );

// Detail screen route
case '/DowntimeDetail':
  return MaterialPageRoute(
    settings: const RouteSettings(name: '/DowntimeDetail'),
    builder: (_) {
      final Map<String, dynamic> argument = args as Map<String, dynamic>;
      return DowntimeDetail(
        id: argument['id'] as String,
        dateTimeOld: argument['dateTimeOld'] as String,
        user: argument['user'] as DataUser,
      );
    },
  );
```

### 2. List Screen (`DowntimeList.dart`)
The list screen displays all downtime records with filtering and search capabilities:

Key components:
- **Main UI**: Card-based list showing downtime records with key information
- **Pull-to-refresh**: Updates list data without full page reload
- **Filtering**: Accessed via drawer with filtering options
- **Navigation**: Create new records or view details of existing ones
- **Status visualization**: Color-coded indicators for different statuses
- **Error handling**: Network and data loading error management

Implementation details:
- Uses `RefreshIndicator` for pull-to-refresh functionality
- Displays time spans with automatic duration calculation
- Implements responsive card layout with department/step information
- Uses the `endDrawer` approach for filtering panel
- Navigation to detail screen with route parameters

### 3. Search/Filter Component (`FilterListDowntime.dart`)
The search filter component provides a comprehensive interface for filtering downtime records:

Key features:
- **Company selection**: Filter by manufacturing plant
- **Department filtering**: Supports typeahead for department selection
- **Step/Workstation filtering**: Typeahead for production steps
- **Reason search**: Free text search in reason field
- **Date range selection**: Predefined periods or custom date range
- **State management**: Remembers and applies previous filter settings

Implementation details:
- Uses `AutoCompleteField` for typeahead functionality
- Implements dynamic date selection with platform-specific pickers
- Fetches reference data from API (departments, steps)
- Validates date ranges to ensure proper chronological order
- Manages filter state across screen refreshes

### 4. Detail Screen (`DowntimeDetail.dart`)
The detail screen handles both creation and editing of downtime records:

Key components:
- **Form layout**: Structured form with validation
- **Date/time pickers**: Custom pickers with proper formatting
- **Autocomplete fields**: For departments, steps, and employees
- **Employee selection**: Multiple employee selection with typeahead
- **Validation**: Required fields and time range validation
- **History view**: Shows change history for existing records
- **Status management**: Status updates with proper workflow

Implementation details:
- Uses `FormLayout` for consistent form presentation
- Implements platform-specific date/time pickers
- Formats dates and times for UI display vs. API communication
- Handles employee selection with multi-select capability
- Shows validation errors and success messages
- Manages record history display and formatting

### 5. Service Layer (`downtimeFunction.dart`)
The service layer handles all API communications for the Downtime feature:

Key functionality:
- **API integration**: Communicates with backend endpoints
- **Data transformation**: Converts between UI models and API formats
- **Error handling**: Manages network and server errors
- **Mock data support**: Fallback to mock data when needed for development

Implementation details:
- Uses HTTP client for API calls
- Implements environment-aware base URL selection
- Handles token-based authentication
- Provides both mock and real API implementations
- Converts date/time formats between frontend and backend

### 6. Data Models (`downtimeModel.dart`)
The data models define the structure of downtime data:

Key models:
- **DowntimeModel**: Container for list responses
- **DowntimeRecord**: Main data structure for individual records
- **DowntimeHistory**: Structure for change history entries
- **DowntimeSearchModel**: Parameters for search/filter operations
- **EmployeeRecord**, **DepartmentItem**, **StepCodeItem**: Reference data

## Backend Architecture

### 1. API Controller (`DowntimeController.cs`)
The controller defines all API endpoints for the Downtime feature:

Key endpoints:
- **POST /DowntimeList**: Retrieve filtered list of downtime records
- **GET /{id}**: Get details of specific downtime record
- **POST /CreateDowntime**: Create new downtime record
- **PUT /{id}**: Update existing downtime record
- **GET /{id}/history**: Get change history for a record
- **GET /GetListDepartment**: Get departments for reference data
- **GET /GetListStepCode**: Get step codes for reference data
- **GET /GetEmployees**: Get employees for reference data

Implementation details:
- Uses dependency injection for repository access
- Implements proper HTTP status codes and responses
- Validates input models before processing
- Returns standardized API response format
- Includes comprehensive error handling
- Provides adequate logging

### 2. Repository Layer (`DowntimeRepository.cs`)
The repository handles data access and business logic:

Key responsibilities:
- **Data access**: Retrieves and manipulates data in the database
- **Query construction**: Builds queries with proper filtering
- **Data transformation**: Maps between entity models and view models
- **Business rules**: Implements workflow and validation rules
- **History tracking**: Records changes to downtime records

Implementation details:
- Uses Entity Framework for data access
- Implements query optimization with proper includes
- Handles pagination for list operations
- Records history entries for status changes
- Manages timestamps and user tracking
- Provides comprehensive error handling

### 3. Database Model (`DowntimeModel.cs`)
The database model defines the table structure for downtime records:

Key fields:
- **DowntimeId**: Primary key (GUID)
- **Date**: Date of the downtime event
- **DepartmentCode**: Department/team code
- **StepCode**: Production step or workstation
- **StartTime/EndTime**: Time range of the downtime
- **Reason**: Cause of the downtime
- **ResponsibleTeam**: Team responsible for the issue
- **ResponsibleDepartment**: Percentage responsibility
- **Status**: Current status in workflow
- **CompanyCode**: Factory/company identifier
- **Audit fields**: Created/Updated dates and users

### 4. View Models (`DowntimeViewModels.cs`)
The view models define data structures for API communication:

Key models:
- **DowntimeViewModel**: Complete record with all fields
- **DowntimeHistoryViewModel**: Change history records
- **DowntimeSearchModel**: Search/filter parameters
- **DowntimeCreateModel**: Data for new record creation
- **DowntimeUpdateModel**: Data for record updates
- **DowntimeMasterData**: Collection of reference data

## Data Flow

1. **List View Workflow**:
   - User navigates to `/Downtime` route
   - `DowntimeList` component initializes
   - Default filters are applied (current week)
   - `fetchDowntimeList` service call is made
   - Data is displayed in card-based list
   - User can filter, refresh, or view details

2. **Detail View Workflow**:
   - User navigates to `/DowntimeDetail` route
   - `DowntimeDetail` component initializes
   - If editing, `fetchDowntimeDetail` service call loads data
   - Master data is loaded (departments, steps, employees)
   - Form is populated with data (or empty for new record)
   - User edits and submits form
   - `saveDowntime` service call creates/updates record

3. **Filter Workflow**:
   - User opens filter drawer
   - `FilterListDowntime` component initializes
   - Master data is loaded for typeahead suggestions
   - User selects filter criteria
   - `DowntimeSearchModel` is created with parameters
   - Filter is applied, triggering new API call
   - List is updated with filtered results

## Status Workflow

1. **Created**:
   - Initial status when record is first created
   - Editable by creator
   - Can transition to "Pending"

2. **Pending**:
   - Awaiting review/approval
   - Limited editing capabilities
   - Can transition to "Approved" or "Rejected"

3. **Approved**:
   - Record has been verified and approved
   - Read-only
   - Final state in standard workflow

4. **Rejected**:
   - Record has been rejected
   - May require revision
   - Can be updated and resubmitted

## Security Considerations

1. **Authentication**:
   - All API endpoints require valid authentication token
   - Token is passed in HTTP headers
   - Expired tokens are properly handled

2. **Authorization**:
   - Role-based access control
   - Record creators have edit rights
   - Approvers have status change rights
   - Read access is more broadly available

3. **Data Validation**:
   - Required fields are validated
   - Date/time ranges are validated
   - API inputs are sanitized
   - Client-side validation is reinforced by server-side checks

## Performance Optimizations

1. **Data Loading**:
   - Pagination is implemented (20 items per page)
   - Pull-to-refresh reduces full page reloads
   - Master data is cached where appropriate

2. **UI Responsiveness**:
   - Async operations for data loading
   - Loading state indicators
   - Optimized list rendering

3. **Network Usage**:
   - Minimal API calls
   - Efficient data structures
   - Error handling with retry logic

## Integration Points

The Downtime feature integrates with several other systems:

1. **Common date selection** - Reuses filter date components
2. **User management** - For authentication and user information
3. **Company/plant selection** - For multi-factory environments
4. **Employee directory** - For responsibility assignment

## Future Enhancements

Planned improvements include:

1. **UI/UX**:
   - Enhanced filtering capabilities
   - Improved time selection
   - Better status visualization

2. **Features**:
   - Batch operations
   - Export functionality
   - Advanced reporting

3. **Integration**:
   - Additional API endpoints
   - Enhanced master data
   - Expanded workflow options
