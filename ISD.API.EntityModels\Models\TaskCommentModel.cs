﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TaskCommentModel", Schema = "Task")]
    public partial class TaskCommentModel
    {
        public TaskCommentModel()
        {
            Comment_File_Mapping = new HashSet<Comment_File_Mapping>();
        }

        [Key]
        public Guid TaskCommentId { get; set; }
        public Guid? TaskId { get; set; }
        [StringLength(4000)]
        public string Comment { get; set; }
        [StringLength(20)]
        public string ProcessUser { get; set; }
        public Guid? FromStatusId { get; set; }
        public Guid? ToStatusId { get; set; }
        public Guid? CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }

        [InverseProperty("TaskComment")]
        public virtual ICollection<Comment_File_Mapping> Comment_File_Mapping { get; set; }
    }
}