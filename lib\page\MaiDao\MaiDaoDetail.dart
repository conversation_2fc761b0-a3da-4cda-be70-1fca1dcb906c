import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/element/LoadingScreen.dart';
import 'package:ttf/model/userModel.dart';
import 'package:ttf/element/FormLayout.dart';
import 'package:ttf/element/QualityFormFields.dart';
import 'package:ttf/element/ApiSuggestionField.dart';
import 'package:ttf/element/GenericQRScanner.dart';
import 'package:ttf/page/Downtime/element/AutoCompleteField.dart';
import 'package:ttf/page/Downtime/element/EmployeeTypeAhead.dart';
import 'package:ttf/model/maiDaoModel.dart';
import 'package:ttf/model/FilterLsQC.dart';
import 'package:ttf/repository/function/loginFunction.dart';
import 'package:ttf/screenArguments/ScreenArgumentNavigatorBar.dart';
import 'package:ttf/screenArguments/screenArgumentQRPage.dart';
import 'package:ttf/utils/ui_helpers.dart';
import 'package:ttf/utils/user_helper.dart';
import '../LostConnect.dart';
import 'package:ttf/repository/function/maiDaoFunction.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

class MaiDaoDetail extends StatefulWidget {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;
  final MaiDaoRecord? maiDaoRecord;

  MaiDaoDetail({
    Key? key,
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
    this.maiDaoRecord,
  }) : super(key: key) {
    debugPrint("MaiDaoDetail created - id: $id, has maiDaoRecord: ${maiDaoRecord != null}, viewMode: $viewMode");
    if (maiDaoRecord != null) {
      debugPrint("  -> maiDaoRecord ID: ${maiDaoRecord!.id}, Equipment: ${maiDaoRecord!.equipmentName}, Operation: ${maiDaoRecord!.operationType}");
    }
  }

  @override
  _MaiDaoDetailState createState() => _MaiDaoDetailState();

  bool get isViewMode => viewMode == true;
}

class MaiDaoDetailArguments {
  final String id;
  final String dateTimeOld;
  final DataUser user;
  final bool? viewMode;
  final MaiDaoRecord? maiDaoRecord;

  MaiDaoDetailArguments({
    required this.id,
    required this.dateTimeOld,
    required this.user,
    this.viewMode,
    this.maiDaoRecord,
  });

  factory MaiDaoDetailArguments.fromMap(Map<String, dynamic> map) {
    debugPrint("Creating MaiDaoDetailArguments from map: $map");
    final maiDaoRecord = map['maiDaoRecord'] as MaiDaoRecord?;
    debugPrint("  -> maiDaoRecord: ${maiDaoRecord?.id ?? 'null'}");

    return MaiDaoDetailArguments(
      id: map['id'] as String,
      dateTimeOld: map['dateTimeOld'] as String,
      user: map['user'] as DataUser,
      viewMode: map['viewMode'] as bool?,
      maiDaoRecord: maiDaoRecord,
    );
  }
}

// Create a custom adapter for EmployeeTypeAhead
class EmployeeTypeAhead extends StatelessWidget {
  final bool enabled;
  final bool showRemoveButton;
  final List<EmployeeRecord> masterDataList;
  final List<String> excludedEmployeeIds;
  final Function(EmployeeRecord) onSuggestionSelected;
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  final int employeeIndex;
  final Function? onAddEmployee;
  final Function? onRemoveEmployee;
  final int totalEmployees;
  final TextStyle? textStyle;

  const EmployeeTypeAhead({
    Key? key,
    required this.enabled,
    required this.showRemoveButton,
    required this.masterDataList,
    required this.excludedEmployeeIds,
    required this.onSuggestionSelected,
    required this.controller,
    required this.onChanged,
    required this.employeeIndex,
    required this.totalEmployees,
    this.onAddEmployee,
    this.onRemoveEmployee,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLastItem = employeeIndex == totalEmployees - 1;

    return Container(
      margin: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          Expanded(
            flex: 5,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(3.r),
                color: Colors.white,
              ),
              child: TypeAheadField<EmployeeRecord>(
                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                  constraints: BoxConstraints(
                    maxHeight: 200.h,
                  ),
                ),
                textFieldConfiguration: TextFieldConfiguration(
                  enabled: enabled,
                  controller: controller,
                  style: textStyle ?? TextStyle(fontSize: 12.sp),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: onChanged,
                ),
                suggestionsCallback: (pattern) {
                  return masterDataList
                      .where((item) =>
                          (item.employeeName?.toLowerCase().contains(pattern.toLowerCase()) ?? false) ||
                          (item.employeeCode?.toLowerCase().contains(pattern.toLowerCase()) ?? false))
                      .where((item) => !excludedEmployeeIds.contains(item.employeeCode))
                      .toList();
                },
                itemBuilder: (context, EmployeeRecord suggestion) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                    child: Text(
                      "${suggestion.employeeCode} - ${suggestion.employeeName}",
                      style: TextStyle(fontSize: 12.sp),
                    ),
                  );
                },
                onSuggestionSelected: onSuggestionSelected,
                noItemsFoundBuilder: (context) => Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                  child: Text(
                    'Không tìm thấy kết quả',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 4.w),
          // Only show add/remove buttons when enabled (not in view mode)
          if (enabled)
            IconButton(
              padding: EdgeInsets.all(4.h),
              constraints: BoxConstraints(maxHeight: 30.h),
              iconSize: 17.sp,
              color: isLastItem ? Colors.blue : Colors.red,
              icon: Icon(isLastItem ? Icons.add : Icons.remove),
              onPressed: () {
                if (isLastItem) {
                  onAddEmployee?.call();
                } else {
                  onRemoveEmployee?.call();
                }
              },
            ),
        ],
      ),
    );
  }
}

class FormInputField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool enabled;
  final bool required;
  final int maxLines;
  final bool isBorder;

  const FormInputField({
    Key? key,
    required this.label,
    required this.controller,
    this.enabled = true,
    this.required = true,
    this.maxLines = 1,
    this.isBorder = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Expanded(
          flex: 3,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
          flex: 7,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 8.h),
            decoration: BoxDecoration(
              border: isBorder ? Border.all(width: 0.5, color: Colors.grey.shade400) : null,
              borderRadius: BorderRadius.circular(3.r),
              color: Colors.white, // Always white background
            ),
            child: TextField(
              controller: controller,
              enabled: enabled,
              maxLines: maxLines,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black, // Always black text
              ),
              decoration: const InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.zero,
                border: InputBorder.none,
                filled: false,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _MaiDaoDetailState extends State<MaiDaoDetail> {
  void _noOp(String _) {} // Helper function for disabled onChanged
  void _noOpSuggestion(String _) {} // Helper function for disabled onSuggestionSelected
  void _noOpEmployeeRecord(EmployeeRecord _) {} // Helper function for disabled employee selection

  bool _isLoading = true;
  bool _isNotWifi = false;
  bool _isError = false;
  bool _isSaving = false;
  bool _isLoadingEquipment = false;
  bool _isLoadingMaterial = false;
  bool _isDummyData = false;
  // bool _isDummyData = true; // Flag to track if we're using dummy data

  // Add a variable to track the current record ID
  String _currentRecordId = '';

  // Permission variables
  bool _hasConfirmPermission = false;
  bool _hasCompletedPermission = false;
  DataUser? _userData;

  // Nhà máy (Plant/Company) variables
  List<SalesOrgCodes>? _lsNhaMayOptions;
  SalesOrgCodes? _selectedNhaMay;

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _equipmentCodeController = TextEditingController();
  final TextEditingController _equipmentNameController = TextEditingController();
  final TextEditingController _materialCodeController = TextEditingController();
  final TextEditingController _materialNameController = TextEditingController();
  final TextEditingController _materialBatchController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();

  String? _selectedOperationType;
  final List<String> _operationTypes = ["Mài dao", "Đắp dao"];

  // Add status-related variables
  String? _status;
  final List<String> _statusOptions = [
    'Created',
    'Confirmed',
    'Completed',
    'Cancelled',
  ];

  List<EmployeeRecord> _allEmployees = [];
  List<EmployeeRecord?> _selectedEmployees = [];
  List<TextEditingController> _employeeControllers = [];

  // Requesting Employee (single)
  final TextEditingController _requestingEmployeeController = TextEditingController();
  EmployeeRecord? _selectedRequestingEmployee;

  List<String> get _selectedEmployeeIds =>
      _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeCode).where((id) => id != null).map((id) => id!).toList();

  bool get isReadOnlyByStatus {
    // Make records read-only if they are completed or cancelled
    return _status == 'Completed' || _status == 'Cancelled';
  }

  // Helper getter that combines explicit view mode and status-based read-only
  bool get isEffectivelyReadOnly {
    return widget.isViewMode || isReadOnlyByStatus;
  }

  // Helper method to determine if a field should be editable
  bool _isFieldEditable(String fieldName) {
    // If explicitly in view mode, nothing is editable
    if (widget.isViewMode) return false;

    // If using dummy data, nothing should be editable
    if (_isDummyData) return false;

    // If record is completed or cancelled, nothing is editable
    if (_status == 'Completed' || _status == 'Cancelled') return false;

    // For new records (no ID), all fields are editable
    if (_currentRecordId.isEmpty) return true;

    // For existing records with "Created" status, only note field is editable
    if (_status == 'Created') {
      return fieldName == 'note';
    }

    // For confirmed status, no fields are editable (will be handled by status-based logic)
    return false;
  }

  // Helper method specifically for the note field
  bool get _isNoteEditable {
    return _isFieldEditable('note');
  }

  // Helper method for general form fields (not note)
  bool get _areGeneralFieldsEditable {
    return _isFieldEditable('general');
  }

  @override
  void initState() {
    super.initState();

    // Initialize current record ID
    _currentRecordId = widget.id;

    // Debug all props on initialization
    debugPrint("================== INITIALIZING DETAIL STATE ==================");
    debugPrint("ID: ${widget.id}, isViewMode: ${widget.isViewMode}");
    debugPrint("Has maiDaoRecord: ${widget.maiDaoRecord != null}");
    if (widget.maiDaoRecord != null) {
      final record = widget.maiDaoRecord!;
      debugPrint("MaiDaoRecord details:");
      debugPrint("  ID: ${record.id}");
      debugPrint("  Equipment: ${record.equipmentCode} - ${record.equipmentName}");
      debugPrint("  Material: ${record.materialCode} - ${record.materialName}");
      debugPrint("  Operation: ${record.operationType}");
      debugPrint("  Employees: ${record.employeeCodes}");
      debugPrint("  Date: ${record.date}");
    } else {
      debugPrint("NO RECORD PASSED TO DETAIL VIEW");
    }

    // Check if this is dummy data
    _isDummyData = widget.maiDaoRecord?.id?.startsWith('dummy') == true;
    debugPrint("Is dummy data: $_isDummyData");

    // Initialize with one empty employee field
    _addEmployeeField();

    // Initialize with user permission check
    _initializeWithUserData();
  }

  void _addEmployeeField() {
    setState(() {
      _employeeControllers.add(TextEditingController());
      _selectedEmployees.add(null);
    });
  }

  void _removeEmployeeField(int index) {
    setState(() {
      _employeeControllers[index].dispose();
      _employeeControllers.removeAt(index);
      _selectedEmployees.removeAt(index);
    });
  }

  Future<void> _initializeWithUserData() async {
    try {
      // Get user info first
      _userData = await UserHelper().getUserData();

      if (_userData != null) {
        debugPrint("User data loaded: ${_userData!.userName}");

        // Check permissions for MaiDao screen
        _hasConfirmPermission = _userData!.permission?.mobileScreenPermissionModel
                ?.any((screen) => screen.mobileScreenCode == "MaiDao" && screen.functionId == "M_CONFIRM") ??
            false;

        _hasCompletedPermission = _userData!.permission?.mobileScreenPermissionModel
                ?.any((screen) => screen.mobileScreenCode == "MaiDao" && screen.functionId == "M_COMPLETED") ??
            false;

        debugPrint("Permissions - M_CONFIRM: $_hasConfirmPermission, M_COMPLETED: $_hasCompletedPermission");
      } else {
        debugPrint("Failed to load user data");
      }

      // Continue with data initialization
      if (_isDummyData && widget.maiDaoRecord != null) {
        debugPrint("Initializing dummy data with company loading");
        _initializeDummyData(widget.maiDaoRecord!);
      } else {
        debugPrint("Initializing with real data");
        // For real data, fetch from API
        _initializeData();
      }
    } catch (e) {
      debugPrint("Error getting user data: $e");

      // Continue with initialization even if user data fetch fails
      if (_isDummyData && widget.maiDaoRecord != null) {
        debugPrint("Initializing dummy data with company loading");
        _initializeDummyData(widget.maiDaoRecord!);
      } else {
        debugPrint("Initializing with real data");
        // For real data, fetch from API
        _initializeData();
      }
    }
  }

  Future<void> _initializeData() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      debugPrint("=== STARTING INITIALIZATION ===");

      // Always load Nhà máy data first (even for dummy data to populate dropdown)
      // Load company data with the record's company code if available
      final recordCompanyCode = widget.maiDaoRecord?.companyCode;
      debugPrint("Record company code from maiDaoRecord: $recordCompanyCode");
      debugPrint("About to load company data...");

      try {
        await _loadNhaMayData(recordCompanyCode);
      } catch (e) {
        debugPrint("Error loading company data: $e");
        // Ensure we have at least a fallback company
        if (_lsNhaMayOptions == null || _lsNhaMayOptions!.isEmpty) {
          debugPrint("Creating emergency fallback company");
          setState(() {
            _lsNhaMayOptions = [
              SalesOrgCodes(
                saleOrgCode: recordCompanyCode ?? widget.user.companyCode ?? '1000',
                storeName: 'Default Company',
              )
            ];
            _selectedNhaMay = _lsNhaMayOptions!.first;
          });
        }
      }

      debugPrint("Company data loaded. Selected company: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})");

      // Prioritize passed record (e.g., from scanned equipment) over empty ID
      if (widget.maiDaoRecord != null) {
        debugPrint("=== USING PASSED RECORD ===");
        // Use the passed record

        // Load employees first if not dummy data
        if (_isDummyData) {
          debugPrint("Creating mock employees for dummy data");
          // For dummy data, create mock employees directly
          _createMockEmployeesFromRecord();
        } else {
          debugPrint("Fetching real employees from API");
          // Only fetch from API for real records
          await _fetchEmployees();
        }

        // Populate fields from the record (this will now set the correct company)
        final record = widget.maiDaoRecord!;
        debugPrint("About to populate fields from record with company: ${record.companyCode}");
        _populateFieldsFromRecord(record);

        // Set up employee fields based on the record
        _setupEmployeeFields(record);

        debugPrint("Final selected company after population: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})");
      } else if (widget.id.isEmpty && !_isDummyData) {
        debugPrint("=== NEW RECORD FLOW ===");
        // Set default values for completely new record (no scanned equipment)
        _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
        _selectedOperationType = _operationTypes.first;
        _status = 'Created'; // Set default status for new records

        // Load employees for the selection
        await _fetchEmployees();
      } else if (!_isDummyData) {
        debugPrint("=== FETCHING FROM API ===");
        // Only make API call for non-dummy data
        try {
          debugPrint("Fetching record from API for ID: ${widget.id}");
          final record = await MaiDaoFunction.fetchMaiDaoDetail(
            widget.user.token!,
            widget.id,
          );

          if (record != null) {
            debugPrint("Fetched record with company code: ${record.companyCode}");
            // Reload company data with the correct company code from the fetched record
            if (record.companyCode != null) {
              await _loadNhaMayData(record.companyCode);
            }

            // Load employees first
            await _fetchEmployees();

            // Populate fields from the record (this will now set the correct company)
            _populateFieldsFromRecord(record);

            // Set up employee fields
            _setupEmployeeFields(record);
          }
        } catch (e) {
          debugPrint("Error fetching detail: $e");
          setState(() {
            _isError = true;
          });
        }
      }

      debugPrint("=== INITIALIZATION COMPLETE ===");
    } on SocketException catch (e) {
      debugPrint("Network error in _initializeData: $e");
      if (!mounted) return;
      // For dummy data, don't show network error
      if (!_isDummyData) {
        setState(() {
          _isNotWifi = true;
        });
      }
    } catch (error) {
      debugPrint("Error in _initializeData: $error");
      if (!mounted) return;
      // For dummy data, don't show error
      if (!_isDummyData) {
        setState(() {
          _isError = true;
        });
        showToast(
          context: context,
          message: 'Có lỗi xảy ra khi tải dữ liệu',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to populate fields from a record
  void _populateFieldsFromRecord(MaiDaoRecord record) {
    _equipmentCodeController.text = record.equipmentCode ?? '';
    _equipmentNameController.text = record.equipmentName ?? '';
    _materialCodeController.text = record.materialCode ?? '';
    _materialNameController.text = record.materialName ?? '';
    _materialBatchController.text = record.materialBatch ?? '';
    _noteController.text = record.note ?? '';
    _selectedOperationType = record.operationType ?? _operationTypes.first;
    _status = record.status ?? 'Created';

    // Set requesting employee
    if (record.requestingEmployeeCode != null && record.requestingEmployeeName != null) {
      _selectedRequestingEmployee = EmployeeRecord(
        employeeCode: record.requestingEmployeeCode,
        employeeName: record.requestingEmployeeName,
      );
      _requestingEmployeeController.text = "${record.requestingEmployeeCode} | ${record.requestingEmployeeName}";
    }

    // Set date
    if (record.date != null) {
      try {
        _dateController.text = record.date!;
      } catch (e) {
        debugPrint('Error parsing date: $e');
        _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
      }
    } else {
      _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
    }

    // Set the selected company based on record's companyCode
    if (record.companyCode != null && _lsNhaMayOptions != null) {
      debugPrint('Setting company from record.companyCode: ${record.companyCode}');
      debugPrint('Available companies: ${_lsNhaMayOptions?.map((e) => '${e.saleOrgCode}:${e.storeName}').join(', ')}');
      try {
        _selectedNhaMay = _lsNhaMayOptions!.firstWhere(
          (element) => element.saleOrgCode == record.companyCode,
          orElse: () => _lsNhaMayOptions!.first,
        );
        debugPrint('Set selected company to: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})');
      } catch (e) {
        debugPrint('Error setting selected company: $e');
        // Fallback to first option if company not found
        if (_lsNhaMayOptions!.isNotEmpty) {
          _selectedNhaMay = _lsNhaMayOptions!.first;
        }
      }
    } else {
      debugPrint('Cannot set company: record.companyCode=${record.companyCode}, _lsNhaMayOptions=${_lsNhaMayOptions?.length}');
    }
  }

  // Helper method to set up employee fields based on a record
  void _setupEmployeeFields(MaiDaoRecord record) {
    if (record.employeeCodes != null && record.employeeCodes!.isNotEmpty) {
      final employeeCodes = record.employeeCodes!.split(',');

      // Clear default employee fields
      for (var controller in _employeeControllers) {
        controller.dispose();
      }
      _employeeControllers.clear();
      _selectedEmployees.clear();

      // Add employees from record
      for (var code in employeeCodes.where((code) => code.trim().isNotEmpty)) {
        final trimmedCode = code.trim();
        final employee = _allEmployees.firstWhere(
          (e) => e.employeeCode == trimmedCode,
          orElse: () => EmployeeRecord(
            employeeCode: trimmedCode,
            employeeName: 'Unknown Employee',
          ),
        );

        final controller = TextEditingController(text: "${employee.employeeCode} | ${employee.employeeName}");
        _employeeControllers.add(controller);
        _selectedEmployees.add(employee);
      }

      // Add an empty field if no employees were added
      if (_employeeControllers.isEmpty) {
        _addEmployeeField();
      }
    }
  }

  Future<void> _fetchEmployees() async {
    // Skip API calls for dummy data
    if (_isDummyData) return;

    try {
      final employees = await MaiDaoFunction.fetchEmployees(
        widget.user.token!,
        widget.user.companyCode ?? '',
      );

      setState(() {
        _allEmployees = employees;
      });
    } catch (e) {
      debugPrint('Error fetching employees: $e');
    }
  }

  void _createMockEmployeesFromRecord() {
    if (widget.maiDaoRecord?.employeeCodes == null) return;

    final employeeCodes = widget.maiDaoRecord!.employeeCodes!.split(',');
    final employeeNames = widget.maiDaoRecord!.employeeNames?.split(',') ?? [];

    List<EmployeeRecord> mockEmployees = [];

    for (int i = 0; i < employeeCodes.length; i++) {
      final code = employeeCodes[i].trim();
      final name = i < employeeNames.length ? employeeNames[i].trim() : 'Unknown Employee';

      mockEmployees.add(
        EmployeeRecord(
          employeeCode: code,
          employeeName: name,
        ),
      );
    }

    setState(() {
      _allEmployees = mockEmployees;
    });
  }

  Future<void> _fetchEquipmentSuggestions(String searchTerm) async {
    // Skip API calls for dummy data
    if (_isDummyData) return;

    if (searchTerm.length < 3) {
      setState(() {
        _equipmentSuggestions = [];
        _isLoadingEquipment = false;
      });
      return;
    }

    setState(() {
      _isLoadingEquipment = true;
    });

    try {
      final suggestions = await MaiDaoFunction.fetchEquipment(
        widget.user.token!,
        _selectedNhaMay?.saleOrgCode ?? widget.user.companyCode ?? '',
        searchTerm,
      );

      if (!mounted) return;

      setState(() {
        _equipmentSuggestions = suggestions
            .map((item) =>
                item.equipmentCode != null && item.equipmentName != null ? '${item.equipmentCode} | ${item.equipmentName}' : item.equipmentCode ?? '')
            .toList();
        _isLoadingEquipment = false;
      });

      // Add a small delay to ensure UI is updated before potentially opening suggestions
      Future.delayed(Duration(milliseconds: 150), () {
        // Trigger a rebuild to show the new suggestions
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      debugPrint('Error fetching equipment suggestions: $e');
      if (mounted) {
        setState(() {
          _equipmentSuggestions = [];
          _isLoadingEquipment = false;
        });
      }
    }
  }

  Future<void> _fetchMaterialSuggestions(String searchTerm) async {
    // Skip API calls for dummy data
    if (_isDummyData) return;

    if (searchTerm.length < 3) {
      setState(() {
        _materialSuggestions = [];
        _isLoadingMaterial = false;
      });
      return;
    }

    setState(() {
      _isLoadingMaterial = true;
    });

    try {
      final suggestions = await MaiDaoFunction.fetchMaterials(
        widget.user.token!,
        widget.user.companyCode ?? '',
        searchTerm,
      );

      if (!mounted) return;

      setState(() {
        _materialSuggestions = suggestions
            .map((item) =>
                item.materialCode != null && item.materialName != null ? '${item.materialCode} | ${item.materialName}' : item.materialCode ?? '')
            .toList();
        _isLoadingMaterial = false;
      });

      // Add a small delay to ensure UI is updated before potentially opening suggestions
      Future.delayed(Duration(milliseconds: 150), () {
        // Trigger a rebuild to show the new suggestions
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      debugPrint('Error fetching material suggestions: $e');
      if (mounted) {
        setState(() {
          _materialSuggestions = [];
          _isLoadingMaterial = false;
        });
      }
    }
  }

  Future<void> _scanMaterialCode() async {
    // Skip for dummy data
    if (_isDummyData) return;

    try {
      FocusScope.of(context).unfocus();
      await Future<void>.delayed(const Duration(milliseconds: 500));

      // Navigate to QR scan page and wait for result
      final scannedData = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GenericQRScanner(
            title: 'Quét mã vật tư',
            subtitle: 'Hướng camera vào mã QR của vật tư',
            onDataScanned: (scannedData) async {
              // Just return the scanned data, validation will be done later
              return scannedData;
            },
          ),
        ),
      );

      if (scannedData == null) return;
      if (!mounted) return;

      await _handleScannedMaterial(scannedData as String);
    } catch (e) {
      debugPrint('Error scanning material barcode: $e');
      String errorMessage = 'Có lỗi xảy ra khi quét mã vật tư';

      if (e.toString().contains('chỉ được chứa')) {
        errorMessage = 'Mã vật tư không đúng định dạng';
      } else if (e.toString().contains('không tồn tại')) {
        errorMessage = 'Vật tư không có trong hệ thống. Vui lòng kiểm tra lại.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng thử lại.';
      }

      showToast(
        context: context,
        message: errorMessage,
      );
    }
  }

  Future<void> _handleScannedEquipment(String scannedData) async {
    // Validate QR code format first
    if (!scannedData.contains('<T2>') || !scannedData.contains('<T7>')) {
      showToast(
        context: context,
        message: 'QR code thiết bị không hợp lệ',
      );

      // Clear the input field
      _equipmentCodeController.clear();
      _equipmentNameController.clear();
      return;
    }

    // Parse XML-like QR code structure
    String? equipmentCode;
    String? equipmentName;

    // Extract equipment code from T2 tag
    final codeRegex = RegExp(r'<T2>(.*?)</T2>');
    final codeMatch = codeRegex.firstMatch(scannedData);
    if (codeMatch != null) {
      equipmentCode = codeMatch.group(1);
    }

    // Extract equipment name from T7 tag
    final nameRegex = RegExp(r'<T7>(.*?)</T7>');
    final nameMatch = nameRegex.firstMatch(scannedData);
    if (nameMatch != null) {
      equipmentName = nameMatch.group(1);
    }

    // Validate that we found both code and name
    if (equipmentCode == null || equipmentCode.isEmpty) {
      showToast(
        context: context,
        message: 'Không tìm thấy mã thiết bị trong QR code',
      );
      return;
    }

    if (equipmentName == null || equipmentName.isEmpty) {
      showToast(
        context: context,
        message: 'Không tìm thấy tên thiết bị trong QR code',
      );
      return;
    }

    // Check for existing incomplete record
    final shouldProceed = await _checkExistingRecord(equipmentCode);

    // Set the values directly from QR code if no existing record found and user didn't cancel
    if (shouldProceed != false) {
      setState(() {
        _equipmentCodeController.text = equipmentCode!;
        _equipmentNameController.text = equipmentName!;
      });
    }
  }

  Future<void> _handleScannedMaterial(String scannedData) async {
    // Validate QR code format first
    if (!scannedData.contains('<T2>')) {
      showToast(
        context: context,
        message: 'QR code vật tư không hợp lệ',
      );

      // Clear the input field
      _materialCodeController.clear();
      _materialNameController.clear();
      _materialBatchController.clear();
      return;
    }

    // Parse XML-like QR code structure to get material code and batch
    String? materialCode;
    String? materialBatch;

    // Extract material code from T2 tag
    final codeRegex = RegExp(r'<T2>(.*?)</T2>');
    final codeMatch = codeRegex.firstMatch(scannedData);
    if (codeMatch != null) {
      materialCode = codeMatch.group(1);
    }

    // Extract material batch from T3 tag (if exists)
    final batchRegex = RegExp(r'<T3>"?(.*?)"?</T3>');
    final batchMatch = batchRegex.firstMatch(scannedData);
    if (batchMatch != null) {
      materialBatch = batchMatch.group(1);
    }

    // Validate that we found the code
    if (materialCode == null || materialCode.isEmpty) {
      showToast(
        context: context,
        message: 'Không tìm thấy mã vật tư trong QR code',
      );
      return;
    }

    // Skip API calls for dummy data
    if (_isDummyData) {
      setState(() {
        _materialCodeController.text = materialCode!;
        _materialNameController.text = 'Dummy Material Name';
        _materialBatchController.text = materialBatch ?? '';
      });

      showToast(
        context: context,
        message: 'Đã quét vật tư (dữ liệu mẫu): $materialCode',
      );
      return;
    }

    try {
      // Fetch product information from API
      final productInfo = await MaiDaoFunction.fetchProductByCode(
        widget.user.token!,
        materialCode,
      );

      if (!mounted) return;

      if (productInfo != null && productInfo.productCode != null) {
        // Set the values from API response
        setState(() {
          _materialCodeController.text = productInfo.productCode!;
          _materialNameController.text = productInfo.productName ?? '';
          _materialBatchController.text = materialBatch ?? ''; // Set batch from QR code
        });

        showToast(
          context: context,
          message:
              'Đã quét vật tư: ${productInfo.productName ?? materialCode}${materialBatch != null && materialBatch.isNotEmpty ? ' (Batch: $materialBatch)' : ''}',
        );
      } else {
        // Product not found in database
        showToast(
          context: context,
          message: 'Vật tư không có trong hệ thống. Vui lòng kiểm tra lại.',
        );

        // Clear the input field
        _materialCodeController.clear();
        _materialNameController.clear();
        _materialBatchController.clear();
      }
    } catch (e) {
      debugPrint('Error fetching product information: $e');

      if (!mounted) return;

      String errorMessage = 'Có lỗi xảy ra khi quét mã vật tư';

      if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng thử lại.';
      }

      showToast(
        context: context,
        message: errorMessage,
      );

      // Clear the input field
      _materialCodeController.clear();
      _materialNameController.clear();
      _materialBatchController.clear();
    }
  }

  Future<bool> _checkExistingRecord(String equipmentCode) async {
    // Skip for dummy data or if already editing a record
    if (_isDummyData || _currentRecordId.isNotEmpty) return true;

    try {
      // Call API to check for existing incomplete records
      final existingRecord = await MaiDaoFunction.checkExistingRecord(
        widget.user.token!,
        equipmentCode,
        widget.user.companyCode ?? '',
      );

      if (existingRecord != null && mounted) {
        // Show dialog asking user if they want to view the existing record or cancel
        final userChoice = await showDialog<String>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Thiết bị đang được sử dụng'),
            content: Text(
              'Thiết bị này có phiếu chưa hoàn thành. Bạn muốn thực hiện hành động nào?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, 'cancel'),
                child: Text('Quay lại'),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, 'view'),
                child: Text('Xem phiếu'),
                style: TextButton.styleFrom(foregroundColor: Colors.blue),
              ),
            ],
          ),
        );

        if (userChoice == 'view') {
          // Load the existing record
          await _loadExistingRecord(existingRecord);
          return false; // Don't proceed with setting new equipment data
        } else if (userChoice == 'cancel') {
          // User canceled - don't set the equipment fields
          return false;
        }
      }
    } catch (e) {
      debugPrint('Error checking existing record: $e');
      // Continue with normal flow if check fails
    }

    return true; // Proceed with setting equipment data
  }

  Future<void> _loadExistingRecord(MaiDaoRecord record) async {
    try {
      // Populate fields from the existing record
      _populateFieldsFromRecord(record);

      // Load employees if needed
      if (!_isDummyData) {
        await _fetchEmployees();
      }

      // Set up employee fields
      _setupEmployeeFields(record);

      // Update the current record ID to indicate we're now editing
      _currentRecordId = record.id ?? '';

      setState(() {
        // Trigger UI update
      });

      showToast(
        context: context,
        message: 'Đã tải phiếu hiện có của thiết bị',
      );
    } catch (e) {
      debugPrint('Error loading existing record: $e');
      showToast(
        context: context,
        message: 'Lỗi khi tải phiếu hiện có',
      );
    }
  }

  Future<void> _scanBarcodeEquipment() async {
    try {
      final scannedData = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GenericQRScanner(
            title: 'Quét mã thiết bị',
            subtitle: 'Hướng camera vào mã QR của thiết bị',
            onDataScanned: (scannedData) async {
              // Just return the scanned data, validation will be done later
              return scannedData;
            },
          ),
        ),
      );

      if (scannedData == null) return;
      if (!mounted) return;

      await _handleScannedEquipment(scannedData as String);
    } catch (e) {
      debugPrint('Error scanning equipment barcode: $e');
      String errorMessage = 'Có lỗi xảy ra khi quét mã thiết bị';

      if (e.toString().contains('chỉ được chứa')) {
        errorMessage = 'Mã thiết bị không đúng định dạng';
      } else if (e.toString().contains('không tồn tại')) {
        errorMessage = 'Thiết bị không có trong hệ thống. Vui lòng kiểm tra lại.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng thử lại.';
      }

      showToast(
        context: context,
        message: errorMessage,
      );
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    if (isEffectivelyReadOnly || _isDummyData) return;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      setState(() {
        _dateController.text = DateFormat('dd/MM/yyyy').format(pickedDate);
      });
    }
  }

  bool _validateForm() {
    // Skip validation for dummy data
    if (_isDummyData) return true;

    if (_selectedNhaMay == null) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Nhà máy',
      );
      return false;
    }

    if (_dateController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Ngày',
      );
      return false;
    }

    if (_equipmentCodeController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Thiết bị',
      );
      return false;
    }

    if (_materialCodeController.text.isEmpty) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Vật tư',
      );
      return false;
    }

    if (_selectedOperationType == null) {
      showToast(
        context: context,
        message: 'Vui lòng chọn Nghiệp vụ',
      );
      return false;
    }

    return true;
  }

  Future<void> _saveData() async {
    // Show confirmation dialog for non-new records
    if (_currentRecordId.isNotEmpty) {
      String confirmTitle = '';
      String confirmMessage = '';

      switch (_status) {
        case 'Created':
          confirmTitle = 'Xác nhận phiếu';
          confirmMessage = 'Bạn có chắc chắn muốn xác nhận phiếu này không?';
          break;
        case 'Confirmed':
          confirmTitle = 'Xác nhận hoàn thành';
          confirmMessage = 'Bạn có chắc chắn muốn xác nhận hoàn thành phiếu này không?';
          break;
        default:
          confirmTitle = 'Xác nhận cập nhật';
          confirmMessage = 'Bạn có chắc chắn muốn cập nhật phiếu này không?';
          break;
      }

      final shouldProceed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(confirmTitle),
          content: Text(confirmMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Hủy'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Xác nhận'),
              style: TextButton.styleFrom(foregroundColor: Colors.blue),
            ),
          ],
        ),
      );

      if (shouldProceed != true) return;
    }

    // Handle dummy data differently
    if (_isDummyData) {
      showToast(
        context: context,
        message: 'Đây là dữ liệu mẫu - không thể lưu thực tế',
      );

      // But still update the status to demonstrate the workflow
      setState(() {
        _status = _getNextStatus();
      });

      return;
    }

    if (!_validateForm()) return;

    try {
      setState(() => _isSaving = true);

      // Capture whether this is a new record before any state changes
      final isNewRecord = _currentRecordId.isEmpty;

      // Create the model for the API request
      final employeeCodes =
          _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeCode).where((code) => code != null && code.isNotEmpty).join(',');

      final employeeNames =
          _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeName).where((name) => name != null && name.isNotEmpty).join(', ');

      final model = MaiDaoRecord(
        id: _currentRecordId.isEmpty ? null : _currentRecordId,
        date: _dateController.text,
        equipmentCode: _equipmentCodeController.text,
        equipmentName: _equipmentNameController.text,
        materialCode: _materialCodeController.text,
        materialName: _materialNameController.text,
        materialBatch: _materialBatchController.text,
        operationType: _selectedOperationType,
        employeeCodes: employeeCodes,
        employeeNames: employeeNames,
        requestingEmployeeCode: _selectedRequestingEmployee?.employeeCode,
        requestingEmployeeName: _selectedRequestingEmployee?.employeeName,
        note: _noteController.text,
        status: _getNextStatus(),
        companyCode: _selectedNhaMay?.saleOrgCode ?? widget.user.companyCode,
      );

      // Call the API
      final success = await MaiDaoFunction.saveMaiDao(
        widget.user.token!,
        model,
      );

      if (!mounted) return;

      if (success) {
        if (isNewRecord) {
          // For new records, update the state and show creation success message
          setState(() {
            // Set a placeholder ID to indicate this is now an existing record
            _currentRecordId = 'created_${DateTime.now().millisecondsSinceEpoch}';
            _status = 'Created'; // Set to 'Created' for new records
          });

          showToast(
            context: context,
            message: 'Tạo mới thành công',
          );

          // Navigate back and refresh for new records
          Navigator.pop(context, true);
        } else {
          // Update status for existing records
          setState(() {
            _status = _getNextStatus();
          });

          showToast(
            context: context,
            message: _getSuccessMessage(),
          );

          // Navigate back for updates to existing records
          Navigator.pop(context, true);
        }
      } else {
        showToast(
          context: context,
          message: 'Lưu thất bại',
        );
      }
    } catch (error) {
      debugPrint("Error in _saveData: $error");
      if (!mounted) return;
      showToast(
        context: context,
        message: 'Đã xảy ra lỗi khi lưu dữ liệu',
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  List<String> _equipmentSuggestions = [];
  List<String> _materialSuggestions = [];

  @override
  Widget build(BuildContext context) {
    // Apply global theme for view mode
    final viewModeTheme = ThemeData(
      textTheme: TextTheme(
        bodyMedium: TextStyle(
          fontSize: 12.sp,
          color: Colors.black,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: Colors.white,
        filled: true,
      ),
    );

    // Default text style for view mode
    final TextStyle viewModeTextStyle = TextStyle(
      fontSize: 12.sp,
      color: Colors.black,
    );

    return Scaffold(
      backgroundColor: Colors.grey[100],
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          widget.id.isEmpty ? 'Thêm mài dao cụ mũi lưỡi' : 'Chi tiết mài dao cụ mũi lưỡi',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
      ),
      body: SafeArea(
        child: KeyboardDismissOnTap(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 350.h),
                    child: _buildBody(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // DEBUG ONLY
  List<Map<String, String>> dummyEQCode = [
    {
      'label': 'EQ 10000803 Máy khoan',
      'qrcode':
          '<T1>66fd7a8a-727d-4367-a23a-023268616c7a</T1><T2>10000803</T2><T7>Máy khoan lỗ bản lề MZ4214B</T7><T8>PMTTFTCD</T8><T9>000211200692</T9><T10>1000-PX04-33</T10><T11>1022</T11><T12>1000C06</T12><T13>1000</T13>',
    },
  ];

  List<Map<String, String>> dummyMaterial = [
    {
      'label': '300000196',
      'qrcode': '<T1>d209e743-eed6-42aa-a95b-8a4f0bc9cb71</T1><T2>300000196</T2>"<T3>DDC04224D</T3>"',
    },
  ];

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingScreen();
    }

    if (_isNotWifi) {
      return LostConnect(checkConnect: () => _initializeData());
    }

    if (_isError) {
      return Center(
        child: Text(
          'Có lỗi xảy ra! vui lòng thử lại sau',
          style: TextStyle(fontSize: 15.sp),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormLayout(
          title: "Thông tin mài dao cụ mũi lưỡi",
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  kDebugMode
                      ? RenderDebugButtons(dummyEQCode, (item) {
                          _handleScannedEquipment(item['qrcode']!);
                        }, 'label')
                      : Container(),
                  kDebugMode
                      ? RenderDebugButtons(dummyMaterial, (item) {
                          _handleScannedMaterial(item['qrcode']!);
                        }, 'label')
                      : Container(),
                  // Nhà máy Field
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Nhà máy',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<SalesOrgCodes>(
                              isExpanded: true,
                              isDense: true,
                              itemHeight: null,
                              value: _selectedNhaMay,
                              iconSize: 15.sp,
                              style: const TextStyle(color: Colors.white),
                              onChanged: _areGeneralFieldsEditable ? _onNhaMayChanged : null,
                              items: _lsNhaMayOptions?.map((SalesOrgCodes company) {
                                return DropdownMenuItem<SalesOrgCodes>(
                                  value: company,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: 5.h),
                                    child: Text(
                                      company.storeName!,
                                      style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                    ),
                                  ),
                                );
                              }).toList(),
                              selectedItemBuilder: (BuildContext context) {
                                return _lsNhaMayOptions?.map<Widget>((SalesOrgCodes company) {
                                      return Text(
                                        company.storeName!,
                                        style: TextStyle(color: Colors.black, fontSize: 12.sp),
                                        overflow: TextOverflow.ellipsis,
                                      );
                                    }).toList() ??
                                    [];
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // Date Field
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Ngày',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: GestureDetector(
                          onTap: _areGeneralFieldsEditable ? () => _selectDate(context) : null,
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                            decoration: BoxDecoration(
                              border: Border.all(width: 0.5, color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(3.r),
                              color: Colors.white,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    _dateController.text.isEmpty ? 'Chọn ngày' : _dateController.text,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 6.0),
                                  child: Icon(Icons.calendar_today_outlined, size: 18.sp, color: Colors.blue),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // Equipment Field
                  ApiSuggestionField(
                    label: "Equipment:",
                    controller: _equipmentCodeController,
                    enabled: _areGeneralFieldsEditable,
                    minCharsForSuggestions: 3,
                    showCameraButton: true,
                    hintText: "Nhập mã thiết bị (ít nhất 3 ký tự)",
                    isLoading: _isLoadingEquipment,
                    suggestionsCallback: (pattern) async {
                      if (_isDummyData) return <String>[];

                      final suggestions = await MaiDaoFunction.fetchEquipment(
                        widget.user.token!,
                        _selectedNhaMay?.saleOrgCode ?? widget.user.companyCode ?? '',
                        pattern,
                      );

                      return suggestions
                          .map((item) => item.equipmentCode != null && item.equipmentName != null
                              ? '${item.equipmentCode} | ${item.equipmentName}'
                              : item.equipmentCode ?? '')
                          .toList();
                    },
                    onSuggestionSelected: (suggestion) async {
                      final parts = suggestion.split(' | ');
                      final equipmentCode = parts[0];
                      final equipmentName = parts.length > 1 ? parts[1] : '';

                      // Check for existing incomplete record
                      await _checkExistingRecord(equipmentCode);

                      setState(() {
                        _equipmentCodeController.text = equipmentCode;
                        _equipmentNameController.text = equipmentName;
                      });
                    },
                    onChanged: (value) {
                      setState(() {
                        _equipmentNameController.text = '';
                      });
                    },
                    onCameraScan: () async {
                      await _scanBarcodeEquipment();
                    },
                  ),

                  if (_equipmentNameController.text.isNotEmpty) ...[
                    FormInputField(
                      label: 'Tên thiết bị',
                      controller: _equipmentNameController,
                      enabled: false,
                      isBorder: false,
                    ),
                  ],
                  SizedBox(height: 10.h),

                  // Material Field
                  ApiSuggestionField(
                    label: "Vật tư:",
                    controller: _materialCodeController,
                    enabled: _areGeneralFieldsEditable,
                    minCharsForSuggestions: 3,
                    showCameraButton: true,
                    hintText: "Nhập mã vật tư (ít nhất 3 ký tự)",
                    isLoading: _isLoadingMaterial,
                    suggestionsCallback: (pattern) async {
                      if (_isDummyData) return <String>[];

                      final suggestions = await MaiDaoFunction.fetchMaterials(
                        widget.user.token!,
                        widget.user.companyCode ?? '',
                        pattern,
                      );

                      return suggestions
                          .map((item) => item.materialCode != null && item.materialName != null
                              ? '${item.materialCode} | ${item.materialName}'
                              : item.materialCode ?? '')
                          .toList();
                    },
                    onSuggestionSelected: (suggestion) async {
                      final parts = suggestion.split(' | ');
                      setState(() {
                        _materialCodeController.text = parts[0];
                        _materialNameController.text = parts.length > 1 ? parts[1] : '';
                      });
                    },
                    onChanged: (value) {
                      setState(() {
                        _materialNameController.text = '';
                      });
                    },
                    onCameraScan: () async {
                      await _scanMaterialCode();
                    },
                  ),

                  if (_materialNameController.text.isNotEmpty) ...[
                    SizedBox(height: 10.h),
                    FormInputField(
                      label: 'Tên vật tư',
                      controller: _materialNameController,
                      enabled: false,
                      isBorder: false,
                    ),
                  ],

                  if (_materialBatchController.text.isNotEmpty) ...[
                    FormInputField(
                      label: 'Batch vật tư',
                      controller: _materialBatchController,
                      enabled: _areGeneralFieldsEditable,
                      required: false,
                      isBorder: false,
                    ),
                  ],
                  SizedBox(height: 10.h),

                  // Operation Type
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Nghiệp vụ',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: _areGeneralFieldsEditable
                            ? Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 5.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    isDense: true,
                                    isExpanded: true,
                                    value: _selectedOperationType,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.black,
                                    ),
                                    items: _operationTypes
                                        .map((type) => DropdownMenuItem(
                                              value: type,
                                              child: Text(
                                                type,
                                                style: TextStyle(
                                                  fontSize: 12.sp,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ))
                                        .toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedOperationType = value;
                                      });
                                    },
                                  ),
                                ),
                              )
                            : Container(
                                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 7.h),
                                decoration: BoxDecoration(
                                  border: Border.all(width: 0.5, color: Colors.grey.shade400),
                                  borderRadius: BorderRadius.circular(3.r),
                                  color: Colors.white,
                                ),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    _selectedOperationType ?? '',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // Note
                  FormInputField(
                    label: 'Ghi chú',
                    controller: _noteController,
                    enabled: _isNoteEditable,
                    maxLines: 3,
                    required: false,
                  ),
                  SizedBox(height: 10.h),

                  // Employees
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'NV mài',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: Column(
                          children: List.generate(
                            _employeeControllers.length,
                            (index) => EmployeeTypeAhead(
                              enabled: _areGeneralFieldsEditable,
                              textStyle: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.black,
                              ),
                              showRemoveButton: _employeeControllers.length > 1 && _areGeneralFieldsEditable,
                              totalEmployees: _employeeControllers.length,
                              masterDataList: _allEmployees,
                              excludedEmployeeIds: _selectedEmployeeIds,
                              controller: _employeeControllers[index],
                              employeeIndex: index,
                              onChanged: _areGeneralFieldsEditable
                                  ? (value) {
                                      debugPrint('Employee value changed: $value');
                                      setState(() {
                                        _selectedEmployees[index] = null;
                                      });
                                    }
                                  : _noOp,
                              onSuggestionSelected: _areGeneralFieldsEditable
                                  ? (suggestion) {
                                      debugPrint('Employee suggestion selected: ${suggestion.employeeCode} - ${suggestion.employeeName}');
                                      setState(() {
                                        _employeeControllers[index].text = "${suggestion.employeeCode} | ${suggestion.employeeName}";
                                        _selectedEmployees[index] = suggestion;
                                      });
                                    }
                                  : _noOpEmployeeRecord,
                              onAddEmployee: _areGeneralFieldsEditable
                                  ? () {
                                      _addEmployeeField();
                                    }
                                  : null,
                              onRemoveEmployee: _areGeneralFieldsEditable ? () => _removeEmployeeField(index) : null,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // Requesting Employee
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'NV yêu cầu',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        flex: 7,
                        child: Container(
                          // padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            border: Border.all(width: 0.5, color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(3.r),
                            color: Colors.white,
                          ),
                          child: Stack(
                            children: [
                              TypeAheadField<EmployeeRecord>(
                                suggestionsBoxDecoration: SuggestionsBoxDecoration(
                                  constraints: BoxConstraints(
                                      // maxHeight: 200.h,
                                      ),
                                ),
                                textFieldConfiguration: TextFieldConfiguration(
                                  enabled: _areGeneralFieldsEditable,
                                  controller: _requestingEmployeeController,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.black,
                                  ),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    isDense: true,
                                    contentPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                  onChanged: _areGeneralFieldsEditable
                                      ? (value) {
                                          debugPrint('Requesting employee value changed: $value');
                                          setState(() {
                                            _selectedRequestingEmployee = null;
                                          });
                                        }
                                      : _noOp,
                                ),
                                suggestionsCallback: (pattern) {
                                  return _allEmployees
                                      .where((item) =>
                                          (item.employeeName?.toLowerCase().contains(pattern.toLowerCase()) ?? false) ||
                                          (item.employeeCode?.toLowerCase().contains(pattern.toLowerCase()) ?? false))
                                      .toList();
                                },
                                itemBuilder: (context, EmployeeRecord suggestion) {
                                  return Padding(
                                    padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                                    child: Text(
                                      "${suggestion.employeeCode} - ${suggestion.employeeName}",
                                      style: TextStyle(fontSize: 12.sp),
                                    ),
                                  );
                                },
                                onSuggestionSelected: _areGeneralFieldsEditable
                                    ? (suggestion) {
                                        debugPrint(
                                            'Requesting employee suggestion selected: ${suggestion.employeeCode} - ${suggestion.employeeName}');
                                        setState(() {
                                          _requestingEmployeeController.text = "${suggestion.employeeCode} | ${suggestion.employeeName}";
                                          _selectedRequestingEmployee = suggestion;
                                        });
                                      }
                                    : _noOpEmployeeRecord,
                                noItemsFoundBuilder: (context) => Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 10.w),
                                  child: Text(
                                    'Không tìm thấy kết quả',
                                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                                  ),
                                ),
                              ),
                              // Clear button
                              if (_areGeneralFieldsEditable && _requestingEmployeeController.text.isNotEmpty)
                                Positioned(
                                  top: 0,
                                  right: 5,
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _requestingEmployeeController.clear();
                                        _selectedRequestingEmployee = null;
                                      });
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Icon(
                                        Icons.close,
                                        size: 20.sp,
                                        color: Colors.red,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),

                  // Trạng thái - only show for existing records
                  if (_currentRecordId.isNotEmpty) ...[
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            'Trạng thái',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          flex: 7,
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                              decoration: BoxDecoration(
                                color: MaiDaoFunction.getStatusColor(_status ?? 'Created'),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                MaiDaoFunction.getStatusText(_status ?? 'Created'),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.h),
                  ],
                ],
              ),
            ),
          ],
        ),

        // Submit Button
        if (_shouldShowSubmitButton())
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: FormSubmitButton(
              text: _getSubmitButtonText(),
              onPressed: _saveData,
              isLoading: _isSaving,
            ),
          ),

        // Cancel Button
        if (_shouldShowCancelButton())
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 0.h),
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: _isSaving ? null : _cancelRecord,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: BorderSide(color: Colors.red),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
                child: Text(
                  'Hủy',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _dateController.dispose();
    _equipmentCodeController.dispose();
    _equipmentNameController.dispose();
    _materialCodeController.dispose();
    _materialNameController.dispose();
    _materialBatchController.dispose();
    _noteController.dispose();
    _requestingEmployeeController.dispose();
    for (var controller in _employeeControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // Initialize dummy data with company loading
  Future<void> _initializeDummyData(MaiDaoRecord record) async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _isError = false;
        _isNotWifi = false;
      });

      debugPrint("=== STARTING DUMMY DATA INITIALIZATION ===");

      // Load company data first (essential for dropdown)
      final recordCompanyCode = record.companyCode;
      debugPrint("Dummy record company code: $recordCompanyCode");

      try {
        await _loadNhaMayData(recordCompanyCode);
      } catch (e) {
        debugPrint("Error loading company data for dummy: $e");
        // Create fallback company for dummy data
        if (_lsNhaMayOptions == null || _lsNhaMayOptions!.isEmpty) {
          debugPrint("Creating fallback company for dummy data");
          setState(() {
            _lsNhaMayOptions = [
              SalesOrgCodes(
                saleOrgCode: recordCompanyCode ?? widget.user.companyCode ?? '1000',
                storeName: recordCompanyCode != null ? 'Company $recordCompanyCode' : 'Default Company',
              )
            ];
            _selectedNhaMay = _lsNhaMayOptions!.first;
          });
        }
      }

      debugPrint("Company data loaded for dummy. Selected: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})");

      // Now populate the dummy data
      _populateDummyData(record);

      debugPrint("=== DUMMY DATA INITIALIZATION COMPLETE ===");
    } catch (error) {
      debugPrint("Error in _initializeDummyData: $error");
      if (mounted) {
        setState(() {
          _isError = true;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Special method just for dummy data
  void _populateDummyData(MaiDaoRecord record) {
    debugPrint("Populating dummy data: ${record.id}");

    // Populate fields from record
    _equipmentCodeController.text = record.equipmentCode ?? '';
    _equipmentNameController.text = record.equipmentName ?? '';
    _materialCodeController.text = record.materialCode ?? '';
    _materialNameController.text = record.materialName ?? '';
    _materialBatchController.text = record.materialBatch ?? '';
    _noteController.text = record.note ?? '';
    _selectedOperationType = record.operationType ?? _operationTypes.first;
    _status = record.status ?? 'Created';

    // Set requesting employee for dummy data
    if (record.requestingEmployeeCode != null && record.requestingEmployeeName != null) {
      _selectedRequestingEmployee = EmployeeRecord(
        employeeCode: record.requestingEmployeeCode,
        employeeName: record.requestingEmployeeName,
      );
      _requestingEmployeeController.text = "${record.requestingEmployeeCode} | ${record.requestingEmployeeName}";
    }

    // Set date
    if (record.date != null) {
      _dateController.text = record.date!;
    } else {
      _dateController.text = DateFormat('dd/MM/yyyy').format(DateTime.now());
    }

    // Set the selected company based on record's companyCode
    if (record.companyCode != null && _lsNhaMayOptions != null) {
      debugPrint('Setting company from dummy record.companyCode: ${record.companyCode}');
      debugPrint('Available companies: ${_lsNhaMayOptions?.map((e) => '${e.saleOrgCode}:${e.storeName}').join(', ')}');
      try {
        _selectedNhaMay = _lsNhaMayOptions!.firstWhere(
          (element) => element.saleOrgCode == record.companyCode,
          orElse: () => _lsNhaMayOptions!.first,
        );
        debugPrint('Set selected company to: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})');
      } catch (e) {
        debugPrint('Error setting selected company: $e');
        // Fallback to first option if company not found
        if (_lsNhaMayOptions!.isNotEmpty) {
          _selectedNhaMay = _lsNhaMayOptions!.first;
        }
      }
    } else {
      debugPrint('Cannot set company: record.companyCode=${record.companyCode}, _lsNhaMayOptions=${_lsNhaMayOptions?.length}');
    }

    // Create mock employees
    _createMockEmployeesFromDummyRecord(record);

    // Set loading to false
    Future.microtask(() {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _createMockEmployeesFromDummyRecord(MaiDaoRecord record) {
    debugPrint("Creating mock employees from dummy record");
    if (record.employeeCodes == null) return;

    final employeeCodes = record.employeeCodes!.split(',');
    final employeeNames = record.employeeNames?.split(',') ?? [];

    List<EmployeeRecord> mockEmployees = [];

    for (int i = 0; i < employeeCodes.length; i++) {
      final code = employeeCodes[i].trim();
      final name = i < employeeNames.length ? employeeNames[i].trim() : 'Unknown Employee';

      mockEmployees.add(
        EmployeeRecord(
          employeeCode: code,
          employeeName: name,
        ),
      );
    }

    // Set all employees
    _allEmployees = mockEmployees;

    // Clear existing employee fields
    for (var controller in _employeeControllers) {
      controller.dispose();
    }
    _employeeControllers.clear();
    _selectedEmployees.clear();

    // Add employee fields for each employee
    for (var employee in mockEmployees) {
      final controller = TextEditingController(text: "${employee.employeeCode} | ${employee.employeeName}");
      _employeeControllers.add(controller);
      _selectedEmployees.add(employee);
    }

    // Add an empty field if no employees were added
    if (_employeeControllers.isEmpty) {
      _addEmployeeField();
    }
  }

  Future<void> _loadNhaMayData(String? companyCode) async {
    debugPrint('=== _loadNhaMayData START ===');
    debugPrint('Loading Nhà máy data with companyCode: $companyCode');
    debugPrint('Current user companyCode: ${widget.user.companyCode}');
    debugPrint('Current user companyName: ${widget.user.companyName}');

    try {
      // Load Nhà máy (similar to company loading in other screens)
      final companyList = await LoginFunction.getCompanyListFromStorage(widget.user.userName!);
      debugPrint('Loaded company list: ${companyList?.length ?? 0} companies');

      if (!mounted) return;

      if (companyList != null && companyList.isNotEmpty) {
        debugPrint('Setting up company options from storage');
        setState(() {
          _lsNhaMayOptions = companyList.map((company) => SalesOrgCodes(saleOrgCode: company.companyCode, storeName: company.companyName)).toList();

          if (_lsNhaMayOptions!.isNotEmpty) {
            // Use the passed companyCode, or fall back to user's company code, or first option
            final targetCompanyCode = companyCode ?? widget.user.companyCode;
            debugPrint('Target company code: $targetCompanyCode');
            debugPrint('Available companies: ${_lsNhaMayOptions!.map((e) => '${e.saleOrgCode}:${e.storeName}').join(', ')}');

            _selectedNhaMay = _lsNhaMayOptions!.firstWhere(
              (element) => element.saleOrgCode == targetCompanyCode,
              orElse: () => _lsNhaMayOptions!.first,
            );
            debugPrint('Initial selected company: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})');
          }
        });
      } else {
        // Fallback for when company list is empty or null (e.g., for dummy data or network issues)
        debugPrint('Company list is empty or null, creating fallback');
        final fallbackCode = companyCode ?? widget.user.companyCode ?? '1000';
        final fallbackName = widget.user.companyName ?? 'Default Company';

        setState(() {
          _lsNhaMayOptions = [
            SalesOrgCodes(
              saleOrgCode: fallbackCode,
              storeName: fallbackName,
            )
          ];
          _selectedNhaMay = _lsNhaMayOptions!.first;
          debugPrint('Created fallback company: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})');
        });
      }
    } catch (error) {
      debugPrint('Exception in _loadNhaMayData: $error');
      // Create a fallback option when loading fails
      if (mounted) {
        final fallbackCode = companyCode ?? widget.user.companyCode ?? '1000';
        final fallbackName = widget.user.companyName ?? 'Default Company';

        setState(() {
          _lsNhaMayOptions = [
            SalesOrgCodes(
              saleOrgCode: fallbackCode,
              storeName: fallbackName,
            )
          ];
          _selectedNhaMay = _lsNhaMayOptions!.first;
          debugPrint('Created exception fallback company: ${_selectedNhaMay?.storeName} (${_selectedNhaMay?.saleOrgCode})');
        });
      }
    }

    debugPrint('=== _loadNhaMayData END ===');
    debugPrint('Final state: ${_lsNhaMayOptions?.length ?? 0} options, selected: ${_selectedNhaMay?.storeName}');
  }

  void _onNhaMayChanged(SalesOrgCodes? value) {
    setState(() {
      _selectedNhaMay = value;
    });
  }

  // Add a method to create styled form labels
  Widget _formLabel(String label, {bool required = true}) {
    return Text(
      label + (required ? ' *' : ''),
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    );
  }

  // Helper method to get submit button text based on status
  String _getSubmitButtonText() {
    if (_currentRecordId.isEmpty) {
      return 'Tạo mới';
    }

    switch (_status) {
      case 'Created':
        return 'Xác nhận';
      case 'Confirmed':
        return 'Xác nhận hoàn thành';
      default:
        return 'Cập nhật';
    }
  }

  // Helper method to determine if submit button should be shown
  bool _shouldShowSubmitButton() {
    if (isEffectivelyReadOnly) return false;

    // Hide for 'Completed' and 'Cancelled'
    if (_status == 'Completed' || _status == 'Cancelled') return false;

    // For new records (empty ID), always show button (no permission check needed for creation)
    if (_currentRecordId.isEmpty) return true;

    // For existing records, check permissions based on status
    switch (_status) {
      case 'Created':
        // Show "Xác nhận" button only if user has M_CONFIRM permission
        return _hasConfirmPermission;
      case 'Confirmed':
        // Show "Xác nhận hoàn thành" button only if user has M_COMPLETED permission
        return _hasCompletedPermission;
      default:
        return false;
    }
  }

  // Helper method to determine if cancel button should be shown
  bool _shouldShowCancelButton() {
    if (isEffectivelyReadOnly) return false;
    if (_currentRecordId.isEmpty) return false;

    // Only show cancel button when status is 'Created' (Mới tạo) and user has confirm permission
    return _status == 'Created' && _hasConfirmPermission;
  }

  Future<void> _cancelRecord() async {
    try {
      // Show confirmation dialog
      final shouldCancel = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Xác nhận hủy'),
          content: Text('Bạn có chắc chắn muốn hủy?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Không'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Hủy'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
          ],
        ),
      );

      if (shouldCancel != true) return;

      // Handle dummy data differently
      if (_isDummyData) {
        showToast(
          context: context,
          message: 'Đây là dữ liệu mẫu - đã cập nhật trạng thái thành Hủy',
        );

        setState(() {
          _status = 'Cancelled';
        });

        return;
      }

      setState(() => _isSaving = true);

      // Update status to Cancelled
      final employeeCodes =
          _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeCode).where((code) => code != null && code.isNotEmpty).join(',');

      final employeeNames =
          _selectedEmployees.whereType<EmployeeRecord>().map((e) => e.employeeName).where((name) => name != null && name.isNotEmpty).join(', ');

      final model = MaiDaoRecord(
        id: _currentRecordId,
        date: _dateController.text,
        equipmentCode: _equipmentCodeController.text,
        equipmentName: _equipmentNameController.text,
        materialCode: _materialCodeController.text,
        materialName: _materialNameController.text,
        materialBatch: _materialBatchController.text,
        operationType: _selectedOperationType,
        employeeCodes: employeeCodes,
        employeeNames: employeeNames,
        requestingEmployeeCode: _selectedRequestingEmployee?.employeeCode,
        requestingEmployeeName: _selectedRequestingEmployee?.employeeName,
        note: _noteController.text,
        status: 'Cancelled',
        companyCode: _selectedNhaMay?.saleOrgCode ?? widget.user.companyCode,
      );

      final success = await MaiDaoFunction.saveMaiDao(
        widget.user.token!,
        model,
      );

      if (!mounted) return;

      if (success) {
        showToast(
          context: context,
          message: 'Đã hủy thành công',
        );
        Navigator.pop(context, true);
      } else {
        showToast(
          context: context,
          message: 'Hủy thất bại',
        );
      }
    } catch (error) {
      debugPrint("Error in _cancelRecord: $error");
      if (!mounted) return;
      showToast(
        context: context,
        message: 'Đã xảy ra lỗi khi hủy',
      );
    } finally {
      if (mounted) setState(() => _isSaving = false);
    }
  }

  String _getNextStatus() {
    if (_currentRecordId.isEmpty) {
      return 'Created';
    }

    switch (_status) {
      case 'Created':
        return 'Confirmed';
      case 'Confirmed':
        return 'Completed';
      default:
        return _status ?? 'Created';
    }
  }

  String _getSuccessMessage() {
    if (_currentRecordId.isEmpty) {
      return 'Tạo mới thành công';
    } else {
      switch (_status) {
        case 'Created':
          return 'Xác nhận thành công';
        case 'Confirmed':
          return 'Đã xác nhận thành công';
        case 'Completed':
          return 'Đã xác nhận hoàn thành thành công';
        default:
          return 'Cập nhật thành công';
      }
    }
  }
}
