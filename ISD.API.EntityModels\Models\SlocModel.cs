﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SlocModel", Schema = "tMasterData")]
    public partial class SlocModel
    {
        public SlocModel()
        {
            ReceiveInformationModel = new HashSet<ReceiveInformationModel>();
            WarehouseExportModel = new HashSet<WarehouseExportModel>();
            WarehouseTranferModelSlocExport = new HashSet<WarehouseTranferModel>();
            WarehouseTranferModelSlocImport = new HashSet<WarehouseTranferModel>();
        }

        [Key]
        public Guid Id { get; set; }
        [Required]
        [StringLength(4)]
        public string Sloc { get; set; }
        public string SlocName { get; set; }
        [Required]
        [StringLength(4)]
        public string Plant { get; set; }
        [StringLength(3)]
        public string WarehouseNo { get; set; }
        [StringLength(50)]
        public string DefaultStorageBin { get; set; }
        public bool? ProductionWarehouse { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }

        [InverseProperty("Sloc")]
        public virtual ICollection<ReceiveInformationModel> ReceiveInformationModel { get; set; }
        [InverseProperty("Sloc")]
        public virtual ICollection<WarehouseExportModel> WarehouseExportModel { get; set; }
        [InverseProperty("SlocExport")]
        public virtual ICollection<WarehouseTranferModel> WarehouseTranferModelSlocExport { get; set; }
        [InverseProperty("SlocImport")]
        public virtual ICollection<WarehouseTranferModel> WarehouseTranferModelSlocImport { get; set; }
    }
}