// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import '../element/TableInfo.dart';
// import '../model/getQuantityMaterialUsedShift.dart';
// import '../model/materialRetailList.dart';
//
// class MaterialRetailViewAdd extends StatelessWidget {
//
//   final TextEditingController textEditingController;
//   final Widget getCamera;
//   final MaterialRetailList materialRetailList;
//   final DataQuantityMaterialUsedShift? dataQuantityMaterialUsedShift;
//   final FocusNode focusNode;
//   const MaterialRetailViewAdd(
//       {Key? key,
//         required this.textEditingController,
//         required this.getCamera,
//         required this.materialRetailList,
//         required this.dataQuantityMaterialUsedShift,
//         required this.focusNode
//          }) : super(key: key);
//
//
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//                   width: double.infinity,
//                   padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
//                   decoration: const BoxDecoration(color: Colors.white),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: <Widget> [
//                       getCamera,
//                       const TableInfo(
//                         textCL1: "Sloc:",
//                         textCL2: "",
//                         colorCL1: 0xff303F9F,
//                         colorCL2: 0xFFFFFFFF,
//                       ),
//                       TableInfoNoTop(
//                         textCL1: "Material:",
//                         textCL2: materialRetailList.material ?? "",
//                         colorCL1: 0xff303F9F,
//                         colorCL2: 0xFFFFFFFF,
//                       ),
//                       TableInfoNoTop(
//                         textCL1: "Description:",
//                         textCL2: materialRetailList.description ?? "",
//                         colorCL1: 0xff303F9F,
//                         colorCL2: 0xFFFFFFFF,
//                       ),
//                       IntrinsicHeight(
//                         child: Row(
//                           children: <Widget> [
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 height: double.infinity,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xff303F9F),
//                                   border: Border(
//                                     left: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   "Số lô:", style: TextStyle(fontSize: 11.sp,  color: Colors.white, fontWeight: FontWeight.bold),
//                                 ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 height: double.infinity,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xFFFFFFFF),
//                                   border: Border(
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   materialRetailList.batchNumber ?? "", style: TextStyle(fontSize: 11.sp, color: Colors.black),
//                                 ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 2,
//                               child: Container(
//                                 height: double.infinity,
//                                 alignment: Alignment.centerLeft,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xFFFFFFFF),
//                                   border: Border(
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   "", style: TextStyle(fontSize: 11.sp, color: Colors.black),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       IntrinsicHeight(
//                         child: Row(
//                           children: <Widget> [
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 height: double.infinity,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xff303F9F),
//                                   border: Border(
//                                     left: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   "SL sử dụng trong ngày:", style: TextStyle(fontSize: 11.sp,  color: Colors.white, fontWeight: FontWeight.bold),
//                                 ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 6,
//                               child: Container(
//                                 height: double.infinity,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xFFFFFFFF),
//                                   border: Border(
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(dataQuantityMaterialUsedShift == null ? "":
//                                 dataQuantityMaterialUsedShift!.quantity == null ?"":
//                                 dataQuantityMaterialUsedShift!.quantity.toString() + " ${dataQuantityMaterialUsedShift!.unit ?? ""}" ,
//                                   style: TextStyle(
//                                     fontSize: 11.sp,
//                                     color: Colors.red.shade900,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       IntrinsicHeight(
//                         child: Row(
//                           children: <Widget> [
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 height: double.infinity,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xff303F9F),
//                                   border: Border(
//                                     left: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   "SL sử dụng cho LSX:", style: TextStyle(fontSize: 11.sp,  color: Colors.white, fontWeight: FontWeight.bold),
//                                 ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 4,
//                               child: Container(
//                                 height: double.infinity,
//                                 alignment: Alignment.centerRight,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xFFFFFFFF),
//                                   border: Border(
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//
//                                 child:
//                                 Padding(
//                                   padding: EdgeInsets.symmetric(
//                                       horizontal: 3.w,
//                                       vertical: 3.h),
//                                   child: Container(
//                                     padding: EdgeInsets.symmetric(
//                                         horizontal: 3.w,
//                                         vertical: 3.h),
//                                     decoration: BoxDecoration(
//                                         border: Border.all(width: 0.5.w,  color: Colors.grey.shade500),
//                                         borderRadius: BorderRadius.circular(3.r)
//                                     ),
//                                     child: TextFormField(
//                                       maxLines: null,
//                                       focusNode: focusNode,
//                                       textAlign: TextAlign.center,
//                                       controller: textEditingController,
//                                       style: TextStyle(fontSize: 12.sp),
//                                       keyboardType: TextInputType.number,
//                                       inputFormatters: <TextInputFormatter>[
//                                         FilteringTextInputFormatter.allow(RegExp("[0-9]")),],
//                                       decoration: InputDecoration(
//                                         border: InputBorder.none,
//                                         isDense: true,
//                                         contentPadding: EdgeInsets.zero,
//                                         errorBorder: InputBorder.none,
//                                         disabledBorder: InputBorder.none,
//                                         filled: true,
//                                         fillColor: Colors.white,
//                                         hintStyle: TextStyle(fontSize: 12.sp),),
//                                     ),
//                                   ),
//                                 ),
//                                 // TextFormField(
//                                 //   focusNode: focusNode,
//                                 //   textAlign: TextAlign.right,
//                                 //   maxLines: null,
//                                 //   keyboardType: TextInputType.number,
//                                 //   inputFormatters: <TextInputFormatter>[
//                                 //     FilteringTextInputFormatter
//                                 //         .allow(RegExp(
//                                 //         "[0-9]")),
//                                 //   ],
//                                 //   controller: textEditingController,
//                                 //   style: TextStyle(fontSize: 12.sp),
//                                 //   decoration: InputDecoration(
//                                 //     border: InputBorder.none,
//                                 //     focusedBorder: OutlineInputBorder(
//                                 //       borderRadius:
//                                 //       BorderRadius.circular(3.r),
//                                 //       borderSide: BorderSide(
//                                 //           width: 0.5.w,
//                                 //           color: Colors.grey.shade400),
//                                 //     ),
//                                 //     enabledBorder: OutlineInputBorder(
//                                 //       borderRadius:
//                                 //       BorderRadius.circular(3.r),
//                                 //       borderSide: BorderSide(
//                                 //           width: 0.5.w,
//                                 //           color: Colors.grey.shade400),
//                                 //     ),
//                                 //     errorBorder: InputBorder.none,
//                                 //     disabledBorder: InputBorder.none,
//                                 //     filled: true,
//                                 //     isDense: true,
//                                 //     fillColor: Colors.white,
//                                 //     hintStyle:
//                                 //     TextStyle(fontSize: 12.sp),
//                                 //     contentPadding: EdgeInsets.symmetric(
//                                 //         horizontal: 3.w, vertical: 0.h),
//                                 //   ),
//                                 // ),
//                               ),
//                             ),
//                             Expanded(
//                               flex: 2,
//                               child: Container(
//                                 height: double.infinity,
//                                 alignment: Alignment.centerLeft,
//                                 padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
//                                 decoration: BoxDecoration(
//                                   color: const Color(0xFFFFFFFF),
//                                   border: Border(
//                                     right: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                     bottom: BorderSide(
//                                       color: Colors.black,
//                                       width: 0.5.w,
//                                     ),
//                                   ),
//                                 ),
//                                 child: Text(
//                                   "M3", style: TextStyle(fontSize: 11.sp, color: Colors.black),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//         );
//   }
// }
