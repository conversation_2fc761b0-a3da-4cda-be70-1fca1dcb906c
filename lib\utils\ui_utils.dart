import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ttf/model/FilterLsQC.dart';

class UIUtils {
  static TableBorder createTableBorder() {
    return TableBorder(
      left: BorderSide(color: Colors.black, width: 0.5.w),
      right: BorderSide(color: Colors.black, width: 0.5.w),
      bottom: BorderSide(color: Colors.black, width: 0.5.w),
      top: BorderSide(color: Colors.black, width: 0.5.w),
      horizontalInside: BorderSide(color: Colors.black, width: 0.5.w),
      verticalInside: BorderSide(color: Colors.black, width: 0.5.w),
    );
  }

  static String timeAgoEn(DateTime d) {
    Duration diff = DateTime.now().difference(d);
    if (diff.inDays > 365) return "${(diff.inDays / 365).floor()} ${(diff.inDays / 365).floor() == 1 ? "year" : "years"} ago";
    if (diff.inDays > 30) return "${(diff.inDays / 30).floor()} ${(diff.inDays / 30).floor() == 1 ? "month" : "months"} ago";
    if (diff.inDays > 7) return "${(diff.inDays / 7).floor()} ${(diff.inDays / 7).floor() == 1 ? "week" : "weeks"} ago";
    if (diff.inDays > 0) return "${diff.inDays} ${diff.inDays == 1 ? "day" : "days"} ago";
    if (diff.inHours > 0) return "${diff.inHours} ${diff.inHours == 1 ? "hour" : "hours"} ago";
    if (diff.inMinutes > 0) return "${diff.inMinutes} ${diff.inMinutes == 1 ? "minute" : "minutes"} ago";
    return "just now";
  }

  static String timeAgo(DateTime d) {
    Duration diff = DateTime.now().difference(d);
    if (diff.inDays > 365) return "${(diff.inDays / 365).floor()} năm trước";
    if (diff.inDays > 30) return "${(diff.inDays / 30).floor()} tháng trước";
    if (diff.inDays > 7) return "${(diff.inDays / 7).floor()} tuần trước";
    if (diff.inDays > 0) return "${diff.inDays} ngày trước";
    if (diff.inHours > 0) return "${diff.inHours} giờ trước";
    if (diff.inMinutes > 0) return "${diff.inMinutes} phút trước";
    return "bây giờ";
  }

  static const _vietnamese = 'aAeEoOuUiIdDyY';
  static final _vietnameseRegex = <RegExp>[
    RegExp(r'à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ'),
    RegExp(r'À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ'),
    RegExp(r'è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ'),
    RegExp(r'È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ'),
    RegExp(r'ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ'),
    RegExp(r'Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ'),
    RegExp(r'ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ'),
    RegExp(r'Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ'),
    RegExp(r'ì|í|ị|ỉ|ĩ'),
    RegExp(r'Ì|Í|Ị|Ỉ|Ĩ'),
    RegExp(r'đ'),
    RegExp(r'Đ'),
    RegExp(r'ỳ|ý|ỵ|ỷ|ỹ'),
    RegExp(r'Ỳ|Ý|Ỵ|Ỷ|Ỹ')
  ];

  static String removeVietnameseAccent(final String text) {
    var result = text;
    for (var i = 0; i < _vietnamese.length; ++i) {
      result = result.replaceAll(_vietnameseRegex[i], _vietnamese[i]);
    }
    return result;
  }

  static CommonDates? findMatchingCommonDate(List<CommonDates>? _commonDates, DateTime fromDate, DateTime toDate) {
    if (_commonDates == null) return null;

    // First try exact match
    for (var commonDate in _commonDates!) {
      if (commonDate.catalogCode == "Custom") continue;

      // Get the date range for this common date
      final now = DateTime.now();
      DateTime? startDate;
      DateTime? endDate;

      switch (commonDate.catalogCode) {
        case "Today":
          startDate = DateTime(now.year, now.month, now.day);
          endDate = startDate;
          break;
        case "Yesterday":
          startDate = DateTime(now.year, now.month, now.day - 1);
          endDate = startDate;
          break;
        case "ThisWeek":
          startDate = now.subtract(Duration(days: now.weekday - 1));
          endDate = startDate.add(const Duration(days: 6));
          break;
        case "LastWeek":
          final lastWeekStart = now.subtract(Duration(days: now.weekday + 6));
          startDate = lastWeekStart;
          endDate = lastWeekStart.add(const Duration(days: 6));
          break;
        case "ThisMonth":
          startDate = DateTime(now.year, now.month, 1);
          endDate = DateTime(now.year, now.month + 1, 0);
          break;
        case "LastMonth":
          startDate = DateTime(now.year, now.month - 1, 1);
          endDate = DateTime(now.year, now.month, 0);
          break;
        default:
          continue;
      }

      // Compare with search model dates
      if (startDate != null && endDate != null) {
        // Normalize dates to compare only dates without time
        final normalizedFromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        final normalizedToDate = DateTime(toDate.year, toDate.month, toDate.day);
        final normalizedStartDate = DateTime(startDate.year, startDate.month, startDate.day);
        final normalizedEndDate = DateTime(endDate.year, endDate.month, endDate.day);

        if (normalizedFromDate.isAtSameMomentAs(normalizedStartDate) && normalizedToDate.isAtSameMomentAs(normalizedEndDate)) {
          return commonDate;
        }
      }
    }

    // If no match found, return Custom
    return _commonDates!.firstWhere(
      (element) => element.catalogCode == "Custom",
      orElse: () => _commonDates!.first,
    );
  }
}
