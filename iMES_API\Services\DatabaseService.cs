using iMES_API.Helpers;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace iMES_API.Services
{
    public class DatabaseService
    {
        private readonly DbContextFactory _contextFactory;
        private readonly IConfiguration _configuration;

        public DatabaseService(DbContextFactory contextFactory, IConfiguration configuration)
        {
            _contextFactory = contextFactory;
            _configuration = configuration;
        }

        public async Task<bool> TestConnection(string connectionName = "DefaultConnection")
        {
            try
            {
                var context = _contextFactory.CreateDbContext(connectionName);
                return await context.Database.CanConnectAsync();
            }
            catch (Exception)
            {
                return false;
            }
        }

        public ISD.API.EntityModels.Data.EntityDataContext GetContext(string connectionName = "DefaultConnection")
        {
            return _contextFactory.CreateDbContext(connectionName);
        }

        public ISD.API.EntityModels.Data.EntityDataContext GetProductionContext()
        {
            return _contextFactory.CreateDbContext("DefaultConnection_PRD");
        }

        public ISD.API.EntityModels.Data.EntityDataContext GetDevelopmentContext()
        {
            return _contextFactory.CreateDbContext("DefaultConnection");
        }

        public ISD.API.EntityModels.Data.EntityDataContext GetContextByEnvironment(string environment)
        {
            switch (environment.ToLower())
            {
                case "prod":
                case "production":
                    return GetProductionContext();
                case "dev":
                case "development":
                default:
                    return GetDevelopmentContext();
                case "qas":
                case "qa":
                    return _contextFactory.CreateDbContext("DefaultConnection_QAS");
            }
        }
    }
} 