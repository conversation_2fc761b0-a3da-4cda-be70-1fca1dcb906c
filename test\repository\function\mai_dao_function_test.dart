import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:ttf/repository/function/maiDaoFunction.dart';
import 'package:ttf/model/maiDaoModel.dart';

void main() {
  group('MaiDaoFunction Tests', () {
    group('Helper Methods', () {
      test('getStatusColor returns correct colors', () {
        expect(MaiDaoFunction.getStatusColor('Created'), Colors.orange);
        expect(MaiDaoFunction.getStatusColor('Confirmed'), Colors.blue);
        expect(MaiDaoFunction.getStatusColor('Completed'), Colors.green);
        expect(MaiDaoFunction.getStatusColor('Cancelled'), Colors.red);
        expect(MaiDaoFunction.getStatusColor('Unknown'), Colors.grey);
      });

      test('getStatusText returns correct Vietnamese texts', () {
        expect(MaiDaoFunction.getStatusText('Created'), '<PERSON><PERSON><PERSON> tạo');
        expect(MaiDaoFunction.getStatusText('Confirmed'), 'Đã xác nhận');
        expect(MaiDaoFunction.getStatusText('Completed'), 'Đã mài xong');
        expect(MaiDaoFunction.getStatusText('Cancelled'), 'Đã hủy');
        expect(MaiDaoFunction.getStatusText('Unknown'), 'Unknown');
      });
    });

    group('API Functions - Integration Tests', () {
      const testToken = 'test_token_123';
      const testCompanyCode = '1000';
      const testEquipmentCode = 'EQ001';
      const testSearchTerm = 'search';

      test('fetchMaiDaoList success', () async {
        // Arrange
        final searchModel = MaiDaoSearchModel(
          companyCode: testCompanyCode,
          pageNumber: 1,
          pageSize: 20,
        );

        // Act
        try {
          final result = await MaiDaoFunction.fetchMaiDaoList(testToken, searchModel);

          // Assert (Note: This will actually make HTTP calls, but we can verify the structure)
          print('fetchMaiDaoList test result: ${result?.data?.length ?? 'null'}');
          // In a real test environment, you'd mock the HTTP client
        } catch (e) {
          print('fetchMaiDaoList test - Expected error due to no network/server: $e');
        }
      });

      test('fetchMaiDaoDetail with valid ID', () async {
        // Act
        try {
          final result = await MaiDaoFunction.fetchMaiDaoDetail(testToken, '1');
          print('fetchMaiDaoDetail test result: ${result?.id ?? 'null'}');
        } catch (e) {
          print('fetchMaiDaoDetail test - Expected error due to no network/server: $e');
        }
      });

      test('createMaiDao with new record', () async {
        // Arrange
        final newRecord = MaiDaoRecord(
          id: null, // New record
          date: '25/04/2025',
          equipmentCode: 'EQ001',
          equipmentName: 'Test Equipment',
          materialCode: 'MAT001',
          materialName: 'Test Material',
          operationType: 'Mài dao',
          employeeCodes: 'NV001',
          employeeNames: 'Test Employee',
          status: 'Created',
          companyCode: testCompanyCode,
        );

        // Act
        try {
          final result = await MaiDaoFunction.createMaiDao(testToken, newRecord);
          print('createMaiDao test result: $result');
        } catch (e) {
          print('createMaiDao test - Expected error due to no network/server: $e');
        }
      });

      test('updateMaiDao with existing record', () async {
        // Arrange
        final existingRecord = MaiDaoRecord(
          id: '1',
          date: '25/04/2025',
          equipmentCode: 'EQ001',
          equipmentName: 'Updated Equipment',
          materialCode: 'MAT001',
          materialName: 'Updated Material',
          operationType: 'Đắp dao',
          employeeCodes: 'NV001,NV002',
          employeeNames: 'Employee 1, Employee 2',
          status: 'Confirmed',
          companyCode: testCompanyCode,
        );

        // Act
        try {
          final result = await MaiDaoFunction.updateMaiDao(testToken, existingRecord);
          print('updateMaiDao test result: $result');
        } catch (e) {
          print('updateMaiDao test - Expected error due to no network/server: $e');
        }
      });

      test('saveMaiDao routes to createMaiDao for new records', () async {
        // Arrange
        final newRecord = MaiDaoRecord(
          id: null,
          date: '25/04/2025',
          equipmentCode: 'EQ001',
          companyCode: testCompanyCode,
        );

        // Act
        try {
          final result = await MaiDaoFunction.saveMaiDao(testToken, newRecord);
          print('saveMaiDao (create) test result: $result');
        } catch (e) {
          print('saveMaiDao (create) test - Expected error: $e');
        }
      });

      test('saveMaiDao routes to updateMaiDao for existing records', () async {
        // Arrange
        final existingRecord = MaiDaoRecord(
          id: '1',
          date: '25/04/2025',
          equipmentCode: 'EQ001',
          companyCode: testCompanyCode,
        );

        // Act
        try {
          final result = await MaiDaoFunction.saveMaiDao(testToken, existingRecord);
          print('saveMaiDao (update) test result: $result');
        } catch (e) {
          print('saveMaiDao (update) test - Expected error: $e');
        }
      });

      test('fetchEquipment with search term', () async {
        // Act
        try {
          final result = await MaiDaoFunction.fetchEquipment(testToken, testCompanyCode, testSearchTerm);
          print('fetchEquipment test result: ${result.length} items');
          for (var item in result.take(3)) {
            print('  Equipment: ${item.equipmentCode} - ${item.equipmentName}');
          }
        } catch (e) {
          print('fetchEquipment test - Expected error due to no network/server: $e');
        }
      });

      test('fetchMaterials with search term', () async {
        // Act
        try {
          final result = await MaiDaoFunction.fetchMaterials(testToken, testCompanyCode, testSearchTerm);
          print('fetchMaterials test result: ${result.length} items');
          for (var item in result.take(3)) {
            print('  Material: ${item.materialCode} - ${item.materialName}');
          }
        } catch (e) {
          print('fetchMaterials test - Expected error due to no network/server: $e');
        }
      });

      test('fetchEmployees for company', () async {
        // Act
        try {
          final result = await MaiDaoFunction.fetchEmployees(testToken, testCompanyCode);
          print('fetchEmployees test result: ${result.length} employees');
          for (var emp in result.take(3)) {
            print('  Employee: ${emp.employeeCode} - ${emp.employeeName}');
          }
        } catch (e) {
          print('fetchEmployees test - Expected error due to no network/server: $e');
        }
      });

      test('checkExistingRecord for equipment', () async {
        // Act
        try {
          final result = await MaiDaoFunction.checkExistingRecord(testToken, testEquipmentCode, testCompanyCode);
          print('checkExistingRecord test result: ${result?.id ?? 'No existing record'}');
          if (result != null) {
            print('  Found existing record: ${result.equipmentCode} - ${result.status}');
          }
        } catch (e) {
          print('checkExistingRecord test - Expected error due to no network/server: $e');
        }
      });
    });

    group('Data Model Tests', () {
      test('MaiDaoRecord JSON serialization', () {
        // Arrange
        final record = MaiDaoRecord(
          id: '1',
          date: '25/04/2025',
          equipmentCode: 'EQ001',
          equipmentName: 'Test Equipment',
          materialCode: 'MAT001',
          materialName: 'Test Material',
          operationType: 'Mài dao',
          employeeCodes: 'NV001,NV002',
          employeeNames: 'Employee 1, Employee 2',
          requestingEmployeeCode: 'NV003',
          requestingEmployeeName: 'Requesting Employee',
          note: 'Test note',
          status: 'Created',
          companyCode: '1000',
          createdDate: '2025-04-25T08:00:00',
        );

        // Act
        final json = record.toJson();
        final recreatedRecord = MaiDaoRecord.fromJson(json);

        // Assert
        expect(recreatedRecord.id, record.id);
        expect(recreatedRecord.equipmentCode, record.equipmentCode);
        expect(recreatedRecord.materialCode, record.materialCode);
        expect(recreatedRecord.operationType, record.operationType);
        expect(recreatedRecord.status, record.status);
        print('JSON serialization test passed');
      });

      test('MaiDaoSearchModel creation', () {
        // Arrange & Act
        final searchModel = MaiDaoSearchModel(
          companyCode: '1000',
          equipmentCode: 'EQ001',
          materialCode: 'MAT001',
          operationType: 'Mài dao',
          status: 'Created',
          fromDate: DateTime(2025, 4, 1),
          toDate: DateTime(2025, 4, 30),
          pageNumber: 1,
          pageSize: 20,
        );

        // Assert
        expect(searchModel.companyCode, '1000');
        expect(searchModel.equipmentCode, 'EQ001');
        expect(searchModel.pageNumber, 1);
        expect(searchModel.pageSize, 20);
        print('MaiDaoSearchModel creation test passed');
      });
    });

    group('Error Handling Tests', () {
      test('fetchMaiDaoList handles network error gracefully', () async {
        // Act
        try {
          final result = await MaiDaoFunction.fetchMaiDaoList('invalid_token', MaiDaoSearchModel(companyCode: '1000'));
          expect(result, isNull); // Should return null on error
          print('Error handling test passed - returned null');
        } catch (e) {
          print('Error handling test - caught exception: $e');
          // This is also acceptable behavior
        }
      });

      test('fetchEquipment handles empty search term', () async {
        const testToken = 'test_token_123';
        const testCompanyCode = '1000';

        // Act
        try {
          final result = await MaiDaoFunction.fetchEquipment(testToken, testCompanyCode, '');
          expect(result, isA<List<EquipmentItem>>());
          print('Empty search term test passed - returned list with ${result.length} items');
        } catch (e) {
          print('Empty search term test - caught exception: $e');
        }
      });
    });
  });
}
