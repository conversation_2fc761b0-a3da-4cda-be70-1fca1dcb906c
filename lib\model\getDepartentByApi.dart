class GetDepartentByApi {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetDepartentBy? data;
  String? additionalData;

  GetDepartentByApi(
      {this.code,
        this.isSuccess,
        this.message,
        this.data,
        this.additionalData});

  GetDepartentByApi.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetDepartentBy.fromJson(json['data']) : null;
    additionalData = json['additionalData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['additionalData'] = additionalData;
    return data;
  }
}

class DataGetDepartentBy {
  String? department;
  String? workshop;

  DataGetDepartentBy({this.department, this.workshop});

  DataGetDepartentBy.fromJson(Map<String, dynamic> json) {
    department = json['department'];
    workshop = json['workshop'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['department'] = department;
    data['workshop'] = workshop;
    return data;
  }
}
