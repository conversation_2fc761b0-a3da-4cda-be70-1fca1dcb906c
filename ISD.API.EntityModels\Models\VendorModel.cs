﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("VendorModel", Schema = "tMasterData")]
    public partial class VendorModel
    {
        [Key]
        public Guid VendorId { get; set; }
        [StringLength(10)]
        public string SupplierNumber { get; set; }
        [StringLength(40)]
        public string ShortName { get; set; }
        [StringLength(255)]
        public string LongName { get; set; }
        [StringLength(255)]
        public string Address { get; set; }
        [StringLength(16)]
        public string Telephone { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        public bool? Actived { get; set; }
    }
}