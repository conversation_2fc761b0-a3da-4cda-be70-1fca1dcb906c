﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("RoutingInventorModel", Schema = "MES")]
    public partial class RoutingInventorModel
    {
        [Key]
        public Guid RoutingInventorId { get; set; }
        [StringLength(10)]
        public string MANDT { get; set; }
        [StringLength(50)]
        public string VERSO { get; set; }
        [StringLength(10)]
        public string ARBPL { get; set; }
        [StringLength(10)]
        public string ARBPL_SUB { get; set; }
        [StringLength(10)]
        public string WERKS { get; set; }
        [StringLength(50)]
        public string MATNR { get; set; }
        [StringLength(10)]
        public string ITMNO { get; set; }
        [StringLength(500)]
        public string KTEXT { get; set; }
        [StringLength(50)]
        public string IDNRK { get; set; }
        [StringLength(50)]
        public string IDNRK_MES { get; set; }
        public string MAKTX { get; set; }
        [StringLength(10)]
        public string BMEIN { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? BMSCH { get; set; }
        [StringLength(100)]
        public string PLNNR { get; set; }
        [StringLength(100)]
        public string LTXA1 { get; set; }
        [StringLength(10)]
        public string MEINS { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? MENGE { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW01 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW02 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW03 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW04 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW05 { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? VGW06 { get; set; }
        [StringLength(50)]
        public string ACTON { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ANDAT { get; set; }
        [StringLength(50)]
        public string AENAM { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? AEDAT { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
    }
}