﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using  Microsoft.Extensions.Configuration;

namespace ISD.API.Constant
{
    public static class SystemConfig
    {
        //Format date
        public const string DateTimeFormat = "{0:dd/MM/yyyy HH:mm:ss}";

        //Format number
        public const string QuantityFormat = "{0:n0}";

        //Format currency
        public const string CurrencyFormat = "{0:n0}";


    }
}
