import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FormLayout extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final Widget? bottomButton;

  const FormLayout({
    Key? key,
    required this.title,
    required this.children,
    this.bottomButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(5.w),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(width: 0.5, color: Colors.grey.shade400),
            ),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.h),
                  decoration: const BoxDecoration(
                    color: Color(0xff0052cc),
                  ),
                  child: Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13.sp, color: Colors.white),
                  ),
                ),
                SizedBox(height: 10.h),
                ...children,
                SizedBox(height: 10.h),
              ],
            ),
          ),
          if (bottomButton != null) ...[
            SizedBox(height: 10.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: bottomButton,
            ),
            SizedBox(height: 10.h),
          ],
        ],
      ),
    );
  }
}

class FormInputField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final bool required;
  final String? Function(String?)? validator;
  final bool enabled;
  final ValueChanged<String>? onChanged;
  final TextStyle? style;

  const FormInputField({
    Key? key,
    required this.label,
    required this.controller,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.required = true,
    this.validator,
    this.enabled = true,
    this.onChanged,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            flex: 7,
            child: Column(
              children: <Widget>[
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(width: 0.5, color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                  child: TextFormField(
                    controller: controller,
                    keyboardType: keyboardType,
                    inputFormatters: inputFormatters,
                    maxLines: maxLines,
                    enabled: enabled,
                    onChanged: onChanged,
                    style: style ??
                        TextStyle(
                          fontSize: 12.sp,
                          color: Colors.black,
                        ),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 4.w),
                      filled: true,
                      fillColor: enabled ? Colors.white : Colors.grey[100],
                      disabledBorder: InputBorder.none,
                    ),
                    validator: validator ?? (required ? (value) => value?.isEmpty == true ? 'Vui lòng nhập ${label.toLowerCase()}' : null : null),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class FormSubmitButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;

  const FormSubmitButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: isLoading ? Colors.grey : const Color(0xff0052cc),
        padding: EdgeInsets.symmetric(vertical: 12.h),
        minimumSize: const Size(double.infinity, 0),
      ),
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20, // Changed height to 20
              child: CircularProgressIndicator(color: Colors.white),
            )
          : Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                height: 20 / 12.sp, // Set the height to match the CircularProgressIndicator
              ),
            ),
    );
  }
}
