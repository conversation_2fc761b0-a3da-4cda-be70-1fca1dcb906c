import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';

import '../../model/userModel.dart';
import '../../screenArguments/CommonScreenUserArgument.dart';
import '../../screenArguments/CommonScreenArgument.dart';
import '../../utils/ui_helpers.dart';
import 'CommonMenuButton.dart';

class KiemTraChatLuong extends StatefulWidget {
  const KiemTraChatLuong(
      {Key? key,
      required this.permission,
      required this.token,
      required this.plant,
      required this.dateTimeOld,
      required this.accountId,
      required this.user})
      : super(key: key);

  final Permission? permission;
  final String token;
  final String plant;
  final String dateTimeOld;
  final String accountId;
  final DataUser user;

  @override
  _KiemTraChatLuongState createState() => _KiemTraChatLuongState();
}

class _KiemTraChatLuongState extends State<KiemTraChatLuong> {
  bool _isLoadingScroll = false;
  late bool _timeOut;
  bool _isLoading = false;
  bool _isNotWifi = false;
  String _error = "";
  String title = 'Kiểm tra chất lượng';

  @override
  void initState() {
    super.initState();

    // debugPrint(DateFormat("yyyy-MM-ddThh:mm:ss").parse('9/19/2023 2:19:00.000 AM').toString());
    // debugPrint(DateFormat("yyyy-MM-dd HH:mm").parse(DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())).toString());
    // _getListData();
    // _controllerListView.addListener(_goScroll);
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Todo set rule
  List<Map<String, dynamic>> get buttonList => [
        {
          'text': "Báo cáo nghiệm thu đầu vào",
          'code': "NghiemThuDauVao",
          'icon': Icons.note_alt_outlined,
          'route': "/BaoCaoDauVao",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user)
        },
        {
          'text': "Báo cáo nghiệm thu đầu vào 2",
          'code': "NghiemThuDauVao",
          'icon': Icons.note_alt_outlined,
          'route': "/BaoCaoDauVao2",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
          'subText': "Nghiệm thu đầu vào tích hợp SAP",
        },
        {
          'text': "Phiếu KCS công đoạn",
          'code': "NghiemThuCongDoan",
          'icon': Icons.edit_note_rounded,
          'route': "/PhieuKCSCongDoan",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
          'subText': "KCS kiểm tra trên chuyền",
        },
        {
          'text': "Báo cáo QAQC nghiệm thu",
          'code': "QAQCNghiemThu",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQAQCNghiemThu",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
        },
        {
          'text': "Báo cáo nghiệm thu SP mẫu",
          'code': "QCMau",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCMau",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
        },
        {
          'text': "Báo cáo nghiệm thu tại nhà gia công",
          'code': "QCGiaCong",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCGiaCong",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
        },
        {
          'text': "Báo cáo QAQC sản phẩm dùng barcode khách",
          'code': "QCSanPham",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCSanPham",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
        },
        {
          'text': "Báo cáo QA hiện trường",
          'code': "QCHienTruong",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCHienTruong",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
          'isTest': true
        },
        {
          'text': "Báo cáo Mẫu đầu chuyền",
          'code': "QCMauDauChuyen",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCMauDauChuyen",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
          // 'isTest': true
        },
        {
          'text': "Báo cáo Test lab",
          'code': "QCTestLab",
          'icon': Icons.note_alt_outlined,
          // 'route': "",
          'route': "/BaoCaoQCTestLab",
          'arguments': CommonScreenUserArgument(null, widget.dateTimeOld, null, null, widget.user),
          'isTest': true
        },
      ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xff0052cc),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          // padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 14.sp, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14.sp, color: Colors.white),
        ),
        actions: [
          // IconButton(
          //   highlightColor: Colors.transparent,
          //   splashColor: Colors.transparent,
          //   hoverColor: Colors.transparent,
          //   icon: Icon(
          //     Icons.camera_alt,
          //     size: 19.sp,
          //     color: Colors.white,
          //   ),
          //   onPressed: _isLoading == true ? null : () async {},
          // ),
        ],
      ),
      body: _isLoading == true
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(10.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: buttonList
                      .where((data) =>
                          data['isTest'] == true ||
                          widget.permission!.mobileScreenModel!.firstWhereOrNull(
                                (element) =>
                                    element.screenCode == data['code'] ||
                                    element.screenCode == (data['code'] + '2') ||
                                    element.screenCode == (data['code'] + '3') ||
                                    element.screenCode == (data['code'] + '4'),
                              ) !=
                              null)
                      .map((data) => Padding(
                            padding: const EdgeInsets.only(bottom: 10.0),
                            child: CommonMenuButton(
                              text: data['text'],
                              icon: Icon(data['icon'], size: 20, color: Colors.white),
                              route: () {
                                if (data['route'] == '') {
                                  showAlert(
                                    context: context,
                                    title: 'Thông báo',
                                    content: 'Chức năng này đang phát triển',
                                    buttonsWithOnPressed: {
                                      'OK': () => Navigator.of(context).pop(),
                                    },
                                  );
                                  return;
                                }
                                Navigator.pushNamed(context, data['route'], arguments: data['arguments']);
                              },
                              subText: data['subText'],
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
    );
  }
}
