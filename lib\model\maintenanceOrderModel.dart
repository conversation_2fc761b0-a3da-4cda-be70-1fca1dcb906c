class MaintenanceOrderModel {
  final List<MaintenanceOrder> data;
  final String message;
  final bool status;
  final int totalCount;
  final int pageNumber;
  final int pageSize;
  final int totalPages;

  MaintenanceOrderModel({
    required this.data,
    required this.message,
    required this.status,
    required this.totalCount,
    required this.pageNumber,
    required this.pageSize,
    required this.totalPages,
  });

  factory MaintenanceOrderModel.fromJson(Map<String, dynamic> json) {
    return MaintenanceOrderModel(
      data: (json['data']['items'] as List).map((item) => MaintenanceOrder.fromJson(item)).toList(),
      message: json['message'] ?? '',
      status: json['isSuccess'] ?? false,
      totalCount: json['data']['totalCount'] ?? 0,
      pageNumber: json['data']['pageNumber'] ?? 1,
      pageSize: json['data']['pageSize'] ?? 20,
      totalPages: json['data']['totalPages'] ?? 1,
    );
  }
}

class MaintenanceOrder {
  int? index;
  String? orderNumber;
  String? orderType;
  String? description;
  String? creationDate;
  String? createdBy;
  String? plant;
  String? locationPlant;
  String? costCenter;
  String? mainWorkCenter;
  String? equipmentNumber;
  String? equipmentName;
  String? functionalLocation;
  String? location;
  String? status;
  String? closeDate;

  MaintenanceOrder({
    this.index,
    this.orderNumber,
    this.orderType,
    this.description,
    this.creationDate,
    this.createdBy,
    this.plant,
    this.locationPlant,
    this.costCenter,
    this.mainWorkCenter,
    this.equipmentNumber,
    this.equipmentName,
    this.functionalLocation,
    this.location,
    this.status,
    this.closeDate,
  });

  factory MaintenanceOrder.fromJson(Map<String, dynamic> json) {
    return MaintenanceOrder(
      index: json['index'],
      orderNumber: json['orderNumber'],
      orderType: json['orderType'],
      description: json['description'],
      creationDate: json['creationDate'],
      createdBy: json['createdBy'],
      plant: json['plant'],
      locationPlant: json['locationPlant'],
      costCenter: json['costCenter'],
      mainWorkCenter: json['mainWorkCenter'],
      equipmentNumber: json['equipmentNumber'],
      equipmentName: json['equipmentName'],
      functionalLocation: json['functionalLocation'],
      location: json['location'],
      status: json['status'],
      closeDate: json['closeDate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'orderNumber': orderNumber,
      'orderType': orderType,
      'description': description,
      'creationDate': creationDate,
      'createdBy': createdBy,
      'plant': plant,
      'locationPlant': locationPlant,
      'costCenter': costCenter,
      'mainWorkCenter': mainWorkCenter,
      'equipmentNumber': equipmentNumber,
      'equipmentName': equipmentName,
      'functionalLocation': functionalLocation,
      'location': location,
      'status': status,
      'closeDate': closeDate,
    };
  }
}

class MaintenanceOrderSearchModel {
  final String companyCode;
  final String? orderNumber;
  final String? status;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int pageNumber;
  final int pageSize;

  MaintenanceOrderSearchModel({
    required this.companyCode,
    this.orderNumber,
    this.status,
    this.fromDate,
    this.toDate,
    this.pageNumber = 1,
    this.pageSize = 20,
  });

  Map<String, dynamic> toJson() {
    return {
      'companyCode': companyCode,
      'orderNumber': orderNumber,
      'status': status,
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'pageNumber': pageNumber,
      'pageSize': pageSize,
    };
  }
}

class CreateMaintenanceOrderModel {
  final String plant;
  final String equipment;
  final String shortText;
  final String workCenter;
  final String operationDescription;
  final String? locationPlant;
  final String? costCenter;

  CreateMaintenanceOrderModel({
    required this.plant,
    required this.equipment,
    required this.shortText,
    required this.workCenter,
    this.operationDescription = '',
    this.locationPlant,
    this.costCenter,
  });

  Map<String, dynamic> toJson() {
    return {
      'plant': plant,
      'equipment': equipment,
      'shortText': shortText,
      'workCenter': workCenter,
      'operationDescription': operationDescription,
      if (locationPlant != null) 'locationPlant': locationPlant,
      if (costCenter != null) 'costCenter': costCenter,
    };
  }

  factory CreateMaintenanceOrderModel.fromJson(Map<String, dynamic> json) {
    return CreateMaintenanceOrderModel(
      plant: json['plant'] ?? '',
      equipment: json['equipment'] ?? '',
      shortText: json['shortText'] ?? '',
      workCenter: json['workCenter'] ?? '',
      operationDescription: json['operationDescription'] ?? '',
      locationPlant: json['locationPlant'],
      costCenter: json['costCenter'],
    );
  }
}

class MaintenanceOrderResponse {
  final bool success;
  final String message;
  final String? orderNumber;

  MaintenanceOrderResponse({
    required this.success,
    required this.message,
    this.orderNumber,
  });
}
