class ConfirmWorkCenter {
  int? code;
  bool? isSuccess;
  String? message;
  DataConfirmWorkCenter? data;
  List<AdditionalDataConfirmWorkCenter>? additionalData;

  ConfirmWorkCenter(
      {this.code,
        this.isSuccess,
        this.message,
        this.data,
        this.additionalData});

  ConfirmWorkCenter.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataConfirmWorkCenter.fromJson(json['data']) : null;
    if (json['additionalData'] != null) {
      additionalData = <AdditionalDataConfirmWorkCenter>[];
      json['additionalData'].forEach((v) {
        additionalData!.add(AdditionalDataConfirmWorkCenter.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (additionalData != null) {
      data['additionalData'] =
          additionalData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataConfirmWorkCenter {
  String? taskId;
  String? parentTaskId;
  String? unit;
  String? fromStepCode;
  String? fromStepName;
  String? productAttributes;
  double? productAttributesQty;
  String? workDate;
  int? qty;
  String? productCode;
  String? productName;
  String? productId;
  String? productionOrderSAP;
  String? createByFullName;
  String? createTime;
  String? lastEditByFullName;
  String? lastEditTime;
  bool? actived;
  String? toStepCode;
  String? toStepName;
  String? productionOrder;
  String? summary;
  String? productAttributesName;
  String? poT12;
  String? productAttributesUnit;
  double? productAttributesQtyD;
  List<ListDetail>? listDetail;

  DataConfirmWorkCenter(
      {this.taskId,
        this.parentTaskId,
        this.unit,
        this.fromStepCode,
        this.fromStepName,
        this.productAttributes,
        this.productAttributesQty,
        this.workDate,
        this.qty,
        this.productCode,
        this.productName,
        this.productId,
        this.productionOrderSAP,
        this.createByFullName,
        this.createTime,
        this.lastEditByFullName,
        this.lastEditTime,
        this.actived,
        this.toStepCode,
        this.toStepName,
        this.productionOrder,
        this.summary,
        this.productAttributesName,
        this.poT12,
        this.productAttributesUnit,
        this.productAttributesQtyD,
        this.listDetail});

  DataConfirmWorkCenter.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'];
    parentTaskId = json['parentTaskId'];
    unit = json['unit'];
    fromStepCode = json['fromStepCode'];
    fromStepName = json['fromStepName'];
    productAttributes = json['productAttributes'];
    productAttributesQty = json['productAttributesQty'];
    workDate = json['workDate'];
    qty = json['qty'];
    productCode = json['productCode'];
    productName = json['productName'];
    productId = json['productId'];
    productionOrderSAP = json['productionOrder_SAP'];
    createByFullName = json['createByFullName'];
    createTime = json['createTime'];
    lastEditByFullName = json['lastEditByFullName'];
    lastEditTime = json['lastEditTime'];
    actived = json['actived'];
    toStepCode = json['toStepCode'];
    toStepName = json['toStepName'];
    productionOrder = json['productionOrder'];
    summary = json['summary'];
    productAttributesName = json['productAttributesName'];
    poT12 = json['poT12'];
    productAttributesUnit = json['productAttributesUnit'];
    productAttributesQtyD = json['productAttributesQtyD'];
    if (json['listDetail'] != null) {
      listDetail = <ListDetail>[];
      json['listDetail'].forEach((v) {
        listDetail!.add(ListDetail.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['taskId'] = taskId;
    data['parentTaskId'] = parentTaskId;
    data['unit'] = unit;
    data['fromStepCode'] = fromStepCode;
    data['fromStepName'] = fromStepName;
    data['productAttributes'] = productAttributes;
    data['productAttributesQty'] = productAttributesQty;
    data['workDate'] = workDate;
    data['qty'] = qty;
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['productId'] = productId;
    data['productionOrder_SAP'] = productionOrderSAP;
    data['createByFullName'] = createByFullName;
    data['createTime'] = createTime;
    data['lastEditByFullName'] = lastEditByFullName;
    data['lastEditTime'] = lastEditTime;
    data['actived'] = actived;
    data['toStepCode'] = toStepCode;
    data['toStepName'] = toStepName;
    data['productionOrder'] = productionOrder;
    data['summary'] = summary;
    data['productAttributesName'] = productAttributesName;
    data['poT12'] = poT12;
    data['productAttributesUnit'] = productAttributesUnit;
    data['productAttributesQtyD'] = productAttributesQtyD;
    if (listDetail != null) {
      data['listDetail'] = listDetail!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListDetail {
  String? ktext;
  double? quantity;
  String? productId;
  String? itmno;
  String? customerReference;
  int? phase;

  ListDetail(
      {this.ktext,
        this.quantity,
        this.productId,
        this.itmno,
        this.customerReference,
        this.phase});

  ListDetail.fromJson(Map<String, dynamic> json) {
    ktext = json['ktext'];
    quantity = json['quantity'];
    productId = json['productId'];
    itmno = json['itmno'];
    customerReference = json['customerReference'];
    phase = json['phase'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ktext'] = this.ktext;
    data['quantity'] = this.quantity;
    data['productId'] = this.productId;
    data['itmno'] = this.itmno;
    data['customerReference'] = this.customerReference;
    data['phase'] = this.phase;
    return data;
  }
}

class AdditionalDataConfirmWorkCenter {
  String? arbpLSUB;
  String? display;

  AdditionalDataConfirmWorkCenter({this.arbpLSUB, this.display});

  AdditionalDataConfirmWorkCenter.fromJson(Map<String, dynamic> json) {
    arbpLSUB = json['arbpL_SUB'];
    display = json['display'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['arbpL_SUB'] = arbpLSUB;
    data['display'] = display;
    return data;
  }
}
