class GetBarcodeReceiveModel {
  int? code;
  bool? isSuccess;
  String? message;
  DataGetBarcodeReceive? data;


  GetBarcodeReceiveModel(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetBarcodeReceiveModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataGetBarcodeReceive.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetBarcodeReceive {
  String? productCode;
  String? productName;
  String? batchNumber;
  String? manuDate;


  DataGetBarcodeReceive({this.productCode, this.productName, this.batchNumber});

  DataGetBarcodeReceive.fromJson(Map<String, dynamic> json) {
    productCode = json['productCode'];
    productName = json['productName'];
    batchNumber = json['batchNumber'];
    manuDate = json['manuDate'];
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['productCode'] = productCode;
    data['productName'] = productName;
    data['batchNumber'] = batchNumber;
    data['manuDate'] = manuDate;
    data['batchNumber'] = batchNumber;
    return data;
  }
}