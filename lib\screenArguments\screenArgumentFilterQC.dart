import 'package:ttf/model/commonDateModel.dart';

import '../model/FilterLsQC.dart';

class ScreenArgumentFilterQC {
  final List<SalesOrgCodes> salesOrgCodes;
  final List<WorkCenters> workCenters;
  final List<WorkShops> workShops;
  final List<CommonDates> commonDates;
  final List<ResultsDataQC> results;
  final List<StatusData> lsStatus;
  final CommonDateModel commonDateModel;
  final FilterQCModel filterLSQC;
  ScreenArgumentFilterQC(
      this.salesOrgCodes, this.workCenters, this.workShops, this.commonDates, this.results, this.lsStatus, this.commonDateModel, this.filterLSQC);
}
