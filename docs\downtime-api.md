# Downtime API Documentation

## Base Configuration

The API supports both QAS (Quality Assurance) and PRD (Production) environments:
- QAS URL: Configured in `UrlApi.baseUrl_2Qas`
- PRD URL: Configured in `UrlApi.baseUrl_2Prd`

Base endpoint: `/api/v1/MES/Downtime/`

## Authentication

All endpoints require authentication via token-based authorization:
```http
Authorization: Bearer {token}
Content-Type: application/json
```

## Endpoints

### 1. List Downtime Records

```http
POST /DowntimeList
```

Retrieves a paginated list of downtime records.

**Request Body:**
```json
{
  "companyCode": "string",
  "pageNumber": 1,
  "pageSize": 20,
  "fromDate": "2024-03-25",
  "toDate": "2024-03-25",
  "status": "string",
  "departmentCode": "string"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Success",
  "data": [
    {
      "id": "string",
      "date": "2024-03-25",
      "departmentCode": "string",
      "departmentName": "string",
      "stepCode": "string",
      "stepName": "string",
      "startTime": "08:00",
      "endTime": "10:00",
      "reason": "string",
      "responsibleTeam": "string",
      "responsibleDepartment": "string",
      "personCausedDowntimeCodeMany": "string",
      "note": "string",
      "status": "string",
      "verificationStatus": "string",
      "companyCode": "string",
      "createdDate": "2024-03-25T08:00:00",
      "createBy": "string",
      "updatedDate": "2024-03-25T10:00:00",
      "updateBy": "string"
    }
  ]
}
```

### 2. Get Downtime Detail

```http
GET /{id}
```

Retrieves details of a specific downtime record.

**Response:**
```json
{
  "status": true,
  "message": "Success",
  "data": {
    "id": "string",
    "date": "2024-03-25",
    "departmentCode": "string",
    "departmentName": "string",
    "stepCode": "string",
    "stepName": "string",
    "startTime": "08:00",
    "endTime": "10:00",
    "reason": "string",
    "responsibleTeam": "string",
    "responsibleDepartment": "string",
    "personCausedDowntimeCodeMany": "string",
    "note": "string",
    "status": "string",
    "verificationStatus": "string",
    "companyCode": "string"
  }
}
```

### 3. Create Downtime Record

```http
POST /CreateDowntime
```

Creates a new downtime record.

**Request Body:**
```json
{
  "date": "2024-03-25",
  "departmentCode": "string",
  "stepCode": "string",
  "startTime": "08:00",
  "endTime": "10:00",
  "reason": "string",
  "responsibleTeam": "string",
  "responsibleDepartment": "string",
  "personCausedDowntimeCodeMany": "string",
  "note": "string",
  "companyCode": "string"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Success",
  "data": {
    "id": "string"
  }
}
```

### 4. Update Downtime Record

```http
PUT /{id}
```

Updates an existing downtime record.

**Request Body:**
Same as Create Downtime Record

**Response:**
```json
{
  "status": true,
  "message": "Success"
}
```

### 5. Get Downtime History

```http
GET /{id}/history?companyCode={companyCode}
```

Retrieves the history of changes for a specific downtime record.

**Response:**
```json
{
  "isSuccess": true,
  "message": "Success",
  "data": [
    {
      "historyId": "string",
      "downtimeId": "string",
      "action": "string",
      "actionDisplay": "string",
      "oldStatus": "string",
      "oldStatusDisplay": "string",
      "newStatus": "string",
      "newStatusDisplay": "string",
      "changedBy": "string",
      "changedByName": "string",
      "changedDate": "2024-03-25T08:00:00",
      "verifierRole": "string",
      "comment": "string"
    }
  ]
}
```

### 6. Get List Department

```http
GET /GetListDepartment?companyCode={companyCode}
```

Retrieves list of departments for the specified company.

**Response:**
```json
{
  "status": true,
  "message": "Success",
  "data": [
    {
      "departmentCode": "string",
      "departmentName": "string"
    }
  ]
}
```

### 7. Get List Step Code

```http
GET /GetListStepCode?companyCode={companyCode}
```

Retrieves list of step codes for the specified company.

**Response:**
```json
{
  "isSuccess": true,
  "message": "Success",
  "data": [
    {
      "stepCode": "string",
      "stepName": "string"
    }
  ]
}
```

### 8. Get Employees

```http
GET /GetEmployees?companyCode={companyCode}
```

Retrieves list of employees for the specified company.

**Response:**
```json
{
  "isSuccess": true,
  "message": "Success",
  "data": [
    {
      "employeeCode": "string",
      "employeeName": "string"
    }
  ]
}
```

## Status Codes

- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

## Error Handling

All endpoints return errors in the following format:

```json
{
  "status": false,
  "message": "Error message description"
}
```

## Environment Selection

The API automatically selects the appropriate base URL based on the environment:

```dart
final environment = await SecureStorage.getString("environment", null);
final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
```

## Testing

The API includes a mock data mode that can be enabled for testing:

```dart
static const bool useMockData = false; // Set to true for mock data
```

When enabled, the API will return mock data instead of making actual HTTP requests. This is useful for development and testing without requiring a backend connection.
