﻿using Hangfire;
using ISD.API.EntityModels.Data;
using ISD.API.EntityModels.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace iMES_API.Infrastructures.JobSchedulers
{
    public class PushNotificationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IOneSignalService _oneSignalService;

        public PushNotificationService(
            IServiceProvider serviceProvider,
            IOneSignalService oneSignalService = null)
        {
            this._serviceProvider = serviceProvider;
            this._oneSignalService = oneSignalService;
        }

        public enum NotificationStatus
        {
            Pending, // 0 pending
            Failed, // 1 failed
            Sent // 2 sent
        }

        //[DisableConcurrentExecution(30)] // Hangfire.Storage.DistributedLockTimeoutException: Timeout expired. The timeout elapsed prior to obtaining a distributed lock on the 'TTF_MES:PushNotificationService.Run' resource.
        [DisableConcurrentExecution(300)] // Increased from 90 to 300 seconds
        public async Task Run()
        {
            try
            {
                //Create scope db context
                using var scope = _serviceProvider.CreateScope();
                using var _context = scope.ServiceProvider.GetRequiredService<EntityDataContext>();

                const int batchSize = 20; // Process notifications in smaller batches
                var pendingNotifications = await _context.PushNotificationModel
                                                    .Where(n => n.Status == (int)NotificationStatus.Pending)
                                                    .OrderBy(n => n.CreateTime)
                                                    .Take(batchSize) // Limit query to avoid processing too many at once
                                                    .ToListAsync();

                foreach (var notification in pendingNotifications)
                {
                    try
                    {
                        // Make a REST API call
                        var isSuccess = await _oneSignalService.SendToExternalIdsAsync(notification);
                        if (isSuccess)
                        {
                            notification.Status = (int)NotificationStatus.Sent;
                            notification.SentAt = DateTime.Now;
                        }
                        else
                        {
                            notification.Status = (int)NotificationStatus.Failed;
                        }
                        
                        // Save immediately after each notification to prevent losing progress
                        _context.Update(notification);
                        await _context.SaveChangesAsync();
                    }
                    catch (Exception)
                    {
                        notification.Status = (int)NotificationStatus.Failed;
                        _context.Update(notification);
                        await _context.SaveChangesAsync();
                    }
                }
            }
            catch (Exception)
            {
                return;
            }
        }
    }
}
