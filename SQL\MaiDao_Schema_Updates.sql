-- Migration script for MaiDaoModel table updates
-- Add missing columns for enhanced MaiDao functionality

USE [iMES_API_DB] -- Replace with your actual database name
GO

-- Check if the table exists before adding columns
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
           WHERE TABLE_SCHEMA = 'MES' AND TABLE_NAME = 'MaiDaoModel')
BEGIN
    PRINT 'MaiDaoModel table found. Adding missing columns...'
    
    -- Add EquipmentName column if it doesn't exist
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'MES' AND TABLE_NAME = 'MaiDaoModel' 
                   AND COLUMN_NAME = 'EquipmentName')
    BEGIN
        ALTER TABLE [MES].[MaiDaoModel] 
        ADD [EquipmentName] NVARCHAR(200) NULL
        PRINT 'Added EquipmentName column'
    END
    
    -- Add EmployeeNames column if it doesn't exist
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'MES' AND TABLE_NAME = 'MaiDaoModel' 
                   AND COLUMN_NAME = 'EmployeeNames')
    BEGIN
        ALTER TABLE [MES].[MaiDaoModel] 
        ADD [EmployeeNames] NVARCHAR(1000) NULL
        PRINT 'Added EmployeeNames column'
    END
    
    -- Add RequestingEmployeeName column if it doesn't exist
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'MES' AND TABLE_NAME = 'MaiDaoModel' 
                   AND COLUMN_NAME = 'RequestingEmployeeName')
    BEGIN
        ALTER TABLE [MES].[MaiDaoModel] 
        ADD [RequestingEmployeeName] NVARCHAR(200) NULL
        PRINT 'Added RequestingEmployeeName column'
    END
    
    -- Check if Status column exists and modify if needed
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_SCHEMA = 'MES' AND TABLE_NAME = 'MaiDaoModel' 
               AND COLUMN_NAME = 'Status')
    BEGIN
        PRINT 'Status column already exists'
    END
    ELSE
    BEGIN
        ALTER TABLE [MES].[MaiDaoModel] 
        ADD [Status] VARCHAR(50) NULL
        PRINT 'Added Status column'
    END
    
    -- Update existing records to set default status if null
    UPDATE [MES].[MaiDaoModel] 
    SET [Status] = 'Created' 
    WHERE [Status] IS NULL OR [Status] = ''
    
    PRINT 'Schema update completed successfully!'
END
ELSE
BEGIN
    PRINT 'MaiDaoModel table not found. Creating table with all required columns...'
    
    -- Create the table with all columns if it doesn't exist
    CREATE TABLE [MES].[MaiDaoModel] (
        [MaiDaoId] uniqueidentifier NOT NULL PRIMARY KEY,
        [Date] nvarchar(10) NOT NULL,
        [EquipmentCode] nvarchar(100) NOT NULL,
        [EquipmentName] nvarchar(200) NULL,
        [MaterialCode] nvarchar(100) NULL,
        [MaterialName] nvarchar(200) NULL,
        [OperationType] nvarchar(20) NOT NULL,
        [EmployeeCodes] nvarchar(500) NULL,
        [EmployeeNames] nvarchar(1000) NULL,
        [RequestingEmployeeCode] varchar(50) NULL,
        [RequestingEmployeeName] nvarchar(200) NULL,
        [Note] nvarchar(500) NULL,
        [Status] varchar(50) NULL,
        [CreatedDate] datetime NOT NULL,
        [CreateBy] uniqueidentifier NULL,
        [UpdatedDate] datetime NULL,
        [UpdateBy] uniqueidentifier NULL,
        [CompanyCode] nvarchar(10) NULL
    )
    
    PRINT 'Created MaiDaoModel table with all required columns'
END

GO

-- Add indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaiDaoModel_EquipmentCode')
BEGIN
    CREATE INDEX IX_MaiDaoModel_EquipmentCode 
    ON [MES].[MaiDaoModel] ([EquipmentCode])
    PRINT 'Added index on EquipmentCode'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaiDaoModel_CompanyCode')
BEGIN
    CREATE INDEX IX_MaiDaoModel_CompanyCode 
    ON [MES].[MaiDaoModel] ([CompanyCode])
    PRINT 'Added index on CompanyCode'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaiDaoModel_Status')
BEGIN
    CREATE INDEX IX_MaiDaoModel_Status 
    ON [MES].[MaiDaoModel] ([Status])
    PRINT 'Added index on Status'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaiDaoModel_CreatedDate')
BEGIN
    CREATE INDEX IX_MaiDaoModel_CreatedDate 
    ON [MES].[MaiDaoModel] ([CreatedDate])
    PRINT 'Added index on CreatedDate'
END

PRINT 'Migration completed successfully!' 