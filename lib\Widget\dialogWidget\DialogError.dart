import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DialogError extends StatelessWidget {
  final String message;
  const DialogError({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: SingleChildScrollView(
          child: Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
        // Icon(Icons.warning_rounded,size: 50.sp),
        SizedBox(height: 15.h),
        Text(
          "X<PERSON>y ra lỗi! Vui lòng thử lại sau",
          style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold, color: Colors.red.shade700),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 15.h),
        Text(
          message,
          style: TextStyle(fontSize: 15.sp),
          textAlign: TextAlign.center,
        )
      ])),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'OK',
            style: TextStyle(fontSize: 15.sp),
          ),
        ),
      ],
    );
  }
}
