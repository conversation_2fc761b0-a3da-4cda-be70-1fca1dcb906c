﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("SalesEmployeeInfoModel", Schema = "tMasterData")]
    [Index("InfoType", "Code", Name = "UQ_SalesEmployeeInfoModel_InfoType_Code", IsUnique = true)]
    public partial class SalesEmployeeInfoModel
    {
        [Key]
        public int Id { get; set; }
        [Required]
        [StringLength(1)]
        public string InfoType { get; set; }
        [Required]
        [StringLength(50)]
        public string Code { get; set; }
        [Required]
        [StringLength(255)]
        public string Name { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        [Required]
        public bool? Actived { get; set; }
    }
}