import 'package:intl/intl.dart';

class PostFilterWareHouseTranfer{
  String? plant;
  String? slocExport;
  String? slocImport;
  String? reservationCode;
  String? statusReservation;
  String? productCode;
  String? statusWarehouse;
  DateTime? requestDateFrom;
  DateTime? requestDateTo;
  DateTime? documentDateFrom;
  DateTime? documentDateTo;

  PostFilterWareHouseTranfer(
      this.plant,
      this.slocExport,
      this.slocImport,
      this.reservationCode,
      this.statusReservation,
      this.productCode,
      this.statusWarehouse,
      this.requestDateFrom,
      this.requestDateTo,
      this.documentDateFrom,
      this.documentDateTo
      );
  PostFilterWareHouseTranfer.fromJson(Map<String, dynamic> json) {
    plant = json['plant'];
    slocExport = json['slocExport'];
    slocImport = json['slocImport'];
    reservationCode = json['reservationCode'];
    statusReservation = json['statusReservation'];
    productCode = json['productCode'];
    statusWarehouse = json['statusWarehouse'];
    requestDateFrom = json['requestDateFrom'] == null ? null :DateFormat("yyyy-MM-ddThh:mm:ss").parse(json['requestDateFrom']);
    requestDateTo = json['requestDateTo'] == null ? null :DateFormat("yyyy-MM-ddThh:mm:ss").parse(json['requestDateTo']);
    documentDateFrom = json['documentDateFrom'] == null ? null :DateFormat("yyyy-MM-ddThh:mm:ss").parse(json['documentDateFrom']);
    documentDateTo = json['documentDateTo'] == null ? null :DateFormat("yyyy-MM-ddThh:mm:ss").parse(json['documentDateTo']);
  }
  Map<String, dynamic> toJson() {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['plant'] = plant;
  data['slocExport'] = slocExport;
  data['slocImport'] = slocImport;
  data['reservationCode'] = reservationCode;
  data['statusReservation'] = statusReservation;
  data['productCode'] = productCode;
  data['statusWarehouse'] = statusWarehouse;
  data['requestDateFrom'] = requestDateFrom != null ?requestDateFrom!.toIso8601String():null;
  data['requestDateTo'] = requestDateTo != null ? requestDateTo!.toIso8601String():null;
  data['documentDateFrom'] = documentDateFrom != null ? documentDateFrom!.toIso8601String():null;
  data['documentDateTo'] = documentDateTo != null ?documentDateTo!.toIso8601String():null;
  return data;
  }

}
class Paging {
  int? draw;
  int? start;
  int? length;

  Paging({this.draw, this.start, this.length});


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['draw'] = draw;
    data['start'] = start;
    data['length'] = length;
    return data;
  }
}