import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../urlApi/urlApi.dart';

class CommonDateApi {
  static Future<http.Response> getCommonDate(String commonDate, String token) async {
    Map<String, dynamic> data = {"CommonDate": commonDate};
    if (kDebugMode) {
      print(data);
    }

    final environment = await SecureStorage.getString("environment", null);
    final baseUrl = environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;

    final urlString = '${baseUrl}${UrlApi.baseUrlCommonDate}GetDateByCommonDate?CommonDate=${data['CommonDate']}';

    final url = Uri.parse(urlString);

    debugPrint(url.toString());
    final response = await http.get(url, headers: UrlApi.headersToken(token));
    return response;
  }
}
