class GetAutoBatch {
  String? materialType;
  String? importExportType;
  String? materialGroup;
  String? materialStatus;
  int? nsxhsd;
  String? skinType;
  String? skinColor;
  String? caseCode;

  GetAutoBatch(
      {this.materialType,
        this.importExportType,
        this.materialGroup,
        this.materialStatus,
        this.nsxhsd,
        this.skinType,
        this.skinColor,
        this.caseCode
      });

  GetAutoBatch.fromJson(Map<String, dynamic> json) {
    materialType = json['materialType'];
    importExportType = json['importExportType'];
    materialGroup = json['materialGroup'];
    materialStatus = json['materialStatus'];
    nsxhsd = json['nsxhsd'];
    skinType = json['skinType'];
    skinColor = json['skinColor'];
    caseCode = json['caseCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['materialType'] = materialType;
    data['importExportType'] = importExportType;
    data['materialGroup'] = materialGroup;
    data['materialStatus'] = materialStatus;
    data['nsxhsd'] = nsxhsd;
    data['skinType'] = skinType;
    data['skinColor'] = skinColor;
    data['caseCode'] = caseCode;
    return data;
  }
}
class GetAutoBatchData {
  int? code;
  bool? isSuccess;
  DataGetAutoBatchData? data;


  GetAutoBatchData(
      {this.code,
        this.isSuccess,
        this.data});

  GetAutoBatchData.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    data = json['data'] != null ? DataGetAutoBatchData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataGetAutoBatchData {
  String? batchNumber;

  DataGetAutoBatchData({this.batchNumber});

  DataGetAutoBatchData.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    return data;
  }
}