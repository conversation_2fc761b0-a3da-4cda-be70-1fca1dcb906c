---
description: 
globs: 
alwaysApply: false
---
# Automated Backend API Checks & Generation

This guide explains how to use Cursor AI to automatically apply backend guidelines when working with C# files in the iMES_API project.

## Prompt Templates for Code Generation

When generating new code with Cursor, use these prompt templates to enforce correct patterns:

### Controller Generation Template

```
Generate a {FeatureName}Controller in iMES_API/iMES_API/Areas/MES/Controllers/ with these requirements:
- Use [Area("MES")] attribute
- Route pattern: api/v1/MES/[controller]
- Inject IMaiDaoRepository
- Follow RESTful endpoint patterns (GET, POST, PUT)
- Use async/await for all database operations
- Always check existing CommonController for reusable methods
- Access employee data via CommonController, not directly

Review the code for:
- Correct entity context (EntityDataContext)
- Proper SAP field naming (EQUNR, EQKTX, IWERK)
- SalesEmployeeModel field mapping
```

### Repository Implementation Template

```
Generate a {FeatureName}Repository implementation with these requirements:
- Correct context: EntityDataContext (not AppDbContext)
- Handle equipment codes with 18-char zero padding
- Use SalesEmployeeModel for employee data
- Properly handle SAP field names:
  - EQUNR not EquipmentCode
  - EQKTX not EquipmentName
  - IWERK not WERKS
- Implement field mapping in Select projections
```

## AI Code Review Checklist

Before committing C# backend changes, run this Cursor AI review prompt:

```
Review this C# code for iMES_API backend issues:

1. Context check: Verify using EntityDataContext (not AppDbContext)
2. SAP field names: Check for EQUNR, EQKTX, and IWERK usage
3. Employee data: Verify SalesEmployeeModel (not Employees)
4. Code structure: Confirm [Area("MES")] attribute for controllers
5. Common reuse: Look for duplicate code that exists in CommonController
6. Format handling: Check for padding/trimming of equipment codes
```

## Code Snippets for Quick Reference

Use these snippets to quickly insert correct patterns:

### Equipment Query Pattern

```csharp
// SAP equipment lookup with proper padding
var paddedCodes = codes.Select(c => c.PadLeft(18, '0')).ToList();
var equipment = await _context.EquipmentMdModel
    .Where(e => paddedCodes.Contains(e.EQUNR))
    .ToDictionaryAsync(e => e.EQUNR.TrimStart('0'), e => e.EQKTX);
```

### Employee Data Access Pattern

```csharp
// CommonController employee lookup
[HttpGet("GetEmployees")]
public async Task<IActionResult> GetEmployees([FromQuery] string companyCode)
{
    var commonController = new CommonController();
    commonController.ControllerContext = this.ControllerContext;
    return await commonController.GetEmployees(companyCode);
}
```

## Auto-Enforcement Steps

1. **Code Generation**: Always use the templates above when creating new controllers/repositories
2. **Pre-commit Review**: Run the AI review checklist before committing changes
3. **Peer Review**: Share these guidelines with the team for PR reviews
4. **Periodic Scans**: Periodically scan codebase for inconsistencies using Cursor's "Find in Files" with review prompt


