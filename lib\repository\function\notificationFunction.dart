import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:ttf/Widget/dialogWidget/DialogErrorValidate.dart';
import 'package:ttf/model/ApiResponse.dart';
import 'package:ttf/model/EmployeeVm.dart';
import 'package:ttf/model/PushNotificationVm.dart';
import 'package:ttf/repository/api/notificationApi.dart';
import 'package:ttf/repository/api/traHangNCCApi.dart';
import '../../Widget/dialogWidget/DialogError.dart';
import '../../model/GetDefectLevel.dart';
import '../../model/GetQuantitySampleModel.dart';
import '../../model/RequestTraHangNCC.dart';
import '../../model/dataQualityControl.dart';
import '../../model/qualityControlApi.dart';
import '../api/getDefectLevel.dart';
import '../api/getQuantitySample.dart';
import '../api/qualityControlApi.dart';

class NotificationFunction {
  static bool checkIsSend = false;
  static QualityCheckerInfo defaultValueQC = QualityCheckerInfo(accountId: " ", salesEmployeeName: '--Vui lòng chọn--');
  static QualityTypeList defaultValueQualityTypeList = QualityTypeList(catalogCode: " ", catalogTextVi: '--Vui lòng chọn--');

  // static Future<DataRequestReturnVendorModel?> getData(String token, String id) async {
  //   final response = await NotificationApi.getData(token, id);

  //   if (response.statusCode == 200) {
  //     final responseItem = jsonDecode(response.body);
  //     final responseData = RequestReturnVendorModelItem.fromJson(responseItem);
  //     if (responseData.code == 200 && responseData.isSuccess == true) {
  //       return responseData.data;
  //     } else {
  //       return null;
  //     }
  //   } else {
  //     throw (response.body.toString());
  //   }
  // }

  static Future<List?> getListData(String token, String accountId) async {
    final response = await NotificationApi.getListData(token, accountId);

    if (response.statusCode == 200) {
      final responseGetList = jsonDecode(response.body);
      final responseData = ApiResponse.fromJson(responseGetList);
      return responseData.data;
    } else {
      throw (response.body.toString());
    }
  }

  static Future<List?> getListDataNew(String token, String accountId, int pageIndex, int pageSize) async {
    final response = await NotificationApi.getListDataNew(token, accountId, pageIndex, pageSize);

    if (response.statusCode == 200) {
      final responseGetList = jsonDecode(response.body);
      final responseData = ApiResponse.fromJson(responseGetList);
      return responseData.data;
    } else {
      throw (response.body.toString());
    }
  }

  static Future<List?> getListEmployee(String token, String plant) async {
    final response = await NotificationApi.getListEmployee(token, plant);

    if (response.statusCode == 200) {
      final responseGetList = jsonDecode(response.body);
      final responseData = ApiResponse.fromJson(responseGetList);
      return responseData.data;
    } else {
      throw (response.body.toString());
    }
  }

  static Future<bool> postForward(String token, String forwardById, String notificationId, List<EmployeeVm> forwardList) async {
    final response = await NotificationApi.postForward(token, forwardById, notificationId, forwardList);
    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }

  static String dateFormatConfirm(QualityControl? qualityControl) {
    if (qualityControl != null) {
      if (qualityControl.confirmDate != null) {
        DateTime stringToDateTimeConfirm = DateFormat("yyyy-MM-dd").parse(qualityControl.confirmDate!);
        String dateFormatStringConfirm = DateFormat('dd/MM/yyyy').format(stringToDateTimeConfirm);
        return dateFormatStringConfirm;
      } else {
        return "";
      }
    } else {
      return "";
    }
  }

  static String getDate(QualityControl? qualityControl, DateTime? date) {
    String? dateFormatStringQualityDate;
    if (qualityControl!.qualityDate != null && date == null) {
      dateFormatStringQualityDate =
          DateFormat("dd-MM-yyyy hh:mm a").format(DateFormat("yyyy-MM-dd hh:mm").parse(qualityControl.qualityDate.toString()));
      // dateFormatStringQualityDate = DateFormat.yMMMMd('vi').format(stringToDateTimeQualityDate);
    } else if (qualityControl.qualityDate == null && date == null) {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.now());
    } else {
      dateFormatStringQualityDate = DateFormat("dd-MM-yyyy hh:mm a").format(date!);
      // return '${date.month}/${date.day}/${date.year}';
    }
    return dateFormatStringQualityDate;
  }
}
