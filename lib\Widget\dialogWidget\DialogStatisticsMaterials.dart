import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../model/getBackDataDialogStatistics.dart';
import '../../model/getInventoryBySOWBS.dart';
import '../../model/getListSOWBSByBatch.dart';
import '../../model/postGetListSOWBSByBatch.dart';
import '../../page/LostConnect.dart';
import '../../repository/function/materialUsedShiftFunction.dart';
import '../container/errorFormatCheck.dart';

class DialogStatisticsMaterials extends StatefulWidget {
  final PostGetListSOWBSByBatch postGetListSOWBSByBatch;
  final String token;
  const DialogStatisticsMaterials({Key? key, required this.postGetListSOWBSByBatch, required this.token}) : super(key: key);
  @override
  State<DialogStatisticsMaterials> createState() => _DialogStatisticsMaterialsState();
}

class _DialogStatisticsMaterialsState extends State<DialogStatisticsMaterials> {
  // late List<bool> _isChecked;
  bool _isLoading = false;
  bool _notWifi = false;
  List<DataGetListSOWBSByBatch> _lsDataGetListSOWBSByBatch = [];
  List<TextEditingController> _lsSoLuongSX = [];
  bool _errorCheckBox = false;
  // List<DataGetListSOWBSByBatch> _lsChoose = [];

  @override
  void initState() {
    super.initState();
    _getSOWBSByBatch();
  }

  Future<void> _getSOWBSByBatch() async {
    try {
      setState(() {
        _isLoading = true;
        _notWifi = false;
      });
      final data = await MaterialUsedShiftFunction.getDataGetListSOWBSByBatch(widget.postGetListSOWBSByBatch, widget.token);
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (data != null) {
        _lsDataGetListSOWBSByBatch = data;
        for (int i = 0; i < _lsDataGetListSOWBSByBatch.length; i++) {
          _lsDataGetListSOWBSByBatch[i].isCheck = true;

          _lsSoLuongSX.add(
              TextEditingController(text: _lsDataGetListSOWBSByBatch[i].quantity == null ? "" : _lsDataGetListSOWBSByBatch[i].quantity.toString()));
        }
        debugPrint(_lsDataGetListSOWBSByBatch[0].isCheck.toString());
        // _isChecked = List<bool>.filled(_lsDataGetListSOWBSByBatch.length, false);
      }
    } on SocketException catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _notWifi = true;
      });
    } catch (error) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _notWifi = false;
      });
    }
  }

  void _checkError() {
    if (_lsDataGetListSOWBSByBatch.isNotEmpty) {
      if (_lsDataGetListSOWBSByBatch.where((element) => element.isCheck == true).isNotEmpty) {
        if (_errorCheckBox != false) {
          setState(() {
            _errorCheckBox = false;
          });
        }
      } else {
        if (_errorCheckBox != true) {
          setState(() {
            _errorCheckBox = true;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _lsSoLuongSX) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss the keyboard when tapping outside of the text field
        FocusScope.of(context).unfocus();
      },
      child: WillPopScope(
        onWillPop: () => Future.value(false),
        child: AlertDialog(
          contentPadding: EdgeInsets.zero,
          content: _notWifi
              ? LostConnect(checkConnect: () => _getSOWBSByBatch())
              : _isLoading == true
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 30.h),
                          child: const CircularProgressIndicator(),
                        ),
                      ],
                    )
                  : SingleChildScrollView(
                      child: ListBody(
                        children: [
                          SizedBox(
                            // width: double.minPositive,
                            width: MediaQuery.of(context).size.width * 0.98,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Table(
                                    border: TableBorder(
                                      left: BorderSide(
                                        color: Colors.black,
                                        width: 0.5.w,
                                      ),
                                      right: BorderSide(
                                        color: Colors.black,
                                        width: 0.5.w,
                                      ),
                                      bottom: BorderSide(
                                        color: Colors.black,
                                        width: 0.5.w,
                                      ),
                                      verticalInside: BorderSide(
                                        color: Colors.black,
                                        width: 0.5.w,
                                      ),
                                    ),
                                    columnWidths: <int, TableColumnWidth>{
                                      0: FixedColumnWidth(70.w),
                                      1: FixedColumnWidth(100.w),
                                      2: FixedColumnWidth(100.w),
                                      3: FixedColumnWidth(70.w),
                                      4: FixedColumnWidth(70.w),
                                      5: FixedColumnWidth(100.w),
                                    },
                                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                    children: const <TableRow>[
                                      TableRow(
                                        decoration: BoxDecoration(
                                          color: Color(0xff303F9F),
                                        ),
                                        children: <Widget>[
                                          _TitleTable(text: ""),
                                          _TitleTable(text: "SO/WBS"),
                                          _TitleTable(text: "LSX Đại trà"),
                                          _TitleTable(text: "Số lượng"),
                                          _TitleTable(text: "ĐVT"),
                                          _TitleTable(text: "Số lượng SX"),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: List.generate(
                                      (_lsDataGetListSOWBSByBatch).length,
                                      (index) => Table(
                                        border: TableBorder(
                                          left: BorderSide(
                                            color: Colors.black,
                                            width: 0.5.w,
                                          ),
                                          right: BorderSide(
                                            color: Colors.black,
                                            width: 0.5.w,
                                          ),
                                          bottom: BorderSide(
                                            color: Colors.black,
                                            width: 0.5.w,
                                          ),
                                          verticalInside: BorderSide(
                                            color: Colors.black,
                                            width: 0.5.w,
                                          ),
                                        ),
                                        columnWidths: <int, TableColumnWidth>{
                                          0: FixedColumnWidth(70.w),
                                          1: FixedColumnWidth(100.w),
                                          2: FixedColumnWidth(100.w),
                                          3: FixedColumnWidth(70.w),
                                          4: FixedColumnWidth(70.w),
                                          5: FixedColumnWidth(100.w),
                                        },
                                        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                                        children: <TableRow>[
                                          TableRow(
                                            decoration: const BoxDecoration(
                                              color: Color(0xFFFFFFFF),
                                            ),
                                            children: <Widget>[
                                              //      Visibility(
                                              // visible:
                                              // (_lsDataGetListSOWBSByBatch[index].so == null || _lsDataGetListSOWBSByBatch[index].so == "" ) &&
                                              //     (_lsDataGetListSOWBSByBatch[index].soLine == null || _lsDataGetListSOWBSByBatch[index].soLine == "" )
                                              // &&(_lsDataGetListSOWBSByBatch[index].wbs == null || _lsDataGetListSOWBSByBatch[index].wbs == "" )
                                              //     ? false : true,
                                              //          child:

                                              /// 1. Check
                                              Container(
                                                margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                child: Center(
                                                  child: Theme(
                                                    child: Transform.scale(
                                                      scale: 0.8.sp,
                                                      child: Checkbox(
                                                        checkColor: Colors.white,
                                                        activeColor: Colors.black,
                                                        value: _lsDataGetListSOWBSByBatch[index].isCheck,
                                                        onChanged: (bool? value) {
                                                          setState(() {
                                                            _lsDataGetListSOWBSByBatch[index].isCheck = value!;
                                                            if (_lsDataGetListSOWBSByBatch.where((element) => element.isCheck == true).isNotEmpty) {
                                                              if (_errorCheckBox != false) {
                                                                _errorCheckBox = false;
                                                              }
                                                            } else {
                                                              if (_errorCheckBox != true) {
                                                                _errorCheckBox = true;
                                                              }
                                                            }
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                    data: ThemeData(
                                                      unselectedWidgetColor: Colors.black,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              // ),
                                              /// 2. SO/WBS
                                              Container(
                                                margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
                                                child: (_lsDataGetListSOWBSByBatch[index].so != null && _lsDataGetListSOWBSByBatch[index].so != "") &&
                                                        (_lsDataGetListSOWBSByBatch[index].soLine != null &&
                                                            _lsDataGetListSOWBSByBatch[index].soLine != "") &&
                                                        (_lsDataGetListSOWBSByBatch[index].wbs == null || _lsDataGetListSOWBSByBatch[index].wbs == "")
                                                    ? Column(
                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          Text(
                                                            _lsDataGetListSOWBSByBatch[index].so ?? "",
                                                            style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                                            textAlign: TextAlign.center,
                                                          ),
                                                          Text(
                                                            _lsDataGetListSOWBSByBatch[index].soLine ?? "",
                                                            style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                                          ),
                                                        ],
                                                      )
                                                    : (_lsDataGetListSOWBSByBatch[index].so == null || _lsDataGetListSOWBSByBatch[index].so == "") &&
                                                            (_lsDataGetListSOWBSByBatch[index].soLine == null ||
                                                                _lsDataGetListSOWBSByBatch[index].soLine == "") &&
                                                            (_lsDataGetListSOWBSByBatch[index].wbs != null &&
                                                                _lsDataGetListSOWBSByBatch[index].wbs != "")
                                                        ? Center(
                                                            child: Text(
                                                              _lsDataGetListSOWBSByBatch[index].wbs ?? "",
                                                              style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                                            ),
                                                          )
                                                        : Center(
                                                            child: Text(
                                                              "Tồn trơn",
                                                              style: TextStyle(fontSize: 12.sp, color: Colors.black),
                                                            ),
                                                          ),
                                              ),

                                              /// 3. LSX Đại trà
                                              _ColumnTable(
                                                text: _lsDataGetListSOWBSByBatch[index].lsxdt ?? "",
                                              ),

                                              /// 4. Số lượng
                                              _ColumnTable(
                                                text: _lsDataGetListSOWBSByBatch[index].quantity == null
                                                    ? ""
                                                    : _lsDataGetListSOWBSByBatch[index].quantity!.toString(),
                                              ),

                                              /// 5. ĐVT
                                              _ColumnTable(
                                                text: _lsDataGetListSOWBSByBatch[index].unit ?? "",
                                              ),

                                              /// TODO:6. Số lượng SX
                                              Container(
                                                // padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 4.h),
                                                margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
                                                decoration: BoxDecoration(
                                                  border: Border.all(width: 0.5.w, color: Colors.grey.shade400),
                                                ),
                                                child: TextFormField(
                                                  enabled: _lsDataGetListSOWBSByBatch[index].quantity != null,
                                                  maxLines: null,
                                                  keyboardType: TextInputType.number,
                                                  // inputFormatters: <TextInputFormatter>[
                                                  //   FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                                                  // ],
                                                  textAlign: TextAlign.center,
                                                  controller: _lsSoLuongSX[index],
                                                  style: TextStyle(fontSize: 12.sp),
                                                  decoration: InputDecoration(
                                                    border: InputBorder.none,
                                                    focusedBorder: InputBorder.none,
                                                    enabledBorder: InputBorder.none,
                                                    errorBorder: InputBorder.none,
                                                    disabledBorder: InputBorder.none,
                                                    filled: true,
                                                    isDense: true,
                                                    fillColor: Colors.white,
                                                    hintStyle: TextStyle(fontSize: 12.sp),
                                                    // contentPadding: EdgeInsets.zero,
                                                    contentPadding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                                                  ),
                                                  onChanged: (value) {
                                                    if (value.isNotEmpty) {
                                                      double inputValue = double.tryParse(value) ?? 0;
                                                      double maxValue = _lsDataGetListSOWBSByBatch[index].quantity ?? 0;
                                                      if (inputValue > maxValue) {
                                                        _lsSoLuongSX[index].text = maxValue.toString();
                                                      }
                                                    }
                                                  },
                                                ),
                                              )
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: _errorCheckBox == true ? 10.h : 0),
                          ContainerError.widgetError(_errorCheckBox, 'Vui lòng chọn SO/WBS'),
                          SizedBox(height: 20.h),
                          Row(
                            children: [
                              Expanded(
                                flex: _lsDataGetListSOWBSByBatch.isNotEmpty ? 5 : 10,
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                                  decoration: const BoxDecoration(),
                                  child: ElevatedButton(
                                    style: ButtonStyle(
                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                      side: MaterialStateProperty.all(
                                        const BorderSide(color: Color(0xffe0e0e0)),
                                      ),
                                      backgroundColor: MaterialStateProperty.all(const Color(0xffe0e0e0)),
                                    ),
                                    onPressed: () {
                                      GetBackDataDialogStatistic getBackDataDialogStatistic =
                                          GetBackDataDialogStatistic(isSave: false, getInventoryBySOWBS: null);
                                      Navigator.pop(context, getBackDataDialogStatistic);
                                    },
                                    child: Container(
                                      margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                      child: Text(
                                        "Hủy",
                                        style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: _lsDataGetListSOWBSByBatch.isNotEmpty ? 5 : 0,
                                child: Visibility(
                                    visible: _lsDataGetListSOWBSByBatch.isNotEmpty ? true : false,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                                      decoration: const BoxDecoration(),
                                      child: ElevatedButton(
                                        style: ButtonStyle(
                                          shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(5.r), side: const BorderSide(color: Colors.white))),
                                          side: MaterialStateProperty.all(
                                            const BorderSide(
                                              color: Color(0xff0052cc),
                                            ),
                                          ),
                                          backgroundColor: MaterialStateProperty.all(const Color(0xff0052cc)),
                                        ),
                                        onPressed: () {
                                          _checkError();
                                          if (_errorCheckBox == false) {
                                            List<SowbSs>? lsSowbs = [];
                                            for (var i = 0; i < _lsDataGetListSOWBSByBatch.length; i++) {
                                              if (_lsDataGetListSOWBSByBatch[i].isCheck == true) {
                                                double? soLuongSX;
                                                if (_lsSoLuongSX[i].text.isNotEmpty) {
                                                  soLuongSX = double.tryParse(_lsSoLuongSX[i].text);
                                                }

                                                lsSowbs.add(
                                                  SowbSs(
                                                    so: _lsDataGetListSOWBSByBatch[i].so == "" ? null : _lsDataGetListSOWBSByBatch[i].so,
                                                    soLine: _lsDataGetListSOWBSByBatch[i].soLine == "" ? null : _lsDataGetListSOWBSByBatch[i].soLine,
                                                    wbs: _lsDataGetListSOWBSByBatch[i].wbs == "" ? null : _lsDataGetListSOWBSByBatch[i].wbs,
                                                    // Update 1: add so luong sl
                                                    soLuong: _lsDataGetListSOWBSByBatch[i].quantity,
                                                    soLuongSX: soLuongSX, // Assuming your SowbSs model has this field
                                                  ),
                                                );
                                              }
                                            }
                                            // lsSowbs.removeWhere((element) => (element.wbs == null || element.wbs == "" ) && (element.so == null || element.so == "") && (element.soLine == null || element.soLine == ""));
                                            GetInventoryBySOWBS getInventoryBySOWBS = GetInventoryBySOWBS(
                                              productCode: widget.postGetListSOWBSByBatch.productCode,
                                              plant: widget.postGetListSOWBSByBatch.plant,
                                              sloc: widget.postGetListSOWBSByBatch.sloc,
                                              batchNumber: widget.postGetListSOWBSByBatch.batchNumber,
                                              sowbSs: lsSowbs,
                                            );
                                            // debugPrint(jsonEncode(getInventoryBySOWBS));
                                            GetBackDataDialogStatistic getBackDataDialogStatistic =
                                                GetBackDataDialogStatistic(isSave: true, getInventoryBySOWBS: getInventoryBySOWBS);
                                            Navigator.pop(context, getBackDataDialogStatistic);
                                          }
                                        },
                                        child: Container(
                                          margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                                          child: Text(
                                            "Chọn",
                                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 13.sp),
                                          ),
                                        ),
                                      ),
                                    )),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.h),
                        ],
                      ),
                    ),
        ),
      ),
    );
  }
}

class _TitleTable extends StatelessWidget {
  final String text;
  const _TitleTable({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Center(
        child: Text(
          text,
          style: TextStyle(fontSize: 12.sp, color: Colors.white),
        ),
      ),
    );
  }
}

class _ColumnTable extends StatelessWidget {
  final String text;
  const _ColumnTable({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w, vertical: 3.h),
      child: Center(
        child: Text(
          text,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
      ),
    );
  }
}
