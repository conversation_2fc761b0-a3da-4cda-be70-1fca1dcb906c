﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("TargetGroupModel", Schema = "Marketing")]
    public partial class TargetGroupModel
    {
        public TargetGroupModel()
        {
            MemberOfExternalProfileTargetGroupModel = new HashSet<MemberOfExternalProfileTargetGroupModel>();
            MemberOfTargetGroupModel = new HashSet<MemberOfTargetGroupModel>();
        }

        [Key]
        public Guid Id { get; set; }
        public int TargetGroupCode { get; set; }
        [Required]
        [StringLength(50)]
        public string TargetGroupName { get; set; }
        public Guid CreateBy { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreateTime { get; set; }
        public Guid? LastEditBy { get; set; }
        public bool Actived { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastEditTime { get; set; }
        /// <summary>
        /// Marketing|Event
        /// </summary>
        [StringLength(50)]
        public string Type { get; set; }

        [ForeignKey("CreateBy")]
        [InverseProperty("TargetGroupModelCreateByNavigation")]
        public virtual AccountModel CreateByNavigation { get; set; }
        [ForeignKey("LastEditBy")]
        [InverseProperty("TargetGroupModelLastEditByNavigation")]
        public virtual AccountModel LastEditByNavigation { get; set; }
        [InverseProperty("TargetGroup")]
        public virtual ICollection<MemberOfExternalProfileTargetGroupModel> MemberOfExternalProfileTargetGroupModel { get; set; }
        [InverseProperty("TargetGroup")]
        public virtual ICollection<MemberOfTargetGroupModel> MemberOfTargetGroupModel { get; set; }
    }
}