class GetBatchNumberByPallet {
  int? code;
  bool? isSuccess;
  String? message;
  DataBatch? data;


  GetBatchNumberByPallet(
      {this.code,
        this.isSuccess,
        this.message,
        this.data});

  GetBatchNumberByPallet.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    isSuccess = json['isSuccess'];
    message = json['message'];
    data = json['data'] != null ? DataBatch.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['isSuccess'] = isSuccess;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataBatch {
  String? batchNumber;

  DataBatch({this.batchNumber});

  DataBatch.fromJson(Map<String, dynamic> json) {
    batchNumber = json['batchNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['batchNumber'] = batchNumber;
    return data;
  }
}