﻿using Azure;
using HtmlAgilityPack;
using iMES_API.ServiceExtensions;
using ISD.API.Core;
using ISD.API.EntityModels.Models;
using ISD.API.Extensions;
using ISD.API.Resources;
using ISD.API.ViewModels;
using ISD.API.ViewModels.MasterData.SampMethod;
using ISD.API.ViewModels.MESP2;
using ISD.API.ViewModels.MESP2.Requests;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Linq;
using System.Net;
using ISD.API.ViewModels.MES;
using ISD.API.EntityModels.Data;
using Microsoft.Extensions.DependencyInjection;
using SAPGetStock;
using SAPGetLSXDT;
using Hangfire;
using iMES_API.Infrastructures.JobSchedulers;
using ISD.API.Constant.MESP2;
using iMES_API.Infrastructures.JobSchedulers.WarehouseSyncSAP;

namespace iMES_API.Areas.MES.Controllers
{
    [Route("api/v{version:apiVersion}/MES/[controller]")]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiController]
    //[ApiExplorerSettings(GroupName = "mobile")]
    public class CommonController : ControllerBaseAPI
    {
        private IServiceProvider serviceProvider;

        //[HttpGet("RunHangFireJob")]
        //public async Task<IActionResult> RunHangFireJob()
        //{
        //    // run AutoAllocateService job
        //    var jobId = BackgroundJob.Enqueue<AutoAllocateService>(x => x.Run());
        //    return Ok(jobId);
        //}

        [HttpGet("StopRemoveLogApi")]
        public Task<IActionResult> StopRemoveLogApi()
        {
            try
            {
                RecurringJob.RemoveIfExists("RemoveLogApi");
                
                return Task.FromResult<IActionResult>(Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = "RemoveLogApi job has been stopped successfully"
                }));
            }
            catch (Exception ex)
            {
                return Task.FromResult<IActionResult>(StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Error stopping RemoveLogApi job: {ex.Message}"
                }));
            }
        }

        [HttpGet("StopJobById")]
        public Task<IActionResult> StopJobById([FromQuery] string jobId)
        {
            try
            {
                // Delete the specific job by ID
                BackgroundJob.Delete(jobId);
                
                return Task.FromResult<IActionResult>(Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = $"Job {jobId} has been stopped successfully"
                }));
            }
            catch (Exception ex)
            {
                return Task.FromResult<IActionResult>(StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Error stopping job {jobId}: {ex.Message}"
                }));
            }
        }

        [HttpGet("RegisterJob")]
        public async Task<IActionResult> RegisterJob([FromQuery] string jobName)
        {
            try
            {

                var service = new RemoveLogApiService(serviceProvider);

                // Check trạng thái run job
                var settingJob = await _context.SettingJob.SingleOrDefaultAsync(x => x.JobName == jobName);
                if (settingJob?.IsRun == false)
                {
                    return Ok(new ApiResponse
                    {
                        Code = 200,
                        IsSuccess = true,
                        Message = $"Job {jobName} is not set to run."
                    });
                }

                // Get GMT+7 timezone (SE Asia Standard Time)
                TimeZoneInfo gmt7 = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");

                // Xóa job nếu tồn tại trước khi thêm mới
                RecurringJob.RemoveIfExists(jobName);
                RecurringJob.RemoveIfExists(SettingSyncSAPConstant.WarehouseSyncSAP);

                // Thêm mới job và run với timezone GMT+7
                if (settingJob?.Config != null)
                {
                    RecurringJob.AddOrUpdate(
                        jobName,
                        () => service.Run(),
                        settingJob.Config,
                        gmt7 // GMT+7 Timezone
                    );
                }
                else
                {
                    // Default to daily at 0:35 AM GMT+7 if no config is provided
                    RecurringJob.AddOrUpdate(
                        jobName,
                        () => service.Run(),
                        Cron.Daily(0, 35),
                        gmt7 // GMT+7 Timezone
                    );
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    IsSuccess = true,
                    Message = $"Job {jobName} has been registered successfully with cron expression: {settingJob?.Config ?? "Default: Daily at 0:35 AM"}"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Error registering job {jobName}: {ex.Message}"
                });
            }
        }

        [HttpGet("TestPhanBo")]
        public async Task<IActionResult> TestPhanBo()
        {
            GetSOWBSByBatchRequest req = new GetSOWBSByBatchRequest();
            req.BatchNumber = "IGO01N24DH";
            req.Plant = "1200";
            req.ProductCode = "230018956";
            req.Sloc = "SF01";

            var response = new List<GetSOWBSByBatchResponse>();
            var SaleOrg = req.Plant;
            var DVT = "M3";

            //Call SAP get số lượng tồn kho theo material code
            var querySAP = await _unitOfWork.SAPAPIRepository.GetStockSAP(new ZFM_MES_STOCK
            {
                LGORT = req.Sloc,
                MATNR = req.ProductCode,
                WERKS = req.Plant,
                CHARG = req.BatchNumber
            });

            var responseToSAP = querySAP.ZFM_MES_STOCKResponse.STOCKLIST.Where(x => x.LABST > 0);
            if (responseToSAP.Any())
            {

                foreach (var x in responseToSAP)
                {
                    //SSNum có 14 kí tự, kí tự thứ 11 là dấu / và kí tự thứ 12 là khoảng trắng => SO/ SOLine
                    if (x.SOBKZ == "E")
                    //if (x.SSNUM.Length == 14 && x.SSNUM.IndexOf("/") == 10 && x.SSNUM.IndexOf(" ") == 11)
                    {
                        //Cắt dấu /
                        var SOSOLine = x.SSNUM.Trim().Split("/");

                        //SO
                        var so = SOSOLine[0].Trim();

                        //SO Line
                        var soLine = SOSOLine[1].Trim();

                        //Get soSoLine
                        ZST_MES_SOLINE[] soSoLines = new ZST_MES_SOLINE[]
                        {
                        new ZST_MES_SOLINE
                        {
                            //SO
                            VBELN = so,
                            //SOLine
                            POSNR = soLine
                        }
                        };

                        //Get lsx đại trà
                        var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                        {
                            //SO và SOLine
                            SOLINES = soSoLines,
                            WERKS = SaleOrg
                        });

                        var resSAP = new GetSOWBSByBatchResponse
                        {
                            //SO và SOLine
                            SO = so,
                            SOLine = soLine,
                            //Số lượng tồn
                            Quantity = x.LABST,
                            //ĐVT
                            Unit = x.MEINS,
                            //Lệnh sản xuất đại trà
                            LSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse?.LIST_LSXDT?.FirstOrDefault()?.ZZLSX
                        };
                        response.Add(resSAP);
                    }
                    //Tồn trơn
                    else if (string.IsNullOrEmpty(x.SSNUM))
                    {

                        var resSAP = new GetSOWBSByBatchResponse
                        {
                            //WBS
                            WBS = string.IsNullOrEmpty(x.SSNUM) ? null : x.SSNUM,
                            //Số lượng tồn
                            Quantity = x.LABST,
                            //ĐVT
                            Unit = DVT,
                        };

                        response.Add(resSAP);
                    }
                    else
                    {
                        //Get soSoLine
                        ZST_MES_PROJN[] WBS = new ZST_MES_PROJN[]
                        {
                        new ZST_MES_PROJN
                        {
                            //WBS
                            PROJN = x.SSNUM
                        }
                        };

                        //Get lsx đại trà
                        var queryLSXDTSAP = await _unitOfWork.SAPAPIRepository.GetLSXDTSAP(new ZMES_FM_LSXDT
                        {
                            //WBS
                            PROJN = WBS,
                            WERKS = SaleOrg
                        });

                        var resSAP = new GetSOWBSByBatchResponse
                        {
                            //WBS
                            WBS = string.IsNullOrEmpty(x.SSNUM) ? null : x.SSNUM,
                            //Số lượng tồn
                            Quantity = x.LABST,
                            //ĐVT
                            Unit = DVT,
                            //Lệnh sản xuất đại trà
                            LSXDT = queryLSXDTSAP.ZMES_FM_LSXDTResponse?.LIST_LSXDT?.FirstOrDefault()?.ZZLSX
                        };

                        response.Add(resSAP);
                    }
                }
            }

            return Ok(response);
        }

        //[HttpGet("TestAutoAllocateService")]
        //public async Task<IActionResult> TestAutoAllocateService()
        //{
        //    var yesterday = DateTime.Now.Date.AddDays(-1);
        //    string formattedDate = yesterday.ToString("dd/MM/yyyy");

        //    await RunAutoAllocate(formattedDate, _context);

        //    return Ok(1);
        //}

        [HttpGet("TestCallMVC")]
        public async Task<IActionResult> TestCallMVC(string input)
        {
            var response = "";
            return Ok(response);

            using (var httpClient = new HttpClient())
            {
                var _mvcClientService = new MvcClientService(httpClient);
                //response = await _mvcClientService.GetAsync("https://localhost:44326/Home/GetPPOrderOpen"); // OK
                //response = await _mvcClientService.GetAsync("https://localhost:44326/Home/GetPPOrderOpenSecure");
                response = await _mvcClientService.GetTDSInfo("https://tdsp.ttf.com.vn/secure/ViewTicket.jspa?id=" + input);
            }

            if (string.IsNullOrEmpty(response))
            {
                return Ok("Chưa có ticket");
            }

            HtmlDocument document = new HtmlDocument();
            document.LoadHtml(response);

            HtmlNode node = document.GetElementbyId("ticket-property");

            var data = new Dictionary<string, string>();
            DateTime createdOn = DateTime.MinValue;

            // Select all <li> nodes
            var liNodes = document.DocumentNode.SelectNodes("//li");

            if (liNodes != null)
            {
                foreach (var liNode in liNodes)
                {
                    // Extract the title and value
                    var nameElement = liNode.SelectSingleNode(".//strong[contains(@class, 'name')]");
                    string title = nameElement != null ? nameElement.InnerText.Trim() : "Unknown Title";

                    var valueElement = liNode.SelectSingleNode(".//span[contains(@class, 'value')]");
                    string value = valueElement != null ? valueElement.InnerText.Trim() : "Unknown Value";

                    // Add to dictionary if the title is one of the expected titles
                    if (new[] { TicketProperties.Title, TicketProperties.RequestType, TicketProperties.PIC, TicketProperties.Status, TicketProperties.Location, TicketProperties.CreatedOn }.Contains(title))
                    {
                        data[title] = value;
                    }
                }
            }
            else
            {
                Console.WriteLine("No <li> elements found.");
            }

            // Find the <span> element with the id 'createdTime'
            var createdTimeElement = document.DocumentNode.SelectSingleNode("//span[@id='createdTime']");

            // Find the <span> element with the id 'createdTime'
            if (createdTimeElement != null)
            {
                // Extract and clean the date string
                string dateString = createdTimeElement.InnerText.Trim();

                // Define the format of the date string
                string format = "dd/MM/yyyy 'lúc' h:mm tt";

                if (dateString.StartsWith("Hôm nay"))
                {
                    // Replace "Hôm nay" with today's date
                    string todayDate = DateTime.Today.ToString("dd/MM/yyyy");
                    dateString = dateString.Replace("Hôm nay", todayDate);

                    if (DateTime.TryParseExact(dateString, format, new CultureInfo("vi-VN"), DateTimeStyles.None, out createdOn))
                    {
                        Console.WriteLine("Parsed DateTime: " + createdOn);
                    }
                    else
                    {
                        Console.WriteLine("Failed to parse date string: " + dateString);
                    }
                }
                else if (dateString.StartsWith("Hôm qua"))
                {
                    // Replace "Hôm qua" with yesterday's date
                    string yesterdayDate = DateTime.Today.AddDays(-1).ToString("dd/MM/yyyy");
                    dateString = dateString.Replace("Hôm qua", yesterdayDate);

                    if (DateTime.TryParseExact(dateString, format, new CultureInfo("vi-VN"), DateTimeStyles.None, out createdOn))
                    {
                        Console.WriteLine("Parsed DateTime: " + createdOn);
                    }
                    else
                    {
                        Console.WriteLine("Failed to parse date string: " + dateString);
                    }
                }
                else
                {
                    // Parse the date string into a DateTime object
                    if (DateTime.TryParseExact(dateString, format, new CultureInfo("vi-VN"), DateTimeStyles.None, out createdOn))
                    {
                        Console.WriteLine("Parsed DateTime: " + createdOn);
                    }
                    else
                    {
                        Console.WriteLine("Failed to parse date string: " + dateString);
                    }
                }
            }
            else
            {
                Console.WriteLine("Created time element not found.");
            }

            var tdsCode = HtmlHelper.ExtractTdsCode(response);

            if (tdsCode == null)
            {
                return Ok("TDS not found");
            }

            var element = document.DocumentNode.SelectSingleNode("//*[@id='ticketDetailJsonData']");
            if (element != null)
            {
                // Extract the text content of the element
                string textContent = element.InnerText.Trim();
                string decodedJson = HttpUtility.HtmlDecode(textContent);
                JObject jsonObject = JObject.Parse(decodedJson);
                // Create an object to hold the data
                var ticketData = new
                {
                    TicketDetailJsonData = decodedJson
                };

                // Serialize the data to JSON
                var jsonData = JsonConvert.SerializeObject(jsonObject, Formatting.Indented);

                // Output the JSON
                Console.WriteLine(jsonData);
            }
            else
            {
                Console.WriteLine("Element with ID 'ticketDetailJsonData' not found.");
            }

            // Create an instance of the entity with the extracted data
            //var tdsModel = new TDSModel
            //{
            //    LabelWorkflow = labelWorkflow,
            //    Title = data.GetValueOrDefault("Tiêu đề:", ""),
            //    RequestType = data.GetValueOrDefault("Loại yêu cầu:", ""),
            //    PIC = data.GetValueOrDefault("PIC:", ""),
            //    TrangThai = data.GetValueOrDefault("Trạng thái:", ""),
            //    ViTri = data.GetValueOrDefault("Vị trí:", ""),
            //    CreatedOn = DateTime.TryParseExact(data.GetValueOrDefault("Đã tạo:", ""), "dd/MM/yyyy 'lúc' h:mm tt", new System.Globalization.CultureInfo("vi-VN"), System.Globalization.DateTimeStyles.None, out DateTime createdOn) ? (DateTime?)createdOn : null,
            //    HtmlData = htmlContent
            //};

            // Update 1: not use magic string
            //// Create an instance of the entity with the extracted data
            var tdsModel = new TDSModel
            {
                TdsCode = (int)tdsCode,
                Title = data.GetValueOrDefault(TicketProperties.Title, ""),
                RequestType = data.GetValueOrDefault(TicketProperties.RequestType, ""),
                PIC = data.GetValueOrDefault(TicketProperties.PIC, ""),
                TrangThai = data.GetValueOrDefault(TicketProperties.Status, ""),
                ViTri = data.GetValueOrDefault(TicketProperties.Location, ""),
                CreatedOn = createdOn,
                CreatedOnText = data.GetValueOrDefault(TicketProperties.CreatedOn, ""),
                CreatedTime = DateTime.Now,
                //LastEditTime = DateTime.Now,  // Set LastEditTime to the current time
                //HtmlData = htmlContent
            };

            var existingTDSModel = await _context.TDSModel.FirstOrDefaultAsync(t => t.TdsCode == tdsModel.TdsCode);
            if (existingTDSModel == null)
            {
                _context.TDSModel.Add(tdsModel);
            }
            else
            {
                // Update 
                existingTDSModel.Title = tdsModel.Title;
                existingTDSModel.TrangThai = tdsModel.TrangThai;
                existingTDSModel.LastEditTime = DateTime.Now;
            }
            _context.SaveChanges();

            //return Ok(response);
            return Ok(tdsModel);
        }

        // Get tdscdoe


        #region Get sloc
        /// <summary>API Get Sloc</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v1/MES/Common/GetSloc?Sloc=0001 Plant=0003
        ///     Params: 
        ///             + version : 1
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                  "code": 200,
        ///                  "isSuccess": true,
        ///                  "message": "Get sloc thành công.",
        ///                  "data": {
        ///                      "sloc": "0001",
        ///                      "plant": "0003",
        ///                      "warehouseNo": "LWM",
        ///                      "defaultStorageBin": null
        ///                  },
        ///                  "additionalData": null
        ///              }
        /// </remarks>
        [HttpGet("GetSloc")]
        public async Task<IActionResult> GetSloc([FromQuery] GetSlocRequest request)
        {
            try
            {
                //Tìm kiếm sloc
                var sloc = _context.SlocModel.Where(x => (x.Actived == true) && (!string.IsNullOrEmpty(request.Sloc) ? x.Sloc.ToLower().Trim().Contains(request.Sloc.ToLower().Trim()) : true) &&
                                                         (!string.IsNullOrEmpty(request.Plant) ? x.Plant == request.Plant : true) &&
                                                         (!string.IsNullOrEmpty(request.Type) ? x.ProductionWarehouse == true : true))
                                              .OrderBy(x => x.Sloc).AsNoTracking();



                //Danh sách Sloc
                var response = await sloc.Select(x => new SlocDetailResponse
                {
                    SlocId = x.Id,
                    Sloc = x.Sloc,
                    SlocDisplay = $"{x.Sloc} | {x.SlocName}",
                    Plant = x.Plant,
                    DefaultStorageBin = x.DefaultStorageBin,
                    WarehouseNo = x.WarehouseNo,
                    DefaultStorageBinId = _context.StorageBinModel.FirstOrDefault(e => e.StorageBin == x.DefaultStorageBin) == null ? null :
                                          _context.StorageBinModel.FirstOrDefault(e => e.StorageBin == x.DefaultStorageBin).StorageBinId
                }).ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Get sloc")
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Get reservation
        /// <summary>API Get Reservation</summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v1/MES/Common/GetReservation?MaterialReservationId={}
        ///     Params: 
        ///             + version : 1
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                 "code": 200,
        ///                 "isSuccess": true,
        ///                 "message": "Get reservation thành công.",
        ///                 "data": {
        ///                   "movementType": "311",
        ///                   "reservationCode": "15239",
        ///                   "item": "1",
        ///                   "materialCode": "000000000270000016",
        ///                   "plant": "1000",
        ///                   "storageLocation": "N006",
        ///                   "batch": "",
        ///                   "reqQuantity": 3.6,
        ///                   "unit": "L",
        ///                   "riPlant": "1000",
        ///                   "riStorageLocation": "X001",
        ///                   "sosoLine": "/0",
        ///                   "wbs": "",
        ///                   "lsx": ""
        ///                 },
        ///                 "additionalData": null
        ///              }
        /// </remarks>
        [HttpGet("GetReservation")]
        public async Task<IActionResult> GetReservation([FromQuery] Guid MaterialReservationId)
        {
            try
            {
                //Tìm kiếm Reservation
                var rs = await _context.MaterialReservationModel.Include(x => x.ReservationHeader)
                                                                         .Where(x => x.ReservationId == MaterialReservationId)
                                                                         .FirstOrDefaultAsync();
                //Kiểm tra tồn tại
                if (rs == null)
                    return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Reservation") });

                var response = new GetReservationByIdResponse
                {
                    //Loại giao dịch kho
                    MovementType = rs.ReservationHeader?.MovementType,
                    //Mã reservation
                    ReservationCode = rs.ReservationNumber,
                    //Item reservation
                    Item = rs.ReservationItemNumber,
                    //Mã NVL
                    MaterialCode = rs.MaterialNumber,
                    //Nhà máy xuất
                    Plant = rs.Plant,
                    //Kho xuất
                    StorageLocation = rs.StorageLocation,
                    //Số lô
                    Batch = rs.BatchNumber,
                    //Số lượng yêu cầu và ĐVT
                    ReqQuantity = rs.ReqQuantity,
                    Unit = rs.MeasureUnit,
                    //Nhà máy nhận và kho nhận
                    RIPlant = rs.RIPlant,
                    RIStorageLocation = rs.RIStorageLocation,
                    //SO/SOLine
                    SOSOLine = $"{rs.SalesOrderNumber}/{rs.SalesOrderNumberItem}",
                    //WBS
                    WBS = rs.WBSElement,
                    //Lệnh sản xuất
                    LSX = rs.OrderNumber
                };

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Get reservation")
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }


        [HttpGet("GetReservationItems")]
        public async Task<IActionResult> GetReservationItems([FromQuery] Guid MaterialReservationId)
        {
            try
            {
                //Tìm kiếm Reservation item
                var materialReservation = await _context.MaterialReservationModel.Include(x => x.ReservationHeader)
                                                                         .Where(x => x.ReservationId == MaterialReservationId)
                                                                         .FirstOrDefaultAsync();
                //Từ item đó, lấy list tất cả item
                var rs2Temp = await _context.MaterialReservationModel.Include(x => x.ReservationHeader)
                                                                          .Where(x => x.ReservationHeaderId == materialReservation.ReservationHeaderId
                                                                                    && x.ItemDeleted != "X")
                                                                          .ToListAsync();

                var rs2 = rs2Temp
                                                                          .OrderBy(x => x.ReservationNumber)
                                                                          .ThenBy(x => x.StorageLocation)
                                                                          .ThenBy(x => x.RIStorageLocation)
                                                                          .ThenBy(x => x.MaterialNumber)
                                                                          .ThenBy(x => int.Parse(x.ReservationItemNumber)) // Sorting in memory
                                                                          .ToList();

                //Kiểm tra tồn tại
                if (!rs2.Any())
                    return Ok(new ApiResponse { Code = 400, Data = false, IsSuccess = false, Message = string.Format(CommonResource.Msg_NotFound, "Reservation") });

                //var rs2 = rs2.OrderBy(x => int.Parse(x.ReservationItemNumber));

                //var response = new GetReservationByIdResponse
                //{
                //    //Loại giao dịch kho
                //    MovementType = rs.ReservationHeader?.MovementType,
                //    //Mã reservation
                //    ReservationCode = rs.ReservationNumber,
                //    //Item reservation
                //    Item = rs.ReservationItemNumber,
                //    //Mã NVL
                //    MaterialCode = rs.MaterialNumber,
                //    //Nhà máy xuất
                //    Plant = rs.Plant,
                //    //Kho xuất
                //    StorageLocation = rs.StorageLocation,
                //    //Số lô
                //    Batch = rs.BatchNumber,
                //    //Số lượng yêu cầu và ĐVT
                //    ReqQuantity = rs.ReqQuantity,
                //    Unit = rs.MeasureUnit,
                //    //Nhà máy nhận và kho nhận
                //    RIPlant = rs.RIPlant,
                //    RIStorageLocation = rs.RIStorageLocation,
                //    //SO/SOLine
                //    SOSOLine = $"{rs.SalesOrderNumber}/{rs.SalesOrderNumberItem}",
                //    //WBS
                //    WBS = rs.WBSElement,
                //    //Lệnh sản xuất
                //    LSX = rs.OrderNumber
                //};

                var responses = new List<GetReservationByIdResponse>();

                foreach (var rs in rs2)
                {
                    var response = new GetReservationByIdResponse
                    {
                        ReservationId = rs.ReservationId,
                        // Loại giao dịch kho
                        MovementType = rs.ReservationHeader?.MovementType,
                        // Mã reservation
                        ReservationCode = rs.ReservationNumber,
                        // Item reservation
                        Item = rs.ReservationItemNumber,
                        // Mã NVL
                        MaterialCode = rs.MaterialNumber,
                        // Nhà máy xuất
                        Plant = rs.Plant,
                        // Kho xuất
                        StorageLocation = rs.StorageLocation,
                        // Số lô
                        Batch = rs.BatchNumber,
                        // Số lượng yêu cầu và ĐVT
                        ReqQuantity = rs.ReqQuantity,
                        Unit = rs.MeasureUnit,
                        // Nhà máy nhận và kho nhận
                        RIPlant = rs.RIPlant,
                        RIStorageLocation = rs.RIStorageLocation,
                        // SO/SOLine
                        SOSOLine = $"{rs.SalesOrderNumber}/{rs.SalesOrderNumberItem}",
                        // WBS
                        WBS = rs.WBSElement,
                        // Lệnh sản xuất
                        LSX = rs.OrderNumber,
                        Note = rs.Note,
                    };

                    responses.Add(response);
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = responses,
                    IsSuccess = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Get reservation")
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Get status reservation
        /// <summary>
        /// Get status Reservation
        /// </summary>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>

        [HttpGet("GetStatusReservation")]
        public async Task<IActionResult> GetStatusReservation()
        {
            try
            {
                //Trạng thái reservation
                var response = await _context.CatalogModel.Where(x => x.CatalogTypeCode == "StatusReservation").Select(x => new CommonResponse
                {
                    Key = x.CatalogCode.Trim(),
                    Value = x.CatalogText_vi.Trim()
                }).ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Get status Warehouse tranfer
        /// <summary>
        /// Get status Warehouse tranfer
        /// </summary>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        [HttpGet("GetStatusWarehouseTranfer")]
        public async Task<IActionResult> GetStatusWarehouseTranfer()
        {
            try
            {
                //Trạng thái reservation
                var response = await _context.CatalogModel.Where(x => x.CatalogTypeCode == "StatusWarehouseTranfer").Select(x => new CommonResponse
                {
                    Key = x.CatalogCode.Trim(),
                    Value = x.CatalogText_vi.Trim()
                }).ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Get product
        /// <summary>
        /// Get product
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetProduct")]
        public async Task<IActionResult> GetProduct([FromQuery] GetProductRequest request)
        {
            try
            {
                //Danh sách product
                var response = await _context.ProductModel.Where(x => string.IsNullOrEmpty(request.ProductCode) ? true :
                                                                     (x.ProductCode.ToLower().Contains(request.ProductCode.ToLower()) || x.ProductName.ToLower().Contains(request.ProductCode.ToLower())))
                .Select(x => new CommonResponse
                {
                    Key = x.ProductCode,
                    Value = $"{x.ProductCode} | {x.ProductName}"
                }).Take(10).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }

        [HttpGet("GetProductByCode")]
        public async Task<IActionResult> GetProductByCode([FromQuery] GetProductRequest request)
        {
            try
            {
                //Danh sách product
                var response = await _context.ProductModel.Where(p => p.ProductCode == request.ProductCode).AsNoTracking().FirstOrDefaultAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }

        /// <summary>
        /// Get product information by product code from ProductLatestModel
        /// </summary>
        /// <param name="productCode">Product Code</param>
        /// <returns>Product information</returns>
        [HttpGet("GetProductLatestByCode")]
        public async Task<IActionResult> GetProductLatestByCode([FromQuery] string productCode)
        {
            try
            {
                if (string.IsNullOrEmpty(productCode))
                {
                    return BadRequest(new ApiResponse
                    {
                        Code = 400,
                        IsSuccess = false,
                        Message = "Product code is required"
                    });
                }

                // Get product from ProductLatestModel by exact product code match
                var response = await _context.ProductLatestModel
                    .Where(p => p.ProductCode == productCode)
                    .Select(p => new 
                    {
                        ProductCode = p.ProductCode,
                        ProductName = p.ProductName
                    })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (response == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Code = 404,
                        IsSuccess = false,
                        Message = "Product not found"
                    });
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                    Message = "Product retrieved successfully"
                });
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Code = 500,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("GetProductDvt")]

        public async Task<IActionResult> GetProductDvt([FromQuery] GetProductRequest request)
        {
            try
            {
                //Danh sách product
                var response = await _context.ProductModel.Where(x => string.IsNullOrEmpty(request.ProductCode) ? true :
                                                                     (x.ProductCode.ToLower().Contains(request.ProductCode.ToLower()) || x.ProductName.ToLower().Contains(request.ProductCode.ToLower())))
                .Select(x => new CommonResponse2
                {
                    Key = x.ProductCode,
                    Value = $"{x.ProductCode} | {x.ProductName}",
                    Data = x.MEINS
                }).Take(10).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Get step
        /// <summary>
        /// Get danh sách công đoạn
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetStep")]
        public async Task<IActionResult> GetStep(string StepCode)
        {
            try
            {
                var CompanyCode = CurrentUser.CompanyCode;
                //Danh sách product
                var response = await _context.RoutingModel.Where(x => x.Plant == CompanyCode && (string.IsNullOrEmpty(StepCode) ? true : x.StepCode.Contains(StepCode)))
                .Select(x => new CommonResponse
                {
                    Key = x.StepCode,
                    Value = $"{x.StepCode} | {x.StepName}"
                }).OrderBy(x => x.Key).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }
        #endregion

        #region Get list storage bin
        /// <summary>
        /// Get storage bin
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("get-storage-bin")]
        public async Task<IActionResult> GetStorageBin([FromQuery] GetStorageBinRequest request)
        {
            try
            {
                //Tìm kiếm bin
                var response = await _context.StorageBinModel.Where(x => !string.IsNullOrEmpty(request.StorageBin) ? (x.StorageBin.ToLower().Trim().Contains(request.StorageBin.Trim().ToLower())) : true)
                                                        .OrderBy(x => x.StorageBin.Length)
                                                        .ThenBy(x => x.StorageBin)
                                                        .Select(x => new Common2Response
                                                        {
                                                            Key = x.StorageBinId,
                                                            Value = x.StorageBin
                                                        }).Take(10).AsNoTracking().ToListAsync();


                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Get storage bin")
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion

        #region Dropdown trạng thái giao hàng
        /// <summary>
        /// Dropdown trạng thái giao hàng
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetStatusGoodsArrive")]
        public async Task<IActionResult> GetStatusGoodsArrive()
        {
            try
            {
                //Danh sách product
                var response = await _context.CatalogModel.Where(x => x.CatalogTypeCode == "GoodsArrive").OrderBy(x => x.OrderIndex)
                .Select(x => new CommonResponse
                {
                    Key = x.CatalogCode,
                    Value = x.CatalogText_vi
                }).OrderBy(x => x.Key).AsNoTracking().ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }
        #endregion

        #region Lấy dropdown po code
        /// <summary>
        /// Lấy dropdown po code
        /// </summary>
        /// <param name="kw"></param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        [HttpGet("GetDropDownPurchaseOrder")]
        public async Task<IActionResult> GetDropDownPurchaseOrder([FromQuery] string kw)
        {
            try
            {
                //Lấy danh sách POCode theo keyword
                var response = await _context.PurchaseOrderMasterModel.Where(p => (!string.IsNullOrEmpty(kw) ? p.PurchaseOrderCode.Trim().Contains(kw.Trim()) : true) &&
                                                                                  (p.PurchaseOrderDetailModel.Count(e => e.DelivCompl != "X") > 0) &&
                                                                                  (p.Actived == true && p.ReleaseIndicator == "X"))
                                                                .OrderBy(p => p.PurchaseOrderCode)
                                                                .Select(x => new CommonResponse()
                                                                {
                                                                    Key = x.PurchaseOrderCode,
                                                                    Value = x.PurchaseOrderCode
                                                                })
                                                                .Take(10)
                                                                .ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                    Message = string.Format(CommonResource.Msg_Succes, "Lấy dropdown po code")
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }
        #endregion


        public class SampleQuantityResponse
        {
            public int? QuantitySample { get; set; }
        }

        #region Get số lượng lấy mẫu
        /// <summary>
        /// Get số lượng lấy mẫu
        /// </summary>
        /// <returns></returns>
        /// <remarks>
        /// Mẫu request
        /// 
        /// POST
        /// 
        ///     Url: /api/v1/MES/Common/get-quantity-sample?SampleName=S-1 QuantityCheck=5      
        ///             
        /// OUT PUT
        /// 
        ///              {
        ///                "code": 200,
        ///                "isSuccess": true,
        ///                "message": "Get số lượng lấy mẫu thành công.",
        ///                "data": {
        ///                  "quantitySample": 2
        ///                },
        ///                "additionalData": null
        ///              }
        /// </remarks>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("get-quantity-sample")]
        public async Task<IActionResult> GetDropDownPurchaseOrder([FromQuery] GetSampleQuantityRequest request)
        {
            // request.QuantityCheck: SL kiểm tra tab Phiếu KT
            // request.SampleName: Mức độ lấy mẫu KT (S-1, S-2, ...)

            // p.SampleSizeFrom: cỡ lô from
            // p.SampleSizeTo: cỡ lô to
            // 
            // Cỡ lô       Mức độ lấy mẫu   Số lượng lấy mẫu
            // 2 Đến 8     S-1              2
            // 9 Đến 15    S-1              2


            //Lấy phương pháp lấy mẫu theo mẫu kiểm tra
            var response = await _context.SampMethodModel.Where(p => (p.SampleName == request.SampleName) &&
                                                                     (request.QuantityCheck >= p.SampleSizeFrom && request.QuantityCheck <= p.SampleSizeTo)
                                                                )
                                                            .Select(x => new SampleQuantityResponse
                                                            {
                                                                QuantitySample = x.SampleQuantity
                                                            }).FirstOrDefaultAsync();
            if (response == null)
            {
                response = new SampleQuantityResponse { QuantitySample = 1 };
            }

            return Ok(new ApiResponse
            {
                Code = 200,
                Data = response,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Get số lượng lấy mẫu")
            });
        }
        #endregion


        [HttpGet("get-quantity-sample2")]
        public async Task<IActionResult> GetDropDownPurchaseOrder2([FromQuery] GetSampleQuantityRequest request)
        {
            // request.QuantityCheck: SL kiểm tra tab Phiếu KT
            // request.SampleName: Mức độ lấy mẫu KT (S-1, S-2, ...)

            // p.SampleSizeFrom: cỡ lô from
            // p.SampleSizeTo: cỡ lô to
            // 
            // Cỡ lô       Mức độ lấy mẫu   Số lượng lấy mẫu
            // 2 Đến 8     S-1              2
            // 9 Đến 15    S-1              2


            //Lấy phương pháp lấy mẫu theo mẫu kiểm tra
            var response = await _context.SampMethodModel.Where(p => (p.SampleName == request.SampleName) &&
                                                                     (request.QuantityCheck >= p.SampleSizeFrom && request.QuantityCheck <= p.SampleSizeTo)
                                                                )
                                                            .FirstOrDefaultAsync();

            if (response == null)
            {
                response = new SampMethodModel()
                {
                    SampleQuantity = 1

                };
            }

            return Ok(new ApiResponse
            {
                Code = 200,
                Data = response,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Get số lượng lấy mẫu")
            });
        }

        #region Get mức độ lỗi 
        /// <summary>
        /// Get mức độ lỗi
        /// </summary>
        /// <returns></returns>
        [HttpGet("get-defect-level")]
        public async Task<IActionResult> GetDropDownPurchaseOrder()
        {
            //Lấy danh sách mức độ lỗi 
            var response = await _context.CatalogModel.Where(p => p.CatalogTypeCode == "DefectLevel")
                                                      .OrderBy(x => x.OrderIndex)
                                                      .Select(x => new CommonResponse
                                                      {
                                                          Key = x.CatalogCode,
                                                          Value = x.CatalogText_vi
                                                      }).ToListAsync();

            return Ok(new ApiResponse
            {
                Code = 200,
                Data = response,
                IsSuccess = true,
                Message = string.Format(CommonResource.Msg_Succes, "Get mức độ lỗi")
            });
        }
        #endregion

        /// <summary>
        /// Get product
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpGet("GetPO")]
        public async Task<IActionResult> GetPO([FromQuery] string PO)
        {
            try
            {
                var response = (from po in _context.PurchaseOrderMasterModel
                                join vendor in _context.VendorModel on po.VendorNumber equals vendor.SupplierNumber
                                where po.PurchaseOrderCode.Contains(PO)
                                select new { PurchaseOrderCode = po.PurchaseOrderCode, ShortName = vendor.ShortName })
                  .Take(20)
                  .Select(x => new CommonResponse
                  {
                      Key = x.PurchaseOrderCode,
                      Value = $"{x.PurchaseOrderCode} | {x.ShortName}"
                  });



                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }



        public class GetPODetailResponse
        {
            public string Material { get; set; }
            public decimal? POQuantity { get; set; }
        }

        [HttpGet("GetPODetails")]

        public async Task<IActionResult> GetPODetails([FromQuery] string PO)
        {
            try
            {
                var response = (from po in _context.PurchaseOrderMasterModel
                                join details in _context.PurchaseOrderDetailModel on po.PurchaseOrderCode equals details.PurchaseOrderCode
                                where po.PurchaseOrderCode == PO && details.DeletionInd != "L"
                                group details by new { details.Material } into g
                                select new GetPODetailResponse
                                {
                                    Material = g.Key.Material,
                                    POQuantity = g.Sum(d => d.POQuantity)
                                }).ToList();


                foreach (var item in response)
                {
                    item.Material = item.Material.TrimStart('0');
                }

                return Ok(new ApiResponse
                {
                    Code = 200,
                    Data = response,
                    IsSuccess = true,
                });
            }
            catch (System.Exception ex)
            {

                throw new System.Exception(ex.Message);
            }
        }

        [HttpGet("GetEmployees")]
        public async Task<IActionResult> GetEmployees([FromQuery] string companyCode)
        {
            //_logger.LogDebug($"GetEmployees API called with companyCode: {companyCode}");
            try
            {
                var employees = await _context.SalesEmployeeModel
                    .Where(x => x.Actived == true)
                    .Select(x => new EmployeeDropdownItem
                    {
                        EmployeeCode = x.SalesEmployeeCode,
                        EmployeeName = x.SalesEmployeeName
                    })
                    .ToListAsync();

                //_logger.LogDebug($"GetEmployees returned {employees.Count} records.");
                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = employees
                });
            }
            catch (Exception ex)
            {
                //_logger.LogError($"Error in GetEmployees: {ex}");
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Search employees with pagination and filtering
        /// </summary>
        /// <param name="query">Search query to filter employees</param>
        /// <param name="companyCode">Company code</param>
        /// <param name="limit">Maximum number of results to return (default 20)</param>
        /// <returns>List of matching employees</returns>
        [HttpGet("SearchEmployees")]
        public async Task<IActionResult> SearchEmployees([FromQuery] string query, [FromQuery] string companyCode, [FromQuery] int limit = 20)
        {
            try
            {
                var employees = await _context.SalesEmployeeModel
                    .Where(x => x.Actived == true &&
                               (string.IsNullOrEmpty(query) ||
                                x.SalesEmployeeCode.Contains(query) ||
                                x.SalesEmployeeName.Contains(query)))
                    .OrderBy(x => x.SalesEmployeeCode)
                    .Take(limit)
                    .Select(x => new EmployeeDropdownItem
                    {
                        EmployeeCode = x.SalesEmployeeCode,
                        EmployeeName = x.SalesEmployeeName
                    })
                    .ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = employees,
                    Message = string.Format(CommonResource.Msg_Succes, "Search employees")
                });
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

        [HttpGet("GetListWorkshop")]
        public async Task<IActionResult> GetListWorkshop([FromQuery] string companyCode)
        {
            try
            {
                var workshopsList = await (from wsm in _context.WorkShopModel
                                          join sm in _context.StoreModel on wsm.StoreId equals sm.StoreId
                                          where wsm.Actived == true && 
                                                sm.SaleOrgCode == companyCode
                                          orderby wsm.WorkShopCode
                                          select new WorkshopDropdownItem
                                          {
                                              WorkShopCode = wsm.WorkShopCode,
                                              WorkShopName = wsm.WorkShopName,
                                              SaleOrgCode = sm.SaleOrgCode,
                                              StoreName = sm.StoreName
                                          }).ToListAsync();

                return Ok(new ApiResponse
                {
                    Code = (int)HttpStatusCode.OK,
                    IsSuccess = true,
                    Data = workshopsList
                });
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, new ApiResponse
                {
                    Code = (int)HttpStatusCode.InternalServerError,
                    IsSuccess = false,
                    Message = $"Error: {ex.Message}"
                });
            }
        }

    }
}

public static class TicketProperties
{
    public const string Title = "Tiêu đề:";
    public const string RequestType = "Loại yêu cầu:";
    public const string PIC = "PIC:";
    public const string Status = "Trạng thái:";
    public const string Location = "Vị trí:";
    public const string CreatedOn = "Đã tạo:";
}