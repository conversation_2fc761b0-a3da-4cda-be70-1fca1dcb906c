import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:ttf/Storage/storageSecureStorage.dart';
import '../../model/downtimeModel.dart';
import '../../urlApi/urlApi.dart';
import '../function/commonFunction.dart';

class DowntimeFunction {
  // Flag to switch between mock and API data
  // static const bool useMockData = true; // Set to false for production API calls
  static const bool useMockData = false; // Set to false for production API calls

  // Add URL variable
  static const String _baseEndpoint = '/api/v1/MES/';
  static const String _downtimeEndpoint = '${_baseEndpoint}Downtime/';
  static const String _commonEndpoint = '${_baseEndpoint}Common/';
  static const String _departmentEndpoint = '${_downtimeEndpoint}GetListDepartment';
  static const String _stepCodeEndpoint = '${_downtimeEndpoint}GetListStepCode';
  static const String _employeeEndpoint = '${_downtimeEndpoint}GetEmployees';
  static const String _workshopEndpoint = '${_commonEndpoint}GetListWorkshop';

  // Helper method to get base URL based on environment
  static Future<String> _getBaseUrl() async {
    final environment = await SecureStorage.getString("environment", null);
    return environment == 'PRD' ? UrlApi.baseUrl_2Prd : await UrlApi.baseUrl_2Qas;
  }

  // Mock in-memory storage
  static final List<DowntimeRecord> _mockDb = [
    DowntimeRecord(
      id: "1", // Will be mapped to downtimeId in API
      date: "2024-03-25", // Format matches API yyyy-MM-dd
      departmentCode: "D001",
      departmentName: "Phòng May 1",
      stepCode: "STEP001",
      stepName: "May áo",
      startTime: "08:00",
      endTime: "10:00",
      reason: "Máy hỏng",
      responsibleTeam: "Bộ phận kỹ thuật",
      responsibleDepartment: "50", // Percentage responsibility
      personCausedDowntimeCodeMany: "NV001,NV002",
      note: "Đã sửa xong",
      status: "Completed",
      verificationStatus: "Approved",
      createdDate: "2024-03-25T08:00:00",
      createBy: "user1",
      updatedDate: "2024-03-25T10:30:00",
      updateBy: "user1",
      history: [
        DowntimeHistory(
          historyId: "h1",
          downtimeId: "1",
          action: "STATUS_CHANGE",
          oldStatus: "Pending",
          newStatus: "In Progress",
          changedBy: "Nguyễn Văn A",
          changedDate: "2024-03-25T08:15:00",
        ),
        DowntimeHistory(
          historyId: "h2",
          downtimeId: "1",
          action: "APPROVAL",
          oldStatus: "In Progress",
          newStatus: "APPROVED",
          changedBy: "Trần Thị B",
          changedDate: "2024-03-25T09:00:00",
          verifierRole: "TEAM_LEADER",
          comment: "Đã kiểm tra và xác nhận sự cố",
        ),
      ],
    ),
    // Add more mock records as needed...
  ];

  // API Functions
  static Future<DowntimeModel?> _fetchDowntimeListFromApi(String token) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_downtimeEndpoint}GetDowntimeList';
      debugPrint('DowntimeFunction._fetchDowntimeListFromApi | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('API Response Status: ${response.statusCode}');
      debugPrint('API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return DowntimeModel.fromJson(jsonResponse);
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('DowntimeFunction._fetchDowntimeListFromApi | error: $e');
      return null;
    }
  }

  static Future<DowntimeRecord?> _fetchDowntimeDetailFromApi(String token, String id) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_downtimeEndpoint$id';
      debugPrint('DowntimeFunction._fetchDowntimeDetailFromApi | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        if (jsonResponse['data'] != null) {
          return DowntimeRecord.fromJson(jsonResponse['data']);
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('DowntimeFunction._fetchDowntimeDetailFromApi | error: $e');
      return null;
    }
  }

  static Future<bool> _createDowntimeViaApi(String token, DowntimeRecord record) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_downtimeEndpoint}CreateDowntime';
      debugPrint('DowntimeFunction._createDowntimeViaApi | url: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
        body: json.encode(record.toJson()),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('DowntimeFunction._createDowntimeViaApi | error: $e');
      return false;
    }
  }

  static Future<bool> _updateDowntimeViaApi(String token, DowntimeRecord record) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_downtimeEndpoint${record.id}';
      debugPrint('DowntimeFunction._updateDowntimeViaApi | url: $url');

      final response = await http.put(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
        body: json.encode(record.toJson()),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('DowntimeFunction._updateDowntimeViaApi | error: $e');
      return false;
    }
  }

  // Mock Functions
  static Future<DowntimeModel?> _fetchDowntimeListFromMock(String token) async {
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
    return DowntimeModel(
      status: true,
      message: "Success",
      data: List.from(_mockDb),
    );
  }

  static Future<DowntimeRecord?> _fetchDowntimeDetailFromMock(String token, String id) async {
    await Future.delayed(const Duration(seconds: 1));
    try {
      return _mockDb.firstWhere((record) => record.id == id);
    } catch (e) {
      return null;
    }
  }

  static Future<bool> _createDowntimeViaMock(String token, DowntimeRecord record) async {
    await Future.delayed(const Duration(seconds: 1));
    final newId = (_mockDb.length + 1).toString();
    record.id = newId;
    record.createdDate = DateTime.now().toIso8601String();
    record.updatedDate = record.createdDate;
    _mockDb.add(record);
    return true;
  }

  static Future<bool> _updateDowntimeViaMock(String token, DowntimeRecord record) async {
    await Future.delayed(const Duration(seconds: 1));
    final index = _mockDb.indexWhere((r) => r.id == record.id);
    if (index != -1) {
      record.updatedDate = DateTime.now().toIso8601String();
      _mockDb[index] = record;
      return true;
    }
    return false;
  }

  // Public Interface - These functions will use either mock or API based on useMockData flag
  static Future<DowntimeModel?> fetchDowntimeList(String token, DowntimeSearchModel searchModel) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl${_downtimeEndpoint}DowntimeList';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...UrlApi.headersToken(token),
          'Content-Type': 'application/json',
        },
        body: jsonEncode(searchModel.toJson()),
      );

      if (response.statusCode == 200) {
        return DowntimeModel.fromJson(jsonDecode(response.body));
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching downtime list: $e');
      return null;
    }
  }

  static Future<DowntimeRecord?> fetchDowntimeDetail(String token, String id) async {
    debugPrint("--- TIEN fetchDowntimeDetail | id: $id | useMockData: $useMockData");
    return useMockData ? _fetchDowntimeDetailFromMock(token, id) : _fetchDowntimeDetailFromApi(token, id);
  }

  static Future<bool> createDowntime(String token, DowntimeRecord record) async {
    debugPrint("--- TIEN createDowntime | useMockData: $useMockData");
    return useMockData ? _createDowntimeViaMock(token, record) : _createDowntimeViaApi(token, record);
  }

  static Future<bool> updateDowntime(String token, DowntimeRecord record) async {
    debugPrint("--- TIEN updateDowntime | id: ${record.id} | useMockData: $useMockData");
    return useMockData ? _updateDowntimeViaMock(token, record) : _updateDowntimeViaApi(token, record);
  }

  static Future<bool> saveDowntime(String token, DowntimeRecord record) async {
    debugPrint("--- TIEN saveDowntime | useMockData: $useMockData");
    debugPrint("Operation: ${record.id == null || record.id!.isEmpty ? 'Create' : 'Update'}");

    if (record.id == null || record.id!.isEmpty) {
      record.history = [
        DowntimeHistory(
          historyId: DateTime.now().millisecondsSinceEpoch.toString(),
          action: "STATUS_CHANGE",
          oldStatus: null,
          newStatus: "Pending",
          changedBy: "Current User",
          changedDate: DateTime.now().toIso8601String(),
        ),
      ];
      return createDowntime(token, record);
    } else {
      if (useMockData) {
        final existingRecord = _mockDb.firstWhere((r) => r.id == record.id);
        record.history = existingRecord.history;
      }
      return updateDowntime(token, record);
    }
  }

  static Future<DowntimeMasterDataResponse?> fetchDepartments(String token, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_departmentEndpoint?companyCode=$companyCode';
      debugPrint('DowntimeFunction.fetchDepartments | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('API Response Status: ${response.statusCode}');
      debugPrint('API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        debugPrint('Parsed departments: ${jsonResponse['data']}');

        if (jsonResponse['data'] != null) {
          return DowntimeMasterDataResponse(
              departments: (jsonResponse['data'] as List)
                  .map((item) => DepartmentItem(
                        departmentCode: item['departmentCode'],
                        departmentName: item['departmentName'],
                      ))
                  .toList(),
              status: jsonResponse['status'] ?? true,
              message: jsonResponse['message'] ?? 'Success');
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('DowntimeFunction.fetchDepartments | error: $e');
      return null;
    }
  }

  static Future<DowntimeMasterDataResponse?> fetchWorkshop(String token, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_workshopEndpoint?companyCode=$companyCode';
      debugPrint('DowntimeFunction.fetchWorkshop | url: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('API Response Status: ${response.statusCode}');
      debugPrint('API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        debugPrint('Parsed workshops: ${jsonResponse['data']}');

        if (jsonResponse['data'] != null) {
          return DowntimeMasterDataResponse(
              workshops: (jsonResponse['data'] as List)
                  .map((item) => WorkshopItem(
                        workShopCode: item['workShopCode'],
                        workShopName: item['workShopName'],
                        saleOrgCode: item['saleOrgCode'],
                        storeName: item['storeName'],
                      ))
                  .toList(),
              status: jsonResponse['isSuccess'] ?? true,
              message: jsonResponse['message'] ?? 'Success');
        }
      }

      debugPrint('API Error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('DowntimeFunction.fetchWorkshop | error: $e');
      return null;
    }
  }

  static Future<DowntimeMasterDataResponse?> fetchStepCodes(String token, String companyCode) async {
    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_stepCodeEndpoint?companyCode=$companyCode';
      debugPrint('Full URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('Response Status: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');
      debugPrint('Request URL: ${response.request?.url}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return DowntimeMasterDataResponse(
            steps: (jsonResponse['data'] as List?)
                ?.map((item) => StepCodeItem(
                      stepCode: item['stepCode'],
                      stepName: item['stepName'],
                    ))
                .toList(),
            status: jsonResponse['isSuccess'] ?? true,
            message: jsonResponse['message'] ?? 'Success');
      }

      return null;
    } catch (e) {
      debugPrint('Error in fetchStepCodes: $e');
      return null;
    }
  }

  static Future<DowntimeMasterDataResponse?> _fetchDepartmentsFromMock(String token, String companyCode) async {
    await Future.delayed(const Duration(seconds: 1));
    return DowntimeMasterDataResponse(departments: [
      DepartmentItem(departmentCode: "D001", departmentName: "D001 | Phòng May 1"),
      DepartmentItem(departmentCode: "D002", departmentName: "D002 | Phòng May 2"),
    ], status: true, message: "Success");
  }

  static Future<DowntimeMasterDataResponse?> _fetchWorkshopsFromMock(String token, String companyCode) async {
    await Future.delayed(const Duration(seconds: 1));
    return DowntimeMasterDataResponse(workshops: [
      WorkshopItem(workShopCode: "WS001", workShopName: "WS001 | Phân xưởng May 1", saleOrgCode: companyCode, storeName: "Nhà máy chính"),
      WorkshopItem(workShopCode: "WS002", workShopName: "WS002 | Phân xưởng May 2", saleOrgCode: companyCode, storeName: "Nhà máy chính"),
    ], status: true, message: "Success");
  }

  static Future<DowntimeMasterDataResponse?> _fetchStepCodesFromMock(String token) async {
    await Future.delayed(const Duration(seconds: 1));
    return DowntimeMasterDataResponse(steps: [
      StepCodeItem(stepCode: "STEP001", stepName: "STEP001 | May áo"),
      StepCodeItem(stepCode: "STEP002", stepName: "STEP002 | May quần"),
    ], status: true, message: "Success");
  }

  static Future<DowntimeMasterDataResponse?> getDepartments(String token, String companyCode) async {
    debugPrint("--- TIEN getDepartments | useMockData: $useMockData | companyCode: $companyCode");
    return useMockData ? _fetchDepartmentsFromMock(token, companyCode) : fetchDepartments(token, companyCode);
  }

  static Future<DowntimeMasterDataResponse?> getWorkshops(String token, String companyCode) async {
    debugPrint("--- TIEN getWorkshops | useMockData: $useMockData | companyCode: $companyCode");
    return useMockData ? _fetchWorkshopsFromMock(token, companyCode) : fetchWorkshop(token, companyCode);
  }

  static Future<DowntimeMasterDataResponse?> getStepCodes(String token, String companyCode) async {
    debugPrint("--- TIEN getStepCodes | useMockData: $useMockData");
    return useMockData ? _fetchStepCodesFromMock(token) : fetchStepCodes(token, companyCode);
  }

  static Future<DowntimeMasterDataResponse?> fetchEmployees(String token, String companyCode) async {
    try {
      final response = await CommonFunction.fetchEmployees(token, companyCode);
      return DowntimeMasterDataResponse(employees: response.employees, status: response.status, message: response.message);
    } catch (e) {
      debugPrint('DowntimeFunction.fetchEmployees | error: $e');
      return null;
    }
  }

  static Future<DowntimeMasterDataResponse?> getEmployees(String token, String companyCode) async {
    debugPrint("--- TIEN getEmployees | useMockData: $useMockData | companyCode: $companyCode");
    return useMockData ? _fetchEmployeesFromMock(token, companyCode) : fetchEmployees(token, companyCode);
  }

  static Future<DowntimeMasterDataResponse?> _fetchEmployeesFromMock(String token, String companyCode) async {
    await Future.delayed(const Duration(seconds: 1));
    return DowntimeMasterDataResponse(employees: [
      EmployeeRecord(employeeId: "NV001", employeeName: "NV001 | Nguyễn Văn A"),
      EmployeeRecord(employeeId: "NV002", employeeName: "NV002 | Trần Thị B"),
    ], status: true, message: "Success");
  }

  static Future<List<DowntimeHistory>?> fetchDowntimeHistory(String token, String id, String? companyCode) async {
    debugPrint("--- TIEN fetchDowntimeHistory | id: $id | companyCode: $companyCode");

    try {
      final baseUrl = await _getBaseUrl();
      final url = '$baseUrl$_downtimeEndpoint$id/history${companyCode != null ? '?companyCode=$companyCode' : ''}';

      final response = await http.get(
        Uri.parse(url),
        headers: UrlApi.headersToken(token),
      );

      debugPrint('fetchDowntimeHistory response status: ${response.statusCode}');
      debugPrint('fetchDowntimeHistory response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);

        if (jsonResponse['isSuccess'] == true && jsonResponse['data'] != null) {
          return (jsonResponse['data'] as List).map((item) => DowntimeHistory.fromJson(item)).toList();
        }

        // Return empty list if no data
        return [];
      }

      throw Exception('Failed to fetch history. Status code: ${response.statusCode}');
    } catch (e) {
      debugPrint('DowntimeFunction.fetchDowntimeHistory | error: $e');
      return null;
    }
  }

  // Add mock version if needed
  static Future<List<DowntimeHistory>?> _fetchDowntimeHistoryFromMock(String token, String id, String? companyCode) async {
    await Future.delayed(const Duration(seconds: 1));
    return [
      DowntimeHistory(
          historyId: "1",
          downtimeId: id,
          action: "CREATE",
          actionDisplay: "Tạo mới",
          newStatus: "Created",
          newStatusDisplay: "Mới tạo",
          changedBy: "USER001",
          changedByName: "Nguyễn Văn A",
          changedDate: DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
          comment: "Tạo mới downtime"),
      DowntimeHistory(
          historyId: "2",
          downtimeId: id,
          action: "STATUS_CHANGE",
          actionDisplay: "Thay đổi trạng thái",
          oldStatus: "Created",
          oldStatusDisplay: "Mới tạo",
          newStatus: "In Progress",
          newStatusDisplay: "Đang xử lý",
          changedBy: "USER002",
          changedByName: "Trần Thị B",
          changedDate: DateTime.now().toIso8601String(),
          comment: "Chuyển trạng thái"),
    ];
  }

  // Add getter method that handles mock toggle
  static Future<List<DowntimeHistory>?> getDowntimeHistory(String token, String id, String? companyCode) async {
    debugPrint("--- TIEN getDowntimeHistory | useMockData: $useMockData | id: $id | companyCode: $companyCode");
    return useMockData ? _fetchDowntimeHistoryFromMock(token, id, companyCode) : fetchDowntimeHistory(token, id, companyCode);
  }
}

// Add these model classes if they don't exist
class EmployeeResponse {
  final List<Employee>? employees;
  final String? message;

  EmployeeResponse({this.employees, this.message});

  factory EmployeeResponse.fromJson(Map<String, dynamic> json) {
    return EmployeeResponse(
      employees: (json['data'] as List?)?.map((e) => Employee.fromJson(e)).toList(),
      message: json['message'],
    );
  }
}

class Employee {
  final String? employeeId;
  final String? employeeName;

  Employee({this.employeeId, this.employeeName});

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      employeeId: json['employeeId'],
      employeeName: json['employeeName'],
    );
  }
}

// DowntimeMasterDataResponse is now defined in the main model file

class DowntimeListResponse {
  final List<DowntimeRecord>? records;
  final bool status;
  final String message;

  DowntimeListResponse({
    this.records,
    this.status = true,
    this.message = 'Success',
  });

  factory DowntimeListResponse.fromJson(Map<String, dynamic> json) {
    return DowntimeListResponse(
      records: (json['data'] as List?)?.map((item) => DowntimeRecord.fromJson(item)).toList(),
      status: json['status'] ?? true,
      message: json['message'] ?? 'Success',
    );
  }
}
