# Feature Implementation Instruction Template

## Overview

**Feature Name**: [Feature Name]  
**Priority**: [High/Medium/Low]  
**Estimated Effort**: [X days/hours]  
**Assigned To**: [Assignee Name]  

## Requirements

### Business Requirements

- [Requirement 1]
- [Requirement 2]
- [Requirement 3]

### Technical Requirements

- [Technical requirement 1]
- [Technical requirement 2]
- [Technical requirement 3]

## Implementation Steps

### Frontend (Flutter)

1. **Create Models**
   - Create `FeatureNameModel.dart` in `lib/model/` directory
   - Implement required properties and methods
   - Add fromJson/toJson methods

2. **Create Service Functions**
   - Add `featureNameFunction.dart` in `lib/repository/function/` directory
   - Implement API calls for list, detail, create, update

3. **Create List Screen**
   - Create `FeatureNameList.dart` in `lib/page/FeatureName/` directory
   - Implement loading, error handling, and UI components
   - Add search/filter functionality

4. **Create Detail Screen**
   - Create `FeatureNameDetail.dart` in `lib/page/FeatureName/` directory
   - Implement form with appropriate input fields
   - Add validation logic

5. **Add Routes**
   - Update `route.dart` to include new feature routes
   - Ensure proper argument handling

6. **Update Main Page**
   - Add feature to main page menu
   - Implement permission check

### Backend (ASP.NET Core)

1. **Create Database Schema**
   - Design table structure
   - Create SQL scripts
   - Update entity model

2. **Create Models**
   - Implement entity classes
   - Add DTO/ViewModel classes

3. **Create Repository**
   - Implement repository interface
   - Implement repository methods

4. **Create Controller**
   - Add API endpoints
   - Implement CRUD operations
   - Add validation

## UI Components

Use the following UI components:

| Field Type | Component | Notes |
|------------|-----------|-------|
| Date | Date Picker | Format: dd/MM/yyyy |
| Time | Time Picker | Format: hh:mm a |
| Text | Standard Text Field | Use validation as needed |
| Numeric | Numeric Input | Use appropriate input formatters |
| Dropdown | DropdownButtonFormField | Load options from API if dynamic |
| Autocomplete | AutoCompleteField | Use for code-based lookups |
| Multiple line | TextArea | Set minLines and maxLines |

## API Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/FeatureName` | GET | Get list of records |
| `/api/FeatureName/{id}` | GET | Get specific record |
| `/api/FeatureName` | POST | Create new record |
| `/api/FeatureName/{id}` | PUT | Update existing record |
| `/api/FeatureName/{id}` | DELETE | Delete record |

## Testing Scenarios

1. **List Loading**
   - Verify records load correctly
   - Test search/filter functionality
   - Check pagination if implemented

2. **Create Flow**
   - Test form validation
   - Verify successful submission
   - Check error handling

3. **Update Flow**
   - Test loading existing data
   - Verify successful updates
   - Test validation rules

4. **Integration Testing**
   - Test end-to-end workflows
   - Verify data consistency
   - Check network error handling

## Additional Notes

- [Any special considerations]
- [Dependencies on other features]
- [Known limitations]

## References

- [Link to design mockups]
- [Link to API documentation]
- [Other relevant documentation]
