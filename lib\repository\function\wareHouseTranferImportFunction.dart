import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../model/getQuantityExported.dart';
import '../../model/warehouseTranferImport.dart';
import '../../screenArguments/sceemArgumentGetBackData.dart';
import '../../service/globalValue.dart';
import '../api/wareHouseTranferImportApi.dart';

class WareHouseTranferImportFunction {
  static Future<String?> getWarehouseTranferImport(WarehouseTranferImportModel warehouseTranferImport, String token) async {
    final response = await WareHouseTranferImportApi.postWareHouseTranferImport(warehouseTranferImport, token);
    if (response.statusCode == 200) {
      final getMessage = WarehouseTranferImportMessage.fromJson(jsonDecode(response.body));
      if (getMessage.code == 200 && getMessage.isSuccess == true) {
        return getMessage.message;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static Future<void> sendWarehouseTranferImport(
      WarehouseTranferImportModel warehouseTranferImport, String token, BuildContext context, String plant) async {
    try {
      showDialog<String>(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) => WillPopScope(
          onWillPop: () async => false,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
      final message = await getWarehouseTranferImport(warehouseTranferImport, token);
      Navigator.pop(context);
      if (message != null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              message,
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
        Future.delayed(const Duration(seconds: 0), () {
          Navigator.pop(context, true);

          // Navigator.of(context).popUntil((route) {
          //   if (route.settings.name == '/ListTranferMaterial') {
          //
          //     (route.settings.arguments as ScreenArgumentGetBackData).refresh = true;
          //     return true;
          //   } else {
          //     return false;
          //   }
          // });
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            backgroundColor: Colors.black,
            content: Text(
              "Chuyển nhập kho thất bại",
              style: TextStyle(fontSize: 15.sp, color: Colors.white),
            ),
            duration: const Duration(seconds: 2)));
      }
    } on SocketException catch (_) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Không có kết nối mạng',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    } catch (error) {
      Navigator.pop(context);
      debugPrint(error.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          backgroundColor: Colors.black,
          content: Text(
            'Xảy ra lỗi! Vui lòng thử lại sau',
            style: TextStyle(fontSize: 15.sp, color: Colors.white),
          ),
          duration: const Duration(seconds: 1)));
    }
  }

  static Future<DataQuantityExported?> fetchQuantityExported(String reservationId, String token) async {
    final response = await WareHouseTranferImportApi.getQuantityExported(reservationId, token);
    if (response.statusCode == 200) {
      final responseQuantityExported = jsonDecode(response.body);
      if (responseQuantityExported != null) {
        final getQuantityExported = GetQuantityExported.fromJson(responseQuantityExported);
        if (getQuantityExported.code == 200 && getQuantityExported.isSuccess == true) {
          return getQuantityExported.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}
