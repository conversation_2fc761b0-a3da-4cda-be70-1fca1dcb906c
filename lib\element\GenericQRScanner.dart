import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class GenericQRScanner extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Future<dynamic> Function(String scannedData) onDataScanned;
  final String Function(dynamic error)? getErrorMessage;
  final Color? overlayBorderColor;
  final double? cutOutSize;
  final bool showFlashToggle;
  final bool showBackButton;
  final String backButtonText;
  final Color? backgroundColor;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final Widget? customOverlay;
  final bool vibrationOnScan;
  final Duration? pauseDurationAfterScan;
  final bool autoReturn;

  const GenericQRScanner({
    Key? key,
    this.title = 'Quét mã',
    this.subtitle,
    required this.onDataScanned,
    this.getErrorMessage,
    this.overlayBorderColor = Colors.red,
    this.cutOutSize = 300,
    this.showFlashToggle = true,
    this.showBackButton = true,
    this.backButtonText = 'Thoát',
    this.backgroundColor,
    this.titleStyle,
    this.subtitleStyle,
    this.customOverlay,
    this.vibrationOnScan = true,
    this.pauseDurationAfterScan,
    this.autoReturn = true,
  }) : super(key: key);

  @override
  State<GenericQRScanner> createState() => _GenericQRScannerState();
}

class _GenericQRScannerState extends State<GenericQRScanner> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool _isProcessing = false;
  String? _errorMessage;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller?.pauseCamera();
    }
    controller?.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        await _pauseCameraAndExit();
        return false;
      },
      child: Scaffold(
        backgroundColor: widget.backgroundColor ?? Colors.black,
        appBar: _buildAppBar(),
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            _buildQrView(context),
            if (widget.subtitle != null) _buildSubtitle(),
            if (_errorMessage != null) _buildErrorMessage(),
            _buildBottomControls(),
            if (_isProcessing) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget? _buildAppBar() {
    if (widget.title.isEmpty) return null;

    return AppBar(
      title: Text(
        widget.title,
        style: widget.titleStyle ??
            TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: widget.showBackButton
          ? IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white, size: 24.sp),
              onPressed: _pauseCameraAndExit,
            )
          : null,
      centerTitle: true,
    );
  }

  Widget _buildSubtitle() {
    return Positioned(
      top: 120.h,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          widget.subtitle!,
          style: widget.subtitleStyle ??
              TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Positioned(
      bottom: 150.h,
      left: 20.w,
      right: 20.w,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.red.shade800,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.white, size: 20.sp),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                _errorMessage!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: 18.sp),
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                });
                _resumeCamera();
              },
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 30.h,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          if (widget.showFlashToggle)
            IconButton(
              onPressed: () async {
                await controller?.toggleFlash();
                setState(() {});
              },
              icon: FutureBuilder<bool?>(
                future: controller?.getFlashStatus(),
                builder: (context, snapshot) {
                  if (snapshot.data != null) {
                    return Icon(
                      snapshot.data! ? Icons.flash_on : Icons.flash_off,
                      color: Colors.white,
                      size: 30.sp,
                    );
                  } else {
                    return Container();
                  }
                },
              ),
            )
          else
            SizedBox(width: 48.w),
          SizedBox(width: 40.w),
          if (widget.showBackButton)
            GestureDetector(
              onTap: _pauseCameraAndExit,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  widget.backButtonText,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            )
          else
            SizedBox(width: 48.w),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              SizedBox(height: 16.h),
              Text(
                'Đang xử lý...',
                style: TextStyle(fontSize: 14.sp),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: widget.customOverlay as QrScannerOverlayShape? ??
          QrScannerOverlayShape(
            borderColor: widget.overlayBorderColor!,
            borderRadius: 10.r,
            borderLength: 30,
            borderWidth: 10,
            cutOutSize: widget.cutOutSize!.w,
          ),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller) async {
    setState(() {
      this.controller = controller;
    });

    if (Platform.isAndroid) {
      await this.controller!.resumeCamera();
    }

    controller.scannedDataStream.listen((scanData) async {
      if (!_isProcessing && scanData.code != null) {
        await _handleScannedData(scanData.code!);
      }
    });
  }

  Future<void> _handleScannedData(String scannedData) async {
    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    await controller?.pauseCamera();

    if (widget.vibrationOnScan) {
      // Add haptic feedback if available
      // HapticFeedback.mediumImpact();
    }

    try {
      // Process the scanned data using the provided callback
      final result = await widget.onDataScanned(scannedData);

      if (widget.autoReturn) {
        // Auto return with result
        Navigator.pop(context, result);
      } else {
        // Manual return - let user decide
        setState(() {
          _isProcessing = false;
        });
        _showSuccessDialog(result);
      }
    } catch (error) {
      // Handle error
      final errorMessage = widget.getErrorMessage?.call(error) ?? _getDefaultErrorMessage(error);

      setState(() {
        _isProcessing = false;
        _errorMessage = errorMessage;
      });

      // Auto resume camera after delay
      if (widget.pauseDurationAfterScan != null) {
        await Future.delayed(widget.pauseDurationAfterScan!);
        if (mounted && _errorMessage != null) {
          _resumeCamera();
        }
      } else {
        // Default delay of 2 seconds
        await Future.delayed(Duration(seconds: 2));
        if (mounted && _errorMessage != null) {
          _resumeCamera();
        }
      }
    }
  }

  String _getDefaultErrorMessage(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceFirst('Exception: ', '');
    }
    return error.toString();
  }

  void _showSuccessDialog(dynamic result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Quét thành công'),
        content: Text('Dữ liệu đã được quét. Bạn có muốn sử dụng kết quả này?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _resumeCamera(); // Resume scanning
            },
            child: Text('Quét lại'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context, result); // Return result
            },
            child: Text('Sử dụng'),
          ),
        ],
      ),
    );
  }

  Future<void> _pauseCameraAndExit() async {
    await Future.delayed(const Duration(milliseconds: 100));
    await controller?.pauseCamera();
    Navigator.pop(context);
  }

  Future<void> _resumeCamera() async {
    await Future.delayed(const Duration(milliseconds: 500));
    await controller?.resumeCamera();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
