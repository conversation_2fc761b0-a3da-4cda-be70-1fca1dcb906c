import 'dart:convert';
import '../../model/getQuantityImported.dart';
import '../api/detailReservationImportedApi.dart';
import '../api/getQuantityImportedApi.dart';

class DetailReservationFunction{

  static Future<DataGetQuantityImported?> fetchGetQuantityImported(String rawMaterialCardId, String token) async{
    final response = await GetQuantityImportedApi.getQuantityImportedApi(rawMaterialCardId, token);
    if (response.statusCode == 200) {
      final responseReservation= jsonDecode(response.body);
      if (responseReservation != null) {
        final getReservation = GetQuantityImported.fromJson(responseReservation);
        if (getReservation.code == 200 && getReservation.isSuccess == true) {
          return getReservation.data;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}