﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace ISD.API.EntityModels.Models
{
    [Table("PlateFeeModel", Schema = "tSale")]
    public partial class PlateFeeModel
    {
        public PlateFeeModel()
        {
            PlateFeeDetailModel = new HashSet<PlateFeeDetailModel>();
            Product = new HashSet<ProductModel>();
        }

        [Key]
        public Guid PlateFeeId { get; set; }
        [StringLength(50)]
        public string PlateFeeCode { get; set; }
        [StringLength(500)]
        public string PlateFeeName { get; set; }
        [StringLength(1000)]
        public string Description { get; set; }
        public bool? Actived { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDate { get; set; }
        [StringLength(100)]
        public string CreatedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifyDate { get; set; }
        [StringLength(100)]
        public string LastModifyUser { get; set; }

        [InverseProperty("PlateFee")]
        public virtual ICollection<PlateFeeDetailModel> PlateFeeDetailModel { get; set; }

        [ForeignKey("PlateFeeId")]
        [InverseProperty("PlateFee")]
        public virtual ICollection<ProductModel> Product { get; set; }
    }
}