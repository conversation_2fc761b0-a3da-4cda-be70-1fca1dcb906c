import 'dart:io';

class SendErrorControl {
  String? quanlityControlErrorId;
  String? catalogCode;
  String? levelError;
  int? quantityError;
  String? notes;
  List<File>? lsFile;
  String? caNhanGayLoi;
  String? congDoanLoi;
  String? phuongAnXuLy;
  String? nhaMayLoi;
  String? phanXuongLoi;
  String? toChuyenLoi;

  String? caNhanGayLoiMany;

  String? quanDoc;
  String? toTruong;
  String? qaqc;
  String? kcs;

  SendErrorControl(
    this.quanlityControlErrorId,
    this.catalogCode,
    this.levelError,
    this.quantityError,
    this.notes,
    this.lsFile,
    this.caNhanGayLoi,
    this.congDoanLoi,
    this.phuongAnXuLy,
    this.nhaMayLoi,
    this.phanXuongLoi,
    this.toChuyenLoi,
    this.caNhanGayLoiMany,
    this.quanDoc,
    this.toTruong,
    this.qaqc,
    this.kcs,
  );

  SendErrorControl.fromJson(Map<String, dynamic> json) {
    quanlityControlErrorId = json['quanlityControl_Error_Id'];
    catalogCode = json['catalogCode'];
    levelError = json['levelError'];
    quantityError = json['quantityError'];
    notes = json['notes'];
    lsFile = json['file'];
    caNhanGayLoi = json['caNhanGayLoi'];
    congDoanLoi = json['congDoanLoi'];
    phuongAnXuLy = json['phuongAnXuLy'];
    nhaMayLoi = json['nhaMayLoi'];
    phanXuongLoi = json['phanXuongLoi'];
    toChuyenLoi = json['toChuyenLoi'];

    caNhanGayLoiMany = json['caNhanGayLoiMany'];

    quanDoc = json['quanDoc'];
    toTruong = json['toTruong'];
    qaqc = json['qaqc'];
    kcs = json['kcs'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quanlityControl_Error_Id'] = quanlityControlErrorId;
    data['catalogCode'] = catalogCode;
    data['levelError'] = levelError;
    data['quantityError'] = quantityError;
    data['notes'] = notes;
    data['file'] = lsFile;
    data['caNhanGayLoi'] = caNhanGayLoi;
    data['congDoanLoi'] = congDoanLoi;
    data['phuongAnXuLy'] = phuongAnXuLy;
    data['nhaMayLoi'] = nhaMayLoi;
    data['phanXuongLoi'] = phanXuongLoi;
    data['toChuyenLoi'] = toChuyenLoi;

    data['caNhanGayLoiMany'] = caNhanGayLoiMany;

    data['quanDoc'] = quanDoc;
    data['toTruong'] = toTruong;
    data['qaqc'] = qaqc;
    data['kcs'] = kcs;
    return data;
  }
}
